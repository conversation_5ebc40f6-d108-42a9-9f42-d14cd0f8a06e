using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Estimate.Parameter.BusinessComponents;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RVPBC = RIB.Visual.Platform.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RIB.Visual.Basics.Core.Core;
using System.ComponentModel.Composition;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Estimate.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Common.Enum;
using RIB.Visual.Basics.Common.BusinessComponents;
using System.Globalization;
using RIB.Visual.Basics.Core.Core.Common;
using RIB.Visual.Platform.OperationalManagement;
using System.Reflection;
using EntityFramework.Functions;
using RIB.Visual.Platform.AppServer.Runtime;
using EntityFrameworkExtras.EF6;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	[Export(typeof(IEstimateMainLS2RuleLogic))]
	public class EstimateMainLS2RuleLogic : RVPBizComp.LogicBase, IEstimateMainLS2RuleLogic
	{
		private int _EstHeaderId;

		private int? _ProjectId;

		private List<int> _StructureToAdd = new List<int>();

		private List<int> _RuleAssignedTo = new List<int>();

		private List<int> _ParamToUpdate = new List<int>();

		private List<int> _LineItemIdsToUpdate = new List<int>();

		private List<EstRuleSourceEntity> _RuleSourcesToDelete = new List<EstRuleSourceEntity>();

		private List<EstRuleSrc2CostGroupRuleEntity> _EstRuleSrc2CostGroupRulesToDelete = new List<EstRuleSrc2CostGroupRuleEntity>();

		private IEnumerable<EstResourceEntity> _ResourcesGeneratedByRule = new List<EstResourceEntity>();

		private IEnumerable<EstLineItemEntity> _LineItemsGeneratedByRule = new List<EstLineItemEntity>();

		/// <summary>
		/// Provides access to the database model.
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel() => ModelBuilder.DbModel;

		/// <summary>
		/// 
		/// </summary>
		/// <typeparam name="T"></typeparam>
		/// <param name="entities"></param>
		/// <param name="estHeaderId"></param>
		/// <param name="leadingStructureType"></param>
		/// <param name="ruleSourceIds"></param>
		/// <returns></returns>
		public bool IsLineItemsCanDelete<T>(IEnumerable<T> entities, int estHeaderId, LeadingStructure2RuleEnum leadingStructureType, List<int> ruleSourceIds = null) where T : IIdentifyable
		{
			if (entities == null || !entities.Any())
			{
				return true;
			}

			if (ruleSourceIds == null)
			{
				/* get ruleSource entities */
				List<int> ls2RuleIds = entities.Select(e => e.Id).ToList();

				var ruleResourceEntities = new EstRuleResourceLogic().GetByRuleFks(leadingStructureType, ls2RuleIds).ToList();

				/* get lineItem generated by rule */
				ruleSourceIds = ruleResourceEntities.Select(e => e.Id).ToList();
			}

			var lineItemGenratedByRule = new EstimateMainLineItemLogic().Get(e => e.EstRuleSourceFk.HasValue && ruleSourceIds.Contains(e.EstRuleSourceFk.Value) && e.EstHeaderFk == estHeaderId);

			if (lineItemGenratedByRule == null || !lineItemGenratedByRule.Any())
			{
				return true;
			}

			//find the progressReport related to those lineitems
			//the generated lineItem may be used by progressReport, 
			//so if have progressReports using those lineItems , can't delete
			var activityProgressReportLogic = RVPARB.BusinessEnvironment.GetExportedValue<IActivityProgressReportLogic>("Scheduling.Main.ActivityProgressReportLogic");

			return activityProgressReportLogic.hasProgressReportFindByLineItemFks(lineItemGenratedByRule.Select(e => e.Id).ToList());
		}

		/// <summary>
		/// It's used for judge the boq2rule(prjBoq2Rule , estBoq2Rule) can be delete or not
		/// </summary>
		/// <param name="prjBoq2EstRuleIds"></param>
		/// <returns></returns>
		public bool CanDeleteBoq2Rule(List<int> prjBoq2EstRuleIds)
		{
			//ruleSource can be used in est_lineItem and est_resource as rule_source_fk
			//if used, should remove both the lineitem and resource
			//acturally, if not found lineitem or resource generated from the ruleSource, the ruleSource can be deleted
			//todo, sai.zhou, not hanlder this situation here
			var ruleResourceEntities = new EstRuleResourceLogic().GetByRuleFks(LeadingStructure2RuleEnum.PrjBoq2estRuleFk, prjBoq2EstRuleIds).ToList();

			return !ruleResourceEntities.Any();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="prjEstRuleCodeList"></param>
		/// <param name="projectId"></param>
		/// <param name="lsItemId"></param>
		/// <param name="lsName"></param>
		/// <param name="estHeaderId"></param>
		/// <returns></returns>
		public bool CanDelete4LS2Rule(List<string> prjEstRuleCodeList, int projectId, int lsItemId, string lsName, int estHeaderId)
		{
			var prjEstRules = new EstimateRulePrjEstRuleLogic().GetListByFilter(e => e.ProjectFk == projectId && prjEstRuleCodeList.Contains(e.Code));

			if (prjEstRules == null || !prjEstRules.Any())
			{
				return true;
			}

			switch (lsName)
			{
				case "EstLineItems":
					{
						List<EstLineItem2EstRuleEntity> ls2EstRules = new EstimateRuleLineItemLogic().getListByCodes(prjEstRuleCodeList, projectId, estHeaderId, lsItemId);

						return IsLineItemsCanDelete(ls2EstRules, estHeaderId, LeadingStructure2RuleEnum.EstLineitem2estRuleFk);
					}
				case "EstBoq":
					{
						List<EstBoq2EstRuleEntity> ls2EstRules = new EstimateRuleBoqLogic().getListByCodes(prjEstRuleCodeList, projectId, estHeaderId, lsItemId);

						return IsLineItemsCanDelete(ls2EstRules, estHeaderId, LeadingStructure2RuleEnum.EstBoq2estRuleFk);
					}
				case "EstActivity":
					{
						List<EstActivity2EstRuleEntity> ls2EstRules = new EstimateRuleActivityLogic().getListByCodes(prjEstRuleCodeList, projectId, estHeaderId, lsItemId);

						return IsLineItemsCanDelete(ls2EstRules, estHeaderId, LeadingStructure2RuleEnum.EstActivity2estRuleFk);
					}
				case "EstCtu":
					{
						List<EstCtu2EstRuleEntity> ls2EstRules = new EstimateRuleControllingUnitLogic().getListByCodes(prjEstRuleCodeList, projectId, estHeaderId, lsItemId);

						return IsLineItemsCanDelete(ls2EstRules, estHeaderId, LeadingStructure2RuleEnum.EstCtu2estRuleFk);
					}
				case "EstPrjLocation":
					{
						List<EstPrjLoc2EstRuleEntity> ls2EstRules = new EstimateRulePrjLocationLogic().getListByCodes(prjEstRuleCodeList, projectId, estHeaderId, lsItemId);

						return IsLineItemsCanDelete(ls2EstRules, estHeaderId, LeadingStructure2RuleEnum.EstPrjLoc2estRuleFk);
					}
				case "EstPrcStructure":
					{
						List<EstPrcStruc2EstRuleEntity> ls2EstRules = new EstimateRulePrcStructureLogic().getListByCodes(prjEstRuleCodeList, projectId, estHeaderId, lsItemId);

						return IsLineItemsCanDelete(ls2EstRules, estHeaderId, LeadingStructure2RuleEnum.EstPrcStruc2estRuleFk);
					}
				case "EstCostGrp":
					{
						List<EstCostGrpRuleEntity> ls2EstRules = new EstimateCostGrpRuleLogic().getListByCodes(prjEstRuleCodeList, projectId, estHeaderId, lsItemId);

						return IsLineItemsCanDelete(ls2EstRules, estHeaderId, LeadingStructure2RuleEnum.BasCostGroupFk);
					}
				case "EstAssemblyCat":
					{
						List<EstAssembly2EstRuleEntity> ls2EstRules = new EstimateRuleAssemblyCatLogic().getListByCodes(prjEstRuleCodeList, projectId, estHeaderId, lsItemId);

						return IsLineItemsCanDelete(ls2EstRules, estHeaderId, LeadingStructure2RuleEnum.EstAssembly2estRuleFk);
					}
				default:
					return true;
			}
		}

		private void CollectRuleSourcesInSave<T>(IEnumerable<T> ruleAssignmentsToSave, LeadingStructure2RuleEnum leadingStructureType, int ruleAssignType) where T : EntityBase, IRuleAssignmentEntity
		{
			if (ruleAssignmentsToSave == null || !ruleAssignmentsToSave.Any())
			{
				return;
			}

			this._StructureToAdd.Add(ruleAssignType);

			/* if the isExecution of rule assignment is false, then it need to delete the entities generated by this rule assingment */
			var ruleAssignmentIds = ruleAssignmentsToSave.Where(e => !e.IsExecution).Select(e => e.Id).Distinct().ToList();

			AddRuleSourcesToDelete(ruleAssignmentIds, leadingStructureType);
		}

		private IEnumerable<T> CollectRuleSourcesInDelete<T>(IEnumerable<T> ruleAssignmentsToDelete, LeadingStructure2RuleEnum leadingStructureType, Func<IEnumerable<T>, int?, IEnumerable<T>> getListFunc) where T : EntityBase, IRuleAssignmentEntity
		{
			if (ruleAssignmentsToDelete == null || !ruleAssignmentsToDelete.Any())
			{
				return ruleAssignmentsToDelete;
			}

			var ruleAssignmentInDb = getListFunc(ruleAssignmentsToDelete, this._ProjectId);

			var ruleAssignmentIds = ruleAssignmentInDb.Select(e => e.Id).Distinct().ToList();

			AddRuleSourcesToDelete(ruleAssignmentIds, leadingStructureType);

			return ruleAssignmentInDb;
		}

		private void AddRuleSourcesToDelete(IEnumerable<int> ruleAssignmentIds, LeadingStructure2RuleEnum leadingStructureType)
		{
			if (ruleAssignmentIds == null || !ruleAssignmentIds.Any()) { return; }

			if (leadingStructureType == LeadingStructure2RuleEnum.BasCostGroupFk)
			{
				var estRuleSrc2CostGroupRules = new EstRuleSrc2CostGroupRuleLogic().GetListByFilter(e => ruleAssignmentIds.Contains(e.EstCostGroupRuleFk)).ToList();

				if (estRuleSrc2CostGroupRules == null) { return; }

				this._EstRuleSrc2CostGroupRulesToDelete.AddRange(estRuleSrc2CostGroupRules);

				var ruleSourceIds = estRuleSrc2CostGroupRules.Select(e => e.EstRuleSourceFk).ToList();

				var ruleSourceEntities = new EstRuleResourceLogic().GetListByFilter(e => ruleSourceIds.Contains(e.Id));

				if (ruleSourceEntities == null || !ruleSourceEntities.Any()) { return; }

				this._RuleSourcesToDelete.AddRange(ruleSourceEntities);
			}
			else
			{
				var ruleSourceEntities = new EstRuleResourceLogic().GetByRuleFks(leadingStructureType, ruleAssignmentIds);

				if (ruleSourceEntities == null || !ruleSourceEntities.Any()) { return; }

				this._RuleSourcesToDelete.AddRange(ruleSourceEntities);
			}
		}

		private void DeleteEntitiesGeneratedByRule()
		{
			if (this._RuleSourcesToDelete == null || !this._RuleSourcesToDelete.Any())
			{
				return;
			}

			List<int> ruleSourceIds = this._RuleSourcesToDelete.Select(e => e.Id).ToList();

			using (var dbcontext = CreateDbContext())
			{
				var resourceDeletedIds = dbcontext.ObjectContext().ExecuteStoredProcedure<EstEntityKey>(new EstDeleteResourceGeneratedByRuleProc()
				{
					EstHeaderId = _EstHeaderId,
					RuleSourceIds = ruleSourceIds.Select(e => new EstUdttIds() { Id = e }).ToList()
				});

				this._ResourcesGeneratedByRule = resourceDeletedIds.Select(e => new EstResourceEntity()
				{
					Id = e.Id,
					EstHeaderFk = e.Pk1.Value,
					EstLineItemFk = e.Pk2.Value
				}).ToList();

				var lineItemIds = resourceDeletedIds.Where(e => e.Pk2.HasValue).Select(e => e.Pk2.Value).Distinct().ToList();

				var lineItemDeletedIds = dbcontext.ObjectContext().ExecuteStoredProcedure<EstEntityKey>(new EstDeleteLineItemGeneratedByRule()
				{
					EstHeaderId = _EstHeaderId,
					RuleSourceIds = ruleSourceIds.Select(e => new EstUdttIds() { Id = e }).ToList(),
					WhoIsr = BusinessApplication.BusinessEnvironment.CurrentContext.UserId
				});

				this._LineItemsGeneratedByRule = lineItemDeletedIds.Select(e => new EstLineItemEntity()
				{
					Id = e.Id,
					EstHeaderFk = e.Pk1.Value
				}).ToList();

				if (lineItemDeletedIds.Any())
				{
					var lineItemIdsIgnore = lineItemDeletedIds.Select(e => e.Id).Distinct().ToHashSet<int>();

					this._LineItemIdsToUpdate = lineItemIds.Where(e => !lineItemIdsIgnore.Contains(e)).ToList();
				}
				else
				{
					this._LineItemIdsToUpdate = lineItemIds;
				}
			}

			if (this._EstRuleSrc2CostGroupRulesToDelete != null && this._EstRuleSrc2CostGroupRulesToDelete.Any())
			{
				new EstRuleSrc2CostGroupRuleLogic().Delete(this._EstRuleSrc2CostGroupRulesToDelete);
			}

			if (this._RuleSourcesToDelete.Any())
			{
				new EstRuleResourceLogic().Delete(this._RuleSourcesToDelete);
			}
		}

		private void CollectEntitiesToDelete(EstMainCompleteEntity estMainComplete)
		{
			if (estMainComplete.PrjEstRuleToSave != null && estMainComplete.PrjEstRuleToSave.Any())
			{
				estMainComplete.PrjEstRuleToSave = new EstimateRulePrjEstRuleLogic().Save(estMainComplete.PrjEstRuleToSave, estMainComplete.ProjectId);
			}

			if (estMainComplete.EstHeaderRuleToSave != null && estMainComplete.EstHeaderRuleToSave.Any())
			{
				estMainComplete.EstHeaderRuleToSave = new EstimateRuleHeaderLogic().Save(estMainComplete.EstHeaderRuleToSave, estMainComplete.PrjEstRuleToSave);

				CollectRuleSourcesInSave(estMainComplete.EstHeaderRuleToSave, LeadingStructure2RuleEnum.EstHeader2estRuleFk, (int)EstimateId.EstHeader);
			}

			if (estMainComplete.EstHeaderRuleToDelete != null && estMainComplete.EstHeaderRuleToDelete.Any())
			{
				estMainComplete.EstHeaderRuleToDelete = CollectRuleSourcesInDelete(estMainComplete.EstHeaderRuleToDelete, LeadingStructure2RuleEnum.EstHeader2estRuleFk, new EstimateRuleHeaderLogic().GetList);
			}

			if (estMainComplete.EstHeaderParamToSave != null && estMainComplete.EstHeaderParamToSave.Any())
			{
				_ParamToUpdate.Add((int)EstimateId.EstHeader);
			}

			if (estMainComplete.EstLineItemsRuleToDelete != null && estMainComplete.EstLineItemsRuleToDelete.Any())
			{
				estMainComplete.EstLineItemsRuleToDelete = CollectRuleSourcesInDelete(estMainComplete.EstLineItemsRuleToDelete, LeadingStructure2RuleEnum.EstLineitem2estRuleFk, new EstimateRuleLineItemLogic().CollectLineItem2RuleToDelete);
			}

			if (estMainComplete.EstLineItemsRuleToSave != null && estMainComplete.EstLineItemsRuleToSave.Any())
			{
				estMainComplete.EstLineItemsRuleToSave = new EstimateRuleLineItemLogic().Save(estMainComplete.EstLineItemsRuleToSave, estMainComplete.PrjEstRuleToSave);

				CollectRuleSourcesInSave(estMainComplete.EstLineItemsRuleToSave, LeadingStructure2RuleEnum.EstLineitem2estRuleFk, (int)EstimateId.LineItem);
			}

			if (estMainComplete.EstLineItemsParamToSave != null && estMainComplete.EstLineItemsParamToSave.Any())
			{
				_ParamToUpdate.Add((int)EstimateId.LineItem);
			}

			if (estMainComplete.EstBoqRuleToSave != null && estMainComplete.EstBoqRuleToSave.Any())
			{
				estMainComplete.EstBoqRuleToSave = new EstimateRuleBoqLogic().Save(estMainComplete.EstBoqRuleToSave, estMainComplete.PrjEstRuleToSave);

				CollectRuleSourcesInSave(estMainComplete.EstBoqRuleToSave, LeadingStructure2RuleEnum.EstBoq2estRuleFk, (int)StructureId.BoQ);
			}

			if (estMainComplete.EstBoqRuleToDelete != null && estMainComplete.EstBoqRuleToDelete.Any())
			{
				estMainComplete.EstBoqRuleToDelete = CollectRuleSourcesInDelete(estMainComplete.EstBoqRuleToDelete, LeadingStructure2RuleEnum.EstBoq2estRuleFk, new EstimateRuleBoqLogic().GetList);
			}

			if (estMainComplete.EstBoqParamToSave != null && estMainComplete.EstBoqParamToSave.Any())
			{
				_ParamToUpdate.Add((int)StructureId.BoQ);
			}

			if (estMainComplete.EstActivityRuleToSave != null && estMainComplete.EstActivityRuleToSave.Any())
			{
				estMainComplete.EstActivityRuleToSave = new EstimateRuleActivityLogic().Save(estMainComplete.EstActivityRuleToSave, estMainComplete.PrjEstRuleToSave);

				CollectRuleSourcesInSave(estMainComplete.EstActivityRuleToSave, LeadingStructure2RuleEnum.EstActivity2estRuleFk, (int)StructureId.Schedule);
			}

			if (estMainComplete.EstActivityRuleToDelete != null && estMainComplete.EstActivityRuleToDelete.Any())
			{
				estMainComplete.EstActivityRuleToDelete = CollectRuleSourcesInDelete(estMainComplete.EstActivityRuleToDelete, LeadingStructure2RuleEnum.EstActivity2estRuleFk, new EstimateRuleActivityLogic().GetList);
			}

			if (estMainComplete.EstActivityParamToSave != null && estMainComplete.EstActivityParamToSave.Any())
			{
				_ParamToUpdate.Add((int)StructureId.Schedule);
			}

			if (estMainComplete.EstAssemblyCatRuleToSave != null && estMainComplete.EstAssemblyCatRuleToSave.Any())
			{
				estMainComplete.EstAssemblyCatRuleToSave = new EstimateRuleAssemblyCatLogic().Save(estMainComplete.EstAssemblyCatRuleToSave, estMainComplete.PrjEstRuleToSave);

				CollectRuleSourcesInSave(estMainComplete.EstAssemblyCatRuleToSave, LeadingStructure2RuleEnum.EstAssembly2estRuleFk, (int)StructureId.AssemblyStructure);
			}

			if (estMainComplete.EstAssemblyCatRuleToDelete != null && estMainComplete.EstAssemblyCatRuleToDelete.Any())
			{
				estMainComplete.EstAssemblyCatRuleToDelete = CollectRuleSourcesInDelete(estMainComplete.EstAssemblyCatRuleToDelete, LeadingStructure2RuleEnum.EstAssembly2estRuleFk, new EstimateRuleAssemblyCatLogic().GetList);
			}

			if (estMainComplete.EstAssemblyCatParamToSave != null && estMainComplete.EstAssemblyCatParamToSave.Any())
			{
				_ParamToUpdate.Add((int)StructureId.AssemblyStructure);
			}

			if (estMainComplete.EstCtuRuleToSave != null && estMainComplete.EstCtuRuleToSave.Any())
			{
				estMainComplete.EstCtuRuleToSave = new EstimateRuleControllingUnitLogic().Save(estMainComplete.EstCtuRuleToSave, estMainComplete.PrjEstRuleToSave);

				CollectRuleSourcesInSave(estMainComplete.EstCtuRuleToSave, LeadingStructure2RuleEnum.EstCtu2estRuleFk, (int)StructureId.Controllingunits);
			}

			if (estMainComplete.EstCtuRuleToDelete != null && estMainComplete.EstCtuRuleToDelete.Any())
			{
				estMainComplete.EstCtuRuleToDelete = CollectRuleSourcesInDelete(estMainComplete.EstCtuRuleToDelete, LeadingStructure2RuleEnum.EstCtu2estRuleFk, new EstimateRuleControllingUnitLogic().GetList);
			}

			if (estMainComplete.EstCtuParamToSave != null && estMainComplete.EstCtuParamToSave.Any())
			{
				_ParamToUpdate.Add((int)StructureId.Controllingunits);
			}

			if (estMainComplete.EstPrcStructureRuleToSave != null && estMainComplete.EstPrcStructureRuleToSave.Any())
			{
				estMainComplete.EstPrcStructureRuleToSave = new EstimateRulePrcStructureLogic().Save(estMainComplete.EstPrcStructureRuleToSave, estMainComplete.PrjEstRuleToSave);

				CollectRuleSourcesInSave(estMainComplete.EstPrcStructureRuleToSave, LeadingStructure2RuleEnum.EstPrcStruc2estRuleFk, (int)StructureId.ProcurementStructure);
			}

			if (estMainComplete.EstPrcStructureRuleToDelete != null && estMainComplete.EstPrcStructureRuleToDelete.Any())
			{
				estMainComplete.EstPrcStructureRuleToDelete = CollectRuleSourcesInDelete(estMainComplete.EstPrcStructureRuleToDelete, LeadingStructure2RuleEnum.EstPrcStruc2estRuleFk, new EstimateRulePrcStructureLogic().GetList);
			}

			if (estMainComplete.EstPrcStructureParamToSave != null && estMainComplete.EstPrcStructureParamToSave.Any())
			{
				_ParamToUpdate.Add((int)StructureId.ProcurementStructure);
			}

			if (estMainComplete.EstPrjLocationRuleToSave != null && estMainComplete.EstPrjLocationRuleToSave.Any())
			{
				estMainComplete.EstPrjLocationRuleToSave = new EstimateRulePrjLocationLogic().Save(estMainComplete.EstPrjLocationRuleToSave, estMainComplete.PrjEstRuleToSave);

				CollectRuleSourcesInSave(estMainComplete.EstPrjLocationRuleToSave, LeadingStructure2RuleEnum.EstPrjLoc2estRuleFk, (int)StructureId.Location);
			}

			if (estMainComplete.EstPrjLocationRuleToDelete != null && estMainComplete.EstPrjLocationRuleToDelete.Any())
			{
				estMainComplete.EstPrjLocationRuleToDelete = CollectRuleSourcesInDelete(estMainComplete.EstPrjLocationRuleToDelete, LeadingStructure2RuleEnum.EstPrjLoc2estRuleFk, new EstimateRulePrjLocationLogic().GetList);
			}

			if (estMainComplete.EstPrjLocationParamToSave != null && estMainComplete.EstPrjLocationParamToSave.Any())
			{
				_ParamToUpdate.Add((int)StructureId.Location);
			}

			if (estMainComplete.EstCostGrpRuleToSave != null && estMainComplete.EstCostGrpRuleToSave.Any())
			{
				estMainComplete.EstCostGrpRuleToSave = new EstimateCostGrpRuleLogic().Save(estMainComplete.EstCostGrpRuleToSave, estMainComplete.PrjEstRuleToSave);

				CollectRuleSourcesInSave(estMainComplete.EstCostGrpRuleToSave, LeadingStructure2RuleEnum.BasCostGroupFk, (int)StructureId.BasCostGroup);
			}

			if (estMainComplete.EstCostGrpRuleToDelete != null && estMainComplete.EstCostGrpRuleToDelete.Any())
			{
				estMainComplete.EstCostGrpRuleToDelete = CollectRuleSourcesInDelete(estMainComplete.EstCostGrpRuleToDelete, LeadingStructure2RuleEnum.BasCostGroupFk, new EstimateCostGrpRuleLogic().GetList);
			}

			if (estMainComplete.EstCostGrpParamToSave != null && estMainComplete.EstCostGrpParamToSave.Any())
			{
				_ParamToUpdate.Add((int)StructureId.BasCostGroup);
			}
		}

		private void DeleteRuleAndParamAssignments(EstMainCompleteEntity estMainComplete)
		{
			if (estMainComplete.EstHeaderRuleToDelete != null && estMainComplete.EstHeaderRuleToDelete.Any())
			{
				new EstimateRuleHeaderLogic().Delete(estMainComplete.EstHeaderRuleToDelete);
			}

			if (estMainComplete.EstHeaderParamToSave != null && estMainComplete.EstHeaderParamToSave.Any())
			{
				estMainComplete.EstHeaderParamToSave = new EstimateParameterHeaderLogic().SaveItems(estMainComplete.EstHeaderParamToSave);
			}

			if (estMainComplete.EstHeaderParamToDelete != null && estMainComplete.EstHeaderParamToDelete.Any())
			{
				var paramsToBeDeleted = HandleRuleParameterToDelete(estMainComplete, estMainComplete.EstHeaderParamToDelete, (int)EstimateId.EstHeader);

				estMainComplete.EstHeaderParamToDelete = new EstimateParameterHeaderLogic().DeleteItems(paramsToBeDeleted);
			}

			if (estMainComplete.EstProjectParamToDelete != null && estMainComplete.EstProjectParamToDelete.Any())
			{
				new EstimateParameterPrjParamLogic().Delete(estMainComplete.EstProjectParamToDelete);
			}

			if (estMainComplete.EstLineItemsRuleToDelete != null && estMainComplete.EstLineItemsRuleToDelete.Any())
			{
				estMainComplete.EstLineItemsRuleToDelete = new EstimateRuleLineItemLogic().DeleteItems(estMainComplete.EstLineItemsRuleToDelete);
			}

			if (estMainComplete.EstLineItemsParamToDelete != null && estMainComplete.EstLineItemsParamToDelete.Any())
			{
				var paramsToBeDeleted = HandleRuleParameterToDelete(estMainComplete, estMainComplete.EstLineItemsParamToDelete, (int)EstimateId.LineItem);

				estMainComplete.EstLineItemsParamToDelete = new EstimateParameterLineItemLogic().DeleteItems(paramsToBeDeleted);
			}

			if (estMainComplete.EstLineItemsParamToSave != null && estMainComplete.EstLineItemsParamToSave.Any())
			{
				estMainComplete.EstLineItemsParamToSave = new EstimateParameterLineItemLogic().SaveItems(estMainComplete.EstLineItemsParamToSave, estMainComplete.PrjEstRuleToSave);
			}

			if (estMainComplete.EstBoqRuleToSave != null && estMainComplete.EstBoqRuleToSave.Any())
			{
				estMainComplete.EstBoqRuleToSave = new EstimateRuleBoqLogic().Save(estMainComplete.EstBoqRuleToSave, estMainComplete.PrjEstRuleToSave);
			}

			if (estMainComplete.EstBoqRuleToDelete != null && estMainComplete.EstBoqRuleToDelete.Any())
			{
				new EstimateRuleBoqLogic().Delete(estMainComplete.EstBoqRuleToDelete);
			}

			if (estMainComplete.EstBoqParamToSave != null && estMainComplete.EstBoqParamToSave.Any())
			{
				estMainComplete.EstBoqParamToSave = new EstimateParameterBoqLogic().SaveItems(estMainComplete.EstBoqParamToSave);
			}

			if (estMainComplete.EstBoqParamToDelete != null && estMainComplete.EstBoqParamToDelete.Any())
			{
				var paramsToBeDeleted = HandleRuleParameterToDelete(estMainComplete, estMainComplete.EstBoqParamToDelete, (int)StructureId.BoQ);

				estMainComplete.EstBoqParamToDelete = new EstimateParameterBoqLogic().DeleteItems(paramsToBeDeleted);
			}

			if (estMainComplete.EstActivityRuleToDelete != null && estMainComplete.EstActivityRuleToDelete.Any())
			{
				new EstimateRuleActivityLogic().Delete(estMainComplete.EstActivityRuleToDelete);
			}

			if (estMainComplete.EstActivityParamToSave != null && estMainComplete.EstActivityParamToSave.Any())
			{
				estMainComplete.EstActivityParamToSave = new EstimateParameterActivityLogic().SaveItems(estMainComplete.EstActivityParamToSave);
			}

			if (estMainComplete.EstActivityParamToDelete != null && estMainComplete.EstActivityParamToDelete.Any())
			{
				var paramsToBeDeleted = HandleRuleParameterToDelete(estMainComplete, estMainComplete.EstActivityParamToDelete, (int)StructureId.Schedule);

				estMainComplete.EstActivityParamToDelete = new EstimateParameterActivityLogic().DeleteItems(paramsToBeDeleted);
			}

			if (estMainComplete.EstAssemblyCatRuleToDelete != null && estMainComplete.EstAssemblyCatRuleToDelete.Any())
			{
				new EstimateRuleAssemblyCatLogic().Delete(estMainComplete.EstAssemblyCatRuleToDelete);
			}

			if (estMainComplete.EstAssemblyCatParamToSave != null && estMainComplete.EstAssemblyCatParamToSave.Any())
			{
				estMainComplete.EstAssemblyCatParamToSave = new EstimateParameterAssemblyCatLogic().SaveItems(estMainComplete.EstAssemblyCatParamToSave);
			}

			if (estMainComplete.EstAssemblyCatParamToDelete != null && estMainComplete.EstAssemblyCatParamToDelete.Any())
			{
				var paramsToBeDeleted = HandleRuleParameterToDelete(estMainComplete, estMainComplete.EstAssemblyCatParamToDelete, (int)StructureId.AssemblyStructure);

				estMainComplete.EstAssemblyCatParamToDelete = new EstimateParameterAssemblyCatLogic().DeleteItems(paramsToBeDeleted);
			}

			if (estMainComplete.EstCtuRuleToDelete != null && estMainComplete.EstCtuRuleToDelete.Any())
			{
				new EstimateRuleControllingUnitLogic().Delete(estMainComplete.EstCtuRuleToDelete);
			}

			if (estMainComplete.EstCtuParamToSave != null && estMainComplete.EstCtuParamToSave.Any())
			{
				estMainComplete.EstCtuParamToSave = new EstimateParameterControllingUnitLogic().SaveItems(estMainComplete.EstCtuParamToSave);
			}

			if (estMainComplete.EstCtuParamToDelete != null && estMainComplete.EstCtuParamToDelete.Any())
			{
				var paramsToBeDeleted = HandleRuleParameterToDelete(estMainComplete, estMainComplete.EstCtuParamToDelete, (int)StructureId.Controllingunits);

				estMainComplete.EstCtuParamToDelete = new EstimateParameterControllingUnitLogic().DeleteItems(paramsToBeDeleted);
			}

			if (estMainComplete.EstPrcStructureRuleToDelete != null && estMainComplete.EstPrcStructureRuleToDelete.Any())
			{
				new EstimateRulePrcStructureLogic().Delete(estMainComplete.EstPrcStructureRuleToDelete);
			}

			if (estMainComplete.EstPrcStructureParamToSave != null && estMainComplete.EstPrcStructureParamToSave.Any())
			{
				estMainComplete.EstPrcStructureParamToSave = new EstimateParameterPrcStructureLogic().SaveItems(estMainComplete.EstPrcStructureParamToSave);
			}

			if (estMainComplete.EstPrcStructureParamToDelete != null && estMainComplete.EstPrcStructureParamToDelete.Any())
			{
				var paramsToBeDeleted = HandleRuleParameterToDelete(estMainComplete, estMainComplete.EstPrcStructureParamToDelete, (int)StructureId.ProcurementStructure);

				estMainComplete.EstPrcStructureParamToDelete = new EstimateParameterPrcStructureLogic().DeleteItems(paramsToBeDeleted);
			}

			if (estMainComplete.EstPrjLocationRuleToDelete != null && estMainComplete.EstPrjLocationRuleToDelete.Any())
			{
				new EstimateRulePrjLocationLogic().Delete(estMainComplete.EstPrjLocationRuleToDelete);
			}

			if (estMainComplete.EstPrjLocationParamToSave != null && estMainComplete.EstPrjLocationParamToSave.Any())
			{
				estMainComplete.EstPrjLocationParamToSave = new EstimateParameterPrjLocationLogic().SaveItems(estMainComplete.EstPrjLocationParamToSave);
			}

			if (estMainComplete.EstPrjLocationParamToDelete != null && estMainComplete.EstPrjLocationParamToDelete.Any())
			{
				var paramsToBeDeleted = HandleRuleParameterToDelete(estMainComplete, estMainComplete.EstPrjLocationParamToDelete, (int)StructureId.Location);

				estMainComplete.EstPrjLocationParamToDelete = new EstimateParameterPrjLocationLogic().DeleteItems(paramsToBeDeleted);
			}

			if (estMainComplete.EstCostGrpRuleToDelete != null && estMainComplete.EstCostGrpRuleToDelete.Any())
			{
				new EstimateCostGrpRuleLogic().Delete(estMainComplete.EstCostGrpRuleToDelete);
			}

			if (estMainComplete.EstCostGrpParamToSave != null && estMainComplete.EstCostGrpParamToSave.Any())
			{
				estMainComplete.EstCostGrpParamToSave = new EstimateCostGrpParameterLogic().SaveItems(estMainComplete.EstCostGrpParamToSave);
			}

			if (estMainComplete.EstCostGrpParamToDelete != null && estMainComplete.EstCostGrpParamToDelete.Any())
			{
			 
				var paramsToBeDeleted = HandleRuleParameterToDelete(estMainComplete, estMainComplete.EstCostGrpParamToDelete,(int)StructureId.EnterpriseCostGroup);

				estMainComplete.EstCostGrpParamToDelete = new EstimateCostGrpParameterLogic().DeleteItems(paramsToBeDeleted);
			}
		}

		private void SaveParamValueAssignments(EstMainCompleteEntity estMainComplete)
		{
			if (estMainComplete.EstParamValuesToSave != null && estMainComplete.EstParamValuesToSave.Any())
			{
				estMainComplete.EstParamValuesToSave = new EstimateRulePrjEstRuleParamValueLogic().Save(estMainComplete.EstParamValuesToSave);
			}
		}


		/// <summary>
		/// save parameter in the estLineItemParameter container
		/// </summary>
		/// <param name="estLineItemParametersToSave"></param>
		private IEnumerable<EstLineItemParametersEntity> SaveEstLineItemParameters(IEnumerable<EstLineItemParametersEntity> estLineItemParametersToSave)
		{
			var returnParams = new List<IRuleCommonParamEntity>();
			if (estLineItemParametersToSave.Any())
			{
				var paramList = new List<IEstimateRuleCommonParamEntity>();

				var leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.Location).ToList();
				if (leadingStructureParams.Any()) 
				{
					foreach (var item in leadingStructureParams)
                    {
						var locationParam = new EstPrjLocParamEntity();
						SynchronizeProperties(item, locationParam);
						locationParam.PrjLocationFk = item.PrjLocationFk != null && item.PrjLocationFk.HasValue ? item.PrjLocationFk.Value : locationParam.PrjLocationFk;
						paramList.Add(locationParam);
					}
					returnParams.AddRange(new EstimateParameterPrjLocationLogic().SaveOrUpdate(paramList));
				}

				leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.BoQ).ToList();
				if (leadingStructureParams.Any())
				{
					paramList = new List<IEstimateRuleCommonParamEntity>();
					foreach (var item in leadingStructureParams)
					{
						var boqParam = new EstBoqParamEntity();
						SynchronizeProperties(item, boqParam);
						boqParam.BoqHeaderFk = item.BoqHeaderFk;
						boqParam.BoqItemFk = item.BoqItemFk != null && item.BoqItemFk.HasValue ? item.BoqItemFk.Value : boqParam.BoqItemFk;

						paramList.Add(boqParam);
					}
					returnParams.AddRange(new EstimateParameterBoqLogic().SaveOrUpdate(paramList));
				}


				leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.EstHeader).ToList();
				if (leadingStructureParams.Any())
				{
					paramList = new List<IEstimateRuleCommonParamEntity>();
					foreach (var item in leadingStructureParams)
					{
						var estHeaderParam = new EstHeaderParamEntity();
						SynchronizeProperties(item, estHeaderParam);
						estHeaderParam.EstHeaderFk = item.EstHeaderFk;
						paramList.Add(estHeaderParam);
					}
					returnParams.AddRange(new EstimateParameterHeaderLogic().SaveOrUpdate(paramList));
				}

				leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.LineItem).ToList();
				if (leadingStructureParams.Any())
				{
					paramList = new List<IEstimateRuleCommonParamEntity>();
					foreach (var item in leadingStructureParams)
					{
						var lineItemParam = new EstLineItemParamEntity();
						SynchronizeProperties(item, lineItemParam);
						lineItemParam.EstHeaderFk = item.EstHeaderFk;
						lineItemParam.EstLineItemFk = item.EstLineItemFk;
						paramList.Add(lineItemParam);
					}
					returnParams.AddRange(new EstimateParameterLineItemLogic().SaveOrUpdate(paramList));
				}


				leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.AssemblyStructure).ToList();
				if (leadingStructureParams.Any())
				{
					paramList = new List<IEstimateRuleCommonParamEntity>();
					foreach (var item in leadingStructureParams)
					{
						var assemblyParam = new EstAssemblyParamEntity();
						SynchronizeProperties(item, assemblyParam);
						assemblyParam.EstAssemblyCatFk = item.EstAssemblyCatFk != null && item.EstAssemblyCatFk.HasValue ? item.EstAssemblyCatFk.Value : assemblyParam.EstAssemblyCatFk;
						paramList.Add(assemblyParam);
					}
					returnParams.AddRange(new EstimateParameterAssemblyCatLogic().SaveOrUpdate(paramList));
				}

				leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.ProcurementStructure).ToList();
				if (leadingStructureParams.Any())
				{
					paramList = new List<IEstimateRuleCommonParamEntity>();
					foreach (var item in leadingStructureParams)
					{
						var param = new EstPrcStrucParamEntity();
						SynchronizeProperties(item, param);
						param.PrcStructureFk = item.PrcStructureFk != null && item.PrcStructureFk.HasValue ? item.PrcStructureFk.Value : param.PrcStructureFk;
						paramList.Add(param);
					}
					returnParams.AddRange(new EstimateParameterPrcStructureLogic().SaveOrUpdate(paramList));
				}

				leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.Controllingunits).ToList();
				if (leadingStructureParams.Any())
				{
					paramList = new List<IEstimateRuleCommonParamEntity>();
					foreach (var item in leadingStructureParams)
					{
						var param = new EstCtuParamEntity();
						SynchronizeProperties(item, param);
						param.MdcControllingUnitFk = item.MdcControllingUnitFk != null && item.MdcControllingUnitFk.HasValue ? item.MdcControllingUnitFk.Value : param.MdcControllingUnitFk;
						paramList.Add(param);
					}
					returnParams.AddRange(new EstimateParameterControllingUnitLogic().SaveOrUpdate(paramList));
				}

				leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.Project).ToList();
				if (leadingStructureParams.Any())
				{
					paramList = new List<IEstimateRuleCommonParamEntity>();
					foreach (var item in leadingStructureParams)
					{
						var prjParam = new EstPrjParamEntity();
						SynchronizeProperties(item, prjParam);
						prjParam.ProjectEstRuleFk = item.ProjectEstRuleFk != null && item.ProjectEstRuleFk.HasValue ? item.ProjectEstRuleFk.Value : prjParam.ProjectEstRuleFk;
						prjParam.PrjEstRuleFk = item.PrjEstRuleFk;
						prjParam.ProjectFk = item.ProjectFk;
						paramList.Add(prjParam);
					}
					returnParams.AddRange(new EstimateParameterPrjParamLogic().SaveOrUpdate(paramList));
				}


				leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.Schedule).ToList();
				if (leadingStructureParams.Any())
				{
					paramList = new List<IEstimateRuleCommonParamEntity>();
					foreach (var item in leadingStructureParams)
					{
						var scheduleParam = new EstActivityParamEntity();
						SynchronizeProperties(item, scheduleParam);
						scheduleParam.PsdActivityFk = item.PsdActivityFk != null && item.PsdActivityFk.HasValue ? item.PsdActivityFk.Value : scheduleParam.PsdActivityFk;
						paramList.Add(scheduleParam);
					}
					returnParams.AddRange(new EstimateParameterActivityLogic().SaveOrUpdate(paramList));
				}

				leadingStructureParams = estLineItemParametersToSave.Where(e => e.AssignedStructureId == (int)EnumStructure.BasCostGroup ||
											e.AssignedStructureId == (int)EnumStructure.EnterpriseCostGroup ||
											e.AssignedStructureId == (int)EnumStructure.ProjectCostGroup).ToList();
				if (leadingStructureParams.Any())
				{
					paramList = new List<IEstimateRuleCommonParamEntity>();
					foreach (var item in leadingStructureParams)
					{
						var estCostGrpParam = new EstCostGrpParamEntity();
						SynchronizeProperties(item, estCostGrpParam);
						estCostGrpParam.CostGroupCatFk = item.CostGroupCatFk != null && item.CostGroupCatFk.HasValue ? item.CostGroupCatFk.Value : estCostGrpParam.CostGroupCatFk;
						estCostGrpParam.CostGroupFk = item.CostGroupFk != null && item.CostGroupFk.HasValue ? item.CostGroupFk.Value : estCostGrpParam.CostGroupFk;

						paramList.Add(estCostGrpParam);
					}
					returnParams.AddRange(new EstimateCostGrpParameterLogic().SaveOrUpdate(paramList));
				}
			}

			if (returnParams.Any())
			{
				foreach (var item in returnParams)
				{
					var param = estLineItemParametersToSave.FirstOrDefault(e => e.RealyId == item.Id);
					if (param != null)
					{
						param.Version = item.Version;
					}
				}
			}

			return estLineItemParametersToSave;
		}

		private void SynchronizeProperties(EstLineItemParametersEntity estLineItemParametersEntity, IEstimateRuleCommonParamEntity targetEntity)
		{
			targetEntity.ValueDetail = estLineItemParametersEntity.ValueDetail;
			targetEntity.IsLookup = estLineItemParametersEntity.IsLookup;
			targetEntity.DefaultValue = estLineItemParametersEntity.DefaultValue;
			targetEntity.ParameterText = estLineItemParametersEntity.ParameterText;
			targetEntity.UomFk = estLineItemParametersEntity.UomFk;
			targetEntity.ParameterValue = estLineItemParametersEntity.ParameterValue;
			targetEntity.EstRuleParamValueFk = estLineItemParametersEntity.EstRuleParamValueFk;
			targetEntity.Version = estLineItemParametersEntity.Version;
			targetEntity.Id = estLineItemParametersEntity.RealyId != null && estLineItemParametersEntity.RealyId.HasValue ? estLineItemParametersEntity.RealyId.Value : targetEntity.Id;
			targetEntity.EstParameterGroupFk = estLineItemParametersEntity.EstParameterGroupFk;
			targetEntity.Code = estLineItemParametersEntity.Code;
			targetEntity.EstHeaderFk = estLineItemParametersEntity.EstHeaderFk;
			targetEntity.DescriptionInfo = estLineItemParametersEntity.DescriptionInfo;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="estMainComplete"></param>
		public void HandleRuleAndParamAssignments(EstMainCompleteEntity estMainComplete)
		{
			this._EstHeaderId = estMainComplete.EstHeaderId;

			this._ProjectId = estMainComplete.ProjectId;

			estMainComplete.ParamsNotDeleted = new List<string>();

			CollectEntitiesToDelete(estMainComplete);

			DeleteEntitiesGeneratedByRule();

			DeleteRuleAndParamAssignments(estMainComplete);

			SaveParamValueAssignments(estMainComplete);

			//save the change of the LineItem Parameter Container
			if (estMainComplete.EstLineItemParametersToSave != null)
			{
				var estLineItemParametersToSave = estMainComplete.EstLineItemParametersToSave.Where(e => e.EstLineItemParameters != null).Select(e => e.EstLineItemParameters).ToList();
				SaveEstLineItemParameters(estLineItemParametersToSave);
			}

			var resourceGeneratedByRuleOfCurrentLineItem = estMainComplete.MainItemId > 0 ? this._ResourcesGeneratedByRule.Where(e => e.EstLineItemFk == estMainComplete.MainItemId).ToList() : this._ResourcesGeneratedByRule;

			if (estMainComplete.EstResourceToDelete != null)
			{
				estMainComplete.EstResourceToDelete = estMainComplete.EstResourceToDelete.Concat(resourceGeneratedByRuleOfCurrentLineItem);
			}
			else
			{
				estMainComplete.EstResourceToDelete = resourceGeneratedByRuleOfCurrentLineItem;
			}

			if (estMainComplete.EstLineItemToDelete != null)
			{
				estMainComplete.EstLineItemToDelete = estMainComplete.EstLineItemToDelete.Concat(this._LineItemsGeneratedByRule);
			}
			else
			{
				estMainComplete.EstLineItemToDelete = this._LineItemsGeneratedByRule;
			}

			if (this._StructureToAdd.Any())
			{
				this._RuleAssignedTo.AddRange(this._StructureToAdd);

				new EstimateMainStructureDetailLogic().SetEstStructDetail(estMainComplete.EstHeaderId, this._StructureToAdd);
			}

			/* recalculate parameters and update */
			if (this._ParamToUpdate.Any())
			{
				new EstimateMainCalculator().UpdateParameters(this._ParamToUpdate, ref estMainComplete);
			}

			/* get rule's parameter */
			if (this._RuleAssignedTo.Any() && estMainComplete.PrjEstRuleToSave.Any())
			{
				estMainComplete.FormulaParameterEntities = new EstimateMainRuleParamHelper(estMainComplete.EstHeaderId, estMainComplete.ProjectId).GetPrjEstRuleParamToAssign(estMainComplete, this._RuleAssignedTo.ToArray());

				new EstimateRuleParameterLogic(estMainComplete.EstHeaderId, estMainComplete.ProjectId).SetParamEntities(estMainComplete, this._RuleAssignedTo.ToArray());
			}

			if (estMainComplete.EstRuleCompleteToSave != null && estMainComplete.EstRuleCompleteToSave.Any())
			{
				new EstimateRulePrjEstRuleLogic().SaveComplete(estMainComplete.EstRuleCompleteToSave);
			}

			if (this._LineItemIdsToUpdate != null && this._LineItemIdsToUpdate.Any())
			{
				var lineItemIds = this._LineItemIdsToUpdate.Distinct().ToList();

				var lineItems = new EstimateMainLineItemLogic().GetLineItemByIdsAndHeader(lineItemIds, estMainComplete.EstHeaderId).OfType<EstLineItemEntity>().ToList();

				var resources = new EstimateMainResourceLogic().GetResourcesOfLineItems(lineItems, this._ProjectId, false, true);

				using (var estimateScope = new EstimateScope(estMainComplete.EstHeaderId, estMainComplete.ProjectId))
				{
					EstLineItemUtilities.AttachResourcesToLineItems(lineItems, resources);

					var calculationContext = EstimateCalculatorService.Calculate(new EstimateCalculationOption(estMainComplete.EstHeaderId, estMainComplete.ProjectId)
					{
						IsAssembly = false,
						CheckModification = true
					}, lineItems);

					calculationContext.Save(true, true);

					/* filte the lineItem, just return the lineItem in current page */
					var lineItemsOfCurrentPage = lineItems;

					if (estMainComplete.LineItemIds != null && estMainComplete.LineItemIds.Any())
					{
						var lineItemIdsHash = new HashSet<int>(estMainComplete.LineItemIds);

						lineItemsOfCurrentPage = lineItems.Where(e => lineItemIdsHash.Contains(e.Id)).ToList();
					}

					EstimateUpdateLogic.ConcatRefLineItemsIntoCompleteEntity(estMainComplete, lineItemsOfCurrentPage);

					/* mark the userDefinedcolVal of lineItem as modified */
					var lineItemUserDefinedcolVals = new List<UserDefinedcolValEntity>();

					foreach (var lineItem in lineItems)
					{
						if (lineItem.UserDefinedcolValEntity == null) { continue; }

						lineItemUserDefinedcolVals.Add(lineItem.UserDefinedcolValEntity as UserDefinedcolValEntity);
					}

					estMainComplete.UserDefinedcolsOfLineItemModified = lineItemUserDefinedcolVals;

					//clear the resource and UDP attach to lineItem
					foreach (var lineItem in lineItems)
					{
						lineItem.EstResourceEntities = null;
						lineItem.Resources = null;
						lineItem.UserDefinedcolValEntity = null;
						lineItem.Characteristic1List = null;
						lineItem.Characteristic2List = null;
					}
				}
			}
		}

		private IEnumerable<T> HandleRuleParameterToDelete<T>(EstMainCompleteEntity estMainComplete, IEnumerable<T> ruleParameterToDelete, int structureType) where T : IRuleCommonParamEntity
		{
			var ruleParams = ruleParameterToDelete.Select(e => e as IRuleCommonParamEntity).ToList();

			var paramsInUse = new EstimateRuleParameterLogic(estMainComplete.EstHeaderId, estMainComplete.ProjectId).TryGetParamsInUse(estMainComplete.EstHeaderId, structureType, estMainComplete.ProjectId, estMainComplete.EstLeadingStuctureContext, ruleParams,out var lineItems).ToList();

			IEnumerable<T> paramsToBeDeleted = ruleParameterToDelete;

			if (paramsInUse != null && paramsInUse.Any())
			{
				var paramsInUseIds = paramsInUse.Select(e => e.Id).ToList();
				paramsToBeDeleted = ruleParameterToDelete.Where(e => !paramsInUseIds.Contains(e.Id)).ToList();

				estMainComplete.ParamsNotDeleted.AddRange(paramsInUse.Select(e => e.Code).ToList());
			}

			return paramsToBeDeleted;
		}

		/// <summary>
		/// Save RuleParam to the selected Assigned item from client
		/// </summary>
		/// <param name="completeEntity"></param>
		/// <returns></returns>
		public object SaveRuleParam(EstMainCompleteEntity completeEntity)
		{
			return new EstimateParameterCommonLogic().SaveRuleParam(new EstimateParamUpdateData()
			{
				RuleParamSaveTo = completeEntity.RuleParamSaveTo,
				EstHeaderId = completeEntity.EstHeaderId,
				ProjectId = completeEntity.ProjectId,
				PrjEstRuleParamAssignedTo = completeEntity.PrjEstRuleParamAssignedTo
			});
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ruleAssignmentType"></param>
		/// <param name="ruleAssignmentIds"></param>
		/// <returns></returns>
		public IEnumerable<int> DeleteLineItemAndResourceGeneratedByRule(int ruleAssignmentType, IEnumerable<int> ruleAssignmentIds)
		{
			try
			{
				var estRuleSourceEntities = new EstRuleResourceLogic().GetByRuleFks((LeadingStructure2RuleEnum)ruleAssignmentType, ruleAssignmentIds);

				var ruleSourceIds = estRuleSourceEntities.Select(e => e.Id).ToList();

				//delete resources generated by rule
				var resourceGeneratedByRule = new EstimateMainResourceLogic().Get(r => r.EstRuleSourceFk.HasValue && ruleSourceIds.Contains(r.EstRuleSourceFk.Value));

				if (resourceGeneratedByRule != null && resourceGeneratedByRule.Any())
				{
					new EstimateMainResourceLogic().Delete(resourceGeneratedByRule);
				}

				//delete lineitems generated by rule
				var lineItemGeneratedByRule = new EstimateMainLineItemLogic().Get(e => e.EstRuleSourceFk.HasValue && ruleSourceIds.Contains(e.EstRuleSourceFk.Value));

				if (lineItemGeneratedByRule != null && lineItemGeneratedByRule.Any())
				{
					new EstimateMainLineItemLogic().Delete(lineItemGeneratedByRule);
				}

				new EstRuleResourceLogic().Delete(estRuleSourceEntities);

				return ruleSourceIds;
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException("DeleteLineItemAndResourceGeneratedByRule failed..." + ex.Message, ex);
			}

		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="estHeaderFks"></param>
		public void SaveEstimateRulesAndParameters(int projectId, IEnumerable<int> estHeaderFks)
		{
			var masterDataContextId = EstimateContext.MasterDataContextId;
			var basicsCustomizeEstConfigTypeLogic = new RIB.Visual.Basics.Customize.BusinessComponents.BasicsCustomizeEstConfigTypeLogic();
			var configTypesByContext = basicsCustomizeEstConfigTypeLogic.GetByFilter(e => e.ContextFk == masterDataContextId).ToList();
			var configTypeDefault = configTypesByContext.Where(e => e.IsDefault && e.IsLive).OrderBy(e => e.Id).FirstOrDefault();

			if (configTypeDefault == null)
			{
				return;
			}

			var estConfigLogic = new EstConfigLogic();
			var configDefault = estConfigLogic.GetById(new Platform.Core.IdentificationData() { Id = configTypeDefault.ConfigFk });

			if (configDefault == null)
			{
				return;
			}

			if (!configDefault.EstRootAssignmentTypeFk.HasValue)
			{
				return;
			}

			var estimateMainRootAssignmentTypeLogic = new EstimateMainRootAssignmentTypeLogic();
			var rootAssignmentTypes = estimateMainRootAssignmentTypeLogic.Get(e => e.Id == configDefault.EstRootAssignmentTypeFk.Value).ToList();
			if (!rootAssignmentTypes.Any())
			{
				return;
			}

			var assignmentType = rootAssignmentTypes.FirstOrDefault();
			var complete = estimateMainRootAssignmentTypeLogic.GetCompleteById(assignmentType.Id);
			if (!complete.EstRootAssignmentDetailEntities.Any())
			{
				return;
			}

			var estimateRuleLogic = new EstimateRuleLogic();
			var estimateRuleScriptLogic = new EstimateRuleScriptLogic();

			var prjRuleLogic = new EstimateRulePrjEstRuleLogic();
			var prjRuleParamLogic = new EstimateRulePrjEstRuleParamLogic();

			var headerRuleHeaderLogic = new EstimateRuleHeaderLogic();
			var headerParamHeaderLogic = new EstimateParameterHeaderLogic();
			//var header2RulesToSave = new List<EstHeader2EstRuleEntity>();

			var prjEstRuleParamValueLogic = new EstimateRulePrjEstRuleParamValueLogic();

			var prjRuleEntityDic = prjRuleLogic.GetListEnhance(projectId).ToList();

			var prjRuleFks = prjRuleEntityDic.Select(e => e.Id).ToArray();

			var prjRuleParamsDic = prjRuleParamLogic.GetListByRuleIds(prjRuleFks).ToList();

			foreach (var estHeaderId in estHeaderFks)
			{
				foreach (var estRootAssignDetail in complete.EstRootAssignmentDetailEntities)
				{
					//A. Condition: Only Process Rules which are in Rule MasterDataFilter
					var masterDataRules = getAllMasterDataFilterRulesIds(projectId);
					if (masterDataRules.Any() && !masterDataRules.ContainsKey(estRootAssignDetail.EstRuleFk))
					{
						continue;
					}

					var masterRule = estimateRuleLogic.GetById(estRootAssignDetail.EstRuleFk);

					//1. Check If Rule already exists in Project Rule
					#region 1 Check If Rule already exists in Project Rule
					var prjRuleEntity = prjRuleEntityDic.FirstOrDefault(e => e.Code == masterRule.Code);
					int? prjRuleFk = prjRuleEntity == null ? null : (int?)prjRuleEntity.Id;

					bool isExists = prjRuleEntity != null;

					if (!prjRuleFk.HasValue)
					{
						//Create projectRule
						var newPrjRuleEntity = prjRuleLogic.Create(new PrjEstRuleEntity()
						{
							MdcLineItemContextFk = assignmentType.MdcLineItemContextFk,
							ProjectFk = projectId,

							EstEvaluationSequenceFk = masterRule.EstEvaluationSequenceFk,
							EstRuleExecutionTypeFk = masterRule.EstRuleExecutionTypeFk,
							Comment = masterRule.Comment,
							Remark = masterRule.Remark,
							IsLive = masterRule.IsLive,
							BasRubricCategoryFk = masterRule.BasRubricCategoryFk,
							DescriptionInfo = masterRule.DescriptionInfo,

							IsForBoq = false,
							IsForEstimate = true,

							Operand = masterRule.Operand,
							FormFk = masterRule.FormFk,

							Code = masterRule.Code,
							Icon = masterRule.Icon
						});

						prjRuleLogic.Save(newPrjRuleEntity);

						//Todo cheni:Fixed ALM #134616,The project rule will duplicate if create multiple estimate header and click save once.
						prjRuleEntityDic.Add(newPrjRuleEntity);

						prjRuleFk = newPrjRuleEntity.Id;
					}

					#endregion

					//1.1 Save Rule Scripts 
					#region 1.1 Save Rule Script Of Project Rule

					var prjRuleScriptEntitiesToSave = new List<PrjEstRuleScriptEntity>();

					var estRulePrjEstRuleScriptLogic = new EstimateRulePrjEstRuleScriptLogic();
					var sourcePrjScriptEntities = estRulePrjEstRuleScriptLogic.GetByFilter(e => e.PrjEstRuleFk == prjRuleFk);

					if (!sourcePrjScriptEntities.Any())
					{
						var newPrjEstRuleScript = estRulePrjEstRuleScriptLogic.Create(new Platform.Core.IdentificationData());

						var masterRuleScriptEntities = estimateRuleScriptLogic.GetListByRuleFk(masterRule.Id);
						var masterRuleScriptEntity = new EstRuleScriptEntity();

						if (masterRuleScriptEntities.Any())
						{
							masterRuleScriptEntity = masterRuleScriptEntities.FirstOrDefault();
						}

						newPrjEstRuleScript.PrjEstRuleFk = prjRuleFk.Value;
						newPrjEstRuleScript.ScriptData = masterRuleScriptEntity.ScriptData;
						newPrjEstRuleScript.TestInput = masterRuleScriptEntity.TestInput;
						newPrjEstRuleScript.ValidateScriptData = masterRuleScriptEntity.ValidateScriptData;

						prjRuleScriptEntitiesToSave.Add(newPrjEstRuleScript);
						estRulePrjEstRuleScriptLogic.Save(prjRuleScriptEntitiesToSave);


					}

					#endregion


					if (masterRule.FormFk.HasValue)
					{
						var userFormDataLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IUserFormDataLogic>();
						userFormDataLogic.SaveRuleFormData(RubricConstant.ProjectRules, prjRuleFk.Value, masterRule.FormFk.Value);

					}

					//1.2. Save Rule Parameters here

					//estHeaderParamsSave
					var paramsHeaderToAssignToSave = new List<EstHeaderParamEntity>();

					//prjEstRuleParamsSave
					var paramsProjectToAssignToSave = new List<PrjEstRuleParamEntity>();

					var prjRuleParams = prjRuleParamsDic.Where(e => e.PrjEstRuleFk == prjRuleFk);

					//filter the same code data(dirty data)
					var cacheParam = new List<string>();

					if (prjRuleParams.Any())
					{
						//if rule code already exists in Project Rule
						foreach (var param in prjRuleParams)
						{
							//create est header rule params
							if (!cacheParam.Contains(param.Code))
							{
								var estRuleHeaderParam = headerParamHeaderLogic.Create(new Platform.Core.IdentificationData());

								estRuleHeaderParam.EstHeaderFk = estHeaderId;
								estRuleHeaderParam.ProjectEstRuleFk = prjRuleFk.Value;
								estRuleHeaderParam.PrjEstRuleFk = prjRuleFk.Value;

								estRuleHeaderParam.EstParameterGroupFk = param.EstParameterGroupFk;
								estRuleHeaderParam.Code = param.Code;
								estRuleHeaderParam.DescriptionInfo = param.DescriptionInfo;
								estRuleHeaderParam.Sorting = param.Sorting;
								estRuleHeaderParam.ValueDetail = param.ValueDetail;
								estRuleHeaderParam.UomFk = param.UomFk;
								estRuleHeaderParam.DefaultValue = param.DefaultValue;
								estRuleHeaderParam.ValueType = param.ValueType;
								estRuleHeaderParam.IsLookup = param.IsLookup;
								estRuleHeaderParam.EstRuleParamValueFk = param.EstRuleParamValueFk;
								estRuleHeaderParam.ValueText = param.ValueText;
								estRuleHeaderParam.ParameterText = param.ParameterText;

								estRuleHeaderParam.ParameterValue = estRuleHeaderParam.DefaultValue;
								if (estRuleHeaderParam.ValueType == (int)EnumParamValueType.Decimal2)
								{
									estRuleHeaderParam.ValueDetail = string.IsNullOrEmpty(estRuleHeaderParam.ValueDetail) ? estRuleHeaderParam.DefaultValue.ToString(CultureInfo.CurrentCulture) : estRuleHeaderParam.ValueDetail;
								}

								paramsHeaderToAssignToSave.Add(estRuleHeaderParam);
								cacheParam.Add(param.Code);
							}
						}
					}
					else
					{
						//--a) Get rule params from customization configuration
						var paramsToAssign = complete.EstRootAssignmentParamEntities.Where(e => e.EstRootAssignmentDetailFk == estRootAssignDetail.Id);

						foreach (var param in paramsToAssign)
						{
							if (!cacheParam.Contains(param.Code))
							{
								#region I) estimateHeader Params
								var estRuleHeaderParam = headerParamHeaderLogic.Create(new Platform.Core.IdentificationData());

								estRuleHeaderParam.EstHeaderFk = estHeaderId;
								estRuleHeaderParam.PrjEstRuleFk = prjRuleFk.Value;

								estRuleHeaderParam.EstParameterGroupFk = param.EstParameterGroupFk;
								estRuleHeaderParam.Code = param.Code;
								estRuleHeaderParam.DescriptionInfo = param.DescriptionInfo;
								estRuleHeaderParam.Sorting = param.Sorting;
								estRuleHeaderParam.ValueDetail = param.ValueDetail;
								estRuleHeaderParam.UomFk = param.UomFk;
								estRuleHeaderParam.DefaultValue = param.DefaultValue;
								estRuleHeaderParam.ValueType = param.ValueType;
								estRuleHeaderParam.IsLookup = param.IsLookup;
								estRuleHeaderParam.EstRuleParamValueFk = param.EstRuleParamValueFk;
								estRuleHeaderParam.ValueText = param.ValueText;
								estRuleHeaderParam.ParameterText = param.ParameterText;

								estRuleHeaderParam.ParameterValue = estRuleHeaderParam.DefaultValue;
								if (estRuleHeaderParam.ValueType == (int)EnumParamValueType.Decimal2)
								{
									estRuleHeaderParam.ValueDetail = string.IsNullOrEmpty(estRuleHeaderParam.ValueDetail) ? estRuleHeaderParam.DefaultValue.ToString(CultureInfo.CurrentCulture) : estRuleHeaderParam.ValueDetail;
								}

								paramsHeaderToAssignToSave.Add(estRuleHeaderParam);
								#endregion

								#region II) projectEstimate Params

								var estProjectRuleParam = prjRuleParamLogic.Create(new Platform.Core.IdentificationData());

								estProjectRuleParam.ProjectFk = projectId;
								estProjectRuleParam.EstHeaderFk = estHeaderId;
								estProjectRuleParam.PrjEstRuleFk = prjRuleFk.Value;

								estProjectRuleParam.EstParameterGroupFk = param.EstParameterGroupFk;
								estProjectRuleParam.Code = param.Code;
								estProjectRuleParam.DescriptionInfo = param.DescriptionInfo;
								estProjectRuleParam.Sorting = param.Sorting;
								estProjectRuleParam.ValueDetail = param.ValueDetail;
								estProjectRuleParam.UomFk = param.UomFk;
								estProjectRuleParam.DefaultValue = param.DefaultValue;
								estProjectRuleParam.ValueType = param.ValueType;
								estProjectRuleParam.IsLookup = param.IsLookup;
								estProjectRuleParam.EstRuleParamValueFk = param.EstRuleParamValueFk;
								estProjectRuleParam.ValueText = param.ValueText;
								estProjectRuleParam.ParameterText = param.ParameterText;

								estProjectRuleParam.ParameterValue = estProjectRuleParam.DefaultValue;
								if (estProjectRuleParam.ValueType == (int)EnumParamValueType.Decimal2)
								{
									estProjectRuleParam.ValueDetail = string.IsNullOrEmpty(estProjectRuleParam.ValueDetail) ? estProjectRuleParam.DefaultValue.ToString(CultureInfo.CurrentCulture) : estProjectRuleParam.ValueDetail;
								}

								paramsProjectToAssignToSave.Add(estProjectRuleParam);

								#endregion

								#region III) Create and save project rule parameter Values
								//
								EstRuleParamValueEntity projectParamValue =
										 estRuleHeaderParam.EstRuleParamValueFk.HasValue ?
										 new EstRuleParameterValueHandler(projectId).GetOrCreatePrjRuleParamValue(
											  new EstRuleParamEntity() { EstRuleParamValueFk = estRuleHeaderParam.EstRuleParamValueFk }) :
										 null;

								if (projectParamValue != null)
								{
									prjEstRuleParamValueLogic.CopyRuleParaValue2PrjParaValue(projectId, estProjectRuleParam.Code, assignmentType.MdcLineItemContextFk, (int)estProjectRuleParam.EstRuleParamValueFk);
								}

								#endregion

								cacheParam.Add(param.Code);
							}
						}

					}

					//Save all parameters
					headerParamHeaderLogic.SaveItems(paramsHeaderToAssignToSave);
					if (paramsProjectToAssignToSave.Any())
					{
						prjRuleParamLogic.Save(paramsProjectToAssignToSave);
						prjRuleParamsDic.AddRange(paramsProjectToAssignToSave);
					}

					//2.1 Save Parameter Value here
					//EstRuleParamValueEntity projectParamValue = assemblyParam.EstRuleParamValueFk.HasValue ? this._EstRuleParameterValueHandler.GetOrCreatePrjRuleParamValue(assemblyParam) : null;

					//2. Create and Save Header2Rule entity
					#region 2. Create and Save Header2Rule entity
					var newHeader2RuleEntity = headerRuleHeaderLogic.Create(new Platform.Core.IdentificationData());
					newHeader2RuleEntity.EstHeaderFk = estHeaderId;
					newHeader2RuleEntity.PrjEstRuleFk = prjRuleFk.Value;

					newHeader2RuleEntity.Code = isExists? prjRuleEntity.Code: masterRule.Code;

					newHeader2RuleEntity.EstEvaluationSequenceFk = isExists ? prjRuleEntity.EstEvaluationSequenceFk : masterRule.EstEvaluationSequenceFk;
					//newHeader2RuleEntity.IsExecution = masterRule.IsExecution;
					newHeader2RuleEntity.Comment = isExists ? prjRuleEntity.Comment : masterRule.Comment;
					newHeader2RuleEntity.Operand = isExists ? prjRuleEntity.Operand : masterRule.Operand;

					//header2RulesToSave.Add(newHeader2RuleEntity);
					headerRuleHeaderLogic.Save(newHeader2RuleEntity);
					#endregion
				}
				//headerRuleHeaderLogic.SaveEntities(header2RulesToSave);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		private Dictionary<int, int> getAllMasterDataFilterRulesIds(int projectId)
		{
			IRateBookLogic rateBookLogic = RVPARB.BusinessEnvironment.GetExportedValue<IRateBookLogic>();

			var estRuleParameterValueHandler = new EstRuleParameterValueHandler(projectId);
			var estRuleHandler = new EstRuleAndParamHandler(projectId, estRuleParameterValueHandler);
			var matchMasterFilterRuleIds = rateBookLogic.GetFilteredIdsByProjectAndType(projectId, (int)MasterDataFilterType.EstRule).ToList();
			if (matchMasterFilterRuleIds != null && matchMasterFilterRuleIds.Any())
			{
				//get all rule ids 
				return new EstimateRuleLogic().GetTreeList().Where(e => matchMasterFilterRuleIds.Contains(e.Id)).Flatten(e => e.TheGroupChildren).Select(e => e.Id).Distinct().ToDictionary(e => e, e => e);
			}

			return new Dictionary<int, int>();
		}
	}
}
