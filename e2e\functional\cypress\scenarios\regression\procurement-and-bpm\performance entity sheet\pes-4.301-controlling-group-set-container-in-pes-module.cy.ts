import apiConstantData from "cypress/constantData/apiConstantData";
import { commonLocators, sidebar, app, cnt, tile, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _commonAPI, _procurementContractPage, _projectPage, _saleContractPage, _validate } from "cypress/pages";

const PES_DESC = _common.generateRandomString(4);

let MODAL_PROJECTS;
let ITEM_PARAMETER_1
let CONTROLLING_UNIT_PARAMETERS
let CONTAINERS_CONTROLLING_UNIT
let CONTAINERS_PES, CONTAINERS_ITEM, CONTAINERS_CONTROLLING_GROUP_SET_PES;
let CONTAINER_COLUMNS_PES, CONTAINER_COLUMNS_ITEM, CONTAINER_COLUMNS_CONTROLLING_GROUP_SET_PES, CONTAINER_COLUMNS_REQUISITIONS;

describe("PCM- 4.301 | Controlling group set container in pes module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/pes-4.301-controlling-group-set-container-in-pes-module.json").then((data) => {
            this.data = data;
            MODAL_PROJECTS = this.data.MODAL.PROJECTS
            CONTAINERS_CONTROLLING_UNIT = this.data.CONTAINERS.CONTROLLING_UNIT
            CONTAINERS_PES = this.data.CONTAINERS.PES
            CONTAINER_COLUMNS_PES = this.data.CONTAINER_COLUMNS.PES
            CONTAINERS_ITEM = this.data.CONTAINERS.ITEM
            CONTAINER_COLUMNS_ITEM = this.data.CONTAINER_COLUMNS.ITEM
            CONTAINER_COLUMNS_CONTROLLING_GROUP_SET_PES = this.data.CONTAINER_COLUMNS.CONTROLLING_GROUP_SET_PES
            CONTAINERS_CONTROLLING_GROUP_SET_PES = this.data.CONTAINERS.CONTROLLING_GROUP_SET_PES
            CONTAINER_COLUMNS_REQUISITIONS = this.data.CONTAINER_COLUMNS.REQUISITIONS

            ITEM_PARAMETER_1 = {
                [app.GridCells.MDC_MATERIAL_FK]: CONTAINERS_ITEM.MATERIAL_NO1,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_ITEM.QUANTITY,
            };
            CONTROLLING_UNIT_PARAMETERS = {
                [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNIT.QUANTITY, CONTAINERS_CONTROLLING_UNIT.QUANTITY],
                [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true"]
            }

        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT)
                _common.waitForLoaderToDisappear()
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            })

            _common.waitForLoaderToDisappear();
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        })
    })
    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

    it("TC - API: Create controlling unit", function () {
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_PARAMETERS)
    })

    it("TC - Create new pes record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PES)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_3)
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0)
            _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_PES)
            _common.set_columnAtTop([CONTAINER_COLUMNS_PES.description, CONTAINER_COLUMNS_PES.projectfk, CONTAINER_COLUMNS_PES.projectname, CONTAINER_COLUMNS_PES.code, CONTAINER_COLUMNS_PES.businesspartnerfk], cnt.uuid.HEADERS)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.HEADERS)
        _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        _common.create_newRecord(cnt.uuid.HEADERS)
        _common.select_activeRowInContainer(cnt.uuid.HEADERS)
        cy.log(CONTAINERS_PES.BUSINESS_PARTNER_NAME)
        _common.edit_dropdownCellWithInput(cnt.uuid.HEADERS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PES.BUSINESS_PARTNER_NAME)
        _common.clickOn_activeRowCell(cnt.uuid.HEADERS, app.GridCells.DESCRIPTION)
        cy.wait(1000)
        _common.edit_containerCell(cnt.uuid.HEADERS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, PES_DESC)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.HEADERS)
        _common.saveCellDataToEnv(cnt.uuid.HEADERS, app.GridCells.CODE, "PES_CODE")
        _common.minimizeContainer(cnt.uuid.HEADERS)
    });

    it("TC - Create new item", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('PES_CODE')).pinnedItem();
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 1);
            _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
            _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.quantity, CONTAINER_COLUMNS_ITEM.controllingunitfk, CONTAINER_COLUMNS_ITEM.mdcmaterialfk], cnt.uuid.ITEMS)
            _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        });
        _common.maximizeContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _procurementContractPage.enterRecord_toCreateContractItems(cnt.uuid.ITEMS, ITEM_PARAMETER_1)
        _common.select_activeRowInContainer(cnt.uuid.ITEMS)
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMS, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env("API_CNT_CODE_0"))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.ITEMS)
        _common.saveCellDataToEnv(cnt.uuid.ITEMS, app.GridCells.DESCRIPTION_1, "ITEM-DESC")
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create controlling group set & verify each lookup is working", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.FooterTab.CONTROLLING_GROUP_SET, 2)
        });
        _common.waitForLoaderToDisappear();
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 1);
        _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        _common.search_inSubContainer(cnt.uuid.ITEMS, Cypress.env("ITEM-DESC"))
        _common.select_rowHasValue(cnt.uuid.ITEMS, Cypress.env("ITEM-DESC"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.FooterTab.CONTROLLING_GROUP_SET, 2)
            _common.setup_gridLayout(cnt.uuid.PES_CONTROLLING_GROUP_SET, CONTAINER_COLUMNS_CONTROLLING_GROUP_SET_PES)
            _common.clear_subContainerFilter(cnt.uuid.PES_CONTROLLING_GROUP_SET)
        })
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PES_CONTROLLING_GROUP_SET)
        _common.select_activeRowInContainer(cnt.uuid.PES_CONTROLLING_GROUP_SET)
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTROLLING_GROUP_SET_PES.CONTROLLING_GROUP[0])
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP_DETAIL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTROLLING_GROUP_SET_PES.GROUP_DETAIL[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP, "CODE-1")
        _common.clear_subContainerFilter(cnt.uuid.PES_CONTROLLING_GROUP_SET)
        _common.select_activeRowInContainer(cnt.uuid.PES_CONTROLLING_GROUP_SET)
        _common.create_newRecord(cnt.uuid.PES_CONTROLLING_GROUP_SET)
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTROLLING_GROUP_SET_PES.CONTROLLING_GROUP[1])
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP_DETAIL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTROLLING_GROUP_SET_PES.GROUP_DETAIL[1])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP, "CODE-2")
        cy.REFRESH_CONTAINER
        _common.waitForLoaderToDisappear()
    });

    it("TC - Validate record in controlling group set after refresh", function () {
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('PES_CODE'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0)
        });
        _common.select_rowInSubContainer(cnt.uuid.HEADERS)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 1);
        });
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.FooterTab.CONTROLLING_GROUP_SET);
        });
        _common.select_rowInSubContainer(cnt.uuid.PES_CONTROLLING_GROUP_SET)
        _validate.verify_isRecordPresent(cnt.uuid.PES_CONTROLLING_GROUP_SET, Cypress.env('CODE-1'))
        _validate.verify_isRecordPresent(cnt.uuid.PES_CONTROLLING_GROUP_SET, Cypress.env('CODE-2'))
        _common.select_rowHasValue(cnt.uuid.PES_CONTROLLING_GROUP_SET, Cypress.env('CODE-1'))
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP, CONTAINERS_CONTROLLING_GROUP_SET_PES.CONTROLLING_GROUP[0])
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP_DETAIL, CONTAINERS_CONTROLLING_GROUP_SET_PES.GROUP_DETAIL[0])
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP_DESCRIPTION, CONTAINERS_CONTROLLING_GROUP_SET_PES.CONTROLLING_GROUP_DESCRIPTION[0])
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP_DETAIL_DESCRIPTION, CONTAINERS_CONTROLLING_GROUP_SET_PES.GROUP_DETAIL_DESCRIPTION[0])
        _common.select_rowHasValue(cnt.uuid.PES_CONTROLLING_GROUP_SET, Cypress.env('CODE-2'))
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP, CONTAINERS_CONTROLLING_GROUP_SET_PES.CONTROLLING_GROUP[1])
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.GridCells.CONTROLLING_GROUP_DETAIL, CONTAINERS_CONTROLLING_GROUP_SET_PES.GROUP_DETAIL[1])
    });

    it("TC - Delete controlling group set record & verify record is not present", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 1)
        });
        _common.waitForLoaderToDisappear();
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        _common.search_inSubContainer(cnt.uuid.ITEMS, Cypress.env("ITEM-DESC"))
        _common.select_rowHasValue(cnt.uuid.ITEMS, Cypress.env("ITEM-DESC"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.FooterTab.CONTROLLING_GROUP_SET, 2)
        })
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PES_CONTROLLING_GROUP_SET)
        _common.search_inSubContainer(cnt.uuid.PES_CONTROLLING_GROUP_SET, CONTAINERS_CONTROLLING_GROUP_SET_PES.CONTROLLING_GROUP[1])
        _common.select_rowHasValue(cnt.uuid.PES_CONTROLLING_GROUP_SET, CONTAINERS_CONTROLLING_GROUP_SET_PES.CONTROLLING_GROUP[1])
        _common.waitForLoaderToDisappear()
        _common.clickOn_toolbarButton(cnt.uuid.PES_CONTROLLING_GROUP_SET, btn.IconButtons.ICO_REC_DELETE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0)
        })
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        _common.search_inSubContainer(cnt.uuid.HEADERS, Cypress.env("PES_CODE"))
        _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE"))
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 1)
        });
        _common.waitForLoaderToDisappear();
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        _common.search_inSubContainer(cnt.uuid.ITEMS, Cypress.env("ITEM-DESC"))
        _common.select_rowHasValue(cnt.uuid.ITEMS, Cypress.env("ITEM-DESC"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_CONTROLLING_GROUP_SET, app.FooterTab.CONTROLLING_GROUP_SET, 2)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PES_CONTROLLING_GROUP_SET)
        _validate.verify_recordNotPresentInContainer(cnt.uuid.PES_CONTROLLING_GROUP_SET, CONTAINERS_CONTROLLING_GROUP_SET_PES.CONTROLLING_GROUP[1])
        _common.waitForLoaderToDisappear()

    });

});
