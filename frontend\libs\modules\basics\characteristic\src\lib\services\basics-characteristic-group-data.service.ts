/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, Injectable } from '@angular/core';
import { DataServiceHierarchicalRoot, IDataServiceEndPointOptions, IDataServiceRoleOptions } from '@libs/platform/data-access';
import { ServiceRole } from '@libs/platform/data-access';
import { IDataServiceOptions } from '@libs/platform/data-access';
import { ISearchPayload, ISearchResult } from '@libs/platform/common';
import { IFilterResponse } from '@libs/basics/shared';
import { BasicsCharacteristicGroupComplete } from '../model/basics-characteristic-group-complete.class';
import { ICharacteristicGroupEntity } from '@libs/basics/interfaces';
import { IDialogResult, StandardDialogButtonId, UiCommonMessageBoxService } from '@libs/ui/common';

interface ICharacteristicGroupLoadedEntity {
	CharacteristicGroup: ICharacteristicGroupEntity[];
	FilterResult: IFilterResponse;
}

/**
 * characteristic group entity data service
 */
@Injectable({
	providedIn: 'root',
})
export class BasicsCharacteristicGroupDataService extends DataServiceHierarchicalRoot<ICharacteristicGroupEntity, BasicsCharacteristicGroupComplete> {
	private readonly messageBoxService = inject(UiCommonMessageBoxService);
	public constructor() {
		const options: IDataServiceOptions<ICharacteristicGroupEntity> = {
			apiUrl: 'basics/characteristic/group',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'treefiltered',
				usePost: true,
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create',
				usePost: true,
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'multidelete', //'delete',
			},
			roleInfo: <IDataServiceRoleOptions<ICharacteristicGroupEntity>>{
				role: ServiceRole.Root,
				itemName: 'Groups',
			},
		};

		super(options);
		this.refreshAllLoaded();
	}

	protected override provideLoadByFilterPayload(): object {
		return {};
	}

	protected override onLoadByFilterSucceeded(loaded: ICharacteristicGroupLoadedEntity): ISearchResult<ICharacteristicGroupEntity> {
		const fr = loaded.FilterResult;
		return {
			FilterResult: {
				ExecutionInfo: fr.ExecutionInfo,
				RecordsFound: fr.RecordsFound,
				RecordsRetrieved: fr.RecordsRetrieved,
				ResultIds: fr.ResultIds,
			},
			dtos: loaded.CharacteristicGroup,
		};
	}

	public override createUpdateEntity(modified: ICharacteristicGroupEntity | null): BasicsCharacteristicGroupComplete {
		const complete = new BasicsCharacteristicGroupComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.Groups = [modified];
		}
		return complete;
	}

	public override getModificationsFromUpdate(complete: BasicsCharacteristicGroupComplete): ICharacteristicGroupEntity[] {
		if (complete.Groups === null) {
			return [];
		}

		return complete.Groups;
	}

	protected override checkCreateIsAllowed(entities: ICharacteristicGroupEntity[] | ICharacteristicGroupEntity | null): boolean {
		return entities !== null;
	}

	public override childrenOf(element: ICharacteristicGroupEntity): ICharacteristicGroupEntity[] {
		return element.Groups ?? [];
	}

	public override parentOf(element: ICharacteristicGroupEntity): ICharacteristicGroupEntity | null {
		if (element.CharacteristicGroupFk === undefined) {
			return null;
		}

		const parentId = element.CharacteristicGroupFk;
		const foundParent = this.flatList().find((candidate) => candidate.Id === parentId);

		if (foundParent === undefined) {
			return null;
		}

		return foundParent;
	}

	public override provideCreatePayload() {
		return {};
	}

	public override provideCreateChildPayload() {
		const selectedEntity = this.getSelectedEntity();
		if (selectedEntity) {
			return {
				GroupId: selectedEntity?.Id,
			};
		}
		throw new Error('There should be a selected parent Characteristic Group');
	}

	protected override onCreateSucceeded(created: ICharacteristicGroupEntity): ICharacteristicGroupEntity {
		return created;
	}

	public override delete(entities: ICharacteristicGroupEntity[] | ICharacteristicGroupEntity) {
		this.messageBoxService.deleteSelectionDialog()?.then((dialogResult: IDialogResult) => {
			if (dialogResult.closingButtonId === StandardDialogButtonId.Yes || dialogResult.closingButtonId === StandardDialogButtonId.Ok) {
				super.delete(entities);
			}
		});
	}
	public override supportsSidebarSearch(): boolean {
		return false;
	}

	public override refreshAllLoaded() {
		const searchPayload: ISearchPayload = {
			executionHints: false,
			filter: '',
			includeNonActiveItems: false,
			isReadingDueToRefresh: false,
			pageNumber: 0,
			pageSize: 100,
			pattern: '',
			pinningContext: [],
			projectContextId: null,
			useCurrentClient: true,
		};
		return this.filter(searchPayload).then(function (result) {
			return result.dtos;
		});
	}
	//todo: update function does not work well.
}
