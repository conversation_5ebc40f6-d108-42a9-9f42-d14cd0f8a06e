/**
 * Created by hzh on 5/27/2025.
 */

(function (angular) {
	'use strict';

	const moduleName = 'documents.centralquery';

	angular.module(moduleName).controller('documentsCentralquerySpConfigurationController',
		['_', '$q', 'globals', '$rootScope', '$scope', '$translate', '$http', 'platformGridAPI', 'documentsCentralquerySpConfigurationService', 'documentsProjectDocumentModuleContext',
			'platformGridControllerService', '$timeout', '$injector', 'documentsCentralquerySpSyncFormatterService',
			'platformRuntimeDataService', 'documentsCentralquerySpFolderTemplateContantService', 'platformModalService',
			'cloudDesktopSharePointMainService', 'documentCentralQueryDataService',
			function (_, $q, globals, $rootScope, $scope, $translate, $http, platformGridAPI, spConfigurationService, documentsProjectDocumentModuleContext,
			          gridControllerService, $timeout, $injector, syncButtonFormatter, platformRuntimeDataService, spFolderTemplateContantService,
			          platformModalService, sharepointMainService, documentCentralQueryDataService) {
				$scope.options = $scope.$parent.modalOptions;

				let _grid = null;

				let currentList = [];
				let projectIdsSynced = [];

				const gridColumns = {
					getStandardConfigForListView: function () {
						return {
							columns: [
								{
									id: 'IsCheck',
									field: 'IsSelected',
									name: 'IsSelected',
									name$tr$: 'documents.centralquery.sharepoint.columns.isSelectedTitle',
									formatter: 'boolean',
									editor: 'boolean',
									width: 80,
									headerChkbox: true,
									validator: function (item) {
										if (item.IsProjectSynced) {
											return false;
										}
									}
								},
								{
									id: 'IsProjectSynced',
									field: 'IsProjectSynced',
									name: 'IsProjectSynced',
									name$tr$: 'documents.centralquery.sharepoint.columns.projectSynced',
									width: 40,
									formatter: 'image',
									formatterOptions: {
										imageSelector: {
											isCss: function () {
												return true;
											},
											select: function (item) {
												let icon = 'control-icons ico-not-synced';
												if (item.IsProjectSynced) {
													icon = 'control-icons ico-synced';
												}
												return icon;
											}
										}
									},
									cssClass: 'cell-center',
								},
								{
									id: 'ProjectNo',
									field: 'ProjectNo',
									name: 'ProjectNo',
									name$tr$: 'documents.centralquery.sharepoint.columns.ProjectNo',
									width: 90,
								},
								{
									id: 'ProjectName',
									field: 'ProjectName',
									name: 'ProjectName',
									name$tr$: 'documents.centralquery.sharepoint.columns.projectName',
									width: 250,
								},
								{
									id: 'FolderTemplate',
									field: 'PrjDocSpprjFolderSettingFk',
									name: 'FolderTemplate',
									name$tr$: 'documents.centralquery.sharepoint.columns.folderTemplate',
									editor: 'lookup',
									editorOptions: {
										directive: 'documents-centralquery-sp-folder-template-lookup',
										lookupOptions: {
											showClearButton: false,
											displayMember: 'Name'
										}
									},
									formatter: 'lookup',
									formatterOptions: {
										lookupType: 'SharePointFolderSetting',
										valueMember: 'Id',
										displayMember: 'Name'
									},
									width: 100
								},
								{
									id: 'ProjectDrillDownFolder',
									field: 'PrjDocSpprjFolderSettingFk',
									name: 'ProjectDrillDownFolder',
									name$tr$: 'documents.centralquery.sharepoint.columns.projectDrillDown',
									editor: 'lookup',
									editorOptions: {
										directive: 'documents-centralquery-sp-drill-down-lookup',
										lookupOptions: {
											showClearButton: false,
											displayMember: 'Name'
										}
									},
									formatter: 'lookup',
									formatterOptions: {
										lookupType: 'SharePointFolderSetting',
										valueMember: 'Id',
										displayMember: 'Name'
									},
									width: 140
								},
								{
									id: 'UserAssignment',
									field: 'UserAssignment',
									name: 'UserAssignment',
									name$tr$: 'documents.centralquery.sharepoint.columns.userAssignment',
									width: 120,
									editor: 'lookup',
									editorOptions: {
										directive: 'documents-centralquery-sp-user-dialog',
										lookupOptions: {
											showClearButton: false,
											displayMember: 'UserAssignment'
										}
									},
									formatter: 'lookup',
									formatterOptions: {
										lookupType: 'userAssignmentDialog',
										valueMember: 'Id',
										displayMember: 'UserAssignment',
									}
								},
								{
									id: 'IsAutoSync',
									field: 'IsAutoSync',
									name: 'IsAutoSync',
									name$tr$: 'documents.centralquery.sharepoint.columns.autoSyncDoc',
									width: 130,
									formatter: syncButtonFormatter,
									formatterOptions: {
										domainType: 'syncSwitch',
									}
								},
								{
									id: 'Sync2SharePoint',
									field: 'Sync2SharePoint',
									name: 'Sync2SharePoint',
									name$tr$: 'documents.centralquery.sharepoint.columns.sync2SharePoint',
									width: 120,
									formatter: syncButtonFormatter,
									formatterOptions: {
										domainType: 'syncButton',
										domainText: $translate.instant('documents.centralquery.sharepoint.columns.synchronizeBtnText')
									}
								}
							]
						};
					}
				};


				$scope.globalMetaDataTypes = [];
				$scope.searchText = '';

				$scope.modalOptions = {
					header: {
						title: $translate.instant('documents.centralquery.sharepoint.title')
					},
					body: {
						projectNameText: $translate.instant('documents.centralquery.sharepoint.projectNo'),
						btnSearchText: $translate.instant('documents.centralquery.sharepoint.btnSearchText'),  // "Search",//
						filterStatusText: $translate.instant('documents.centralquery.sharepoint.columns.status'),  // "Status", //
					},
					footer: {
						btnSynchronize: $translate.instant('documents.centralquery.sharepoint.btnSynchronizeText'),
						btnCancel: $translate.instant('basics.common.button.cancel')
					},
				};

				$scope.metaDataType = {
					options: {
						multipleSelection: true,
						showFilterRow: false
					}
				};

				let page = {
					enabled: true,
					size: 200,
					number: 0,
					count: 0,
					currentLength: 0,
					totalLength: 0

				};

				$scope.getPageText = function () {
					let startIndex = page.number * page.size,
						endIndex = ((page.count - (page.number + 1) > 0 ? startIndex + page.size : page.totalLength));
					if (page.totalLength > 0) {
						return (startIndex + 1) + ' - ' + endIndex + ' / ' + (page.hasMore ? page.totalLength + '+' : page.totalLength);
					}
					return startIndex + ' - ' + endIndex + ' / ' + (page.hasMore ? page.totalLength + '+' : page.totalLength);
				};
				$scope.getFirstPage = function () {
					page.number = 0;
					getSearchList();
				};
				$scope.getLastPage = function () {
					page.number = page.count - 1;
					getSearchList();
				};
				$scope.getPrevPage = function () {
					if (page.number <= 0) {
						return;
					}
					page.number--;
					getSearchList();
				};
				$scope.getNextPage = function () {
					if (page.count <= page.number) {
						return;
					}
					page.number++;
					getSearchList();
				};

				$scope.canFirstPage = $scope.canPrevPage = function () {
					return page.number > 0;
				};
				$scope.canLastPage = $scope.canNextPage = function () {
					return page.count > (page.number + 1);
				};

				function getSearchList() {
					let startIndex = page.number * page.size,
						endIndex = ((page.count - (page.number + 1) > 0 ? startIndex + page.size : page.totalLength));

					let data = currentList.slice(startIndex, endIndex);
					scopeGrid.dataView.setItems(data);
				}

				// set data to grid
				function setDataSource() {
					$scope.isLoading = true;
					spConfigurationService.getProjectConfigurationList()
						.then(function (data) {
							$scope.isLoading = false;
							data = data ?? [];
							spConfigurationService.setDataList(data);

							currentList = data;

							data.forEach(function (item) {
								if (item.IsProjectSynced) {
									setFieldReadonly(item, 'IsSelected', true);
								}
							});

							getDataSource(data);

							let newText = $scope.searchText;
							if (newText) {
								currentList = _.filter(data, function (item) {
									if (item.ProjectNo.indexOf(newText) > -1 || item.ProjectName?.indexOf(newText) > -1) {
										return true;
									}
								});
							}

							page.totalLength = currentList.length;
							let number = Math.ceil(page.totalLength / page.size);
							page.count = number;

							if (page.number >= number) {
								page.number = number >= 1 ? number - 1 : 0;
							}

							getSearchList();

						}, function () {
							$scope.isLoading = false;
						});
				}

				function getDataSource() {
					spConfigurationService.refreshGrid();
					$scope.onContentResized();
				}

				const unWatch = $scope.$watch('searchText', function (newText, oldText) {
					if (newText !== oldText) {
						if (newText === '') {
							currentList = spConfigurationService.getList();
						} else {
							currentList = _.filter(spConfigurationService.getList(), function (item) {
								if (item.ProjectNo.indexOf(newText) > -1 || item.ProjectName?.indexOf(newText) > -1) {
									return true;
								}
							});
						}

						page.totalLength = currentList.length;
						page.count = Math.ceil(page.totalLength / page.size);

						if (page.number >= page.count) {
							let number = page.count - 1;
							page.number = number >= 0 ? number : 0;
						}

						scopeGrid.dataView.setItems(currentList);
					}
				});

				$scope.modalOptions.ok = function ok() {
					$scope.isLoading = true;

					const needSyncProjects = _.filter(spConfigurationService.getList(), function (item) {
						if (item.IsSelected && !item.IsProjectSynced) {
							return true;
						}
					});

					if (needSyncProjects?.length === 0) {
						$scope.isLoading = false;
						return;
					}

					syncProjectsToSharePoint(needSyncProjects).then().finally(() => {
						$scope.isLoading = false;
					});
				};

				$scope.modalOptions.cancel = function onCancel() {
					$scope.$close(false);
				};

				const gridConfig = {
					initCalled: false,
					columns: []
				};

				$scope.setTools = function () {
				};

				$scope.tools = {
					update: () => {
					}
				};

				$scope.data = [];
				$scope.gridId = '07da5e0eeb294bbda898751dea3c1650';

				$scope.onContentResized = function () {
					resizeGrid();
				};

				function ShowDialog(bodyText, showYes, showNo, headerText) {
					let modalOptions = {
						headerTextKey: headerText ?? $translate.instant('documents.centralquery.sharepoint.title'),
						bodyTextKey: bodyText,
						showYesButton: showYes ?? false,
						showNoButton: showNo ?? false,
						iconClass: 'ico-warning'
					};

					return platformModalService.showDialog(modalOptions);
				}

				function syncProjectsToSharePoint(needSyncProjects) {
					let notDrillDownConfig = _.find(needSyncProjects, {HasDrillDown: false});
					if (notDrillDownConfig) {
						let noDrillDownAbortSyncInfo = $translate.instant('documents.centralquery.sharepoint.noDrillDownAbortSyncInfo');
						return ShowDialog(noDrillDownAbortSyncInfo.replace('{0}', notDrillDownConfig.ProjectNo));
					}

					let prjIds = _.map(needSyncProjects, item => item.PrjProjectFk);

					return $http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/getfoldercountbyprjs', prjIds)
						.then(function (response) {
							let data = response.data;
							let folderCount = _.sumBy(data, item => item.Value);
							if (folderCount >= 500) {
								let folderMoreThan500Info = $translate.instant('documents.centralquery.sharepoint.folderMoreThan500Info');
								return ShowDialog(folderMoreThan500Info);
							} else if (folderCount > 200) {
								let folderMoreThan200Info = $translate.instant('documents.centralquery.sharepoint.folderMoreThan200Info');
								return ShowDialog(folderMoreThan200Info, true, true)
									.then(function (result) {
										if (result.yes) {
											return sync(needSyncProjects);
										}
									});

							} else {
								return sync(needSyncProjects);
							}

							function sync(needSyncProjects) {
								return sharepointMainService.msalClient.acquireTokenAsync().then(function (r) {
									if (!r) {
										return r;
									}

									return sharepointMainService.readProfile().then(function (response) {
										let request = {
											Dtos: needSyncProjects,
											AadUserId: response.id,
											AccessToken: r
										};

										return spConfigurationService.syncProjectsToSharePoint(request)
											.then(function (data) {
												if (data) {
													_.forEach(request.Dtos, function (item) {
														let syncedProject = _.find(data, {ProjectId: item.PrjProjectFk});
														if (syncedProject?.IsSynced) {
															item.IsProjectSynced = syncedProject.IsSynced;
															item.IsSelected = false;
															projectIdsSynced.push(item.PrjProjectFk);
															setFieldReadonly(item, 'IsSelected', true);
														}
													});

													if (_grid) {
														refreshGrid(_grid);
													}
												}
											});
									});
								});
							}
						});

				}

				function resizeGrid() {
					$timeout(function () {
						platformGridAPI.grids.resize($scope.gridId);
					});
				}

				if (platformGridAPI.grids.exist($scope.gridId)) {
					platformGridAPI.grids.unregister($scope.gridId);
				}

				platformGridAPI.events.register($scope.gridId, 'onHeaderCheckboxChanged', onGridCheckboxClickedFuc);
				platformGridAPI.events.register($scope.gridId, 'onCellChange', onGridCheckboxClickedFuc);
				platformGridAPI.events.register($scope.gridId, 'onClick', onGridClickedFunc);

				spConfigurationService.refreshData.register(setDataSource);

				gridControllerService.initListController($scope, gridColumns, spConfigurationService, null, gridConfig);

				const scopeGrid = platformGridAPI.grids.element('id', $scope.gridId);


				function onGridClickedFunc(e, args) {
					_grid = args.grid;
					const columnIndex = args.cell;
					const rowIndex = args.row;
					let selection = _grid.getDataItem(rowIndex);

					spConfigurationService.setSelected(selection);

					const cell = _grid.getColumns()[columnIndex];
					switch (cell.id) {
						case 'IsAutoSync':
							if (!selection.PrjDocSpprjFolderSettingFk) {
								return;
							}

							const isAutoSync = !selection.IsAutoSync;
							spConfigurationService.updateIsAutoSync(selection, isAutoSync).then(function (data) {
								if (data) {
									selection.IsAutoSync = isAutoSync;
									refreshGrid(_grid);
								}
							});

							break;

						case 'Sync2SharePoint':
							if (!selection.IsProjectSynced) {
								$scope.isLoading = true;
								syncProjectsToSharePoint([selection]).then().finally(() => {
									$scope.isLoading = false;
								});
							}
							break;

						case 'FolderTemplate':
							setFieldReadonly(selection, 'PrjDocSpprjFolderSettingFk', false);
							break;

						case 'ProjectDrillDownFolder':
						case 'UserAssignment':
							let isReadonly = false;

							if (!selection.PrjDocSpprjFolderSettingFk) {
								isReadonly = true;
							}

							setFieldReadonly(selection, 'PrjDocSpprjFolderSettingFk', isReadonly);
							setFieldReadonly(selection, 'UserAssignment', isReadonly);

							break;
					}
				}

				function setFieldReadonly(selection, field, isReadonly) {
					platformRuntimeDataService.readonly(selection, [{field: field, readonly: isReadonly}]);
				}

				function onGridCheckboxClickedFuc(e, args) {
					_grid = args.grid;

					refresh($scope);
				}

				function refresh(scope) {
					const curScope = scope || $scope;
					if (!$rootScope.$$phase) {
						curScope.$digest();
					} else {
						curScope.$evalAsync();
					}
				}

				// resize the grid
				$timeout(function () { // use timeout to do after the grid instance is finished
					platformGridAPI.grids.resize($scope.gridId);
				});

				$scope.$on('$destroy', function () {
					$scope.isLoading = false;

					platformGridAPI.grids.unregister($scope.gridId);

					spConfigurationService.setDataList([]);

					spConfigurationService.refreshData.unregister(setDataSource);
					unWatch();
					if (projectIdsSynced && projectIdsSynced.length > 0) {
						let documents = documentCentralQueryDataService.getList();
						let hasDocumentsToRefresh = documents.some(e => {
							return _.includes(projectIdsSynced, e.PrjProjectFk);
						});
						if (hasDocumentsToRefresh) {
							documentCentralQueryDataService.refresh();
						}
					}
				});

				function getGlobalMetaData() {
					$http.get(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/getglobalmetadata')
						.then(function (response) {
							_.forEach(response.data, function (item) {
								let metaData = _.filter(spFolderTemplateContantService.getGlobalMetaDataTypes(), {Id: item.MetadataType})[0];
								if (metaData) {
									$scope.globalMetaDataTypes.push(metaData.Id);
								}
							});
						});
				}


				function init() {
					setDataSource();
					spConfigurationService.getAadUsers();
					getGlobalMetaData();
				}

				init();

				function refreshGrid(grid) {
					grid.invalidateAllRows();
					grid.render();
				}
			}]);
})(angular);
