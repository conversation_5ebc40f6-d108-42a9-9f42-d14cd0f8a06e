import { app, btn, cnt, sidebar,commonLocators, tile } from "cypress/locators";
import Buttons from "cypress/locators/buttons";
import CommonLocators from "cypress/locators/common-locators";
import Sidebar from "cypress/locators/sidebar";
import {_projectPage,_saleContractPage, _common,_validate,_mainView,_procurementContractPage, _sidebar} from "cypress/pages";
import type { DataCells } from 'cypress/pages/interfaces.d.ts'

const allure = Cypress.Allure.reporter.getInterface();

const ITEM_DESC = "ITEM-DESC-" + Cypress._.random(0, 999);
const CHANGE_PROJECT_DESC = "ITEM-DESC-" + Cypress._.random(0, 999);
const PROJECT_NO=_common.generateRandomString(2)
const PROJECT_DESC="PRDESC-" + Cypress._.random(0, 999);

let CONTRACT_PARAMETER:DataCells;
let CONTAINERS_CONTRACT;
let CONTAINERS_ITEM;
let PROJECTS_PARAMETERS
let CONTAINERS_CONTRACT_CONTACT

let CONTAINER_COLUMNS_ITEM;
let CONTAINER_COLUMNS_PROCUREMENTCONTRACT,CONTAINERS_MATERIAL_CATALOGS,CONTAINERS_MATERIAL_RECORDS,CONTAINER_COLUMNS_MATERIAL_RECORDS,
CONTAINERS_CURRENCY

allure.epic("PROCUREMENT AND BPM");
allure.feature("Contract");
allure.story("PCM- 4.132 | Contacts container in Contract module");

describe('PCM- 4.132 | Contacts container in Contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('pcm/con-4.132-contacts-container-in-contract-module.json').then((data) => {
            this.data = data;
           
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
            CONTAINERS_CONTRACT_CONTACT = this.data.CONTAINERS.CONTACT;
            CONTAINERS_ITEM = this.data.CONTAINERS.ITEM;       
            CONTAINERS_CURRENCY= this.data.CONTAINERS.CURRENCY
            CONTAINER_COLUMNS_ITEM = this.data.CONTAINER_COLUMNS.ITEM
            CONTAINER_COLUMNS_PROCUREMENTCONTRACT = this.data.CONTAINER_COLUMNS.PROCUREMENTCONTRACT
            CONTAINERS_MATERIAL_CATALOGS=this.data.CONTAINERS.MATERIAL_CATALOGS
            CONTAINERS_MATERIAL_RECORDS=this.data.CONTAINERS.MATERIAL_RECORDS
            CONTAINER_COLUMNS_MATERIAL_RECORDS=this.data.CONTAINER_COLUMNS.MATERIAL_RECORDS
            CONTRACT_PARAMETER = {
              [commonLocators.CommonLabels.CONFIGURATION]:CONTAINERS_CONTRACT.CONFIGURATION,
              [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESSPARTNER

          };
          PROJECTS_PARAMETERS={
            [commonLocators.CommonLabels.PROJECT_NUMBER]:PROJECT_NO,
            [commonLocators.CommonLabels.NAME]:PROJECT_DESC,
            [commonLocators.CommonLabels.CLERK]:"SmiJ"
        }
        });
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
       
     });
  
    after(() => {
        cy.LOGOUT();
    });


    it('TC - Create project', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT); 
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
        _common.setDefaultView(app.TabBar.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
        _common.create_newRecord(cnt.uuid.PROJECTS);
        _projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS);
        cy.SAVE(); 
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO).pinnedItem();
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
    
    })
    it("TC - Create new contract record ", function () {
        _common.openTab(app.TabBar.CONTRACT).then(()=>{
        _common.setDefaultView(app.TabBar.CONTRACT)
        _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT,app.FooterTab.CONTRACTS,0)
        })
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO)
        _common.waitForLoaderToDisappear()

        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT,0)
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "Contract_Code")
    })
    it("TC - Create new  Contact in contract AC1- AC3-AC4 ", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONTRACT_CONTACT, app.FooterTab.CONTACTS, 2);
			_common.waitForLoaderToDisappear()
		});

		_common.create_newRecord(cnt.uuid.CONTRACT_CONTACT);
		_common.select_rowInContainer(cnt.uuid.CONTRACT_CONTACT);
		_common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CONTACT, app.GridCells.BPD_CONTACT_FIRST_NAME,commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CONTACT.ANTON)
        cy.wait(1000)//NEED TO LOAD
		cy.SAVE();
		_common.waitForLoaderToDisappear()
        cy.SAVE();
		_common.waitForLoaderToDisappear()
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_CONTACT, app.GridCells.BPD_CONTACT_FIRST_NAME,CONTAINERS_CONTRACT_CONTACT.ANTON)
	});
    it("TC - Delete  Contact in contract AC2", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONTRACT_CONTACT, app.FooterTab.CONTACTS, 2);
			_common.waitForLoaderToDisappear()	
		});

		_common.select_rowInContainer(cnt.uuid.CONTRACT_CONTACT);
		_common.waitForLoaderToDisappear()
        _common.delete_recordFromContainer(cnt.uuid.CONTRACT_CONTACT)
        cy.SAVE();
        _common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.waitForLoaderToDisappear()
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordDeleted(cnt.uuid.CONTRACT_CONTACT, CONTAINERS_CONTRACT_CONTACT.ANTON)
	});
    it("TC - Add Contact in contract", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONTRACT_CONTACT, app.FooterTab.CONTACTS, 2);
			_common.waitForLoaderToDisappear()	
		});

		_common.create_newRecord(cnt.uuid.CONTRACT_CONTACT);
		_common.select_rowInContainer(cnt.uuid.CONTRACT_CONTACT);
		_common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CONTACT, app.GridCells.BPD_CONTACT_FIRST_NAME,commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CONTACT.ANTON)
		cy.SAVE();
		_common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_CONTACT, app.GridCells.BPD_CONTACT_FIRST_NAME,CONTAINERS_CONTRACT_CONTACT.ANTON);
	});
    it("TC - Create Items for Contract",function(){
    
        _common.openTab(app.TabBar.ORDER_ITEM).then(()=>{
        cy.wait(1000)//NEED WAIT TO SEARCH CONTAINER
        _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT,app.FooterTab.ITEMS,0)
        _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT,CONTAINER_COLUMNS_ITEM)
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        })
        _common.maximizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        _procurementContractPage.enterRecord_toCreateItem(cnt.uuid.ITEMSCONTRACT,CONTAINERS_ITEM.MATERIAL_NO,ITEM_DESC,CONTAINERS_CONTRACT_CONTACT.QUANTITY)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
		_common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
    })
    it("TC - Create Requisition Contract termination from wizard-AC5", function () {
            _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
            })
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
            _common.search_fromSidebar(CommonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CONTRACT_TERMINATION);
            _saleContractPage.contractTermination_FromWizard(CommonLocators.CommonKeys.NEW_REQUISITION, 0,PROJECT_NO, CHANGE_PROJECT_DESC, CommonLocators.CommonKeys.DESIGN_CHANGE, CommonLocators.CommonKeys.CHANGE_REQUEST)
            _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_REQUISITION)
            cy.wait(2000)


    })
    it("TC - Verify the contact in requisition", function () {
          _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
          _common.clear_searchInSidebar()
          _common.waitForLoaderToDisappear()
            
          _common.openTab(app.TabBar.MAIN).then(() => {
            cy.wait(1000)
            _common.select_tabFromFooter(cnt.uuid.REQUISITIONS, app.FooterTab.REQUISITIONS, 0);
            _common.clear_subContainerFilter(cnt.uuid.REQUISITIONS)
           })
           _common.openTab(app.TabBar.MAIN).then(() => {
                cy.wait(1000)//NEED WAIT TO SEARCH CONTAINER
                _common.select_tabFromFooter(cnt.uuid.REQUISITION_CONTACT, app.FooterTab.CONTACTS, 2);
    
           })
   
            _common.select_rowInContainer(cnt.uuid.REQUISITION_CONTACT);
		    _common.waitForLoaderToDisappear()
            _common.assert_cellData_insideActiveRow(cnt.uuid.REQUISITION_CONTACT, app.GridCells.BPD_CONTACT_FIRST_NAME, CONTAINERS_CONTRACT_CONTACT.ANTON)
       
    })
    it("TC - Create another contract record", function () {
          _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
          _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
    
            _common.openTab(app.TabBar.CONTRACT).then(()=>{
            _common.setDefaultView(app.TabBar.CONTRACT)
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT,app.FooterTab.CONTRACTS,0)
            })
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO).pinnedItem();
            _common.waitForLoaderToDisappear()
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
            _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT,0)
                _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
            _common.waitForLoaderToDisappear()
            cy.SAVE();
            _common.waitForLoaderToDisappear()
            _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "Contract_Code")
    })
    it("TC - Create new  Contact in contract for contract", function () {

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()

            _common.openTab(app.TabBar.CONTRACT).then(() => {
                _common.select_tabFromFooter(cnt.uuid.CONTRACT_CONTACT, app.FooterTab.CONTACTS, 2);
                _common.waitForLoaderToDisappear()
                
            });
    
            _common.create_newRecord(cnt.uuid.CONTRACT_CONTACT);
            _common.select_rowInContainer(cnt.uuid.CONTRACT_CONTACT);
            _common.waitForLoaderToDisappear()
           _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CONTACT, app.GridCells.BPD_CONTACT_FK,commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,  CONTAINERS_CONTRACT_CONTACT.ANTON)
            cy.SAVE();
            _common.waitForLoaderToDisappear()
            _common.waitForLoaderToDisappear()
            _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_CONTACT, app.GridCells.BPD_CONTACT_FIRST_NAME, CONTAINERS_CONTRACT_CONTACT.ANTON);
    });
    it("TC - Create Items for Contract",function(){
    
        _common.openTab(app.TabBar.ORDER_ITEM).then(()=>{
         cy.wait(1000)//NEED WAIT TO SEARCH CONTAINER
        _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT,app.FooterTab.ITEMS,0)
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        })
        _common.maximizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        _procurementContractPage.enterRecord_toCreateItem(cnt.uuid.ITEMSCONTRACT,CONTAINERS_ITEM.MATERIAL_NO,ITEM_DESC, CONTAINERS_CONTRACT_CONTACT.QUANTITY)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
		_common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
    })
    it("TC - Create contract from Contract termination from wizard AC6", function () {
            _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
            })
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
            _common.search_fromSidebar(CommonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CONTRACT_TERMINATION);
            _saleContractPage.contractTermination_FromWizard(CommonLocators.CommonKeys.NEW_CONTRACT, 0,PROJECT_NO, CHANGE_PROJECT_DESC, CommonLocators.CommonKeys.DESIGN_CHANGE, CommonLocators.CommonKeys.CHANGE_REQUEST, CONTAINERS_CONTRACT_CONTACT.BUSINESSPARTNER)
            _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_CONTRACT)
    })
    it("TC - Verify the contact in contract ", function () {


        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
			_common.waitForLoaderToDisappear()	
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
           _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,PROJECT_NO)
		});

            _common.openTab(app.TabBar.CONTRACT).then(() => {
                _common.select_tabFromFooter(cnt.uuid.CONTRACT_CONTACT, app.FooterTab.CONTACTS, 2);
    
            })
            _common.select_rowInContainer(cnt.uuid.CONTRACT_CONTACT);
		    _common.waitForLoaderToDisappear()
            _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_CONTACT, app.GridCells.BPD_CONTACT_FIRST_NAME,CONTAINERS_CONTRACT_CONTACT.ANTON)
       
    })
 })
