import {_businessPartnerPage,_projectPage, _common, _controllingUnit, _package, _sidebar, _mainView, _validate, _modalView, _rfqPage, _saleContractPage, _procurementContractPage, _materialPage } from 'cypress/pages';
import { cnt, tile, app, sidebar, commonLocators, btn } from 'cypress/locators';
import common from 'mocha/lib/interfaces/common';
import type { DataCells } from 'cypress/pages/interfaces.d.ts'
import Buttons from 'cypress/locators/buttons';
import CommonLocators from 'cypress/locators/common-locators';
import { ContractPage } from 'cypress/pages/module/sales/contract/contractSales-page';


const CONTRACT_CODE = _common.generateRandomString(4);
const CONTRACT_DESC = _common.generateRandomString(5);
const CONTRACT_DESC1 = _common.generateRandomString(5);


let PROCUREMENT_CONTRACT_PARAMETER:DataCells
let PROCUREMENT_CONTRACT_PARAMETER1:DataCells
let CONTAINERS_CONTRACT
let CONTAINER_COLUMNS_CONTRACT



describe('PCM- 4.230 | Wizard change contract code in Contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('procurement-and-bpm/con-4.230-wizard-change-contract-code-in-contract-module.json').then((data) => {
            this.data = data;             
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT             
         
            PROCUREMENT_CONTRACT_PARAMETER = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIG,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BP[0]
			}
            PROCUREMENT_CONTRACT_PARAMETER1 = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIG,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BP[1]
			}
            
        
        });
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.waitForLoaderToDisappear()
        _common.openDesktopTile(tile.DesktopTiles.PROJECT)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("projectName")).pinnedItem()
    });
    after(() => {
        cy.LOGOUT();
    });       
    it("TC - Create new contract record", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear() 
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("projectName"))      
		_common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT)
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)		
		});
        cy.REFRESH_CONTAINER()        
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESC)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE)
		cy.SAVE()        
        _common.waitForLoaderToDisappear()        
		_common.waitForLoaderToDisappear()
        cy.REFRESH_SELECTED_ENTITIES()
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, CONTRACT_DESC)
        _common.waitForLoaderToDisappear()
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, CONTAINERS_CONTRACT.CONTRACT_CODE)
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER1);
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESC1)
        _common.waitForLoaderToDisappear()
		cy.SAVE()        
		_common.waitForLoaderToDisappear()        
	}); 
    it("TC - Verify change contract code wizard option", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_CODE);    
        _common.inputField_fromModal(commonLocators.CommonElements.ROW, CommonLocators.CommonLabels.CODE, 0, app.InputFields.DOMAIN_TYPE_CODE).clear().type(Cypress.env(CONTAINERS_CONTRACT.CONTRACT_CODE))
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _validate.accept_buttonInModalInside(CONTAINERS_CONTRACT.INFORMATION)
        _common.waitForLoaderToDisappear()        
        _common.inputField_fromModal(commonLocators.CommonElements.ROW, CommonLocators.CommonLabels.CODE, 0, app.InputFields.DOMAIN_TYPE_CODE).clear().type(CONTRACT_CODE)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, CONTRACT_CODE)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, CONTRACT_CODE)
    })  

})