/*
 * Copyright(c) RIB Software GmbH
 */
import { EntityInfo } from '@libs/ui/business-base';
import { IOrdHeaderEntity, IOrdWarrantyEntity } from '@libs/sales/interfaces';
import { SalesCommonWarrantyEntityInfoFactory } from '@libs/sales/common';
import { SalesContractWarrantyDataService } from '../../services/sales-contract-warranty-data.service';
import { SalesContractWarrantyBehavior } from '../../behaviors/sales-contract-warranty-behavior.service';
import { SalesContractContractsComplete } from '../complete-class/sales-contract-contracts-complete.class';
import { SalesContractWarrantyValidationService } from '../../services/validations/sales-contract-warranty-validation.service';

/**
 * Sales Contract Warranty Entity Info
 */
export const SALES_CONTRACT_WARRANTY_ENTITY_INFO: EntityInfo = SalesCommonWarrantyEntityInfoFactory.create<IOrdWarrantyEntity, IOrdHeaderEntity, SalesContractContractsComplete>({
	containerUuid: 'f3c2bfb95684489bb83cc50eeddc1017',
	dataServiceToken: SalesContractWarrantyDataService,
	validationServiceToken: SalesContractWarrantyValidationService,
	behaviorToken: SalesContractWarrantyBehavior,
	dtoSchemeId: { moduleSubModule: 'Sales.Contract', typeName: 'OrdWarrantyDto' },
	permissionUuid: 'fea263be52ed4f2098f70ff5290dc910',
});