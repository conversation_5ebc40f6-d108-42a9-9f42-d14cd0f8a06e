/*
 * Copyright(c) RIB Software GmbH
 */

import { IApplicationModuleInfo } from '@libs/platform/common';
import { TimekeepingCertificateModuleInfo } from './lib/model/timekeeping-certificate-module-info.class';

export * from './lib/timekeeping-certificate.module';
export * from './lib/model/wizards/timekeeping-certificate-wizard.service';

/**
 * Returns the module info object for the timekeeping certificate module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
	return TimekeepingCertificateModuleInfo.instance;
}
