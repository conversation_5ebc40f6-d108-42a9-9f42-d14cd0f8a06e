/*
* Copyright(c) RIB Software GmbH
*/

import { inject, Injectable, ProviderToken } from '@angular/core';
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions, PlatformSchemaService, ValidationServiceFactory } from '@libs/platform/data-access';
import { TimekeepingCertificateDataService } from './timekeeping-certificate-data.service';
import { ICertificateEntity } from '@libs/resource/interfaces';

@Injectable({
	providedIn: 'root'
})

export class TimekeepingEmployeeCertificateValidationService extends BaseValidationService<ICertificateEntity> {
	private validators: IValidationFunctions<ICertificateEntity> | null = null;

	public constructor(protected dataService: TimekeepingCertificateDataService) {
		super();

		const schemaSvcToken: ProviderToken<PlatformSchemaService<ICertificateEntity>> = PlatformSchemaService<ICertificateEntity>;
		const platformSchemaService = inject(schemaSvcToken);

		platformSchemaService.getSchema({moduleSubModule: 'Timekeeping.Certificate', typeName: 'EmployeeCertificateDto'}).then(
			(scheme) => {
				this.validators = new ValidationServiceFactory<ICertificateEntity>().provideValidationFunctionsFromScheme(scheme, this);
			}
		);
	}

	protected generateValidationFunctions(): IValidationFunctions<ICertificateEntity> {
		if(this.validators !== null) {
			return this.validators;
		}

		return {};

	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<ICertificateEntity> {
		return this.dataService;
	}

}
