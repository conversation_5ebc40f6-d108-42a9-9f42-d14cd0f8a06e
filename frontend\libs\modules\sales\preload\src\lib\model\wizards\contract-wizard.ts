import { IInitialization<PERSON>ontext, I<PERSON><PERSON><PERSON>, PlatformModuleManagerService } from '@libs/platform/common';

export const SALES_CONTRACT_WIZARDS: IWizard[] = [
    {
        uuid: 'b812ea3d7dd64b3aa97387395d70b82d',
        name: 'Create Bill',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().createBill(context));
        },
    },
    {
        uuid: 'c3eeedbc977049b08cb321a3d574b39c',
        name: 'Create WIP',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().createWip(context));
        },
    },
    {
        uuid: '4c51d56e1f084fa99640e47df9d0cb13',
        name: 'Update Estimate',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().updatEstimate(context));
        },
    },
    {
        uuid: '9c25c7fcc5964600b0e829cda55b9e2c',
        name: 'Change Contract Status',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().changeContractStatus(context));
        },
    },
    {
        uuid: '314c998a4e4048bbac900c57e56c2c5d',
        name: 'Change Code',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().changeContractCode(context));
        },
    },
    {
        uuid: '5d038199d29d41b4be0ba27956824e10',
        name: 'Generate Payment Schedule',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().generatePaymentSchedule(context));
        },
    },
    {
        uuid: '7e4c43416c334aeaaa17be7c702235b1',
        name: 'Generate Advance Payment Bill',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().generateAdvancePaymentBill(context));
        },
    },
    {
        uuid: '9d97cb3943864fe1a78b7d6af5e21944',
        name: 'Change Advance Line Status',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().changeAdvanceLineStatus(context));
        },
    },
    {
        uuid: '917973f5ff674e618235d32e10c4077d',
        name: 'Change Payment Schedule Status',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().changePaymentScheduleStatus(context));
        },
    },
    {
        uuid: '83bf7ef150a24b999652f2b21b4081cc',
        name: 'changeSalesConfiguration',
        execute(context: IInitializationContext) {
            const currentModule = context.injector.get(PlatformModuleManagerService).activeModule?.internalModuleName ?? '';
            return import('@libs/sales/contract').then((m) => {
                context.injector.get(m.SalesContractChangeConfigurationWizardService).changeSalesConfiguration(currentModule);
            });
        },
    },
    {
        uuid: 'e494c864688d4d999b7032c89ee6a02c',
        name: 'Generate Bill From Payment Schedule', // TODO: Need translated text
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().generateBillFromPaymentSchedule(context));
        },
    },
    {
        uuid: '45da63d5abdb47d260b56d1deffcc249',
        name: 'generateTransactions',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().generateTransactionsForOrders(context));
        },
    },
    {
        uuid: 'c89d65f760f04970ba295a62021f2e22',
        name: 'generatePaymentScheduleFromSchedule',
        execute: (context) => {
            return import('@libs/sales/contract').then((module) => new module.SalesContractMainWizard().generatePaymentScheduleFromSchedule(context));
        },
    },
    {
        uuid: 'e5f4f53d40174379a5b612caf7d80d83',
        name: 'UpdateEstimateBudget',
        execute(context: IInitializationContext) {
            return import('@libs/sales/contract').then((m) => {
                context.injector.get(m.SalesContractUpdateEstimateBudgetWizardService).updateEstimateBudget();
            });
        },
    },
    {
        uuid: '3974b2d3f99148ff90a581263a1ffc0f',
        name: 'changeStatusForProjectDocument',
        async execute(context: IInitializationContext) {
            const m = await import('@libs/sales/contract');
            return m.SalesContractChangeStatusForProjectDocumentWizardService.execute(context);
        },
    },
];