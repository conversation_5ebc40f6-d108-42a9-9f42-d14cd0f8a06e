using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Web.Http;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Final;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostCodes.BusinessComponents;
using RIB.Visual.Basics.CostCodes.ServiceFacade.WebApi;
using RIB.Visual.Estimate.Main.BusinessComponents;

namespace RIB.Visual.Estimate.Main.ServiceFacade.WebApi.Controllers
{
	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("estimate/main/estallmarkup2costcode")]
	public class EstAllMarkup2CostCodeController : EntityReadOnlyController<EstAllMarkup2CostCodeLogic, EstAllMarkup2costcodeDto, EstAllMarkup2costcodeEntity, Platform.Core.IdentificationData>
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[Route("create"), HttpPost]
		public List<EstAllMarkup2costcodeDto> Create(ReadData data)
		{
			var result = Logic.Create(data).Select(e => new EstAllMarkup2costcodeDto(e)).ToList();
			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[Route("delete"), HttpPost]
		public void Delete(IEnumerable<EstAllMarkup2costcodeDto> data)
		{
			this.Logic.Delete(data.Select(e => e.Copy()));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[Route("getEstimateAllMarkup2CostCodeNew"), HttpPost]
		public EstAllMarkup2CostCodeCompleteDto GetListNew(EstAllowanceMarkupReadData data)
		{
			var complete = this.Logic.GetAllMarkupByAllowance(data);
			return new EstAllMarkup2CostCodeCompleteDto(complete);
		}



		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[Route("getEstAllMarkup2CostCodeByAreas"), HttpPost]
		public EstAllMarkup2CostCodeCompleteDto getEstAllMarkup2CostCodeByAreas(EstAllowanceMarkupReadData data)
		{
			var complete = this.Logic.GetAllMarkupByAllowance(data);
			return new EstAllMarkup2CostCodeCompleteDto(complete);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="estHeaderId"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("masterCCandProjectCCChildOnly")]
		public object GetMasterAndProjectCostCodeChildOnly(int projectId, int estHeaderId)
		{
			List<int> deleteMdcCostCodeIds = new List<int>();
			var costCodes = this.Logic.GetMasterCostCodeAndProjectCostCodeChildOnly(projectId, estHeaderId, true, ref deleteMdcCostCodeIds,false);
			var isEstimateCcCostCodeType = this.Logic.getIsEstimateCcCostCodeTypeIds();
			return new
			{
				allCostCodes = costCodes != null ? costCodes.Select(e => new CostCodeDto(e)).ToList() : new List<CostCodeDto>(),
				estimateCcCostCodeType = isEstimateCcCostCodeType,
				deleteCostCodes = deleteMdcCostCodeIds.Count > 0 ? deleteMdcCostCodeIds : null
			};
		}

		/// <summary>
		/// Creates a estimate structure detail entity
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("updateMajorCostCode")]
		public object UpdateMajorCCostCode(ReadData data)
		{
			List<int> deleteMdcCostCodeIds = new List<int>();
			var updateMajorCostCode = this.Logic.UpdateMajorCCostCode(data);
			var dto = updateMajorCostCode == null ? null : updateMajorCostCode.Select(e => new EstAllMarkup2costcodeDto(e)).ToList();

			var allCostCode = this.Logic.GetMasterCostCodeAndProjectCostCodeChildOnly(data.ProjectId, data.EstHeaderId, true, ref deleteMdcCostCodeIds);
			return new
			{
				dto = dto,
				allCostCode = allCostCode != null ? allCostCode.Select(e => new CostCodeDto(e)).ToList() : new List<CostCodeDto>()
			};
		}

	}
}
