using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Diagnostics;
using System.IO;
using System.IO.Pipes;
using System.Linq;
using System.Linq.Expressions;
using Microsoft.Graph;
using Microsoft.Graph.ExternalConnectors;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PnP.Framework.Extensions;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Services.Scheduler.BusinessComponents;
using RIB.Visual.Services.Scheduler.Core;

namespace RIB.Visual.Documents.Project.BusinessComponents
{
	/// <summary>
	/// Represents a scheduled task for synchronizing documents to SharePoint. Inherits from SchedulerTaskBase.
	/// </summary>
	[Task(TaskId, ResourceAssemblyName, AutoSyncDescription, UiCreate = true, UiChangeable = true, UiDelete = true, RunInUserContext = true)]
	[Export(TaskId, typeof(IScheduleTask))]
	[Export(typeof(IScheduleTask))]
	[PartCreationPolicy(CreationPolicy.NonShared)]
	public class SyncDocumentsToSharePointScheduleTask : SchedulerTaskBase
	{
		private const string ResourceAssemblyName = "RIB.Visual.Documents.Project.Localization";
		private const string DataSourceDescription = "SchedulerTask_TXT_CompanyCodesKey";
		private const string AutoSyncDescription = "SchedulerTask_TXT_AutoSync";

		/// <summary>
		/// 
		/// </summary>
		protected readonly int externalSourceFk = DocSpCommonLogic<DocMetadata2extEntity>.ExternalSourceFk;

		/// <summary>
		///
		/// </summary>
		[Input(ResourceAssemblyName, DataSourceDescription, Required = false)]
		public string CompanyCodes { get; set; }

		/// <summary>
		///
		/// </summary>
		public string ProjectIds { get; set; }

		/// <summary>
		///
		/// </summary>
		public string DocumentIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		protected int[] DocumentIdArray { get; set; }

		/// <summary>
		///
		/// </summary>
		public string ItemProfile { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string AccessToken { get; set; }

		/// <summary>
		///
		/// </summary>
		public (DriveRecipient[] Recipients, string Role, string Message) RecipientsForDoc
		{
			get
			{
				if (string.IsNullOrWhiteSpace(this.ItemProfile))
				{
					return ([], null, null);
				}

				if (_recipientsForDoc.recipients == null)
				{
					var profile = JsonConvert.DeserializeObject<SharePointItemProfile>(this.ItemProfile);

					var driveRecipients = profile.AadUsers.Select(user =>
					{
						var driveRecipient = new DriveRecipient();
						if (string.IsNullOrWhiteSpace(user.Mail))
						{
							// TODO: not sure it is working
							driveRecipient.ObjectId = user.Id;
						}
						else
						{
							driveRecipient.Email = user.Mail;
						}

						return driveRecipient;
					}).ToArray();
					_recipientsForDoc = (driveRecipients, profile.ShareOption.ToSharePointRole(), profile.Message);
				}

				return _recipientsForDoc;
			}
		}

		/// <summary>
		///
		/// </summary>
		public const String TaskId = "8b4b2717640e41c0a9ead5e8c1177d7b";

		private GraphServiceClient _graphClient;

		private static Object _syncClassObject = new Object();

		private (DriveRecipient[] recipients, string role, string message) _recipientsForDoc;

		private int[] _sharePointDocTypes = [];

		// status for SharePoint synced documents
		private readonly int _sharePointSyncedStatus = DocumentSharePointLogic.SharePointSyncedStatus;
		// use Lazy<T> to ensure that the Logics are only instantiated when needed.
		// avoid dependencies issue during initialization.

		/// <summary>
		///
		/// </summary>
		protected virtual JobEntity GetJobById()
		{
			var job = JobLogic.Value.GetJobById(Context.JobId);
			return job;
		}

		/// <summary>
		/// Parses a comma-separated string into an array of strings.
		/// </summary>
		/// <param name="input"></param>
		/// <returns></returns>
		public static string[] ParseStringToArray(string input)
		{
			if (string.IsNullOrEmpty(input))
			{
				return [];
			}

			return input.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
		}

		/// <summary>
		///
		/// </summary>
		public Lazy<DocumentSharePointLogic> DocSharePointLogic { get; set; } = new Lazy<DocumentSharePointLogic>(() => new DocumentSharePointLogic());

		/// <summary>
		/// 
		/// </summary>
		public Lazy<JobLogic> JobLogic { get; set; } = new Lazy<JobLogic>(() => new JobLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<DocumentTypeLogic> DocumentTypeLogic { get; set; } = new Lazy<DocumentTypeLogic>(() => new DocumentTypeLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<DocSpProjectConfigLogic> DocSpProjectConfigLogic { get; set; } = new Lazy<DocSpProjectConfigLogic>(() => new DocSpProjectConfigLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<DocSpprjFolderSettingLogic> DocSpprjFolderSettingLogic { get; set; } = new Lazy<DocSpprjFolderSettingLogic>(() => new DocSpprjFolderSettingLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<DocSpprjDetailStructLogic> DocSpprjDetailStructLogic { get; set; } = new Lazy<DocSpprjDetailStructLogic>(() => new DocSpprjDetailStructLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<IDocSpCommonLogic<DocMetadata2extEntity>> DocMetadata2extEntityLogic { get; set; } = new Lazy<IDocSpCommonLogic<DocMetadata2extEntity>>(() => new DocMetadata2extEntityLogic());

		/// <summary>
		/// 
		/// </summary>
		public Lazy<IDocSpCommonLogic<DocSpprjMetaDataEntity>> DocSpprjMetaDataEntityLogic { get; set; } = new Lazy<IDocSpCommonLogic<DocSpprjMetaDataEntity>>(() => new DocSPProjectMetaDataLogic());


		/// <summary>
		///
		/// </summary>
		public virtual Lazy<Basics.Core.Core.IGetCompanyLogic> CompanyLogic { get; set; } = new Lazy<Basics.Core.Core.IGetCompanyLogic>(() => new BasicsCompanyLogic());

		/// <summary>
		/// 
		/// </summary>
		public virtual Lazy<Basics.Core.Core.IGetProjectLogic> ProjectLogic { get; set; } = new Lazy<Basics.Core.Core.IGetProjectLogic>(() => Injector.Get<Basics.Core.Core.IGetProjectLogic>());


		/// <summary>
		///
		/// </summary>
		public Lazy<SystemOptionLogic> SystemOptionLogic { get; set; } = new Lazy<SystemOptionLogic>(() => new SystemOptionLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<DocumentHistoryLogic> DocumentHistoryLogic { get; set; } = new Lazy<DocumentHistoryLogic>(() => new DocumentHistoryLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<DocumentRevisionLogic> DocumentRevisionLogic { get; set; } = new Lazy<DocumentRevisionLogic>(() => new DocumentRevisionLogic());

		/// <summary>
		/// Executes the scheduled task to synchronize documents to SharePoint.
		/// </summary>
		public override void Execute()
		{
			lock (_syncClassObject)
			{
				if (!IsSharePointEnable())
				{
					WriteLogMessage(LoggingLevel.Info, "SharePoint is not enabled. Task will not be executed.");
					return;
				}

				var job = GetJobById();
				try
				{
					BusinessApplication.BusinessEnvironment.DisablePermissionCheck();
				}
				catch (InvalidOperationException)
				{
					// permission is already disabled.
				}
				List<(SharePointFolderProfile profile, Platform.Core.IdentificationData[] documentIds)> list = [];

				var companyCodes = ParseStringToArray(this.CompanyCodes);

				var settings = new Dictionary<int, DocSpprjFolderSettingEntity>();

				// auto sync
				if (job.TaskType == TaskId)
				{
					foreach (var companyCode in companyCodes)
					{
						var company = this.CompanyLogic.Value.GetCompanyId(companyCode);
						if (company == 0)
						{
							WriteLogMessage(LoggingLevel.Error, $"Company with code {companyCode} not found.");
							continue;
						}

						var configs = this.DocSpProjectConfigLogic.Value.GetAutoSyncProjectIdsByCompanyId(company);

						var settings1 = this.DocSpprjFolderSettingLogic.Value.GetFolderSettingsByProjectIds(configs.Select(e => e.Id).ToArray(), company);

						settings1.ForEach(e =>
						{
							settings.TryAdd(e.Id, e);
						});
					}
					// when no company codes are provided, get all companies
					if (companyCodes.Length == 0)
					{
						var companies = this.CompanyLogic.Value.GetList();
						foreach (var company in companies)
						{
							var configs = this.DocSpProjectConfigLogic.Value.GetAutoSyncProjectIdsByCompanyId(company.Id);
							if (configs.Any())
							{
								var settings1 = this.DocSpprjFolderSettingLogic.Value.GetFolderSettingsByProjectIds(configs.Select(e => e.Id).ToArray(), company.Id);
								settings1.ForEach(e =>
								{
									settings.TryAdd(e.Id, e);
								});
							}
						}
					}
				}

				var projectIds = ParseStringToArray(this.ProjectIds).Select(e => int.Parse(e)).ToArray();

				var projects = this.ProjectLogic.Value.GetProjectsById(projectIds);

				var groupByCompany = projects
					.GroupBy(e => e.CompanyFk).ToArray();

				foreach (var group in groupByCompany)
				{
					var projectIdsForCompany = group.Select(e => e.Id).ToArray();
					var settings2 = this.DocSpprjFolderSettingLogic.Value.GetFolderSettingsByProjectIds(projectIdsForCompany, group.Key);
					settings2.ForEach(e =>
					{
						settings.TryAdd(e.Id, e);
					});
				}

				this.DocumentIdArray = ParseStringToArray(this.DocumentIds)
					.Select(e => int.Parse(e)).ToArray();

				_sharePointDocTypes = DocumentTypeLogic.Value.GetList().Where(e => e.IsSharePoint).Select(e => e.Id).ToArray();

				foreach (var (_, setting) in settings)
				{
					var documents = new List<DocumentEntity>();

					// manual sync
					if (DocumentIdArray.Length > 0)
					{
						var documentsManual = DocSharePointLogic.Value.GetDocumentsByFilter(e =>
							e.PrjProjectFk != null
							&& e.PrjProjectFk.Value == setting.PrjProjectFk
							&& DocumentIdArray.Contains(e.Id));
						documents = documentsManual.ToList();
					}
					else
					{
						// auto sync
						if (setting.DocSpprojectconfigEntities != null && setting.DocSpprojectconfigEntities.Count > 0)
						{
							var config = setting.DocSpprojectconfigEntities.FirstOrDefault(e => e.IsAutoSync && e.IsProjectSynced);
							if (config != null)
							{
								var documentsAuto = DocSharePointLogic.Value.GetDocumentsByFilter(e =>
									e.PrjProjectFk != null
									&& e.PrjProjectFk.Value == setting.PrjProjectFk
									&& _sharePointDocTypes.Contains(e.PrjDocumentTypeFk)
								);
								documents.AddRange(documentsAuto);
							}
						}
					}

					var siteId = DocMetadata2extEntityLogic.Value
						.GetCoresByFilter(e =>
							e.BasExternalsourceFk == externalSourceFk
							&& e.Objectid == setting.PrjProjectFk
							&& e.Type == (int)MetaDataType.Project).FirstOrDefault();

					var teamId = DocMetadata2extEntityLogic.Value
						.GetCoresByFilter(e =>
							e.BasExternalsourceFk == externalSourceFk
							&& e.Objectid == setting.PrjProjectFk
							&& e.Type == (int)MetaDataType.Team).FirstOrDefault();

					if (setting?.DocSpprjdetailstructEntities == null || setting.DocSpprjdetailstructEntities.Count == 0)
					{
						WriteLogMessage(LoggingLevel.Error, $"No folder structure found for project {setting.PrjProjectFk}.");
						continue;
					}

					var structures = setting.DocSpprjdetailstructEntities.BuildTree().ToList();

					var configs = DocSpprjDetailStructLogic.Value.ConvertToSharePointFolderConfigs(structures);

					HashSet<MetaDataType> metaDataTypes = [];

					DocSpprjMetaDataEntityLogic.Value.GetCoresByFilter(e => e.ContextFk == null && e.PrjDocSpprjFolderSettingFk == null)
						.ToList().ForEach(e =>
						{
							metaDataTypes.Add((MetaDataType)e.MetadataType);
						});

					if (setting.DocSpprjmetadataEntities != null && setting.DocSpprjmetadataEntities.Count > 0)
					{
						foreach (var item in setting.DocSpprjmetadataEntities)
						{
							metaDataTypes.Add((MetaDataType)item.MetadataType);
						}
					}

					list.Add((
						new SharePointFolderProfile
						{
							MainId = setting.PrjProjectFk.Value,
							ContextId = setting.DocSpprjmetadataEntities.FirstOrDefault(e => e.ContextFk.HasValue)?.ContextFk,
							Folders = configs,
							SiteId = siteId?.ExternalId,
							TeamId = teamId?.ExternalId,
							MetaDataTypes = metaDataTypes.ToArray()
						},
						documents.Select(e => new Platform.Core.IdentificationData { Id = e.Id }).ToArray()
					));
				}

				list.ForEach(e =>
				{
					this.SyncDocuments(e.profile, e.documentIds);
				});
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public virtual bool IsSharePointEnable()
		{
			return SystemOptionLogic.Value.IsSharePointEnabled();
		}

		/// <summary>
		///
		/// </summary>
		protected Lazy<DownloadLogic> DownloadLogic { get; set; } = new Lazy<DownloadLogic>(() => new DownloadLogic());

		/// <summary>
		/// Gets the download file state for the specified file archive document foreign key.
		/// This method can be overridden in derived classes to provide custom file retrieval logic.
		/// </summary>
		/// <param name="fileArchiveDocFk">The file archive document foreign key value</param>
		/// <returns>The download file state containing file information and paths</returns>
		protected virtual FileState GetDownloadFileState(int fileArchiveDocFk)
		{
			return DownloadLogic.Value.GetDownloadFileStateNew(fileArchiveDocFk);
		}

		/// <summary>
		/// Synchronizes documents to SharePoint. This method has been refactored from async to sync.
		/// </summary>
		protected void SyncDocuments(SharePointFolderProfile profile, Platform.Core.IdentificationData[] documentIds)
		{
			try
			{
				var docSharePointLogic = DocSharePointLogic.Value;
				//documentIds = [new IdentificationData(1026429), new IdentificationData(1026430), new IdentificationData(1026431)];
				//siteId = "891a10f9-9a7d-47ef-aa6d-995934e20feb";

				var siteId = profile.SiteId;
				var teamId = profile.TeamId;

				// init graph client every time
				_graphClient = docSharePointLogic.GraphClientWithOwner();

				// get site
				var site = docSharePointLogic.TryGetSite(siteId, teamId);

				var drive = _graphClient.Sites[siteId].Drive.Request().Expand("list").GetAsync().GetAwaiter().GetResult();
				var list = drive.List;
				var driveItem = _graphClient.Drives[drive.Id].Root.Request().GetAsync().GetAwaiter().GetResult();

				var rootFolderPath = "";

				//var profileJson =
				//	"[{\"MainId\":1009161,\"Folders\":[{\"Id\":285,\"Description\":\"Correspondence\",\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Description\":\"testthird\",\"id\":1000633,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]}]},{\"Id\":286,\"Description\":\"ContractualDocuments\",\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Description\":\"test\",\"id\":1000478,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]},{\"Description\":\"test2\",\"id\":1000479,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]}]},{\"Id\":353,\"Description\":\"SecondDocument\",\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Description\":\"材料采购\",\"id\":1000089,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]},{\"Description\":\"计划管理\",\"id\":1000090,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]},{\"Description\":\"订单管理\",\"id\":1000091,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]},{\"Description\":\"质量监督\",\"id\":1000092,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]}]},{\"Id\":1002468,\"Description\":\"ThirdDocument\",\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Description\":\"技术质量\",\"id\":1000088,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]},{\"Description\":\"GreenConstruction\",\"id\":1000468,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]},{\"Description\":\"Model\",\"id\":1000469,\"Folders\":[{\"Roles\":[\"write\"],\"DriveRecipients\":[{\"Email\":\"<EMAIL>\"}],\"Id\":1,\"Description\":\"Draft\"},{\"Id\":1000253,\"Description\":\"RIBArchiv\"},{\"Id\":2,\"Description\":\"Drawing\"},{\"Id\":3,\"Description\":\"Contract\"},{\"Id\":4,\"Description\":\"Invoice\"}]}]}]}]";

				//var profiles = JsonConvert.DeserializeObject<SharePointFolderProfile[]>(profileJson);

				//profile = profiles.First(e => e.MainId == 1009161);

				//TODO mock data only
				//profile.MetaDataTypes = new[] { MetaDataType.RubricCategory, MetaDataType.ProjectDocumentCategory, MetaDataType.ProjectDocumentType, MetaDataType.ProcurementStructure, MetaDataType.BusinessPartner, MetaDataType.ControllingUnit, };

				var termStore = _graphClient.Sites[siteId].TermStore.Request().GetAsync().GetAwaiter().GetResult();

				var termGroupGlobal = docSharePointLogic.TermStoreCreateGroupSync();

				foreach (var metaDataType in profile.MetaDataTypes)
				{
					docSharePointLogic.CreateColumnByMetaDataType(metaDataType,
						siteId,
						termGroupGlobal,
						termStore.Id,
						profile.ContextId.ToString(),
						site);
				}

				var sw = new Stopwatch();
				sw.Start();

				foreach (var docId in documentIds)
				{
					// refresh token every 30 mins
					if (sw.Elapsed.TotalSeconds > 1800)
					{
						sw.Restart();
						_graphClient = docSharePointLogic.GraphClientWithOwner();
					}

					var doc = docSharePointLogic.GetDocumentById(docId);

					// already exists, only invite users
					if (doc.PrjDocumentStatusFk == _sharePointSyncedStatus)
					{
						if (doc.FileArchiveDocFk.HasValue 
							&& this.DocumentIdArray.Contains(doc.Id)
							&& RecipientsForDoc.Recipients.Length > 0)
						{
							var extData = docSharePointLogic.GetMetaDataFromExt(MetaDataType.Document, doc.FileArchiveDocFk.Value);
							if (!string.IsNullOrEmpty(extData.ExternalId))
							{
								try
								{
									docSharePointLogic.InviteRecipientsToDriveItemSync(drive.Id, extData.ExternalId, RecipientsForDoc.Recipients, RecipientsForDoc.Role, RecipientsForDoc.Message, AccessToken);
								}
								catch (Exception ex)
								{
									WriteLogMessage(LoggingLevel.Error, $"DocumentId: {doc.Id}, FileArchiveDocFk: {doc.FileArchiveDocFk.Value} invite users failed. Exception: {ex.Message}");
								}
							}
						}
						continue;
					}

					doc.PrjDocumentStatusFk = _sharePointSyncedStatus;

					var folderConfigs = docSharePointLogic.BuildSharePointRelativePath(doc, profile);
					var pathArray = folderConfigs.Where(e => !string.IsNullOrWhiteSpace(e.Description)).Select(e => e.Description).ToArray();
					var relativePath = string.Join("/", folderConfigs.Select(e => e.Description));
					
					var path = _graphClient.Drives[drive.Id].AppendSegmentToRequestUrl($"/root:{rootFolderPath}/{relativePath}");

					var folder = new Microsoft.Graph.DriveItem();
					try
					{
						var (_, _, _, folderId) = docSharePointLogic.GetMetaDataFromExt(MetaDataType.Folder, folderConfigs[^1].DetailStructId);
						if (string.IsNullOrWhiteSpace(folderId))
						{
							throw new Exception($"itemNotFound");
						}
						folder = _graphClient.Sites[siteId].Drive.Items[folderId].Request().GetAsync().GetAwaiter().GetResult();
					}
					catch (Exception ex) when (ex.GetBaseException().Message.Contains("itemNotFound"))
					{
						try
						{
							if (pathArray.Length < folderConfigs.Length)
							{
								WriteLogMessage(LoggingLevel.Error, $"DocumentId: {docId} with path:({path}) is invalid.");
								continue;
							}
							var requestBuilder = new DriveItemRequestBuilder(path, _graphClient);
							folder = requestBuilder.Request().GetAsync().GetAwaiter().GetResult();
						}
						catch (Exception ex2) when (ex2.GetBaseException().Message.Contains("itemNotFound"))
						{
							folder = docSharePointLogic.GeneratePathSync(drive.Id, driveItem.Id, folderConfigs);
							docSharePointLogic.SaveMetaDataToExt(MetaDataType.Folder, folderConfigs[^1].DetailStructId, [folder.Id], relativePath);
						}
					}

					if (doc.FileArchiveDocFk == null)
					{
						WriteLogMessage(LoggingLevel.Error, $"DocumentId: {docId} no file.");
						continue;
					}

					FileState file;
					try
					{
						file = GetDownloadFileState(doc.FileArchiveDocFk.Value);
					}
					catch (Exception ex)
					{
						WriteLogMessage(LoggingLevel.Error, $"DocumentId: {docId} download file failed. Exception: {ex.Message}");
						continue;
					}

					var sharePointFileName = docSharePointLogic.GenerateUniqueFileNameSync(drive.Id, folder.Id, file.DownloadFileName);

					DriveItem fileInSharePoint;

					try
					{
						using (var fs = new FileStream(file.WorkingCopyPathAndFile, FileMode.Open, FileAccess.Read, FileShare.Read))
						{
							// Create the upload session
							// itemPath does not need to be a path to an existing item
							var uploadSession = _graphClient.Drives[drive.Id].Items[folder.Id].ItemWithPath($"{sharePointFileName}")
								.CreateUploadSession(new DriveItemUploadableProperties
								{
									AdditionalData = new Dictionary<string, object>
									{
									{ "@microsoft.graph.conflictBehavior", "rename" },
									},
								}).Request().PostAsync().GetAwaiter().GetResult();

							// Max slice size must be a multiple of 320 KiB
							int maxSliceSize = 320 * 1024;
							var fileUploadTask = new LargeFileUploadTask<DriveItem>(
								uploadSession, fs, maxSliceSize, _graphClient);

							var totalLength = fs.Length;
							// Create a callback that is invoked after each slice is uploaded
							IProgress<long> progress = new Progress<long>(prog =>
							{
								Console.WriteLine($"Uploaded {prog} bytes of {totalLength} bytes");
							});
							// upload
							//fileInSharePoint = _graphClient.Drives[drive.Id].Items[folder.Id].ItemWithPath($"{sharePointFileName}").Content.Request()
							//	.PutAsync<DriveItem>(fs).GetAwaiter().GetResult();
							// Upload the file
							var uploadResult = fileUploadTask.UploadAsync(progress).GetAwaiter().GetResult();
							fileInSharePoint = uploadResult.ItemResponse;
						}
						// refresh the token after upload
						_graphClient = docSharePointLogic.GraphClientWithOwner();
						if (this.DocumentIdArray.Contains(doc.Id) && RecipientsForDoc.Recipients.Length > 0)
						{
							// invite users
							docSharePointLogic.InviteRecipientsToDriveItemSync(drive.Id, fileInSharePoint.Id, RecipientsForDoc.Recipients, RecipientsForDoc.Role, RecipientsForDoc.Message, AccessToken);
						}
					}
					catch (Exception ex)
					{
						WriteLogMessage(LoggingLevel.Error, $"DocumentId({docId}), file:({file.DownloadFileName}) upload failed. Exception: {ex.Message}");
						continue;
					}



					WriteLogMessage(LoggingLevel.Info, $"DocumentId: ({docId}), fileId:({doc.FileArchiveDocFk.Value}) uploaded to SharePoint at {DateTime.UtcNow}.");
					docSharePointLogic.SaveDocument(doc, fileInSharePoint.Id, sharePointFileName);

					docSharePointLogic.ProcessMetaDataSync(doc, site, list, fileInSharePoint.Id, profile.MetaDataTypes);
					WriteLogMessage(LoggingLevel.Info, $"DocumentId:({docId}) metadata processed and updated in SharePoint at {DateTime.UtcNow}.");
				}
			}
			catch (Exception ex)
			{
				WriteLogMessage(Services.Scheduler.Core.LoggingLevel.Error, ex.ToString());
				throw;
			}
		}
	}
}