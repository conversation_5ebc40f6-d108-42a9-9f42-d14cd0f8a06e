using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Globalization;
using System.Linq;
using System.Numerics;
using System.Security.Cryptography;
using System.Transactions;
using Microsoft.Online.SharePoint.TenantAdministration;
using PnP.Framework.Extensions;
using PnP.Framework.Modernization.Publishing;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using static System.Runtime.InteropServices.JavaScript.JSType;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;

namespace RIB.Visual.Documents.Project.BusinessComponents
{

#pragma warning disable S3267 // Loops should be simplified with "LINQ" expressions
	/// <summary>
	///
	/// </summary>
	public class DocSpprjFolderSettingLogic : DocSpCommonLogic<DocSpprjFolderSettingEntity>
	{

		/// <summary>
		/// GetEntityTableName
		/// </summary>
		/// <returns></returns>
		protected override string GetEntityTableName()
		{
			return "PRJ_DOC_SPPRJFOLDERSETTING";
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectIds"></param>
		/// <param name="comanyId"></param>
		/// <returns></returns>
		public virtual List<DocSpprjFolderSettingEntity> GetFolderSettingsByProjectIds(int[] projectIds, int comanyId)
		{
			using var dbContext = this.CreateDbContext();
			var folderSetttings = dbContext.Entities<DocSpprjFolderSettingEntity>()
				//.Include(e => e.DocSpprjdetailstructEntities)
				.Include(e => e.DocSpprjmetadataEntities)
				.Include(e => e.DocSpprojectconfigEntities)
				.Where(e => e.PrjProjectFk.HasValue && projectIds.Contains(e.PrjProjectFk.Value))
				.ToList();
			var detailStructLogic = new DocSpprjDetailStructLogic();

			var metadataLogic = new DocSPProjectMetaDataLogic();
			var globalMetaData = metadataLogic.GetGlobalMetadata(comanyId);

			foreach (var folderSetting in folderSetttings)
			{
				var details = detailStructLogic.GetDetailStrucByFs(folderSetting.Id, false, true);
				folderSetting.DocSpprjdetailstructEntities = details.ToList();

				foreach (var item in globalMetaData)
				{
					folderSetting.DocSpprjmetadataEntities.Add(item);
				}
			}

			return folderSetttings;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectIds"></param>
		/// <param name="bigDataCount"></param>
		/// <returns></returns>
		public IEnumerable<DocSpprjFolderSettingEntity> GetItemsByProjectIds(IEnumerable<int> projectIds, int bigDataCount = 500)
		{
			if (projectIds == null || !projectIds.Any())
			{
				return new List<DocSpprjFolderSettingEntity>();
			}
			string requestId = null;
			try
			{
				if (projectIds.Count() > bigDataCount)
				{
					var identificationData = projectIds.Select(e => new Platform.Core.IdentificationData() { PKey1 = e }).ToArray();
					using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
					{
						try
						{
							requestId = Guid.NewGuid().ToString("N"); // call RegisterTempIdsRequestUuid at the end
							AddIdentificationDataToTempTable(identificationData, requestId);
							var query = dbContext.Entities<DocSpprjFolderSettingEntity>().AsQueryable();

							query = new TempDataIntegrator<BusinessComponents.DdTempIdsEntity>((e, tmp) => null != e.PrjProjectFk && e.PrjProjectFk.Value == tmp.Key1)
								.ReduceByTempData(query, dbContext, requestId);
							return query.ToList();
						}
						catch (Exception e)
						{
							throw new BusinessLayerException("Get Folder Setting by TempTable Failed!..." + e.Message, e);
						}
					}
				}
				else
				{
					return GetByFilter(e => e.PrjProjectFk.HasValue && projectIds.Contains(e.PrjProjectFk.Value)).ToList();
				}
			}
			catch (Exception e)
			{
				throw new BusinessLayerException(e.Message, e);
			}
			finally
			{
				if (!string.IsNullOrEmpty(requestId))
				{
					BusinessApplication.BusinessEnvironment.RegisterTempIdsRequestUuid(requestId);
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="complete"></param>
		/// <returns></returns>
		public DocSpprjFolderSettingEntity SaveAsTemplate(SharePointFolderSettingComplete complete)
		{
			ArgumentNullException.ThrowIfNull(complete);
			var structureLogic = new DocSpprjFolderStructLogic();
			var metaDataLogic = new DocSPProjectMetaDataLogic();

			DocSpprjFolderSettingEntity folderSettingToSave = null;
			var folderStructuresToSave = new List<DocSpprjFolderStructEntity>();
			var folderStructuresToDelete = new List<DocSpprjFolderStructEntity>();
			var metaDataToSave = new List<DocSpprjMetaDataEntity>();
			var metaDataToCreate = new List<DocSpprjMetaDataEntity>();
			var metaDataToDelete = new List<DocSpprjMetaDataEntity>();

			var folderSettingInDb = this.GetByFilter(e => e.Id == complete.FolderSetting.Id && e.IsTemplate).FirstOrDefault();

			if (folderSettingInDb == null)
			{
				complete.FolderSetting.Id = this.GetNextSequence();
				complete.FolderSetting.Version = 0;
				complete.FolderSetting.IsTemplate = true;
				folderSettingToSave = complete.FolderSetting;
			}

			if (!complete.FolderStructures.IsNullOrEmpty())
			{
				var folderStructuresInDb = structureLogic.GetItemsByFolderSettingIds([complete.FolderSetting.Id]);
				var structures = complete.FolderStructures;
				List<DocSpprjFolderStructEntity> folderStructureToCreate = [];
				DocSpprjFolderStructLogic.CollectFolderStructureChanges(complete.FolderSetting.Id, structures, folderStructuresInDb,
					ref folderStructuresToSave, ref folderStructureToCreate, ref folderStructuresToDelete);

				structureLogic.InitEntities(folderStructureToCreate, complete.FolderSetting.Id);
				folderStructuresToSave.AddRange(folderStructureToCreate);
			}

			var metaDataListInDb = metaDataLogic.GetByFilter(e => e.PrjDocSpprjFolderSettingFk == complete.FolderSetting.Id);
			metaDataLogic.CollectProjectMetaDataChanges(complete.MetaDataType, complete.FolderSetting.Id, metaDataListInDb,
				ref metaDataToSave, ref metaDataToCreate, ref metaDataToDelete);

			metaDataToSave.AddRange(metaDataToCreate);

			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				this.Save(folderSettingToSave);

				structureLogic.Delete(folderStructuresToDelete);
				structureLogic.Save(folderStructuresToSave);

				metaDataLogic.Delete(metaDataToDelete);
				metaDataLogic.Save(metaDataToSave);

				transaction.Complete();
			}

			return folderSettingToSave ?? folderSettingInDb;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		public DocSpprjFolderSettingEntity SaveSetting(SaveSharePointFolderSettingParam param)
		{
			ArgumentNullException.ThrowIfNull(param);
			ArgumentNullException.ThrowIfNull(param.FolderSettingComplete);

			var projectConfigLogic = new DocSpProjectConfigLogic();
			var structureLogic = new DocSpprjFolderStructLogic();
			var detailStructureLogic = new DocSpprjDetailStructLogic();
			var metaDataLogic = new DocSPProjectMetaDataLogic();
			var bulkSaveHelper = new BulkSaveHelper();

			var complete = param.FolderSettingComplete;
			var projectConfig = projectConfigLogic.GetByFilter(e => e.PrjProjectFk == param.ProjectId).FirstOrDefault();
			var folderSettingInDb = this.GetByFilter(e => e.PrjProjectFk == param.ProjectId).FirstOrDefault();

			var folderSettingToSave = new List<DocSpprjFolderSettingEntity>();
			var projectConfigToCreate = new List<DocSpProjectConfigEntity>();
			var projectConfigToSave = new List<DocSpProjectConfigEntity>();
			var folderStructuresToSave = new List<DocSpprjFolderStructEntity>();
			var folderStructuresToDelete = new List<DocSpprjFolderStructEntity>();
			var metaDataToSave = new List<DocSpprjMetaDataEntity>();
			var metaDataToCreate = new List<DocSpprjMetaDataEntity>();
			var metaDataToDelete = new List<DocSpprjMetaDataEntity>();

			if (projectConfig == null)
			{
				projectConfig = projectConfigLogic.Create();
				projectConfig.PrjProjectFk = param.ProjectId;
				projectConfigToCreate.Add(projectConfig);
			}

			if (folderSettingInDb == null)
			{
				complete.FolderSetting = InitNewProjectFolderSetting(complete.FolderSetting, this.GetNextSequence(), param.ProjectId);
				folderSettingToSave.Add(complete.FolderSetting);
			}
			else
			{
				complete.FolderSetting.Id = folderSettingInDb.Id;
				complete.FolderSetting.IsTemplate = false;
				complete.FolderSetting.Name = folderSettingInDb.Name;
			}

			if (projectConfig != null && projectConfig.PrjDocSpprjFolderSettingFk != complete.FolderSetting.Id)
			{
				projectConfig.PrjDocSpprjFolderSettingFk = complete.FolderSetting.Id;
				if (projectConfig.Version > 0)
				{
					projectConfigToSave.Add(projectConfig);
				}
			}

			List<int> settingId4FolderStrucToDelete = [];
			if (!complete.FolderStructures.IsNullOrEmpty())
			{
				var folderStructuresInDb = structureLogic.GetItemsByFolderSettingIds([complete.FolderSetting.Id]);
				var structures = complete.FolderStructures;
				if (projectConfig.IsProjectSynced)
				{
					var structuresToCreate = new List<DocSpprjFolderStructEntity>();
					var folderStrucMap = folderStructuresInDb.OrderBy(e => e.Level)
						.GroupBy(e => e.Level)
						.ToDictionary(e => e.Key, e => e.First());
					var structuresMap = structures.OrderBy(e => e.Level).GroupBy(e => e.Level).ToDictionary(e => e.Key, e => e.First());
					var count = Math.Max(structuresMap.Count, folderStrucMap.Count);

					for (var i = 0; i < count; ++i)
					{
						structuresMap.TryGetValue(i, out var structure);
						folderStrucMap.TryGetValue(i, out var structureInDb);
						if (structure != null && structureInDb != null) // if some meta data type of structure is found, update values
						{
							if (structure.MetaDataType == structureInDb.MetaDataType)
							{
								structureInDb.ShareOption = structure.ShareOption;
								structureInDb.SpUsers = structure.SpUsers;
								folderStructuresToSave.Add(structureInDb);
							}
						}
						else if (structure != null) // if new structure exists, new one
						{
							structuresToCreate.Add(structure);
						}
					}
					structureLogic.InitEntities(structuresToCreate, complete.FolderSetting.Id);
					folderStructuresToSave.AddRange(structuresToCreate);
				}
				else
				{
					List<DocSpprjFolderStructEntity> folderStructureToCreate = [];
					DocSpprjFolderStructLogic.CollectFolderStructureChanges(complete.FolderSetting.Id, structures, folderStructuresInDb,
						ref folderStructuresToSave, ref folderStructureToCreate, ref folderStructuresToDelete);

					structureLogic.InitEntities(folderStructureToCreate, complete.FolderSetting.Id);
					folderStructuresToSave.AddRange(folderStructureToCreate);
				}
			}
			else if (!projectConfig.IsProjectSynced)
			{
				settingId4FolderStrucToDelete.Add(complete.FolderSetting.Id);
			}

			var metaDataListInDb = metaDataLogic.GetByFilter(e => e.PrjDocSpprjFolderSettingFk == complete.FolderSetting.Id);
			metaDataLogic.CollectProjectMetaDataChanges(complete.MetaDataType, complete.FolderSetting.Id, metaDataListInDb,
				ref metaDataToSave, ref metaDataToCreate, ref metaDataToDelete);

			metaDataToSave.AddRange(metaDataToCreate);

			var detailStructureToUpdate = new List<DocSpprjDetailStructEntity>();
			var detailStructureToCreate = new List<DocSpprjDetailStructEntity>();
			var detailStructureToDelete = new List<DocSpprjDetailStructEntity>();
			detailStructureLogic.CollectDetailStructureChanges([complete.FolderSetting.Id], complete.FolderStructures,
				ref detailStructureToUpdate, ref detailStructureToCreate, ref detailStructureToDelete);

			projectConfigToSave.AddRange(projectConfigToCreate);

			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				this.Save(folderSettingToSave);
				projectConfigLogic.Save(projectConfigToSave);

				structureLogic.Delete(folderStructuresToDelete);
				structureLogic.DeleteFolderStrucByFs(settingId4FolderStrucToDelete);
				detailStructureLogic.DeleteDetailStrucByFs(settingId4FolderStrucToDelete);
				structureLogic.Save(folderStructuresToSave);

				metaDataLogic.Delete(metaDataToDelete);
				metaDataLogic.Save(metaDataToSave);

				using (var dbContext = this.CreateDbContext())
				{
					bulkSaveHelper.BulkUpdate(dbContext, detailStructureToUpdate);
					bulkSaveHelper.BulkInsert(dbContext, detailStructureToCreate);
					bulkSaveHelper.BulkDelete(dbContext, detailStructureToDelete);
				}

				transaction.Complete();
			}

			return complete.FolderSetting;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		public DocSpprjFolderSettingEntity SaveSettingToProjectsSelected(SaveSharePointFolderSettingParam param)
		{
			ArgumentNullException.ThrowIfNull(param);
			ArgumentNullException.ThrowIfNull(param.FolderSettingComplete);
			ArgumentNullException.ThrowIfNull(param.ProjectIdsSelected);
			var complete = param.FolderSettingComplete;

			var projectConfigLogic = new DocSpProjectConfigLogic();
			var folderSettingLogic = new DocSpprjFolderSettingLogic();
			var detailStructLogic = new DocSpprjDetailStructLogic();
			var folderStructureLogic = new DocSpprjFolderStructLogic();
			var metaDataLogic = new DocSPProjectMetaDataLogic();
			var bulkSaveHelper = new BulkSaveHelper();

			var projectIds = param.ProjectIdsSelected;
			var projects = Injector.Get<IGetProjectLogic>().GetProjectsById(projectIds);
			var spPrjConfigDic = projectConfigLogic.GetByFilter(e => projectIds.Contains(e.PrjProjectFk))
				.GroupBy(e => e.PrjProjectFk)
				.ToDictionary(e => e.Key, e => e.First());

			var projectConfigsToSync = new List<DocSpProjectConfigEntity>();
			var projectIdsToSync = new List<int>();
			foreach (var project in projects)
			{
				var projectId = project.Id;
				if (!spPrjConfigDic.TryGetValue(projectId, out var projectConfig))
				{
					projectConfig = new DocSpProjectConfigEntity();
					projectConfig.PrjProjectFk = projectId;
					spPrjConfigDic.Add(projectId, projectConfig);
				}

				if (projectConfig.IsProjectSynced)
				{
					continue;
				}

				projectConfigsToSync.Add(projectConfig);
				projectIdsToSync.Add(project.Id);
			}

			var folderSettings = folderSettingLogic.GetItemsByProjectIds(projectIdsToSync);
			var folderSettingMap = folderSettings.ToDictionary(e => e.Id);
			var folderSettingIds = folderSettings.CollectIds(e => e.Id);
			var folderStructuresInDb = folderStructureLogic.GetItemsByFolderSettingIds(folderSettingIds);
			var metaDataListInDb = metaDataLogic.GetItemsByFolderSettingIds(folderSettingIds);
			var folderStructuresMap = folderStructuresInDb.GroupBy(e => e.PrjDocSpprjFolderSettingFk)
				.ToDictionary(e => e.Key, e => e.ToList());
			var metaDataMap = metaDataListInDb.GroupBy(e => e.PrjDocSpprjFolderSettingFk)
				.ToDictionary(e => e.Key, e => e.ToList());

			var fakeId = 0;
			var projectConfigToUpdate = new List<DocSpProjectConfigEntity>();
			var projectConfigToCreate = new List<DocSpProjectConfigEntity>();
			var folderSettingToCreate = new List<DocSpprjFolderSettingEntity>();

			// folder setting does not need to update, but its sub data needs.
			var folderSettingAffected = new List<DocSpprjFolderSettingEntity>();
			// key: folder setting id, value: folder structures to update
			var folderStructureToUpdateMap = new Dictionary<int, List<DocSpprjFolderStructEntity>>();
			// key: folder setting id, value: folder structures to create
			var folderStructureToCreateMap = new Dictionary<int, List<DocSpprjFolderStructEntity>>();
			// key: folder setting id, value: folder structures to delete
			var folderStructureToDeleteMap = new Dictionary<int, List<DocSpprjFolderStructEntity>>();
			// key: folder setting id, value: project meta data list to update
			var metaDataToUpdateMap = new Dictionary<int, List<DocSpprjMetaDataEntity>>();
			// key: folder setting id, value: project meta data list to create
			var metaDataToCreateMap = new Dictionary<int, List<DocSpprjMetaDataEntity>>();
			// key: folder setting id, value: project meta data list to delete
			var metaDataToDeleteMap = new Dictionary<int, List<DocSpprjMetaDataEntity>>();

			var newProjectConfigCount = 0;
			var newFolderSettingCount = 0;
			var newFolderStrucCount = 0;
			var newMetaDataCount = 0;

			var folderSettingCompletes = new List<SharePointFolderSettingComplete>();
			var currentConfigFolderSetting = param.FolderSettingComplete.FolderSetting;

			foreach (var config in projectConfigsToSync)
			{
				DocSpprjFolderSettingEntity folderSetting = null;
				if (!config.PrjDocSpprjFolderSettingFk.HasValue)
				{
					folderSetting = complete.FolderSetting.Clone() as DocSpprjFolderSettingEntity;
					folderSetting = InitNewProjectFolderSetting(folderSetting, --fakeId, config.PrjProjectFk);
					folderSettingToCreate.Add(folderSetting);
					newFolderSettingCount++;

					// project config
					config.PrjDocSpprjFolderSettingFk = folderSetting.Id;
					if (config.Version == 0)
					{
						projectConfigToCreate.Add(config);
						newProjectConfigCount++;
					}
					else
					{
						projectConfigToUpdate.Add(config);
					}

					if (config.PrjProjectFk == param.ProjectId)
					{
						currentConfigFolderSetting = folderSetting;
					}
				}
				else if (folderSettingMap.TryGetValue(config.PrjDocSpprjFolderSettingFk.Value, out folderSetting))
				{
					// collect the folder settings to be affected.
					folderSettingAffected.Add(folderSetting);

					if (config.PrjProjectFk == param.ProjectId)
					{
						currentConfigFolderSetting = folderSetting;
					}
				}
			}

			// collect the sub data to create and update (including folder structure and meta data)
			foreach (var setting in folderSettingAffected)
			{
				folderStructuresMap.TryGetValue(setting.Id, out var structuresInDb);
				structuresInDb = structuresInDb ?? new List<DocSpprjFolderStructEntity>();
				var folderStructuresToUpdate = new List<DocSpprjFolderStructEntity>();
				var folderStructuresToCreate = new List<DocSpprjFolderStructEntity>();
				var folderStructuresToDelete = new List<DocSpprjFolderStructEntity>();
				DocSpprjFolderStructLogic.CollectFolderStructureChanges(setting.Id, complete.FolderStructures, structuresInDb,
					ref folderStructuresToUpdate, ref folderStructuresToCreate, ref folderStructuresToDelete);

				DocSpprjFolderStructLogic.UpdateFolderStructureMap(setting.Id, folderStructureToUpdateMap, folderStructuresToUpdate);
				DocSpprjFolderStructLogic.UpdateFolderStructureMap(setting.Id, folderStructureToCreateMap, folderStructuresToCreate);
				DocSpprjFolderStructLogic.UpdateFolderStructureMap(setting.Id, folderStructureToDeleteMap, folderStructuresToDelete);
				newFolderStrucCount = newFolderStrucCount + folderStructuresToCreate.Count;

				metaDataMap.TryGetValue(setting.Id, out var metaDataListBySettingIdInDb);
				metaDataListBySettingIdInDb = metaDataListBySettingIdInDb ?? new List<DocSpprjMetaDataEntity>();
				var metaDataToSaveTemp = new List<DocSpprjMetaDataEntity>();
				var metaDataToCreateTemp = new List<DocSpprjMetaDataEntity>();
				var metaDataToDeleteTemp = new List<DocSpprjMetaDataEntity>();
				metaDataLogic.CollectProjectMetaDataChanges(complete.MetaDataType, setting.Id, metaDataListBySettingIdInDb,
					ref metaDataToSaveTemp, ref metaDataToCreateTemp, ref metaDataToDeleteTemp);
				DocSPProjectMetaDataLogic.UpdateProjectMetaDataMap(setting.Id, metaDataToUpdateMap, metaDataToSaveTemp);
				DocSPProjectMetaDataLogic.UpdateProjectMetaDataMap(setting.Id, metaDataToCreateMap, metaDataToCreateTemp);
				DocSPProjectMetaDataLogic.UpdateProjectMetaDataMap(setting.Id, metaDataToDeleteMap, metaDataToDeleteTemp);
				newMetaDataCount = newMetaDataCount + metaDataToUpdateMap.Count;
			}

			// collect the sub data to create (including folder structure and meta data)
			foreach (var setting in folderSettingToCreate)
			{
				if (!folderStructureToCreateMap.TryGetValue(setting.Id, out var structures))
				{
					structures = [];
					folderStructureToCreateMap.Add(setting.Id, structures);
				}

				if (!metaDataToCreateMap.TryGetValue(setting.Id, out var metaDataList))
				{
					metaDataList = [];
					metaDataToCreateMap.Add(setting.Id, metaDataList);
				}

				if (!complete.FolderStructures.IsNullOrEmpty())
				{
					foreach (var folderStruct in complete.FolderStructures)
					{
						var newFolderStruct = folderStruct.Clone() as DocSpprjFolderStructEntity;
						newFolderStruct.PrjDocSpprjFolderSettingFk = setting.Id;
						newFolderStruct.Version = 0;
						structures.Add(newFolderStruct);
						newFolderStrucCount = newFolderStrucCount + structures.Count;
					}
				}

				if (complete.MetaDataType.HasValue)
				{
					var newMetaData = new DocSpprjMetaDataEntity();
					newMetaData.MetadataType = complete.MetaDataType.Value;
					metaDataList.Add(newMetaData);
					newMetaDataCount++;
				}
			}

			// new ids
			var projectConfigIds = newProjectConfigCount > 0 ?
				projectConfigLogic.GetNextSequences(projectConfigToCreate.Count).GetEnumerator() : new List<int>().GetEnumerator();
			var settingIds = newFolderSettingCount > 0 ?
				folderSettingLogic.GetNextSequences(newFolderSettingCount).GetEnumerator() : new List<int>().GetEnumerator();
			var strucIds = newFolderStrucCount > 0 ?
				folderStructureLogic.GetNextSequences(newFolderStrucCount).GetEnumerator() : new List<int>().GetEnumerator();
			var metaDataIds = newMetaDataCount > 0 ?
				metaDataLogic.GetNextSequences(newMetaDataCount).GetEnumerator() : new List<int>().GetEnumerator();

			foreach (var setting in folderSettingAffected)
			{
				var id = setting.Id;
				if (folderStructureToCreateMap.TryGetValue(id, out var structures))
				{
					foreach (var struc in structures)
					{
						strucIds.MoveNext();
						struc.Id = strucIds.Current;
						struc.PrjDocSpprjFolderSettingFk = id;
					}
				}

				if (metaDataToCreateMap.TryGetValue(id, out var metaDataList))
				{
					foreach (var metaData in metaDataList)
					{
						metaDataIds.MoveNext();
						metaData.Id = metaDataIds.Current;
						metaData.PrjDocSpprjFolderSettingFk = id;
					}
				}

				var settingComplete = new SharePointFolderSettingComplete()
				{
					FolderSetting = setting,
					FolderStructures = structures,
					MetaData = metaDataList.FirstOrDefault()
				};

				folderSettingCompletes.Add(settingComplete);
			}

			var folderSettingFakeId2NewIdMap = new Dictionary<int, int>();

			foreach (var setting in folderSettingToCreate)
			{
				settingIds.MoveNext();
				var oldId = setting.Id;
				setting.Id = settingIds.Current;
				if (folderSettingFakeId2NewIdMap.ContainsKey(oldId))
				{
					folderSettingFakeId2NewIdMap[oldId] = setting.Id;
				}
				else
				{
					folderSettingFakeId2NewIdMap.Add(oldId, setting.Id);
				}

				if (folderStructureToCreateMap.TryGetValue(oldId, out var structures))
				{
					foreach (var struc in structures)
					{
						strucIds.MoveNext();
						struc.Id = strucIds.Current;
						struc.PrjDocSpprjFolderSettingFk = setting.Id;
					}
				}

				if (metaDataToCreateMap.TryGetValue(oldId, out var metaDataList))
				{
					foreach (var metaData in metaDataList)
					{
						metaDataIds.MoveNext();
						metaData.Id = metaDataIds.Current;
						metaData.PrjDocSpprjFolderSettingFk = setting.Id;
					}
				}

				var settingComplete = new SharePointFolderSettingComplete()
				{
					FolderSetting = setting,
					FolderStructures = structures,
					MetaData = metaDataList.FirstOrDefault()
				};

				folderSettingCompletes.Add(settingComplete);
			}

			foreach (var config in projectConfigToCreate)
			{
				projectConfigIds.MoveNext();
				config.Id = projectConfigIds.Current;
				if (!folderSettingFakeId2NewIdMap.ContainsKey(config.PrjDocSpprjFolderSettingFk.Value))
				{
					continue;
				}
				config.PrjDocSpprjFolderSettingFk = folderSettingFakeId2NewIdMap[config.PrjDocSpprjFolderSettingFk.Value];
			}

			foreach (var config in projectConfigToUpdate)
			{
				if (!config.PrjDocSpprjFolderSettingFk.HasValue || !folderSettingFakeId2NewIdMap.ContainsKey(config.PrjDocSpprjFolderSettingFk.Value))
				{
					continue;
				}
				config.PrjDocSpprjFolderSettingFk = folderSettingFakeId2NewIdMap[config.PrjDocSpprjFolderSettingFk.Value];
			}

			var folderStructureToUpdate = folderStructureToUpdateMap.SelectMany(e => e.Value).ToList();
			var folderStructureToCreate = folderStructureToCreateMap.SelectMany(e => e.Value).ToList();
			var folderStructureToDelete = folderStructureToDeleteMap.SelectMany(e => e.Value).ToList();
			var metaDataToUpdate = metaDataToUpdateMap.SelectMany(e => e.Value).ToList();
			var metaDataToCreate = metaDataToCreateMap.SelectMany(e => e.Value).ToList();
			var metaDataToDelete = metaDataToDeleteMap.SelectMany(e => e.Value).ToList();

			var detailStructureToUpdate = new List<DocSpprjDetailStructEntity>();
			var detailStructureToCreate = new List<DocSpprjDetailStructEntity>();
			var detailStructureToDelete = new List<DocSpprjDetailStructEntity>();
			detailStructLogic.CollectDetailStructureChanges(folderSettingIds, complete.FolderStructures,
				ref detailStructureToUpdate, ref detailStructureToCreate, ref detailStructureToDelete);


			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				using (var dbContext = this.CreateDbContext())
				{
					bulkSaveHelper.BulkInsert(dbContext, folderSettingToCreate);
					bulkSaveHelper.BulkInsert(dbContext, projectConfigToCreate);
					bulkSaveHelper.BulkUpdate(dbContext, projectConfigToUpdate);

					bulkSaveHelper.BulkUpdate(dbContext, folderStructureToUpdate);
					bulkSaveHelper.BulkInsert(dbContext, folderStructureToCreate);
					bulkSaveHelper.BulkDelete(dbContext, folderStructureToDelete);

					bulkSaveHelper.BulkUpdate(dbContext, metaDataToUpdate);
					bulkSaveHelper.BulkInsert(dbContext, metaDataToCreate);
					bulkSaveHelper.BulkDelete(dbContext, metaDataToDelete);

					bulkSaveHelper.BulkUpdate(dbContext, detailStructureToUpdate);
					bulkSaveHelper.BulkInsert(dbContext, detailStructureToCreate);
					bulkSaveHelper.BulkDelete(dbContext, detailStructureToDelete);
				}

				transaction.Complete();
			}

			return currentConfigFolderSetting;
		}

		private static bool HasParametersNull(params object[] parameters)
		{
			var hasNull = false;
			foreach (var param in parameters)
			{
				if (param == null)
				{
					hasNull = true;
					break;
				}
			}
			return hasNull;
		}

		private static DocSpprjFolderSettingEntity InitNewProjectFolderSetting(DocSpprjFolderSettingEntity setting, int id, int projectId)
		{
			if (HasParametersNull(setting))
			{
				return setting;
			}

			setting.Id = id;
			setting.PrjProjectFk = projectId;
			setting.Name = "With Setting";
			setting.IsTemplate = false;
			setting.Version = 0;
			return setting;
		}
	}
}
