
import {_businessPartnerPage,_projectPage, _common, _controllingUnit, _package, _sidebar, _mainView, _validate, _modalView, _rfqPage, _saleContractPage, _procurementContractPage } from 'cypress/pages';
import { cnt, tile, app, sidebar, commonLocators, btn } from 'cypress/locators';
import common from 'mocha/lib/interfaces/common';
import type { DataCells } from 'cypress/pages/interfaces.d.ts'
import Buttons from 'cypress/locators/buttons';
import CommonLocators from 'cypress/locators/common-locators';

const PROJECT_NO = _common.generateRandomString(4);
const  PROJECT_DESC=_common.generateRandomString(4);

let PROJECTS_PARAMETERS
let CONTRACT_PARAMETER
let  CONTAINER_COLUMNS_SUBCONTRACTOR
let CONTAINERS_CONTRACT


describe('PCM- 4.139 | Subcontract container in contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('pcm/con-4.139-subcontractors-container-in-contract-module.json').then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
            CONTAINER_COLUMNS_SUBCONTRACTOR = this.data.CONTAINER_COLUMNS.SUB_CONTRACT
            PROJECTS_PARAMETERS = {
                [commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
                [commonLocators.CommonLabels.NAME]: PROJECT_DESC,
                [commonLocators.CommonLabels.CLERK]: "HS"
            };
         
            CONTRACT_PARAMETER={
                [commonLocators.CommonLabels.BUSINESS_PARTNER]:CONTAINERS_CONTRACT.AdolfKoch
            }
        
        });
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
       
    });
    after(() => {
        cy.LOGOUT();
    });
    it("TC - Create new project", function () {
        _common.openDesktopTile(tile.DesktopTiles.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.create_newRecord(cnt.uuid.PROJECTS)
        _projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
    });
    it('TC - Create contract ', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS);
        });
    
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        
    });
    it("TC - Create subcontractor record", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
          _common.select_tabFromFooter(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.FooterTab.SUBCONTRACTOR, 2);
          _common.setup_gridLayout(cnt.uuid.CONTRACT_SUBCONTRACTOR, CONTAINER_COLUMNS_SUBCONTRACTOR)
        });

        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.BPD_BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.AntonWaliser)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.BPD_BUSINESS_PARTNER_FK)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.PRC_STRUCTURE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_CONTRACT.STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.BPD_BUSINESS_PARTNER_FK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.CONTRACT_SUBCONTRACTOR)

    })
    it("TC - Delete subcontractor record and verify it", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
          _common.select_tabFromFooter(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.FooterTab.SUBCONTRACTOR, 2);
        });
        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        _common.delete_recordFromContainer(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordDeleted(cnt.uuid.CONTRACT_SUBCONTRACTOR,CONTAINERS_CONTRACT.AntonWaliser)
    });
    it("TC - Verify lookup business partner successfully ", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.FooterTab.SUBCONTRACTOR, 2);
          });
          _common.waitForLoaderToDisappear()
          _common.maximizeContainer(cnt.uuid.CONTRACT_SUBCONTRACTOR)
          _common.waitForLoaderToDisappear()
          _common.create_newRecord(cnt.uuid.CONTRACT_SUBCONTRACTOR)
          _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.BPD_BUSINESS_PARTNER_FK)
          _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.BPD_BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.AntonWaliser)
           cy.wait(1000)//Need time to add BP
          _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.PRC_STRUCTURE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.STRUCTURE)
          cy.wait(1000)//Need time to add STRUCTURE
          _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.BPD_BUSINESS_PARTNER_FK)
          _common.waitForLoaderToDisappear()
          cy.SAVE()
          _common.waitForLoaderToDisappear()
          _common.waitForLoaderToDisappear()
          cy.SAVE()
         _common.waitForLoaderToDisappear()
          _common.waitForLoaderToDisappear()
          _common.edit_containerCell(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.USER_DEFINED_DATE_1,app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT,_common.getDate(commonLocators.CommonKeys.CURRENT_SMALL))
          _common.waitForLoaderToDisappear()
          _common.edit_containerCell(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.USER_DEFINED_DATE_2,app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT,_common.getDate(CommonLocators.CommonKeys.INCREMENTED_SMALL,1))
          _common.waitForLoaderToDisappear()
          _common.edit_containerCell(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.USER_DEFINED_DATE_3,app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT,_common.getDate(CommonLocators.CommonKeys.INCREMENTED_SMALL,2))
          _common.waitForLoaderToDisappear()
          cy.SAVE()
          _common.waitForLoaderToDisappear()
    });
    it("TC - Verify business partner and structure lookup", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
          _common.select_tabFromFooter(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.FooterTab.SUBCONTRACTOR, 2);
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_SUBCONTRACTOR, CONTAINERS_CONTRACT.AntonWaliser)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.BPD_BUSINESS_PARTNER_FK, CONTAINERS_CONTRACT.AntonWaliser)
        _validate.verify_dataUnderFilter(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.PRC_STRUCTURE_FK, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.GRID, CONTAINERS_CONTRACT.STRUCTURE)
        _common.minimizeContainer(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        
    });
    it("TC - Verify the branches contract and supplier get value after set to business partner", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
          _common.select_tabFromFooter(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.FooterTab.SUBCONTRACTOR, 2);
        });
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_SUBCONTRACTOR,app.GridCells.BPD_SUBSIDIARY_FK_CAPS, CONTAINERS_CONTRACT.BRANCH)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_SUBCONTRACTOR,app.GridCells.BPD_SUPPLIER_FK, CONTAINERS_CONTRACT.SUPPLIER)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_SUBCONTRACTOR,app.GridCells.BPD_CONTACT_FK, CONTAINERS_CONTRACT.CONTRACT)
  
        
    });

    
});
