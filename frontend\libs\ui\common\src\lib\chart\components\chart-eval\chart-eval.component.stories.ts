/*
 * Copyright(c) RIB Software GmbH
 */

import { CommonModule } from '@angular/common';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { Chart, registerables, ChartOptions } from 'chart.js';
import { UiCommonChartEvalComponent } from './chart-eval.component';
import { ChartDataProcessService } from '../../service/chart-data-process.service';
import { ChartTypeEnum } from '../../entity/chart-type.enum';
import { IChartDataSource } from '../../interface/chart-data.interface';
import { ServiceLocator } from '@libs/platform/common';
import { Type } from '@angular/core';

// Register Chart.js components
Chart.register(...registerables);

// Type for doughnut chart options including cutout
interface DoughnutChartOptions extends ChartOptions {
  cutout?: string | number;
}

// Mock ServiceLocator for Storybook
const mockServiceLocator = {
  injector: {
    get: <T>(token: Type<T>): T | null => {
      if (token === ChartDataProcessService) {
        return new ChartDataProcessService() as T;
      }
      return null;
    }
  }
};

// Set up the ServiceLocator mock before using it
Object.defineProperty(ServiceLocator, 'injector', {
  value: mockServiceLocator.injector,
  writable: true,
  configurable: true
});

// Mock data for different chart types
const sampleLabels = ['January', 'February', 'March', 'April', 'May', 'June'];

const barChartData: IChartDataSource = {
  datasets: [
    {
      label: 'Sales',
      data: [65, 59, 80, 81, 56, 55],
      backgroundColor: 'rgba(54, 162, 235, 0.6)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 1
    },
    {
      label: 'Revenue', 
      data: [28, 48, 40, 19, 86, 27],
      backgroundColor: 'rgba(255, 99, 132, 0.6)',
      borderColor: 'rgba(255, 99, 132, 1)',
      borderWidth: 1
    }
  ],
  labels: sampleLabels,
  legends: [
    { name: 'Sales' },
    { name: 'Revenue' }
  ]
};

const lineChartData: IChartDataSource = {
  datasets: [
    {
      label: 'Temperature',
      data: [20, 25, 22, 28, 32, 30],
      backgroundColor: 'rgba(75, 192, 192, 0.2)',
      borderColor: 'rgba(75, 192, 192, 1)',
      borderWidth: 2,
      fill: false
    },
    {
      label: 'Humidity',
      data: [65, 70, 68, 75, 80, 78],
      backgroundColor: 'rgba(153, 102, 255, 0.2)',
      borderColor: 'rgba(153, 102, 255, 1)',
      borderWidth: 2,
      fill: false
    }
  ],
  labels: sampleLabels,
  legends: [
    { name: 'Temperature' },
    { name: 'Humidity' }
  ]
};

const pieChartData: IChartDataSource = {
  datasets: [
    {
      label: 'Market Share',
      data: [30, 25, 20, 15, 10],
      backgroundColor: [
        'rgba(255, 99, 132, 0.8)',
        'rgba(54, 162, 235, 0.8)',
        'rgba(255, 205, 86, 0.8)',
        'rgba(75, 192, 192, 0.8)',
        'rgba(153, 102, 255, 0.8)'
      ],
      borderColor: [
        'rgba(255, 99, 132, 1)',
        'rgba(54, 162, 235, 1)',
        'rgba(255, 205, 86, 1)',
        'rgba(75, 192, 192, 1)',
        'rgba(153, 102, 255, 1)'
      ],
      borderWidth: 1
    }
  ],
  labels: ['Product A', 'Product B', 'Product C', 'Product D', 'Product E'],
  legends: [
    { name: 'Market Share' }
  ]
};

const doughnutChartData: IChartDataSource = {
  datasets: [
    {
      label: 'Budget Distribution',
      data: [40, 30, 20, 10],
      backgroundColor: [
        'rgba(255, 99, 132, 0.8)',
        'rgba(54, 162, 235, 0.8)',
        'rgba(255, 205, 86, 0.8)',
        'rgba(75, 192, 192, 0.8)'
      ],
      borderColor: [
        'rgba(255, 99, 132, 1)',
        'rgba(54, 162, 235, 1)',
        'rgba(255, 205, 86, 1)',
        'rgba(75, 192, 192, 1)'
      ],
      borderWidth: 2
    }
  ],
  labels: ['Marketing', 'Development', 'Sales', 'Support'],
  legends: [
    { name: 'Budget Distribution' }
  ]
};

const radarChartData: IChartDataSource = {
  datasets: [
    {
      label: 'Skills Assessment',
      data: [80, 90, 70, 85, 75, 95],
      backgroundColor: 'rgba(54, 162, 235, 0.2)',
      borderColor: 'rgba(54, 162, 235, 1)',
      borderWidth: 2
    },
    {
      label: 'Team Average',
      data: [70, 80, 75, 78, 82, 85],
      backgroundColor: 'rgba(255, 99, 132, 0.2)',
      borderColor: 'rgba(255, 99, 132, 1)',
      borderWidth: 2
    }
  ],
  labels: ['JavaScript', 'TypeScript', 'Angular', 'React', 'Node.js', 'Testing'],
  legends: [
    { name: 'Skills Assessment' },
    { name: 'Team Average' }
  ]
};


const meta: Meta<UiCommonChartEvalComponent> = {
    title: 'UI Common/Chart/Chart Eval',
    component: UiCommonChartEvalComponent,
    parameters: {
        docs: {
            description: {
                component: `
A comprehensive chart component built on Chart.js that supports multiple chart types with data processing capabilities.


                `,
            },
        },
        layout: 'fullscreen',
    },
    argTypes: {
        chartType: {
            control: { type: 'select' },
            options: Object.values(ChartTypeEnum),
            description: 'Type of chart to display',
        },
        chartUuid: {
            control: { type: 'text' },
            description: 'Unique identifier for the chart',
        },
        legendColors: {
            control: { type: 'object' },
            description: 'Array of custom colors for chart legends',
        },
        chartDataSource: {
            control: { type: 'object' },
            description: 'Chart data including datasets, labels, and legends',
        },
        chartOption: {
            control: { type: 'object' },
            description: 'Chart.js options for customizing chart behavior',
        },
    },
    decorators: [
        moduleMetadata({
            imports: [CommonModule],
            providers: [
                ChartDataProcessService,
                {
                    provide: ServiceLocator,
                    useValue: {
                        injector: {
                            get: <T>(token: Type<T>): T | null => {
                                if (token === ChartDataProcessService) {
                                    return new ChartDataProcessService() as T;
                                }
                                return null;
                            }
                        }
                    }
                }
            ]
        }),
    ],
};

export default meta;

type Story = StoryObj<UiCommonChartEvalComponent>;

export const BarChart: Story = {
  args: {
    chartType: ChartTypeEnum.bar,
    chartDataSource: barChartData,
    chartUuid: 'bar-chart-example',
    legendColors: ['rgba(54, 162, 235, 1)', 'rgba(255, 99, 132, 1)'],
    chartOption: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: 'Monthly Sales and Revenue'
        },
        legend: {
          position: 'top'
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Amount ($)'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Months'
          }
        }
      }
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'A vertical bar chart showing sales and revenue data over months.'
      }
    }
  }
};

export const LineChart: Story = {
  args: {
    chartType: ChartTypeEnum.line,
    chartDataSource: lineChartData,
    chartUuid: 'line-chart-example',
    legendColors: ['rgba(75, 192, 192, 1)', 'rgba(153, 102, 255, 1)'],
    chartOption: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: 'Weather Data Trends'
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Value'
          }
        }
      },
      elements: {
        point: {
          radius: 6,
          hoverRadius: 8
        },
        line: {
          tension: 0.2
        }
      }
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'A line chart perfect for showing trends and changes over time.'
      }
    }
  }
};

export const PieChart: Story = {
  args: {
    chartType: ChartTypeEnum.pie,
    chartDataSource: pieChartData,
    chartUuid: 'pie-chart-example',
    legendColors: [
      'rgba(255, 99, 132, 1)',
      'rgba(54, 162, 235, 1)',
      'rgba(255, 205, 86, 1)',
      'rgba(75, 192, 192, 1)',
      'rgba(153, 102, 255, 1)'
    ],
    chartOption: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: 'Market Share Distribution'
        },
        legend: {
          position: 'right'
        }
      }
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'A pie chart showing proportional data distribution across categories.'
      }
    }
  }
};

export const DoughnutChart: Story = {
  args: {
    chartType: ChartTypeEnum.doughnut,
    chartDataSource: doughnutChartData,
    chartUuid: 'doughnut-chart-example',
    legendColors: [
      'rgba(255, 99, 132, 1)',
      'rgba(54, 162, 235, 1)',
      'rgba(255, 205, 86, 1)',
      'rgba(75, 192, 192, 1)'
    ],
    chartOption: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: 'Budget Allocation'
        },
        legend: {
          position: 'bottom'
        }
      },
      cutout: '50%'
    } as DoughnutChartOptions
  },
  parameters: {
    docs: {
      description: {
        story: 'A doughnut chart with a hollow center, ideal for displaying budget or resource allocation.'
      }
    }
  }
};

export const RadarChart: Story = {
  args: {
    chartType: ChartTypeEnum.radar,
    chartDataSource: radarChartData,
    chartUuid: 'radar-chart-example',
    legendColors: ['rgba(54, 162, 235, 1)', 'rgba(255, 99, 132, 1)'],
    chartOption: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: 'Skills Comparison'
        }
      },
      scales: {
        r: {
          beginAtZero: true,
          max: 100,
          ticks: {
            stepSize: 20
          }
        }
      }
    }
  },
  parameters: {
    docs: {
      description: {
        story: 'A radar chart for comparing multiple variables across different categories.'
      }
    }
  }
};

