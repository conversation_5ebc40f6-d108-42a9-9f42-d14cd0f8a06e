import { ConcreteFieldOverload, createLookup, FieldType, IAdditionalLookupOptions } from '@libs/ui/common';
import { IBilHeaderEntity, IOrdAdvanceStatusEntity, IOrdStatusEntity } from '@libs/sales/interfaces';
import { OrdStatusLookupService } from '../services/lookups/sales-contract-status-lookup.service';
import { OrdAdvanceLineStatusLookupService } from '../services/lookups/sales-contract-advance-status-lookup.service';
import { SalesContractBilHeaderLookupService } from '../services/lookups/sales-contract-bil-header-lookup.service';

export class SalesContractCustomizeLookupOverloadProvider {

	// Overload functions for Ord status, i.e. the database table ORD_STATUS
	public static provideOrdStatusLookupOverload<T extends object>(showClearBtn: boolean, readOnly: boolean): ConcreteFieldOverload<T> | IAdditionalLookupOptions<T> {
		return {
			type: FieldType.Lookup,
			lookupOptions: createLookup<T, IOrdStatusEntity>({
				dataServiceToken: OrdStatusLookupService,
				showClearButton: showClearBtn,
				readonly: readOnly
			})
		};
	}

	public static provideOrdAdvanceStatusLookupOverload<T extends object>(showClearBtn: boolean, readOnly: boolean): ConcreteFieldOverload<T> | IAdditionalLookupOptions<T> {
		return {
			type: FieldType.Lookup,
			lookupOptions: createLookup<T, IOrdAdvanceStatusEntity>({
				dataServiceToken: OrdAdvanceLineStatusLookupService,
				showClearButton: showClearBtn,
				readonly: readOnly
			})
		};
	}

	public static provideBilHeaderLookupOverload<T extends object>(showClearBtn: boolean, readOnly: boolean): ConcreteFieldOverload<T> | IAdditionalLookupOptions<T> {
		return {
			type: FieldType.Lookup,
			lookupOptions: createLookup<T, IBilHeaderEntity>({
				dataServiceToken: SalesContractBilHeaderLookupService,
				showClearButton: showClearBtn,
				readonly: readOnly,
				showDescription: true,
				descriptionMember: 'DescriptionInfo.Description'
			})
		};
	}
}