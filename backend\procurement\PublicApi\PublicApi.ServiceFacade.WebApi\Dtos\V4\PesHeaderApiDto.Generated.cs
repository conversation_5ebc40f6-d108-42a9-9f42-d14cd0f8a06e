﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Procurement.PublicApi.BusinessComponents;

namespace RIB.Visual.Procurement.PublicApi.ServiceFacade.WebApi.V4
{


    /// <summary>
    /// There are no comments for PesHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("PES_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(4)]
    public partial class PesHeaderApiDto : RIB.Visual.Platform.Core.ITypedDto<PesHeaderApiEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class PesHeaderApiDto.
        /// </summary>
        public PesHeaderApiDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class PesHeaderApiDto.
        /// </summary>
        /// <param name="entity">the instance of class PesHeaderApiEntity</param>
        public PesHeaderApiDto(PesHeaderApiEntity entity)
        {
            Id = entity.Id;
            PesStatusId = entity.PesStatusId;
            PesStatusDescription = entity.PesStatusDescription;
            CompanyId = entity.CompanyId;
            CompanyCode = entity.CompanyCode;
            ProjectId = entity.ProjectId;
            ProjectCode = entity.ProjectCode;
            ProjectDescription = entity.ProjectDescription;
            PackageId = entity.PackageId;
            PackageCode = entity.PackageCode;
            PackageDescription = entity.PackageDescription;
            ClerkPrcId = entity.ClerkPrcId;
            ClerkPrcCode = entity.ClerkPrcCode;
            ClerkPrcDescription = entity.ClerkPrcDescription;
            ClerkReqId = entity.ClerkReqId;
            ClerkReqCode = entity.ClerkReqCode;
            ClerkReqDescription = entity.ClerkReqDescription;
            CurrencyId = entity.CurrencyId;
            CurrencyDescription = entity.CurrencyDescription;
            Code = entity.Code;
            Description = entity.Description;
            DocumentDate = entity.DocumentDate;
            SearchPattern = entity.SearchPattern;
            DateDelivered = entity.DateDelivered;
            DateDeliveredfrom = entity.DateDeliveredfrom;
            ConHeaderId = entity.ConHeaderId;
            ConHeaderCode = entity.ConHeaderCode;
            ConHeaderDescription = entity.ConHeaderDescription;
            MdcControllingunitId = entity.MdcControllingunitId;
            MdcControllingunitCode = entity.MdcControllingunitCode;
            MdcControllingunitDescription = entity.MdcControllingunitDescription;
            BusinesspartnerId = entity.BusinesspartnerId;
            BusinesspartnerDescription = entity.BusinesspartnerDescription;
            SubsidiaryId = entity.SubsidiaryId;
            SubsidiaryDescription = entity.SubsidiaryDescription;
            SupplierId = entity.SupplierId;
            SupplierCode = entity.SupplierCode;
            SupplierDescription = entity.SupplierDescription;
            Remark = entity.Remark;
            UserDefined1 = entity.UserDefined1;
            UserDefined2 = entity.UserDefined2;
            UserDefined3 = entity.UserDefined3;
            UserDefined4 = entity.UserDefined4;
            UserDefined5 = entity.UserDefined5;
            PrcConfigurationId = entity.PrcConfigurationId;
            PrcConfigurationDescription = entity.PrcConfigurationDescription;
            PrcStructureId = entity.PrcStructureId;
            PrcStructureCode = entity.PrcStructureCode;
            PrcStructureDescription = entity.PrcStructureDescription;
            PesValue = entity.PesValue;
            PesVat = entity.PesVat;
            Exchangerate = entity.Exchangerate;
            PesValueOc = entity.PesValueOc;
            PesVatOc = entity.PesVatOc;
            PesShipmentinfoId = entity.PesShipmentinfoId;
            Isnotaccrual = entity.Isnotaccrual;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            MdcBillingSchemaId = entity.MdcBillingSchemaId;
            MdcBillingSchemaDescription = entity.MdcBillingSchemaDescription;
            DateEffective = entity.DateEffective;
            VatgroupId = entity.VatgroupId;
            VatgroupDescription = entity.VatgroupDescription;
            PesHeaderId = entity.PesHeaderId;
            PesHeaderCode = entity.PesHeaderCode;
            PesHeaderDescription = entity.PesHeaderDescription;
            BasSalesTaxMethodId = entity.BasSalesTaxMethodId;
            BasSalesTaxMethodDesc = entity.BasSalesTaxMethodDesc;
            LanguageId = entity.LanguageId;
            TotalStandardCost = entity.TotalStandardCost;
            ClerkPrcFamilyName = entity.ClerkPrcFamilyName;
            ClerkPrcFirstName = entity.ClerkPrcFirstName;
            ClerkReqFamilyName = entity.ClerkReqFamilyName;
            ClerkReqFirstName = entity.ClerkReqFirstName;
            ExternalCode = entity.ExternalCode;
            BasLanguageFk = entity.BasLanguageFk;
            BasLanguageDesc = entity.BasLanguageDesc;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for PesStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PesStatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PesStatusId { get; set; }
    
        /// <summary>
        /// There are no comments for PesStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PesStatusDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyId { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string CompanyCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public int? ProjectId { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ProjectCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ProjectDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PackageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PackageFk")]
        public int? PackageId { get; set; }
    
        /// <summary>
        /// There are no comments for PackageCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PackageCode { get; set; }
    
        /// <summary>
        /// There are no comments for PackageDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_DESC", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PackageDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ClerkPrcId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string ClerkPrcCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string ClerkPrcDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public int? ClerkReqId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkReqCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkReqDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 17)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CurrencyId { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 18)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string CurrencyDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 19)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }
    
        /// <summary>
        /// There are no comments for DocumentDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DOCUMENT_DATE", TypeName = "date", Order = 21)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? DocumentDate { get; set; }
    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 22)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public string SearchPattern { get; set; }
    
        /// <summary>
        /// There are no comments for DateDelivered in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERED", TypeName = "date", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateDelivered { get; set; }
    
        /// <summary>
        /// There are no comments for DateDeliveredfrom in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVEREDFROM", TypeName = "date", Order = 24)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDeliveredFrom")]
        public System.DateTime? DateDeliveredfrom { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_ID", TypeName = "int", Order = 25)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConHeaderFk")]
        public int? ConHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_CODE", TypeName = "nvarchar(16)", Order = 26)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ConHeaderCode { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_DESC", TypeName = "nvarchar(252)", Order = 27)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ConHeaderDescription { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 28)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingUnitFk")]
        public int? MdcControllingunitId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(16)", Order = 29)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string MdcControllingunitCode { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 30)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcControllingunitDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 31)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int BusinesspartnerId { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 32)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string BusinesspartnerDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public int? SubsidiaryId { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 34)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SubsidiaryDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 35)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public int? SupplierId { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 36)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierCode { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 37)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 39)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined1 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 40)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined2 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined3 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 42)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined4 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined5 { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 44)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcConfigurationFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcConfigurationId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_DESC", TypeName = "nvarchar(2000)", Order = 45)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcConfigurationDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcStructureId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_ID", TypeName = "int", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcStructureFk")]
        public int? PrcStructureId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcStructureCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_CODE", TypeName = "nvarchar(16)", Order = 47)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PrcStructureCode { get; set; }
    
        /// <summary>
        /// There are no comments for PrcStructureDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_DESC", TypeName = "nvarchar(2000)", Order = 48)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcStructureDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PesValue in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_VALUE", TypeName = "numeric(19,7)", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal PesValue { get; set; }
    
        /// <summary>
        /// There are no comments for PesVat in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_VAT", TypeName = "numeric(19,7)", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal PesVat { get; set; }
    
        /// <summary>
        /// There are no comments for Exchangerate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal Exchangerate { get; set; }
    
        /// <summary>
        /// There are no comments for PesValueOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_VALUE_OC", TypeName = "numeric(19,7)", Order = 52)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal PesValueOc { get; set; }
    
        /// <summary>
        /// There are no comments for PesVatOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_VAT_OC", TypeName = "numeric(19,7)", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal PesVatOc { get; set; }
    
        /// <summary>
        /// There are no comments for PesShipmentinfoId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_SHIPMENTINFO_ID", TypeName = "int", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PesShipmentinfoFk")]
        public int? PesShipmentinfoId { get; set; }
    
        /// <summary>
        /// There are no comments for Isnotaccrual in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISNOTACCRUAL", TypeName = "bit", Order = 55)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsNotAccrual")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isnotaccrual { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 56)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 59)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 60)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }
    
        /// <summary>
        /// There are no comments for MdcBillingSchemaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_ID", TypeName = "int", Order = 61)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BillingSchemaFk")]
        public int? MdcBillingSchemaId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcBillingSchemaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 62)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcBillingSchemaDescription { get; set; }
    
        /// <summary>
        /// There are no comments for DateEffective in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_EFFECTIVE", TypeName = "date", Order = 63)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateEffective { get; set; }
    
        /// <summary>
        /// There are no comments for VatgroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 64)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public int? VatgroupId { get; set; }
    
        /// <summary>
        /// There are no comments for VatgroupDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 65)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string VatgroupDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PesHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_HEADER_ID", TypeName = "int", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PesHeaderFk")]
        public int? PesHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for PesHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_HEADER_CODE", TypeName = "nvarchar(16)", Order = 67)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PesHeaderCode { get; set; }
    
        /// <summary>
        /// There are no comments for PesHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_HEADER_DESC", TypeName = "nvarchar(252)", Order = 68)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PesHeaderDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasSalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 69)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int BasSalesTaxMethodId { get; set; }
    
        /// <summary>
        /// There are no comments for BasSalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 70)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasSalesTaxMethodDesc { get; set; }
    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 71)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int LanguageId { get; set; }
    
        /// <summary>
        /// There are no comments for TotalStandardCost in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TOTAL_STANDARD_COST", TypeName = "numeric(19,7)", Order = 72)]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal TotalStandardCost { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcFamilyName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CLERK_PRC_FAMILY_NAME", TypeName = "nvarchar(252)", Order = 73)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkPrcFamilyName { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcFirstName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CLERK_PRC_FIRST_NAME", TypeName = "nvarchar(252)", Order = 74)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkPrcFirstName { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqFamilyName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CLERK_REQ_FAMILY_NAME", TypeName = "nvarchar(252)", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkReqFamilyName { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqFirstName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CLERK_REQ_FIRST_NAME", TypeName = "nvarchar(252)", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkReqFirstName { get; set; }
    
        /// <summary>
        /// There are no comments for ExternalCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXTERNAL_CODE", TypeName = "nvarchar(252)", Order = 77)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ExternalCode { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public int? BasLanguageFk { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 79)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BasLanguageDesc { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(PesHeaderApiEntity); }
        }

        /// <summary>
        /// Copy the current PesHeaderApiDto instance to a new PesHeaderApiEntity instance.
        /// </summary>
        /// <returns>a new instance of class PesHeaderApiEntity</returns>
        public PesHeaderApiEntity Copy()
        {
          var entity = new PesHeaderApiEntity();

          entity.Id = this.Id;
          entity.PesStatusId = this.PesStatusId;
          entity.PesStatusDescription = this.PesStatusDescription;
          entity.CompanyId = this.CompanyId;
          entity.CompanyCode = this.CompanyCode;
          entity.ProjectId = this.ProjectId;
          entity.ProjectCode = this.ProjectCode;
          entity.ProjectDescription = this.ProjectDescription;
          entity.PackageId = this.PackageId;
          entity.PackageCode = this.PackageCode;
          entity.PackageDescription = this.PackageDescription;
          entity.ClerkPrcId = this.ClerkPrcId;
          entity.ClerkPrcCode = this.ClerkPrcCode;
          entity.ClerkPrcDescription = this.ClerkPrcDescription;
          entity.ClerkReqId = this.ClerkReqId;
          entity.ClerkReqCode = this.ClerkReqCode;
          entity.ClerkReqDescription = this.ClerkReqDescription;
          entity.CurrencyId = this.CurrencyId;
          entity.CurrencyDescription = this.CurrencyDescription;
          entity.Code = this.Code;
          entity.Description = this.Description;
          entity.DocumentDate = this.DocumentDate;
          entity.SearchPattern = this.SearchPattern;
          entity.DateDelivered = this.DateDelivered;
          entity.DateDeliveredfrom = this.DateDeliveredfrom;
          entity.ConHeaderId = this.ConHeaderId;
          entity.ConHeaderCode = this.ConHeaderCode;
          entity.ConHeaderDescription = this.ConHeaderDescription;
          entity.MdcControllingunitId = this.MdcControllingunitId;
          entity.MdcControllingunitCode = this.MdcControllingunitCode;
          entity.MdcControllingunitDescription = this.MdcControllingunitDescription;
          entity.BusinesspartnerId = this.BusinesspartnerId;
          entity.BusinesspartnerDescription = this.BusinesspartnerDescription;
          entity.SubsidiaryId = this.SubsidiaryId;
          entity.SubsidiaryDescription = this.SubsidiaryDescription;
          entity.SupplierId = this.SupplierId;
          entity.SupplierCode = this.SupplierCode;
          entity.SupplierDescription = this.SupplierDescription;
          entity.Remark = this.Remark;
          entity.UserDefined1 = this.UserDefined1;
          entity.UserDefined2 = this.UserDefined2;
          entity.UserDefined3 = this.UserDefined3;
          entity.UserDefined4 = this.UserDefined4;
          entity.UserDefined5 = this.UserDefined5;
          entity.PrcConfigurationId = this.PrcConfigurationId;
          entity.PrcConfigurationDescription = this.PrcConfigurationDescription;
          entity.PrcStructureId = this.PrcStructureId;
          entity.PrcStructureCode = this.PrcStructureCode;
          entity.PrcStructureDescription = this.PrcStructureDescription;
          entity.PesValue = this.PesValue;
          entity.PesVat = this.PesVat;
          entity.Exchangerate = this.Exchangerate;
          entity.PesValueOc = this.PesValueOc;
          entity.PesVatOc = this.PesVatOc;
          entity.PesShipmentinfoId = this.PesShipmentinfoId;
          entity.Isnotaccrual = this.Isnotaccrual;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;
          entity.MdcBillingSchemaId = this.MdcBillingSchemaId;
          entity.MdcBillingSchemaDescription = this.MdcBillingSchemaDescription;
          entity.DateEffective = this.DateEffective;
          entity.VatgroupId = this.VatgroupId;
          entity.VatgroupDescription = this.VatgroupDescription;
          entity.PesHeaderId = this.PesHeaderId;
          entity.PesHeaderCode = this.PesHeaderCode;
          entity.PesHeaderDescription = this.PesHeaderDescription;
          entity.BasSalesTaxMethodId = this.BasSalesTaxMethodId;
          entity.BasSalesTaxMethodDesc = this.BasSalesTaxMethodDesc;
          entity.LanguageId = this.LanguageId;
          entity.TotalStandardCost = this.TotalStandardCost;
          entity.ClerkPrcFamilyName = this.ClerkPrcFamilyName;
          entity.ClerkPrcFirstName = this.ClerkPrcFirstName;
          entity.ClerkReqFamilyName = this.ClerkReqFamilyName;
          entity.ClerkReqFirstName = this.ClerkReqFirstName;
          entity.ExternalCode = this.ExternalCode;
          entity.BasLanguageFk = this.BasLanguageFk;
          entity.BasLanguageDesc = this.BasLanguageDesc;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(PesHeaderApiEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(PesHeaderApiEntity entity);
    }

}
