using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Core.BusinessComponents;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.Core;
using RIB.Visual.Scheduling.Calendar.BusinessComponents;
using RIB.Visual.Scheduling.Schedule.BusinessComponents;
using RVPBC = RIB.Visual.Platform.BusinessComponents;
using Constants = RIB.Visual.Scheduling.Main.Core.Constants;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;

namespace RIB.Visual.Scheduling.Main.BusinessComponents
{
	/// <summary>
	/// Updating quantities in estimation of line items
	/// </summary>
	[Export("scheduling.main.activity", typeof(IQuantityUpdater))]
	public class QuantityUpdater : IQuantityUpdater
	{
		/// <summary>
		/// Update planned and installed quantities belonging to the passed activity in line item quanitities
		/// </summary>
		/// <param name="activityId">Update planned and installed quantities belonging to the passed activity in line item quanitities</param>
		void IQuantityUpdater.UpdateFromActivity(int activityId)
		{
			var data = new IdentificationData() { Id = activityId };
			var lineItems = GetUsedLineItems(data);
			var source = GetPlannedLineItemQuantities(data, lineItems).ToList();
			source.AddRange(GetInstalledLineItemQuantities(data, lineItems).ToList());

			if (source != null && source.Any())
			{
				var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
				if (carrier != null)
				{
					carrier.TakeDelivery(source);
				}
			}
		}

		/// <summary>
		/// Update planned and installed quantities belonging to the activities of the passed project in line item quanitities
		/// </summary>
		/// <param name="scheduleId">Id of the project for which activities the line item quantities should be updated</param>
		void IQuantityUpdater.UpdateFromSchedule(int scheduleId)
		{
			var data = new IdentificationData() { Id = -1, PKey1 = scheduleId };
			var lineItems = GetUsedLineItems(data);
			var source = GetPlannedLineItemQuantities(data, lineItems).ToList();
			source.AddRange(GetInstalledLineItemQuantities(data, lineItems).ToList());

			if (source != null && source.Any())
			{
				var carrier =
					BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
				if (carrier != null)
				{
					carrier.TakeDelivery(source);
				}
			}
		}

		/// <summary>
		/// Update planned and installed quantities belonging to the activities of the passed schedule in line item quanitities
		/// </summary>
		/// <param name="projectId">Id of the project for which activities the line item quantities should be updated</param>
		void IQuantityUpdater.UpdateFromProject(int projectId)
		{
			var data = new IdentificationData() { Id = -1, PKey2 = projectId };
			var lineItems = GetUsedLineItems(data);
			var source = GetPlannedLineItemQuantities(data, lineItems).ToList();
			source.AddRange(GetInstalledLineItemQuantities(data, lineItems).ToList());

			if (source != null && source.Any())
			{
				var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
				if (carrier != null)
				{
					carrier.TakeDelivery(source);
				}
			}
		}

		void IQuantityUpdater.UpdateAllLineItems(IEnumerable<IEstLineItemEntity> lineItems)
		{
			this.UpdatePlannedLineItemsQuantities(lineItems);

			this.UpdateInstalledLineItemsQuantities(lineItems);
		}

		/// <summary>
		///  Update planned quantities belonging for all passed line items
		/// </summary>
		/// <param name="lineItems">Line items the planned quantities are to be updated</param>
		/// <param name="considerScurve"></param>
		public void UpdatePlannedLineItemsQuantities(IEnumerable<IEstLineItemEntity> lineItems, bool considerScurve = false)
		{
			var data = new IdentificationData() { Id = -1 };
			var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
			var source = GetPlannedLineItemQuantities(data, lineItems, considerScurve).ToList();

			if (source.Any() && carrier != null)
			{
				carrier.TakeDelivery(source);
			}
		}

		/// <summary>
		/// Update installed quantities belonging for all passed line items
		/// </summary>
		/// <param name="lineItems">Line items the installed quantities are to be updated</param>
		public void UpdateInstalledLineItemsQuantities(IEnumerable<IEstLineItemEntity> lineItems)
		{
			var data = new IdentificationData() { Id = -1 };
			var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");

			var source = GetInstalledLineItemQuantities(data, lineItems).ToList();
			if (source.Any() && carrier != null)
			{
				carrier.TakeDelivery(source);
			}
		}

		/// <summary>
		/// Update Activity/LineItem Quantity from wizard
		/// </summary>
		/// <param name="data"></param>
		public IEnumerable<ActivityEntity> UpdateActivityQuantity(UpdateQuantityData data)
		{
			if (data == null || data.ScheduleId <= 0) return null;
			var activities = data.ActivityIds != null && data.ActivityIds.Any()
									? new ActivityLogic().GetActivity(e => data.ActivityIds.Contains(e.Id))
									: new ActivityLogic().GetScheduleActivities(data.ScheduleId);

			if (activities == null || !activities.Any()) return null;
			var reportsTosave = new List<ActivityProgressReportEntity>();
			var actIds = activities.Select(e => e.Id).ToList();
			var estLineItemLogic = Injector.Get<IEstimateMainLineItemLogic>();
			var lineItems = estLineItemLogic.ByActivities(actIds).ToList();
			var headerIds = lineItems.Select(e => e.EstHeaderFk).ToList();
			var estHeaderLogic = Injector.Get<IEstimateMainHeaderLogic>();
			var headers = estHeaderLogic.GetEstimateHeaders(headerIds).ToList();
			if (lineItems == null || !lineItems.Any()) return null;

			if (data.IsPes)
			{
				var prcLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcItemAssignmentLogic>();
				var reports = prcLogic.GetQuantityUpdateCompleteByEstimate(lineItems);
				reportsTosave = GetProgressReportQuantitiesOfActivities(activities, reports, lineItems).ToList();
				UpdateReportsActivities(activities, reportsTosave);
				// validate Progress
				var progressLogic = new ActivityProgressReportLogic();
				var lineItemProgLogic = new LineItemProgressLogic();
				var progReps = progressLogic.GetListByFilter(pr => pr.ActivityFk.HasValue && actIds.Contains(pr.ActivityFk.Value)).ToList();
				var reportsToSave = new List<ActivityProgressReportEntity>();
				foreach (var act in activities)
				{
					var actComplete = new ActivityComplete();
					actComplete.MainItemId = act.Id;
					actComplete.Activity = act;
					actComplete.ActivityPlanningChange = new CalculationActivityEntity();
					foreach (var rep in reportsTosave)
					{
						if (rep.ActivityFk == act.Id)
						{
							var report = rep;
							var lineItem = lineItems.Where(e => e.Id == rep.EstLineItemFk && e.EstHeaderFk == rep.EstHeaderFk).FirstOrDefault();
							var matchedEntity = progReps.FirstOrDefault(pr => progressLogic.IsSameDateAndReportingLevelEntity(pr, rep, act));
							if (matchedEntity == null)
							{
								var entity = progressLogic.Create(new IdentificationData() { PKey1 = act.Id, PKey2 = act.ScheduleFk });
								report.Id = entity.Id;
								report.PlannedQuantity = lineItem.QuantityTotal;
								progReps.Add(report);
							}
							else
							{
								report = matchedEntity;
								report.PCo = rep.PCo;
							}
							actComplete.ActivityPlanningChange.DueDate = report.PerformanceDate.Value;
							actComplete.ActivityPlanningChange.PercentageCompletion = report.PCo;
							var lineItemProgress = new LineItemProgressEntity();
							var header = headers.Where(e => e.Id == rep.EstHeaderFk).FirstOrDefault();
							lineItemProgLogic.Initialize(lineItemProgress, actComplete.Activity, lineItem, header);

							actComplete.LineItemProgress = lineItemProgress;
							actComplete.ProgressReportsToSave = new List<ActivityProgressReportEntity>() { report };
							var res = progressLogic.Validate(actComplete);
							reportsToSave.AddRange(res.ProgressReportsToSave);
						}
					}
				}
				progressLogic.Save(reportsToSave);
				//new ActivityProgressReportLogic().ValidateAndUpdateProgressReports(reportsTosave, activities);
				if (data.IsUpdateLineItem)
				{
					prcLogic.UpdateLineItemQuantitiesFromPes(lineItems);
				}
			}
			if (data.IsWip)
			{
				var wipBoqLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesWipBoqLogic>();
				var reports = wipBoqLogic.GetQuantityUpdateCompleteByEstimate(lineItems);
				reportsTosave = GetProgressReportQuantitiesOfActivities(activities, reports, lineItems).ToList();
				UpdateReportsActivities(activities, reportsTosave);
				new ActivityProgressReportLogic().ValidateAndUpdateProgressReports(reportsTosave, activities);
				if (data.IsUpdateLineItem)
				{
					wipBoqLogic.UpdateLineItemQuantitiesFromWip(lineItems);
				}
			}
			return new ActivityLogic().GetActivity(e => actIds.Contains(e.Id));
		}

		/// <summary>
		/// Update planned quantities belonging to the passed activity in line item quanitities
		/// </summary>
		/// <param name="activityId">Update planned and installed quantities belonging to the passed activity in line item quanitities</param>
		/// <param name="considerScurve"></param>
		public int UpdatePlannedQuantitiesFromActivity(int activityId, bool considerScurve)
		{
			int res = 0;
			IEnumerable<LineItemQuantity> source = GetPlannedLineItemQuantities(new IdentificationData() { Id = activityId }, considerScurve).ToList();

			if (source != null && source.Any())
			{
				res = source.Count();
				var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
				if (carrier != null)
				{
					carrier.TakeDelivery(source);
				}
			}

			return res;
		}

		/// <summary>
		/// Update planned quantities belonging to the activities of the passed project in line item quanitities
		/// </summary>
		/// <param name="scheduleId">Id of the schedule for which activities the line item quantities should be updated</param>
		/// <param name="considerScurve"></param>
		public int UpdatePlannedQuantitiesFromSchedule(int scheduleId, bool considerScurve)
		{
			int res = 0;
			var source = GetPlannedLineItemQuantities(new IdentificationData() { Id = -1, PKey1 = scheduleId }, considerScurve).ToList();

			if (source != null && source.Any())
			{
				res = source.Count();
				var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
				if (carrier != null)
				{
					carrier.TakeDelivery(source);
				}
			}

			return res;
		}

		/// <summary>
		/// Update planned quantities belonging to the activities of the passed project in line item quanitities
		/// </summary>
		/// <param name="projectId">Id of the project for which activities the line item quantities should be updated</param>
		public int UpdatePlannedQuantitiesFromProject(int projectId)
		{
			int res = 0;
			var source = GetPlannedLineItemQuantities(new IdentificationData() { Id = -1, PKey2 = projectId }, false).ToList();

			if (source != null && source.Any())
			{
				res = source.Count();
				var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
				if (carrier != null)
				{
					carrier.TakeDelivery(source);
				}
			}

			return res;
		}

		/// <summary>
		/// Update quantities belonging to the passed activity in line item quanitities
		/// </summary>
		/// <param name="activityId">Update planned and installed quantities belonging to the passed activity in line item quanitities</param>
		public int UpdateInstalledQuantitiesFromActivity(int activityId)
		{
			int res = 0;
			IEnumerable<LineItemQuantity> source = GetInstalledLineItemQuantities(new IdentificationData() { Id = activityId }).ToList();

			if (source != null && source.Any())
			{
				res = source.Count();
				var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
				if (carrier != null)
				{
					carrier.TakeDelivery(source);
				}
			}

			return res;
		}

		/// <summary>
		/// Update installed quantities belonging to the activities of the passed project in line item quanitities
		/// </summary>
		/// <param name="scheduleId">Id of the project for which activities the line item quantities should be updated</param>
		public int UpdateInstalledQuantitiesFromSchedule(int scheduleId)
		{
			int res = 0;
			IEnumerable<LineItemQuantity> source = GetInstalledLineItemQuantities(new IdentificationData() { Id = -1, PKey1 = scheduleId }).ToList();

			if (source != null && source.Any())
			{
				res = source.Count();
				var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
				if (carrier != null)
				{
					carrier.TakeDelivery(source);
				}
			}

			return res;
		}

		/// <summary>
		/// Update planned and installed quantities belonging to the activities of the passed schedule in line item quanitities
		/// </summary>
		/// <param name="projectId">Id of the project for which activities the line item quantities should be updated</param>
		public int UpdateInstalledQuantitiesFromProject(int projectId)
		{
			int res = 0;
			IEnumerable<LineItemQuantity> source = GetInstalledLineItemQuantities(new IdentificationData() { Id = -1, PKey2 = projectId }).ToList();

			if (source != null && source.Any())
			{
				res = source.Count();
				var carrier = BusinessApplication.BusinessEnvironment.GetExportedValue<IEntityCarrier>("estimate.main.lineitemquantity");
				if (carrier != null)
				{
					carrier.TakeDelivery(source);
				}
			}

			return res;
		}

		private IEnumerable<IEstLineItemEntity> GetUsedLineItems(IdentificationData data)
		{
			var lineItemLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();
			IEnumerable<IEstLineItemEntity> lineItems;
			if (data.PKey2.HasValue)
			{
				lineItems = lineItemLogic.ByProject(data.PKey2.Value);
			}
			else if (data.PKey1.HasValue)
			{//Use the project to determine the line item
				var schedule = new ScheduleLogic().GetScheduleById(data.PKey1.Value);
				lineItems = lineItemLogic.ByProject(schedule.ProjectFk);
			}
			else
			{
				lineItems = lineItemLogic.ByActivity(data.Id);
			}
			lineItems = lineItems.Where(li => li.ActivityFk.HasValue).ToList();

			return lineItems;
		}

		private IEnumerable<LineItemQuantity> GetPlannedLineItemQuantities(IdentificationData data, bool considerScurve)
		{
			return GetPlannedLineItemQuantities(data, GetUsedLineItems(data), considerScurve);
		}

		private IEnumerable<LineItemQuantity> GetPlannedLineItemQuantities(IdentificationData data, IEnumerable<IEstLineItemEntity> lineItems, bool considerScurve = false)
		{
			var result = new List<LineItemQuantity>();
			IEnumerable<ActivityEntity> sourceActivities;
			Dictionary<int, CalendarData> allCalendarData = new Dictionary<int, CalendarData>();
			Dictionary<int, Tuple<IEnumerable<IEstQuantityScurveResults>, decimal>> scurveDataByActivity = new Dictionary<int, Tuple<IEnumerable<IEstQuantityScurveResults>, decimal>>();
			ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };

			if (data.PKey2.HasValue)
			{
				sourceActivities = new ActivityLogic().GetActivity(e => e.ProjectFk == data.PKey2.Value && e.IsLive && !e.BaseActivityFk.HasValue && !e.BaselineFk.HasValue);
			}
			else if (data.PKey1.HasValue)
			{
				sourceActivities = new ActivityLogic().GetActivity(e => e.ScheduleFk == data.PKey1.Value && e.IsLive && !e.BaseActivityFk.HasValue && !e.BaselineFk.HasValue);
			}
			else if (data.Id > 0)
			{
				sourceActivities = new List<ActivityEntity>() { new ActivityLogic().GetActivityById(data.Id) };
			}
			else
			{
				var actIds = lineItems.Select(li => li.ActivityFk).ToList();
				sourceActivities = new ActivityLogic().GetActivity(a => actIds.Contains(a.Id)).ToList();
			}
			if (considerScurve)
			{
				Parallel.ForEach(sourceActivities, parallelOptions, activity =>
				{
					if (activity.SCurveFk is null && activity.ParentActivityFk.HasValue)
					{
						activity.SCurveFk = new ActivityLogic().GetParentSCurveFk(activity.Id);
					}
				});

			}

			if (sourceActivities.Any())
			{
				if (considerScurve)
				{
					var quantitySCurveLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainQuantitySCurveLogic>();
					scurveDataByActivity = (Dictionary<Int32, Tuple<IEnumerable<IEstQuantityScurveResults>, Decimal>>)quantitySCurveLogic.CalculateScurveByBinWeightList(sourceActivities);
				}
				var min = sourceActivities.Select(a => a.PlannedStart).Min();
				var max = sourceActivities.Select(a => a.PlannedFinish.HasValue ? a.PlannedFinish.Value : a.PlannedStart).Max();
				min = min.AddDays(-100);
				max = max.AddDays(100);
				var periods = new BasicsCompanyPeriodLogic().GetPeriodByDates(min, max).ToList();
				CalendarData calData;
				var calLogic = new CalendarUtilitiesLogic();
				var calendarIds = sourceActivities.Select(e => e.CalendarFk).Distinct().ToArray();
				for (int i = 0; i < calendarIds.Length; i++)
				{
					if (!allCalendarData.TryGetValue(calendarIds[i], out calData))
					{
						calData = new CalendarData(calendarIds[i]);
						//set non working days 
						var filter = new UtilitiesDataFilter();
						filter.Calendar = calendarIds[i];
						filter.Start = min;
						filter.End = max;
						calData.NonWorkingDays = new CalendarUtilitiesLogic().GetNonWorkingDayChecker(filter) as NonWorkingDayCheck;
						allCalendarData.Add(calendarIds[i], calData);
					}
					//result.AddRange(GetPlannedLineItemQuantitiesOfActivity(act, periods, lineItems, calLogic, calData, considerScurve, scurveDataByActivity));
				}

				object monitor = new Object();

				Parallel.ForEach(sourceActivities, parallelOptions, act => {
					if (allCalendarData.TryGetValue(act.CalendarFk, out calData))
					{
						var liQuantities = GetPlannedLineItemQuantitiesOfActivity(act, periods, lineItems, calLogic, calData, considerScurve && act.SCurveFk.HasValue, scurveDataByActivity);
						lock (monitor)
						{
							result.AddRange(liQuantities);
						}
					}
				});
			}
			return result;
		}

		private IEnumerable<LineItemQuantity> GetPlannedLineItemQuantitiesOfActivity(ActivityEntity act, IEnumerable<CompanyPeriodEntity> periods, IEnumerable<IEstLineItemEntity> lineItems, CalendarUtilitiesLogic calLogic, CalendarData calData, bool considerScurve, Dictionary<int, Tuple<IEnumerable<IEstQuantityScurveResults>, decimal>> scurveDataByActivity)
		{
			var result = new List<LineItemQuantity>();

			var matchingLineItems = lineItems.Where(li => li.ActivityFk.HasValue && li.ActivityFk.Value == act.Id);
			if (matchingLineItems.Any())
			{
				var matchingPeriods = periods.Where(p => p.StartDate <= act.PlannedStart && p.EndDate >= act.PlannedStart ||
																								 p.StartDate > act.PlannedStart && p.EndDate < act.PlannedFinish.Value ||
																								 p.StartDate <= act.PlannedFinish.Value && p.EndDate >= act.PlannedFinish.Value).OrderBy(rp => rp.EndDate).ToList();

				if (matchingPeriods.Any())
				{
					var lastStart = act.PlannedStart;
					var totalEnd = act.PlannedFinish.Value;

					var totalDuration = act.PlannedDuration.Value;
					if (totalDuration < 0.005m)
					{
						totalDuration = 1.0m;
					}
					var prDuration = 0.0m;
					IEnumerable<IEstQuantityDailyFractionResults> estQuantityDailyFractionResults = new List<IEstQuantityDailyFractionResults>();
					if (considerScurve && scurveDataByActivity.TryGetValue(act.Id, out var scurveData))
					{
						var quantitySCurveLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainQuantitySCurveLogic>();
						estQuantityDailyFractionResults = quantitySCurveLogic.CalculateScurveLogicForQuantity(act, scurveData);
					}
					decimal prDurationCount = 0;

					foreach (var rp in matchingPeriods)
					{
						if (DateTime.Compare(rp.EndDate, totalEnd) < 0)
						{
							prDuration = calLogic.GetDuration(lastStart, rp.EndDate, calData.NonWorkingDays);
						}
						else
						{
							prDuration = calLogic.GetDuration(lastStart, totalEnd, calData.NonWorkingDays);
						}
						decimal costFractionPerPeriod = 0;
						if (considerScurve)
						{
							decimal periodStartIndex = prDurationCount;
							prDurationCount = prDurationCount + prDuration;
							decimal periodEndIndex = prDurationCount;
							costFractionPerPeriod = estQuantityDailyFractionResults.Where(x => x.Id >= periodStartIndex && x.Id < periodEndIndex && x.DailyFraction.HasValue).Sum(s => s.DailyFraction.Value);
						}

						foreach (var li in matchingLineItems)
						{
							result.Add(new LineItemQuantity()
							{
								ActivityFk = act.Id,
								BoqHeaderFk = null,
								BoqItemFk = null,
								Date = rp.EndDate,
								EstimateHeaderFk = li.EstHeaderFk,
								LineItemFk = li.Id,
								Quantity = considerScurve ? costFractionPerPeriod * li.QuantityTotal : li.QuantityTotal * prDuration / totalDuration,
								QuantityTypeFk = Constants.QuantityTypePlanned
							});
						}

						lastStart = rp.EndDate.AddDays(1);
					}
					if (DateTime.Compare(lastStart, totalEnd) < 0)
					{
						decimal costFractionPerPeriod = 0;
						prDuration = calLogic.GetDuration(lastStart, totalEnd, calData.NonWorkingDays);
						if (considerScurve)
						{
							costFractionPerPeriod = estQuantityDailyFractionResults.Where(x => x.Id >= prDurationCount && x.DailyFraction.HasValue).Sum(s => s.DailyFraction.Value);
						}
						foreach (var li in matchingLineItems)
						{
							result.Add(new LineItemQuantity()
							{
								ActivityFk = act.Id,
								BoqHeaderFk = null,
								BoqItemFk = null,
								Date = totalEnd,
								EstimateHeaderFk = li.EstHeaderFk,
								LineItemFk = li.Id,
								Quantity = considerScurve ? costFractionPerPeriod * li.QuantityTotal : li.QuantityTotal * prDuration / totalDuration,
								QuantityTypeFk = Constants.QuantityTypePlanned
							});
						}

					};
				}
				else
				{
					foreach (var li in matchingLineItems)
					{
						result.Add(new LineItemQuantity()
						{
							ActivityFk = act.Id,
							BoqHeaderFk = null,
							BoqItemFk = null,
							Date = act.PlannedFinish.Value,
							EstimateHeaderFk = li.EstHeaderFk,
							LineItemFk = li.Id,
							Quantity = li.QuantityTotal,
							QuantityTypeFk = Constants.QuantityTypePlanned
						});
					}
				}
			}

			return result;
		}

		private IEnumerable<LineItemQuantity> GetInstalledLineItemQuantities(IdentificationData data)
		{
			return GetInstalledLineItemQuantities(data, GetUsedLineItems(data));
		}

		private IEnumerable<LineItemQuantity> GetInstalledLineItemQuantities(IdentificationData data, IEnumerable<IEstLineItemEntity> lineItems)
		{
			IEnumerable<ActivityProgressReportEntity> sourceReports;
			List<ActivityEntity> sourceActivities = null;
			if (data.PKey2.HasValue)
			{
				var scheduleIds = new ScheduleLogic().GetCoresAsListByFilter(e => e.ProjectFk == data.PKey2.Value).Select(s => s.Id);
				sourceReports = new ActivityProgressReportLogic().GetCoresAsListByFilter(pr => scheduleIds.Contains(pr.ScheduleFk));
				sourceActivities = new ActivityLogic().GetActivity(a => a.ProjectFk == data.PKey2.Value).ToList();
			}
			else if (data.PKey1.HasValue)
			{
				sourceReports = new ActivityProgressReportLogic().GetCoresAsListByFilter(pr => pr.ScheduleFk == data.PKey1.Value);
				sourceActivities = new ActivityLogic().GetActivity(a => a.ScheduleFk == data.PKey1.Value).ToList();
			}
			else if (data.Id > 0)
			{
				sourceReports = new ActivityProgressReportLogic().GetCoresAsListByFilter(pr => pr.ActivityFk == data.Id);
				sourceActivities = new ActivityLogic().GetActivity(a => a.Id == data.Id).ToList();
			}
			else
			{
				var actIds = lineItems.Select(li => li.ActivityFk).ToList();
				sourceReports = new ActivityProgressReportLogic().GetCoresAsListByFilter(pr => actIds.Contains(pr.ActivityFk));
				sourceActivities = new ActivityLogic().GetActivity(a => actIds.Contains(a.Id)).ToList();
			}

			var toDeleteIds = new List<int>();
			var result = GetInstalledLineItemQuantitiesOfActivities(sourceActivities, sourceReports, lineItems, toDeleteIds);

			if (toDeleteIds.Any())
			{
				var estimateMainLineItemQuantityLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemQuantityLogic>();
				estimateMainLineItemQuantityLogic.DeleteBulkEntities(toDeleteIds);
			}

			return result;
		}

		private List<LineItemQuantity> GetInstalledLineItemQuantitiesOfActivities(IEnumerable<ActivityEntity> activities, IEnumerable<ActivityProgressReportEntity> sourceReports, IEnumerable<IEstLineItemEntity> lineItems, List<int> toDeleteIds)
		{
			var result = new List<LineItemQuantity>();
			var estimateMainLineItemQuantityLogic = BusinessApplication.BusinessEnvironment.
					GetExportedValue<IEstimateMainLineItemQuantityLogic>();

			foreach (var act in activities)
			{
				var matchingReports = sourceReports.Where(rp => rp.ActivityFk == act.Id);
				var matchingLineItems = lineItems.Where(li => li.ActivityFk == act.Id);

				if (matchingLineItems.Any() && matchingReports.Any())
				{
					var prm = act.ProgressReportMethodFk.HasValue ? act.ProgressReportMethodFk.Value : Constants.ProgressReportMethodByActivityQuantity;

					switch (prm)
					{
						case Constants.ProgressReportMethodByActivityQuantity:
							GetInstalledLineItemQuantitiesByActivityQuantity(matchingReports, matchingLineItems, result);
							break;
						case Constants.ProgressReportMethodByLineItemQuantity:
							GetInstalledLineItemQuantitiesByLineItemQuantity(matchingReports, matchingLineItems, result);
							break;
						case Constants.ProgressReportMethodByActivityWork:
							GetInstalledLineItemQuantitiesByActivityWork(matchingReports, matchingLineItems, result);
							break;
						case Constants.ProgressReportMethodByLineItemWork:
							GetInstalledLineItemQuantitiesByLineItemWork(matchingReports, matchingLineItems, result);
							break;
						case Constants.ProgressReportMethodAsScheduled:
							GetInstalledLineItemQuantitiesByActivityQuantity(matchingReports, matchingLineItems, result);
							break;
						case Constants.ProgressReportMethodByModelObject:
							GetInstalledLineItemQuantitiesByModelObject(matchingReports, matchingLineItems, result);
							break;
						default: break;
					}
				}

				var lineItemsWithoutReports = matchingLineItems.Where(i => !matchingReports.Any(r => r.EstLineItemFk.Equals(i.EstLineItemFk)));				
				var ids = estimateMainLineItemQuantityLogic.GetLineItemQuantitiesByLi(
					lineItemsWithoutReports.CollectIds(li => li.Id),
					lineItemsWithoutReports.CollectIds(li => li.EstHeaderFk)).Select(i => i.Id);
				toDeleteIds.AddRange(ids);
			}

			return result;
		}

		private void GetInstalledLineItemQuantitiesByActivityQuantity(IEnumerable<ActivityProgressReportEntity> sourceReports, IEnumerable<IEstLineItemEntity> lineItems,
			List<LineItemQuantity> lineItemQuantities)
		{
			var onePercent = new decimal(0.01);
			var accPCo = new decimal(0.00);
			foreach (var pr in sourceReports)
			{
				foreach (var li in lineItems)
				{
					lineItemQuantities.Add(new LineItemQuantity()
					{
						ActivityFk = pr.ActivityFk,
						BoqHeaderFk = null,
						BoqItemFk = null,
						Date = pr.PerformanceDate,
						EstimateHeaderFk = li.EstHeaderFk,
						LineItemFk = li.Id,
						Quantity = pr.PCo.HasValue ? li.QuantityTotal * (pr.PCo.Value - accPCo) * onePercent : (decimal)0.0,
						QuantityTypeFk = Constants.QuantityTypeInstalled
					});
				}
				if (pr.PCo.HasValue)
				{
					accPCo = pr.PCo.Value;
				}
			}
		}

		private void GetInstalledLineItemQuantitiesByLineItemQuantity(IEnumerable<ActivityProgressReportEntity> sourceReports, IEnumerable<IEstLineItemEntity> lineItems,
			List<LineItemQuantity> lineItemQuantities)
		{
			foreach (var pr in sourceReports)
			{
				if (pr.EstHeaderFk.HasValue)
				{
					lineItemQuantities.Add(new LineItemQuantity()
					{
						ActivityFk = pr.ActivityFk,
						BoqHeaderFk = null,
						BoqItemFk = null,
						Date = pr.PerformanceDate,
						EstimateHeaderFk = pr.EstHeaderFk.Value,
						LineItemFk = pr.EstLineItemFk.Value,
						Quantity = pr.Quantity ?? 0m,
						QuantityTypeFk = Constants.QuantityTypeInstalled
					});
				}
			}
		}

		private void GetInstalledLineItemQuantitiesByActivityWork(IEnumerable<ActivityProgressReportEntity> sourceReports, IEnumerable<IEstLineItemEntity> lineItems,
			List<LineItemQuantity> lineItemQuantities)
		{
			var onePercent = new decimal(0.01);
			var accPCo = new decimal(0.00);
			foreach (var pr in sourceReports)
			{
				foreach (var li in lineItems)
				{
					lineItemQuantities.Add(new LineItemQuantity()
					{
						ActivityFk = pr.ActivityFk,
						BoqHeaderFk = null,
						BoqItemFk = null,
						Date = pr.PerformanceDate,
						EstimateHeaderFk = li.EstHeaderFk,
						LineItemFk = li.Id,
						Quantity = pr.PCo.HasValue ? li.QuantityTotal * (pr.PCo.Value - accPCo) * onePercent : (decimal)0.0,
						QuantityTypeFk = Constants.QuantityTypeInstalled
					});
				}
				if (pr.PCo.HasValue)
				{
					accPCo = pr.PCo.Value;
				}
			}
		}

		private void GetInstalledLineItemQuantitiesByLineItemWork(IEnumerable<ActivityProgressReportEntity> sourceReports, IEnumerable<IEstLineItemEntity> lineItems,
			List<LineItemQuantity> lineItemQuantities)
		{
			var onePercent = new decimal(0.01);
			foreach (var pr in sourceReports)
			{
				if (pr.EstHeaderFk.HasValue)
				{
					var lineItem =
						lineItems.FirstOrDefault(li => li.EstHeaderFk == pr.EstHeaderFk.Value && li.Id == pr.EstLineItemFk.Value);
					if (lineItem != null)
					{
						lineItemQuantities.Add(new LineItemQuantity()
						{
							ActivityFk = pr.ActivityFk,
							BoqHeaderFk = null,
							BoqItemFk = null,
							Date = pr.PerformanceDate,
							EstimateHeaderFk = pr.EstHeaderFk.Value,
							LineItemFk = pr.EstLineItemFk.Value,
							Quantity = pr.PCo.HasValue ? lineItem.QuantityTotal * pr.PCo.Value * onePercent : (decimal)0.0,
							QuantityTypeFk = Constants.QuantityTypeInstalled
						});
					}
				}
			}
		}

		private void GetInstalledLineItemQuantitiesByModelObject(IEnumerable<ActivityProgressReportEntity> sourceReports, IEnumerable<IEstLineItemEntity> lineItems,
			List<LineItemQuantity> lineItemQuantities)
		{
			foreach (var pr in sourceReports)
			{
				if (pr.EstHeaderFk.HasValue)
				{

					lineItemQuantities.Add(new LineItemQuantity()
					{
						ActivityFk = pr.ActivityFk,
						BoqHeaderFk = null,
						BoqItemFk = null,
						Date = pr.PerformanceDate,
						EstimateHeaderFk = pr.EstHeaderFk.Value,
						LineItemFk = pr.EstLineItemFk.Value,
						Quantity = pr.Quantity ?? 0m,
						QuantityTypeFk = Constants.QuantityTypeInstalled
					});
				}
			}
		}

		private void GetProgressReportQuantitiesByLineItem(ActivityEntity act, IEnumerable<IEstLineItemEntity> lineItems, IEnumerable<IQuantityUpdateCompleteEntity> sourceReports, List<ActivityProgressReportEntity> result)
		{
			foreach (var pr in sourceReports)
			{
				if (pr.PCo.HasValue && pr.PCo.Value > 0)
				{
					var entity = new ActivityProgressReportEntity
					{
						ScheduleFk = act.ScheduleFk,
						ActivityFk = pr.ActivityFk,
						PCo = pr.PCo,
						PerformanceDate = pr.PerformanceDate ?? DateTime.UtcNow,
						EstLineItemFk = pr.EstLineItemFk,
						EstHeaderFk = pr.EstHeaderFk,
						PlannedQuantity = lineItems.FirstOrDefault(li => li.Id == pr.EstLineItemFk && li.EstHeaderFk == pr.EstHeaderFk)?.QuantityTotal ?? 1m
					};
					result.Add(entity);
				}
			}
		}

		private void GetProgressReportQuantitiesByActivity(ActivityEntity act,
			IEnumerable<IQuantityUpdateCompleteEntity> reports, IEnumerable<IEstLineItemEntity> lineItems,
			List<ActivityProgressReportEntity> result)
		{
			//get lineItems for specific activity
			var matchingLineItems = lineItems.Where(li => li.ActivityFk.HasValue && li.ActivityFk.Value == act.Id);
			var lineItemsCount = matchingLineItems.Count();
			decimal? actPCo = null;
			DateTime? peformanceDate = null;

			if (lineItemsCount > 1)
			{
				decimal totalCostPCo = 0;
				decimal totalCost = matchingLineItems.Sum(e => e.CostTotal);

				//PCo (ACT)	= SUM(CostTotal(n)* PCo(EST)(n)/100) / Sum(CostTotal) * 100 

				foreach (var li in matchingLineItems)
				{
					var maxDate = reports.Where(e => e.EstLineItemFk == li.Id && e.EstHeaderFk == li.EstHeaderFk && e.PerformanceDate.HasValue).Max(i => i.PerformanceDate);
					var pr = reports.FirstOrDefault(e => e.EstLineItemFk == li.Id && e.EstHeaderFk == li.EstHeaderFk && e.PerformanceDate == maxDate);
					if (pr != null && pr.PCo != null) totalCostPCo += pr.PCo.Value * li.CostTotal;
				}
				actPCo = totalCost > 0 ? totalCostPCo / totalCost : 0;
				peformanceDate = reports.Where(e => e.PerformanceDate.HasValue).Max(i => i.PerformanceDate);
			}

			if (lineItemsCount == 1)
			{
				var li = matchingLineItems.FirstOrDefault();
				if (li == null) return;
				peformanceDate = reports.Where(e => e.EstLineItemFk == li.Id && e.EstHeaderFk == li.EstHeaderFk).Max(i => i.PerformanceDate);

				var pr = reports.FirstOrDefault(e => e.EstLineItemFk == li.Id && e.EstHeaderFk == li.EstHeaderFk && e.PerformanceDate == peformanceDate);
				if (pr != null) actPCo = pr.PCo;
			}
			if (lineItemsCount >= 1)
			{
				//create new progress report with actPCo , pr.PerformanceDate , actId, ScheduleId
				var reportEntity = new ActivityProgressReportEntity
				{
					PerformanceDate = peformanceDate ?? DateTime.UtcNow,
					PCo = actPCo,
					ActivityFk = act.Id,
					ScheduleFk = act.ScheduleFk
				};
				result.Add(reportEntity);
			}
		}

		private IEnumerable<ActivityProgressReportEntity> GetProgressReportQuantitiesOfActivities(IEnumerable<ActivityEntity> activities,
				IEnumerable<IQuantityUpdateCompleteEntity> sourceReports, IEnumerable<IEstLineItemEntity> lineItems)
		{
			var result = new List<ActivityProgressReportEntity>();

			foreach (var act in activities)
			{
				if (act == null) continue;
				if (sourceReports != null)
				{
					var matchingReports = sourceReports.Where(rp => rp.ActivityFk.HasValue && rp.ActivityFk.Value == act.Id);
					var hasOldReports = new ActivityProgressReportLogic().HasProgressReportByActivityId(act.Id);
					var prm = act.ProgressReportMethodFk.HasValue && hasOldReports
						? act.ProgressReportMethodFk.Value
						: Constants.ProgressReportMethodByLineItemQuantity;

					act.ProgressReportMethodFk = prm;

					switch (prm)
					{
						case Constants.ProgressReportMethodByActivityQuantity:
						case Constants.ProgressReportMethodByActivityWork:
							GetProgressReportQuantitiesByActivity(act, matchingReports, lineItems, result);
							break;
						case Constants.ProgressReportMethodByLineItemQuantity:
						case Constants.ProgressReportMethodByLineItemWork:
							GetProgressReportQuantitiesByLineItem(act, lineItems, matchingReports, result);
							break;
					}
				}
			}
			return result;
		}

		private void UpdateReportsActivities(IEnumerable<ActivityEntity> activities, List<ActivityProgressReportEntity> reportsTosave)
		{
			if (activities != null && reportsTosave != null && reportsTosave.Any())
			{
				var actIds = reportsTosave.Where(e => e.PCo > 0).Select(e => e.ActivityFk).Distinct().ToList();
				var actsToSave = activities.Where(e => e.ProgressReportMethodFk.HasValue && e.ProgressReportMethodFk.Value == Constants.ProgressReportMethodByLineItemQuantity).Select(e => e);
				new ActivityLogic().UpdateActivities(actsToSave);
			}
		}

	}
}
