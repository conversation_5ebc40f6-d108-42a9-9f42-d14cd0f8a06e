﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Procurement.PublicApi.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Procurement.PublicApi.BusinessComponents.PrcPackageApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("PRC_PACKAGEAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(6)]
    public partial class PrcPackageApiEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new PrcPackageApiEntity object.
        /// </summary>
        public PrcPackageApiEntity()
        {
            OnConstruct(); // call partial constructor if present
        }

        #region Properties

        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }


        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ProjectId {
            get; set;
        }


        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string ProjectCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 3)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ProjectDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcPackageStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGESTATUS_ID", TypeName = "int", Order = 4)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PackageStatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcPackageStatusId {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcPackageStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGESTATUS_DESC", TypeName = "nvarchar(2000)", Order = 5)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcPackageStatusDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 6)]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string Code {
            get; set;
        }


        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 7)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Description {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcStructureId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("StructureFk")]
        public virtual int? PrcStructureId {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcStructureCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PrcStructureCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcStructureDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_DESC", TypeName = "nvarchar(2000)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcStructureDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CurrencyId {
            get; set;
        }


        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string CurrencyDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for Reference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REFERENCE", TypeName = "nvarchar(252)", Order = 13)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Reference {
            get; set;
        }


        /// <summary>
        /// There are no comments for PlannedStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_START", TypeName = "date", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedStart")]
        public virtual System.DateTime? PlannedStart {
            get; set;
        }


        /// <summary>
        /// There are no comments for PlannedEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_END", TypeName = "date", Order = 15)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedEnd")]
        public virtual System.DateTime? PlannedEnd {
            get; set;
        }


        /// <summary>
        /// There are no comments for ActualStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ACTUAL_START", TypeName = "date", Order = 16)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ActualStart")]
        public virtual System.DateTime? ActualStart {
            get; set;
        }


        /// <summary>
        /// There are no comments for ActualEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ACTUAL_END", TypeName = "date", Order = 17)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ActualEnd")]
        public virtual System.DateTime? ActualEnd {
            get; set;
        }


        /// <summary>
        /// There are no comments for Quantity in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QUANTITY", TypeName = "numeric(19,6)", Order = 18)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Quantity")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal Quantity {
            get; set;
        }


        /// <summary>
        /// There are no comments for UomId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOM_ID", TypeName = "int", Order = 19)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UomFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int UomId {
            get; set;
        }


        /// <summary>
        /// There are no comments for UomDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOM_DESC", TypeName = "nvarchar(2000)", Order = 20)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string UomDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcPackageTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGETYPE_ID", TypeName = "int", Order = 21)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PackageTypeFk")]
        public virtual int? PrcPackageTypeId {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcPackageTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGETYPE_DESC", TypeName = "nvarchar(2000)", Order = 22)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcPackageTypeDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public virtual int? ClerkPrcId {
            get; set;
        }


        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkPrcCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 25)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkPrcDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public virtual int? ClerkReqId {
            get; set;
        }


        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 27)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkReqCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 28)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkReqDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for TaxCodeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_ID", TypeName = "int", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TaxCodeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int TaxCodeId {
            get; set;
        }


        /// <summary>
        /// There are no comments for TaxCodeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_CODE", TypeName = "nvarchar(16)", Order = 30)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string TaxCodeCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for TaxCodeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_DESC", TypeName = "nvarchar(2000)", Order = 31)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string TaxCodeDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 32)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark {
            get; set;
        }


        /// <summary>
        /// There are no comments for Remark2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK2", TypeName = "nvarchar(2000)", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark2")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark2 {
            get; set;
        }


        /// <summary>
        /// There are no comments for Remark3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK3", TypeName = "nvarchar(2000)", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark3")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark3 {
            get; set;
        }


        /// <summary>
        /// There are no comments for PsdActivityId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_ACTIVITY_ID", TypeName = "int", Order = 35)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ActivityFk")]
        public virtual int? PsdActivityId {
            get; set;
        }


        /// <summary>
        /// There are no comments for PsdActivityCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_ACTIVITY_CODE", TypeName = "nvarchar(16)", Order = 36)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PsdActivityCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for PsdActivityDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_ACTIVITY_DESC", TypeName = "nvarchar(255)", Order = 37)]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public virtual string PsdActivityDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for PsdScheduleId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_SCHEDULE_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ScheduleFk")]
        public virtual int? PsdScheduleId {
            get; set;
        }


        /// <summary>
        /// There are no comments for PsdScheduleCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_SCHEDULE_CODE", TypeName = "nvarchar(16)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PsdScheduleCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for PsdScheduleDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_SCHEDULE_DESC", TypeName = "nvarchar(2000)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PsdScheduleDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for AddressId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_ID", TypeName = "int", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AddressFk")]
        public virtual int? AddressId {
            get; set;
        }


        /// <summary>
        /// There are no comments for AddressDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_DESC", TypeName = "nvarchar(2000)", Order = 42)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string AddressDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for IsLive in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISLIVE", TypeName = "bit", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsLive")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsLive {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 44)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined1 {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined2 {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined3 {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 47)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined4 {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined5 {
            get; set;
        }


        /// <summary>
        /// There are no comments for OverallDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT", TypeName = "numeric(19,7)", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscount {
            get; set;
        }


        /// <summary>
        /// There are no comments for OverallDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscountOc {
            get; set;
        }


        /// <summary>
        /// There are no comments for OverallDiscountPercent in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_PERCENT", TypeName = "numeric(9,3)", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscountPercent {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 52)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConfigurationFk")]
        public virtual int? PrcConfigurationId {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcConfigurationDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_DESC", TypeName = "nvarchar(2000)", Order = 53)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcConfigurationDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyId {
            get; set;
        }


        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 55)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string CompanyCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for ExchangeRate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 56)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal ExchangeRate {
            get; set;
        }


        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public virtual string SearchPattern {
            get; set;
        }


        /// <summary>
        /// There are no comments for BusinessPartnerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        public virtual int? BusinessPartnerId {
            get; set;
        }


        /// <summary>
        /// There are no comments for BusinessPartnerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BusinessPartnerDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for SubsidiaryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 60)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public virtual int? SubsidiaryId {
            get; set;
        }


        /// <summary>
        /// There are no comments for SubsidiaryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 61)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SubsidiaryDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for SupplierId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 62)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public virtual int? SupplierId {
            get; set;
        }


        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for SupplierDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 64)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasCashProjectionId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CASHPROJECTION_ID", TypeName = "int", Order = 65)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CashProjectionFk")]
        public virtual int? BasCashProjectionId {
            get; set;
        }


        /// <summary>
        /// There are no comments for MdcAssetMasterId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_ID", TypeName = "int", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AssetMasterFk")]
        public virtual int? MdcAssetMasterId {
            get; set;
        }


        /// <summary>
        /// There are no comments for MdcAssetMasterCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_CODE", TypeName = "nvarchar(16)", Order = 67)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string MdcAssetMasterCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for MdcAssetMasterDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_DESC", TypeName = "nvarchar(2000)", Order = 68)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcAssetMasterDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcContractTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_ID", TypeName = "int", Order = 69)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcContractTypeFk")]
        public virtual int? PrcContractTypeId {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcContractTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_DESC", TypeName = "nvarchar(2000)", Order = 70)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcContractTypeDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for BaselinePath in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_PATH", TypeName = "nvarchar(255)", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselinePath")]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public virtual string BaselinePath {
            get; set;
        }


        /// <summary>
        /// There are no comments for BaselineUpdate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_UPDATE", TypeName = "datetime", Order = 77)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselineUpdate")]
        public virtual System.DateTime? BaselineUpdate {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefinedDate1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE1", TypeName = "date", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate1")]
        public virtual System.DateTime? UserDefinedDate1 {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefinedDate2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE2", TypeName = "date", Order = 79)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate2")]
        public virtual System.DateTime? UserDefinedDate2 {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefinedDate3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE3", TypeName = "date", Order = 80)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate3")]
        public virtual System.DateTime? UserDefinedDate3 {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefinedDate4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE4", TypeName = "date", Order = 81)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate4")]
        public virtual System.DateTime? UserDefinedDate4 {
            get; set;
        }


        /// <summary>
        /// There are no comments for UserDefinedDate5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE5", TypeName = "date", Order = 82)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate5")]
        public virtual System.DateTime? UserDefinedDate5 {
            get; set;
        }


        /// <summary>
        /// There are no comments for DateEffective in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_EFFECTIVE", TypeName = "date", Order = 83)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateEffective")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateEffective {
            get; set;
        }


        /// <summary>
        /// There are no comments for VatGroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 84)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public virtual int? VatGroupId {
            get; set;
        }


        /// <summary>
        /// There are no comments for VatGroupDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 85)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string VatGroupDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for BaselinePhase in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_PHASE", TypeName = "int", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselinePhase")]
        public virtual int? BaselinePhase {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasCountryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COUNTRY_ID", TypeName = "int", Order = 87)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CountryFk")]
        public virtual int? BasCountryId {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasCountryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COUNTRY_DESC", TypeName = "nvarchar(2000)", Order = 88)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasCountryDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrjRegionId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_REGION_ID", TypeName = "int", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RegionFk")]
        public virtual int? PrjRegionId {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrjRegionCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_REGION_CODE", TypeName = "nvarchar(5)", Order = 90)]
        [System.ComponentModel.DataAnnotations.StringLength(5)]
        public virtual string PrjRegionCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrjRegionDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_REGION_DESC", TypeName = "nvarchar(2000)", Order = 91)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrjRegionDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasTelephoneNumberId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_NUMBER_ID", TypeName = "int", Order = 92)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TelephoneNumberFk")]
        public virtual int? BasTelephoneNumberId {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasTelephoneNumberDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_NUMBER_DESC", TypeName = "nvarchar(100)", Order = 93)]
        [System.ComponentModel.DataAnnotations.StringLength(100)]
        public virtual string BasTelephoneNumberDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasTelephoneTelefaxId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_TELEFAX_ID", TypeName = "int", Order = 94)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TelephoneTelefaxFk")]
        public virtual int? BasTelephoneTelefaxId {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasTelephoneTelefaxDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_TELEFAX_DESC", TypeName = "nvarchar(100)", Order = 95)]
        [System.ComponentModel.DataAnnotations.StringLength(100)]
        public virtual string BasTelephoneTelefaxDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasTelephoneMobileId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_MOBILE_ID", TypeName = "int", Order = 96)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TelephoneMobileFk")]
        public virtual int? BasTelephoneMobileId {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasTelephoneMobileDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_MOBILE_DESC", TypeName = "nvarchar(100)", Order = 97)]
        [System.ComponentModel.DataAnnotations.StringLength(100)]
        public virtual string BasTelephoneMobileDescription {
            get; set;
        }


        /// <summary>
        /// There are no comments for Email in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EMAIL", TypeName = "nvarchar(100)", Order = 98)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Email")]
        [System.ComponentModel.DataAnnotations.StringLength(100)]
        public virtual string Email {
            get; set;
        }


        /// <summary>
        /// There are no comments for TextInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TEXT_INFO", TypeName = "nvarchar(252)", Order = 99)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TextInfo")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string TextInfo {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcCopyModeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_ID", TypeName = "int", Order = 100)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcCopyModeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcCopyModeId {
            get; set;
        }


        /// <summary>
        /// There are no comments for PrcCopyModeDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_DESC", TypeName = "nvarchar(2000)", Order = 101)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcCopyModeDesc {
            get; set;
        }


        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 102)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int LanguageId {
            get; set;
        }


        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 103)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDelivery")]
        public virtual System.DateTime? DateDelivery {
            get; set;
        }


        /// <summary>
        /// There are no comments for DeadlineDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DEADLINE_DATE", TypeName = "date", Order = 104)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DeadlineDate")]
        public virtual System.DateTime? DeadlineDate {
            get; set;
        }


        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 105)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int SalesTaxMethodId {
            get; set;
        }


        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 106)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string SalesTaxMethodDesc {
            get; set;
        }


        /// <summary>
        /// There are no comments for DeadlineTime in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DEADLINE_TIME", TypeName = "time", Order = 107)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DeadlineTime")]
        public virtual global::System.TimeSpan? DeadlineTime {
            get; set;
        }


        /// <summary>
        /// There are no comments for MdcControllingunitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 108)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("MdcControllingUnitFk")]
        public virtual int? MdcControllingunitId {
            get; set;
        }


        /// <summary>
        /// There are no comments for MdcControllingunitCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(32)", Order = 109)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string MdcControllingunitCode {
            get; set;
        }


        /// <summary>
        /// There are no comments for MdcControllingunitDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 110)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcControllingunitDesc {
            get; set;
        }


        /// <summary>
        /// There are no comments for BaselineUpdateStatus in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_UPDATE_STATUS", TypeName = "nvarchar(252)", Order = 111)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselineUpdateStatus")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BaselineUpdateStatus {
            get; set;
        }


        /// <summary>
        /// There are no comments for BaselineCOMgntUpdate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_CO_MGNT_UPDATE", TypeName = "datetime", Order = 112)]
        [RIB.Visual.Platform.Common.InternalApiField("BaselineCOMgntUpdate")]
        public virtual System.DateTime? BaselineCOMgntUpdate {
            get; set;
        }


        /// <summary>
        /// There are no comments for DateAwardDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_AWARDDEADLINE", TypeName = "date", Order = 113)]
        [RIB.Visual.Platform.Common.InternalApiField("DateAwardDeadline")]
        public virtual System.DateTime? DateAwardDeadline {
            get; set;
        }


        /// <summary>
        /// There are no comments for DateRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REQUESTED", TypeName = "date", Order = 114)]
        [RIB.Visual.Platform.Common.InternalApiField("DateRequested")]
        public virtual System.DateTime? DateRequested {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 115)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual int? BasLanguageFk {
            get; set;
        }


        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 116)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BasLanguageDesc {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            PrcPackageApiEntity obj = new PrcPackageApiEntity();
            obj.Id = Id;
            obj.ProjectId = ProjectId;
            obj.ProjectCode = ProjectCode;
            obj.ProjectDescription = ProjectDescription;
            obj.PrcPackageStatusId = PrcPackageStatusId;
            obj.PrcPackageStatusDescription = PrcPackageStatusDescription;
            obj.Code = Code;
            obj.Description = Description;
            obj.PrcStructureId = PrcStructureId;
            obj.PrcStructureCode = PrcStructureCode;
            obj.PrcStructureDescription = PrcStructureDescription;
            obj.CurrencyId = CurrencyId;
            obj.CurrencyDescription = CurrencyDescription;
            obj.Reference = Reference;
            obj.PlannedStart = PlannedStart;
            obj.PlannedEnd = PlannedEnd;
            obj.ActualStart = ActualStart;
            obj.ActualEnd = ActualEnd;
            obj.Quantity = Quantity;
            obj.UomId = UomId;
            obj.UomDescription = UomDescription;
            obj.PrcPackageTypeId = PrcPackageTypeId;
            obj.PrcPackageTypeDescription = PrcPackageTypeDescription;
            obj.ClerkPrcId = ClerkPrcId;
            obj.ClerkPrcCode = ClerkPrcCode;
            obj.ClerkPrcDescription = ClerkPrcDescription;
            obj.ClerkReqId = ClerkReqId;
            obj.ClerkReqCode = ClerkReqCode;
            obj.ClerkReqDescription = ClerkReqDescription;
            obj.TaxCodeId = TaxCodeId;
            obj.TaxCodeCode = TaxCodeCode;
            obj.TaxCodeDescription = TaxCodeDescription;
            obj.Remark = Remark;
            obj.Remark2 = Remark2;
            obj.Remark3 = Remark3;
            obj.PsdActivityId = PsdActivityId;
            obj.PsdActivityCode = PsdActivityCode;
            obj.PsdActivityDescription = PsdActivityDescription;
            obj.PsdScheduleId = PsdScheduleId;
            obj.PsdScheduleCode = PsdScheduleCode;
            obj.PsdScheduleDescription = PsdScheduleDescription;
            obj.AddressId = AddressId;
            obj.AddressDescription = AddressDescription;
            obj.IsLive = IsLive;
            obj.UserDefined1 = UserDefined1;
            obj.UserDefined2 = UserDefined2;
            obj.UserDefined3 = UserDefined3;
            obj.UserDefined4 = UserDefined4;
            obj.UserDefined5 = UserDefined5;
            obj.OverallDiscount = OverallDiscount;
            obj.OverallDiscountOc = OverallDiscountOc;
            obj.OverallDiscountPercent = OverallDiscountPercent;
            obj.PrcConfigurationId = PrcConfigurationId;
            obj.PrcConfigurationDescription = PrcConfigurationDescription;
            obj.CompanyId = CompanyId;
            obj.CompanyCode = CompanyCode;
            obj.ExchangeRate = ExchangeRate;
            obj.SearchPattern = SearchPattern;
            obj.BusinessPartnerId = BusinessPartnerId;
            obj.BusinessPartnerDescription = BusinessPartnerDescription;
            obj.SubsidiaryId = SubsidiaryId;
            obj.SubsidiaryDescription = SubsidiaryDescription;
            obj.SupplierId = SupplierId;
            obj.SupplierCode = SupplierCode;
            obj.SupplierDescription = SupplierDescription;
            obj.BasCashProjectionId = BasCashProjectionId;
            obj.MdcAssetMasterId = MdcAssetMasterId;
            obj.MdcAssetMasterCode = MdcAssetMasterCode;
            obj.MdcAssetMasterDescription = MdcAssetMasterDescription;
            obj.PrcContractTypeId = PrcContractTypeId;
            obj.PrcContractTypeDescription = PrcContractTypeDescription;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            obj.BaselinePath = BaselinePath;
            obj.BaselineUpdate = BaselineUpdate;
            obj.UserDefinedDate1 = UserDefinedDate1;
            obj.UserDefinedDate2 = UserDefinedDate2;
            obj.UserDefinedDate3 = UserDefinedDate3;
            obj.UserDefinedDate4 = UserDefinedDate4;
            obj.UserDefinedDate5 = UserDefinedDate5;
            obj.DateEffective = DateEffective;
            obj.VatGroupId = VatGroupId;
            obj.VatGroupDescription = VatGroupDescription;
            obj.BaselinePhase = BaselinePhase;
            obj.BasCountryId = BasCountryId;
            obj.BasCountryDescription = BasCountryDescription;
            obj.PrjRegionId = PrjRegionId;
            obj.PrjRegionCode = PrjRegionCode;
            obj.PrjRegionDescription = PrjRegionDescription;
            obj.BasTelephoneNumberId = BasTelephoneNumberId;
            obj.BasTelephoneNumberDescription = BasTelephoneNumberDescription;
            obj.BasTelephoneTelefaxId = BasTelephoneTelefaxId;
            obj.BasTelephoneTelefaxDescription = BasTelephoneTelefaxDescription;
            obj.BasTelephoneMobileId = BasTelephoneMobileId;
            obj.BasTelephoneMobileDescription = BasTelephoneMobileDescription;
            obj.Email = Email;
            obj.TextInfo = TextInfo;
            obj.PrcCopyModeId = PrcCopyModeId;
            obj.PrcCopyModeDesc = PrcCopyModeDesc;
            obj.LanguageId = LanguageId;
            obj.DateDelivery = DateDelivery;
            obj.DeadlineDate = DeadlineDate;
            obj.SalesTaxMethodId = SalesTaxMethodId;
            obj.SalesTaxMethodDesc = SalesTaxMethodDesc;
            obj.DeadlineTime = DeadlineTime;
            obj.MdcControllingunitId = MdcControllingunitId;
            obj.MdcControllingunitCode = MdcControllingunitCode;
            obj.MdcControllingunitDesc = MdcControllingunitDesc;
            obj.BaselineUpdateStatus = BaselineUpdateStatus;
            obj.BaselineCOMgntUpdate = BaselineCOMgntUpdate;
            obj.DateAwardDeadline = DateAwardDeadline;
            obj.DateRequested = DateRequested;
            obj.BasLanguageFk = BasLanguageFk;
            obj.BasLanguageDesc = BasLanguageDesc;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(PrcPackageApiEntity clonedEntity);

    }


}
