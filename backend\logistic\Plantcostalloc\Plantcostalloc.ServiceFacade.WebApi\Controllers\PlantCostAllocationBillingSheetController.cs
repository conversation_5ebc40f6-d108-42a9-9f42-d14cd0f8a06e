using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using RIB.Visual.Platform.Core;

using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Final;
using RIB.Visual.Logistic.Plantcostalloc.BusinessComponents;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using System.Net.Http;
using System.Net;
using System.Threading.Tasks;
using System;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Dtos;

namespace RIB.Visual.Logistic.Plantcostalloc.ServiceFacade.WebApi
{
	/// <summary>
	/// Web API controller class for LogisticCostCodeRateEntity
	/// </summary>
	[RoutePrefix("logistic/plantcostalloc/billingsheet")]
	public class LogisticPlantCostAllocationBillingSheetController : EntityUpdateController<BillingSheetLogic, BillingSheetDto, BillingSheetEntity, IdentificationData>
	{
		/// <summary>
		/// Standard constructor doing the necessary initialisation.
		/// </summary>
		public LogisticPlantCostAllocationBillingSheetController()
		{
			this.CopyDto = d => d.Copy();
			this.CreateDto = e => new BillingSheetDto(e);
		}


		/// <summary>
		/// Returns a list
		/// </summary>
		[HttpPost]
		[Route("listbyparent")]
		public IEnumerable<BillingSheetDto> GetList(IdentificationData containerSpec)
		{
			int parent = containerSpec.PKey1.Value;
			return Logic.GetListByFilter(e => e.ProjectFk == parent).Select(CreateDto).ToArray();
		}

		/// <summary>
		/// Returns a list 
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("createbillingsheet")]
		public Task<HttpResponseMessage> CreateBillingSheets(CreateBillingSheetsData data)
		{
			var logText = new CreateReservationBillingSheetsWizardLogic().CreateRecordsForReserveBillingSheets(data);
			logText = logText.Replace("\\n", "<br/>");
			return Task.FromResult(Request.CreateResponse(HttpStatusCode.OK, logText));
		}

		/// <summary>
		/// Returns flag result indicating whether the operation quantity is readonly or not
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("operationquantityreadonlystatus")]
		public bool GetOperationQuantityReadonlyStatus(int projectId, DateTime recordDate)

		{
			bool isOperationQuantityReadonly = new CreateReservationBillingSheetsWizardLogic().GetOperationQuantityReadonlyStatus(projectId, recordDate);
			return isOperationQuantityReadonly;

		}

		/// <summary>
		/// create job to create billing sheet 
		/// </summary>
		[HttpPost]
		[Route("createjobtoexecute")]
		public void CreateJobForBillingSheet(CreateBillingSheetsData data)
		{
			new CreateJobForBillingSheetsLogic().CreateJobForBillingSheet(data);
		}

		/// <summary>
		/// change status
		/// </summary>
		/// <returns></returns>
		[Route("changestatus")]
		[HttpPost]
		public BillingSheetDto ChangeStatus(ChangeStatusDto dto)
		{
			var entity = Logic.SetStatus(new IdentificationData() { Id = dto.EntityId, PKey1 = dto.EntityPKey1 }, dto.NewStatusId, dto.OldStatusId);
			return entity == null ? null : new BillingSheetDto(entity);
		}
	}
}
