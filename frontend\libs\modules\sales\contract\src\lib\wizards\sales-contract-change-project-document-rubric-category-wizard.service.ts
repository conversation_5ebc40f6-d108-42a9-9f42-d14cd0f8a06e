/*
 * Copyright(c) RIB Software GmbH
 */

import {Injectable} from '@angular/core';
import { IInitializationContext } from '@libs/platform/common';
import {BasicsSharedDocumentWizard} from '@libs/documents/shared';
import { IBasicChangeProjectDocumentRubricCategoryService } from '@libs/basics/interfaces';
import { SalesContractDocumentProjectDataService } from '../services/sales-contract-document-project-data.service';

/**
 * Sales contract Change Project Document RubricCategory Wizard
 */
@Injectable({
	providedIn: 'root'
})
export class SalesContractChangeProjectDocumentRubricCategoryWizardService implements IBasicChangeProjectDocumentRubricCategoryService{
	public async execute(context: IInitializationContext) {
		const dataService = context.injector.get(SalesContractDocumentProjectDataService);
		await new BasicsSharedDocumentWizard().changeProjectDocumentRubricCategory(context, dataService);
	}
}