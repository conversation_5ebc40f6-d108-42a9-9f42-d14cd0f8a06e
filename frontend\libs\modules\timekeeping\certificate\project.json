{"name": "modules-timekeeping-certificate", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/modules/timekeeping/certificate/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/modules/timekeeping/certificate/jest.config.ts"}}}}