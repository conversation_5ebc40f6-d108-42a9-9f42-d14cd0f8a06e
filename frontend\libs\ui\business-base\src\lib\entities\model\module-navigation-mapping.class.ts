/*
 * Copyright(c) RIB Software GmbH
 */

import { ModuleName } from '@libs/platform/common';

/**
 * Helper class for getting navigatable module names.
 */
export class ModuleNavigationMapping {

	private moduleNavigationMapping: Partial<Record<ModuleName, ModuleName>> = {
		'scheduling.schedule': 'scheduling.main',
		'constructionsystem.project': 'constructionsystem.main'
	};

	/**
	 * Returns a navigatable module name
	 * @param moduleName modulename from the schema
	 * @returns a navigatable module name
	 */
	public getNavigatableModuleName(moduleName: ModuleName): ModuleName {
		const navigatableModuleName = this.moduleNavigationMapping[moduleName.toLowerCase() as ModuleName];
		if (navigatableModuleName) {
			return navigatableModuleName;
		}
		return moduleName;
	}
}