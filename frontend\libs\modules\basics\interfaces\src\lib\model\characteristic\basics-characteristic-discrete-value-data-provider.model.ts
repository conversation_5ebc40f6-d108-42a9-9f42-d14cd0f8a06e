import { LazyInjectionToken } from '@libs/platform/common';
import { IBasicsCharacteristicDiscreteValueDataProvider } from './basics-characteristic-discrete-value-data-provider.interface';
import { ICharacteristicValueEntity } from '../../model/entities/characteristic/characteristic-value-entity.interface';

export const BASICS_CHARACTERISTIC_DISCRETE_VALUE_DATA_PROVIDER = new LazyInjectionToken<IBasicsCharacteristicDiscreteValueDataProvider<ICharacteristicValueEntity>>('basics.characteristic.discrete.value.data.service');
