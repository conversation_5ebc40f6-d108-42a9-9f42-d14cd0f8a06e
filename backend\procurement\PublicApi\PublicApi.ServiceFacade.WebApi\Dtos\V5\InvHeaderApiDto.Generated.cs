﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Procurement.PublicApi.BusinessComponents;

namespace RIB.Visual.Procurement.PublicApi.ServiceFacade.WebApi.V5
{


    /// <summary>
    /// There are no comments for InvHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("INV_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(5)]
    public partial class InvHeaderApiDto : RIB.Visual.Platform.Core.ITypedDto<InvHeaderApiEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class InvHeaderApiDto.
        /// </summary>
        public InvHeaderApiDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class InvHeaderApiDto.
        /// </summary>
        /// <param name="entity">the instance of class InvHeaderApiEntity</param>
        public InvHeaderApiDto(InvHeaderApiEntity entity)
        {
            Id = entity.Id;
            InvStatusId = entity.InvStatusId;
            InvStatusDescription = entity.InvStatusDescription;
            CompanyId = entity.CompanyId;
            CompanyCode = entity.CompanyCode;
            RubricCategoryId = entity.RubricCategoryId;
            RubricCategoryDescription = entity.RubricCategoryDescription;
            PrcConfigurationId = entity.PrcConfigurationId;
            PrcConfigurationDescription = entity.PrcConfigurationDescription;
            Code = entity.Code;
            Description = entity.Description;
            BusinesspartnerId = entity.BusinesspartnerId;
            BusinesspartnerDescription = entity.BusinesspartnerDescription;
            SubsidiaryId = entity.SubsidiaryId;
            SubsidiaryDescription = entity.SubsidiaryDescription;
            SupplierId = entity.SupplierId;
            SupplierCode = entity.SupplierCode;
            SupplierDescription = entity.SupplierDescription;
            InvTypeId = entity.InvTypeId;
            InvTypeDescription = entity.InvTypeDescription;
            InvGroupId = entity.InvGroupId;
            InvGroupDescription = entity.InvGroupDescription;
            ClerkPrcId = entity.ClerkPrcId;
            ClerkPrcCode = entity.ClerkPrcCode;
            ClerkPrcDescription = entity.ClerkPrcDescription;
            ClerkReqId = entity.ClerkReqId;
            ClerkReqCode = entity.ClerkReqCode;
            ClerkReqDescription = entity.ClerkReqDescription;
            ClerkWfeId = entity.ClerkWfeId;
            ClerkWfeCode = entity.ClerkWfeCode;
            ClerkWfeDescription = entity.ClerkWfeDescription;
            CurrencyId = entity.CurrencyId;
            CurrencyDescription = entity.CurrencyDescription;
            AmountNet = entity.AmountNet;
            AmountNetOc = entity.AmountNetOc;
            AmountGross = entity.AmountGross;
            AmountGrossOc = entity.AmountGrossOc;
            Exchangerate = entity.Exchangerate;
            TaxCodeId = entity.TaxCodeId;
            TaxCodeCode = entity.TaxCodeCode;
            TaxCodeDescription = entity.TaxCodeDescription;
            DateInvoiced = entity.DateInvoiced;
            Reference = entity.Reference;
            DateReceived = entity.DateReceived;
            DatePosted = entity.DatePosted;
            ProjectId = entity.ProjectId;
            ProjectCode = entity.ProjectCode;
            ProjectDescription = entity.ProjectDescription;
            PackageId = entity.PackageId;
            PackageCode = entity.PackageCode;
            PackageDescription = entity.PackageDescription;
            MdcControllingunitId = entity.MdcControllingunitId;
            MdcControllingunitCode = entity.MdcControllingunitCode;
            MdcControllingunitDescription = entity.MdcControllingunitDescription;
            PrcStructureId = entity.PrcStructureId;
            PrcStructureCode = entity.PrcStructureCode;
            PrcStructureDescription = entity.PrcStructureDescription;
            Reconcilationhint = entity.Reconcilationhint;
            ConHeaderId = entity.ConHeaderId;
            ConHeaderCode = entity.ConHeaderCode;
            ConHeaderDescription = entity.ConHeaderDescription;
            PesHeaderId = entity.PesHeaderId;
            PesHeaderCode = entity.PesHeaderCode;
            PesHeaderDescription = entity.PesHeaderDescription;
            PaymentTermId = entity.PaymentTermId;
            PaymentTermCode = entity.PaymentTermCode;
            PaymentTermDescription = entity.PaymentTermDescription;
            DateDiscount = entity.DateDiscount;
            AmountDiscountbasis = entity.AmountDiscountbasis;
            AmountDiscountbasisOc = entity.AmountDiscountbasisOc;
            PercentDiscount = entity.PercentDiscount;
            AmountDiscount = entity.AmountDiscount;
            AmountDiscountOc = entity.AmountDiscountOc;
            DateNetpayable = entity.DateNetpayable;
            AmountNetpes = entity.AmountNetpes;
            AmountVatpes = entity.AmountVatpes;
            AmountNetcontract = entity.AmountNetcontract;
            AmountVatcontract = entity.AmountVatcontract;
            AmountNetother = entity.AmountNetother;
            AmountVatother = entity.AmountVatother;
            AmountNetreject = entity.AmountNetreject;
            AmountVatreject = entity.AmountVatreject;
            Remark = entity.Remark;
            UserDefined1 = entity.UserDefined1;
            UserDefined2 = entity.UserDefined2;
            UserDefined3 = entity.UserDefined3;
            UserDefined4 = entity.UserDefined4;
            UserDefined5 = entity.UserDefined5;
            SearchPattern = entity.SearchPattern;
            MdcBillingSchemaId = entity.MdcBillingSchemaId;
            MdcBillingSchemaDescription = entity.MdcBillingSchemaDescription;
            AmountNetpesOc = entity.AmountNetpesOc;
            AmountNetcontractOc = entity.AmountNetcontractOc;
            AmountNetotherOc = entity.AmountNetotherOc;
            AmountNetrejectOc = entity.AmountNetrejectOc;
            AmountVatpesOc = entity.AmountVatpesOc;
            AmountVatcontractOc = entity.AmountVatcontractOc;
            AmountVatotherOc = entity.AmountVatotherOc;
            AmountVatrejectOc = entity.AmountVatrejectOc;
            DateDelivered = entity.DateDelivered;
            DateDelivredfrom = entity.DateDelivredfrom;
            ContractTotal = entity.ContractTotal;
            ContractChangeorder = entity.ContractChangeorder;
            TotalPerformedNet = entity.TotalPerformedNet;
            TotalPerformedGross = entity.TotalPerformedGross;
            UserDefinedMoney01 = entity.UserDefinedMoney01;
            UserDefinedMoney02 = entity.UserDefinedMoney02;
            UserDefinedMoney03 = entity.UserDefinedMoney03;
            UserDefinedMoney04 = entity.UserDefinedMoney04;
            UserDefinedMoney05 = entity.UserDefinedMoney05;
            UserDefinedDate01 = entity.UserDefinedDate01;
            UserDefinedDate02 = entity.UserDefinedDate02;
            UserDefinedDate03 = entity.UserDefinedDate03;
            PaymentHint = entity.PaymentHint;
            Progressid = entity.Progressid;
            CompanyDeferaltypeId = entity.CompanyDeferaltypeId;
            CompanyDeferaltypeDescription = entity.CompanyDeferaltypeDescription;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            DateDeferalstart = entity.DateDeferalstart;
            VatgroupId = entity.VatgroupId;
            VatgroupDescription = entity.VatgroupDescription;
            ReferenceStructured = entity.ReferenceStructured;
            BasPaymentmethodId = entity.BasPaymentmethodId;
            BasPaymentmethodDescription = entity.BasPaymentmethodDescription;
            BankTypeId = entity.BankTypeId;
            BankTypeDescription = entity.BankTypeDescription;
            BankId = entity.BankId;
            LanguageId = entity.LanguageId;
            BasAccassignBusinessId = entity.BasAccassignBusinessId;
            BasAccassignBusinessDescription = entity.BasAccassignBusinessDescription;
            BasAccassignControlId = entity.BasAccassignControlId;
            BasAccassignControlDescription = entity.BasAccassignControlDescription;
            BasAccassignAccountId = entity.BasAccassignAccountId;
            BasAccassignAccountDescription = entity.BasAccassignAccountDescription;
            SalesTaxMethodId = entity.SalesTaxMethodId;
            SalesTaxMethodDesc = entity.SalesTaxMethodDesc;
            BpdContactId = entity.BpdContactId;
            BasAccassignConTypeId = entity.BasAccassignConTypeId;
            BasAccassignConTypeCode = entity.BasAccassignConTypeCode;
            BasAccassignConTypeDescription = entity.BasAccassignConTypeDescription;
            BpdContactDesc = entity.BpdContactDesc;
            BusinessPostingGroupId = entity.BusinessPostingGroupId;
            BusinessPostingGroupDesc = entity.BusinessPostingGroupDesc;
            RejectionRemark = entity.RejectionRemark;
            BasLanguageFk = entity.BasLanguageFk;
            BasLanguageDesc = entity.BasLanguageDesc;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// Id of the invoice
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// Id of the invstatus
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InvStatusFk")]
        [RIB.Visual.Platform.Common.EntityStatusFk("invoice")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int InvStatusId { get; set; }
    
        /// <summary>
        /// Description of invstatus
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string InvStatusDescription { get; set; }
    
        /// <summary>
        /// Id of the company
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyId { get; set; }
    
        /// <summary>
        /// Code of the company
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string CompanyCode { get; set; }
    
        /// <summary>
        /// Id of the rubriccategory
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_RUBRIC_CATEGORY_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RubricCategoryFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int RubricCategoryId { get; set; }
    
        /// <summary>
        /// Description of rubriccategory
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_RUBRIC_CATEGORY_DESC", TypeName = "nvarchar(2000)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string RubricCategoryDescription { get; set; }
    
        /// <summary>
        /// Id of the configuration
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 7)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcConfigurationFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcConfigurationId { get; set; }
    
        /// <summary>
        /// Description of configuration
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_DESC", TypeName = "nvarchar(2000)", Order = 8)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcConfigurationDescription { get; set; }
    
        /// <summary>
        /// Code
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 9)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// Description
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 10)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }
    
        /// <summary>
        /// Id of the businesspartner
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        public int? BusinesspartnerId { get; set; }
    
        /// <summary>
        /// Description of businesspartner
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BusinesspartnerDescription { get; set; }
    
        /// <summary>
        /// Id of the subsidiary
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 13)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public int? SubsidiaryId { get; set; }
    
        /// <summary>
        /// Description of subsidiary
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 14)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SubsidiaryDescription { get; set; }
    
        /// <summary>
        /// Id of the supplier
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 15)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public int? SupplierId { get; set; }
    
        /// <summary>
        /// Code of the supplier
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierCode { get; set; }
    
        /// <summary>
        /// Description of supplier
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 17)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierDescription { get; set; }
    
        /// <summary>
        /// Id of the invtype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_TYPE_ID", TypeName = "int", Order = 18)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InvTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int InvTypeId { get; set; }
    
        /// <summary>
        /// Description of invtype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 19)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string InvTypeDescription { get; set; }
    
        /// <summary>
        /// Id of the invgroup
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_GROUP_ID", TypeName = "int", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InvGroupFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int InvGroupId { get; set; }
    
        /// <summary>
        /// Description of invgroup
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_GROUP_DESC", TypeName = "nvarchar(2000)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string InvGroupDescription { get; set; }
    
        /// <summary>
        /// Id of the clerkprc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 22)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public int? ClerkPrcId { get; set; }
    
        /// <summary>
        /// Code of the clerkprc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 23)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkPrcCode { get; set; }
    
        /// <summary>
        /// Description of clerkprc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkPrcDescription { get; set; }
    
        /// <summary>
        /// Id of the clerkreq
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 25)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public int? ClerkReqId { get; set; }
    
        /// <summary>
        /// Code of the clerkreq
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 26)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkReqCode { get; set; }
    
        /// <summary>
        /// Description of clerkreq
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 27)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkReqDescription { get; set; }
    
        /// <summary>
        /// Id of the clerkwfe
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_WFE_ID", TypeName = "int", Order = 28)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkWfeFk")]
        public int? ClerkWfeId { get; set; }
    
        /// <summary>
        /// Code of the clerkwfe
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_WFE_CODE", TypeName = "nvarchar(16)", Order = 29)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkWfeCode { get; set; }
    
        /// <summary>
        /// Description of clerkwfe
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_WFE_DESC", TypeName = "nvarchar(252)", Order = 30)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkWfeDescription { get; set; }
    
        /// <summary>
        /// Id of the currency
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 31)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CurrencyId { get; set; }
    
        /// <summary>
        /// Description of currency
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 32)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string CurrencyDescription { get; set; }
    
        /// <summary>
        /// AmountNet
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NET", TypeName = "numeric(19,7)", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNet")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNet { get; set; }
    
        /// <summary>
        /// AmountNetOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NET_OC", TypeName = "numeric(19,7)", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNetOc { get; set; }
    
        /// <summary>
        /// AmountGross
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_GROSS", TypeName = "numeric(19,7)", Order = 35)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountGross")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountGross { get; set; }
    
        /// <summary>
        /// AmountGrossOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_GROSS_OC", TypeName = "numeric(19,7)", Order = 36)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountGrossOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountGrossOc { get; set; }
    
        /// <summary>
        /// ExchangeRate
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 37)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal Exchangerate { get; set; }
    
        /// <summary>
        /// Id of the taxcode
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TaxCodeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int TaxCodeId { get; set; }
    
        /// <summary>
        /// Code of the taxcode
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_CODE", TypeName = "nvarchar(16)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string TaxCodeCode { get; set; }
    
        /// <summary>
        /// Description of taxcode
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_DESC", TypeName = "nvarchar(2000)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string TaxCodeDescription { get; set; }
    
        /// <summary>
        /// DateInvoiced
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_INVOICED", TypeName = "date", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateInvoiced")]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateInvoiced { get; set; }
    
        /// <summary>
        /// Reference
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REFERENCE", TypeName = "nvarchar(252)", Order = 42)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Reference")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Reference { get; set; }
    
        /// <summary>
        /// DateReceived
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_RECEIVED", TypeName = "date", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateReceived")]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateReceived { get; set; }
    
        /// <summary>
        /// DatePosted
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_POSTED", TypeName = "date", Order = 44)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DatePosted")]
        public System.DateTime? DatePosted { get; set; }
    
        /// <summary>
        /// Id of the project
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public int? ProjectId { get; set; }
    
        /// <summary>
        /// Code of the project
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 46)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ProjectCode { get; set; }
    
        /// <summary>
        /// Description of project
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 47)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ProjectDescription { get; set; }
    
        /// <summary>
        /// Id of the package
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_ID", TypeName = "int", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcPackageFk")]
        public int? PackageId { get; set; }
    
        /// <summary>
        /// Code of the package
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_CODE", TypeName = "nvarchar(16)", Order = 49)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PackageCode { get; set; }
    
        /// <summary>
        /// Description of package
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_DESC", TypeName = "nvarchar(252)", Order = 50)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PackageDescription { get; set; }
    
        /// <summary>
        /// Id of the controllingunit
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingUnitFk")]
        public int? MdcControllingunitId { get; set; }
    
        /// <summary>
        /// Code of the controllingunit
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(16)", Order = 52)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string MdcControllingunitCode { get; set; }
    
        /// <summary>
        /// Description of controllingunit
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 53)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcControllingunitDescription { get; set; }
    
        /// <summary>
        /// Id of the structure
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_ID", TypeName = "int", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcStructureFk")]
        public int? PrcStructureId { get; set; }
    
        /// <summary>
        /// Code of the structure
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_CODE", TypeName = "nvarchar(16)", Order = 55)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PrcStructureCode { get; set; }
    
        /// <summary>
        /// Description of structure
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_DESC", TypeName = "nvarchar(2000)", Order = 56)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcStructureDescription { get; set; }
    
        /// <summary>
        /// ReconcilationHint
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RECONCILATIONHINT", TypeName = "nvarchar(255)", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReconcilationHint")]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public string Reconcilationhint { get; set; }
    
        /// <summary>
        /// Id of the contract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConHeaderFk")]
        public int? ConHeaderId { get; set; }
    
        /// <summary>
        /// Code of the contract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_CODE", TypeName = "nvarchar(16)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ConHeaderCode { get; set; }
    
        /// <summary>
        /// Description of contract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_DESC", TypeName = "nvarchar(252)", Order = 60)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ConHeaderDescription { get; set; }
    
        /// <summary>
        /// Id of the pes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_HEADER_ID", TypeName = "int", Order = 61)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PesHeaderFk")]
        public int? PesHeaderId { get; set; }
    
        /// <summary>
        /// Code of the pes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_HEADER_CODE", TypeName = "nvarchar(16)", Order = 62)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PesHeaderCode { get; set; }
    
        /// <summary>
        /// Description of pes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_HEADER_DESC", TypeName = "nvarchar(252)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PesHeaderDescription { get; set; }
    
        /// <summary>
        /// Id of the paymentterm
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_ID", TypeName = "int", Order = 64)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermFk")]
        public int? PaymentTermId { get; set; }
    
        /// <summary>
        /// Code of the paymentterm
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_CODE", TypeName = "nvarchar(16)", Order = 65)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermCode { get; set; }
    
        /// <summary>
        /// Description of paymentterm
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_DESC", TypeName = "nvarchar(2000)", Order = 66)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermDescription { get; set; }
    
        /// <summary>
        /// DateDiscount
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DISCOUNT", TypeName = "date", Order = 67)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDiscount")]
        public System.DateTime? DateDiscount { get; set; }
    
        /// <summary>
        /// AmountDiscountBasis
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNTBASIS", TypeName = "numeric(19,7)", Order = 68)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountDiscountBasis")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountDiscountbasis { get; set; }
    
        /// <summary>
        /// AmountDiscountBasisOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNTBASIS_OC", TypeName = "numeric(19,7)", Order = 69)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountDiscountBasisOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountDiscountbasisOc { get; set; }
    
        /// <summary>
        /// PercentDiscount
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PERCENT_DISCOUNT", TypeName = "numeric(10,2)", Order = 70)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PercentDiscount")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal PercentDiscount { get; set; }
    
        /// <summary>
        /// AmountDiscount
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNT", TypeName = "numeric(19,7)", Order = 71)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountDiscount")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountDiscount { get; set; }
    
        /// <summary>
        /// AmountDiscountOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 72)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountDiscountOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountDiscountOc { get; set; }
    
        /// <summary>
        /// DateNetPayable
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_NETPAYABLE", TypeName = "date", Order = 73)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateNetPayable")]
        public System.DateTime? DateNetpayable { get; set; }
    
        /// <summary>
        /// AmountNetPes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETPES", TypeName = "numeric(19,7)", Order = 74)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetPes")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNetpes { get; set; }
    
        /// <summary>
        /// AmountVatPes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATPES", TypeName = "numeric(19,7)", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatPes")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountVatpes { get; set; }
    
        /// <summary>
        /// AmountNetContract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETCONTRACT", TypeName = "numeric(19,7)", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetContract")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNetcontract { get; set; }
    
        /// <summary>
        /// AmountVatContract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATCONTRACT", TypeName = "numeric(19,7)", Order = 77)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatContract")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountVatcontract { get; set; }
    
        /// <summary>
        /// AmountNetOther
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETOTHER", TypeName = "numeric(19,7)", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetOther")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNetother { get; set; }
    
        /// <summary>
        /// AmountVatOther
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATOTHER", TypeName = "numeric(19,7)", Order = 79)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatOther")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountVatother { get; set; }
    
        /// <summary>
        /// AmountNetReject
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETREJECT", TypeName = "numeric(19,7)", Order = 80)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetReject")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNetreject { get; set; }
    
        /// <summary>
        /// AmountVatReject
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATREJECT", TypeName = "numeric(19,7)", Order = 81)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatReject")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountVatreject { get; set; }
    
        /// <summary>
        /// Remark
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 82)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark { get; set; }
    
        /// <summary>
        /// Userdefined1
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 83)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined1 { get; set; }
    
        /// <summary>
        /// Userdefined2
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 84)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined2 { get; set; }
    
        /// <summary>
        /// Userdefined3
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 85)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined3 { get; set; }
    
        /// <summary>
        /// Userdefined4
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined4 { get; set; }
    
        /// <summary>
        /// Userdefined5
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 87)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined5 { get; set; }
    
        /// <summary>
        /// SearchPattern
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 88)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public string SearchPattern { get; set; }
    
        /// <summary>
        /// Id of the billingschema
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_ID", TypeName = "int", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BillingSchemaFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int MdcBillingSchemaId { get; set; }
    
        /// <summary>
        /// Description of billingschema
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 90)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcBillingSchemaDescription { get; set; }
    
        /// <summary>
        /// AmountNetPesOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETPES_OC", TypeName = "numeric(19,7)", Order = 91)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetPesOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNetpesOc { get; set; }
    
        /// <summary>
        /// AmountNetContractOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETCONTRACT_OC", TypeName = "numeric(19,7)", Order = 92)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetContractOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNetcontractOc { get; set; }
    
        /// <summary>
        /// AmountNetOtherOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETOTHER_OC", TypeName = "numeric(19,7)", Order = 93)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetOtherOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNetotherOc { get; set; }
    
        /// <summary>
        /// AmountNetRejectOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETREJECT_OC", TypeName = "numeric(19,7)", Order = 94)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetRejectOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountNetrejectOc { get; set; }
    
        /// <summary>
        /// AmountVatPesOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATPES_OC", TypeName = "numeric(19,7)", Order = 95)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatPesOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountVatpesOc { get; set; }
    
        /// <summary>
        /// AmountVatContractOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATCONTRACT_OC", TypeName = "numeric(19,7)", Order = 96)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatContractOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountVatcontractOc { get; set; }
    
        /// <summary>
        /// AmountVatOtherOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATOTHER_OC", TypeName = "numeric(19,7)", Order = 97)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatOtherOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountVatotherOc { get; set; }
    
        /// <summary>
        /// AmountVatRejectOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATREJECT_OC", TypeName = "numeric(19,7)", Order = 98)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatRejectOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountVatrejectOc { get; set; }
    
        /// <summary>
        /// DateDelivered
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERED", TypeName = "date", Order = 99)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDelivered")]
        public System.DateTime? DateDelivered { get; set; }
    
        /// <summary>
        /// DateDeliveredFrom
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVREDFROM", TypeName = "date", Order = 100)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDeliveredFrom")]
        public System.DateTime? DateDelivredfrom { get; set; }
    
        /// <summary>
        /// ContractTotal
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONTRACT_TOTAL", TypeName = "numeric(19,7)", Order = 101)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContractTotal")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal ContractTotal { get; set; }
    
        /// <summary>
        /// ContractChangeOrder
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONTRACT_CHANGEORDER", TypeName = "numeric(19,7)", Order = 102)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContractChangeOrder")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal ContractChangeorder { get; set; }
    
        /// <summary>
        /// TotalPerformedNet
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TOTAL_PERFORMED_NET", TypeName = "numeric(19,7)", Order = 103)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TotalPerformedNet")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal TotalPerformedNet { get; set; }
    
        /// <summary>
        /// TotalPerformedGross
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TOTAL_PERFORMED_GROSS", TypeName = "numeric(19,7)", Order = 104)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TotalPerformedGross")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal TotalPerformedGross { get; set; }
    
        /// <summary>
        /// UserDefinedMoney01
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY01", TypeName = "numeric(19,7)", Order = 105)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney01")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal UserDefinedMoney01 { get; set; }
    
        /// <summary>
        /// UserDefinedMoney02
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY02", TypeName = "numeric(19,7)", Order = 106)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney02")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal UserDefinedMoney02 { get; set; }
    
        /// <summary>
        /// UserDefinedMoney03
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY03", TypeName = "numeric(19,7)", Order = 107)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney03")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal UserDefinedMoney03 { get; set; }
    
        /// <summary>
        /// UserDefinedMoney04
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY04", TypeName = "numeric(19,7)", Order = 108)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney04")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal UserDefinedMoney04 { get; set; }
    
        /// <summary>
        /// UserDefinedMoney05
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY05", TypeName = "numeric(19,7)", Order = 109)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney05")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal UserDefinedMoney05 { get; set; }
    
        /// <summary>
        /// UserDefinedDate01
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE01", TypeName = "date", Order = 110)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate01")]
        public System.DateTime? UserDefinedDate01 { get; set; }
    
        /// <summary>
        /// UserDefinedDate02
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE02", TypeName = "date", Order = 111)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate02")]
        public System.DateTime? UserDefinedDate02 { get; set; }
    
        /// <summary>
        /// UserDefinedDate03
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE03", TypeName = "date", Order = 112)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate03")]
        public System.DateTime? UserDefinedDate03 { get; set; }
    
        /// <summary>
        /// PaymentHint
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PAYMENT_HINT", TypeName = "nvarchar(3)", Order = 113)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentHint")]
        [System.ComponentModel.DataAnnotations.StringLength(3)]
        public string PaymentHint { get; set; }
    
        /// <summary>
        /// ProgressId
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROGRESSID", TypeName = "int", Order = 114)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProgressId")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Progressid { get; set; }
    
        /// <summary>
        /// Id of the companydeferaltype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_DEFERALTYPE_ID", TypeName = "int", Order = 115)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyDeferalTypeFk")]
        public int? CompanyDeferaltypeId { get; set; }
    
        /// <summary>
        /// Description of companydeferaltype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_DEFERALTYPE_DESC", TypeName = "nvarchar(2000)", Order = 116)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string CompanyDeferaltypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 117)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 118)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 119)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 120)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 121)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }
    
        /// <summary>
        /// DateDeferalStart
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DEFERALSTART", TypeName = "date", Order = 122)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDeferalStart")]
        public System.DateTime? DateDeferalstart { get; set; }
    
        /// <summary>
        /// Id of the vatgroup
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 123)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public int? VatgroupId { get; set; }
    
        /// <summary>
        /// Description of vatgroup
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 124)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string VatgroupDescription { get; set; }
    
        /// <summary>
        /// ReferenceStructured
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REFERENCE_STRUCTURED", TypeName = "nvarchar(252)", Order = 125)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReferenceStructured")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ReferenceStructured { get; set; }
    
        /// <summary>
        /// Id of the paymentmethod
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENTMETHOD_ID", TypeName = "int", Order = 126)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasPaymentMethodFk")]
        public int? BasPaymentmethodId { get; set; }
    
        /// <summary>
        /// Description of paymentmethod
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENTMETHOD_DESC", TypeName = "nvarchar(2000)", Order = 127)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasPaymentmethodDescription { get; set; }
    
        /// <summary>
        /// Id of the banktype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_TYPE_ID", TypeName = "int", Order = 128)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdBankTypeFk")]
        public int? BankTypeId { get; set; }
    
        /// <summary>
        /// Description of banktype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 129)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BankTypeDescription { get; set; }
    
        /// <summary>
        /// Id of the bank
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_ID", TypeName = "int", Order = 130)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BankFk")]
        public int? BankId { get; set; }
    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 131)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int LanguageId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignBusinessId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_BUSINESS_ID", TypeName = "int", Order = 132)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignBusinessFk")]
        public int? BasAccassignBusinessId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignBusinessDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_BUSINESS_DESC", TypeName = "nvarchar(2000)", Order = 133)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasAccassignBusinessDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignControlId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CONTROL_ID", TypeName = "int", Order = 134)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignControlFk")]
        public int? BasAccassignControlId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignControlDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CONTROL_DESC", TypeName = "nvarchar(2000)", Order = 135)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasAccassignControlDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignAccountId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_ACCOUNT_ID", TypeName = "int", Order = 136)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignAccountFk")]
        public int? BasAccassignAccountId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignAccountDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_ACCOUNT_DESC", TypeName = "nvarchar(2000)", Order = 137)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasAccassignAccountDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 139)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int SalesTaxMethodId { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 138)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string SalesTaxMethodDesc { get; set; }
    
        /// <summary>
        /// There are no comments for BpdContactId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_ID", TypeName = "int", Order = 140)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContactFk")]
        public int? BpdContactId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignConTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_ID", TypeName = "int", Order = 141)]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignConTypeFk")]
        public int? BasAccassignConTypeId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignConTypeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_CODE", TypeName = "nvarchar(16)", Order = 142)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string BasAccassignConTypeCode { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignConTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 143)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasAccassignConTypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BpdContactDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_DESC", TypeName = "nvarchar(505)", Order = 144)]
        [System.ComponentModel.DataAnnotations.StringLength(505)]
        public string BpdContactDesc { get; set; }
    
        /// <summary>
        /// There are no comments for BusinessPostingGroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPOSTINGGROUP_ID", TypeName = "int", Order = 145)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPostingGroupFk")]
        public int? BusinessPostingGroupId { get; set; }
    
        /// <summary>
        /// There are no comments for BusinessPostingGroupDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPOSTINGGROUP_DESC", TypeName = "nvarchar(2000)", Order = 146)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BusinessPostingGroupDesc { get; set; }
    
        /// <summary>
        /// There are no comments for RejectionRemark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REJECTION_REMARK", TypeName = "nvarchar(2000)", Order = 147)]
        [RIB.Visual.Platform.Common.InternalApiField("RejectionRemark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string RejectionRemark { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 148)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public int? BasLanguageFk { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 149)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BasLanguageDesc { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(InvHeaderApiEntity); }
        }

        /// <summary>
        /// Copy the current InvHeaderApiDto instance to a new InvHeaderApiEntity instance.
        /// </summary>
        /// <returns>a new instance of class InvHeaderApiEntity</returns>
        public InvHeaderApiEntity Copy()
        {
          var entity = new InvHeaderApiEntity();

          entity.Id = this.Id;
          entity.InvStatusId = this.InvStatusId;
          entity.InvStatusDescription = this.InvStatusDescription;
          entity.CompanyId = this.CompanyId;
          entity.CompanyCode = this.CompanyCode;
          entity.RubricCategoryId = this.RubricCategoryId;
          entity.RubricCategoryDescription = this.RubricCategoryDescription;
          entity.PrcConfigurationId = this.PrcConfigurationId;
          entity.PrcConfigurationDescription = this.PrcConfigurationDescription;
          entity.Code = this.Code;
          entity.Description = this.Description;
          entity.BusinesspartnerId = this.BusinesspartnerId;
          entity.BusinesspartnerDescription = this.BusinesspartnerDescription;
          entity.SubsidiaryId = this.SubsidiaryId;
          entity.SubsidiaryDescription = this.SubsidiaryDescription;
          entity.SupplierId = this.SupplierId;
          entity.SupplierCode = this.SupplierCode;
          entity.SupplierDescription = this.SupplierDescription;
          entity.InvTypeId = this.InvTypeId;
          entity.InvTypeDescription = this.InvTypeDescription;
          entity.InvGroupId = this.InvGroupId;
          entity.InvGroupDescription = this.InvGroupDescription;
          entity.ClerkPrcId = this.ClerkPrcId;
          entity.ClerkPrcCode = this.ClerkPrcCode;
          entity.ClerkPrcDescription = this.ClerkPrcDescription;
          entity.ClerkReqId = this.ClerkReqId;
          entity.ClerkReqCode = this.ClerkReqCode;
          entity.ClerkReqDescription = this.ClerkReqDescription;
          entity.ClerkWfeId = this.ClerkWfeId;
          entity.ClerkWfeCode = this.ClerkWfeCode;
          entity.ClerkWfeDescription = this.ClerkWfeDescription;
          entity.CurrencyId = this.CurrencyId;
          entity.CurrencyDescription = this.CurrencyDescription;
          entity.AmountNet = this.AmountNet;
          entity.AmountNetOc = this.AmountNetOc;
          entity.AmountGross = this.AmountGross;
          entity.AmountGrossOc = this.AmountGrossOc;
          entity.Exchangerate = this.Exchangerate;
          entity.TaxCodeId = this.TaxCodeId;
          entity.TaxCodeCode = this.TaxCodeCode;
          entity.TaxCodeDescription = this.TaxCodeDescription;
          entity.DateInvoiced = this.DateInvoiced;
          entity.Reference = this.Reference;
          entity.DateReceived = this.DateReceived;
          entity.DatePosted = this.DatePosted;
          entity.ProjectId = this.ProjectId;
          entity.ProjectCode = this.ProjectCode;
          entity.ProjectDescription = this.ProjectDescription;
          entity.PackageId = this.PackageId;
          entity.PackageCode = this.PackageCode;
          entity.PackageDescription = this.PackageDescription;
          entity.MdcControllingunitId = this.MdcControllingunitId;
          entity.MdcControllingunitCode = this.MdcControllingunitCode;
          entity.MdcControllingunitDescription = this.MdcControllingunitDescription;
          entity.PrcStructureId = this.PrcStructureId;
          entity.PrcStructureCode = this.PrcStructureCode;
          entity.PrcStructureDescription = this.PrcStructureDescription;
          entity.Reconcilationhint = this.Reconcilationhint;
          entity.ConHeaderId = this.ConHeaderId;
          entity.ConHeaderCode = this.ConHeaderCode;
          entity.ConHeaderDescription = this.ConHeaderDescription;
          entity.PesHeaderId = this.PesHeaderId;
          entity.PesHeaderCode = this.PesHeaderCode;
          entity.PesHeaderDescription = this.PesHeaderDescription;
          entity.PaymentTermId = this.PaymentTermId;
          entity.PaymentTermCode = this.PaymentTermCode;
          entity.PaymentTermDescription = this.PaymentTermDescription;
          entity.DateDiscount = this.DateDiscount;
          entity.AmountDiscountbasis = this.AmountDiscountbasis;
          entity.AmountDiscountbasisOc = this.AmountDiscountbasisOc;
          entity.PercentDiscount = this.PercentDiscount;
          entity.AmountDiscount = this.AmountDiscount;
          entity.AmountDiscountOc = this.AmountDiscountOc;
          entity.DateNetpayable = this.DateNetpayable;
          entity.AmountNetpes = this.AmountNetpes;
          entity.AmountVatpes = this.AmountVatpes;
          entity.AmountNetcontract = this.AmountNetcontract;
          entity.AmountVatcontract = this.AmountVatcontract;
          entity.AmountNetother = this.AmountNetother;
          entity.AmountVatother = this.AmountVatother;
          entity.AmountNetreject = this.AmountNetreject;
          entity.AmountVatreject = this.AmountVatreject;
          entity.Remark = this.Remark;
          entity.UserDefined1 = this.UserDefined1;
          entity.UserDefined2 = this.UserDefined2;
          entity.UserDefined3 = this.UserDefined3;
          entity.UserDefined4 = this.UserDefined4;
          entity.UserDefined5 = this.UserDefined5;
          entity.SearchPattern = this.SearchPattern;
          entity.MdcBillingSchemaId = this.MdcBillingSchemaId;
          entity.MdcBillingSchemaDescription = this.MdcBillingSchemaDescription;
          entity.AmountNetpesOc = this.AmountNetpesOc;
          entity.AmountNetcontractOc = this.AmountNetcontractOc;
          entity.AmountNetotherOc = this.AmountNetotherOc;
          entity.AmountNetrejectOc = this.AmountNetrejectOc;
          entity.AmountVatpesOc = this.AmountVatpesOc;
          entity.AmountVatcontractOc = this.AmountVatcontractOc;
          entity.AmountVatotherOc = this.AmountVatotherOc;
          entity.AmountVatrejectOc = this.AmountVatrejectOc;
          entity.DateDelivered = this.DateDelivered;
          entity.DateDelivredfrom = this.DateDelivredfrom;
          entity.ContractTotal = this.ContractTotal;
          entity.ContractChangeorder = this.ContractChangeorder;
          entity.TotalPerformedNet = this.TotalPerformedNet;
          entity.TotalPerformedGross = this.TotalPerformedGross;
          entity.UserDefinedMoney01 = this.UserDefinedMoney01;
          entity.UserDefinedMoney02 = this.UserDefinedMoney02;
          entity.UserDefinedMoney03 = this.UserDefinedMoney03;
          entity.UserDefinedMoney04 = this.UserDefinedMoney04;
          entity.UserDefinedMoney05 = this.UserDefinedMoney05;
          entity.UserDefinedDate01 = this.UserDefinedDate01;
          entity.UserDefinedDate02 = this.UserDefinedDate02;
          entity.UserDefinedDate03 = this.UserDefinedDate03;
          entity.PaymentHint = this.PaymentHint;
          entity.Progressid = this.Progressid;
          entity.CompanyDeferaltypeId = this.CompanyDeferaltypeId;
          entity.CompanyDeferaltypeDescription = this.CompanyDeferaltypeDescription;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;
          entity.DateDeferalstart = this.DateDeferalstart;
          entity.VatgroupId = this.VatgroupId;
          entity.VatgroupDescription = this.VatgroupDescription;
          entity.ReferenceStructured = this.ReferenceStructured;
          entity.BasPaymentmethodId = this.BasPaymentmethodId;
          entity.BasPaymentmethodDescription = this.BasPaymentmethodDescription;
          entity.BankTypeId = this.BankTypeId;
          entity.BankTypeDescription = this.BankTypeDescription;
          entity.BankId = this.BankId;
          entity.LanguageId = this.LanguageId;
          entity.BasAccassignBusinessId = this.BasAccassignBusinessId;
          entity.BasAccassignBusinessDescription = this.BasAccassignBusinessDescription;
          entity.BasAccassignControlId = this.BasAccassignControlId;
          entity.BasAccassignControlDescription = this.BasAccassignControlDescription;
          entity.BasAccassignAccountId = this.BasAccassignAccountId;
          entity.BasAccassignAccountDescription = this.BasAccassignAccountDescription;
          entity.SalesTaxMethodId = this.SalesTaxMethodId;
          entity.SalesTaxMethodDesc = this.SalesTaxMethodDesc;
          entity.BpdContactId = this.BpdContactId;
          entity.BasAccassignConTypeId = this.BasAccassignConTypeId;
          entity.BasAccassignConTypeCode = this.BasAccassignConTypeCode;
          entity.BasAccassignConTypeDescription = this.BasAccassignConTypeDescription;
          entity.BpdContactDesc = this.BpdContactDesc;
          entity.BusinessPostingGroupId = this.BusinessPostingGroupId;
          entity.BusinessPostingGroupDesc = this.BusinessPostingGroupDesc;
          entity.RejectionRemark = this.RejectionRemark;
          entity.BasLanguageFk = this.BasLanguageFk;
          entity.BasLanguageDesc = this.BasLanguageDesc;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(InvHeaderApiEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(InvHeaderApiEntity entity);
    }

}
