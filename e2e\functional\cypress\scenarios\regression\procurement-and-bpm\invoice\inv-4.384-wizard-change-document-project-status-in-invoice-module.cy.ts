import {_businessPartnerPage,_projectPage, _common, _controllingUnit, _package, _sidebar, _mainView, _validate, _modalView, _rfqPage, _saleContractPage, _procurementContractPage, _materialPage, _commonAPI } from 'cypress/pages';
import { cnt, tile, app, sidebar, commonLocators, btn } from 'cypress/locators';
import Buttons from 'cypress/locators/buttons';
import CommonLocators from 'cypress/locators/common-locators';
import apiConstantData from 'cypress/constantData/apiConstantData';
import { CONTAINER_COLUMNS_CONTROLLING_UNITS } from 'cypress/scenarios/sequential-part-3/controlling/controlling/mdc-2.16-verify-bis-export-with-cost-code-description-having-maximum-character-field-length.cy';


const INVOICE_NO = _common.generateRandomString(5);
const DOCUMENTS_PROJECT_DESC1 = _common.generateRandomString(5);
const DOCUMENTS_PROJECT_DESC2 = _common.generateRandomString(5);
const REMARK = _common.generateRandomString(6);

let CONTAINERS_INVOICE;
let CONTAINER_COLUMNS_INVOICE
let CONTAINERS_CONTROLLING_UNIT
let CONTAINER_COLUMNS_PROJECT_DOCUMENTS
let CONTROLLING_UNIT_PARAMETERS
let CONTAINERS_DATA_RECORDS


describe('PCM- 4.384 | Wizard change document project status in invoice module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('pcm/inv-4.384-wizard-change -document-project-status-in-invoice-module.json').then((data) => {
            this.data = data; 
			CONTAINERS_DATA_RECORDS = this.data.CONTAINERS.DATA_RECORDS           
            CONTAINERS_INVOICE = this.data.CONTAINERS.INVOICE;
            CONTAINER_COLUMNS_INVOICE = this.data.CONTAINER_COLUMNS.INVOICE;      
            CONTAINER_COLUMNS_PROJECT_DOCUMENTS=this.data.CONTAINER_COLUMNS.PROJECT_DOCUMENTS
            CONTAINERS_CONTROLLING_UNIT=this.data.CONTAINERS.CONTROLLING_UNIT
            CONTROLLING_UNIT_PARAMETERS = {
                [app.GridCells.QUANTITY_SMALL]:[CONTAINERS_CONTROLLING_UNIT.QUANTITY[0],CONTAINERS_CONTROLLING_UNIT.QUANTITY[0]],
                [app.GridCells.BAS_UOM_FK]:[apiConstantData.ID.UOM_BAGS,apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]:["true","true","true"]
            }
        });
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.waitForLoaderToDisappear()
        _common.openDesktopTile(tile.DesktopTiles.PROJECT)
        _common.waitForLoaderToDisappear()
			_commonAPI.getAccessToken()
                      .then((result) => {
                        cy.log(`Token Retrieved: ${result.token}`);
                      });
    });
    after(() => {
        cy.LOGOUT();
    });

    it("TC - API: Create project", function () {
        _commonAPI.createProject()
            .then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
                _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'),2,CONTROLLING_UNIT_PARAMETERS)
            });
    })

    it("TC - Create invoice record in invoice container", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.INVOICE);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.INVOICEHEADER, app.FooterTab.INVOICEHEADER, 1)
            _common.setup_gridLayout(cnt.uuid.INVOICEHEADER, CONTAINER_COLUMNS_INVOICE);
            _common.set_columnAtTop([CONTAINER_COLUMNS_INVOICE.businesspartnerfk, CONTAINER_COLUMNS_INVOICE.code, CONTAINER_COLUMNS_INVOICE.controllingunitfk, CONTAINER_COLUMNS_INVOICE.prcstructurefk], cnt.uuid.INVOICEHEADER)
        })
        _common.maximizeContainer(cnt.uuid.INVOICEHEADER)
        _common.clear_subContainerFilter(cnt.uuid.INVOICEHEADER)
        _common.create_newRecord(cnt.uuid.INVOICEHEADER)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.INVOICEHEADER, app.GridCells.REFERENCE, app.InputFields.DOMAIN_TYPE_DESCRIPTION, INVOICE_NO)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.BUSINESS_PARTNER)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.PRC_CONFIGURATION_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.CONFIGURATION)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.PRC_STRUCTURE_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.STRUCTURE_CODE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.INVOICEHEADER)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create record in documents project and verify status in overview container", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, INVOICE_NO)
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.INVOICEHEADER, app.FooterTab.INVOICEHEADER, 1)
        })
        _common.select_rowInSubContainer(cnt.uuid.INVOICEHEADER)
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_DOCUMENTS, app.FooterTab.DOCUMENTS_PROJECT, 1);
            _common.setup_gridLayout(cnt.uuid.PROJECT_DOCUMENTS, CONTAINER_COLUMNS_PROJECT_DOCUMENTS);
            _common.set_columnAtTop([CONTAINER_COLUMNS_PROJECT_DOCUMENTS.description], cnt.uuid.PROJECT_DOCUMENTS)
            _common.clear_subContainerFilter(cnt.uuid.PROJECT_DOCUMENTS)
        });
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROJECT_DOCUMENTS)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_DOCUMENTS)
        _common.edit_containerCell(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, DOCUMENTS_PROJECT_DESC1)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(CommonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PROJECT_DOCUMENT_RUBRIC_CATEGORY);
        _common.edit_dropDownWithInput_fromModal(commonLocators.CommonLabels.RUBRIC_CATEGORY, CONTAINERS_DATA_RECORDS.RUBRIC_CATEGORY, CommonLocators.CommonKeys.LIST);
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROJECT_DOCUMENTS)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_DOCUMENTS)
        _common.edit_containerCell(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, DOCUMENTS_PROJECT_DESC2)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(CommonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PROJECT_DOCUMENT_RUBRIC_CATEGORY);
        _common.edit_dropDownWithInput_fromModal(commonLocators.CommonLabels.RUBRIC_CATEGORY, CONTAINERS_DATA_RECORDS.RUBRIC_CATEGORY, CommonLocators.CommonKeys.LIST);
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })    

    it("TC - Select all record and change project document status using wizard at the same time", function () {
        _common.select_rowInSubContainer(cnt.uuid.INVOICEHEADER)
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_DOCUMENTS, app.FooterTab.DOCUMENTS_PROJECT, 1);
            _common.clear_subContainerFilter(cnt.uuid.PROJECT_DOCUMENTS)
        });
        _common.select_allContainerData(cnt.uuid.PROJECT_DOCUMENTS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PROJECT_DOCUMENTS_STATUS)
        _common.waitForLoaderToDisappear()
        _common.changeStatus_ofMultipleRecord_fromModal(commonLocators.CommonKeys.RECEIVED)
        _common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PROJECT_DOCUMENTS, DOCUMENTS_PROJECT_DESC1)
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.PRJ_DOCUMENT_STATUS_FK,commonLocators.CommonKeys.RECEIVED)
		_common.select_rowHasValue(cnt.uuid.PROJECT_DOCUMENTS, DOCUMENTS_PROJECT_DESC2)
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.PRJ_DOCUMENT_STATUS_FK,commonLocators.CommonKeys.RECEIVED)
    });

    it("TC - Select first record and change project document status using wizard", function () {
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_DOCUMENTS, app.FooterTab.DOCUMENTS_PROJECT, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_DOCUMENTS)
        _common.select_rowHasValue(cnt.uuid.PROJECT_DOCUMENTS, DOCUMENTS_PROJECT_DESC1)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PROJECT_DOCUMENTS_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.NEW, REMARK)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PROJECT_DOCUMENTS, DOCUMENTS_PROJECT_DESC1)
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.PRJ_DOCUMENT_STATUS_FK,commonLocators.CommonKeys.NEW)
    })

    it("TC - Add message to history by wizard", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_DOCUMENTS, app.FooterTab.DOCUMENTS_PROJECT, 1);
        });
        _common.select_rowHasValue(cnt.uuid.PROJECT_DOCUMENTS, DOCUMENTS_PROJECT_DESC1)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PROJECT_DOCUMENTS_STATUS)
        _common.clickOn_modalFooterButton(btn.ButtonText.HISTORY)
        _common.clickOn_cellHasValue_fromModal(app.GridCells.REMARK, REMARK)
        _common.assert_cellDataByContent_fromModal(app.GridCells.REMARK, REMARK)
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()   
    });

    it("TC - Change status ui, check the status filter", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_DOCUMENTS, app.FooterTab.DOCUMENTS_PROJECT, 1);
        });
        _common.select_rowHasValue(cnt.uuid.PROJECT_DOCUMENTS, DOCUMENTS_PROJECT_DESC1)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PROJECT_DOCUMENTS_STATUS)
        _common.clickOn_checkboxUnderModal_byClass(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonKeys.CHECK)
        //cy.wait(1000) // wait is required to validate the checkbox
        _validate.verify_recordNotPresentInmodal(commonLocators.CommonKeys.CANCELED)
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PROJECT_DOCUMENTS, DOCUMENTS_PROJECT_DESC1)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PROJECT_DOCUMENTS_STATUS)
        cy.wait(1000)// required wait for modal to appear
        _common.clickOn_checkboxUnderModal_byClass(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonKeys.UNCHECK)
        _validate.verify_recordPresentInmodal(commonLocators.CommonKeys.CANCELED)
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()  
    });
})