import { _common, _estimatePage, _validate, _mainView, _boqPage, _bidPage, _saleContractPage, _modalView, _salesPage, _wipPage, _package, _projectPage } from "cypress/pages";
import { app, tile, cnt, btn, sidebar, commonLocators } from "cypress/locators";
import { DataCells } from "cypress/pages/interfaces";
import CommonLocators from "cypress/locators/common-locators";
import Buttons from "cypress/locators/buttons";

const allure = Cypress.Allure.reporter.getInterface();
const ESTIMATE_CODE = "1" + Cypress._.random(0, 999);
const ESTIMATE_DESCRIPTION = "EST-DESC-" + Cypress._.random(0, 999);
const LINE_ITEM_DESCRIPTION = "LINE-ITEM-DESC-" + Cypress._.random(0, 999);
const PROJECT_NO = _common.generateRandomString(3)
const PROJECT_DESC = "PRDESC-" + Cypress._.random(0, 999);
const CLERK = "HS"

let ESTIMATE_PARAMETERS: DataCells,
  LINE_ITEM_PARAMETERS: DataCells,
  RESOURCE_PARAMETERS: DataCells,
  PROJECTS_PARAMETERS: DataCells,
  RESOURCE_PLANT_MASTER_PARAMETER: DataCells

let CONTAINERS_ESTIMATE,
  CONTAINERS_LINE_ITEM,
  CONTAINER_COLUMNS_ESTIMATE,
  CONTAINERS_RESOURCE,
  CONTAINER_COLUMNS_RESOURCE,
  CONTAINER_COLUMNS_LINE_ITEM,
  CONTAINERS_DATA_RECORDS,
  CONTAINERS_LOGISTIC_JOBS,
  CONTAINER_COLUMNS_LOGISTICS_JOBS,
  CONTAINER_COLUMNS_DATA_RECORDS
let UPDATE_ESTIMATE_PARAMETER: DataCells
let MODAL_UPDATE_ESTIMATE_WIZARD;


allure.epic("ESTIMATE");
allure.feature("Line Item");
allure.story("EST- 10.18 | If System option 10115 False Verify update estimate wizard to support updating plant assemblies");

describe("EST- 10.18 | If System option 10115 False Verify update estimate wizard to support updating plant assemblies", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  before(function () {
    cy.fixture("estimate/est-10.18-if-system-option-10115-false-verify-update-estimate-wizard-to-support-updating-plant-assemblies.json")
      .then((data) => {
        this.data = data;

        CONTAINERS_ESTIMATE = this.data.CONTAINERS.ESTIMATE;
        CONTAINER_COLUMNS_ESTIMATE = this.data.CONTAINER_COLUMNS.ESTIMATE;
        CONTAINERS_LINE_ITEM = this.data.CONTAINERS.LINE_ITEM;
        CONTAINER_COLUMNS_LINE_ITEM = this.data.CONTAINER_COLUMNS.LINE_ITEM;
        CONTAINERS_RESOURCE = this.data.CONTAINERS.RESOURCE;
        CONTAINER_COLUMNS_RESOURCE = this.data.CONTAINER_COLUMNS.RESOURCE;
        CONTAINERS_LOGISTIC_JOBS = this.data.CONTAINERS.LOGISTIC_JOBS;
        CONTAINER_COLUMNS_LOGISTICS_JOBS = this.data.CONTAINER_COLUMNS.LOGISTICS_JOBS;
        CONTAINER_COLUMNS_DATA_RECORDS = this.data.CONTAINER_COLUMNS.DATA_RECORDS;
        CONTAINERS_DATA_RECORDS = this.data.CONTAINERS.DATA_RECORDS
        PROJECTS_PARAMETERS = {
          [commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
          [commonLocators.CommonLabels.NAME]: PROJECT_DESC,
          [commonLocators.CommonLabels.CLERK]: CLERK
        }
        ESTIMATE_PARAMETERS = {
          [app.GridCells.CODE]: ESTIMATE_CODE,
          [app.GridCells.DESCRIPTION_INFO]: ESTIMATE_DESCRIPTION,
          [app.GridCells.RUBRIC_CATEGORY_FK]: CONTAINERS_ESTIMATE.RUBRIC_CATEGORY,
          [app.GridCells.EST_TYPE_FK]: CONTAINERS_ESTIMATE.ESTIMATE_TYPE,
        };
        LINE_ITEM_PARAMETERS = {
          [app.GridCells.DESCRIPTION_INFO]: LINE_ITEM_DESCRIPTION
        };
        RESOURCE_PARAMETERS = {
          [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY,
        }
        RESOURCE_PLANT_MASTER_PARAMETER = {
          [commonLocators.CommonKeys.CODE]: CONTAINERS_RESOURCE.CODE
        }
        MODAL_UPDATE_ESTIMATE_WIZARD = this.data.MODAL.UPDATE_ESTIMATE_WIZARD
        UPDATE_ESTIMATE_PARAMETER = {
            [commonLocators.CommonKeys.CHECKBOX]: MODAL_UPDATE_ESTIMATE_WIZARD
        }
      })
      .then(() => {
        cy.preLoading(Cypress.env("adminUserName"), Cypress.env("adminPassword"), Cypress.env("parentCompanyName"), Cypress.env("childCompanyName"));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear();
        _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.setDefaultView(app.TabBar.PROJECT)
          _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0)
        });
      });
  });

  it("TC - Precondition : system option 10115 should be false", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
    _common.waitForLoaderToDisappear()
    cy.wait(2000) //required wait to open sidebar
    _common.openTab(app.TabBar.MASTER_DATA).then(() => {
      _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES, 0);
    });
    _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
    _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CommonLocators.CommonLabels.SYSTEM_OPTION)
    _common.clickOn_cellHasUniqueValue(cnt.uuid.DATA_TYPES, app.GridCells.NAME, CommonLocators.CommonLabels.SYSTEM_OPTION)
    _common.openTab(app.TabBar.MASTER_DATA).then(() => {
      _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORD, 0);
      _common.setup_gridLayout(cnt.uuid.DATA_RECORDS, CONTAINER_COLUMNS_DATA_RECORDS);
    });
    _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
    _common.search_inSubContainer(cnt.uuid.DATA_RECORDS, CONTAINERS_DATA_RECORDS.SYSTEM_OPTION_10115)
    _common.waitForLoaderToDisappear()
    _common.select_rowHasValue(cnt.uuid.DATA_RECORDS, CONTAINERS_DATA_RECORDS.SYSTEM_OPTION_10115)
    _common.edit_containerCell(cnt.uuid.DATA_RECORDS, app.GridCells.PARAMETER_VALUE, app.InputFields.DOMAIN_TYPE_COMMENT, "false")
    cy.SAVE()
    _common.waitForLoaderToDisappear()
  });

  it("TC - Precondition :Create new project",function(){
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
    _common.openTab(app.TabBar.PROJECT).then(() => {
      _common.setDefaultView(app.TabBar.PROJECT)
      _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0)
    });
    _common.create_newRecord(cnt.uuid.PROJECTS)
    _projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
    _common.waitForLoaderToDisappear();
  })

  it("TC - Precondition : Logistic Job should have assigned with Price condition, Calender, Plant estimate price list fileds.", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
    _common.waitForLoaderToDisappear()
    cy.wait(1000)//required wait to open sidebar
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO);
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
      _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS);
      _common.clear_subContainerFilter(cnt.uuid.JOBS)
      _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_LOGISTICS_JOBS);
    });
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue(cnt.uuid.JOBS, PROJECT_NO)
      _common.waitForLoaderToDisappear()
      _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.PLANT_ESTIMATE_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, CONTAINERS_LOGISTIC_JOBS.PLANT_ESTIMATE_PRICELIST)
      _common.waitForLoaderToDisappear()
      cy.SAVE()
      _common.waitForLoaderToDisappear()
  });

 
  it("TC - Create new estimate", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
    _common.waitForLoaderToDisappear()
    cy.wait(2000) //required wait to open sidebar
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.setDefaultView(app.TabBar.ESTIMATE);
      _common.waitForLoaderToDisappear();
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE, CONTAINER_COLUMNS_ESTIMATE);
      _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
      _common.create_newRecord(cnt.uuid.ESTIMATE);
      _estimatePage.enterRecord_toCreateEstimate(cnt.uuid.ESTIMATE, ESTIMATE_PARAMETERS);
      _common.waitForLoaderToDisappear();
      cy.SAVE();
      _common.waitForLoaderToDisappear();
      _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
      _common.waitForLoaderToDisappear();
    });
  });

  it("TC - Create new line item with quantity", function () {
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.setDefaultView(app.TabBar.ESTIMATELINEITEM);
      _common.waitForLoaderToDisappear();
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS, CONTAINER_COLUMNS_LINE_ITEM);
      _common.maximizeContainer(cnt.uuid.ESTIMATE_LINEITEMS);
      _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
      _common.create_newRecord(cnt.uuid.ESTIMATE_LINEITEMS);
      _estimatePage.enterRecord_toCreateLineItem(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_PARAMETERS);
      _common.waitForLoaderToDisappear();
      cy.SAVE();
      _common.waitForLoaderToDisappear();
      _common.minimizeContainer(cnt.uuid.ESTIMATE_LINEITEMS);
    });
  });

  it("TC - Assign resource to the line item", function () {
    _common.waitForLoaderToDisappear();
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
      _common.setup_gridLayout(cnt.uuid.RESOURCES, CONTAINER_COLUMNS_RESOURCE);
      _common.set_columnAtTop([CONTAINER_COLUMNS_RESOURCE.quantitytotal,CONTAINER_COLUMNS_RESOURCE.costtotal,CONTAINER_COLUMNS_RESOURCE.quantityfactor1,CONTAINER_COLUMNS_RESOURCE.costfactor1,CONTAINER_COLUMNS_RESOURCE.estresourcetypeshortkey,CONTAINER_COLUMNS_RESOURCE.code,CONTAINER_COLUMNS_RESOURCE.costunit,CONTAINER_COLUMNS_RESOURCE.workoperationtypefk],cnt.uuid.RESOURCES)
      _common.maximizeContainer(cnt.uuid.RESOURCES);
      _common.clear_subContainerFilter(cnt.uuid.RESOURCES);
      _common.create_newRecord(cnt.uuid.RESOURCES);
      _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS);
      _common.clickOn_activeRowCell(cnt.uuid.RESOURCES, app.GridCells.CODE)
      _estimatePage.assign_plantGroup_fromLookUpModal(cnt.uuid.RESOURCES, RESOURCE_PLANT_MASTER_PARAMETER)
      _common.waitForLoaderToDisappear();
      cy.SAVE();
      _common.waitForLoaderToDisappear();
    });
   })
    it("TC -Set Is Manual checkbox uncheck ", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_ESTIMATION);
        _common.waitForLoaderToDisappear()
        cy.wait(2000) //required wait to open sidebar
        _common.openTab(app.TabBar.PLANT_ESTIMATE).then(() => {
          _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_1)
          _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0);
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PLANT)
        _common.search_inSubContainer(cnt.uuid.PLANT, CONTAINERS_RESOURCE.PLANT)
        _common.select_rowHasValue(cnt.uuid.PLANT, CONTAINERS_RESOURCE.PLANT)
    
        _common.openTab(app.TabBar.PLANT_ESTIMATE).then(() => {
          _common.select_tabFromFooter(cnt.uuid.PLANT_ESTIMATE_PRICELIST, app.FooterTab.PLANT_ESTIMATE_PRICE_LIST, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PLANT_ESTIMATE_PRICELIST)
        _common.search_inSubContainer(cnt.uuid.PLANT_ESTIMATE_PRICELIST, CONTAINERS_RESOURCE.PRICE_LIST)
        _common.select_rowHasValue(cnt.uuid.PLANT_ESTIMATE_PRICELIST, CONTAINERS_RESOURCE.PRICE_LIST)
    
        _common.openTab(app.TabBar.PLANT_ESTIMATE).then(() => {
          _common.select_tabFromFooter(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER, app.FooterTab.SPECIFIC_VALUES, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER)
        _common.search_inSubContainer(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER, CONTAINERS_RESOURCE.SPECIFIC_VALUE)
        _common.select_rowHasValue(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER, CONTAINERS_RESOURCE.SPECIFIC_VALUE)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.UNCHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
    })
   
    it("TC - Edit quantity of estimate assemblies resource and check quantity total-1", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
        cy.wait(1000)//required wait to open sidebar
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
        _common.waitForLoaderToDisappear();
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_3)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid. PROJECT_PLANT_ASSEMBLIES, app.FooterTab. EQUIPMENT_ASSEMBLY,2);
        })
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_PLANT_ASSEMBLIES)
        _common.search_inSubContainer(cnt.uuid.PROJECT_PLANT_ASSEMBLIES, CONTAINERS_RESOURCE.ASSEMBLIES)
        _common.select_rowHasValue(cnt.uuid.PROJECT_PLANT_ASSEMBLIES, CONTAINERS_RESOURCE.ASSEMBLIES)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.select_tabFromFooter(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, app.FooterTab.EQUIPMENT_ASSEMBLY_RESOURCES,2);
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES)
        _common.search_inSubContainer(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, CONTAINERS_RESOURCE.COST_CODE1)
        _common.select_rowHasValue(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, CONTAINERS_RESOURCE.COST_CODE1)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES,app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_RESOURCE.EDITED_QUANTITY[0])
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.getText_fromCell(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES,app.GridCells.QUANTITY_TOTAL).then(($ele1: JQuery<HTMLElement>) => {
            Cypress.env("QUANTITY_TOTAL1", $ele1.text().replace(",",""))
            cy.log(Cypress.env("QUANTITY_TOTAL1"))
        })
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES)
    })

    
    it("TC - Verify changed quantity total for Is Manual flag false", function () {
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.ESTIMATE).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATE);
            _common.waitForLoaderToDisappear();
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
        })
        _common.select_allContainerData(cnt.uuid.ESTIMATE)
        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
        _common.waitForLoaderToDisappear();
        cy.wait(1000)
        _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES);
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
        _common.waitForLoaderToDisappear()
        _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.clickOn_cellHasValue(cnt.uuid.RESOURCES,app.GridCells.CODE,CONTAINERS_RESOURCE.COST_CODE1)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE);
        _estimatePage.update_estimate_fromWizard(UPDATE_ESTIMATE_PARAMETER)
                             cy.wait(2000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.RESOURCES)
        cy.wait(2000)//required wait to load page
        _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
        _common.waitForLoaderToDisappear()
        _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.RESOURCES)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.COST_CODE1)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.RESOURCES,app.GridCells.QUANTITY_SMALL,CONTAINERS_RESOURCE.EDITED_QUANTITY[0])
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.RESOURCES,app.GridCells.QUANTITY_TOTAL,Cypress.env("QUANTITY_TOTAL1"))
        _common.waitForLoaderToDisappear()
    })
    
    it("TC -Set Is Rate Project checkbox check ", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.waitForLoaderToDisappear()
          _common.select_tabFromFooter(cnt.uuid.PROJECT_COST_CODES, app.FooterTab.COSTCODES);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_COST_CODES)
        _common.search_inSubContainer(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
        _common.select_rowHasValue(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.PROJECT_COST_CODES, app.GridCells.IS_RATE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })
    it("TC - Edit Rate(Project) of Cost code job rate-1", function () {
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
      _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.PROJECT).then(() => {
        _common.select_tabFromFooter(cnt.uuid.PROJECT_COST_CODES, app.FooterTab.COST_CODES);
      });
      _common.waitForLoaderToDisappear()
      _common.clear_subContainerFilter(cnt.uuid.PROJECT_COST_CODES)
      _common.waitForLoaderToDisappear()
      _common.search_inSubContainer(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.select_rowHasValue(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.waitForLoaderToDisappear()
          _common.select_tabFromFooter(cnt.uuid. COSTCODESJOBRATE, app.FooterTab. COSTCODES_JOB_RATES,2);
      })
      _common.waitForLoaderToDisappear()
      _common.select_allContainerData(cnt.uuid.COSTCODESJOBRATE)
      _common.select_rowHasValue(cnt.uuid.COSTCODESJOBRATE,PROJECT_NO)
      _common.waitForLoaderToDisappear()
      _common.edit_containerCell(cnt.uuid.COSTCODESJOBRATE,app.GridCells.RATE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_RESOURCE.EDITED_RATE[0])
      cy.SAVE()
      _common.waitForLoaderToDisappear()

    })

    it("TC -Update estimate from wizard-1", function () {
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.ESTIMATE).then(() => {
          _common.setDefaultView(app.TabBar.ESTIMATE);
          _common.waitForLoaderToDisappear();
          _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
      })
      _common.select_allContainerData(cnt.uuid.ESTIMATE)
      _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
      _common.waitForLoaderToDisappear();
      cy.wait(1000)
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES);
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
      _common.waitForLoaderToDisappear()
      _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
      _common.clickOn_cellHasValue(cnt.uuid.RESOURCES,app.GridCells.CODE,CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
      _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE);
      _estimatePage.update_estimate_fromWizard(UPDATE_ESTIMATE_PARAMETER)
      _common.clickOn_modalFooterButton(btn.ButtonText.OK)
      cy.wait(2000)//cy.wait(2000)//required wait to load page
      _common.clear_subContainerFilter(cnt.uuid.RESOURCES)
      _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
      _common.waitForLoaderToDisappear()
      _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
      _common.waitForLoaderToDisappear()
      _common.select_allContainerData(cnt.uuid.RESOURCES)
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.getText_fromCell(cnt.uuid.RESOURCES,app.GridCells.COST_UNIT).then(($ele1: JQuery<HTMLElement>) => {
        Cypress.env("COST_UNIT", $ele1.text().replace(",",""))
        cy.log(Cypress.env("COST_UNIT"))
      })
      _common.waitForLoaderToDisappear()
      _common.getText_fromCell(cnt.uuid.RESOURCES,app.GridCells.COST_FACTOR_1).then(($ele1: JQuery<HTMLElement>) => {
        Cypress.env("COST_FACTOR_1", $ele1.text().replace(",",""))
        cy.log(Cypress.env("COST_FACTOR_1"))
      })
      _common.waitForLoaderToDisappear()
      _common.getText_fromCell(cnt.uuid.RESOURCES,app.GridCells.QUANTITY_FACTOR_1).then(($ele1: JQuery<HTMLElement>) => {
        Cypress.env("QUANTITY_FACTOR_1", $ele1.text().replace(",",""))
        cy.log(Cypress.env("QUANTITY_FACTOR_1"))
      })
    })
    it("TC - Verify Cost per unit for Is Manual flag false and Is Rate flag True", function () {
      cy.wait(1000)// required to load value
      let COSTUNIT2=parseFloat(Cypress.env("COST_UNIT"))*parseFloat(Cypress.env("QUANTITY_FACTOR_1"))*parseFloat(Cypress.env("COST_FACTOR_1"))*parseFloat(CONTAINERS_RESOURCE.EDITED_QUANTITY[0])
       _common.waitForLoaderToDisappear()
       cy.wait(1000)// required to load value

     _common.assert_forNumericValues(cnt.uuid.RESOURCES,app.GridCells.COST_TOTAL, COSTUNIT2.toString())
     _common.waitForLoaderToDisappear()
     _common.assert_forNumericValues(cnt.uuid.RESOURCES,app.GridCells.COST_UNIT,CONTAINERS_RESOURCE.EDITED_RATE[0])
     _common.waitForLoaderToDisappear()
    })
    it("TC -Set Is Manual checkbox check ", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_ESTIMATION);
        _common.waitForLoaderToDisappear()
        cy.wait(2000) //required wait to open sidebar
        _common.openTab(app.TabBar.PLANT_ESTIMATE).then(() => {
          _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_1)
          _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PLANT)
        _common.search_inSubContainer(cnt.uuid.PLANT, CONTAINERS_RESOURCE.PLANT)
        _common.select_rowHasValue(cnt.uuid.PLANT, CONTAINERS_RESOURCE.PLANT)
    
        _common.openTab(app.TabBar.PLANT_ESTIMATE).then(() => {
          _common.select_tabFromFooter(cnt.uuid.PLANT_ESTIMATE_PRICELIST, app.FooterTab.PLANT_ESTIMATE_PRICE_LIST, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PLANT_ESTIMATE_PRICELIST)
        _common.search_inSubContainer(cnt.uuid.PLANT_ESTIMATE_PRICELIST, CONTAINERS_RESOURCE.PRICE_LIST)
        _common.select_rowHasValue(cnt.uuid.PLANT_ESTIMATE_PRICELIST, CONTAINERS_RESOURCE.PRICE_LIST)
    
        _common.openTab(app.TabBar.PLANT_ESTIMATE).then(() => {
          _common.select_tabFromFooter(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER, app.FooterTab.SPECIFIC_VALUES, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER)
        _common.search_inSubContainer(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER, CONTAINERS_RESOURCE.SPECIFIC_VALUE)
        _common.select_rowHasValue(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER, CONTAINERS_RESOURCE.SPECIFIC_VALUE)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.SPECIFIC_VALUE_PLANT_MASTER, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
    })
    
    it("TC - Edit quantity of estimate assemblies resource and check quantity total -2", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
        _common.waitForLoaderToDisappear();
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid. PROJECT_PLANT_ASSEMBLIES, app.FooterTab. EQUIPMENT_ASSEMBLY);
          });
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_PLANT_ASSEMBLIES)
        _common.maximizeContainer(cnt.uuid.PROJECT_PLANT_ASSEMBLIES)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.PROJECT_PLANT_ASSEMBLIES, CONTAINERS_RESOURCE.ASSEMBLIES)
        _common.select_rowHasValue(cnt.uuid.PROJECT_PLANT_ASSEMBLIES, CONTAINERS_RESOURCE.ASSEMBLIES)
        _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.waitForLoaderToDisappear()
          _common.select_tabFromFooter(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, app.FooterTab.EQUIPMENT_ASSEMBLY_RESOURCES);
        });
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES)
        cy.wait(2000)//required wait to load page
        _common.search_inSubContainer(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, CONTAINERS_RESOURCE.COST_CODE1)
        _common.select_rowHasValue(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, CONTAINERS_RESOURCE.COST_CODE1)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES,app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_RESOURCE.EDITED_QUANTITY[1])
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.getText_fromCell(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES,app.GridCells.QUANTITY_TOTAL).then(($ele1: JQuery<HTMLElement>) => {
            Cypress.env("QUANTITY_TOTAL3", $ele1.text().replace(",",""))
            cy.log(Cypress.env("QUANTITY_TOTAL3"))
        })
        _common.minimizeContainer(cnt.uuid.PROJECT_PLANT_ASSEMBLIES)
        _common.waitForLoaderToDisappear()
    })
    it("TC - Verify changed quantity total for Is Manual flag true", function () {
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.ESTIMATE).then(() => {
          _common.setDefaultView(app.TabBar.ESTIMATE);
          _common.waitForLoaderToDisappear();
          _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
      })
      _common.select_allContainerData(cnt.uuid.ESTIMATE)
      _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
      _common.waitForLoaderToDisappear();
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES);
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
      _common.waitForLoaderToDisappear()
      _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
      _common.clickOn_cellHasValue(cnt.uuid.RESOURCES,app.GridCells.CODE,CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
      _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE);
      _estimatePage.update_estimate_fromWizard(UPDATE_ESTIMATE_PARAMETER)
      _common.clickOn_modalFooterButton(btn.ButtonText.OK)
      _common.waitForLoaderToDisappear()
      _common.clear_subContainerFilter(cnt.uuid.RESOURCES)
      cy.wait(2000)//required wait to load page
      _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
      _common.waitForLoaderToDisappear()
      _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
      _common.waitForLoaderToDisappear()
      _common.select_allContainerData(cnt.uuid.RESOURCES)
        _common.waitForLoaderToDisappear()
      _common.select_rowHasValue(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.RESOURCES,app.GridCells.QUANTITY_SMALL,CONTAINERS_RESOURCE.EDITED_QUANTITY[1])
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.RESOURCES,app.GridCells.QUANTITY_TOTAL,Cypress.env("QUANTITY_TOTAL3"))
        _common.waitForLoaderToDisappear()
    })
    
    it("TC - Edit Rate(Project) of Cost code job rate-2", function () {
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
      _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.PROJECT).then(() => {
        cy.wait(1000)
        _common.select_tabFromFooter(cnt.uuid.PROJECT_COST_CODES, app.FooterTab.COST_CODES);
      });
      _common.waitForLoaderToDisappear()
      _common.clear_subContainerFilter(cnt.uuid.PROJECT_COST_CODES)
      _common.waitForLoaderToDisappear()
      _common.search_inSubContainer(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.select_rowHasValue(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.waitForLoaderToDisappear()
          _common.select_tabFromFooter(cnt.uuid. COSTCODESJOBRATE, app.FooterTab. COSTCODES_JOB_RATES,2);
      })
      _common.waitForLoaderToDisappear()
      _common.select_allContainerData(cnt.uuid.COSTCODESJOBRATE)
      _common.select_rowHasValue(cnt.uuid.COSTCODESJOBRATE,PROJECT_NO)
      _common.waitForLoaderToDisappear()
      _common.edit_containerCell(cnt.uuid.COSTCODESJOBRATE,app.GridCells.RATE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_RESOURCE.EDITED_RATE[1])
      cy.SAVE()
      _common.waitForLoaderToDisappear()

    })

    it("TC -Update estimate from wizard-2", function () {
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.ESTIMATE).then(() => {
          _common.setDefaultView(app.TabBar.ESTIMATE);
          _common.waitForLoaderToDisappear();
          _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
      })
      _common.select_allContainerData(cnt.uuid.ESTIMATE)
      _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
      _common.waitForLoaderToDisappear();
      cy.wait(1000)
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES);
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
      _common.waitForLoaderToDisappear()
      _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
      _common.clickOn_cellHasValue(cnt.uuid.RESOURCES,app.GridCells.CODE,CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
      _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE);
      _estimatePage.update_estimate_fromWizard(UPDATE_ESTIMATE_PARAMETER)
      _common.clickOn_modalFooterButton(btn.ButtonText.OK)
      cy.wait(2000)//required wait to load page
      _common.clear_subContainerFilter(cnt.uuid.RESOURCES)
      _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
      _common.waitForLoaderToDisappear()
      _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
      _common.waitForLoaderToDisappear()
      _common.select_allContainerData(cnt.uuid.RESOURCES)
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.getText_fromCell(cnt.uuid.RESOURCES,app.GridCells.COST_UNIT).then(($ele1: JQuery<HTMLElement>) => {
        Cypress.env("COST_UNIT", $ele1.text().replace(",",""))
        cy.log(Cypress.env("COST_UNIT"))
      })
      _common.waitForLoaderToDisappear()
      _common.getText_fromCell(cnt.uuid.RESOURCES,app.GridCells.COST_FACTOR_1).then(($ele1: JQuery<HTMLElement>) => {
        Cypress.env("COST_FACTOR_1", $ele1.text().replace(",",""))
        cy.log(Cypress.env("COST_FACTOR_1"))
      })
      _common.waitForLoaderToDisappear()
      _common.getText_fromCell(cnt.uuid.RESOURCES,app.GridCells.QUANTITY_FACTOR_1).then(($ele1: JQuery<HTMLElement>) => {
        Cypress.env("QUANTITY_FACTOR_1", $ele1.text().replace(",",""))
        cy.log(Cypress.env("QUANTITY_FACTOR_1"))
        _common.waitForLoaderToDisappear()
      })
    })

      it("TC - Verify Cost per unit for for Is Manual flag true and Is Rate flag true", function(){
      cy.wait(1000)//required to load value
      let COSTUNIT2=parseFloat(Cypress.env("COST_UNIT"))*parseFloat(Cypress.env("QUANTITY_FACTOR_1"))*parseFloat(Cypress.env("COST_FACTOR_1"))*parseFloat(CONTAINERS_RESOURCE.EDITED_QUANTITY[1])
       _common.waitForLoaderToDisappear()
       cy.wait(1000) //required to load value
      _common.assert_forNumericValues(cnt.uuid.RESOURCES,app.GridCells.COST_UNIT,CONTAINERS_RESOURCE.EDITED_RATE[1])
       _common.waitForLoaderToDisappear()
      _common.assert_forNumericValues(cnt.uuid.RESOURCES,app.GridCells.COST_TOTAL, COSTUNIT2.toString())
    })

    it("TC -Set Is Rate Project checkbox uncheck ", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.select_tabFromFooter(cnt.uuid.PROJECT_COST_CODES, app.FooterTab.COST_CODES, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_COST_CODES)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
        _common.select_rowHasValue(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.PROJECT_COST_CODES, app.GridCells.IS_RATE, commonLocators.CommonKeys.UNCHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Edit Rate(Project) of Cost code job rate-3", function () {
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
      _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.PROJECT).then(() => {
        _common.select_tabFromFooter(cnt.uuid.PROJECT_COST_CODES, app.FooterTab.COST_CODES);
      });
      _common.waitForLoaderToDisappear()
      _common.clear_subContainerFilter(cnt.uuid.PROJECT_COST_CODES)
      _common.waitForLoaderToDisappear()
      _common.search_inSubContainer(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.select_rowHasValue(cnt.uuid.PROJECT_COST_CODES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.waitForLoaderToDisappear()
          _common.select_tabFromFooter(cnt.uuid. COSTCODESJOBRATE, app.FooterTab. COSTCODES_JOB_RATES,2);
      })
      _common.waitForLoaderToDisappear()
      _common.select_allContainerData(cnt.uuid.COSTCODESJOBRATE)
      _common.select_rowHasValue(cnt.uuid.COSTCODESJOBRATE,PROJECT_NO)
      _common.waitForLoaderToDisappear()
      _common.edit_containerCell(cnt.uuid.COSTCODESJOBRATE,app.GridCells.RATE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_RESOURCE.EDITED_RATE[2])
      cy.SAVE()
      _common.waitForLoaderToDisappear()

    })

    it("TC - Verify Cost per unit for for Is Manual flag true and Is Rate flag flase", function () {
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.ESTIMATE).then(() => {
          _common.setDefaultView(app.TabBar.ESTIMATE);
          _common.waitForLoaderToDisappear();
          _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
      })
      _common.select_allContainerData(cnt.uuid.ESTIMATE)
      _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
      _common.waitForLoaderToDisappear();
      cy.wait(1000)
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES);
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
      _common.waitForLoaderToDisappear()
      _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
      _common.clickOn_cellHasValue(cnt.uuid.RESOURCES,app.GridCells.CODE,CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
      _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE);
      _estimatePage.update_estimate_fromWizard(UPDATE_ESTIMATE_PARAMETER)
      _common.clickOn_modalFooterButton(btn.ButtonText.OK)
      cy.wait(2000)//required wait to load page
      _common.clear_subContainerFilter(cnt.uuid.RESOURCES)
      _common.select_rowHasValue_onIndexBased(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.CODE ,0)
      _common.waitForLoaderToDisappear()
      _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
      _common.waitForLoaderToDisappear()
      _common.select_allContainerData(cnt.uuid.RESOURCES)
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue(cnt.uuid.RESOURCES, CONTAINERS_RESOURCE.COST_CODE1)
      _common.waitForLoaderToDisappear()
      _common.assert_forNumericValues_notEqualCondition(cnt.uuid.RESOURCES,app.GridCells.COST_UNIT,CONTAINERS_RESOURCE.EDITED_RATE[2])
       _common.waitForLoaderToDisappear()
    })
})

  after(() => {
    cy.LOGOUT();
  });