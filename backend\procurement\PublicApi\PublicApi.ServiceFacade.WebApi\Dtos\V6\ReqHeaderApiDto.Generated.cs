﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Procurement.PublicApi.BusinessComponents;

namespace RIB.Visual.Procurement.PublicApi.ServiceFacade.WebApi.V6
{


    /// <summary>
    /// There are no comments for ReqHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("REQ_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(6)]
    public partial class ReqHeaderApiDto : RIB.Visual.Platform.Core.ITypedDto<ReqHeaderApiEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class ReqHeaderApiDto.
        /// </summary>
        public ReqHeaderApiDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class ReqHeaderApiDto.
        /// </summary>
        /// <param name="entity">the instance of class ReqHeaderApiEntity</param>
        public ReqHeaderApiDto(ReqHeaderApiEntity entity)
        {
            Id = entity.Id;
            ReqStatusId = entity.ReqStatusId;
            ReqStatusDescription = entity.ReqStatusDescription;
            CompanyId = entity.CompanyId;
            CompanyCode = entity.CompanyCode;
            ProjectId = entity.ProjectId;
            ProjectCode = entity.ProjectCode;
            ProjectDescription = entity.ProjectDescription;
            PackageId = entity.PackageId;
            PackageCode = entity.PackageCode;
            PackageDescription = entity.PackageDescription;
            TaxCodeId = entity.TaxCodeId;
            TaxCodeCode = entity.TaxCodeCode;
            TaxCodeDescription = entity.TaxCodeDescription;
            ClerkPrcId = entity.ClerkPrcId;
            ClerkPrcCode = entity.ClerkPrcCode;
            ClerkPrcDescription = entity.ClerkPrcDescription;
            ClerkReqId = entity.ClerkReqId;
            ClerkReqCode = entity.ClerkReqCode;
            ClerkReqDescription = entity.ClerkReqDescription;
            CurrencyId = entity.CurrencyId;
            CurrencyDescription = entity.CurrencyDescription;
            Exchangerate = entity.Exchangerate;
            PrjChangeId = entity.PrjChangeId;
            PrjChangeCode = entity.PrjChangeCode;
            PrjChangeDescription = entity.PrjChangeDescription;
            ReqHeaderId = entity.ReqHeaderId;
            ReqHeaderCode = entity.ReqHeaderCode;
            ReqHeaderDescription = entity.ReqHeaderDescription;
            Haschanges = entity.Haschanges;
            MaterialCatalogId = entity.MaterialCatalogId;
            MaterialCatalogCode = entity.MaterialCatalogCode;
            MaterialCatalogDescription = entity.MaterialCatalogDescription;
            PrcHeaderId = entity.PrcHeaderId;
            PrcConfigurationId = entity.PrcConfigurationId;
            Code = entity.Code;
            Description = entity.Description;
            SearchPattern = entity.SearchPattern;
            DateReceived = entity.DateReceived;
            DateCanceled = entity.DateCanceled;
            DateRequired = entity.DateRequired;
            ReqTypeId = entity.ReqTypeId;
            ReqTypeDescription = entity.ReqTypeDescription;
            MdcControllingunitId = entity.MdcControllingunitId;
            MdcControllingunitCode = entity.MdcControllingunitCode;
            MdcControllingunitDescription = entity.MdcControllingunitDescription;
            BusinesspartnerId = entity.BusinesspartnerId;
            BusinesspartnerDescription = entity.BusinesspartnerDescription;
            SubsidiaryId = entity.SubsidiaryId;
            SubsidiaryDescription = entity.SubsidiaryDescription;
            SupplierId = entity.SupplierId;
            SupplierCode = entity.SupplierCode;
            SupplierDescription = entity.SupplierDescription;
            PrcIncotermId = entity.PrcIncotermId;
            PrcIncotermDescription = entity.PrcIncotermDescription;
            AddressId = entity.AddressId;
            AddressDescription = entity.AddressDescription;
            Remark = entity.Remark;
            PaymentTermPaId = entity.PaymentTermPaId;
            PaymentTermPaCode = entity.PaymentTermPaCode;
            PaymentTermPaDescription = entity.PaymentTermPaDescription;
            PaymentTermFiId = entity.PaymentTermFiId;
            PaymentTermFiCode = entity.PaymentTermFiCode;
            PaymentTermFiDescription = entity.PaymentTermFiDescription;
            PrcContracttypeId = entity.PrcContracttypeId;
            PrcContracttypeDescription = entity.PrcContracttypeDescription;
            PrcAwardmethodId = entity.PrcAwardmethodId;
            PrcAwardmethodDescription = entity.PrcAwardmethodDescription;
            PrcPackage2headerId = entity.PrcPackage2headerId;
            PrcPackage2headerDescription = entity.PrcPackage2headerDescription;
            UserDefined1 = entity.UserDefined1;
            UserDefined2 = entity.UserDefined2;
            UserDefined3 = entity.UserDefined3;
            UserDefined4 = entity.UserDefined4;
            UserDefined5 = entity.UserDefined5;
            OverallDiscount = entity.OverallDiscount;
            OverallDiscountOc = entity.OverallDiscountOc;
            OverallDiscountPercent = entity.OverallDiscountPercent;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            PaymentTermAdId = entity.PaymentTermAdId;
            PaymentTermAdCode = entity.PaymentTermAdCode;
            PaymentTermAdDescription = entity.PaymentTermAdDescription;
            DateEffective = entity.DateEffective;
            VatGroupId = entity.VatGroupId;
            VatGroupDescription = entity.VatGroupDescription;
            SalesTaxMethodId = entity.SalesTaxMethodId;
            SalesTaxMethodDesc = entity.SalesTaxMethodDesc;
            DeadlineDate = entity.DeadlineDate;
            DeadlineTime = entity.DeadlineTime;
            DateDelivery = entity.DateDelivery;
            BoqWicCatBoqId = entity.BoqWicCatBoqId;
            BoqWicCatBoqReference = entity.BoqWicCatBoqReference;
            BoqWicCatId = entity.BoqWicCatId;
            BoqWicCatCode = entity.BoqWicCatCode;
            BoqWicCatDescription = entity.BoqWicCatDescription;
            LanguageId = entity.LanguageId;
            DatePriceFixing = entity.DatePriceFixing;
            PlannedStart = entity.PlannedStart;
            PlannedEnd = entity.PlannedEnd;
            DateAwardDeadline = entity.DateAwardDeadline;
            DateRequested = entity.DateRequested;
            BasLanguageFk = entity.BasLanguageFk;
            BasLanguageDesc = entity.BasLanguageDesc;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for ReqStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReqStatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ReqStatusId { get; set; }
    
        /// <summary>
        /// There are no comments for ReqStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ReqStatusDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyId { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string CompanyCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public int? ProjectId { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ProjectCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ProjectDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PackageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PackageFk")]
        public int? PackageId { get; set; }
    
        /// <summary>
        /// There are no comments for PackageCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PackageCode { get; set; }
    
        /// <summary>
        /// There are no comments for PackageDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_DESC", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PackageDescription { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TaxCodeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int TaxCodeId { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_CODE", TypeName = "nvarchar(16)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string TaxCodeCode { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_DESC", TypeName = "nvarchar(2000)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string TaxCodeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public int? ClerkPrcId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkPrcCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkPrcDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 17)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public int? ClerkReqId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 18)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkReqCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 19)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkReqDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasCurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CurrencyId { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string CurrencyDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Exchangerate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 22)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal Exchangerate { get; set; }
    
        /// <summary>
        /// There are no comments for PrjChangeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_ID", TypeName = "int", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectChangeFk")]
        public int? PrjChangeId { get; set; }
    
        /// <summary>
        /// There are no comments for PrjChangeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_CODE", TypeName = "nvarchar(16)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PrjChangeCode { get; set; }
    
        /// <summary>
        /// There are no comments for PrjChangeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_DESC", TypeName = "nvarchar(252)", Order = 25)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PrjChangeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ReqHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_HEADER_ID", TypeName = "int", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReqHeaderFk")]
        public int? ReqHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for ReqHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_HEADER_CODE", TypeName = "nvarchar(16)", Order = 27)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ReqHeaderCode { get; set; }
    
        /// <summary>
        /// There are no comments for ReqHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_HEADER_DESC", TypeName = "nvarchar(252)", Order = 28)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ReqHeaderDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Haschanges in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("HASCHANGES", TypeName = "bit", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("HasChanges")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Haschanges { get; set; }
    
        /// <summary>
        /// There are no comments for MaterialCatalogId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_ID", TypeName = "int", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("MaterialCatalogFk")]
        public int? MaterialCatalogId { get; set; }
    
        /// <summary>
        /// There are no comments for MaterialCatalogCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_CODE", TypeName = "nvarchar(16)", Order = 31)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string MaterialCatalogCode { get; set; }
    
        /// <summary>
        /// There are no comments for MaterialCatalogDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_DESC", TypeName = "nvarchar(2000)", Order = 32)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MaterialCatalogDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_HEADER_ID", TypeName = "int", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcHeaderFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcConfigurationId { get; set; }
    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 35)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 36)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }
    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 37)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public string SearchPattern { get; set; }
    
        /// <summary>
        /// There are no comments for DateReceived in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_RECEIVED", TypeName = "date", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateReceived")]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateReceived { get; set; }
    
        /// <summary>
        /// There are no comments for DateCanceled in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CANCELED", TypeName = "date", Order = 39)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCanceled")]
        public System.DateTime? DateCanceled { get; set; }
    
        /// <summary>
        /// There are no comments for DateRequired in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REQUIRED", TypeName = "date", Order = 40)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateRequired")]
        public System.DateTime? DateRequired { get; set; }
    
        /// <summary>
        /// There are no comments for ReqTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_TYPE_ID", TypeName = "int", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReqTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ReqTypeId { get; set; }
    
        /// <summary>
        /// There are no comments for ReqTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 42)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ReqTypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingUnitFk")]
        public int? MdcControllingunitId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(32)", Order = 44)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string MdcControllingunitCode { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 45)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcControllingunitDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        public int? BusinesspartnerId { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 47)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BusinesspartnerDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public int? SubsidiaryId { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 49)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SubsidiaryDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public int? SupplierId { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 51)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierCode { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 52)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcIncotermId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_ID", TypeName = "int", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IncotermFk")]
        public int? PrcIncotermId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcIncotermDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_DESC", TypeName = "nvarchar(2000)", Order = 54)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcIncotermDescription { get; set; }
    
        /// <summary>
        /// There are no comments for AddressId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_ID", TypeName = "int", Order = 55)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AddressFk")]
        public int? AddressId { get; set; }
    
        /// <summary>
        /// There are no comments for AddressDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_DESC", TypeName = "nvarchar(2000)", Order = 56)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string AddressDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasPaymentTermPaFk")]
        public int? PaymentTermPaId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_CODE", TypeName = "nvarchar(16)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermPaCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_DESC", TypeName = "nvarchar(2000)", Order = 60)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermPaDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_ID", TypeName = "int", Order = 61)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasPaymentTermFiFk")]
        public int? PaymentTermFiId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_CODE", TypeName = "nvarchar(16)", Order = 62)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermFiCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_DESC", TypeName = "nvarchar(2000)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermFiDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcContracttypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_ID", TypeName = "int", Order = 64)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcContracttypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcContracttypeId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcContracttypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_DESC", TypeName = "nvarchar(2000)", Order = 65)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcContracttypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcAwardmethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_ID", TypeName = "int", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcAwardmethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcAwardmethodId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcAwardmethodDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_DESC", TypeName = "nvarchar(2000)", Order = 67)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcAwardmethodDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackage2headerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_ID", TypeName = "int", Order = 68)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Package2HeaderFk")]
        public int? PrcPackage2headerId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackage2headerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_DESC", TypeName = "nvarchar(252)", Order = 69)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PrcPackage2headerDescription { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 70)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined1 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 71)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined2 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 72)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined3 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 73)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined4 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 74)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined5 { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT", TypeName = "numeric(19,7)", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscount { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscountOc { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscountPercent in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_PERCENT", TypeName = "numeric(9,3)", Order = 77)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscountPercent { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InsertedAt")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 79)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InsertedBy")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 80)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UpdatedAt")]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 81)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UpdatedBy")]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 82)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Version")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_ID", TypeName = "int", Order = 83)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasPaymentTermAdFk")]
        public int? PaymentTermAdId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_CODE", TypeName = "nvarchar(16)", Order = 84)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermAdCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_DESC", TypeName = "nvarchar(2000)", Order = 85)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermAdDescription { get; set; }
    
        /// <summary>
        /// There are no comments for DateEffective in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_EFFECTIVE", TypeName = "date", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateEffective")]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateEffective { get; set; }
    
        /// <summary>
        /// There are no comments for VatGroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 87)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public int? VatGroupId { get; set; }
    
        /// <summary>
        /// There are no comments for VatGroupDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 88)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string VatGroupDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int SalesTaxMethodId { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 90)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string SalesTaxMethodDesc { get; set; }
    
        /// <summary>
        /// There are no comments for DeadlineDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DEADLINE_DATE", TypeName = "date", Order = 91)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? DeadlineDate { get; set; }
    
        /// <summary>
        /// There are no comments for DeadlineTime in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DEADLINE_TIME", TypeName = "time", Order = 97)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public global::System.TimeSpan? DeadlineTime { get; set; }
    
        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 98)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? DateDelivery { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatBoqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_BOQ_ID", TypeName = "int", Order = 92)]
        public int? BoqWicCatBoqId { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatBoqReference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_BOQ_REFERENCE", TypeName = "nvarchar(252)", Order = 94)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BoqWicCatBoqReference { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_ID", TypeName = "int", Order = 93)]
        public int? BoqWicCatId { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_CODE", TypeName = "nvarchar(16)", Order = 95)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string BoqWicCatCode { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_DESC", TypeName = "nvarchar(2000)", Order = 96)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BoqWicCatDescription { get; set; }
    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 99)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int LanguageId { get; set; }
    
        /// <summary>
        /// There are no comments for DatePriceFixing in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_PRICEFIXING", TypeName = "date", Order = 100)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? DatePriceFixing { get; set; }
    
        /// <summary>
        /// There are no comments for PlannedStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_START", TypeName = "date", Order = 101)]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedStart")]
        public System.DateTime? PlannedStart { get; set; }
    
        /// <summary>
        /// There are no comments for PlannedEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_END", TypeName = "date", Order = 102)]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedEnd")]
        public System.DateTime? PlannedEnd { get; set; }
    
        /// <summary>
        /// There are no comments for DateAwardDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_AWARDDEADLINE", TypeName = "date", Order = 103)]
        [RIB.Visual.Platform.Common.InternalApiField("DateAwardDeadline")]
        public System.DateTime? DateAwardDeadline { get; set; }
    
        /// <summary>
        /// There are no comments for DateRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REQUESTED", TypeName = "date", Order = 104)]
        [RIB.Visual.Platform.Common.InternalApiField("DateRequested")]
        public System.DateTime? DateRequested { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 105)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public int? BasLanguageFk { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 106)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BasLanguageDesc { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(ReqHeaderApiEntity); }
        }

        /// <summary>
        /// Copy the current ReqHeaderApiDto instance to a new ReqHeaderApiEntity instance.
        /// </summary>
        /// <returns>a new instance of class ReqHeaderApiEntity</returns>
        public ReqHeaderApiEntity Copy()
        {
          var entity = new ReqHeaderApiEntity();

          entity.Id = this.Id;
          entity.ReqStatusId = this.ReqStatusId;
          entity.ReqStatusDescription = this.ReqStatusDescription;
          entity.CompanyId = this.CompanyId;
          entity.CompanyCode = this.CompanyCode;
          entity.ProjectId = this.ProjectId;
          entity.ProjectCode = this.ProjectCode;
          entity.ProjectDescription = this.ProjectDescription;
          entity.PackageId = this.PackageId;
          entity.PackageCode = this.PackageCode;
          entity.PackageDescription = this.PackageDescription;
          entity.TaxCodeId = this.TaxCodeId;
          entity.TaxCodeCode = this.TaxCodeCode;
          entity.TaxCodeDescription = this.TaxCodeDescription;
          entity.ClerkPrcId = this.ClerkPrcId;
          entity.ClerkPrcCode = this.ClerkPrcCode;
          entity.ClerkPrcDescription = this.ClerkPrcDescription;
          entity.ClerkReqId = this.ClerkReqId;
          entity.ClerkReqCode = this.ClerkReqCode;
          entity.ClerkReqDescription = this.ClerkReqDescription;
          entity.CurrencyId = this.CurrencyId;
          entity.CurrencyDescription = this.CurrencyDescription;
          entity.Exchangerate = this.Exchangerate;
          entity.PrjChangeId = this.PrjChangeId;
          entity.PrjChangeCode = this.PrjChangeCode;
          entity.PrjChangeDescription = this.PrjChangeDescription;
          entity.ReqHeaderId = this.ReqHeaderId;
          entity.ReqHeaderCode = this.ReqHeaderCode;
          entity.ReqHeaderDescription = this.ReqHeaderDescription;
          entity.Haschanges = this.Haschanges;
          entity.MaterialCatalogId = this.MaterialCatalogId;
          entity.MaterialCatalogCode = this.MaterialCatalogCode;
          entity.MaterialCatalogDescription = this.MaterialCatalogDescription;
          entity.PrcHeaderId = this.PrcHeaderId;
          entity.PrcConfigurationId = this.PrcConfigurationId;
          entity.Code = this.Code;
          entity.Description = this.Description;
          entity.SearchPattern = this.SearchPattern;
          entity.DateReceived = this.DateReceived;
          entity.DateCanceled = this.DateCanceled;
          entity.DateRequired = this.DateRequired;
          entity.ReqTypeId = this.ReqTypeId;
          entity.ReqTypeDescription = this.ReqTypeDescription;
          entity.MdcControllingunitId = this.MdcControllingunitId;
          entity.MdcControllingunitCode = this.MdcControllingunitCode;
          entity.MdcControllingunitDescription = this.MdcControllingunitDescription;
          entity.BusinesspartnerId = this.BusinesspartnerId;
          entity.BusinesspartnerDescription = this.BusinesspartnerDescription;
          entity.SubsidiaryId = this.SubsidiaryId;
          entity.SubsidiaryDescription = this.SubsidiaryDescription;
          entity.SupplierId = this.SupplierId;
          entity.SupplierCode = this.SupplierCode;
          entity.SupplierDescription = this.SupplierDescription;
          entity.PrcIncotermId = this.PrcIncotermId;
          entity.PrcIncotermDescription = this.PrcIncotermDescription;
          entity.AddressId = this.AddressId;
          entity.AddressDescription = this.AddressDescription;
          entity.Remark = this.Remark;
          entity.PaymentTermPaId = this.PaymentTermPaId;
          entity.PaymentTermPaCode = this.PaymentTermPaCode;
          entity.PaymentTermPaDescription = this.PaymentTermPaDescription;
          entity.PaymentTermFiId = this.PaymentTermFiId;
          entity.PaymentTermFiCode = this.PaymentTermFiCode;
          entity.PaymentTermFiDescription = this.PaymentTermFiDescription;
          entity.PrcContracttypeId = this.PrcContracttypeId;
          entity.PrcContracttypeDescription = this.PrcContracttypeDescription;
          entity.PrcAwardmethodId = this.PrcAwardmethodId;
          entity.PrcAwardmethodDescription = this.PrcAwardmethodDescription;
          entity.PrcPackage2headerId = this.PrcPackage2headerId;
          entity.PrcPackage2headerDescription = this.PrcPackage2headerDescription;
          entity.UserDefined1 = this.UserDefined1;
          entity.UserDefined2 = this.UserDefined2;
          entity.UserDefined3 = this.UserDefined3;
          entity.UserDefined4 = this.UserDefined4;
          entity.UserDefined5 = this.UserDefined5;
          entity.OverallDiscount = this.OverallDiscount;
          entity.OverallDiscountOc = this.OverallDiscountOc;
          entity.OverallDiscountPercent = this.OverallDiscountPercent;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;
          entity.PaymentTermAdId = this.PaymentTermAdId;
          entity.PaymentTermAdCode = this.PaymentTermAdCode;
          entity.PaymentTermAdDescription = this.PaymentTermAdDescription;
          entity.DateEffective = this.DateEffective;
          entity.VatGroupId = this.VatGroupId;
          entity.VatGroupDescription = this.VatGroupDescription;
          entity.SalesTaxMethodId = this.SalesTaxMethodId;
          entity.SalesTaxMethodDesc = this.SalesTaxMethodDesc;
          entity.DeadlineDate = this.DeadlineDate;
          entity.DeadlineTime = this.DeadlineTime;
          entity.DateDelivery = this.DateDelivery;
          entity.BoqWicCatBoqId = this.BoqWicCatBoqId;
          entity.BoqWicCatBoqReference = this.BoqWicCatBoqReference;
          entity.BoqWicCatId = this.BoqWicCatId;
          entity.BoqWicCatCode = this.BoqWicCatCode;
          entity.BoqWicCatDescription = this.BoqWicCatDescription;
          entity.LanguageId = this.LanguageId;
          entity.DatePriceFixing = this.DatePriceFixing;
          entity.PlannedStart = this.PlannedStart;
          entity.PlannedEnd = this.PlannedEnd;
          entity.DateAwardDeadline = this.DateAwardDeadline;
          entity.DateRequested = this.DateRequested;
          entity.BasLanguageFk = this.BasLanguageFk;
          entity.BasLanguageDesc = this.BasLanguageDesc;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(ReqHeaderApiEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(ReqHeaderApiEntity entity);
    }

}
