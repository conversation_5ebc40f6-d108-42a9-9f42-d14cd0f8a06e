using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Net.Http;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Microsoft.Graph;
using Microsoft.Graph.TermStore;
using Microsoft.SharePoint.Client;
using Microsoft.SharePoint.Client.DocumentManagement;
using Microsoft.SharePoint.Client.Taxonomy;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PnP.Core.Model.SharePoint;
using PnP.Core.QueryModel;
using PnP.Core.Services;
using PnP.Framework;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Common.Core.Final;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Basics.LookupData.Core;
using RIB.Visual.Documents.Project.BusinessComponents.Constant;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Services.Scheduler.BusinessComponents;
using RIB.Visual.Services.Scheduler.Core;
using Site = Microsoft.Graph.Site;

#nullable enable

namespace RIB.Visual.Documents.Project.BusinessComponents
{
	/// <summary>
	/// Handles logic related to document sharing in SharePoint. Manages operations and interactions for document sharing
	/// functionality.
	/// </summary>
	public partial class DocumentSharePointLogic
	{
		private readonly Lazy<JobLogic> _frmJobLogic = new Lazy<JobLogic>(() => new JobLogic());
		private readonly Lazy<IBusinessPartner2ExternalLogic> Bp2ExtLogic = new(() => Injector.Get<IBusinessPartner2ExternalLogic>());
		private readonly Lazy<IControllingunit2ExtLogic> ControllingUnit2ExtLogic = new(() => Injector.Get<IControllingunit2ExtLogic>());

		/// <summary>
		/// Initializes and returns a GraphServiceClient instance configured for Azure Active Directory access.
		/// </summary>
		/// <returns>Returns a GraphServiceClient object for interacting with Microsoft Graph API.</returns>
		public GraphServiceClient GraphClientWithOwner()
		{
			Office365ServerConfigReader office365ServerConfigReader = new Office365ServerConfigReader();
			OfficeAuthParams authParams = office365ServerConfigReader.ReadServerAuthParamsFromConfig(ExternalSourceType.SharePoint);
			//authParams.AuthApp.ClientSecret = authParams.Password;
			var refreshToken = Platform.Server.Common.AppSettingsReader.ReadString("MSsharepoint:RefreshToken", "");
			authParams.RefreshToken = refreshToken;
			if (string.IsNullOrWhiteSpace(refreshToken))
			{
				//authParams.AuthType = OfficeAuthType.;
			}
			else
			{
				authParams.AuthType = OfficeAuthType.RefreshToken;
				authParams.RefreshToken = refreshToken;
				authParams.UserName = "RefreshToken";
			}
			if (!string.IsNullOrWhiteSpace(authParams.AuthApp.RibTermgroup))
			{
				this.RibGlobalTermGroup = authParams.AuthApp.RibTermgroup;
			}

			this.OfficeAuthParams = authParams;
			GraphClient = AzureActiveDirectoryManagement.Instance.GetGraphClient(authParams, "v1.0", requestNewToken: true, useScopeForRequest: true);
			return GraphClient;
		}

		/// <summary>
		/// status for SharePoint synced documents
		/// </summary>
		public static readonly int SharePointSyncedStatus = 16;

		/// <summary>
		/// Initializes a PnPContext for a given SharePoint site using the Microsoft Graph API.
		/// </summary>
		/// <param name="site"></param>
		/// <returns></returns>
		/// <exception cref="InvalidOperationException"></exception>
		public PnPContext InitPnpClient(Microsoft.Graph.Site site)
		{
			var scopeUrl = site.WebUrl.Replace($"sites/{site.Name}", ".default");

			var aad = AzureActiveDirectoryManagement.Instance;

			AADAccessor.Authority(aad);

			using var httpClient = new HttpClient();

			var refreshToken = Platform.Server.Common.AppSettingsReader.ReadString("MSsharepoint:RefreshToken", "");

			var dict = new Dictionary<string, string>
			{
				{ "client_id", OfficeAuthParams.AuthApp.ClientId },
				//{ "grant_type", "password"},
				//{ "username", OfficeAuthParams.UserName },
				//{ "password", OfficeAuthParams.Password },
				{ "client_secret", OfficeAuthParams.AuthApp.ClientSecret },

				//{ "grant_type", "client_credentials"},
				{ "grant_type", "refresh_token" },
				{ "refresh_token", refreshToken },
				{ "scope", scopeUrl },
			};
			if (string.IsNullOrWhiteSpace(refreshToken))
			{
				dict["grant_type"] = "password";
				dict["username"] = OfficeAuthParams.UserName;
				dict["password"] = OfficeAuthParams.Password;
			}
			var httpContent = new FormUrlEncodedContent(dict);
			var res = httpClient.PostAsync($"{AADAccessor.Authority(aad)}/{OfficeAuthParams.AuthApp.TenantId}/oauth2/v2.0/token", httpContent).GetAwaiter().GetResult();
			var str = res.Content.ReadAsStringAsync().GetAwaiter().GetResult();

			var authResponse = JsonConvert.DeserializeObject<AuthResponse>(str);

			using var context = new ClientContext(site.WebUrl);

			if (authResponse == null || string.IsNullOrEmpty(authResponse.AccessToken))
			{
				throw new InvalidOperationException("Failed to obtain access token for SharePoint.");
			}

			context.ExecutingWebRequest += (sender, e) =>
			{
				// Insert the access token in the request
				e.WebRequestExecutor.RequestHeaders["Authorization"] = "Bearer " + authResponse.AccessToken;
			};

			return PnPCoreSdk.Instance.GetPnPContextAsync(context).GetAwaiter().GetResult();
		}

		/// <summary>
		/// Initializes the SharePoint profile for document sharing, setting up the necessary configurations and properties.
		/// </summary>
		public void InitSharePointProfile()
		{
			// TODO
			_sharePointFolderProfile = new SharePointFolderProfile();
		}

		private SharePointFolderProfile _sharePointFolderProfile = new();

		/// <summary>
		///
		/// </summary>
		public const string RibId = "RibId";

		/// <summary>
		///
		/// </summary>
		public const string RibName = "RibName";

		/// <summary>
		///
		/// </summary>
		public virtual string RibGlobalTermGroup { get; set; } = "RIB40";

		/// <summary>
		///
		/// </summary>
		// TODO: get from sharepoint default language
		public const string LanguageTag = "en-US";

		/// <summary>
		///
		/// </summary>
		public Lazy<IBusinessPartnerLookupDataProvider> BusinessPartnerLogic { get; set; } = new Lazy<IBusinessPartnerLookupDataProvider>(() => Injector.Get<IBusinessPartnerLookupDataProvider>());

		/// <summary>
		///
		/// </summary>
		public Lazy<IReadOnlyLookupLogic<RubricCategoryEntity>> RubricCategoryLogic { get; set; } = new Lazy<IReadOnlyLookupLogic<RubricCategoryEntity>>(() => new RubricCategoryLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<IEntityProvider> ProjectDocumentCategoryLogic { get; set; } = new Lazy<IEntityProvider>(() => new BasicsCustomizeProjectDocumentCategoryLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<IReadOnlyLookupLogic<DocumentTypeEntity>> DocumentTypeLogic { get; set; } = new Lazy<IReadOnlyLookupLogic<DocumentTypeEntity>>(() => new DocumentTypeLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<IPrcStructureLogic> ProcurementStructureLogic { get; set; } = new Lazy<IPrcStructureLogic>(() => Injector.Get<IPrcStructureLogic>());

		/// <summary>
		///
		/// </summary>
		public Lazy<IControllingUnitLogic> ControllingUnitLogic { get; set; } = new Lazy<IControllingUnitLogic>(() => (IControllingUnitLogic)BusinessApplication.BusinessEnvironment.GetExportedValue<IChangeStatus>("controllingunit"));

		/// <summary>
		///
		/// </summary>
		public Lazy<IDocSpCommonLogic<DocMetadata2extEntity>> DocMetadata2extLogic { get; set; } = new Lazy<IDocSpCommonLogic<DocMetadata2extEntity>>(() => new DocMetadata2extEntityLogic());

		/// <summary>
		///
		/// </summary>
		public Lazy<DocumentLogic> DocumentLogic { get; set; } = new Lazy<DocumentLogic>(() => new DocumentLogic());

		/// <summary>
		/// 
		/// </summary>
		private readonly Lazy<FileArchiveDoc2ExternalLogic> FileArchive2ExtLogic = new(() => new FileArchiveDoc2ExternalLogic());

		/// <summary>
		/// 
		/// </summary>
		public Lazy<DocumentRevisionLogic> DocumentRevisionLogic { get; set; } = new Lazy<DocumentRevisionLogic>(() => new DocumentRevisionLogic());
		/// <summary>
		/// 
		/// </summary>
		public Lazy<DocumentHistoryLogic> DocumentHistoryLogic { get; set; } = new Lazy<DocumentHistoryLogic>(() => new DocumentHistoryLogic());

		/// <summary>
		/// Represents a client for interacting with Microsoft Graph services. It is accessible only within the class.
		/// </summary>
		public GraphServiceClient GraphClient { get; protected set; } = AzureActiveDirectoryManagement.Instance.GetGraphClient(new OfficeAuthParams { AuthType = Basics.Common.Core.OfficeAuthType.ClientSecret }, "v1.0");

		/// <summary>
		/// 
		/// </summary>
		public OfficeAuthParams OfficeAuthParams { get; set; }

		/// <summary>
		/// Initiates an automatic synchronization of documents to SharePoint for a specific company/project.
		/// Split via comma ','
		/// </summary>
		/// <returns>jobIds</returns>
		public List<int> Sync(string? companyCodes = null, string? projectIds = null, string? documentIds = null, SharePointItemProfile? itemProfile = null, string? accessToken = null, bool isAutoSync = true)
		{
			var param = new List<ParameterEntity>
			{
				new() { Name = "CompanyCodes", Type = typeof(String).FullName, Value = companyCodes },
				new() { Name = "ProjectIds", Type = typeof(String).FullName, Value = projectIds },
				new() { Name = "DocumentIds", Type = typeof(String).FullName, Value = documentIds },
				new() { Name = "AccessToken", Type = typeof(String).FullName, Value = accessToken },
				new() { Name = "ItemProfile", Type = typeof(String).FullName, Value = itemProfile != null ? JsonConvert.SerializeObject(itemProfile) : null }
			};
			var job = this.CreateFrmJob(
				isAutoSync ? SyncDocumentsToSharePointScheduleTask.TaskId : ManualSyncDocumentsToSharePointScheduleTask.TaskId,
				isAutoSync ? nameof(SyncDocumentsToSharePointScheduleTask) : nameof(ManualSyncDocumentsToSharePointScheduleTask),
				JsonConvert.SerializeObject(param),
				isAutoSync);
			return [job.Id];
		}

		/// <summary>
		/// Generates a relative path for a SharePoint document based on the provided document entity and profile.
		/// </summary>
		/// <param name="doc">Represents the document entity for which the relative path is being constructed.</param>
		/// <param name="profile">Contains configuration details for the SharePoint environment.</param>
		/// <returns>Returns a string that represents the relative path to the specified document in SharePoint.</returns>
		public FolderConfig[] BuildSharePointRelativePath(DocumentEntity doc, SharePointFolderProfile profile)
		{
			// // TODO: get level dynamically
			// var level1 = profile.Folders.First(e => e.Id == doc.RubricCategoryFk);
			// var level2 = level1.Folders.First(e => e.Id == doc.PrjDocumentCategoryFk);
			// var level3 = level2.Folders.First(e => e.Id == doc.PrjDocumentTypeFk);

			var jObject = JObject.FromObject(doc);

			var list = new List<FolderConfig>();

			BuildPath(profile.Folders, list);

			void BuildPath(FolderConfig[] folders, List<FolderConfig> list)
			{
				var config = folders.FirstOrDefault(e =>
				{
					var mapper = e.MetaDataType.GetMetaDataTypeMapper();
					return e.Id == (int?)jObject[mapper.RibName];
				});

				if (config == null)
				{
					return;
				}

				list.Add(config);

				if (config.Folders != null && config.Folders.Length != 0)
				{
					BuildPath(config.Folders, list);
				}
			}

			return list.ToArray();
		}

		/// <summary>
		/// Creates a series of folders in a specified drive based on a relative path. The folders are created under a
		/// specified parent item. (Synchronous version)
		/// </summary>
		/// <param name="driveId">Identifies the specific drive where the folders will be created.</param>
		/// <param name="parentDriveItemId">Specifies the parent item under which the new folders will be created.</param>
		/// <param name="folderConfigs"></param>
		/// <returns>Returns the last created folder item in the series.</returns>
		public DriveItem GeneratePathSync(string driveId, string parentDriveItemId, FolderConfig[] folderConfigs)
		{
			var driveItem = GraphClient.Drives[driveId].Items[parentDriveItemId].Request().GetAsync().GetAwaiter().GetResult();

			var index = 0;
			foreach (var folder in folderConfigs)
			{
				var newFolder = GraphClient.Drives[driveId].Items[driveItem.Id].Children.Request()
					.AddAsync(new DriveItem { Name = folder.Description, Folder = new Microsoft.Graph.Folder(), AdditionalData = new Dictionary<string, object>() { { "@microsoft.graph.conflictBehavior", "replace" } } }).GetAwaiter().GetResult();
				driveItem = newFolder;

				if (folder.DriveRecipients?.Length == 0 || folder.Roles?.Length == 0)
				{
					continue;
				}
				if (index == 0)
				{
					GraphClient.Drives[driveId].Items[newFolder.Id].Invite(folder.DriveRecipients, true, folder.Roles, false, null, false).Request().PostAsync().GetAwaiter().GetResult();
				}
				index++;
				GraphClient.Drives[driveId].Items[newFolder.Id].Invite(folder.DriveRecipients, true, folder.Roles, false, null, true).Request().PostAsync().GetAwaiter().GetResult();
			}

			return driveItem;
		}

		/// <summary>
		/// Downloads a document from a specified drive using the Graph service client.
		/// </summary>
		/// <param name="driveId">Identifies the specific drive from which the document will be downloaded.</param>
		/// <param name="driveItemId">Specifies the document to be downloaded from the identified drive.</param>
		/// <returns>Returns a stream containing the content of the downloaded document.</returns>
		public Stream DownloadDocument(string driveId, string driveItemId)
		{
			// TODO: large file download
			return GraphClient.Drives[driveId].Items[driveItemId].Content.Request().GetAsync().GetAwaiter().GetResult();
		}

		/// <summary>
		/// Replaces a document in SharePoint with a new file stream and updates its name to a unique value.
		/// </summary>
		/// <param name="driveId">Identifies the specific drive in SharePoint where the document is located.</param>
		/// <param name="driveItemId">Specifies the item within the drive that is to be replaced.</param>
		/// <param name="stream">Contains the new file data that will replace the existing document.</param>
		/// <param name="fileName">Represents the desired name for the new file after it has been uploaded.</param>
		/// <returns>Returns the updated DriveItem and the new file name.</returns>
		public (DriveItem fileInSharePoint, string fileName) ReplaceDocument(string driveId, string driveItemId, Stream stream, string fileName)
		{
			var fileInSharePoint = GraphClient.Drives[driveId].Items[driveItemId].Content.Request()
				.PutAsync<DriveItem>(stream).GetAwaiter().GetResult();

			fileName = GenerateUniqueFileNameSync(driveId, driveItemId, fileName);

			fileInSharePoint = GraphClient.Drives[driveId].Items[driveItemId].Request().UpdateAsync(new DriveItem { Name = fileName }).GetAwaiter().GetResult();

			return (fileInSharePoint, fileName);
		}

		/// <summary>
		/// Generates a unique file name by appending a number to the original name if a file with that name already exists.
		/// (Synchronous version)
		/// </summary>
		/// <param name="driveId">Identifies the specific drive where the file is being checked.</param>
		/// <param name="driveItemId">Specifies the item within the drive to check for existing files.</param>
		/// <param name="fileName">The original name of the file that needs to be made unique.</param>
		/// <returns>The unique file name that can be used without conflict.</returns>
		public string GenerateUniqueFileNameSync(string driveId, string driveItemId, string fileName)
		{
			var newCount = 1;
			var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

			var fileExtension = Path.GetExtension(fileName);

			while (true)
			{
				try
				{
					var filesWithDownloadFileName = GraphClient.Drives[driveId].Items[driveItemId].ItemWithPath($"{fileName}").Request().GetAsync().GetAwaiter().GetResult();
				}
				catch (Exception ex) when (ex.GetBaseException().Message.Contains("itemNotFound"))
				{
					break;
				}

				newCount++;
				fileName = $"{fileNameWithoutExtension} ({newCount}){fileExtension}";
			}

			return fileName;
		}

		/// <summary>
		/// Creates a new term group in the term store of a specified site in SharePoint. (Synchronous version)
		/// </summary>
		public Microsoft.Graph.TermStore.Group TermStoreCreateGroupSync(string siteId = "")
		{
			if (!string.IsNullOrWhiteSpace(siteId))
			{
				// site term group name should be unique.
				var siteTermGroup = RibGlobalTermGroup + "_" + GetSiteGuid(siteId, true);
				var siteGroups = GraphClient.Sites[siteId].TermStore.Groups.Request().Filter($"displayName eq '{siteTermGroup}'").GetAsync().GetAwaiter().GetResult();
				if (siteGroups.Count > 0)
				{
					return siteGroups[0];
				}

				var siteGroup = GraphClient.Sites[siteId].TermStore.Groups.Request().AddAsync(new Microsoft.Graph.TermStore.Group { DisplayName = siteTermGroup, Scope = Microsoft.Graph.TermStore.TermGroupScope.SiteCollection }).GetAwaiter().GetResult();

				SaveMetaDataToExt(MetaDataType.SiteTermGroup, (int)SpecialMetaDataKey.TermGroup, [siteId, siteGroup.Id], siteTermGroup);

				return siteGroup;
			}

			var groups = GraphClient.Sites.Root.TermStore.Groups.Request().Filter($"displayName eq '{RibGlobalTermGroup}'").GetAsync().GetAwaiter().GetResult();

			if (groups.Count > 0)
			{
				return groups[0];
			}

			var globalTermGroup = GraphClient.Sites.Root.TermStore.Groups.Request().AddAsync(new Microsoft.Graph.TermStore.Group { DisplayName = RibGlobalTermGroup }).GetAwaiter().GetResult();

			SaveMetaDataToExt(MetaDataType.GlobalTermGroup, (int)SpecialMetaDataKey.TermGroup, [globalTermGroup.Id], RibGlobalTermGroup);

			return globalTermGroup;
		}

		/// <summary>
		/// Creates a new term set in the term store of a specified site and group in SharePoint. (Synchronous version)
		/// </summary>
		/// <param name="metaDataType"></param>
		/// <param name="siteId"></param>
		/// <param name="group"></param>
		/// <param name="extString"></param>
		public Set CreateTermSetSync(MetaDataType metaDataType, string siteId, Microsoft.Graph.TermStore.Group group, string? extString = null)
		{
			var (_, internalName, spDisplayName, _) = metaDataType.GetMetaDataTypeMapper(extString);

			Set set;

			// site level
			var sets = GraphClient.Sites[siteId].TermStore.Groups[group.Id].Sets.Request()
				.Select("id,description,properties")
				.Filter($"properties/any(p:p/key eq '{RibName}') and properties/any(p:p/value eq '{internalName}')").GetAsync().GetAwaiter().GetResult();

			if (sets.Count > 0)
			{
				set = sets[0];
			}
			else
			{
				if (group.DisplayName == RibGlobalTermGroup)
				{
					set = GraphClient.Sites.Root.TermStore.Groups[group.Id].Sets.Request().AddAsync(new Set
					{
						Description = spDisplayName,
						LocalizedNames = [new LocalizedName { Name = spDisplayName, LanguageTag = LanguageTag }],
						Properties = [new KeyValue { Key = RibName, Value = internalName }]
					}).GetAwaiter().GetResult();
					SaveMetaDataToExt(metaDataType, (int)SpecialMetaDataKey.GlobalTermSet, [group.Id, set.Id], internalName);
				}
				else
				{
					set = GraphClientWithOwner().Sites[siteId].TermStore.Groups[group.Id].Sets.Request().AddAsync(new Set
					{
						Description = spDisplayName,
						LocalizedNames = [new LocalizedName { Name = spDisplayName, LanguageTag = LanguageTag }],
						Properties = [new KeyValue { Key = RibName, Value = internalName }]
					}).GetAwaiter().GetResult();
					SaveMetaDataToExt(metaDataType, (int)SpecialMetaDataKey.TermSet, [siteId, group.Id, set.Id], internalName);
				}
			}

			_sharePointFolderProfile.MetaDataTermSetMapping[metaDataType] = set.Id.ToString();

			return set;
		}

		/// <summary>
		/// Creates a new column in the "Documents" list of a specified site in SharePoint, based on the provided metadata type.
		/// (Synchronous version)
		/// </summary>
		/// <param name="metaDataType"></param>
		/// <param name="site"></param>
		/// <param name="termStoreId"></param>
		/// <param name="termSetId"></param>
		public IField CreateColumnSync(MetaDataType metaDataType, Site site, string termStoreId, string termSetId = "")
		{
			var (_, internalName, displayName, readOnly) = metaDataType.GetMetaDataTypeMapper();

			using var pnpCoreContext = this.InitPnpClient(site);
			var list = pnpCoreContext.Web.Lists.GetByTitleAsync("Documents", p => p.Fields.QueryProperties(p => p.InternalName
			)).GetAwaiter().GetResult();

			if (string.IsNullOrWhiteSpace(termSetId))
			{
				if (!_sharePointFolderProfile.MetaDataTermSetMapping.TryGetValue(metaDataType, out var storeTermSetId))
				{
					throw new InvalidOperationException($"Term set ID for metadata type '{metaDataType}' is not configured.");
				}

				termSetId = storeTermSetId;
			}

			var field = list.Fields.AsRequested().FirstOrDefault(f => f.InternalName == internalName);

			if (field == null)
			{
				field = list.Fields.AddTaxonomyAsync(
					displayName,
					new FieldTaxonomyOptions { InternalName = internalName, TermStoreId = Guid.Parse(termStoreId), TermSetId = Guid.Parse(termSetId), AddToDefaultView = true }).GetAwaiter().GetResult();

				SaveMetaDataToExt(metaDataType, (int)SpecialMetaDataKey.Column, [site.Id, list.Id.ToString(), field.Id.ToString()], $"{GetSiteGuid(site.Id)} {field.InternalName}");
			}

			field.ReadOnlyField = readOnly;

			field.UpdateAsync().GetAwaiter().GetResult();

			return field;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="metaDataType"></param>
		/// <param name="siteId"></param>
		/// <param name="termGroupGlobal"></param>
		/// <param name="termStoreId"></param>
		/// <param name="contextId"></param>
		/// <param name="site"></param>
		/// <returns></returns>
		/// <exception cref="ArgumentOutOfRangeException"></exception>
		public IField CreateColumnByMetaDataType(MetaDataType metaDataType, string siteId, Microsoft.Graph.TermStore.Group termGroupGlobal, string termStoreId, string? contextId = null, Site? site = null)
		{
			if ((int)metaDataType > 1000)
			{
				throw new ArgumentOutOfRangeException(nameof(metaDataType), "Not all MetaDataType are support create column in SharePoint.");
			}

			if (metaDataType == MetaDataType.ControllingUnit)
			{
				var termGroupSite = TermStoreCreateGroupSync(siteId);
				CreateTermSetSync(metaDataType, siteId, termGroupSite);
			}
			else if (metaDataType == MetaDataType.ProcurementStructure && !string.IsNullOrWhiteSpace(contextId))
			{
				CreateTermSetSync(metaDataType, siteId, termGroupGlobal, contextId);
			}
			else
			{
				CreateTermSetSync(metaDataType, siteId, termGroupGlobal);
			}

			if (site == null)
			{
				site = GraphClient.Sites[siteId].Request().GetAsync().GetAwaiter().GetResult();
			}

			return CreateColumnSync(metaDataType, site, termStoreId);
		}
		/// <summary>
		/// Processes metadata for a document entity by retrieving the corresponding item from SharePoint and updating its metadata fields.
		/// (Synchronous version)
		/// </summary>
		/// <param name="entityToProcess"></param>
		/// <param name="site"></param>
		/// <param name="list"></param>
		/// <param name="driveItemId"></param>
		/// <param name="metaDataTypes"></param>
		public void ProcessMetaDataSync(DocumentEntity entityToProcess, Microsoft.Graph.Site site, Microsoft.Graph.List list, string driveItemId, MetaDataType[] metaDataTypes)
		{
			var item = GraphClient.Sites[site.Id].Drive.Items[driveItemId].Request().Expand("listItem").GetAsync().GetAwaiter().GetResult();

			using var pnpCoreContext = this.InitPnpClient(site);

			var jObject = JObject.FromObject(entityToProcess);

			foreach (var metaDataType in metaDataTypes)
			{
				this.ProcessMetaDataByTypeSync(pnpCoreContext, jObject, metaDataType, site, list, item);
			}
		}

		/// <summary>
		/// Invites recipients to a SharePoint drive item. (Synchronous version)
		/// </summary>
		/// <param name="driveId"></param>
		/// <param name="driveItemId"></param>
		/// <param name="recipients"></param>
		/// <param name="role"></param>
		/// <param name="message"></param>
		/// <param name="accessToken"></param>
		public void InviteRecipientsToDriveItemSync(string driveId, string driveItemId, DriveRecipient[] recipients, string role, string? message = null, string? accessToken = null)
		{
			GraphServiceClient client;
			var item = DecodeAccessToken(accessToken);
			// use user accessToken if it is valid
			if (item.exp.HasValue && item.exp.Value > DateTimeOffset.UtcNow.AddMinutes(1))
			{
				client = AzureActiveDirectoryManagement.Instance.GetGraphClient(
					new OfficeAuthParams
					{
						AuthType = OfficeAuthType.AccessToken,
						AccessToken = accessToken
					}, "v1.0");
			}
			else
			{
				client = GraphClient;
			}
			client.Drives[driveId].Items[driveItemId].Invite(recipients, true, [role], true, message, true).Request().PostAsync().GetAwaiter().GetResult();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="siteId"></param>
		/// <param name="removeHyphen"></param>
		/// <returns></returns>
		public string GetSiteGuid(string siteId, bool removeHyphen = false)
		{
			var siteIdArr = siteId.Split(',');

			if (siteIdArr.Length > 1)
			{
				return removeHyphen ? siteIdArr[1].Replace("-", "") : siteIdArr[1];
			}

			return removeHyphen ? siteIdArr[0].Replace("-", "") : siteIdArr[0];
		}

		private void ProcessMetaDataByTypeSync(PnPContext pnpCoreContext, JObject entityToProcess, MetaDataType metaDataType, Microsoft.Graph.Site site, Microsoft.Graph.List list, DriveItem driveItem)
		{
			var (ribName, sharePointName, _, _) = metaDataType.GetMetaDataTypeMapper();

			var termSetId = _sharePointFolderProfile.MetaDataTermSetMapping[metaDataType];

			var property = (int?)entityToProcess[ribName];
			if (property.HasValue)
			{
				Microsoft.Graph.TermStore.Term term;

				try
				{
					term = GenerateTermByMetaDataType(metaDataType, property.Value, GraphClient, site.Id, termSetId);
				}
				catch (Exception ex)
				{
					Console.Error.WriteLine($"Error creating term for {metaDataType}:({property.Value}) {ex.Message}");
					throw;
				}

				var listPnp = pnpCoreContext.Web.Lists.GetByIdAsync(Guid.Parse(list.Id)).GetAwaiter().GetResult();
				var item = listPnp.Items.GetByIdAsync(int.Parse(driveItem.ListItem.Fields.Id)).GetAwaiter().GetResult();
				item.Values[sharePointName] = new FieldTaxonomyValue(Guid.Parse(term.Id), term.Labels.First().Name);
				item.UpdateAsync().GetAwaiter().GetResult();
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="metaDataType"></param>
		/// <param name="id"></param>
		/// <param name="graphClient"></param>
		/// <param name="siteId"></param>
		/// <param name="termSetId"></param>
		/// <returns></returns>
		/// <exception cref="NotSupportedException"></exception>
		public virtual Microsoft.Graph.TermStore.Term GenerateTermByMetaDataType(MetaDataType metaDataType, int id, GraphServiceClient graphClient, string siteId, string termSetId)
		{
			string termStr;
			Microsoft.Graph.TermStore.Term termCreated;

			var terms = QueryTerms(siteId, termSetId, id);

			if (terms.Count > 0)
			{
				return terms.First();
			}

			switch (metaDataType)
			{
				case MetaDataType.RubricCategory:
					termStr = ConvertInfoToNotEmptyString(RubricCategoryLogic.Value.GetItemByKey(id).DescriptionInfo);
					termCreated = CreateTerm(metaDataType, siteId, termSetId, termStr, id);
					break;
				case MetaDataType.ProjectDocumentCategory:
					termStr = ConvertInfoToNotEmptyString((ProjectDocumentCategoryLogic.Value.GetById(id) as BasicsCustomizeProjectDocumentCategoryEntity)?.DescriptionInfo);
					termCreated = CreateTerm(metaDataType, siteId, termSetId, termStr, id);
					break;
				case MetaDataType.ProjectDocumentType:
					termStr = ConvertInfoToNotEmptyString(DocumentTypeLogic.Value.GetItemByKey(id).DescriptionInfo);
					termCreated = CreateTerm(metaDataType, siteId, termSetId, termStr, id);
					break;
				case MetaDataType.ProcurementStructure:
					var structure = ProcurementStructureLogic.Value.GetItemsByKey(new int?[] { id }).FirstOrDefault() as IPrcStructureEntity;
					var parentTermStr = GetParentStructure(structure!);
					termStr = ConvertInfoToNotEmptyString(structure?.DescriptionInfo, structure?.Code);
					termCreated = CreateTerm(metaDataType, siteId, termSetId, termStr, id, parentTermStr?.Id);

					// tree structure
					Microsoft.Graph.TermStore.Term? GetParentStructure(IPrcStructureEntity structure2)
					{
						if (structure2.PrcStructureFk.HasValue)
						{
							var terms = QueryTerms(siteId, termSetId, structure2.PrcStructureFk.Value);

							if (terms.Count > 0)
							{
								return terms.First();
							}

							var parentStructure = ProcurementStructureLogic.Value.GetItemsByKey(new int?[] { structure2.PrcStructureFk }).First() as IPrcStructureEntity;
							var parentTerm = GetParentStructure(parentStructure!);
							var termStr2 = ConvertInfoToNotEmptyString(structure2.DescriptionInfo, structure2.Code);
							var term = CreateTerm(metaDataType, siteId, termSetId, termStr2, structure2.Id, parentTerm?.Id);
							return term;
						}
						return null;
					}
					break;
				case MetaDataType.ControllingUnit:
					var controllingUnit = ControllingUnitLogic.Value.GetCtuItemById(id);
					termStr = ConvertInfoToNotEmptyString(controllingUnit.DescriptionInfo, controllingUnit.Code);
					var parentTermCu = GetParentControllingUnit(controllingUnit);
					termCreated = CreateTerm(metaDataType, siteId, termSetId, termStr, id, parentTermCu?.Id);

					// tree structure
					Microsoft.Graph.TermStore.Term? GetParentControllingUnit(IControllingUnitEntity cunit)
					{
						if (cunit.ControllingUnitFk.HasValue)
						{
							var terms = QueryTerms(siteId, termSetId, cunit.ControllingUnitFk.Value);

							if (terms.Count > 0)
							{
								return terms.First();
							}

							var parentCUnit = ControllingUnitLogic.Value.GetCtuItemById(cunit.ControllingUnitFk.Value);
							var parentTerm = GetParentControllingUnit(parentCUnit!);
							var termStr2 = ConvertInfoToNotEmptyString(cunit.DescriptionInfo, cunit.Code);
							var term = CreateTerm(metaDataType, siteId, termSetId, termStr2, cunit.Id, parentTerm?.Id);
							return term;
						}
						return null;
					}

					break;
				case MetaDataType.BusinessPartner:
					termStr = BusinessPartnerLogic.Value.GetBusinessPartnerById(id).BusinessPartnerName1 ?? "-";
					termCreated = CreateTerm(metaDataType, siteId, termSetId, termStr, id);
					break;
				default:
					throw new NotSupportedException($"MetaDataType '{metaDataType}' is not supported.");
			}

			return termCreated;
		}

		private ISetTermsCollectionPage QueryTerms(string siteId, string termSetId, int ribId)
		{
			var terms = GraphClient.Sites[siteId].TermStore.Sets[termSetId].Terms
					.Request()
					.Select("id,labels,properties")
					.Filter($"properties/any(p:p/key eq '{RibId}') and properties/any(p:p/value eq '{ribId}')")
					.GetAsync().GetAwaiter().GetResult();

			return terms;
		}

		private Microsoft.Graph.TermStore.Term CreateTerm(MetaDataType metaDataType, string siteId, string termSetId, string termStr, int ribId, string? parentTermId = null)
		{
			var term = new Microsoft.Graph.TermStore.Term
			{
				Labels =
						[new LocalizedLabel { Name = termStr, LanguageTag = LanguageTag, IsDefault = true }],
				Properties = new List<KeyValue> { new KeyValue { Key = RibId, Value = ribId.ToString() } }
			};

			if (parentTermId != null)
			{
				term = GraphClient.Sites[siteId].TermStore.Sets[termSetId].Terms[parentTermId].Children.Request().AddAsync(term).GetAwaiter().GetResult();
			}
			else
			{
				term = GraphClient.Sites[siteId].TermStore.Sets[termSetId].Children.Request().AddAsync(term).GetAwaiter().GetResult();
			}

			SaveMetaDataToExt(metaDataType, ribId, [siteId, termSetId, term.Id], termStr);

			return term;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="descriptionInfo"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		public string ConvertInfoToNotEmptyString(DescriptionTranslateType? descriptionInfo, string? code = null)
		{
			string? desc = descriptionInfo?.Description;
			bool hasCode = !string.IsNullOrWhiteSpace(code);
			bool hasDesc = !string.IsNullOrWhiteSpace(desc);

			var termStr = (hasCode, hasDesc) switch
			{
				(true, true) => $"{code}-{desc}",
				(true, false) => code!,
				(false, true) => desc!,
				_ => "-",
			};

			if (termStr.Length > 255)
			{
				Console.Error.WriteLine($"Term string '{termStr}' exceeds maximum length of 255 characters. Truncating.");
				termStr = termStr.Substring(0, 255);
			}

			return termStr;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="metaDataType"></param>
		/// <param name="objectId"></param>
		/// <param name="externalIds"></param>
		/// <param name="description"></param>
		public virtual void SaveMetaDataToExt(MetaDataType metaDataType, int objectId, string[] externalIds, string? description = null)
		{
			if (metaDataType == MetaDataType.BusinessPartner && objectId > 0)
			{
				var metaData = this.Bp2ExtLogic.Value.Create(objectId, externalSourceFk, string.Join(";", externalIds), description);
				this.Bp2ExtLogic.Value.Save(metaData);
				return;
			}

			if (metaDataType == MetaDataType.ControllingUnit && objectId > 0)
			{
				var metaData = this.ControllingUnit2ExtLogic.Value.Create(objectId, externalSourceFk, string.Join(";", externalIds), description);
				this.ControllingUnit2ExtLogic.Value.Save(metaData);
				return;
			}

			DocMetadata2extLogic.Value.Save(new DocMetadata2extEntity
			{
				BasExternalsourceFk = externalSourceFk,
				Type = (int)metaDataType,
				ExternalId = string.Join(";", externalIds),
				Objectid = objectId, // <0 means is not bound to any specific entity
				ExternalDescription = description,
			});
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="metaDataType"></param>
		/// <param name="objectId"></param>
		/// <returns></returns>
		public virtual (int Id, int ObjectId, int ExternalSourceFk, string ExternalId) GetMetaDataFromExt(MetaDataType metaDataType, int objectId)
		{
			if (metaDataType == MetaDataType.BusinessPartner && objectId > 0)
			{
				var bPMetaData = this.Bp2ExtLogic.Value.Get(objectId, externalSourceFk);
				return bPMetaData != null ? (bPMetaData.Id, objectId, externalSourceFk, bPMetaData.ExternalId) : (-1, -1, -1, string.Empty);
			}

			if (metaDataType == MetaDataType.ControllingUnit && objectId > 0)
			{
				var cuMetaData = this.ControllingUnit2ExtLogic.Value.Get(objectId, externalSourceFk);
				return cuMetaData != null ? (cuMetaData.Id, objectId, externalSourceFk, cuMetaData.ExternalId) : (-1, -1, -1, string.Empty);
			}

			if (metaDataType == MetaDataType.Document && objectId > 0)
			{
				var docMetaData = this.FileArchive2ExtLogic.Value.GetSearchList(e => e.FileArchiveDocFk == objectId && e.BasExternalSourceFk == externalSourceFk).FirstOrDefault();
				return docMetaData != null ? (docMetaData.Id, objectId, externalSourceFk, docMetaData.ExtGuid) : (-1, -1, -1, string.Empty);
			}

			var metaData = DocMetadata2extLogic.Value.GetCoresByFilter(e =>
				e.BasExternalsourceFk == externalSourceFk
				&& e.Type == (int)metaDataType
				&& e.Objectid == objectId).FirstOrDefault();

			return metaData != null ? (metaData.Id, objectId, externalSourceFk, metaData.ExternalId) : (-1, -1, -1, string.Empty);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="filterFn"></param>
		/// <returns></returns>
		public virtual IEnumerable<DocumentEntity> GetDocumentsByFilter(Expression<Func<DocumentEntity, bool>> filterFn)
		{
			return this.DocumentLogic.Value
				.GetCoresByFilter(
					filterFn) ?? [];
		}

		/// <summary>
		/// Gets a document by its identification data.
		/// This method can be overridden in derived classes to provide custom document retrieval logic.
		/// </summary>
		/// <param name="docId">The document identification data containing Id and PKey values</param>
		/// <returns>The document entity matching the provided identification data</returns>
		public virtual DocumentEntity GetDocumentById(Platform.Core.IdentificationData docId)
		{
			return DocumentLogic.Value.GetById(docId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="doc"></param>
		/// <param name="sharePointId"></param>
		/// <param name="sharePointFileName"></param>
		/// <returns></returns>
		public virtual DocumentEntity SaveDocument(DocumentEntity doc, string sharePointId, string sharePointFileName)
		{
			var doc2Ext = this.FileArchive2ExtLogic.Value.GetSearchList(e => e.FileArchiveDocFk == doc.FileArchiveDocFk
			&& e.BasExternalSourceFk == externalSourceFk).FirstOrDefault();
			if (doc2Ext == null)
			{
				doc2Ext = this.FileArchive2ExtLogic.Value.Create();
			}
			doc2Ext.FileArchiveDocFk = doc.FileArchiveDocFk;
			doc2Ext.BasExternalSourceFk = externalSourceFk;
			doc2Ext.ExtGuid = sharePointId;
			using (var trans = TransactionScopeFactory.CreateRequiresNew())
			{
				this.FileArchive2ExtLogic.Value.Save(doc2Ext);
				var newDoc = DocumentLogic.Value.GetById(new Platform.Core.IdentificationData { Id = doc.Id });
				newDoc.PrjDocumentStatusFk = doc.PrjDocumentStatusFk;
				DocumentLogic.Value.Save(newDoc);
				DocumentHistoryLogic.Value.saveHistory(newDoc, DocumentHistoryOperationType.synchronizedToSharePoint, newDoc.Revision);
				trans.Complete();
			}
			return doc;
		}

		///// <summary>
		///// 
		///// </summary>
		///// <param name="request"></param>
		///// <returns></returns>
		//public bool InviteUsers(SendManualSyncInfoEntity request)
		//{
		//	var documents = GetDocumentsByFilter(e =>
		//					e.PrjProjectFk != null
		//					&& request.DocIds.Contains(e.Id));

		//	foreach (var item in documents.GroupBy(e => e.PrjProjectFk))
		//	{
		//		var ext = DocMetadata2extLogic.Value
		//	.GetCoresByFilter(e =>
		//		e.BasExternalsourceFk == externalSourceFk
		//		&& e.Objectid == item.Key
		//		&& e.Type == (int)MetaDataType.Project).First();
		//		var drive = GraphClient.Sites[siteId].Drive.Request().GetAsync().GetAwaiter().GetResult();

		//		this.InviteRecipientsToDriveItemSync()

		//	}

		//}

		/// <summary>
		/// Decode standard oauth accessToken
		/// </summary>
		/// <param name="accessToken"></param>
		/// <returns></returns>
		public virtual (DateTimeOffset? exp, string? uniqueName, string? oid) DecodeAccessToken(string? accessToken)
		{
			try
			{
				var payload = accessToken!.Split('.')[1];
				payload = payload.Replace('-', '+').Replace('_', '/');
				switch (payload.Length % 4)
				{
					case 2:
						payload += "==";
						break;
					case 3:
						payload += "=";
						break;
				}
				var jsonBytes = System.Convert.FromBase64String(payload);
				var jsonPayload = System.Text.Encoding.UTF8.GetString(jsonBytes);
				var jObject = JObject.Parse(jsonPayload);
				var exp = jObject.Value<long?>("exp");
				var uniqueName = jObject.Value<string>("unique_name");
				var oid = jObject.Value<string>("oid");

				DateTimeOffset? expDateTime = null;
				if (exp.HasValue)
				{
					expDateTime = DateTimeOffset.FromUnixTimeSeconds(exp.Value);
				}
				return (expDateTime, uniqueName, oid);
			}
			catch
			{
				// ignore any exception and return null
			}
			return (null, null, null);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="siteId"></param>
		/// <param name="teamId"></param>
		/// <param name="reTry"></param>
		/// <returns></returns>
		public virtual Site TryGetSite(string siteId, string? teamId = null, int reTry = 3)
		{
			try
			{
				// try add server user to team
				if (GraphClient.AuthenticationProvider is TokenAuthenticationProvider authenticationProvider)
				{
					var item = DecodeAccessToken(authenticationProvider.AccessToken);
					if (!string.IsNullOrEmpty(item.oid) && !string.IsNullOrEmpty(teamId))
					{
						GraphClient.Teams[teamId].Members.Request().AddAsync(
							new AadUserConversationMember
							{
								Roles = ["owner"],
								AdditionalData = new Dictionary<string, object>
								{
									{ "<EMAIL>", $"https://graph.microsoft.com/v1.0/users('{item.oid}')" }
								}
							}).GetAwaiter().GetResult();
					}
				}
				Task.Delay(5000).GetAwaiter().GetResult(); // wait for a while to ensure the permission is applied
				return GraphClient.Sites[siteId].Request().GetAsync().GetAwaiter().GetResult();
			}
			catch (ServiceException ex) when (ex.Error.Code == "accessDenied")
			{
				if (reTry <= 0)
				{
					throw;
				}
				return TryGetSite(siteId, teamId, reTry - 1);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		public virtual void UpdateMetaDataToSharePoint(DocMetadata2extEntity metaDataEntity, string termStr)
		{
			var ids = metaDataEntity.ExternalId.Split(';');

			GraphClient.Sites[ids[0]].TermStore.Sets[ids[1]].Terms[ids[2]].Request()
				.UpdateAsync(new Microsoft.Graph.TermStore.Term
				{
					Labels = [new LocalizedLabel { Name = termStr, LanguageTag = LanguageTag, IsDefault = true }],
					Properties = [new KeyValue { Key = RibId, Value = metaDataEntity.Objectid.ToString() }]
				}).GetAwaiter().GetResult();
		}

		private JobEntity CreateFrmJob(string taskType, string taskName, string parameters, bool isAutoSync = true)
		{
			JobEntity frmJob = _frmJobLogic.Value.Create();

			frmJob.TaskType = taskType;
			frmJob.Name = taskName;
			frmJob.Enable = true;
			frmJob.LoggingLevel = (Int16)LoggingLevel.Info;
			frmJob.Priority = (Int16)Priority.Normal;
			frmJob.StartTime = DateTime.UtcNow;
			frmJob.RepeatUnit = (short)(isAutoSync ? Frequency.Daily : Frequency.None);
			frmJob.ParameterList = parameters;
			frmJob.RunInUserContext = true;
			frmJob.InsertedBy = 1;
			_frmJobLogic.Value.AddJob(frmJob);
			return frmJob;
		}
	}


	/// <summary>
	///
	/// </summary>
	// TODO: remove reflections
	public class AADAccessor
	{
		///// <summary>
		/////
		///// </summary>
		///// <param name="aad"></param>
		///// <returns></returns>
		//[UnsafeAccessor(UnsafeAccessorKind.Method, Name = "GetClientId")]
		//public static extern string ClientId(AzureActiveDirectoryManagement aad);

		/// <summary>
		///
		/// </summary>
		/// <param name="aad"></param>
		/// <returns></returns>
		[UnsafeAccessor(UnsafeAccessorKind.Method, Name = "GetAuthority")]
		public static extern string Authority(AzureActiveDirectoryManagement aad);

		///// <summary>
		/////
		///// </summary>
		///// <param name="aad"></param>
		///// <returns></returns>
		//[UnsafeAccessor(UnsafeAccessorKind.Method, Name = "GetClientSecret")]
		//public static extern string ClientSecret(AzureActiveDirectoryManagement aad);

		///// <summary>
		/////
		///// </summary>
		///// <param name="aad"></param>
		///// <returns></returns>
		//[UnsafeAccessor(UnsafeAccessorKind.Method, Name = "GetTenantId")]
		//public static extern string TenantId(AzureActiveDirectoryManagement aad);

		/// <summary>
		///
		/// </summary>
		/// <param name="aad"></param>
		/// <param name="version"></param>
		/// <param name="accessToken"></param>
		[UnsafeAccessor(UnsafeAccessorKind.Method, Name = "GetGraphClient")]
		public static extern GraphServiceClient GetGraphClient(AzureActiveDirectoryManagement aad, string version, string accessToken);
	}
}