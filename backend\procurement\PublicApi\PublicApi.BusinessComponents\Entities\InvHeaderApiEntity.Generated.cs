﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Procurement.PublicApi.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Procurement.PublicApi.BusinessComponents.InvHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("INV_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(5)]
    public partial class InvHeaderApiEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new InvHeaderApiEntity object.
        /// </summary>
        public InvHeaderApiEntity()
        {
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// Id of the invoice
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// Id of the invstatus
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InvStatusFk")]
        [RIB.Visual.Platform.Common.EntityStatusFk("invoice")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int InvStatusId {
            get; set;
        }

    
        /// <summary>
        /// Description of invstatus
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string InvStatusDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the company
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyId {
            get; set;
        }

    
        /// <summary>
        /// Code of the company
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string CompanyCode {
            get; set;
        }

    
        /// <summary>
        /// Id of the rubriccategory
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_RUBRIC_CATEGORY_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RubricCategoryFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int RubricCategoryId {
            get; set;
        }

    
        /// <summary>
        /// Description of rubriccategory
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_RUBRIC_CATEGORY_DESC", TypeName = "nvarchar(2000)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string RubricCategoryDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the configuration
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 7)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcConfigurationFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcConfigurationId {
            get; set;
        }

    
        /// <summary>
        /// Description of configuration
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_DESC", TypeName = "nvarchar(2000)", Order = 8)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcConfigurationDescription {
            get; set;
        }

    
        /// <summary>
        /// Code
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 9)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Code {
            get; set;
        }

    
        /// <summary>
        /// Description
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 10)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Description {
            get; set;
        }

    
        /// <summary>
        /// Id of the businesspartner
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        public virtual int? BusinesspartnerId {
            get; set;
        }

    
        /// <summary>
        /// Description of businesspartner
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BusinesspartnerDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the subsidiary
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 13)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public virtual int? SubsidiaryId {
            get; set;
        }

    
        /// <summary>
        /// Description of subsidiary
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 14)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SubsidiaryDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the supplier
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 15)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public virtual int? SupplierId {
            get; set;
        }

    
        /// <summary>
        /// Code of the supplier
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierCode {
            get; set;
        }

    
        /// <summary>
        /// Description of supplier
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 17)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the invtype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_TYPE_ID", TypeName = "int", Order = 18)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InvTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int InvTypeId {
            get; set;
        }

    
        /// <summary>
        /// Description of invtype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 19)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string InvTypeDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the invgroup
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_GROUP_ID", TypeName = "int", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InvGroupFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int InvGroupId {
            get; set;
        }

    
        /// <summary>
        /// Description of invgroup
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INV_GROUP_DESC", TypeName = "nvarchar(2000)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string InvGroupDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the clerkprc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 22)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public virtual int? ClerkPrcId {
            get; set;
        }

    
        /// <summary>
        /// Code of the clerkprc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 23)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkPrcCode {
            get; set;
        }

    
        /// <summary>
        /// Description of clerkprc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkPrcDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the clerkreq
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 25)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public virtual int? ClerkReqId {
            get; set;
        }

    
        /// <summary>
        /// Code of the clerkreq
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 26)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkReqCode {
            get; set;
        }

    
        /// <summary>
        /// Description of clerkreq
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 27)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkReqDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the clerkwfe
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_WFE_ID", TypeName = "int", Order = 28)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkWfeFk")]
        public virtual int? ClerkWfeId {
            get; set;
        }

    
        /// <summary>
        /// Code of the clerkwfe
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_WFE_CODE", TypeName = "nvarchar(16)", Order = 29)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkWfeCode {
            get; set;
        }

    
        /// <summary>
        /// Description of clerkwfe
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_WFE_DESC", TypeName = "nvarchar(252)", Order = 30)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkWfeDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the currency
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 31)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CurrencyId {
            get; set;
        }

    
        /// <summary>
        /// Description of currency
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 32)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string CurrencyDescription {
            get; set;
        }

    
        /// <summary>
        /// AmountNet
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NET", TypeName = "numeric(19,7)", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNet")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNet {
            get; set;
        }

    
        /// <summary>
        /// AmountNetOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NET_OC", TypeName = "numeric(19,7)", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNetOc {
            get; set;
        }

    
        /// <summary>
        /// AmountGross
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_GROSS", TypeName = "numeric(19,7)", Order = 35)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountGross")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountGross {
            get; set;
        }

    
        /// <summary>
        /// AmountGrossOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_GROSS_OC", TypeName = "numeric(19,7)", Order = 36)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountGrossOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountGrossOc {
            get; set;
        }

    
        /// <summary>
        /// ExchangeRate
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 37)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal Exchangerate {
            get; set;
        }

    
        /// <summary>
        /// Id of the taxcode
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TaxCodeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int TaxCodeId {
            get; set;
        }

    
        /// <summary>
        /// Code of the taxcode
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_CODE", TypeName = "nvarchar(16)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string TaxCodeCode {
            get; set;
        }

    
        /// <summary>
        /// Description of taxcode
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_DESC", TypeName = "nvarchar(2000)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string TaxCodeDescription {
            get; set;
        }

    
        /// <summary>
        /// DateInvoiced
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_INVOICED", TypeName = "date", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateInvoiced")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateInvoiced {
            get; set;
        }

    
        /// <summary>
        /// Reference
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REFERENCE", TypeName = "nvarchar(252)", Order = 42)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Reference")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Reference {
            get; set;
        }

    
        /// <summary>
        /// DateReceived
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_RECEIVED", TypeName = "date", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateReceived")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateReceived {
            get; set;
        }

    
        /// <summary>
        /// DatePosted
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_POSTED", TypeName = "date", Order = 44)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DatePosted")]
        public virtual System.DateTime? DatePosted {
            get; set;
        }

    
        /// <summary>
        /// Id of the project
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public virtual int? ProjectId {
            get; set;
        }

    
        /// <summary>
        /// Code of the project
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 46)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ProjectCode {
            get; set;
        }

    
        /// <summary>
        /// Description of project
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 47)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ProjectDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the package
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_ID", TypeName = "int", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcPackageFk")]
        public virtual int? PackageId {
            get; set;
        }

    
        /// <summary>
        /// Code of the package
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_CODE", TypeName = "nvarchar(16)", Order = 49)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PackageCode {
            get; set;
        }

    
        /// <summary>
        /// Description of package
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_DESC", TypeName = "nvarchar(252)", Order = 50)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string PackageDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the controllingunit
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingUnitFk")]
        public virtual int? MdcControllingunitId {
            get; set;
        }

    
        /// <summary>
        /// Code of the controllingunit
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(16)", Order = 52)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string MdcControllingunitCode {
            get; set;
        }

    
        /// <summary>
        /// Description of controllingunit
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 53)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcControllingunitDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the structure
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_ID", TypeName = "int", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcStructureFk")]
        public virtual int? PrcStructureId {
            get; set;
        }

    
        /// <summary>
        /// Code of the structure
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_CODE", TypeName = "nvarchar(16)", Order = 55)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PrcStructureCode {
            get; set;
        }

    
        /// <summary>
        /// Description of structure
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_DESC", TypeName = "nvarchar(2000)", Order = 56)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcStructureDescription {
            get; set;
        }

    
        /// <summary>
        /// ReconcilationHint
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RECONCILATIONHINT", TypeName = "nvarchar(255)", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReconcilationHint")]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public virtual string Reconcilationhint {
            get; set;
        }

    
        /// <summary>
        /// Id of the contract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConHeaderFk")]
        public virtual int? ConHeaderId {
            get; set;
        }

    
        /// <summary>
        /// Code of the contract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_CODE", TypeName = "nvarchar(16)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ConHeaderCode {
            get; set;
        }

    
        /// <summary>
        /// Description of contract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_DESC", TypeName = "nvarchar(252)", Order = 60)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ConHeaderDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the pes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_HEADER_ID", TypeName = "int", Order = 61)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PesHeaderFk")]
        public virtual int? PesHeaderId {
            get; set;
        }

    
        /// <summary>
        /// Code of the pes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_HEADER_CODE", TypeName = "nvarchar(16)", Order = 62)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PesHeaderCode {
            get; set;
        }

    
        /// <summary>
        /// Description of pes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PES_HEADER_DESC", TypeName = "nvarchar(252)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string PesHeaderDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the paymentterm
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_ID", TypeName = "int", Order = 64)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermFk")]
        public virtual int? PaymentTermId {
            get; set;
        }

    
        /// <summary>
        /// Code of the paymentterm
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_CODE", TypeName = "nvarchar(16)", Order = 65)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermCode {
            get; set;
        }

    
        /// <summary>
        /// Description of paymentterm
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_DESC", TypeName = "nvarchar(2000)", Order = 66)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermDescription {
            get; set;
        }

    
        /// <summary>
        /// DateDiscount
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DISCOUNT", TypeName = "date", Order = 67)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDiscount")]
        public virtual System.DateTime? DateDiscount {
            get; set;
        }

    
        /// <summary>
        /// AmountDiscountBasis
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNTBASIS", TypeName = "numeric(19,7)", Order = 68)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountDiscountBasis")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountDiscountbasis {
            get; set;
        }

    
        /// <summary>
        /// AmountDiscountBasisOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNTBASIS_OC", TypeName = "numeric(19,7)", Order = 69)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountDiscountBasisOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountDiscountbasisOc {
            get; set;
        }

    
        /// <summary>
        /// PercentDiscount
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PERCENT_DISCOUNT", TypeName = "numeric(10,2)", Order = 70)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PercentDiscount")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal PercentDiscount {
            get; set;
        }

    
        /// <summary>
        /// AmountDiscount
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNT", TypeName = "numeric(19,7)", Order = 71)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountDiscount")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountDiscount {
            get; set;
        }

    
        /// <summary>
        /// AmountDiscountOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 72)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountDiscountOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountDiscountOc {
            get; set;
        }

    
        /// <summary>
        /// DateNetPayable
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_NETPAYABLE", TypeName = "date", Order = 73)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateNetPayable")]
        public virtual System.DateTime? DateNetpayable {
            get; set;
        }

    
        /// <summary>
        /// AmountNetPes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETPES", TypeName = "numeric(19,7)", Order = 74)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetPes")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNetpes {
            get; set;
        }

    
        /// <summary>
        /// AmountVatPes
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATPES", TypeName = "numeric(19,7)", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatPes")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountVatpes {
            get; set;
        }

    
        /// <summary>
        /// AmountNetContract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETCONTRACT", TypeName = "numeric(19,7)", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetContract")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNetcontract {
            get; set;
        }

    
        /// <summary>
        /// AmountVatContract
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATCONTRACT", TypeName = "numeric(19,7)", Order = 77)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatContract")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountVatcontract {
            get; set;
        }

    
        /// <summary>
        /// AmountNetOther
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETOTHER", TypeName = "numeric(19,7)", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetOther")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNetother {
            get; set;
        }

    
        /// <summary>
        /// AmountVatOther
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATOTHER", TypeName = "numeric(19,7)", Order = 79)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatOther")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountVatother {
            get; set;
        }

    
        /// <summary>
        /// AmountNetReject
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETREJECT", TypeName = "numeric(19,7)", Order = 80)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetReject")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNetreject {
            get; set;
        }

    
        /// <summary>
        /// AmountVatReject
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATREJECT", TypeName = "numeric(19,7)", Order = 81)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatReject")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountVatreject {
            get; set;
        }

    
        /// <summary>
        /// Remark
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 82)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark {
            get; set;
        }

    
        /// <summary>
        /// Userdefined1
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 83)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined1 {
            get; set;
        }

    
        /// <summary>
        /// Userdefined2
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 84)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined2 {
            get; set;
        }

    
        /// <summary>
        /// Userdefined3
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 85)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined3 {
            get; set;
        }

    
        /// <summary>
        /// Userdefined4
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined4 {
            get; set;
        }

    
        /// <summary>
        /// Userdefined5
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 87)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined5 {
            get; set;
        }

    
        /// <summary>
        /// SearchPattern
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 88)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public virtual string SearchPattern {
            get; set;
        }

    
        /// <summary>
        /// Id of the billingschema
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_ID", TypeName = "int", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BillingSchemaFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int MdcBillingSchemaId {
            get; set;
        }

    
        /// <summary>
        /// Description of billingschema
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 90)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcBillingSchemaDescription {
            get; set;
        }

    
        /// <summary>
        /// AmountNetPesOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETPES_OC", TypeName = "numeric(19,7)", Order = 91)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetPesOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNetpesOc {
            get; set;
        }

    
        /// <summary>
        /// AmountNetContractOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETCONTRACT_OC", TypeName = "numeric(19,7)", Order = 92)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetContractOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNetcontractOc {
            get; set;
        }

    
        /// <summary>
        /// AmountNetOtherOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETOTHER_OC", TypeName = "numeric(19,7)", Order = 93)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetOtherOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNetotherOc {
            get; set;
        }

    
        /// <summary>
        /// AmountNetRejectOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_NETREJECT_OC", TypeName = "numeric(19,7)", Order = 94)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountNetRejectOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountNetrejectOc {
            get; set;
        }

    
        /// <summary>
        /// AmountVatPesOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATPES_OC", TypeName = "numeric(19,7)", Order = 95)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatPesOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountVatpesOc {
            get; set;
        }

    
        /// <summary>
        /// AmountVatContractOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATCONTRACT_OC", TypeName = "numeric(19,7)", Order = 96)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatContractOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountVatcontractOc {
            get; set;
        }

    
        /// <summary>
        /// AmountVatOtherOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATOTHER_OC", TypeName = "numeric(19,7)", Order = 97)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatOtherOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountVatotherOc {
            get; set;
        }

    
        /// <summary>
        /// AmountVatRejectOc
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_VATREJECT_OC", TypeName = "numeric(19,7)", Order = 98)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AmountVatRejectOc")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountVatrejectOc {
            get; set;
        }

    
        /// <summary>
        /// DateDelivered
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERED", TypeName = "date", Order = 99)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDelivered")]
        public virtual System.DateTime? DateDelivered {
            get; set;
        }

    
        /// <summary>
        /// DateDeliveredFrom
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVREDFROM", TypeName = "date", Order = 100)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDeliveredFrom")]
        public virtual System.DateTime? DateDelivredfrom {
            get; set;
        }

    
        /// <summary>
        /// ContractTotal
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONTRACT_TOTAL", TypeName = "numeric(19,7)", Order = 101)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContractTotal")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal ContractTotal {
            get; set;
        }

    
        /// <summary>
        /// ContractChangeOrder
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONTRACT_CHANGEORDER", TypeName = "numeric(19,7)", Order = 102)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContractChangeOrder")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal ContractChangeorder {
            get; set;
        }

    
        /// <summary>
        /// TotalPerformedNet
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TOTAL_PERFORMED_NET", TypeName = "numeric(19,7)", Order = 103)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TotalPerformedNet")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal TotalPerformedNet {
            get; set;
        }

    
        /// <summary>
        /// TotalPerformedGross
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TOTAL_PERFORMED_GROSS", TypeName = "numeric(19,7)", Order = 104)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TotalPerformedGross")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal TotalPerformedGross {
            get; set;
        }

    
        /// <summary>
        /// UserDefinedMoney01
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY01", TypeName = "numeric(19,7)", Order = 105)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney01")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal UserDefinedMoney01 {
            get; set;
        }

    
        /// <summary>
        /// UserDefinedMoney02
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY02", TypeName = "numeric(19,7)", Order = 106)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney02")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal UserDefinedMoney02 {
            get; set;
        }

    
        /// <summary>
        /// UserDefinedMoney03
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY03", TypeName = "numeric(19,7)", Order = 107)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney03")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal UserDefinedMoney03 {
            get; set;
        }

    
        /// <summary>
        /// UserDefinedMoney04
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY04", TypeName = "numeric(19,7)", Order = 108)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney04")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal UserDefinedMoney04 {
            get; set;
        }

    
        /// <summary>
        /// UserDefinedMoney05
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDMONEY05", TypeName = "numeric(19,7)", Order = 109)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedMoney05")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal UserDefinedMoney05 {
            get; set;
        }

    
        /// <summary>
        /// UserDefinedDate01
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE01", TypeName = "date", Order = 110)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate01")]
        public virtual System.DateTime? UserDefinedDate01 {
            get; set;
        }

    
        /// <summary>
        /// UserDefinedDate02
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE02", TypeName = "date", Order = 111)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate02")]
        public virtual System.DateTime? UserDefinedDate02 {
            get; set;
        }

    
        /// <summary>
        /// UserDefinedDate03
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE03", TypeName = "date", Order = 112)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate03")]
        public virtual System.DateTime? UserDefinedDate03 {
            get; set;
        }

    
        /// <summary>
        /// PaymentHint
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PAYMENT_HINT", TypeName = "nvarchar(3)", Order = 113)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentHint")]
        [System.ComponentModel.DataAnnotations.StringLength(3)]
        public virtual string PaymentHint {
            get; set;
        }

    
        /// <summary>
        /// ProgressId
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROGRESSID", TypeName = "int", Order = 114)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProgressId")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Progressid {
            get; set;
        }

    
        /// <summary>
        /// Id of the companydeferaltype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_DEFERALTYPE_ID", TypeName = "int", Order = 115)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyDeferalTypeFk")]
        public virtual int? CompanyDeferaltypeId {
            get; set;
        }

    
        /// <summary>
        /// Description of companydeferaltype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_DEFERALTYPE_DESC", TypeName = "nvarchar(2000)", Order = 116)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string CompanyDeferaltypeDescription {
            get; set;
        }

    
        /// <summary>
        /// DateDeferalStart
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DEFERALSTART", TypeName = "date", Order = 122)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDeferalStart")]
        public virtual System.DateTime? DateDeferalstart {
            get; set;
        }

    
        /// <summary>
        /// Id of the vatgroup
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 123)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public virtual int? VatgroupId {
            get; set;
        }

    
        /// <summary>
        /// Description of vatgroup
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 124)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string VatgroupDescription {
            get; set;
        }

    
        /// <summary>
        /// ReferenceStructured
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REFERENCE_STRUCTURED", TypeName = "nvarchar(252)", Order = 125)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReferenceStructured")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ReferenceStructured {
            get; set;
        }

    
        /// <summary>
        /// Id of the paymentmethod
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENTMETHOD_ID", TypeName = "int", Order = 126)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasPaymentMethodFk")]
        public virtual int? BasPaymentmethodId {
            get; set;
        }

    
        /// <summary>
        /// Description of paymentmethod
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENTMETHOD_DESC", TypeName = "nvarchar(2000)", Order = 127)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasPaymentmethodDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the banktype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_TYPE_ID", TypeName = "int", Order = 128)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdBankTypeFk")]
        public virtual int? BankTypeId {
            get; set;
        }

    
        /// <summary>
        /// Description of banktype
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 129)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BankTypeDescription {
            get; set;
        }

    
        /// <summary>
        /// Id of the bank
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_ID", TypeName = "int", Order = 130)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BankFk")]
        public virtual int? BankId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 131)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int LanguageId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignBusinessId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_BUSINESS_ID", TypeName = "int", Order = 132)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignBusinessFk")]
        public virtual int? BasAccassignBusinessId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignBusinessDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_BUSINESS_DESC", TypeName = "nvarchar(2000)", Order = 133)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasAccassignBusinessDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignControlId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CONTROL_ID", TypeName = "int", Order = 134)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignControlFk")]
        public virtual int? BasAccassignControlId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignControlDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CONTROL_DESC", TypeName = "nvarchar(2000)", Order = 135)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasAccassignControlDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignAccountId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_ACCOUNT_ID", TypeName = "int", Order = 136)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignAccountFk")]
        public virtual int? BasAccassignAccountId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignAccountDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_ACCOUNT_DESC", TypeName = "nvarchar(2000)", Order = 137)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasAccassignAccountDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 139)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int SalesTaxMethodId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 138)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string SalesTaxMethodDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdContactId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_ID", TypeName = "int", Order = 140)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContactFk")]
        public virtual int? BpdContactId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignConTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_ID", TypeName = "int", Order = 141)]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignConTypeFk")]
        public virtual int? BasAccassignConTypeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignConTypeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_CODE", TypeName = "nvarchar(16)", Order = 142)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string BasAccassignConTypeCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignConTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 143)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasAccassignConTypeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdContactDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_DESC", TypeName = "nvarchar(505)", Order = 144)]
        [System.ComponentModel.DataAnnotations.StringLength(505)]
        public virtual string BpdContactDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinessPostingGroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPOSTINGGROUP_ID", TypeName = "int", Order = 145)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPostingGroupFk")]
        public virtual int? BusinessPostingGroupId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinessPostingGroupDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPOSTINGGROUP_DESC", TypeName = "nvarchar(2000)", Order = 146)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BusinessPostingGroupDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RejectionRemark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REJECTION_REMARK", TypeName = "nvarchar(2000)", Order = 147)]
        [RIB.Visual.Platform.Common.InternalApiField("RejectionRemark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string RejectionRemark {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 148)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual int? BasLanguageFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 149)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BasLanguageDesc {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            InvHeaderApiEntity obj = new InvHeaderApiEntity();
            obj.Id = Id;
            obj.InvStatusId = InvStatusId;
            obj.InvStatusDescription = InvStatusDescription;
            obj.CompanyId = CompanyId;
            obj.CompanyCode = CompanyCode;
            obj.RubricCategoryId = RubricCategoryId;
            obj.RubricCategoryDescription = RubricCategoryDescription;
            obj.PrcConfigurationId = PrcConfigurationId;
            obj.PrcConfigurationDescription = PrcConfigurationDescription;
            obj.Code = Code;
            obj.Description = Description;
            obj.BusinesspartnerId = BusinesspartnerId;
            obj.BusinesspartnerDescription = BusinesspartnerDescription;
            obj.SubsidiaryId = SubsidiaryId;
            obj.SubsidiaryDescription = SubsidiaryDescription;
            obj.SupplierId = SupplierId;
            obj.SupplierCode = SupplierCode;
            obj.SupplierDescription = SupplierDescription;
            obj.InvTypeId = InvTypeId;
            obj.InvTypeDescription = InvTypeDescription;
            obj.InvGroupId = InvGroupId;
            obj.InvGroupDescription = InvGroupDescription;
            obj.ClerkPrcId = ClerkPrcId;
            obj.ClerkPrcCode = ClerkPrcCode;
            obj.ClerkPrcDescription = ClerkPrcDescription;
            obj.ClerkReqId = ClerkReqId;
            obj.ClerkReqCode = ClerkReqCode;
            obj.ClerkReqDescription = ClerkReqDescription;
            obj.ClerkWfeId = ClerkWfeId;
            obj.ClerkWfeCode = ClerkWfeCode;
            obj.ClerkWfeDescription = ClerkWfeDescription;
            obj.CurrencyId = CurrencyId;
            obj.CurrencyDescription = CurrencyDescription;
            obj.AmountNet = AmountNet;
            obj.AmountNetOc = AmountNetOc;
            obj.AmountGross = AmountGross;
            obj.AmountGrossOc = AmountGrossOc;
            obj.Exchangerate = Exchangerate;
            obj.TaxCodeId = TaxCodeId;
            obj.TaxCodeCode = TaxCodeCode;
            obj.TaxCodeDescription = TaxCodeDescription;
            obj.DateInvoiced = DateInvoiced;
            obj.Reference = Reference;
            obj.DateReceived = DateReceived;
            obj.DatePosted = DatePosted;
            obj.ProjectId = ProjectId;
            obj.ProjectCode = ProjectCode;
            obj.ProjectDescription = ProjectDescription;
            obj.PackageId = PackageId;
            obj.PackageCode = PackageCode;
            obj.PackageDescription = PackageDescription;
            obj.MdcControllingunitId = MdcControllingunitId;
            obj.MdcControllingunitCode = MdcControllingunitCode;
            obj.MdcControllingunitDescription = MdcControllingunitDescription;
            obj.PrcStructureId = PrcStructureId;
            obj.PrcStructureCode = PrcStructureCode;
            obj.PrcStructureDescription = PrcStructureDescription;
            obj.Reconcilationhint = Reconcilationhint;
            obj.ConHeaderId = ConHeaderId;
            obj.ConHeaderCode = ConHeaderCode;
            obj.ConHeaderDescription = ConHeaderDescription;
            obj.PesHeaderId = PesHeaderId;
            obj.PesHeaderCode = PesHeaderCode;
            obj.PesHeaderDescription = PesHeaderDescription;
            obj.PaymentTermId = PaymentTermId;
            obj.PaymentTermCode = PaymentTermCode;
            obj.PaymentTermDescription = PaymentTermDescription;
            obj.DateDiscount = DateDiscount;
            obj.AmountDiscountbasis = AmountDiscountbasis;
            obj.AmountDiscountbasisOc = AmountDiscountbasisOc;
            obj.PercentDiscount = PercentDiscount;
            obj.AmountDiscount = AmountDiscount;
            obj.AmountDiscountOc = AmountDiscountOc;
            obj.DateNetpayable = DateNetpayable;
            obj.AmountNetpes = AmountNetpes;
            obj.AmountVatpes = AmountVatpes;
            obj.AmountNetcontract = AmountNetcontract;
            obj.AmountVatcontract = AmountVatcontract;
            obj.AmountNetother = AmountNetother;
            obj.AmountVatother = AmountVatother;
            obj.AmountNetreject = AmountNetreject;
            obj.AmountVatreject = AmountVatreject;
            obj.Remark = Remark;
            obj.UserDefined1 = UserDefined1;
            obj.UserDefined2 = UserDefined2;
            obj.UserDefined3 = UserDefined3;
            obj.UserDefined4 = UserDefined4;
            obj.UserDefined5 = UserDefined5;
            obj.SearchPattern = SearchPattern;
            obj.MdcBillingSchemaId = MdcBillingSchemaId;
            obj.MdcBillingSchemaDescription = MdcBillingSchemaDescription;
            obj.AmountNetpesOc = AmountNetpesOc;
            obj.AmountNetcontractOc = AmountNetcontractOc;
            obj.AmountNetotherOc = AmountNetotherOc;
            obj.AmountNetrejectOc = AmountNetrejectOc;
            obj.AmountVatpesOc = AmountVatpesOc;
            obj.AmountVatcontractOc = AmountVatcontractOc;
            obj.AmountVatotherOc = AmountVatotherOc;
            obj.AmountVatrejectOc = AmountVatrejectOc;
            obj.DateDelivered = DateDelivered;
            obj.DateDelivredfrom = DateDelivredfrom;
            obj.ContractTotal = ContractTotal;
            obj.ContractChangeorder = ContractChangeorder;
            obj.TotalPerformedNet = TotalPerformedNet;
            obj.TotalPerformedGross = TotalPerformedGross;
            obj.UserDefinedMoney01 = UserDefinedMoney01;
            obj.UserDefinedMoney02 = UserDefinedMoney02;
            obj.UserDefinedMoney03 = UserDefinedMoney03;
            obj.UserDefinedMoney04 = UserDefinedMoney04;
            obj.UserDefinedMoney05 = UserDefinedMoney05;
            obj.UserDefinedDate01 = UserDefinedDate01;
            obj.UserDefinedDate02 = UserDefinedDate02;
            obj.UserDefinedDate03 = UserDefinedDate03;
            obj.PaymentHint = PaymentHint;
            obj.Progressid = Progressid;
            obj.CompanyDeferaltypeId = CompanyDeferaltypeId;
            obj.CompanyDeferaltypeDescription = CompanyDeferaltypeDescription;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            obj.DateDeferalstart = DateDeferalstart;
            obj.VatgroupId = VatgroupId;
            obj.VatgroupDescription = VatgroupDescription;
            obj.ReferenceStructured = ReferenceStructured;
            obj.BasPaymentmethodId = BasPaymentmethodId;
            obj.BasPaymentmethodDescription = BasPaymentmethodDescription;
            obj.BankTypeId = BankTypeId;
            obj.BankTypeDescription = BankTypeDescription;
            obj.BankId = BankId;
            obj.LanguageId = LanguageId;
            obj.BasAccassignBusinessId = BasAccassignBusinessId;
            obj.BasAccassignBusinessDescription = BasAccassignBusinessDescription;
            obj.BasAccassignControlId = BasAccassignControlId;
            obj.BasAccassignControlDescription = BasAccassignControlDescription;
            obj.BasAccassignAccountId = BasAccassignAccountId;
            obj.BasAccassignAccountDescription = BasAccassignAccountDescription;
            obj.SalesTaxMethodId = SalesTaxMethodId;
            obj.SalesTaxMethodDesc = SalesTaxMethodDesc;
            obj.BpdContactId = BpdContactId;
            obj.BasAccassignConTypeId = BasAccassignConTypeId;
            obj.BasAccassignConTypeCode = BasAccassignConTypeCode;
            obj.BasAccassignConTypeDescription = BasAccassignConTypeDescription;
            obj.BpdContactDesc = BpdContactDesc;
            obj.BusinessPostingGroupId = BusinessPostingGroupId;
            obj.BusinessPostingGroupDesc = BusinessPostingGroupDesc;
            obj.RejectionRemark = RejectionRemark;
            obj.BasLanguageFk = BasLanguageFk;
            obj.BasLanguageDesc = BasLanguageDesc;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(InvHeaderApiEntity clonedEntity);

    }


}
