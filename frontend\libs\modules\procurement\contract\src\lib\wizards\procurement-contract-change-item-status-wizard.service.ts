/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable } from '@angular/core';
import { ProcurementContractItemDataService } from '../services/procurement-contract-item-data.service';
import { ProcurementContractHeaderDataService } from '../services/procurement-contract-header-data.service';
import { IConHeaderEntity, IConItemEntity } from '../model/entities';
import { ContractComplete } from '../model/contract-complete.class';
import { ProcurementCommonChangeItemStatusWizardService } from '@libs/procurement/common';

@Injectable({
	providedIn: 'root',
})
/**
 * Change Status for Item wizard service
 */
export class ProcurementContractChangeItemStatusWizardService extends ProcurementCommonChangeItemStatusWizardService<IConItemEntity, IConHeaderEntity, ContractComplete> {
	public constructor(mainService: ProcurementContractHeaderDataService, dataService: ProcurementContractItemDataService) {
		super(mainService, dataService);
	}
}
