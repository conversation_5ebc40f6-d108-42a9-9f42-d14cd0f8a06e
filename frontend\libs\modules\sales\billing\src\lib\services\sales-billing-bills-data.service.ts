/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { DataServiceFlatRoot, ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, IDataServiceRoleOptions } from '@libs/platform/data-access';
import { IBilHeaderEntity } from '@libs/sales/interfaces';
import { BilHeaderComplete } from '../model/complete-class/bil-header-complete.class';
import { SalesCommonNumberGenerationService } from '@libs/sales/common';
import { PlatformTranslateService } from '@libs/platform/common';

@Injectable({
	providedIn: 'root'
})
/**
 * Sales billing bills data service
 */
export class SalesBillingBillsDataService extends DataServiceFlatRoot<IBilHeaderEntity, BilHeaderComplete> {
	private readonly numberGenerationService = inject(SalesCommonNumberGenerationService);
	private readonly translateService = inject(PlatformTranslateService);

	public constructor() {
		const options: IDataServiceOptions<IBilHeaderEntity> = {
			apiUrl: 'sales/billing',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'listfiltered',
				usePost: true
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'delete'
			},
			roleInfo: <IDataServiceRoleOptions<IBilHeaderEntity>>{
				role: ServiceRole.Root,
				itemName: 'BilHeader',
			}
		};

		super(options);
	}

	protected override onCreateSucceeded(created: object): IBilHeaderEntity {
		const billingComplete = created as BilHeaderComplete;

		if (billingComplete.BilHeader && billingComplete.BilHeader.length > 0) {
			const bill = billingComplete.BilHeader[0];

			if (bill && bill.RubricCategoryFk) {
				const rubricCategoryId = bill.RubricCategoryFk;

				// Set the BillNo field to "IsGenerated" if it should be auto-generated
				if (this.numberGenerationService.hasToGenerateForRubricCategory('billing', rubricCategoryId)) {
					bill.BillNo = this.translateService.instant({ key: 'cloud.common.isGenerated' }).text;
				}

				// Set the ConsecutiveBillNo field to "IsGenerated" if it should be auto-generated
				const consecutiveBillNoRubricIndex = 10; // ConsecutiveBillNo index from SalesBillingRubricIndexEnum
				if (this.numberGenerationService.hasToGenerateForRubricCategory('billing', rubricCategoryId, consecutiveBillNoRubricIndex)) {
					bill.ConsecutiveBillNo = this.translateService.instant({ key: 'cloud.common.isGenerated' }).text;
				}
			}

			return bill;
		}

		return created as unknown as IBilHeaderEntity;
	}

	public override createUpdateEntity(modified: IBilHeaderEntity | null): BilHeaderComplete {
		const complete = new BilHeaderComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.BilHeader = [modified];
		}
		return complete;
	}


	public override getModificationsFromUpdate(complete: BilHeaderComplete): IBilHeaderEntity[] {
		if (complete.BilHeader) {
			return complete.BilHeader;
		}
		return [];
	}

}