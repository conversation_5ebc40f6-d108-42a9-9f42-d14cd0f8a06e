namespace AppLayout {
	export enum Layouts {
		MAIN_CONTAINER = "mainContainer",
		SUB_VIEW_HEADER_TOOLBAR = "subview-header toolbar",
		TOOLBAR_WH_ICONS = "toolbar-wh-icons",
		SUB_VIEW_TABS = ".subview-tabs",
		INDICATOR_UI_SORTABLE_HANDLE = "indicator ui-sortable-handle",
		ICO_COPY_PASTE_DEEP = "ico-copy-paste-deep",
		SPLIT_VIEW_HEADER_TOOLBAR = "splitview-header toolbar",
		SUB_VIEW_CONTENT = "subview-content",
		FILTER_PANEL="filterPanel",
		MODAL_CONTENT="modal-content"
	}

	export enum GridCellIcons {
		ICO_FOLDER_DOC = "ico-folder-doc",
		ICO_BOQ_ITEM = "ico-boq-item",
		ICO_FOLDER_EMPTY = "ico-folder-empty",
		ICO_ROOT_SCHEDULING = "ico-root-scheduling",
		CONTROL_ICONS_ICO_TASK = "control-icons ico-task",
		ICO_FOLDER_ESTIMATE = "ico-folder-estimate",
		ICONS_ICO_MENU = "icons ico-menu",
		ICO_BASE_LINE = "ico-base-line",
		ICO_RUBRIC_CATEGORY = "ico-rubric-category",
		ICO_INPUT_DELETE = "ico-input-delete",
		ICO_RUBRIC_CONTRACTS = "ico-rubric-contracts",
		ICO_RUBRIC_CUSTOMER_BILLING = "ico-rubric-customer-billing",
		ICO_RUBRIC_PES = "ico-rubric-pes",
		ICO_TREE_EXPAND = "ico-tree-expand",
		ICO_FO_REQUISITION_TOTAL = "ico-fo-requisition-total",
		BLOCK_IMAGE = "block-image",
		ICO_COMPARE_FIELDS = "ico-compare-fields",
		ICO_DOMAIN_QUANTITY = "ico-domain-quantity",
		ICO_DOMAIN_MONEY = "ico-domain-money",
		ICO_STATUS_07 = "ico-status07",
		ICO_STATUS_06 = "ico-status06",
		ICO_FOLDER_OVERLEY_1 = "ico-folder-overlay1",
		ICO_MILESTONE = "ico-milestone",
		ICO_LOCATION_GROUP = "ico-location-group",
		ICO_LOCATION2 = "ico-location2",
		ICO_HAMMOCK = "ico-hammock",
		ICO_SUB_TASK = "ico-task-sub",
		ICO_TASK_SUMMARY = "ico-task-summary",
		ICO_TASK = "ico-task",
		ICO_MENU = "ico-menu",
		ICO_REFRESH = "ico-refresh",
		ICO_DIALOG = "ico-dialog",
		ICO_INDICATOR_SEARCH = "ico-indicator-search",
		ICO_CONTROLLING_UNIT1 = "ico-controlling-unit1",
		ICO_CONTROLLING_UNIT2 = "ico-controlling-unit2",
		ICO_CALCULATOR = "ico-calculator",
		ICO_RES_TYPE_C = "ico-res-type-c",
		ICO_RES_TYPE_P = "ico-res-type-p",
		ICO_PARAMETER = "ico-parameter",
		ICO_EST_PARAMETER = "ico-est-parameter",
		ICO_COPY_ACTION_1_2="ico-copy-action1-2",
		ICO_TICK="ico-tick",
		ICO_TREE_COLLAPSE="ico-tree-collapse",
		ICO_TREE_COLLAPSE_ALL="ico-tree-collapse-all",
		ICO_TREE_EXPAND_ALL="ico-tree-expand-all",
		ICO_SOURCE_LINE="ico-source-line",
		ICO_REFERENCE_LINE="ico-reference-line",
		ICO_INDICATOR1_0="ico-indicator1-0",
		ICO_PRICE_UPDATE="ico-price-update",
	}

	export enum SubContainerLayout {
		COLUMN_ID = "column-id",
		INDICATOR = "indicator",
		IS_INCLUDED = "IsIncluded",
		ITEM_FIELD = "item-field_",
		SELECTED = "Selected",
		ACTIVE = "active",
		CUSTOM_GROUP="custom-group",
		SLICK_HEADER_COLUMN="slick-header-column"
	}

	export enum RowInputClass {
		PLATFORM_FORM_GROUP = "platform-form-group ",
		COMMODITY_ROW = "commodity-row",
		CART_ITEM_SUBTOTAL = "cart-item-subtotal",
		CART_ITEM_TOTAL = "cart-item-total",
		COMMODITY_ROW_PRICE = "commodity-row-price",
		COMMODITY_ROW_DELIVERY_CONTENT = "commodity-row-delivery-content",
	}

	export enum GridCells {
		CODE = "code",
		IS_ACCEPTED = "isaccepted",
		ASSEMBLYPOPUP_CODE = "1",
		DESCRIPTION_INFO = "descriptioninfo",
		DESCRIPTION = "description",
		CLERK_FK_DESCRIPTION = "clerkfkdescription",
		RUBRIC_CATEGORY_FK = "rubriccategoryfk",
		EST_TYPE_FK = "esttypefk",
		QUANTITY_SMALL = "quantity",
		QUANTITY = "Quantity",
		BAS_UOM_FK = "basuomfk",
		UOM_FK = "uomfk",
		EST_RESOURCE_TYPE_SHORT_KEY = "estresourcetypeshortkey",
		EST_HEADER_FK = "estheaderfk",
		BUDGET = "budget",
		BRIEF_INFO_SMALL = "briefinfo",
		BRIEF_INFO = "BriefInfo",
		REFERENCE = "reference",
		BOQ_LINE_TYPE_FK = "boqlinetypefk",
		RATE = "rate",
		PRICE_SMALL = "price",
		QUANTITY_ADJ = "quantityadj",
		QUANTITY_TARGET = "quantitytarget",
		EST_QTY_REL_BOQ_FK = "estqtyrelboqfk",
		PRICE_GROSS = "pricegross",
		QUANTITY_UOM_FK = "quantityuomfk",
		PSD_ACTIVITY_FK = "psdactivityfk",
		ADJUST_COST_UNIT = "adjustcostunit",
		COST_UNIT_ORIGINAL = "costunitoriginal",
		BUDGET_UNIT = "budgetunit",
		IS_GC = "isgc ",
		COST_UNIT = "costunit",
		CONFIGURATION_FK = "configurationfk",
		IS_FIXED_PRICE = "isfixedprice",
		COST_GROUP_LIC_DIN276_2018_12 = "costgroup_lic_din276_2018-12",
		BOQ_ITEM_FK = "boqitemfk",
		BOQ_ITEM_FK_BIG = "boqItemFk",
		PRJ_LOCATION_FK = "prjlocationfk",
		PRC_STRUCTURE_FK = "prcstructurefk",
		LI_BILLED_QUANTITY = "libilledquantity",
		RECORDING_LEVEL = "recordinglevel",
		CUSTOMER_FK = "customerfk",
		FINAL_PRICE_SMALL = "finalprice",
		NAME = "name",
		MODULE_NAME = "modulename",
		IS_LUMP_SUM = "islumpsum",
		PLANNED_FINISH = "plannedfinish",
		BUSINESS_PARTNER_FK = "businesspartnerfk",
		BP_NAME = "bpName",
		REMAINING_QUANTITY = "remainingquantity",
		PLANNED_QUANTITY = "plannedquantity",
		REMAINING_PCO = "remainingpco",
		EST_STRUCTURE = "estStructure",
		ICO_BOQ_ITEM = "ico-boq-item",
		PROJECT_NO = "projectno",
		LI_QUANTITY = "liquantity",
		CONTROLLING_UNIT_FK = "controllingunitfk",
		COST_GROUP = "costgroup",
		BUDGET_TOTAL = "budgettotal",
		EST_QTY_TELAOT_FK = "estqtytelaotfk",
		PRJ_CHANGE_FK = "prjchangefk",
		WQ_QUANTITY_TARGET = "wqquantitytarget",
		PRC_CONFIG_HEADER_TYPE_FK = "prcconfigheadertypefk",
		AUTO_CREATE_BOQ = "autocreateboq",
		BAS_CONFIGURATION_TYPE_FK = "basconfigurationtypefk",
		BILLING_SCHEMA_FK = "billingschemafk",
		PAYMENT_TERM_FI_FK = "paymenttermfifk",
		AMOUNT_NET = "amountnet",
		GRAND_TOTAL = "grandtotal",
		EST_QTY_REL_ACT_FK = "estqtyrelactfk",
		VALUE_1_DETAIL = "value1detail",
		VALUE_DETAIL = "valuedetail",
		BTN_DEFAULT = "btn-default",
		BIL_AMOUNT_NET = "bilamountnet",
		PAYMENT_BALANCE_NET = "paymentbalancenet",
		BIL_HEADER_FK = "bilheaderfk",
		LUMP_SUM_PRICE = "lumpsumprice",
		PREV_QUANTITY = "PrevQuantity",
		PREV_QUANTITY_SMALL = "prevquantity",
		BILL_NO = "billno",
		AMOUNT_SMALL = "amount",
		COST_TOTAL = "costtotal",
		PACKAGE_ASSIGNMENTS = "packageassignments",
		REVENUE = "revenue",
		PRC_PACKAGE_FK = "prcpackagefk",
		TOTAL_TYPE_FK_DESCRIPTION_INFO = "TotalTypeFkDescriptionInfo",
		TRANSLATED = "Translated",
		VALUE_NET = "valuenet",
		ITEM_NO = "itemno",
		EST_ASSEMBLY_FK = "estassemblyfk",
		HINT = "hint",
		FINAL_GROSS = "finalgross",
		TOTAL = "total",
		BASE_COST_TOTAL = "basecosttotal",
		BASE_COST_UNIT = "basecostunit",
		QUANTITY_TOTAL = "quantitytotal",
		QUANTITY_FACTOR_1 = "quantityfactor1",
		QUANTITY_FACTOR_2 = "quantityfactor2",
		QUANTITY_FACTOR_4 = "quantityfactor4",
		QUANTITY_FACTOR_CC = "quantityfactorcc",
		COST_FACTOR_1 = "costfactor1",
		COST_FACTOR_2 = "costfactor2",
		COST_FACTOR_CC = "costfactorcc",
		PRODUCTIVITY_FACTOR = "productivityfactor",
		WIC_BOQ_ITEM_FK = "wicboqitemfk",
		VALUE_TAX = "valuetax",
		GROSS = "gross",
		BP_NAME_1 = "bpName1",
		COMPARE_DESCRIPTION = "compareDescription",
		QUANTITY_SUMMARY = "quantitysummary",
		QUANTITY_UNIT_TARGET = "quantityunittarget",
		QUANTITY_FACTOR_3 = "quantityfactor3",
		COLUMN_INDICATOR = "column indicator",
		BOQ_ROOT_ITEM_BRIEF_INFO = "boqrootitem.briefinfo",
		BRIEF = "brief",
		BOQ_ROUNDING_COLUMN_ID = "boqRoundingColumnId",
		UI_DISPLAY_TO = "uiDisplayTo",
		IS_WITHOUT_RONDING = "isWithoutRonding",
		ROUND_TO = "roundTo",
		BOQ_ITEM_FK_BRIEF = "boqitemfkbrief",
		EST_ROUNDING_COLUMN_ID = "estRoundingColumnId",
		PRC_PACKAGE_FK_DESCRIPTION = "PrcPackageFkDescription",
		BOQ_ROOT_ITEM = "boqrootitem",
		IS_LABOUR = "isLabour",
		IS_BOLD = "isBold",
		IS_ITALIC = "isItalic",
		IS_UNDERLINE = "isUnderLine",
		EST_MDC_COST_CODE_FK = "estMdcCostCodeFk",
		TYPE = "Type",
		CON_TYPE_FK = "contypefk",
		MATERIAL_DESCRIPTION = "MaterialDescription",
		MATERIAL_CODE = "MaterialCode",
		IS_VISIBLE = "isvisible",
		IS_PROJECT_CATALOG = "isprojectcatalog",
		IS_CHECKED = "IsChecked",
		INVALID_CELL = "invalid-cell",
		QUANTITY_REL = "quantityRel",
		INDICATOR_ACTIVE = "indicator active",
		PAYMENT_TERM_AD_FK = "paymenttermadfk",
		COST_GROUP_CATALOG_FK = "costgroupcatalogfk",
		SORTING = "sorting",
		MATERIAL_CATALOG_TYPE_FK = "materialcatalogtypefk",
		VALID_FROM = "validfrom",
		VALID_TO = "validto",
		BUDGET_PER_UNIT = "budgetperunit",
		PACKAGE_STATUS_FK = "packagestatusfk",
		BUSINESS_PARTNER_NAME_1 = "businesspartnername1",
		MATERIAL_CATALOG_FK = "materialcatalogfk",
		WQ_BUDGET = "wqbudget",
		AQ_BUDGET = "aqbudget",
		MARKUP_GA = "markupga",
		MARKUP_RP = "markuprp",
		MARKUP_AM = "markupam",
		MASTER_CONTEXT_FK = "mastercontextfk",
		ALLOWANCE_SETTING = "AllowanceSetting",
		ESTIMATE_ALLOWANCE_SETTING = "EstimateAllowanceSetting",
		MASTER_DATA_CONTEXT_FK = "masterdatacontextfk",
		MDC_ALLOWANCE_FK = "MdcAllowanceFk",
		GA = "ga",
		RP = "rp",
		AM = "am",
		IS_ACTIVE = "isactive",
		CORRECTION = "correction",
		CONTACT_FIRST_NAME = "ContactFirstName",
		FIRST_NAME_CAPS = "FirstName",
		CONTACT_FK = "contactfk",
		FAMILY_NAME_CAPS = "FamilyName",
		PRC_CONTRACT_TYPE_FK = "prccontracttypefk",
		REF = "ref",
		BOQ_ROOT_REF = "boqrootref",
		ID = "Id",
		ID_SMALL = "id",
		CONTACT_FK_CAPS = "ContactFk",
		TITLE = "Title",
		TITLE_SMALL = "title",
		ORD_QUANTITY = "OrdQuantity",
		MDC_CONTROLLING_UNIT_FK = "MdcControllingUnitFk",
		ITEM_FIELD_PURCHASE_ORDERS = "item-field_PurchaseOrders",
		QUANTITY_TARGET_CAPS = "QuantityTarget",
		TEXT_CENTER = "text-center",
		MDC_CONTROLLING_UNIT_FK_SMALL = "mdccontrollingunitfk",
		MDC_CONTROLLING_UNIT_FK_DESCRIPTION = "mdccontrollingunitfkdescription",
		DESCRIPTION_1 = "description1",
		MDC_MATERIAL_FK = "mdcmaterialfk",
		PLANNED_END = "plannedend",
		PLANNED_START = "plannedstart",
		QUANTITY_REMAINING = "quantityremaining",
		CON_HEADER_FK = "conheaderfk",
		CON_HEADER_FK_CAPS = "ConHeaderFk",
		CONTRACT_TOTAL = "contractTotal",
		SUPPLIER_DESCRIPTION = "SupplierDescription",
		AMOUNT_GROSS = "amountgross",
		INSTALLED_QUANTITY = "installedquantity",
		ISA_ACCOUNTING_ELEMENT = "isaccountingelement",
		PLANNED_DURATION = "plannedduration",
		LOCATION = "location",
		QUANTITY_PERCENT = "QuantityPercent",
		QUANTITY_TOTAL_CAPS = "QuantityTotal",
		EST_LINE_ITEM_FK = "estlineitemfk",
		INFO = "info",
		COST_UNIT_TARGET = "costunittarget",
		RETAIL_PRICE = "retailprice",
		LIST_PRICE = "listprice",
		ESTIMATION_DESCRIPTION = "estimationdescription",
		PROJECT_NAME = "ProjectName",
		EST_QTY_REL_GT_UFK = "estqtyrelgtufk",
		WIC_EST_ASSEMBLY_2_WIC_FLAG_FK = "wicestassembly2wicflagfk",
		DESCRIPTION_CAPS = "Description",
		TREE = "tree",
		WIC_2_ASSEMBLY_QUANTITY = "wic2assemblyquantity",
		PES_VALUE = "pesvalue",
		NET = "net",
		BOQ_WIC_CAT_FK = "boqwiccatfk",
		BOQ_WIC_CAT_BOQ_FK = "boqwiccatboqfk",
		CODE_CAPS = "Code",
		PROJECT_NAME_SMALL = "projectname",
		CONFIGURATION = "Configuration",
		PROJECT_WIC_CODE = "projectWicCode",
		BOQ_HEADER_FK = "boqHeaderFk",
		EST_HEADER_ID = "estheaderid",
		EST_HEADER_DESCRIPTION = "estheaderdescription",
		BOQ_HEADER_FK_DESCRIPTION = "boqHeaderFkDescription",
		REQ_HEADER_FK = "reqheaderfk",
		STRUCTURE_FK = "structurefk",
		PRC_TEXT_TYPE_FK = "prctexttypefk",
		IS_RUBRIC_BASED = "isrubricbased",
		PRC_CONFIG_HEADER_FK = "prcconfigheaderfk",
		PRC_GENERALS_TYPE_FK = "prcgeneralstypefk",
		VALUE = "value",
		VALUE_TYPE = "valuetype",
		FAMILY_NAME = "familyname",
		FIRST_NAME = "firstname",
		CLERK_ROLE_FK = "clerkrolefk",
		CLERK_FK = "clerkfk",
		BPD_CERTIFICATE_TYPE_FK = "bpdcertificatetypefk",
		IS_REQUIRED = "isrequired",
		IS_MANDATORY = "ismandatory",
		CHARACTERISTIC_FK = "characteristicfk",
		PRC_STRUCTURE_TYPE_FK = "prcstructuretypefk",
		CONTEXT_FK = "contextfk",
		VALUE_TEXT = "valuetext",
		MDC_TAX_CODE_FK = "MdcTaxCodeFk",
		BAS_TEXT_MODULE_TYPE_FK = "bastextmoduletypefk",
		TEXT_MODULE_TYPE_FK = "textmoduletypefk",
		STRUCTURE = "structure",
		IS_ESTIMATE = "isestimate",
		DESCRIPTION_INFO_1 = "descriptioninfo1",
		SELECTED = "selected",
		SOURCE_CODE_DESC = "sourceCodeDesc",
		PACKAGE_CODE = "packageCode",
		PROJECT_FK_PROJECT_NAME = "projectfkprojectname",
		COPIED_PRICE = "CopiedPrice",
		PACKAGE_FK = "packagefk",
		PRICE_UNIT = "priceunit",
		CLERK_PRC_FK = "clerkprcfk",
		CALENDAR_FK = "calendarfk",
		CALENDAR_TYPE_FK = "calendartypefk",
		WORK_HOURS_PER_DAY = "workhoursperday",
		WORK_HOURS_PER_WEEK = "workhoursperweek",
		WORK_HOURS_PER_MONTH = "workhourspermonth",
		TOTAL_TYPE_FK = "totaltypefk",
		PURCHASE_ORDERS = "purchaseorders",
		IS_FIXED_BUDGET = "isfixedbudget",
		BUDGET_FIXED_TOTAL = "budgetfixedtotal",
		IS_FIXED_BUDGET_UNIT = "isfixedbudgetunit",
		BUDGET_FIXED_UNIT = "budgetfixedunit",
		CLERK_REQ_FK = "clerkreqfk",
		CHECKED = "checked",
		CHARACTERISTIC_TYPE_FK = "characteristictypefk",
		IS_SUPPLIER = "issupplier",
		MDC_TAX_CODE_FK_SMALL = "mdctaxcodefk",
		TAX_CODE_FK = "taxcodefk",
		SUBSIDIARY_FK = "subsidiaryfk",
		SUPPLIER_FK = "supplierfk",
		BPD_VAT_GROUP_FK = "bpdvatgroupfk",
		VAT_PERCENT = "vatpercent",
		BILLING_LINE_TYPE_FK = "billinglinetypefk",
		TAXCODE_FK = "taxcodefk",
		GENERALS_TYPE_FK = "generalstypefk",
		CRED_FACTOR = "credfactor",
		INV_TYPE_FK = "invtypefk",
		PRC_CONFIGURATION_FK = "prcconfigurationfk",
		CONFIGURATION_SMALL = "configuration",
		IS_FREE_QUANTITY = "isfreequantity",
		FIELD_NAME = "fieldName",
		MESSAGE = "message",
		CONTRACT_HEADER_FK = "contractheaderfk",
		ORD_QUANTITY_SMALL = "ordquantity",
		MIN_QUANTITY = "minquantity",
		SELL_UNIT = "sellunit",
		DISCOUNT_TYPE = "discounttype",
		DISCOUNT = "discount",
		LANG_NAME = "langname",
		COL_10 = "col0",
		REQ_STATUS_FK = "reqstatusfk",
		TOTAL_TYPE_DESCRIPTION_INFO_TRANSLATED = "TotalTypeFkDescriptionInfo.Translated",
		PAYMENT_TERM_PA_FK = "paymenttermpafk",
		BANK_FK = "bankfk",
		PRC_INCO_TERM_FK = "prcincotermfk",
		INCO_TERM_FK_DESCRIPTION = "IncotermFkDescription",
		VAT_GROUP_FK = "vatgroupfk",
		PRC_AWARD_METHOD_FK = "prcawardmethodfk",
		AWARD_METHOD_FK = "awardmethodfk",
		CONSTATUS_FK = "constatusfk",
		ORD_STATUS_FK = "ordstatusfk",
		PROJECT_FK = "projectfk",
		PES_HEADER_FK = "pesheaderfk",
		IS_SUCCESS = "issuccess",
		QUANTITY_TYPE_FK = "quantitytypefk",
		LINETYPE = "LineType",
		PROPERTY_INFO = "propertyinfo",
		HAS_FIXED_VALUES = "hasfixedvalues",
		IS_PERCENT = "ispercent",
		IS_COST = "iscost",
		IS_PROCUREMENT = "isprocurement",
		LEDGER_CONTEXT_FK = "ledgercontextfk",
		MATERIAL_PRICE = "MaterialPrice",
		IS_NEUTRAL = "isneutral",
		IS_TICKET_SYSTEM = "isticketsystem",
		IS_INTERNET_CATALOG = "IsInternetCatalog",
		PRICE = "Price",
		PACKAGE_NUMBER = "packagenumber",
		RFQ_HEADER_FK = "rfqheaderfk",
		REMARK = "remark",
		IS_DEFAULT = "isdefault",
		IS_LIVE = "islive",
		IS_FRAMEWORK = "isframework",
		CODE_FINANCE = "codefinance",
		NET_DAYS = "netdays",
		DISCOUNT_DAYS = "discountdays",
		DISCOUNT_PERCENT = "discountpercent",
		DAY_OF_MONTH = "dayofmonth",
		CALCULATION_TYPE_FK = "calculationtypefk",
		AMOUNT_DISCOUNT = "amountdiscount",
		AMOUNT_DISCOUNT_BASIS = "amountdiscountbasis",
		DATE_DISCOUNT = "datediscount",
		DATE_NET_PAYABLE = "datenetpayable",
		CURRENCY = "currency",
		CURRENCY_FOREIGN_FK = "currencyforeignfk",
		CURRENCY_FK = "currencyfk",
		BAS_CURRENCY_FK = "bascurrencyfk",
		PAYMENT_TERM_FK = "paymenttermfk",
		IS_PROGRESS = "isprogress",
		STRUCTURE_CODE = "structureCode",
		BANK_TYPE_FK = "banktypefk",
		BPD_BANK_TYPE_FK = "bpdbanktypefk",
		TOTAL_PERFORMED_GROSS = "totalperformedgross",
		AMOUNT_GROSS_OC = "amountgrossoc",
		AMOUNT_VAT_BALANCE = "amountVatBalance",
		TOTAL_PERFORMED_NET = "totalperformednet",
		AMOUNT_NET_OC = "amountnetoc",
		BUSINESS_PARTNER_FK_SMALL = "businessPartnerFk",
		IS_CHECKED_SMALL = "isChecked",
		WQ_ADJUSTMENT_PRICE = "wqadjustmentprice",
		RFQ_STATUS_FK = "rfqstatusfk",
		BAS_RUBRIC_CATEGORY_FK = "basrubriccategoryfk",
		NEUTRAL_MATERIAL_CATALOG_FK = "neutralmaterialcatalogfk",
		DATE_REQUESTED = "daterequested",
		PROGRESS_ID = "progressid",
		TOTAL_GROSS = "TotalGross",
		TOTAL_GROSS_SMALL = "totalgross",
		PRC_ITEM_FK = "prcitemfk",
		LABEL = "label",
		VISIBLE = "visible",
		REM_QUANTITY = "remquantity",
		CC_QUANTITY = "ccQuantity",
		IS_CONSOLIDATE_CHANGE = "isconsolidatechange",
		AQ_QUANTITY = "aqquantity",
		QA_ESTIMATED_PRICE = "aqestimatedprice",
		QA_ADJUSTMENT_PRICE = "aqadjustmentprice",
		PRC_MILESTONE_TYPE_FK = "prcmilestonetypefk",
		MILESTONE = "milestone",
		BPD_CONTACT_ROLE_FK = "bpdcontactrolefk",
		BPD_CONTACT_FK = "bpdcontactfk",
		BPD_BUSINESS_PARTNER_FK = "bpdbusinesspartnerfk",
		GUARANTEE_COST = "guaranteecost",
		COUNT = "count",
		COUNTRY_DESC = "countryDesc",
		NEW_STATUS = "newStatus",
		LINE_ITEM_CONTEXT_FK = "lineitemcontextfk",
		TOTAL_OC = "totaloc",
		TOTAL_PRICE = "totalprice",
		PRC_PRICE_CONDITION_FK = "prcpriceconditionfk",
		PRICE_EXTRA = "priceextra",
		PRICE_OC = "priceoc",
		PRICE_EXTRA_OC = "priceextraoc",
		TOTAL_PRICE_OC = "totalpriceoc",
		RESULT = "result",
		IS_VALUED = "isvalued",
		REQUIRED_AMOUNT = "requiredamount",
		STATUS_FK = "statusfk",
		AGN = "agn",
		AAN = "aan",
		BAS_ITEM_TYPE_2_FK = "basitemtype2fk",
		BPD_CONTACT_FIRST_NAME = "BpdContactFirstName",
		LGM_JOB_FK = "lgmjobfk",
		SPECIFICATION_INFO = "specificationinfo",
		SPECIFICATION = "specification",
		BAS_UOM_PRICE_UNIT_FK = "basuompriceunitfk",
		FACTOR_PRICE_UNIT = "factorpriceunit",
		MDC_MATERIAL_STOCK_FK = "mdcmaterialstockfk",
		QL_EDITOR = "ql-editor",
		PRICE_LIST_FK = "pricelistfk",
		MATERIAL_PRICE_VERSION_FK = "materialpriceversionfk",
		CHARACTERISTIC_INFO = "characteristicinfo",
		PROPERTY_DESCRIPTION = "propertydescription",
		CHARACTERISTIC_DESCRIPTION = "characteristicdescription",
		PRC_COPY_MODE_FK = "prccopymodefk",
		COPY_TYPE = "copytype",
		MDC_MATERIAL_CATALOG_FK = "mdcmaterialcatalogfk",
		CONTACT_ROLE_TYPE_FK = "contactroletypefk",
		BPD_CONTACT_SUBSIBIARY = "BpdContactSubsidiary",
		QUOTE_VERSION = "quoteversion",
		PRC_ITEM_STATUS_FK = "prcitemstatusfk",
		MDC_LEDGER_CONTEXT_FK = "mdcledgercontextfk",
		IS_MAIN_ADDRESS = "ismainaddress",
		ADDRESS_TYPE_FK = "addresstypefk",
		ADDRESSD_TO = "addressdto",
		CONTACT_ROLE_FK = "contactrolefk",
		DATE_REQUIRED = "daterequired",
		TIME_REQUIRED = "timerequired",
		CODE_QUOTATION = "codequotation",
		RELATED_BILL_HEADER_FK = "relatedbillheaderfk",
		TOTAL_QUANTITY = "totalquantity",
		DOCUMENT_TYPE = "documenttype",
		QUANTITY_ADJ_CAPS = "QuantityAdj",
		FINAL_PRICE = "Finalprice",
		BOQ_ITEM_CODE = "boqitemcode",
		ABBREVIATION = "abbreviation",
		TEXT_FORMAT_FK = "textformatfk",
		EXCHANGE_RATE = "exchangerate",
		BAS_COST_CODE = "bascostcode",
		TAX_NO = "taxno",
		BAS_PAYMENT_TERM_FI_FK = "baspaymenttermfifk",
		BAS_PAYMENT_TERM_PA_FK = "baspaymenttermpafk",
		INCO_TERM_FK = "incotermfk",
		BAS_PAYMENT_TERM_AD_FK = "baspaymenttermadfk",
		BAS_PAYMENT_TERM_FK = "baspaymenttermfk",
		BPD_SUPPLIER_FK = "bpdsupplierfk",
		BPD_SUBSIDIARY_FK = "bpdsubsidiaryfk",
		PROJECT_NAME_2 = "projectname2",
		BOQ_LINE_TYPE = "boqLineType",
		CHARGES = "charges",
		ESTIMATE_PRICE = "estimateprice",
		CONTRACT_TOTAL_GROSS = "contractTotalGross",
		RECON_NAME = "ReconName",
		RECON_NET = "ReconNet",
		COL_1 = "col1",
		BP_NAME_1_SMALL = "bpname1",
		IS_READONLY = "isreadonly",
		QTN_VERSION = "qtnversion",
		SUPPLIER_CODE = "suppliercode",
		REQ_STATUS = "reqStatus",
		TOTAL_NO_DISCOUNT = "totalnodiscount",
		QTOE_COL_1000037_1000007_1 = "QuoteCol_1000037_1000007_1",
		QTOE_COL_1000038_10000064_1 = "QuoteCol_1000038_1000064_1",
		VAT = "vat",
		RECON_GROSS_CO = "ReconGrossOc",
		RECON_VAT = "ReconVat",
		RECON_GROSS = "ReconGross",
		STRUCTURE_DESCRIPTION = "structureDescription",
		PES_VAT = "pesvat",
		IS_SALES = "issales",
		COST = "cost",
		RATE_DATE = "ratedate",
		BAS_ITEM_TYPE_FK = "basitemtypefk",
		HAS_TEXT = "hastext",
		HAS_DELIVERY_SCHEDULE = "hasdeliveryschedule",
		HAS_SCOPE = "hasscope",
		PRC_PRICE_CONDITION_TYPE_FK = "prcpriceconditiontypefk",
		CONDITION_TOTAL = "conditionTotal",
		MAT_SCOPE = "matscope",
		SCOPE_OF_SUPPLY_TYPE_FK = "scopeofsupplytypefk",
		IS_STOCK_MANAGEMENT = "isstockmanagement",
		STOCK_LOCATION_FK = "stocklocationfk",
		MATERIAL_FK = "materialfk",
		PROVISION_PERCENT = "provisionpercent",
		PRJ_STOCK_FK = "prjstockfk",
		IS_STOCK_EXCLUDED = "isstockexcluded",
		UNIT_RATE = "unitRate",
		EST_COST_TYPE_FK = "estcosttypefk",
		EST_RESOURCE_FK = "estresourcefk",
		ASSEMBLY_TYPE_LOGIC_FK = "assemblytypelogicfk",
		SHORT_KEY_INFO = "shortkeyinfo",
		EST_ASSEMBLY_TYPE_FK = "estassemblytypefk",
		ADDRESS = "address",
		IBAN = "iban",
		PROJECT_NO_CAPS = "ProjectNo",
		GROUP_DESCRIPTION = "groupdescription",
		QUANTITY_CONTRACTED = "quantitycontracted",
		MATERIAL_CODE_CAPS = "materialCode",
		VARIANCE = "variance",
		QUANTITY_DELIVERED = "quantityDelivered",
		BOQ_BRIEF = "boqBrief",
		ACCESS_RIGHT_DESCRIPTION_04_FK = "accessrightdescriptor04fk",
		DELETE = "delete",
		READ = "read",
		WRITE = "write",
		CREATE = "create",
		DATE = "date",
		DATE_EFFECTIVE = "dateeffective",
		UPDATE_DATE = "updatedate",
		DATEE_FFECTIVE = "dateeffective",
		DATE_QUOTED = "datequoted",
		ITEM_CODE_DESC = "itemCodeDesc",
		COMPANY_FK = "companyfk",
		INV_STATUS_FK = "invstatusfk",
		CONFIRMED_TOTAL = "confirmedtotal",
		QUANTITY_CONFIRMED = "quantityconfirmed",
		PRICE_CONFIRMED = "priceconfirmed",
		INV_REJECTION_REASON_FK = "invrejectionreasonfk",
		INC_REJECT_FK = "invrejectfk",
		ORDER_QUANTITY = "orderquantity",
		UOM = "uom",
		PERCENTAGE = "Percentage",
		TOTAL_VALUE_GROSS = "totalvaluegross",
		PRC_BOQ_FK = "prcboqfk",
		FINAL_PRICE_GROSS = "finalpricegross",
		COMPANY_NAME = "CompanyName",
		IS_OWNER = "isowner",
		CAN_EDIT = "canedit",
		CAN_LOOKUP = "canlookup",
		PRC_EVENT_TYPE_FK = "prceventtypefk",
		START_NO_OF_DAYS = "startnoofdays",
		START_BASIS = "startbasis",
		END_NO_OF_DAYS = "endnoofdays",
		END_BASIS = "endbasis",
		START_OVERTWRITE = "startoverwrite",
		END_OVERWRITE = "endoverwrite",
		START_CALCULATED = "startcalculated",
		END_CALCULATED = "endcalculated",
		HAS_AMOUNT = "hasamount",
		COST_PRICE_GROSS = "CostPriceGross",
		REQ_HEADER_FK_CODE = "ReqHeaderFkCode",
		BPD_SUBSIDIARY_FK_CAPS = "BpdSubsidiaryFk",
		PRC_EVENT_TYPE_END_FK = "prceventtypeendfk",
		PRC_SYSTEM_EVENT_TYPE_START_FK = "prcsystemeventtypestartfk",
		PRC_EVENT_TYPE_START_FK = "prceventtypestartfk",
		ACTUAL_START = "actualstart",
		ACTUAL_FINISHED = "actualfinish",
		ACTUAL_END = "actualend",
		START_ACTUAL = "startactual",
		END_ACTUAL = "endactual",
		SYSTEM_EVENT_TYPE_START_FK = "systemeventtypestartfk",
		SYSTEM_EVENT_TYPE_END_FK = "systemeventtypeendfk",
		PRC_SYSTEM_EVENT_TYPE_END_FK = "prcsystemeventtypeendfk",
		ADD_LEAD_TIME_TO_START = "addleadtimetostart",
		ADD_LEAD_TIME_TO_END = "addleadtimetoend",
		SAFETY_LEAD_TIME = "safetyleadtime",
		COMMENT_CONTRACTOR = "commentcontractor",
		COST_CODE_PRICE_VER_FK = "costcodepriceverfk",
		COST_CODE_PRICE_VER_FK_PRICE_LIST_DESCRIPTION = "CostcodePriceVerFkPriceListDescription",
		CO2_PROJECT = "co2project",
		CO2_SOURCE = "co2source",
		CO2_SOURCE_FK = "co2sourcefk",
		COMPANY_NAME_SMALL = "companyname",
		USER_FK = "userfk",
		QTO_FORMULA_FK = "qtoformulafk",
		IQ_REMAINING_QUANTITY = "iqremainingquantity",
		IQ_PREV_QUANTITY = "iqprevquantity",
		IS_LANGUAGE_DEPENDENT = "islanguagedependent",
		ADDRESS_FK = "addressfk",
		ADDRESS_ENTITY = "addressentity",
		MATERIAL_PRICE_VERSION_PRICE_LIST = "materialpriceversionPriceList",
		BAS_CO2_SOURCE_FK = "basco2sourcefk",
		CO2_PROJECT_TOTAL = "co2projecttotal",
		CO2_SOURCE_TOTAL = "co2sourcetotal",
		CO2_TOTAL_VARIANCE = "co2totalvariance",
		STATUS_FK_CAPS = "StatusFk",
		DATE_RECEIVED = "datereceived",
		MATERIAL_TYPE_FK = "materialtypefk",
		PROJECT_ID = "projectId",
		CONTRACT_TYPE_FK = "contracttypefk",
		TOTAL_VALUE = "totalvalue",
		IS_TURNOVER = "isturnover",
		DATE_PAYMENT = "datepayment",
		PERCENT_OF_CONTRACT = "percentofcontract",
		IS_SHOW_IN_TICKET_SYSTEM = "isshowinticketsystem",
		DB_TABLE_NAME = "dbtablename",
		IS_FOR_PROCUREMENT = "isforprocurement",
		ICO_ALTERNATIVE = "ico-alternative",
		DESCRIPTION_CO2_PROJECT = "description-co2project",
		ATTRIBUTE_PROPERTY = "attribute-property",
		ATTRIBUTE_VALUE = "attribute-value",
		LINE_TYPE = "linetype",
		IS_DEFAULT_BASELINE = "isdefaultbaseline",
		IS_SELECTED = "IsSelected",
		IS_SELECTED_SMALL = "isselected",
		PLANT_KIND_FK = "plantkindfk",
		PLANT_TYPE_FK = "planttypefk",
		LEAD_TIME = "leadtime",
		BAS_TEXT_MODULE_FK = "bastextmodulefk",
		AMOUNT_NET_PES = "amountNetPes",
		PRJ_DOCUMENT_STATUS_FK = "prjdocumentstatusfk",
		PRICE_VERSION_FK = "priceversionfk",
		IS_PUBLISHED = "ispublished",
		IS_ORDERED = "isordered",
		IS_QUOTED = "isquoted",
		AMOUNT_TOTAL = "amounttotal",
		IS_ASSET_MANAGEMENT = "isassetmanagement",
		FIXED_ASSET_FK = "fixedassetfk",
		BAS_COMPANY_DEFERRAL_TYPE_FK = "bascompanydeferaltypefk",
		DATE_DEFERRAL_START = "datedeferalstart",
		IS_CHAINED = "ischained",
		ACTIVITY_FK = "activityfk",
		ITEM_TYPE_2 = "ItemType2",
		IS_DELIVERED = "isdelivered",
		IS_VIRTUAL = "isvirtual",
		IS_INVOICED = "isinvoiced",
		IS_CANCELED = "iscanceled",
		IS_NOT_ACCRUAL = "isnotaccrual",
		TRANS_HEADER = "transHeader",
		DATE_REPORTED = "datereported",
		RULE = "rule",
		PARAM = "param",
		IS_RATE = "israte",
		ESTIMATION_CODE = "estimationcode",
		PARAMETER_VALUE = "parametervalue",
		EST_RESOURCE_TYPE_FK_EXTEND = "estresourcetypefkextend",
		COST_CODE_FK = "costcodefk",
		COST_CODE_TYPE_FK = "costcodetypefk",
		MDC_COST_CODE_FK = "mdccostcodefk",
		DAY_WORK_RATE_UNIT = "dayworkrateunit",
		ESTIMATE_LINE_ITEM_UDP1_COST_UNIT = "EstimateLineItemUDP1costunit",
		ESTIMATE_LINE_ITEM_UDP2_COST_UNIT = "EstimateLineItemUDP2costunit",
		ESTIMATE_LINE_ITEM_UDP3_COST_UNIT = "EstimateLineItemUDP3costunit",
		ESTIMATE_LINE_ITEM_UDP4_COST_UNIT = "EstimateLineItemUDP4costunit",
		ESTIMATE_LINE_ITEM_UDP5_COST_UNIT = "EstimateLineItemUDP5costunit",
		ESTIMATE_RESOURCE_UDP1_COST_UNIT = "EstimateResourceUDP1costunit",
		ESTIMATE_RESOURCE_UDP2_COST_UNIT = "EstimateResourceUDP2costunit",
		ESTIMATE_RESOURCE_UDP3_COST_UNIT = "EstimateResourceUDP3costunit",
		ESTIMATE_RESOURCE_UDP4_COST_UNIT = "EstimateResourceUDP4costunit",
		ESTIMATE_RESOURCE_UDP5_COST_UNIT = "EstimateResourceUDP5costunit",
		ACTIVITY_TYPE_FK = "activitytypefk",
		ACTIVITY_DATE = "activitydate",
		REMINDER_START_DATE = "reminderstartdate",
		REMINDER_END_DATE = "reminderenddate",
		IS_FINISHED = "isfinished",
		UP_MATERIAL_CODE = "upMaterialCode",
		UP_JOB_CATALOG_CODE = "upJobCatalogCode",
		PRICE_VERSION = "priceVersion",
		IS_OPTIONAL = "isoptional",
		IS_OPTIONAL_IT = "isoptionalit",
		ACCOUNT_NO = "accountno",
		IS_GC_BoQ = "isgcboq",
		BOQ_HEADER = "boqheader",
		MDC_ALLOWANCE_TYPE_FK = "mdcallowancetypefk",
		MDC_COST_CODE_DESCRIPTION = "mdccostcodeDescription",
		AM_PERC = "amperc",
		GA_PERC = "gaperc",
		RP_PERC = "rpperc",
		ADVANCED_ALL_UNIT_ITEM = "advancedallunititem",
		GRAND_COST_UNIT_TARGET = "grandcostunittarget",
		DEF_MGC_PERC = "defmgcperc",
		FM = "fm",
		GC = "gc",
		URD = "urd",
		ADVANCED_ALL = "advancedall",
		EFFICIENCY_FACTOR_1 = "efficiencyfactor1",
		EFFICIENCY_FACTOR_2 = "efficiencyfactor2",
		ESTIMATE_RESOURCE_UDP1_TOTAL = "EstimateResourceUDP1total",
		ESTIMATE_RESOURCE_UDP2_TOTAL = "EstimateResourceUDP2total",
		ESTIMATE_RESOURCE_UDP3_TOTAL = "EstimateResourceUDP3total",
		ESTIMATE_RESOURCE_UDP4_TOTAL = "EstimateResourceUDP4total",
		ESTIMATE_RESOURCE_UDP5_TOTAL = "EstimateResourceUDP5total",
		DAY_WORK_RATE_TOTAL = "dayworkratetotal",
		IS_TOTAL_WQ = "istotalwq",
		IS_TOTAL_AQ_BUDGET = "istotalaqbudget",
		IS_BALANCE_FP = "isbalancefp",
		VERSION_DESCRIPTION = "versiondescription",
		VERSION_NO = "versionno",
		IS_DISABLED = "isdisabled",
		COMMENT_TEXT = "commenttext",
		IS_GENERATED_PRC = "isgeneratedprc",
		IS_DISABLED_PRC = "isdisabledprc",
		MEASSAGE_SEVERITY_FK = "messageseverityfk",
		BAS_COST_CODE_RATE = "bascostcode.rate",
		COST_FACTOR_DETAIL_1 = "costfactordetail1",
		EMAIL = "email",
		EST_LINE_ITEM_STATUS_FK = "estlineitemstatusfk",
		RP_VALUE = "rpvalue",
		AM_VALUE = "amvalue",
		GA_VALUE = "gavalue",
		ALLOWANCE = "allowance",
		DAYWORK_RATE = "dayworkrate",
		IS_BUDGET = "isbudget",
		IS_LABOUR_SMALL = "islabour",
		DOCUMENT_CATEGORY_FK = "documentcategoryfk",
		DOCUMENT_TYPE_FK = "documenttypefk",
		PRJ_DOCUMENT_CATAGORY_FK = "prjdocumentcategoryfk",
		IS_SELECT = "isSelect",
		SELECTION = "selection",
		SALES_PRICE = "salesprice",
		PRJ_DOCUMENT_TYPE_FK = "prjdocumenttypefk",
		RADIUS_IN_METER = "radiusinmeter",
		EST_RESOURCE_FK_DESCRIPTION = "estresourcefkdescription",
		SCHEDULE_TYPE_FK = "scheduletypefk",
		CONVERTED_UNIT_RATE = "convertedUnitRate",
		CLERK_REQ_FK_DESCRIPTION = "ClerkReqFkDescription",
		BASIS = "basis",
		PECENTAGE_QUANTITY = "percentagequantity",
		CUMULATIVE_PERCENT = "cumulativepercentage",
		CONVERT_PRICE = "ConvertPrice",
		STRUCTURE_FK_DESCRIPTION_INFO = "StructureFkDescriptionInfo",
		MATCHNESS = "Matchness",
		STRUCTURE_DESCRIPTION_CAPS = "StructureDescription",
		ICON = "icon",
		VALUE_1_IS_ACTIVE = "value1isactive",
		OPERATOR_1 = "operator1",
		VALUE_2_IS_ACTIVE = "value2isactive",
		OPERATOR_2 = "operator2",
		VALUE_3_IS_ACTIVE = "value3isactive",
		OPERATOR_3 = "operator3",
		VALUE_4_IS_ACTIVE = "value4isactive",
		OPERATOR_4 = "operator4",
		VALUE_5_IS_ACTIVE = "value5isactive",
		OPERATOR_5 = "operator5",
		QTO_FORMULA_TYPE_FK = "qtoformulatypefk",
		IS_IDENTIFIED = "isidentified",
		IS_ALLOWED_QTO_FOR_SALES = "isallowedqtoforsales",
		BAS_FORM_FK = "basformfk",
		PERCENT_DISCOUNT = "percentdiscount",
		ETM_CONTEXT_FK = "etmcontextfk",
		PRICE_LIST_TYPE_FK = "pricelisttypefk",
		PERCENT = "percent",
		LOCATION_FK = "locationfk",
		IS_MANUAL_EDIT_PLANT_MASTER = "ismanualeditplantmaster",
		UNIT_INFO = "unitinfo",
		IS_MINOR_EQUIPMENT = "isminorequipment",
		IS_HIRE = "ishire",
		IS_BULK = "isbulk",
		IS_TIMEKEEPING = "istimekeeping",
		IS_CLUSTER = "iscluster",
		IS_CLUSTERED = "isclustered",
		IS_TIMEKEEPING_DEFAULT = "istimekeepingdefault",
		POINTS = "points",
		UOM_TYPE_FK = "uomtypefk",
		BAS_UOM_HOUR_FK = "basuomhourfk",
		BAS_UOM_DAY_FK = "basuomdayfk",
		TELEPHONE_NUMBER_DESCRIPTOR = "telephonenumberdescriptor",
		CITY = "city",
		BUSINESSPARTNER_STATUS_FK = "businesspartnerstatusfk",
		MARKER = "marker",
		IS_ALLOWED_QTO_FOR_PROC = "isallowedqtoforproc",
		BASIC_COST = "basiccost",
		DESCRIPTION_COL = "descriptionCol",
		CONTROLLINGUNITDESCRIPTION_BUDGET_SHIFT = "mdccounittargetfkdescription",
		SOURCE_OR_TARGET = "sourceOrTar",
		SHIFT_BUDGET = "shiftBudget",
		CONTROLLINGUNIT_BUDGET_SHIFT = "mdccounittargetfk",
		GCC_PACKAGE_BUDGET = "budgetpackage",
		COSTCODECHILDALLOWED = "isprojectchildallowed",
		UOM_1 = "uomfk1",
		UOM_2 = "uomfk2",
		PERFORMANCE_SHEET = "performancesheetfk",
		SCHEDULE_TEMPLATE = "activitytemplatefk",
		SCHEDULE_PRODUCTIVITY = "performancefactor",
		PERF1UOM_FK = "perf1uomfk",
		QUANTITY_PORTION = "quantityportion",
		CONTROLLING_GROUP = "controllinggroupfk",
		CONTROLLING_GROUP_DETAIL = "controllinggroupdetailfk",
		IS_DISPLAYED = "isdisplayed",
		BASIC_COST_CO = "basiccostco",
		ADDITIONAL_EXPENSES = "additional",
		AMOUNT = "Amount",
		CONTROLLING_UNIT_DESCRIPTION = "controllingunitdescription",
		PERFORMANCE = "performance",
		INVOICE = "invoice",
		CONTRACT = "contract",
		IS_MANUAL = "ismanual",
		PRICE_PORTION_1 = "priceportion1",
		PRICE_PORTION_2 = "priceportion2",
		PRICE_PORTION_3 = "priceportion3",
		WORK_OPERATION_TYPE_FK = "workoperationtypefk",
		JOB_CARD_RECORD_TYPE_FK = "jobcardrecordtypefk",
		CARD_RECORD_FK = "cardrecordfk",
		LEAD_DAYS = "leaddays",
		LEAD_QUANTITY = "leadquantity",
		IS_RECALC_DATES = "isrecalcdates",
		IS_FIXED_DAYS = "isfixeddays",
		IS_PERFORMANCE_BASED = "isperformancebased",
		JOB_CARD_TEMPLATE_FK = "jobcardtemplatefk",
		DAYS_AFTER = "daysafter",
		DURATION = "duration",
		BUDGET_DIFFERENCE = "budgetdifference",
		ID_IS_ACTIVE = "id_isactive",
		UP_SELECTED = "upSelected",
		BAS_UOM_WEEK_FK = "basuomweekfk",
		BAS_UOM_MONTH_FK = "basuommonthfk",
		F1 = "f1",
		F2 = "f2",
		F3 = "f3",
		QUANTITY_DETAIL = "quantitydetail",
		COST_GROUP_LIC_DIN276_2018_12_DESC = "costgroup_lic_din276_2018-12_Desc",
		IS_PLANTMANAGEMENT = "isplantmanagement",
		ITEM_TOTAL = "itemtotal",
		DISCOUNTED_PRICE = "discountedprice",
		DISCOUNT_PERCENT_IT = "discountpercentit",
		MAINTENANCE_SCHEMA_FK = "maintenanceschemafk",
		PLANT_COMPONENT_TYPE_FK = "plantcomponenttypefk",
		MAINTENANCE_STATUS_FK = "maintenancestatusfk",
		PSD_ACTIVITY_FK_DEC = "psdactivityfkdec",
		SCOPE = "scope",
		IS_ONE_STEP = "isonestep",
		SHORT_DESC = "shortdesc",
		IS_SURCHARGED = "issurcharged",
		SELECT_MARKUP = "selectmarkup",
		QUANTITY_SPLIT_TOTAL = "quantitysplittotal",
		PLANT_GROUP_FK = "plantgroupfk",
		CLERK_RESPONSIBLE_FK = "clerkresponsiblefk",
		PROCUREMENT_STRUCTURE_FK = "procurementstructurefk",
		SERIAL_NUMBER = "serialnumber",
		MATCH_CODE = "matchcode",
		CLERK_OWNER_FK = "clerkownerfk",
		EQUIPMENT_CALCULATION_TYPE = "equipmentcalculationtypefk",
		EQUIPMENT_CATALOG_FK = "equipmentcatalogfk",
		REFERENCE_YEAR = "referenceyear",
		SPECIFIC_VALUE_TYPE_FK = "specificvaluetypefk",
		FACTOR = "factor",
		ISINHERITED = "isinherited",
		PLANT_2_FK = "plant2fk",
		PLANT_2_FK_DESCRIPTION = "plant2fkdescription",
		SCHEDULING_CONTEXT_FK = "schedulingcontextfk",
		WARRANTY_END = "warrantyend",
		HOURS = "hours",
		PARTNER_TYPE_FK = "partnertypefk",
		ROLE_FK = "rolefk",
		STATE_SELECTED = "stateSelected",
		CHANGE_ORDER_SELECTED = "changeOrderSelected",
		EST_ASSEMBLY_DESCRIPTION = "estassemblydescription",
		INSERTED_BY = "insertedby",
		SUB_ACTIVITY = "activitysubfk",
		HOME_PROJECT_FK = "homeprojectfk",
		PROJECT_LOCATION_FK = "projectlocationfk",
		MAINTENANCE_DESCRIPTION = "maintenancedescription",
		INCLUDE = "include",
		JOB_CARD_FK = "jobcardfk",
		TRAFFIC_LIGHT_FK = "trafficlightfk",
		METER_NO = "meterno",
		DELIVERED_QUANTITY = "deliveredquantity",
		IS_DONE = "isdone",
		JOB_PERFORMING_FK = "jobperformingfk",
		BAS_CLERK_RESPONSIBLE_FK = "basclerkresponsiblefk",
		CODE_FORMATE_FK = "codeformatfk",
		TARGET_START = "targetstart",
		TARGET_END = "targetend",
		IS_FINISHED_WITH100_PERCENT = "isfinishedwith100percent",
		EXTERNAL_CODE = "externalcode",
		PERCENTAGE_COMPLETION = "percentagecompletion",
		PROGRESS_REPORT_METHOD_FK = "progressreportmethodfk",
		MAINT_SCHEMA_FK = "maintschemafk",
		RECORDED = "recorded",
		DETAILS_STACK = "detailsstack",
		REF_LINE_ITEM_CODE = "reflineitemcode",
		S_CURVE_FK = "scurvefk",
		PERCENT_OF_TIME = "percentoftime",
		PERCENT_OF_COST = "percentofcost",
		BIN = "bin",
		WEIGHT = "weight",
		WIC_BOQ_ITEM_FK_BRIEF = "wicboqitemfkbrief",
		PLACED_BEFORE = "placedbefore",
		DISTANCE_TO = "distanceto",
		SCHEDULE_FK = "schedulefk",
		LINE_ITEM_DESCRIPTION_INFO = "lineitemdescriptioninfo",
		EXTERNAL_CODE_CAP = "ExternalCode",
		USE_IN_SPLIT = "useinsplit",
		QUANTITY_FACTOR_DETAIL_1 = "quantityfactordetail1",
		QUANTITY_FACTOR_DETAIL_2 = "quantityfactordetail2",
		COST_FACTOR_DETAIL_2 = "costfactordetail2",
		EFFICIENCY_FACTOR_DETAIL_1 = "efficiencyfactordetail1",
		EST_ASSEMBLY_DESCRIPTION_INFO = "estassemblydescriptioninfo",
		EST_ASSEMBLY_CAT_FK = "estassemblyfk",
		END_DATE_SMALL = "enddate",
		TEXT = "text",
		ACTIVITY_PRESENTATION_FK = "activitypresentationfk",
		DEFAULT_VALUE = "defaultvalue",
		TASK_TYPE_FK = "tasktypefk",
		IS_BASE = "isbase",
		WIZARD_FK = "wizardfk",
		ROLE_FK_2 = "rolefk2",
		ACCESS_ROLE_FK_2 = "accessrolefk2",
		ACCESS_ROLE = "AccessRole",
		MOBILE_NO = "telephonemobilfk",
		PERF2UOM_FK = "perf2uomfk",
		IQ = "IQ",
		AQ = "AQ",
		INDEX_HEADER_FK = "indexheaderfk",
		LOGISTIC_JOB_FK = "logisticjobfk",
		ACTION_TYPE_FK = "actiontypefk",
		ACRONYM = "acronym",
		TIME_SYMBOL_FK = "timesymbolfk",
		WEEK_DAY_FK = "weekdayfk",
		IS_STANDARDRATE = "isstandardrate",
		FROM_TIME = "fromtime",
		TO_TIME = "totime",
		BREAK_FROM = "breakfrom",
		BREAK_TO = "breakto",
		IS_PRODUCTIVE = "isproductive",
		IS_PRESENCE = "ispresence",
		VALUATION_PERCENT = "valuationpercent",
		VALUATION_RATE = "valuationrate",
		TIME_SYMBOL_TYPE_FK = "timesymboltypefk",
		TIME_SYMBOL_GROUP_FK = "timesymbolgroupfk",
		SURCHARGE_TYPE_FK = "surchargetypefk",
		WEEK_DAY_INDEX = "weekdayindex",
		TARGET_HOURS = "targethours",
		START_DATE = "startdate",
		GENERATE_RECORDING = "generaterecording",
		IS_CREW_LEADER = "iscrewleader",
		SHIFT_FK = "shiftfk",
		TIME_KEEPING_GROUP_FK = "timekeepinggroupfk",
		PAYMENT_GROUP_FK = "paymentgroupfk",
		PAYROLL_YEAR = "payrollyear",
		DUE_DATE = "duedate",
		JOB_FK = "jobfk",
		PROJECT_ACTION_FK = "projectactionfk",
		EMPLOYEE_FK = "employeefk",
		TIME_KEEPING_PERIOD_FK = "timekeepingperiodfk",
		USER_DEFINED_DATE_01 = "userdefineddate01",
		USER_DEFINED_DATE_02 = "userdefineddate02",
		SHEET_FK = "sheetfk",
		TRANSACTION_CASE = "transactioncase",
		LENGTH_DIMENSION = "lengthdimension",
		EXECUTION_STARTED = "executionstarted",
		IS_EXPENSE = "isexpense",
		PARENT_ACTIVITY_FK = "parentactivityfk",
		FIX_LAG_TIME = "fixlagtime",
		USE_CALENDAR = "usecalendar",
		EXECUTION_FINISHED = "executionfinished",
		ACTIVITY_MEMBER_FK = "activitymemberfk",
		CHILD_ACTIVITY_FK = "childactivityfk",
		SUCCESSOR_DESC = "successordesc",
		PREDECESSOR_DESC = "predecessordesc",
		CONSTRUCTION_LIBRARY_GROUP = "cosgroupfk",
		IS_WTM_RELEVANT = "iswtmrelevant",
		PRC_STRUCTURE_FK_DESCRIPTION_INFO = "PrcStructureFkDescriptionInfo",
		SCHEDULING_METHOD_FK = "schedulingmethodfk",
		CHART_PRESENTATION_FK = "chartpresentationfk",
		BAS_3D_VISUALIZATION_TYPE_FK = "bas3dvisualizationtypefk",
		CONSTRAINT_TYPE_FK = "constrainttypefk",
		REMAINING = "remaining",
		COMPANY_CHARGED_FK = "companychargedfk",
		ACCOUNT_REV_FK = "accountrevfk",
		ACCOUNT_COST_FK = "accountcostfk",
		NOMINAL_DIMENSION1_COST = "nominaldimension1cost",
		NOMINAL_DIMENSION2_COST = "nominaldimension2cost",
		NOMINAL_DIMENSION3_COST = "nominaldimension3cost",
		NOMINAL_DIMENSION1_REV = "nominaldimension1rev",
		NOMINAL_DIMENSION2_REV = "nominaldimension2rev",
		NOMINAL_DIMENSION3_REV = "nominaldimension3rev",
		NOMINAL_DIMENSION1 = "nominaldimension1",
		NOMINAL_DIMENSION2 = "nominaldimension2",
		NOMINAL_DIMENSION3 = "nominaldimension3",
		COMPANY_CHARGED_FK_COMPANY_NAME = "companychargedfkcompanyname",
		ACCOUNT_FK = "accountfk",
		BREAK_START = "breakstart",
		BREAK_END = "breakend",
		IS_TIME_ACCOUNT = "istimeaccount",
		IS_OVERTIME = "isovertime",
		IS_OFF_DAY = "isoffday",
		CONSECUTIVE_BILL_NO = "consecutivebillno",
		IS_TIME_ALLOCATION = "istimeallocation",
		EXCEPT_DATE = "exceptdate",
		PERFORMED_TO = "performedto",
		PERFORMED_FROM = "performedfrom",
		IS_VACATION = "isvacation",
		WEEK_ENDS_ON = "weekendson",
		TIMESHEET_CONTEXT_FK = "timesheetcontextfk",
		GROUP_FK = "groupfk",
		MANUAL_MARKUP = "manualmarkup",
		IS_PROTECTED = "isprotected",
		MATERIAL_PRICE_VERSIO_FKN = "materialpriceversionfk",
		EXSALES_REJECTED_QUANTITY = "exsalesrejectedquantity",
		PREV_REJECTED_QUANTITY = "prevrejectedquantity",
		TOTAL_REJECTED_QUANTITY = "totalrejectedquantity",
		COST_SUMMARY = "costsummary",
		EMPLOYEE_CREW_FK = "employeecrewfk",
		EMPLOYEE_CREW_FK_CODE = "employeecrewfkcode",
		DEF_M_PERC = "defmperc",
		TIME_SYMBOL_ASL_1_FK = "timesymbolasl1fk",
		TO_TIME_PART_TIME = "totimeparttime",
		EMPLOYEE_WORKING_TIME_MODEL_FK = "employeeworkingtimemodelfk",
		TIME_SYMBOL_BD_L1_FK = "timesymbolbdl1fk",
		TIME_SYMBOL_RECAP_BL_FK = "timesymbolrecapblfk",
		IS_BILLING_ELEMENT = "isbillingelement",
		IS_ACCOUNTING_ELEMENT = "isaccountingelement",
		IS_PLANNING_ELEMENT = "isplanningelement",
		SETTLED_BY_TYPE_FK = "settledbytypefk",
		PRICING_GROUP_FK = "pricinggroupfk",
		EQUIPMENT_PRICE_LIST_FK = "equipmentpricelistfk",
		IS_HANDLING_CHARGE = "ishandlingcharge",
		PRICE_CONDITION_FK = "priceconditionfk",
		WEEKLY_LIMIT = "weeklylimit",
		MONTHLY_LIMIT = "monthlylimit",
		TIME_SYMBOL_BDL1_FK_DESCRIPTION = "timesymbolbdl1fkdescription",
		CONTROLLING_UNIT_STATUS_FK = "controllingunitstatusfk",
		CONTROLLING_CAT_FK = "controllingcatfk",
		EFFECTIVE_DATE = "effectivedate",
		JOB1_FK = "job1fk",
		JOB2_FK = "job2fk",
		RECORD_TYPE_FK = "recordtypefk",
		ARTICLE_FK = "articlefk",
		IS_CONTROLLING = "iscontrolling",
		CONTRO_COST_CODE_FK = "contrcostcodefk",
		CONTRACT_CODE = "contractcode",
		FORMULA_SMALL = "formula",
		HAS_TOTAL = "hastotal",
		IS_PRINTED = "isprinted",
		PRICE_CONDITION_TYPE_FK = "priceconditiontypefk",
		PRJ_BOQ_FK = "prjboqfk",
		EC_AQ_IN_RP = "ec_aq_in_rp",
		DESCRIPTION_2_INFO = "description2info",
		BAS_ACCOUNT_FK = "basaccountfk",
		MDC_CONTR_COST_CODE_FK = "mdccontrcostcodefk",
		DISCHARGED_DATE = "dischargeddate",
		EXPIRATION_DATE = "expirationdate",
		PRJ_PROJECT_FK = "prjprojectfk",
		PSD_SCHEDULE_FK = "psdschedulefk",
		AQ_ESTIMATED_PRICE = "aqestimatedprice",
		EC_TOTAL = "ec_total",
		EC_TO_RP = "ec_to_rp",
		EP_IN_RP = "ep_in_rp",
		EP_TO_RP = "ep_to_rp",
		EP_TOTAL = "ep_total",
		VALUE_TYPE_FK = "valuetypefk",
		AC_TO_RP = "ac_to_rp",
		AC_TOTAL = "ac_total",
		ACC_TO_RP = "acc_to_rp",
		ACC_TOTAL = "acc_total",
		TAC_TO_RP = "tac_to_rp",
		TAC_TOTAL = "tac_total",
		EC_AQ_TO_RP = "ec_aq_to_rp",
		EC_AQ_TOTAL = "ec_aq_total",
		USER_FORM_ASSIGN_PARAM_TAB_BG = "userFormAssignParamTabBg",
		RELATION_KIND_FK = "relationkindfk",
		IS_READY_FOR_SETTLEMENT = "isreadyforsettlement",
		IS_TIMEKEEPING_ELEMENT = "istimekeepingelement",
		DISPATCH_HEADER_FK = "dispatchheaderfk",
		DISPATCH_RECORD_FK_DESCRIPTION = "dispatchrecordfkdescription",
		IS_DURATION_ESTIMATION_DRIVEN = "isdurationestimationdriven",
		HOURS_TOTAL = "hourstotal",
		ESTIMATE_HOURS_TOTAL = "estimatehourstotal",
		SETTLEMENT_NO = "settlementno",
		PERCENTAGE_01 = "percentage01",
		PERCENTAGE_02 = "percentage02",
		PERCENTAGE_03 = "percentage03",
		PERCENTAGE_04 = "percentage04",
		PERCENTAGE_05 = "percentage05",
		PERCENTAGE_06 = "percentage06",
		PRICE_TOTAL_OC = "pricetotaloc",
		IS_QUANTITY_EVALUATED = "isquantityevaluated",
		CTC = "ctc",
		CAC = "cac",
		EC_FQ_TOTAL = "ec_fq_total",
		B_AQ_TOTAL = "b_aq_total",
		B_FQ_TOTAL = "b_fq_total",
		B_AQ_IN_RP = "b_aq_in_rp",
		B_FQ_IN_RP = "b_fq_in_rp",
		ACTIVITY_STATE_FK = "activitystatefk",
		PACKAGE_CODE_SMALL = "packagecode",
		INSERTED_AT = "insertedat",
		UPDATED_AT = "updatedat",
		UPDATED_BY = "updatedby",
		IS_UPLIFT = "isuplift",
		CLAIM_EXIST = "claimexist",
		DISPATCH_RECORD_STATUS_FK = "dispatchrecordstatusfk",
		ORDER_CHANGE_FK = "orderchangefk",
		FROM_DATE = "fromdatetime",
		TO_DATE = "todatetime",
		PLANT_FK = "plantfk",
		PRICE_PORTION_4 = "priceportion4",
		PRICE_PORTION_5 = "priceportion5",
		PRICE_PORTION_6 = "priceportion6",
		PRICE_TOTAL = "pricetotal",
		EXQ_TO_QUANTITYADJ = "ExQtoQuantityAdj",
		PCO = "pco",
		PERFORMANCE_DATE = "performancedate",
		IS_REVENUE = "isrevenue",
		FROM_DATE_TIME = "fromdatetime",
		TO_DATE_TIME = "todatetime",
		PLANTS_QUANTITY = "PlantsQuantity-column",
		IS_LOGISTICDATA_REQUIRED = "islogisticdatarequired",
		EXPECTED_QUANTITY = "expectedquantity",
		COMPANY_PERIOD = "companyperiodfk",
		ABSENCE_DAY = "absenceday",
		MAX_QUANTITY = "maxquantity",
		PROJECT_STOCK_FK = "projectstockfk",
		PROJECT_STOCK_FK_DESCRIPTION = "projectstockfkdescription",
		IS_LOCATION_MANDATORY = "islocationmandatory",
		STOCK_FK = "stockfk",
		BCF = "bcf",
		CAC_BC = "cac_bc",
		TIME_SYMBOL_BLSL1_FK = "timesymbolblsl1fk",
		LOWER_DAILY_SAVING_LIMIT = "lowerdailysavinglimit",
		PARAMETER_GROUP_FK = "parametergroupfk",
		EST_PARAM_GRP_FK = "estparamgrpfk",
		SAC = "sac",
		WS_FQ_CO_NOT_APP_TOTAL = "ws_fq_co_not_app_total",
		WS_AQ_CO_NOT_APP_TOTAL = "ws_aq_co_not_app_total",
		WS_AQ_CO_APP_TO_RP = "ws_aq_co_app_to_rp",
		WS_FQ_CO_APP_TO_RP = "ws_fq_co_app_to_rp",
		WS_FQ_CO_APP_TOTAL = "ws_fq_co_app_total",
		WS_AQ_CO_APP_TOTAL = "ws_aq_co_app_total",
		ORDER_FINAL_PRICE = "orderfinalprice",
		IQ_FINAL_PRICE = "iqfinalprice",
		BQ_FINAL_PRICE = "bqfinalprice",
		BILLED_QUANTITY = "billedquantity",
		IS_BQ = "isbq",
		QTO_TYPE_FK = "qtotypefk",
		CLAIM_REASON_FK = "claimreasonfk",
		PRICE_PORTION_01 = "priceportion01",
		PRICE_PORTION_02 = "priceportion02",
		PRICE_PORTION_03 = "priceportion03",
		PRICE_PORTION_04 = "priceportion04",
		PRICE_PORTION_05 = "priceportion05",
		WP_TOTAL = "wp_total",
		WS_AQ_TOTAL = "ws_aq_total",
		WS_FQ_TOTAL = "ws_fq_total",
		PRICE_PORTION_06 = "priceportion06",
		ITEM_INFO = "iteminfo",
		ITEM_INFO_CAPITAL = "ItemInfo",
		BAS_ITEM_TYPE_FK_1 = "BasItemTypeFk",
		IS_LUMP_SUM_1 = "IsLumpsum",
		BAS_ITEM_TYPE_2_FK_CAPITAL = "BasItemType2Fk",
		PLANT_ESTIMATE_PRICE_LIST_FK = "plantestimatepricelistfk",
		BILL_TO_FK = "billtofk", //No changes required create bill from wip module wizard option
		RES_TYPE_FK = "restypefk",
		CREATE_RESOURCE_TYPE_CHECKBOX = "createResourceType-checkbox",
		EXPECTED_EFFECTIVE_DATE = "expectedeffectivedate",
		BAS_CONTR_COLUMN_TYPE_FK = "bascontrcolumntypefk",
		IS_EDITABLE = "iseditable",
		IS_PLANT = "isplant",
		WCF = "wcf",
		CAC_WC = "cac_wc",
		EST_PLANT_GROUP_FK = "EstPlantGroupFk",
		PLANT_ASSEMBLY_TYPE_FK = "plantassemblytypefk",
		IS_INHERITED = "isinherited",
		PRICE_TOTAL_QTY = "pricetotalqty",
		ASSEMBLY_TYPE = "assemblytype",
		PERIOD_QUANTITY_PERFORMANCE = "periodquantityperformance",
		DUE_DATE_QUANTITY_PERFORMANCE = "duedatequantityperformance",
		REMAINING_ACTIVITY_QUANTITY = "remainingactivityquantity",
		TYPE_REQUESTED_FK = "typerequestedfk",
		IS_REQUESTED_ENTIRE_PERIOD = "isrequestedentireperiod",
		NECESSARY_OPERATORS = "necessaryoperators",
		UOM_DAY_FK = "uomdayfk",
		HAS_2ND_DEMAND = "has2nddemand",
		SKILL_GROUP_FK = "skillgroupfk",
		TYPE_FK = "typefk",
		SKILL_FK = "skillfk",
		HR = "hr",
		CONTR_FORMULA_PROP_DEF_FK = "contrformulapropdeffk",
		WP_TO_RP = "wp_to_rp",
		CPI = "cpi",
		SPI = "spi",
		REMAINING_LINEITEM_QUANTITY = "remaininglineitemquantity",
		PREDECESSOR_ACTIVITY_FK = "predecessoractivityfk",
		QUANTITY_CONFIRM = "quantityconfirm",
		DELIVER_DATE_CONFIRM = "deliverdateconfirm",
		REMARK_2 = "remark2",
		REMARK_3 = "remark3",
		LINE_ITEM_DESCRIPTION = "lineitemdescription",
		USER_DEFINED_DATE_1 = "userdefineddate1",
		USER_DEFINED_DATE_2 = "userdefineddate2",
		USER_DEFINED_DATE_3 = "userdefineddate3",
		USER_DEFINED_DATE_4 = "userdefineddate4",
		HAS_VALUE = "hasvalue",
		IS_PRICE_COMPONENT = "ispricecomponent",
		IS_ACTIVATED = "isactivated",
		PRC_PACKAGE_BOQ_FK = "prcpackageboqfk",
		BOQ_NUMBER = "BoqNumber",
		TIME_SYMBOL_BUS1_FK = "timesymbolbusl1fk",
		UPPER_DAILY_SAVING_LIMIT = "upperdailysavinglimit",
		HOURSUNIT = "hoursunit",
		FROM_TIME_PART_TIME = "fromtimeparttime",
		IS_FOR_PACKAGE = "isforpackage",
		IS_UNIQUE = "isunique",
		EXCEPTDATE = "exceptdate",
		PRJ_DOCUMENT_OPERATION_FK = "prjdocumentoperationfk",
		IS_INTER_COMPANY = "isintercompany",
		CHARACTER_COLUMN_1000003_10_1000002 = "charactercolumn_1000003_10_1000002",
		FROM_TIME_PART_DATE = "fromtimepartdate",
		TO_TIME_PART_DATE = "totimepartdate",
		IS_FOR_PACKAGE_ACCESS = "isforpackageaccess",
		WEEKLY_SAVING_LIMIT = "weeklysavinglimit",
		IS_INTERCOMPANY = "isintercompany",
		PRE_CALCULATED_WORK_OPERATION_TYPE_FK = "precalculatedworkoperationtypefk",
		DISPATCH_HEADER_IN_FK = "dispatchheaderinfk",
		DISPATCH_HEADER_OUT_FK = "dispatchheaderoutfk",
		PACKAGE_TEMPLATE_FK = "packagetemplatefk",
		PACKAGE_TYPE_FK = "packagetypefk",
		BILLING_JOB_FK = "billingjobfk",
		JOB_TYPE_FK = "jobtypefk",
		BOQ_STATUS_FK = "boqstatusfk",
		PRJ_STOCK_LOCATION_FK = "prjstocklocationfk",
		IS_STOCK = "isstock",
		PRJ_STOCK_FK_DESCRIPTION = "PrjStockFkDescription",
		QUANTITY_ON_ORDER = "quantityonorder",
		PRC_STOCK_TRANSACTION_TYPE_FK = "prcstocktransactiontypefk",
		DESCRIPTION_1_INFO = "description1info",
		BREAK_FROM_TIME = "fromtimebreaktime",
		BREAK_TO_TIME = "totimebreaktime",
		FROM_TIME_BREAK_DATE = "fromtimebreakdate",
		TO_TIME_BREAK_DATE = "totimebreakdate",
		GRAND_COST_UNIT = "grandcostunit",
		SUBSIDIARY_ADRESS = "subsidiaryAdress",
		DESC = "desc",
		DISPATCH_RECORD_FK = "dispatchrecordfk",
		ARTICLE = "article",
		DEBTOR = "debtor",
		VAT_AMOUNT = "vatamount",
		VAT_CODE = "vatcode",
		USER_DEFINED_1 = "userdefined1",
		USER_DEFINED_2 = "userdefined2",
		USER_DEFINED_3 = "userdefined3",
		USER_DEFINED_4 = "userdefined4",
		USER_DEFINED_5 = "userdefined5",
		USER_DEFINED_1_CAPS = "UserDefined1",
		USER_DEFINED_2_CAPS = "UserDefined2",
		USER_DEFINED_3_CAPS = "Userdefined3",
		USER_DEFINED_4_CAPS = "Userdefined4",
		USER_DEFINED_5_CAPS = "Userdefined5",
		PACKAGE_STRUCTURE = "packageStructure",
		RUBRIC_FK = "rubricfk",
		FORM_FK = "FormFk",
		COST_UOM = "costuom",
		EXTERNAL_ROLE_FK = "externalrolefk",
		PRC_PS_STATUS_FK = "prcpsstatusfk",
		IS_DEBIT = "isdebit",
		BAS_WARRANTY_SECURITY_FK = "baswarrantysecurityfk",
		BAS_WARRANTY_OBLIGATION_FK = "baswarrantyobligationfk",
		HAND_OVER_DATE = "handoverdate",
		DURATION_MONTHS = "durationmonths",
		WARRANTY_END_DATE = "warrantyenddate",
		DJC_TOTAL_OP = "djctotalop",
		REMAINING_WORK = "remainingwork",
		EVALUATED_ON = "evaluatedon",
		EVEALUATION_LEVEL = "evaluationlevel",
		COMMENT = "comment",
		IS_FOR_CONTRACT = "isforcontract",
		IS_PORTAL = "isportal",
		IS_MAIN = "ismain",
		PRONUNCIATION = "pronunciation",
		TIME_SYMBOL_BUSL_2_FK = "timesymbolbusl2fk",
		TIME_SYMBOL_ASL_2_FK = "timesymbolasl2fk",
		CONTACT_ABC_FK = "contactabcfk",
		BAS_LANGUAGE_FK = "baslanguagefk",
		CONTACT_TIMELINESS_FK = "contacttimelinessfk",
		CONTACT_ORIGIN_FK = "contactoriginfk",
		TITLE_FK = "titlefk",
		COUNTRY_FK = "countryfk",
		BAS_COMPANY_FK = "bascompanyfk",
		IS_SELECTED_BIG = "isSelected",
		CALL_OFF_AGREEMENT = "calloffagreement",
		EXECUTION_DURATION = "executionduration",
		EARLIEST_START = "earlieststart",
		LATEST_START = "lateststart",
		CONTRACT_PENALTY = "contractpenalty",
		DEBTOR_GROUP = "debtorgroup",
		BUSINESS_POSTING_GROUP = "businesspostinggroup",
		BAS_COMPANY_RESPONSIBLE_FK = "bascompanyresponsiblefk",
		REQUIRED_DATE = "requireddate",
		VALIDATED_DATE = "validateddate",
		CERTIFICATE_TYPE_FK = "certificatetypefk",
		ACTUAL_QUANTITY = "actualquantity",
		MATERIAL_CODE_SMALL = "materialcode",
		DISCOUNT_SPLIT = "discountsplit",
		DISCOUNT_SPLIT_OC = "discountsplitoc",
		DISCOUNT_OC = "discountoc",
		OVERALL_DISCOUNT = "overalldiscount",
		FORM_DATA_STATUS_FK = "FormDataStatusFk",
		RESPONSIBLE_COMPANY_NAME = "ResponsibleCompanyName",
		_SELECTED = "_selected",
		EXTERNAL_SOURCE_FK = "externalsourcefk",
		EXTERNAL_ID = "externalid",
		EXTERNAL_DESCRIPTION = "externaldescription",
		PRC_ITEM_EVALUATION_FK = "prcitemevaluationfk",
		IS_CONSUMED = "isconsumed",
		STOCK_TOTAL = "stocktotal",
		EX_SALES_TAX_GROUP_FK = "exsalestaxgroupfk",
		ADJ_COST_SUMMARY = "adjcostsummary",
		COST_SUMMARY_ORIGINAL = "costsummaryoriginal",
		COST_SUMMARY_ORIGINAL_DIFFERENCE = "costsummaryoriginaldifference",
		COST_SUMMARY_DIFFERENCE = "costsummarydifference",
		ACTUAL_PROVISION_TOTAL = "actualprovisiontotal",
		PROVISION_TOTAL = "provisiontotal",
		INVENTORY_TOTAL = "inventorytotal",
		STOCK_VALUATION_RULE_FK = "stockvaluationrulefk",
		EVENT_FK = "eventfk",
		IS_RECEIPT = "isreceipt",
		IS_PROVISION = "isprovision",
		IS_PROVISION_ALLOWED = "isprovisionallowed",
		IS_ALLOWED_MANUAL = "isallowedmanual",
		CERTIFICATE_DATE = "certificatedate",
		REFERENCE_DATE = "referencedate",
		CERTIFICATE_STATUS_FK = "certificatestatusfk",
		REQUISITION_TYPE_FK = "requisitiontypefk",
		STOCK_LOCATION_FK_DESCRIPTION = "stocklocationfkdescription",
		TYPE_SMALL = "type",
		ACTUAL_TOTAL = "actualtotal",
		STOCK_PROVISION_TOTAL = "stockprovisiontotal",
		DATE_DUE = "datedue",
		AMOUNT_DUE = "amountdue",
		DATE_DONE = "datedone",
		AMOUNT_DONE = "amountdone",
		PRC_ADVANCE_TYPE_FK = "prcadvancetypefk",
		AMOUNT_DUE_OC = "amountdueoc",
		AMOUNT_DONE_OC = "amountdoneoc",
		EVALUATION_MOTIVE_FK = "evaluationmotivefk",
		BAS_CLERK_PRC_FK = "basclerkprcfk",
		BAS_CLERK_REQ_FK = "basclerkreqfk",
		PRC_CONFIGURATION_REQ_FK = "prcconfigurationreqfk",
		PROPOSED_QUANTITY = "proposedquantity",
		PROJECT_CODE = "ProjectCode",
		DESCRIPTION_2 = "description2",
		DESCRIPTION_INFO_2 = "descriptioninfo2",
		ALLOCATED_FROM = "allocatedfrom",
		ALLOCATED_TO = "allocatedto",
		COMPANY_IN_NAME = "companyinname",
		COMPANY_OUT_NAME = "companyoutname",
		GUARANTEE_COST_PERCENT = "guaranteecostpercent",
		MDCBRAND_FK = "mdcbrandfk",
		STOCK_CODE = "stockcode",
		STOCK_DESCRIPTION = "stockdescription",
		CATALOG_CODE = "catalogcode",
		CATALOG_DESCRIPTION = "catalogdescription",
		BRAND_DESCRIPTION = "branddescription",
		PROVISION_PER_UOM = "provisionperuom",
		IS_LOT_MANAGEMENT = "islotmanagement",
		LOT_NO = "lotno",
		QUANTITY_AVAILABLE = "quantityavailable",
		TOTAL_QUANTITY_BY_PENDING = "totalquantitybypending",
		IS_RESERVATION = "isreservation",
		IS_DELTA = "isdelta",
		DELIVERY_DATE = "deliverydate",
		PRJ_LOCATION_FK_DESCRIPTION = "prjlocationfkdescription",
		ADDRESS_FK_DESCRIPTION="addressfkdescription",
		TOTAL_VALUE_CAP = "TotalValue",
		TOTAL_PROVISION_CAP="TotalProvision",
		EXPENSES_CAP="Expenses",
		BAS_CLERK_FK="basclerkfk",
		LOGIN_ALLOWED="loginallowed",
		WEIGHTING="weighting",
		PDSUDSIDIARY_FK="pdSubsidiaryFk",
		PDSUPPLIER_FK="pdsupplierfk",
    	STOCK_CODE_CAPS="StockCode",
		TOLERANCE="tolerance",
		LOG="log",
		TOTAL_PROVISION="totalprovision",
		PROVISION_RECEIPT="provisionreceipt",
		PROVISION_CONSUMED="provisionconsumed",
		TOTAL_CONSUMED="totalconsumed",
		TOTAL_RECEIPT="totalreceipt",
		EXPENSE_TOTAL="expensetotal",
		EXPENSE_CONSUMED="expenseconsumed",
		EXPENSES="expenses",
		QUANTITY_RECEIPT="quantityreceipt",
		QUANTITY_CONSUMED="quantityconsumed",

		CLERK_DESCRIPTION="clerkDescription",
		LOGONNAME="logonname",
    	CLIENT_FK="clientfk",
    	ACCESS_ROLE_FK="accessrolefk",
    	ACCESS_GROUP_FK="accessgroupfk",
    	EXPLICITACCESS="explicitaccess",
    	INTEGRATEDACCESS="integratedaccess",
		COMMENT_TEXT_INFO = "commenttextinfo",
		CLERK_PRC_FK_DESCRIPTION = "ClerkPrcFkDescription",
		CON_HEADER_CODE="CON_HEADER_CODE",
		CON_HEADER_DESCRIPTION="CON_HEADER_DESCRIPTION",
		CON_STATUS_DESCRIPTION="CON_STATUS_DESCRIPTION",
		CON_TOTAL_VALUE_NET="CON_TOTAL_VALUE_NET",
		CON_TOTAL_VALUE_TAX="CON_TOTAL_VALUE_TAX",
		CON_TOTAL_VALUE_GROSS="CON_TOTAL_VALUE_GROSS",
		CON_HEADER_DATE_ORDERED="CON_HEADER_DATE_ORDERED",
		CON_TYPE_DESCRIPTION="CON_TYPE_DESCRIPTION",
		FLAG="FLAG",
		STOCK_TRANSACTION_TYPE_FK="stocktransactiontypefk",
		ARTICLE_DESC="articledesc",
		DISPATCH_HEADER_DESCRIPTION="dispatchheaderDescription",
		DISPATCH_RECORD_DESCRIPTION="dispatchrecordDescription",
		GROUP_ICON_DETAILS="groupicondetails",
        CON_BOQ_HEADER_FK="conboqheaderfk",
		PACKAGE_BOQ_HEADER_FK="packageboqheaderfk",
		CON_HEADER_BOQ_FK="conheaderboqfk",
		GROUP_ORDER="grouporder",
		IS_MULTISELECT="ismultiselect",
		POINTS_POSSIBLE="pointspossible",
		POINTS_MINIMUM="pointsminimum",
		REQ_HEADER_FK_DESCRIPTION="ReqHeaderFkDescription",
		REQ_HEADER_ENTITY="ReqHeaderEntity",
		PRC_HEADER_ENTITY="PrcHeaderEntity",
		REQ_HEADER_FK_CLERK_REQ_CODE="ReqHeaderFkClerkReqCode",
		REQ_HEADER_FK_COMPANY_NAME="ReqHeaderFkCompanyName",
		STATUS_NEW_FK="statusnewfk",
		STATUS_OLD_FK="statusoldfk",
		VALUE_NET_OC="valuenetoc",
		VALUE_TAX_OC="valuetaxoc",
		GROSS_OC="grossoc",
		CON_TOTAL_VALUE_NET_OC="CON_TOTAL_VALUE_NET_OC",
		CON_TOTAL_VALUE_TAX_OC="CON_TOTAL_VALUE_TAX_OC",
		CON_TOTAL_VALUE_GROSS_OC="CON_TOTAL_VALUE_GROSS_OC",
		PES_DESCRIPTION="pesdescription",
		PES_CODE="pescode",
		CON_CODE="concode",
		CON_DESCRIPTION="condescription",
		MAT_CODE="matcode",
		ALTERNATIVE_QUANTITY="alternativequantity",
		CON_QUANTITY="conquantity",
		PESSTATUS_FK="pesstatusfk",
		ITEMDATE="itemdate",
		IS_JOURNAL="isjournal",
		TRANS_VOUCHER_NUMBER="transVoucherNumber",
		TRANS_QUANTITY="transQuantity",
		TRANS_AMOUNT="transAmount",
		TRANS_CURRENCY="transCurrency",
		TRANS_ACCOUNT="transAccount",
		BAS_COMPANY_TRANSACTION_FK="bascompanytransactionfk",
		TRANS_POSTING_DATE="transPostingDate",
		TRANS_DOC_TYPE="transDocType",
		TRANS_TAX_CODE="transTaxCode",
		TRANS_POSTING_AREA="transPostingArea",
		TRANS_POSTING_NARRITIVE="transPostingNarritive",
		TRANS_OFFSET_ACCOUNT="transOffsetAccount",
		TRANS_AMOUNT_OC="transAmountOc",
		TRANS_CONTROLLING_UNIT_CODE="transControllingUnitCode",
		TRANS_CONTROLLING_UNIT_ASSIGN_01="transControllingUnitAssign01",
		TRANS_NOMINAL_DIMENSION="transNominalDimension",
		TRANS_NOMINAL_DIMENSION_2="transNominalDimension2",
		POINTS_FROM="pointsfrom",
		POINTS_TO="pointsto",
		BARCH_DATE="batchdate",
		BATCH_ID="batchid",
		CATEGORY_WIC_FK="categorywicfk",
		CATALOG_WIC_FK="catalogwicfk",
		ITEM_COUNT="itemCount",
		REMARK_INFO="remarkinfo",
		INDIVIDUAL_PERFORMANCE="individualperformance",
		START="start",
		END="end",
		CHANGE_TYPE_FK = "changetypefk",
		LAST_DATE = "lastdate",
		PROBABILITY = "probability",
		EXPECTED_COST = "expectedcost",
		EXPECTED_REVENUE = "expectedrevenue",
		REASON = "reason",
		INSTRUCTION_DATE = "instructiondate",
		SUBMISSION_DATE = "submissiondate",
		CONFIRMATION_DATE = "confirmationdate",
		USER_DEFINED_TEXT_01 = "userdefinedtext01",
		USER_DEFINED_NUMBER_01 = "userdefinednumber01",
		EVALUATION_SCHEMA_FK="evaluationschemafk",
		DATE_REQUEST = "daterequest",
		SALES_DATE_TYPE_FK="salesdatetypefk",
		SALES_DATE_KIND_FK="salesdatekindfk",
		TELEFAX="telefax",
		MEASURED_PERFORMANCE="measuredperformance",
		BOQ_LINE_TYPE_FK_CAPS= "BoqLineTypeFk",
		USER_DEFINED_TEXT_1 = "userdefinedtext1",
		USER_DEFINED_TEXT_2 = "userdefinedtext2",
		USER_DEFINED_TEXT_3 = "userdefinedtext3",
		USER_DEFINED_TEXT_4 = "userdefinedtext4",
		USER_DEFINED_TEXT_5 = "userdefinedtext5",
		USER_DEFINED_NUMBER_1 = "userdefinednumber1",
		USER_DEFINED_NUMBER_2 = "userdefinednumber2",
		USER_DEFINED_NUMBER_3 = "userdefinednumber3",
		USER_DEFINED_NUMBER_4 = "userdefinednumber4",
		USER_DEFINED_NUMBER_5 = "userdefinednumber5",
		REMAINING_OC = "remainingoc",
        DATE_ORDERED="dateordered",
		DATE_DELIVERY="datedelivery",
		INC_AMOUNT="incamount",
		INC_VAT_AMOUNT="incvatamount",
		COMPANY_FK_COMPANY_NAME="CompanyFkCompanyName",
		CONTROLLING_UNIT_ASSIGN_01="controllingunitassign01",
		CONTROLLING_UNIT_ASSIGN_02="controllingunitassign02",
		CONTROLLING_UNIT_ASSIGN_03="controllingunitassign03",
		CONTROLLING_UNIT_ASSIGN_04="controllingunitassign04",
		CONTROLLING_UNIT_ASSIGN_05="controllingunitassign05",
		CONTROLLING_UNIT_ASSIGN_06="controllingunitassign06",
		CONTROLLING_UNIT_ASSIGN_07="controllingunitassign07",
		CONTROLLING_UNIT_ASSIGN_08="controllingunitassign08",
		CONTROLLING_UNIT_ASSIGN_09="controllingunitassign09",
		CONTROLLING_UNIT_ASSIGN_10="controllingunitassign10",
		AMOUNT_OC="amountoc",
		VAT_AMOUNT_OC="vatamountoc",
		INC_AMOUNT_OC="incamountoc",
		RUNNING_NUMBER="runningnumber",
		FORMULA="formula",
		BUDGET_MARGIN="budgetmargin",
		DIR_COST_UNIT="dircostunit",
		IS_DISPATCHING="isdispatching",
		ACCEPTED_QUANTITY="acceptedquantity",
		MTG_TYPE_FK = "mtgtypefk",
		CLERK_RSP_FK = "clerkrspfk",
		MTG_URL ="mtgurl",
		START_TIME="starttime",
		FINISH_TIME="finishtime",
		BP_EMAIL="BpEmail",
		BP_SUBSIDIARY_TEL="BpSubsidiaryTel",
		CONTACT_EMAIL="ContactEmail",
		BP_SUBSIDIARY_DESCRIPTION="BpSubsidiaryDescription",
		QTN_HEADER_FK="qtnheaderfk",
		POINTS_SPACE = "points ",
		INC_VAT_AMOUNT_OC="incvatamountoc",
		PRJ_DOCUMENT_REVISION="PrjDocumentRevision",
		BAS_CLERKFK_DESCRIPTION="basclerkfkdescription",
		JOB1_FK_DESCRIPTION="job1fkdescription",
		DISPATCH_HEADER_IN_FK_DESCRIPTION="dispatchheaderinfkdescription",
		RFQ_BUSINESS_PARTNER_STATUS_FK="rfqbusinesspartnerstatusfk",
		BREAK_DURATION = "breakduration",
		IS_MANUAL_EDIT_DISPATCHING="ismanualeditdispatching",
		CODE_CAPITAL="CODE",
		DATE_RECEIVED_CAPS="DATE_RECEIVED",
		REFERENCE_CAPS = "REFERENCE",
		BPD_SUPPLIER_CODE_CAPS="BPD_SUPPLIER_CODE",
		BPD_SUPPLIER_DESCRIPTION="BPD_SUPPLIER_DESCRIPTION",
		BAS_CURRENCY_CURRENCY="BAS_CURRENCY_CURRENCY",
		AMOUNT_NET_CAPS="AMOUNT_NET",
		BAS_CLERK_CODE_DESCRIPTION="BAS_CLERK_CODE_DESCRIPTION",
		AMOUNT_VAT_CAPS="AMOUNT_VAT",
		AMOUNT_GROSS_CAPS="AMOUNT_GROSS",
		CLERK_PRC_FK_UNDEFINED="ClerkPrcFkundefined",
		AMOUNT_VAT_CONTRACT_OC="amountVatContractOc",
		AMOUNT_VAT_CONTRACT="amountVatContract",
		FINAL_PRICE_OC="finalpriceoc",
		IQ_TOTAL_QUANTITY="iqtotalquantity",
		BQ_TOTAL_QUANTITY="bqtotalquantity",
        IS_ESTIMATE_CC="isestimatecc",
		PLANT_PRICE_LIST_FK="plantpricelistfk",
		JOB2_FK_DESCRIPTION="job2fkdescription",
		PRC_COMMUNICATION_CHANNEL_FK="prccommunicationchannelfk",
		BAS_PAYMENT_TERM_ID="BasPaymentTermId",
		PRICE_PORTION_SUM="priceportionsum",
		IS_CONSOLIDATED_TRANSACTION="isconsolidatedtransaction",
		IS_CHANGE_FROMMA_INCONTRACT="ischangefrommaincontract",
		IS_FREE_ITEMS_ALLOWED="isfreeitemsallowed",
		ALLOW_ASSIGNMENT="AllowAssignment",
		TO_TIME_BREAK_TIME = "totimebreaktime",
		FROM_TIME_BREAK_TIME = "fromtimebreaktime",
		HAS_COMPANY = "hascompany",
		IS_LIMITED = "islimited",
		COST_REIMBURSABLE = "costreimbursable",
		OLD_STATUS="oldStatus",
		CHANGE_STATUS_DETAIL_ITEM_DEFAULT="change-status-detail-item-default",
		BARCODE="barcode",
		STREET = "street",
		ZIP_CODE = "zipcode",
		IS_AUTOMATIC= "isautomatic",
		IS_STARTED= "isstarted",
		IS_DELAYED= "isdelayed",
		IS_AHEAD= "isahead",
		IS_FINISHED_DELAYED= "isfinisheddelayed",
		IS_PLANNING_FINISHED= "isplanningfinished",
		IS_MOUNTING="IsMounting",
		IS_ALLOWANCE="IsAllowance",
		IS_RP="Isrp",
		IS_GA="Isga",
		IS_AM="Isam",
		IS_COMMISSIONING="IsCommissioning",
		HAS_ORDER="HasOrder",
		IS_INFORMATION="IsInformation",
		CLERK_RESPONSIBLE_DESCRIPTION="ClerkResponsibleDescription",
		STRUCTURE_CAPS="Structure",
        BOQ_LINE_TYPE_FK_CAMEL= "boqLineTypeFk",
		MERGE="Merge",
		TASK_TYPE="tasktype",
		RUN_IN_USER_CONTEXT="runinusercontext",
		EXTRA_INCREMENT="extraincrement",
		SETTLEMENT_STATUS_FK="settlementstatusfk",
		QUANTITY_MULTIPLIER="quantitymultiplier",
		ORD_HEADER_FK="ordheaderfk",
		PROJECT_NAME_1="projectName",
		ORD_HEADER_DESCRIPTION="OrdHeaderDescription",
		CON_HEADER_DESCRIPTION_1="ConHeaderDescription",
		ISSUER="issuer",
		BUSINESS_PARTNER_ISSUER_FK="businesspartnerissuerfk",
		COST_REIMBURSED_DATE="costreimburseddate",
		PRICE_GROSS_OC="pricegrossoc",
		TOTAL_PRICE_GROSS="totalpricegross",
		TOTAL_PRICE_GROSS_OC="totalpricegrossoc",
		TOTAL_GROSS_OC="totalgrossoc",
		QUANTITY_DELIVERED_SMALL="quantitydelivered",
		PES_VALUE_OC="pesvalueoc",
		VAT_OC="vatOC",
		PES_VAT_OC="pesvatoc",
		TOTAL_GROSS_OC_CAPITAL="TotalGrossOc",
		IS_NEW_WIZARDACTIVE="isnewwizardactive",
		COMPANY_RESPONSIBLE_FK = "companyresponsiblefk",
		PROFIT_CENTER = "profitcenter",
		CURRENT_START="currentstart",
		CURRENT_FINISH="currentfinish",
		ACTUAL_FINISH="actualfinish",
	    COMPANY_PERIOD_FK_END_DATE="companyperiodfkenddate",
		COMPANY_PERIOD_FK_START_DATE="companyperiodfkstartdate",
		RECORDING_FK="recordingfk",
		RECORD_TYPE="recordtype",
		RECORD_FK="recordfk",
		CHARACTER_COLUMN_5_2_21="charactercolumn_5_2_21",
		INVENTORY_DATE="inventorydate",
		TRANSACTION_DATE="transactiondate",
		AGGREGATES="aggregates",
		INV_HEADER_FK="invheaderfk",
		DOCUMENT_DATE="documentdate",
		BILL_HEADER_FK="bilheaderfk",
		WIP_HEADER_FK="wipheaderfk",
		BID_HEADER_FK="bidheaderfk",
		QTO_HEADER_FK="qtoheaderfk",
		LGM_DISPATCHING_HEADER_FK="lgmdispatchheaderfk",
		PROJECT_INFO_REQUEST_FK="projectinforequestfk",
		RFQ_HEADER_FK_DESCRIPTION="RfqHeaderFkDescription",
		URL="url",
		IS_URB="isurb",
		URB1="urb1",
		CAPS_SELECTED = "Selected",
		BILL_INVOICE_TYPE_FK= "bilinvoicetypefk",
		PRJ_CONTRACT_TYPE_FK = "prjcontracttypefk",
		BID_STATUS_FK="bidstatusfk",
		MONTH='month',
		WIP_STATUS_FK="wipstatusfk",
		BIL_STATUS_FK="bilstatusfk",
		SORT_CODE_01_FK = "sortcode01fk",
		SORT_DESC_01_FK = "sortdesc01fk",
		SORT_CODE_02_FK = "sortcode02fk",
		SORT_DESC_02_FK = "sortdesc02fk",
		SORT_CODE_03_FK = "sortcode03fk",
		SORT_DESC_03_FK = "sortdesc03fk",
		SORT_CODE_04_FK = "sortcode04fk",
		SORT_DESC_04_FK = "sortdesc04fk",
		PROJECT_CHANGE_FK="projectchangefk",
		IS_BILL_OPTIONAL_ITEMS="isbilloptionalitems",
		REQUIRED_BY = "requiredby",
		IS_REQUIRED_SUB_SUB= "isrequiredsubsub",
		IS_MANDATORY_SUB_SUB= "ismandatorysubsub",
		PRJ_CHANGE_STATUS_FK="prjchangestatusfk",
		CUSTOMER_STATUS_ID="CustomerStatusId",
		CUSTOMER_LEDGER_GROUP_ID="CustomerLedgerGroupId",
		BUSINESS_UNIT_ID="BusinessUnitId",
		CUSTOMER_BRANCH_ID="CustomerBranchId",
		REST = "rest",
		FROM_BOQ_ITEM_FK="fromboqitemfk",
		TO_BOQ_ITEM_FK="toboqitemfk",
		APPLY_SPLIT_RESULT_TO_CONFIG_QUANTITY="applySplitResultToConfig-Quantity",
		APPLY_SPLIT_RESULT_TO_CONFIG_QUANTITY_TARGET="applySplitResultToConfig-QuantityTarget",
		PROJECT_BILL_TO_ID_DESCRIPTION="prjbilltoiddescription",
		ITEM_ID_2="item-id_2",
		ITEM_ID_3="item-id_3",
		IS_CHECKED_SMALL_CASE="ischecked",
		PREV_WIPHEADERFK_DESCRIPTION="prevwipheaderfkdescription",
		PREV_WIPHEADERFK="prevwipheaderfk",
		BOQ_BILL_TOFK_DESCRIPTION="boqbilltofkdescription",
		UPDATE_BID_ID="sales_bid_create_update_wizard_2",
		CREATE_BID_ID="sales_bid_create_update_wizard_1",
		GENERATE_BASE_COST_ID="updateBaseCostGroupConfig1-IsGenerateBaseCost",
		UPDATE_BASE_COST_ID="updateBaseCostGroupConfig1-IsUpdateBaseCost",
		GRAND_TOTAL_ID="GenerateEstBudgetFrom-1",
		REVENUE_ID="GenerateEstBudgetFrom-2",
		BASE_COST_TOTAL_ID="GenerateEstBudgetFrom-3",
		COST_TOTAL_ID="GenerateEstBudgetFrom-4",
		DISCOUNTED_ABSOLUTE_GROSS_OC="discountabsolutegrossoc",
		DISCOUNTED_ABSOLUTE_GROSS="discountabsolutegross",
        ALL_LINE_ITEMS_ID="updateRevenueConfig-AllLineItems",
		SELECTED_LINE_ITEMS_ID="updateRevenueConfig-SelectedLineItems",
        COST_ID="updateRevenueConfig1-Cost",
		BUDGET_ID="updateRevenueConfig1-Budget",
		PROJECT_BOQUDP1_TOTAL="ProjectBoqUDP1total",
		PROJECT_BOQUDP1_COSTUNIT="ProjectBoqUDP1costunit",
		PROJECT_BOQUDP2_TOTAL="ProjectBoqUDP2total",
		PROJECT_BOQUDP2_COSTUNIT="ProjectBoqUDP2costunit",
		PROJECT_BOQUDP3_TOTAL="ProjectBoqUDP3total",
		PROJECT_BOQUDP3_COSTUNIT="ProjectBoqUDP3costunit",
		DISCOUNTED_ABSOLUTE="discountabsolute",
		DISCOUNTED_UNIT_PRICE = "discountedunitprice",
		FINAL_PRICE_GROSS_OC = "finalpricegrossoc",
		DISCOUNT_ABSOLUTE_OC="discountabsoluteoc",
		DISCOUNTED_UNIT_PRICE_OC="discountedunitpriceoc",
		PRC_DOCUMENT_TYPE_FK="prcdocumenttypefk",
		CONTROLLING_UNIT_CODE="controllingunitcode",
		DATE_DELIVERED="datedelivered",
		CONTROLLING_GROUP_DESCRIPTION="controllinggroupfkdescription",
		CONTROLLING_GROUP_DETAIL_DESCRIPTION="controllinggroupdetailfkdescription",
		FINAL_TOTAL="finaltotal",
		HAS_CONTROLLING_UNIT="hascontrollingunit",
		OFFSET_ACCOUNT_NO="offsetaccountno",
		CODE_RETENSION="coderetention",
		CRED_LINE_TYPE_FK="credlinetypefk",
		RESULT_OC="resultoc",
		BILLING_SCHEMA_FINAL="BillingSchemaFinal",
		BILLING_SCHEMA_FINAL_OC="BillingSchemaFinalOC",
		VALUE_2_DETAIL = "value2detail",
		VALUE_3_DETAIL = "value3detail",
		VALUE_4_DETAIL = "value4detail",
		VALUE_5_DETAIL = "value5detail",
		CREATE_TYPE1="CreateType1",
		CREATE_TYPE2="CreateType2",
		ASSET_MASTER_FK="assetmasterfk",
		IS_BLOCKED="isblocked",
		LANGUAGE_FK="LanguageFk",
		PREVIOUS_BILL_FK="PreviousBillFk",
		MATCH_CODE_CAMEL_CASE="matchCode",
		CLERK_FK_FIRST_NAME="ClerkFkFirstName",
		CLERK_FK_FAMILY_NAME="ClerkFkFamilyName",
		COMPANY_NAME_C_SMALL="companyName",
		COMPANY_CODE="companyCode",
		CLERK_FK_DEPARTMENT="ClerkFkDepartment",
		TRADING_YEAR="tradingyear",
		COMPANY_YEAR_FK="companyyearfk",
		COMPANY_TRANS_HEADER_STATUS_FK="companytransheaderstatusfk",
		ACTIVITY_DESCRIPTION="activitydescription",
		RECLAIM_DATE_1="reclaimdate1",
		RECLAIM_DATE_2="reclaimdate2",
		RECLAIM_DATE_3="reclaimdate3",
		IS_END_AFTER_MIDNIGHT="isendaftermidnight",
		AMOUNT_VAT = "amountvat",
		DISCOUNT_AMOUNT_NET = "discountamountnet",
		DISCOUNT_AMOUNT_VAT = "discountamountvat",
		PAYMENT_DATE = "paymentdate",
		POSTING_DATE = "postingdate",
		BANK_VOUCHER_NO = "bankvoucherno",
		BANK_ACCOUNT = "bankaccount",
		POSTING_NARRITIVE = "postingnarritive",
		AMOUNT_NET_SPACE = "amount_net",
		DISCOUNT_AMOUNT = "discountamount",
		FROM_PAYMENT_TOTAL_PAYMENT = "frompaymenttotalpayment",
		FROM_PAYMENT_TOTAL_PAYMENT_DISCOUNT = "frompaymenttotalpaymentdiscount",
		IS_RETENSION = "isretention",
		IS_OVER_PAYMENT = "isoverpayment",
		IS_START_AFTER_MIDNIGHT="isstartaftermidnight",
		STATE = "state",
		REFERENCE_TYPE= "referencetype",
		DEPARTURE_RATING_PERCENT="departureratingpercent",
		USERDEFINED_01="userdefined01",
		USERDEFINED_02="userdefined02",
		USERDEFINED_03="userdefined03",
		USERDEFINED_04="userdefined04",
		USERDEFINED_05="userdefined05",
		IS_RESOURCE="isresource",
		IS_BILLED="isbilled",
		IS_POSTED="isposted",
		IS_CHANGE="ischange",
		IS_DRIVER="isdriver",
		IS_BOLD_BILLING_SCHEMA = "isbold",
		IS_ITALIC_BILLING_SCHEMA ="isitalic",
		IS_HIDDEN = "ishidden",
		IS_HIDDEN_IF_ZERO="ishiddenifzero",
		GROUP_1="group1",
		GROUP_2="group2",
		IS_NET_ADJUSTED="isnetadjusted",
		IS_UNDERLINE_BILLING_SCHEMA="isunderline",
		DEB_FACTOR="debfactor",
		CODE_RETENTION="coderetention",
		DETAILS_AUTHOR_AMOUNT_FK="detailauthoramountfk",
		BILLING_SCHEMA_DETAIL_TAX_FK="billingschemadetailtaxfk",
		DEB_LINE_TYPE_FK="deblinetypefk",
		COST_LINE_TYPE_FK="costlinetypefk",
		PLACE_OF_WORK_FK="placeofworkfk",
		EMP_CERTIFICATE_FK="empcertificatefk" ,
		PROJECT_CHANGE_FK_DESCRIPTION="ProjectChangeFkDescription",
		PROJECT_CHANGE_TYPE="ProjectChangeType",
		GROUP="group",
		IS_CLOCKING="isclocking",
		ABSENT_TILL="absenttill",
		DEFAULT_PLAT_KIND_FK="defaultplantkindfk",
		DEFAULT_PROCUREMENT_STRUCTURE_FK="defaultprocurementstructurefk",
		DEFAULT_PLANT_TYPE_FK="defaultplanttypefk",
		TOTAL_COST="totalcost",
		B_BQ_TOTAL="b_bq_total",
        B_BQ_IN_RP="b_bq_in_rp",
        B_BQ_TO_RP="b_bq_to_rp",
        EC_BQ_TOTAL="ec_bq_total",
        EC_BQ_IN_RP="ec_bq_in_rp",
        EC_BQ_TO_RP="ec_bq_to_rp",
        ER_BP_TOTAL="er_bq_total",
        ER_BQ_IN_RP="er_bq_in_rp",
        ER_BQ_TO_RP="er_bq_to_rp",
		TOTAL_COST_COMPLETED="totalcostcompleted",
		INV_HEADER_CHAINED_FK="invheaderchainedfk",
        INV_HEADER_CHAINED_FK_STATUS_DESCRIPTION_INFO="InvHeaderChainedFkStatusDescriptionInfo",
        INV_STATUS_ERROR_FK="invstatuserrorfk",
        INV_HEADER_CHAINED_FK_DESCRPTION="InvHeaderChainedFkDescription",
        INV_HEADER_CHAINED_FK_REFERENCE="InvHeaderChainedFkReference",
		INV_HEADER_CHAINED_FK_DATE_INVOICED="InvHeaderChainedFkDateInvoiced",
		INV_HEADER_CHAINED_FK_DATE_RECEIVED="InvHeaderChainedFkDateReceived",
		DESCRIPTION_SMALL="description",
		DATE_INVOICED="dateinvoiced",
		IS_ABSENCE="isabsence",
		IS_PLANNED="IsPlanned",
		IS_STOCK_POSTED="IsStockPosted",
		HAS_LOADING_COSTS="HasLoadingCosts",
		DATE_PRICE_FIXING="datepricefixing",
		IS_EXCLUDED= "isexcluded",
		IS_SHORTLISTED= "isshortlisted",
		IS_VALIDATED="isvalidated",
		OVERALL_DISCOUNT_PERCENT="overalldiscountpercent",
		OVERALL_DISCOUNT_OC="overalldiscountoc",
		SALES_TAX_METHOD_FK="salestaxmethodfk",
		SUPPLIER_FK_UNDEFINED="SupplierFkundefined",
		BRIEF_CAPS = "Brief",
		PROGRESS_INVOICE_NO="progressinvoiceno",
		UUID = "uuid",
		NAME_CAPS= "Name",
		ACCESS_RIGHT_DESCRIPTOR_FK="accessrightdescriptorfk",
		IS_ARCHIVED="Isarchived",
		DISPATCH_HEADER_OUT_FK_DESCRIPTION="dispatchheaderoutfkdescription",
		DISPATCH_STATUS_FK="dispatchstatusfk"	,
		PROJECT_FK_PROJECT_NAME_SMALL = "projectfkprojectname",
		RESERVATION_STATUS_FK="reservationstatusfk",
		SELECT = "select",
		COMPANY_OPERATING_FK="companyoperatingfk",
		CHARACTER_COLUMN_1000004_3_1000005="charactercolumn_1000004_3_1000005",
		_100="100",
		HOURS_ACTUAL_TOTAL="hour_actual_total",
		HOURS_ACTUAL_IN_RP="hour_actual_in_rp",
		HOURS_ACTUAL_TO_RP="hour_actual_to_rp",
		HOUR_FQ_TOTAL="hour_fq_total",
		HOUR_FQ_IN_RP="hour_fq_in_rp",
		HOUR_FQ_TO_RP="hour_fq_to_rp",
		HOUR_IQ_TOTAL="hour_iq_total",
		HOUR_IQ_TO_RP="hour_iq_to_rp",
		HOUR_IQ_IN_RP="hour_iq_in_rp",
		HOUR_AQ_TOTAL="hour_aq_total",
		HOUR_AQ_IN_RP="hour_aq_in_rp",
		HOUR_AQ_TO_RP="hour_aq_to_rp",
		HOUR_WQ_TOTAL="hour_wq_total",
		HOUR_WQ_TO_RP="hour_wq_to_rp",
		HOUR_WQ_IN_RP="hour_wq_in_rp",
		REVISION = "revision",
		ORIGIN_FILE_NAME = "originfilename",
		MODEL_FK = "modelfk",
		HAS_DOCUMENT_REVISION = "hasdocumentrevision",
		FILE_SIZE = "filesize",
		BPD_CERTIFICATE_FK = "bpdcertificatefk",
		EXTENTION = "extention",
		TIME_SYMBOL_FK_DESCRIPTION="timesymbolfkdescription",
		TOTAL_IQ_ACCEPTED="totaliqaccepted",
		BILLING_METHOD_FK="billingmethodfk",
		IS_DURATION_QUANTITY_ACTIVITY="isdurationquantityactivity",
		FROM_DATE_EST="fromdate",
		TO_DATE_EST="todate",
		HOUR_MONTH="hourmonth",
		MDC_COSTCODE="mdc_costcode",
		MDC_COSTCODE_CAPS="Mdc_CostCode",
		FINAL_GROSS_OC="finalgrossoc",
		COST_GROUP_LIC_LICCG2="costgroup_lic_liccg2",
		SUPPLIER_NO="supplierno",
		EST_ASSEMBLY_CATFK="estassemblycatfk",
		HIGHLIGHTED_QUANTITY_TAKEOFF_LINE="QtoScope_1",
		CURRENT_RESULT_SET="QtoScope_2",
		ENTIRE_QUANTITY_TAKEOFF="QtoScope_3",
		BUSINESS_PARTNER2_FK="businesspartner2fk",
        OWNER_STATUS="OwnerStatus",
        OPPOSITE_STATUS="OppositeStatus",
        RELATION_TYPE_FK="relationtypefk",
		BASIC_COST_CODE_UDP1_COST_UNIT="BasicsCostCodeUDP1costunit",
		BASIC_COST_CODE_UDP2_COST_UNIT="BasicsCostCodeUDP2costunit",
		BASIC_COST_CODE_UDP3_COST_UNIT="BasicsCostCodeUDP3costunit",
		EST_COLUMN_ID="estColumnId",
		EST_LINE_TYPE="estLineType",
		EST_MDC_COST_CODE_DESCRIPTION="estMdcCostCodeDescription",
		EST_COLOUMN_HEADER="estColumnHeader",
		CONF_DETAIL="ConfDetail",
		ADDRESS_LINE_CAPS="AddressLine",
		SUBSIDIARY_ADDRESS="subsidiaryAddress",
		SUBSIDIARY_2_ADDRESS="subsidiary2Address",
		BP_SUBSIDIARY_2_FK="bpsubsidiary2fk",
		OPPOSITE_DESCRIPTION_INFO="oppositedescriptioninfo",
		OPPOSITE_DESCRIPTION="oppositeDesc",
		ITEM_WIC_FK="itemwicfk",
		REMAINING_ACTIVITY_WORK="remainingactivitywork",
		PERIOD_WORK_PERFORMANCE="periodworkperformance",
		DUE_DATE_WORK_PERFORMANCE="duedateworkperformance",
		ACTUAL_DURATION="actualduration",
		SALES_BID_CREATE_UPDATE_WIZARD_4="sales_bid_create_update_wizard_4",
		SALES_BID_CREATE_UPDATE_WIZARD_5="sales_bid_create_update_wizard_5",
		BP_SUBSIDIARY_FK="bpsubsidiaryfk",
		BUSINESS_PARTNER_STATUS="BusinessPartnerStatus",
		SUB_DES="subDes",
		ZIP_CODE_CAMEL_CASE = "zipCode",
		ADDRESS_TYPE="addrType",
		ADDRESS_LINE="addressLine",
		ADDRESS_TYPE_COMPLETE="addressType",
		IS_MAIN_ADDRESS_BP_OPP_LIST="isMainAddr",
		IS_MAIN_ADDRESS_CAMEL_CASE="isMainAddress",
		COUNTRY="country",
		ISO2="iso2",
		CLERK_PRC_DESCRIPTION = "clerkprcdescription",
		PRJ_STOCK_FK_PROJECT_NAME="prjstockfkprojectname",
		PRJ_STOCK_FK_COMPANY_CODE="prjstockfkcompanycode",
		DATA_DELIVERD_FROM="datedeliveredfrom",
		INVOICE_QUANTITY="invoicequantity",
		MDC_SALES_TAX_GROUP_FK="mdcsalestaxgroupfk",
		ALTERNATIVE_UOM_FK="alternativeuomfk",
		IS_FINAL_DELIVERY="isfinaldelivery",
		PRC_ITEMS_SPECIFICATION="prcitemspecification",
		MATERIAL_EXTERNAL_CODE="materialexternalcode",
		QUANTITY_ASKED_FOR="quantityaskedfor",
		PRC_STOCK_TRANSACTION_FK="prcstocktransactionfk",
		SALES_BID_CREATE_UPDATE_WIZARD_3="sales_bid_create_update_wizard_3",
		TRANSFER_MDC_COST_CODE_FK="transfermdccostcodefk",
		TRANSFER_MDC_MATERIAL_FK="transfermdcmaterialfk",
		KEY="key",
		CATALOG_CONFIG_TYPE_FK="catalogconfigtypefk",
		BOQ_ITEM_FK_CAMEL_CASE="BoqItemFk",
		QTO_LINE_TYPE_FK="QtoLineTypeFk",
		PLANT_GROUP_FK_DESCRIPTION="plantgroupfkdescription",
		HOUR_FACTOR="factorhour",
		ACTION="action",
		LIST_PRICE_1="listprice1",
		DISCOUNT_1="discount1",
		CHARGES_1="charges1",
		DAY_WORK_RATE_1="dayworkrate1",
		ESTIMATE_PRICE_1="estimateprice1",
		CODE_1="code1",
		PRICE_EXTRA_1="priceextra1",
		PRICE_UNIT_1="priceunit1",
		PRC_PRICE_CONDITION_1="prcpriceconditionfk1",
		FACTOR_PRICE_UNIT_1="factorpriceunit1",
		AMOUNT_DISCOUNT_BASIS_OC = "amountdiscountbasisoc",
		AMOUNT_DISCOUNT_OC="amountdiscountbasisoc",
		SUBSCHEMA_1="subschema1",
		SUBSCHEMA_2="subschema2",
		SUBSCHEMA_3="subschema3",
		SUBSCHEMA_4="subschema4",
		SUBSCHEMA_5 = "subschema5",
		RUBRIC_CATEGORY_PERIOD_FK="rubriccategoryperiodfk",
		IS_HIGH_IMPORTANCE = "ishighimportance",
		PAYMENT_STATUS_FK="paymentstatusfk",
		CONTROLLING_UNIT_REVENUE_CU="controllingunitrevenuefk",
		SETTLEMENT_ITEM_PRICE_OC="priceorigcur",
		PLANT_SUPPLIER="plantsupplierfk",
		PRICE_PORTION_OC_3="priceorigcur3",
		IS_OVER_NIGHT_TRAVEL="isovernighttravel",
		STATUS="status"
	}


	export enum InputFields {
		DOMAIN_TYPE_CODE = "domain-type-code",
		DOMAIN_TYPE_TRANSLATION = "domain-type-translation",
		INPUT_GROUP_CONTENT = "input-group-content",
		DOMAIN_TYPE_DESCRIPTION = "domain-type-description",
		FILTER_INPUT = "filterinput",
		INPUT_GROUP_FORM_CONTROL = "input-group form-control",
		SEARCH_FIELD = "searchfield",
		DOMAIN_TYPE_COMMENT = "domain-type-comment",
		DOMAIN_TYPE_MONEY = "domain-type-money",
		DOMAIN_TYPE_QUANTITY = "domain-type-quantity",
		DOMAIN_TYPE_BOOLEAN = "domain-type-boolean",
		FORM_CONTROL = "form-control",
		DOMAIN_TYPE_REMARK = "domain-type-remark",
		INPUT_GROUP_DOMAIN_TYPE_SELECT = "input-group domain-type-select",
		FLEX_BOX_RULE = "flex-box rule",
		FLEX_BOX = "flex-box",
		INPUT_GROUP_DOMAIN = "input-group domain",
		INPUT_GROUP_CONTENT_DATE_PICKER_INPUT = "input-group-content datepickerinput",
		QUANTITY_INPUT = "quantity-input",
		INPUT_GROUP_LOOKUP_CONTAINER = "input-group lookup-container",
		DOMAIN_TYPE_TEXT = "domain-type-text",
		ICO_INPUT_LOOKUP = "ico-input-lookup",
		DOMAIN_TYPE_TIME = "domain-type-time",
		DOMAIN_TYPE_DIRECTIVE = "domain-type-directive",
		DOMAIN_TYPE_IBAN = "domain-type-iban",
		DOMAIN_TYPE_TIME_UTC = "domain-type-timeutc",
		K_CONTENT_FRAME = "k-content-frame",
		DOMAIN_TYPE_FACTOR = "domain-type-factor",
		SEARCH_BUTTON_TEXT = "search-button-text",
		SCHEDULE_FK = "schedulefk",
		DOMAIN_TYPE_RADIO = "domain-type-radio",
		ICO_DOWN = "ico-down",
		MS_SV_SEARCH_BUTTON_TEXT = "ms-sv-search-button-text",
		CHECKBOX = "checkbox",
		INPUT_GROUP = "input-group",
    	DOMAIN_TYPE_PASSWORD="domain-type-password",
		PRISTINE="ng-pristine",
		SLICK_GROUPED="slick-grouped",
		DESCRIPTION="dec",

	}

	export enum ContainerElements {
		SUBVIEW_HEADER_TOOLBAR = "subview-header toolbar",
		SUBVIEW_HEADER = "subview-header",
		NG_BINDING = "ng-binding",
		FLEX_ELEMENT = "flex-element",
		CARET = "caret",
		CART_FOOT_BUTTON = "cart-foot-button",
	}

	export enum TabBar {
		PROJECT = "tab_383",
		ESTIMATE = "tab_911",
		ASSEMBLIES = "tab_919",
		BOQBASED = "tab_1315",
		CONTRACTS = "tab_1372",
		BOQ = "tab_1379",
		PACKAGE = "tab_1312",
		BOQS = "tab_1379",
		BOQSTRUCTURE = "tab_385",
		ESTIMATELINEITEM = "tab_883",
		SCHEDULING = "tab_384",
		BIDBOQ = "tab_1356",
		BID = "tab_1337",
		WIPBOQ = "tab_1550",
		PERFORMANCE = "tab_376",
		BILLS = "tab_1382",
		APPLICATIONS = "tab_1551",
		WIP = "tab_1383",
		CONTRACTBOQ = "tab_1374",
		PROCUREMENTCONTRACTBOQ = "tab_818",
		DETAIL = "tab_885",
		GANTT = "tab_377",
		PLANNING = "tab_374",
		QTOHEADER = "tab_884",
		LOB = "tab_375",
		PRICECOMPARISON = "tab_924",
		CONTRACT = "tab_415",
		INVOICES = "tab_928",
		BOQDETAILS = "tab_1347",
		MODULES = "tab_913",
		WIZARD = "tab_1385",
		QUOTES = "tab_408",
		QUOTE_ITEM = "tab_410",
		CONTROLLINGSTRUCTURE = "tab_434",
		HEADER = "tab_1319",
		MAIN = "tab_397",
		PERFORMANCEENTRYSHEET = "tab_1349",
		ESTIMATEBYBOQ = "tab_1335",
		RECORDS = "tab_796",
		PESBOQ = "tab_900",
		WIC = "tab_927",
		RFQ = "tab_389",
		WORKITEMCATALOG = "tab_927",
		REQUISITIONITEMS = "tab_399",
		REQUISITIONBOQS = "tab_401",
		GENERALS = "tab_895",
		ROLE = "tab_1300",
		MASTER_DATA = "tab_920",
		MATERIAL_CATALOG = "tab_893",
		PROCUREMENTSTRUCTURE = "tab_892",
		BILLING_SCHEMA = "tab_935",
		COSTGROUPS = "tab_904",
		CHARACTERISTIC_GROUP = "tab_886",
		DETAILSMATERIAL = "tab_797",
		COST_GROUPS = "tab_904",
		NUMBER_SERIES = "tab_406",
		CATALOGS = "tab_893",
		BUSINESS_PARTNERS = "tab_391",
		QUOTEBOQS = "tab_1304",
		MASTERDATA = "tab_920",
		ORDER_ITEM = "tab_417",
		APPLICATION = "tab_929",
		GENERAL_CONTRACT = "tab_418",
		PAYMENT_TERMS = "tab_932",
		SEARCH = "tab_901",
		CART = "tab_902",
		DETAILS = "tab_416",
		PRICE_CONDITION = "tab_1303",
		CERTIFICATES = "tab_896",
		CONTACTS_BP = "tab_394",
		CALENDER = "tab_387",
		TAXCODE = "tab_1613",
		COST_CODES = "tab_663",
		QUOTES_ITEMS = "tab_410",
		ROLES = "tab_1332",
		PES_ITEMS = "tab_1310",
		ATTRIBUTES = "tab_899",
		GENERAL = "tab_400",
		BOQ_COPY = "tab_386",
		TRANSACTION_HEADER = "tab_1574",
		COMPANY = "tab_405",
		CLERK = "tab_402",
		TEXT_MODULES_MAIN = "tab_1306",
		ORDERHISTORY = "tab_903",
		PROCUREMENT_VIEW = "tab_1342",
		PLANTS_OVERVIEW = "tab_1496",
		CURRENCY = "tab_419",
		PRICE_COMPARISON_BOQ = "tab_1307",
		OTHERS = "tab_1357",
		UNIT = "tab_404",
		CHANGES = "tab_1392",
		QTO_MAIN = "tab_889",
		TYPES = "tab_1563",
		GENERAL_CONTRACTOR_CONTROLLING = "tab_1629",
		PLANT_GROUP_AND_LOCATIONS = "tab_1497",
		ACTIVITY_TEMPLATES = "tab_421",
		ACTIVITY_CONTROL_UNIT = "tab_422",
		TEMPLATES = "tab_1561",
		RESOURCE_MAINTENANCE = "tab_1560",
		MAINTENANCE = "tab_1586",
		ESTIMATE_BY_ACTIVITIES = "tab_909",
		PLANT_PRICING = "tab_1649",
		PLANT_ESTIMATE = "tab_1650",
		CARDS = "tab_1562",
		ACTIVITIES_AND_RECORDS = "tab_1580",
		PRICE_COMPARISON_ITEMS = "tab_1308",
		INDEX_HEADER = "tab_1566",
		PAYMENTGROUPS = "tab_1618",
		SHIFT_MODEL = "tab_1554",
		TIME_SYMBOLS = "tab_1555",
		WORKTIMEMODEL = "tab_1621",
		EMPLOYEE = "tab_1553",
		PERIOD = "tab_1569",
		REVIEW_RECORDINGS = "tab_1568",
		COS_MASTER = "tab_1359",
		COMMERTIAL_DATA = "tab_1584",
		LOCATIONS = "tab_1000002",
		LOGISTIC_JOB = "tab_1506",
		PLANT = "tab_1582",
		LOGISTIC_PRICE_CONDITION = "tab_1514",
		CONTROLLING_UNIT_TEMPLATE = "tab_1633",
		SETTLEMENT = "tab_1516",
		PLANT_DELIVERY = "tab_1511",
		CONTROLLING_PROJECT_CONTROLS = "tab_1648",
		CONTROLLING_COST_CODES = "tab_1520",
		ACTUALS = "tab_1576",
		RULES = "tab_1377",
		SETTLEMENT_ITEMS = "tab_1000018",
		EFB_SHEETS = "tab_1606",
		MANAGED_PLANTS = "tab_1000023",
		COST_CODES_AND_MATERIALS = "tab_1581",
		TYPES_RESOURCE_TYPE = "tab_1491",
		CONTROLLING_CONFIGURATION = "tab_1647",
		SKILLS = "tab_1558",
		TRANSACTIONS = "tab_1000019",
		STOCK = "tab_1407",
		CONTACT = "tab_1000001",
		FORMS = "tab_432",
		COUNTRIES = "tab_925",
		CERTIFICATE = "tab_934",
		INVENTORY_HEADER = "tab_1575",
		ORDER_PROPOSAL = "tab_1604",
		RESOURCE_REQUISITION = "tab_1492",
		SCHEMAS_MAIN = "tab_933",
    	USERS="tab_1316",
    	GROUPS="tab_1318",
		SCHEDULER="tab_1640",
		WORKFLOW="tab_915",
		TIMEKEEPING_TIME_ALLOCATION="tab_1626",
		TIME_CONTROLLING="tab_1641",
		RESOURCES="tab_1398",
		ASSET_MASTER="tab_894",
		SITE="tab_1418",
		RESERVATION="tab_1489",
		BILL_TRANSACTION="tab_1552",
		WORKFLOW_ADMINISTRATION="tab_1325",
		FUEL_CONSUMPTION="tab_1000021",
		STRUCTURES="tab_1298"
	}

  	export enum FooterTab {
		PROJECTS = "projects",
		COSTCODES_JOB_RATES = "costcodesjobrates",
		ESTIMATE = "estimate",
		ESTIMATE_RULES = "estimaterules",
		ESTIMATE_RULE = "estimaterule",
		RULES = "rules",
		ESTIMATE_RULE_PARAMETER_VALUE = "estimateruleparametervalue",
		ESTIMATE_RULES_PARAMETER = "estimaterulesparameter",
		LINE_ITEMS = "lineitems",
		LINE_ITEMS_STRUCTURE = "lineitemsstructure",
		RESOURCES = "resources",
		BOQs = "boqs ",
		SCHEDULES = "schedules",
		ASSEMBLIES = "assemblies",
		CONTRACTS = "contracts",
		BOQ_STRUCTURE = "boqstructure",
		BIDS = "bids",
		BIDDERS = "bidders",
		LINE_ITEM_QUANTITY = "lineitemquantity",
		ACTIVITY_STRUCTURE = "activitystructure",
		ITEMS = "items",
		WIP = "wip",
		RESOURCES_SUMMARY = "resourcessummary",
		PACKAGEITEMS = "items",
		PACKAGEDETAILS = "packagedetail",
		TOTAL = "totals",
		PACKAGE = "package",
		SOURCE_BOQ = "sourceboq",
		BILLS = "bills",
		COST_GROUP = "costgroup",
		LOCATIONS = "locations",
		CONTROLLING_UNITS = "controllingunits",
		CONTROLLING_UNIT = "controllingunit",
		CONFIGURATION_HEADER = "configurationheader",
		TOTAL_TYPES = "totaltypes",
		CONFIGURATIONS = "configurations",
		BILLING_SCHEMATA = "billingschemata",
		PROCUREMENT_BOQ = "procurementboqs",
		CONTRACTDETAILS = "contractdetail",
		INVOICEHEADER = "invoiceheader",
		PROCUREMENT_STRUCTURE = "procurementstructure",
		QUOTES = "quotes",
		PACKAGE_ITEM_ASSIGNMENT = "packageitemassignment",
		TOTALS = "totals",
		COSTGROUPCATALOG = "costgroupcatalogs",
		COSTGROUPS = "costgroups",
		SOURCE_LINEITEM = "sourcelineitems",
		WIC_GROUPS = "wicgroups",
		WIC_CATALOGS = "wiccatalogs",
		ASSEMBLY_ASSIGNMENT = "assemblyassignment",
		GANTT_TREEGRID = "gantttreegrid",
		PROGRESS_REPORT = "progressreporthistory",
		ACTIVITIES = "activities",
		QUANTITY_TAKEOFF = "quantitytakeoff",
		QUANTITYTAKEOFFHEADER = "quantitytakeoffheader",
		BILLDETAILS = "billdetails",
		PAYMENT_SCHEDULE = "paymentschedule",
		RELATED_ASSEMBLIES = "relatedassemblies",
		HEADERS = "headers",
		PAYMENTSCHEDULE = "paymentschedule",
		MATERIAL_RECORDS = "materialrecords",
		ASSEMBLY_RESOURCE = "assemblyresources",
		MATERIALS = "materials",
		ASSEMBLY = "assembly ",
		WIC = "wic",
		RFQ = "rfq",
		REQUISITION = "requisitions",
		WICGROUPS = "wicgroups",
		WICCATALOGUES = "wiccatalogues",
		REQUISITIONS = "requisitions",
		HEADER_TEXTS = "headertexts",
		GENERALS = "generals",
		CLERK = "clerk",
		CERTIFICATES = "certificates",
		CHARATERISTICS = "characteristics",
		CHARATERISTICS2 = "characteristics2",
		PROCUREMENT_STRUCTURES = "procurementstructures",
		HEADER_TEXT = "headertext",
		CLERK_RIGHT = "clerkrights",
		SUB_PACKAGE = "subpackage",
		DATA_TYPES = "datatypes",
		DATA_RECORDS = "datarecords",
		MATERIALCATALOG = "materialcatalogs",
		MATERIALGROUP = "materialgroups",
		MATERIALFILTER = "materialcatalogfilter",
		TAX_CODE = "taxcodes",
		ROLES = "roles",
		BILLINGSCHEMA = "billingschemas",
		BILLINGSCHEMADETAILS = "billingschemadetails",
		COSTGROUPCATALOG1 = "costgroupcatalog",
		CHARACTERISTIC_GROUP = "characteristicgroups",
		CHARACTERISTIC_SECTIONS = "characteristicsections",
		MATERIALGROUPFILTER = "materialgroupfilter",
		COSTGROUP = "costgroup",
		COST_GROUP_CATALOG = "costgroupcatalog",
		BRANCHES = "branches",
		SUPPLIERS = "suppliers",
		MATERIAL_CATALOGS = "materialcatalogs",
		ESTIMATELINEITEM = "estimatelineitem",
		PROJECTCALENDARS = "projectcalendars",
		CALENDARSTYPE = "calendartypefk",
		BOQS = "boqs ",
		MODULES = "modules",
		DISCOUNT_GROUP = "discountgroups",
		TRASLATION = "translation",
		STANDARS_ALLOWANCES = "standardallowances",
		DATA_RECORD = "datarecords",
		ATTRIBUTES = "attributes",
		CONTACTS = "contacts",
		PRICE_COMPARISON_ITEM = "pricecomparison(items)",
		NARRIVITE_SCRIPT = "narrativescript",
		BUSINESS_PARTNER = "businesspartners",
		BANKS = "banks",
		PAYMENT_TERMS = "paymentterms",
		PRICEADJUSTMENT = "priceadjustment",
		NUMBERRANGE = "numberrange",
		BILLING_SCHEMA = "billingschema",
		INVOICE_HEADER_DETAILS = "invoiceheaderdetail",
		CONTRACT_ITEMS = "contractitems",
		MILESTONES = "milestones",
		SUBCONTRACTOR = "subcontractors",
		DOCUMENT = "document ",
		OVERVIEW = "overview",
		CART_ITEMS = "cartitem",
		COMMODITY_SEARCH = "commoditysearch",
		PRICE_CONDITION = "pricecondition",
		SPECIFICATION_FORMATTED_TEXT = "specificationformattedtext",
		SPECIFICATION_FORMATTED_TEXT_ITEM = "specificationformattedtext-item",
		PRICE_VERSIONS = "priceversions",
		PRICE_LISTS = "pricelists",
		ATTRIBUTES_VALUES = "attributevalues ",
		CONTRACT_ATTACHMENTS = "contractattachments",
		PRICE_COMPARISONBOQ = "pricecomparison(boq)",
		DELIVERY_SCHEDULE = "deliveryschedules",
		RECONSILIATION = "reconciliation",
		SIMPLE_COMPARISON = "simplecomparison",
		CALENDARS = "calendars",
		CALENDARSDETAILS = "calendardetails",
		SUGGESTED_BIDDERS = "suggestedbidders",
		DOCUMENTS_PROJECT = "documentsproject",
		PERFORMANCE_ENTRY_SHEETS = "performanceentrysheets",
		RECONCILIATION = "reconciliation",
		BILL_OF_QUANTITY = "billofquantity",
		ITEM_TEXTS = "itemtexts",
		VARIANT = "variant",
		SCOPE_OF_SUPPLY = "scopeofsupply",
		STOCK_LOCATION = "stocklocations",
		PROJECT_STOCK_MATERIALS = "projectstockmaterials",
		TAXCODE = "taxcode",
		ITEM_ASSIGNMENT = "itemassignment",
		ASSEMBLYCATEGORY = "assemblycategories",
		COSTCODES = "costcodes",
		COSTCODEDETAILS = "costcodesdetails",
		ASSEMBLY_CATEGORIES = "assemblycategories",
		BP_CERTIFICATE = "currentcertificates",
		RIGHTS = "rights",
		BP_EVALUATION = "evaluation",
		REJECTIONS = "rejections",
		SPECIFICATION_PLAIN_TEXT = "specificationplaintext",
		CATALOGS_TO_COMPANIES = "catalogtocompanies",
		ACTUAL_CERTIFICATE_DETAIL = "actualcertificatedetail",
		EVENT = "event",
		PROCUREMENT_EVENTS = "procurementevents",
		TRANSACTION_HEADERS = "transactionheaders",
		COMPANIES = "companies",
		CLERKS = "clerks",
		TEXT_ASSEMBLIES = "textassemblies",
		PLAIN_TEXT = "plaintext",
		ITEM_PLAIN_TEXT = "itemplaintexts",
		RECONCILLIATION = "reconciliation",
		OTHER_SERVICES = "otherservices",
		TRANSACTION = "transaction",
		VALIDATIONS = "validations",
		ORDER_REQUEST = "orderrequest",
		PLANTS = "plants",
		HOME_CURRENCY = "homecurrency",
		CURRENCY_CONVERSION = "currencyconversion",
		HEADERPLAINTEXT = "headerplaintext",
		ACCRUALS = "accruals",
		ESTIMATERESOURCE = "estimateresource",
		ESTIMATE_RESOURCE = "estimateresource",
		SPLIT_QUANTITY = "splitquantities",
		COSTCODES_JOBRATE = "costcodesjobrates",
		COST_CODE_JOB_RATE = "costcodesjobrates",
		ALLOWANCE_MARKUP = "allowancemarkup",
		CONTRACT_STRUCTURE_MAIN_SUB = "contractstructure(main/sub)",
		EVENTS = "events",
		UNITOFMEASUREMENT = "unitsofmeasurement",
		UNITOFMEASUREMENTDETAILS = "unitofmeasurementdetails",
		CHANGES = "changes",
		QUANTITY_TAKEOFF_RUBERIC = "quantitytakeoffrubric",
		FORMULA = "formula",
		Formula = "formula",
		OPERATION_TYPE = "operationtypes",
		PLANT_TYPES = "planttypes",
		COST_CONTROL = "costcontrol",
		PLANT_GROUPS = "plantgroups",
		BUDGET_SHIFT = "budgetshift",
		PRC_PACKAGES = "prcpackages",
		ACTIVITY_GROUPS = "activitygroups",
		PERFORMANCE_RULES = "performancerules",
		BILL_TO = "billto",
		CONTROLLING_UNIT_GROUPS_ASSIGNMENT = "controllingunitgroupsassignment",
		ADDITIONAL_EXPENSES = "additionalexpenses",
		CONTRACT_SALES = "contractsales",
		PRC_INVOICE = "prcinvoices",
		PRC_PES = "prcpes",
		PRC_CONTRACTS = "prccontracts",
		CARD_TEMPLETS = "cardtemplates",
		RECORDS = "records",
		PRICE_LISTS_SMALL = "pricelists",
		MAINTENANCE_SCHEMES = "maintenanceschemes",
		SCHEME_RECORDS = "schemerecords",
		COMPONENTS = "components",
		MAINTENANCE = "maintenance",
		BID_DETAILS = "biddetails",
		LOCATIONS_DETAILS = "locationdetails",
		SURCHARGE_ON = "surchargeon",
		COMPATIBLE_MATERIALS = "compatiblematerials",
		COMPONENT_WARRANTIES = "componentwarranties",
		PLANT_PRICELIST_TYPES = "plantpricelisttypes",
		ESTIMATE_PLANT_PRICELISTS = "estimateplantpricelists",
		PLANT_PRICELISTS = "plantpricelists",
		WIC_CATALOGUES = "wiccatalogues",
		COMPATIBLE_ACCESSORIES = "compatibleaccessories",
		SPECIFIC_VALUES = "specificvalues",
		ASSIGNMENTS = "assignments",
		CARDS = "cards",
		SCHEDULE_DETAILS = "scheduledetails",
		WIC_BOQS = "wicboqs",
		ASSEMBLY_STRUCTURE = "assemblystructure",
		SUB_SCHEDULES = "sub-schedules",
		SUB_SCHEDULE_DETAILS = "sub-scheduledetails",
		LINE_ITEM_PROGRESS = "lineitemprogress",
		COMPONENT_MAINTENANCE_SCHEMAS = "componentmaintenanceschemas",
		METER_READING = "meterreadings",
		COST_CODES = "costcodes ",
		REFERENCES = "references",
		BASE_LINES = "baselines",
		WIZARD_GROUP = "wizardgroup",
		WIZARD_TO_GROUP = "wizardstogroup",
		KEY_DATES = "keydates",
		INDEX_HEADER = "indexheader",
		INDEX_DETAIL = "indexdetail",
		CONFIDENCE_CHECK = "confidencecheck",
		AUTOMATIC_ASSIGNMENT = "automaticassignment",
		CUSTOMER = "customer",
		USED_IN_COMPANY = "usedincompany",
		BP_CHARACTERISTICS = "bp_characteristics",
		BUSINESS_PARTNERS = "business_partners",
		ACTIONS = "actions",
		PAYMENT_GROUPS_LIST = "paymentgrouplist",
		PAYMENT_GROUP_RATES = "paymentgrouprates",
		SHIFT_MODELS = "shiftmodels",
		WORKING_TIMES = "workingtimes",
		TIME_SYMBOLS = "timesymbols",
		ACCOUNT_ALLOCATION_RULES = "accountallocationrules",
		WORKING_TIME_MODELS = "workingtimemodels",
		WORKING_TIME_MODEL_DAYS = "workingtimemodeldays",
		TIMEKEEPING_GROUPS = "timekeepinggroups",
		EMPLOYEES = "employees",
		PERIODS = "periods",
		CREW_RECORDINGS = "crewrecordings",
		EMPLOYEE_SHEETS = "employeesheets",
		TIMEKEEPING_RESULTS = "timekeepingresults",
		EMPLOYEE_REPORTS = "employeesreports",
		TRANSACTIONS = "transactions",
		PAYMENT_GROUPS_SURCHARGES = "paymentgroupsurcharges",
		CLERK_ROLES = "clerkroles",
		PREDECESSORS = "predecessors",
		MATERIAL_CATALOG_FILTER = "materialcatalogfilter",
		HAMMOCK = "hammock",
		SUCCESORS = "successors",
		CONSTRUCTION_SYSTEM_GROUP = "constructionsystemgroup",
		CONSTRUCTION_SYSTEM_LIBRARIES = "constructionsystemlibraries",
		EXCHANGE_RATES = "exchangerates",
		BREAKS = "breaks",
		EXCEPTIONS = "exceptions",
		ROOT_ASSIGNMENT = "rootassignment",
		RULE_EXECUTION_OUTPUT = "ruleexecutionoutput",
		PLANT_LOCATION = "plantlocation",
		PLANT_ALLOCATIONS = "plantallocations",
		CREW_ASSIGNMENTS = "crewassignments",
		OVERTIME_CALCULATION_PARAMETER = "overtimecalculationparameters",
		EMPLOYEE_WORKING_TIME_MODEL = "employeeworkingtimemodel",
		RELATED_CONTRACTS = "relatedcontracts",
		OVERTIME_CALCULATION_PARAMETERS_DETAILS = "overtimecalculationparametersdetails",
		JOBS = "jobs",
		PLANT_CATALOG_PRICELISTS = "plantcatalogpricelists",
		WORK_OPERATION_TYPE_CONDITIONS = "workoperationtypeconditions",
		CONDITIONS = "conditions",
		PROJECT_ADRESS_LIST = "projectaddresslist",
		CLERK_ROLE = "clerkroles",
		POSTED_DISPATCH_NOTES_NOT_READY_FOR_SETTLEMENT = "posteddispatchnotesnotreadyforsettlement",
		LOGISTIC_JOB_WITH_NEGATIVE_QUANTITY_FOR_BULK = "logisticjobswithnegativequantityforbulk",
		DISPATCHING_HEADER = "dispatchingheader",
		PERFORMING_JOB_PLANT_LOCATIONS = "performingjobplantlocations",
		DISPATCHING_RECORDS = "dispatchingrecords",
		SETTLEMENTS = "settlements",
		BOQ_PRICE_CONDITION = "boqpricecondition",
		PROJECT_CONTROLS_DASHBOARD = "projectcontrolsdashboard",
		CONTROLLING_COST_CODE = "controllingcostcode",
		ACCOUNT = "account",
		BOQ_CHANGE_OVERVIEW = "boqchangeoverview",
		COST_HEADER = "costheader",
		COST_DATA = "costdata",
		SETTLEMENT_ITEMS = "settlementitems",
		ACTIVITY_LINE_ITEMS_TYPE_BID_ESTIMATE = "activitylineitemstypebidestimate",
		SETTLEMENT_CLAIMS = "settlementclaims",
		CREW_MIX_TO_COST_CODES = "crewmixtocostcodes",
		PLANNED_ABSENCES="plannedabsences",
		PLANT_PRICES="plantprices",
		STOCK_LOCATIONS="stocklocations",
		STORAGE_LOCATIONS="storagelocations",
		STOCK_LIST="stocklist",
		CREW_MIXES = "crewmixes",
		TECHNICAL_COST_CODE="technicalcostcode",
		FORMULA_DEFINITION="formuladefinition",
		BOQS_CAPITAL="BoQs",
		MATERIAL_PRICES="materialprices",
		RESOURCE_TYPES="resourcetypes",
		ACTIVITY_DETAILS="activitydetails",
		PLANT_ESTIMATE_PRICE_LIST="plantestimatepricelist",
		EQUIPMENT_ASSEMBLIES="equipmentassemblies",
		EQUIPMENT_ASSEMBLY_RESOURCES="equipmentassemblyresources",
		REQUESTED_TYPES="requestedtypes",
		SKILL="skill",
		REQUIRED_SKILLS="requiredskills",
		EQUIPMENT_ASSEMBLY="equipmentassembly ",
		FURTHER_EXTERNAL_BPS="furtherexternalbps",
		REMARK="remark",
		PRICE_CONDITION_PARAM="priceconditionparam",
		STATUS_HISTORY = "statushistory",
		ESTIMATE_HEADER="estimateheader",
		CONTROLLING_GROUP_SET="controllinggroupset",
		ESTIMATE_LINE_ITEM="estimatelineitem",
		PRICE_LIST_CONDITION="pricelistcondition",
		MASTER_RESTRICTIONS = "masterrestrictions",
		SPECIFICATION = "specification",
		HISTORICAL_PRICE_FOR_BOQ="historicalpriceforboq",
		DOCUMENT_PROJECT_HISTORY="documentprojecthistory",
		SPECIFICATION_PLAIN_TEXT_ITEM="specificationplaintext-item",
		PAKET="Paket",
		ÜBERSICHT = "Übersicht",
		STOCK_HEADER="stockheader",
		STOCK_TOTAL="stocktotal",
		PROJECT_DOCUMENTS_CLERK_AUTHORIZATIONS="projectdocumentclerk(authorizations)",
		REQUISITION_VARIENT="requisitionvariant",
		REQUISITION_BOQ_VARIANT="requisitionboqvariant",
		BUSINESS_PARTNER_ASSIGNMENTS="businesspartnerassignments",
		FORM_LIST="formlist",
		FORM_DATA="formdata",
		SCOPE_ITEM_TEXTS="scopeitemtexts",
		SCOPE_ITEM_PLAIN_TEXTS="scopeitemplaintexts",
		WARRANTY="warranty",
		APPROVALS="approvals",
		EXTERNAL_ROLES= "externalroles",
		EXTERNAL_ROLE_DETAIL= "externalroledetail",
		REQUISITION_ITEM_VARIENT="requisitionitemvariant",
		SCOPE_PRICE_CONDITION="scopepricecondition",
		PRICE_CONDITION_DETAILS="priceconditiondetails",
		CONTRACT_CLERK="contractclerk",
		CALL_OFFS="calloffs",
		CONTACT_FORM_DATA="contactformdata",
		REGISTERED_FOR_COMPANY="registeredforcompany",
		REGISTERED_FOR_COMPANY_DETAIL="registeredforcompanydetail",
		CONTACT_CLERK="contactclerk",
		COUNTRIES="countries",
		CALL_OFF_AGREEMENTS="calloffagreements",
		CONTACT_CLERK_DETAILS="contactclerkdetails",
		CURRENT_CERTIFICATES="currentcertificates",
		CONTACT_EXTERNAL="contactexternal ",
		CONTACT_EXTERNAL_DETAIL="contactexternaldetail",
		INVENTORY_HEADER="inventoryheader",
		INVENTORY="inventory",
		ORDER_PROPOSALS="orderproposals",
		INVENTORY_HEADER_DETAIL = "inventoryheaderdetail",
		REQUISITION_CAPITAL ="Requisitions",
		MODIFY_BASELINE="modifybaseline",
		HISTORICAL_PRICE_FOR_ITEM="historicalpriceforitem",
		ADVANCES="advances",
		SCHEMATA="schemata",
		BUSINESS_PARTNER_EVALUATION_CHART="businesspartnerevaluationchart",
		LINE_OF_BALANCE="lineofbalance",
		ORDER_PROPOSALS_DETAIL= "orderproposalsdetail",
		ASSEMBLY_CHARACTERISTICS1= "assemblycharacteristics1",
		STOCK_HEADER_DETAIL="stockheaderdetail",
		STOCK_HEADER_RECONCILIATION="stockheaderreconciliation",
		STOCK_TOTAL_DETAIL="stocktotaldetail",
		STOCK_TOTAL_RECONCILIATION="stocktotalreconciliation",
		GROUPS="groups",
		GROUP_DETAIL="groupdetail",
		STOCK_DOWNTIME="stockdowntime",
		USERS="users",
		USER_TO_GROUP="usertogroup",
		USERS_IN_GROUP="usersingroup",
		COMPANY_AND_ROLE_ASSIGNMENT="companyandroleassignment",
		ACCESS_ROLE_ASSIGNMENTS="accessroleassignments",
		SCHEMA_DETAIL = "schemadetail",
		GROUP_ICONS="groupicons",
		GROUP_ICON_DETAIL="groupicondetail",
		MASTER_RESTRICTION = "masterrestriction",
		SUBGROUPS = "subgroups",
		SUBGROUP_DETAIL = "subgroupdetail",
		USER_DETAILS="userdetails",
		CONTRACT_RELATIONS="contractrelations",
		PROJECT_CHANGE="projectchange",
		TEXT_ASSEMBLIES_MODULE_PLAIN_TEXT="textassembliesmodule-plaintext",
		ITEM_DETAIL="itemdetail",
		CONTRACT_PES_INFORMATION_FOR_STOCK = "contract&pesinformationforstock",
		REMINDERS="reminders",
		REMINDER_DETAIL="reminderdetail",
		BREAK = "break",
		CREW="crew",
		MANDATORY_DEADLINES= "mandatorydeadlines",
		TEXT_ASSEMBLIES_MODULE_FORMATTED_TEXT="textassembliesmodule-formattedtext",
		QUOTE_EVALUATION="quoteevaluation",
		CONTRACT_STRUCTURE="contractstructure",
		DOCUMENT_PROJECT_DETAILS = "documentprojectdetails",
		GENERAL_LIST="generallist",
		BID_CLERK="bidclerk",
		CONTACT_DETAIL = "contactdetail",
		CONTROLLING_UNIT_DETAILS="controllingunitsdetails",
		CERTIFICATE_DETAIL="certificatedetail",
		TEXT_ASSEMBLIES_ITEM_PLAIN_TEXT="textassembliesitem-plaintext",
		TEXT_ASSEMBLIES_ITEM_FORMATTED_TEXT="textassembliesitem-formattedtext",
		PERFORMING_JOB_MATERIAL_STOCK='performingjobmaterialstock',
		MEETING = "meeting",
		INVOICES="invoices",
		SHIPMENT_INFO="shipmentinfo",
		JOB_LIST="joblist",
		PROJECT_DOCUMENTS_CLERK_AUTHORIZATION_DETAILS="projectdocumentclerk(authorizations)",
		PROJECT_DOCUMENTS_CLERK_AUTHORIZATIONS_DETAILS="projectdocumentclerk(authorization)details",
		WORKFLOW_TEMPLATES="workflowtemplates ",
		WORKFLOW_TEMPLATES_VERSIONS="workflowtemplatesversions",
		DEBUG="debug",
		DATA_CONFIGURATION="dataconfiguration",
		RESULT_HEADER="resultheader",
		TIME_ALLOCATION="timeallocations",
		EMPLOYEE_REPORT_VALIDATIONS="employeereportvalidations",
		BUSINESS_PARTNER_ASSIGNMENT_DETAIL="businesspartnerassignmentdetail",
		BILL_TO_BOQ="billto(boq)",
		ALLOWANCE_AREAS_AND_GC_AREAS="allowanceareas&gcareas",
		BOQ_AREA_ASSIGNMENT="boqareaassignment",
		RESOURCE_CHARACTERISTICS = "resourcecharacteristics",
		ASSET_MASTER_GRID="assetmastergrid",
		SCHEMA_ICONS="schemaicons",
		SCHEMA_ICON_DETAIL="schemaicondetail",
		ACTUAL_CERTIFICATES = "actualcertificates",
		PAYMENTS = "payments",
		CERTIFICATE_CHARACTERISTICS="certificatecharacteristics",
		CHAINED_INVOICES="chainedinvoices",
		BUSINESS_YEARS="businessyears",
		BUSINESS_PARTNER_CLERK= "businesspartnerclerk",
		BUSINESS_PARTNER_CLERK_DETAIL="businesspartnerclerkdetail",
		DESIGNER="designer",
		ACTION_PARAMETERS="actionparameters",
		SITES="sites",
		CHNAGE_ORDER="changeorder",
		RELATED_BILLS="relatedbills",
		RELATED_WIPS="relatedwips",
		EMPLOYEE_CERTIFICATION="employeecertifications",
		PACKAGE_STRUCTURE="packagestructure",
		SETTLEMENT_STRUCTURE="settlementstructure",
		SUBSCRIBED_EVENTS="subscribedevents",
		ACTIVITY_LINE_ITEMS_TYPE_JOB_ESTIMATE = "activityresourcestypejobestimate",
		ROLE_TO_ROLE="roletorole",
		COMPANY_SUPPLIERS="companysuppliers",
		RESERVATION_STRUCTURE="reservationsstructure",
		RESERVATIONS="reservations",
		CREW_MEMBER="crewmember",
		WIZARD_PARAMETER_VALUES="wizardparametervalues",
		QTO_DETAIL_DOCUMENTS="qtodetaildocuments",
		PLANT_LIST="plantlist",
		PLANT_TOTALS="planttotals",
		BOQ_STRUCTURE_BID="BoQ Structure",
		CONTRACT_DETAILS="contractdetails",
		BUSINESS_PARTNER_DETAILS="businesspartnerdetail",
        BUSINESS_PARTNER_RELATION="businesspartnerrelation",
        BUSINESS_PARTNER_RELATION_DETAILS="businesspartnerrelationdetail",
		ESTIMATE_HEADER_TEXT="estimateheadertext",
		ACTIVITIES_CRITERIA="activitiescriteria",
		APPROVER_CONFIG="approverconfig",
		APPROVER="approver",
		TEMPLATE_CONTEXT="templatecontext",
		ACTIVITY_CODE="activitycode",
		WORKFLOW_INSTANCE="workflowinstance",
		SOURCE_QTO= "sourceqto",
		ACTUAL_CERTIFICATE="actualcertificate",
		INSTANCE_CONTEXT="instancecontext",
		BILL_CLERK="billclerk",
		ACTION_INSTANCE="actioninstance",
		PLANT_SUPPLIER_LIST="plantsupplierlist",
		PLANT_SUPPLY_ITEMS="plantsupplyitems",
		MODELS="models",
		MODEL_FILES="modelfiles",
		MODEL_3D_VIEWER="3dviewer"
	}

  	export enum DataNGLocator {
		PRC_STRUCTURE_SELECTED_ITEM_FK = "prcstructure.selectedItemFk",
		LOCATION_REGIONAL_SELECTED_ITEM_FK = "location.regional.selectedItemFk",
		MODIFY_OPTION_DEL_ADJUST_PRICES = "modifyOption.DelAdjustPrices",
		ITEM_BUSINESSPARTNER_FK = "item.businessPartnerFk",
		ITEM_PRC_TYPE = "item.prcType",
		CREW_ASSIGNMENTS = "Crew Assignments",

	}

	export enum ModalInputFields{
		PROJECT_NUMBER='e2e-entity-projectno', //done
		NAME='e2e-entity-projectname', //done
		CLERK='e2e-clerkfk', //no change required for project modal
		DESCRIPTION_INFO= "e2e-descriptioninfo",//no use in any script
		DESCRIPTION_INFO_TRANSLATED="e2e-descriptioninfo-translated",
		TYPE="e2e-typefk",//no change required for bid modal
		CONFIGURATION="e2e-configurationfk",//no change required for bid modal
		MAIN_BID="e2e-bidheaderfk", //no change required for bid modal
		PROJECT_NAME="e2e-projectfk",//no change  required create bill from bill module
		CHANGE_TYPE="e2e-changetypefk",//no change  required create bill from bill module
		CODE="e2e-code",//updated in all pom ad script
		CODE_ENTITY="e2e-entity-code",//change in custamizing module
		DESCRIPTION="e2e-entity-description",
		DESCRIPTION_ENTITY="e2e-entity-description",//change in custamizing module
		MAIN_CONTRACT="e2e-ordheaderfk",//no change  required create bill from bill module
		RUBRIC_CATEGORY="e2e-rubriccategoryfk",//no change  required create bill from bill module
		BUSINESS_PARTNER_FK="e2e-businesspartnerfk",//no change  required create bill from bill module
		BILL_TO="e2e-billtofk",//no change  required create bill from bill module
		BOQ_SOURCE="e2e-entity-boqsource", //updated change 
		CHANGE_ORDER="e2e-prjchangefk",//no change required for bid modal
		CHANGE_REASON="e2e-changereasonfk",//no change required for bid modal
		ESTIMATE_CODE="e2e-entity-estimateheaderfk",//updated change in insert LI from wizard in scheduling module
		APPLY_SPLIT_RESULT_TO="e2e-entity-applysplitresultto",//updated change in insert LI from wizard in scheduling module
		SET_LINE_ITEM_TO_NO_RELATION="e2e-entity-norelation",//updated change in insert LI from wizard in scheduling module
		SUBPACKAGE_CODE="e2e-package-2-headerfk",//updated
		PROCUREMENT_STRUCTURE="e2e-structurefk",//no change requird
		ASSEMBLY_CATEGORY_ID="e2e-assemblycategoryid",//no change requird
		WIZARDACTION="e2e-entity-wizardaction", //updated in boq structure module
		INCLUDE_SPLIT_QUNATITIES="e2e-entity-includesplitquantities", //updated in boq structure module
		DOCUMENT_CATEGORY="e2e-prjdocumentcategoryfk",//no change required in pes module wizard option
		PROJECT_DOCUMENT_TYPE="e2e-prjdocumenttypefk",//no changes required in certificate module
		KEEP_OR_OVERWRITE_ASSIGNMENTS="e2e-entity-keeporoverwriteassignment",//updated in boq structure module wizard option
		SPLIT_METHOD="e2e-splitmethod",//no change required in li wizard option
		SPLIT_AS_REFERENCED_LINE_ITEMS="e2e-entity-dosplitasreference",//updated in LI wizard option
		SELECT_ESTIMATE_SCOPE="e2e-entity-estimatescope",//updated change in create bid from wizard module
		ALLOWANCE="e2e-allowancetypefk",//no change required in custamizing module
        MARKUP_CALCULATION_TYPE="e2e-markupcalctypefk",//no change required in custamizing module
        MASTER_DATA_CONTEXT="e2e-mastercontextfk",//no change required in custamizing module
        AREA_WISE="e2e-allareagrouptypefk",//no change required in custamizing module
        DCM_BASED_ON_WQ_AQ_QUANTITY="e2e-quantitytypefk",//no change required in custamizing module
        SINGLE_STEP_ALLOWANCE="e2e-entity-isonestep", //updated change in custamizing module 
        LEVEL_OUT_DIFFERENCES_FROM_FP_ITEMS="e2e-entity-isbalancefp",//updated change in custamizing module 
        R_P="e2e-entity-markuprp",//updated change in custamizing module 
        A_M="e2e-entity-markupam",//updated change in custamizing module 
        G_A="e2e-entity-markupga",//updated change in custamizing module 
        PREVIOUS_WIP_ID="e2e-previouswipid",//no change required in contract sales module
		PREV_WIPHEADERFK="e2e-prevwipheaderfk",//no change required in WIP module
		QTO_TARGET_TYPE="e2e-qtotargettype",//no change required in qto module
		QTO_TYPE="e2e-qtotypefk",//Updated in qto module
		CON_HEADER_FK="e2e-conheaderfk",//no change required in qto module
		PRC_BOQ_FK="e2e-prcboqfk",//no change required in qto module
		PRJ_BOQ_FK="e2e-prjboqfk",//no change required in qto module
		PREVIOUS_WIPFK="e2e-previouswipfk",//no use
		GENERATE_UPDATE_BASE_COST="e2e-entity-generatebasecost",//updated change in LI from wizard module
		BUDGET_FROM="e2e-entity-budgetfrm",// Updated
		X_FACTOR="e2e-entity-factor",// Updated
		IGNORE_FIXED_BUDGET_AND_BUDGET_UNIT_RECORDS="e2e-entity-skipfixedbudgetitems",// Updated
		STRUCTURE_TYPE="e2e-entity-structuretype", //updated change in create bid from wizard module
		USE_UR_BREAKDOWN="e2e-entity-estuppusingurp",//updated change in create bid from wizard module
		LINKED_BOQ_ITEM="e2e-entity-islinkedboqitem",// Updated
		CREATE_NEW_LINE_ITEM_FOR_NEW_BOQ_ITEM="e2e-entity-islineitemfornewboq",// Update
		BAS_UOM_DAY_FK="e2e-basuomfk",//no changes required
		UPDATE_REVENUE="e2e-entity-selectedlevel",//updated in esitmate module
		DISTRIBUTE_BY="e2e-entity-distributeby",//no change required
		CALCULATE_RULE_PARAMETER="e2e-entity-calcruleparam",//updated in esitmate module
		TEMPLATE_PROJECT="e2e-templateprojectfk",//no changes required
		SCHEDULE="e2e-templateschedulefk",//no changes required
		EDIT_ESTIMATE_TYPE="e2e-entity-iseditesttype", // Updated 
		IS_COLUMN_CONFIGURE_ACTIVATED="e2e-entity-iscolumnconfig",// Updated 
		EDIT_TYPE_ESTIMATE_STRUCTURE="e2e-entity-iseditstructtype",// Updated 
		SOURCE_LEADING_STRUCTURE="e2e-structureid",//no changes required
		GENERATE_LINE_ITEM_BY_SPLIT_QUANTITY="e2e-entity-isbysplitquantity",// Updated 
		GENERATE_AS_REFERENCE_LINE_ITEMS="e2e-entity-isgenerateasreferencelineitems",// Updated 
		BOQ_FINAL_PRICE="e2e-entity-copyboqfinalprice",// Updated 
		COPY_RELATED_ASSEMBLIES_FROM_WIC="e2e-entity-copyrelatedwicassembly",// Updated 
		COPYING_DESCRIPTION="e2e-entity-copyleadingstructruedesc",// Updated 
		UPDATE_DESCRIPTION_TO_EXISITING_ITEMS="e2e-entity-updateleadstrucdesctoexistingitem",// Updated 
		WIC_BOQ="e2e-entity-copywic",// Updated 
		COST_GROUP="e2e-entity-copycostgroup",// Updated 
		PROJECT_COST_GROUP="e2e-entity-copyprjcostgroup",// Updated
		CONTROLLING_UNIT="e2e-entity-copycontrollingunit",// Updated
		COPY_LOCATION="e2e-entity-copylocation",// Updated
		COPYING_PROCUREMENT_STRUCTURE="e2e-entity-copyprocstructure",// Updated
		CONTROL_DIRECTIVE="control-directive", //no changes required
		DATE_DELIVERED="e2e-entity-datedelivered",//updated changes in createpes from qto module wizard option
		PRC_CONFIGURATION_FK="e2e-prcconfigurationfk",//no changes required
		BILLING_SCHEMA_FK="e2e-billingschemafk",//no changes required
		EXCEL_PROFILEID="e2e-excelprofileid",//no changes required
		EXTERNAL_SOURCE_FK="e2e-externalsourcefk",//no changes required
		STREET="e2e-entity-street",//update in bussiness partner module
		ZIP_CODE="e2e-entity-zipcode",//update in bussiness partner module
		CITY="e2e-entity-city",//update in bussiness partner module
		SITE="e2e-sitefk",//no changes required 
		UOM_TIME="e2e-uomtimefk",//no changes required
		RESOURCE_KIND="e2e-kindfk",//no changes required
		RESOURCE_GROUP="e2e-groupfk",//no changes required
		COUNTRY_FK="e2e-countryfk",//no changes required
		TRANSLATED="e2e-translated",
		CONTRCAT_TYPE_FK="e2e-contracttypefk", //no use in any script
		PERFORMED_FROM="e2e-entity-performedfrom",//no use in any script
		PERFORMED_TO="e2e-entity-performedto",//no use in any script
		RELATED_BILL_ID="e2e-relatedbillid",//no change require 
		IS_TEST_RUN="e2e-entity-istestrun", //updated in settlement module
		JOB_2_FK="e2e-job-2-fk", //update in dispatching notes module 
		DUE_DATE="e2e-entity-duedate",//updated in settlement module 
		WORK_OPERATION_TYPE_FK="e2e-workoperationtypefk",//no change 
		CONTROLLING_UNIT_FK="e2e-controllingunitfk",//no change
		STRUCTURE="e2e-procurementstructurefk",//no change
        E2E_PROJECT_ID="e2e-projectid",//no use in any script
		PROJECT="e2e-project",//no change
		WIC_GROUP="e2e-wicgroup",//no change
		PRC_WIC_CATBOQS="e2e-prcwiccatboqs",//no change
		CONTACT="e2e-contact",//no change
		PRC_STRUCTURE_FK="e2e-prcstructurefk",//no changes requires
		SELECT_QUANTITY_BOQHEADER_CONTEXT_ITEM_TARGET="e2e-selectedquantityboqheadercontextitemtarget",//no change required
		SELECT_QUANTITY_BOQHEADER_CONTEXT_ITEM_SOURCE="e2e-selectedquantityboqheadercontextitemsource",//no change required
		CONSIDER_S_CURVE="e2e-entity-considerscurve",//updated in update aq or iq LI qty in sheduling module
		SCHEDULE_SCOPE="e2e-entity-schedulescope",//updated in update aq or iq LI qty in sheduling module
		UPDATE_PLANNED_QUANTITIES="e2e-entity-updateplannedquantity",//updated in update aq or iq LI qty in sheduling module
		SELECT_QUANTITY="Select Quantity",//no change in update aq or iq LI qty in sheduling module
		E2E_ESTIMATE_FILTER_TYPE="e2e-entity-estimatefiltertype",//Updated
		PREVIOUSE_BILL_FK="e2e-previousbillfk",//no change required for create biil from wip modal
		PREVIOUSE_BILL_ID="e2e-previousbillid",//no change required set prvious bill option from wizard
		RUBRIC_CATEGORY_ID="e2e-rubriccategoryid", // no change required
		COMPANY_NAME="e2e-entity-companyname",//updated in company module 
		LOGIN_ALLOWED="e2e-entity-loginallowed",//updated in company module 
		IS_LIVE="e2e-entity-islive",//updated in company module 
		ALLOCATED_FROM = "e2e-selection-allocatedfrom", //updated create initial allocation from plant master module
		JOB_FK="e2e-selection-jobfk", //updated create initial allocation from plant master module
		NUMBER_MASK="e2e-entity-numbermask", //change in company module number series
		DOCUMENT_TYPE_FK="e2e-documenttypefk",//no change
		REVISION="e2e-entity-revision",//updated in certificate module
		PROJECT_DOCUMENT_STATUS_FK="e2e-prjdocumentstatusfk",//no change 
		CONTROL_TEMPLATE_FK="e2e-controltemplatefk",//no change
		IS_KEEP_TEMPLATE_CODE="e2e-entity-iskeeptemplatecode",//updated in controlling unit
		REPLACE_TEMPLATE_ROOT_CODE="e2e-entity-replacetemplaterootcode",//updated in controlling unit
		INCLUDE_REFERNCE_LINE_ITEMS="e2e-filterrequest-includereferencelineitems",//change in estimate module
		SELECT_UPDATE_SCOPE='e2e-entity-selectupdatescope',//updated 
		UPDATE_BOQ_UNIT_RATE="e2e-entity-updatefpboqunitrate",//updated change in create bid from wizard module
		SELECT_UPADTE_COST_GROUP="e2e-entity.updateCostGroup",//updated change in estimate from wizard module
		CHANGE_ORDER_FK="e2e-changeorderfk",//no change
		JOB_CODE="e2e-entity-jobcode",// updated in estimate module
		JOB_DESCRIPTION="e2e-entity-jobdescription",// updated in estimate module
		REB_FORMAT_ID="e2e-entity-rebformatid",//updated in export qto document from wizard module
		UOM_FK="e2e-uomfk",//not used in any script
		PRICING_GROUP_FK="e2e-pricinggroupfk",
		EDIT_COL_CONFIG_TYPE="e2e-entity-isedittolconfigtype",//updated in estimate module
		UPDATE_TO_BOQ="e2e-entity-updboq",//update in estimate
		UPDATE_FROM_BOQ="e2e-entity-updfromboq",//updated in estimate
		WEIGHTING="e2e-entity-weighting",//Updated in schema module 
		TEXT_TYPE="e2e-prctexttypefk",
		IS_ORDER_QUANTITY_ONLY="e2e-entity-isordquantityonly",//updated change in create WIP from wizard in QTO module
		IS_COLLECTIVE_WIP="e2e-entity-iscollectivewip",//updated change in create WIP from wizard in QTO module
		MAJOR_LINE_ITEMS="e2e-entity-majorlineitems",//updated changed for create bid from wizard
		SELECT_TRANSFER_SCOPE="e2e-entity-selecttransferscope",//update in assembly 
		POSSIBLE_MINIMUM="e2e-pointsminimum",// no change
		IS_EDITABLE="entity.IsEditable",//Updated in project module
		PRC_CONDITION = "e2e-prcpricecondition",//not available in 25.2
		BOQ_HEADER = "e2e-boqnumber",
		EST_HEADER = "e2e-estimatemainheade",
		START_DATE = "e2e-currentitem-startdate",
		OUTLINE_SPECIFICATION = "ui-salesboq-briefInfo",
		TITLE = "e2e-entity-title",
		URL = "e2e-entity-url",
		IS_IMPORTANCE = "e2e-entity-isimportance",
		REQUIRED_CLERK_ITEMS = "e2e-requiredclerkitems",
		REQUIRED_CONTACT_ITEMS = "e2e-requiredcontactitems",
		OPTIONAL_CLERK_ITEMS = "e2e-optionalclerkitems",
		OPTIONAL_CONTACT_ITEMS = "e2e-optionalcontactitems",
		MODAL_OPTION_START_DATE="e2e-modaloptions-startdate",
		MODAL_OPTION_END_DATE = "e2e-modaloptions-enddate ",
		START_TIME = "e2e-modaloptions-starttime",
		END_TIME = "e2e-modaloptions-endtime",
		LOCATION="e2e-entity-location",
		RECURRENCE_RANGE_END_DATE="e2e-recurrencerange-enddate",
		BRANCH="e2e-subsidiaryfk",
		ESTIMATE_FK="e2e-estimatefk"
	}
}

export default AppLayout;
