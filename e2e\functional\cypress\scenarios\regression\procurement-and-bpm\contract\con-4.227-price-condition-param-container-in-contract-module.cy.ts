import { commonLocators, tile, app, cnt, sidebar, btn } from "cypress/locators";
import { _common, _projectPage, _validate, _procurementContractPage } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

let PRJ_NO = _common.generateRandomString(3);
let PRJ_NAME = _common.generateRandomString(3);
let COMMENT = _common.generateRandomString(4);

let CREATE_PROJECT_PARAMETERS: DataCells;
let CONTAINER_PRICE_CONDITION_PARAM;
let CONTAINER_CONTRACT;
let CONTRACT_PARAMETER
let CONTAINERS_PROJECT;
let CONTAINER_COLUMNS_PRICE_CONDITION_PARAM

describe("PCM- 4.227 | Price condition param container in contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

    before(function () {
        cy.fixture("pcm/con-4.227-price-condition-param-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINERS_PROJECT = this.data.CONTAINERS.PROJECT
            CREATE_PROJECT_PARAMETERS = {
                [commonLocators.CommonLabels.PROJECT_NUMBER]: PRJ_NO,
                [commonLocators.CommonLabels.NAME]: PRJ_NAME,
                [commonLocators.CommonLabels.CLERK]: CONTAINERS_PROJECT.CLERK_NAME
            }
            CONTAINER_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINER_CONTRACT.BUSINESS_PARTNER
            }
            CONTAINER_COLUMNS_PRICE_CONDITION_PARAM = this.data.CONTAINER_COLUMNS.PRICE_CONDITION_PARAM
            CONTAINER_PRICE_CONDITION_PARAM = this.data.CONTAINERS.PRICE_CONDITION_PARAM
        })
        cy.preLoading(Cypress.env("adminUserName"), Cypress.env("adminPassword"), Cypress.env("parentCompanyName"), Cypress.env("childCompanyName"));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
    });
    after(() => {
        cy.LOGOUT();
    });

    it('TC - Create new project', function () {
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.create_newRecord(cnt.uuid.PROJECTS);
        _projectPage.enterRecord_toCreateProject(CREATE_PROJECT_PARAMETERS);
        _common.waitForLoaderToDisappear()
        cy.wait(1000) // required wait to search from sidebar and select record
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem().search_fromSidebar(commonLocators.CommonKeys.STANDARD, PRJ_NO).pinnedItem();
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create new contract", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            cy.REFRESH_SELECTED_ENTITIES()
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONFIGURATION_FK)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create & verify price condition param", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.FooterTab.PRICE_CONDITION_PARAM, 1)
            _common.setup_gridLayout(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, CONTAINER_COLUMNS_PRICE_CONDITION_PARAM)
            _common.clear_subContainerFilter(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT)
        })
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.GridCells.PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_PRICE_CONDITION_PARAM.PRICE_CONDITION_TYPE[0])
        _common.clickOn_activeRowCell(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.GridCells.VALUE)
        _common.edit_containerCell(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_PRICE_CONDITION_PARAM.VALUE[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.select_rowInContainer((cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.FooterTab.PRICE_CONDITION_PARAM, 1)
        })
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT)
        _validate.verify_isRecordPresent(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, CONTAINER_PRICE_CONDITION_PARAM.PRICE_CONDITION_TYPE[0])
        _common.waitForLoaderToDisappear()
    });

    it("TC - Delete record and verify its deleted", function () {
        _common.delete_recordFromContainer(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.FooterTab.PRICE_CONDITION_PARAM, 1)
        })
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordDeleted(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, CONTAINER_PRICE_CONDITION_PARAM.PRICE_CONDITION_TYPE[0])
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create & verify price condition param", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.FooterTab.PRICE_CONDITION_PARAM, 1)
            _common.setup_gridLayout(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, CONTAINER_COLUMNS_PRICE_CONDITION_PARAM)
            _common.clear_subContainerFilter(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT)
        })
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.GridCells.PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_PRICE_CONDITION_PARAM.PRICE_CONDITION_TYPE[1])
        _common.clickOn_activeRowCell(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.GridCells.VALUE)
        _common.edit_containerCell(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_PRICE_CONDITION_PARAM.VALUE[1])
        _common.edit_containerCell(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_REMARK, COMMENT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.select_rowInContainer((cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, app.FooterTab.PRICE_CONDITION_PARAM, 1)
        })
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT)
        _validate.verify_isRecordPresent(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, CONTAINER_PRICE_CONDITION_PARAM.PRICE_CONDITION_TYPE[1])
        _validate.verify_isRecordPresent(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, CONTAINER_PRICE_CONDITION_PARAM.VALUE[1])
        _validate.verify_isRecordPresent(cnt.uuid.PRICE_CONDITION_PARAM_CONTRACT, COMMENT)
        _common.waitForLoaderToDisappear()
    });

});

