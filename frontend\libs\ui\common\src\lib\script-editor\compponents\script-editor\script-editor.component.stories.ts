/*
 * Copyright(c) RIB Software GmbH
 */

import { NgModule } from '@angular/core';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule } from '@angular/common/http';
import { Observable, of } from 'rxjs';

import { UiCommonScriptEditorComponent } from './script-editor.component';
import { ScriptEditorConfigService } from '../../services/script-editor-config.service';
import { ScriptHintService } from '../../services/script-hint.service';
import { IScriptHintProvider } from '../../model/interfaces/script-hint-provider.interface';
import { IScriptDefProvider } from '../../model/interfaces/script-def-provider.interface';
import { PlatformConfigurationService, PlatformTranslateService } from '@libs/platform/common';
import { Completion } from '@codemirror/autocomplete';
import { Def } from 'tern/lib/tern';

// Mock Definition Provider
class MockScriptDefProvider implements IScriptDefProvider {
	public getDefs(): Observable<Def[]> {
		// Basic JavaScript definitions for demonstration
		const mockDefs: Def[] = [
			{
				'!name': 'basic',
				'!define': {
					console: {
						log: 'fn(message: string)',
						error: 'fn(message: string)',
						warn: 'fn(message: string)',
					},
					Math: {
						PI: 'number',
						abs: 'fn(value: number) -> number',
						max: 'fn(a: number, b: number) -> number',
						min: 'fn(a: number, b: number) -> number',
						round: 'fn(value: number) -> number',
					},
					String: {
						prototype: {
							length: 'number',
							charAt: 'fn(index: number) -> string',
							indexOf: 'fn(searchValue: string) -> number',
							substring: 'fn(start: number, end?: number) -> string',
							toLowerCase: 'fn() -> string',
							toUpperCase: 'fn() -> string',
						},
					},
				},
			},
		];
		return of(mockDefs);
	}

	public addVariable(items: { name: string; type: string; description: string }[]): void {
		// Mock implementation
		console.log('Adding variables:', items);
	}
}

// Mock Hint Provider
class MockScriptHintProvider implements IScriptHintProvider {
	public getArgHints(funcName: string, index: number, argsBefore?: string[]): Observable<Completion[]> {
		const mockHints: Completion[] = [
			{ label: 'option1', info: 'First option', type: 'string' },
			{ label: 'option2', info: 'Second option', type: 'string' },
			{ label: 'option3', info: 'Third option', type: 'string' },
		];
		return of(mockHints);
	}
}

// Test Module
@NgModule({
	imports: [CommonModule, FormsModule, ReactiveFormsModule, HttpClientModule],
	declarations: [UiCommonScriptEditorComponent],
	providers: [ScriptEditorConfigService, ScriptHintService, PlatformConfigurationService, PlatformTranslateService],
	exports: [UiCommonScriptEditorComponent],
})
class TestScriptEditorModule {}

// Sample JavaScript code snippets
const sampleJavaScript = `// Sample JavaScript code
function calculateSum(a, b) {
	return a + b;
}

const result = calculateSum(5, 10);
console.log('Result:', result);

// Using Math object
const pi = Math.PI;
const rounded = Math.round(3.14159);

// String operations
const message = "Hello, World!";
const upperCase = message.toUpperCase();
const length = message.length;`;

export default {
	title: 'UI Common/ScriptEditor/ScriptEditorComponent',
	component: UiCommonScriptEditorComponent,
	decorators: [
		moduleMetadata({
			imports: [TestScriptEditorModule],
		}),
	],
	parameters: {
		docs: {
			description: {
				component: `

## Overview

The Script Editor Component provides a professional code editing experience with enterprise-grade features including:


## Key Features
- Full JavaScript syntax support with syntax highlighting
- Real-time error detection and validation
- Customizable editor options and themes

### ⌨️ **Essential Keyboard Shortcuts**

- \`Ctrl+Z\` - Undo the last edit operation
- \`Ctrl+A\` - Select all text in the editor
- \`Ctrl+D\` - Select next occurrence of current selection
- \`Ctrl+/\` - Toggle comments on selected lines
- \`Tab\` - Indent selected text or current line
- \`Shift+Tab\` - Remove indentation from selected text
- \`Alt+↑/↓\` - Move current line up or down
- \`Ctrl+Shift+K\` - Delete the current line
- \`Ctrl+F\` - Open find dialog
- \`Ctrl+Enter\` - Insert new line below current line


## Stories Overview

Each story in this collection demonstrates different aspects and use cases of the script editor component, from basic usage to advanced interactive scenarios.
				`,
			},
		},
	},
	argTypes: {
		value: {
			control: 'text',
			description: 'The JavaScript code content',
		},
		readonly: {
			control: 'boolean',
			description: 'Whether the editor is read-only',
		},
		options: {
			control: 'object',
			description: 'Script editor configuration options',
		},
	},
} as Meta<UiCommonScriptEditorComponent>;

type Story = StoryObj<UiCommonScriptEditorComponent>;

export const Default: Story = {
	args: {
		value: sampleJavaScript,
		readonly: false,
		options: {
			completeOptions: {
				caseInsensitive: true,
				guess: false,
				includeKeywords: true,
			},
			defProvider: new MockScriptDefProvider(),
			hintProvider: new MockScriptHintProvider(),
		},
	},
	parameters: {
		docs: {
			description: {
				story: 'Default script editor with basic JavaScript code, syntax highlighting, and autocompletion enabled.',
			},
		},
	},
};
