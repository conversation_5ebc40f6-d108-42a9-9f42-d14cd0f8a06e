import apiConstantData from "cypress/constantData/apiConstantData";
import { commonLocators, sidebar, app, cnt, tile, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _boqPage, _common, _commonAPI, _controllingUnit, _estimatePage, _procurementContractPage, _projectPage, _saleContractPage, _salesPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";


const OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const BOQ_OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const QTO_DESCRIPTION=_common.generateRandomString(4);
const FORMULA_DESC=_common.generateRandomString(4);
const ASSET_MASTER_CODE=Cypress._.random(0, 999);
const ASSET_MASTER_DESCRIPTION=_common.generateRandomString(4)
const LOCATIONS_CODE=Cypress._.random(0, 999);
const LOCATIONS_DESCRIPTION=_common.generateRandomString(4)

let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let API_CONTROLLING_UNIT_PARAMETERS: DataCells, BOQ_STRUCTURE_PARAMETERS: DataCells,QTO_HEADER_PARAMETER:DataCells
let CONTAINERS_CONTRACT,CONTAINERS_QUANTITY_TAKEOFF_HEADER,CONTAINERS_QUANTITY_TAKEOFF,CONTAINERS_FORMULA;

let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_PROCUREMENT_CONTRACT_BOQ_STRUCTURE,CONTAINER_COLUMNS_PROCUREMENT_BOQ_STRUCTURE,CONTAINER_COLUMNS_QUANTITY_TAKEOFF_HEADER,CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL;
let CONTAINERS_PERFORMANCE_ENTRY_SHEET, CONTAINER_COLUMNS_HEADERS;

describe("PCM- 4.307 | Quantity takeoff container in Pes module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/pes-4.307-quantity-takeoff-container-in-pes-module.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            CONTAINER_COLUMNS_PROCUREMENT_CONTRACT_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.CONTRACT_BOQ_STRUCTURE
            CONTAINER_COLUMNS_PROCUREMENT_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.PROCUREMENT_BOQ_STRUCTURE

            CONTAINER_COLUMNS_QUANTITY_TAKEOFF_HEADER=this.data.CONTAINER_COLUMNS.QUANTITY_TAKEOFF_HEADER
            CONTAINERS_QUANTITY_TAKEOFF_HEADER=this.data.CONTAINERS.QUANTITY_TAKEOFF_HEADER
            CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL=this.data.CONTAINER_COLUMNS.QUANTITY_TAKEOFF_DETAIL
            CONTAINERS_QUANTITY_TAKEOFF=this.data.CONTAINERS.QUANTITY_TAKEOFF
            CONTAINERS_FORMULA=this.data.CONTAINERS.FORMULA
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }
            BOQ_STRUCTURE_PARAMETERS = {
                [commonLocators.CommonLabels.TYPE]: commonLocators.CommonLabels.NEW_RECORD,
                [app.GridCells.BRIEF_INFO_SMALL]: BOQ_OUTLINE_SPECIFICATION,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_CONTRACT.QUANTITY[2],
                [app.GridCells.PRICE_SMALL]:CONTAINERS_CONTRACT.UNIT_RATE
            }
            CONTAINER_COLUMNS_HEADERS = this.data.CONTAINER_COLUMNS.HEADERS
            CONTAINERS_PERFORMANCE_ENTRY_SHEET = this.data.CONTAINERS.PERFORMANCE_ENTRY_SHEET
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear();
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT);
                _common.waitForLoaderToDisappear();
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });
    });

    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

    it("TC - API: Create controlling unit", function () {
        API_CONTROLLING_UNIT_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTRACT.QUANTITY[0], CONTAINERS_CONTRACT.QUANTITY[1]],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS]
        }
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, API_CONTROLLING_UNIT_PARAMETERS)
    })

    it('TC - Create Location',function(){
        _common.openTab(app.TabBar.BOQ).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_LOCATION, app.FooterTab.LOCATIONS, 1);
        });

        _common.openTab(app.TabBar.BOQ).then(()=>{
            _common.select_tabFromFooter(cnt.uuid.PROJECTS,app.FooterTab.PROJECTS)
            _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
        })

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();

        _common.openTab(app.TabBar.BOQ).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_LOCATION, app.FooterTab.LOCATIONS, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_LOCATION)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROJECT_LOCATION)
        _common.enterRecord_inNewRow(cnt.uuid.PROJECT_LOCATION, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, LOCATIONS_CODE.toString())
        _common.enterRecord_inNewRow(cnt.uuid.PROJECT_LOCATION, app.GridCells.DESCRIPTION_INFO,app.InputFields.DOMAIN_TYPE_TRANSLATION, LOCATIONS_DESCRIPTION)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Create asset master', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.ASSET_MASTER)
        _common.openTab(app.TabBar.ASSET_MASTER).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ASSET_MASTER_GRID, app.FooterTab.ASSET_MASTER_GRID, 0)
        })
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.ASSET_MASTER_GRID)
        _common.enterRecord_inNewRow(cnt.uuid.ASSET_MASTER_GRID, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, ASSET_MASTER_CODE.toString())
        _common.enterRecord_inNewRow(cnt.uuid.ASSET_MASTER_GRID, app.GridCells.DESCRIPTION_INFO,app.InputFields.DOMAIN_TYPE_TRANSLATION, ASSET_MASTER_DESCRIPTION)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.waitForLoaderToDisappear()

            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        })
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ);
        });
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINER_COLUMNS_PROCUREMENT_CONTRACT_BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()
            _common.set_columnAtTop([CONTAINER_COLUMNS_PROCUREMENT_CONTRACT_BOQ_STRUCTURE.price,CONTAINER_COLUMNS_PROCUREMENT_CONTRACT_BOQ_STRUCTURE.quantity, CONTAINER_COLUMNS_PROCUREMENT_CONTRACT_BOQ_STRUCTURE.briefinfo, CONTAINER_COLUMNS_PROCUREMENT_CONTRACT_BOQ_STRUCTURE.boqlinetypefk], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()


        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()

        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()

        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal_byClass(PROCUREMENT_CONTRACT_PARAMETER)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
      
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.STRUCTURE_CODE, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.MATERIAL)
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        cy.wait(2000) // Added this wait as script was getting failed
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.YES)
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    });

    it('TC - Create procurement boq for the respective contract', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE"))

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS);
        _boqPage.enterRecord_ToCreate_procurementBoQs(CommonLocators.CommonKeys.MATERIAL, OUTLINE_SPECIFICATION, commonLocators.CommonLabels.CREATE_NEW_BOQ);
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it('TC - Create boq structure', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, commonLocators.CommonKeys.ROOT)
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.waitForLoaderToDisappear()
        _boqPage.enterRecord_toCreateBoQStructure(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRUCTURE_PARAMETERS);
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, commonLocators.CommonKeys.ROOT)
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)

        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create new formula", function () {

        let FORMULA_PARAMETERS:DataCells = {
            [app.GridCells.DESCRIPTION_INFO]: FORMULA_DESC,
            [app.GridCells.QTO_FORMULA_TYPE_FK]: commonLocators.CommonKeys.PREDEFINE,
            [app.GridCells.ICON]: CommonLocators.CommonElements.ICON,
            [app.GridCells.VALUE_1_IS_ACTIVE]: commonLocators.CommonKeys.CHECK,
            [app.GridCells.OPERATOR_1]: CONTAINERS_FORMULA.OP[0],
            [app.GridCells.VALUE_2_IS_ACTIVE]:commonLocators.CommonKeys.CHECK,
            [app.GridCells.OPERATOR_2]: CONTAINERS_FORMULA.OP[1],
            [app.GridCells.VALUE_3_IS_ACTIVE]: commonLocators.CommonKeys.CHECK,
            [app.GridCells.OPERATOR_3]: CONTAINERS_FORMULA.OP[2],
            [app.GridCells.VALUE_4_IS_ACTIVE]:commonLocators.CommonKeys.CHECK,
            [app.GridCells.OPERATOR_4]: CONTAINERS_FORMULA.OP[3],
            [app.GridCells.VALUE_5_IS_ACTIVE]: commonLocators.CommonKeys.CHECK,
            [app.GridCells.OPERATOR_5]: CONTAINERS_FORMULA.OP[4]
        }        
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.QTO_FORMULA);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.QTO_MAIN).then(() => {
            _common.select_tabFromFooter(  cnt.uuid.QUANTITY_TAKEOFF_RUBRIC, app.FooterTab.Formula, 0);
        });
        _common.select_rowHasValue(cnt.uuid.QUANTITY_TAKEOFF_RUBRIC,CONTAINERS_FORMULA.LIVE_TAKE_OFF)

        _common.openTab(app.TabBar.QTO_MAIN).then(() => {
            _common.select_tabFromFooter(cnt.uuid.FORMULA, app.FooterTab.Formula, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.FORMULA);
        _common.maximizeContainer(cnt.uuid.FORMULA)
        _common.create_newRecord(cnt.uuid.FORMULA);
        _salesPage.enterRecord_toCreateFormula(FORMULA_PARAMETERS)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.FORMULA,FORMULA_DESC)
        _common.waitForLoaderToDisappear()

        _common.saveCellDataToEnv(cnt.uuid.FORMULA,app.GridCells.CODE,"FORMULA_CODE")
        _common.minimizeContainer(cnt.uuid.FORMULA)
    });

    it("TC - Create qto header", function () {
        QTO_HEADER_PARAMETER = {
            [commonLocators.CommonLabels.QTO_PURPOSE]: commonLocators.CommonKeys.PROCUREMENT_OR_PES,
            [commonLocators.CommonLabels.QTO_TYPE]: commonLocators.CommonKeys.STANDARD_CAPS,
            [commonLocators.CommonLabels.PROJECT]: Cypress.env('API_PROJECT_NUMBER_1'),
            [commonLocators.CommonLabels.DESCRIPTION]: QTO_DESCRIPTION,
            [commonLocators.CommonLabels.CONTRACT]: Cypress.env('CONTRACT_CODE')
        };

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.QTO);
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar()

        _common.openTab(app.TabBar.QTOHEADER).then(() => {
            _common.setDefaultView(app.TabBar.QTOHEADER)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKEOFF_HEADER, app.FooterTab.QUANTITYTAKEOFFHEADER,0)
            _common.setup_gridLayout(cnt.uuid.QUANTITY_TAKEOFF_HEADER, CONTAINER_COLUMNS_QUANTITY_TAKEOFF_HEADER)
        })
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.DETAIL).then(() => {
            _common.setDefaultView(app.TabBar.DETAIL)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.BILL_OF_QUANTITY_LOOKUP, app.FooterTab.BILL_OF_QUANTITY, 0);
        })
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.DETAIL).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKEOFF_DETAIL, app.FooterTab.QUANTITY_TAKEOFF, 1);
            _common.setup_gridLayout(cnt.uuid.QUANTITY_TAKEOFF_DETAIL, CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL)
            _common.set_columnAtTop([CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.qtoformulafk,CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.value1detail,CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.value2detail,CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.result],cnt.uuid.QUANTITY_TAKEOFF_DETAIL)
        })


        _common.openTab(app.TabBar.QTOHEADER).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKEOFF_HEADER, app.FooterTab.QUANTITYTAKEOFFHEADER)
            _common.clear_subContainerFilter(cnt.uuid.QUANTITY_TAKEOFF_HEADER)
        })
        _common.create_newRecord(cnt.uuid.QUANTITY_TAKEOFF_HEADER);
        _common.waitForLoaderToDisappear()

        _salesPage.enterRecord_toCreateQTOHeader_fromQTO_byModal(QTO_HEADER_PARAMETER);
        _common.waitForLoaderToDisappear()
        cy.SAVE();

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, QTO_DESCRIPTION)
    });
    
    it("TC - Create quantity takeoff", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.DETAIL).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BILL_OF_QUANTITY_LOOKUP, app.FooterTab.BILL_OF_QUANTITY, 0);
        })
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.BILL_OF_QUANTITY_LOOKUP)
        _common.clickOn_expandCollapseButton(cnt.uuid.BILL_OF_QUANTITY_LOOKUP, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.clickOn_cellHasIconWithIndex_forContainer(cnt.uuid.BILL_OF_QUANTITY_LOOKUP, app.GridCells.TREE, app.GridCellIcons.ICO_BOQ_ITEM,3)
        _common.clickOn_activeRowCell(cnt.uuid.BILL_OF_QUANTITY_LOOKUP, app.GridCells.MARKER)

        _common.openTab(app.TabBar.DETAIL).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKEOFF_DETAIL, app.FooterTab.QUANTITY_TAKEOFF, 1);
        })
        _common.create_newRecord(cnt.uuid.QUANTITY_TAKEOFF_DETAIL)
        cy.wait(1000) // Added this wait as script was getting failed
        _common.waitForLoaderToDisappear()    

        _common.edit_dropdownCellWithInput(cnt.uuid.QUANTITY_TAKEOFF_DETAIL, app.GridCells.QTO_FORMULA_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_COLUMN_VOLUME)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKEOFF_DETAIL)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKEOFF_DETAIL, app.GridCells.VALUE_1_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE1[0])
        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKEOFF_DETAIL, app.GridCells.VALUE_2_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE2)
        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKEOFF_DETAIL, app.GridCells.VALUE_3_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE3)

        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKEOFF_DETAIL)
        cy.SAVE()
        _common.waitForLoaderToDisappear()    

        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear() 
        
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, QTO_DESCRIPTION)

        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.DETAIL).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BILL_OF_QUANTITY_LOOKUP, app.FooterTab.BILL_OF_QUANTITY, 0);
        })
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.BILL_OF_QUANTITY_LOOKUP)
        _common.clickOn_expandCollapseButton(cnt.uuid.BILL_OF_QUANTITY_LOOKUP, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.clickOn_cellHasIconWithIndex_forContainer(cnt.uuid.BILL_OF_QUANTITY_LOOKUP, app.GridCells.TREE, app.GridCellIcons.ICO_BOQ_ITEM,3)
        _common.clickOn_activeRowCell(cnt.uuid.BILL_OF_QUANTITY_LOOKUP, app.GridCells.MARKER)
    
        _common.openTab(app.TabBar.DETAIL).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKEOFF_DETAIL, app.FooterTab.QUANTITY_TAKEOFF, 1);
        })
        _common.select_rowInContainer(cnt.uuid.QUANTITY_TAKEOFF_DETAIL)
    })  
    
    it("TC - Create / update PES from QTO", function () {
        let CREATE_UPDATE_PES_QTO:DataCells={
            [app.ModalInputFields.CONTROL_DIRECTIVE]:app.GridCells.CREATE_TYPE1,
            [app.ModalInputFields.DATE_DELIVERED]: _common.getDate(commonLocators.CommonKeys.CURRENT)
        }
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_UPDATE_PES);
        _common.waitForLoaderToDisappear()
        _saleContractPage.createUpdatePES_fromQTOWizard_byClass(CREATE_UPDATE_PES_QTO)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_2)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS,0)
            _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_HEADERS)
            _common.waitForLoaderToDisappear()
        })

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS_CAPITAL,0)
            _common.waitForLoaderToDisappear()
        })

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE,1)
            _common.waitForLoaderToDisappear()

            _common.setup_gridLayout(cnt.uuid.PES_BOQS_STRUCTURE,CONTAINER_COLUMNS_PROCUREMENT_BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()

            _common.set_columnAtTop([CONTAINER_COLUMNS_PROCUREMENT_BOQ_STRUCTURE.quantity],cnt.uuid.PES_BOQS_STRUCTURE)
            _common.waitForLoaderToDisappear()
        })

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF,2)
            _common.setup_gridLayout(cnt.uuid.QUANTITY_TAKE_OFF_PES, CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL)
            _common.waitForLoaderToDisappear()
            _common.set_columnAtTop([
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.qtoformulafk,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.value1detail,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.operator1,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.value2detail,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.operator2,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.value3detail,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.operator3,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.value4detail,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.operator4,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.value5detail,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.operator5,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.result,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.mdccontrollingunitfk,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.prcstructurefk,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.assetmasterfk,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.prjlocationfk
            ],cnt.uuid.QUANTITY_TAKE_OFF_PES)
            _common.waitForLoaderToDisappear()
        })

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'));
        _common.waitForLoaderToDisappear()

        cy.wait(4000) // Added this wait as script was getting failed

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS_CAPITAL)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_ITEMS)
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
        cy.wait(2000) // Added this wait as script was getting failed

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,commonLocators.CommonKeys.POSITION)
    })

    it("TC - Create button is working", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.clear_subContainerFilter(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.create_newRecord(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.waitForLoaderToDisappear()    

        _common.edit_dropdownCellWithInput(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.QTO_FORMULA_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_AREA)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_1_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE1[1])
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_2_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE2)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        cy.SAVE()
        _common.waitForLoaderToDisappear()   
        cy.SAVE()
        _common.waitForLoaderToDisappear()    

        _common.minimizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear() 

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'));
        _common.waitForLoaderToDisappear()
        cy.wait(1000) // Added this wait as script was getting failed

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS_CAPITAL)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_ITEMS)
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,commonLocators.CommonKeys.POSITION)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.clickOn_cellHasValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_AREA)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_AREA)
        _common.minimizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

    })

    it("TC - Delete button is working", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.clear_subContainerFilter(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.clickOn_cellHasValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_AREA)
        _common.delete_recordFromContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        cy.SAVE()
        _common.waitForLoaderToDisappear()    
        _common.minimizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear() 

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'));
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS_CAPITAL)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_ITEMS)
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,commonLocators.CommonKeys.POSITION)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.select_rowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _validate.assert_cellData_not_equal(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_AREA)
        _common.minimizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

    })   
    
    it("TC - Deep copy function is working, it will copy a record", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()

        })
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.clear_subContainerFilter(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.clickOn_cellHasValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_COLUMN_VOLUME)
        _common.clickOn_toolbarButton(cnt.uuid.QUANTITY_TAKE_OFF_PES,btn.ToolBar.ICO_COPY_PASTE_DEEP)

        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue_onIndexBased(cnt.uuid.QUANTITY_TAKE_OFF_PES,CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_COLUMN_VOLUME,1)
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_COLUMN_VOLUME)
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_1_DETAIL, CONTAINERS_QUANTITY_TAKEOFF.VALUE1[0])
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_2_DETAIL, CONTAINERS_QUANTITY_TAKEOFF.VALUE2)
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_3_DETAIL, CONTAINERS_QUANTITY_TAKEOFF.VALUE3)

        _common.minimizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

    })

    it("TC - Check each field is working (location, asset maser, procurement structure, controlling unit)", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.clear_subContainerFilter(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.create_newRecord(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.waitForLoaderToDisappear()    

        _common.edit_dropdownCellWithInput(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.QTO_FORMULA_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_AREA)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_1_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE1[1])
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_2_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE2)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.edit_dropdownCellWithInput(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.ASSET_MASTER_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, ASSET_MASTER_CODE.toString())
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000) // Added this wait as script was getting failed

        _common.edit_dropdownCellWithInput(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.MDC_CONTROLLING_UNIT_FK_SMALL, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,  Cypress.env(`API_CNT_CODE_0`))
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.edit_dropdownCellWithInput(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.PRC_STRUCTURE_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.M)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.edit_dropdownCellWithInput(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.PRJ_LOCATION_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, LOCATIONS_CODE.toString())
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        cy.wait(1000) // Added this wait as script was getting failed
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear() 

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'));
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS_CAPITAL)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_ITEMS)
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,commonLocators.CommonKeys.POSITION)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.clickOn_cellHasValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_AREA)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,CONTAINERS_QUANTITY_TAKEOFF.RECTANGLE_AREA)
        _common.assert_forNumericValues(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_1_DETAIL, CONTAINERS_QUANTITY_TAKEOFF.VALUE1[1])
        _common.assert_forNumericValues(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_2_DETAIL, CONTAINERS_QUANTITY_TAKEOFF.VALUE2)
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.MDC_CONTROLLING_UNIT_FK_SMALL,  Cypress.env(`API_CNT_CODE_0`))
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.PRC_STRUCTURE_FK, commonLocators.CommonKeys.M)
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.ASSET_MASTER_FK, ASSET_MASTER_CODE.toString())
        _common.assert_cellData_insideActiveRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.PRJ_LOCATION_FK,  LOCATIONS_CODE.toString())

        _common.minimizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

    })

    it("TC - Check formula type='Script', then input value and operator to calculate result)", function () {
       let scriptResult:any=parseInt(CONTAINERS_QUANTITY_TAKEOFF.VALUE1[1])  * parseInt(CONTAINERS_QUANTITY_TAKEOFF.VALUE2);
        _common.assert_forNumericValues(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.RESULT,scriptResult.toString())
    })

    it("TC - Check formula type='predefine', then input value and operator to calculate result)", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)


        _common.clear_subContainerFilter(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.create_newRecord(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.waitForLoaderToDisappear()    

        _common.edit_dropdownCellWithInput(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.QTO_FORMULA_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, FORMULA_DESC)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_1_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE1[0])
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)  
        _common.edit_dropdownCellWithCaret(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.OPERATOR_1,commonLocators.CommonKeys.LIST,CONTAINERS_FORMULA.OP[0])
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_2_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE2)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.OPERATOR_2,commonLocators.CommonKeys.LIST,CONTAINERS_FORMULA.OP[1])
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_3_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE3)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.OPERATOR_3,commonLocators.CommonKeys.LIST,CONTAINERS_FORMULA.OP[2])
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_4_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE4)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.OPERATOR_4,commonLocators.CommonKeys.LIST,CONTAINERS_FORMULA.OP[3])
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.enterRecord_inNewRow(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.VALUE_5_DETAIL, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_QUANTITY_TAKEOFF.VALUE5)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.GridCells.OPERATOR_5,commonLocators.CommonKeys.LIST,CONTAINERS_FORMULA.OP[4])
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        cy.SAVE()
        _common.waitForLoaderToDisappear()  
        cy.SAVE()
        _common.waitForLoaderToDisappear()   

        _common.clickOn_cellHasValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,Cypress.env('FORMULA_CODE'))
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.assert_forNumericValues(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.RESULT,CONTAINERS_QUANTITY_TAKEOFF.RESULT)
        _common.minimizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

    })

    it("TC - Sum(result) of quantity take off should be update to boq structure quantity)", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })
        let sumResult= _common.returnArrayForMultipleCell(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.RESULT)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,commonLocators.CommonKeys.POSITION)
        cy.wait(1000) // Added this wait as script was getting failed; 

        _common.maximizeContainer(cnt.uuid.PES_BOQS_STRUCTURE);
        cy.then(()=>{
            _common.getText_fromCell(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.QUANTITY_SMALL)
                    .then(($sumResultValTotal) => {
                        let sumResultVal: any = 0;
                        for (var i in sumResult) {
                            sumResultVal += +parseFloat(sumResult[i].replace(',', ''));
                        }
                        expect(parseFloat($sumResultValTotal.text().replace(',', ''))).equal(parseFloat(sumResultVal));
                        _common.minimizeContainer(cnt.uuid.PES_BOQS_STRUCTURE);
                    });
        })
         
    })

    it("TC - If disable=true, this record's result should not update to boq structure quantity", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF,2)
            _common.waitForLoaderToDisappear()
            _common.set_columnAtTop([
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.qtoformulafk,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.result,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.isreadonly,
                CONTAINER_COLUMNS_QUANTITY_TAKEOFF_DETAIL.isblocked
            ],cnt.uuid.QUANTITY_TAKE_OFF_PES)
        })
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS_CAPITAL)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_ITEMS)
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,commonLocators.CommonKeys.POSITION)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })

        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.clickOn_cellHasValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,Cypress.env('FORMULA_CODE'))
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.set_cellCheckboxValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.IS_BLOCKED,commonLocators.CommonKeys.CHECK)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        let sumResult= _common.returnArrayForMultipleCell(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.RESULT)
        _common.minimizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,commonLocators.CommonKeys.POSITION)
        cy.wait(1000) // Added this wait as script was getting failed; // Ensure UI updates before proceeding

        _common.maximizeContainer(cnt.uuid.PES_BOQS_STRUCTURE);
        cy.then(()=>{
            _common.getText_fromCell(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.QUANTITY_SMALL)
                    .then(($sumResultValTotal) => {
                        let sumResultVal: any = 0;
                        for (var i in sumResult) {
                            if (parseFloat(sumResult[i].replace(',', '')) !== parseFloat(CONTAINERS_QUANTITY_TAKEOFF.RESULT)) {  // Fix: Convert i to a number
                                sumResultVal += +parseFloat(sumResult[i].replace(',', ''));
                            }
                        }
                        expect(parseFloat($sumResultValTotal.text().replace(',', ''))).equal(parseFloat(sumResultVal));
                        _common.minimizeContainer(cnt.uuid.PES_BOQS_STRUCTURE);
                    }); 
                })
    })

    it("TC - If isreadonly=true, the record should be read only", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.QUANTITY_TAKE_OFF_PES, app.FooterTab.QUANTITY_TAKEOFF)
            _common.waitForLoaderToDisappear()
        })
        _common.maximizeContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _common.clickOn_cellHasValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,Cypress.env('FORMULA_CODE'))
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.set_cellCheckboxValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.IS_BLOCKED,commonLocators.CommonKeys.UNCHECK)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _common.set_cellCheckboxValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.IS_READONLY,commonLocators.CommonKeys.CHECK)

        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.clickOn_cellHasValue(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,Cypress.env('FORMULA_CODE'))
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)

        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.QTO_FORMULA_FK,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.VALUE_1_DETAIL,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.OPERATOR_1,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.VALUE_2_DETAIL,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.OPERATOR_2,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.VALUE_3_DETAIL,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.OPERATOR_3,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.VALUE_4_DETAIL,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.OPERATOR_4,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.VALUE_5_DETAIL,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.OPERATOR_5,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.RESULT,commonLocators.CommonKeys.NOT_VISIBLE)
        _common.select_activeRowInContainer(cnt.uuid.QUANTITY_TAKE_OFF_PES)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.MDC_CONTROLLING_UNIT_FK_SMALL,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.PRC_STRUCTURE_FK,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.ASSET_MASTER_FK,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.QUANTITY_TAKE_OFF_PES,app.GridCells.PRJ_LOCATION_FK,commonLocators.CommonKeys.NOT_VISIBLE)
    })
});