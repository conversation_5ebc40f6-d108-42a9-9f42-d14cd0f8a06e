/*
 * Copyright(c) RIB Software GmbH
 */

import { Observable } from 'rxjs';
import { CompleteIdentification } from '@libs/platform/common';
import { IEntityProxy, IReadonlyParentService } from '@libs/procurement/shared';
import { IProcurementBoqHeaderContext, IPrcHeaderContext, IProcurementMainContext } from './prc-header-context.interface';
import { IPrcHeaderEntity } from '@libs/procurement/interfaces';
import { ICreationDataProvider } from '@libs/basics/shared';

/**
 * Base interface for procurement main data services that provides common functionality
 * for managing procurement headers and their associated context.
 *
 * @template T - The entity type extending object
 * @template U - The complete identification type for the entity
 */
export interface IProcurementMainDataService<T extends object, U extends CompleteIdentification<T>> extends IReadonlyParentService<T, U> {
	/**
	 * Entity proxy that handles common entity operations such as comparison,
	 * modification tracking, and change notifications for the procurement header.
	 */
	get entityProxy(): IEntityProxy<T>;

	/**
	 * Observable that emits when the readonly state of the procurement header changes.
	 * Used to react to changes in edit permissions and update UI accordingly.
	 */
	get readonlyChanged$(): Observable<boolean>;

	/**
	 * Observable that emits when exchange rate or currency changes in the procurement header.
	 * Used to recalculate monetary values and update currency-dependent fields.
	 */
	get exchangeRateChanged$(): Observable<IExchangeRateChangedEvent>;

	/**
	 * Retrieves the current procurement header context containing essential information
	 * such as project, tax code, controlling unit, and readonly state.
	 *
	 * @returns The current procurement header context
	 */
	getHeaderContext(): IProcurementMainContext;
}

/**
 * Extended interface for procurement header services that handle BOQ (Bill of Quantities) operations.
 * Provides access to BOQ-specific context information in addition to base procurement functionality.
 *
 * @template T - The entity type extending object
 * @template U - The complete identification type for the entity
 */
export interface IProcurementBoqHeaderDataService<T extends object, U extends CompleteIdentification<T>> extends IProcurementMainDataService<T, U> {
	/**
	 * Retrieves the BOQ-specific procurement header context containing additional fields
	 * for WIC (Work Item Category) groups and BOQ entities.
	 *
	 * @returns The BOQ procurement header context
	 */
	getHeaderContext(): IProcurementBoqHeaderContext;
}

/**
 * Comprehensive interface for procurement header data services that provides full
 * procurement functionality including entity management, payment terms, exchange rates,
 * and lead time calculations.
 *
 * @template T - The entity type extending object
 * @template U - The complete identification type for the entity
 */
export interface IPrcHeaderDataService<T extends object, U extends CompleteIdentification<T>> extends IProcurementBoqHeaderDataService<T, U> {
	/**
	 * Observable that emits when payment terms change in the procurement header.
	 * Used to react to changes in financial or procurement payment terms.
	 */
	get paymentTermChanged$(): Observable<IPaymentTermChangedEvent>;

	/**
	 * Observable that emits when totals is invalidated and should be updated.
	 */
	get totalsInvalidated$(): Observable<void>;

	/**
	 * Invalidate current header totals and recalculate again
	 */
	invalidateTotals(): Promise<void>;

	/**
	 * Retrieves the complete procurement header context containing all procurement-related
	 * information including financial, contractual, and operational details.
	 *
	 * @returns The complete procurement header context
	 */
	getHeaderContext(): IPrcHeaderContext;

	/**
	 * Retrieves the currently selected procurement header entity with all its properties
	 * and relationships loaded.
	 *
	 * @returns The selected procurement header entity
	 */
	getSelectedPrcHeaderEntity(): IPrcHeaderEntity;

	/**
	 * Updates the total lead time for the procurement header when lead time changes
	 * in any associated item container.
	 *
	 * @param value - The new lead time value in the appropriate time unit
	 */
	updateTotalLeadTime(value: number): void;

	/**
	 * Extracts the procurement header entity from a given entity object.
	 * Used to access header-specific information from complex entity structures.
	 *
	 * @param entity - The entity from which to extract the procurement header
	 * @returns The procurement header entity
	 */
	getPrcHeaderEntity(entity: T): IPrcHeaderEntity;

	/**
	 * Optional provider for creating total entity data when needed.
	 * Used for generating summary or aggregate entities during procurement operations.
	 */
	totalCreationProvider?: ICreationDataProvider<T>;
}

/**
 * Event data structure for payment term changes in procurement headers.
 * Contains information about both financial and procurement payment terms.
 */
export interface IPaymentTermChangedEvent {
	/**
	 * Foreign key reference to the financial payment term.
	 * Can be null if no financial payment term is set.
	 */
	paymentTermFiFk?: number | null;

	/**
	 * Foreign key reference to the procurement payment term.
	 * Required field for procurement operations.
	 */
	paymentTermPaFk?: number;
}

/**
 * Event data structure for exchange rate and currency changes in procurement headers.
 * Contains information about the new exchange rate, currency, and whether the currency itself changed.
 */
export interface IExchangeRateChangedEvent {
	/**
	 * The new exchange rate value used for currency conversions.
	 */
	exchangeRate: number;

	/**
	 * Foreign key reference to the currency being used.
	 */
	currencyFk: number;

	/**
	 * Indicates whether the currency itself changed (not just the exchange rate).
	 * Used to determine if more extensive recalculations are needed.
	 */
	isCurrencyChanged: boolean;
}
