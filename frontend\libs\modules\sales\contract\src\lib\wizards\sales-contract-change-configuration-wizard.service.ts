/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { SalesCommonChangeSalesTypeOrConfigurationWizard } from '@libs/sales/common';
import { find } from 'lodash';
import { OrdStatusLookupService, salesContractContractLookupService } from '../services/lookups';
import { SalesContractContractsDataService } from '../services/sales-contract-contracts-data.service';
import { BasicsSharedOrderTypeLookupService } from '@libs/basics/shared';
import { IChangeTypeConfigurationEntity, IGetRowData, IOrdHeaderEntity, ITypeLookupService } from '@libs/sales/interfaces';
import { createLookup, IEditorDialogResult, IFieldLookupOptions, IFieldValueChangeInfo } from '@libs/ui/common';
import { PropertyType } from '@libs/platform/common';

/**
 * This service is used to change the sales type or configuration of a sales contract.
 */
@Injectable({
	providedIn: 'root'
})
export class SalesContractChangeConfigurationWizardService extends SalesCommonChangeSalesTypeOrConfigurationWizard<IOrdHeaderEntity> {
	/**
	 * To inject OrdStatusLookupService
	 */
	protected readonly statusLookup = inject(OrdStatusLookupService);
	/**
	 * To inject SalesContractContractsDataService
	 */
	protected readonly salesContractService = inject(SalesContractContractsDataService);
	/**
	 * To inject BasicsSharedOrderTypeLookupService
	 */
	public readonly typeLookupService = inject(BasicsSharedOrderTypeLookupService);

	/**
	 * Provides form row data according to the module.
	 * @returns IGetRowData
	 */
	protected getRowData(): IGetRowData {
		return {
			rowId: 'OrdHeaderFk',
			rowLabel: {
				text: 'Main Contract',
				key: 'sales.contract.entityOrdHeaderFk',
			},
			rowModel: 'OrdHeaderFk',
			formDialogHederText: 'sales.contract.generatePaymentScheduleFromSchedule.entityChangeSalesContractConfig',
		};
	}

	/**
	 * Provides form row type lookup service according to the module.
	 * @returns ITypeLookupService<IOrdHeaderEntity>
	 */
	protected getTypeLookupService(): ITypeLookupService<IOrdHeaderEntity> {
		return this.typeLookupService;
	}

	/**
	 * Provides form row data lookup service token according to the module.
	 * @returns IFieldLookupOptions<IChangeTypeConfigurationEntity>
	 */
	protected rowDataServiceToken(): IFieldLookupOptions<IChangeTypeConfigurationEntity> {
		return createLookup({
			dataServiceToken: salesContractContractLookupService,
			showClearButton: true,
			showDescription: true,
			descriptionMember: 'Descriptioninfo',
		});
	}

	/**
	 * Provides form row type lookup service token according to the module.
	 * @returns IFieldLookupOptions<IChangeTypeConfigurationEntity>
	 */
	protected rowTypeDataServiceToken(): IFieldLookupOptions<IChangeTypeConfigurationEntity> {
		return createLookup({
			dataServiceToken: BasicsSharedOrderTypeLookupService,
			showClearButton: false,
			displayMember: 'DescriptionInfo',
		});
	}

	public title = 'sales.contract.generatePaymentScheduleFromSchedule.entityChangeSalesContractConfig';

	/**
	 * To change the sales contract configuration
	 * @param {string} currentModule
	 * @returns {Promise<void>}
	 */
	public async changeSalesConfiguration(currentModule: string): Promise<void> {
		this.currentModuleName = currentModule;
		this.selectedEntity = this.salesContractService.getSelectedEntity();

		if (this.selectedEntity) {
			const contractStatus = await this.statusLookup.getItemByKeyAsync({ id: this.selectedEntity.OrdStatusFk });
			this.changeTypeConfigurationEntity = {
				ChangeEntityFk: this.selectedEntity.PrjChangeFk ?? null,
				TypeFk: this.selectedEntity.TypeFk ?? 0,
				RubricCategoryFk: this.selectedEntity.RubricCategoryFk ?? 0,
				ConfigurationFk: this.selectedEntity.ConfigurationFk ?? null,
				OrdHeaderFk: this.selectedEntity.OrdHeaderFk ?? null,
			};

			if (contractStatus?.IsOrdered || contractStatus?.IsCanceled || contractStatus?.IsReadOnly) {
				this.messageBoxService.showMsgBox('sales.common.entityRestrictedStatus', this.title, 'ico-info');
			} else {
				this.showChangeSalesTypeOrConfigurationWizard();
				const TypeRow = find(this.changeTypeConfigurationFormConfig.rows, { id: 'TypeFk' });
				if (TypeRow) {
					const changeInfo: IFieldValueChangeInfo<IChangeTypeConfigurationEntity, PropertyType> = {
						oldValue: this.selectedEntity.TypeFk ?? 0,
						newValue: this.selectedEntity.TypeFk ?? 0,
						field: TypeRow,
						entity: this.changeTypeConfigurationEntity,
					};
					this.updateRowConfig(changeInfo);
				}
			}
		} else {
			this.messageBoxService.showMsgBox('sales.contract.noContractHeaderSelected', 'sales.contract.contractSelectionMissing', 'ico-info');
		}
	}

	/**
	 * To handle the OK button click event in the dialog
	 * @param {IEditorDialogResult<IChangeTypeConfigurationEntity>} result
	 */
	public override okButtonDialog(result: IEditorDialogResult<IChangeTypeConfigurationEntity>) {
		if (!result.value || !this.selectedEntity) {
			return;
		}
		if (this.currentModuleName === 'sales.contract') {
			const postData = {
				TypeFk: result.value.TypeFk,
				RubricCategoryFk: result.value.RubricCategoryFk,
				ConfigurationFk: result.value.ConfigurationFk,
				ChangeEntityFk: result.value.ChangeEntityFk,
				OrdHeaderFk: result.value.OrdHeaderFk,
				BidHeaderFk: null,
				ContractId: this.selectedEntity.Id,
			};
			const url = 'sales/contract/changesalestypeorconfiguration';
			this.http.post$(url, postData).subscribe((res) => {
				if (Object(res).IsSuccess) {
					if (this.selectedEntity) {
						this.selectedEntity.PrjChangeFk = result.value?.ChangeEntityFk;
						this.selectedEntity.ConfigurationFk = result.value?.ConfigurationFk;
						this.selectedEntity.TypeFk = result.value?.TypeFk;
						this.selectedEntity.RubricCategoryFk = result.value?.RubricCategoryFk ?? 0;
						this.messageBoxService.showMsgBox('sales.common.changeSalesTypeOrConfigWizard.chgSuccess', this.title, 'ico-info');
						this.salesContractService.setModified(this.selectedEntity);
					}	
				} else {
					this.messageBoxService.showMsgBox(Object(res).Message, this.title, 'ico-warning');
					return;
				}
			});
		}
	}
}