﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Procurement.PublicApi.BusinessComponents;

namespace RIB.Visual.Procurement.PublicApi.ServiceFacade.WebApi.V4
{


    /// <summary>
    /// There are no comments for RfqHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("RFQ_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(4)]
    public partial class RfqHeaderApiDto : RIB.Visual.Platform.Core.ITypedDto<RfqHeaderApiEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class RfqHeaderApiDto.
        /// </summary>
        public RfqHeaderApiDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class RfqHeaderApiDto.
        /// </summary>
        /// <param name="entity">the instance of class RfqHeaderApiEntity</param>
        public RfqHeaderApiDto(RfqHeaderApiEntity entity)
        {
            Id = entity.Id;
            RfqStatusId = entity.RfqStatusId;
            RfqStatusDescription = entity.RfqStatusDescription;
            CompanyId = entity.CompanyId;
            CompanyCode = entity.CompanyCode;
            ProjectId = entity.ProjectId;
            ProjectCode = entity.ProjectCode;
            ProjectDescription = entity.ProjectDescription;
            ClerkPrcId = entity.ClerkPrcId;
            ClerkPrcCode = entity.ClerkPrcCode;
            ClerkPrcDescription = entity.ClerkPrcDescription;
            ClerkReqId = entity.ClerkReqId;
            ClerkReqCode = entity.ClerkReqCode;
            ClerkReqDescription = entity.ClerkReqDescription;
            CurrencyId = entity.CurrencyId;
            CurrencyDescription = entity.CurrencyDescription;
            PaymentTermFiId = entity.PaymentTermFiId;
            PaymentTermFiCode = entity.PaymentTermFiCode;
            PaymentTermFiDescription = entity.PaymentTermFiDescription;
            PaymentTermPaId = entity.PaymentTermPaId;
            PaymentTermPaCode = entity.PaymentTermPaCode;
            PaymentTermPaDescription = entity.PaymentTermPaDescription;
            Code = entity.Code;
            Description = entity.Description;
            SearchPattern = entity.SearchPattern;
            AwardReference = entity.AwardReference;
            DateRequested = entity.DateRequested;
            DateCanceled = entity.DateCanceled;
            DateQuotedeadline = entity.DateQuotedeadline;
            TimeQuotedeadline = entity.TimeQuotedeadline;
            LocaQuotedeadline = entity.LocaQuotedeadline;
            DateAwarddeadline = entity.DateAwarddeadline;
            RfqTypeId = entity.RfqTypeId;
            RfqTypeDescription = entity.RfqTypeDescription;
            PrcAwardMethodId = entity.PrcAwardMethodId;
            PrcAwardMethodDescription = entity.PrcAwardMethodDescription;
            PrcConfigurationId = entity.PrcConfigurationId;
            PrcConfigurationDescription = entity.PrcConfigurationDescription;
            PrcContractTypeId = entity.PrcContractTypeId;
            PrcContractTypeDescription = entity.PrcContractTypeDescription;
            PrcStrategyId = entity.PrcStrategyId;
            PrcStrategyDescription = entity.PrcStrategyDescription;
            RfqHeaderId = entity.RfqHeaderId;
            RfqHeaderCode = entity.RfqHeaderCode;
            RfqHeaderDescription = entity.RfqHeaderDescription;
            Remark = entity.Remark;
            UserDefined1 = entity.UserDefined1;
            UserDefined2 = entity.UserDefined2;
            UserDefined3 = entity.UserDefined3;
            UserDefined4 = entity.UserDefined4;
            UserDefined5 = entity.UserDefined5;
            PlannedStart = entity.PlannedStart;
            PlannedEnd = entity.PlannedEnd;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            BpdEvaluationSchemaId = entity.BpdEvaluationSchemaId;
            BpdEvaluationSchemaDescription = entity.BpdEvaluationSchemaDescription;
            PaymentTermAdId = entity.PaymentTermAdId;
            PaymentTermAdCode = entity.PaymentTermAdCode;
            PaymentTermAdDescription = entity.PaymentTermAdDescription;
            MdcBillingSchemaId = entity.MdcBillingSchemaId;
            MdcBillingSchemaDescription = entity.MdcBillingSchemaDescription;
            LanguageId = entity.LanguageId;
            DateDelivery = entity.DateDelivery;
            BasLanguageFk = entity.BasLanguageFk;
            BasLanguageDesc = entity.BasLanguageDesc;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for RfqStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RfqStatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int RfqStatusId { get; set; }
    
        /// <summary>
        /// There are no comments for RfqStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string RfqStatusDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyId { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string CompanyCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public int? ProjectId { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ProjectCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ProjectDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public int? ClerkPrcId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkPrcCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkPrcDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public int? ClerkReqId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkReqCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkReqDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CurrencyId { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string CurrencyDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_ID", TypeName = "int", Order = 16)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermFiFk")]
        public int? PaymentTermFiId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_CODE", TypeName = "nvarchar(16)", Order = 17)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermFiCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_DESC", TypeName = "nvarchar(2000)", Order = 18)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermFiDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_ID", TypeName = "int", Order = 19)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermPaFk")]
        public int? PaymentTermPaId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_CODE", TypeName = "nvarchar(16)", Order = 20)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermPaCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_DESC", TypeName = "nvarchar(2000)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermPaDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 22)]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }
    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 24)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public string SearchPattern { get; set; }
    
        /// <summary>
        /// There are no comments for AwardReference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AWARD_REFERENCE", TypeName = "nvarchar(252)", Order = 25)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AwardReference")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string AwardReference { get; set; }
    
        /// <summary>
        /// There are no comments for DateRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REQUESTED", TypeName = "date", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateRequested")]
        public System.DateTime? DateRequested { get; set; }
    
        /// <summary>
        /// There are no comments for DateCanceled in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CANCELED", TypeName = "date", Order = 27)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCanceled")]
        public System.DateTime? DateCanceled { get; set; }
    
        /// <summary>
        /// There are no comments for DateQuotedeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_QUOTEDEADLINE", TypeName = "date", Order = 28)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateQuoteDeadline")]
        public System.DateTime? DateQuotedeadline { get; set; }
    
        /// <summary>
        /// There are no comments for TimeQuotedeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TIME_QUOTEDEADLINE", TypeName = "time", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TimeQuoteDeadline")]
        public global::System.TimeSpan? TimeQuotedeadline { get; set; }
    
        /// <summary>
        /// There are no comments for LocaQuotedeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LOCA_QUOTEDEADLINE", TypeName = "nvarchar(252)", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("LocaQuoteDeadline")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string LocaQuotedeadline { get; set; }
    
        /// <summary>
        /// There are no comments for DateAwarddeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_AWARDDEADLINE", TypeName = "date", Order = 31)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateAwardDeadline")]
        public System.DateTime? DateAwarddeadline { get; set; }
    
        /// <summary>
        /// There are no comments for RfqTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_TYPE_ID", TypeName = "int", Order = 32)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RfqTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int RfqTypeId { get; set; }
    
        /// <summary>
        /// There are no comments for RfqTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 33)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string RfqTypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcAwardMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_ID", TypeName = "int", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcAwardMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcAwardMethodId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcAwardMethodDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_DESC", TypeName = "nvarchar(2000)", Order = 35)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcAwardMethodDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 36)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcConfigurationFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcConfigurationId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_DESC", TypeName = "nvarchar(2000)", Order = 37)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcConfigurationDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcContractTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcContractTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcContractTypeId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcContractTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_DESC", TypeName = "nvarchar(2000)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcContractTypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcStrategyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRATEGY_ID", TypeName = "int", Order = 40)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcStrategyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcStrategyId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcStrategyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRATEGY_DESC", TypeName = "nvarchar(2000)", Order = 41)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcStrategyDescription { get; set; }
    
        /// <summary>
        /// There are no comments for RfqHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_ID", TypeName = "int", Order = 42)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RfqHeaderFk")]
        public int? RfqHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for RfqHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_CODE", TypeName = "nvarchar(16)", Order = 43)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string RfqHeaderCode { get; set; }
    
        /// <summary>
        /// There are no comments for RfqHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_DESC", TypeName = "nvarchar(252)", Order = 44)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string RfqHeaderDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined1 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 47)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined2 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined3 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined4 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined5 { get; set; }
    
        /// <summary>
        /// There are no comments for PlannedStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_START", TypeName = "date", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedStart")]
        public System.DateTime? PlannedStart { get; set; }
    
        /// <summary>
        /// There are no comments for PlannedEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_END", TypeName = "date", Order = 52)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedEnd")]
        public System.DateTime? PlannedEnd { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InsertedAt")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InsertedBy")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 55)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UpdatedAt")]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 56)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UpdatedBy")]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Version")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }
    
        /// <summary>
        /// There are no comments for BpdEvaluationSchemaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATIONSCHEMA_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("EvaluationSchemaFk")]
        public int? BpdEvaluationSchemaId { get; set; }
    
        /// <summary>
        /// There are no comments for BpdEvaluationSchemaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATIONSCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BpdEvaluationSchemaDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_ID", TypeName = "int", Order = 60)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermAdFk")]
        public int? PaymentTermAdId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_CODE", TypeName = "nvarchar(16)", Order = 61)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermAdCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_DESC", TypeName = "nvarchar(2000)", Order = 62)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermAdDescription { get; set; }
    
        /// <summary>
        /// There are no comments for MdcBillingSchemaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_ID", TypeName = "int", Order = 63)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BillingSchemaFk")]
        public int? MdcBillingSchemaId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcBillingSchemaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 64)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcBillingSchemaDescription { get; set; }
    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 65)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int LanguageId { get; set; }
    
        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? DateDelivery { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 67)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public int? BasLanguageFk { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 68)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BasLanguageDesc { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(RfqHeaderApiEntity); }
        }

        /// <summary>
        /// Copy the current RfqHeaderApiDto instance to a new RfqHeaderApiEntity instance.
        /// </summary>
        /// <returns>a new instance of class RfqHeaderApiEntity</returns>
        public RfqHeaderApiEntity Copy()
        {
          var entity = new RfqHeaderApiEntity();

          entity.Id = this.Id;
          entity.RfqStatusId = this.RfqStatusId;
          entity.RfqStatusDescription = this.RfqStatusDescription;
          entity.CompanyId = this.CompanyId;
          entity.CompanyCode = this.CompanyCode;
          entity.ProjectId = this.ProjectId;
          entity.ProjectCode = this.ProjectCode;
          entity.ProjectDescription = this.ProjectDescription;
          entity.ClerkPrcId = this.ClerkPrcId;
          entity.ClerkPrcCode = this.ClerkPrcCode;
          entity.ClerkPrcDescription = this.ClerkPrcDescription;
          entity.ClerkReqId = this.ClerkReqId;
          entity.ClerkReqCode = this.ClerkReqCode;
          entity.ClerkReqDescription = this.ClerkReqDescription;
          entity.CurrencyId = this.CurrencyId;
          entity.CurrencyDescription = this.CurrencyDescription;
          entity.PaymentTermFiId = this.PaymentTermFiId;
          entity.PaymentTermFiCode = this.PaymentTermFiCode;
          entity.PaymentTermFiDescription = this.PaymentTermFiDescription;
          entity.PaymentTermPaId = this.PaymentTermPaId;
          entity.PaymentTermPaCode = this.PaymentTermPaCode;
          entity.PaymentTermPaDescription = this.PaymentTermPaDescription;
          entity.Code = this.Code;
          entity.Description = this.Description;
          entity.SearchPattern = this.SearchPattern;
          entity.AwardReference = this.AwardReference;
          entity.DateRequested = this.DateRequested;
          entity.DateCanceled = this.DateCanceled;
          entity.DateQuotedeadline = this.DateQuotedeadline;
          entity.TimeQuotedeadline = this.TimeQuotedeadline;
          entity.LocaQuotedeadline = this.LocaQuotedeadline;
          entity.DateAwarddeadline = this.DateAwarddeadline;
          entity.RfqTypeId = this.RfqTypeId;
          entity.RfqTypeDescription = this.RfqTypeDescription;
          entity.PrcAwardMethodId = this.PrcAwardMethodId;
          entity.PrcAwardMethodDescription = this.PrcAwardMethodDescription;
          entity.PrcConfigurationId = this.PrcConfigurationId;
          entity.PrcConfigurationDescription = this.PrcConfigurationDescription;
          entity.PrcContractTypeId = this.PrcContractTypeId;
          entity.PrcContractTypeDescription = this.PrcContractTypeDescription;
          entity.PrcStrategyId = this.PrcStrategyId;
          entity.PrcStrategyDescription = this.PrcStrategyDescription;
          entity.RfqHeaderId = this.RfqHeaderId;
          entity.RfqHeaderCode = this.RfqHeaderCode;
          entity.RfqHeaderDescription = this.RfqHeaderDescription;
          entity.Remark = this.Remark;
          entity.UserDefined1 = this.UserDefined1;
          entity.UserDefined2 = this.UserDefined2;
          entity.UserDefined3 = this.UserDefined3;
          entity.UserDefined4 = this.UserDefined4;
          entity.UserDefined5 = this.UserDefined5;
          entity.PlannedStart = this.PlannedStart;
          entity.PlannedEnd = this.PlannedEnd;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;
          entity.BpdEvaluationSchemaId = this.BpdEvaluationSchemaId;
          entity.BpdEvaluationSchemaDescription = this.BpdEvaluationSchemaDescription;
          entity.PaymentTermAdId = this.PaymentTermAdId;
          entity.PaymentTermAdCode = this.PaymentTermAdCode;
          entity.PaymentTermAdDescription = this.PaymentTermAdDescription;
          entity.MdcBillingSchemaId = this.MdcBillingSchemaId;
          entity.MdcBillingSchemaDescription = this.MdcBillingSchemaDescription;
          entity.LanguageId = this.LanguageId;
          entity.DateDelivery = this.DateDelivery;
          entity.BasLanguageFk = this.BasLanguageFk;
          entity.BasLanguageDesc = this.BasLanguageDesc;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(RfqHeaderApiEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(RfqHeaderApiEntity entity);
    }

}
