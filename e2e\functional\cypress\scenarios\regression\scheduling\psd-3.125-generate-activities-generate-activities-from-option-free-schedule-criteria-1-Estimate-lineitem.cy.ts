import { _common, _estimatePage, _mainView, _modalView, _wipPage, _schedulePage, _projectPage, _bidPage, _saleContractPage, _sidebar, _validate } from 'cypress/pages';
import { app, tile, cnt, sidebar, commonLocators, btn } from 'cypress/locators';
import type { DataCells } from 'cypress/pages/interfaces.d.ts'


let SCHEDULE_PARAMETERS: DataCells;
let CONTAINER_COLUMNS_SCHEDULES;
let CONTAINER_COLUMNS_ACTIVITY_STRUCTURE;
let CONTAINERS_ACTIVITY_STRUCTURE;
let PROJECTS_PARAMETERS: DataCells;
let MODAL_PROJECTS;
let CONTAINER_COLUMNS_ESTIMATE;
let ESTIMATE_PARAMETERS;
let CONTAINERS_ESTIMATE;
let CONTAINER_COLUMNS_RESOURCE;
let CONTAINER_COLUMNS_LINE_ITEM;
let CONTAINERS_LINE_ITEM;
let LINE_ITEM_PARAMETERS: DataCells;
let RESOURCE_PARAMETERS: DataCells;
let CONTAINERS_RESOURCE;
let CONTAINERS_COST_CODE;
let CONTAINER_COLUMNS_COST_CODES;
let UPDATE_SCHEDULING_QUANTITIES_PARAMETERS: DataCells
let MODAL_UPDATE_SCHEDULING_QUANTITIES
let CONTAINER_COLUMNS_ACTIVITY_STRUCTURE_PERFORMANCE_TAB
let LINE_ITEM_PARAMETERS_2: DataCells;
let MODAL_MESSAGE
let GENERATE_ACTIVITY_PARAMETERS: DataCells

const ESTIMATE_CODE = '1' + _common.generateRandomString(3);
const ESTIMATE_DESCRIPTION = 'EST-DESC-' + _common.generateRandomString(3);
const ALLURE = Cypress.Allure.reporter.getInterface();
const SCHEDULES_CODE = "SCH-" + _common.generateRandomString(3);
const SCHEDULES_DESC = "SCH-DESC-" + _common.generateRandomString(3);
const ACTIVITY_STRUCTURE_DESC = "ACT-DESC-" + _common.generateRandomString(3);
const LI_DESC = "LI_DESC2" + _common.generateRandomString(3);
const PROJECT_NO = _common.generateRandomString(3)
const PROJECT_DESC = "PRDESC-" + _common.generateRandomString(3);
const LINE_ITEM_DESCRIPTION = "LINE_ITEM_DESC1-" + _common.generateRandomString(3);
const COST_CODE = '16' + _common.generateRandomString(3);
const COSTCODE_DESC = "COSTCODE_DESC-" + _common.generateRandomString(3);
const QTY_CODE = '1' + Cypress._.random(0, 9);

ALLURE.epic("SCHEDULING");
ALLURE.feature("Generate Activities");
ALLURE.story("SCH- 3.125 | Generate Activities from option Free Schedule -Criteria 1-Estimate Line item");

describe("SCH- 3.125 | Generate Activities from option Free Schedule -Criteria 1-Estimate Line item", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  before(function () {
    cy.fixture("scheduling/sch-3.125-generate-activities-generate-activities-from-option-free-schedule-criteria-1-Estimate-lineitem.json").then((data) => {
      this.data = data
      MODAL_PROJECTS = this.data.MODAL.PROJECTS
      CONTAINER_COLUMNS_SCHEDULES = this.data.CONTAINER_COLUMNS.SCHEDULES
      CONTAINER_COLUMNS_ACTIVITY_STRUCTURE = this.data.CONTAINER_COLUMNS.ACTIVITY_STRUCTURE
      CONTAINERS_ACTIVITY_STRUCTURE = this.data.CONTAINERS.ACTIVITY_STRUCTURE
      CONTAINER_COLUMNS_ESTIMATE = this.data.CONTAINER_COLUMNS.ESTIMATE
      CONTAINERS_ESTIMATE = this.data.CONTAINERS.ESTIMATE;
      CONTAINERS_LINE_ITEM = this.data.CONTAINERS.LINE_ITEM
      CONTAINER_COLUMNS_LINE_ITEM = this.data.CONTAINER_COLUMNS.LINE_ITEM
      CONTAINERS_RESOURCE = this.data.CONTAINERS.RESOURCE
      CONTAINER_COLUMNS_RESOURCE = this.data.CONTAINER_COLUMNS.RESOURCE
      CONTAINER_COLUMNS_COST_CODES = this.data.CONTAINER_COLUMNS.COST_CODES
      CONTAINERS_COST_CODE = this.data.CONTAINERS.COST_CODES
      MODAL_UPDATE_SCHEDULING_QUANTITIES = this.data.MODAL.UPDATE_SCHEDULING_QUANTITIES
      CONTAINER_COLUMNS_ACTIVITY_STRUCTURE_PERFORMANCE_TAB = this.data.CONTAINER_COLUMNS.ACTIVITY_STRUCTURE_PERFORMANCE_TAB
      MODAL_MESSAGE = this.data.MODAL.MESSAGE

      SCHEDULE_PARAMETERS = {
        [app.GridCells.CODE]: SCHEDULES_CODE,
        [app.GridCells.DESCRIPTION_INFO]: SCHEDULES_DESC,
      };
      PROJECTS_PARAMETERS = {
        [commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
        [commonLocators.CommonLabels.NAME]: PROJECT_DESC,
        [commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK
      };
      ESTIMATE_PARAMETERS = {
        [app.GridCells.CODE]: ESTIMATE_CODE,
        [app.GridCells.DESCRIPTION_INFO]: ESTIMATE_DESCRIPTION,
        [app.GridCells.RUBRIC_CATEGORY_FK]: CONTAINERS_ESTIMATE.RUBRIC_CATEGORY,
        [app.GridCells.EST_TYPE_FK]: CONTAINERS_ESTIMATE.ESTIMATE_TYPE
      };
      LINE_ITEM_PARAMETERS = {
        [app.GridCells.DESCRIPTION_INFO]: LINE_ITEM_DESCRIPTION,
        [app.GridCells.BAS_UOM_FK]: CONTAINERS_LINE_ITEM.UOM,
        [app.GridCells.QUANTITY_SMALL]: CONTAINERS_LINE_ITEM.QUANTITY
      };
      LINE_ITEM_PARAMETERS_2 = {
        [app.GridCells.DESCRIPTION_INFO]: LI_DESC
      };
      RESOURCE_PARAMETERS = {
        [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY,
        [app.GridCells.CODE]: COST_CODE,
        [app.GridCells.QUANTITY_SMALL]: CONTAINERS_RESOURCE.QUANTITY
      }
      UPDATE_SCHEDULING_QUANTITIES_PARAMETERS = {
        [commonLocators.CommonKeys.CHECKBOX]: MODAL_UPDATE_SCHEDULING_QUANTITIES
      };

    }).then(() => {
      cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
      _common.openDesktopTile(tile.DesktopTiles.PROJECT);
      _common.waitForLoaderToDisappear()
      _common.openTab(app.TabBar.PROJECT).then(() => {
        _common.setDefaultView(app.TabBar.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
      });
      _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
      _common.create_newRecord(cnt.uuid.PROJECTS);
      _projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS);
      _common.waitForLoaderToDisappear()
      cy.SAVE();
      _common.waitForLoaderToDisappear()
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem();
    })
  })

  it("TC - Create Cost Code and assign characterisitc", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.COST_CODES);
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.COST_CODES).then(() => {
      _common.select_tabFromFooter(cnt.uuid.COST_CODES, app.FooterTab.COSTCODES, 0);
      _common.setup_gridLayout(cnt.uuid.COST_CODES, CONTAINER_COLUMNS_COST_CODES)
      _common.set_columnAtTop([CONTAINER_COLUMNS_COST_CODES.israte, CONTAINER_COLUMNS_COST_CODES.islabour, CONTAINER_COLUMNS_COST_CODES.uomfk, CONTAINER_COLUMNS_COST_CODES.code], cnt.uuid.COST_CODES)
    });
    _common.clear_subContainerFilter(cnt.uuid.COST_CODES)
    _common.maximizeContainer(cnt.uuid.COST_CODES)
    _common.waitForLoaderToDisappear()
    _common.create_newRecord(cnt.uuid.COST_CODES);
    _common.enterRecord_inNewRow(cnt.uuid.COST_CODES, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, COST_CODE)
    _common.enterRecord_inNewRow(cnt.uuid.COST_CODES, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, COSTCODE_DESC)
    _common.enterRecord_inNewRow(cnt.uuid.COST_CODES, app.GridCells.RATE, app.InputFields.INPUT_GROUP_CONTENT, QTY_CODE)
    _common.set_cellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_RATE, commonLocators.CommonKeys.CHECK)
    _common.set_cellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_LABOUR_SMALL, commonLocators.CommonKeys.CHECK)
    _common.edit_dropdownCellWithCaret(cnt.uuid.COST_CODES, app.GridCells.CURRENCY_FK, commonLocators.CommonKeys.GRID, CONTAINERS_COST_CODE.CURRENCY)
    _common.waitForLoaderToDisappear()
    _common.edit_dropdownCellWithInput(cnt.uuid.COST_CODES, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_COST_CODE.UOM)
    _common.select_activeRowInContainer(cnt.uuid.COST_CODES)
    _common.waitForLoaderToDisappear()
    _common.waitForLoaderToDisappear()
    _common.minimizeContainer(cnt.uuid.COST_CODES)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.waitForLoaderToDisappear()
    _common.waitForLoaderToDisappear()
    _common.waitForLoaderToDisappear()
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
  })

  it('TC - Create new estimate record', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.PROJECT).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem();
    });
    _common.select_rowHasValue(cnt.uuid.PROJECTS, PROJECT_NO)
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.setDefaultView(app.TabBar.ESTIMATE)
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE, CONTAINER_COLUMNS_ESTIMATE);
    });
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
    _common.create_newRecord(cnt.uuid.ESTIMATE);
    _estimatePage.enterRecord_toCreateEstimate(cnt.uuid.ESTIMATE, ESTIMATE_PARAMETERS);
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
    _common.waitForLoaderToDisappear()
  });

  it('TC - Create new line item ', function () {
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 3);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS, CONTAINER_COLUMNS_LINE_ITEM)
      _common.set_columnAtTop([CONTAINER_COLUMNS_LINE_ITEM.hourstotal, CONTAINER_COLUMNS_LINE_ITEM.costunit, CONTAINER_COLUMNS_LINE_ITEM.estqtyrelactfk, CONTAINER_COLUMNS_LINE_ITEM.psdactivityfk], cnt.uuid.ESTIMATE_LINEITEMS)
    });
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS)
    _common.maximizeContainer(cnt.uuid.ESTIMATE_LINEITEMS)
    _common.create_newRecord(cnt.uuid.ESTIMATE_LINEITEMS)
    _estimatePage.enterRecord_toCreateLineItem(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_PARAMETERS);
    cy.SAVE()
    _common.edit_dropdownCellWithCaret(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.EST_QTY_REL_ACT_FK, commonLocators.CommonKeys.SPAN, commonLocators.CommonKeys.FROM_STRUCTURE)
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.minimizeContainer(cnt.uuid.ESTIMATE_LINEITEMS)

  });

  it('TC - Assign resource to the line item', function () {
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
      _common.setup_gridLayout(cnt.uuid.RESOURCES, CONTAINER_COLUMNS_RESOURCE)
    });
    _common.maximizeContainer(cnt.uuid.RESOURCES)
    _common.clear_subContainerFilter(cnt.uuid.RESOURCES);
    _common.create_newRecord(cnt.uuid.RESOURCES);
    _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS);
    _common.minimizeContainer(cnt.uuid.RESOURCES)
    _common.waitForLoaderToDisappear()
    cy.SAVE();
    _common.waitForLoaderToDisappear()
    cy.wait(1000) //required wait to load page
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
  });
  it("TC - Create new schedule header", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem();
    _common.openTab(app.TabBar.SCHEDULING).then(() => {
      _common.select_tabFromFooter(cnt.uuid.SCHEDULES, app.FooterTab.SCHEDULES, 2);
      _common.setup_gridLayout(cnt.uuid.SCHEDULES, CONTAINER_COLUMNS_SCHEDULES)
    });
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
    _common.clear_searchInSidebar()
    _common.clear_subContainerFilter(cnt.uuid.SCHEDULES);
    _common.clickOn_toolbarButton(cnt.uuid.SCHEDULES, btn.ToolBar.ICO_REC_NEW)
    _schedulePage.enterRecord_toCreateSchedules(cnt.uuid.SCHEDULES, SCHEDULE_PARAMETERS);
    _common.waitForLoaderToDisappear()
    cy.SAVE();
    _common.waitForLoaderToDisappear()
    _common.clickOn_toolbarButton(cnt.uuid.SCHEDULES, btn.ToolBar.ICO_GO_TO)
    _common.waitForLoaderToDisappear()
  });

  it("TC - Generate Activity by  using Wizard", function () {

    GENERATE_ACTIVITY_PARAMETERS = {
      [commonLocators.CommonElements.NAV_TABS]: "Generate Activities",
      [commonLocators.CommonKeys.LABEL]: "Generate Activities - free Schedule (no link with Estimate)",
      [commonLocators.CommonKeys.RADIO_INDEX]: "0",
      [commonLocators.CommonKeys.CRITERIA_LABEL]: { "Criteria 1": "Line Item: " + `${ESTIMATE_CODE}` },
      [commonLocators.CommonLabels.CREATE_RELATIONS]: commonLocators.CommonKeys.CHECK,
      [commonLocators.CommonLabels.KIND_CAPS]: "Finish - Start"
    };
    _common.openTab(app.TabBar.PLANNING).then(() => {
      _common.waitForLoaderToDisappear()
      _common.setDefaultView(app.TabBar.PLANNING, commonLocators.CommonKeys.DEFAULT)
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.ACTIVITY_STRUCTURE, app.FooterTab.ACTIVITY_STRUCTURE, 0);
      _common.setup_gridLayout(cnt.uuid.ACTIVITY_STRUCTURE, CONTAINER_COLUMNS_ACTIVITY_STRUCTURE)

    });
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.GENERATE_ACTIVITIES)
    _common.waitForLoaderToDisappear()
    _schedulePage.generate_activityStructureRecord_byWizard(GENERATE_ACTIVITY_PARAMETERS)
    _common.clickOn_modalFooterButton(btn.ButtonText.OK)
    cy.wait(1000)
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()

  });
  it("TC - Asert values in Activity container in the scheduling module", function () {
    _common.openTab(app.TabBar.PLANNING).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ACTIVITY_STRUCTURE, app.FooterTab.ACTIVITY_STRUCTURE)
      _common.setup_gridLayout(cnt.uuid.ACTIVITY_STRUCTURE, CONTAINER_COLUMNS_ACTIVITY_STRUCTURE)
    });
    _common.clear_subContainerFilter(cnt.uuid.ACTIVITY_STRUCTURE)
    _common.select_allContainerData(cnt.uuid.ACTIVITY_STRUCTURE);
    _common.clickOn_expandCollapseButton(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_SELECTED);
    cy.wait(1000);
    _common.select_rowHasValue(cnt.uuid.ACTIVITY_STRUCTURE, LINE_ITEM_DESCRIPTION)
    _common.assert_cellData_insideActiveRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.DESCRIPTION, LINE_ITEM_DESCRIPTION)
    _common.assert_cellData_insideActiveRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.QUANTITY_SMALL, CONTAINERS_LINE_ITEM.QUANTITY)
    _common.assert_cellData_insideActiveRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.PLANNED_DURATION, "7")
    _common.assert_cellData_insideActiveRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.QUANTITY_UOM_FK, CONTAINERS_LINE_ITEM.UOM)
  });



  after(() => {
    cy.LOGOUT();
  })
})