import apiConstantData from "cypress/constantData/apiConstantData";
import { tile, app, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _estimatePage, _validate, _boqPage, _logesticPage, _controllingUnit, _projectPage, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const PLANT_TYPE = _common.generateRandomString(4)
const PLANT_LIST1 = "RENTAL-PPL-" + Cypress._.random(0, 999);
const RENTAL = _common.generateRandomString(4)
const CODE_PERFORMANCE = _common.generateRandomString(4)
const DESC_WORK = _common.generateRandomString(4)
const DESC_PERFORMANCE = _common.generateRandomString(4)
const PLANT_GROUP = _common.generateRandomString(4)
const PLANT_CODE = _common.generateRandomString(4)
const PLANT_GROUP_DESC = _common.generateRandomString(4)
const SUB_PLANT_GROUP = _common.generateRandomString(4)
const SUB_PLANT_GROUP_DESC = _common.generateRandomString(4)
const PLANT_DESCRIPTION = _common.generateRandomString(4)
const CONDITION_CODE = _common.generateRandomString(4)
const CONDITION_DESC = _common.generateRandomString(4)
const DISPATCH_DESC = _common.generateRandomString(8)

let CONTAINER_PROJECT,
	PROJECT_PARAMETERS1,
	PROJECT_PARAMETERS2

let CONTAINER_COLUMNS_DATA_TYPES,
	CONTAINER_DATA_RECORD,
	DATA_RECORD_PARAMETER1,
	DATA_RECORD_PARAMETER2;

let CONTAINER_OPERATION_TYPES,
	CONTAINER_COLUMNS_PLANT_TYPES,
	CONTAINER_COLUMNS_OPERATION_TYPES;

let OPERATION_TYPE_PARAMETER_WORK,
	OPERATION_TYPE_PARAMETER_PERFORMANCE;

let CONTAINERS_CONTROLLING_UNITS,
	CONTAINER_COLUMNS_CONTROLLING_UNITS,
	CONTROLLING_UNIT_MAIN_PARAMETERS,
	CONTROLLING_UNIT_SUB_PARAMETERS

let CONTAINER_COLUMNS_CONTROLLING_UNIT2;
let CONTROLLING_UNIT_PARAMETERS2: DataCells;

let CONTAINERS_PLANT_GROUP,
	CONTAINER_COLUMNS_PLANT_GROUP

let CONTAINERS_PLANT,
	CONTAINER_COLUMNS_PLANT,
	PLANT_PARAMETERS,
	CONTAINER_COLUMNS_PLANT_CONTROLLING;

let CONTAINERS_PLANT_PRICE_LISTS
let ALLOCATION_FOR_PLANTS_PARAMETER

let MODAL_PLANT_ALLOCATION

let CONTAINER_COLUMNS_JOBS;
let CONTAINER_JOBS;
let CONTAINER_COLUMNS_CONDITIONS, CONTAINER_COLUMNS_PRICE_LIST
let CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS
let CONTAINER_COLUMNS_DISPATCHING_HEADER,
	CONTAINERS_DISPATCHING_HEADER,
	CONTAINER_COLUMNS_SETTLEMENT_ITEMS,
	CONTAINER_COLUMNS_CLERK


describe("LRM- 1.82 | Verify change of price portion based on effective day of the dispatch or valid From valid to of price list", () => {
	beforeEach(() => {
		cy.clearCookies();
		cy.clearLocalStorage();
		cy.WaitUntilLoaderComplete_Trial();
		cy.waitUntilDOMLoaded();
	});
	afterEach(() => {
		cy.clearCookies();
		cy.clearLocalStorage();
		cy.WaitUntilLoaderComplete_Trial();
		cy.waitUntilDOMLoaded();
	});

	before(function () {
		cy.fixture("LRM/lgm-1.82-verify-change-of-price-portion-based-on-effective-day-of-the-dispatch-or-valid-from-valid-to-of-pricelist.json").then((data) => {
			this.data = data;
			CONTAINER_DATA_RECORD = this.data.CONTAINERS.DATA_RECORD;
			CONTAINER_COLUMNS_DATA_TYPES = this.data.CONTAINER_COLUMNS.DATA_TYPES;
			CONTAINER_OPERATION_TYPES = this.data.CONTAINERS.OPERATION_TYPES;
			CONTAINER_COLUMNS_PLANT_TYPES = this.data.CONTAINER_COLUMNS.PLANT_TYPES;
			CONTAINER_COLUMNS_OPERATION_TYPES = this.data.CONTAINER_COLUMNS.OPERATION_TYPES;
			CONTAINER_COLUMNS_PRICE_LIST = this.data.CONTAINER_COLUMNS.PRICE_LIST;
			CONTAINER_PROJECT = this.data.CONTAINERS.PROJECT

			CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNITS
			CONTAINER_COLUMNS_CONTROLLING_UNITS = this.data.CONTAINER_COLUMNS.CONTROLLING_UNITS;
			CONTAINER_COLUMNS_CONTROLLING_UNIT2 = this.data.CONTAINER_COLUMNS.CONTAINER_COLUMNS_CONTROLLING_UNIT2;

			OPERATION_TYPE_PARAMETER_PERFORMANCE = {
				[app.GridCells.IS_LIVE]: CONTAINER_OPERATION_TYPES.CHECK,
				[app.GridCells.CODE]: CODE_PERFORMANCE,
				[app.GridCells.DESCRIPTION_INFO]: DESC_PERFORMANCE,
				[app.GridCells.UOM_FK]: CONTAINER_OPERATION_TYPES.HOUR,
			}

			CONTAINERS_PLANT_GROUP = this.data.CONTAINERS.PLANT_GROUP;
			CONTAINER_COLUMNS_PLANT_GROUP = this.data.CONTAINER_COLUMNS.PLANT_GROUP;

			CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;
			CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
			PLANT_PARAMETERS = {
				[app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION,
				[app.GridCells.PLANT_GROUP_FK]: SUB_PLANT_GROUP,
				[app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
				[app.GridCells.PLANT_TYPE_FK]: RENTAL,
				[app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
				[app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
			}
			CONTAINER_COLUMNS_PLANT_CONTROLLING = this.data.CONTAINER_COLUMNS.PLANT_CONTROLLING;
			CONTAINERS_PLANT_PRICE_LISTS = this.data.CONTAINERS.PLANT_PRICE_LISTS;

			MODAL_PLANT_ALLOCATION = this.data.MODAL.PLANT_ALLOCATION;
			ALLOCATION_FOR_PLANTS_PARAMETER = {
				[commonLocators.CommonLabels.JOB]: Cypress.env('API_PROJECT_NUMBER_1'),
				[commonLocators.CommonLabels.ALLOCATED_FROM]: MODAL_PLANT_ALLOCATION.ALLOCATED_FROM,
				[app.GridCells.WORK_OPERATION_TYPE_FK]: DESC_WORK
			}

			CONTAINER_COLUMNS_JOBS = this.data.CONTAINER_COLUMNS.JOBS
			CONTAINER_JOBS = this.data.CONTAINERS.JOBS
			CONTAINER_COLUMNS_CONDITIONS = this.data.CONTAINER_COLUMNS.CONDITIONS
			CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS = this.data.CONTAINERS.WORK_OPERATION_TYPE_CONDITIONS
			CONTAINER_COLUMNS_DISPATCHING_HEADER = this.data.CONTAINER_COLUMNS.DISPATCHING_HEADER
			CONTAINERS_DISPATCHING_HEADER = this.data.CONTAINERS.DISPATCHING_HEADER
			CONTAINER_COLUMNS_SETTLEMENT_ITEMS = this.data.CONTAINER_COLUMNS.SETTLEMENT_ITEMS
			CONTAINER_COLUMNS_CLERK = this.data.CONTAINER_COLUMNS.CLERK


			cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));

			_common.openDesktopTile(tile.DesktopTiles.PROJECT);
			_common.openTab(app.TabBar.PROJECT).then(() => {
				_common.setDefaultView(app.TabBar.PROJECT)
				_common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
			});
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
			_common.waitForLoaderToDisappear()
			_common.clear_subContainerFilter(cnt.uuid.PROJECTS)

			_commonAPI.getAccessToken().then((result) => {
				cy.log(`Token Retrieved: ${result.token}`);
			});
		});

	})

	after(() => {
		cy.LOGOUT();
	});

	it("TC - API call to assign logged-in user a clerk", function () {
		_commonAPI.getULoggedInUserId(Cypress.env("USER_NAME"))
			.then(() => {
				_commonAPI.assignUserToClerk(Cypress.env("API_LOGGED_IN_USER_ID"), Cypress.env("USER_NAME"), apiConstantData.CONSTANT.SMIJ)
			})
	});

	it('TC - API: Create project', function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)

		_commonAPI.createProject().then(() => {
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
			_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
		});
	});

	it("TC - Create Controlling Units", function () {
		CONTROLLING_UNIT_SUB_PARAMETERS = {
			[app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
			[app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
			[app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true"]
		}


		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
		_commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_SUB_PARAMETERS)

	})

	it("TC - Add plant list type record", function () {
		const DISPATCH_HEADER_PARAMETERS = {
			[app.GridCells.IS_READY_FOR_SETTLEMENT]: "true",
		}
		const PLANT_LIST_TYPE_PARAMETERS = {
			[app.GridCells.DESCRIPTION_INFO]: PLANT_TYPE
		}

		_commonAPI.updateDispatchHeaderStatus_underCustomizing(DISPATCH_HEADER_PARAMETERS, commonLocators.CommonKeys.DELIVERED, commonLocators.CommonKeys.MATERIAL)
		_commonAPI.createPlantListType(PLANT_LIST_TYPE_PARAMETERS)
	})

	it("TC - Create new plant price list record", function () {

		DATA_RECORD_PARAMETER1 = {
			[app.GridCells.CONTEXT_FK]: apiConstantData.ID.PLANT_CONTEXT_RIB_DEMO,
			[app.GridCells.PRICE_LIST_TYPE_FK]: Cypress.env('API_PLANT_LIST_TYPE_ID_1'),
			[app.GridCells.CURRENCY]: apiConstantData.ID.CURRENCY_EUR,
			[app.GridCells.UOM_FK]: apiConstantData.ID.UOM_DAY,
			[app.GridCells.DESCRIPTION_INFO]: PLANT_LIST1,
			[app.GridCells.CALCULATION_TYPE_FK]: apiConstantData.ID.CALCULATION_TYPE_AVERAGE_CATALOG_VALUE,
			[app.GridCells.PERCENT]: CONTAINER_DATA_RECORD.PERCENT,
			[app.GridCells.IS_MANUAL_EDIT_PLANT_MASTER]: "true"
		}
		_commonAPI.createPlantPriceList(DATA_RECORD_PARAMETER1)

		//_common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.REFERENCE_YEAR, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_DATA_RECORD.REFERENCE_YEAR)

	})

	it("TC - Create plant type", function () {

		let PLANT_TYPE_RENTAL_PARAMETER: DataCells = {
			[app.GridCells.IS_CLUSTER]: "true",
			[app.GridCells.DESCRIPTION_INFO]: RENTAL
		}
		_commonAPI.createPlantType(PLANT_TYPE_RENTAL_PARAMETER)
	})

	it("TC - Create work operation types and assign plant type to work operation ", function () {

		OPERATION_TYPE_PARAMETER_WORK = {
			[app.GridCells.IS_HIRE]: "true",
			[app.GridCells.UOM]: apiConstantData.ID.UOM_DAY,
			[app.GridCells.IS_LIVE]: "true",
			[app.GridCells.DESCRIPTION_INFO]: DESC_WORK

		}
		_commonAPI.createWorkOperationType(OPERATION_TYPE_PARAMETER_WORK).then(() => {
			let PLANT_TYPE_RENTAL: DataCells = {
				[app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_ID_1`),
				[app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_ID_1`)
			}
			_commonAPI.createWorkOperationPlantType(PLANT_TYPE_RENTAL)

		})


	})

	it("TC - Create new plant group and sub group record", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_GROUP)
		_common.openTab(app.TabBar.PLANT_GROUP_AND_LOCATIONS).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PLANT_GROUP, app.FooterTab.PLANT_GROUPS, 0)
			_common.setup_gridLayout(cnt.uuid.PLANT_GROUP, CONTAINER_COLUMNS_PLANT_GROUP)
			_common.clear_subContainerFilter(cnt.uuid.PLANT_GROUP)
		})
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.select_allContainerData(cnt.uuid.PLANT_GROUP)
		_common.clickOn_expandCollapseButton(cnt.uuid.PLANT_GROUP, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.COLLAPSE_ALL)
		_common.create_newRecord(cnt.uuid.PLANT_GROUP)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_GROUP)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, PLANT_GROUP_DESC)
		_common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PLANT_GROUP, PLANT_GROUP_DESC)
		_common.create_newSubRecord(cnt.uuid.PLANT_GROUP)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, SUB_PLANT_GROUP)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, SUB_PLANT_GROUP_DESC)
		_common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	})

	it("TC - Create new plant record in plant master module", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
		        cy.wait(4000)

		_common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
			_common.setup_gridLayout(cnt.uuid.PLANT, CONTAINER_COLUMNS_PLANT)
			_common.clear_subContainerFilter(cnt.uuid.PLANT)
		});
		_common.waitForLoaderToDisappear()
		_common.maximizeContainer(cnt.uuid.PLANT)
		_common.create_newRecord(cnt.uuid.PLANT)
		_common.waitForLoaderToDisappear()
		_logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS)
		_common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE);
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.minimizeContainer(cnt.uuid.PLANT)
		_common.waitForLoaderToDisappear()
	})

	it('TC - Assign controlling unit to created plant record in plant master module', function () {
		_common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
			_common.clear_subContainerFilter(cnt.uuid.PLANT)
			cy.REFRESH_CONTAINER()
		});
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PLANT_DESCRIPTION)
		_common.select_activeRowInContainer(cnt.uuid.PLANT)
		_common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
			_common.waitForLoaderToDisappear()
			_common.select_tabFromFooter(cnt.uuid.PLANT_CONTROLLING, app.FooterTab.CONTROLLING_UNIT)
			_common.setup_gridLayout(cnt.uuid.PLANT_CONTROLLING, CONTAINER_COLUMNS_PLANT_CONTROLLING)
			_common.clear_subContainerFilter(cnt.uuid.PLANT_CONTROLLING)
		});
		_common.create_newRecord(cnt.uuid.PLANT_CONTROLLING)
		_common.waitForLoaderToDisappear()
		cy.wait(1000)//required wait to appear dropdown
		_common.waitForLoaderToDisappear()
		_common.edit_dropdownCellWithInput(cnt.uuid.PLANT_CONTROLLING, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	})

	it('TC - Assign price list to plant record', function () {
		_common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PLANT_PRICE_LISTS, app.FooterTab.PRICE_LISTS_SMALL, 1)
			_common.setup_gridLayout(cnt.uuid.PLANT_PRICE_LISTS, CONTAINER_COLUMNS_PRICE_LIST)

			_common.clear_subContainerFilter(cnt.uuid.PLANT_PRICE_LISTS)
		});
		_common.maximizeContainer(cnt.uuid.PLANT_PRICE_LISTS)
		_common.create_newRecord(cnt.uuid.PLANT_PRICE_LISTS)
		_common.waitForLoaderToDisappear()
		cy.wait(1000)//required wait to load data in container
		_common.edit_dropdownCellWithCaret_scroll(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_LIST1)
		_common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
		_common.waitForLoaderToDisappear()
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_3, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_4, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_5, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_6, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT, "01/01/"+_common.getDate(commonLocators.CommonKeys.YEAR))
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.VALID_TO, app.InputFields.INPUT_GROUP_CONTENT, "30/06/"+_common.getDate(commonLocators.CommonKeys.YEAR))
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.saveCellDataToEnv(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_SUM, "PRICE_PORTION_SUM_1")
		_common.create_newRecord(cnt.uuid.PLANT_PRICE_LISTS)
		_common.waitForLoaderToDisappear()
		cy.wait(1000)//required wait to load data in container
		_common.edit_dropdownCellWithCaret_scroll(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_LIST1)
		_common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
		_common.waitForLoaderToDisappear()
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_3, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_4, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_5, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_6, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT, "01/07/"+_common.getDate(commonLocators.CommonKeys.YEAR))
		_common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.VALID_TO, app.InputFields.INPUT_GROUP_CONTENT, "31/07/"+_common.getDate(commonLocators.CommonKeys.YEAR))
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.saveCellDataToEnv(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_SUM, "PRICE_PORTION_SUM_2")
		_common.minimizeContainer(cnt.uuid.PLANT_PRICE_LISTS)
	})

	it('TC - Create plant location from wizard', function () {
		_common.openTab(app.TabBar.LOCATIONS).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
			_common.clear_subContainerFilter(cnt.uuid.PLANT)
			_common.search_inSubContainer(cnt.uuid.PLANT, PLANT_DESCRIPTION)
		});
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_INITIAL_ALLOCATION_FOR_PLANTS)
		_common.waitForLoaderToDisappear()
		_logesticPage.create_initialAllocationForPlants_fromWizard(ALLOCATION_FOR_PLANTS_PARAMETER, RENTAL)
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()

		_common.openTab(app.TabBar.LOCATIONS).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
		});
		_common.select_allContainerData(cnt.uuid.PLANT_ALLOCATIONS)

		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()

		_common.openTab(app.TabBar.LOCATIONS).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
		});
		_common.select_allContainerData(cnt.uuid.PLANT_LOCATION)

		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.LOCATIONS).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
			_common.clear_subContainerFilter(cnt.uuid.PLANT)
		});
		_common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE)
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
		_common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
	})

	it('TC - API: Create project - B', function () {

		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
		_common.waitForLoaderToDisappear()

		_common.openTab(app.TabBar.PROJECT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
		});
		_commonAPI.createProject().then(() => {
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
			_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2')).pinnedItem();
		});
	})

	it("TC - Add Controlling Unit", function () {

		CONTROLLING_UNIT_PARAMETERS2 = {
			[app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
			[app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
			[app.GridCells.IS_BILLING_ELEMENT]: ["true", "true"],
			[app.GridCells.ISA_ACCOUNTING_ELEMENT]: ["true", "true"],
			[app.GridCells.IS_PLANNING_ELEMENT]: ["true", "true"],
			[app.GridCells.IS_TIMEKEEPING_ELEMENT]: ["true", "true"]
		}
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
		_commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_2'), 2, CONTROLLING_UNIT_PARAMETERS2)
	})

	it("TC - Create job record in logistic job module", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
		_common.waitForLoaderToDisappear()
		 _common.waitForLoaderToDisappear()
        cy.wait(2000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        cy.wait(1000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
			_common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS, 0);
			_common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_JOBS)
			_common.set_columnAtTop([CONTAINER_COLUMNS_JOBS.controllingunitfk], cnt.uuid.JOBS)

		});
		_common.clear_subContainerFilter(cnt.uuid.JOBS)
		_common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
		_common.maximizeContainer(cnt.uuid.JOBS)
		_common.waitForLoaderToDisappear()
		_common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
		cy.wait(1000)//required wait to enable data input fields
		_common.clickOn_activeRowCell(cnt.uuid.JOBS, app.GridCells.CONTROLLING_UNIT_FK)
		cy.wait(1000)//required wait to save data
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.CONTROLLING_UNIT_FK, Cypress.env(`API_CNT_CODE_1`), commonLocators.CommonKeys.GRID)
		_common.clickOn_modalFooterButton(btn.ButtonText.OK)
		_common.select_activeRowInContainer(cnt.uuid.JOBS)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.minimizeContainer(cnt.uuid.JOBS)
		_common.waitForLoaderToDisappear()

	})

	it("TC - Create job record in logistic price condition module", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_PRICE_CONDITION)
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.LOGISTIC_PRICE_CONDITION).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONDITIONS, app.FooterTab.CONDITIONS);
			_common.setup_gridLayout(cnt.uuid.CONDITIONS, CONTAINER_COLUMNS_CONDITIONS)
			_common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
		});
		_common.maximizeContainer(cnt.uuid.CONDITIONS)
		_common.create_newRecord(cnt.uuid.CONDITIONS)
		_common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, CONDITION_CODE)
		_common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, CONDITION_DESC)
		_common.set_cellCheckboxValue(cnt.uuid.CONDITIONS, app.GridCells.IS_HANDLING_CHARGE, commonLocators.CommonKeys.CHECK)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.minimizeContainer(cnt.uuid.CONDITIONS)
		_common.select_rowHasValue(cnt.uuid.CONDITIONS, CONDITION_CODE)
		_common.openTab(app.TabBar.PLANT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.FooterTab.PLANT_CATALOG_PRICELISTS);
		});
		_common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
		_common.edit_dropdownCellWithCaret_scroll(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_LIST1)

		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PLANT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_ITEM, app.FooterTab.WORK_OPERATION_TYPE_CONDITIONS);
			_common.create_newRecord(cnt.uuid.PRICE_CONDITION_ITEM)
			_common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, DESC_WORK)
		});
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.wait(1000)//required wait to enable data input fields
		_common.edit_caretDropdown_fromModal_byClass(app.ModalInputFields.PRICING_GROUP_FK)
		_common.select_ItemFromPopUpList(commonLocators.CommonKeys.LIST, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PLANT_PRICING_GROUP)
		cy.wait(1000)//required wait to enable button
		_common.clickOn_modalFooterButton(btn.ButtonText.OK)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	})

	it("TC - Assign price condition in logistic job module", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
		_common.waitForLoaderToDisappear()
		cy.wait(1000)//required wait to open sidebar
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'));
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
			_common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS);
			_common.search_inSubContainer(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
			_common.waitForLoaderToDisappear()
			_common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
		});
		_common.waitForLoaderToDisappear()
		_common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PRICE_CONDITION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONDITION_CODE)
		_common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_JOBS.BUSINESS_PARTNER)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	})

	it("TC - Create record in dispatching notes and verify change of price portion based on effective date", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()

		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();		
		_common.clear_searchInSidebar()
		_common.waitForLoaderToDisappear()

		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();		
		_common.clear_searchInSidebar()
		_common.waitForLoaderToDisappear()

		_common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
			_common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
			_common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
			_common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
			_common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
			_common.waitForLoaderToDisappear()
		});
		_common.waitForLoaderToDisappear()
		_common.maximizeContainer(cnt.uuid.DISPATCHING_HEADER)
		_common.create_newRecord(cnt.uuid.DISPATCHING_HEADER)
		cy.wait(1000)

		_common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, "05/02/"+_common.getDate(commonLocators.CommonKeys.YEAR))
		_common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)

		_common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB1_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
		_common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)

		_common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB2_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_2'))
		_common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
		_common.waitForLoaderToDisappear()
		_common.enterRecord_inNewRow(cnt.uuid.DISPATCHING_HEADER, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, DISPATCH_DESC)
		_common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)

		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.wait(1000)//required wait to enable data input fields
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		 _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)

		_common.saveCellDataToEnv(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE, "DISPATCH_CODE")
		_common.minimizeContainer(cnt.uuid.DISPATCHING_HEADER)
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue, (cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
	

		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, app.FooterTab.PERFORMING_JOB_PLANT_LOCATIONS);
		});

		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
			_common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
		});
				_common.waitForLoaderToDisappear()

		_common.create_newRecord(cnt.uuid.DISPATCHING_RECORD)
		_common.waitForLoaderToDisappear()
		_common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.RECORD_TYPE_FK, commonLocators.CommonKeys.GRID, CONTAINERS_DISPATCHING_HEADER.RECORD_TYPE)
		_common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)
		_common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_RECORD, app.GridCells.ARTICLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE)
		_common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)

		_common.waitForLoaderToDisappear()

		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.WORK_OPERATION_TYPE_FK, DESC_WORK, commonLocators.CommonKeys.GRID)
		cy.wait(1000)//required wait to enable button
		_common.clickOn_modalFooterButton(btn.ButtonText.OK)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.RECALCULATE_DISPATCH_NOTE)
		_common.waitForLoaderToDisappear()
		_common.clickOn_modalFooterButton(btn.ButtonText.OK)
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.maximizeContainer(cnt.uuid.DISPATCHING_RECORD)
		_common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_SMALL, Cypress.env("PRICE_PORTION_SUM_1"))
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_01, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_02, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_03, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_04, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_05, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_06, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
		_common.minimizeContainer(cnt.uuid.DISPATCHING_RECORD)
		_common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
			_common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);

		});
		_common.waitForLoaderToDisappear()
		_common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, "15/07/"+_common.getDate(commonLocators.CommonKeys.YEAR))
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.RECALCULATE_DISPATCH_NOTE)
		_common.waitForLoaderToDisappear()
		_common.clickOn_modalFooterButton(btn.ButtonText.OK)
		cy.REFRESH_CONTAINER()
		_common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
		_common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
			_common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
		});
		_common.maximizeContainer(cnt.uuid.DISPATCHING_RECORD)
		_common.waitForLoaderToDisappear()
		_common.select_rowInSubContainer(cnt.uuid.DISPATCHING_RECORD)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_SMALL, Cypress.env("PRICE_PORTION_SUM_2"))
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_01, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_02, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_03, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_04, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_05, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_PORTION_06, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
		_common.minimizeContainer(cnt.uuid.DISPATCHING_RECORD)
	})
});


