/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { createLookup, FieldType, ILayoutConfiguration, ILookupContext } from '@libs/ui/common';
import { prefixAllTranslationKeys, ServiceLocator } from '@libs/platform/common';
import { BasicsCharacteristicHeader } from '@libs/basics/shared';
import { ICharacteristicChainEntity } from '@libs/basics/interfaces';
import { BasicsCharacteristicCodeByGroupLookupService } from '../lookup/basics-characteristic-code-by-group-lookup.service';
import { BasicsCharacteristicGroupDataService } from '../basics-characteristic-group-data.service';
import { BasicsCharacteristicCharacteristicDataService } from '../basics-characteristic-characteristic-data.service';

/**
 * The characteristic discrete value layout service
 */
@Injectable({
	providedIn: 'root',
})
export class BasicsCharacteristicChainCharacteristicLayoutService {
	public generateLayout(): ILayoutConfiguration<ICharacteristicChainEntity> {
		return {
			suppressHistoryGroup: true,
			groups: [
				{
					gid: 'basicData',
					title: {
						key: 'cloud.common.entityProperties',
						text: 'Basic Data',
					},
					attributes: ['ChainedCharacteristicFk'],
				},
			],
			labels: {
				...prefixAllTranslationKeys('basics.characteristic.', {
					ChainedCharacteristicFk: {
						key: 'entityChainedCharacteristicFk',
						text: 'Chained Characteristics',
					},
				}),
			},
			overloads: {
				Id: {
					readonly: true,
				},
				ChainedCharacteristicFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsCharacteristicCodeByGroupLookupService,
						showDescription: true,
						descriptionMember: 'DescriptionInfo.Description',
						serverSideFilter: {
							key: '',
							execute(context: ILookupContext<BasicsCharacteristicHeader, ICharacteristicChainEntity>) {
								const groupService = ServiceLocator.injector.get(BasicsCharacteristicGroupDataService);
								const characteristicService = ServiceLocator.injector.get(BasicsCharacteristicCharacteristicDataService);
								return {
									groupId: groupService.getSelectedEntity()?.Id,
									characteristicId2Remove: characteristicService.getSelectedEntity()?.Id,
								};
							},
						},
					}),
				},
			},
		};
	}
}
