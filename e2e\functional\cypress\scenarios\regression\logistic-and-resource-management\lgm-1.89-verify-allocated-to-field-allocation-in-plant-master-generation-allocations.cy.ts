import { tile, app, cnt, sidebar, commonLocators, btn, apiParameters } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _commonAPI,_estimatePage, _validate, _mainView, _boqPage, _modalView, _logesticPage, _controllingUnit, _projectPage } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import apiConstantData from "cypress/constantData/apiConstantData";
import cypress from "cypress";

const PLANT_TYPE = "PT-HS-" + _common.generateRandomString(4),
      PLANT_LIST1 = "RENTAL-HS-" + _common.generateRandomString(4),
PLANT_GROUP = "PG" + _common.generateRandomString(4),
PLANT_CODE = "PC" + _common.generateRandomString(4),
PLANT_GROUP_DESC = "PG_DESC" + _common.generateRandomString(4),
SUB_PLANT_GROUP = "SUB_PG" +_common.generateRandomString(4),
SUB_PLANT_GROUP_DESC = "SUB_PG_DESC" + _common.generateRandomString(4),
PLANT_DESCRIPTION = "HS-ROLLER" + _common.generateRandomString(4),
CONDITION_CODE = "CC-" + _common.generateRandomString(4),
CONDITION_DESC = "CDESC" + _common.generateRandomString(4),
DN = "DN-" + _common.generateRandomString(4),
COPY_DN = "C_DN" + _common.generateRandomString(4)



let CONTAINERS_PLANT_TYPES,CONTAINER_COLUMNS_PLANT_TYPES;

let CONTROLLING_UNIT_SUB_PARAMETERS:DataCells

let CONTAINERS_PLANT,CONTAINER_COLUMNS_PLANT_ALLOCATION,CONTAINER_COLUMNS_PLANT,
    PLANT_PARAMETERS,CONTAINERS_PLANT_GROUP,CONTAINER_COLUMNS_PLANT_GROUP,
    CONTAINER_COLUMNS_PLANT_CONTROLLING, ALLOCATION_FOR_PLANTS_PARAMETER, MODAL_PLANT_ALLOCATION

let CONTAINER_COLUMNS_JOBS,CONTAINER_JOBS,CONTAINER_COLUMNS_CONDITIONS,CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS,
    CONTAINER_COLUMNS_DISPATCHING_HEADER,CONTAINERS_DISPATCHING_HEADER,CONTAINER_PROJECT,CONTAINER_COLUMNS_DISPATCHING_RECORDS
   


describe("LRM- 1.89 | Verify the Allocated To field in Allocation container in plant master module after generation of Allocations", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

    before(function () {
        cy.fixture("LRM/lgm-1.89-verify-allocated-to-field-allocation-in-plant-master-generation-allocations.json").then((data) => {
            this.data = data;
          
           
            CONTAINER_COLUMNS_PLANT_TYPES = this.data.CONTAINER_COLUMNS.PLANT_TYPES;
            CONTAINERS_PLANT_TYPES = this.data.CONTAINERS.PLANT_TYPES;

            
            CONTAINER_COLUMNS_PLANT_ALLOCATION= this.data.CONTAINER_COLUMNS.PLANT_ALLOCATION
            CONTAINER_COLUMNS_DISPATCHING_RECORDS= this.data.CONTAINER_COLUMNS.DISPATCHING_RECORDS
            CONTAINER_PROJECT = this.data.CONTAINERS.PROJECT
        
            CONTAINERS_PLANT_GROUP = this.data.CONTAINERS.PLANT_GROUP;
            CONTAINER_COLUMNS_PLANT_GROUP = this.data.CONTAINER_COLUMNS.PLANT_GROUP;

            CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;
            CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
           
            CONTAINER_COLUMNS_PLANT_CONTROLLING = this.data.CONTAINER_COLUMNS.PLANT_CONTROLLING;
           

            MODAL_PLANT_ALLOCATION = this.data.MODAL.PLANT_ALLOCATION;
           
            CONTAINER_COLUMNS_JOBS = this.data.CONTAINER_COLUMNS.JOBS
            CONTAINER_JOBS = this.data.CONTAINERS.JOBS
            CONTAINER_COLUMNS_CONDITIONS = this.data.CONTAINER_COLUMNS.CONDITIONS
            CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS = this.data.CONTAINERS.WORK_OPERATION_TYPE_CONDITIONS
            CONTAINER_COLUMNS_DISPATCHING_HEADER = this.data.CONTAINER_COLUMNS.DISPATCHING_HEADER
            CONTAINERS_DISPATCHING_HEADER = this.data.CONTAINERS.DISPATCHING_HEADER
            

            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));

             /* Open desktop should be called in before block */
         _common.openDesktopTile(tile.DesktopTiles.PROJECT);
         _common.waitForLoaderToDisappear()
          _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
          });
          _commonAPI.getAccessToken().then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                  });
        });

    })

    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
		_commonAPI.createProject().then(() => {
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
			_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
		});
	});

    it("TC - API call to assign logged-in user a clerk", function () {
        _commonAPI.getULoggedInUserId(Cypress.env("USER_NAME"))
                  .then(()=>{
                    _commonAPI.assignUserToClerk(Cypress.env("API_LOGGED_IN_USER_ID"),Cypress.env("USER_NAME"),apiConstantData.CONSTANT.SMIJ)
        })
    });
    
    it("TC - Add Plant Type Record", function () {
       const DISPATCH_HEADER_PARAMETERS = {
            [app.GridCells.IS_READY_FOR_SETTLEMENT]: "true",
        }
        const PLANT_LIST_TYPE_PARAMETERS = {
            [app.GridCells.DESCRIPTION_INFO]:PLANT_TYPE
        }

        _commonAPI.updateDispatchHeaderStatus_underCustomizing(DISPATCH_HEADER_PARAMETERS,CONTAINERS_PLANT_TYPES.STATUS,CONTAINERS_PLANT_TYPES.TYPE)
        _commonAPI.createPlantListType(PLANT_LIST_TYPE_PARAMETERS)

    })

    it("TC - Create New Plant price list record", function () {
       
        let PLANT_PRICE_LIST: DataCells = {
            [app.GridCells.CONTEXT_FK]: apiConstantData.ID.PLANT_CONTEXT_RIB_DEMO,
            [app.GridCells.PRICE_LIST_TYPE_FK]: Cypress.env('API_PLANT_LIST_TYPE_ID_1'),
            [app.GridCells.CURRENCY]: apiConstantData.ID.CURRENCY_EUR,
            [app.GridCells.UOM_FK]: apiConstantData.ID.UOM_DAY,
            [app.GridCells.DESCRIPTION_INFO]: PLANT_LIST1,
            [app.GridCells.CALCULATION_TYPE_FK]: apiConstantData.ID.CALCULATION_TYPE_AVERAGE_CATALOG_VALUE,
            [app.GridCells.PERCENT]: "100"
        }
        _commonAPI.createPlantPriceList(PLANT_PRICE_LIST)
       
    })

    it("TC - Add Plant Type Data Record", function () {

        let PLANT_TYPE: DataCells = {
            [app.GridCells.IS_CLUSTER]: "true"
        }
        _commonAPI.createPlantType(PLANT_TYPE)
    })

    it("TC - Create Work Operation Types and assign plant type to work operation ", function () {

        let WORK_OPERATION_TYPE:DataCells={
            [app.GridCells.IS_HIRE]:"true",
            [app.GridCells.UOM]:apiConstantData.ID.UOM_DAY,
            [app.GridCells.IS_LIVE]:"true"

        }
        _commonAPI.createWorkOperationType(WORK_OPERATION_TYPE).then(()=>{
            let PLANT_TYPE:DataCells={
                [app.GridCells.WORK_OPERATION_TYPE_FK]:Cypress.env(`API_WORK_OPERATION_TYPE_ID_1`),
                [app.GridCells.PLANT_TYPE_FK]:Cypress.env(`API_PLANT_TYPE_ID_1`)
            }
            _commonAPI.createWorkOperationPlantType(PLANT_TYPE)
        })

    })

    it("TC - Create Controlling Units", function () {
        CONTROLLING_UNIT_SUB_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_PLANT_TYPES.QUANTITY[0], CONTAINERS_PLANT_TYPES.QUANTITY[1]],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true"]
        }
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_SUB_PARAMETERS)
       
    })

    it("TC - Create New Plant Group and sub record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_GROUP)
        _common.openTab(app.TabBar.PLANT_GROUP_AND_LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_GROUP, app.FooterTab.PLANT_GROUPS, 0)
            _common.setup_gridLayout(cnt.uuid.PLANT_GROUP, CONTAINER_COLUMNS_PLANT_GROUP)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_GROUP)
        })
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.PLANT_GROUP)
        _common.create_newRecord(cnt.uuid.PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, PLANT_GROUP_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.select_rowHasValue(cnt.uuid.PLANT_GROUP, PLANT_GROUP_DESC)
        _common.create_newSubRecord(cnt.uuid.PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, SUB_PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, SUB_PLANT_GROUP_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create New Plant", function () {
        PLANT_PARAMETERS = {
            [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION,
            [app.GridCells.PLANT_GROUP_FK]: SUB_PLANT_GROUP,
            [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
            [app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_DESC_1`),
            [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
            [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
        }

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)

        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
            _common.setup_gridLayout(cnt.uuid.PLANT, CONTAINER_COLUMNS_PLANT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PLANT)
        _common.create_newRecord(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS)

        _common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Add Controlling Unit Record to Plant and assign price list in Plant Master Module', function () {

        cy.wait(3000)//required wait save and fetch API data
        _commonAPI.getPlantDetails(PLANT_DESCRIPTION).then(() => {
            let CNT_PLANT: DataCells = {
                [apiParameters.Keys.PLANT_ID]: Cypress.env(`API_GET_PLANT_ID`),
                [apiParameters.Keys.CONTEXT_ID]: apiConstantData.ID.PLANT_CONTEXT_RIB_DEMO_PROJECT_CONTEXT,
                 [apiParameters.Keys.PROJECT_ID]: Cypress.env('API_PROJECT_ID_1'),
                [apiParameters.Keys.CONTROLLING_UNIT_ID]: Cypress.env(`API_CNT_ID_0`)

            }
            _commonAPI.createControllingUnitUnderPlant(CNT_PLANT)
        }).then(() => {
        let PLANT_PRICE_LIST: DataCells = {
                [apiParameters.Keys.PLANT_ID]: Cypress.env(`API_GET_PLANT_ID`),
                [apiParameters.Keys.PRICE_LIST_ID]: Cypress.env(`API_PLANT_PRICE_LIST_ID_1`),
                [apiParameters.Keys.IS_MANUAL]: "true",
                [apiParameters.Keys.PRICE_PORTION_1]: "20",
                [apiParameters.Keys.PRICE_PORTION_2]: "30",
                [apiParameters.Keys.UOM_ID]: apiConstantData.ID.UOM_DAY
            }
            _commonAPI.createPriceListUnderPlant(PLANT_PRICE_LIST)
        })
    })

    it('TC - Create plant location record from wizard', function () {
        ALLOCATION_FOR_PLANTS_PARAMETER = {
            [commonLocators.CommonLabels.JOB]: Cypress.env('API_PROJECT_NUMBER_1'),
            [commonLocators.CommonLabels.ALLOCATED_FROM]: MODAL_PLANT_ALLOCATION.ALLOCATED_FROM,
            [app.GridCells.WORK_OPERATION_TYPE_FK]:Cypress.env('API_WORK_OPERATION_TYPE_DESC_1')
        }


        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_INITIAL_ALLOCATION_FOR_PLANTS)
        _common.waitForLoaderToDisappear()
        _logesticPage.create_initialAllocationForPlants_fromWizard_byClass(ALLOCATION_FOR_PLANTS_PARAMETER,  Cypress.env(`API_PLANT_TYPE_DESC_1`))
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

    
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
        });
        _common.select_allContainerData(cnt.uuid.PLANT_ALLOCATIONS)

        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
        });
        _common.select_allContainerData(cnt.uuid.PLANT_LOCATION)

        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PLANT_DESCRIPTION)

        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });

        _common.select_rowInContainer(cnt.uuid.PLANT)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
    })

    it('TC - Create New Project-B', function () {

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
          });
        _commonAPI.createProject().then(() => {
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
			_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2')).pinnedItem();
		});
    })

    it("TC - Add Controlling Unit", function () {

        const CONTROLLING_UNIT2_SUB_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_PLANT_TYPES.QUANTITY[0], CONTAINERS_PLANT_TYPES.QUANTITY[1]],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_BILLING_ELEMENT]: ["true", "true"],
            [app.GridCells.ISA_ACCOUNTING_ELEMENT]: ["true", "true"],
            [app.GridCells.IS_PLANNING_ELEMENT]: ["true", "true"],
            [app.GridCells.IS_TIMEKEEPING_ELEMENT]: ["true", "true"]
        }
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
            _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_2'), 2, CONTROLLING_UNIT2_SUB_PARAMETERS)
    })

    it("TC - Create Job record in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enter value of project


        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS, 0);
            _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_JOBS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_JOBS.controllingunitfk], cnt.uuid.JOBS)

        });
        _common.clear_subContainerFilter(cnt.uuid.JOBS)
        _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.maximizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
        cy.wait(1000)//required wait to enable data input fields
        _common.clickOn_activeRowCell(cnt.uuid.JOBS, app.GridCells.CONTROLLING_UNIT_FK)
        cy.wait(1000)//required wait to save data
        _common.waitForLoaderToDisappear()
         cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.CONTROLLING_UNIT_FK,Cypress.env(`API_CNT_CODE_1`), commonLocators.CommonKeys.GRID)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create Job record in logistic price condition module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_PRICE_CONDITION)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.LOGISTIC_PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONDITIONS, app.FooterTab.CONDITIONS);
            _common.setup_gridLayout(cnt.uuid.CONDITIONS, CONTAINER_COLUMNS_CONDITIONS)
            _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        });
        _common.maximizeContainer(cnt.uuid.CONDITIONS)
        _common.create_newRecord(cnt.uuid.CONDITIONS)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, CONDITION_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, CONDITION_DESC)
        _common.set_cellCheckboxValue(cnt.uuid.CONDITIONS, app.GridCells.IS_HANDLING_CHARGE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONDITIONS)
        _common.select_rowHasValue(cnt.uuid.CONDITIONS, CONDITION_CODE)
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.FooterTab.PLANT_CATALOG_PRICELISTS);
        });
        _common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, Cypress.env(`API_PLANT_PRICE_LIST_DESC_1`))

        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_ITEM, app.FooterTab.WORK_OPERATION_TYPE_CONDITIONS);
            _common.create_newRecord(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_WORK_OPERATION_TYPE_DESC_1'))
        });
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.PRICE_CONDITION_ITEM,app.GridCells.PRICING_GROUP_FK,commonLocators.CommonKeys.LIST, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PLANT_PRICING_GROUP)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Assign Price condition in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'));
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS);
            _common.search_inSubContainer(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
            _common.waitForLoaderToDisappear()
            _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        });
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PRICE_CONDITION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONDITION_CODE)
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_JOBS.BUSINESS_PARTNER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create record in dispatching notes", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.create_newRecord(cnt.uuid.DISPATCHING_HEADER)
        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, CONTAINERS_DISPATCHING_HEADER.EFFECTIVE_DATE)
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB1_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB2_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER,app.GridCells.DESCRIPTION,app.InputFields.DOMAIN_TYPE_DESCRIPTION,DN)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE)
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE, "DISPATCH_CODE")
        _common.minimizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue (cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        cy.REFRESH_SELECTED_ENTITIES()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, app.FooterTab.PERFORMING_JOB_PLANT_LOCATIONS);
        });
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_RECORD,CONTAINER_COLUMNS_DISPATCHING_RECORDS)
        });

        _common.create_newRecord(cnt.uuid.DISPATCHING_RECORD)
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.RECORD_TYPE_FK, commonLocators.CommonKeys.GRID, CONTAINERS_DISPATCHING_HEADER.RECORD_TYPE)
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_RECORD, app.GridCells.ARTICLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE)
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD,app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, Cypress.env('API_WORK_OPERATION_TYPE_DESC_1'))
        _common.waitForLoaderToDisappear()
        cy.wait(1000) //required wait to enable work operations type cell
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
        });
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.TRANSPORT_PLANNING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.PICKING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.LOADED)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.DELIVERED)
        _common.waitForLoaderToDisappear()
    })


    it("TC - Validation of dispatch notes in plant master module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PLANT_DESCRIPTION);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)

        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
        });
        _validate.verify_isRecordPresent(cnt.uuid.PLANT_LOCATION, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
            _common.setup_gridLayout(cnt.uuid.PLANT_ALLOCATIONS, CONTAINER_COLUMNS_PLANT_ALLOCATION)
            _common.set_columnAtTop([CONTAINER_COLUMNS_PLANT_ALLOCATION.allocatedto,CONTAINER_COLUMNS_PLANT_ALLOCATION.dispatchheaderoutfkdescription],cnt.uuid.PLANT_ALLOCATIONS)
        });
        
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.PLANT_ALLOCATIONS, Cypress.env("DISPATCH_CODE"))
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.PLANT_ALLOCATIONS, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()

    })

    it("TC - Verify revert allocation from dispatch notes using wizard", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_SELECTED_ENTITIES()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_RECORD,CONTAINER_COLUMNS_DISPATCHING_RECORDS)
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.select_rowInSubContainer(cnt.uuid.DISPATCHING_RECORD)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.REVERT_ALLOCATION_FROM_DISPATCH_RECORD)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
    })
   
    it("TC - Validation of dispatch notes in plant master module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, Cypress.env(`API_GET_PLANT_DESC`))
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, Cypress.env(`API_GET_PLANT_DESC`))
       
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
        });
        _common.waitForLoaderToDisappear()
        _common.assert_cellDataByContent_inContainer(cnt.uuid.PLANT_ALLOCATIONS,app.GridCells.ALLOCATED_TO, "")
        _common.waitForLoaderToDisappear()
        _common.assert_cellDataByContent_inContainer(cnt.uuid.PLANT_ALLOCATIONS,app.GridCells.DISPATCH_HEADER_OUT_FK_DESCRIPTION, "")
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create copy of record in dispatching notes change its status ", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER,Cypress.env("DISPATCH_CODE"))
        _common.waitForLoaderToDisappear()
        _common.clickOn_toolBarButtonWithTitle(cnt.uuid.DISPATCHING_HEADER,btn.ButtonText.DEEP_COPY_INCLUDING_DEPENDENCIES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.maximizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER,DN)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasValue(cnt.uuid.DISPATCHING_HEADER,app.GridCells.DISPATCH_STATUS_FK,commonLocators.CommonKeys.CREATED)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER,app.GridCells.DESCRIPTION,app.InputFields.DOMAIN_TYPE_DESCRIPTION,COPY_DN)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER,COPY_DN)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.minimizeContainer(cnt.uuid.DISPATCHING_HEADER)

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, app.FooterTab.PERFORMING_JOB_PLANT_LOCATIONS);
        });
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_RECORD,CONTAINER_COLUMNS_DISPATCHING_RECORDS)
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
       
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
        });
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.TRANSPORT_PLANNING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.PICKING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.LOADED)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.DELIVERED)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Validation of dispatch notes in plant master module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, Cypress.env(`API_GET_PLANT_DESC`))
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, Cypress.env(`API_GET_PLANT_DESC`))
       
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
        });
        _common.waitForLoaderToDisappear()
        _common.select_firstRowInContainer(cnt.uuid.PLANT_ALLOCATIONS,app.GridCells.PROJECT_NAME_SMALL)
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_whereRecordIsNotEqual(cnt.uuid.PLANT_ALLOCATIONS,app.GridCells.ALLOCATED_TO, "")
        _common.waitForLoaderToDisappear()
        _common.assert_cellDataByContent_inContainer(cnt.uuid.PLANT_ALLOCATIONS,app.GridCells.DISPATCH_HEADER_OUT_FK_DESCRIPTION, COPY_DN)
        _common.waitForLoaderToDisappear()

    })

    it("TC - Verify revert allocation from dispatch notes using wizard", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_SELECTED_ENTITIES()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.select_rowInSubContainer(cnt.uuid.DISPATCHING_RECORD)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.REVERT_ALLOCATION_FROM_DISPATCH_RECORD)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Validation of dispatch notes in plant master module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, Cypress.env(`API_GET_PLANT_DESC`))
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, Cypress.env(`API_GET_PLANT_DESC`))
       
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
        });
        _common.waitForLoaderToDisappear()
        _common.assert_cellDataByContent_inContainer(cnt.uuid.PLANT_ALLOCATIONS,app.GridCells.ALLOCATED_TO, "")
        _common.waitForLoaderToDisappear()
        _common.assert_cellDataByContent_inContainer(cnt.uuid.PLANT_ALLOCATIONS,app.GridCells.DISPATCH_HEADER_OUT_FK_DESCRIPTION, "")
        _common.waitForLoaderToDisappear()

    })
});


