/**
 * Created by hzh on 2025/05/28
 */
(function (angular) {
	'use strict';
	const moduleName = 'documents.centralquery';

	/**
	 * @ngdoc service
	 * @name documentsCentralquerySpConfigurationService
	 * @function
	 *
	 */
	angular.module(moduleName).service('documentsCentralquerySpConfigurationService', documentsCentralquerySpConfigurationService);

	documentsCentralquerySpConfigurationService.$inject = ['_', '$translate', 'globals', '$q', '$http',
		'platformModalService', 'PlatformMessenger'];

	function documentsCentralquerySpConfigurationService(_, $translate, globals, $q, $http,
	                                                     platformModalService, PlatformMessenger) {
		const service = {};

		const dialogConfig = {
			templateUrl: globals.appBaseUrl + 'documents.centralquery/templates/sharepoint/documents-centralquery-sharepoint-configuration-dialog-service.html',
			backdrop: false,
			height: '680px',
			width: '1152px',
			resizeable: true
		};

		service.SharePointPrjDocumentType = 5;

		service.showDialog = function () {
			platformModalService.showDialog(dialogConfig);
		};

		let dataList = [];
		let currentItem;

		service.aadUsers = [];
		service.getSelected = function () {
			return currentItem;
		};

		service.getProjectConfigurationList = function (filterString) {
			const defer = $q.defer();

			if (!filterString) {
				$http.get(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/configurationlist')
					.then(function (response) {
						defer.resolve(response.data);
					});
			} else {
				defer.resolve([]);
			}
			return defer.promise;
		};

		service.syncProjectsToSharePoint = function (request) {
			const defer = $q.defer();

			const data = request.Dtos;
			$http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/syncprojects', request)
				.then(function (response) {
					if (response.data) {
						checkProjectsIsSynced(request).then(function (response) {
							let syncFolderConfigs = _.filter(data, {HasDrillDown: true});
							syncFolderConfigs = _.filter(syncFolderConfigs, function (item) {
								return _.some(response, {ProjectId: item.PrjProjectFk, IsSynced: true});
							});
							if (syncFolderConfigs?.length > 0) {
								let prjIds = _.map(syncFolderConfigs, item => item.PrjProjectFk);
								const syncdetailstructRequest = {
									PrjIds: prjIds,
									AadUserId: request.AadUserId,
									AccessToken: request.AccessToken
								};

								$http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/syncdetailstruct', syncdetailstructRequest)
									.then(function (syncFolderResponse) {
										addFolderPermission(syncdetailstructRequest);

										defer.resolve(syncFolderResponse.data);
									});
							} else {
								defer.resolve(response);
							}
						});
					} else {
						defer.resolve({});
					}

				});
			return defer.promise;
		};

		service.updateIsAutoSync = function (data, newIsAutoSync) {
			const defer = $q.defer();

			$http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/updateisautosync?PrjId=' + data.PrjProjectFk + '&IsAutoSync=' + newIsAutoSync)
				.then(function (response) {
					defer.resolve(response.data);
				});
			return defer.promise;
		};

		service.setSelected = function (item) {
			currentItem = item;
		};

		service.setDataList = function (value) {
			dataList = value;
		};

		service.getList = function () {
			return dataList;
		};
		service.refreshGrid = function () {
			service.getList();
			service.listLoaded.fire();
		};

		service.listLoaded = new PlatformMessenger();
		service.refreshData = new PlatformMessenger();

		service.registerListLoaded = function registerListLoaded(callBackFn) {
			service.listLoaded.register(callBackFn);
		};
		service.unregisterListLoaded = function (callBackFn) {
			service.listLoaded.unregister(callBackFn);
		};

		service.getAadUsers = function () {
			if (service.aadUsers.length === 0) {
				$http.get(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/getaadusers').then(function (response) {
					if (response.data) {
						service.aadUsers = response.data;
					}
				});
			}
		};

		function addFolderPermission(request) {
			return $http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/addfolderpermission', request).then();
		}

		function checkProjectsIsSynced(request) {
			const defer = $q.defer();

			$http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/checkprojectsissynced', request)
				.then(function (response) {
					defer.resolve(response.data);
				});
			return defer.promise;
		}

		return service;
	}
})(angular);
