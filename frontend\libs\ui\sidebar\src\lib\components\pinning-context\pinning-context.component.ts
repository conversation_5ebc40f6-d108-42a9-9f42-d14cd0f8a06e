/**
 * Copyright(c) RIB Software GmbH
 */

import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ModuleForPinningContext, ModuleNamesForPinningContext } from '@libs/platform/common';

/**
 * Component for pinning context in sidebar.
 */
@Component({
	selector: 'ui-sidebar-pinning-context',
	templateUrl: './pinning-context.component.html',
	styleUrl: './pinning-context.component.scss'
})
export class PinningContextComponent {
	/**
	 * List of module pinning context objects.
	 */
	@Input() public items!: ModuleForPinningContext[];

	/**
	 * Event to emit on pinning context delete.
	 */
	@Output() public remove = new EventEmitter<ModuleNamesForPinningContext>();

	/**
	 * Removes a pinning context by a given token.
	 * @param token The token of the pinning context.
	 */
	public removePinningContext(token: ModuleNamesForPinningContext) {
		this.remove.emit(token);
	}
}