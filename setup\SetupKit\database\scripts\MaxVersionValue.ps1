﻿#$vanilladatafolder="D:\05-iTWOCloud\wsdb\Database\Setup\iTWO40\CurrentSetup\Database\sqlserver\update_database\1000004\vanilla"
#$filelist = @(gci -path $vanilladatafolder | ? -FilterScript {$_.name -match "^([0-9]{1,})[_,A-z]*\.sql$"} | Sort-Object)
#
#[System.Collections.ArrayList]$arrList=new-object System.Collections.ArrayList
#$arrList.clear()
#$filelist | ? {$arrlist.add($_.name.split("_")[0].split(".")[0]))}
#$arrList


function GetLatestVersionFromFileList($filelist) {
	#$vanilladatafolder="D:\05-iTWOCloud\wsdb\Database\Setup\iTWO40\CurrentSetup\Database\sqlserver\update_database\1000004\vanilla"
	##$filelist = @(gci -path $vanilladatafolder | ? -FilterScript {$_.name -match "^([0-9]{1,})[_,A-z]*\.sql$"} | Sort-Object)
	[System.Collections.ArrayList]$arrList = new-object System.Collections.ArrayList
	##$arrList.clear()
	$t = $filelist | ? { $arrlist.add($_.name.split(".")[0].split(".")[0]) }
	$rc = ($arrList | Measure -Max).maximum
	[int]$rc
}


$vanilladatafolder = 'D:\05-iTWOCloud\wsdb\Database\Setup\iTWO40\CurrentSetup\Database\sqlserver\update_database\1000004\vanilla'
$filelist = @(gci -path $vanilladatafolder | ? -FilterScript { $_.name -match '^([0-9]{1,})[_,A-z]*\.sql$' } | Sort-Object)

$maxValue = GetLatestVersionFromFileList($filelist)
write-output "Latest version number is $maxValue"
