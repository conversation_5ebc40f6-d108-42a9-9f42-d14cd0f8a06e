/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import {
	BasicsCompanyLookupService,
	BasicsShareControllingUnitLookupService,
	BasicsSharedCustomizeLookupOverloadProvider,
	BasicsSharedLookupOverloadProvider, BasicsSharedProcurementConfigurationLookupService, BasicsSharedVATGroupLookupService
} from '@libs/basics/shared';
import { createLookup, FieldType, ILayoutConfiguration } from '@libs/ui/common';
import { BusinessPartnerLookupService, BusinesspartnerSharedSubsidiaryLookupService } from '@libs/businesspartner/shared';
import { ProjectSharedLookupOverloadProvider } from '@libs/project/shared';
import { SalesCommonLabels } from '@libs/sales/common';
import { ISalesSharedLookupOptions, IWipHeaderEntity, SALES_SHARED_LOOKUP_PROVIDER_TOKEN } from '@libs/sales/interfaces';
import { SalesCommonWipLookupDataService } from '@libs/sales/common';
import { SalesWipWipsDataService } from '../../services/sales-wip-wips-data.service';
import { SalesWipWipValidationService } from '../../services/validations/sales-wip-wip-validation.service';
import { SalesWipLabels } from '../sales-wip-labels.class';
import { prefixAllTranslationKeys } from '@libs/platform/common';

export const SALES_WIP_WIPS_ENTITY_INFO: EntityInfo = EntityInfo.create<IWipHeaderEntity>({
	grid: {
		title: {key: 'sales.wip.containerTitleWips'}
	},
	form: {
		title: {key: 'sales.wip.containerTitleWipsForm'},
		containerUuid: 'd7bfa7174fc14ab49acef0c6f6b6678b',
	},
	dataService: ctx => ctx.injector.get(SalesWipWipsDataService),
	validationService: ctx => ctx.injector.get(SalesWipWipValidationService),
	dtoSchemeId: {moduleSubModule: 'Sales.Wip', typeName: 'WipHeaderDto'},
	permissionUuid: '689E0886DE554AF89AADD7E7C3B46F25',
	layoutConfiguration: async ctx => {
		const salesSharedLookupProvider = await ctx.lazyInjector.inject(SALES_SHARED_LOOKUP_PROVIDER_TOKEN);
		return <ILayoutConfiguration<IWipHeaderEntity>>{
			groups: [
				{
					gid: 'Basic Data', attributes: ['AmountGross', 'AmountGrossOc', 'AmountNet', 'AmountNetOc', 'ProjectFk', 'ConfigurationFk', 'CompanyResponsibleFk', 'OrdHeaderFk', 'WipStatusFk', 'RubricCategoryFk', 'Code', 'ExchangeRate', 'BasSalesTaxMethodFk', 'BillingSchemaFk',
						'ClerkFk', 'CurrencyFk', 'DateEffective', 'IsBilled', 'IsNotAccrual', 'ControllingUnitFk', 'PrcStructureFk','LanguageFk', 'FactorDJC', 'DescriptionInfo', 'ObjUnitFk', 'IsCanceled']
						//TODO:add this field and create lookup 'PrevWipHeaderFk', 'QtoHeaderFk'
				},
				{gid: 'Customer Data', attributes: ['DocumentDate', 'BusinesspartnerFk', 'CustomerFk', 'SubsidiaryFk'] //TODO:add this field and create lookup 'ContactFk'
				},
				{gid: 'Payment Data', attributes: ['PaymentTermFiFk', 'PaymentTermPaFk', 'PaymentTermAdFk', 'TaxCodeFk', 'VatGroupFk']},
				{gid: 'Dates', attributes: ['PerformedFrom', 'PerformedTo']},
				{gid: 'Other', attributes: ['Remark', 'CommentText']},
				{gid: 'User Defined Text', attributes: ['UserDefined1', 'UserDefined2', 'UserDefined3', 'UserDefined4', 'UserDefined5']},
				{gid: 'User Defined Dates', attributes: ['UserDefinedDate01', 'UserDefinedDate02', 'UserDefinedDate03', 'UserDefinedDate04', 'UserDefinedDate05']},
			],
			overloads: {
				ProjectFk: ProjectSharedLookupOverloadProvider.provideProjectLookupOverload(true),
				ConfigurationFk: {
					readonly: true,
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsSharedProcurementConfigurationLookupService,
					}),
				},
				OrdHeaderFk: salesSharedLookupProvider.provideOrdHeaderLookupOverload(new class implements ISalesSharedLookupOptions {
					public readOnly: boolean = true;
					public showClearBtn: boolean = false;
				}),

				CompanyResponsibleFk: {
					readonly: true,
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsCompanyLookupService,
						showDescription: true,
						descriptionMember: 'CompanyName',
					}),
				},
				WipStatusFk: BasicsSharedCustomizeLookupOverloadProvider.provideWorkInProgressStatusReadonlyLookupOverload(),
				RubricCategoryFk: BasicsSharedCustomizeLookupOverloadProvider.provideRubricCategoryReadonlyLookupOverload(),
				BasSalesTaxMethodFk: BasicsSharedCustomizeLookupOverloadProvider.provideSalesAdvanceTypeLookupOverload(true),
				BillingSchemaFk: BasicsSharedLookupOverloadProvider.provideBillingSchemaLookupOverload(true),
				ClerkFk: BasicsSharedLookupOverloadProvider.providerBasicsClerkLookupOverload(true),
				CurrencyFk: BasicsSharedLookupOverloadProvider.provideCurrencyLookupOverload(true),
				LanguageFk: BasicsSharedCustomizeLookupOverloadProvider.provideUserInterfaceLanguageLookupOverload(true),
				ControllingUnitFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsShareControllingUnitLookupService
					})
				},
				PrcStructureFk: BasicsSharedLookupOverloadProvider.providerBasicsProcurementStructureLookupOverload(true),
				// Customer Data
				BusinesspartnerFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BusinessPartnerLookupService,
						displayMember: 'BusinessPartnerName1',
						showClearButton: true
					})
				},
				PrevWipHeaderFk: {
                    type: FieldType.Lookup,
                    width: 150,
                    readonly: true,
                    lookupOptions: createLookup<IWipHeaderEntity, IWipHeaderEntity>({
                        dataServiceToken: SalesCommonWipLookupDataService,
                    }),
                },
				CustomerFk: BasicsSharedCustomizeLookupOverloadProvider.provideCustomerAbcLookupOverload(true),
				SubsidiaryFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BusinesspartnerSharedSubsidiaryLookupService
					})
				},
				// payment data
				PaymentTermFiFk: BasicsSharedLookupOverloadProvider.providePaymentTermListLookupOverload(true),
				PaymentTermPaFk: BasicsSharedLookupOverloadProvider.providePaymentTermListLookupOverload(true),
				PaymentTermAdFk: BasicsSharedLookupOverloadProvider.providePaymentTermListLookupOverload(true),
				TaxCodeFk: BasicsSharedLookupOverloadProvider.provideTaxCodeListLookupOverload(true),
				VatGroupFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup({dataServiceToken: BasicsSharedVATGroupLookupService})
				},
				IsBilled: { readonly: true },
				AmountNet: { readonly: true },
				AmountNetOc: { readonly: true },
				AmountGross: { readonly: true },
				AmountGrossOc: { readonly: true },
				FactorDJC: { readonly: true },
				Code: { readonly: true },
				ObjUnitFk: BasicsSharedCustomizeLookupOverloadProvider.provideObjectUnitTypeSpecLookupOverload(true),
				//TODO:Loolup not available : ContactFk
				//TODO:Loolup not available : PrevWipHeaderFk
				//TODO:Loolup not available : QtoHeaderFk
			},
			labels: {
				...prefixAllTranslationKeys('sales.wip.', {
					PerformedFrom: {key: 'performedFrom'},
					PerformedTo: {key: 'performedTo'},
				}),
				...prefixAllTranslationKeys('cloud.common.', {
					Code: {key: 'entityCode'},
				}),
				...SalesWipLabels.getSalesWipLabels(),
				...SalesCommonLabels.getSalesCommonLabels(),
			},
		};
	}
});