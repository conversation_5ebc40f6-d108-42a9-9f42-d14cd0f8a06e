/*
 * Copyright(c) RIB Software GmbH
 */
import { BasicsSharedBillStatusLookupService, EntityReadonlyProcessorBase, ReadonlyFunctions } from '@libs/basics/shared/src';
import { SalesBillingBillsDataService } from './sales-billing-bills-data.service';
import { IBilHeaderEntity } from '@libs/sales/interfaces';
import { inject } from '@angular/core';
import { SalesCommonContextService } from '@libs/sales/common';

/**
 * Sales billing readonly processor service.
 */
export class SalesBillingReadonlyProcessorService extends EntityReadonlyProcessorBase<IBilHeaderEntity> {
	private readonly moduleContext = inject(SalesCommonContextService);
	private readonly lookupService = inject(BasicsSharedBillStatusLookupService);
	public constructor(protected dataService: SalesBillingBillsDataService) {
		super(dataService);
	}

	/**
	 * Define readonly behavior for fields when BilStatusFk is changed.
	 */
	public override generateReadonlyFunctions(): ReadonlyFunctions<IBilHeaderEntity> {
		return {
			BilStatusFk: {
				shared: [
					'TypeFk',
					'RubricCategoryFk',
					'ConfigurationFk',
					'CompanyResponsibleFk',
					'CompanyIcDebtorFk',
					'BillingSchemaFk',
					'ProjectFk',
					'LanguageFk',
					'BilStatusFk',
					'BillDate',
					'DatePosted',
					'BillNo',
					'ConsecutiveBillNo',
					'ProgressInvoiceNo',
					'ReferenceStructured',
					'InvoiceTypeFk',
					'DescriptionInfo',
					'CurrencyFk',
					'ExchangeRate',
					'BankTypeFk',
					'BankFk',
					'ClerkFk',
					'ContractTypeFk',
					'OrdConditionFk',
					'AmountNet',
					'AmountGross',
					'AmountNetOc',
					'AmountGrossOc',
					'IsCanceled',
					'CancellationNo',
					'CancellationReason',
					'CancellationDate',
					'BookingText',
					'OrdHeaderFk',
					'PreviousBillFk',
					'RelatedBillHeaderFk',
					'ObjUnitFk',
					'ControllingUnitFk',
					'PrcStructureFk',
					'DateEffective',
					'DocumentNo',
					'BasSalesTaxMethodFk',
					'IsNotAccrual',
				],
				readonly: this.readonlyFields.bind(this),
			},
		};
	}

	/**
	 * Determine if the current entity should be readonly based on the user's company and billing status.
	 */
	protected async readonlyFields(): Promise<boolean> {
		let isReadonly = true;
		const entity = this.dataService.getSelectedEntity();

		if (!entity) {
			return true; // default to readonly if no entity selected
		}

		const loginCompany = await this.moduleContext.getCompany().then((c) => c?.CompanyFk);

		if (entity.CompanyFk === loginCompany) {
			const moduleState = await  this.getModuleState(entity);
			isReadonly = moduleState?.IsReadonly ?? true;
		}

		this.dataService.setEntityReadOnly(entity, isReadonly);
		return isReadonly;
	}

	/**
	 * Get module state from the current billing entity.
	 * Assumes BilStatusFk is an object with IsReadonly, or fetch it appropriately.
	 */
	public async getModuleState(entity: IBilHeaderEntity): Promise<{ IsReadonly: boolean }> {
		const bilStatusFk = entity.BilStatusFk;
		if (!bilStatusFk) {
			return { IsReadonly: false };
		}
		const statusList = await this.lookupService.getListAsync();
		const statusItem = Array.isArray(statusList) ? statusList.find((item) => item.Id === bilStatusFk) : null;
		return statusItem ?? { IsReadonly: false };
	}
}
