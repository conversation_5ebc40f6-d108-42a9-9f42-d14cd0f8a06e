/*
 * $Id:  $
 * Copyright (c) RIB Software SE
 */

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Estimate.Common.BusinessComponents;
using RIB.Visual.Estimate.Parameter.BusinessComponents;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// This class contains logic for "Generate Estimate from Ref Boq" wizard
	/// </summary>
	public class EstimateGeneratorFromRefBoqLogic
	{

		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Normal Estimates, Project Boq scenario
		/// Get source line items based on project Estimate Header and copy line items from source to target.
		/// </summary>
		/// <param name="data"></param>
		/// <param name="sourceBoqItemData"></param> targetProjectEntity
		/// <param name="targetProjectEntity"></param> 
		/// <param name="targetEstHeader"></param>
		public IEnumerable<EstLineItemEntity> NormalEstGenerateFromProjectBoQ(GenerateEstimateFromBoqData data, SourceBoQsData sourceBoqItemData, IProjectEntity targetProjectEntity, IEstHeaderEntity targetEstHeader)
		{
			var boqItemLogic = new BoqItemLogic();

			var estimateMainLineItemLogic = new EstimateMainLineItemLogic();

			// Get matching source and target BoQ item IDs with headers
			var estMainBoQData = boqItemLogic.CompareBoqs(data.TargetProjectId,
																								 sourceBoqItemData.ProjectWicId,
																								 null,
																								 data.TargetBoqHeaderFk,
																								 sourceBoqItemData.SourceBoqHeaderFk,
																								 sourceBoqItemData.FromBoqItemRefNo,
																								 sourceBoqItemData.ToBoqItemRefNo,
																								 data.OutlineSpecification,
																								 data.BasUomFk);

			//Dictionary Key is source boq items and Value is TargetBoqItems
			//ConcurrentDictionary<Tuple<int?, int?>, Tuple<int?, int?>> matchingSourceTargetBoqItemIds
			//Tuple<SourceBoqItem Id,Source BoqItemHeaderFk>,Tuple<TargetBoqItem Id, Target BoqHeaderFk>
			var matchingSourceTargetBoqItemIds = estMainBoQData?.Source2TargetPrjBoqItemRefMap;

			if (matchingSourceTargetBoqItemIds == null || matchingSourceTargetBoqItemIds.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var sourceLineItems = new List<EstLineItemEntity>();
			IEnumerable<EstLineItemEntity> targetLineItems = new List<EstLineItemEntity>();

			// Get valid target BoQ item IDs
			var targetBoqItemIds = matchingSourceTargetBoqItemIds.Values
																				  .Select(pair => pair.Item1)
																				  .Where(id => id.HasValue)
																				  .Select(id => id.Value)
																				  .ToList();

			var matchingTargetBoqItems = boqItemLogic.GetBoqItemsByBoqItemIds(targetBoqItemIds);

			var sourceBoqItemIds = matchingSourceTargetBoqItemIds.Keys
																				  .Select(pair => pair.Item1)
																				  .Where(id => id.HasValue)
																				  .Select(id => id.Value)
																				  .ToList();

			// Load source line items only if EstHeader ID is provided
			if (sourceBoqItemData.EstHeaderId.HasValue)
			{
				sourceLineItems = estimateMainLineItemLogic.GetListByFilter(e => e.EstHeaderFk == sourceBoqItemData.EstHeaderId &&
																								e.BoqHeaderFk == sourceBoqItemData.SourceBoqHeaderFk &&
																								sourceBoqItemIds.Contains((int)e.BoqItemFk) &&
																								e.LineItemType == 0 &&
																								e.IsTemp == false).ToList();
			}

			if (sourceLineItems.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var matchingSourceBoqItems = boqItemLogic.GetBoqItemsByBoqItemIds(sourceBoqItemIds);

			switch (data.ExistingEstimate)
			{
				case 1: // Overwrite
					targetLineItems = OverWriteTargetLineItemsFromPrjBoq(matchingSourceTargetBoqItemIds,
																								  matchingTargetBoqItems,
																								  sourceLineItems,
																								  targetProjectEntity,
																								  targetEstHeader,
																								  sourceBoqItemData,
																								  false);
					break;
				case 2: // Append
					{
						var estimateMainCalculatorData = new EstimateMainCalculatorData
						{
							EstHeaderFk = data.TargetEstHeaderId,
							SourceEstHeaderFk = sourceBoqItemData.EstHeaderId.Value,
							ProjectId = data.TargetProjectId,
							SourceProjectId = sourceBoqItemData.ProjectWicId,
							TargetEstHeaderJobId = targetEstHeader.LgmJobFk.HasValue ? targetEstHeader.LgmJobFk : null,
							TargetEstHeaderRubricCatId = targetEstHeader.RubricCategoryFk,
							IsCopyBudget = true,
							IsCopyCostTotalToBudget = true,
							IsCopyBaseCost = true,
							IsDeepCopyEstimate = false,
							BoqSourceTargetMapping = matchingSourceTargetBoqItemIds,
							TargetEstHeader = targetEstHeader
						};
						targetLineItems = new EstLineItemCopyLogicForBoq().CopyLineItemsFromSourcePrjBoq(estimateMainCalculatorData, sourceLineItems).GetTargetLineItems(data.TargetEstHeaderId);
					}
					
					break;
				case 3: // Ignore
					{
						// Ensure all collections are non-null
						matchingSourceBoqItems ??= new List<BoqItemEntity>();
						matchingTargetBoqItems ??= new List<BoqItemEntity>();

						// Get target BoQ items that do not have line items
						var targetBoqItemsWithoutLineItems = GetBoQItemsWithOutLineItems(data, matchingTargetBoqItems)
																							 ?? new List<IBoqItemEntity>();

						var matchedBoqItems = from sourceBoq in matchingSourceBoqItems
													 join targetBoq in targetBoqItemsWithoutLineItems
													 on sourceBoq.Reference equals targetBoq.Reference
													 where sourceBoq != null && targetBoq != null
													 select sourceBoq;

						var matchedSourceBoqItemIds = matchedBoqItems.Where(e => e != null).Select(e => e.Id).ToList();

						sourceLineItems = estimateMainLineItemLogic.GetListByFilter(e => e.EstHeaderFk == sourceBoqItemData.EstHeaderId &&
																										e.BoqHeaderFk == sourceBoqItemData.SourceBoqHeaderFk &&
																										matchedSourceBoqItemIds.Contains((int)e.BoqItemFk) &&
																										e.LineItemType == 0 &&
																										!e.IsTemp)?.ToList() ?? new List<EstLineItemEntity>();

						if (sourceLineItems.Count > 0)
						{
							var estimateMainCalcData = new EstimateMainCalculatorData
							{
								EstHeaderFk = data.TargetEstHeaderId,
								SourceEstHeaderFk = sourceBoqItemData.EstHeaderId.Value,
								ProjectId = data.TargetProjectId,
								SourceProjectId = sourceBoqItemData.ProjectWicId,
								TargetEstHeaderJobId = targetEstHeader.LgmJobFk.HasValue ? targetEstHeader.LgmJobFk : null,
								TargetEstHeaderRubricCatId = targetEstHeader.RubricCategoryFk,
								IsCopyBudget = true,
								IsCopyCostTotalToBudget = true,
								IsCopyBaseCost = true,
								IsDeepCopyEstimate = false,
								BoqSourceTargetMapping = matchingSourceTargetBoqItemIds,
								TargetEstHeader = targetEstHeader
							};
							targetLineItems = new EstLineItemCopyLogicForBoq().CopyLineItemsFromSourcePrjBoq(estimateMainCalcData, sourceLineItems).GetTargetLineItems(data.TargetEstHeaderId);
						}
					}
					break;
				default:
					break;
			}

			return targetLineItems;
		}


		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Normal Estimates, WIC Boq scenario
		/// Get source line items based on WIC Boq and copy line items from source to target.
		/// </summary>
		/// <param name="data"></param>
		/// <param name="sourceBoqItemData"></param>
		/// <param name="targetProjectEntity"></param> 
		/// <param name="targetEstHeader"></param>
		public IEnumerable<EstLineItemEntity> NormalEstGenerateFromWicBoQ(GenerateEstimateFromBoqData data, SourceBoQsData sourceBoqItemData, IProjectEntity targetProjectEntity, IEstHeaderEntity targetEstHeader)
		{
			// Initialize the targetLineItems collection
			IEnumerable<EstLineItemEntity> targetLineItems = Enumerable.Empty<EstLineItemEntity>();

			var boqItemLogic = new BoqItemLogic();
			var estimateMainLineItemLogic = new EstimateMainLineItemLogic();

			// Get matching source and target BoQ item IDs with headers
			var estMainBoQData = boqItemLogic.CompareBoqs(data.TargetProjectId,
																							null,
																							sourceBoqItemData.ProjectWicId,
																							data.TargetBoqHeaderFk,
																							sourceBoqItemData.SourceBoqHeaderFk,
																							sourceBoqItemData.FromBoqItemRefNo,
																							sourceBoqItemData.ToBoqItemRefNo,
																							data.OutlineSpecification,
																							data.BasUomFk);

			//Collection of Project Boq Item to source WIC boq Assemblies Mapping
			var prjBoqItem2WicAssemblies = estMainBoQData?.PrjBoqItem2WicAssembliesMapping;

			if (prjBoqItem2WicAssemblies != null && prjBoqItem2WicAssemblies.Count > 0)
			{
				// Extract matching target BoQ items
				var matchingTargetBoqItems = prjBoqItem2WicAssemblies.Where(e => e.BoqItem != null)
																					  .Select(e => e.BoqItem)
																					  .ToList() ?? new List<IBoqItemEntity>();

				switch (data.ExistingEstimate)
				{
					case 1: // Overwrite
						{
							// Handle overwrite case
							targetLineItems = OverWriteTargetLineItemsWicBoq(prjBoqItem2WicAssemblies,
																							 matchingTargetBoqItems,
																							 targetProjectEntity,
																							 targetEstHeader,
																							 false,
																							 data.MdcLineItemContextId);
						}
						break;
					case 2: // Append
						{
							// Create line items and resources, then fetch target line items
							estimateMainLineItemLogic.CreateLineItemsAndResources(data.MdcLineItemContextId, data.TargetProjectId, prjBoqItem2WicAssemblies);
							targetLineItems = estimateMainLineItemLogic.GetTargetLineItems(data.TargetEstHeaderId);
						}
						break;
					case 3: // Ignore
						{
							// Handle ignore case where target BoQ items don't have line items
							var targetBoqItemsWithOutLineItems = GetBoQItemsWithOutLineItems(data, matchingTargetBoqItems);

							// Filter the source PrjBoqItem2WicAssemblies, with target boq items which doesn't have line items
							var sourceFilteredItems = prjBoqItem2WicAssemblies.Where(e => targetBoqItemsWithOutLineItems.Contains(e.BoqItem)).ToList();
							if (sourceFilteredItems.Any())
							{
								// If there are filtered items, create line items and resources, then fetch target line items
								estimateMainLineItemLogic.CreateLineItemsAndResources(data.MdcLineItemContextId, data.TargetProjectId, sourceFilteredItems);
								targetLineItems = estimateMainLineItemLogic.GetTargetLineItems(data.TargetEstHeaderId);
							}
						}
						break;
					default:
						break;
				}
			}

			return targetLineItems;
		}

		/// <summary>
		/// Get Target BoQ items which doesn't have any line items
		/// </summary>
		/// <param name="data"></param>
		/// <param name="matchingTargetBoqItems"></param>
		/// <returns></returns>
		public List<IBoqItemEntity> GetBoQItemsWithOutLineItems(GenerateEstimateFromBoqData data, IEnumerable<IBoqItemEntity> matchingTargetBoqItems)
		{
			var estimateMainLineItemLogic = new EstimateMainLineItemLogic();

			// Null check for input parameters
			if (matchingTargetBoqItems == null)
			{
				return new List<IBoqItemEntity>();
			}

			var matchingBoqItemList = matchingTargetBoqItems.Where(x => x != null).ToList();

			if (!matchingBoqItemList.Any())
			{
				return new List<IBoqItemEntity>();
			}

			var targetBoQItemIds = matchingBoqItemList.Select(e => e.Id).ToHashSet(); // Faster lookup

			var targetBoQHeaderIds = matchingBoqItemList.Select(e => e.BoqHeaderFk).ToHashSet();

			// Fetch all matching line items for given EstHeader and BoQ info
			var allBoQLineItems = estimateMainLineItemLogic.GetListByFilter(e => e.EstHeaderFk == data.TargetEstHeaderId &&
																			e.BoqHeaderFk.HasValue &&
																			e.BoqItemFk.HasValue &&
																			targetBoQHeaderIds.Contains(e.BoqHeaderFk.Value) &&
																			targetBoQItemIds.Contains(e.BoqItemFk.Value) &&
																			e.LineItemType == 0 &&
																			!e.IsTemp)?.ToList() ?? new List<EstLineItemEntity>();

			// Group line items by composite key for faster checking
			var lineItemKeys = allBoQLineItems
									  .Select(e => Tuple.Create(e.BoqHeaderFk.Value, e.BoqItemFk.Value))  // Create tuple of two fields
									  .Distinct()
									  .ToHashSet();

			// Filter BoQ items that have no matching line items
			var targetBoqItemsWithOutLineItems = matchingBoqItemList
																 .Where(item => !lineItemKeys.Contains(Tuple.Create(item.BoqHeaderFk, item.Id))) // Use tuple for comparison
																 .ToList();

			return targetBoqItemsWithOutLineItems;
		}


		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Normal/Boq Driven Estimates, Project Boq, Overwrite scenario
		/// OverWrite Target LineItems With Source LineItems
		/// </summary>
		/// <param name="matchingSourceTargetBoqItemIds"></param>
		/// <param name="targetBoQItems"></param>
		/// <param name="sourceLineItems"></param>
		/// <param name="targetProjectEntity"></param>
		/// <param name="targetEstHeader"></param>
		/// <param name="sourceBoqItemData"></param>
		/// <param name="isBoqDriven"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> OverWriteTargetLineItemsFromPrjBoq(ConcurrentDictionary<Tuple<int?, int?>, Tuple<int?, int?>> matchingSourceTargetBoqItemIds,
			IEnumerable<IBoqItemEntity> targetBoQItems,
			IEnumerable<EstLineItemEntity> sourceLineItems,
			IProjectEntity targetProjectEntity,
			IEstHeaderEntity targetEstHeader,
			SourceBoQsData sourceBoqItemData,
			bool isBoqDriven)
		{
			var estimateMainLineItemLogic = new EstimateMainLineItemLogic();
			var estMainResourceLogic = new EstimateMainResourceLogic();
			var allTargetLineItems = new List<EstLineItemEntity>();
			var allTargetResources = new List<EstResourceEntity>();

			var packageLineItemIds = new HashSet<int>();

			if (sourceLineItems == null || !sourceLineItems.Any() || targetBoQItems == null)
			{
				return allTargetLineItems;
			}

			bool isSameProject = targetProjectEntity.Id == sourceBoqItemData.ProjectWicId;
			int targetEstHeaderId = targetEstHeader.Id;

			var sourceLineItemsList = sourceLineItems as IList<EstLineItemEntity> ?? sourceLineItems.ToList();
			if (sourceLineItemsList.Count == 0)
			{
				return allTargetLineItems;
			}

			var newLineItemIds = new Stack<int>(estimateMainLineItemLogic.SequenceManager.GetNextList("EST_LINE_ITEM", sourceLineItemsList.Count()));

			var sourceEstResources = estMainResourceLogic.GetResourcesByLineItems(sourceLineItemsList)?.ToList() ?? new List<EstResourceEntity>();

			// Prepare target BoQ header/item composite keys for fast lookup
			var targetBoqHeaderAndItemValues = new HashSet<string>(
				matchingSourceTargetBoqItemIds.Values.Select(k => $"{k.Item2}_{k.Item1}")
			);

			// Fetch all target line items in one go, filter by header and BoQ composite key
			var targetLineItems = estimateMainLineItemLogic.GetListByFilter(x => x.EstHeaderFk == targetEstHeaderId &&
			                                                                     targetBoqHeaderAndItemValues.Contains(x.BoqHeaderFk + "_" + x.BoqItemFk)
			)?.Where(x => x.LineItemType == 0 && x.IsTemp == false).ToList() ?? new List<EstLineItemEntity>();

			var deleteTargetResources = new List<EstResourceEntity>();

			if (targetLineItems.Count > 0)
			{
				// Skip target line items with PrcItemAssignments and system option 10095 is true
				var prcItemAssignmentLogic = Injector.Get<IPrcItemAssignmentLogic>();
				var targetPrcItemAssignments = prcItemAssignmentLogic?.GetEntitiesByEstimateInfo(targetEstHeaderId, targetLineItems.Select(e => e.Id))?.ToList();

				var isProtectContractPackage = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ProtectContractedPackageItemAssignment);

				if (targetPrcItemAssignments != null && targetPrcItemAssignments.Count > 0 && isProtectContractPackage)
				{
					packageLineItemIds = new HashSet<int>(targetPrcItemAssignments.Select(a => a.EstLineItemFk));
				}

				// Get target resources only once
				allTargetResources = estMainResourceLogic.GetResourcesByLineItems(targetLineItems)?.ToList() ?? new List<EstResourceEntity>();
				allTargetResources = allTargetResources.Flatten(e => e.ResourceChildren).ToList();
			}

			var allEstResourceEntities = new List<EstResourceEntity>();

			Dictionary<int, int> oldNewLineItemIds = new Dictionary<int, int>();

			var estCopyOptionLogic = new EstimateCopyOptionLogic(targetEstHeaderId);
			var estCopyOptionEntity = estCopyOptionLogic.GetCopyOptionByEstHeaderId(targetEstHeaderId);

			var allExtraTargetLineItems = new List<EstLineItemEntity>();

			// Iterate over the dictionary (Source -> Target mapping)
			foreach (var kvp in matchingSourceTargetBoqItemIds)
			{
				var sourceKey = kvp.Key;
				var targetValue = kvp.Value;

				// Get the source line item(s) from the already fetched list based on the source tuple
				var matchingSourceLineItems = sourceLineItemsList
					.Where(x => x.BoqHeaderFk == sourceKey.Item2 && x.BoqItemFk == sourceKey.Item1)
					.ToList();

				if (matchingSourceLineItems.Count == 0)
				{
					continue;
				}

				// Get the target line item(s) from the already fetched list based on the target tuple
				var matchingTargetLineItems = targetLineItems
					.Where(x => x.BoqHeaderFk == targetValue.Item2 && x.BoqItemFk == targetValue.Item1)
					.ToList();

				var targetBoqItem = targetBoQItems.FirstOrDefault(e => e.Id == targetValue.Item1 &&
				                                                       e.BoqHeaderFk == targetValue.Item2);

				var filteredDictionary = new ConcurrentDictionary<Tuple<int?, int?>, Tuple<int?, int?>>();
				filteredDictionary.TryAdd(kvp.Key, kvp.Value);

				var matchingSourceResources = sourceEstResources.Where(e => matchingSourceLineItems.Select(e => e.Id).Contains(e.EstLineItemFk) &&
				                                                            matchingSourceLineItems.Select(e => e.EstHeaderFk).Contains(e.EstHeaderFk));

				if (matchingTargetLineItems.Count == 0)
				{
					AddNewTargetLineItems(isBoqDriven, targetProjectEntity, targetEstHeader, matchingSourceLineItems,
						filteredDictionary, targetBoqItem, sourceBoqItemData, allTargetLineItems, newLineItemIds, allEstResourceEntities, matchingSourceResources);
				}
				else
				{
					//Exclude package line items from matching target line items
					var excludePackageLineItems = matchingTargetLineItems.Where(e => !packageLineItemIds.Contains(e.Id)).ToList();

					//Clear all target resources and copy new from source resources, comparision is difficult to overwrite by comparing code 
					deleteTargetResources.AddRange(allTargetResources.Where(e =>
						excludePackageLineItems.Select(e => e.Id).Contains(e.EstLineItemFk) &&
						excludePackageLineItems.Select(e => e.EstHeaderFk).Contains(e.EstHeaderFk)));

					var unmatchedTargetLineItems = new List<EstLineItemEntity>();

					var extraSourceLineItems = OverwriteMatchingLineItems(matchingSourceLineItems, matchingTargetLineItems, targetBoQItems, sourceEstResources,
																							targetProjectEntity, targetEstHeader, estCopyOptionLogic, estCopyOptionEntity, isSameProject,
																							oldNewLineItemIds, allTargetLineItems, allEstResourceEntities, packageLineItemIds, unmatchedTargetLineItems);

					HandleExtraLineItems(isBoqDriven, filteredDictionary, targetBoqItem, targetProjectEntity, targetEstHeader, sourceBoqItemData, allTargetLineItems,
												allExtraTargetLineItems, newLineItemIds, allEstResourceEntities, matchingSourceResources,
												packageLineItemIds, extraSourceLineItems, unmatchedTargetLineItems);
				}
			}
	
			CalculateLineItemsAndResources(targetEstHeaderId,targetProjectEntity.Id, allTargetLineItems, allEstResourceEntities, sourceLineItems, oldNewLineItemIds,
													 targetProjectEntity, sourceBoqItemData, isSameProject, allExtraTargetLineItems, deleteTargetResources);

			return allTargetLineItems;
		}

		private void AddNewTargetLineItems(bool isBoqDriven,
													  IProjectEntity targetProjectEntity,
													  IEstHeaderEntity targetEstHeader,
													  List<EstLineItemEntity> matchingSourceLineItems,
													  ConcurrentDictionary<Tuple<int?, int?>, Tuple<int?, int?>> filteredDictionary,
													  IBoqItemEntity targetBoqItem,
													  SourceBoQsData sourceBoqItemData,
													  List<EstLineItemEntity> allTargetLineItems,
													  Stack<int> newLineItemIds,
													  List<EstResourceEntity> allEstResourceEntities,
													  IEnumerable<EstResourceEntity> matchingSourceResources)
		{
			IEnumerable<EstLineItemEntity> newTargetLineItems = new List<EstLineItemEntity>();
			if (isBoqDriven)
			{
				var newLineItemsAndResources = CopyLineItemsNResources(targetProjectEntity.Id, targetEstHeader, matchingSourceLineItems,
					matchingSourceResources, newLineItemIds, targetBoqItem);

				if (newLineItemsAndResources.Item1 != null && newLineItemsAndResources.Item1.Any())
				{
					allTargetLineItems.AddRange(newLineItemsAndResources.Item1);
				}

				if (newLineItemsAndResources.Item2 != null && newLineItemsAndResources.Item2.Any())
				{
					allEstResourceEntities.AddRange(newLineItemsAndResources.Item2);
				}
			}
			else
			{
				var estimateMainCalculatorData = new EstimateMainCalculatorData
				{
					EstHeaderFk = targetEstHeader.Id,
					SourceEstHeaderFk = sourceBoqItemData.EstHeaderId.Value,
					ProjectId = targetProjectEntity.Id,
					SourceProjectId = sourceBoqItemData.ProjectWicId,
					TargetEstHeaderJobId = targetEstHeader.LgmJobFk.HasValue ? targetEstHeader.LgmJobFk : null,
					TargetEstHeaderRubricCatId = targetEstHeader.RubricCategoryFk,
					IsCopyBudget = true,
					IsCopyCostTotalToBudget = true,
					IsCopyBaseCost = true,
					IsDeepCopyEstimate = false,
					BoqSourceTargetMapping = filteredDictionary,
					TargetEstHeader = targetEstHeader
				};

				newTargetLineItems = new EstLineItemCopyLogicForBoq()
					.CopyLineItemsFromSourcePrjBoq(estimateMainCalculatorData, matchingSourceLineItems)
					.GetTargetLineItems(targetEstHeader.Id).ToList();
			}

			if (newTargetLineItems.Any())
			{
				allTargetLineItems.AddRange(newTargetLineItems);
			}
		}

		private List<EstLineItemEntity> OverwriteMatchingLineItems(List<EstLineItemEntity> matchingSourceLineItems,
															 List<EstLineItemEntity> matchingTargetLineItems,
															 IEnumerable<IBoqItemEntity> targetBoQItems,
															 List<EstResourceEntity> sourceEstResources,
															 IProjectEntity targetProjectEntity,
															 IEstHeaderEntity targetEstHeader,
															 EstimateCopyOptionLogic estCopyOptionLogic,
															 EstCopyOptionEntity estCopyOptionEntity,
															 bool isSameProject,
															 Dictionary<int, int> oldNewLineItemIds,
															 List<EstLineItemEntity> allTargetLineItems,
															 List<EstResourceEntity> allEstResourceEntities,
															 HashSet<int> packageLineItemIds,
															 List<EstLineItemEntity> unmatchedTargetLineItems)
		{
			var unmatchedSourceLineItems = new List<EstLineItemEntity>();

			// Use a queue so each source line item is used only once
			var availableSourceLineItems = new Queue<EstLineItemEntity>(matchingSourceLineItems);

			foreach (var targetLineItem in matchingTargetLineItems)
			{
				if (availableSourceLineItems.Count == 0)
				{
					// No source available → unmatched target
					unmatchedTargetLineItems.Add(targetLineItem);
					continue;
				}

				if (packageLineItemIds.Contains(targetLineItem.Id))
				{
					//Skip package line items and system option 10095 is true
					continue;
				}

				var sourceLineItem = availableSourceLineItems.Dequeue();

				var sourceResources = sourceEstResources
					.Where(e => e.EstLineItemFk == sourceLineItem.Id && e.EstHeaderFk == sourceLineItem.EstHeaderFk)
					.OrderBy(e => e.Code).ToList();

				if (sourceLineItem != null || targetLineItem != null)
				{
					oldNewLineItemIds.TryAdd(sourceLineItem.Id, targetLineItem.Id);
				}

				var targetBoqItem = targetBoQItems.FirstOrDefault(e =>
					e.BoqHeaderFk == targetLineItem.BoqHeaderFk && e.Id == targetLineItem.BoqItemFk);

				// Overwrite target line item with the source line item
				var updatedTargetLineItem = OverWriteFromSourceLineItemToTarget(sourceLineItem, targetLineItem, targetBoqItem, false);

				var targetResources = CopyLineItemResources(targetProjectEntity.Id, targetLineItem, targetEstHeader, sourceResources);

				if (targetResources != null)
				{
					targetLineItem.Resources = targetResources.Select(e => (IScriptEstResource)e).ToList();
				}

				allEstResourceEntities.AddRange(targetResources);

				if (updatedTargetLineItem != null)
				{
					estCopyOptionLogic.CheckLineItemLeadingStructures(updatedTargetLineItem);
					if (!isSameProject)
					{
						if (estCopyOptionEntity.LiBoq.Value)
						{
							updatedTargetLineItem.BoqItemFk = targetBoqItem.Id;
							updatedTargetLineItem.BoqHeaderFk = targetBoqItem.BoqHeaderFk;
						}
						else
						{
							updatedTargetLineItem.BoqItemFk = null;
							updatedTargetLineItem.BoqHeaderFk = null;
							updatedTargetLineItem.BoqSplitQuantityFk = null;
						}
					}
					allTargetLineItems.Add(updatedTargetLineItem);
				}

				// Now collect any unused source line items
				unmatchedSourceLineItems.AddRange(availableSourceLineItems);
			}

			return unmatchedSourceLineItems;
		}

		private void HandleExtraLineItems(bool isBoqDriven,
													 ConcurrentDictionary<Tuple<int?, int?>, Tuple<int?, int?>> filteredDictionary,
													 IBoqItemEntity filteredTargetBoqItem,
													 IProjectEntity targetProjectEntity,
													 IEstHeaderEntity targetEstHeader,
													 SourceBoQsData sourceBoqItemData,
													 List<EstLineItemEntity> allTargetLineItems,
													 List<EstLineItemEntity> allExtraTargetLineItems,
													 Stack<int> newLineItemIds,
													 List<EstResourceEntity> allEstResourceEntities,
													 IEnumerable<EstResourceEntity> matchingSourceResources,
													 HashSet<int> packageLineItemIds,
													 List<EstLineItemEntity> extraSourceLineItems,
													 List<EstLineItemEntity> unmatchedTargetLineItems)
		{
			if (extraSourceLineItems != null && extraSourceLineItems.Count > 0)
			{
				AddNewTargetLineItems(isBoqDriven, targetProjectEntity, targetEstHeader, extraSourceLineItems.ToList(),
					 filteredDictionary, filteredTargetBoqItem, sourceBoqItemData, allTargetLineItems, newLineItemIds, allEstResourceEntities, matchingSourceResources);
			}

			if (unmatchedTargetLineItems != null && unmatchedTargetLineItems.Count > 0)
			{
				//Skip package line items
				var deleteTargetExtraLineItems = unmatchedTargetLineItems.Where(e => !packageLineItemIds.Contains(e.Id)).ToList();

				allExtraTargetLineItems.AddRange(deleteTargetExtraLineItems);
			}
		}

		private void CalculateLineItemsAndResources(int targetEstHeaderId,
			                                         int targetProjectId,
			                                         List<EstLineItemEntity> allTargetLineItems,
			                                         List<EstResourceEntity> allEstResourceEntities,
			                                         IEnumerable<EstLineItemEntity> sourceLineItems,
			                                         Dictionary<int, int> oldNewLineItemIds,
			                                         IProjectEntity targetProjectEntity,
			                                         SourceBoQsData sourceBoqItemData,
																  bool isSameProject,
																  List<EstLineItemEntity> allExtraTargetLineItems,
			                                         List<EstResourceEntity> deleteTargetResources)
		{
			if (allTargetLineItems.Any())
			{
				new EstLineItemUpdateHelper(targetEstHeaderId, targetProjectId, new EstLineItemUpdateOption
				{
					IsCalculateDetail = true,
					IsUpdateRate = false,
					IsUpdateAssembly = false,
					ToSave = false,
					ConsiderIsRate = true,
					IsUpdateExchangeRate = false
				}).CalculateLineItemsOfEstimate(allTargetLineItems);
			}

			var estimateMainResourceLogic = new EstimateMainResourceLogic();
			var estimateMainLineItemLogic = new EstimateMainLineItemLogic();

			using (var transaction = TransactionScopeFactory.Create())
			{
				if (deleteTargetResources.Count > 0)
				{
					estimateMainResourceLogic.Delete(deleteTargetResources);
				}

				if (allExtraTargetLineItems.Count > 0)
				{
					estimateMainLineItemLogic.DeleteLineItems(allExtraTargetLineItems);
				}

				if (allTargetLineItems.Count > 0)
				{
					estimateMainLineItemLogic.BulkSave(allTargetLineItems);
				}

				var flatAllResources = allEstResourceEntities.Flatten(e => e.ResourceChildren).ToList();
				if (flatAllResources.Count > 0)
				{
					estimateMainResourceLogic.BulkSave(flatAllResources);
				}

				SaveRulesAndParams(allTargetLineItems, targetEstHeaderId, sourceLineItems, oldNewLineItemIds,
										targetProjectEntity, sourceBoqItemData, isSameProject);

				transaction.Complete();
			}
		}

		private void SaveRulesAndParams(List<EstLineItemEntity> allTargetLineItems,
												  int targetEstHeaderId,
												  IEnumerable<EstLineItemEntity> sourceLineItems,
												  Dictionary<int, int> oldNewLineItemIds,
												  IProjectEntity targetProjectEntity,
												  SourceBoQsData sourceBoqItemData,
												  bool isSameProject)
		{
			var targetLineItemIds = allTargetLineItems.Select(e => e.Id).ToList();
			var estimateRuleLineItemLogic = new EstimateRuleLineItemLogic();
			var lineItem2EstRulesDelete = estimateRuleLineItemLogic.GetByFilter(e => targetLineItemIds.Contains(e.EstLineItemFk) && e.EstHeaderFk == targetEstHeaderId);
			if (lineItem2EstRulesDelete != null && lineItem2EstRulesDelete.Any())
			{
				estimateRuleLineItemLogic.DeleteItems(lineItem2EstRulesDelete);
			}

			var estimateParameterLineItemLogic = new EstimateParameterLineItemLogic();
			var lineItem2ParamsDelete = estimateParameterLineItemLogic.GetByFilter(e => targetLineItemIds.Contains(e.EstLineItemFk) && e.EstHeaderFk == targetEstHeaderId);
			if (lineItem2ParamsDelete != null && lineItem2ParamsDelete.Any())
			{
				estimateParameterLineItemLogic.DeleteItems(lineItem2ParamsDelete);
			}

			if (!isSameProject)
			{
				new EstimateRulePrjEstRuleLogic().CopyRuleByProject(sourceBoqItemData.ProjectWicId, targetProjectEntity.Id);
				new EstimateParameterPrjParamLogic().CopyParamByProject(sourceBoqItemData.ProjectWicId, targetProjectEntity.Id);
			}

			var sourceEstHeaderId = sourceLineItems.Select(e => e.EstHeaderFk).FirstOrDefault();
			estimateRuleLineItemLogic.CopyNSaveRuleFrmLineItems(sourceEstHeaderId, oldNewLineItemIds, targetEstHeaderId);
			estimateParameterLineItemLogic.CopyNSaveParamFrmLineItems(sourceEstHeaderId, oldNewLineItemIds, targetEstHeaderId);

			var estimateRuleHeaderLogic = new EstimateRuleHeaderLogic();
			var estimateParameterHeaderLogic = new EstimateParameterHeaderLogic();
			if (!isSameProject)
			{
				estimateRuleHeaderLogic.CopyNSaveRuleByProject(sourceEstHeaderId, targetEstHeaderId, sourceBoqItemData.ProjectWicId, targetProjectEntity.Id, true);
				estimateParameterHeaderLogic.CopyParamByProject(sourceEstHeaderId, targetEstHeaderId, sourceBoqItemData.ProjectWicId, targetProjectEntity.Id, true);
			}
			else
			{
				estimateRuleHeaderLogic.CopyNSaveRuleByHeader(sourceEstHeaderId, targetEstHeaderId, true);
				estimateParameterHeaderLogic.CopyNSaveParamByHeader(sourceEstHeaderId, targetEstHeaderId);
			}
		}

		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Normal/Boq Driven Estimates, WIC Boq, Overwrite scenario
		/// </summary>
		/// <param name="prjBoqItem2WicAssemblies"></param>
		/// <param name="targetBoQItems"></param>
		/// <param name="targetProjectEntity"></param>
		/// <param name="targetEstHeaderEntity"></param>
		/// <param name="isBoqDriven"></param>
		/// <param name="mdcLineItemContextId"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> OverWriteTargetLineItemsWicBoq(ICollection<IPrjBoqItem2WicAssemblies> prjBoqItem2WicAssemblies,
																									IEnumerable<IBoqItemEntity> targetBoQItems,
																									IProjectEntity targetProjectEntity,
																									IEstHeaderEntity targetEstHeaderEntity,
																									bool isBoqDriven,
																									int mdcLineItemContextId)
		{
			var estimateMainLineItemLogic = new EstimateMainLineItemLogic();
			var estMainResourceLogic = new EstimateMainResourceLogic();
			var allTargetLineItems = new List<EstLineItemEntity>();
			var allEstResourceEntities = new List<EstResourceEntity>();
			var allExtraTargetLineItems = new List<EstLineItemEntity>();
			var deleteTargetResources = new List<EstResourceEntity>();
			var oldNewLineItemIds = new Dictionary<int, int>();
			var allTargetResources = new List<EstResourceEntity>();

			var packageLineItemIds = new HashSet<int>();

			if (targetBoQItems == null || prjBoqItem2WicAssemblies == null || prjBoqItem2WicAssemblies.Count == 0)
			{
				return allTargetLineItems;
			}

			int targetEstHeaderId = targetEstHeaderEntity.Id;

			// Prepare target BoQ header/item composite keys for fast lookup
			var targetBoqHeaderAndItemValues = new HashSet<string>(targetBoQItems.Select(k => $"{k.BoqHeaderFk}_{k.Id}"));

			// Fetch all target line items in one go, filter by header and BoQ composite key
			var targetLineItems = estimateMainLineItemLogic.GetListByFilter(x => x.EstHeaderFk == targetEstHeaderId &&
														targetBoqHeaderAndItemValues.Contains(x.BoqHeaderFk + "_" + x.BoqItemFk)
												  )?.Where(x => x.LineItemType == 0 && x.IsTemp == false).ToList() ?? new List<EstLineItemEntity>();

			if (targetLineItems.Count > 0)
			{
				// Skip target line items with PrcItemAssignments
				var prcItemAssignmentLogic = Injector.Get<IPrcItemAssignmentLogic>();
				var targetPrcItemAssignments = prcItemAssignmentLogic?.GetEntitiesByEstimateInfo(targetEstHeaderId, targetLineItems.Select(e => e.Id))?.ToList();

				var isProtectContractPackage = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ProtectContractedPackageItemAssignment);

				if (targetPrcItemAssignments != null && targetPrcItemAssignments.Count > 0 && isProtectContractPackage)
				{
					packageLineItemIds = new HashSet<int>(targetPrcItemAssignments.Select(a => a.EstLineItemFk));
				}

				 // Get target resources only once
				 allTargetResources = estMainResourceLogic.GetResourcesByLineItems(targetLineItems)?.ToList() ?? new List<EstResourceEntity>();
				 allTargetResources = allTargetResources.Flatten(e => e.ResourceChildren).ToList();
			}

			/* get assembly entities */
			var assemblyIds = prjBoqItem2WicAssemblies
									 .Where(e => e.WicAssemblies != null)
									 .SelectMany(e => e.WicAssemblies)
									 .Select(e => e.Item2)
									 .Distinct()
									 .ToList();

			if (assemblyIds.Count == 0)
			{
				return allTargetLineItems;
			}

			var assemblyEntities = estimateMainLineItemLogic.GetPrjAssemblyByMasterAssemblyId(assemblyIds, targetProjectEntity.Id);
			var assembly2LineItemLogic = new AssemblyTemplate2LineItemLogic(targetProjectEntity.Id);

			// Prepare target resources only once
			var sourceEstResources = estMainResourceLogic.GetResourcesByLineItems(assemblyEntities)?.ToList() ?? new List<EstResourceEntity>();

			var newLineItemIds = new Stack<int>(estimateMainLineItemLogic.SequenceManager.GetNextList("EST_LINE_ITEM", assemblyEntities.Count()));

			// Iterate over the collection (Source -> Target mapping)
			foreach (var prjBoqItem2WicAssembly in prjBoqItem2WicAssemblies)
			{
				if (prjBoqItem2WicAssembly?.WicAssemblies == null)
				{
					continue;
				}

				var targetBoqItem = targetBoQItems.FirstOrDefault(e => e.BoqHeaderFk == prjBoqItem2WicAssembly.BoqItem.BoqHeaderFk && e.Id == prjBoqItem2WicAssembly.BoqItem.Id);

				var matchingSourceWicAssemblies = GetMatchingSourceWicAssemblies(prjBoqItem2WicAssembly, assemblyEntities);

				//  Get the target line item(s) from the already fetched list based on the target tuple
				var matchingTargetLineItems = targetLineItems
					.Where(x => x.BoqHeaderFk == prjBoqItem2WicAssembly.BoqItem.BoqHeaderFk && x.BoqItemFk == prjBoqItem2WicAssembly.BoqItem.Id)
					.ToList();

				var matchingSourceResources = sourceEstResources.Where(e => matchingSourceWicAssemblies.Select(assembly => assembly.Id).Contains(e.EstLineItemFk) &&
				                                                            matchingSourceWicAssemblies.Select(assembly => assembly.EstHeaderFk).Contains(e.EstHeaderFk));

				if (matchingTargetLineItems == null || matchingTargetLineItems.Count == 0)
				{
					HandleNoTargetLineItems(
						isBoqDriven, targetProjectEntity, targetEstHeaderEntity,
						matchingSourceWicAssemblies, matchingSourceResources, newLineItemIds, prjBoqItem2WicAssembly.BoqItem,
						mdcLineItemContextId, allTargetLineItems, allEstResourceEntities);
				}
				else
				{
					//Exclude package line items from matching target line items
					var excludePackageLineItems = matchingTargetLineItems.Where(e => !packageLineItemIds.Contains(e.Id)).ToList();

					// clear all target resources and copy new from source resources, comparision is difficult to overwrite by comparing code 
					deleteTargetResources.AddRange(allTargetResources.Where(e=>
						excludePackageLineItems.Select(e=>e.Id).Contains(e.EstLineItemFk) &&
						excludePackageLineItems.Select(e => e.EstHeaderFk).Contains(e.EstHeaderFk)));

					var unmatchedTargetLineItems = new List<EstLineItemEntity>();

					var extraSourceWicAssemblies = OverwriteMatchingLineItemsWic(matchingSourceWicAssemblies, matchingTargetLineItems, targetBoqItem, sourceEstResources,
																																targetProjectEntity, targetEstHeaderEntity, oldNewLineItemIds, allTargetLineItems, allEstResourceEntities, packageLineItemIds, unmatchedTargetLineItems);

					HandleExtraSourceAndTargetLineItemsWic(
						isBoqDriven, targetProjectEntity, targetEstHeaderEntity, matchingTargetLineItems, targetBoqItem, sourceEstResources,
						newLineItemIds, prjBoqItem2WicAssembly.BoqItem, mdcLineItemContextId, allTargetLineItems, allEstResourceEntities, allExtraTargetLineItems, packageLineItemIds, unmatchedTargetLineItems, extraSourceWicAssemblies);
				}
			}

			UpdateLineItemsAndResources(targetEstHeaderId, targetProjectEntity.Id, allTargetLineItems);

			SaveRulesAndParamsWic(allTargetLineItems, targetEstHeaderId, allEstResourceEntities, deleteTargetResources, assembly2LineItemLogic, allExtraTargetLineItems);

			return allTargetLineItems;
		}

		private List<EstLineItemEntity> GetMatchingSourceWicAssemblies(
			IPrjBoqItem2WicAssemblies prjBoqItem2WicAssembly,
			IEnumerable<EstLineItemEntity> assemblyEntities)
		{
			var result = new List<EstLineItemEntity>();
			foreach (var wicAssembly in prjBoqItem2WicAssembly.WicAssemblies)
			{
				var assemblyEntity = assemblyEntities.FirstOrDefault(e => e.Id == wicAssembly.Item2 && e.EstHeaderFk == wicAssembly.Item1);
				var prjAssemblyEntity = assemblyEntities.FirstOrDefault(e =>
					e.EstAssemblyFk.HasValue && e.EstHeaderAssemblyFk.HasValue &&
					e.EstAssemblyFk == wicAssembly.Item2 && e.EstHeaderAssemblyFk == wicAssembly.Item1);
				if (prjAssemblyEntity != null)
				{
					assemblyEntity = prjAssemblyEntity;
				}
				
				if (assemblyEntity == null || assemblyEntity.EstAssemblyCatFk == null)
				{
					continue;
				}
					
				result.Add(assemblyEntity);
			}
			return result;
		}


		private void HandleNoTargetLineItems(bool isBoqDriven,
														 IProjectEntity targetProjectEntity,
														 IEstHeaderEntity targetEstHeaderEntity,
														 List<EstLineItemEntity> matchingSourceWicAssemblies,
														 IEnumerable<EstResourceEntity> matchingSourceResources,
														 Stack<int> newLineItemIds,
														 IBoqItemEntity targetBoqItem,
														 int mdcLineItemContextId,
														 List<EstLineItemEntity> allTargetLineItems,
														 List<EstResourceEntity> allEstResourceEntities)
		{
			if (isBoqDriven)
			{
				var newLineItemsAndResources = CopyLineItemsNResources(targetProjectEntity.Id, targetEstHeaderEntity, matchingSourceWicAssemblies,
																												  matchingSourceResources, newLineItemIds, targetBoqItem);

				if (newLineItemsAndResources.Item1 != null && newLineItemsAndResources.Item1.Any())
				{
					allTargetLineItems.AddRange(newLineItemsAndResources.Item1);
				}
					
				if (newLineItemsAndResources.Item2 != null && newLineItemsAndResources.Item2.Any())
				{
					allEstResourceEntities.AddRange(newLineItemsAndResources.Item2);
				}
			}
			else
			{
				ICollection<IPrjBoqItem2WicAssemblies> filteredPrjBoqItem2WicAssemblies = new List<IPrjBoqItem2WicAssemblies>();

				var assemblyFks = new List<Tuple<int, int, decimal>>();

				foreach (var sourceWicAssembly in matchingSourceWicAssemblies)
				{
					if (sourceWicAssembly.EstHeaderAssemblyFk == null || sourceWicAssembly.EstAssemblyFk == null)
					{
						assemblyFks.Add(new Tuple<int, int, decimal>(
							sourceWicAssembly.EstHeaderFk,
							sourceWicAssembly.Id,
							sourceWicAssembly.Quantity));
					}
					else
					{
						assemblyFks.Add(new Tuple<int, int, decimal>(
							sourceWicAssembly.EstHeaderAssemblyFk.Value,
							sourceWicAssembly.EstAssemblyFk.Value,
							sourceWicAssembly.Quantity));
					}
				}

				filteredPrjBoqItem2WicAssemblies.Add(new PrjBoqItem2WicAssemblies((BoqItemEntity)targetBoqItem, assemblyFks));

				var estimateMainLineItemLogic = new EstimateMainLineItemLogic();

				estimateMainLineItemLogic.CreateLineItemsAndResources(mdcLineItemContextId, targetProjectEntity.Id, filteredPrjBoqItem2WicAssemblies);
				var newTargetLineItems =  estimateMainLineItemLogic.GetTargetLineItems(targetEstHeaderEntity.Id).ToList();

				if (newTargetLineItems != null && newTargetLineItems.Any())
				{
					allTargetLineItems.AddRange(newTargetLineItems);
				}
			}
		}

		private List<EstLineItemEntity> OverwriteMatchingLineItemsWic(List<EstLineItemEntity> matchingSourceWicAssemblies,
							                            List<EstLineItemEntity> matchingTargetLineItems,
							                            IBoqItemEntity targetBoqItem,
							                            List<EstResourceEntity> sourceEstResources,
							                            IProjectEntity targetProjectEntity,
							                            IEstHeaderEntity targetEstHeaderEntity,
							                            Dictionary<int, int> oldNewLineItemIds,
							                            List<EstLineItemEntity> allTargetLineItems,
							                            List<EstResourceEntity> allEstResourceEntities,
							                            HashSet<int> packageLineItemIds,
							                            List<EstLineItemEntity> unmatchedTargetLineItems)
		{
			// Create a lookup from composite key (Id, EstHeaderFk) to source assembly
			var assemblyLookup = matchingSourceWicAssemblies
				.ToDictionary(a => (a.Id, a.EstHeaderFk), a => a);

			// Track matched source assembly IDs
			var matchedSourceAssemblyIds = new HashSet<int>();

			foreach (var targetLineItem in matchingTargetLineItems)
			{
				if (packageLineItemIds.Contains(targetLineItem.Id))
				{
					// Skip package line items
					continue;
				}

				if (targetLineItem.EstAssemblyFk != null && targetLineItem.EstHeaderAssemblyFk != null
				                                         && assemblyLookup.TryGetValue((targetLineItem.EstAssemblyFk.Value, targetLineItem.EstHeaderAssemblyFk.Value), out var sourceAssembly))
				{
					if (sourceAssembly == null)
					{
						continue;
					}
					matchedSourceAssemblyIds.Add(sourceAssembly.Id);
					oldNewLineItemIds.TryAdd(sourceAssembly.Id, targetLineItem.Id);

					var sourceResources = sourceEstResources
						.Where(e => e.EstLineItemFk == sourceAssembly.Id && e.EstHeaderFk == sourceAssembly.EstHeaderFk)
						.ToList();

					var updatedTargetLineItem = OverWriteFromSourceLineItemToTarget(sourceAssembly, targetLineItem, targetBoqItem, true);

					var targetResources = CopyLineItemResources(targetProjectEntity.Id, targetLineItem, targetEstHeaderEntity, sourceResources);
					if (targetResources != null)
					{
						targetLineItem.Resources = targetResources.Select(e => (IScriptEstResource)e).ToList();
						allEstResourceEntities.AddRange(targetResources);
					}

					allTargetLineItems.Add(updatedTargetLineItem);
				}
				else
				{
					unmatchedTargetLineItems.Add(targetLineItem);
				}
			}

			// Source assemblies not matched to any target line item
			// Source wic assemblies, are more than target line items, needs to be created(new line items)
			var extraSourceAssemblies = matchingSourceWicAssemblies
				.Where(sa => !matchedSourceAssemblyIds.Contains(sa.Id))
				.ToList();

			return extraSourceAssemblies;
		}

		private void HandleExtraSourceAndTargetLineItemsWic(bool isBoqDriven,
																			 IProjectEntity targetProjectEntity,
																			 IEstHeaderEntity targetEstHeaderEntity,
																			 List<EstLineItemEntity> matchingTargetLineItems,
																			 IBoqItemEntity targetBoqItem,
																			 List<EstResourceEntity> sourceEstResources,
																			 Stack<int> newLineItemIds,
																			 IBoqItemEntity prjBoqItem,
																			 int mdcLineItemContextId,
																			 List<EstLineItemEntity> allTargetLineItems,
																			 List<EstResourceEntity> allEstResourceEntities,
																			 List<EstLineItemEntity> allExtraTargetLineItems,
																			 HashSet<int> packageLineItemIds,
		                                                    List<EstLineItemEntity> unmatchedTargetLineItems,
																			 List<EstLineItemEntity> extraSourceAssemblies)
		{
			if (extraSourceAssemblies != null && extraSourceAssemblies.Count > 0 )
			{
				if (isBoqDriven)
				{
					var extraSourceResources = sourceEstResources.Where(e =>
						extraSourceAssemblies.Select(assembly => assembly.Id).Contains(e.EstLineItemFk) &&
						extraSourceAssemblies.Select(assembly => assembly.Id).Contains(e.EstHeaderFk));

					var newLineItemsAndResources = CopyLineItemsNResources(targetProjectEntity.Id, targetEstHeaderEntity, extraSourceAssemblies,
																													  extraSourceResources, newLineItemIds, prjBoqItem);

					if (newLineItemsAndResources.Item1 != null && newLineItemsAndResources.Item1.Any())
					{
						allTargetLineItems.AddRange(newLineItemsAndResources.Item1);
					}
						
					if (newLineItemsAndResources.Item2 != null && newLineItemsAndResources.Item2.Any())
					{
						allEstResourceEntities.AddRange(newLineItemsAndResources.Item2);
					}
				}
				else
				{
					ICollection<IPrjBoqItem2WicAssemblies> filteredWicAssemblies = new List<IPrjBoqItem2WicAssemblies>();
					var assemblyFks = new List<Tuple<int, int, decimal>>();

					foreach (var sourceWicAssembly in extraSourceAssemblies)
					{
						if (sourceWicAssembly.EstHeaderAssemblyFk == null || sourceWicAssembly.EstAssemblyFk == null)
						{
							assemblyFks.Add(new Tuple<int, int, decimal>(
								sourceWicAssembly.EstHeaderFk,
								sourceWicAssembly.Id,
								sourceWicAssembly.Quantity));
						}
						else
						{
							assemblyFks.Add(new Tuple<int, int, decimal>(
								sourceWicAssembly.EstHeaderAssemblyFk.Value,
								sourceWicAssembly.EstAssemblyFk.Value,
								sourceWicAssembly.Quantity));
						}
					}

					filteredWicAssemblies.Add(new PrjBoqItem2WicAssemblies((BoqItemEntity)targetBoqItem, assemblyFks));

					var estimateMainLineItemLogic = new EstimateMainLineItemLogic();

					estimateMainLineItemLogic.CreateLineItemsAndResources(mdcLineItemContextId, targetProjectEntity.Id, filteredWicAssemblies);
					var newTargetLineItems = estimateMainLineItemLogic.GetTargetLineItems(targetEstHeaderEntity.Id).ToList();

					if (newTargetLineItems != null && newTargetLineItems.Any())
					{
						allTargetLineItems.AddRange(newTargetLineItems);
					}
				}
			}

			if (unmatchedTargetLineItems!= null && unmatchedTargetLineItems.Count > 0)
			{
				//Skip package line items
				var deleteTargetExtraLineItems = unmatchedTargetLineItems.Where(e => !packageLineItemIds.Contains(e.Id)).ToList();

				allExtraTargetLineItems.AddRange(deleteTargetExtraLineItems);
			}
		}

		private void UpdateLineItemsAndResources(int targetEstHeaderId,
															  int targetProjectId,
															  List<EstLineItemEntity> allTargetLineItems)
		{
			if (allTargetLineItems.Any())
			{
				new EstLineItemUpdateHelper(targetEstHeaderId, targetProjectId, new EstLineItemUpdateOption()
				{
					IsCalculateDetail = true,
					IsUpdateRate = false,
					IsUpdateAssembly = false,
					ToSave = false,
					ConsiderIsRate = true,
					IsUpdateExchangeRate = false
				}).CalculateLineItemsOfEstimate(allTargetLineItems);
			}
		}

		private void SaveRulesAndParamsWic(List<EstLineItemEntity> allTargetLineItems,
													  int targetEstHeaderId,
													  List<EstResourceEntity> allEstResourceEntities,
													  List<EstResourceEntity> deleteTargetResources,
													  AssemblyTemplate2LineItemLogic assembly2LineItemLogic,
													  List<EstLineItemEntity> allExtraTargetLineItems)
		{
			var estimateMainLineItemLogic = new EstimateMainLineItemLogic();
			var estMainResourceLogic = new EstimateMainResourceLogic();

			using (var transaction = TransactionScopeFactory.Create())
			{
				if (deleteTargetResources.Count > 0)
				{
					estMainResourceLogic.Delete(deleteTargetResources);
				}

				if (allExtraTargetLineItems.Count > 0)
				{
					estimateMainLineItemLogic.DeleteLineItems(allExtraTargetLineItems);
				}

				if (allTargetLineItems.Count > 0)
				{
					estimateMainLineItemLogic.BulkSave(allTargetLineItems);
				}

				var flatAllResources = allEstResourceEntities.Flatten(e => e.ResourceChildren).ToList();
				if (flatAllResources.Count > 0)
				{
					estMainResourceLogic.BulkSave(flatAllResources);
				}

				var targetLineItemIds = allTargetLineItems.Select(e => e.Id).ToList();

				var estimateRuleLineItemLogic = new EstimateRuleLineItemLogic();
				var lineItem2EstRulesDelete = estimateRuleLineItemLogic.GetByFilter(e => targetLineItemIds.Contains(e.EstLineItemFk) && e.EstHeaderFk == targetEstHeaderId);
				if (lineItem2EstRulesDelete != null && lineItem2EstRulesDelete.Any())
				{
					estimateRuleLineItemLogic.DeleteItems(lineItem2EstRulesDelete);
				}

				var estimateParameterLineItemLogic = new EstimateParameterLineItemLogic();
				var lineItem2ParamsDelete = estimateParameterLineItemLogic.GetByFilter(e => targetLineItemIds.Contains(e.EstLineItemFk) && e.EstHeaderFk == targetEstHeaderId);
				if (lineItem2ParamsDelete != null && lineItem2ParamsDelete.Any())
				{
					estimateParameterLineItemLogic.DeleteItems(lineItem2ParamsDelete);
				}

				foreach (var lineItemEntity in allTargetLineItems)
				{
					assembly2LineItemLogic.AddLineItemForCopyWithAssemblyKey(lineItemEntity, lineItemEntity.EstAssemblyFk, lineItemEntity.EstHeaderAssemblyFk);
				}

				assembly2LineItemLogic.DoCopyRulesAndParams();
				assembly2LineItemLogic.Save();

				transaction.Complete();
			}
		}
		/// <summary>
		/// Generate Estimate from BoQ Reference, Wizard logic for, Boq Driven Estimates.
		/// </summary>
		/// <param name="data"></param>
		/// <param name="projectEntity"></param>
		/// <param name="targetEstHeader"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> BoqDrivenGenerateEstimateFromBoQs(GenerateEstimateFromBoqData data, IProjectEntity projectEntity, IEstHeaderEntity targetEstHeader)
		{
			List<EstLineItemEntity> targetLineItems = new List<EstLineItemEntity>();

			var boqItemLogic = BoqContext.BoqItemLogic;

			// Handle Project and WIC BoQ types
			foreach (var sourceBoqItemData in data.SourceBoQsData.Where(x => x?.SourceBoqHeaderFk != null))
			{
				if (sourceBoqItemData.Type == 1) // Project BoQ
				{
					targetLineItems.AddRange(BoqDrivenEstGenerateFromProjectBoQ(data, sourceBoqItemData, projectEntity, targetEstHeader)?.ToList());
				}
				else // WIC BoQ
				{
					targetLineItems.AddRange(BoqDrivenEstGenerateFromWicBoQ(data, sourceBoqItemData, projectEntity, targetEstHeader)?.ToList());
				}
			}

			return targetLineItems;
		}

		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Boq Driven Estimates, Project Boq scenario
		/// BoQ Driven Estimate, Get source line items based on project Estimate Header and copy line items from source to target.
		/// </summary>
		/// <param name="data"></param>
		/// <param name="sourceBoqItemData"></param>
		/// <param name="targetProjectEntity"></param>
		/// <param name="targetEstHeaderEntity"></param>
		public IEnumerable<EstLineItemEntity> BoqDrivenEstGenerateFromProjectBoQ(GenerateEstimateFromBoqData data, SourceBoQsData sourceBoqItemData, IProjectEntity targetProjectEntity, IEstHeaderEntity targetEstHeaderEntity)
		{
			var boqItemLogic = new BoqItemLogic();

			// Get matching source and target BoQ item IDs with headers
			var estMainBoQData = boqItemLogic.CompareBoqs(data.TargetProjectId,
														sourceBoqItemData.ProjectWicId,
														null,
														data.TargetBoqHeaderFk,
														sourceBoqItemData.SourceBoqHeaderFk,
														sourceBoqItemData.FromBoqItemRefNo,
														sourceBoqItemData.ToBoqItemRefNo,
														data.OutlineSpecification,
														data.BasUomFk);

			//Dictionary Key is source boq items and Value is TargetBoqItems
			//ConcurrentDictionary<Tuple<int?, int?>, Tuple<int?, int?>> matchingSourceTargetBoqItemIds
			//Tuple<SourceBoqItem Id,Source BoqItemHeaderFk>,Tuple<TargetBoqItem Id, Target BoqHeaderFk>
			var matchingSourceTargetBoqItemIds = estMainBoQData?.Source2TargetPrjBoqItemRefMap;

			if (matchingSourceTargetBoqItemIds == null || matchingSourceTargetBoqItemIds.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var sourceLineItems = new List<EstLineItemEntity>();
			IEnumerable<EstLineItemEntity> targetLineItems = new List<EstLineItemEntity>();

			// Get valid target BoQ item IDs
			var targetBoqItemIds = matchingSourceTargetBoqItemIds.Values
																					.Select(pair => pair.Item1)
																					.Where(id => id.HasValue)
																					.Select(id => id.Value)
																					.ToList();

			var matchingTargetBoqItems = boqItemLogic.GetBoqItemsByBoqItemIds(targetBoqItemIds);

			var sourceBoqItemIds = matchingSourceTargetBoqItemIds.Keys
																					.Select(pair => pair.Item1)
																					.Where(id => id.HasValue)
																					.Select(id => id.Value)
																					.ToList();

			var estimateMainLineItemLogic = new EstimateMainLineItemLogic();

			// Load source line items only if EstHeader ID is provided
			if (sourceBoqItemData.EstHeaderId.HasValue)
			{
				sourceLineItems = estimateMainLineItemLogic.GetListByFilter(e => e.EstHeaderFk == sourceBoqItemData.EstHeaderId &&
																			e.BoqHeaderFk == sourceBoqItemData.SourceBoqHeaderFk &&
																			sourceBoqItemIds.Contains((int)e.BoqItemFk) &&
																			e.LineItemType == 0 &&
																			e.IsTemp == false).ToList();
			}

			if (sourceLineItems.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var matchingSourceBoqItems = boqItemLogic.GetBoqItemsByBoqItemIds(sourceBoqItemIds);

			switch (data.ExistingEstimate)
			{
				case 1: // Overwrite
					{
						targetLineItems = OverWriteTargetLineItemsFromPrjBoq(matchingSourceTargetBoqItemIds,
																							  matchingTargetBoqItems,
																							  sourceLineItems,
																							  targetProjectEntity,
																							  targetEstHeaderEntity,
																							  sourceBoqItemData,
																							  true);
					}
					break;
				case 2: // Append
					{
						targetLineItems = BoQDrivenPrjBoqAppendLineItems(targetProjectEntity,
																				 targetEstHeaderEntity,
																				 sourceLineItems,
																				 matchingSourceTargetBoqItemIds,
																				 matchingTargetBoqItems);
					}
					break;
				case 3: // Ignore
					{
						targetLineItems = BoQDrivenPrjBoqIgnoreLineItems(targetProjectEntity,
																				 targetEstHeaderEntity,
																				 sourceLineItems,
																				 matchingSourceTargetBoqItemIds,
																				 matchingTargetBoqItems,
																				 matchingSourceBoqItems);
					}
					break;
				default:
					break;
			}

			return targetLineItems;
		}


		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Boq Driven Estimates, WIC Boq scenario
		/// BoQ Driven Estimate, Get source line items based on WIC Boq and copy line items from source to target.
		/// </summary>
		/// <param name="data"></param>
		/// <param name="sourceBoqItemData"></param>
		/// <param name="targetProjectEntity"></param>
		/// <param name="targetEstHeaderEntity"></param>
		public IEnumerable<EstLineItemEntity> BoqDrivenEstGenerateFromWicBoQ(GenerateEstimateFromBoqData data,
																							SourceBoQsData sourceBoqItemData,
																							IProjectEntity targetProjectEntity,
																							IEstHeaderEntity targetEstHeaderEntity)
		{
			// Initialize the targetLineItems collection
			IEnumerable<EstLineItemEntity> targetLineItems = Enumerable.Empty<EstLineItemEntity>();

			var boqItemLogic = new BoqItemLogic();

			// Get matching source and target BoQ item IDs with headers
			var estMainBoQData = boqItemLogic.CompareBoqs(data.TargetProjectId,
																	  null,
																	  sourceBoqItemData.ProjectWicId,
																	  data.TargetBoqHeaderFk,
																	  sourceBoqItemData.SourceBoqHeaderFk,
																	  sourceBoqItemData.FromBoqItemRefNo,
																	  sourceBoqItemData.ToBoqItemRefNo,
																	  data.OutlineSpecification,
																	  data.BasUomFk);

			//Collection of Project Boq Item to source WIC boq Assemblies Mapping
			var prjBoqItem2WicAssemblies = estMainBoQData?.PrjBoqItem2WicAssembliesMapping;

			if (prjBoqItem2WicAssemblies != null && prjBoqItem2WicAssemblies.Count > 0)
			{
				// Extract matching target BoQ items
				var matchingTargetBoqItems = prjBoqItem2WicAssemblies.Where(e => e.BoqItem != null)
																					  .Select(e => e.BoqItem)
																					  .ToList() ?? new List<IBoqItemEntity>();

				var estimateMainLineItemLogic = new EstimateMainLineItemLogic();

				switch (data.ExistingEstimate)
				{
					case 1: // Overwrite
						{
							// Handle overwrite case
							targetLineItems = OverWriteTargetLineItemsWicBoq(prjBoqItem2WicAssemblies,
																							 matchingTargetBoqItems,
																							 targetProjectEntity,
																							 targetEstHeaderEntity,
																							true,
																							 data.MdcLineItemContextId);
						}
						break;
					case 2: // Append
						{
							targetLineItems = BoQDrivenWicBoqAppendLineItems(targetProjectEntity,
																							 targetEstHeaderEntity,
																							 prjBoqItem2WicAssemblies,
																							 matchingTargetBoqItems);
						}
						break;
					case 3: // Ignore
						{
							targetLineItems = BoQDrivenWicBoqIgnoreLineItems(targetProjectEntity,
																	 targetEstHeaderEntity,
																	 prjBoqItem2WicAssemblies,
																	 matchingTargetBoqItems);
						}
						break;
					default:
						break;
				}
			}

			return targetLineItems;
		}

		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Boq Driven Estimates, Project Boq, Append scenario
		/// Append Source LineItems to Target LineItems based on matching BoQ items.
		/// </summary>
		/// <param name="targetProjectEntity"></param>
		/// <param name="targetEstHeaderEntity"></param>
		/// <param name="sourceLineItems"></param>
		/// <param name="matchingSourceTargetBoqItemIds"></param>
		/// <param name="matchingTargetBoqItems"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> BoQDrivenPrjBoqAppendLineItems(IProjectEntity targetProjectEntity,
																							IEstHeaderEntity targetEstHeaderEntity,
																							IEnumerable<EstLineItemEntity> sourceLineItems,
																							ConcurrentDictionary<Tuple<int?, int?>, Tuple<int?, int?>> matchingSourceTargetBoqItemIds,
																							IEnumerable<IBoqItemEntity> matchingTargetBoqItems)
		{
			// Null and empty checks for all input parameters
			if (targetProjectEntity == null ||
				 targetEstHeaderEntity == null ||
				 sourceLineItems == null ||
				 matchingSourceTargetBoqItemIds == null ||
				 matchingTargetBoqItems == null)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var estMainLineItemLogic = new EstimateMainLineItemLogic();
			var estMainResourceLogic = new EstimateMainResourceLogic();

			// Materialize source line items and filter out nulls
			var sourceLineItemList = sourceLineItems.Where(x => x != null).ToList();
			if (sourceLineItemList.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			// Materialize target BoQ items and filter out nulls
			var filteredTargetBoqItems = (matchingTargetBoqItems ?? Enumerable.Empty<IBoqItemEntity>())
				 .Where(e => e != null && e.BoqLineTypeFk == (int)BoqLineType.Position)
				 .ToList();

			var newLineItemIds = new Stack<int>(estMainLineItemLogic.SequenceManager.GetNextList("EST_LINE_ITEM", sourceLineItemList.Count));

			var sourceEstResources = estMainResourceLogic.GetResourcesByLineItems(sourceLineItems) ?? new List<EstResourceEntity>(); ;

			// Extract all distinct source and target key tuples (avoiding multiple DB calls)
			var targetBoqKeySet = new HashSet<string>(matchingSourceTargetBoqItemIds.Values
																	.Where(k => k != null && k.Item2.HasValue && k.Item1.HasValue)
																	.Select(k => $"{k.Item2}_{k.Item1}")
														 );

			// Fetch all target line items in one go, filter by header and BoQ composite key
			var targetLineItems = estMainLineItemLogic.GetListByFilter(x => x.EstHeaderFk == targetEstHeaderEntity.Id &&
																								 targetBoqKeySet.Contains(x.BoqHeaderFk + "_" + x.BoqItemFk)
																								 )?.Where(x => x != null && x.LineItemType == 0 && x.IsTemp == false).ToList() ?? new List<EstLineItemEntity>();

			// Index target line items for fast lookup
			var targetLineItemsByKey = targetLineItems.GroupBy(x => Tuple.Create(x.BoqHeaderFk.Value, x.BoqItemFk.Value))
																	.ToDictionary(g => g.Key, g => g.ToList());

			// Index target BoQ items for fast lookup
			var targetBoqItemsByKey = filteredTargetBoqItems.GroupBy(x => Tuple.Create(x.BoqHeaderFk, x.Id))
																			.ToDictionary(g => g.Key, g => g.First());

			var allTargetLineItems = new List<EstLineItemEntity>();
			var allEstResourceEntities = new List<EstResourceEntity>();

			// Iterate over the dictionary (Source -> Target mapping)
			foreach (var kvp in matchingSourceTargetBoqItemIds)
			{
				var sourceKey = kvp.Key;
				var targetValue = kvp.Value;

				if (sourceKey == null || targetValue == null ||
					!sourceKey.Item1.HasValue || !sourceKey.Item2.HasValue ||
					!targetValue.Item1.HasValue || !targetValue.Item2.HasValue)
				{
					continue;
				}

				// Get the source line item(s) from the already fetched list based on the source tuple
				var matchingSourceLineItems = sourceLineItemList.Where(x => x.BoqHeaderFk == sourceKey.Item2 && x.BoqItemFk == sourceKey.Item1)
																				.ToList();

				if (matchingSourceLineItems.Count == 0)
				{
					continue;
				}

				var sourceLineItemIds = matchingSourceLineItems.Select(x => x.Id).ToHashSet();
				var filteredSourceEstResources = sourceEstResources.Where(e => sourceLineItemIds.Contains(e.EstLineItemFk)).ToList();

				// Get the target line item(s) from the already fetched list based on the target tuple
				var targetKey = Tuple.Create(targetValue.Item2.Value, targetValue.Item1.Value);
				targetLineItemsByKey.TryGetValue(targetKey, out var matchingTargetLineItems);
				matchingTargetLineItems = matchingTargetLineItems?.Where(x => x != null).ToList() ?? new List<EstLineItemEntity>();

				// Get the target BoQ item
				targetBoqItemsByKey.TryGetValue(targetKey, out var filteredTargetBoQItem);
				if (matchingTargetLineItems.Count == 0)
				{
					// No target line items, create new ones
					var result = CopyLineItemsNResources(targetProjectEntity.Id,
																	 targetEstHeaderEntity,
																	 matchingSourceLineItems,
																	 filteredSourceEstResources,
																	 newLineItemIds,
																	 filteredTargetBoQItem);

					allTargetLineItems.AddRange(result.Item1);
					allEstResourceEntities.AddRange(result.Item2);
				}
				else
				{
					var matchingSourceLineItem = matchingSourceLineItems.First();
					var matchingTargetLineItem = matchingTargetLineItems.First();

					//Append 1: Adds Resources from first Line Item of source to the existing Line Item in the target (generated by BoQ driven estimate).
					var firstSourceResources = sourceEstResources.Where(e => e.EstLineItemFk == matchingSourceLineItem.Id &&
																								e.EstHeaderFk == matchingSourceLineItem.EstHeaderFk)
																				.ToList();

					var targetResources = CopyLineItemResources(targetProjectEntity.Id,
																			  matchingTargetLineItem,
																			  targetEstHeaderEntity,
																			  firstSourceResources);

					allEstResourceEntities.AddRange(targetResources);
					allTargetLineItems.Add(matchingTargetLineItem);

					//Append 2: Copies remaining line items with its resources. So if source was 1 BoQ : 3 Line Items, target will also be 1 BoQ : 3 Line Items
					var remainingSourceLineItems = matchingSourceLineItems
					  .Where(e => e.Id != matchingSourceLineItem.Id)
					  .ToList();

					if (remainingSourceLineItems.Count > 0)
					{
						var remainingSourceResourceIds = remainingSourceLineItems.Select(x => x.Id).ToHashSet();
						var remainingResources = sourceEstResources.Where(e => remainingSourceResourceIds.Contains(e.EstLineItemFk))
																				 .ToList();

						var extraLineItemsAndResources = CopyLineItemsNResources(targetProjectEntity.Id,
																									 targetEstHeaderEntity,
																									 remainingSourceLineItems,
																									 remainingResources,
																									 newLineItemIds,
																									 filteredTargetBoQItem);

						allTargetLineItems.AddRange(extraLineItemsAndResources.Item1);
						allEstResourceEntities.AddRange(extraLineItemsAndResources.Item2);
					}
				}
			}

			if (allEstResourceEntities.Any())
			{
				new EstResourceUpdateHelper().UpdateIsDisabledFromParent(allEstResourceEntities);
			}

			if (allTargetLineItems.Any())
			{
				new EstLineItemUpdateHelper(targetEstHeaderEntity.Id, targetProjectEntity.Id, new EstLineItemUpdateOption()
				{
					IsCalculateDetail = true,
					IsUpdateRate = false,
					IsUpdateAssembly = false,
					ToSave = false,
					ConsiderIsRate = true,
					IsUpdateExchangeRate = false
				}).CalculateLineItemsOfEstimate(allTargetLineItems);
			}

			using (var transaction = TransactionScopeFactory.Create())
			{
				if (allTargetLineItems.Any())
				{
					estMainLineItemLogic.BulkSave(allTargetLineItems);
				}

				var flatAllResources = allEstResourceEntities.Flatten(e => e.ResourceChildren).ToList();
				if (flatAllResources.Any())
				{
					estMainResourceLogic.BulkSave(flatAllResources);
				}

				transaction.Complete();
			}

			return allTargetLineItems;
		}


		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Boq Driven Estimates, Project Boq, Ignore scenario
		/// If resources exist for the line item generated by BoQ Driven Estimate, then moves to qualify the next BoQ position.
		/// </summary>
		/// <param name="targetProjectEntity"></param>
		/// <param name="targetEstHeaderEntity"></param>
		/// <param name="sourceLineItems"></param>
		/// <param name="matchingSourceTargetBoqItemIds"></param>
		/// <param name="matchingTargetBoqItems"></param>
		/// <param name="matchingSourceBoqItems"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> BoQDrivenPrjBoqIgnoreLineItems(IProjectEntity targetProjectEntity,
																							IEstHeaderEntity targetEstHeaderEntity,
																							IEnumerable<EstLineItemEntity> sourceLineItems,
																							ConcurrentDictionary<Tuple<int?, int?>, Tuple<int?, int?>> matchingSourceTargetBoqItemIds,
																							IEnumerable<IBoqItemEntity> matchingTargetBoqItems,
																							IEnumerable<IBoqItemEntity> matchingSourceBoqItems)
		{
			// Null and empty checks for all input parameters
			if (targetProjectEntity == null ||
				 targetEstHeaderEntity == null ||
				 sourceLineItems == null ||
				 matchingSourceTargetBoqItemIds == null)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var estMainLineItemLogic = new EstimateMainLineItemLogic();
			var estMainResourceLogic = new EstimateMainResourceLogic();

			// Ensure all collections are non-null and filter for Position type
			var filteredSourceBoqItems = (matchingSourceBoqItems ?? Enumerable.Empty<IBoqItemEntity>())
				 .Where(e => e != null && e.BoqLineTypeFk == (int)BoqLineType.Position)
				 .ToList();

			var filteredTargetBoqItems = (matchingTargetBoqItems ?? Enumerable.Empty<IBoqItemEntity>())
				 .Where(e => e != null && e.BoqLineTypeFk == (int)BoqLineType.Position)
				 .ToList();

			if (!filteredSourceBoqItems.Any() || !filteredTargetBoqItems.Any())
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var sourceEstResources = new EstimateMainResourceLogic().GetResourcesByLineItems(sourceLineItems);

			// Prepare composite keys for fast lookup
			var targetBoqHeaderAndItemValues = new HashSet<string>(matchingSourceTargetBoqItemIds.Values
																					.Where(k => k != null && k.Item2.HasValue && k.Item1.HasValue)
																					.Select(k => $"{k.Item2}_{k.Item1}")
			);

			var targetLineItems = estMainLineItemLogic.GetListByFilter(x => x.EstHeaderFk == targetEstHeaderEntity.Id &&
																			targetBoqHeaderAndItemValues.Contains(x.BoqHeaderFk + "_" + x.BoqItemFk)).ToList()?
																			.Where(x => x.LineItemType == 0 && x.IsTemp == false).ToList() ?? new List<EstLineItemEntity>();

			var targetEstResources = estMainResourceLogic.GetResourcesByLineItems(targetLineItems) ?? new List<EstResourceEntity>(); ;

			// Get target BoQ items that do not have line item resources
			var targetBoqItemsWithoutLineItemResources = GetBoQItemsWithOutLineItemResources(targetEstHeaderEntity.Id, filteredTargetBoqItems, targetEstResources)
														 ?? new List<IBoqItemEntity>();

			// Join source and target BoQ items by Reference
			var matchedBoqItems = filteredSourceBoqItems.Join(targetBoqItemsWithoutLineItemResources,
																			  sourceBoq => sourceBoq.Reference,
																			  targetBoq => targetBoq.Reference,
																			  (sourceBoq, targetBoq) => sourceBoq)
																			 .Where(e => e != null)
																			 .ToList();

			var matchedSourceBoqItemIds = matchedBoqItems.Select(e => e?.Id)
																		.Where(id => id.HasValue)
																		.Select(id => id.Value).ToList();

			var filteredSourceBoqItemTuples = matchedBoqItems.Where(e => e != null)
																			 .Select(e => Tuple.Create((int?)e.Id, (int?)e.BoqHeaderFk))
																			 .Distinct()
																			 .ToHashSet();

			//Exclude the target boq items with which line items has resources
			//Consider only source boq items for which target boq items doesn't have line item resources
			var filteredMatchSourceTargetBoqItems = matchingSourceTargetBoqItemIds.Where(e => e.Key != null && filteredSourceBoqItemTuples.Contains(e.Key))
																										 .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

			//Get source line items from source BoQs to matching target BoQs doesn't have any line items
			var filteredSourceLineItems = sourceLineItems.Where(e => e != null && e.BoqItemFk.HasValue && matchedSourceBoqItemIds.Contains(e.BoqItemFk.Value))
																		.ToList();

			if (!filteredSourceLineItems.Any())
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var newLineItemIds = new Stack<int>(estMainLineItemLogic.SequenceManager.GetNextList("EST_LINE_ITEM", filteredSourceLineItems.Count));

			var allTargetLineItems = new List<EstLineItemEntity>();
			var allEstResourceEntities = new List<EstResourceEntity>();

			// Iterate over the dictionary (Source -> Target mapping)
			foreach (var kvp in filteredMatchSourceTargetBoqItems)
			{
				var sourceKey = kvp.Key;
				var targetValue = kvp.Value;

				if (sourceKey == null || targetValue == null)
				{
					continue;
				}

				// Get the source line item(s) from the already fetched list based on the source tuple
				var matchingSourceLineItems = filteredSourceLineItems
														 .Where(x => x.BoqHeaderFk == sourceKey.Item2 && x.BoqItemFk == sourceKey.Item1)
														 .ToList();

				if (!matchingSourceLineItems.Any())
				{
					continue;
				}

				var sourceLineItemIds = matchingSourceLineItems.Select(e => e.Id).ToHashSet();

				var filteredSourceEstResources = sourceEstResources.Where(e => sourceLineItemIds.Contains(e.EstLineItemFk)).ToList();

				// Get the target line item(s) from the already fetched list based on the target tuple
				var matchingTargetLineItems = targetLineItems
														.Where(x => x.BoqHeaderFk == targetValue.Item2 && x.BoqItemFk == targetValue.Item1)
														.ToList();

				//Matching target boq items
				var targetBoqItem = filteredTargetBoqItems.FirstOrDefault(e => e.Id == targetValue.Item1 && e.BoqHeaderFk == targetValue.Item2);
				if (!matchingTargetLineItems.Any())
				{
					//If no target boq matching line items, directly create all line items
					var result = CopyLineItemsNResources(targetProjectEntity.Id,
																	targetEstHeaderEntity,
																	matchingSourceLineItems,
																	filteredSourceEstResources,
																	newLineItemIds,
																	targetBoqItem);

					allTargetLineItems.AddRange(result.Item1);
					allEstResourceEntities.AddRange(result.Item2);
				}
				else
				{
					var sourceLineItem = matchingSourceLineItems.FirstOrDefault();
					var targetLineItem = matchingTargetLineItems.FirstOrDefault();

					if (sourceLineItem == null || targetLineItem == null)
					{
						continue;
					}

					// Ignore 2.1 : If resources does not exist for the line item generated by BoQ Driven Estimate, then
					// copies Resources from first Line Item of source to the existing Line Item in the target(generated by BoQ driven estimate).
					var targetLineItemResources = targetEstResources.Where(e => e.EstLineItemFk == targetLineItem.Id && e.EstHeaderFk == targetLineItem.EstHeaderFk);

					if (!targetLineItemResources.Any())
					{
						var sourceLineItemResources = sourceEstResources.Where(e => e.EstLineItemFk == sourceLineItem.Id && e.EstHeaderFk == sourceLineItem.EstHeaderFk);

						var copiedResources = CopyLineItemResources(targetProjectEntity.Id, targetLineItem, targetEstHeaderEntity, sourceLineItemResources);
						allEstResourceEntities.AddRange(copiedResources);
						allTargetLineItems.Add(targetLineItem);
					}

					//Ignore 2.2: Copies remaining line items with its resources. 
					var additionalSourceLineItems = matchingSourceLineItems.Where(e => e.Id != sourceLineItem.Id).ToList();
					if (additionalSourceLineItems.Any())
					{
						var additionalResources = sourceEstResources.Where(e => additionalSourceLineItems.Select(li => li.Id).Contains(e.EstLineItemFk))
																				.ToList();

						var createdLineItemResources = CopyLineItemsNResources(targetProjectEntity.Id,
																								 targetEstHeaderEntity,
																								 additionalSourceLineItems,
																								 additionalResources,
																								 newLineItemIds,
																								 targetBoqItem);

						allTargetLineItems.AddRange(createdLineItemResources.Item1);
						allEstResourceEntities.AddRange(createdLineItemResources.Item2);
					}
				}
			}

			if (allEstResourceEntities.Any())
			{
				new EstResourceUpdateHelper().UpdateIsDisabledFromParent(allEstResourceEntities);
			}

			if (allTargetLineItems.Any())
			{
				new EstLineItemUpdateHelper(targetEstHeaderEntity.Id, targetProjectEntity.Id, new EstLineItemUpdateOption()
				{
					IsCalculateDetail = true,
					IsUpdateRate = false,
					IsUpdateAssembly = false,
					ToSave = false,
					ConsiderIsRate = true,
					IsUpdateExchangeRate = false
				}).CalculateLineItemsOfEstimate(allTargetLineItems);
			}

			using (var transaction = TransactionScopeFactory.Create())
			{
				if (allTargetLineItems.Any())
				{
					estMainLineItemLogic.BulkSave(allTargetLineItems);
				}

				var flatAllResources = allEstResourceEntities.Flatten(e => e.ResourceChildren).ToList();
				if (flatAllResources.Any())
				{
					estMainResourceLogic.BulkSave(flatAllResources);
				}

				transaction.Complete();
			}

			return targetLineItems;
		}


		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Boq Driven Estimates, WIC Boq, Append scenario
		/// Append Source LineItems to Target LineItems based on matching BoQ items.
		/// </summary>
		/// <param name="targetProjectEntity"></param>
		/// <param name="targetEstHeaderEntity"></param>
		/// <param name="prjBoqItem2WicAssemblies"></param>
		/// <param name="targetBoqItems"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> BoQDrivenWicBoqAppendLineItems(IProjectEntity targetProjectEntity,
																							IEstHeaderEntity targetEstHeaderEntity,
																							ICollection<IPrjBoqItem2WicAssemblies> prjBoqItem2WicAssemblies,
																							IEnumerable<IBoqItemEntity> targetBoqItems)
		{
			// Null and empty checks for all input parameters
			if (targetProjectEntity == null ||
				 targetEstHeaderEntity == null ||
				 prjBoqItem2WicAssemblies == null || prjBoqItem2WicAssemblies.Count == 0 ||
				 targetBoqItems == null)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var estMainLineItemLogic = new EstimateMainLineItemLogic();
			var estMainResourceLogic = new EstimateMainResourceLogic();

			// Prepare target BoQ header/item composite keys for fast lookup
			var targetBoqHeaderAndItemValues = new HashSet<string>(targetBoqItems.Where(b => b != null)
																										.Select(k => $"{k.BoqHeaderFk}_{k.Id}"));

			// Fetch all target line items in one go, filter by header and BoQ composite key
			var targetLineItems = estMainLineItemLogic.GetListByFilter(x => x.EstHeaderFk == targetEstHeaderEntity.Id &&
														targetBoqHeaderAndItemValues.Contains(x.BoqHeaderFk + "_" + x.BoqItemFk)
												  )?.Where(x => x.LineItemType == 0 && x.IsTemp == false).ToList() ?? new List<EstLineItemEntity>();
			// Get all unique assembly IDs
			var assemblyIds = prjBoqItem2WicAssemblies
				 .Where(e => e?.WicAssemblies != null)
				 .SelectMany(e => e.WicAssemblies)
				 .Where(e => e != null)
				 .Select(e => e.Item2)
				 .Distinct()
				 .ToList();

			if (assemblyIds.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			// Fetch all assembly entities in one go
			var assemblyEntities = estMainLineItemLogic.GetPrjAssemblyByMasterAssemblyId(assemblyIds, targetProjectEntity.Id)
																	 ?.Where(e => e != null)
																	 .ToList() ?? new List<EstLineItemEntity>();

			if (assemblyEntities.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var newLineItemIds = new Stack<int>(estMainLineItemLogic.SequenceManager.GetNextList("EST_LINE_ITEM", assemblyEntities.Count));

			// Prepare source resources only once
			var sourceEstResources = estMainResourceLogic.GetResourcesByLineItems(assemblyEntities)?.ToList() ?? new List<EstResourceEntity>();

			var allTargetLineItems = new List<EstLineItemEntity>();
			var allEstResourceEntities = new List<EstResourceEntity>();

			// Precompute lookups for performance
			var assemblyEntitiesByIdHeader = assemblyEntities.GroupBy(e => (e.Id, e.EstHeaderFk))
																			 .ToDictionary(g => g.Key, g => g.First());

			var prjAssemblyEntitiesByAssemblyHeader = assemblyEntities.Where(e => e.EstAssemblyFk.HasValue && e.EstHeaderAssemblyFk.HasValue)
																						 .GroupBy(e => (e.EstAssemblyFk.Value, e.EstHeaderAssemblyFk.Value))
																						 .ToDictionary(g => g.Key, g => g.First());

			var targetLineItemsByBoq = targetLineItems.GroupBy(x => (x.BoqHeaderFk, x.BoqItemFk))
																	.ToDictionary(g => g.Key, g => g.ToList());

			// Step 2: Iterate over the collection (Source -> Target mapping)
			foreach (var prjBoqItem2WicAssembly in prjBoqItem2WicAssemblies)
			{
				if (prjBoqItem2WicAssembly?.WicAssemblies == null || prjBoqItem2WicAssembly.BoqItem == null)
				{
					continue;
				}

				// Build list of matching source WIC assemblies
				var matchingSourceWicAssemblies = new List<EstLineItemEntity>();
				foreach (var wicAssembly in prjBoqItem2WicAssembly.WicAssemblies)
				{
					// Prefer project assembly if available
					if (prjAssemblyEntitiesByAssemblyHeader.TryGetValue((wicAssembly.Item2, wicAssembly.Item1), out var prjAssemblyEntity))
					{
						matchingSourceWicAssemblies.Add(prjAssemblyEntity);
					}
					else if (assemblyEntitiesByIdHeader.TryGetValue((wicAssembly.Item2, wicAssembly.Item1), out var assemblyEntity))
					{
						matchingSourceWicAssemblies.Add(assemblyEntity);
					}
				}

				if (matchingSourceWicAssemblies.Count == 0)
				{
					continue;
				}

				// Get the target line item(s) from the already fetched list based on the target tuple
				var key = (prjBoqItem2WicAssembly.BoqItem.BoqHeaderFk, prjBoqItem2WicAssembly.BoqItem.Id);
				if (!targetLineItemsByBoq.TryGetValue(key, out var matchingTargetLineItems) || matchingTargetLineItems.Count == 0)
				{
					continue;
				}

				// Prepare source resources for these assemblies
				var matchingSourceWicAssemblyIds = matchingSourceWicAssemblies.Select(e => e.Id).ToHashSet();
				var matchingSourceWicAssemblyHeaderIds = matchingSourceWicAssemblies.Select(e => e.EstHeaderFk).ToHashSet();
				var filteredSourceEstResources = sourceEstResources
					 .Where(e => matchingSourceWicAssemblyIds.Contains(e.EstLineItemFk) && matchingSourceWicAssemblyHeaderIds.Contains(e.EstHeaderFk))
					 .ToList();

				var filteredTargetBoQItem = targetBoqItems?.FirstOrDefault(e => e != null && e.Id == prjBoqItem2WicAssembly.BoqItem.Id && e.BoqHeaderFk == prjBoqItem2WicAssembly.BoqItem.BoqHeaderFk);

				// If no target line items, create new ones
				if (matchingTargetLineItems.Count == 0)
				{
					var result = CopyLineItemsNResources(targetProjectEntity.Id,
																	 targetEstHeaderEntity,
																	 matchingSourceWicAssemblies,
																	 filteredSourceEstResources,
																	 newLineItemIds,
																	 filteredTargetBoQItem);

					allTargetLineItems.AddRange(result.Item1);
					allEstResourceEntities.AddRange(result.Item2);

					continue;
				}

				var matchingSourceLineItem = matchingSourceWicAssemblies.First();
				var matchingTargetLineItem = matchingTargetLineItems.First();

				//Append 1: Adds Resources from first Line Item of source to the existing Line Item in the target (generated by BoQ driven estimate).
				var firstSourceResources = filteredSourceEstResources.Where(e => e.EstLineItemFk == matchingSourceLineItem.Id &&
																							e.EstHeaderFk == matchingSourceLineItem.EstHeaderFk)
																			.ToList();

				var targetResources = CopyLineItemResources(targetProjectEntity.Id,
																			matchingTargetLineItem,
																			targetEstHeaderEntity,
																			firstSourceResources);

				allEstResourceEntities.AddRange(targetResources);
				allTargetLineItems.Add(matchingTargetLineItem);

				//Append 2: Copies remaining line items with its resources. So if source was 1 BoQ : 3 Line Items, target will also be 1 BoQ : 3 Line Items
				var remainingSourceLineItems = matchingSourceWicAssemblies.Skip(1).ToList();

				if (remainingSourceLineItems.Count > 0)
				{
					var remainingSourceResourceIds = remainingSourceLineItems.Select(x => x.Id).ToHashSet();
					var remainingResources = filteredSourceEstResources
						 .Where(e => remainingSourceResourceIds.Contains(e.EstLineItemFk))
						 .ToList();

					var extraLineItemsAndResources = CopyLineItemsNResources(
						 targetProjectEntity.Id,
						 targetEstHeaderEntity,
						 remainingSourceLineItems,
						 remainingResources,
						 newLineItemIds,
						 filteredTargetBoQItem
					);
					allTargetLineItems.AddRange(extraLineItemsAndResources.Item1);
					allEstResourceEntities.AddRange(extraLineItemsAndResources.Item2);
				}
			}

			if (allEstResourceEntities.Count > 0)
			{
				new EstResourceUpdateHelper().UpdateIsDisabledFromParent(allEstResourceEntities);
			}

			if (allTargetLineItems.Count > 0)
			{
				new EstLineItemUpdateHelper(targetEstHeaderEntity.Id, targetProjectEntity.Id, new EstLineItemUpdateOption()
				{
					IsCalculateDetail = true,
					IsUpdateRate = false,
					IsUpdateAssembly = false,
					ToSave = false,
					ConsiderIsRate = true,
					IsUpdateExchangeRate = false
				}).CalculateLineItemsOfEstimate(allTargetLineItems);
			}

			using (var transaction = TransactionScopeFactory.Create())
			{
				if (allTargetLineItems.Count > 0)
				{
					estMainLineItemLogic.BulkSave(allTargetLineItems);
				}

				var flatAllResources = allEstResourceEntities.Flatten(e => e.ResourceChildren).ToList();
				if (flatAllResources.Count > 0)
				{
					estMainResourceLogic.BulkSave(flatAllResources);
				}

				transaction.Complete();
			}

			return allTargetLineItems;
		}

		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Boq Driven Estimates, WIC Boq, Ignore scenario
		/// If resources exist for the line item generated by BoQ Driven Estimate, then moves to qualify the next BoQ position.
		/// </summary>
		/// <param name="targetProjectEntity"></param>
		/// <param name="targetEstHeaderEntity"></param>
		/// <param name="prjBoqItem2WicAssemblies"></param>
		/// <param name="matchingTargetBoqItems"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> BoQDrivenWicBoqIgnoreLineItems(IProjectEntity targetProjectEntity,
																							IEstHeaderEntity targetEstHeaderEntity,
																							ICollection<IPrjBoqItem2WicAssemblies> prjBoqItem2WicAssemblies,
																							IEnumerable<IBoqItemEntity> matchingTargetBoqItems)
		{
			// Null and empty checks for all input parameters
			if (targetProjectEntity == null ||
				 targetEstHeaderEntity == null ||
				 prjBoqItem2WicAssemblies == null ||
				 prjBoqItem2WicAssemblies.Count == 0 ||
				 matchingTargetBoqItems == null)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var estMainLineItemLogic = new EstimateMainLineItemLogic();
			var estMainResourceLogic = new EstimateMainResourceLogic();

			var filteredTargetBoqItems = matchingTargetBoqItems.Where(e => e.BoqLineTypeFk == (int)BoqLineType.Position).ToList();

			if (filteredTargetBoqItems.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			// Prepare target BoQ header/item composite keys for fast lookup
			var targetBoqHeaderAndItemValues = new HashSet<string>(filteredTargetBoqItems.Select(k => $"{k.BoqHeaderFk}_{k.Id}"));

			// Fetch all target line items in one go, filter by header and BoQ composite key
			var targetLineItems = estMainLineItemLogic.GetListByFilter(x => x.EstHeaderFk == targetEstHeaderEntity.Id &&
																								targetBoqHeaderAndItemValues.Contains(x.BoqHeaderFk + "_" + x.BoqItemFk)
																						 )?.
																						Where(x => x.LineItemType == 0 && x.IsTemp == false)
																						.ToList() ?? new List<EstLineItemEntity>();

			var targetEstResources = estMainResourceLogic.GetResourcesByLineItems(targetLineItems) ?? new List<EstResourceEntity>();

			// Get all unique assembly IDs
			var assemblyIds = prjBoqItem2WicAssemblies
				 .Where(e => e?.WicAssemblies != null)
				 .SelectMany(e => e.WicAssemblies)
				 .Where(e => e != null)
				 .Select(e => e.Item2)
				 .Distinct()
				 .ToList();

			if (assemblyIds.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var assemblyEntities = estMainLineItemLogic.GetPrjAssemblyByMasterAssemblyId(assemblyIds, targetProjectEntity.Id)?
																	 .Where(e => e != null)
																	 .ToList() ?? new List<EstLineItemEntity>();

			if (assemblyEntities.Count == 0)
			{
				return Enumerable.Empty<EstLineItemEntity>();
			}

			var newLineItemIds = new Stack<int>(estMainLineItemLogic.SequenceManager.GetNextList("EST_LINE_ITEM", assemblyEntities.Count));

			// Prepare source resources only once
			var sourceEstResources = estMainResourceLogic.GetResourcesByLineItems(assemblyEntities)?.ToList() ?? new List<EstResourceEntity>();

			var allTargetLineItems = new List<EstLineItemEntity>();
			var allEstResourceEntities = new List<EstResourceEntity>();

			foreach (var prjBoqItem2WicAssembly in prjBoqItem2WicAssemblies)
			{
				if (prjBoqItem2WicAssembly?.WicAssemblies == null || prjBoqItem2WicAssembly.BoqItem == null)
				{
					continue;
				}

				// Build list of matching source WIC assemblies
				var matchingSourceWicAssemblies = new List<EstLineItemEntity>();
				foreach (var wicAssembly in prjBoqItem2WicAssembly.WicAssemblies)
				{
					var assemblyEntity = assemblyEntities.FirstOrDefault(e => e.Id == wicAssembly.Item2 && e.EstHeaderFk == wicAssembly.Item1);

					// Prefer project assembly if available
					var prjAssemblyEntity = assemblyEntities.FirstOrDefault(e => e.EstAssemblyFk.HasValue && e.EstHeaderAssemblyFk.HasValue && e.EstAssemblyFk == wicAssembly.Item2 && e.EstHeaderAssemblyFk == wicAssembly.Item1);

					if (prjAssemblyEntity != null)
					{
						assemblyEntity = prjAssemblyEntity;
					}

					if (assemblyEntity == null || assemblyEntity.EstAssemblyCatFk == null)
					{
						continue;
					}

					matchingSourceWicAssemblies.Add(assemblyEntity);
				}

				// Get the target line item(s) from the already fetched list based on the target tuple
				var matchingTargetLineItems = targetLineItems
														.Where(x => x.BoqHeaderFk == prjBoqItem2WicAssembly.BoqItem.BoqHeaderFk && x.BoqItemFk == prjBoqItem2WicAssembly.BoqItem.Id)
														.ToList();

				if (matchingTargetLineItems.Count == 0)
				{
					continue;
				}

				var matchingTargetResources = targetEstResources.Where(e => matchingTargetLineItems.Select(li => li.Id).Contains(e.EstLineItemFk) &&
																							matchingTargetLineItems.Select(li => li.EstHeaderFk).Contains(e.EstHeaderFk))
																				.ToList();

				if (matchingTargetResources.Count > 0)
				{
					// Ignore 1st point, If resources exist for the line item generated by BoQ Driven Estimate,
					// then moves to qualify the next BoQ position.
					continue;
				}

				var sourceLineItem = matchingSourceWicAssemblies.First();
				var targetLineItem = matchingTargetLineItems.First();

				if (sourceLineItem == null || targetLineItem == null)
				{
					continue;
				}

				// Ignore 2.1 : If resources does not exist for the line item generated by BoQ Driven Estimate, then
				// copies Resources from first Line Item of source to the existing Line Item in the target(generated by BoQ driven estimate).
				var targetLineItemResources = targetEstResources.Where(e => e.EstLineItemFk == targetLineItem.Id && e.EstHeaderFk == targetLineItem.EstHeaderFk);

				if (!targetLineItemResources.Any())
				{
					var sourceLineItemResources = sourceEstResources.Where(e => e.EstLineItemFk == sourceLineItem.Id && e.EstHeaderFk == sourceLineItem.EstHeaderFk);

					var copiedResources = CopyLineItemResources(targetProjectEntity.Id, targetLineItem, targetEstHeaderEntity, sourceLineItemResources);

					allEstResourceEntities.AddRange(copiedResources);
					allTargetLineItems.Add(targetLineItem);
				}

				//Ignore 2.2: Copies remaining line items with its resources. 
				var additionalSourceLineItems = matchingSourceWicAssemblies.Where(e => e.Id != sourceLineItem.Id).ToList();
				if (additionalSourceLineItems.Count > 0)
				{
					var additionalResources = sourceEstResources.Where(e => additionalSourceLineItems.Select(li => li.Id).Contains(e.EstLineItemFk))
																			.ToList();

					var createdLineItemResources = CopyLineItemsNResources(targetProjectEntity.Id,
																							 targetEstHeaderEntity,
																							 additionalSourceLineItems,
																							 additionalResources,
																							 newLineItemIds,
																							 prjBoqItem2WicAssembly.BoqItem);

					allTargetLineItems.AddRange(createdLineItemResources.Item1);
					allEstResourceEntities.AddRange(createdLineItemResources.Item2);
				}
			}


			if (allEstResourceEntities.Count > 0)
			{
				new EstResourceUpdateHelper().UpdateIsDisabledFromParent(allEstResourceEntities);
			}

			if (allTargetLineItems.Count > 0)
			{
				new EstLineItemUpdateHelper(targetEstHeaderEntity.Id, targetProjectEntity.Id, new EstLineItemUpdateOption
				{
					IsCalculateDetail = true,
					IsUpdateRate = false,
					IsUpdateAssembly = false,
					ToSave = false,
					ConsiderIsRate = true,
					IsUpdateExchangeRate = false
				}).CalculateLineItemsOfEstimate(allTargetLineItems);
			}

			using (var transaction = TransactionScopeFactory.Create())
			{
				if (allTargetLineItems.Count > 0)
				{
					estMainLineItemLogic.BulkSave(allTargetLineItems);
				}

				var flatAllResources = allEstResourceEntities.Flatten(e => e.ResourceChildren).ToList();
				if (flatAllResources.Count > 0)
				{
					estMainResourceLogic.BulkSave(flatAllResources);
				}

				transaction.Complete();
			}

			return targetLineItems;
		}

		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Copy BoQ Driven Estimate, Source line items and resources to target
		/// </summary>
		/// <param name="targetProjectId"></param>
		/// <param name="targetEstHeaderEntity"></param>
		/// <param name="sourceLineItems"></param>
		/// <param name="sourceEstResources"></param>
		/// <param name="lineItemIds"></param>
		/// <param name="targetBoqItem"></param>
		/// <returns></returns>
		private Tuple<List<EstLineItemEntity>, List<EstResourceEntity>> CopyLineItemsNResources(int targetProjectId,
														IEstHeaderEntity targetEstHeaderEntity,
														IEnumerable<EstLineItemEntity> sourceLineItems,
														IEnumerable<EstResourceEntity> sourceEstResources,
														Stack<int> lineItemIds,
														IBoqItemEntity targetBoqItem)
		{
			var estimateMainLineItemLogic = new EstimateMainLineItemLogic();
			var targetLineItems = new List<EstLineItemEntity>();
			var targetEstResources = new List<EstResourceEntity>();

			foreach (var sourceLineItem in sourceLineItems)
			{
				var targetLineItem = (EstLineItemEntity)(sourceLineItem.Clone());

				targetLineItem.EstHeaderFk = targetEstHeaderEntity.Id;
				targetLineItem.BoqHeaderFk = targetBoqItem.BoqHeaderFk;
				targetLineItem.BoqItemFk = targetBoqItem.Id;
				targetLineItem.BasUomFk = targetBoqItem.BasUomFk;
				targetLineItem.BasUomTargetFk = targetBoqItem.BasUomFk;
				targetLineItem.WqQuantityTarget = targetBoqItem.Quantity;
				targetLineItem.WqQuantityTargetDetail = targetBoqItem.QuantityDetail;
				targetLineItem.QuantityTarget = targetBoqItem.QuantityAdj;
				targetLineItem.QuantityTargetDetail = targetBoqItem.QuantityAdjDetail;

				if (lineItemIds.TryPop(out int newId))
				{
					targetLineItem.Id = newId;
				}

				targetLineItem.DescriptionInfo.Description = null;
				targetLineItem.DescriptionInfo.DescriptionTr = null;
				targetLineItem.DescriptionInfo.Modified = true;
				targetLineItem.UpdatedAt = null;
				targetLineItem.UpdatedBy = null;
				targetLineItem.InsertedAt = DateTime.UtcNow;
				targetLineItem.InsertedBy = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
				targetLineItem.Version = 0;
				targetLineItem.RevenueUnit = 0m;
				targetLineItem.Revenue = 0m;
				targetLineItem.Margin1 = 0m;
				targetLineItem.Margin2 = 0m;

				if (sourceLineItem.DescriptionInfo != null)
				{
					var translated = sourceLineItem.DescriptionInfo.Translated;

					targetLineItem.DescriptionInfo = new DescriptionTranslateType(translated, translated);
					targetLineItem.CopyTranslate<EstLineItemEntity>(estimateMainLineItemLogic.UserLanguageId, new Func<EstLineItemEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
				}

				targetLineItems.Add(targetLineItem);

				var sourceLineItemResources = sourceEstResources.Where(e => e.EstLineItemFk == sourceLineItem.Id);

				targetEstResources.AddRange(CopyLineItemResources(targetProjectId, targetLineItem, targetEstHeaderEntity, sourceLineItemResources));
			}

			//Generate code for newly created line items
			new EstLineItemCodeGenerator(targetEstHeaderEntity).GenerateCode(targetLineItems, null, targetProjectId);

			//Assign Quantity Relations for newly created line items
			estimateMainLineItemLogic.AssignQtyRel(targetLineItems, targetEstHeaderEntity.Id, (int)EstQuantityRelType.FromStructure).ToList();


			return Tuple.Create(targetLineItems, targetEstResources);
		}


		private IEnumerable<EstResourceEntity> CopyLineItemResources(int targetProjectId,
																						 EstLineItemEntity targetLineItem,
																						 IEstHeaderEntity targetEstHeaderEntity,
																						 IEnumerable<EstResourceEntity> sourceEstResources)
		{
			var estResourceCopyLogic = new EstResourceCopyLogic(targetProjectId);

			var lineItemJobId = targetEstHeaderEntity.LgmJobFk.HasValue ? targetEstHeaderEntity.LgmJobFk : null;

			var srcParentResources = sourceEstResources.Where(e => e.EstResourceFk == null);

			var resourceCopyOption = new ResourceCopyOption()
			{
				LineItemId = targetLineItem.Id,
				HeaderId = targetLineItem.EstHeaderFk,
				JobId = targetLineItem.LgmJobFk.HasValue ? targetLineItem.LgmJobFk : lineItemJobId,
				IsResolveAssembly = true,
				IsSetProjectCurrency = true,
				ResourceCopyFrom = targetLineItem.LineItemType == 1 ? ResourceModuleType.MasterAssembly : ResourceModuleType.Estimate,
				ResourceCopyTo = targetLineItem.LineItemType == 1 ? ResourceModuleType.MasterAssembly : ResourceModuleType.Estimate,
				UserDefinedColumnValCopyOption = new UserDefinedColumnValCopyOption()
				{
					IsCopyUserDefinedColumnVal = true
				},
				CharacteristicCopyOption = new CharacteristicCopyOption()
				{
					IsCopyCharacteristic = true,
					IsAutoAssignCharacteristic = true
				}
			};

			//Copy resources from source line item to target line item resources
			return estResourceCopyLogic.CopyResources(srcParentResources, resourceCopyOption);
		}

		/// <summary>
		/// Get Target BoQ items which has line items, but doesn't have any resources
		/// </summary>
		/// <param name="targetEstHeaderId"></param>
		/// <param name="matchingTargetBoqItems"></param>
		/// <param name="targetEstResources"></param>
		/// <returns></returns>
		private List<IBoqItemEntity> GetBoQItemsWithOutLineItemResources(int targetEstHeaderId,
																							  IEnumerable<IBoqItemEntity> matchingTargetBoqItems,
																							  IEnumerable<EstResourceEntity> targetEstResources)
		{
			// Null check for input parameters
			if (matchingTargetBoqItems == null)
			{
				return new List<IBoqItemEntity>();
			}

			var matchingBoqItemList = matchingTargetBoqItems.Where(x => x != null).ToList();

			if (!matchingBoqItemList.Any())
			{
				return new List<IBoqItemEntity>();
			}

			var targetBoQItemIds = matchingBoqItemList.Select(e => e.Id).ToHashSet(); // Faster lookup

			var targetBoQHeaderIds = matchingBoqItemList.Select(e => e.BoqHeaderFk).ToHashSet();

			// Fetch all matching line items for given EstHeader and BoQ info
			var allBoQTargetLineItems = new EstimateMainLineItemLogic().GetListByFilter(e => e.EstHeaderFk == targetEstHeaderId &&
																					e.BoqHeaderFk.HasValue &&
																					e.BoqItemFk.HasValue &&
																					targetBoQHeaderIds.Contains(e.BoqHeaderFk.Value) &&
																					targetBoQItemIds.Contains(e.BoqItemFk.Value) &&
																					e.LineItemType == 0 &&
																					!e.IsTemp)?.ToList() ?? new List<EstLineItemEntity>();

			//Ignore 1: If resources exist for the line item generated by BoQ Driven Estimate, then moves to qualify the next BoQ position.
			// Find line items that have associated resources
			var lineItemsWithResources = allBoQTargetLineItems.Where(li => targetEstResources.Any(r => r.EstLineItemFk == li.Id))
																				  .ToList();

			// Group line items by composite key for faster checking
			var lineItemKeys = lineItemsWithResources
									  .Select(e => Tuple.Create(e.BoqHeaderFk.Value, e.BoqItemFk.Value))  // Create tuple of two fields
									  .Distinct()
									  .ToHashSet();

			// Filter BoQ items that have no matching line items
			var targetBoqItemsWithOutLineItemResources = matchingBoqItemList
																 .Where(item => !lineItemKeys.Contains(Tuple.Create(item.BoqHeaderFk, item.Id))) // Use tuple for comparison
																 .ToList();

			return targetBoqItemsWithOutLineItemResources;
		}

		/// <summary>
		/// Generate Estimate From Ref Boq wizard, Normal/Boq Driven Estimates, overwrite line items from source to target
		/// Overwrite properties from source line item to target line item
		/// </summary>
		/// <param name="sourceLineItem"></param>
		/// <param name="targetLineItem"></param>
		/// <param name="targetBoqItem"></param>
		/// <param name="isWicAssembly"></param>
		/// <returns></returns>
		public EstLineItemEntity OverWriteFromSourceLineItemToTarget(EstLineItemEntity sourceLineItem,
																						 EstLineItemEntity targetLineItem,
																		             IBoqItemEntity targetBoqItem,
																						 bool isWicAssembly)
		{
			if (sourceLineItem == null || targetLineItem == null || targetBoqItem == null)
			{
				return targetLineItem;
			}

			targetLineItem.WqQuantityTarget = targetBoqItem.Quantity;
			targetLineItem.WqQuantityTargetDetail = targetBoqItem.QuantityDetail;
			targetLineItem.QuantityTarget = targetBoqItem.QuantityAdj;
			targetLineItem.QuantityTargetDetail = targetBoqItem.QuantityAdjDetail;
			targetLineItem.BasUomFk = targetBoqItem.BasUomFk;
			targetLineItem.BasUomTargetFk = targetBoqItem.BasUomFk;

			targetLineItem.DescriptionInfo = sourceLineItem.DescriptionInfo;
			targetLineItem.CopyTranslate<EstLineItemEntity>(Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.DataLanguageId, new Func<EstLineItemEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

			targetLineItem.CostUnit = sourceLineItem.CostUnit;
			targetLineItem.CostUnitTarget = sourceLineItem.CostUnitTarget;

			targetLineItem.DirCostUnit = sourceLineItem.DirCostUnit;
			targetLineItem.DirCostUnitTarget = sourceLineItem.DirCostUnitTarget;
			targetLineItem.DirHoursUnit = sourceLineItem.DirHoursUnit;
			targetLineItem.DirHoursUnitTarget = sourceLineItem.DirHoursUnitTarget;
			targetLineItem.DruCostUnit = sourceLineItem.DruCostUnit;
			targetLineItem.DruCostUnitTarget = sourceLineItem.DruCostUnitTarget;
			targetLineItem.DruHoursUnit = sourceLineItem.DruHoursUnit;
			targetLineItem.DruHoursUnitTarget = sourceLineItem.DruHoursUnitTarget;
			targetLineItem.EntCostUnit = sourceLineItem.EntCostUnit;
			targetLineItem.EntCostUnitTarget = sourceLineItem.EntCostUnitTarget;
			targetLineItem.EntHoursUnit = sourceLineItem.EntHoursUnit;
			targetLineItem.EntHoursUnitTarget = sourceLineItem.EntHoursUnitTarget;
			targetLineItem.IndCostUnit = sourceLineItem.IndCostUnit;
			targetLineItem.IndCostUnitTarget = sourceLineItem.IndCostUnitTarget;
			targetLineItem.IndHoursUnit = sourceLineItem.IndHoursUnit;
			targetLineItem.IndHoursUnitTarget = sourceLineItem.IndHoursUnitTarget;

			targetLineItem.ForeignBudget1 = sourceLineItem.ForeignBudget1;
			targetLineItem.ForeignBudget2 = sourceLineItem.ForeignBudget2;
			targetLineItem.DayWorkRateTotal = sourceLineItem.DayWorkRateTotal;
			targetLineItem.DayWorkRateUnit = sourceLineItem.DayWorkRateUnit;

			targetLineItem.GrandCostUnit = sourceLineItem.GrandCostUnit;
			targetLineItem.GrandCostUnitTarget = sourceLineItem.GrandCostUnitTarget;
			targetLineItem.HoursUnit = sourceLineItem.HoursUnit;
			targetLineItem.HoursUnitTarget = sourceLineItem.HoursUnitTarget;

			targetLineItem.Quantity = sourceLineItem.Quantity;
			targetLineItem.QuantityTotal = sourceLineItem.QuantityTotal;
			targetLineItem.QuantityUnitTarget = sourceLineItem.QuantityUnitTarget;
			targetLineItem.QuantityFactor1 = sourceLineItem.QuantityFactor1;
			targetLineItem.QuantityFactor2 = sourceLineItem.QuantityFactor2;
			targetLineItem.QuantityFactor3 = sourceLineItem.QuantityFactor3;
			targetLineItem.QuantityFactor4 = sourceLineItem.QuantityFactor4;
			targetLineItem.ProductivityFactor = sourceLineItem.ProductivityFactor;
			targetLineItem.CostFactor1 = sourceLineItem.CostFactor1;
			targetLineItem.CostFactor2 = sourceLineItem.CostFactor2;

			targetLineItem.QuantityDetail = sourceLineItem.QuantityDetail;
			targetLineItem.QuantityTargetDetail = sourceLineItem.QuantityTargetDetail;
			targetLineItem.WqQuantityTargetDetail = sourceLineItem.WqQuantityTargetDetail;

			targetLineItem.QuantityFactorDetail1 = sourceLineItem.QuantityFactorDetail1;
			targetLineItem.QuantityFactorDetail2 = sourceLineItem.QuantityFactorDetail2;
			targetLineItem.ProductivityFactorDetail = sourceLineItem.ProductivityFactorDetail;

			targetLineItem.CostFactorDetail1 = sourceLineItem.CostFactorDetail1;
			targetLineItem.CostFactorDetail2 = sourceLineItem.CostFactorDetail2;

			targetLineItem.BaseCostTotal = sourceLineItem.BaseCostTotal;
			targetLineItem.BaseCostUnit = sourceLineItem.BaseCostUnit;
			targetLineItem.BudgetUnit = sourceLineItem.BudgetUnit;
			targetLineItem.BudgetDifference = sourceLineItem.BudgetDifference;
			targetLineItem.Budget = sourceLineItem.Budget;
			targetLineItem.BudgetMargin = sourceLineItem.BudgetMargin;

			targetLineItem.RevenueUnit = sourceLineItem.RevenueUnit;
			targetLineItem.Revenue = sourceLineItem.Revenue;
			targetLineItem.Margin1 = sourceLineItem.Margin1;
			targetLineItem.Margin2 = sourceLineItem.Margin2;

			targetLineItem.IsOptional = sourceLineItem.IsOptional;
			targetLineItem.IsOptionalIT = sourceLineItem.IsOptionalIT;
			targetLineItem.IsIncluded = sourceLineItem.IsIncluded;
			targetLineItem.IsFixedPrice = sourceLineItem.IsFixedPrice;
			targetLineItem.IsFixedBudget = sourceLineItem.IsFixedBudget;
			targetLineItem.IsFixedBudgetUnit = sourceLineItem.IsFixedBudgetUnit;
			targetLineItem.IsDisabled = sourceLineItem.IsDisabled;
			targetLineItem.IsNoMarkup = sourceLineItem.IsNoMarkup;
			targetLineItem.IsLumpsum = sourceLineItem.IsLumpsum;

			targetLineItem.ExternalCode = sourceLineItem.ExternalCode;

			targetLineItem.BoqWicCatFk = sourceLineItem.BoqWicCatFk > 0 ? sourceLineItem.BoqWicCatFk : null;
			targetLineItem.WicBoqItemFk = sourceLineItem.WicBoqItemFk;
			targetLineItem.WicBoqHeaderFk = sourceLineItem.WicBoqHeaderFk;

			targetLineItem.UserDefined1 = sourceLineItem.UserDefined1;
			targetLineItem.UserDefined2 = sourceLineItem.UserDefined2;
			targetLineItem.UserDefined3 = sourceLineItem.UserDefined3;
			targetLineItem.UserDefined4 = sourceLineItem.UserDefined4;
			targetLineItem.UserDefined5 = sourceLineItem.UserDefined5;

			if (isWicAssembly)
			{
				//Update Assembly info from project/master WIC Assembly
				targetLineItem.EstAssemblyFk = sourceLineItem.Id; //sourceLineItem.Id is project/master WIC Assembly Id
				targetLineItem.EstHeaderAssemblyFk = sourceLineItem.EstHeaderFk;
			}
	
			return targetLineItem;
		}
	}
}
