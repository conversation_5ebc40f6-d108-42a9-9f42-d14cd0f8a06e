using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi;
using RIB.Visual.Documents.Project.BusinessComponents;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using RIB.Visual.Documents.Project.BusinessComponents.Logic;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Common.Core;
using System.IO;
using Microsoft.Graph;
using PnP.Framework.Extensions;

namespace RIB.Visual.Documents.Project.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("documents/centralquery/sharepoint")]
	public class DocumentSharePointController : ApiControllerBase<DocumentSharePointLogic>
	{
		/// <summary>
		/// 
		/// </summary>
		public DocumentSharePointController()
		{
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("getsharelink")]
		public MsOfficeApiResult<string> GetShareLink(SharePointGetShareLinkRequestDto request)
		{
			OfficeAuthParams authParams = new OfficeAuthParams();
			authParams.AuthType = OfficeAuthType.AccessToken;
			authParams.AccessToken = request.AccessToken;

			var documentSharePointLogic = new DocumentSharePointLogic(authParams);

			return documentSharePointLogic.GetShareLink(request.OnCopy(), request.AadUserId);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("configurationlist")]
		public IEnumerable<DocSpProjectConfigDto> GetSharePointConfigurationList()
		{
			return Logic.GetSharePointConfigurationList().ToDtos(e => new DocSpProjectConfigDto(e)) ;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("syncprojects")]
		public bool SyncProjects(SyncProjectsRquest request)
		{
			OfficeAuthParams authParams = new OfficeAuthParams();
			authParams.AuthType = OfficeAuthType.AccessToken;
			authParams.AccessToken = request.AccessToken;

			var documentSharePointLogic = new DocumentSharePointLogic(authParams);

			return documentSharePointLogic.SyncProjects(request.Dtos.Select(e => e.Copy()), request.AccessToken, request.RefreshToken, request.AadUserId);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("checkprojectsissynced")]
		public object CheckProjectsIsSynced(CheckProjectsIsSyncedRequest request)
		{
			OfficeAuthParams authParams = new OfficeAuthParams();
			authParams.AuthType = OfficeAuthType.AccessToken;
			authParams.AccessToken = request.AccessToken;

			var documentSharePointLogic = new DocumentSharePointLogic(authParams);

			return documentSharePointLogic.CheckProjectsIsSynced(request.Dtos.Select(e => e.Copy()));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("syncdetailstruct")]
		public object SyncDetailStruct(SyncDetailStructRequest request)
		{
			OfficeAuthParams authParams = new OfficeAuthParams();			
			authParams.AuthType = OfficeAuthType.AccessToken;
			authParams.AccessToken = request.AccessToken;

			var documentSharePointLogic = new DocumentSharePointLogic(authParams);

			return documentSharePointLogic.SyncDetailStruct(request.PrjIds);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		[HttpPost]
		[Route("addfolderpermission")]
		public void AddFolderPermission(SyncDetailStructRequest request)
		{
			OfficeAuthParams authParams = new OfficeAuthParams();
			authParams.AuthType = OfficeAuthType.AccessToken;
			authParams.AccessToken = request.AccessToken;

			var documentSharePointLogic = new DocumentSharePointLogic(authParams);

			documentSharePointLogic.AddFolderPermission(request.PrjIds);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="PrjId"></param>
		/// <param name="IsAutoSync"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("updateisautosync")]
		public bool UpdateIsAutoSync(int PrjId, bool IsAutoSync)
		{
			return new DocumentSharePointLogic().UpdateIsAutoSync(PrjId, IsAutoSync);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("getaadusers")]
		public object GetUsers()
		{
			var authParams = new OfficeAuthParams();
			authParams.AuthType = OfficeAuthType.UserNameAndPassword;
			var documentSharePointLogic = new DocumentSharePointLogic(authParams);
			return documentSharePointLogic.GetAadUsers();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("shareviasharepoint")]
		public bool ShareViaSharePoint(SendManualSyncInfoDto request)
		{
			return Logic.ShareViaSharePoint(request.OnCopy());
		}

		///// <summary>
		///// 
		///// </summary>
		///// <param name="request"></param>
		///// <returns></returns>
		//[HttpPost]
		//[Route("inviteusers")]
		//public bool InviteUser(SendManualSyncInfoDto request)
		//{
		//	return Logic.InviteUsers(request.OnCopy());
		//}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="docRevisionId"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("replaceexistspdoc")]
		public SpResponseDto ReplaceExistSpDoc(int docRevisionId)
		{
			return new SpResponseDto(Logic.ReplaceFileSpDoc(docRevisionId));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("saveglobametadatatype")]
		public SpResponseDto SaveGlobalMetaDataType(SaveGlobalMetaDataTypeRequest request)
		{
			var result = new DocSPProjectMetaDataLogic().SaveGlobalMetadataTypes(request.TypesBaseCompany, request.TypesBaseGlobal);
			return new SpResponseDto(result);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("getglobalmetadata")]
		public IEnumerable<DocSpprjMetaDataEntity> GetGlobalMetaDataType()
		{
			return new DocSPProjectMetaDataLogic().GetGlobalMetadata();
		}

		/// <summary>
		/// down load sharepoint files by prj documents
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("downloadspfiles")]
		public Dictionary<int, int> DownloadSpFiles(DownloadSpFilesRequest request)
		{
			OfficeAuthParams authParams = new OfficeAuthParams();
			authParams.AuthType = OfficeAuthType.AccessToken;
			authParams.AccessToken = request.AccessToken;

			var documentSharePointLogic = new DocumentSharePointLogic(authParams);

			return documentSharePointLogic.DownloadSpFiles(request.Dtos.Select(e => e.Copy()));
		}

		/// <summary>
		/// down load sharepoint files by prj document revisions
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("downloadspfilesbydocrevs")]
		public Dictionary<int, int> DownloadSpFilesByDocRevs(DownloadSpFilesByDocRevsRequest request)
		{
			OfficeAuthParams authParams = new OfficeAuthParams();
			authParams.AuthType = OfficeAuthType.AccessToken;
			authParams.AccessToken = request.AccessToken;

			var documentSharePointLogic = new DocumentSharePointLogic(authParams);

			return new DocumentSharePointLogic(authParams).DownloadSpFilesByDocRevs(request.Dtos.Select(e => e.Copy()));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="prjIds"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("getfoldercountbyprjs")]
		public List<KeyValuePair<int, int>> GetFolderCountByPrjs(IEnumerable<int> prjIds)
		{
			return Logic.GetFolderCountByPrjs(prjIds);
		}
	}
}
