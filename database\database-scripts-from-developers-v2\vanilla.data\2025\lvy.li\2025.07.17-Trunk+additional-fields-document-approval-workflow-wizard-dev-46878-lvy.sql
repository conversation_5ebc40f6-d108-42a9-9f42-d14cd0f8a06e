------------------------------------------------------- 
-- <PERSON><PERSON> Ticket (REQUIRED): DEV-46878
-- Script Type (REQUIRED): Required Vanilla data
-- Reason (REQUIRED): Additional fields to be provided on the document approval/declined dialog box (UI)
-- Install On (OPTIONAL): Trunk
-------------------------------------------------------

DECLARE
@WORKFLOW_ID INT,
@NEW_VERSION_ID INT;
SET @WORKFLOW_ID = 83;
SET @NEW_VERSION_ID = 2957;

-- Change status
UPDATE [dbo].[WFE_TEMPLATEVERSION]
	SET [STATUS] = 3
	WHERE [WFE_TEMPLATE_FK] = @WORKFLOW_ID AND [STATUS] = 2

-- Insert WFE_TEMPLATEVERSION
print ''
print 'start importing for table: WFE_TEMPLATEVERSION '

IF NOT EXISTS (SELECT 1 FROM WFE_TEMPLATEVERSION WHERE ID = @NEW_VERSION_ID)
BEGIN
INSERT INTO [WFE_TEMPLATEVERSION]([ID],[TEMPLATEVERSION],[STATUS],[COMMENT],[HELPTEXT],[CONTEXT],[WORKFLOWACTION],[WFE_TEMPLATE_FK],[LIFETIME],[VALIDFROM],[VALIDTO],[REVISIONDATE],[BAS_CLERK_FK],[INSERTED],[WHOISR],[UPDATED],[WHOUPD],[VERSION])
SELECT t.* FROM (VALUES
  (@NEW_VERSION_ID,
  5,
  2,
  N'Vanilla Data Version 17.07.2025',
  NULL,
  N'""',
  N'{"id":1,"code":"","description":"Start","documentList":null,"actionTypeId":1,"actionId":"0","priorityId":1,"transitions":[{"id":94125,"parameter":null,"workflowAction":{"id":39857,"code":"","description":"Get EntityIds","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","lifeTime":1,"transitions":[{"id":17047,"parameter":null,"workflowAction":{"id":78580,"code":"","description":"get issuer","documentList":[],"actionTypeId":5,"actionId":"6f3a49b7c1b94448886a868625829e4d","userId":"","lifeTime":1,"transitions":[{"id":42772,"parameter":null,"workflowAction":{"id":37740,"code":"","description":"get issued date","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","lifeTime":1,"transitions":[{"id":99985,"parameter":null,"workflowAction":{"id":99986,"code":"","description":"can''t start workflow list","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","lifeTime":1,"transitions":[{"id":95636,"parameter":null,"workflowAction":{"id":73129,"code":"","description":"Context.notStartList.length==0","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","lifeTime":1,"transitions":[{"id":99987,"parameter":"true","workflowAction":{"id":99988,"code":"GT0","description":"can start workflow list (CODE:GT0)","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","lifeTime":1,"transitions":[{"id":54278,"parameter":null,"workflowAction":{"id":51233,"code":"","description":"Formula","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","lifeTime":1,"transitions":[{"id":23253,"parameter":"true","workflowAction":{"id":85143,"code":"","description":"Init","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":89460,"key":"Script","value":"Context.lev1ClerkId=[];\nContext.lev2ClerkId=[];\nContext.lev3ClerkId=[];\n\nContext.round=0;\n\nContext.declineStatusId=3;\nvar userId = parseInt(Context.UserId, 10);\nContext.UserId = userId;\nvar clerk=ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"SELECT ID FROM BAS_CLERK WHERE FRM_USER_FK=\"+userId\n    }).Output ;\n\nif (clerk!==[]){\n    Context.triggerClerkId=clerk[0].ID;\n}\n\n"},{"id":82634,"key":"Context","value":""}],"output":[{"id":116336,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":21966,"parameter":null,"workflowAction":{"id":95037,"code":"","description":"select approver","documentList":[],"actionTypeId":6,"actionId":"000019b479164ad1adeb7631d3fd6161","userId":"","input":[{"id":73921,"key":"HTML","value":"\n\n\n<div style=\"font: 13px/normal source_sans,Microsoft YaHei,STHeiti;width:600px;overflow-y:auto;padding:15px\">\n\n<div class=\"platform-form-group\">\n<div class=\"platform-form-row form-group\">\n    <label class=\"platform-form-label\" style=\"min-width: 111px;padding:0\"> {{''documents.centralquery.workflow.chooseApprovalLevelTitle'' | translate}}</label>\n    <div class=\"platform-form-col\">\n        <div class=\"input-group domain-type-select form-control\">\n            <div class=\"input-group-content ng-pristine ng-valid ng-not-empty ng-touched\" data-platform-select-converter=\"\" data-platform-select-handler=\"\" data-ng-model=\"Context.selectedLel\" data-domain=\"select\" data-config=\"selectConfig\" >\n            </div>\n            <div class=\"input-group-btn\">\n                <button type=\"button\" class=\"btn btn-default dropdown-toggle\">\n                    <span class=\"caret\"></span>\n                </button>\n            </div>\n        </div>\n    </div>\n</div>\n</div>\n\n<div ng-if=\"Context.selectedLel==1\" class=\"picture-icons ico-1-level-approval\" style=\"height:100px;width:100%\"></div>\n\n<div ng-if=\"Context.selectedLel==2\" class=\"picture-icons ico-2-level-approval\" style=\"height:100px;width:100%\"></div>\n\n<div ng-if=\"Context.selectedLel==3\" class=\"picture-icons ico-3-level-approval\" style=\"height:100px;width:100%\"></div>\n\n<div id=\"lev1\" ng-if=\"Context.selectedLel>=1\">\n    <label style=\"float: left;margin: 15px 0;\" >{{''documents.centralquery.workflow.assignApproversForLeve1'' | translate}}: </label>\n\n    <div class=\"platform-form-col\" data-ng-init=''Context.Lev1ApprType=\"s\"'' style=\"float: right;margin: 15px 0;\">\n        <label  style=\"margin-right: 5px;\"><input style=\"vertical-align: sub;margin-right: 5px;\" type=\"radio\"  name=\"apprType1\" data-ng-model = \"Context.Lev1ApprType\"  value=''s''>{{''documents.centralquery.workflow.singleApproval'' | translate}}</label>\n        <label ><input style=\"vertical-align: sub;margin-right: 5px;\" type=\"radio\"  name=\"apprType1\" data-ng-model = \"Context.Lev1ApprType\"  value=''g''>{{''documents.centralquery.workflow.groupApproval'' | translate}}</label>\n    </div>\n    <div class=\"platform-form-row\" >\n\n\n             <div class=\"platform-form-col\" data-ng-init=\"clerkConfig = {lookupDirective:''cloud-clerk-clerk-dialog'', descriptionMember:''Description'', lookupOptions: { multipleSelection: true,showClearButton:true}};\" >\n\n                    <div class=\"lg-4 md-4\" data-ng-model=\"Context.lev1ClerkId\" data-entity=\"Context \" data-options=\"clerkConfig\"\n                data-basics-lookupdata-lookup-composite>\n                    </div>\n            </div>\n     </div>\n\n</div>\n\n<div id=\"lev2\" ng-if=\"Context.selectedLel>=2\">\n    <label style=\"float: left;margin: 15px 0;\" >{{''documents.centralquery.workflow.assignApproversForLeve2'' | translate}}: </label>\n\n    <div class=\"platform-form-col\" data-ng-init=''Context.Lev2ApprType=\"s\"'' style=\"float: right;margin: 15px 0;\">\n        <label style=\"margin-right: 5px;\" ><input style=\"vertical-align: sub;margin-right: 5px;\" type=\"radio\"  name=\"apprType2\" data-ng-model = \"Context.Lev2ApprType\"  value=''s''>{{''documents.centralquery.workflow.singleApproval'' | translate}}</label>\n        <label ><input style=\"vertical-align: sub;margin-right: 5px;\" type=\"radio\"  name=\"apprType2\" data-ng-model = \"Context.Lev2ApprType\"  value=''g''>{{''documents.centralquery.workflow.groupApproval'' | translate}}</label>\n    </div>\n    <div class=\"platform-form-row\" >\n\n\n\n             <div class=\"platform-form-col\" data-ng-init=\"clerkConfig = {lookupDirective:''cloud-clerk-clerk-dialog'', descriptionMember:''Description'', lookupOptions: { multipleSelection: true,showClearButton:true}};\" >\n\n                    <div class=\"lg-4 md-4\" data-ng-model=\"Context.lev2ClerkId\" data-entity=\"Context \" data-options=\"clerkConfig\"\n                data-basics-lookupdata-lookup-composite>\n                    </div>\n            </div>\n     </div>\n\n</div>\n\n<div id=\"lev3\" ng-if=\"Context.selectedLel>=3\">\n    <label style=\"float: left;margin: 15px 0;\" >{{''documents.centralquery.workflow.assignApproversForLeve3'' | translate}}:: </label>\n\n    <div class=\"platform-form-col\" data-ng-init=''Context.Lev3ApprType=\"s\"'' style=\"float: right;margin: 15px 0;\">\n        <label style=\"margin-right: 5px;\"><input style=\"vertical-align: sub;margin-right: 5px;\" type=\"radio\"  name=\"apprType3\" data-ng-model = \"Context.Lev3ApprType\"  value=''s''>{{''documents.centralquery.workflow.singleApproval'' | translate}}</label>\n        <label ><input style=\"vertical-align: sub;margin-right: 5px;\" type=\"radio\"  name=\"apprType3\" data-ng-model = \"Context.Lev3ApprType\"  value=''g''>{{''documents.centralquery.workflow.groupApproval'' | translate}}</label>\n    </div>\n    <div class=\"platform-form-row\" >\n\n\n             <div class=\"platform-form-col\" data-ng-init=\"clerkConfig = {lookupDirective:''cloud-clerk-clerk-dialog'', descriptionMember:''Description'', lookupOptions: { multipleSelection: true,showClearButton:true}};\" >\n\n                    <div class=\"lg-4 md-4\" data-ng-model=\"Context.lev3ClerkId\" data-entity=\"Context \" data-options=\"clerkConfig\"\n                data-basics-lookupdata-lookup-composite>\n                    </div>\n            </div>\n     </div>\n\n</div>\n\n\n\n<div style=\"margin: 15px 0\">\n    <label style=\"line-height:21px; float:left; width:90px;display: inline-block;\">  {{''documents.centralquery.workflow.comments'' | translate}} : </label>\n    <textarea style=\"height: 50px;overflow:hidden;display: inline-block;width: calc(100% - 90px)\" ng-model=\"Context.comment\"></textarea>\n</div>\n\n</div>\n\n\n<footer class=\"modal-footer \">\n<button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"onCancel()\">\n      {{''documents.centralquery.workflow.cancel'' | translate}}\n</button>\n<button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"onOk()\" data-ng-disabled=\"Context.selectedLel==1?Context.lev1ClerkId.length==0:(Context.selectedLel==2?Context.lev1ClerkId.length==0 || Context.lev2ClerkId.length==0:(Context.selectedLel==3?Context.lev1ClerkId.length==0 || Context.lev2ClerkId.length==0 || Context.lev3ClerkId.length==0:true))\">\n{{''documents.centralquery.workflow.ok'' | translate}}\n</button>\n\n</footer>\n\n\n\n"},{"id":90093,"key":"Script","value":"var $translate = $injector.get(''$translate'');\n$scope.Context.selectedLel=1;\n\n$scope.selectConfig = {\n    type: ''select'',\n    options: {\n        displayMember: \"description\",\n        valueMember: \"id\",\n        items: [{\n          id: 1,\n          description: $translate.instant(''documents.centralquery.workflow.levelApproval1'')\n        },{\n          id: 2,\n          description:$translate.instant(''documents.centralquery.workflow.levelApproval2'')\n        },\n           {\n          id: 3,\n           description:$translate.instant(''documents.centralquery.workflow.levelApproval3'')\n        }    ]\n    }\n};\n$scope.entityConfig = 1;\n\n"},{"id":101183,"key":"Context","value":"{{Context}}"},{"id":120784,"key":"IsPopUp","value":"true"},{"id":47692,"key":"EvaluateProxy","value":""},{"id":37147,"key":"DisableRefresh","value":""},{"id":58947,"key":"AllowReassign","value":""},{"id":66485,"key":"Title","value":"Document Approval"},{"id":72599,"key":"Subtitle","value":""},{"id":83268,"key":"DialogConfig","value":""}],"output":[{"id":66158,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":42030,"parameter":null,"workflowAction":{"id":77949,"code":"","description":"get approver array length","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":129545,"key":"Script","value":"\nContext.length1=Context.lev1ClerkId.length;\nContext.length2=Context.lev2ClerkId.length;\nContext.length3=Context.lev3ClerkId.length;"},{"id":126221,"key":"Context","value":""}],"output":[{"id":94230,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":31156,"parameter":null,"workflowAction":{"id":50390,"code":"","description":"level 1 init","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":85941,"key":"Script","value":"Context.counter1=0;\n\nContext.approvedCounter1=0;\n\nContext.apprRowNumAry1=[];\nContext.apprRowNumStr1='''';\n\nContext.declRowNumAry1=[];\nContext.declRowNumStr1='''';\n\n\nif (Context.Lev1ApprType==''s''){\n  Context.passApprNum1=1;\n} else {Context.passApprNum1=Context.length1;}\n"},{"id":98862,"key":"Context","value":""}],"output":[{"id":70448,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":86390,"parameter":null,"workflowAction":{"id":88902,"code":"GT1","description":"set clerkid (GT1)","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":92896,"key":"Script","value":"\n\n  Context.clerkId1=Context.lev1ClerkId[Context.counter1];\n"},{"id":101051,"key":"Context","value":""}],"output":[{"id":84202,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":54392,"parameter":null,"workflowAction":{"id":58842,"code":"","description":"send ToDo (level 1)","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":92777,"key":"Formula","value":"1===1"},{"id":124026,"key":"Context","value":""}],"output":[{"id":99135,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":10720,"parameter":"true","workflowAction":{"id":47260,"code":"","description":"Level 1 approving","documentList":[],"actionTypeId":6,"actionId":"000019b479164ad1adeb7631d3fd6161","userId":"{{Context.clerkId1}}","input":[{"id":121323,"key":"HTML","value":"<div style=\"font: 13px/normal source_sans,Microsoft YaHei,STHeiti;margin:10px;\">\n  <lable>{{''documents.centralquery.workflow.issuer'' | translate}}: {{Context.issuer[0].NAME}}</lable> \n  <label style=\"float:right;margin-right:300px;\">{{''documents.centralquery.workflow.issuedDate'' | translate}}: {{Context.issuedDate}} </label>\n  <br><br>\n<div style=\"display:inline-block\">\n  <label style=\"float:left;margin-right:10px;\">{{''documents.centralquery.workflow.issuerComment'' | translate}}:</label>\n\n<textarea style=\"width:660px;height:70px;border:none;resize:none;\" readonly=\"readonly\">{{Context.comment}}</textarea>\n\n</div>\n\n<br>\n\n    <div style=\"width:100%;\">\n\n        <p>{{''documents.centralquery.workflow.reviewAndApprovalDocuments'' | translate}}:</p>\n        <div class=\"toolbar\" style=\"flex-flow: row-reverse;\">\n            <ul class=\"tools list-items-1998fd12b31ae25aef0ed08b46c1a435\">\n                <li class=\"collapsable\">\n                    <button type=\"button\" class=\" tlb-icons ico-preview-form\" title=\"Preview\" data-ng-click=\"preview()\"><span>{{''documents.centralquery.workflow.preview'' | translate}}</span>\n                    </button>\n                </li>\n\n                <li class=\"collapsable\">\n                    <button type=\"button\" class=\" tlb-icons ico-download\" title=\"Download\" data-ng-click=\"download()\"><span>{{''documents.centralquery.workflow.download'' | translate}}</span>\n                    </button>\n                </li>\n            </ul>\n        </div>\n        \n        <div class=\"platformgrid grid-container\"  style=\"height: 180px;font-size:12px;margin-top:0;overflow-x:scroll;overflow-y: auto;\">\n\n            <platform-Grid data=\"gridData\" ></platform-Grid>\n        </div>\n    </div>\n\n\n\n        <div>\n            \n            <div >\n                <label style=\"display: block;margin: 10px 0\" >{{''documents.centralquery.workflow.approvalComment'' | translate}}</label>\n                <textarea style=\"width:100%;height: 60px;\" ng-model=\"Context.apprCom1\" ng-init=\"Context.apprCom1=''''\"></textarea>\n            </div>\n\n            <div >\n                <label style=\"display: block;margin: 10px 0\" >{{''documents.centralquery.workflow.declineComment'' | translate}}</label>\n                <textarea style=\"width:100%;height: 60px;\" ng-model=\"Context.declCom1\" ng-init=\"Context.declCom1=''''\"></textarea>\n            </div>\n\n\n        </div>\n\n\n\n</div>\n\n\n<footer class=\"modal-footer \">\n    <button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"isFinished();\" ng-disabled=\"isDisabled\">{{''documents.centralquery.workflow.ok'' | translate}}</button>\n</footer>\n"},{"id":69907,"key":"Script","value":"var $state = $injector.get(''$state'');\nvar gridAPI = $injector.get(''platformGridAPI'');\nvar cloudDesktopSidebarService = $injector.get(''cloudDesktopSidebarService'');\nvar basicsCommonFileDownloadService = $injector.get(''basicsCommonFileDownloadService'');\nvar basicsCommonServiceUploadExtension = $injector.get(''basicsCommonServiceUploadExtension'');\nvar $translate = $injector.get(''$translate'');\n$scope.gridId = ''doc'';\n$scope.gridData = {state: $scope.gridId};\n$scope.isDisabled=true;\n\nvar checkDocIds=[];\n$scope.selectRows = [];\n\n\nsetTimeout(function() {\n    var gridIns = gridAPI.grids.element(''id'', $scope.gridId);\n    gridIns.scope.vm.destroyGrid = function() {};\n}, 1000);\n\n\nfunction reCalculate(){\n\n    var sumAppr=0;\n    var sumDecl=0;\n    var docLen=$scope.Context.docInfo1.length;\n\n    for (var i = 0; i < $scope.Context.docInfo1.length; i++) {\n\n        if ($scope.Context.docInfo1[i].Approve){\n            sumAppr++;\n        }\n\n        if ($scope.Context.docInfo1[i].Decline){\n            sumDecl++;\n        }\n\n    }\n\n    if (sumAppr+sumDecl==docLen){\n        return false;\n    } else {return true;}\n}\n$scope.reCalculate=reCalculate;\n\nfunction checkApprove(item,value,fieldName){\n    item[fieldName] = value;\n    if(value && item.Decline){\n        item.Decline = false;\n    }\n    $scope.isDisabled=reCalculate();\n}\nfunction checkDecline(item,value,fieldName){\n    item[fieldName] = value;\n    if(value && item.Approve){\n        item.Approve = false;\n    }\n    $scope.isDisabled=reCalculate();\n}\nvar columns = [\n    {\n        id: 1,\n        formatter: ''boolean'',\n        field: \"Approve\",\n         name: $translate.instant(''documents.centralquery.workflow.approval''),\n        sortable: true,\n        editor: \"boolean\",\n        width: 80,\n        readonly: false,\n        validator:checkApprove\n    },\n    {\n        id:2,\n        formatter: ''boolean'',\n        field: \"Decline\",\n        name: $translate.instant(''documents.centralquery.workflow.decline''),\n        sortable: true,\n        editor: \"boolean\",\n        width: 80,\n        readonly: false,\n        validator:checkDecline\n    },\n    {\n        id: 3,\n        formatter: ''description'',\n        field: \"DESCRIPTION\",\n        name: $translate.instant(''documents.centralquery.workflow.file''),\n        sortable: true,\n        grouping: {},\n        editor: \"\",\n        width:250,\n        editorOptions: {},\n        showClearButton: true,\n        lookupOptions: {},\n        readonly: true\n    },\n    {\n        id: 4,\n        formatter: ''description'',\n        field: \"PROJECT\",\n        name: $translate.instant(''documents.centralquery.workflow.project''),\n        sortable: true,\n        width:250,\n        readonly: true\n    },\n    {\n        id: 5,\n        formatter: ''description'',\n        field: \"PROGRESSBY\",\n        name: $translate.instant(''documents.centralquery.workflow.approver''),\n        sortable: true,\n        width:95,\n        readonly: true\n    }\n\n];\nvar grid = {\n    columns: columns,\n    data: [],\n    id: $scope.gridId,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data=$scope.Context.docInfo1;\n\ngridAPI.grids.config(grid);\ngridAPI.items.data($scope.gridId,data);\ngridAPI.events.register($scope.gridId, ''onSelectedRowsChanged'', onSelectedRowsChanged);\n\nfunction onSelectedRowsChanged(scope, data) {\n    if (data.rows.length ===0 ) {\n        $scope.selectRows = [];\n    }\n    else {\n        $scope.selectRows = data.rows;\n    }\n}\nvar isFinished = function() {\n\n    for (var i = 0; i < $scope.Context.docInfo1.length; i++) {\n\n        var rowNum=$scope.Context.docInfo1[i].ROW_NUM;\n\n        if ($scope.Context.docInfo1[i].Approve) {\n            $scope.Context.apprRowNumAry1.push(rowNum);\n        } else if ($scope.Context.docInfo1[i].Decline) {\n            $scope.Context.declRowNumAry1.push(rowNum);\n\n        }\n\n    }\n\n    $scope.Context.apprRowNumStr1=JSON.stringify($scope.Context.apprRowNumAry1);\n    $scope.Context.declRowNumStr1=JSON.stringify($scope.Context.declRowNumAry1);\n\n\n    $scope.onOk();\n};\n\nvar download = function() {\n    if ($scope.selectRows.length) {\n        var archiveDocFks = [];\n        for(var i=0; i<$scope.selectRows.length; i++ ) {\n            var cur = $scope.Context.docInfo1[$scope.selectRows[i]];\n            archiveDocFks.push(cur[''BAS_FILEARCHIVEDOC_FK'']);\n        }\n        basicsCommonFileDownloadService.download(archiveDocFks);\n    }\n};\n\nvar preview = function() {\n    if ($scope.selectRows.length) {\n        service.previewDocument($scope,true);\n    }\n};\n\n$scope.isFinished = isFinished;\n$scope.download = download;\n$scope.preview = preview;\n\n\nvar service = {\n    getSelected: function() {\n        var cur = $scope.Context.docInfo1[$scope.selectRows[0]];\n        return cur;\n    }\n};\nbasicsCommonServiceUploadExtension.extendWidthPreview(service,{});\n\n$scope.$on(''$destroy'', function () {\n    gridAPI.grids.unregister($scope.gridId);\n});\n"},{"id":130303,"key":"Context","value":"{{Context}}"},{"id":84638,"key":"IsPopUp","value":""},{"id":64835,"key":"EvaluateProxy","value":""},{"id":65865,"key":"DisableRefresh","value":""},{"id":16598,"key":"AllowReassign","value":""},{"id":113247,"key":"Title","value":"Document Approval"},{"id":93681,"key":"Subtitle","value":""},{"id":82921,"key":"DialogConfig","value":"{\"minWidth\":\"800px\"}"}],"output":[{"id":92837,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":71467,"parameter":null,"workflowAction":{"id":25250,"code":"","description":"approvedCounter1++","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":96797,"key":"Script","value":"var instanceId = parseInt(Context.InstanceId, 10);\nContext.InstanceId = instanceId;\nvar approvedCounter= ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"select ISNULL(MAX(CONVERT(INT,USERDEFINED1)),0)+1 COUNTER FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND DESCRIPTION=N''Level 1 approving'' AND ISRUNNING=0 AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL \"\n    }).Output;\n\nContext.approvedCounter1=approvedCounter[0].COUNTER;"},{"id":85573,"key":"Context","value":""}],"output":[{"id":84532,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":99989,"parameter":null,"workflowAction":{"id":99990,"code":"","description":"update USERDEFINED1-5","documentList":[],"actionTypeId":5,"actionId":"6f3a49b7c1b94448886a868625829e4d","userId":"","lifeTime":1,"transitions":[{"id":68327,"parameter":null,"workflowAction":{"id":73810,"code":"","description":"Context.Lev1ApprType","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":80422,"key":"Formula","value":"Context.Lev1ApprType"},{"id":130262,"key":"Context","value":""}],"output":[{"id":79923,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":48327,"parameter":"s","workflowAction":{"id":16729,"code":"","description":"set unapproved task ISRUNNING=0","documentList":[],"actionTypeId":5,"actionId":"6f3a49b7c1b94448886a868625829e4d","userId":"","input":[{"id":117614,"key":"SQL","value":"UPDATE WFE_ACTIONINSTANCE SET ISRUNNING=0 WHERE WFE_INSTANCE_FK=@InstanceId AND DESCRIPTION=N''Level 1 approving''  AND ISRUNNING=1"},{"id":108009,"key":"Params","value":"InstanceId:{{Context.InstanceId}} ;"}],"output":[{"id":76550,"key":"Output","value":""}],"lifeTime":1,"transitions":[{"id":67573,"parameter":null,"workflowAction":{"id":89030,"code":"GT4","description":"level 1 summary (CODE:GT4)","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":118381,"key":"Script","value":"\n\n\nfunction clone(obj) {\n      var o;\n      if (typeof obj == \"object\") {\n        if (obj === null) {\n             o = null;\n         } else {\n             if (obj instanceof Array) {\n                 o = [];\n                 for (var i = 0, len = obj.length; i < len; i++) {\n                     o.push(clone(obj[i]));\n                 }\n             } else {\n                 o = {};\n                 for (var j in obj) {\n                     o[j] = clone(obj[j]);\n                 }\n             }\n         }\n     } else {\n         o = obj;\n     }\n     return o;\n }\n\nvar instanceId = parseInt(Context.InstanceId, 10);\nContext.InstanceId = instanceId;\nvar apprRowNum=ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"SELECT USERDEFINED2  AS APPR_ROW_NUM FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=\" +instanceId+\" AND ISRUNNING=0 AND DESCRIPTION=N''Level 1 approving'' AND USERDEFINED2 <>''''\"\n  }).Output;\n\nContext.apprRowNum1=apprRowNum;\n\nvar temp=[];\nvar len=apprRowNum.length;\n \nfor (var i=0;i<apprRowNum.length;i++){\n   temp=temp.concat(JSON.parse(apprRowNum[i].APPR_ROW_NUM));\n }\nContext.temp1=temp;\n\nvar groups={};\nfor (var i=0;i<temp.length;i++){\n  var item=temp[i];\n  if (groups.hasOwnProperty(item) === false ){\n        groups[item]= {times:0};\n  }\n  groups[item].times++;\n}\n\nContext.groups1=groups;\n\nvar apprRowNumResult=[];\nfor (var e in groups){\n  if (groups[e].times==len){\n   apprRowNumResult.push(e);\n  }\n  \n}\n\nContext.apprRowNumResult1=apprRowNumResult;\n\n\nvar declInfo=ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"SELECT WFE_ACTIONINSTANCE.ENDTIME AT TIME ZONE ''UTC'' AS DELINEDDATE, WFE_ACTIONINSTANCE.USERDEFINED3 AS DECL_ROW_NUM,BAS_CLERK_PROGRESSBY_FK,BAS_CLERK.DESCRIPTION  FROM WFE_ACTIONINSTANCE INNER JOIN BAS_CLERK ON BAS_CLERK.ID=WFE_ACTIONINSTANCE.BAS_CLERK_PROGRESSBY_FK WHERE WFE_INSTANCE_FK=\" +instanceId+\" AND ISRUNNING=0 AND WFE_ACTIONINSTANCE.DESCRIPTION=N''Level 1 approving'' AND WFE_ACTIONINSTANCE.USERDEFINED3 <>''''\"\n  }).Output;\n\nContext.declInfo1=declInfo;\n\nvar obj={};\n  for (var i=0;i<declInfo.length;i++){\n    var rowNumAry=JSON.parse(declInfo[i].DECL_ROW_NUM);\n    for (var j=0;j<rowNumAry.length;j++){ \n      \n        if (obj.hasOwnProperty(rowNumAry[j])===false){\n            obj[rowNumAry[j]]={ DESCRIPTION: '''', DELINEDDATE: '''' };\n        }\n\n        obj[rowNumAry[j]].DESCRIPTION += declInfo[i].DESCRIPTION+'' '';\n        if ( !obj[rowNumAry[j]].DELINEDDATE || new Date(obj[rowNumAry[j]].DELINEDDATE) > new Date(declInfo[i].DELINEDDATE)) {\n            obj[rowNumAry[j]].DELINEDDATE = declInfo[i].DELINEDDATE;\n        }\n      }      \n    }\n\nContext.temtObj1=obj;\n\nContext.apprDocInfo1=[];\n\nvar apprDocInfo=Context.docInfo1.filter(function(e){ return Context.apprRowNumResult1.indexOf(e.ROW_NUM.toString())>-1;});\n\nContext.apprDocInfo1=clone(apprDocInfo);\n\nContext.declDocInfo1=[];\n\nfor (var rownum in obj){\n\n  for (var i=0;i<Context.docInfo1.length;i++){\n\n    if (Context.docInfo1[i].ROW_NUM.toString()===rownum){\n\n        Context.docInfo1[i].PROGRESSBY=obj[rownum].DESCRIPTION;\n        Context.docInfo1[i].DELINEDDATE=obj[rownum].DELINEDDATE;\n        Context.declDocInfo1.push(clone(Context.docInfo1[i]));\n\n    }\n  }  \n}\n\n\nvar apprCom= ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"SELECT WFE_ACTIONINSTANCE.ENDTIME AT TIME ZONE ''UTC'' AS APPROVALDATE, BAS_CLERK_PROGRESSBY_FK,BAS_CLERK.DESCRIPTION,WFE_ACTIONINSTANCE.USERDEFINED4 AS COMMENT FROM WFE_ACTIONINSTANCE INNER JOIN BAS_CLERK ON BAS_CLERK.ID=WFE_ACTIONINSTANCE.BAS_CLERK_PROGRESSBY_FK WHERE WFE_INSTANCE_FK=\" +instanceId+\" AND WFE_ACTIONINSTANCE.DESCRIPTION=N''Level 1 approving'' AND ISRUNNING=0 AND WFE_ACTIONINSTANCE.USERDEFINED4<>''''\"\n    }).Output;\n\n    Context.apprComResult1=apprCom;\n\n     var declCom= ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"SELECT WFE_ACTIONINSTANCE.ENDTIME AT TIME ZONE ''UTC'' AS DELINEDDATE, BAS_CLERK_PROGRESSBY_FK,BAS_CLERK.DESCRIPTION,WFE_ACTIONINSTANCE.USERDEFINED5 AS COMMENT FROM WFE_ACTIONINSTANCE INNER JOIN BAS_CLERK ON BAS_CLERK.ID=WFE_ACTIONINSTANCE.BAS_CLERK_PROGRESSBY_FK WHERE WFE_INSTANCE_FK=\" +instanceId+\" AND WFE_ACTIONINSTANCE.DESCRIPTION=N''Level 1 approving'' AND ISRUNNING=0 AND WFE_ACTIONINSTANCE.USERDEFINED5<>''''\"\n    }).Output;\n\n    Context.declComResult1=declCom;\n  \n\n"},{"id":87845,"key":"Context","value":""}],"output":[{"id":99580,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":34089,"parameter":null,"workflowAction":{"id":35033,"code":"","description":"round++","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":114652,"key":"Script","value":"Context.round++;"},{"id":107598,"key":"Context","value":""}],"output":[{"id":81571,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":80871,"parameter":null,"workflowAction":{"id":59838,"code":"","description":"round<selectedLel","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":104016,"key":"Formula","value":"Context.round<Context.selectedLel"},{"id":108768,"key":"Context","value":""}],"output":[{"id":91477,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":40569,"parameter":"true","workflowAction":{"id":70713,"code":"","description":"level 2 init","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":90274,"key":"Script","value":"function clone(obj) {\n      var o;\n      if (typeof obj == \"object\") {\n        if (obj === null) {\n             o = null;\n         } else {\n             if (obj instanceof Array) {\n                 o = [];\n                 for (var i = 0, len = obj.length; i < len; i++) {\n                     o.push(clone(obj[i]));\n                 }\n             } else {\n                 o = {};\n                 for (var j in obj) {\n                     o[j] = clone(obj[j]);\n                 }\n             }\n         }\n     } else {\n         o = obj;\n     }\n     return o;\n }\n\n\n\n\nContext.counter2=0;\n\nContext.approvedCounter2=0;\n\nContext.apprRowNumAry2=[];\nContext.apprRowNumStr2='''';\n\nContext.declRowNumAry2=[];\nContext.declRowNumStr2='''';\n\n\nif (Context.Lev2ApprType==''s''){\n  Context.passApprNum2=1;\n} else {Context.passApprNum2=Context.length2;}\n\n\nContext.docInfo2=[];\n\nfor (var i=0;i<Context.docInfo1.length;i++){\n  \n  var a=Context.docInfo1.slice(i, i+1); \n  var b=clone(a);\n  delete b[0].Approve;\n  \n  if (Context.apprRowNumResult1.indexOf(Context.docInfo1[i].ROW_NUM.toString())>-1){\n    Context.docInfo2.push(b[0]);    \n  }\n  \n}\n\n\n\n\n\n"},{"id":91423,"key":"Context","value":""}],"output":[{"id":85871,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":53795,"parameter":null,"workflowAction":{"id":85057,"code":"GT2","description":"set clerkid (GT2)","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":69537,"key":"Script","value":"\n\n  Context.clerkId2=Context.lev2ClerkId[Context.counter2];\n"},{"id":111416,"key":"Context","value":""}],"output":[{"id":114813,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":61472,"parameter":null,"workflowAction":{"id":46154,"code":"","description":"send ToDo (level 2)","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":77868,"key":"Formula","value":"1===1"},{"id":85773,"key":"Context","value":""}],"output":[{"id":119604,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":56503,"parameter":"true","workflowAction":{"id":11002,"code":"","description":"Level 2 approving","documentList":[],"actionTypeId":6,"actionId":"000019b479164ad1adeb7631d3fd6161","userId":"{{Context.clerkId2}}","input":[{"id":121323,"key":"HTML","value":"<div style=\"font: 13px/normal source_sans,Microsoft YaHei,STHeiti;margin:10px;\">\n  <lable>{{''documents.centralquery.workflow.issuer'' | translate}}: {{Context.issuer[0].NAME}}</lable> \n  <label style=\"float:right;margin-right:300px;\">{{''documents.centralquery.workflow.issuedDate'' | translate}}: {{Context.issuedDate}} </label>\n  <br><br>\n<div style=\"display:inline-block\">\n  <label style=\"float:left;margin-right:10px;\">{{''documents.centralquery.workflow.issuerComment'' | translate}}:</label>\n\n<textarea style=\"width:660px;height:70px;border:none;resize:none;\" readonly=\"readonly\">{{Context.comment}}</textarea>\n\n</div>\n\n<br>\n\n    <div style=\"width:100%;\">\n\n        <p>{{''documents.centralquery.workflow.reviewAndApprovalDocuments'' | translate}}:</p>\n        <div class=\"toolbar\" style=\"flex-flow: row-reverse;\">\n            <ul class=\"tools list-items-1998fd12b31ae25aef0ed08b46c1a435\">\n                <li class=\"collapsable\">\n                    <button type=\"button\" class=\" tlb-icons ico-preview-form\" title=\"Preview\" data-ng-click=\"preview()\"><span>{{''documents.centralquery.workflow.preview'' | translate}}</span>\n                    </button>\n                </li>\n\n                <li class=\"collapsable\">\n                    <button type=\"button\" class=\" tlb-icons ico-download\" title=\"Download\" data-ng-click=\"download()\"><span>{{''documents.centralquery.workflow.download'' | translate}}</span>\n                    </button>\n                </li>\n            </ul>\n        </div>\n\n        <div class=\"platformgrid grid-container\"  style=\"height: 180px;font-size:12px;margin-top:0;overflow-x:scroll;overflow-y: auto;\">\n        <platform-Grid data=\"gridData\" ></platform-Grid>\n        </div>\n    </div>\n\n\n\n\n    <div>\n        <div >\n            <label style=\"display: block;margin: 10px 0\" >{{''documents.centralquery.workflow.approvalComment'' | translate}}</label>\n            <textarea style=\"width:100%;height: 60px;\" ng-model=\"Context.apprCom2\" ng-init=\"Context.apprCom2=''''\"></textarea>\n        </div>\n\n        <div >\n            <label style=\"display: block;margin: 10px 0\" >{{''documents.centralquery.workflow.declineComment'' | translate}}</label>\n            <textarea style=\"width:100%;height: 60px;\" ng-model=\"Context.declCom2\" ng-init=\"Context.declCom2=''''\"></textarea>\n            </div>\n\n\n    </div>\n\n\n\n</div>\n\n\n<footer class=\"modal-footer \">\n   <button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"isFinished();\" ng-disabled=\"isDisabled\">{{''documents.centralquery.workflow.ok'' | translate}}</button>\n</footer>\n"},{"id":69907,"key":"Script","value":"var $state = $injector.get(''$state'');\nvar gridAPI = $injector.get(''platformGridAPI'');\nvar cloudDesktopSidebarService = $injector.get(''cloudDesktopSidebarService'');\nvar basicsCommonFileDownloadService = $injector.get(''basicsCommonFileDownloadService'');\nvar basicsCommonServiceUploadExtension = $injector.get(''basicsCommonServiceUploadExtension'');\n$scope.gridId = ''doc'';\n$scope.gridData = {state: $scope.gridId};\nvar $translate = $injector.get(''$translate'');\nvar checkDocIds=[];\n$scope.selectRows = [];\n\n$scope.isDisabled=true;\n\nsetTimeout(function() {\n    var gridIns = gridAPI.grids.element(''id'', $scope.gridId);\n    gridIns.scope.vm.destroyGrid = function() {};\n}, 1000);\n\nfunction reCalculate(){\n\n    var sumAppr=0;\n    var sumDecl=0;\n    var docLen=$scope.Context.docInfo2.length;\n\n    for (var i = 0; i < $scope.Context.docInfo2.length; i++) {\n\n        if ($scope.Context.docInfo2[i].Approve){\n            sumAppr++;\n        }\n\n        if ($scope.Context.docInfo2[i].Decline){\n            sumDecl++;\n        }\n\n    }\n\n        if (sumAppr+sumDecl==docLen){\n            return false;\n        } else {return true;}\n}\n\n$scope.reCalculate=reCalculate;\n\nfunction checkApprove(item,value,fieldName){\n     item[fieldName] = value;\n     if(value && item.Decline){\n       item.Decline = false;\n     }\n     $scope.isDisabled=reCalculate();\n\n}\n\nfunction checkDecline(item,value,fieldName){\n     item[fieldName] = value;\n     if(value && item.Approve){\n       item.Approve = false;\n     }\n     $scope.isDisabled=reCalculate();\n}\n\nvar columns = [\n      {\n        id: 1,\n        formatter: ''boolean'',\n        field: \"Approve\",\n         name: $translate.instant(''documents.centralquery.workflow.approval''),\n        sortable: true,\n        editor: \"boolean\",\n        width: 80,\n        readonly: false,\n        validator:checkApprove\n    },\n    {\n        id:2,\n        formatter: ''boolean'',\n        field: \"Decline\",\n        name: $translate.instant(''documents.centralquery.workflow.decline''),\n        sortable: true,\n        editor: \"boolean\",\n        width: 80,\n        readonly: false,\n        validator:checkDecline\n    },\n    {\n        id: 3,\n        formatter: ''description'',\n        field: \"DESCRIPTION\",\n        name: $translate.instant(''documents.centralquery.workflow.file''),\n        sortable: true,\n        grouping: {},\n        editor: \"\",\n        width:250,\n        editorOptions: {},\n        showClearButton: true,\n        lookupOptions: {},\n        readonly: true\n    },\n    {\n        id: 4,\n        formatter: ''description'',\n        field: \"PROJECT\",\n        name: $translate.instant(''documents.centralquery.workflow.project''),\n        sortable: true,\n        width:250,\n        readonly: true\n    },\n    {\n        id: 5,\n        formatter: ''description'',\n        field: \"PROGRESSBY\",\n        name: $translate.instant(''documents.centralquery.workflow.approver''),\n        sortable: true,\n        width:95,\n        readonly: true\n    }\n\n];\n\nvar grid = {\n    columns: columns,\n    data: [],\n    id: $scope.gridId,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data=$scope.Context.docInfo2;\n\n\ngridAPI.grids.config(grid);\ngridAPI.items.data($scope.gridId,data);\n\n\ngridAPI.events.register($scope.gridId, ''onSelectedRowsChanged'', onSelectedRowsChanged);\n\nfunction onSelectedRowsChanged(scope, data) {\n    if (data.rows.length ===0 ) {\n        $scope.selectRows = [];\n    }\n    else {\n        $scope.selectRows = data.rows;\n    }\n}\n\n\nvar isFinished = function() {\n\n    for (var i = 0; i < $scope.Context.docInfo2.length; i++) {\n        \n        var rowNum=$scope.Context.docInfo2[i].ROW_NUM;\n\n        if ($scope.Context.docInfo2[i].Approve) {\n            $scope.Context.apprRowNumAry2.push(rowNum);\n        } else if ($scope.Context.docInfo2[i].Decline) {\n            $scope.Context.declRowNumAry2.push(rowNum);\n            \n        }\n\n    }\n\n\t$scope.Context.apprRowNumStr2=JSON.stringify($scope.Context.apprRowNumAry2);\n\t$scope.Context.declRowNumStr2=JSON.stringify($scope.Context.declRowNumAry2);\n\n\n    $scope.onOk();\n\n};\n\nvar download = function() {\n    if ($scope.selectRows.length) {\n        var archiveDocFks = [];\n        for(var i=0; i<$scope.selectRows.length; i++ ) {\n            var cur =$scope.Context.docInfo2[$scope.selectRows[i]];\n            archiveDocFks.push(cur[''BAS_FILEARCHIVEDOC_FK'']);\n        }\n        basicsCommonFileDownloadService.download(archiveDocFks);\n    }\n};\n\nvar preview = function() {\n\n    if ($scope.selectRows.length) {\n        service.previewDocument($scope,true);\n    }\n};\n\n$scope.isFinished = isFinished;\n$scope.download = download;\n$scope.preview = preview;\n\n\nvar service = {\n    getSelected: function() {\n        var cur = $scope.Context.docInfo2[$scope.selectRows[0]];\n        return cur;\n    }\n};\nbasicsCommonServiceUploadExtension.extendWidthPreview(service,{});\n\n$scope.$on(''$destroy'', function () {\n    gridAPI.grids.unregister($scope.gridId);\n});\n"},{"id":130303,"key":"Context","value":"{{Context}}"},{"id":84638,"key":"IsPopUp","value":""},{"id":88976,"key":"EvaluateProxy","value":""},{"id":40544,"key":"DisableRefresh","value":""},{"id":27197,"key":"AllowReassign","value":""},{"id":113247,"key":"Title","value":"Document Approval"},{"id":93681,"key":"Subtitle","value":""},{"id":82921,"key":"DialogConfig","value":"{\"minWidth\":\"800px\"}"}],"output":[{"id":92837,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":71339,"parameter":null,"workflowAction":{"id":57351,"code":"","description":"approvedCounter2++","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":67198,"key":"Script","value":"var instanceId = parseInt(Context.InstanceId, 10);\nContext.InstanceId = instanceId;\nvar approvedCounter= ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"select ISNULL(MAX(CONVERT(INT,USERDEFINED1)),0)+1 COUNTER FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND DESCRIPTION=N''Level 2 approving'' AND ISRUNNING=0 AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL \"\n    }).Output;\n\nContext.approvedCounter2=approvedCounter[0].COUNTER;"},{"id":109765,"key":"Context","value":""}],"output":[{"id":78469,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":99991,"parameter":null,"workflowAction":{"id":99992,"code":"","description":"update USERDEFINED1-5","documentList":[],"actionTypeId":5,"actionId":"6f3a49b7c1b94448886a868625829e4d","userId":"","lifeTime":1,"transitions":[{"id":75422,"parameter":null,"workflowAction":{"id":51856,"code":"","description":"Context.Lev2ApprType","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":80422,"key":"Formula","value":"Context.Lev2ApprType"},{"id":130262,"key":"Context","value":""}],"output":[{"id":79923,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":87831,"parameter":"s","workflowAction":{"id":85901,"code":"","description":"set unapproved task ISRUNNING=0","documentList":[],"actionTypeId":5,"actionId":"6f3a49b7c1b94448886a868625829e4d","userId":"","input":[{"id":117614,"key":"SQL","value":"UPDATE WFE_ACTIONINSTANCE SET ISRUNNING=0 WHERE WFE_INSTANCE_FK=@InstanceId AND DESCRIPTION=N''Level 2 approving''  AND ISRUNNING=1"},{"id":108009,"key":"Params","value":"InstanceId:{{Context.InstanceId}} ;"}],"output":[{"id":76550,"key":"Output","value":""}],"lifeTime":1,"transitions":[{"id":51211,"parameter":null,"workflowAction":{"id":83943,"code":"GT5","description":"level 2 summary (CODE:GT5)","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":118381,"key":"Script","value":"function clone(obj) {\n      var o;\n      if (typeof obj == \"object\") {\n        if (obj === null) {\n             o = null;\n         } else {\n             if (obj instanceof Array) {\n                 o = [];\n                 for (var i = 0, len = obj.length; i < len; i++) {\n                     o.push(clone(obj[i]));\n                 }\n             } else {\n                 o = {};\n                 for (var j in obj) {\n                     o[j] = clone(obj[j]);\n                 }\n             }\n         }\n     } else {\n         o = obj;\n     }\n     return o;\n }\n\nvar instanceId = parseInt(Context.InstanceId, 10);\nContext.InstanceId = instanceId;\nvar apprRowNum=ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"SELECT USERDEFINED2  AS APPR_ROW_NUM FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND ISRUNNING=0 AND DESCRIPTION=N''Level 2 approving'' AND USERDEFINED2 <>''''\"\n  }).Output;\n\nContext.apprRowNum2=apprRowNum;\n\nvar temp=[];\nvar len=apprRowNum.length;\n \nfor (var i=0;i<apprRowNum.length;i++){\n   temp=temp.concat(JSON.parse(apprRowNum[i].APPR_ROW_NUM));\n }\n\nContext.temp2=temp;\n\nvar groups={};\nfor (var i=0;i<temp.length;i++){\n  var item=temp[i];\n  if (groups.hasOwnProperty(item) === false ){\n        groups[item]= {times:0};\n  }\n  groups[item].times++;\n}\n\nContext.groups2=groups;\n\nvar apprRowNumResult=[];\nfor (var e in groups){\n  if (groups[e].times==len){\n   apprRowNumResult.push(e);\n  }\n  \n}\n\nContext.apprRowNumResult2=apprRowNumResult;\n\n\nvar declInfo=ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"SELECT WFE_ACTIONINSTANCE.ENDTIME AT TIME ZONE ''UTC'' AS DELINEDDATE, WFE_ACTIONINSTANCE.USERDEFINED3 AS DECL_ROW_NUM,BAS_CLERK_PROGRESSBY_FK,BAS_CLERK.DESCRIPTION FROM WFE_ACTIONINSTANCE INNER JOIN BAS_CLERK ON BAS_CLERK.ID=WFE_ACTIONINSTANCE.BAS_CLERK_PROGRESSBY_FK WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND ISRUNNING=0 AND WFE_ACTIONINSTANCE.DESCRIPTION=N''Level 2 approving'' AND WFE_ACTIONINSTANCE.USERDEFINED3 <>''''\"\n  }).Output;\n\nContext.declInfo2=declInfo;\n\nvar obj={};\n  for (var i=0;i<declInfo.length;i++){\n    var rowNumAry=JSON.parse(declInfo[i].DECL_ROW_NUM);\n    for (var j=0;j<rowNumAry.length;j++){ \n      \n        if (obj.hasOwnProperty(rowNumAry[j])===false){\n            obj[rowNumAry[j]]={ DESCRIPTION: '''', DELINEDDATE: '''' };\n        }\n\n         obj[rowNumAry[j]].DESCRIPTION += declInfo[i].DESCRIPTION+'' '';\n        if ( !obj[rowNumAry[j]].DELINEDDATE || new Date(obj[rowNumAry[j]].DELINEDDATE) > new Date(declInfo[i].DELINEDDATE)) {\n            obj[rowNumAry[j]].DELINEDDATE = declInfo[i].DELINEDDATE;\n        }\n\n\n      }      \n    }\n\nContext.tempObj2=obj;\n\nContext.apprDocInfo2=[];\n\nvar apprDocInfo=Context.docInfo2.filter(function(e){ return Context.apprRowNumResult2.indexOf(e.ROW_NUM.toString())>-1;});\n\nContext.apprDocInfo2=clone(apprDocInfo);\n\nContext.declDocInfo2=[];\n\nfor (var rownum in obj){\n\n  for (var i=0;i<Context.docInfo2.length;i++){\n\n    if (Context.docInfo2[i].ROW_NUM.toString()===rownum){\n\n          Context.docInfo2[i].PROGRESSBY=obj[rownum].DESCRIPTION;\n          Context.docInfo2[i].DELINEDDATE=obj[rownum].DELINEDDATE;\n\n      \n        Context.declDocInfo2.push(clone(Context.docInfo2[i]));\n\n    }\n  }  \n}\n\n\nContext.declResult1_2=clone(Context.declDocInfo1.concat(Context.declDocInfo2));\n\nvar apprCom= ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n  {\n      SQL: \"SELECT WFE_ACTIONINSTANCE.ENDTIME AT TIME ZONE ''UTC'' AS APPROVALDATE, BAS_CLERK_PROGRESSBY_FK,BAS_CLERK.DESCRIPTION,WFE_ACTIONINSTANCE.USERDEFINED4 AS COMMENT FROM WFE_ACTIONINSTANCE INNER JOIN BAS_CLERK ON BAS_CLERK.ID=WFE_ACTIONINSTANCE.BAS_CLERK_PROGRESSBY_FK WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND WFE_ACTIONINSTANCE.DESCRIPTION=N''Level 2 approving'' AND ISRUNNING=0 AND WFE_ACTIONINSTANCE.USERDEFINED4<>''''\"\n  }).Output;\n\n  Context.apprComResult2=apprCom;\n\n  Context.apprComResult1_2=clone(Context.apprComResult1.concat(Context.apprComResult2));\n\n   var declCom= ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n  {\n      SQL: \"SELECT WFE_ACTIONINSTANCE.ENDTIME AT TIME ZONE ''UTC'' AS DELINEDDATE, BAS_CLERK_PROGRESSBY_FK,BAS_CLERK.DESCRIPTION,WFE_ACTIONINSTANCE.USERDEFINED5 AS COMMENT FROM WFE_ACTIONINSTANCE INNER JOIN BAS_CLERK ON BAS_CLERK.ID=WFE_ACTIONINSTANCE.BAS_CLERK_PROGRESSBY_FK WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND WFE_ACTIONINSTANCE.DESCRIPTION=N''Level 2 approving'' AND ISRUNNING=0 AND WFE_ACTIONINSTANCE.USERDEFINED5<>''''\"\n  }).Output;\n\n  Context.declComResult2=declCom;\n\n  Context.declComResult1_2=Context.declComResult1.concat(Context.declComResult2);\n\n\n\n\n\n"},{"id":87845,"key":"Context","value":""}],"output":[{"id":99580,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":13701,"parameter":null,"workflowAction":{"id":86212,"code":"","description":"round++","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":93629,"key":"Script","value":"Context.round++;"},{"id":110432,"key":"Context","value":""}],"output":[{"id":112102,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":30266,"parameter":null,"workflowAction":{"id":89707,"code":"","description":"round<selectedLel","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":83257,"key":"Formula","value":"Context.round<Context.selectedLel"},{"id":120393,"key":"Context","value":""}],"output":[{"id":123529,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":46310,"parameter":"true","workflowAction":{"id":11345,"code":"","description":"level 3 init","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":90274,"key":"Script","value":"function clone(obj) {\n      var o;\n      if (typeof obj == \"object\") {\n        if (obj === null) {\n             o = null;\n         } else {\n             if (obj instanceof Array) {\n                 o = [];\n                 for (var i = 0, len = obj.length; i < len; i++) {\n                     o.push(clone(obj[i]));\n                 }\n             } else {\n                 o = {};\n                 for (var j in obj) {\n                     o[j] = clone(obj[j]);\n                 }\n             }\n         }\n     } else {\n         o = obj;\n     }\n     return o;\n }\n\n\nContext.counter3=0;\n\nContext.approvedCounter3=0;\n\nContext.apprRowNumAry3=[];\nContext.apprRowNumStr3='''';\n\nContext.declRowNumAry3=[];\nContext.declRowNumStr3='''';\n\nif (Context.Lev3ApprType==''s''){\n  Context.passApprNum3=1;\n} else {Context.passApprNum3=Context.length3;}\n\n\nContext.docInfo3=[];\n\nfor (var i=0;i<Context.docInfo2.length;i++){\n  var a=Context.docInfo2.slice(i, i+1); \n  var b=clone(a);\n  delete b[0].Approve;\n  if (Context.apprRowNumResult2.indexOf(Context.docInfo2[i].ROW_NUM.toString())>-1){\n    Context.docInfo3.push(b[0]);\n    \n  }\n  \n}\n\n\n"},{"id":91423,"key":"Context","value":""}],"output":[{"id":85871,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":31301,"parameter":null,"workflowAction":{"id":72554,"code":"GT3","description":"set clerkid (GT3)","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":69537,"key":"Script","value":"\n\n  Context.clerkId3=Context.lev3ClerkId[Context.counter3];\n"},{"id":111416,"key":"Context","value":""}],"output":[{"id":114813,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":87065,"parameter":null,"workflowAction":{"id":89798,"code":"","description":"send ToDo (level 3)","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":92777,"key":"Formula","value":"1===1"},{"id":124026,"key":"Context","value":""}],"output":[{"id":99135,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":36324,"parameter":"true","workflowAction":{"id":95762,"code":"","description":"Level 3 approving","documentList":[],"actionTypeId":6,"actionId":"000019b479164ad1adeb7631d3fd6161","userId":"{{Context.clerkId3}}","input":[{"id":121323,"key":"HTML","value":"<div style=\"font: 13px/normal source_sans,Microsoft YaHei,STHeiti;margin:10px;\">\n  <lable>{{''documents.centralquery.workflow.issuer'' | translate}}: {{Context.issuer[0].NAME}}</lable> \n  <label style=\"float:right;margin-right:300px;\">{{''documents.centralquery.workflow.issuedDate'' | translate}}: {{Context.issuedDate}} </label>\n  <br><br>\n<div style=\"display:inline-block\">\n  <label style=\"float:left;margin-right:10px;\">{{''documents.centralquery.workflow.issuerComment'' | translate}}:</label>\n\n<textarea style=\"width:660px;height:70px;border:none;resize:none;\" readonly=\"readonly\">{{Context.comment}}</textarea>\n\n</div>\n\n<br>\n\n\n    <div style=\"width:100%;\">\n\n        <p>{{''documents.centralquery.workflow.reviewAndApprovalDocuments'' | translate}}:</p>\n            <div class=\"toolbar\" style=\"flex-flow: row-reverse;\">\n                <ul class=\"tools list-items-1998fd12b31ae25aef0ed08b46c1a435\">\n                    <li class=\"collapsable\">\n                        <button type=\"button\" class=\" tlb-icons ico-preview-form\" title=\"Preview\" data-ng-click=\"preview()\"><span>{{''documents.centralquery.workflow.preview'' | translate}}</span>\n                        </button>\n                    </li>\n\n                    <li class=\"collapsable\">\n                        <button type=\"button\" class=\" tlb-icons ico-download\" title=\"Download\" data-ng-click=\"download()\"><span>{{''documents.centralquery.workflow.download'' | translate}}</span>\n                        </button>\n                    </li>\n                </ul>\n            </div>\n\n        <div class=\"platformgrid grid-container\"  style=\"height: 180px;font-size:12px;margin-top:0;overflow-x:scroll;overflow-y: auto;\">\n        <platform-Grid data=\"gridData\" ></platform-Grid>\n        </div>\n    </div>\n\n    <div style=\"margin: 17px;\">\n\n        <div >\n            <label style=\"display: block;margin: 10px 0\" >{{''documents.centralquery.workflow.approvalComment'' | translate}}</label>\n            <textarea style=\"width:100%;height: 60px;\" ng-model=\"Context.apprCom3\" ng-init=\"Context.apprCom3=''''\"></textarea>\n        </div>\n\n        <div >\n            <label style=\"display: block;margin: 10px 0\" >{{''documents.centralquery.workflow.declineComment'' | translate}}</label>\n            <textarea style=\"width:100%;height: 60px;\" ng-model=\"Context.declCom3\" ng-init=\"Context.declCom3=''''\"></textarea>\n            </div>\n    </div>\n\n\n\n</div>\n\n\n<footer class=\"modal-footer \">\n   <button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"isFinished();\" ng-disabled=\"isDisabled\">{{''documents.centralquery.workflow.ok'' | translate}}</button>\n</footer>\n"},{"id":69907,"key":"Script","value":"var $state = $injector.get(''$state'');\nvar gridAPI = $injector.get(''platformGridAPI'');\nvar cloudDesktopSidebarService = $injector.get(''cloudDesktopSidebarService'');\nvar basicsCommonFileDownloadService = $injector.get(''basicsCommonFileDownloadService'');\nvar basicsCommonServiceUploadExtension = $injector.get(''basicsCommonServiceUploadExtension'');\n$scope.gridId = ''doc'';\n$scope.gridData = {state: $scope.gridId};\nvar $translate = $injector.get(''$translate'');\nvar checkDocIds=[];\n$scope.selectRows = [];\n\n$scope.isDisabled=true;\n\nsetTimeout(function() {\n    var gridIns = gridAPI.grids.element(''id'', $scope.gridId);\n    gridIns.scope.vm.destroyGrid = function() {};\n}, 1000);\n\nfunction reCalculate(){\n\n    var sumAppr=0;\n    var sumDecl=0;\n    var docLen=$scope.Context.docInfo3.length;\n\n    for (var i = 0; i < $scope.Context.docInfo3.length; i++) {\n\n        if ($scope.Context.docInfo3[i].Approve){\n            sumAppr++;\n        }\n\n        if ($scope.Context.docInfo3[i].Decline){\n            sumDecl++;\n        }\n\n    }\n\n        if (sumAppr+sumDecl==docLen){\n            return false;\n        } else {return true;}\n}\n\n$scope.reCalculate=reCalculate;\n\nfunction checkApprove(item,value,fieldName){\n     item[fieldName] = value;\n     if(value && item.Decline){\n       item.Decline = false;\n     }\n     $scope.isDisabled=reCalculate();\n\n}\n\nfunction checkDecline(item,value,fieldName){\n     item[fieldName] = value;\n     if(value && item.Approve){\n       item.Approve = false;\n     }\n     $scope.isDisabled=reCalculate();\n}\n\nvar columns = [\n   {\n        id: 1,\n        formatter: ''boolean'',\n        field: \"Approve\",\n         name: $translate.instant(''documents.centralquery.workflow.approval''),\n        sortable: true,\n        editor: \"boolean\",\n        width: 80,\n        readonly: false,\n        validator:checkApprove\n    },\n    {\n        id:2,\n        formatter: ''boolean'',\n        field: \"Decline\",\n        name: $translate.instant(''documents.centralquery.workflow.decline''),\n        sortable: true,\n        editor: \"boolean\",\n        width: 80,\n        readonly: false,\n        validator:checkDecline\n    },\n    {\n        id: 3,\n        formatter: ''description'',\n        field: \"DESCRIPTION\",\n        name: $translate.instant(''documents.centralquery.workflow.file''),\n        sortable: true,\n        grouping: {},\n        editor: \"\",\n        width:250,\n        editorOptions: {},\n        showClearButton: true,\n        lookupOptions: {},\n        readonly: true\n    },\n    {\n        id: 4,\n        formatter: ''description'',\n        field: \"PROJECT\",\n        name: $translate.instant(''documents.centralquery.workflow.project''),\n        sortable: true,\n        width:250,\n        readonly: true\n    },\n    {\n        id: 5,\n        formatter: ''description'',\n        field: \"PROGRESSBY\",\n        name: $translate.instant(''documents.centralquery.workflow.approver''),\n        sortable: true,\n        width:95,\n        readonly: true\n    }\n];\n\n\nvar grid = {\n    columns: columns,\n    data: [],\n    id: $scope.gridId,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data=$scope.Context.docInfo3;\n\n\ngridAPI.grids.config(grid);\ngridAPI.items.data($scope.gridId,data);\n\ngridAPI.events.register($scope.gridId, ''onSelectedRowsChanged'', onSelectedRowsChanged);\n\nfunction onSelectedRowsChanged(scope, data) {\n    if (data.rows.length ===0 ) {\n        $scope.selectRows = [];\n    }\n    else {\n        $scope.selectRows = data.rows;\n    }\n}\n\n\nvar isFinished = function() {\n\n    for (var i = 0; i < $scope.Context.docInfo3.length; i++) {\n        \n    var rowNum=$scope.Context.docInfo3[i].ROW_NUM;\n\n    if ($scope.Context.docInfo3[i].Approve) {\n        $scope.Context.apprRowNumAry3.push(rowNum);\n\n    } else if ($scope.Context.docInfo3[i].Decline) {\n        $scope.Context.declRowNumAry3.push(rowNum);\n\n    }\n\n}\n\n$scope.Context.apprRowNumStr3=JSON.stringify($scope.Context.apprRowNumAry3);\n$scope.Context.declRowNumStr3=JSON.stringify($scope.Context.declRowNumAry3);\n\n\n$scope.onOk();\n\n};\n\n\nvar download = function() {\n    if ($scope.selectRows.length) {\n        var archiveDocFks = [];\n        for(var i=0; i<$scope.selectRows.length; i++ ) {\n            var cur = $scope.Context.docInfo3[$scope.selectRows[i]];\n            archiveDocFks.push(cur[''BAS_FILEARCHIVEDOC_FK'']);\n        }\n        basicsCommonFileDownloadService.download(archiveDocFks);\n    }\n};\n\nvar preview = function() {\n\n    if ($scope.selectRows.length) {\n        service.previewDocument($scope,true);\n    }\n};\n\n$scope.isFinished = isFinished;\n$scope.download = download;\n$scope.preview = preview;\n\n\nvar service = {\n    getSelected: function() {\n        var cur = $scope.Context.docInfo3[$scope.selectRows[0]];\n        return cur;\n    }\n};\nbasicsCommonServiceUploadExtension.extendWidthPreview(service,{});\n\n$scope.$on(''$destroy'', function () {\n    gridAPI.grids.unregister($scope.gridId);\n});\n\n\n\n"},{"id":130303,"key":"Context","value":"{{Context}}"},{"id":84638,"key":"IsPopUp","value":""},{"id":79474,"key":"EvaluateProxy","value":""},{"id":91741,"key":"DisableRefresh","value":""},{"id":92078,"key":"AllowReassign","value":""},{"id":113247,"key":"Title","value":"Document Approval"},{"id":93681,"key":"Subtitle","value":""},{"id":82921,"key":"DialogConfig","value":"{\"minWidth\":\"800px\"}"}],"output":[{"id":92837,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":20805,"parameter":null,"workflowAction":{"id":79190,"code":"","description":"approvedCounter3++","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":95354,"key":"Script","value":"var instanceId = parseInt(Context.InstanceId, 10);\nContext.InstanceId = instanceId;\nvar approvedCounter= ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"select ISNULL(MAX(CONVERT(INT,USERDEFINED1)),0)+1 COUNTER FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND DESCRIPTION=N''Level 3 approving'' AND ISRUNNING=0 AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL  \"\n    }).Output;\n\nContext.approvedCounter3=approvedCounter[0].COUNTER;"},{"id":130028,"key":"Context","value":""}],"output":[{"id":119368,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":99993,"parameter":null,"workflowAction":{"id":99994,"code":"","description":"update USERDEFINED1-5","documentList":[],"actionTypeId":5,"actionId":"6f3a49b7c1b94448886a868625829e4d","userId":"","lifeTime":1,"transitions":[{"id":17966,"parameter":null,"workflowAction":{"id":43507,"code":"","description":"Context.Lev3ApprType","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":80422,"key":"Formula","value":"Context.Lev3ApprType"},{"id":130262,"key":"Context","value":""}],"output":[{"id":79923,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":35245,"parameter":"s","workflowAction":{"id":62353,"code":"","description":"set unapproved task ISRUNNING=0","documentList":[],"actionTypeId":5,"actionId":"6f3a49b7c1b94448886a868625829e4d","userId":"","input":[{"id":117614,"key":"SQL","value":"UPDATE WFE_ACTIONINSTANCE SET ISRUNNING=0 WHERE WFE_INSTANCE_FK=@InstanceId AND DESCRIPTION=N''Level 3 approving''  AND ISRUNNING=1"},{"id":108009,"key":"Params","value":"InstanceId:{{Context.InstanceId}} ;"}],"output":[{"id":76550,"key":"Output","value":""}],"lifeTime":1,"transitions":[{"id":23085,"parameter":null,"workflowAction":{"id":20027,"code":"","description":"Go To ''level 3 summary  (CODE:GT6)''","documentList":[],"actionTypeId":10,"actionId":"E0000000000000000000000000000000","userId":"","input":[{"id":97771,"key":"Code","value":"GT6"}],"output":[],"lifeTime":1,"transitions":[{"id":88786,"parameter":null,"workflowAction":{"id":50694,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":10004,"parameter":"g","workflowAction":{"id":38083,"code":"","description":"all approval finished?","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":93758,"key":"Formula","value":"Context.approvedCounter3==Context.passApprNum3"},{"id":124817,"key":"Context","value":""}],"output":[{"id":103194,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":17583,"parameter":"true","workflowAction":{"id":78187,"code":"GT6","description":"level 3 summary  (CODE:GT6)","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":99102,"key":"Script","value":"function clone(obj) {\n      var o;\n      if (typeof obj == \"object\") {\n        if (obj === null) {\n             o = null;\n         } else {\n             if (obj instanceof Array) {\n                 o = [];\n                 for (var i = 0, len = obj.length; i < len; i++) {\n                     o.push(clone(obj[i]));\n                 }\n             } else {\n                 o = {};\n                 for (var j in obj) {\n                     o[j] = clone(obj[j]);\n                 }\n             }\n         }\n     } else {\n         o = obj;\n     }\n     return o;\n }\n\nvar instanceId = parseInt(Context.InstanceId, 10);\nContext.InstanceId = instanceId;\nvar apprRowNum=ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"SELECT USERDEFINED2  AS APPR_ROW_NUM FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND ISRUNNING=0 AND DESCRIPTION=N''Level 3 approving'' AND USERDEFINED2<>''''\"\n  }).Output;\n\nContext.apprRowNum3=apprRowNum;\n\nvar temp=[];\nvar len=apprRowNum.length;\n \nfor (var i=0;i<apprRowNum.length;i++){\n   temp=temp.concat(JSON.parse(apprRowNum[i].APPR_ROW_NUM));\n }\n\nContext.temp3=temp;\n\nvar groups={};\nfor (var i=0;i<temp.length;i++){\n  var item=temp[i];\n  if (groups.hasOwnProperty(item) === false ){\n        groups[item]= {times:0};\n  }\n  groups[item].times++;\n}\n\nContext.groups3=groups;\n\nvar apprRowNumResult=[];\nfor (var e in groups){\n  if (groups[e].times==len){\n   apprRowNumResult.push(e);\n  }\n  \n}\n\nContext.apprRowNumResult3=apprRowNumResult;\n\n\nvar declInfo=ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n    {\n        SQL: \"SELECT WFE_ACTIONINSTANCE.ENDTIME AT TIME ZONE ''UTC'' AS DELINEDDATE, WFE_ACTIONINSTANCE.USERDEFINED3 AS DECL_ROW_NUM,BAS_CLERK_PROGRESSBY_FK,BAS_CLERK.DESCRIPTION  FROM WFE_ACTIONINSTANCE INNER JOIN BAS_CLERK ON BAS_CLERK.ID=WFE_ACTIONINSTANCE.BAS_CLERK_PROGRESSBY_FK WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND ISRUNNING=0 AND WFE_ACTIONINSTANCE.DESCRIPTION=N''Level 3 approving'' AND WFE_ACTIONINSTANCE.USERDEFINED3 <>''''\"\n  }).Output;\n\nContext.declInfo3=declInfo;\n\nvar obj={};\n  for (var i=0;i<declInfo.length;i++){\n    var rowNumAry=JSON.parse(declInfo[i].DECL_ROW_NUM);\n    for (var j=0;j<rowNumAry.length;j++){ \n      \n        if (obj.hasOwnProperty(rowNumAry[j])===false){\n               obj[rowNumAry[j]]={ DESCRIPTION: '''', DELINEDDATE: '''' };\n        }\n\n           obj[rowNumAry[j]].DESCRIPTION += declInfo[i].DESCRIPTION+'' '';\n           if ( !obj[rowNumAry[j]].DELINEDDATE || new Date(obj[rowNumAry[j]].DELINEDDATE) > new Date(declInfo[i].DELINEDDATE)) {\n               obj[rowNumAry[j]].DELINEDDATE = declInfo[i].DELINEDDATE;\n           }\n\n      }      \n    }\n\nContext.tempObj3=obj;\nContext.declDocInfo3=[];\n\nContext.apprDocInfo3=[];\n\nvar apprDocInfo=Context.docInfo3.filter(function(e){ return Context.apprRowNumResult3.indexOf(e.ROW_NUM.toString())>-1;});\n\nContext.apprDocInfo3=clone(apprDocInfo);\n\nfor (var rownum in obj){\n\n  for (var i=0;i<Context.docInfo3.length;i++){\n\n    if (Context.docInfo3[i].ROW_NUM.toString()===rownum){\n\n           Context.docInfo3[i].PROGRESSBY=obj[rownum].DESCRIPTION;\n           Context.docInfo3[i].DELINEDDATE=obj[rownum].DELINEDDATE;\n       \n        Context.declDocInfo3.push(clone(Context.docInfo3[i]));\n\n    }\n  }  \n}\nContext.declResult1_2_3=clone(Context.declResult1_2.concat(Context.declDocInfo3));\n\nvar apprCom= ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n{\n    SQL: \"SELECT WFE_ACTIONINSTANCE.ENDTIME AT TIME ZONE ''UTC'' AS APPROVALDATE, BAS_CLERK_PROGRESSBY_FK,BAS_CLERK.DESCRIPTION,WFE_ACTIONINSTANCE.USERDEFINED4 AS COMMENT FROM WFE_ACTIONINSTANCE INNER JOIN BAS_CLERK ON BAS_CLERK.ID=WFE_ACTIONINSTANCE.BAS_CLERK_PROGRESSBY_FK WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND WFE_ACTIONINSTANCE.DESCRIPTION=N''Level 3 approving'' AND ISRUNNING=0 AND WFE_ACTIONINSTANCE.USERDEFINED4<>''''\"\n}).Output;\n\nContext.apprComResult3=apprCom;\n\nContext.apprComResult1_2_3=clone(Context.apprComResult1_2.concat(Context.apprComResult3));\n\n var declCom= ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'',\n{\n    SQL: \"SELECT WFE_ACTIONINSTANCE.ENDTIME AT TIME ZONE ''UTC'' AS DELINEDDATE, BAS_CLERK_PROGRESSBY_FK,BAS_CLERK.DESCRIPTION,WFE_ACTIONINSTANCE.USERDEFINED5 AS COMMENT FROM WFE_ACTIONINSTANCE INNER JOIN BAS_CLERK ON BAS_CLERK.ID=WFE_ACTIONINSTANCE.BAS_CLERK_PROGRESSBY_FK WHERE WFE_INSTANCE_FK=\"+instanceId+\" AND WFE_ACTIONINSTANCE.DESCRIPTION=N''Level 3 approving'' AND ISRUNNING=0 AND WFE_ACTIONINSTANCE.USERDEFINED5<>''''\"\n}).Output;\n\nContext.declComResult3=declCom;\n\nContext.declComResult1_2_3=clone(Context.declComResult1_2.concat(Context.declComResult3));\n\n\n\n\n\n\n"},{"id":71148,"key":"Context","value":""}],"output":[{"id":74581,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":36186,"parameter":null,"workflowAction":{"id":73979,"code":"","description":"change status","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":71300,"key":"Script","value":"\n//approved\nfor (var i=0;i<Context.apprDocInfo3.length;i++){\n \n    ActionUtil.ExecuteAction(''5F6E595C0BF6412694D9C40AD66621DF'',\n    {\n        StatusName:''prjdocument'',\n        ObjectId:Context.apprDocInfo3[i].ID,\n        NewStatusId:Context.apprDocInfo3[i].PRJ_DOCUMENTSTATUS_TARGET_FK,//approved status id\n        ProjectId:0,\n        Remark:''workflow change status'',\n        IgnoreAccessRight:'''',\n        ObjectPk1:'''',\n        ObjectPk2:'''',\n        CheckValidate:false \n });\n  \n\tContext.apprDocInfo3[i].ApprovalDate = new Date().toISOString();\n  \n }\n\n\n\n\n//declined\n/*\nfor (var i=0;i<Context.declResult1_2_3.length;i++){\n \n    ActionUtil.ExecuteAction(''5F6E595C0BF6412694D9C40AD66621DF'',\n    {\n        StatusName:''prjdocument'',\n        ObjectId:Context.declResult1_2_3[i].ID,\n        NewStatusId:Context.declineStatusId,//declined status id\n        ProjectId:0,\n        Remark:''workflow change status'',\n        IgnoreAccessRight:'''',\n        ObjectPk1:'''',\n        ObjectPk2:'''',\n        CheckValidate:false \n });\n }\n*/"},{"id":122179,"key":"Context","value":""}],"output":[{"id":75108,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":68779,"parameter":null,"workflowAction":{"id":16580,"code":"","description":"send noticification","documentList":[],"actionTypeId":6,"actionId":"000019b479164ad1adeb7631d3fd6161","userId":"{{Context.triggerClerkId}}","input":[{"id":128584,"key":"HTML","value":"<div style=\"font: 13px/normal source_sans,Microsoft YaHei,STHeiti;overflow-y: auto; overflow-x: hidden;height: 460px;margin:10px\">\n\n    <label ng-if=\"Context.apprDocInfo3.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.approvedDocumentList'' | translate}}:</label>\n\n    <div ng-if=\"Context.apprDocInfo3.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData1\" ></platform-Grid>\n    </div>\n\n\n    <label ng-if=\"Context.apprComResult1_2_3.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.approvedCommentList'' | translate}} :</label>\n    <div ng-if=\"Context.apprComResult1_2_3.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData3\" ></platform-Grid>\n    </div>\n\n    <label ng-if=\"Context.declResult1_2_3.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.declinedDocumentList'' | translate}} :</label>\n\n    <div ng-if=\"Context.declResult1_2_3.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData2\" ></platform-Grid>\n    </div>\n\n    <label ng-if=\"Context.declComResult1_2_3.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.declinedCommentList'' | translate}}:</label>\n    <div ng-if=\"Context.declComResult1_2_3.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData4\" ></platform-Grid>\n    </div>\n\n</div>\n    \n    <footer class=\"modal-footer \">\n    <button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"onOk()\" >\n       {{''documents.centralquery.workflow.ok'' | translate}}\n    </button>\n\n</footer>"},{"id":96605,"key":"Script","value":"var gridAPI = $injector.get(''platformGridAPI'');\nvar $translate = $injector.get(''$translate'');\n$scope.gridId1 = ''appr'';\n$scope.gridData1 = {state: $scope.gridId1};\n\n\nvar columns1 = [  \n    {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\n   name: $translate.instant(''documents.centralquery.workflow.file''),\n    sortable: true,\n    width:150,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"PROJECT\",\n   name: $translate.instant(''documents.centralquery.workflow.project''),\n    sortable: true,\n    width:180,\n    readonly: true\n    },\n    {\n    id: 3,\n    formatter: ''date'',\n    field: \"FileUploadDate\",\n   name: $translate.instant(''documents.centralquery.workflow.uploadDate''),\n    sortable: true,\n    width:110,\n    readonly: true\n    },\n    {\n    id: 4,\n    formatter: ''datetime'',\n    field: \"ApprovalDate\",\n   name: $translate.instant(''documents.centralquery.workflow.approvalDate''),\n    sortable: true,\n    width:130,\n    readonly: true\n    }\n];\n\n\nvar grid1 = {\n    columns: columns1,\n    data: [],\n    id: $scope.gridId1,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data1=$scope.Context.apprDocInfo3;\n\n\ngridAPI.grids.config(grid1);\ngridAPI.items.data($scope.gridId1,data1);\n\n$scope.gridId2 = ''decl'';\n$scope.gridData2 = {state: $scope.gridId2};\n\n\nvar columns2 = [  \n    {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\n    name: $translate.instant(''documents.centralquery.workflow.file''),\n    sortable: true,\n    width:150,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"PROJECT\",\n   name: $translate.instant(''documents.centralquery.workflow.project''),\n    sortable: true,\n    width:180,\n    readonly: true\n    },\n      {\n    id: 3,\n    formatter: ''description'',\n    field: \"PROGRESSBY\",\n   name: $translate.instant(''documents.centralquery.workflow.declinedBy''),\n    sortable: true,\n    width:90,\n    readonly: true\n    },\n    {\n    id: 4,\n    formatter: ''date'',\n    field: \"FileUploadDate\",\n   name: $translate.instant(''documents.centralquery.workflow.uploadDate''),\n    sortable: true,\n    width:110,\n    readonly: true\n    },\n    {\n    id: 5,\n    formatter: ''datetime'',\n    field: \"DELINEDDATE\",\n   name: $translate.instant(''documents.centralquery.workflow.declinedDate''),\n    sortable: true,\n    width: 130,\n    readonly: true\n    }\n];\n\n\nvar grid2 = {\n    columns: columns2,\n    data: [],\n    id: $scope.gridId2,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data2=$scope.Context.declResult1_2_3;\n\n\ngridAPI.grids.config(grid2);\ngridAPI.items.data($scope.gridId2,data2);\n\n\n$scope.gridId3 = ''apprCom'';\n$scope.gridData3 = {state: $scope.gridId3};\n\n\nvar columns3 = [  \n    {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\nname: $translate.instant(''documents.centralquery.workflow.clerk''),\n    sortable: true,\n    width:100,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"COMMENT\",\n    name: $translate.instant(''documents.centralquery.workflow.comment''),\n    sortable: true,\n    width:350,\n    readonly: true\n    },\n    {\n    id: 3,\n    formatter: ''datetime'',\n    field: \"APPROVALDATE\",\n   name: $translate.instant(''documents.centralquery.workflow.commentDate''),\n    sortable: true,\n    width:130,\n    readonly: true\n    }\n];\n\n\nvar grid3 = {\n    columns: columns3,\n    data: [],\n    id: $scope.gridId3,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''BAS_CLERK_PROGRESSBY_FK'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data3=$scope.Context.apprComResult1_2_3;\n\n\ngridAPI.grids.config(grid3);\ngridAPI.items.data($scope.gridId3,data3);\n\n\n\n\n$scope.gridId4 = ''declCom'';\n$scope.gridData4 = {state: $scope.gridId4};\n\n\nvar columns4 = [  \n    {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\nname: $translate.instant(''documents.centralquery.workflow.clerk''),\n    sortable: true,\n    width:100,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"COMMENT\",\n    name: $translate.instant(''documents.centralquery.workflow.comment''),\n    sortable: true,\n    width:350,\n    readonly: true\n    },\n    {\n    id: 3,\n    formatter: ''datetime'',\n    field: \"DELINEDDATE\",\n   name: $translate.instant(''documents.centralquery.workflow.commentDate''),\n    sortable: true,\n    width:130,\n    readonly: true\n    }\n];\n\n\nvar grid4 = {\n    columns: columns4,\n    data: [],\n    id: $scope.gridId4,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''BAS_CLERK_PROGRESSBY_FK'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data4=$scope.Context.declComResult1_2_3;\n\n\ngridAPI.grids.config(grid4);\ngridAPI.items.data($scope.gridId4,data4);\n\n\n"},{"id":77581,"key":"Context","value":"{{Context}}"},{"id":128179,"key":"IsPopUp","value":""},{"id":20911,"key":"EvaluateProxy","value":""},{"id":98126,"key":"DisableRefresh","value":""},{"id":11985,"key":"AllowReassign","value":""},{"id":77569,"key":"Title","value":"Approval Result"},{"id":95269,"key":"Subtitle","value":""},{"id":106578,"key":"DialogConfig","value":"{\"minWidth\":\"650px\"}"}],"output":[{"id":115281,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":76569,"parameter":null,"workflowAction":{"id":46376,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":72488,"parameter":"false","workflowAction":{"id":31983,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":"true","parameter":"true"},{"id":"false","parameter":"false"}],"level":7210}}],"options":[{"id":"s","parameter":"s"},{"id":"g","parameter":"g"}],"level":7030}}],"input":[{"id":106114,"key":"SQL","value":"UPDATE WFE_ACTIONINSTANCE SET USERDEFINED1=CONVERT(nvarchar(255),@approvedCounter3) WHERE ID= (SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 3 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL  ORDER BY ENDTIME DESC)\n\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED2=@apprRowNumStr3 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 3 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL  ORDER BY ENDTIME DESC)\n\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED3=@declRowNumStr3 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 3 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL  ORDER BY ENDTIME DESC)\n\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED4=@apprCom3 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 3 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL  ORDER BY ENDTIME DESC)\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED5=@declCom3 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 3 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL  ORDER BY ENDTIME DESC)"},{"id":90029,"key":"Params","value":"InstanceId:{{Context.InstanceId}} ;approvedCounter3 :{{Context.approvedCounter3}} ;apprRowNumStr3 :{{Context.apprRowNumStr3}} ;declRowNumStr3 :{{Context.declRowNumStr3}} ;apprCom3 :{{Context.apprCom3}} ;declCom3 :{{Context.declCom3}} ;"}],"output":[{"id":98762,"key":"Output","value":""}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":69720,"parameter":"true","workflowAction":{"id":40508,"code":"","description":"counter3++","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":109859,"key":"Script","value":"Context.counter3++;"},{"id":89252,"key":"Context","value":""}],"output":[{"id":119136,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":56938,"parameter":null,"workflowAction":{"id":83241,"code":"","description":"send ToDo finished?","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":88307,"key":"Formula","value":"Context.counter3>=Context.length3"},{"id":121673,"key":"Context","value":""}],"output":[{"id":96008,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":17742,"parameter":"true","workflowAction":{"id":75763,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}},{"id":61380,"parameter":"false","workflowAction":{"id":99982,"code":"","description":"Go To ''set clerkid (GT3)''","documentList":[],"actionTypeId":10,"actionId":"E0000000000000000000000000000000","userId":"","input":[{"id":76607,"key":"Code","value":"GT3"}],"output":[],"lifeTime":1,"transitions":[{"id":74130,"parameter":null,"workflowAction":{"id":14498,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":"true","parameter":"true"},{"id":"false","parameter":"false"}],"level":6670}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":"true","parameter":"true"}],"level":6310}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":12239,"parameter":"false","workflowAction":{"id":38526,"code":"","description":"change status","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":85209,"key":"Script","value":"\n//approved\nfor (var i=0;i<Context.apprDocInfo2.length;i++){\n \n    ActionUtil.ExecuteAction(''5F6E595C0BF6412694D9C40AD66621DF'',\n    {\n        StatusName:''prjdocument'',\n        ObjectId:Context.apprDocInfo2[i].ID,\n        NewStatusId:Context.apprDocInfo2[i].PRJ_DOCUMENTSTATUS_TARGET_FK,//approved status id\n        ProjectId:0,\n        Remark:''workflow change status'',\n        IgnoreAccessRight:'''',\n        ObjectPk1:'''',\n        ObjectPk2:'''',\n        CheckValidate:false \n });\n  \n\tContext.apprDocInfo2[i].ApprovalDate = new Date().toISOString();\n  \n }\n\n\n\n\n//declined\n/*\nfor (var i=0;i<Context.declResult1_2.length;i++){\n \n    ActionUtil.ExecuteAction(''5F6E595C0BF6412694D9C40AD66621DF'',\n    {\n        StatusName:''prjdocument'',\n        ObjectId:Context.declResult1_2[i].ID,\n        NewStatusId:Context.declineStatusId,//declined status id\n        ProjectId:0,\n        Remark:''workflow change status'',\n        IgnoreAccessRight:'''',\n        ObjectPk1:'''',\n        ObjectPk2:'''',\n        CheckValidate:false \n });\n }\n*/"},{"id":88521,"key":"Context","value":""}],"output":[{"id":105692,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":87638,"parameter":null,"workflowAction":{"id":74650,"code":"","description":"send noticification","documentList":[],"actionTypeId":6,"actionId":"000019b479164ad1adeb7631d3fd6161","userId":"{{Context.triggerClerkId}}","input":[{"id":128584,"key":"HTML","value":"<div style=\"font: 13px/normal source_sans,Microsoft YaHei,STHeiti;overflow-y: auto; overflow-x: hidden;height: 460px;margin:10px\">\n\n    <label ng-if=\"Context.apprDocInfo2.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.approvedDocumentList'' | translate}}:</label>\n\n    <div ng-if=\"Context.apprDocInfo2.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData1\" ></platform-Grid>\n    </div>\n\n\n    <label ng-if=\"Context.apprComResult1_2.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.approvedCommentList'' | translate}} :</label>\n    <div ng-if=\"Context.apprComResult1_2.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData3\" ></platform-Grid>\n    </div>\n\n    <label ng-if=\"Context.declResult1_2.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.declinedDocumentList'' | translate}} :</label>\n\n    <div ng-if=\"Context.declResult1_2.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData2\" ></platform-Grid>\n    </div>\n\n    <label ng-if=\"Context.declComResult1_2.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.declinedCommentList'' | translate}}:</label>\n    <div ng-if=\"Context.declComResult1_2.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData4\" ></platform-Grid>\n    </div>\n\n</div>\n    \n    <footer class=\"modal-footer \">\n    <button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"onOk()\" >\n       {{''documents.centralquery.workflow.ok'' | translate}}\n    </button>\n\n</footer>"},{"id":96605,"key":"Script","value":"var gridAPI = $injector.get(''platformGridAPI'');\nvar $translate = $injector.get(''$translate'');\n$scope.gridId1 = ''appr'';\n$scope.gridData1 = {state: $scope.gridId1};\n\n\nvar columns1 = [\n      {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\n   name: $translate.instant(''documents.centralquery.workflow.file''),\n    sortable: true,\n    width:150,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"PROJECT\",\n   name: $translate.instant(''documents.centralquery.workflow.project''),\n    sortable: true,\n    width:180,\n    readonly: true\n    },\n    {\n    id: 3,\n    formatter: ''date'',\n    field: \"FileUploadDate\",\n   name: $translate.instant(''documents.centralquery.workflow.uploadDate''),\n    sortable: true,\n    width:110,\n    readonly: true\n    },\n    {\n    id: 4,\n    formatter: ''datetime'',\n    field: \"ApprovalDate\",\n   name: $translate.instant(''documents.centralquery.workflow.approvalDate''),\n    sortable: true,\n    width:130,\n    readonly: true\n    }\n];\n\n\nvar grid1 = {\n    columns: columns1,\n    data: [],\n    id: $scope.gridId1,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data1=$scope.Context.apprDocInfo2;\n\n\ngridAPI.grids.config(grid1);\ngridAPI.items.data($scope.gridId1,data1);\n\n$scope.gridId2 = ''decl'';\n$scope.gridData2 = {state: $scope.gridId2};\n\n\nvar columns2 = [\n        {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\n    name: $translate.instant(''documents.centralquery.workflow.file''),\n    sortable: true,\n    width:150,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"PROJECT\",\n   name: $translate.instant(''documents.centralquery.workflow.project''),\n    sortable: true,\n    width:180,\n    readonly: true\n    },\n      {\n    id: 3,\n    formatter: ''description'',\n    field: \"PROGRESSBY\",\n   name: $translate.instant(''documents.centralquery.workflow.declinedBy''),\n    sortable: true,\n    width:90,\n    readonly: true\n    },\n    {\n    id: 4,\n    formatter: ''date'',\n    field: \"FileUploadDate\",\n   name: $translate.instant(''documents.centralquery.workflow.uploadDate''),\n    sortable: true,\n    width:110,\n    readonly: true\n    },\n    {\n    id: 5,\n    formatter: ''datetime'',\n    field: \"DELINEDDATE\",\n   name: $translate.instant(''documents.centralquery.workflow.declinedDate''),\n    sortable: true,\n    width: 130,\n    readonly: true\n    }\n];\n\n\nvar grid2 = {\n    columns: columns2,\n    data: [],\n    id: $scope.gridId2,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data2=$scope.Context.declResult1_2;\n\n\ngridAPI.grids.config(grid2);\ngridAPI.items.data($scope.gridId2,data2);\n\n\n$scope.gridId3 = ''apprCom'';\n$scope.gridData3 = {state: $scope.gridId3};\n\n\nvar columns3 = [\n {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\nname: $translate.instant(''documents.centralquery.workflow.clerk''),\n    sortable: true,\n    width:100,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"COMMENT\",\n    name: $translate.instant(''documents.centralquery.workflow.comment''),\n    sortable: true,\n    width:350,\n    readonly: true\n    },\n    {\n    id: 3,\n    formatter: ''datetime'',\n    field: \"APPROVALDATE\",\n   name: $translate.instant(''documents.centralquery.workflow.commentDate''),\n    sortable: true,\n    width:130,\n    readonly: true\n    }\n];\n\n\nvar grid3 = {\n    columns: columns3,\n    data: [],\n    id: $scope.gridId3,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''BAS_CLERK_PROGRESSBY_FK'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data3=$scope.Context.apprComResult1_2;\n\n\ngridAPI.grids.config(grid3);\ngridAPI.items.data($scope.gridId3,data3);\n\n\n\n\n$scope.gridId4 = ''declCom'';\n$scope.gridData4 = {state: $scope.gridId4};\n\n\nvar columns4 = [\n {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\nname: $translate.instant(''documents.centralquery.workflow.clerk''),\n    sortable: true,\n    width:100,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"COMMENT\",\n    name: $translate.instant(''documents.centralquery.workflow.comment''),\n    sortable: true,\n    width:350,\n    readonly: true\n    },\n    {\n    id: 3,\n    formatter: ''datetime'',\n    field: \"DELINEDDATE\",\n   name: $translate.instant(''documents.centralquery.workflow.commentDate''),\n    sortable: true,\n    width:130,\n    readonly: true\n    }\n];\n\n\nvar grid4 = {\n    columns: columns4,\n    data: [],\n    id: $scope.gridId4,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''BAS_CLERK_PROGRESSBY_FK'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data4=$scope.Context.declComResult1_2;\n\n\ngridAPI.grids.config(grid4);\ngridAPI.items.data($scope.gridId4,data4);\n\n\n"},{"id":77581,"key":"Context","value":"{{Context}}"},{"id":128179,"key":"IsPopUp","value":""},{"id":49606,"key":"EvaluateProxy","value":""},{"id":30517,"key":"DisableRefresh","value":""},{"id":46322,"key":"AllowReassign","value":""},{"id":77569,"key":"Title","value":"Approval Result"},{"id":95269,"key":"Subtitle","value":""},{"id":106578,"key":"DialogConfig","value":"{\"minWidth\":\"650px\"}"}],"output":[{"id":115281,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":96410,"parameter":null,"workflowAction":{"id":55058,"code":"","description":"End","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":"true","parameter":"true"},{"id":"false","parameter":"false"}],"level":5770}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":73077,"parameter":"g","workflowAction":{"id":66799,"code":"","description":"all approval finished?","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":67361,"key":"Formula","value":"Context.approvedCounter2==Context.passApprNum2"},{"id":89316,"key":"Context","value":""}],"output":[{"id":97029,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":65227,"parameter":"true","workflowAction":{"id":54622,"code":"","description":"Go To ''level 2 summary (CODE:GT5)''","documentList":[],"actionTypeId":10,"actionId":"E0000000000000000000000000000000","userId":"","input":[{"id":92876,"key":"Code","value":"GT5"}],"output":[],"lifeTime":1,"transitions":[{"id":47928,"parameter":null,"workflowAction":{"id":30326,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":null,"parameter":null}]}},{"id":91124,"parameter":"false","workflowAction":{"id":21512,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":"true","parameter":"true"},{"id":"false","parameter":"false"}],"level":5230}}],"options":[{"id":"s","parameter":"s"},{"id":"g","parameter":"g"}],"level":5050}}],"input":[{"id":113831,"key":"SQL","value":"UPDATE WFE_ACTIONINSTANCE SET USERDEFINED1=CONVERT(nvarchar(255),@approvedCounter2) WHERE ID= (SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 2 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL  ORDER BY ENDTIME DESC)\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED2=@apprRowNumStr2 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 2 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL ORDER BY ENDTIME DESC)\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED3=@declRowNumStr2 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 2 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL ORDER BY ENDTIME DESC)\n\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED4=@apprCom2 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 2 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL ORDER BY ENDTIME DESC)\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED5=@declCom2 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 2 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL ORDER BY ENDTIME DESC)"},{"id":106619,"key":"Params","value":"InstanceId: {{Context.InstanceId}} ;approvedCounter2 :{{Context.approvedCounter2}} ;apprRowNumStr2 :{{Context.apprRowNumStr2}} ;declRowNumStr2 :{{Context.declRowNumStr2}} ;apprCom2 :{{Context.apprCom2}} ;declCom2 :{{Context.declCom2}} ;"}],"output":[{"id":103825,"key":"Output","value":""}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":16299,"parameter":"true","workflowAction":{"id":10647,"code":"","description":"counter2++","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":72944,"key":"Script","value":"Context.counter2++;"},{"id":89592,"key":"Context","value":""}],"output":[{"id":70922,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":98987,"parameter":null,"workflowAction":{"id":12443,"code":"","description":"send ToDo finished?","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":88307,"key":"Formula","value":"Context.counter2>=Context.length2"},{"id":121673,"key":"Context","value":""}],"output":[{"id":96008,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":91392,"parameter":"true","workflowAction":{"id":94251,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}},{"id":87614,"parameter":"false","workflowAction":{"id":60903,"code":"","description":"Go To ''set clerkid (GT2)''","documentList":[],"actionTypeId":10,"actionId":"E0000000000000000000000000000000","userId":"","input":[{"id":109265,"key":"Code","value":"GT2"}],"output":[],"lifeTime":1,"transitions":[{"id":76926,"parameter":null,"workflowAction":{"id":58693,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":"true","parameter":"true"},{"id":"false","parameter":"false"}],"level":4690}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":"true","parameter":"true"}],"level":4330}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":54882,"parameter":"false","workflowAction":{"id":87977,"code":"","description":"change status","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":85209,"key":"Script","value":"\n//approved\nfor (var i=0;i<Context.apprDocInfo1.length;i++){\n \n    ActionUtil.ExecuteAction(''5F6E595C0BF6412694D9C40AD66621DF'',\n    {\n        StatusName:''prjdocument'',\n        ObjectId:Context.apprDocInfo1[i].ID,\n        NewStatusId:Context.apprDocInfo1[i].PRJ_DOCUMENTSTATUS_TARGET_FK,//approved status id\n        ProjectId:0,\n        Remark:''workflow change status'',\n        IgnoreAccessRight:'''',\n        ObjectPk1:'''',\n        ObjectPk2:'''',\n        CheckValidate:false \n });\n  \n\tContext.apprDocInfo1[i].ApprovalDate = new Date().toISOString();\n  \n }\n\n\n\n\n//declined\n/*\nfor (var i=0;i<Context.declDocInfo1.length;i++){\n \n    ActionUtil.ExecuteAction(''5F6E595C0BF6412694D9C40AD66621DF'',\n    {\n        StatusName:''prjdocument'',\n        ObjectId:Context.declDocInfo1[i].ID,\n        NewStatusId:Context.declineStatusId,//declined status id\n        ProjectId:0,\n        Remark:''workflow change status'',\n        IgnoreAccessRight:'''',\n        ObjectPk1:'''',\n        ObjectPk2:'''',\n        CheckValidate:false \n });\n }\n*/"},{"id":88521,"key":"Context","value":""}],"output":[{"id":105692,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":81374,"parameter":null,"workflowAction":{"id":14455,"code":"","description":"send noticification","documentList":[],"actionTypeId":6,"actionId":"000019b479164ad1adeb7631d3fd6161","userId":"{{Context.triggerClerkId}}","input":[{"id":128584,"key":"HTML","value":"<div style=\"font: 13px/normal source_sans,Microsoft YaHei,STHeiti;overflow-y: auto; overflow-x: hidden;height: 460px;margin:10px\">\n\n    <label ng-if=\"Context.apprDocInfo1.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.approvedDocumentList'' | translate}}:</label>\n\n    <div ng-if=\"Context.apprDocInfo1.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData1\" ></platform-Grid>\n    </div>\n\n\n    <label ng-if=\"Context.apprComResult1.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.approvedCommentList'' | translate}} :</label>\n    <div ng-if=\"Context.apprComResult1.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData3\" ></platform-Grid>\n    </div>\n\n    <label ng-if=\"Context.declDocInfo1.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.declinedDocumentList'' | translate}} :</label>\n\n    <div ng-if=\"Context.declDocInfo1.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData2\" ></platform-Grid>\n    </div>\n\n    <label ng-if=\"Context.declComResult1.length>0\" style=\"margin:10px 0\">{{''documents.centralquery.workflow.declinedCommentList'' | translate}}:</label>\n    <div ng-if=\"Context.declComResult1.length>0\" class=\"platformgrid grid-container\"  style=\"overflow-y: auto; overflow-x: hidden;max-height: 180px;font-size:12px\">\n    <platform-Grid data=\"gridData4\" ></platform-Grid>\n    </div>\n\n</div>\n    \n    <footer class=\"modal-footer \">\n    <button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"onOk()\" >\n       {{''documents.centralquery.workflow.ok'' | translate}}\n    </button>\n\n</footer>"},{"id":96605,"key":"Script","value":"\nvar $translate = $injector.get(''$translate'');\nvar gridAPI = $injector.get(''platformGridAPI'');\n\n$scope.gridId1 = ''appr'';\n$scope.gridData1 = {state: $scope.gridId1};\n\n\nvar columns1 = [  \n      {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\n   name: $translate.instant(''documents.centralquery.workflow.file''),\n    sortable: true,\n    width:150,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"PROJECT\",\n   name: $translate.instant(''documents.centralquery.workflow.project''),\n    sortable: true,\n    width:180,\n    readonly: true\n    },\n    {\n    id: 3,\n    formatter: ''date'',\n    field: \"FileUploadDate\",\n   name: $translate.instant(''documents.centralquery.workflow.uploadDate''),\n    sortable: true,\n    width:110,\n    readonly: true\n    },\n    {\n    id: 4,\n    formatter: ''datetime'',\n    field: \"ApprovalDate\",\n   name: $translate.instant(''documents.centralquery.workflow.approvalDate''),\n    sortable: true,\n    width:130,\n    readonly: true\n    }\n];\n\n\nvar grid1 = {\n    columns: columns1,\n    data: [],\n    id: $scope.gridId1,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data1=$scope.Context.apprDocInfo1;\n\n\ngridAPI.grids.config(grid1);\ngridAPI.items.data($scope.gridId1,data1);\n\n$scope.gridId2 = ''decl'';\n$scope.gridData2 = {state: $scope.gridId2};\n\n\nvar columns2 = [  \n    {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\n    name: $translate.instant(''documents.centralquery.workflow.file''),\n    sortable: true,\n    width:150,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"PROJECT\",\n   name: $translate.instant(''documents.centralquery.workflow.project''),\n    sortable: true,\n    width:180,\n    readonly: true\n    },\n      {\n    id: 3,\n    formatter: ''description'',\n    field: \"PROGRESSBY\",\n   name: $translate.instant(''documents.centralquery.workflow.declinedBy''),\n    sortable: true,\n    width:90,\n    readonly: true\n    },\n    {\n    id: 4,\n    formatter: ''date'',\n    field: \"FileUploadDate\",\n   name: $translate.instant(''documents.centralquery.workflow.uploadDate''),\n    sortable: true,\n    width:110,\n    readonly: true\n    },\n    {\n    id: 5,\n    formatter: ''datetime'',\n    field: \"DELINEDDATE\",\n   name: $translate.instant(''documents.centralquery.workflow.declinedDate''),\n    sortable: true,\n    width: 130,\n    readonly: true\n    }\n];\n\n\nvar grid2 = {\n    columns: columns2,\n    data: [],\n    id: $scope.gridId2,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data2=$scope.Context.declDocInfo1;\n\n\ngridAPI.grids.config(grid2);\ngridAPI.items.data($scope.gridId2,data2);\n\n\n$scope.gridId3 = ''apprCom'';\n$scope.gridData3 = {state: $scope.gridId3};\n\n\nvar columns3 = [  \n {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\nname: $translate.instant(''documents.centralquery.workflow.clerk''),\n    sortable: true,\n    width:100,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"COMMENT\",\n    name: $translate.instant(''documents.centralquery.workflow.comment''),\n    sortable: true,\n    width:350,\n    readonly: true\n    },\n    {\n    id: 3,\n    formatter: ''datetime'',\n    field: \"APPROVALDATE\",\n   name: $translate.instant(''documents.centralquery.workflow.commentDate''),\n    sortable: true,\n    width:130,\n    readonly: true\n    }\n];\n\n\nvar grid3 = {\n    columns: columns3,\n    data: [],\n    id: $scope.gridId3,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''BAS_CLERK_PROGRESSBY_FK'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data3=$scope.Context.apprComResult1;\n\n\ngridAPI.grids.config(grid3);\ngridAPI.items.data($scope.gridId3,data3);\n\n\n\n\n$scope.gridId4 = ''declCom'';\n$scope.gridData4 = {state: $scope.gridId4};\n\n\nvar columns4 = [  \n    {\n    id: 1,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\n    name: $translate.instant(''documents.centralquery.workflow.clerk''),\n    sortable: true,\n    width:100,\n    readonly: true\n    },\n    {\n    id: 2,\n    formatter: ''description'',\n    field: \"COMMENT\",\nname: $translate.instant(''documents.centralquery.workflow.comment''),\n    sortable: true,\n    width:350,\n    readonly: true\n    },\n    {\n    id: 3,\n    formatter: ''datetime'',\n    field: \"DELINEDDATE\",\n   name: $translate.instant(''documents.centralquery.workflow.commentDate''),\n    sortable: true,\n    width:130,\n    readonly: true\n    }\n];\n\n\nvar grid4 = {\n    columns: columns4,\n    data: [],\n    id: $scope.gridId4,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''BAS_CLERK_PROGRESSBY_FK'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data4=$scope.Context.declComResult1;\n\n\ngridAPI.grids.config(grid4);\ngridAPI.items.data($scope.gridId4,data4);\n\n\n"},{"id":77581,"key":"Context","value":"{{Context}}"},{"id":128179,"key":"IsPopUp","value":""},{"id":76310,"key":"EvaluateProxy","value":""},{"id":94678,"key":"DisableRefresh","value":""},{"id":92727,"key":"AllowReassign","value":""},{"id":77569,"key":"Title","value":"Approval Result"},{"id":95269,"key":"Subtitle","value":""},{"id":106578,"key":"DialogConfig","value":"{\"minWidth\":\"650px\"}"}],"output":[{"id":115281,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":62456,"parameter":null,"workflowAction":{"id":83702,"code":"","description":"End","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":"true","parameter":"true"},{"id":"false","parameter":"false"}],"level":3790}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":37607,"parameter":"g","workflowAction":{"id":44413,"code":"","description":"all approval finished?","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":86596,"key":"Formula","value":"Context.approvedCounter1==Context.passApprNum1"},{"id":67331,"key":"Context","value":""}],"output":[{"id":116752,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":65140,"parameter":"true","workflowAction":{"id":79273,"code":"","description":"Go To ''level 1 summary (CODE:GT4)''","documentList":[],"actionTypeId":10,"actionId":"E0000000000000000000000000000000","userId":"","input":[{"id":119253,"key":"Code","value":"GT4"}],"output":[],"lifeTime":1,"transitions":[{"id":21585,"parameter":null,"workflowAction":{"id":95992,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":null,"parameter":null}]}},{"id":54147,"parameter":"false","workflowAction":{"id":77421,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":"true","parameter":"true"},{"id":"false","parameter":"false"}],"level":3250}}],"options":[{"id":"s","parameter":"s"},{"id":"g","parameter":"g"}],"level":3070}}],"input":[{"id":65948,"key":"SQL","value":"UPDATE WFE_ACTIONINSTANCE SET USERDEFINED1=CONVERT(nvarchar(255),@approvedCounter1) WHERE ID= (SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 1 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL  ORDER BY ENDTIME DESC);\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED2=@apprRowNumStr1 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 1 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL ORDER BY ENDTIME DESC);\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED3=@declRowNumStr1  WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 1 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL ORDER BY ENDTIME DESC);\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED4=@apprCom1 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 1 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL ORDER BY ENDTIME DESC);\n\nUPDATE WFE_ACTIONINSTANCE SET USERDEFINED5=@declCom1 WHERE ID=(SELECT TOP 1 ID FROM WFE_ACTIONINSTANCE WHERE WFE_INSTANCE_FK=@InstanceId AND ISRUNNING=0 AND DESCRIPTION=N''Level 1 approving'' AND BAS_CLERK_PROGRESSBY_FK IS NOT NULL ORDER BY ENDTIME DESC);"},{"id":86351,"key":"Params","value":"InstanceId:{{Context.InstanceId}} ;approvedCounter1 :{{Context.approvedCounter1}} ;apprRowNumStr1 :{{Context.apprRowNumStr1}} ;declRowNumStr1 :{{Context.declRowNumStr1}} ; apprCom1:{{Context.apprCom1}} ;declCom1 :{{Context.declCom1}} ;"}],"output":[{"id":103352,"key":"Output","value":""}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":49744,"parameter":"true","workflowAction":{"id":34688,"code":"","description":"counter1++","documentList":[],"actionTypeId":5,"actionId":"409ed310344011e5a151feff819cdc9f","userId":"","input":[{"id":72944,"key":"Script","value":"Context.counter1++;"},{"id":89592,"key":"Context","value":""}],"output":[{"id":70922,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":69947,"parameter":null,"workflowAction":{"id":19481,"code":"","description":"send ToDo finished?","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","input":[{"id":88307,"key":"Formula","value":"Context.counter1>=Context.length1"},{"id":121673,"key":"Context","value":""}],"output":[{"id":96008,"key":"Result","value":""}],"lifeTime":1,"transitions":[{"id":13009,"parameter":"true","workflowAction":{"id":19804,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}},{"id":24408,"parameter":"false","workflowAction":{"id":23424,"code":"","description":"Go To ''set clerkid (GT1)''","documentList":[],"actionTypeId":10,"actionId":"E0000000000000000000000000000000","userId":"","input":[{"id":86517,"key":"Code","value":"GT1"}],"output":[],"lifeTime":1,"transitions":[{"id":94498,"parameter":null,"workflowAction":{"id":30188,"code":"","description":"????","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","input":[],"output":[],"lifeTime":1,"transitions":[],"options":[]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":"true","parameter":"true"},{"id":"false","parameter":"false"}],"level":2710}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":"true","parameter":"true"}],"level":2350}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}}],"options":[{"id":null,"parameter":null}]}},{"id":100021,"parameter":"false","workflowAction":{"id":47237,"code":"","description":"User Input","documentList":[],"actionTypeId":6,"actionId":"00000000000000000000000000000000","userId":"","lifeTime":1,"transitions":[{"id":46544,"parameter":null,"workflowAction":{"id":43455,"code":"","description":"End","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","lifeTime":1,"transitions":null,"input":[],"output":[]}}],"input":[{"id":77517,"key":"Config","value":"[{\"description\":\"Title\",\"type\":\"title\",\"options\":{\"displayText\":\"Warning:\"}},{\"description\":\"Subtitle\",\"type\":\"subtitle\",\"options\":{\"displayText\":\"{{Context.documentInValidNote}}\",\"escapeHtml\":false}}]"},{"id":130383,"key":"IsPopUp","value":true},{"id":46055,"key":"IsNotification","value":""},{"id":37047,"key":"EvaluateProxy","value":""},{"id":32891,"key":"DisableRefresh","value":""},{"id":95587,"key":"AllowReassign","value":""},{"id":66551,"key":"EscalationDisabled","value":""},{"id":61487,"key":"StopVisible","value":""},{"id":44686,"key":"CancelVisible","value":""},{"id":110372,"key":"Context","value":""}],"output":[{"id":74498,"key":"Context","value":""}]}}],"input":[{"id":111187,"key":"Formula","value":"Context.docInfo1.length>0"},{"id":84688,"key":"Context","value":""}],"output":[{"id":70143,"key":"Result","value":""}],"level":1270}}],"input":[{"id":124199,"key":"Script","value":"var templateId = parseInt(Context.TemplateId, 10);\nvar entityIds = ''('';\nif (Context.EntityIdList &&  Context.EntityIdList.length) {\n    for (var i = 0; i < Context.EntityIdList.length; i++) {\n        entityIds += parseInt(Context.EntityIdList[i], 10) + '','';\n    }\n    entityIds = entityIds.substr(0, entityIds.length - 1) + '')'';\n    var sql = \n\"SELECT \" +\n\t\t\"ROW_NUMBER()OVER(ORDER BY PRJ_DOCUMENT.ID) AS ROW_NUM, \" +\n\t\t\"PRJ_DOCUMENT.ID, \" +\n\t\t\"PRJ_DOCUMENT.PRJ_DOCUMENTSTATUS_FK, \" +\n\t\t\"FROMSTATUS.DESCRIPTION AS FROM_STATUS, \" +\n\t\t\"STATUSLIST.PRJ_DOCUMENTSTATUS_TARGET_FK, \" +\n\t\t\"TOSTATUS.DESCRIPTION AS TO_STATUS, \" +\n\t\t\"PRJ_DOCUMENT.DESCRIPTION , \" +\n\t\t\"CASE \" +\n\t\t\t\t\"WHEN PRJ_DOCUMENT.PRJ_PROJECT_FK IS NULL THEN '''' \" +\n\t\t\t\t\"ELSE PROJECTNO+''/''+PROJECT_NAME \" +\n\t\t\"END AS PROJECT, \" +\n\t\t\"BAS_FILEARCHIVEDOC_FK, \" +\n\t\t\"BAS_FILEARCHIVEDOC_FK as FileArchiveDocFk, \" + \n\t\t\"BAS_FILEARCHIVEDOC.FILEUPLOADDATE as FileUploadDate, \" +\n\t\t\"BAS_FILEARCHIVEDOC.ORIGINFILENAME as OriginFileName  \" +\n\"FROM PRJ_DOCUMENT \" +\n\"LEFT JOIN PRJ_PROJECT ON PRJ_PROJECT.ID=PRJ_DOCUMENT.PRJ_PROJECT_FK \" +\n\"LEFT JOIN BAS_FILEARCHIVEDOC ON BAS_FILEARCHIVEDOC.ID=PRJ_DOCUMENT.BAS_FILEARCHIVEDOC_FK \" +\n\"LEFT JOIN ( \" +\n\t\t\"SELECT \" +\n\t\t\t\t\"PRJ_DOCUMENTSTATUS_FK, \" +\n\t\t\t\t\"PRJ_DOCUMENTSTATUS_TARGET_FK, \" +\n\t\t\t\t\"PRJ_DOCUMENTSTATUS.BAS_RUBRIC_CATEGORY_FK AS RUBRIC_CATEGORY_FK \" +\n\t\t\"FROM PRJ_DOCUMENTSTAWORKFLOW \" +\n\t\t\"LEFT JOIN PRJ_DOCUMENTSTATUSRULE ON PRJ_DOCUMENTSTATUSRULE.ID=PRJ_DOCUMENTSTAWORKFLOW.PRJ_DOCUMENTSTATUSRULE_FK \" + \n\t\t\"LEFT JOIN PRJ_DOCUMENTSTATUS ON PRJ_DOCUMENTSTATUS.ID = PRJ_DOCUMENTSTATUSRULE.PRJ_DOCUMENTSTATUS_FK \" +\n\t\t\"WHERE WFE_TEMPLATE_FK=\" + templateId + \") \" +\n\t\t\"STATUSLIST ON STATUSLIST.PRJ_DOCUMENTSTATUS_FK=PRJ_DOCUMENT.PRJ_DOCUMENTSTATUS_FK \" +\n\t\t\t\t\t\"AND PRJ_DOCUMENT.BAS_RUBRIC_CATEGORY_FK = STATUSLIST.RUBRIC_CATEGORY_FK \" +\n\"LEFT JOIN PRJ_DOCUMENTSTATUS FROMSTATUS ON FROMSTATUS.ID=PRJ_DOCUMENT.PRJ_DOCUMENTSTATUS_FK \" +\n\"LEFT JOIN PRJ_DOCUMENTSTATUS TOSTATUS ON TOSTATUS.ID=STATUSLIST.PRJ_DOCUMENTSTATUS_TARGET_FK \" +\n\"WHERE PRJ_DOCUMENTSTATUS_TARGET_FK IS NOT NULL AND  PRJ_DOCUMENT.ID IN \" + entityIds;\n    var docInfo1 = ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'', {\n        SQL: sql\n    }).Output;\n\n    Context.docInfo1 = docInfo1;\n}\nelse {\n     entityIds = ''()'';\n    Context.docInfo1 = [];\n}\nContext.TemplateId = templateId;\nContext.EntityIds = entityIds;"},{"id":120719,"key":"Context","value":""}],"output":[{"id":97520,"key":"Context","value":""}]}},{"id":23121,"parameter":"false","workflowAction":{"id":18413,"code":"","description":"warning","documentList":[],"actionTypeId":6,"actionId":"000019b479164ad1adeb7631d3fd6161","userId":"","input":[{"id":101281,"key":"HTML","value":"\n<div style=\"width:100%;font: 13px/normal source_sans,Microsoft YaHei,STHeiti;\">\n       <label style=\"padding:10px\">{{''documents.centralquery.workflow.continueDocument'' | translate}}\n       </label>\n<div class=\"platformgrid grid-container modal-body\" style=\" overflow-y: auto;width:580px;font-size:12px;\">\n    <platform-Grid data=\"gridData\" ></platform-Grid>\n    </div>\n    </div>\n \n    <footer class=\"modal-footer \">\n    <button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"isFinish(''Cancel'')\">\n        {{''documents.centralquery.workflow.cancel'' | translate}}\n    </button>\n    <button type=\"button\" class=\"btn btn-default ng-binding\" data-ng-click=\"isFinish(''OK'')\" >\n        {{''documents.centralquery.workflow.ok'' | translate}}\n    </button>\n\n</footer>\n    \n    "},{"id":88536,"key":"Script","value":"var gridAPI = $injector.get(''platformGridAPI'');\n$scope.gridId = ''doc'';\n$scope.gridData = {state: $scope.gridId};\nvar $translate = $injector.get(''$translate'');\nvar columns = [\n   {\n    id: 1,\n    formatter: ''number'',\n    field: \"ID\",\n    name: $translate.instant(''documents.centralquery.workflow.documentId''),\n    sortable: true,\n    grouping: {},\n    editor: \"\",\n    width:80,\n    editorOptions: {},\n    showClearButton: true,\n    lookupOptions: {},\n    readonly: true\n    },\n   {\n    id: 2,\n    formatter: ''description'',\n    field: \"FROM_STATUS\",\n    name: $translate.instant(''documents.centralquery.workflow.status''),\n    sortable: true,\n    grouping: {},\n    editor: \"\",\n    width:100,\n    editorOptions: {},\n    showClearButton: true,\n    lookupOptions: {},\n    readonly: true\n    },\n \n   {\n    id: 3,\n    formatter: ''description'',\n    field: \"DESCRIPTION\",\n   name: $translate.instant(''documents.centralquery.workflow.file''),\n    sortable: true,\n    grouping: {},\n    editor: \"\",\n    width:200,\n    editorOptions: {},\n    showClearButton: true,\n    lookupOptions: {},\n    readonly: true\n    },\n    {\n    id: 4,\n    formatter: ''description'',\n    field: \"PROJECT\",\n   name: $translate.instant(''documents.centralquery.workflow.project''),\n    sortable: true,\n    width:250,\n    readonly: true\n    }\n    \n\n];\n\n\nvar grid = {\n    columns: columns,\n    data: [],\n    id: $scope.gridId,\n    options: {\n        tree: false,\n        indicator: true,\n        idProperty: ''ROW_NUM'',\n        iconClass: ''ico-warning'',\n        skipPermissionCheck: true,\n        collapsed : false\n    }\n};\n\nvar data=$scope.Context.notStartList;\n\n\ngridAPI.grids.config(grid);\ngridAPI.items.data($scope.gridId,data);\n\nfunction isFinish(e){\n  $scope.Context.selectResult = e;\n  \n $scope.onOk();\n  \n}\n\n$scope.isFinish=isFinish;\n\n$scope.Context.documentInValidNote = $translate.instant(''documents.centralquery.workflow.documentInValidNote'');"},{"id":97183,"key":"Context","value":"{{Context}}"},{"id":90893,"key":"IsPopUp","value":"true"},{"id":72237,"key":"EvaluateProxy","value":""},{"id":93629,"key":"DisableRefresh","value":""},{"id":22431,"key":"AllowReassign","value":""},{"id":129377,"key":"Title","value":"Warning"},{"id":91386,"key":"Subtitle","value":""},{"id":92646,"key":"DialogConfig","value":""}],"output":[{"id":103448,"key":"Context","value":""}],"lifeTime":1,"transitions":[{"id":73919,"parameter":null,"workflowAction":{"id":99325,"code":"","description":"Formula","documentList":[],"actionTypeId":3,"actionId":"d02b52ff5e0943d2936e1eb00d7ce2de","userId":"","lifeTime":1,"transitions":[{"id":48489,"parameter":"OK","workflowAction":{"id":99282,"code":"","description":"Go To ''(CODE:GT0)''","documentList":[],"actionTypeId":10,"actionId":"E0000000000000000000000000000000","userId":"","lifeTime":1,"transitions":[{"id":89316,"parameter":null,"workflowAction":{"id":87167,"code":"","description":"End","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","lifeTime":1,"transitions":null,"input":[],"output":[]}}],"input":[{"id":95877,"key":"Code","value":"GT0"}],"output":[]}},{"id":71949,"parameter":"Cancel","workflowAction":{"id":52740,"code":"","description":"End","documentList":[],"actionTypeId":2,"actionId":null,"userId":"","lifeTime":1,"transitions":null,"input":[],"output":[]}}],"input":[{"id":111057,"key":"Formula","value":"Context.selectResult"},{"id":72059,"key":"Context","value":""}],"output":[{"id":115417,"key":"Result","value":""}],"level":1270}}],"options":[]}}],"input":[{"id":112607,"key":"Formula","value":"Context.notStartList.length==0"},{"id":110640,"key":"Context","value":""}],"output":[{"id":108620,"key":"Result","value":""}],"level":910}}],"input":[{"id":82730,"key":"Script","value":"var templateId = parseInt(Context.TemplateId, 10);\nvar entityIds = ''('';\nif (Context.EntityIdList &&  Context.EntityIdList.length) {\n    for (var i = 0; i < Context.EntityIdList.length; i++) {\n        entityIds += parseInt(Context.EntityIdList[i], 10) + '','';\n    }\n    entityIds = entityIds.substr(0, entityIds.length - 1) + '')'';\n    var sql =\n\"SELECT \" +\n\t\t\"ROW_NUMBER()OVER(ORDER BY PRJ_DOCUMENT.ID) AS ROW_NUM, \" +\n\t\t\"PRJ_DOCUMENT.ID, \" +\n\t\t\"PRJ_DOCUMENT.PRJ_DOCUMENTSTATUS_FK, \" +\n\t\t\"FROMSTATUS.DESCRIPTION AS FROM_STATUS, \" +\n\t\t\"STATUSLIST.PRJ_DOCUMENTSTATUS_TARGET_FK, \" +\n\t\t\"TOSTATUS.DESCRIPTION AS TO_STATUS, \" +\n\t\t\"PRJ_DOCUMENT.DESCRIPTION , \" +\n\t\t\"CASE \" +\n\t\t\t\t\"WHEN PRJ_DOCUMENT.PRJ_PROJECT_FK IS NULL THEN '''' \" +\n\t\t\t\t\"ELSE PROJECTNO+''/''+PROJECT_NAME \" +\n\t\t\"END AS PROJECT, \" +\n\t\t\"BAS_FILEARCHIVEDOC_FK \" +\n\"FROM PRJ_DOCUMENT \" +\n\"LEFT JOIN PRJ_PROJECT ON PRJ_PROJECT.ID=PRJ_DOCUMENT.PRJ_PROJECT_FK \" +\n\"LEFT JOIN ( \" +\n\t\t\"SELECT \" +\n\t\t\t\t\"PRJ_DOCUMENTSTATUS_FK, \" +\n\t\t\t\t\"PRJ_DOCUMENTSTATUS_TARGET_FK, \" +\n\t\t\t\t\"PRJ_DOCUMENTSTATUS.BAS_RUBRIC_CATEGORY_FK AS RUBRIC_CATEGORY_FK \" +\n\t\t\"FROM PRJ_DOCUMENTSTAWORKFLOW \" +\n\t\t\"LEFT JOIN PRJ_DOCUMENTSTATUSRULE ON PRJ_DOCUMENTSTATUSRULE.ID=PRJ_DOCUMENTSTAWORKFLOW.PRJ_DOCUMENTSTATUSRULE_FK \" + \n\t\t\"LEFT JOIN PRJ_DOCUMENTSTATUS ON PRJ_DOCUMENTSTATUS.ID = PRJ_DOCUMENTSTATUSRULE.PRJ_DOCUMENTSTATUS_FK \" +\n\t\t\"WHERE WFE_TEMPLATE_FK=\" + templateId + \") \" +\n\t\t\"STATUSLIST ON STATUSLIST.PRJ_DOCUMENTSTATUS_FK=PRJ_DOCUMENT.PRJ_DOCUMENTSTATUS_FK \" +\n\t\t\t\t\t\"AND PRJ_DOCUMENT.BAS_RUBRIC_CATEGORY_FK = STATUSLIST.RUBRIC_CATEGORY_FK \" +\n\"LEFT JOIN PRJ_DOCUMENTSTATUS FROMSTATUS ON FROMSTATUS.ID=PRJ_DOCUMENT.PRJ_DOCUMENTSTATUS_FK \" +\n\"LEFT JOIN PRJ_DOCUMENTSTATUS TOSTATUS ON TOSTATUS.ID=STATUSLIST.PRJ_DOCUMENTSTATUS_TARGET_FK \" +\n\"WHERE PRJ_DOCUMENTSTATUS_TARGET_FK IS NULL AND  PRJ_DOCUMENT.ID IN \" + entityIds;\n    var notStartList = ActionUtil.ExecuteAction(''6f3a49b7c1b94448886a868625829e4d'', {\n        SQL: sql\n    }).Output;\n\n    Context.notStartList = notStartList;\n}\nelse {\n     entityIds = ''()'';\n    Context.notStartList = [];\n}\nContext.TemplateId = templateId;\nContext.EntityIds = entityIds;"},{"id":77657,"key":"Context","value":""}],"output":[{"id":80883,"key":"Context","value":""}]}}],"input":[{"id":108794,"key":"Script","value":"Context.issuedDate=Context.startDate.split(''T'')[0];"},{"id":84934,"key":"Context","value":""}],"output":[{"id":102797,"key":"Context","value":""}]}}],"input":[{"id":122211,"key":"SQL","value":"SELECT NAME FROM FRM_USER WHERE ID=@UserId"},{"id":65873,"key":"Params","value":"UserId:{{Context.UserId}} ;"}],"output":[{"id":77477,"key":"Output","value":"issuer"}]}}],"input":[{"id":87790,"key":"Script","value":"\nContext.EntityIds = ''('';\nif (Context.EntityIdList && Context.EntityIdList.length) {\n    for (var i = 0; i < Context.EntityIdList.length; i++) {\n        Context.EntityIds += Context.EntityIdList[i] + '','';\n    }\n    Context.EntityIds = Context.EntityIds.substr(0, Context.EntityIds.length - 1) + '')'';\n} else {\n    Context.EntityIds = ''()'';\n}"},{"id":111831,"key":"Context","value":""}],"output":[{"id":87397,"key":"Context","value":""}]}}]}',
  @WORKFLOW_ID,
  0,
  NULL,
  NULL,
  NULL,
  NULL,
  GETDATE(),
  1,
  NULL,
  NULL,
  1)
) as t ([ID],[TEMPLATEVERSION],[STATUS],[COMMENT],[HELPTEXT],[CONTEXT],[WORKFLOWACTION],[WFE_TEMPLATE_FK],[LIFETIME],[VALIDFROM],[VALIDTO],[REVISIONDATE],[BAS_CLERK_FK],[INSERTED],[WHOISR],[UPDATED],[WHOUPD],[VERSION])

print 'done importing for table: WFE_TEMPLATEVERSION '
END