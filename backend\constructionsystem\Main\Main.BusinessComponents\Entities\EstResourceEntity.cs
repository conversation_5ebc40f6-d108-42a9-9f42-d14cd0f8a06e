using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Common;

namespace RIB.Visual.ConstructionSystem.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class EstResourceEntity : IScriptEstResource
	{
		/// <summary>
		/// 
		/// </summary>
		public decimal? RiskCostUnit
		{
			get;
			set;
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal? RiskCostTotal
		{
			get;
			set;
		}
		/// <summary>
		/// Gets or Sets EstHeaderFk.
		/// </summary>
		public int EstHeaderFk { get; set; }

		/// <summary>
		/// Gets or Sets EstLineItemFk.
		/// </summary>
		public int EstLineItemFk { get; set; }

		/// <summary>
		/// Gets or Sets EstResourceFk.
		/// </summary>
		public int? EstResourceFk { get; set; }

		/// <summary>
		/// Gets or Sets EstResourceTypeFk.
		/// </summary>
		public int EstResourceTypeFk { get; set; }

		/// <summary>
		/// Gets or Sets MdcCostCodeFk.
		/// </summary>
		public int? MdcCostCodeFk { get; set; }

		/// <summary>
		/// Gets or Sets MdcMaterialFk.
		/// </summary>
		public int? MdcMaterialFk { get; set; }

		/// <summary>
		/// Gets or Sets EstHeaderAssemblyFk.
		/// </summary>
		public int? EstHeaderAssemblyFk { get; set; }

		/// <summary>
		/// Gets or Sets EstAssemblyFk.
		/// </summary>
		public int? EstAssemblyFk { get; set; }

		/// <summary>
		/// Gets or Sets EstAssemblyTypeFk.
		/// </summary>
		public int? EstAssemblyTypeFk { get; set; }

		/// <summary>
		/// Gets or Sets Code.
		/// </summary>
		public string Code { get; set; }
		
		/// <summary>
		/// Gets or Sets DescriptionInfo.
		/// </summary>
		public DescriptionTranslateType DescriptionInfo { get; set; }

		/// <summary>
		/// Gets or Sets QuantityDetail.
		/// </summary>
		public string QuantityDetail { get; set; }

		/// <summary>
		/// Gets or Sets Quantity.
		/// </summary>
		public decimal Quantity { get; set; }

		/// <summary>
		/// Gets or Sets BasUomFk.
		/// </summary>
		public int BasUomFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? PrcPackage2HeaderFk { get; set; }

		/// <summary>
		/// Gets or Sets QuantityFactorDetail1.
		/// </summary>
		public string QuantityFactorDetail1 { get; set; }

		/// <summary>
		/// Gets or Sets QuantityFactor1.
		/// </summary>
		public decimal QuantityFactor1 { get; set; }

		/// <summary>
		/// Gets or Sets QuantityFactorDetail2.
		/// </summary>
		public string QuantityFactorDetail2 { get; set; }

		/// <summary>
		/// Gets or Sets QuantityFactor2.
		/// </summary>
		public decimal QuantityFactor2 { get; set; }

		/// <summary>
		/// Gets or Sets QuantityFactor3.
		/// </summary>
		public decimal QuantityFactor3 { get; set; }

		/// <summary>
		/// Gets or Sets QuantityFactor4.
		/// </summary>
		public decimal QuantityFactor4 { get; set; }

		/// <summary>
		/// Gets or Sets ProductivityFactorDetail.
		/// </summary>
		public string ProductivityFactorDetail { get; set; }

		/// <summary>
		/// Gets or Sets ProductivityFactor.
		/// </summary>
		public decimal ProductivityFactor { get; set; }

		/// <summary>
		/// Gets or Sets EfficiencyFactorDetail1.
		/// </summary>
		public string EfficiencyFactorDetail1 { get; set; }

		/// <summary>
		/// Gets or Sets EfficiencyFactor1.
		/// </summary>
		public decimal EfficiencyFactor1 { get; set; }

		/// <summary>
		/// Gets or Sets EfficiencyFactorDetail2.
		/// </summary>
		public string EfficiencyFactorDetail2 { get; set; }

		/// <summary>
		/// Gets or Sets EfficiencyFactor2.
		/// </summary>
		public decimal EfficiencyFactor2 { get; set; }

		/// <summary>
		/// Gets or Sets QuantityFactorCc.
		/// </summary>
		public decimal QuantityFactorCc { get; set; }

		/// <summary>
		/// Gets or Sets QuantityReal.
		/// </summary>
		public decimal QuantityReal { get; set; }

		/// <summary>
		/// Gets or Sets QuantityInternal.
		/// </summary>
		public decimal QuantityInternal { get; set; }

		/// <summary>
		/// Gets or Sets QuantityUnitTarget.
		/// </summary>
		public decimal QuantityUnitTarget { get; set; }

		/// <summary>
		/// Gets or Sets QuantityTotal.
		/// </summary>
		public decimal QuantityTotal { get; set; }

		/// <summary>
		/// Gets or Sets CostUnit.
		/// </summary>
		public decimal CostUnit { get; set; }

		/// <summary>
		/// Gets or Sets BasCurrencyFk.
		/// </summary>
		public int? BasCurrencyFk { get; set; }

		/// <summary>
		/// Gets or Sets CostFactorDetail1.
		/// </summary>
		public string CostFactorDetail1 { get; set; }

		/// <summary>
		/// Gets or Sets CostFactor1.
		/// </summary>
		public decimal CostFactor1 { get; set; }

		/// <summary>
		/// Gets or Sets CostFactorDetail2.
		/// </summary>
		public string CostFactorDetail2 { get; set; }

		/// <summary>
		/// Gets or Sets CostFactor2.
		/// </summary>
		public decimal CostFactor2 { get; set; }

		/// <summary>
		/// Gets or Sets CostFactorCc.
		/// </summary>
		public decimal CostFactorCc { get; set; }

		/// <summary>
		/// Gets or Sets CostUnitSubItem.
		/// </summary>
		public decimal CostUnitSubItem { get; set; }

		/// <summary>
		/// Gets or Sets CostUnitLineItem.
		/// </summary>
		public decimal CostUnitLineItem { get; set; }

		/// <summary>
		/// Gets or Sets CostUnitTarget.
		/// </summary>
		public decimal CostUnitTarget { get; set; }

		/// <summary>
		/// Gets or Sets CostTotal.
		/// </summary>
		public decimal CostTotal { get; set; }

		/// <summary>
		/// Gets or Sets HoursUnit.
		/// </summary>
		public decimal HoursUnit { get; set; }

		/// <summary>
		/// Gets or Sets HoursUnitSubItem.
		/// </summary>
		public decimal HoursUnitSubItem { get; set; }

		/// <summary>
		/// Gets or Sets HoursUnitLineItem.
		/// </summary>
		public decimal HoursUnitLineItem { get; set; }

		/// <summary>
		/// Gets or Sets HoursUnitTarget.
		/// </summary>
		public decimal HoursUnitTarget { get; set; }

		/// <summary>
		/// Gets or Sets HoursTotal.
		/// </summary>
		public decimal HoursTotal { get; set; }

		/// <summary>
		/// Gets or Sets IsLumpsum.
		/// </summary>
		public bool IsLumpsum { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsGeneratedPrc { get; set; }

		/// <summary>
		/// Gets or Sets IsDisabled.
		/// </summary>
		public bool IsDisabled { get; set; }

		/// <summary>
		/// Gets or Sets IsDisabled.
		/// </summary>
		public bool IsDisabledPrc { get; set; }

		/// <summary>
		/// Gets or Sets CommentText.
		/// </summary>
		public string CommentText { get; set; }

		/// <summary>
		/// Gets or Sets PrcPackageFk.
		/// </summary>
		public int? PrcPackageFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public ICollection<IScriptEstResource> Resources { get; set; }

		/// <summary>
		/// interface for calculator logic.
		/// </summary>
		public IScriptEstResource Parent { get; set; }

		/// <summary>
		/// interface for calculator logic.
		/// </summary>
		public decimal CostReal { get; set; }

		/// <summary>
		/// interface for calculator logic.
		/// </summary>
		public decimal CostInternal { get; set; }

		/// <summary>
		/// interface for calculator logic.
		/// </summary>
		public decimal? ExchangeRate { get; set; }

		/// <summary>
		/// Interface for multi currency logic
		/// </summary>
		public decimal? CostExchangeRate1 { get; set; }

		/// <summary>
		/// Interface for multi currency logic
		/// </summary>
		public decimal? CostExchangeRate2 { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int ColumnId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? RootCostCodeFk { get; set; }

		/// <summary>
		/// IsIndirectCost
		/// </summary>
		public bool IsIndirectCost { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? EstResourceRuleFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? LgmJobFk { get; set; }

		/// <summary>
		/// Sorting
		/// </summary>
		public int Sorting
		{
			get;
			set;
		}

		/// <summary>
		/// InsertedAt
		/// </summary>
		public DateTime InsertedAt
		{
			get;
			set;
		}

		  /// <summary>
		  /// 
		  /// </summary>
		  public System.DateTime OriginalCreateTime { get; set; }


		/// <summary>
		/// InsertedBy
		/// </summary>
		public int InsertedBy
		{
			get;
			set;
		}


		/// <summary>
		/// UpdatedAt
		/// </summary>
		public DateTime? UpdatedAt
		{
			get;
			set;
		}


		/// <summary>
		/// UpdatedBy
		/// </summary>
		public int? UpdatedBy
		{
			get;
			set;
		}


		/// <summary>
		/// Version
		/// </summary>
		public int Version
		{
			get;
			set;
		}

		/// <summary>
		/// EstResourceFlagFk
		/// </summary>
		public int? EstResourceFlagFk
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public int? EstCostTypeFk
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal HourFactor
		{
			get;
			set;
		}


		/// <summary>
		/// 
		/// </summary>
		public decimal QuantityOriginal
		{
			get;
			set;
		}

		/// <summary>
		/// There are no comments for CostUnitOriginal in the schema.
		/// </summary>
		public decimal CostUnitOriginal
		{
			get;
			set;
		}

		/// <summary>
		/// Budget per Unit
		/// </summary>
		public decimal BudgetUnit
		{
			get;
			set;
		}

		/// <summary>
		/// Budget per Unit
		/// </summary>
		public decimal Budget
		{
			get;
			set;
		}

		/// <summary>
		/// Budget per Unit
		/// </summary>
		public decimal? BudgetDifference
		{
			get;
			set;
		}

		/// <summary>
		///Fixed Budget flag
		/// </summary>
		public bool IsFixedBudget
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal AdvancedAllowanceCostUnit
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal AdvancedAllowanceCostUnitSubItem
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal AdvancedAllowanceCostUnitLineItem
		{
			get;
			set;
		}
		/// <summary>
		/// 
		/// </summary>
		public int? EstRuleSourceFk
		{
			get;
			set;
		}
		/// <summary>
		/// 
		/// </summary>
		public int Id
		{
			get;
			set;
		}

		/// <summary>
		///cost flag
		/// </summary>
		public bool IsCost
		{
			get;
			set;
		}

		/// <summary>
		///Budget flag
		/// </summary>
		public bool IsBudget
		{
			get;
			set;
		}

		/// <summary>
		///IsEstimateCostcode
		/// </summary>
		public bool IsEstimateCostCode
		{ get; set; }

		/// <summary>
		///IsRuleMarkupCostcode
		/// </summary>
		public bool IsRuleMarkupCostCode
		{ get; set; }

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public object Clone()
		{
			throw new NotImplementedException();
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="sourceEntity"></param>
		public void RecalculateQtyNFactor(IScriptEstResource sourceEntity)
		{
			throw new NotImplementedException();
		}

		/// <summary>
		/// 
		/// </summary>
		public DescriptionTranslateType DescriptionInfo1
		{
			get;
			set;
		}

		  /// <summary>
		  /// 
		  /// </summary>
		  public decimal CostTotalCurrency
		  {
				get;
				set;
		  }

		  /// <summary>
		  /// 
		  /// </summary>
		  public bool IsRate { get; set; }

		  /// <summary>
		  /// 
		  /// </summary>
		  public int? ConHeaderFk { get; set; }

		  /// <summary>
		  /// 
		  /// </summary>
		  public int? QtnHeaderFk { get; set; }

		  /// <summary>
		  /// 
		  /// </summary>
		  public int? MaterialPriceListFk { get; set; }

		  /// <summary>
		  /// 
		  /// </summary>
		  public int? ProjectCostCodeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal MarkupCostUnit
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal MarkupCostUnitLineItem
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal DayWorkRateUnit { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal DayWorkRateTotal { get; set; }
		
		/// <summary>
		/// 
		/// </summary>
		public decimal DayWorkRateUnitLineItem { get; set; }
		
		/// <summary>
		/// 
		/// </summary>
		public decimal AnscetorCostFactor { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IUserDefinedcolValEntity UserDefinedcolValEntity { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsInformation { get; set; }

		/// <summary>
		/// Gets or Sets IsDisabled.
		/// </summary>
		public bool IsDisabledDirect { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal? EscResourceCostUnit { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal? EscResourceCostTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal BaseCostUnit { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal BaseCostTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal CostTotalOc { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal Gc  { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal Ga  { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal Am  { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal Rp  { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal Gar  { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public decimal Fm
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal GcUnitLineItem
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal GaUnitLineItem
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal AmUnitLineItem
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal RpUnitLineItem
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal GarUnitLineItem
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public decimal FmUnitLineItem
		{
			get; set;
		}

		/// <summary>
		/// 
		/// </summary>
		public String PackageAssignments { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[NotMapped]
		public Boolean IsReadOnlyByPrcPackage { get; set; }

		/// <summary>
		/// There are no comments for IsFixedBudgetUnit in the schema.
		/// </summary>
		public bool IsFixedBudgetUnit
		{
			get;
			set;
		}

		/// <summary>
		/// 
		/// </summary>
		[NotMapped]
		public ICollection<int> PackageIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal? Co2Source { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal? Co2SourceTotal { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal? Co2Project { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Decimal? Co2ProjectTotal { get; set; }

		/// <summary>
		/// Gets or Sets CostUom.
		/// </summary>
		public decimal CostUom { get; set; }

		/// <summary>
		/// COS script, setCharacteristic
		/// </summary>
		[NotMapped]
		public ICollection<ICharacteristicDataEntity> Characteristic1List { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Boolean IsManual { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public Int32? WorkOperationTypeFk { get; set; }

		/// <summary>
		/// PlantGroupSpecValueFk
		/// </summary>
		public Int32? PlantGroupSpecValueFk { get; set; }

		/// <summary>
		/// ETM Plant Foreign Key
		/// </summary>
		public int? EtmPlantFk
		{
			get; set;
		}
	}
}
