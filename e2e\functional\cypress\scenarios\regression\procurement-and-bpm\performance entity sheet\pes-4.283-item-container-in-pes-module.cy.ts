import { app, btn, cnt, sidebar, commonLocators, tile } from "cypress/locators";
import Buttons from "cypress/locators/buttons";
import Sidebar from "cypress/locators/sidebar";
import { _common, _validate,_projectPage,_controllingUnit, _mainView, _procurementContractPage, _sidebar, _package } from "cypress/pages";
import type { DataCells } from 'cypress/pages/interfaces.d.ts'


const PES_DESCRIPTION = _common.generateRandomString(5)
const PRJ_NO =  _common.generateRandomString(4);
const PRJ_NAME = "PRJ_NAME"+  _common.generateRandomString(4);
const CONTROLLING_UNIT_DESCRIPTION = 'CU-DESC-' + _common.generateRandomString(3)
const FURTHER_DESCRIPTION = 'FT-DESC-' + _common.generateRandomString(3)

let PROJECT_PARAMETER,CONTRACT_PARAMETER,CONTROLLING_UNIT_PARAMETERS,ITEM_PARAMETER,ITEM_PARAMETER_1: DataCells;
let CONTAINERS_CONTRACT,CONTAINERS_MATERIAL_CATALOGS,CONTAINER_COLUMNS_MATERIAL_RECORDS
let CONTAINERS_ITEM;

let CONTAINER_COLUMNS_ITEM,CONTAINER_COLUMNS_PES;
let CONTAINER_COLUMNS_PROCUREMENTCONTRACT


describe('PCM- 4.283 | Items container in pes module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  before(function () {
    cy.fixture('pcm/pes-4.283-Items-container-in-pes-module.json').then((data) => {
      this.data = data;

      CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
      CONTAINERS_ITEM = this.data.CONTAINERS.ITEM;
      CONTAINER_COLUMNS_PES = this.data.CONTAINER_COLUMNS.PES
      CONTAINER_COLUMNS_ITEM = this.data.CONTAINER_COLUMNS.ITEM
      CONTAINER_COLUMNS_PROCUREMENTCONTRACT = this.data.CONTAINER_COLUMNS.PROCUREMENTCONTRACT
      CONTAINERS_MATERIAL_CATALOGS= this.data.CONTAINERS.MATERIAL_CATALOGS
      CONTAINER_COLUMNS_MATERIAL_RECORDS=this.data.CONTAINER_COLUMNS.MATERIAL_RECORDS
      PROJECT_PARAMETER = {
        [commonLocators.CommonLabels.PROJECT_NUMBER]:PRJ_NO,
        [commonLocators.CommonLabels.NAME]:PRJ_NAME,
        [commonLocators.CommonLabels.CLERK]:CONTAINERS_CONTRACT.CLERK_NAME
      }
      CONTROLLING_UNIT_PARAMETERS={
        [app.GridCells.DESCRIPTION_INFO]:CONTROLLING_UNIT_DESCRIPTION,
        [app.GridCells.QUANTITY_SMALL]:CONTAINERS_ITEM.QUANTITY[0],
        [app.GridCells.UOM_FK]:CONTAINERS_ITEM.UOM
      }
      CONTRACT_PARAMETER = {
        [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
        [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESSPARTNER

      };

      ITEM_PARAMETER = {
        [app.GridCells.MDC_MATERIAL_FK]: CONTAINERS_ITEM.MATERIAL_NO,
        [app.GridCells.QUANTITY_SMALL]: CONTAINERS_ITEM.QUANTITY[0],
       
      };
      ITEM_PARAMETER_1 = {
        [app.GridCells.MDC_MATERIAL_FK]: CONTAINERS_ITEM.MATERIAL_NO1,
        [app.GridCells.QUANTITY_SMALL]: CONTAINERS_ITEM.QUANTITY[0],
        
      };
    });
    cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
    _common.openDesktopTile(tile.DesktopTiles.PROJECT);
  
   
  });

  after(() => {
    cy.LOGOUT();
  });

  it("TC- Create new Project and Pinned it",function () {   
    _common.openTab(app.TabBar.PROJECT).then(()=>{
      _common.select_tabFromFooter(cnt.uuid.PROJECTS,app.FooterTab.PROJECTS)
      _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
    })
    _common.create_newRecord(cnt.uuid.PROJECTS)
    _projectPage.enterRecord_toCreateProject(PROJECT_PARAMETER)
    cy.SAVE()
    _common.pinnedItem()
  })
  
  it("TC - Create controlling unit", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTROLLING_UNITS); 
		_common.waitForLoaderToDisappear()

		_common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT_PROJECTS, app.FooterTab.PROJECTS, 0);
		});
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PRJ_NO).pinnedItem();  

		_common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
			_common.setDefaultView(app.TabBar.CONTROLLINGSTRUCTURE)
			_common.waitForLoaderToDisappear()
			_common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT, app.FooterTab.CONTROLLING_UNITS, 2);
			
		});
		_common.waitForLoaderToDisappear()
		_common.maximizeContainer(cnt.uuid.CONTROLLING_UNIT)
		_controllingUnit.enterRecord_toCreateControllingUnit(CONTROLLING_UNIT_PARAMETERS);
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.CONTROLLING_UNIT,CONTROLLING_UNIT_DESCRIPTION)
		_common.saveCellDataToEnv(cnt.uuid.CONTROLLING_UNIT,app.GridCells.CODE,"CONTROLLING_UNIT_CODE")
		_common.minimizeContainer(cnt.uuid.CONTROLLING_UNIT)
		_common.waitForLoaderToDisappear()
	
	});

  it("TC - Update material record for further description", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.MATERIAL); 
		_common.waitForLoaderToDisappear()

    _common.openTab(app.TabBar.RECORDS).then(() => {
      _common.select_tabFromFooter(cnt.uuid.MATERIAL_CATALOG_FILTER, app.FooterTab.MATERIALFILTER, 1);
  });
  _common.waitForLoaderToDisappear()
  _common.clear_subContainerFilter(cnt.uuid.MATERIAL_CATALOG_FILTER)
  _common.maximizeContainer(cnt.uuid.MATERIAL_CATALOG_FILTER)
  _common.set_cellCheckboxValue_forColumn(cnt.uuid.MATERIAL_CATALOG_FILTER, app.GridCells.IS_CHECKED, commonLocators.CommonKeys.CHECK)
  _common.minimizeContainer(cnt.uuid.MATERIAL_CATALOG_FILTER)
  _common.openTab(app.TabBar.RECORDS).then(() => {
      _common.select_tabFromFooter(cnt.uuid.MATERIAL_RECORDS, app.FooterTab.MATERIAL_RECORDS, 1);
      _common.setup_gridLayout(cnt.uuid.MATERIAL_RECORDS, CONTAINER_COLUMNS_MATERIAL_RECORDS)
  });
  _common.clear_subContainerFilter(cnt.uuid.MATERIAL_RECORDS)
  _common.maximizeContainer(cnt.uuid.MATERIAL_RECORDS)
  _common.search_inSubContainer(cnt.uuid.MATERIAL_RECORDS,CONTAINERS_ITEM.MATERIAL_NO)
  _common.clickOn_cellHasValue(cnt.uuid.MATERIAL_RECORDS,app.GridCells.CODE,CONTAINERS_ITEM.MATERIAL_NO)
  _common.edit_containerCell(cnt.uuid.MATERIAL_RECORDS,app.GridCells.DESCRIPTION_INFO_2,app.InputFields.DOMAIN_TYPE_TRANSLATION,FURTHER_DESCRIPTION)
  _common.waitForLoaderToDisappear()
  cy.SAVE()
  })

  it("TC - Create new contract record ", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
    _common.waitForLoaderToDisappear()


    _common.openTab(app.TabBar.CONTRACT).then(()=>{
    _common.setDefaultView(app.TabBar.CONTRACT)
    _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT,app.FooterTab.CONTRACTS,0)
    })
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PRJ_NO)
    _common.waitForLoaderToDisappear()
    _common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT)
    _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
    _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT,0)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.SAVE();
    _common.waitForLoaderToDisappear()
    _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
    _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
    _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.CLERK_NAME)
    _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTROLLING_UNIT_DESCRIPTION)
    _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.STRUCTURE_CODE)
    _common.clickOn_activeContainerButton(cnt.uuid.PROCUREMENTCONTRACT,btn.IconButtons.ICO_INPUT_DELETE)
    _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK)
    cy.SAVE();
    _common.waitForLoaderToDisappear()
    _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "Contract_Code")
    _common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT)

    _common.openTab(app.TabBar.CONTRACT).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.PACKAGEITEMS, 2)
  });
  _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
  _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
  _procurementContractPage.enterRecord_toCreateContractItems(cnt.uuid.ITEMSCONTRACT, ITEM_PARAMETER)
  _common.waitForLoaderToDisappear()
  cy.SAVE()
  _common.waitForLoaderToDisappear()
  


  _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
  _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
  _common.waitForLoaderToDisappear()
  _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED);
  _common.waitForLoaderToDisappear()

})

  it("TC - Create pes record", function () {
    _common.openTab(app.TabBar.CONTRACT).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
    });
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES)
    _common.waitForLoaderToDisappear()
    _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_PES)
    _common.waitForLoaderToDisappear()

    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PRJ_NO);  
    _common.waitForLoaderToDisappear()

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
        _common.setDefaultView(app.TabBar.PERFORMANCEENTRYSHEET)
        _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_6)
        _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
        _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_PES)
       
    });
    _common.maximizeContainer(cnt.uuid.HEADERS)
    _common.clear_subContainerFilter(cnt.uuid.HEADERS);
    _common.waitForLoaderToDisappear()
    _common.select_activeRowInContainer(cnt.uuid.HEADERS)
    _common.edit_containerCell(cnt.uuid.HEADERS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, PES_DESCRIPTION)
    _common.waitForLoaderToDisappear()
    _common.clickOn_activeRowCell(cnt.uuid.HEADERS, app.GridCells.PRC_STRUCTURE_FK)
    _common.clickOn_activeContainerButton(cnt.uuid.HEADERS,btn.IconButtons.ICO_INPUT_DELETE)
    _common.clickOn_activeRowCell(cnt.uuid.HEADERS, app.GridCells.DESCRIPTION)
    cy.SAVE();
    _common.waitForLoaderToDisappear()
    _common.clickOn_activeRowCell(cnt.uuid.HEADERS, app.GridCells.PRC_STRUCTURE_FK)
    _common.clickOn_activeContainerButton(cnt.uuid.HEADERS,btn.IconButtons.ICO_INPUT_DELETE)
    _common.clickOn_activeRowCell(cnt.uuid.HEADERS, app.GridCells.DESCRIPTION)
    cy.SAVE();
    _common.waitForLoaderToDisappear()

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
        _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
    });
    _common.waitForLoaderToDisappear()
    _common.select_rowInContainer(cnt.uuid.HEADERS)
    _common.waitForLoaderToDisappear()
    _common.getText_fromCell(cnt.uuid.HEADERS, app.GridCells.CODE).then(($ele1: JQuery<HTMLElement>) => {
        Cypress.env("PES_CODE", $ele1.text())
        cy.log(Cypress.env("PES_CODE"))
    })
    _common.minimizeContainer(cnt.uuid.HEADERS)
});

  it("TC - Create new items for pes", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('PES_CODE')).pinnedItem();

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 1);
      _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
  });
  _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.mdcmaterialfk], cnt.uuid.ITEMS)
  _common.maximizeContainer(cnt.uuid.ITEMS)
  _common.waitForLoaderToDisappear()
    _common.create_newRecord(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()
    _procurementContractPage.enterRecord_toCreateContractItems(cnt.uuid.ITEMS, ITEM_PARAMETER_1)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.maximizeContainer(cnt.uuid.ITEMS)
  })

  it('TC - Verify delete button of item is working', function () {

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 1);
  });
   
    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.clickOn_cellHasValue(cnt.uuid.ITEMS,app.GridCells.DESCRIPTION_1, CONTAINERS_ITEM.MATERIAL_DESC)
    _common.waitForLoaderToDisappear()
    _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMS, app.GridCells.MDC_MATERIAL_FK, CONTAINERS_ITEM.MATERIAL_NO)
    _common.waitForLoaderToDisappear()


    _common.delete_recordFromContainer(cnt.uuid.ITEMS)
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
   
    _common.waitForLoaderToDisappear()
    _validate.verify_isRecordDeleted(cnt.uuid.ITEMS, CONTAINERS_ITEM.MATERIAL_NO)
    _common.minimizeContainer(cnt.uuid.ITEMS)
  })

  it('TC - Verify set value to each field and check lookup filter is working', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PRJ_NO)

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
        _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_PES)
    });
    _common.maximizeContainer(cnt.uuid.HEADERS)
    _common.select_rowInSubContainer(cnt.uuid.HEADERS)
    _common.saveCellDataToEnv(cnt.uuid.HEADERS,app.GridCells.EXCHANGE_RATE,"HEADER_RATE")
    _common.minimizeContainer(cnt.uuid.HEADERS)

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
      _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
    });
    _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.controllingunitfk,CONTAINER_COLUMNS_ITEM.quantity,CONTAINER_COLUMNS_ITEM.mdcmaterialfk,
      CONTAINER_COLUMNS_ITEM.price,CONTAINER_COLUMNS_ITEM.vat,CONTAINER_COLUMNS_ITEM.priceextra,CONTAINER_COLUMNS_ITEM.totalpricegross,
      CONTAINER_COLUMNS_ITEM.priceoc,CONTAINER_COLUMNS_ITEM.totalprice,CONTAINER_COLUMNS_ITEM.vatOC,CONTAINER_COLUMNS_ITEM.totaloc,CONTAINER_COLUMNS_ITEM.totalpriceoc,
      CONTAINER_COLUMNS_ITEM.totalgross,CONTAINER_COLUMNS_ITEM.totalgrossoc,CONTAINER_COLUMNS_ITEM.total,CONTAINER_COLUMNS_ITEM.mdctaxcodefk], cnt.uuid.ITEMS)
  
    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()
    _common.select_rowInSubContainer(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()
    _common.edit_dropdownCellWithInput(cnt.uuid.ITEMS, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTROLLING_UNIT_DESCRIPTION)
    _common.waitForLoaderToDisappear()
    _common.edit_containerCell(cnt.uuid.ITEMS, app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEM.QUANTITY[0])
    _common.waitForLoaderToDisappear()
    _common.edit_dropdownCellWithInput(cnt.uuid.ITEMS, app.GridCells.MDC_TAX_CODE_FK_SMALL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ITEM.QUANTITY[2])
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    

    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PRJ_NO)
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
    });
    _common.select_rowInContainer(cnt.uuid.ITEMS)
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_SMALL,"ITEM_PRICE")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.VAT,"ITEM_VAT")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.VAT_OC,"ITEM_VAT_OC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_EXTRA,"ITEM_PRICEEXTRA")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_OC,"ITEM_PRICEOC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_PRICE_OC,"ITEM_TOTAL_PRICEOC") 
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_PRICE,"ITEM_TOTALPRICE")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_PRICE_GROSS,"ITEM_TOTALPRICEGROSS")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_OC,"ITEM_TOTAL_OC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_GROSS_SMALL,"ITEM_TOTAL_GROSS")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_GROSS_OC,"ITEM_TOTAL_GROSS_OC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL,"ITEM_TOTAL")  
   
    _common.waitForLoaderToDisappear()
    _common.minimizeContainer(cnt.uuid.ITEMS)
  })

  it('TC - Verify check calculation is working and also for header container', function () {

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
      _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
    });
    _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.priceoc,CONTAINER_COLUMNS_ITEM.pricegross,CONTAINER_COLUMNS_ITEM.totalprice,
      CONTAINER_COLUMNS_ITEM.pricegrossoc,CONTAINER_COLUMNS_ITEM.totalpricegross,CONTAINER_COLUMNS_ITEM.totalgross,
      CONTAINER_COLUMNS_ITEM.totalpriceoc,CONTAINER_COLUMNS_ITEM.totaloc,CONTAINER_COLUMNS_ITEM.total,
    CONTAINER_COLUMNS_ITEM.totalpricegrossoc,CONTAINER_COLUMNS_ITEM.totalgrossoc], cnt.uuid.ITEMS)
    
    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()
    _common.select_rowInContainer(cnt.uuid.ITEMS)
    _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.ITEMS,Cypress.env("ITEM_PRICE"), Cypress.env("HEADER_RATE"), app.GridCells.PRICE_OC)
    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_PRICE"),  CONTAINERS_ITEM.VAT_PECENT, app.GridCells.PRICE_GROSS)
    _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.ITEMS,Cypress.env("ITEM_PRICE"), Cypress.env("ITEM_PRICEEXTRA"), app.GridCells.TOTAL_PRICE)
    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_PRICEOC"), CONTAINERS_ITEM.VAT_PECENT, app.GridCells.PRICE_GROSS_OC)
    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_TOTALPRICE"), CONTAINERS_ITEM.VAT_PECENT, app.GridCells.TOTAL_PRICE_GROSS)
    _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.ITEMS,Cypress.env("ITEM_TOTAL_OC"), Cypress.env("ITEM_PRICEEXTRA"), app.GridCells.TOTAL_PRICE_OC)
    _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.ITEMS,CONTAINERS_ITEM.QUANTITY[0], Cypress.env("ITEM_TOTALPRICE"), app.GridCells.TOTAL)
    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_TOTAL"), CONTAINERS_ITEM.VAT_PECENT, app.GridCells.TOTAL_GROSS_SMALL)
    _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.ITEMS,CONTAINERS_ITEM.QUANTITY[0], Cypress.env("ITEM_TOTAL_PRICEOC"), app.GridCells.TOTAL_OC)
    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_TOTAL_OC"),CONTAINERS_ITEM.VAT_PECENT, app.GridCells.TOTAL_GROSS_OC)
    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_TOTAL_PRICEOC"), CONTAINERS_ITEM.VAT_PECENT, app.GridCells.TOTAL_PRICE_GROSS_OC)

    _common.waitForLoaderToDisappear()
    _common.minimizeContainer(cnt.uuid.ITEMS)

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
      _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_PES)
    });
    _common.set_columnAtTop([CONTAINER_COLUMNS_PES.pesvalue,CONTAINER_COLUMNS_PES.pesvalueoc,CONTAINER_COLUMNS_PES.pesvat,
      CONTAINER_COLUMNS_PES.pesvatoc,CONTAINER_COLUMNS_PES.TotalGross,CONTAINER_COLUMNS_PES.TotalGrossOc], cnt.uuid.HEADERS)
    
      
    _common.maximizeContainer(cnt.uuid.HEADERS)
    _common.waitForLoaderToDisappear()
    _common.select_rowInContainer(cnt.uuid.HEADERS)
    _common.assert_forNumericValues(cnt.uuid.HEADERS,app.GridCells.PES_VALUE,Cypress.env("ITEM_TOTAL"))
    _common.assert_forNumericValues(cnt.uuid.HEADERS,app.GridCells.PES_VALUE_OC,Cypress.env("ITEM_TOTAL_OC"))
    _common.assert_forNumericValues(cnt.uuid.HEADERS,app.GridCells.PES_VAT,Cypress.env("ITEM_VAT"))
    _common.assert_forNumericValues(cnt.uuid.HEADERS,app.GridCells.PES_VAT_OC,Cypress.env("ITEM_VAT_OC"))
    _common.assert_forNumericValues(cnt.uuid.HEADERS,app.GridCells.TOTAL_GROSS,Cypress.env("ITEM_TOTAL_GROSS"))
    _common.assert_forNumericValues(cnt.uuid.HEADERS,app.GridCells.TOTAL_GROSS_OC_CAPITAL,Cypress.env("ITEM_TOTAL_GROSS_OC"))
    
    _common.minimizeContainer(cnt.uuid.HEADERS)
  })

  it('TC - Verify set value to quantity', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PRJ_NO)

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
      _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
    });
    _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.price,CONTAINER_COLUMNS_ITEM.quantity,CONTAINER_COLUMNS_ITEM.priceoc,CONTAINER_COLUMNS_ITEM.totalpriceoc,
      CONTAINER_COLUMNS_ITEM.totalprice,CONTAINER_COLUMNS_ITEM.pricegrossoc,CONTAINER_COLUMNS_ITEM.total,CONTAINER_COLUMNS_ITEM.totalgrossoc,CONTAINER_COLUMNS_ITEM.priceextraoc,
      CONTAINER_COLUMNS_ITEM.totalgross,CONTAINER_COLUMNS_ITEM.priceextra,CONTAINER_COLUMNS_ITEM.totalpricegross,CONTAINER_COLUMNS_ITEM.totaloc], cnt.uuid.ITEMS)
    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()
    _common.select_rowInSubContainer(cnt.uuid.ITEMS)
    _common.edit_containerCell(cnt.uuid.ITEMS,app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ITEM.QUANTITY[1])
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);

    });
  
  
    _common.select_rowInContainer(cnt.uuid.ITEMS)
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_SMALL,"ITEM_UPDATEDPRICE")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_GROSS_OC,"ITEM_UPDATEDPRICEGROSSOC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_OC,"ITEM_UPDATEDPRICEOC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_PRICE,"ITEM_UPDATEDTOTALPRICE")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL,"ITEM_UPDATEDTOTAL")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_GROSS_SMALL,"ITEM_UPDATEDTOTALGROSS")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_EXTRA,"ITEM_UPDATED_PRICEEXTRA")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_GROSS_OC,"ITEM_UPDATED_TOTAL_GROSS_OC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_PRICE_GROSS,"ITEM_UPDATED_TOTALPRICEGROSS")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_OC,"ITEM_UPDATED_TOTAL_OC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_PRICE_OC,"ITEM_UPDATED_TOTAL_PRICEOC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_EXTRA_OC,"ITEM_UPDATED_PRICE_EXTRA_OC")
    _common.minimizeContainer(cnt.uuid.ITEMS)
  })

  it('TC - Verify total values after quantity updated it should allow to recalculate', function () {
    
      _common.waitForLoaderToDisappear()
  
      _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
        _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
        _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
      });
    _common.waitForLoaderToDisappear()
  
    _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.total,CONTAINER_COLUMNS_ITEM.totaloc,CONTAINER_COLUMNS_ITEM.totalpriceoc,
      CONTAINER_COLUMNS_ITEM.totalgross,CONTAINER_COLUMNS_ITEM.totalgrossoc,CONTAINER_COLUMNS_ITEM.totalpricegross,
      CONTAINER_COLUMNS_ITEM.totalpricegrossoc,CONTAINER_COLUMNS_ITEM.totalprice], cnt.uuid.ITEMS)

      _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.select_rowInSubContainer(cnt.uuid.ITEMS)
    _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.ITEMS, CONTAINERS_ITEM.QUANTITY[1], Cypress.env("ITEM_UPDATEDTOTALPRICE"),app.GridCells.TOTAL)
    _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.ITEMS,CONTAINERS_ITEM.QUANTITY[1], Cypress.env("ITEM_UPDATED_TOTAL_PRICEOC"), app.GridCells.TOTAL_OC)
    _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.ITEMS,Cypress.env("ITEM_UPDATEDPRICEOC"), Cypress.env("ITEM_UPDATED_PRICE_EXTRA_OC"), app.GridCells.TOTAL_PRICE_OC)

    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_UPDATEDTOTAL"), CONTAINERS_ITEM.VAT_PECENT, app.GridCells.TOTAL_GROSS_SMALL)
    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_UPDATED_TOTAL_PRICEOC"),CONTAINERS_ITEM.VAT_PECENT,app.GridCells.TOTAL_PRICE_GROSS_OC)
    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_UPDATEDTOTALPRICE"), CONTAINERS_ITEM.VAT_PECENT, app.GridCells.TOTAL_PRICE_GROSS)
    _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_UPDATED_TOTAL_OC"), CONTAINERS_ITEM.VAT_PECENT,app.GridCells.TOTAL_GROSS_OC)
    _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.ITEMS, Cypress.env("ITEM_PRICE"), Cypress.env("ITEM_UPDATED_PRICEEXTRA"),app.GridCells.TOTAL_PRICE)
    _common.minimizeContainer(cnt.uuid.ITEMS)
    
  })
  it('TC - Verify set value to material, related fields will get value from material', function () {

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
      _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
    });
    _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.mdcmaterialfk,CONTAINER_COLUMNS_ITEM.uomfk,CONTAINER_COLUMNS_ITEM.price,
      CONTAINER_COLUMNS_ITEM.description1,CONTAINER_COLUMNS_ITEM.prcpriceconditionfk,CONTAINER_COLUMNS_ITEM.description2], cnt.uuid.ITEMS)
      
    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.create_newRecord(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()

    _common.clickOn_activeRowCell(cnt.uuid.ITEMS, app.GridCells.MDC_MATERIAL_FK)
    _common.edit_dropdownCellWithInput(cnt.uuid.ITEMS, app.GridCells.MDC_MATERIAL_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEM.MATERIAL_NO)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.clickOn_cellHasValue(cnt.uuid.ITEMS, app.GridCells.MDC_MATERIAL_FK, CONTAINERS_ITEM.MATERIAL_NO)


    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
    })
    _common.waitForLoaderToDisappear()
    _common.select_rowHasValue_byId(cnt.uuid.ITEMS, CONTAINERS_ITEM.MATERIAL_NO)
      _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMS, app.GridCells.UOM_FK, CONTAINERS_ITEM.UOM1)
      _common.assert_forNumericValues(cnt.uuid.ITEMS, app.GridCells.PRICE_SMALL, CONTAINERS_ITEM.PRICE1)
      _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMS, app.GridCells.DESCRIPTION_1, CONTAINERS_ITEM.MATERIAL_DESC)
      _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMS, app.GridCells.PRC_PRICE_CONDITION_FK, CONTAINERS_ITEM.PRICE_CONDITION)
      _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMS, app.GridCells.DESCRIPTION_2, FURTHER_DESCRIPTION)
    
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue_byId(cnt.uuid.ITEMS, CONTAINERS_ITEM.MATERIAL_NO)
      _common.delete_recordFromContainer(cnt.uuid.ITEMS)
      cy.SAVE()
      _common.waitForLoaderToDisappear()
      cy.REFRESH_CONTAINER()
      _common.waitForLoaderToDisappear()
    _common.minimizeContainer(cnt.uuid.ITEMS)

  })

  it('TC - Verify check extra when price condition has value', function () {

    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
    })
    _common.waitForLoaderToDisappear()
    _common.select_rowInContainer(cnt.uuid.ITEMS)

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2);
    });
    _common.maximizeContainer(cnt.uuid.PES_PRICE_CONDITION)
    _common.clear_subContainerFilter(cnt.uuid.PES_PRICE_CONDITION)
    _package.search_inLookupSubContainer(cnt.uuid.PES_PRICE_CONDITION,commonLocators.CommonKeys.SURCHARGE)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.saveCellDataToEnv(cnt.uuid.PES_PRICE_CONDITION,app.GridCells.CONDITION_TOTAL,"PRICE_CONDITION_TOTAL")
    _common.minimizeContainer(cnt.uuid.PES_PRICE_CONDITION)

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
      _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
    });
    _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.priceextra,CONTAINER_COLUMNS_ITEM.totalprice], cnt.uuid.ITEMS)
    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_SMALL,"ITEM_AFTER_PC_PRICE")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_GROSS_OC,"ITEM_AFTER_PC_PRICEGROSSOC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_OC,"ITEM_AFTER_PC_PRICEOC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_PRICE,"ITEM_AFTER_PC_TOTALPRICE")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL,"ITEM_AFTER_PC_TOTAL")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_GROSS_SMALL,"ITEM_AFTER_PC_TOTALGROSS")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_EXTRA,"ITEM_AFTER_PC_PRICEEXTRA")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_GROSS_OC,"ITEM_AFTER_PC_TOTAL_GROSS_OC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_PRICE_GROSS,"ITEM_AFTER_PC_TOTALPRICEGROSS")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_OC,"ITEM_AFTER_PC_TOTAL_OC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.TOTAL_PRICE_OC,"ITEM_AFTER_PC_TOTAL_PRICEOC")
    _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.PRICE_EXTRA_OC,"ITEM_AFTER_PC_PRICE_EXTRA_OC")
    _common.minimizeContainer(cnt.uuid.ITEMS)
  })

  it('TC - Verify total values after price condition updated ', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PRJ_NO)

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
      _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
    });
    _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.priceextra,CONTAINER_COLUMNS_ITEM.totalprice], cnt.uuid.ITEMS)
    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()
    _common.select_rowInSubContainer(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
    });
   
  _common.waitForLoaderToDisappear()
  _common.select_rowInSubContainer(cnt.uuid.ITEMS)
  _common.assert_forNumericValues(cnt.uuid.ITEMS, app.GridCells.PRICE_EXTRA, Cypress.env("PRICE_CONDITION_TOTAL"))
  _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.ITEMS, Cypress.env("ITEM_PRICE"), Cypress.env("PRICE_CONDITION_TOTAL"),app.GridCells.TOTAL_PRICE)

  _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.ITEMS, CONTAINERS_ITEM.QUANTITY[1], Cypress.env("ITEM_AFTER_PC_TOTALPRICE"),app.GridCells.TOTAL)
  _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.ITEMS,CONTAINERS_ITEM.QUANTITY[1], Cypress.env("ITEM_AFTER_PC_TOTAL_PRICEOC"), app.GridCells.TOTAL_OC)
  _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.ITEMS,Cypress.env("ITEM_AFTER_PC_PRICEOC"), Cypress.env("ITEM_AFTER_PC_PRICE_EXTRA_OC"), app.GridCells.TOTAL_PRICE_OC)

  _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_AFTER_PC_TOTAL"), CONTAINERS_ITEM.VAT_PECENT, app.GridCells.TOTAL_GROSS_SMALL)
  _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_AFTER_PC_TOTAL_PRICEOC"),CONTAINERS_ITEM.VAT_PECENT,app.GridCells.TOTAL_PRICE_GROSS_OC)
  _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_AFTER_PC_TOTALPRICE"), CONTAINERS_ITEM.VAT_PECENT, app.GridCells.TOTAL_PRICE_GROSS)
  _validate.verify_priceGrossForPES(cnt.uuid.ITEMS,Cypress.env("ITEM_AFTER_PC_TOTAL_OC"), CONTAINERS_ITEM.VAT_PECENT,app.GridCells.TOTAL_GROSS_OC)
   _common.minimizeContainer(cnt.uuid.ITEMS)
   
  
})

  it('TC - Verify Copy item button is working, it will create same item as selected item', function () {

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
    });
  

    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.select_rowHasValue_byId(cnt.uuid.ITEMS, CONTAINERS_ITEM.MATERIAL_NO1)
    _common.waitForLoaderToDisappear()
    _common.clickOn_toolbarButton(cnt.uuid.ITEMS,btn.ToolBar.ICO_REC_NEW_COPY)

    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _common.select_rowHasValue_onIndexBased(cnt.uuid.ITEMS, CONTAINERS_ITEM.MATERIAL_NO1,1)
   
    _common.minimizeContainer(cnt.uuid.ITEMS)
  })

  it('TC - Verify Copy contract & non copy contract button is working, it will create same item as selected item', function () {

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
    });
  

    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.select_rowInContainer(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()
    _common.delete_recordFromContainer(cnt.uuid.ITEMS)
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()

    _common.select_rowInContainer(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()
    _common.delete_recordFromContainer(cnt.uuid.ITEMS)
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _common.clickOn_toolBarButtonWithTitle(cnt.uuid.ITEMS,btn.ToolBar.COPY_CONTRACT_AND_NON_CONTRACT_ITEM)

    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _common.clickOn_cellHasValue(cnt.uuid.ITEMS, app.GridCells.DESCRIPTION_1,CONTAINERS_ITEM.MATERIAL_DESC)
   
    _common.minimizeContainer(cnt.uuid.ITEMS)
  })
 
  it('TC - Verify Set value to quantity, delivery quantity and remaining quantity should be calculate', function () {

    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PRJ_NO)

    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
      _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_ITEM)
      _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.quantityremaining,CONTAINER_COLUMNS_ITEM.quantitydelivered], cnt.uuid.ITEMS)
    
    });
  
    _common.maximizeContainer(cnt.uuid.ITEMS)
    _common.waitForLoaderToDisappear()
    _common.select_rowInSubContainer(cnt.uuid.ITEMS)
    _common.edit_containerCell(cnt.uuid.ITEMS,app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ITEM.QUANTITY[0])
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()


    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
    });
    _common.waitForLoaderToDisappear()
    _common.select_rowInSubContainer(cnt.uuid.ITEMS)
    _common.assert_forNumericValues(cnt.uuid.ITEMS,app.GridCells.QUANTITY_DELIVERED_SMALL,CONTAINERS_ITEM.QUANTITY[0])
    _common.assert_forNumericValues_mathsAbs(cnt.uuid.ITEMS,app.GridCells.QUANTITY_REMAINING,CONTAINERS_ITEM.QUANTITY[3])
    _common.minimizeContainer(cnt.uuid.ITEMS)
  })
  
})
