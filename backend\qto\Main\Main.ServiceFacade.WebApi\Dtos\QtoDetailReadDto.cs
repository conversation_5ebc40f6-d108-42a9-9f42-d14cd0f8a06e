using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Qto.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// qto detail filter parameter dto
	/// </summary>
	public class QtoDetailReadDto
	{
		/// <summary>
		/// 
		/// </summary>
		public int QtoScope { get; set; }

		/// <summary>
		/// gets and sets MainItemId
		/// </summary>
		public int MainItemId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<int> QtoDetailIds { get; set; }

		  /// <summary>
		  /// 
		  /// </summary>
		public int? PrjProjectFk { get; set; }

		/// <summary>
		/// gets and sets Locations
		/// </summary>
		public IEnumerable<int> Locations { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<int> BillTos { get; set; }

		/// <summary>
		/// gets and sets Boqs
		/// </summary>
		public IEnumerable<int> Boqs { get; set; }

		//PageNumbers
		/// <summary>
		/// gets and sets PageNumbers
		/// </summary>
		public IEnumerable<int> PageNumberIds { get; set; }

		/// <summary>
		/// gets and sets Structures
		/// </summary>
		public IEnumerable<int> Structures { get; set; }

		/// <summary>
		/// gets and sets EstHeaderIds
		/// </summary>
		public IEnumerable<int> EstHeaderIds { get; set; }

		/// <summary>
		/// gets and sets LineItemIds
		/// </summary>
		public IEnumerable<int> LineItemIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int BasRubricCategoryFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int BoqHeaderFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsPrjBoq { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsPrcBoq { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsPesBoq { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsWipBoq { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsBillingBoq { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool IsQtoBoq { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsCrbBoq { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<int> SubQuantityBoqItemFks { get; set; }

		/// <summary/>
		public int? BoqSplitQuantityFk { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int PesHeaderFk { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int WipHeaderFk { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int BilHeaderFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<int> CostGroupFks { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsFilterByNoWipOrBilActive { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsFilterByCorrectionBillActive { get; set; }
	}
}
