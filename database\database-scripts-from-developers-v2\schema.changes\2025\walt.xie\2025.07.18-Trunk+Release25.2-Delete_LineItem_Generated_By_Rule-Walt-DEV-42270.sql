------------------------------------------------------- 
-- <PERSON><PERSON> Ticket (REQUIRED): DEV-42270
-- Script Type (REQUIRED): Required Schema Change
-- Reason (REQUIRED): use EST_DELETE_LINEITEMS_SP to delete lineItem
-- Install On (OPTIONAL): Trunk, Release25.2
-------------------------------------------------------

CREATE OR ALTER PROCEDURE EST_DELETE_LINEITEM_GENERATED_BY_RULE(
	@EstHeaderId int,
	@RuleSourceIds [UDTT_IDS] READONLY,
	@WhoIsr int
)
AS
BEGIN
	SET NOCOUNT ON;

	DECLARE @LastTime DATETIME = SYSDATETIME();
	DECLARE @Duration AS VARCHAR(255);

	--Get the LINEITEM which generated by rule
	WITH LI_CTE AS
	(
		SELECT LI.ID, LI.EST_HEADER_FK FROM EST_LINE_ITEM LI
		JOIN @RuleSourceIds RULE_SOURCE_IDS 
		ON LI.EST_RULE_SOURCE_FK = RULE_SOURCE_IDS.ID AND LI.EST_HEADER_FK = @EstHeaderId
		UNION ALL
		SELECT CHILD.ID, CHILD.EST_LINE_ITEM_FK FROM EST_LINE_ITEM CHILD
		JOIN LI_CTE PARENT ON CHILD.EST_LINE_ITEM_FK = PARENT.ID AND CHILD.EST_HEADER_FK = PARENT.EST_HEADER_FK
		WHERE CHILD.EST_HEADER_FK = @EstHeaderId
	)
	SELECT DISTINCT ID, EST_HEADER_FK INTO #LI_IDS FROM LI_CTE;

	SET @Duration = CAST(DATEDIFF(MILLISECOND, @LastTime, SYSDATETIME()) AS VARCHAR(255)) + ' ms'; 
	PRINT 'Get the LINEITEM which generated by rule - Cost Time: ' + @Duration;
	SET @LastTime = SYSDATETIME();

	--delete lineItems genereated by rule
	DECLARE @udtt_lineItemIdsToDelete udtt_HeaderLineItem;
	INSERT INTO @udtt_lineItemIdsToDelete(EST_HEADER_FK, EST_LINE_ITEM_FK)
	SELECT EST_HEADER_FK, ID FROM #LI_IDS;
	
	EXEC EST_DELETE_LINEITEMS_SP @udtt_lineItemIdsToDelete, @WhoIsr;

	SET @Duration = CAST(DATEDIFF(MILLISECOND, @LastTime, SYSDATETIME()) AS VARCHAR(255)) + ' ms'; 
	PRINT 'DELETE LINEITEM AND RESOURCE - Cost Time: ' + @Duration;
	SET @LastTime = SYSDATETIME();

	--Return LINEITEM ids
	SELECT ID as Id, EST_HEADER_FK as Pk1 FROM #LI_IDS;

END


