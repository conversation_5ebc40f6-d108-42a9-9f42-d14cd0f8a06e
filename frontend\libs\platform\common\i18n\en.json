{"platform": {"uiLanguage": {"en-gb": "English", "de-de": "German", "en-us": "English (US)", "es-es": "Spanish", "fi-fi": "Finnish", "fr-fr": "French", "nl-nl": "Dutch", "ru-ru": "Russian", "zh-cn": "Chinese", "it-it": "Italian", "cs-cz": "Czech", "pl-pl": "Polish", "sv-se": "Swedish", "nb-no": "Norwegian", "lt-lt": "Lithuanian", "ja-jp": "Japanese", "pt-pt": "Portuguese", "da-dk": "Danish", "ko-kr": "Korean", "vi-vn": "Vietnamese", "th-th": "Thai", "id-id": "Indonesian", "zh-hant": "Chinese (Traditional)", "de-ch": "German (Swiss)", "fr-ch": "French (Swiss)", "it-ch": "Italian (Swiss)", "ro-ro": "Romanian", "hu-hu": "Hungarian", "sk-sk": "Slovak", "nl-be": "Flemish"}, "buttons": "Buttons", "button": "<PERSON><PERSON>", "cancelBtn": "Cancel", "containerContentNotShown": "Due to lack of permissions the content of the container will not be displayed", "containerContentNotShownPermission": "Lacking permission:", "containerContentNotShownLoading": "not yet available, please wait ...", "contextIssue": {"title": "Context Mismatch found", "body": "<b>Oops!!!.</b><p>Mismatch of Context UserId and Access_Token UserId found.<br>Seems you have two sessions with different Logon Accounts. This is not allowed!.<br>Refreshing JWT AccessToken with wrong RefreshToken is not allowed.<br>Session cannot be continued.</p>Info: Context-UserId=<%=cUserId%> AccessToken UserId=<%=tUserId%><br>We will navigate you to the iTWO4.0 with valid user account.<br><br>Sorry for the inconvenience!<br>"}, "deleteConfirm": "Do you want to delete the current selection?", "deleteConfirmHeader": "Delete confirmation", "error": {"errormsgCode": "Errorcode: {{code}}", "errormsgCodeMsg": "Errorcode: {{code}}<br>Message: {{msg}}", "errormsgCodeMsgDetail": "Errorcode: {{code}}<br>Message: {{msg}}<br>Details: {{detail}}", "errorMsgCode": "Errorcode: <%=code%>", "errorMsgCodeMsg": "Errorcode: <%=code%><br>Message: <%=msg%>", "errorMsgCodeMsgDetail": "Errorcode: <%=code%><br>Message: <%=msg%><br>Details: <%=detail%>", "errorDetails": "Details"}, "exception": "Exception", "formAbout": "About '{{p1}}'", "formConfigAllowEnterNavigation": "Allow Enter Navigation", "formConfigCancelBnt": "Cancel", "formConfigCloseBnt": "Cancel", "formConfigCustomerLabelName": "Customization Label Name", "formConfigDialogTitle": "Customize Form", "formConfigLabelName": "Label Name", "formConfigMoveDownBnt": "Move Down", "formConfigMoveUpBnt": "Move Up", "formConfigOKBnt": "OK", "formConfigRestoreBnt": "Rest<PERSON>", "formConfigVisibility": "Visibility", "formSettings": "Settings", "gridDropPanel": "Drop a column header here to group by the column", "gridMarkerHeader": "Filter", "gridTreeHeader": "Structure", "ignoreBtn": "Ignore", "latestview": "Latest View", "layoutsystem": {"edit": "Edit View", "export": "Export Views", "import": "Import Views", "roleviews": "Role Views", "save": "Save View", "viewManagement": "View Management", "systemviews": "System Views", "userviews": "User Views", "portalviews": "Portal Views", "tabbedContainer": "Tabbed Container", "anchorButtonTitle": "Show More Tabs", "closeModuleButton": "Close Module", "showSystemFonts": "Show System Fonts", "showRuler": "Show Ruler", "defaultFont": "<PERSON><PERSON><PERSON>", "defaultFontSize": "<PERSON><PERSON><PERSON>ont Si<PERSON>", "exportDialogHeader": "Please select the views to be exported", "importSuccess": "The following views have been successfully imported.", "view": "View Name", "isoverwritten": "Is Overwritten"}, "tableProperties": {"backgroundColor": "Background Color", "borderColor": "Border Color", "borderStyle": "Border Style", "borderWidth": "Border Width (pt)", "cellAlignment": "Cell Alignment", "cellAlignmentHorizontal": "Horizontal", "cellAlignmentVertical": "Vertical", "cellHeight": "Cell Height (mm)", "cellWidth": "Cell Width ", "dimensions": "Dimensions", "horizontalPadding": "Horizontal Padding (mm)", "style": "Style", "tableHeight": "Table Height (mm)", "tableWidth": "Table Width (mm)", "verticalPadding": "Vertical Padding (mm)"}, "loginBusyMessage": "Login in Process. Please Wait", "loginButton": "<PERSON><PERSON>", "loginEnterPassword": "Enter Password", "loginEnterUsername": "<PERSON><PERSON> Username", "loginFailedMessage": "<PERSON><PERSON> failed. Please try again", "loginLanguage": "Language", "loginLanguageEnglish": "English", "loginLanguageEnglishUS": "English (US)", "loginLanguageGerman": "De<PERSON>ch", "loginLanguageGermanDE": "<PERSON><PERSON><PERSON> (DE)", "loginLanguageSpanish": "Español", "loginPassword": "Password", "loginSuccessfulMessage": "Logon successful. Loading. Please wait", "loginUsername": "Username", "loginViaFacebook": "Sign in with Facebook", "loginViaMicrosoftAccount": "Sign in with Microsoft Account", "loginViaTwitter": "Sign in with Twitter", "loginViaAad": "Azure Active Directory / Office 365", "loginViaAdfs": "Sign in with Microsoft ADFS Account", "loginViaOpenId": "Sign in with OpenIdConnect", "loginWaitHint": "Login disabled. Please wait <%=graceTime%>s", "logoffromAad": "Logout - Azure Active Directory / Office 365", "logoffromAdfs": "Logout - Microsoft ADFS Account", "logoffIdpProviderButton": "<PERSON><PERSON><PERSON> from Authentication Provider", "loginWithoutIdentityprovider": "Login with:", "loginWithIdentityprovider": "Or login with:", "longTextContainer": {"container": "Container", "field": "Field"}, "navOptionsMenu": "Option Menu", "newMessage": "A new message", "noBtn": "No", "okBtn": "OK", "overwriteInherited": "Overwrite Inherited Value", "overwriteExisting": "Overwrite existing?", "importLayoutHeader": "Import Layout", "refreshConfirm": "Changes were detected. Do you want to save before reloading?", "refreshConfirmHeader": "Refresh Confirmation", "retryBtn": "Retry", "searchPanelPlaceholder": "Search Term", "searchPanelString": "Search Term", "yesBtn": "Yes", "dragdrop": {"anyData": "Data", "items": "{{count}} item(s)"}, "inputControls": {"boolean": "Boolean", "code": "Code", "comment": "Multi Line Text (255)", "dateutc": "Date (UTC)", "description": "Single Line Text", "email": "E-Mail", "exchangerate": "Float (5-digits)", "factor": "Float (6-digits)", "inputselect": "Combo Box", "integer": "Integer", "money": "Money", "percent": "Percent", "quantity": "Float (3-digits)", "remark": "Multi Line Text (2000)", "select": "Dropdown"}, "searchcontrol": {"placeholderInputField": "Insert search Term", "popupHeader": "Search Mode", "popupSecondHeader": "Search In", "includes": "Includes", "startsWith": "Starts with", "endsWith": "Ends with", "allItems": "All Items"}, "loginViaGoogle": "Sign in with Google", "masterdetail": {"add": "Add item", "delete": "Delete item", "filter": "Filter items", "filterTemplate": "Search Term"}, "wizard": {"back": "Back", "finish": "Finish", "next": "Next", "nextStep": "Next Step:", "stepFinish": "Finish", "isIncluded": "Included", "listFilter": "Enter text to filter the list"}, "generatingMatrix": "Matrix is being generated", "hours": "Hours", "loginWith": "Sign in with", "minutes": "Minutes", "niceNames": {"codeDesc": "{{code}} ({{desc}})", "id": "#{{id}}"}, "processing": "Processing", "removeTooltip": "Remove", "subordinated": "Subordinated", "summary": "Summary", "todayTooltip": "Today", "bulkEditor": {"configName": "new Config Name", "run": "Run", "save": "Save", "saveAndRun": "Save & Run", "changedRecords": "Changed records", "changeReport": "Bulk Editor - Change report", "details": "Details", "logs": "Report Log", "msgChangeError": "Could not set {{property}} to {{value}}. Cause: {{validationErrorMsg}}", "msgChangeSuccess": "Set {{property}} to: {{value}}", "msgNewOldValueAreTheSame": "The value of {{property}} is already: {{value}}", "msgPropertyIsReadonly": "Could not set a value. {{property}} is readonly", "records": "Records", "totalRecords": "Total records", "unchangedRecords": "Unchanged records", "allMustMatch": "All Criteria must match", "anyMustMatch": "Any Criteria must match", "appendText": "Text to append", "backgrounds": "Background-Colours", "chooseField": "Select a Field", "chooseItem": "Select an Item", "chooseOperator": "Select a Operator", "Colour": "Colour", "compareDate": "Compare Date", "compareEnd": "End Value", "compareField": "Compare Field", "compareStart": "Start Value", "compareValue": "Compare Value", "contentDefinition": "Contents", "createDescriptionBackground": "No Background-Colours defined", "createDescriptionFonts": "No Fonts defined", "createRules": "Add a new Condition", "fonts": "Fonts", "Icon": "Icon", "InDecrease": "In- /Decrease by Percent", "Isbold": "Bold", "Isitalic": "Italic", "Isstriked": "Striked", "Isunderlined": "Underlined", "IsDisabled": "Disable Rule", "matrixSettingsDialogHeader": "Matrix Display Settings", "missingPContext": "This rule can not be executed. Required item not pinned: {{p1}}", "newDateValue": "new Date Value", "newDecimalValue": "new Decimal Value", "newEmailValue": "new Email", "newNumberValue": "new Number Value", "newSelection": "new Selection", "newTextValue": "new Text Value", "newColorValue": "new Color", "numberOfDays": "Number of Days", "percentValue": "Percent Value", "prependText": "Text to prepend", "string": "String", "substring": "Substring", "value": "Value", "invalidConfig": "Incomplete configurations can not be saved", "maxLengthError": "Processed Text became invalid. Maxlength mismatch", "valueToReplace": "Value to replace"}, "wysiwygEditor": {"unitOfMeasurement": "Unit of Measurement", "activateUserSettings": "Activate User Settings", "activateAutoNumberSettings": "Activate Autonumber Settings", "settingsHeader": "Settings", "settings": {"addFontFamily": "Add Font Family", "editFontFamily": "Edit <PERSON>ont Family", "toolAddFontSize": "Add Font-Size", "toolDeleteFontSize": "Delete Font-Size", "miSystem": "Text Editor", "groupFonts": "Fonts", "groupGeneral": "General", "groupDocview": "Document View", "fontSize": "Font Size (pt)", "colDisplayName": "Display Name", "colFontFamily": "Font Family", "colUrl": "Url", "colWeight": "Font Weight", "normal": "Normal", "weightBold": "Strong", "colStyle": "Font Style", "styleItalic": "Italic", "showSystemFonts": "Show System Fonts", "defaultFont": "<PERSON><PERSON><PERSON>", "defaultFontSize": "<PERSON><PERSON><PERSON>", "defaultAlignment": "Default Alignment", "unitOfMeasurement": "Unit of Measurement", "toolAddRecord": "Add Font Family", "toolDeleteRecord": "Delete Font Family", "toolEditFont": "<PERSON>ont-Family", "dialogDescription": "Here you can define the font files that are assigned to the font family.", "documentWidth": "<PERSON><PERSON><PERSON>", "documentWidthInfo": "Width of the document in mm", "documentPadding": "<PERSON><PERSON>", "documentPaddingInfo": "Margin of the document in mm"}, "bold": "Bold", "centerJustify": "Center Justify", "code": "Code", "font": "Font", "fontColor": "Font Color", "fontSize": "Font Size", "highlightColor": "Highlight Color", "indent": "Indent", "insertImage": "Insert Image", "italic": "Italic", "leftJustify": "Left Justify", "link": "Link", "orderedList": "Ordered List", "outdent": "Outdent", "paragraph": "Paragraph", "quote": "Quote", "removeFormatting": "Remove Formatting", "rightJustify": "Right Justify", "strikeThrough": "Strikethrough", "subscript": "Subscript", "superscript": "Superscript", "underline": "Underline", "unlink": "Unlink", "unorderedList": "Unordered List", "uploadFile": "Upload File", "insertTable": "Insert Table", "selectLanguage": "Select Language", "insertVariable": "Variable", "documentView": "Document View"}, "dialogs": {"deleteSelection": {"bodyText": "Do you want to delete the selected data? Possible dependencies will also be deleted.", "headerText": "Delete Data"}, "autoLogout": {"headerText": "Logout due to inactivity", "infoText": "You will be logged out automatically due to inactivity. To stay logged in, please close this dialog before the timer runs out.", "remainingTimeText": "Logging out in <%=timeRemaining%> s"}, "inputDialog": {"invalidInput": "The input is invalid.", "invalidCharacters": "Invalid Characters."}, "changeSelection": {"bodyText": "Do you want to change selected data records?<br>You can't undo this action.", "headerText": "Change Data"}}, "richTextEditor": {"font": "Select a font", "size": "Select a font size", "header": "Select the text style", "bold": "Bold", "italic": "Italic", "underline": "Underline", "strike": "Strikethrough", "color-picker-font": "Select a text color", "color-picker-highlight": "Select a highlight color", "background": "Select a background color", "blockquote": "Quote", "code-block": "Code", "script": {"sub": "Subscript", "super": "Superscript"}, "list": {"ordered": "Numbered list", "bullet": "Bulleted list"}, "indent": {"-1": "Decrease indent", "+1": "Increase indent"}, "direction": {"rtl": "Text direction (right to left | left to right)", "ltr": "Text direction (left ro right | right to left)"}, "align": {"left": "<PERSON><PERSON> left", "center": "Align center", "right": "Align right"}, "link": "Insert a link", "image": "Insert an image", "warning": "Warning", "image-paste-warning": "It is not possible to copy an image along with any other content. The pasted content should not contain any images.", "formula": "Insert a formula", "clean": "Remove format", "insertTable": "Insert table", "document-view": "Document View", "table": {"addRowAbove": "Add Row Above", "addRowBelow": "Add Row Below", "addColumnBefore": "Add Column Before", "addColumnAfter": "Add Column After", "deleteRow": "Delete Row", "deleteColumn": "Delete Column", "deleteTable": "Delete Table", "showVerticalBorder": "Show Vertical Border", "showHorizontalBorder": "Show Horizontal Border", "showAllBorders": "Show All Borders", "noBorder": "No Border", "append-row": "Add a row to the selected table", "append-col": "Add a column to the selected table", "remove-table": "Remove selected table", "tableEditor": "Table Editor", "cellEditor": "Cell Editor", "addTableOptions": "Add...", "deleteTableOptions": "Delete...", "tableBorderSetting": "Border Settings"}, "tableProperties": {"backgroundColor": "Background Color", "borderColor": "Border Color", "borderStyle": "Border Style", "borderWidth": "Border Width (pt)", "cellAlignment": "Cell Alignment", "cellAlignmentHorizontal": "Horizontal", "cellAlignmentVertical": "Vertical", "cellHeight": "Cell Height (mm)", "cellWidth": "Cell Width", "dimensions": "Dimensions", "horizontalPadding": "Horizontal Padding (mm)", "style": "Style", "tableHeight": "Table Height (mm)", "tableWidth": "Table Width (mm)", "verticalPadding": "Vertical Padding (mm)"}, "help": "Show help", "language": "Select Language", "variable": "Variable"}, "formContainer": {"collapseall": "Collapse All", "discard": "Discard", "expandall": "Expand All", "first": "First", "last": "Last", "next": "Next", "previous": "Previous", "print": "Print", "refresh": "Refresh", "save": "Save", "search": "Search", "settings": "Settings"}, "gridContainer": {"configDialogTitle": "Grid Layout"}, "grid": {"availableColumns": "Available Columns", "visibleColumns": "Visible Columns", "items": "Items", "filtered": "filtered", "gridSearchTitle": "Grid search", "gridSearchDescription": "Displays the search result of the grid search", "sidebarSearchTitle": "Sidebar search", "sidebarSearchDescription": "Search result from the database. Depending on the number of hits and the option 'Records per Page', the result list is displayed on several result pages"}, "formattedUnits": {"cubicMeters": {"short": "{{value}}m³"}, "meters": {"short": "{{value}}m"}, "squareMeters": {"short": "{{value}}m²"}}, "errorMessage": {"email": "Please enter a valid email (<EMAIL>)", "iban": "Please enter a valid IBAN", "url": "Please enter a valid url (https://www.mycompany.com)"}, "portal": {"backButton": "Back to Logon", "city": "City", "clerk": "Clerk", "companyName": "Company Name", "country": "Country", "loadingProviderInfo": "Reading available Social Media Provider. Please wait ...", "phone": "Phone", "providerName": "Name", "providerUserEmail": "Email", "registerButton": "Complete Register Now", "remark": "Remark", "socialInfo": "Your Social Provider Information", "street": "Street", "zipCode": "Zip Code", "accessGroup": "Access Group", "bpdName": "Partner Name", "contactName": "Contact Name", "email": "Email", "bpdAddress": "Partner Address", "confirmlabel": "Confirm", "finishedinfo": "Receive an email after inivitation finished?", "loadingInvitationInfo": "Resolving Invitation Info. Please wait... ", "invitationButton": "Complete Invitation Now"}, "listselection": {"available": "Available", "item": "<PERSON><PERSON>", "selected": "Selected"}, "sso": {"checkWaitMessage": "Check Single Sign-on Data. Please Wait...", "ssoInfoTemplate": "<b><%= ssoOwner %></b> Single Sign-on request via <%= ssoType %><br><b><%= company %></b> started.<br><br><br><br>", "title": "Single Sign-on", "ssoErrorTemplate": "<b><%= ssoOwner %></b> Single Sign-on request failed !<br><b>Status Code:</b>&emsp;<%= statuscode %><br><b>Error Details:</b>&emsp;<%= errorinfo %><br><br><br><br>"}, "changePasswordHeaderText": "Change Password", "changechangedinfo": "Your password has successfully changed", "changepasswordchangedprocess": "Changing password in progress... please wait", "changepasswordinfo": "Your password has expired or you need to change your initial password", "changePasswordInfoIntegrated": "In case you’re logged in with your domain account, the OldPassword should be the one define for the explicite user credentials. These might be different from the Integrated Account.", "changePasswordInstruction": "Please enter your current password followed by a new password including confirmation of the password", "confirmLoginEnterPassword": "enter confirm password", "confirmLoginPassword": "Confirm Password", "loginLogonName": "Logonname", "newLoginEnterPassword": "enter new password", "newLoginPassword": "New Password", "oldLoginEnterPassword": "enter old password", "oldLoginPassword": "Old Password", "backButton": "Back to Logon", "backButtonNavInfo": "Navigate to Logon Page. Please wait...", "changePwdBtn": "Change Password", "changenotchangedinfo": "Your password has not been changed", "invalidDiffPassword": "New Password must be different from previous", "invalidEqPassword": "New Password must be equal to confirm password", "invalidLenPassword": "New Password must contain at least six characters!", "invalidLowerCasePassword": "New Password must contain at least one lowercase letter (a-z)!", "invalidPassword": "Invalid old or new or confirm password", "invalidSpecialCharPassword": "New Password must contain at least one number (0-9)! or one special character", "invalidUpperCasePassword": "New Password must contain at least one uppercase letter (A-Z)!", "invalidUsernamePassword": "New Password must be different from Username", "navToCompanyPage": "Navigate to Company Page. Please wait...", "navToLogonPage": "User <PERSON>gon missing. Navigate to Logon Page. Please wait...", "planningboard": {"gridSettings": "<PERSON><PERSON>", "demandSettings": "Demand Grid Layout", "supplierSettings": "Supplier Grid Layout", "settingsGroup": "Planning Board Settings", "chartPresentation": "Chart Presentation", "configDialog": "Planning Board Settings", "assignment": "Assignment", "assignmentTimeline": "Assignment Timeline", "rowHeight": "Row Height", "flexibleRowHeight": "Flexible Row Height", "showStatusIcon": "Show Status Icon", "showTypeIcon": "Show Type Icon", "visual": "Visual", "dayBack": "Day Back", "dayForward": "Day Forward", "refreshData": "Reload Data", "today": "Today", "weekAbbreviation": "CW", "weekBack": "Week Back", "weekForward": "Week Forward", "setDefaultZoomLevel": "Set Default Zoom Level", "saveLastZoom": "Save Last Zoom", "zoom": "Zoom", "zoomIn": "Zoom In", "zoomNormal": "Reset Zoom", "zoomOut": "Zoom Out", "zoomOneWeek": "One Week", "zoomTwoWeek": "Two Weeks", "zoomOneMonth": "One Month", "showHeaderColor": "Show Header Color", "showSameAssignments": "Assignment Membership", "showStatusAsBGColor": "Show Status Color as Background", "snapToDays": "Snap to Day Grid", "showExtendedDemands": "Show extended Demand range", "showInfo1Text": "Show Info Text 1", "showInfo2Text": "Show Info Text 2", "showInfo3Text": "Show Info Text 3", "showHeaderBackground": "Show Calendar Backgrounds in Header", "settings": "Planning Board Configuration", "setDefault": "Edit", "setStatus": "Set Status", "create": "On Demand Creation", "onDemand": "On Demand", "useLevelOfDetail": "Collect Items After", "levelOfDetail": {"day": "n Days", "week": "n Weeks", "month": "n Months", "mode": "Collection Mode"}, "default": "Collection Mode Off", "day": "Collection On Days", "week": "Collection On Weeks", "month": "Collection On Months", "manual": "manual", "auto": "auto", "validateAssignments": "Validate Assignments", "showAggregations": "Show Aggregations", "showSumAggregations": "Show Sum Aggregations", "validation": "Validation", "validateSelectedDemand": "Show Only Suppliers For Selected Demand", "validateSelectedSupplier": "Show Only Demands For Selected Supplier", "missingSkill": "Missing Skill", "aggregation": "Aggregation", "total": "Total", "useTaggingSystem": "Use Tagging System", "tagging": "Tagging", "status": "Status", "type": "Type", "project": "Project", "description": "Description", "color": "Color", "order": "Order", "visible": "Visible", "collection": "Collection", "background": "Background", "border": "Border", "font": "Font", "useMinAggregation": "Use Minimal Aggregation", "setMinAggregationLevel": "Set Min Aggregation Level", "minAggregationLevel": {"hour": "Hour", "day": "Day", "week": "Week", "month": "Month"}, "setBackgroundColorConfig": "Set Backgroundcolor", "backgroundColorConfig": {"default": "Default Color", "status": "Status Color", "project": "Project Color", "ppsHeader": "Production Planning Color"}, "showMainLabelText": "Show Main Text", "mainLabelText": "Main Label", "ignoreIsFullyCovered": "Ignore Is Fully Covered", "ignoreIsNotFullyCovered": "Ignore Is Not Fully Covered", "amount": "Amount of Assignments", "targetValue": "Target Value", "actualValue": "Actual Value", "residualValue": "Residual Value", "sumAggregation": "Sum Aggregation", "sumAggregationLine1": "Sum Aggregation Line1", "sumAggregationLine2": "Sum Aggregation Line2", "sumAggregationLine3": "Sum Aggregation Line3", "sumAggregationPropertiesLine1": "Sum Aggregation Property Line1", "sumAggregationPropertiesLine2": "Sum Aggregation Property Line2", "sumAggregationPropertiesLine3": "Sum Aggregation Property Line3", "useDemandTimesForReservation": "Use Demand Times For Reservation", "aggregationTrafficLightsConfig": "Aggregation Traffic Lights", "underload": "Under Loaded", "goodload": "Good Loaded", "maxload": "Max Loaded", "overload": "Overloaded", "underloadvalue": "Under Loaded Value", "goodloadvalue": "Good Loaded Value", "maxloadvalue": "Max Loaded Value", "overloadvalue": "Over Loaded Value", "useFixedAssignmentHeight": "Minimal Height", "demandpreview": "Show Demand Preview", "filterDemands": "<PERSON><PERSON> Demands", "filter": "Filter", "lockCalendar": "Lock Calendar", "sureToSaveChanges": "Do you want to save the changes?", "assignmentModifications": "Assignment Modifications", "ppsHeader": "Production Planning", "createAssignmentTooltip": "Creates assignments of the selected demand by clicking into the board", "capacityHistogram": "Capacity Histogram"}, "dateUtil": {"conflictingPhases": "Conflicting Phases", "createNewSequence": "Create New Sequence", "merge": "<PERSON><PERSON>"}, "userInfoNotAvailable": "not available", "groupedItems": {"allLevels": "All Levels", "breakdown": "Breakdown", "calendarWeek": "Calendar Week", "color": "Color", "date": "Date", "groupingOptions": "Grouping Options", "month": "Month", "radio": "Radio", "tillLevel1": "Till Level 1", "tillLevel2": "Till Level 2", "tillLevel3": "Till Level 3", "tillLevel4": "Till Level 4", "tillLevel5": "Till Level 5", "tillLevel6": "Till Level 6", "tillLevel7": "Till Level 7", "tillLevel8": "Till Level 8", "tillLevel9": "Till Level 9", "weekday": "Weekday", "year": "Year", "day": "Day", "sortDesc": "Invert Order", "dayMonth": "Day of Month", "dayYear": "Day of Year", "today": "Select Today", "strictTillLevel": "Strict Till Level"}, "gridCountHeader": "Count", "numberShortening": {"siPrefix_10_0": "_", "siPrefix_10_1": "da", "siPrefix_10_-1": "d", "siPrefix_10_12": "T", "siPrefix_10_-12": "p", "siPrefix_10_15": "P", "siPrefix_10_-15": "f", "siPrefix_10_18": "E", "siPrefix_10_-18": "a", "siPrefix_10_2": "h", "siPrefix_10_-2": "c", "siPrefix_10_21": "Z", "siPrefix_10_-21": "z", "siPrefix_10_24": "Y", "siPrefix_10_-24": "y", "siPrefix_10_3": "k", "siPrefix_10_-3": "m", "siPrefix_10_6": "M", "siPrefix_10_-6": "µ", "siPrefix_10_9": "G", "siPrefix_10_-9": "n", "siPrefix_1024_0": "_", "siPrefix_1024_1": "<PERSON>", "siPrefix_1024_2": "<PERSON>", "siPrefix_1024_3": "Gi", "siPrefix_1024_4": "Ti", "siPrefix_1024_5": "Pi", "siPrefix_1024_6": "<PERSON>i", "siPrefix_1024_7": "<PERSON><PERSON>", "siPrefix_1024_8": "<PERSON>", "tradingPrefix_10_0": "_", "tradingPrefix_10_1": "", "tradingPrefix_10_-1": "", "tradingPrefix_10_10": "", "tradingPrefix_10_-10": "", "tradingPrefix_10_11": "", "tradingPrefix_10_-11": "", "tradingPrefix_10_12": "tn", "tradingPrefix_10_-12": "", "tradingPrefix_10_13": "", "tradingPrefix_10_-13": "", "tradingPrefix_10_14": "", "tradingPrefix_10_-14": "", "tradingPrefix_10_15": "", "tradingPrefix_10_-15": "", "tradingPrefix_10_-16": "", "tradingPrefix_10_-17": "", "tradingPrefix_10_18": "", "tradingPrefix_10_-18": "", "tradingPrefix_10_19": "", "tradingPrefix_10_-19": "", "tradingPrefix_10_2": "", "tradingPrefix_10_-2": "", "tradingPrefix_10_20": "", "tradingPrefix_10_-20": "", "tradingPrefix_10_21": "", "tradingPrefix_10_-21": "", "tradingPrefix_10_22": "", "tradingPrefix_10_-22": "", "tradingPrefix_10_23": "", "tradingPrefix_10_-23": "", "tradingPrefix_10_24": "", "tradingPrefix_10_-24": "", "tradingPrefix_10_3": "k", "tradingPrefix_10_-3": "", "tradingPrefix_10_4": "", "tradingPrefix_10_-4": "", "tradingPrefix_10_5": "", "tradingPrefix_10_-5": "", "tradingPrefix_10_6": "m", "tradingPrefix_10_-6": "", "tradingPrefix_10_7": "", "tradingPrefix_10_-7": "", "tradingPrefix_10_8": "", "tradingPrefix_10_-8": "", "tradingPrefix_10_9": "bn", "tradingPrefix_10_-9": ""}, "unsavedData": "Unsaved Data", "customTranslateControl": {"language": "Language", "description": "Description", "header": "Translation"}, "nextPage": "Next", "previousPage": "Back", "gantt": {"dateshiftModes": "Dateshift Modes", "fullshift": "Fullshift", "both": "All", "right": "Right", "left": "Left", "self": "Self", "push": "<PERSON><PERSON>", "moveModes": "Move Modes", "auto": "Auto", "hour": "Hour", "day": "Day", "week": "Week"}, "dashboard": {"projectTitle1": "Dashboard 1", "projectTitle2": "Dashboard 2", "projectTitle3": "Dashboard 3", "projectTitle4": "Dashboard 4", "enterpriseTitle": "Dashboard"}, "identityserver": {"disableHint": "User disable because of to many faulty login attempts.", "disabledByUserManagement": "User was disable by the user Management"}, "aggregateColumnTitle": "Aggregate", "aggregations": {"sum": "SUM", "avg": "AVG", "max": "MAX", "min": "MIN", "none": ""}, "concurrencyExceptionHeader": "Data cannot be saved", "concurrencyExceptionAlternatives": "The data in the client is out of date and changes cannot be saved. In order to reload the data press Reload. In order to keep the outdated data in the browser press Cancel", "concurrencyExceptionOnlyCancel": "The data in the client is out of date and changes cannot be saved. You have to refresh the data before you can save changes again.", "concurrencyReload": "Reload", "modules": {"moduleNames": {"projectMain": "Project"}}}}