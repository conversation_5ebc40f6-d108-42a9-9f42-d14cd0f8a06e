using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Platform.Core;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Basics.Company.BusinessComponents;
using NLS = RIB.Visual.Estimate.Main.Localization.Properties.Resources;
using RIB.Visual.Estimate.Common.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;
using Microsoft.ClearScript;
using LocalizationProps = RIB.Visual.Estimate.Main.Localization.Properties;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public abstract class EstResourceHandlerV2<T> : ScriptEntityObject<T> where T : IIdentifyable
	{
		private IEstimateResourceFactory _EstResourceFactory;

		private EstResourceObject CreateResource(Func<IScriptEstResource> CreateResourceFunc, EstResourceType type, bool triggerAction = true)
		{
			try
			{
				if (CreateResourceFunc == null)
				{
					return null;
				}

				/* check whether the parent is subitem, if not, can not create resource */
				var result = CanAddResource();

				if (result.Disabled)
				{
					WriteError(string.Format("Create {0} failed because {1}.", type.ToString(), result.Error));

					return null;
				}

				/* do before create resource */
				BeforeCreateResource();

				/* create resource*/
				var entity = CreateResourceFunc();

				if (entity == null)
				{
					return null;
				}

				/* set parent */
				IScriptEstResource parent = this.GetParent() != null ? this.GetParent().Entity : null;

				if (parent != null)
				{
					entity.Parent = parent;
				}

			 /* mark the lineItem as modified */
			 (this.Response as ScriptResponseBase).AddModifiedLineItem(entity.EstLineItemFk);

				/* for estimate */
				IScriptEstLineItem lineItem = this.GetLineItem();

				EstResourceObject wrapper = GetResourceInstance(entity, lineItem);

				/* for construction */
				this.Resources.Add(wrapper);

				if (triggerAction)
				{
					AfterCreateResource(wrapper);
				}

				AddResourceBase(entity, parent, lineItem);

				return wrapper;
			}
			catch (Exception ex)
			{
				if (ex is EstRuleWarningException)
				{
					WriteWarning(ex.Message);
					return null;
				}
				else
				{
					throw;
				}
			}
		}

		/// <summary>
		/// Resources.
		/// </summary>
		public virtual ICollection<IEstResourceScriptObject> Resources { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IScriptRequest Request { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IScriptResponse Response { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public EstResourceHandlerV2()
		{

		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="engine"></param>
		/// <param name="request"></param>
		/// <param name="response"></param>
		public EstResourceHandlerV2(ScriptEngine engine, ScriptRequestBase request, ScriptResponseBase response)
			: base(engine, request, response)
		{
			this.Request = request;

			this.Response = response;

			this.Resources = new List<IEstResourceScriptObject>();

			if (this.Request is IEstimateScriptRequest)
			{
				_EstResourceFactory = (this.Request as IEstimateScriptRequest).GetEstimateResourceFactory();
			}
			else
			{
				_EstResourceFactory = new EstimateResourceFactory(request.ProjectFk);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public IEstResourceScriptObject GetSingleSubItem(string code)
		{
			if (this.Request is EstRuleScriptRequest)
			{
				Func<IScriptEstResource> createSubItemFunc = () =>
				{
					return CreateResource(EstResourceType.SubItem).Entity;
				};

				var lineItem = this.GetLineItem();

				var parent = this.GetParent() != null ? this.GetParent().Entity : null;

				var subItem = (this.Request as EstRuleScriptRequest).ApplicationContext.ResourceModificationTracker.GetSingleLineItemByCode(code, lineItem, parent, createSubItemFunc);

				return new EstResourceObject().New(lineItem, subItem, this.Request, this.Response, this.Engine);
			}

			return null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="materialCodes"></param>
		/// <param name="catalogCodes"></param>
		/// <returns></returns>
		public EstResourceObject[] CreateMaterials(object[] materialCodes, object[] catalogCodes = null)
		{
			if (materialCodes == null || !materialCodes.Any())
			{
				return new EstResourceObject[0];
			}

			int index = 0;

			var result = new List<EstResourceObject>();

			while (index < materialCodes.Length)
			{
				try
				{
					if (catalogCodes == null || catalogCodes.Length <= index)
					{
						result.Add(this.CreateMaterial(materialCodes[index] as string, String.Empty));
					}
					else
					{
						result.Add(this.CreateMaterial(materialCodes[index] as string, catalogCodes[index] as string));
					}
				}
				catch(Exception e)
				{
					WriteError(new BusinessLayerException(string.Format("Create material {0} in catalog {1} failed due to inner exception!", materialCodes[index], catalogCodes[index]), e).ToString());
				}

				index++;
			}

			return result.ToArray();
		}

		/// <summary>
		/// todo-wui: Obsolete, would delete
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public EstResourceObject GetResource(string code)
		{
			return SearchResource(code);
		}

		/// <summary>
		/// todo-wui: Obsolete, would delete
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public EstResourceObject RemoveResource(string code)
		{
			return SearchResource(code, true);
		}

		/// <summary>
		/// Get all resource
		/// </summary>
		/// <returns></returns>
		public IEstResourceScriptObject[] GetResources()
		{
			return this.Resources.ToArray();
		}

		/// <summary>
		/// Get resource by code
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public IEstResourceScriptObject GetResourceByCode(string code)
		{
			return SearchResource(code);
		}

		/// <summary>
		/// Remove resource by code
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public IEstResourceScriptObject RemoveResourceByCode(string code)
		{
			return SearchResource(code, true);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public IEstResourceScriptObject[] GetResourceByMajorCostCode(string code)
		{
			if (!string.IsNullOrEmpty(code))
			{
				var resourceList = this.GetResourcesByMajorCC(code);

				return resourceList.Select(e => Injector.Get<IEstResourceScriptObject>().New(e, this.Request, this.Response, this.Engine)).ToArray();
			}

			return new IEstResourceScriptObject[0];
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <returns></returns>
		public IEstResourceScriptObject ReplaceCostCode(string oldCode, string newCode)
		{
			this.RemoveResourceByCode(oldCode);

			return this.CreateCostCode(newCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <param name="newCatalogCode"></param>
		/// <returns></returns>
		public IEstResourceScriptObject ReplaceMaterial(string oldCode, string newCode, string newCatalogCode = "")
		{
			this.RemoveResourceByCode(oldCode);

			return this.CreateMaterial(newCode, newCatalogCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <returns></returns>
		public IEstResourceScriptObject ReplacePlant(string oldCode, string newCode)
		{
			this.RemoveResourceByCode(oldCode);

			return this.CreatePlant(newCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <param name="newCatalogCode"></param>
		/// <param name="newRootAssemblyCatCode"></param>
		/// <returns></returns>
		public IEstResourceScriptObject ReplaceAssembly(string oldCode, string newCode, string newCatalogCode="", string newRootAssemblyCatCode="")
		{
			this.RemoveResourceByCode(oldCode);

			return this.CreateAssembly(newCode, newCatalogCode, newRootAssemblyCatCode, (this.Request as ScriptRequestBase).ProjectFk);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <returns></returns>
		public IEstResourceScriptObject ReplaceSubItem(string oldCode, string newCode)
		{
			this.RemoveResourceByCode(oldCode);

			return this.CreateSubItem(newCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="resourceType"></param>
		/// <returns></returns>
		public EstResourceObject CreateResource(EstResourceType resourceType)
		{
			return CreateResource(
					  () => this._EstResourceFactory.CreateResource((int)resourceType, GenerateCreationInfo()),
					  resourceType);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="desc"></param>
		/// <param name="resourceType"></param>
		/// <returns></returns>
		public EstResourceObject CreateResourceText(string desc, EstResourceType resourceType)
		{
			if (EstResourceType.TextLine == resourceType || EstResourceType.InternalTextLine == resourceType)
			{
				var creationInfo = GenerateCreationInfo();
				creationInfo.DescriptionInfo = new DescriptionTranslateType()
				{
					Description = desc,
					Translated = desc,
					VersionTr = 0
				};
				return CreateResource(() => this._EstResourceFactory.CreateResource((int)resourceType, creationInfo), resourceType);
			}
			return null;
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public EstResourceObject CreateCostCode(string code)
		{
			var creationInfo = GenerateCreationInfo();
			string msg = string.Empty;
			if (ScriptHelper.CanCreateCostCode(code, this.Request, creationInfo, out msg))
			{
				return CreateResource(
						  () => this._EstResourceFactory.CreateCostCodeByCode(code, GenerateCreationInfo()),
						  EstResourceType.CostCode);
			}
			else
			{
				this.ResponseBase.WriteWarning(this.FormatError(msg));
				return null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="materialCatalogCode"></param>
		/// <returns></returns>
		public EstResourceObject CreateMaterial(string code, string materialCatalogCode)
		{
			return CreateResource(
					  () => this._EstResourceFactory.CreateMaterialByCode(code, materialCatalogCode, GenerateCreationInfo()),
					  EstResourceType.Material);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="plantMasterCode"></param>
		/// <returns></returns>
		public EstResourceObject CreatePlant(string code, string plantMasterCode = "")
		{
			var job = EstJobHelper.GetJobById(this.GetJobId(), (this.Request as ScriptRequestBase).ProjectFk);

			if (job != null && job.PlantEstimatePriceListFk.HasValue)
			{
				var lineitemId = this.GetLineItemId();
				var creationInfo = GenerateCreationInfo();
				var unsavedResourceTree = (this.Request as EstRuleScriptRequest).ApplicationContext.ResourceModificationTracker.GetResourcesByLineItemId(lineitemId);
				creationInfo.UnsavedResourceTree = unsavedResourceTree;
				creationInfo.EstLineItem = this.GetLineItem();
				creationInfo.ParentId = this.GetParentId();
                creationInfo.AfterCreatedEnhance = (entities, existPlant) =>
                {
                    var entity = entities.FirstOrDefault();
                    if (entities.Count == 1)
                    {
                        if (!string.IsNullOrWhiteSpace(code) && entity.EstResourceTypeFk == (int)EstResourceType.Plant && entity.EtmPlantFk.HasValue)
                        {
                            var palntInstance = CreateResource(() => entity, EstResourceType.Plant);

                            return palntInstance.Entity.Resources.FirstOrDefault();
                        }
                        return entity;
                    }
                    else if (entities.Count > 1 && existPlant != null)
                    {
                        var plant = existPlant as EstResourceEntity;

                        foreach (var item in entities.Where(e => e.EstResourceFk == plant.Id))
                        {
                            CreateResource(() => item, EstResourceType.Plant);
                        }

                        return plant;
                    }
                    return entity;
                };
                return CreateResource(
						  () => this._EstResourceFactory.CreatePlanByCode(code, plantMasterCode, creationInfo),
						  EstResourceType.Plant);
			}
			else
			{
				WriteWarning(LocalizationProps.Resources.Warn_Rules_Resource_Plant_Not_Create);
			}
			return null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public EstResourceObject CreateComputationalLine(string quantityDetail)
		{
			var creationInfo = GenerateCreationInfo();
			creationInfo.AfterCreated = e => e.QuantityDetail = string.IsNullOrWhiteSpace(quantityDetail)? "" : quantityDetail;
			return CreateResource(
					  () => this._EstResourceFactory.CreateResource((int)EstResourceType.ComputationalLine, creationInfo),
					  EstResourceType.ComputationalLine);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public EstResourceObject CreateSubItem(string code)
		{
			return CreateResource(
					  () => this._EstResourceFactory.CreateSubItemByCode(code, GenerateCreationInfo()),
					  EstResourceType.SubItem);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="assemblyCatCode"></param>
		/// <param name="rootAssemblyCatCode"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public EstResourceObject CreateAssembly(string code, string assemblyCatCode, string rootAssemblyCatCode, int? projectId)
		{
			var wrapper = CreateResource(
								 () => _EstResourceFactory.CreateAssemblyByCode(code, assemblyCatCode, rootAssemblyCatCode, projectId, GenerateCreationInfo()),
								 EstResourceType.Assembly,
								 false);

			if (wrapper.Entity.Resources != null)
			{
				wrapper.Resources = wrapper.Entity.Resources.Select(e => GetResourceInstance(e, this.GetLineItem()) as IEstResourceScriptObject).ToList();
			}

			AfterCreateResource(wrapper);

			if (wrapper.Entity.Resources != null)
			{
				SetRuleSourceFk(wrapper.Entity.Resources, wrapper.Entity);
			}

			return wrapper;
		}

		private IEstResourceCreationInfo GenerateCreationInfo()
		{
			return new EstResourceCreationInfo()
			{
				EstHeaderId = this.GetHeaderId(),
				EstLineItemId = this.GetLineItemId(),
				ParentId = this.GetParentId(),
				JobId = this.GetJobId(),
				ProjectId = (this.Request as ScriptRequestBase).ProjectFk,
				GenerateCharacteristicId = false,
				IsGenerateDefaultCharacteristic = true
			};
		}

		private void SetRuleSourceFk(IEnumerable<IScriptEstResource> resourceList, IScriptEstResource resourceParent)
		{
			if (resourceList != null && resourceParent != null)
			{
				foreach (var res in resourceList)
				{
					res.EstRuleSourceFk = resourceParent.EstRuleSourceFk;

					if (res.Resources != null)
					{
						SetRuleSourceFk(res.Resources, res);
					}
				}
			}
		}

		private EstResourceObject SearchResource(string code, bool isRemove = false)
		{
			EstResourceObject result = null;

			Queue<IEstResourceScriptObject> queue = new Queue<IEstResourceScriptObject>();

			ICollection<IEstResourceScriptObject> resource = this.Resources;

			while (queue.Count > 0 || resource.Count > 0)
			{
				foreach (var item in resource)
				{
					if (item.Entity.Code == code)
					{
						result = item as EstResourceObject;

						if (isRemove)
						{
							resource.Remove(result);
						}

						break;
					}

					queue.Enqueue(item);
				}

				if (result == null)
				{
					resource = queue.Dequeue().Resources;
				}
				else
				{
					break;
				}
			}

			return result;
		}

		private IEnumerable<EstResourceObject> GetResources(string code)
		{
			List<EstResourceObject> resourceObjInstanceList = new List<EstResourceObject>();

			if (this.Resources != null)
			{
				resourceObjInstanceList = this.Resources.Where(e => e.Entity.Code.ToUpper() == code.Trim().ToUpper()).Select(e => e as EstResourceObject).ToList();
			}

			return resourceObjInstanceList;
		}

		private IEnumerable<IScriptEstResource> GetResourcesByMajorCC(string costCode)
		{
			IEnumerable<IScriptEstResource> resourceList = new List<IScriptEstResource>();

			if (this.Resources != null && this.Resources.Any())
			{
				foreach (var item in this.Resources)
				{
					var currenctResources = item.FlattenResources();

					if (currenctResources != null && currenctResources.Any())
					{
						resourceList = resourceList.Concat(currenctResources);
					}
				}
			}

			return new EstimateMainResourceTotalLogic().GetResoucesByMajorCostCode(resourceList, costCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract int? GetParentId();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract int GetHeaderId();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract int GetLineItemId();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected virtual int? GetJobId()
		{
			/* get jobId from request */
			var defaultJobId = (this.Request as ScriptRequestBase).JobFk;

			if (defaultJobId.HasValue)
			{
				return defaultJobId;
			}

			if(this.Request.ApiId == "Estimate.Rule" && this.Request is EstRuleScriptRequest)
			{
				return (this.Request as EstRuleScriptRequest).ApplicationContext.GetEstimateScopeObject().GetDefaultJobId();
			}
			else
			{
				/* get jobId from header(using executionCache) */
				var estHeaderEntity = ExecutionCache.Current.Get<IEstHeaderEntity>(
					String.Format("EstHeader:{0}", (this.Request as ScriptRequestBase).EstHeaderFk),
					() => new EstimateMainHeaderLogic().GetItemById((this.Request as ScriptRequestBase).EstHeaderFk));

				if (estHeaderEntity != null && estHeaderEntity.LgmJobFk.HasValue)
				{
					return estHeaderEntity.LgmJobFk;
				}

				/* get jobId from project(using executionCache) */
				return ExecutionCache.Current.Get<int?>(
					string.Format("getJobIdByProject:{0}", (this.Request as ScriptRequestBase).ProjectFk),
					() => EstJobHelper.GetProjectJobId((this.Request as ScriptRequestBase).ProjectFk));
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract CanAddResourceModel CanAddResource();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract EstResourceObject GetParent();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract IScriptEstLineItem GetLineItem();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public abstract ICollection<IScriptEstResource> GetRawResources();

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="parent"></param>
		/// <param name="lineItem"></param>
		protected abstract void AddResourceBase(IScriptEstResource entity, IScriptEstResource parent, IScriptEstLineItem lineItem);

		/// <summary>
		/// Before creating resource hook
		/// </summary>
		public virtual void BeforeCreateResource()
		{

		}

		/// <summary>
		/// After creating resource hook
		/// </summary>
		/// <param name="entity"></param>
		public virtual void AfterCreateResource(IEstResourceScriptObject entity)
		{

		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="lineItem"></param>
		/// <returns></returns>
		public virtual EstResourceObject GetResourceInstance(IScriptEstResource entity, IScriptEstLineItem lineItem)
		{
			return new EstResourceObject(lineItem, entity, (this.Request as ScriptRequestBase), (this.Response as ScriptResponseBase), this.Engine);
		}

		/// <summary>
		/// 
		/// </summary>
		public Action<object, TCreateResourceEventArgV2> AfterCreateResourceAction;

		/// <summary>
		/// 
		/// </summary>
		public Action<IEstResourceScriptObject> AfterCloneResourceAction;

		/// <summary>
		/// 
		/// </summary>
		public event Action<IEstResourceScriptObject> DoAfterResourceClone
		{
			add
			{
				AfterCloneResourceAction += value;
			}

			remove
			{
				AfterCloneResourceAction -= value;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		public event Action<object, TCreateResourceEventArgV2> DoAfterCreateResource
		{
			add
			{
				AfterCreateResourceAction += value;
			}

			remove
			{
				AfterCreateResourceAction -= value;
			}
		}

	}
}
