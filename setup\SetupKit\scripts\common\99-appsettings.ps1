function ProcessExcelAddonsConfig {
	param (
		[boolean]$install,
		[string] $addOnsFolder )

	if ($false -eq $install ) {
		return
 }


	Write-Host 'Process Addons xml file.' -ForegroundColor $colorinfo
	$addonsConfigFilePath = Join-Path $trgAddonsIISPath 'excelimport\excelImportManifest.xml.template'
	$addonsConfigFileTargetPath = Join-Path $trgAddonsIISPath 'excelimport\excelImportManifest.xml'
	$addonsContent = (Get-Content $addonsConfigFilePath)
	# replace place holder by target url
	$addonsRootUrl = $itwo40url
	$addonsContent = $addonsContent.replace('%appBaseUrl%', $addonsRootUrl)
	if (Test-Path $addonsConfigFileTargetPath) {
		Remove-Item $addonsConfigFileTargetPath'.sav' -Force -ErrorAction ignore
		Rename-Item -Path $addonsConfigFileTargetPath -NewName $addonsConfigFileTargetPath'.sav' -Force
	}
	$addonsContent | Out-File $addonsConfigFileTargetPath -Force -Encoding utf8
	Write-Host 'Processed Addons xml file. path=addonsConfigFileTargetPath' -ForegroundColor $colorinfo
}

function ProcessClientWebConfig {
	param (
		[boolean]$install,
		[string] $clientFolder )

	if ($false -eq $install ) {
		return
 }

	Write-Host -ForegroundColor $colorinfo 'Process Client template web.config.netcore to web.config '
	Copy-Item -Force -Path $(Join-Path $clientFolder 'web.config.net' ) -Destination $(Join-Path $clientFolder 'web.config' )

}

function ProcessClientConfigJs {
	param (
		[boolean]$install,
		[string] $clientFolder )

	if ($false -eq $install ) {
		return
 }

	$ConfigJsPath = Join-Path $clientFolder 'config.js'
	Write-Host "Process Client config.js file: $ConfigJsPath" -ForegroundColor $colorinfo
	$ConfigJsPath = Join-Path $clientFolder 'config.js'
	$json = (Get-Content $ConfigJsPath -Raw)
	$json | Out-File -Encoding 'utf8' "$ConfigJsPath.sav" -Force

	$json = $json -replace "(version: '[A-Za-z0-9-.@ ',]+)", ''
	$json = $json -replace "(versionInfo: '[A-Za-z0-9-.@ ',]+)", "productversion: '$productversion', buildversion: '$buildversion', productdate: '$productdate', installationdate: '$installationdate',"
	$newIdSrv = "identityBaseUrl: '" + $identityServerUrl + "/core/',"
	$result = $json -replace "identityBaseUrl: '([A-Za-z0-9.:./.-]+',)", $newIdSrv

	## datapine url and token processing rei@18.6.2020
	if ($datapineiframeurl) {
		$_result = ConfigJsReplaceDatapineUrl -configjscontent $result -url $datapineiframeurl # return $null when failed
		if ($_result) {
			$result = $_result
		}
	}
	if ($usei18nOverrideJsonText -eq 1) {
		$_result = ConfigJsReplacei18NCustom -configjscontent $result -isactive $true
		if ($_result) {
			$result = $_result
		}
	}

	<# support userlane detail see https://docs.userlane.com/en/articles/1864515-userlane-snippet-how-to-install-adjust-it#>
	# [optional] Please enter here your userlane user_id, i you enter the user_id, we will activate the userlane feature in iTWO4.0.
	if ( ($null -ne $userlanePropertyId) -and $($userlanePropertyId.length) -gt 0) {
		$_result = ConfigJsReplaceUserlanePropertyId -configjscontent $result -userlanePropertyId $userlanePropertyId
		if ($_result) {
			$result = $_result
		}
	}

	if ($office365Tenant -and $office365ApplicationClientId -and $office365ApplicationSecret -and $officeViewerServerUrl) {
		Write-Host -ForegroundColor $colorinfo 'iTWO4.0 Office365 Integration enabled, set config.js parameters'
		Write-Host -ForegroundColor $colorinfo "  authority='$office365AuthorityUrl', msGraph='$office365MsgraphUrl',skype='$office365SkypeUrl',officeViewerServerUrl='$officeViewerServerUrl', tenant='$office365Tenant', clientId='$office365ApplicationClientId'"

		<# Office 365 Integration like OneDrive and Skype #>
		$_result = ConfigJsReplaceOfficeIntegrationParameter -configjscontent $result `
			-authorityUrl $office365AuthorityUrl -tenant $office365Tenant -office365clientid $office365ApplicationClientId `
			-msgraphUrl $office365MsgraphUrl -skypeUrl $office365SkypeUrl -officeViewerServerUrl $officeViewerServerUrl
		if ($_result) {
			$result = $_result
		}
	}
	#now save it
	$result | Out-File -Encoding 'utf8' $ConfigJsPath
	<# config.js done #################################################################>
}

function ProcessClientConfigJson {
	param (
		[boolean]$install,
		[string] $clientFolder )

	if ($false -eq $install ) {
		return
 }
	$ConfigJsonPath = Join-Path $clientFolder 'config.json'
	if (Test-Path $ConfigJsonPath  ) {
		Write-Host "Process Client config.json file: $ConfigJsonPath" -ForegroundColor $colorimportant
		$content = (Get-Content $ConfigJsonPath -Raw) | ConvertFrom-Json

		setobjectmember -in $content -member productversion -value $productversion
		setobjectmember -in $content -member buildversion -value $buildversion
		setobjectmember -in $content -member productdate -value $productdate
		setobjectmember -in $content -member installationdate -value $installationdate

		$_idsrvurl = $identityServerUrl + '/core/'
		setobjectmember -in $content -member identityBaseUrl -value $_idsrvurl

		## datapine url and token processing rei@18.6.2020
		if ($datapineiframeurl) {
			# dashboard: {
			#    url: 'https://[domain]/mvc/organizations/[organization-is]',
			#    ssoCallbackKey: 'itwo40'
			# },
			setobjectmember -in $content.dashboard -member url -value $datapineiframeurl
			setobjectmember -in $content.dashboard -member ssoCallbackKey -value 'itwo40'
		} else {
			setobjectmember -in $content.dashboard -member url -value ''
		}

		if ($usei18nOverrideJsonText -eq 1) {
			setobjectmember -in $content -member i18nvalue -value 'true'
		}

		<# support userlane detail see https://docs.userlane.com/en/articles/1864515-userlane-snippet-how-to-install-adjust-it#>
		# [optional] Please enter here your userlane user_id, i you enter the user_id, we will activate the userlane feature in iTWO4.0.
		if ($userlanePropertyId) {
			setobjectmember -in $content -member userlanePropertyId -value $userlanePropertyId
		} else {
			setobjectmember -in $content -member userlanePropertyId -value ''
		}

		if ($office365Tenant -and $office365ApplicationClientId -and $office365ApplicationSecret -and $officeViewerServerUrl) {
			Write-Host -ForegroundColor $colorinfo 'iTWO4.0 Office365 Integration enabled, set config.json parameters'
			Write-Host -ForegroundColor $colorinfo "  authority='$office365AuthorityUrl', msGraph='$office365MsgraphUrl',skype='$office365SkypeUrl',officeViewerServerUrl='$officeViewerServerUrl', tenant='$office365Tenant', clientId='$office365ApplicationClientId'"

			setobjectmember -in $content.aad -member authority -value $office365AuthorityUrl
			setobjectmember -in $content.aad -member tenant -value $office365Tenant
			setobjectmember -in $content.aad -member office365ClientId -value $office365ApplicationClientId
			setobjectmember -in $content.aad -member officeViewerServerUrl -value $officeViewerServerUrl
			setobjectmember -in $content.aad.resource -member msGraph -value $office365MsgraphUrl
			setobjectmember -in $content.aad.resource -member skype -value $office365SkypeUrl
		} else {
			setobjectmember -in $content.aad -member office365ClientId -value ''
		}
		#now save it
		#$content | Out-File -Encoding "utf8" $ConfigJsonPath
		$content | ConvertTo-Json -Depth 5 | Set-Content $ConfigJsonPath
	}
	<# config.json done #################################################################>
}

function ProcessNewFontendConfigJson {
	param (
		[boolean]$install,
		[string] $frontendFolder )

	if ($false -eq $install ) {
		return
 }
	$ConfigJsonPath = Join-Path $frontendFolder 'assets/config.json'
	if (Test-Path $ConfigJsonPath  ) {
		Write-Host "Process frontend assets/config.json file: $ConfigJsonPath" -ForegroundColor $colorimportant
		$content = (Get-Content $ConfigJsonPath -Raw) | ConvertFrom-Json

		SetObjectMember -in $content -member productversion -value $productversion
		SetObjectMember -in $content -member buildversion -value $buildversion
		SetObjectMember -in $content -member productdate -value $productdate
		SetObjectMember -in $content -member installationdate -value $installationdate
		SetObjectMember -in $content -member serverBaseUrl -value "$itwo40url/services/"

		$_idsrvurl = $identityServerUrl + '/core'
		SetObjectMember -in $content -member identityBaseUrl -value $_idsrvurl

		## datapine url processing
		if ($null -ne $content.dashboard) {
			if ($datapineiframeurl) {
				# dashboard: {
				#    url: 'https://[domain]/mvc/organizations/[organization-is]',
				#    ssoCallbackKey: 'itwo40'
				# },
				SetObjectMember -in $content.dashboard -member url -value $datapineiframeurl
				SetObjectMember -in $content.dashboard -member ssoCallbackKey -value 'itwo40'
			} else {
				SetObjectMember -in $content.dashboard -member url -value ''
			}
		}

		if ($usei18nOverrideJsonText -eq 1) {
			SetObjectMember -in $content -member i18nvalue -value 'true'
		}

		<# support userlane detail see https://docs.userlane.com/en/articles/1864515-userlane-snippet-how-to-install-adjust-it#>
		# [optional] Please enter here your userlane user_id, i you enter the user_id, we will activate the userlane feature in iTWO4.0.
		if ($userlanePropertyId) {
			SetObjectMember -in $content -member userlanePropertyId -value $userlanePropertyId
		} else {
			SetObjectMember -in $content -member userlanePropertyId -value ''
		}

		if ($null -ne $content.aad) {
			if ( $office365Tenant -and $office365ApplicationClientId -and $office365ApplicationSecret -and $officeViewerServerUrl) {
				Write-Host -ForegroundColor $colorinfo 'iTWO4.0 Office365 Integration enabled, set config.json parameters'
				Write-Host -ForegroundColor $colorinfo "  authority='$office365AuthorityUrl', msGraph='$office365MsgraphUrl',skype='$office365SkypeUrl',officeViewerServerUrl='$officeViewerServerUrl', tenant='$office365Tenant', clientId='$office365ApplicationClientId'"

				SetObjectMember -in $content.aad -member authority -value $office365AuthorityUrl
				SetObjectMember -in $content.aad -member tenant -value $office365Tenant
				SetObjectMember -in $content.aad -member office365ClientId -value $office365ApplicationClientId
				SetObjectMember -in $content.aad -member officeViewerServerUrl -value $officeViewerServerUrl
				SetObjectMember -in $content.aad.resource -member msGraph -value $office365MsgraphUrl
				SetObjectMember -in $content.aad.resource -member skype -value $office365SkypeUrl
			} else {
				SetObjectMember -in $content.aad -member office365ClientId -value ''
			}
		}

		#now save it
		#$content | Out-File -Encoding "utf8" $ConfigJsonPath
		$content | ConvertTo-Json -Depth 5 | Set-Content $ConfigJsonPath
	}
}

function ReplacePlaceHolder {
	param ([string]$in, [string]$member, [string]$value, [switch]$isbool)

	$token = '%' + $member + '%'
	if ($isbool.IsPresent) {
		$token = "'%" + $member + "%'"       # for bool we replace '' as well
	}
	$result = $in.replace($token, $value)
	return $result
}

function ProcessClientConfigJsTemplate {
	param (
		[boolean]$install,
		[string] $clientFolder )

	if ($false -eq $install ) {
		return
 }
	$ConfigTemplateJsPath = Join-Path $clientFolder 'config_template.js'
	$ConfigTargetJsPath = Join-Path $clientFolder 'config.js'
	if (Test-Path $ConfigTemplateJsPath  ) {
		Write-Host "Process Client config_template.json file: $ConfigTemplateJsPath" -ForegroundColor $colorimportant
		$content = (Get-Content $ConfigTemplateJsPath -Raw)

		$content = ReplacePlaceHolder -in $content -member 'productversion' -value $productversion
		$content = ReplacePlaceHolder -in $content -member 'buildversion' -value $buildversion
		$content = ReplacePlaceHolder -in $content -member 'productdate' -value $productdate
		$content = ReplacePlaceHolder -in $content -member 'installationdate' -value $installationdate
		$content = ReplacePlaceHolder -in $content -member 'additionalInfo' -value $additionalInfo     # rei@01.10.21 support additional info

		$_idsrvurl = $identityServerUrl + '/core/'
		$content = ReplacePlaceHolder -in $content -member 'identityBaseUrl' -value $_idsrvurl

		## datapine url and token processing rei@18.6.2020
		if (-not $datapinecallbackIdentifier) {
			$datapinecallbackIdentifier = 'itwo40'
  }
		if ($datapineiframeurl) {
			# dashboard: {url: 'https://itwobi-admin.itwo40.eu/mvc/organizations/CfbesENPXUQ90vLpUEYtDJ',
			#     ssoCallbackKey: 'itwo40' },
			$content = ReplacePlaceHolder -in $content -member 'dashboard.url' -value $datapineiframeurl
			$content = ReplacePlaceHolder -in $content -member 'dashboard.ssoCallbackKey' -value $datapinecallbackIdentifier
		} else {
			$content = ReplacePlaceHolder -in $content -member 'dashboard.url' -value '' # default
			$content = ReplacePlaceHolder -in $content -member 'dashboard.ssoCallbackKey' -value $datapinecallbackIdentifier # default
		}

		if ($usei18nOverrideJsonText -eq 1) {
			$content = ReplacePlaceHolder -in $content -member 'i18nCustom' -value 'true' -isbool
		} else {
			$content = ReplacePlaceHolder -in $content -member 'i18nCustom' -value 'false' -isbool // default
		}

		<# support userlane detail see https://docs.userlane.com/en/articles/1864515-userlane-snippet-how-to-install-adjust-it#>
		# [optional] Please enter here your userlane user_id, i you enter the user_id, we will activate the userlane feature in iTWO4.0.
		if ($userlanePropertyId) {
			$content = ReplacePlaceHolder -in $content -member 'userlanePropertyId' -value $userlanePropertyId
		} else {
			$content = ReplacePlaceHolder -in $content -member 'userlanePropertyId' -value ''
		}
		if ($office365Tenant -and $office365ApplicationClientId -and $office365ApplicationSecret -and $officeViewerServerUrl) {
			Write-Host -ForegroundColor $colorinfo 'iTWO4.0 Office365 Integration enabled, set config.json parameters'
			Write-Host -ForegroundColor $colorinfo "  authority='$office365AuthorityUrl', msGraph='$office365MsgraphUrl',skype='$office365SkypeUrl',officeViewerServerUrl='$officeViewerServerUrl', tenant='$office365Tenant', clientId='$office365ApplicationClientId'"

			$content = ReplacePlaceHolder -in $content -member 'aad.authority' -value $office365AuthorityUrl
			$content = ReplacePlaceHolder -in $content -member 'aad.tenant' -value $office365Tenant
			$content = ReplacePlaceHolder -in $content -member 'aad.office365ClientId' -value $office365ApplicationClientId
			$content = ReplacePlaceHolder -in $content -member 'aad.officeViewerServerUrl' -value $officeViewerServerUrl
			$content = ReplacePlaceHolder -in $content -member 'aad.resource.msGraph' -value $office365MsgraphUrl
			$content = ReplacePlaceHolder -in $content -member 'aad.resource.skype' -value $office365SkypeUrl
		} else {
			$content = ReplacePlaceHolder -in $content -member 'aad.authority' -value 'https://login.microsoftonline.com'
			$content = ReplacePlaceHolder -in $content -member 'aad.tenant' -value 'common'
			$content = ReplacePlaceHolder -in $content -member 'aad.office365ClientId' -value '{your key here}'
			$content = ReplacePlaceHolder -in $content -member 'aad.officeViewerServerUrl' -value $officeViewerServerUrl
			$content = ReplacePlaceHolder -in $content -member 'aad.resource.msGraph' -value 'https://graph.microsoft.com'
			$content = ReplacePlaceHolder -in $content -member 'aad.resource.skype' -value 'https://webdir.online.lync.com'
		}
		#now save it
		#$content | Out-File -Encoding "utf8" $ConfigJsonPath
		if (Test-Path $ConfigTargetJsPath) {
			Move-Item -Force $ConfigTargetJsPath ($ConfigTargetJsPath + '.sav')
		}
		$content | Out-File -Encoding 'utf8' $ConfigTargetJsPath
	}
	<# config.json done #################################################################>
}


<#
This method parsed the "to be registered renderserver list"  $renderClusterRegisterRenderServer
into the RenderCluster Section required xml <url> element list
#>
function ParseLogonName2JwtList {
	param (
		$logonName2JwtList = $null,
		$content
	)

	# check input parameters
	if (-not $logonName2JwtList) {
		return ''
 }
	if ($logonName2JwtList.GetType().Name -ne 'Object[]') {
		Write-Host -ForegroundColor $colorError 'Wrong parameter type ''$appsettingkeyvaluelist'$($logonName2JwtList.GetType().Name)
		return ''
	}
	if (-not $content) {
		Write-Host -ForegroundColor $colorError "Wrong parameter `$content not defined"
		return ''
	}

	Write-Host 'Process LogonName2JwtList for appsettings.json' -ForegroundColor $colorinfo
	# Get the content of the config file and cast it to XML and save a backup copy labeled .bak followed by the date
	foreach ($i in $logonName2JwtList) {
		#$item = '<url url="{0}" apikey="{1}" /> <!-- activate a specific render server (from config file) -->'
		Write-Host "Set AppSetting Element: Key=$($i.key) Value=$($i.value)" -ForegroundColor $colorInfo
		SetObjectMember -in $content.AppSettings -member $($i.key) -value $($i.value)
	}
}

function ProcessIdentityServerAppsettings {
	param ([boolean]$install,
		[string] $appsettingsfolder)

	if ($false -eq $install ) {
		return
 }

	# Get the content of the appsetting template json file and save it as appsettings.json file
	$appsettingtplfile = Join-Path $appsettingsfolder 'appsettings_template.json'
	$appsettingfileout = Join-Path $appsettingsfolder 'appsettings.json'

	Write-Host "Process IdentityServer config file to $appsettingfileout" -ForegroundColor $colorinfo

	$content = Get-Content $appsettingtplfile | ConvertFrom-Json

	$idpredirecturis = "$itwo40url/frontend/, $itwo40url/frontend/#/auth-callback, $itwo40url/client/, $itwo40url/client/#/callback, $itwo40url/portal/start/, $itwo40url/portal/start/#/callback"  # [calculated value] $idpredirecturis always required, without redirect urls ipd authentication never works.
	$openidsignedoutredirecturi = "$itwo40url/portal/start/"

	if ($null -eq $dbTrustServerCertificate) {
		$dbTrustServerCertificate = $true
	}

	## Connection String Calculation, based on template from appsettings template file
	if ($useAzureSslConnectionString) {
		Write-Host 'IdentityServer config using Azure SQL DB based Connectionstring'
		$connString = @($content.Connectionstrings.default_azuresqldb)
		$_connString = $connString -f $identityDBServer, $identityDBName, $identityDbUsername, $identityDbPassword
	} else {
		if ($identityTrustedlogin) {
			$connString = @($content.Connectionstrings.default_trusted)
			$_connString = $connString -f $identityDBServer, $identityDBName, $dbTrustServerCertificate
		} else {
			$connString = @($content.Connectionstrings.default_explicit)
			$_connString = $connString -f $identityDBServer, $identityDBName, $identityDbUsername, $identityDbPassword, $dbTrustServerCertificate
		}
	}
	SetObjectMember -in $content.Connectionstrings -member default -value $_connString

	if ($identityRefreshTokenEnabled) {
		$content.IdentityServerSetting.RefreshTokenEnabled = $identityRefreshTokenEnabled
		if ($identitySlidingRefreshTokenLifetimeinHour) {
			$content.IdentityServerSetting.SlidingRefreshTokenLifetimeinHour = $identitySlidingRefreshTokenLifetimeinHour
		}
		if ($identityAbsoluteRefreshTokenLifetimeinHour) {
			$content.IdentityServerSetting.AbsoluteRefreshTokenLifetimeinHour = $identityAbsoluteRefreshTokenLifetimeinHour
		}
	}
	if ($identityAccesstokenLifetimeInMin) {
		$content.IdentityServerSetting.AccesstokenLifetimeInMin = $identityAccesstokenLifetimeInMin
		$content.IdentityServerSetting.WebApiAccessTokenLifetimeinMin = $identityAccesstokenLifetimeInMin     # rei@20.10.21, same refreshtime like std accesstoken
	}
	if ($identityPortalAccesstokenLifetimeInMin) {
		$content.IdentityServerSetting.PortalAccesstokenLifetimeInMin = $identityPortalAccesstokenLifetimeInMin
	}
	$content.IdentityServerSetting.IdentityLogging = ($identityIdentityLogging -eq $true)
	if ($identityIdentityPiiLogging) {
		## rei@4.8.20 wrong checking of parameter fixed
		$content.IdentityServerSetting.IdentityPiiLogging = $true
	}
	## rei@20.02.2020, new since 4.0.0
	if (1 -eq $identityRunningLoadBalanced) {
		Write-Host 'IdentityServer is running in load balanced an environment - we save keys in database (new since 4.0.0).' -ForegroundColor $colorinfo
		$content.IdentityServerSetting.IsDataProtectionEnabled = $true
	}
	if ($true -eq $identityDisableHomePage) {
		$content.IdentityServerSetting.DisableHomePage = $true
	}

	# rei@10.11.2020 rel 4.1.A/3.2.G
	if ($false -eq $PortalSelfRegistrationAllowed) {
		$content.IdentityServerSetting.PortalSelfRegistrationAllowed = $false
 }
	if ($false -eq $PortalSelfRegistrationAllowed) {
		$content.IdentityServerSetting.PortalSelfRegistrationAllowed = $false
 }

	# rei@21.07.2020 fix issue with not setting if set to false
	if ( ($null -ne $content.IdentityServerSetting.SecurityStackTraceEnable) -and ($null -ne $securitystacktraceenable)) {
		$content.IdentityServerSetting.SecurityStackTraceEnable = $securitystacktraceenable
		Write-Host -ForegroundColor $colorInfo "SecurityStackTraceEnable: set to $($content.IdentityServerSetting.SecurityStackTraceEnable)"
	}
	# rei@04.08.2020
	if (($null -ne $content.IdentityServerSetting.GraceLoginAttempts) -and ($null -ne $identityGraceLoginAttempts)) {
		$content.IdentityServerSetting.GraceLoginAttempts = $identityGraceLoginAttempts
		Write-Host -ForegroundColor $colorInfo "Security-Option: GraceLoginAttempts set to $($content.IdentityServerSetting.GraceLoginAttempts)"
	}

	$content.IdentityServerSetting.AesKey = $appServerAesKey
	$content.IdentityServerSetting.Certificatex509Thumbprint = $identityServerCertificateFingerPrint
	$content.IdentityServerSetting.PublicOrigin = $identityPublicOrigin

	# rei@07.10.2022, support Clinetparameter RefreshTimer
	if ($identityRefreshClientParameterInterval -ne $null -and $identityRefreshClientParameterInterval -gt -1) {
		Write-Host "Identity Server is using Refreshing of Client Parameters with Refresh Timer:  $identityRefreshClientParameterInterval" -ForegroundColor $colorimportant
		$content.IdentityServerSetting.ClientParametersRefreshTimer = $identityRefreshClientParameterInterval
	}

	## idp settings
	$content.Idp.explicitLogon = ($idpExplicitLogon -eq $true)
	if ($idpredirecturis) {
		$content.Idp.redirecturis = $idpredirecturis
	}

	if ($identityProxyUrl) {
		Write-Host "Identity Server is using a Proxy Server: Url: $identityProxyUrl" -ForegroundColor $colorimportant
		$content.IdentityServerSetting.ProxyUrlwithPort = $identityProxyUrl
		if ($null -ne $identityproxybypasslist) {
			$jsonByPassListString = '["' + [system.String]::Join('","', $identityproxybypasslist) + '"]'
			$content.IdentityServerSetting.ProxyByPasslist = $identityproxybypasslist
			Write-Host "Identity Server is using a Proxy Server: ByPassList: $jsonByPassListString" -ForegroundColor $colorimportant
		}
		if ($identityProxyCredentialsUsername -and $identityProxyCredentialsPassword) {
			Write-Host "Identity Server is using a Proxy Server: Username: $identityProxyCredentialsUsername" -ForegroundColor $colorimportant
			$content.IdentityServerSetting.ProxyCredentials.UserName = $identityProxyCredentialsUsername
			$content.IdentityServerSetting.ProxyCredentials.PassWord = $identityProxyCredentialsPassword
		}
	}

	SetObjectMember -in $content.Idp -member googleclientid -value (iifElse $idpgoogleclientid '')
	SetObjectMember -in $content.Idp -member googleclientsecret -value (iifElse $idpgoogleclientsecret '')

	SetObjectMember -in $content.Idp -member openidclientid -value (iifElse $idpopenidclientid '')
	SetObjectMember -in $content.Idp -member openidclientsecret -value (iifElse $idpopenidclientsecret '')
	SetObjectMember -in $content.Idp -member openidauthority -value (iifElse $idpopenidauthority '')
	SetObjectMember -in $content.Idp -member openidlogoncaption -value (iifElse $idpopenidlogoncaption '')
	# new with 4.1.A/3.2.G
	SetObjectMember -in $content.Idp -member openidauthorizationcodeflow -value (iifElse $($idpopenidauthorizationcodeflow -eq 1) $false)
	SetObjectMember -in $content.Idp -member openidenableipdlogout -value (iifElse $($idpopenidenableipdlogout -eq 1) $false)
	SetObjectMember -in $content.Idp -member openidsignedoutredirecturi -value (iifElse $openidsignedoutredirecturi '')
	# new in 6.3.B
	SetObjectMember -in $content.Idp -member openidusepkce -value (iifElse $($idpopenidusepkce -eq 1) $false)
	SetObjectMember -in $content.Idp -member openiduseofflineaccessscope -value (iifElse $($idpopeniduseofflineaccessscope -eq 1) $false)
	SetObjectMember -in $content.Idp -member openiduserdefinedscopes -value (iifElse $idpopeniduserdefinedscopes '')

	SetObjectMember -in $content.Idp -member facebookappid -value (iifElse $idpfacebookappid '')
	SetObjectMember -in $content.Idp -member facebookappsecret -value (iifElse $idpfacebookappsecret '')

	SetObjectMember -in $content.Idp -member twitterconsumerkey -value (iifElse $idptwitterconsumerkey '')
	SetObjectMember -in $content.Idp -member twitterconsumersecret -value (iifElse $idptwitterconsumersecret '')

	SetObjectMember -in $content.Idp -member maccclientid -value (iifElse $idpmaccclientid '')
	SetObjectMember -in $content.Idp -member maccclientsecret -value (iifElse $idpmaccclientsecret '')

	SetObjectMember -in $content.Idp -member Azureadclientid -value (iifElse $idpAzureadclientid '')
	SetObjectMember -in $content.Idp -member AzureAdLogin -value (iifElse $idpAzureAdLogin '')

	## rei@20.2.2020 support multiple registered apps, azure secrects and tenant id
	SetObjectMember -in $content.Idp -member Azureadtenantid -value (iifElse $idpAzureadtenantid '')
	SetObjectMember -in $content.Idp -member AzureadPortalclientid -value (iifElse $idpAzureadportalclientid '')

	## rei@12.7.2022 support authorization code flow for AAD Standard & Portal logins
	SetObjectMember -in $content.Idp -member AzureAdClientSecret -value (iifElse $idpAzureadclientsecret '')
	SetObjectMember -in $content.Idp -member AzureadPortalsecret -value (iifElse $idpAzureadportalsecret '')

	SetObjectMember -in $content.Idp -member WsfedMetadataaddress -value (iifElse $idpWsfedMetadataaddress '')
	SetObjectMember -in $content.Idp -member WsfedWtrealm -value (iifElse $idpWsfedWtrealm '')

	## rei@20.9.2021 support MFA required for Azure Active Directory only, Experimental
	SetObjectMember -in $content.Idp -member RequireMfa -value $(IIf $requiremfa $true $false)

	# eventually we calculate it from url "rib_WsfedLogoutUrl": "https://adfs.rib-software.com/adfs/ls/?wa=wsignout1.0",
	if ([string]::IsNullOrWhiteSpace($idpWsfedLogoutUrl) -and (-not [string]::IsNullOrWhiteSpace($idpWsfedMetadataaddress))) {
		[System.Uri]$private:uri = $idpWsfedMetadataaddress
		$idpWsfedLogoutUrl = $private:uri.Scheme + '//' + $private:uri.Host + '/adfs/ls/?wa=wsignout1.0'
	}
	SetObjectMember -in $content.Idp -member WsfedLogoutUrl -value (iifElse $idpWsfedLogoutUrl '')
	SetObjectMember -in $content.Idp -member WsfedLogin -value (iifElse $idpWsfedLogin '')

	# Save it
	$content | ConvertTo-Json -Depth 5 | Set-Content $appsettingfileout
}

function ProcessAppServerAppSettings {
	param (
		[boolean]$install,
		[string] $servicesFolder,
		$apikey,
		$forRenderServer = $false,
		$forClusterServer = $false,
		$forImportScheduler = $false
	)

	if ($false -eq $install ) {
		return
 }

	<# 1. setting for the appserver dll.config #>
	################################################################################################
	if ($true) {
		#$webConfigPath = Join-Path $servicesFolder  "web.config"
		$ConfigFilePath = Join-Path $servicesFolder 'RIB.Visual.Platform.AppServer.Web.dll.config'
		Write-Host "Process Services $ConfigFilePath file." -ForegroundColor $colorinfo
		# Get the content of the config file and cast it to XML and save a backup copy labeled .bak followed by the date
		$xml = [xml](Get-Content $ConfigFilePath)
		$xml.Save($ConfigFilePath + '.sav')

		# @todo:rei $connectionString = ReadConnectionStringFromWebConfig
		#this was the trick I had been looking for
		$root = $xml.get_DocumentElement()
		# if ($configuration -eq "Release") {
		#     $root."system.web".compilation.debug = "false"
		# }


		<#local installation all components #>
		if ($renderingEnabled) {
			RemoveSectionForRenderingXml $xml # clean rendering Sections before continue

			if ($forImportScheduler) {
				ConfigureWebServerForRenderingXml -webcfgXml $xml -loglevel $renderingLogLevel -apikey $apikey -renderinglogfolder $renderingLogFolder `
					-createStorage $true -createCluster $false -createRenderserver $false `
					-storagetype $renderStorageType -storageParam $renderStorageParam -azureBlobContainer $renderStorageBlobContainer

				if ($renderStorageType -eq 'share') {
					$_res = CreateDirectory -folder $renderStorageParam -showInfo $true
				}
			}

			if (-not $rendererVersions) {
				$rendererVersions = ''
   }

			if ($forRenderServer) {
				if ($renderingServicesLocal) {
					$_storage = $true   # we need local storage for local render services
					$_storagetype = $renderStorageType
					$_storageParam = $renderStorageParam
					$_azureBlobContainer = $renderStorageBlobContainer
				} else {
					$_storage = $false   # we need local storage for local render services
					$_storagetype = $_storageParam = $_azureBlobContainer = $null
				}

				ConfigureWebServerForRenderingXml -webcfgXml $xml -apikey $apikey -loglevel $renderingLogLevel `
					-createStorage $_storage -createCluster $false -createRenderserver $true `
					-storagetype $_storagetype -storageParam $_storageParam -azureBlobContainer $_azureBlobContainer `
					-localPersistentFileRootFolder $renderingPersistentRootFolder  <# sqlite db are there stored#> `
					-renderserverurl $renderservicesUrl `
					-modelcache $rendererModelcache -modelcacheDisablecopying $renderServerModelcacheDisablecopying `
					-rendererversions $rendererVersions -hcsserviceport $hcsserviceport -rendererportsrange $rendererportsrange -rendererbinpath $rendererPhysicalPath `
					-renderinglogfolder $renderingLogFolder
				<#-renderclientenabled -renderserverenabled -brokerport <<< we take default values#>

				if ($renderingPersistentRootFolder) {
					$_res = CreateDirectory -folder $renderingPersistentRootFolder -showInfo $true
					<#check for old ports.slite db#>
					$_portsqllitepath = Join-Path $renderingPersistentRootFolder 'ports.sqlite'
					$_Res = DeleteFileIfExist -file $_portsqllitepath -showinfo $true
				}
			}


			if ($forClusterServer) {
				<# install render cluster controller #>
				ConfigureWebServerForRenderingXml -webcfgXml $xml -loglevel $renderingLogLevel -apikey $apikey `
					-createStorage $true -createCluster $true -createRenderserver $false `
					-storagetype $renderStorageType -storageParam $renderStorageParam -azureBlobContainer $renderStorageBlobContainer `
					-localPersistentFileRootFolder $renderingPersistentRootFolder  <# log and sqllite db are there stored #> `
					-renderserverurl $renderservicesUrl -remoteRenderClusterUrl $remoteRenderClusterUrl <# optional, we enable the cluster for self registration #> `
					-renderClusterProxyUrl $renderClusterProxyUrl  <#optional since 4.1 #> `
					-preRegisteredRenderservers $renderClusterRegisterRenderServer `
					-renderinglogfolder $renderingLogFolder
				<#-loglevel "warn" -renderclientenabled -renderserverenabled -brokerport <<< we take default values#>
				<# check storage destination and create if not already there #>
				if ($renderStorageType -eq 'share' -and $renderStorageParam) {
					$_res = CreateDirectory -folder $renderStorageParam -showInfo $true
				}
				if ($renderingPersistentRootFolder) {
					$_res = CreateDirectory -folder $renderingPersistentRootFolder -showInfo $true
					<#check for old ports.slite db#>
					$_portsqllitepath = Join-Path $renderingPersistentRootFolder 'ports.sqlite'
					$_Res = DeleteFileIfExist -file $_portsqllitepath -showinfo $true
				}
				if ($renderingLogFolder) {
					$_res = CreateDirectory -folder $renderingLogFolder -showInfo $true
				}
			}
		}
		# Save it
		$xml.Save($ConfigFilePath)
	}
	<# 2. appsettings.json #>
	if ($true) {
		<# .NET Core #>
		################################################################################################
		## IIS App-server Services web.config
		## .NET Core appsettings.json
		# Get the content of the appsetting template json file and save it as appsettings.json file
		$appsettingtplfile = Join-Path $servicesFolder 'appsettings_template.json'
		$appsettingfileout = Join-Path $servicesFolder 'appsettings.json'

		Write-Host "Process Services config file to $appsettingfileout" -ForegroundColor $colorinfo

		$content = Get-Content $appsettingtplfile | ConvertFrom-Json

		if ($null -eq $dbTrustServerCertificate) {
			$dbTrustServerCertificate = $true
		}

		## Connection String Calculation, based on template from appsettings template file
		if ($true) {
			if ($useAzureSslConnectionString) {
				Write-Host 'Services config using Azure SQL DB based Connectionstring'
				$connString = @($content.Connectionstrings.tpl_azuresqldb)
				$_connString = $connString -f $dbservermachinename, $dbname, $dbusername, $dbpassword
			} else {
				if ($dbtrustedlogin) {
					$connString = @($content.Connectionstrings.tpl_trusted)
					$_connString = $connString -f $dbservermachinename, $dbname, $dbTrustServerCertificate
				} else {
					$connString = @($content.Connectionstrings.tpl_explicit)
					$_connString = $connString -f $dbservermachinename, $dbname, $dbusername, $dbpassword, $dbTrustServerCertificate
				}
			}
			SetObjectMember -in $content.Connectionstrings -member default -value $_connString
		}
		# audit connection string settings
		if ($enableaudittrail) {
			if ($useAzureSslConnectionString) {
				Write-Host 'Auditting using Azure SQL DB based Connectionstring'
				$connString = @($content.Connectionstrings.tpl_azuresqldb)
				$_connString = $connString -f $dbservermachinename, $dbname, $audittraildbusername, $audittraildbpassword
			} else {
				if ($dbtrustedlogin) {
					Write-Host -ForegroundColor $colorinfo 'Auditting User with Trusted Login'
					$connString = @($content.Connectionstrings.tpl_trusted)
					$_connString = $connString -f $dbservermachinename, $dbname, $dbTrustServerCertificate
				} else {
					Write-Host -ForegroundColor $colorinfo "Auditting User with Login User '$audittraildbusername'"
					$connString = @($content.Connectionstrings.tpl_explicit)
					$_connString = $connString -f $dbservermachinename, $dbname, $audittraildbusername, $audittraildbpassword, $dbTrustServerCertificate
				}
			}
			SetObjectMember -in $content.Connectionstrings -member audittrail -value $_connString
		}

		# reporting user connection string
		if ($true) {
			if ($useAzureSslConnectionString) {
				Write-Host -ForegroundColor $colorinfo 'Reporting User using Azure SQL DB based Connectionstring'
				$connString = @($content.Connectionstrings.tpl_azuresqldb)
				$_connString = $connString -f $dbservermachinename, $dbname, $reportingdbusername, $reportingdbpassword
			} else {
				if ($dbtrustedlogin) {
					Write-Host -ForegroundColor $colorinfo 'Reporting User with Trusted Login'
					$connString = @($content.Connectionstrings.tpl_trusted)
					$_connString = $connString -f $dbservermachinename, $dbname, $dbTrustServerCertificate
				} else {
					Write-Host -ForegroundColor $colorinfo "Reporting User with Login User '$reportingdbusername'"
					$connString = @($content.Connectionstrings.tpl_explicit)
					$_connString = $connString -f $dbservermachinename, $dbname, $reportingdbusername, $reportingdbpassword, $dbTrustServerCertificate
				}
			}
			SetObjectMember -in $content.Connectionstrings -member reporting -value $_connString
		}
	}

	$idServerUrl = ($identityServerUrl + '/core/').toLower()

	SetObjectMember -in $content.AppSettings -member 'identity:authority' -value $idServerUrl
	SetObjectMember -in $content.AppSettings -member 'identity:x509Thumbprint' -value $identityServerCertificateFingerPrint
	SetObjectMember -in $content.AppSettings -member 'identity:x509SerialNumber' -value ''
	SetObjectMember -in $content.AppSettings -member 'reporting:reportBasePath' -value (Join-Path $FileServerRootUncPath 'reports'	)
	SetObjectMember -in $content.AppSettings -member 'reporting:outputPath' -value (Join-Path $FileServerRootUncPath 'downloads\reports'	)
	SetObjectMember -in $content.AppSettings -member 'fileserver:basePath' -value $FileServerRootUncPath
	SetObjectMember -in $content.AppSettings -member 'fileserver:downloadsPath' -value (Join-Path $FileServerRootUncPath 'downloads'	)
	SetObjectMember -in $content.AppSettings -member 'fileserver:uploadsPath' -value (Join-Path $FileServerRootUncPath 'uploads'	)

	SetObjectMember -in $content.AppSettings -member 'fileserver:cdnPath' -value (Join-Path $FileServerRootUncPath 'cdn')                    #"fileserver:cdnPath": "\\\\rib-s-itwo40-fs\\itwo40fs\\itwo40\\rib-s-sql-dev\\piTWO40rel4_1_0.core\\cdn",
	SetObjectMember -in $content.AppSettings -member 'fileserver:documentationPath' -value(Join-Path $FileServerRootUncPath 'documentation') #"fileserver:documentationPath": "\\\\rib-s-itwo40-fs\\itwo40fs\\itwo40\\rib-s-sql-dev\\piTWO40rel4_1_0.core\\documentation"

	SetObjectMember -in $content.AppSettings -member 'audittrailPath' -value (Join-Path $FileServerRootUncPath 'audittrail'	)
	SetObjectMember -in $content.AppSettings -member 'aes:key' -value $appServerAesKey
	## rei@23.6.18 added for license checking
	SetObjectMember -in $content.AppSettings -member 'license:subscriptionId' -value $license_subscriptionId
	SetObjectMember -in $content.AppSettings -member 'license:subscriptionSign' -value $license_subscriptionSign

	## rei@25.07.18 added missing system context setting
	## fixed kh@2018-08-06
	if ($null -eq $ctx_username) {
		$ctx_username = ''
 } # set default
	if ($ctx_username) {
		if ([string]::IsNullOrEmpty($ctx_username)) {
			Write-Host 'Web-server is using the application pool principal as system context user, make sure have entry in FRM_USER Table' -ForegroundColor DarkGreen
		} else {
			Write-Host "Web-server is using '$ctx_username' as system context user, make sure have entry in FRM_USER Table" -ForegroundColor DarkGreen
		}
	}
	SetObjectMember -in $content.AppSettings -member 'syscontext:userName' -value $ctx_username  # rei@14.6.2021

	SetObjectMember -in $content.AppSettings -member 'syscontext:disabled' -value (iif $sysContextDisabled 'true' 'false' )  # rei@24.5.2021

	SetObjectMember -in $content.AppSettings -member 'syscontext:signedInClientId' -value $ctx_signedInClientId
	SetObjectMember -in $content.AppSettings -member 'syscontext:clientId' -value $ctx_clientId
	SetObjectMember -in $content.AppSettings -member 'syscontext:permissionClientId' -value $ctx_permissionClientId
	SetObjectMember -in $content.AppSettings -member 'syscontext:permissionRoleId' -value $ctx_permissionRoleId

	## rei@16.08.2022 add cors development feature
	if ($corsenableforurls) {
		Write-Host -ForegroundColor $colorInfo "Development feature enable Cors for specific url enabled: $corsenableforurls"
		SetObjectMember -in $content.AppSettings -member 'cors:enableforurls' -value $corsenableforurls
	}

	## rei@03.02.22 support internal logging
	if ($internallogging) {
		Write-Host -ForegroundColor $colorInfo "Internal Logging is activated, set appsetting.json parameters: dbcontext:nlogtrace=$true, nlogcleaner:keepmessagesinminutes=$($internallogging.databasekeeprecords)"
		if ($internallogging.nlogtrace -eq $true) {
			SetObjectMember -in $content.AppSettings -member 'dbcontext:nlogtrace' -value $true
		}
		if ($internallogging.databasekeeprecords -ne $null) {
			SetObjectMember -in $content.AppSettings -member 'nlogcleaner:keepmessagesinminutes' -value $internallogging.databasekeeprecords
		}
	}

	## rei@16.10.18 config scheduler in web.config.
	if ($enableSchedulerConfig) {
		Write-Host 'Scheduler Activation and Polling Interval will be configured via web.config.' -ForegroundColor $colorinfo
		SetObjectMember -in $content.AppSettings -member 'scheduler:enabled' -value $schedulerEnabled
		SetObjectMember -in $content.AppSettings -member 'scheduler:pollingInterval' -value $schedulerPollingInterval
		SetObjectMember -in $content.AppSettings -member 'scheduler:parallelJobs' -value $schedulerParallelJobs
		SetObjectMember -in $content.AppSettings -member 'scheduler:executionGroups' -value $schedulerExecutionGroup
		ParseSchedulerApplicationIdToExecutionGroup -content $content
	}

	## rei@15.07.19 switch to new rendering engine
	if ($true <#$useRendingVersion -eq 2#>) {
		Write-Host "You're using the new Rendering Engine." -ForegroundColor $colorinfo
		SetObjectMember -in $content.AppSettings -member 'viewer:hoops-backend-version' -value '2'
		SetObjectMember -in $content.AppSettings -member 'model-converter:keep-temp-files' -value 'true'
		## rei@07.10.2022 support model-converter:fontpath used while model conversion (wde,ige) 2DModels
		SetObjectMember -in $content.AppSettings -member 'model-converter:fontpath' -value (Join-Path $FileServerRootUncPath 'fonts\model')
	}

	## WebApiHelp enable / disable
	if ($enableWebApihelp -and ($null -ne $WebApiFilter)) {
		Write-Host "Public WebApiHelp is enabled with custom filter '$WebApiFilter'" -ForegroundColor $colorinfo
		SetObjectMember -in $content.AppSettings -member 'webapihelp:moduleFilter' -value $WebApiFilter
	}
	if (-not $enableWebApihelp) {
		Write-Host 'Public WebApiHelp is disabled' -ForegroundColor $colorinfo
		SetObjectMember -in $content.AppSettings -member 'webapihelp:moduleFilter' -value 'yxz-disabled'
	}

	<# model-logcollect:accesskey required here, if not specified we must reset web.config entry#>
	Write-Host -ForegroundColor $colorinfo "Render Cluster Admin Interface: model-logcollect:accesskey is set to '$renderLogCollectAccessKey'"
	SetObjectMember -in $content.AppSettings -member 'model-logcollect:accesskey' -value (iif $renderLogCollectAccessKey $renderLogCollectAccessKey '')

	if ($renderingEnabled -or $standAloneSchedulerschedulerEnabled) {
		# enable / disable render testmodel
		SetObjectMember -in $content.AppSettings -member 'model-testmodel:enabled' -value (iif $renderingTestsiteDisabled 'false' 'true')

		# only when we have a local rendering service with scheduler running we set the convert parameters
		if (($renderingLocal -or $renderingModelConverterLocal -or $schedulerEnabled -or $standAloneSchedulerschedulerEnabled) -and $rendererModelcache) {
			$_res = CreateDirectory (Join-Path $rendererModelcache 'json_data') -showInfo $true
			$_res = CreateDirectory (Join-Path $rendererModelcache 'model_data') -showInfo $true
			$_res = CreateDirectory (Join-Path $rendererModelcache 'tmp_data') -showInfo $true
			SetObjectMember -in $content.AppSettings -member 'model-content:path' -value $rendererModelcache
		}

		<# model converter definitions ---------------------------------------- #>
		$_deletescfiles = if ($renderingModelConverterLocal) {
			'true'
  } else {
			'false'
  }
		SetObjectMember -in $content.AppSettings -member 'model-converter:deletescfiles' -value $_deletescfiles
		SetObjectMember -in $content.AppSettings -member 'model-converter:sourcesdestdir' -value (Join-Path $FileServerRootUncPath '2qmodels')
		$_res = CreateDirectory (Join-Path $FileServerRootUncPath '2qmodels')

		if ($renderStorageType -eq 'blobStorage') {
			# we deal with Azure Blobstorage, complete connection string defined here
			SetObjectMember -in $content.AppSettings -member 'model-converter:scdestdir' -value '' <# we clean and override it later #>
			SetObjectMember -in $content.AppSettings -member 'model-converter:scdestblobstorage' -value $renderStorageParam

			if (-not [string]::IsNullOrWhiteSpace($renderStorageBlobContainer)) {
				# new since rel5.0.0
				SetObjectMember -in $content.AppSettings -member 'model-converter:blobstoragecontainer' -value $renderStorageBlobContainer
			}
		} else {
			# in case of: http, share
			$_renderStorageParam = $renderStorageParam
			if ($renderingLocal -or -not $renderStorageParam) {
				$_renderStorageParam = ''
   }
			SetObjectMember -in $content.AppSettings -member 'model-converter:scdestdir' -value $_renderStorageParam
			SetObjectMember -in $content.AppSettings -member 'model-converter:scdestblobstorage' -value '' <# we clean and override it later #>
		}
	}

	SetObjectMember -in $content.AppSettings -member 'exception:stacktrace' -value (iif $securitystacktraceenable 'true' 'false')
	#ldap validation rei@20.1.22
	SetObjectMember -in $content.AppSettings -member 'ldap:allowedDomains' -value (iif $securityLdapDomainList $securityLdapDomainList '')

	#datapine rei@18.6.2020
	SetObjectMember -in $content.AppSettings -member 'datapine:sso-secret' -value (iif $datapinessosecret $datapinessosecret '')
	SetObjectMember -in $content.AppSettings -member 'datapine:instance-id' -value (iif $datapineinstanceId $datapineinstanceId '')

	#wde-viewer 2d-models path rei@23.6.2020
	$_wdepath = (iif $wde2dmodelsDataPath $wde2dmodelsDataPath $(Join-Path $FileServerRootUncPath 'filearchives\wde2dmodels'))
	Write-Host -ForegroundColor $colorinfo "2D Model WDE Path set to: $_wdepath"
	SetObjectMember -in $content.AppSettings -member 'wde:dataPath' -value $_wdepath
	$_res = CreateDirectory -folder $_wdepath

	#ige-viewer 2d-models path rei@22.06.2021
	$_igepath = (iif $ige2dmodelsDataPath $ige2dmodelsDataPath $(Join-Path $FileServerRootUncPath 'filearchives\2dmodels\ige'))
	Write-Host -ForegroundColor $colorinfo "2D Model IGE Path set to: $_igepath"
	SetObjectMember -in $content.AppSettings -member 'ige:dataPath' -value $_igepath
	$_res = CreateDirectory -folder $_igepath

	<#-------------------------------------------------#>
	$serverurlExt = ''
	if ($isRenderserviceInstallation -or $isSchedulerInstallation) {
		$serverurlExt = (iif $isRenderserviceInstallation '/renderservices' '/scheduler')
	}
	SetObjectMember -in $content.AppSettings -member 'itwo40:serverurl' -value "$itwo40url$serverurlExt"              <# serverurl rei@14.06.2021 #>
	SetObjectMember -in $content.AppSettings -member 'itwo40:sitename' -value "$websiteroot"              <# serverurl rei@10.06.2022 #>
	SetObjectMember -in $content.AppSettings -member 'itwo40:localserverurl' -value "$itwo40localurl$serverurlExt"    <# serverurl rei@14.06.2021 #>

	SetObjectMember -in $content.AppSettings -member 'itwo40:productversion' -value $productVersion
	SetObjectMember -in $content.AppSettings -member 'itwo40:productdate' -value $productdate
	SetObjectMember -in $content.AppSettings -member 'itwo40:buildversion' -value $buildversion
	SetObjectMember -in $content.AppSettings -member 'itwo40:installationdate' -value $installationdate

	if ($aadSynchronizationactive -and $aadSynchronizationTenantId -and $aadSynchronizationApplicationId -and $aadSynchronizationClientSecret) {
		<# new with rel5.0.0 #>
		SetObjectMember -in $content.AppSettings -member 'aad:synchronizationactive' -value (iif $aadSynchronizationactive $true $false )
		SetObjectMember -in $content.AppSettings -member 'aad:synchronizationtenantid' -value $aadSynchronizationTenantId
		SetObjectMember -in $content.AppSettings -member 'aad:synchronizationapplicationid' -value $aadSynchronizationApplicationId
		SetObjectMember -in $content.AppSettings -member 'aad:synchronizationclientsecret' -value $aadSynchronizationClientSecret
	}

	<# AI Server Settings added 24.3.22 #>
	if ($aiServerBaseUrl -and $aiServerType -and ($aiStoragePath -or $aiStorageConnection) ) {
		<# new with rel5.2.0 #>
		Write-Host -ForegroundColor $colorinfo 'iTWO4.0 AI Server Configuration found'
		Write-Host -ForegroundColor $colorinfo " BaseUrl=$aiServerBaseUrl Type=$aiServerType, storageType=$aiStorageType"
		SetObjectMember -in $content.AppSettings -member 'ai:server:baseUrl' -value $aiServerBaseUrl
		SetObjectMember -in $content.AppSettings -member 'ai:server:type' -value $aiServerType
		SetObjectMember -in $content.AppSettings -member 'ai:storage:type' -value $aiStorageType
		SetObjectMember -in $content.AppSettings -member 'ai:storage:connection' -value $aiStorageConnection
		SetObjectMember -in $content.AppSettings -member 'ai:storage:containerName' -value $aiStorageContainerName
		SetObjectMember -in $content.AppSettings -member 'ai:storage:path' -value $aiStoragePath
		SetObjectMember -in $content.AppSettings -member 'ai:storage:shareName' -value $aiStorageShareName
	}

	<# Office 365 Integration application settings value maintained 22.6.2020#>
	if ($office365TenantId -and $office365ApplicationClientId -and $office365ApplicationSecret) {
		Write-Host -ForegroundColor $colorinfo 'iTWO4.0 Office365 Integration enabled, set web.config parameters'
		Write-Host -ForegroundColor $colorinfo "  aad:authority='$office365AuthorityUrl', msGraph='$office365MsgraphUrl', tenant='$office365Tenant', tenantId='$office365TenantId', clientId='$office365ApplicationClientId'"

		SetObjectMember -in $content.AppSettings -member 'aad:authority' -value $office365AuthorityUrl  # fixed value
		SetObjectMember -in $content.AppSettings -member 'aad:tenant' -value $office365Tenant   # attention its the tenant, might have in some cases same value as tenantid
		SetObjectMember -in $content.AppSettings -member 'aad:tenantId' -value $office365TenantId # attention its the tenant, might have in some cases same value as tenantid
		SetObjectMember -in $content.AppSettings -member 'aad:office365ClientId' -value $office365ApplicationClientId
		SetObjectMember -in $content.AppSettings -member 'aad:resource:msGraph' -value $office365MsgraphUrl   # fixed value
		SetObjectMember -in $content.AppSettings -member 'aad:office365ClientSecret' -value $office365ApplicationSecret
	}

	<# rei@28.09.21 support  chatbot entries ##>
	if ($chatbotLuisAuthoringKey -and $chatbotLuisPredictKey -and $chatbotLuisApiUrl ) {
		Write-Host -ForegroundColor $colorInfo "iTWO4.0 chatbot luis services enabled using $chatbotLuisApiUrl services"
		SetObjectMember -in $content.AppSettings -member 'chatbot:luisAuthoringKey' -value $chatbotLuisAuthoringKey
		SetObjectMember -in $content.AppSettings -member 'chatbot:luisPredictKey' -value $chatbotLuisPredictKey
		SetObjectMember -in $content.AppSettings -member 'chatbot:luisApi' -value $chatbotLuisApiUrl
	}

	<# rei@07.02.22 bosch idm active? #>
	if ($boschidmactive) {
		Write-Host -ForegroundColor $colorInfo "iTWO4.0 BoschIDM is activated. Using apikey: $boschidmapikey"
		SetObjectMember -in $content.AppSettings -member 'usermanagement:boschapi' -value $boschidmapikey
	}

	# rei@23.09.2021 webapi-serialization handler
	# "webApi:suppressDeserializationError": false,
	if ($null -ne $webapiSuppressDeserializationError) {
		SetObjectMember -in $content.AppSettings -member 'webApi:suppressDeserializationError' -value $(iif $webapiSuppressDeserializationError $true $false)
	} else {
		<# by default we suppress web-api serialization issues #>
		SetObjectMember -in $content.AppSettings -member 'webApi:suppressDeserializationError' -value $true
	}

	if ($logonname2JwtList) {
		ParseLogonName2JwtList -logonName2JwtList $logonname2JwtList -content $content
	}

	SetObjectMember -in $content.AppSettings -member 'identity:providerPlugins' -value (iifElse $identityProviderPlugins '')

	# apply ApplicationInsights connection string
	if (($null -ne $applicationInsightsConnectionString) -and ($applicationInsightsConnectionString.length -gt 0)) {
		SetObjectMember -in $content.ApplicationInsights -member 'ConnectionString' -value $applicationInsightsConnectionString
	}

	# Web api callback settings
	if ($apiCallbackEnabled -and $apiCallbackMaximumLifetime -and $apiCallbackFailedRequestCount) {
		SetObjectMember -in $content.AppSettings -member 'apiCallbackEnabled' -value $apiCallbackEnabled
		SetObjectMember -in $content.AppSettings -member 'apiCallbackMaximumLifetimeDays' -value $apiCallbackMaximumLifetimeDays
		SetObjectMember -in $content.AppSettings -member 'apiCallbackFailedRequestCount' -value $apiCallbackFailedRequestCount
	}


	# Save it
	$content | ConvertTo-Json -Depth 5 | Set-Content $appsettingfileout

	# rei@11.02.2022 internal logging support
	ConfigureNlogConfig -targetfolder $servicesFolder
}

function ConfigureBoschIdmAppSettings ($servicestargetfolder) {
	<# 1. setting for the appserver dll.config #>
	################################################################################################
	if ($boschidmactive) {
		$ConfigFilePath = Join-Path $servicestargetfolder 'usermanagement.boschidm\web.config'
		Write-Host "Process BoschIDM $ConfigFilePath file." -ForegroundColor $colorinfo
		# Get the content of the config file and cast it to XML and save a backup copy labeled .bak followed by the date
		$xml = [xml](Get-Content $ConfigFilePath)
		$xml.Save($ConfigFilePath + '.sav')

		$root = $xml.get_DocumentElement()
		$apps = $root.appSettings.add
		SetAppKeyValue -appRoot $root.appSettings -appSettings $apps -appkey 'ApiKey' -appNewValue $boschidmapikey
		SetAppKeyValue -appRoot $root.appSettings -appSettings $apps -appkey 'BaseUri' -appNewValue $itwo40url
		# Save it
		$xml.Save($ConfigFilePath)
	}

}


function ConfigureNlogConfig ($targetfolder) {
	<# 1. setting for the nlog.config file #>
	################################################################################################
	## rei@03.02.22 support internal logging
	if ($internallogging) {
		Write-Host -ForegroundColor $colorInfo 'Internal Logging is activated, set appsetting.json parameters'

		$ConfigFilePath = Join-Path $targetfolder 'nlog.config'
		Write-Host "Process NLOG.Config $ConfigFilePath file." -ForegroundColor $colorinfo
		# Get the content of the config file and cast it to XML and save a backup copy labeled .bak followed by the date
		[xml] $xml = [xml](Get-Content $ConfigFilePath)
		$xml.Save($ConfigFilePath + '.sav')
		$root = $xml.DocumentElement
		$databaseLogger = $root.rules.logger | Where-Object { $_.writeTo -eq 'database' -and $_.name -eq '*' }
		$applicationLogger = $root.rules.logger | Where-Object { $_.writeTo -eq 'application' -and $_.name -eq '*' }
		$eventLogLogger = $root.rules.logger | Where-Object { $_.writeTo -eq 'eventlog' -and $_.name -eq '*' }

		if ($internallogging.eventLog -and $eventLogLogger) {
			$eventLogLogger.minlevel = $internallogging.eventLog
			$eventLogLogger.enabled = if ($internallogging.eventLog -eq 'off' ) {
				'false'
   } else {
				'true'
   }

		}
		if ($internallogging.databaseLog -and $databaseLogger) {
			$databaseLogger.minlevel = $internallogging.databaseLog
			$databaseLogger.enabled = if ($internallogging.databaseLog -eq 'off' ) {
				'false'
   } else {
				'true'
   }

			if ($internallogging.databaseLog -ne 'off' ) {

				if (-not $internallogging.databaseconnectstr) {
					Write-Host -ForegroundColor $colorerror 'Nlog Database target requires Connectionstring which is missing. Please fix!'
				} else {
					$databaseTarget = $root.targets.target | Where-Object { $_.name -eq 'database' -and $_.type -eq 'Database' }
					$databaseTarget.connectionString = $internallogging.databaseconnectstr
					<# set database connection string#>
				}
			}
		}
		if ($internallogging.applicationLog -and $applicationLogger) {
			$applicationLogger.minlevel = $internallogging.applicationLog
			$applicationLogger.enabled = if ($internallogging.applicationLog -eq 'off' ) {
				'false'
   } else {
				'true'
   }
		}
		$xml.Save($ConfigFilePath)
	}
}
# $colorinfo = "yellow"
# $colorerror = "red"
# $internallogging = @{
#     nlogtrace          = $true;
#     eventLog           = "off"; #trace|debug|info|off"    default: off
#     applicationLog     = "off"; #trace|debug|info|off"  default: debug
#     databaseLog        = "info"; #trace|debug|info|off"  default: off
#     databasedefault    = $true;
#     databaseconnectstr = "Server=.;Initial Catalog=itwo40_600;trusted_connection=yes;Connection Timeout=600;";
# }
# ConfigureNlogConfig -targetfolder "D:\rib_dev\dev\watrunk\BinPool\Debug.Server"