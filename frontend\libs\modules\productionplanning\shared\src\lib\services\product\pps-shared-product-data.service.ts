/*
 * Copyright(c) RIB Software GmbH
 */

import { get } from 'lodash';
import {
	DataServiceFlatNode, DataServiceFlatRoot,
	IDataServiceChildRoleOptions,
	IDataServiceEndPointOptions,
	IDataServiceOptions, IEntityProcessor, IReadOnlyField, ServiceRole
} from '@libs/platform/data-access';
import { CompleteIdentification, IEntityIdentification, PlatformTranslateService } from '@libs/platform/common';
import { IPpsProductEntityGenerated } from '../../model/product/product-entity-generated.interface';
import { inject } from '@angular/core';

/**
 *  For UnassignBundle, Bundle, Formwork, ProductionSet, ProductTemplate
 * */
export class PpsSharedProductDataService<T extends IPpsProductEntityGenerated, U extends CompleteIdentification<T>, PT extends IEntityIdentification, PU extends CompleteIdentification<PT>>
	extends DataServiceFlatNode<T, U, PT, PU> {

	private translateService: PlatformTranslateService = inject(PlatformTranslateService);

	private readonly productCodeInitializationProcessor: IEntityProcessor<T> = {
		process: (item: T) => {
			if (item.Version === 0) {
				item.Code = this.translateService.instant('cloud.common.isGenerated').text;
			}
			const readonlyFields: IReadOnlyField<T>[] = [{ field: 'Code', readOnly: item.Version === 0 }];
			this.setEntityReadOnlyFields(item as T, readonlyFields);
		},
		revertProcess: function (toProcess: T): void {
			throw new Error('Function not implemented.');
		}
	};

	public constructor(
		private parentService: DataServiceFlatRoot<PT, PU>,
		protected config: {
			apiUrl?: string,
			itemName?: string,
			filter?: string,
			PKey1?: string
		}
	) {
		config.apiUrl = config.apiUrl || 'productionplanning/common/product';
		config.itemName = config.itemName || 'Product';
		const options: IDataServiceOptions<T> = {
			apiUrl: config.apiUrl,
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'customlistbyforeignkey',
				usePost: false
			},
			createInfo: <IDataServiceEndPointOptions>{
				endPoint: 'create',
				usePost: true,
				prepareParam: ident => {
					const PKey1 = get(this.getSelectedParent(), config.PKey1 || '');
					return {
						Id: this.getSelectedParent()?.Id,
						PKey1: PKey1
					};
				}
			},
			roleInfo: <IDataServiceChildRoleOptions<T, PT, PU>>{
				role: ServiceRole.Node,
				itemName: config.itemName,
				parent: parentService
			}
		};
		super(options);
		this.processor.addProcessor([this.productCodeInitializationProcessor]);
	}

	public override registerByMethod(): boolean {
		return true;
	}

	protected override provideLoadPayload(): object {
		const mainItemId = this.getSelectedParent()?.Id || -1;
		if (this.config.filter !== '') {
			return { foreignKey: this.config.filter, mainItemId: mainItemId };
		} else {
			throw new Error('There should be a selected parent to load the corresponding product data');
		}
	}

	// // protected override onCreateSucceeded(created: object): T {
	// protected override onCreateSucceeded(created: IPpsProductEntityGenerated): T {
	// 	created.Code = this.translateService.instant('productionplanning.common.product.code');
	// 	return created as T;
	// }
}
