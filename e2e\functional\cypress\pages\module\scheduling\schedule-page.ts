/// <reference types="cypress" />
import { _common, _mainView, _modalView, _validate } from "cypress/pages";
import { btn, app, cnt, tile, commonLocators, sidebar } from "cypress/locators";
import { DataCells } from "cypress/pages/interfaces";
import { data } from "cypress/types/jquery";

var scheduleCode: string;
var sch_qty;
var wip_qty;
var lineitem_qty;
export class SchedulePage {

  enterRecord_toCreateSchedules(container_UUID:string,data: DataCells) {
    if (data[app.GridCells.DESCRIPTION_INFO]) {
      _common.enterRecord_inNewRow(container_UUID, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, data[app.GridCells.DESCRIPTION_INFO]);
    } 
    if (data[app.GridCells.CODE]) {
      _mainView.findModuleClientArea()
               .findAndShowContainer(container_UUID)
               .findGrid()
               .findActiveRow()
               .getCell(app.GridCells.CODE)
               .wrapElements()
               .then(()=>{
                  cy.get("body")
                    .then(($body) => {
                      if ($body.find(`.column-id_${app.GridCells.CODE} [class="invalid-cell"]`).length > 0) {
                        _common.enterRecord_inNewRow(container_UUID, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, data[app.GridCells.CODE]);
                      }
                    });
               })
               .then(()=>{
                  _common.select_activeRowInContainer(container_UUID)
                  _common.waitForLoaderToDisappear()
               })
    }
 
    if (data[app.GridCells.CALENDAR_FK]) {
		  _common.edit_dropdownCellWithInput(container_UUID,app.GridCells.CALENDAR_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,data[app.GridCells.CALENDAR_FK])
		}

    if (data[app.GridCells.PERFORMANCE_SHEET]) {
      _common.edit_dropdownCellWithCaret(container_UUID,app.GridCells.PERFORMANCE_SHEET,commonLocators.CommonKeys.LIST,data[app.GridCells.PERFORMANCE_SHEET])
		}
    if (data[app.GridCells.CODE_FORMATE_FK]) {
      _common.edit_dropdownCellWithInput(container_UUID,app.GridCells.CODE_FORMATE_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,data[app.GridCells.CODE_FORMATE_FK])
		}
    if (data[app.GridCells.TARGET_START]) {
      _common.enterRecord_inNewRow(container_UUID,app.GridCells.TARGET_START,app.InputFields.INPUT_GROUP_CONTENT,data[app.GridCells.TARGET_START])
    }

    if (data[app.GridCells.TARGET_END]) {
      _common.enterRecord_inNewRow(container_UUID,app.GridCells.TARGET_END,app.InputFields.INPUT_GROUP_CONTENT,data[app.GridCells.TARGET_END])
    }
    cy.SAVE();

  }
    
  enterRecord_toCreateActivityStructure(description: string,quantity: string,uom: string) {
    cy.get("body").then(($body) => {
      if ($body.find(".cid_"+cnt.uuid.ACTIVITY_STRUCTURE+" .column-id_"+app.GridCells.TREE).length > 0) {
        _mainView.findModuleClientArea()
                 .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                 .findGrid()
                 .getCell(app.GridCells.TREE)
                 .clickIn();
        _common.create_newSubRecord(cnt.uuid.ACTIVITY_STRUCTURE);
        _common.waitForLoaderToDisappear()
        _mainView.findModuleClientArea()
                 .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                 .findGrid()
                 .findActiveRow()
                 .findCell(app.GridCells.QUANTITY_SMALL)
                 .clickIn();
        _mainView.findModuleClientArea()
                 .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                 .findGrid()
                 .findActiveRow()
                 .findCell(app.GridCells.QUANTITY_SMALL)
                 .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
                 .type(quantity, { force: true });
        _mainView.findModuleClientArea()
                 .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                 .findGrid()
                 .findActiveRow()
                 .findCell(app.GridCells.DESCRIPTION)
                 .findTextInput(app.InputFields.DOMAIN_TYPE_COMMENT)
                 .type(description);
        _mainView.findModuleClientArea()
                 .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                 .findGrid()
                 .findActiveRow()
                 .findCell(app.GridCells.QUANTITY_UOM_FK)
                 .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
                 .type(uom, { force: true })
                 .then(()=>{
                    _mainView.select_popupItem("grid", uom);
                 })
        cy.SAVE();
      }else{
        _common.create_newRecord(cnt.uuid.ACTIVITY_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.create_newSubRecord(cnt.uuid.ACTIVITY_STRUCTURE);
        _common.waitForLoaderToDisappear()
        _mainView.findModuleClientArea()
                 .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                 .findGrid()
                 .findActiveRow()
                 .findCell(app.GridCells.QUANTITY_SMALL)
                 .clickIn();
        _mainView.findModuleClientArea()
                 .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                 .findGrid()
                 .findActiveRow()
                 .findCell(app.GridCells.QUANTITY_SMALL)
                 .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
                 .type(quantity, { force: true });
        _mainView.findModuleClientArea()
                 .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                 .findGrid()
                 .findActiveRow()
                 .findCell(app.GridCells.DESCRIPTION)
                 .findTextInput(app.InputFields.DOMAIN_TYPE_COMMENT)
                 .type(description);
        _mainView.findModuleClientArea()
                 .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                 .findGrid()
                 .findActiveRow()
                 .findCell(app.GridCells.QUANTITY_UOM_FK)
                 .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
                 .type(uom, { force: true })
                 .then(()=>{
                    _mainView.select_popupItem("grid", uom);
                 })
        cy.SAVE();     
      }
    });
  }

  get_Qty_FromProgressReportHistory() {
    _mainView
      .findModuleClientArea()
      .findAndShowContainer(cnt.uuid.PROGRESS_REPORT_HISTORY)
      .findGrid()
      .findActiveRow()
      .getCell(app.GridCells.QUANTITY_SMALL)
      .wrapElements()
      .eq(0)
      .invoke("text")
      .then((qty) => {
        sch_qty = qty;
        console.log(sch_qty);
      });
  }

 

  
  enterRecord_ToActivityAndGANTTGrid(container_UUID:string,data: DataCells) {
    if (data[app.GridCells.CODE]) {
      cy.wait(1000)
      _mainView.findModuleClientArea()
               .findAndShowContainer(container_UUID)
               .findGrid()
               .findActiveRow()
               .findCell(app.GridCells.CODE)
               .findTextInput(app.InputFields.DOMAIN_TYPE_CODE)
               .invoke("val")
               .then(function (codeVal: string) {
                if (codeVal == "") {
                  _common.enterRecord_inNewRow(container_UUID, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, data[app.GridCells.CODE]);
                  Cypress.env("SCH_CODE", data[app.GridCells.CODE])
                }else{
                Cypress.env("SCH_CODE", codeVal)
              }
              });
}
if (data[app.GridCells.DESCRIPTION]) {
  _common.enterRecord_inNewRow(container_UUID, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_COMMENT, data[app.GridCells.DESCRIPTION]);
}
if (data[app.GridCells.QUANTITY_SMALL]) {
  _common.enterRecord_inNewRow(container_UUID, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, data[app.GridCells.QUANTITY_SMALL]);
}
if (data[app.GridCells.QUANTITY_UOM_FK]) {
  _common.edit_dropdownCellWithCaret(container_UUID,app.GridCells.QUANTITY_UOM_FK,commonLocators.CommonKeys.LIST,data[app.GridCells.QUANTITY_UOM_FK])
}
if (data[app.GridCells.PLANNED_START]) {
  _common.enterRecord_inNewRow(container_UUID,app.GridCells.PLANNED_START,app.InputFields.INPUT_GROUP_CONTENT,data[app.GridCells.PLANNED_START])
}
if (data[app.GridCells.PLANNED_FINISH]) {
  _common.enterRecord_inNewRow(container_UUID,app.GridCells.PLANNED_FINISH,app.InputFields.INPUT_GROUP_CONTENT,data[app.GridCells.PLANNED_FINISH])
}
if (data[app.GridCells.PLANNED_DURATION]) {
  _common.enterRecord_inNewRow(container_UUID, app.GridCells.PLANNED_DURATION, app.InputFields.INPUT_GROUP_CONTENT, data[app.GridCells.PLANNED_DURATION]);
}
//! This piece of code will enter new estimate code when the duplicate estimate code added.
cy.get("body")
  .then(($body) => {
    if ($body.find("[class*='invalid-cell']").length > 0) {
      let schCode=data[app.GridCells.CODE] + Cypress._.random(0, 100);
      _common.enterRecord_inNewRow(cnt.uuid.SCHEDULES, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, schCode);
      Cypress.env("SCH_CODE", schCode)
    }
  });      
} 

/*
   * This is used to assign assembly template from look-up modal
   * Updated Date: 2/12/2024
   * Author : Anupama G
   */


  generate_activityStructureRecord_byWizard(data: DataCells) {
    cy.wait(2000)
    _modalView.findModal()
      .wrapElements()
      .contains(`${commonLocators.CommonElements.NAV_TABS}`, data[commonLocators.CommonElements.NAV_TABS])
      .click({ force: true })
      .then(() => {
        if (data[commonLocators.CommonElements.NAV_TABS] === commonLocators.CommonLabels.GENERATE_ACTIVITIES) {
          _modalView.findModal()
            .findRadio_byLabel_InModal(data[commonLocators.CommonKeys.LABEL], commonLocators.CommonKeys.RADIO, data[commonLocators.CommonKeys.RADIO_INDEX], app.InputFields.DOMAIN_TYPE_RADIO)

          if (data[commonLocators.CommonKeys.LABEL] === commonLocators.CommonLabels.GENERATE_ACTIVITIES_FROM_ONE_ACTIVE_ESTIMATE) {
            _modalView.findModal()
              .findDropdownInputField_insideModal_byClass(`${app.ModalInputFields.ESTIMATE_FK}`)
              .clear()
              .type(data[commonLocators.CommonKeys.CODE], { force: true })
              .then(() => {
                _common.waitForLoaderToDisappear()
                _common.select_ItemFromPopUpList(commonLocators.CommonKeys.GRID, data[commonLocators.CommonKeys.CODE])
              })
          }

          if (data[commonLocators.CommonKeys.CRITERIA_LABEL]) {
            Object.keys(data[commonLocators.CommonKeys.CRITERIA_LABEL])
              .forEach((key) => {
                _modalView.findModal()
                  .wrapElements()
                  .contains(`${commonLocators.CommonElements.PLATFORM_FORM_LABEL}`, key)
                  .closest(`${commonLocators.CommonElements.ROW}`)
                  .within(() => {
                    cy.get(`.caret`)
                      .trigger("click")
                  })
                  .then(() => {
                    _common.waitForLoaderToDisappear()
                    _common.select_ItemFromPopUpList(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonKeys.CRITERIA_LABEL][key])
                  })
              })

          }
          if (data[commonLocators.CommonLabels.CREATE_RELATIONS]) {
            _common.waitForLoaderToDisappear()
            cy.get("[class*='modal-dialog'] [class*='nav-tabs'] a").last().click({ force: true });
            _modalView.findModal()
              .findCheckBox_byLabel(commonLocators.CommonLabels.CREATE_RELATIONS, "checkbox")
              .as("check")
              .invoke("is", ":checked")
              .then((checked) => {
                if (data[commonLocators.CommonLabels.CREATE_RELATIONS] == "check") {
                  if (!checked) {
                    cy.get("@check").check();
                  }
                }
                if (data[commonLocators.CommonLabels.CREATE_RELATIONS] == "uncheck") {
                  if (checked) {
                    cy.get("@check").uncheck();
                  }
                }
              })
              .then(() => {
                _common.waitForLoaderToDisappear()
                _modalView.findModal()
                  .wrapElements()
                  .contains(`${commonLocators.CommonElements.PLATFORM_FORM_LABEL}`, commonLocators.CommonLabels.KIND_CAPS)
                  .closest(`${commonLocators.CommonElements.ROW}`)
                  .within(() => {
                    cy.get(`.caret`)
                      .trigger("click")
                  })
                  .then(() => {
                    _common.waitForLoaderToDisappear()
                    _common.select_ItemFromPopUpList(commonLocators.CommonKeys.LIST, data[commonLocators.CommonLabels.KIND_CAPS])
                  })
              })

          }
        }
        _common.waitForLoaderToDisappear()

      })
  }

  
  enterrecord_toActivestructuregrid(
    code: string,
    description: string,
    Quantity: string,
    uom: string,
    startdate: string,
    finishdate: string
  ) {
    _mainView
      .findModuleClientArea()
      .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
      .findGrid()
      .findActiveRow()
      .findCell(app.GridCells.CODE)
      .findTextInput(app.InputFields.DOMAIN_TYPE_CODE)
      .clear({ force: true })
      .type(code, { force: true });
    _mainView
      .findModuleClientArea()
      .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
      .findGrid()
      .findActiveRow()
      .findCell(app.GridCells.DESCRIPTION)
      .typeIn(description);
    _mainView
      .findModuleClientArea()
      .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
      .findGrid()
      .findActiveRow()
      .findCell(app.GridCells.QUANTITY_SMALL)
      .typeIn(Quantity);
    _mainView
      .findModuleClientArea()
      .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
      .findGrid()
      .findActiveRow()
      .findCell(app.GridCells.BAS_UOM_FK)
      .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
      .type(uom, { force: true });
    _mainView.select_popupItem("grid", uom);
    _mainView
      .findModuleClientArea()
      .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
      .findGrid()
      .findActiveRow()
      .findCell(app.GridCells.PLANNED_START)
      .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
      .clear({ force: true })
      .type(startdate, { force: true });
    _mainView
      .findModuleClientArea()
      .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
      .findGrid()
      .findActiveRow()
      .findCell(app.GridCells.PLANNED_START)
      .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
      .clear({ force: true })
      .type(finishdate, { force: true });
    cy.SAVE();
  }

  enterDataTo_CreateScheduleActivity(description: string, quantity: string, uom: string, plannedStart?: string, plannedFinished?: string) {
    _mainView.findModuleClientArea()
             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
             .toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
             .findButton(btn.ToolBar.ICO_REC_NEW) // Check if "Create New Record" button exists
             .wrapElements()
             .then((button) => {
                if (button.length > 0) {  // If the button exists      
                      _mainView.findModuleClientArea()
                              .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                              .toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
                              .findButton(btn.IconButtons.ICO_SUB_FLD_NEW)
                              .clickIn();
                    // Enter Data into Active Row Cells
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .findGrid()
                             .findActiveRow()
                             .findCell(app.GridCells.QUANTITY_SMALL)
                             .clickIn();
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .findGrid()
                             .findActiveRow()
                             .findCell(app.GridCells.QUANTITY_SMALL)
                             .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
                             .type(quantity, { force: true });
                    _common.waitForLoaderToDisappear();
                    cy.wait(2000)
                    _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    _common.waitForLoaderToDisappear();
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .findGrid()
                             .findActiveRow()
                             .findCell(app.GridCells.DESCRIPTION)
                             .findTextInput(app.InputFields.DOMAIN_TYPE_COMMENT)
                             .type(description);
                    _common.waitForLoaderToDisappear();
                    _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    _common.waitForLoaderToDisappear();
                    cy.wait(2000)
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .findGrid()
                             .findActiveRow()
                             .findCell(app.GridCells.QUANTITY_UOM_FK)
                             .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
                             .type(uom, { force: true });
                    _mainView.select_popupItem(commonLocators.CommonKeys.GRID, uom);
                    _common.waitForLoaderToDisappear();
                    _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    _common.waitForLoaderToDisappear();
                    if (plannedStart) {
                      _common.enterRecord_inNewRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.PLANNED_START, app.InputFields.INPUT_GROUP_CONTENT, plannedStart);
                      _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    }
                    if (plannedFinished) {
                      _common.enterRecord_inNewRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.PLANNED_FINISH, app.InputFields.INPUT_GROUP_CONTENT, plannedFinished);
                      _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    }
                } else {    // If the button does not exist, access Activity Settings
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .toolbar(btn.ToolBar.ICO_SETTINGS)
                             .clickIn();      
                    _mainView.findModuleClientArea()
                             .select_popupItem(commonLocators.CommonKeys.SPAN, commonLocators.CommonLabels.ACTIVITY_SETTING);
                    _modalView.findModal()
                              .findCheckBox_byLabel(commonLocators.CommonLabels.ENABLE_TRANSIENT_ROOT_ENTITY, commonLocators.CommonKeys.CHECKBOX_SMALL)
                              .as("chkbox").invoke("is", commonLocators.CommonKeys.CHECKBOX_SMALL)
                              .then((checked) => {
                                if (checked) {
                                  _modalView.findModal()
                                            .acceptButton(commonLocators.CommonKeys.CANCEL);
                                } else {
                                  cy.get("@chkbox")
                                    .check({ force: true });
                                  _modalView.findModal()
                                            .acceptButton(btn.ButtonText.OK);
                                }
                              });
                    cy.REFRESH_CONTAINER(); // After configuring settings, create new record and enter data
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .findGrid()
                             .findCell_ByIcon(app.GridCellIcons.ICO_ROOT_SCHEDULING, 0)
                             .clickIn();
                    _common.create_newSubRecord(cnt.uuid.ACTIVITY_STRUCTURE);
                    cy.wait(2000);
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .findGrid()
                             .findActiveRow()
                             .findCell(app.GridCells.QUANTITY_SMALL)
                             .clickIn();
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .findGrid()
                             .findActiveRow()
                             .findCell(app.GridCells.QUANTITY_SMALL)
                             .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
                             .type(quantity, { force: true });
                    _common.waitForLoaderToDisappear();
                    _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    _common.waitForLoaderToDisappear();
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .findGrid()
                             .findActiveRow()
                             .findCell(app.GridCells.DESCRIPTION)
                             .findTextInput(app.InputFields.DOMAIN_TYPE_COMMENT)
                             .type(description);
                    _common.waitForLoaderToDisappear();
                    _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    _common.waitForLoaderToDisappear();
                    _mainView.findModuleClientArea()
                             .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                             .findGrid()
                             .findActiveRow()
                             .findCell(app.GridCells.QUANTITY_UOM_FK)
                             .findTextInput(app.InputFields.INPUT_GROUP_CONTENT)
                             .type(uom, { force: true });
                    _mainView.select_popupItem(commonLocators.CommonKeys.GRID, uom);
                    _common.waitForLoaderToDisappear();
                    _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    _common.waitForLoaderToDisappear();
                    if (plannedStart) {
                      _common.enterRecord_inNewRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.PLANNED_START, app.InputFields.INPUT_GROUP_CONTENT, plannedStart);
                      _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    }
                    if (plannedFinished) {
                      _common.enterRecord_inNewRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.PLANNED_FINISH, app.InputFields.INPUT_GROUP_CONTENT, plannedFinished);
                      _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                    }
                }
             });
    cy.SAVE(); 
}
    
  /*
   * This is used to create schedule from wizard
   * Updated Date: 5/1/2023
   * Author : Anurag Singh
  */

  create_schedule_fromWizard_byClass(data:DataCells) {

    if (data[commonLocators.CommonLabels.TEMPLATE_PROJECT]) {
      _modalView.findModal()
                .findDropdownInputField_insideModal_byClass(app.ModalInputFields.TEMPLATE_PROJECT)
                .clear({ force: true })
                .type(data[commonLocators.CommonLabels.TEMPLATE_PROJECT])
                .then(()=>{
                  _modalView.select_popupItem(commonLocators.CommonKeys.GRID, data[commonLocators.CommonLabels.TEMPLATE_PROJECT]);
                })
    }
    if (data[commonLocators.CommonLabels.SCHEDULE]) {
      _modalView.findModal()
                .findDropdownInputField_insideModal_byClass(app.ModalInputFields.SCHEDULE)
                .clear({ force: true })
                .type(data[commonLocators.CommonLabels.SCHEDULE])
                .then(()=>{
                  _modalView.select_popupItem(commonLocators.CommonKeys.GRID, data[commonLocators.CommonLabels.SCHEDULE]);
                })
    }
    _modalView.findModal()
              .findInputField_insideModal_byClass(app.ModalInputFields.CODE_ENTITY)
              .invoke("val")
              .then(function (codeVal: string) {
                Cypress.env("SCHEDULE_CODE", codeVal)
              });

    _common.waitForLoaderToDisappear()
    _common.clickOn_modalFooterButton(btn.ButtonText.OK)
    _common.waitForLoaderToDisappear()
    cy.wait(1000)// This wait is added as modal take time to load
    _common.clickOn_modalFooterButton(btn.ButtonText.OK)
    _common.waitForLoaderToDisappear()
  }

  enterRecord_toCreateBaseline(data: DataCells) {
    if (data[commonLocators.CommonLabels.DESCRIPTION]) {
           _modalView.findModal()
                     .findInputFieldInsideModal(commonLocators.CommonLabels.DESCRIPTION,app.InputFields.INPUT_GROUP_CONTENT)
                     .clear({ force: true })
                     .type(data[commonLocators.CommonLabels.DESCRIPTION])
    } 
    
    if (data[commonLocators.CommonLabels.REMARKS]) {
           _modalView.findModal()
                     .findInputFieldInsideModal(commonLocators.CommonLabels.REMARKS,app.InputFields.DOMAIN_TYPE_REMARK)
                     .clear({ force: true })
                     .type(data[commonLocators.CommonLabels.REMARKS])
    } 
           _modalView.findModal().acceptButton(btn.ButtonText.OK);
           cy.wait(500)
           _modalView.findModal().acceptButton(btn.ButtonText.OK);
 
  }

   /*
   * This is used to create set Renumbering Activities from wizard
   * Updated Date: 2/6/2024
   * Author : Anupama G
  */
  enterRecord_to_set_Renumbering(sublevelsText:string,data: DataCells){
  
    if (data[commonLocators.CommonLabels.CODE_FORMAT]) {
      cy.get("body")
      .then(($body) => {
          let length = $body.find(`${commonLocators.CommonModalElements.MODAL_DIALOG_CLASS} .${btn.IconButtons.ICO_DOWN}`).length;
          if (length > 0) {
            for (let index = 1; index <= length; index++) {
              cy.get(`${commonLocators.CommonModalElements.MODAL_DIALOG_CLASS} .${btn.IconButtons.ICO_DOWN}`)
            }      
          }  
             cy.get(`[class="panel-title"] [class*="platform-form-group-header-text"]`)
               .contains(sublevelsText)
                         if (data[commonLocators.CommonLabels.CODE_FORMAT]) {
       
                              _modalView.findModal()
                                        .wrapElements()
                                        .contains(commonLocators.CommonElements.PLATFORM_FORM_LABEL,commonLocators.CommonLabels.CODE_FORMAT)
                                        .closest(commonLocators.CommonElements.ROW)
                                        .within(()=>{
                                          cy.get(commonLocators.CommonElements.PLATFORM_FORM_COL)
                                            .find('[class*="'+app.InputFields.INPUT_GROUP_CONTENT+'"]')
                                            .clear()
                                            .type(data[commonLocators.CommonLabels.CODE_FORMAT])
                                            .then(()=>{
                                              cy.document()
                                                .its('body')
                                                .within(()=>{
                                                  _modalView.select_popupItem(commonLocators.CommonKeys.DIV, data[commonLocators.CommonLabels.CODE_FORMAT]);
                                                  cy.wait(1000)
                                                })
                                                })     
            })
                      cy.get(`[class="panel-title"] [class*="platform-form-group-header-text"]`)
                        .contains(commonLocators.CommonLabels.LEVEL_1)
                                _modalView.findModal()
                                          .wrapElements()
                                          .contains(commonLocators.CommonElements.PLATFORM_FORM_LABEL,commonLocators.CommonLabels.LEVEL_1)
                                          .closest(commonLocators.CommonElements.ROW)
                                          .find("[class*='"+app.ContainerElements.CARET+"']")
                                          .click()
                                 _mainView.select_popupItem(commonLocators.CommonKeys.GRID_1,commonLocators.CommonKeys.CODE);
                                        cy.wait(1000)//need time to load //
                                 _modalView.findModal().acceptButton(btn.ButtonText.OK);
                                         cy.wait(500)
                                 _modalView.findModal().acceptButton(btn.ButtonText.OK);
              
            }
           })

     
    
    } }


    /*
   * This is used to create Apply performance sheet from wizard
   * Updated Date: 2/20/2024
   * Author : Anupama G
   */

    apply_performance_sheet_fromWizard(data:DataCells) {
   
      if (data[commonLocators.CommonKeys.CHECKBOX]) {
        for (var index in data[commonLocators.CommonKeys.CHECKBOX]) {
          if (data[commonLocators.CommonKeys.CHECKBOX][index].VALUE === commonLocators.CommonKeys.CHECK) {
            cy.get(commonLocators.CommonModalElements.MODAL_DIALOG_CLASS + " " + commonLocators.CommonModalElements.MODAL_CONTENT_CLASS + " " + commonLocators.CommonElements.ROW)
              .contains(commonLocators.CommonModalElements.MODAL_DIALOG_CLASS + " " + commonLocators.CommonModalElements.MODAL_CONTENT_CLASS + " " + commonLocators.CommonElements.ROW, data[commonLocators.CommonKeys.CHECKBOX][index].LABEL_NAME)
              .then((ele) => {
                cy.wrap(ele)
                  .find(commonLocators.CommonElements.CHECKBOX_INPUT)
                  .eq(data[commonLocators.CommonKeys.CHECKBOX][index].INDEX)
                  .as("check")
                  .invoke("is", ":checked")
                  .then((checked) => {
                    if (!checked) {
                      cy.get("@check").check();
                    }
                  });
              })
          }
          if (data[commonLocators.CommonKeys.CHECKBOX][index].VALUE === commonLocators.CommonKeys.UNCHECK) {
            cy.get(commonLocators.CommonModalElements.MODAL_DIALOG_CLASS + " " + commonLocators.CommonModalElements.MODAL_CONTENT_CLASS + " " + commonLocators.CommonElements.ROW)
              .contains(commonLocators.CommonModalElements.MODAL_DIALOG_CLASS + " " + commonLocators.CommonModalElements.MODAL_CONTENT_CLASS + " " + commonLocators.CommonElements.ROW, data[commonLocators.CommonKeys.CHECKBOX][index].LABEL_NAME)
              .then((ele) => {
                cy.wrap(ele)
                  .find(commonLocators.CommonElements.CHECKBOX_INPUT)
                  .eq(data[commonLocators.CommonKeys.CHECKBOX][index].INDEX)
                  .as("check")
                  .invoke("is", ":checked")
                  .then((checked) => {
                    if (checked) {
                      cy.get("@check").uncheck();
                    }
                  });
              });
          }
        }
      }
   
      cy.wait(1000)//This wait necessary 
      _common.clickOn_modalFooterButton(btn.ButtonText.OK)
      cy.wait(1000)//This wait necessary 
      _common.clickOn_modalFooterButton(btn.ButtonText.OK)
    }
    insert_ActivityLineItems(){
      cy.contains(".modal-content", commonLocators.CommonKeys.INSERT_LINE_ITEMS)
        .within(() => {
          cy.contains("div", "Search Results")
          cy.get(`.column-id_projectno`).click({force:true})
          cy.get(`.active .column-id_select`)
            .find('[type="checkbox"]')
            .check()              
        })
      _common.clickOn_modalFooterButton(btn.ButtonText.OK)

    }  

  




  enterRecord_toDeepCopySchedule(data: DataCells) {
    if (data[commonLocators.CommonLabels.DESCRIPTION]) {
      _modalView.findModal()
        .findInputFieldInsideModal(commonLocators.CommonLabels.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION)
        .clear({ force: true })
        .type(data[commonLocators.CommonLabels.DESCRIPTION])
    }

    if (data[commonLocators.CommonLabels.COPY_KEYDATES]==commonLocators.CommonKeys.CHECK) {
      _modalView.findModal()
        .findCheckbox_byLabelnModal("platform-form-group", commonLocators.CommonLabels.COPY_KEYDATES, 0)
        .check({ force: true })
    }
    if (data[commonLocators.CommonLabels.COPY_SUB_SCHEDULES]==commonLocators.CommonKeys.CHECK) {
      _modalView.findModal()
        .findCheckbox_byLabelnModal("platform-form-group", commonLocators.CommonLabels.COPY_SUB_SCHEDULES, 1)
        .check({ force: true })
    }
    _modalView.findModal().acceptButton(btn.ButtonText.FINISH);
  }

  enterDataTo_CreateScheduleActivitysettings( container_UUID:string,date: any,description: string, checkBoxValue: string) {
    _mainView.findModuleClientArea()
              .findAndShowContainer(container_UUID)
              .toolbar(btn.ToolBar.ICO_SETTINGS).clickIn()
    _mainView.findModuleClientArea()
              .select_popupItem("span", "Activity Settings")
 
    _modalView.findModal()
              .findInputFieldInsideModal(commonLocators.CommonLabels.DUE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT)
              .clear({ force: true })
              .type(date, { force: true })
            cy.wait(1000) //required wait to enter value
    _modalView.findModal()
              .findInputFieldInsideModal(commonLocators.CommonLabels.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION)
              .clear({ force: true })
              .type(description, { force: true })
            cy.wait(1000) //required wait to enter value
         
    _modalView.findModal()
              .findCheckBox_byLabel(commonLocators.CommonLabels.ENABLE_TRANSIENT_ROOT_ENTITY, "checkbox")
              .as("check")
              .invoke("is", ":checked")
              .then((checked) => {
                if (checkBoxValue == "check") {
                  if (!checked) {
                    cy.get("@check").check();
                  }
                }
                if (checkBoxValue == "uncheck") {
                  if (checked) {
                    cy.get("@check").uncheck();
                  }
                }
                })
   _modalView.findModal().acceptButton(btn.ButtonText.OK);




}

enterRecord_toCreateLOB_Settings(data: DataCells) {

    if (data[commonLocators.CommonLabels.PAPER_SIZE]) {
      _modalView.findModal()
                .findCaretByLabel(commonLocators.CommonLabels.PAPER_SIZE)
      _modalView.select_popupItem(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.PAPER_SIZE]);
      cy.wait(2000) //Need wait to select the value from grid
    }
  

    if (data[commonLocators.CommonLabels.HEADER]) {
      _modalView.findModal()
                .findCaretByLabel(commonLocators.CommonLabels.HEADER)
      _modalView.select_popupItem(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.HEADER]);
      cy.wait(2000) //Need wait to select the value from grid 
    } 

    if (data[commonLocators.CommonLabels.FOOTER]) {
      _modalView.findModal()
                .findCaretByLabel(commonLocators.CommonLabels.FOOTER)
      _modalView.select_popupItem(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.FOOTER]);
        
    }
}
   /*
   * This is used to add prifix to calender
   * Author : Anupama G
   */

getDate_withPrefix(type: string, numberOfDays?: number, dateFetched?: string, withPrefix?: boolean) {
  const getDayPrefix = (date: Date): string => {
    const dayNames = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];
    return dayNames[date.getDay()];
  };

  const formatWithPrefix = (date: Date, formattedDate: string): string => {
    return withPrefix ? `${getDayPrefix(date)} ${formattedDate}` : formattedDate;
  };

  if (type === 'year') {
    let date = new Date();
    let year = date.getFullYear();
    return year.toString(); // Convert number to string
  }

  if (type === 'current') {
    let date = new Date();
    let year = date.getFullYear();
    let month = (date.getMonth() + 1).toString().padStart(2, '0');
    let day = date.getDate().toString().padStart(2, '0');
    let formattedDate = `${day}/${month}/${year}`;
    return formatWithPrefix(date, formattedDate);
  }

  if (type === 'incremented') {
    let date = new Date();
    date.setDate(date.getDate() + (numberOfDays || 0));
    let year = date.getFullYear();
    let month = (date.getMonth() + 1).toString().padStart(2, '0');
    let day = date.getDate().toString().padStart(2, '0');
    let formattedDate = `${day}/${month}/${year}`;
    return formatWithPrefix(date, formattedDate);
  }

  if (type === 'fetchedDateIncrement') {
    let dateArray = dateFetched?.split('/');
    if (!dateArray) return '';
    let date = new Date(`${dateArray[1]}/${dateArray[0]}/${dateArray[2]}`);
    date.setDate(date.getDate() + (numberOfDays || 0));
    let year = date.getFullYear();
    let month = (date.getMonth() + 1).toString().padStart(2, '0');
    let day = date.getDate().toString().padStart(2, '0');
    let formattedDate = `${day}/${month}/${year}`;
    return formatWithPrefix(date, formattedDate);
  }

  if (type === 'fetchedDateDecrement') {
    let dateArray = dateFetched?.split('/');
    if (!dateArray) return '';
    let date = new Date(`${dateArray[1]}/${dateArray[0]}/${dateArray[2]}`);
    date.setDate(date.getDate() - (numberOfDays || 0));
    let year = date.getFullYear();
    let month = (date.getMonth() + 1).toString().padStart(2, '0');
    let day = date.getDate().toString().padStart(2, '0');
    let formattedDate = `${day}/${month}/${year}`;
    return formatWithPrefix(date, formattedDate);
  }

  if (type === 'lastDateOfCurrentMonth') {
    let date = new Date();
    let lastDayOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
    let year = lastDayOfMonth.getFullYear();
    let month = (lastDayOfMonth.getMonth() + 1).toString().padStart(2, '0');
    let day = lastDayOfMonth.getDate().toString().padStart(2, '0');
    let formattedDate = `${day}/${month}/${year}`;
    return formatWithPrefix(lastDayOfMonth, formattedDate);
  }

  if (type === 'lastDateOfNextMonth') {
    let date = new Date();
    let firstDayOfNextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
    let lastDayOfNextMonth = new Date(firstDayOfNextMonth.getFullYear(), firstDayOfNextMonth.getMonth() + 1, 0);
    let year = lastDayOfNextMonth.getFullYear();
    let month = (lastDayOfNextMonth.getMonth() + 1).toString().padStart(2, '0');
    let day = lastDayOfNextMonth.getDate().toString().padStart(2, '0');
    let formattedDate = `${day}/${month}/${year}`;
    return formatWithPrefix(lastDayOfNextMonth, formattedDate);
  }

  if (type === 'currentFormatYMD') {
    let date = new Date();
    let year = date.getFullYear();
    let month = (date.getMonth() + 1).toString().padStart(2, '0');
    let day = date.getDate().toString().padStart(2, '0');
    let formattedDate = `${year}-${month}-${day}`;
    return formatWithPrefix(date, formattedDate);
  }

  if (type === 'incrementedFormatYMD') {
    let date = new Date();
    date.setDate(date.getDate() + (numberOfDays || 0));
    let year = date.getFullYear();
    let month = (date.getMonth() + 1).toString().padStart(2, '0');
    let day = date.getDate().toString().padStart(2, '0');
    let formattedDate = `${year}-${month}-${day}`;
    return formatWithPrefix(date, formattedDate);
  }

  return '';
}
   /*
   * This is used to  search the similar title in toolbar button
   * Author : Anupama G
   */
clickOn_toolBarButtonWithTitle_withIndex(containerUUID: string,index?:number) {
        _mainView.findModuleClientArea().findAndShowContainer(containerUUID)
                 .toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
                    cy.wrap(containerUUID)
                    cy.get('.tlb-icons.ico-print') 
                      .should('exist')
                      .eq(index)
                      .click({ force: true });
}

   /*
   * This is used to verify the default settings in gantt settings
   * Author : Anupama G
   */
enterRecord_toVerifyDefaultGantt_Settings(data:DataCells) {
        cy.wait(2000)

       if (data[commonLocators.CommonLabels.PAPER_SIZE]) {
            _modalView.findModal()
                      .findCaretByLabel(commonLocators.CommonLabels.PAPER_SIZE)
            _modalView.select_popupItem(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.PAPER_SIZE]);
                    cy.wait(2000) //Need wait to select the value from grid
       }

      if (data[commonLocators.CommonLabels.HEADER]) {
          cy.get("[class*='modal-dialog'] [class*='nav-tabs'] a").eq(1).click( {force: true});
            _modalView.findModal()
                      .findCaretByLabel(commonLocators.CommonLabels.HEADER)
             _validate.assert_labelText_directly_fromModal(commonLocators.CommonElements.ROW, commonLocators.CommonLabels.HEADER, app.InputFields.INPUT_GROUP_CONTENT,commonLocators.CommonLabels.NO_HEADER)
                 
      }
      if (data[commonLocators.CommonLabels.FOOTER]) {
          cy.get("[class*='modal-dialog'] [class*='nav-tabs'] a").eq(1).click( {force: true});
            _modalView.findModal()
            _validate.assert_labelText_directly_fromModal(commonLocators.CommonElements.ROW, commonLocators.CommonLabels.FOOTER, app.InputFields.INPUT_GROUP_CONTENT,commonLocators.CommonLabels.NO_FOOTER)
              
      }
            
}
   /*
   * This is used to set the report settings in gantt settings
   * Author : Anupama G
   */

enterRecord_report_settings_in_Gantt(data:DataCells) {
         cy.wait(2000)

      if (data[commonLocators.CommonLabels.PAPER_SIZE]) {
             _modalView.findModal()
                       .findCaretByLabel(commonLocators.CommonLabels.PAPER_SIZE)
             _modalView.select_popupItem(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.PAPER_SIZE]);
                     cy.wait(2000) //Need wait to select the value from grid
      }
              
      if (data[commonLocators.CommonLabels.HEADER1]) {
          cy.get("[class*='modal-dialog'] [class*='nav-tabs'] a").eq(1).click( {force: true});
              _modalView.findModal()
                        .findCaretByLabel(commonLocators.CommonLabels.HEADER1)
              _modalView.select_popupItem(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.PAPER_SIZE]);
                       cy.wait(2000) //Need wait to select the value from grid 
      }
      if (data[commonLocators.CommonLabels.FOOTER1]) {
          cy.get("[class*='modal-dialog'] [class*='nav-tabs'] a").eq(1).click( {force: true});
              _modalView.findModal()
                        .findCaretByLabel(commonLocators.CommonLabels.FOOTER1)
              _modalView.select_popupItem(commonLocators.CommonKeys.GRID_1, data[commonLocators.CommonLabels.PAPER_SIZE]);
                      
      }        
}

  /*
   * This is used to create activity in array
   * Date: 24/12/2024
   * Author : Anupama G
  */
     enterDataTo_CreateScheduleActivity_in_array(recordCount) {
            if (!recordCount || recordCount <= 0) {
              throw new Error("Invalid record count! Please provide a positive number.");
            }
            // Generate an array of unique descriptions
            const descriptionsArray = Array.from({ length: recordCount }, (_, i) => `Description ${i + 1}`);
            // Loop through the descriptions and create records
              descriptionsArray.forEach((description) => {
                      _mainView.findModuleClientArea()
                              .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                              .toolbar(app.Layouts.SUB_VIEW_HEADER_TOOLBAR)
                              .findButton(btn.ToolBar.ICO_REC_NEW) 
                              .clickIn()
                            cy.wait(2000);
                        _common.waitForLoaderToDisappear();
                        _common.select_activeRowInContainer(cnt.uuid.ACTIVITY_STRUCTURE);
                      _mainView.findModuleClientArea()
                              .findAndShowContainer(cnt.uuid.ACTIVITY_STRUCTURE)
                              .findGrid()
                              .findActiveRow()
                              .findCell(app.GridCells.DESCRIPTION)
                              .findTextInput(app.InputFields.DOMAIN_TYPE_COMMENT)
                              .type(description);
                        _common.waitForLoaderToDisappear();
                            cy.SAVE(); // Save the record
                        });
  }
  
 /*
   * This is used to create alternative project for schedule 
   * Date: 27/12/2024
   * Author : Anupama G
  */
 enterRecord_toAlternativeProject(projectNumber: string) {
    _modalView.findModal()
              .findInputFieldInsideModal(commonLocators.CommonLabels.NAME, app.InputFields.DOMAIN_TYPE_DESCRIPTION)
              .clear()
              .type(projectNumber)
    _modalView.findModal()
              .acceptButton(btn.ButtonText.NEXT);
       _common.waitForLoaderToDisappear();
    _modalView.findModal()
              .acceptButton(btn.ButtonText.NEXT);
       _common.waitForLoaderToDisappear();
    _modalView.findModal()
              .acceptButton(btn.ButtonText.NEXT);
    _modalView.findModal()
              .findCheckBox_byLabel(commonLocators.CommonKeys.SCHEDULES,commonLocators.CommonKeys.CHECKBOX_SMALL)
              .check({ force: true });
    _modalView.findModal()
              .acceptButton(btn.ButtonText.NEXT);
       _common.waitForLoaderToDisappear();
    _modalView.findModal()
              .acceptButton(btn.ButtonText.NEXT);
       _common.waitForLoaderToDisappear();
    _modalView.findModal()
              .acceptButton(btn.ButtonText.NEXT);
    _modalView.findModal()
              .acceptButton(btn.ButtonText.FINISH);
    cy.contains(commonLocators.CommonElements.SPINNER_LG, commonLocators.CommonLabels.DEEP_COPY_IN_PROGRESS, { timeout: 10000 }).should('not.exist');
  }

   /*
   * This is used to get the subtitle breadcrumb from scheduling
   * Date: 31/12/2024
   * Author : Anupama G
  */
  get_subtitle_text(projectCode, index, alternative, values, description) {
		const dynamicValue = `${projectCode}, Index: ${index}, Alternative: ${alternative} - ${values.slice(0, 2).join(' / ')} - ${values.slice(2).join(' / ')} - ${description}`;
		     cy.get(`#infoView`)
		       .find(`#infoTitle [class*='subtitle']`)
		       .within(() => {
			   cy.get('label').contains(dynamicValue).should('exist');
		  });
	  }
    
  /*
   * This is used to get the default check box value from scheduling
   * Date: 31/12/2024
   * Author : Anupama G
  */
  set_lineItems_toNoRelation(checkBoxValue:string){
        cy.contains(commonLocators.CommonModalElements.MODAL_CONTENT_CLASS, commonLocators.CommonKeys.INSERT_LINE_ITEMS)
          .within(() => {
            cy.contains(commonLocators.CommonKeys.LABEL, commonLocators.CommonKeys.SET_LINE_ITEM_TO_NO_RELATION)
              .should('exist') // Ensure the label exists
              .and('be.visible'); // Ensure the label is visible            
          })
          _modalView.findModal()
                    .findCheckBox_byLabel(commonLocators.CommonKeys.SET_LINE_ITEM_TO_NO_RELATION, commonLocators.CommonKeys.CHECKBOX_SMALL)
                    .as("check")
                    .invoke("is", ":checked")
                    .then((checked) => {
                            if (checkBoxValue == "check") {
                              if (!checked) {
                                cy.get("@check").check();
                              }
                            }
                            if (checkBoxValue == "uncheck") {
                              if (checked) {
                                cy.get("@check").uncheck();
                              }
                            }
            })
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)

  } 

      /*
       * This is used to  split the quantity in estimate from scheduling
       * Date: 10/01/2025
       * Author : Anupama G
      */
   
  
  insertLineItems_byModal(data:DataCells){
    cy.wait(2000) // Added this wait as modal takes time to load, which results in failure
		if (data[commonLocators.CommonKeys.CODE]) {
			_modalView.findModal()
                .findCaretInsideModal_byClass(app.ModalInputFields.ESTIMATE_CODE)
                .then(()=>{
                 _modalView.select_popupItem(commonLocators.CommonKeys.LIST, data[commonLocators.CommonKeys.CODE])
                })
		}
		if (data[app.ModalInputFields.APPLY_SPLIT_RESULT_TO]) {
			_modalView.findModal()
					      .findRadio_underModal_byClass(app.ModalInputFields.APPLY_SPLIT_RESULT_TO,data[app.ModalInputFields.APPLY_SPLIT_RESULT_TO])
		}
		if (data[commonLocators.CommonKeys.SET_LINE_ITEM_TO_NO_RELATION]) {
			_modalView.findModal()
					     .findCheckbox_underModal_byClass(app.ModalInputFields.SET_LINE_ITEM_TO_NO_RELATION,data[commonLocators.CommonKeys.SET_LINE_ITEM_TO_NO_RELATION])
		}

		if (data[commonLocators.CommonKeys.SEARCH_RESULT]) {
			_modalView.findModal()
                .wrapElements()
                .find(`[class*='${app.InputFields.INPUT_GROUP_FORM_CONTROL}']` +` `+ `[type='text']` )
                .clear({force:true})
                .type(data[commonLocators.CommonKeys.SEARCH_RESULT], {force:true})
                .then(()=>{
                _modalView.findModal()
                          .wrapElements()
                          .find(`[class*='${app.InputFields.INPUT_GROUP_FORM_CONTROL}']` +` `+ `[class*='${btn.IconButtons.ICO_SEARCH}']` )
                          .click()
                _modalView.findModal()
                          .wrapElements()
                          .find(`[class*='column-id_${app.GridCells.DESCRIPTION_INFO}']`)
                          .contains(data[commonLocators.CommonKeys.SEARCH_RESULT])
                          .click({force:true})
                })
		}
		if (data[app.GridCells.QUANTITY_PERCENT]) {
			_modalView.findModalBody()
                .wrapElements()
                .find(`.active [class*='${app.GridCells.QUANTITY_PERCENT}']`)
                .click()
                .then(()=>{
                  _modalView.findModalBody()
                            .wrapElements()
                            .find(`.active .${app.InputFields.INPUT_GROUP_CONTENT}`)
                            .type(data[app.GridCells.QUANTITY_PERCENT])
                })
		}
		_modalView.findModalBody()
              .findActiveRow()
              .wrapElements()
              .find(`${commonLocators.CommonElements.CHECKBOX_TYPE}`)
              .check()
	}
  /*
       * This is used to update the AQ and IQ quantity from schedule to estimate line item
       * Date: 03/04/2025
       * Author : Anupama G
      */
   
  
    update_AQ_IQ_byModal(data:DataCells){
         cy.wait(2000) // Added this wait as modal takes time to load, which results in failure
   
        if (data[app.ModalInputFields.SCHEDULE_SCOPE]) {
          _modalView.findModal()
                    .findRadio_underModal_byClass(app.ModalInputFields.SCHEDULE_SCOPE,data[app.ModalInputFields.SCHEDULE_SCOPE])
        }
        
        if (data[commonLocators.CommonKeys.CONSIDER_S_CURVE_FOR_PLANNED_QUANTITY]) {
          _modalView.findModal()
                   .findCheckbox_underModal_byClass(app.ModalInputFields.CONSIDER_S_CURVE,data[commonLocators.CommonKeys.CONSIDER_S_CURVE_FOR_PLANNED_QUANTITY])
                   .check()           
                   
        }
        
        if (data[app.ModalInputFields.SELECT_QUANTITY]) {
          _modalView.findModal()
                    .findRadio_underModal_byClass(app.ModalInputFields.UPDATE_PLANNED_QUANTITIES,data[app.ModalInputFields.SELECT_QUANTITY])
        }
  
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
  
  }

  
  } 

  
    
      
       

            




  

 



  