import cypress from "cypress";
import { tile, app, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _estimatePage, _validate, _mainView,_commonAPI, _boqPage, _modalView, _logesticPage, _controllingUnit, _projectPage } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import apiConstantData from "cypress/constantData/apiConstantData";

const PLANT_CODE1 = _common.generateRandomString(3);
const PLANT_DESCRIPTION1 = _common.generateRandomString(3);
const CONDITION_CODE = _common.generateRandomString(3)
const CONDITION_DESC = _common.generateRandomString(3)
const PLANT_TYPE = _common.generateRandomString(3)
const RENTAL = _common.generateRandomString(3)
const DESC_WORK = _common.generateRandomString(3)
const CODE_WORK = _common.generateRandomString(3)
const PLANT_PRICE_LIST = _common.generateRandomString(3)

let OPERATION_TYPE_PARAMETER_WORK:DataCells
let CONTAINER_DATA_RECORD
let CONTAINERS_CONTROLLING_UNITS,
    CONTAINER_COLUMNS_CONTROLLING_UNITS,
    CONTROLLING_UNIT_SUB_PARAMETERS1
let ALLOCATION_FOR_PLANTS_PARAMETER: DataCells,
    CONTROLLING_UNIT_A_PARAMETERS
let MODAL_PLANT_ALLOCATION
let  PLANT_PARAMETERS1,
     DATA_RECORD_PARAMETER1:DataCells
let CONTAINERS_PLANT, CONTAINER_OPERATION_TYPES,
    CONTAINER_COLUMNS_PLANT,
    CONTAINERS_PLANT_PRICE_LISTS
let CONTAINER_COLUMNS_JOBS;
let CONTAINER_JOBS;
let CONTAINER_COLUMNS_CONDITIONS
let CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS

describe("LRM- 1.94 | Verify the plant prices record in logistic price condition module with same plant and same wot and null valid from valid to should not get save", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("LRM/lgm-1.94-verify-the-plant-prices-record-in-logistic-price-condition-module-with-same-plant-and-same-wot-and-null-valid-from-valid-to-should-not-get-save.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNITS
            CONTAINER_COLUMNS_CONTROLLING_UNITS = this.data.CONTAINER_COLUMNS.CONTROLLING_UNITS;
           CONTROLLING_UNIT_A_PARAMETERS = {
                          [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
                          [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
                          [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true", "true"]
                      }
            CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;
            CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
            CONTAINERS_PLANT_PRICE_LISTS = this.data.CONTAINERS.PLANT_PRICE_LISTS;
            PLANT_PARAMETERS1 = {
                [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION1,
                [app.GridCells.PLANT_GROUP_FK]: CONTAINERS_PLANT.GROUP,
                [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
                [app.GridCells.PLANT_TYPE_FK]: RENTAL,
                [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
                [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
            },
           
            MODAL_PLANT_ALLOCATION = this.data.MODAL.PLANT_ALLOCATION;
            ALLOCATION_FOR_PLANTS_PARAMETER = {
                [commonLocators.CommonLabels.JOB]:  Cypress.env('API_PROJECT_NUMBER_1'),
                [commonLocators.CommonLabels.ALLOCATED_FROM]: MODAL_PLANT_ALLOCATION.ALLOCATED_FROM,
                [app.GridCells.WORK_OPERATION_TYPE_FK]: CODE_WORK
            },
            CONTAINER_COLUMNS_JOBS = this.data.CONTAINER_COLUMNS.JOBS
            CONTAINER_JOBS = this.data.CONTAINERS.JOBS
            CONTAINER_COLUMNS_CONDITIONS = this.data.CONTAINER_COLUMNS.CONDITIONS
            CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS = this.data.CONTAINERS.WORK_OPERATION_TYPE_CONDITIONS
            CONTAINER_DATA_RECORD = this.data.CONTAINERS.DATA_RECORD;
            DATA_RECORD_PARAMETER1 = {
                [app.GridCells.PRICE_LIST_TYPE_FK]: PLANT_TYPE,
                [app.GridCells.ETM_CONTEXT_FK]: CONTAINER_DATA_RECORD.LINE_ITEM_CONTEXT,
                [app.GridCells.CURRENCY_FK]: CONTAINER_DATA_RECORD.CURRENCY,
                [app.GridCells.PERCENT]: CONTAINER_DATA_RECORD.PERCENT,
                [app.GridCells.DESCRIPTION_INFO]: PLANT_PRICE_LIST,
                [app.GridCells.UOM_FK]: CONTAINER_DATA_RECORD.UOM,
                [app.GridCells.CALCULATION_TYPE_FK]: CONTAINER_DATA_RECORD.AVERAGE_CATALOG_VALUE
            }
            CONTAINER_OPERATION_TYPES= this.data.CONTAINERS.OPERATION_TYPES
            OPERATION_TYPE_PARAMETER_WORK = {
                [app.GridCells.IS_LIVE]: CommonLocators.CommonKeys.CHECK,
                [app.GridCells.CODE]: CODE_WORK,
                [app.GridCells.DESCRIPTION_INFO]: DESC_WORK,
                [app.GridCells.UOM_FK]: CONTAINER_OPERATION_TYPES.DAY,
                [app.GridCells.IS_HIRE]: CommonLocators.CommonKeys.CHECK
            }
        })
        .then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
            _commonAPI.getAccessToken()
                .then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                });
        })
    })

    after(() => {
        cy.LOGOUT();
    });

   it("TC - API: Create project A with logistic job and controlling unit", function () {
           _commonAPI.createProject()
               .then(() => {
                   _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                   _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
                   _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_A_PARAMETERS)
               });
       })
    
    it("TC - Add plant list type record in customizing module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING)
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CONTAINER_DATA_RECORD.PLANT_LIST_TYPE)
        _common.select_rowHasValue(cnt.uuid.DATA_TYPES, CONTAINER_DATA_RECORD.PLANT_LIST_TYPE)
        _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORDS)
        _common.create_newRecord(cnt.uuid.DATA_RECORDS)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, PLANT_TYPE);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })
    it("TC - Create new plant price list data record", function () {
        _common.openTab(app.TabBar.MASTERDATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES)
        })
        _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CONTAINER_DATA_RECORD.PLANT_PRICE_LIST)
        _common.select_rowHasValue(cnt.uuid.DATA_TYPES, CONTAINER_DATA_RECORD.PLANT_PRICE_LIST)

        _common.maximizeContainer(cnt.uuid.DATA_RECORDS)
        _common.create_newRecord(cnt.uuid.DATA_RECORDS)
        _logesticPage.enterRecord_toCreatePlantListDataRecord(DATA_RECORD_PARAMETER1)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.REFERENCE_YEAR, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_DATA_RECORD.REFERENCE_YEAR)
        _common.minimizeContainer(cnt.uuid.DATA_RECORDS)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Add plant type data record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING)
        cy.REFRESH_CONTAINER()
        _common.openTab(app.TabBar.MASTERDATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES)
        })
        _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CONTAINER_DATA_RECORD.PLANT_TYPE)
        _common.select_rowHasValue(cnt.uuid.DATA_TYPES, CONTAINER_DATA_RECORD.PLANT_TYPE)
        _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORDS)
        _common.maximizeContainer(cnt.uuid.DATA_RECORDS)
        _common.create_newRecord(cnt.uuid.DATA_RECORDS)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, RENTAL);
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_CLUSTER, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create work operation types", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.WORK_OPERATION_TYPES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.TYPES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.OPERATION_TYPE, app.FooterTab.OPERATION_TYPE)
            _common.waitForLoaderToDisappear
            _common.clear_subContainerFilter(cnt.uuid.OPERATION_TYPE)
            cy.REFRESH_CONTAINER()
            _common.waitForLoaderToDisappear
        })
        _common.maximizeContainer(cnt.uuid.OPERATION_TYPE)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.OPERATION_TYPE)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreateOprationType(OPERATION_TYPE_PARAMETER_WORK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.OPERATION_TYPE)
        _common.clear_subContainerFilter(cnt.uuid.OPERATION_TYPE)
        _common.search_inSubContainer(cnt.uuid.OPERATION_TYPE, DESC_WORK)
        cy.wait(1000) //required wait to load data in container
        _common.select_rowHasValue(cnt.uuid.OPERATION_TYPE, DESC_WORK)
        _common.openTab(app.TabBar.TYPES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.OPERATION_2_PLANT_TYPE, app.FooterTab.PLANT_TYPES)
        })
        _common.select_tabFromFooter(cnt.uuid.OPERATION_2_PLANT_TYPE, app.FooterTab.PLANT_TYPES)
        _common.create_newRecord(cnt.uuid.OPERATION_2_PLANT_TYPE)
        _common.edit_dropdownCellWithInput(cnt.uuid.OPERATION_2_PLANT_TYPE, app.GridCells.PLANT_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, RENTAL);
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })
    
    it("TC - Create plant in plant master commercial data", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
            _common.setup_gridLayout(cnt.uuid.PLANT,CONTAINER_COLUMNS_PLANT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PLANT)
        _common.create_newRecord(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS1)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE1);
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT,  app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_PLANT.UOM);
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to save data
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_CONTROLLING, app.FooterTab.CONTROLLING_UNIT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_CONTROLLING)
        });
        _common.create_newRecord(cnt.uuid.PLANT_CONTROLLING)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to appear dropdown
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_CONTROLLING, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
       
    })
    it('TC - Create plant location record from wizard', function () {
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, PLANT_DESCRIPTION1)
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION1)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_INITIAL_ALLOCATION_FOR_PLANTS)
        _common.waitForLoaderToDisappear()
        _logesticPage.create_initialAllocationForPlants_fromWizard(ALLOCATION_FOR_PLANTS_PARAMETER, RENTAL)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION1)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
        _common.waitForLoaderToDisappear()
    })
   
    it("TC - Create job record in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS, 0);
            _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_JOBS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_JOBS.controllingunitfk, CONTAINER_COLUMNS_JOBS.pricinggroupfk], cnt.uuid.JOBS)
        });
        _common.clear_subContainerFilter(cnt.uuid.JOBS)
        _common.select_rowHasValue(cnt.uuid.JOBS,  Cypress.env('API_PROJECT_NUMBER_1'))
        _common.maximizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
        cy.wait(1000)//required wait to enable data input fields
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to save data
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropDownWithInput_fromModal(commonLocators.CommonLabels.CONTROLLING_UNIT,Cypress.env(`API_CNT_CODE_0`), commonLocators.CommonKeys.GRID)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.minimizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
    })
    it("TC - Create record in logistic price condition module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_PRICE_CONDITION)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONDITIONS, app.FooterTab.CONDITIONS);
            _common.setup_gridLayout(cnt.uuid.CONDITIONS, CONTAINER_COLUMNS_CONDITIONS)
            _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        });
        _common.maximizeContainer(cnt.uuid.CONDITIONS)
        _common.create_newRecord(cnt.uuid.CONDITIONS)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, CONDITION_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, CONDITION_DESC)
        _common.set_cellCheckboxValue(cnt.uuid.CONDITIONS, app.GridCells.IS_HANDLING_CHARGE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONDITIONS)
        _common.select_rowHasValue(cnt.uuid.CONDITIONS, CONDITION_CODE)
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.FooterTab.PLANT_CATALOG_PRICELISTS);
        });
        _common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST,PLANT_PRICE_LIST)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_ITEM, app.FooterTab.WORK_OPERATION_TYPE_CONDITIONS);
            _common.create_newRecord(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CODE_WORK)
        });
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        _common.editModalDropdown_WithCaret(commonLocators.CommonLabels.PLANT_PRICING_GROUP)
        _common.select_ItemFromPopUpList(commonLocators.CommonKeys.LIST, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PLANT_PRICING_GROUP)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })
    it("TC - Create plant price in logistic price condition", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICES, app.FooterTab.PLANT_PRICES)
            _common.waitForLoaderToDisappear()
        });
        _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICES)
        _common.create_newRecord(cnt.uuid.PLANT_PRICES)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_PRICES, app.GridCells.PLANT_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE1)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_PRICES, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CODE_WORK)
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_1)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICES, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_3, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_4, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_5, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_6, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICES)
        _common.create_newRecord(cnt.uuid.PLANT_PRICES)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_PRICES, app.GridCells.PLANT_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE1)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_PRICES, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CODE_WORK)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_1)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICES, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_3, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_4, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_5, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICES, app.GridCells.PRICE_PORTION_6, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })
    it("TC - Verify error message for same wot and null valid from, valid to in price list of logistic price condition", function () {
        _validate. assert_errorMessage_underModal(commonLocators.CommonLabels.VALID_FROM,CONTAINERS_PLANT_PRICE_LISTS.ERROR_MESSAGE,0)
        _common.waitForLoaderToDisappear()
        _validate. assert_errorMessage_underModal(commonLocators.CommonLabels.VALID_TO,CONTAINERS_PLANT_PRICE_LISTS.ERROR_MESSAGE,1)

    })
})
