/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { IOrdHeaderEntity, IOrdWarrantyEntity } from '@libs/sales/interfaces';
import { SalesContractContractsComplete } from '../../model/complete-class/sales-contract-contracts-complete.class';
import { SalesCommonWarrantyValidationService } from '@libs/sales/common';
import { SalesContractWarrantyDataService } from '../sales-contract-warranty-data.service';

/**
 * Validation service for contract warranty entities
 */
@Injectable({
  providedIn: 'root'
})
export class SalesContractWarrantyValidationService extends SalesCommonWarrantyValidationService<IOrdWarrantyEntity, IOrdHeaderEntity, SalesContractContractsComplete> {
  public constructor(dataService: SalesContractWarrantyDataService) {
    super(dataService);
  }
}
