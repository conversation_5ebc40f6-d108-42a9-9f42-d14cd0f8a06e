/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { DataServiceFlatRoot, ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, IDataServiceRoleOptions } from '@libs/platform/data-access';
import { WipHeaderComplete } from '../model/complete-class/wip-header-complete.class';
import { IWipHeaderEntity } from '@libs/sales/interfaces';
import { SalesCommonNumberGenerationService } from '@libs/sales/common';
import { PlatformTranslateService } from '@libs/platform/common';

@Injectable({
	providedIn: 'root'
})

export class SalesWipWipsDataService extends DataServiceFlatRoot<IWipHeaderEntity, WipHeaderComplete> {
	private readonly numberGenerationService = inject(SalesCommonNumberGenerationService);
	private readonly translateService = inject(PlatformTranslateService);

	public constructor() {
		const options: IDataServiceOptions<IWipHeaderEntity> = {
			apiUrl: 'sales/wip',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'listfiltered',
				usePost: true
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'multidelete'
			},
			roleInfo: <IDataServiceRoleOptions<IWipHeaderEntity>>{
				role: ServiceRole.Root,
				itemName: 'WipHeader',
			}
		};

		super(options);
	}

	protected override onCreateSucceeded(created: object): IWipHeaderEntity {
		const wipComplete = created as WipHeaderComplete;

		if (wipComplete.WipHeader && wipComplete.WipHeader.RubricCategoryFk) {
			const rubricCategoryId = wipComplete.WipHeader.RubricCategoryFk;

			if (this.numberGenerationService.hasToGenerateForRubricCategory('wip', rubricCategoryId)) {
				wipComplete.WipHeader.Code = this.translateService.instant({ key: 'cloud.common.isGenerated' }).text;
			}
		}

		return wipComplete.WipHeader as IWipHeaderEntity;
	}

	public override createUpdateEntity(modified: IWipHeaderEntity | null): WipHeaderComplete {
		const complete = new WipHeaderComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.WipHeader = modified;
		}
		return complete;
	}

	public override getModificationsFromUpdate(complete: WipHeaderComplete): IWipHeaderEntity[] {
			if (complete.WipHeader === null) {
				return [];
			}
			return [complete.WipHeader];
		}
}












