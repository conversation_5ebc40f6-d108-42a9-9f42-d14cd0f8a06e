using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class EstAllowanceMarkupReadData
	{
		/// <summary>
		/// 
		/// </summary>
		public int ProjectId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int EstAllowanceFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int EstHeaderId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public bool isReturnCostCodes { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int EstAllowanceAreaFk { get; set; }


		/// <summary>
		/// 
		/// </summary>
		public List<int> EstAllowanceAreaFks { get; set; }
	}
}
