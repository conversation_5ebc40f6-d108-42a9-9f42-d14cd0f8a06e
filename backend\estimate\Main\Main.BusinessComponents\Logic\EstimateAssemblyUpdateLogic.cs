using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Transactions;
using MathNet.Numerics;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.BulkExpressions.Criteria.CriteriaToExpression;
using RIB.Visual.Basics.Core.Common.Enum;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Estimate.Assemblies.BusinessComponents;
using RIB.Visual.Estimate.Common.BusinessComponents;
using RIB.Visual.Estimate.Main.Localization.Properties;
using RIB.Visual.Estimate.Parameter.BusinessComponents;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RVPBC = RIB.Visual.Platform.BusinessComponents;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// logic for assembly update
	/// </summary>
	public class EstimateAssemblyUpdateLogic : CompanyScopeService
	{
		private readonly int EstHeaderId;

		private readonly EstResourceUpdateHelper _EstResourceUpdateHelper = new EstResourceUpdateHelper();

		private readonly EstAssemblyTypeLogic _EstAssemblyTypeLogic = new EstAssemblyTypeLogic();

		private readonly MdcCommoditySearchVLogic _MdcCommoditySearchVLogic = new MdcCommoditySearchVLogic();

		private Dictionary<int, EstAssemblyTypeEntity> _AssemblyCategoryId2AssemblyTypeMap = new Dictionary<int, EstAssemblyTypeEntity>();

		private readonly Dictionary<int, MdcCommoditySearchVEntity> _MdcCommoditySearchVEntityCache = new Dictionary<int, MdcCommoditySearchVEntity>();

		private List<ICostCodeEntity> _masterCostCodes = null;

		private List<IProjectCostCodesEntity> _prjCostCodes = null;

		private List<MaterialLookupVEntity> _metrials = null;

		internal int _mdcContextId = -1;

		internal int? _defaultJobId = null;

		private readonly EstLineItemAttachmentInitializer _LineItemAttachmentInitializer;

		private readonly EstResourceAttachmentInitializer _ResourceAttachmentInitializer;

		private AssemblyParamUpdateItem _AssemblyParamUpdateResult;

		/// <summary>
		///
		/// </summary>
		/// <param name="estHeaderId"></param>
		public EstimateAssemblyUpdateLogic(int estHeaderId)
		{
			this.EstHeaderId = estHeaderId;

			_LineItemAttachmentInitializer = new EstLineItemAttachmentInitializer(EstHeaderId);

			_ResourceAttachmentInitializer = new EstResourceAttachmentInitializer(EstHeaderId);
		}

		private IEnumerable<AssemblyRefTreeStructure> GetAssemblyLevelByAssemblyId(int headerId, int assemblyId)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var assemblyTreeIds = dbcontext.ExecuteStoredProcedure<AssemblyRefTreeStructure>("EST_ASSEMBLY_REFERENCE_TREE_ID_SP", headerId, assemblyId).ToList();

				if (assemblyTreeIds == null || !assemblyTreeIds.Any())
				{
					return new List<AssemblyRefTreeStructure>();
				}

				return assemblyTreeIds.ToList();
			}
		}

		private string GetSqlString(IEnumerable<int> assemblyIds, int headerId, int lineItemType = (int)CommonLogic.LineItemTypes.Assembly)
		{
			var idsStr = string.Join(",", assemblyIds);

			string queryStr = "WITH BASE_REFERENCE AS ";
			queryStr += "(";
			queryStr += "SELECT A.ID, Level = 1 from EST_LINE_ITEM A WHERE A.EST_HEADER_FK = {1} AND A.LINE_ITEM_TYPE = {2} ";
			queryStr += "AND A.ID IN ({0}) ";
			queryStr += "UNION ALL ";
			queryStr += "SELECT A.EST_LINE_ITEM_FK AS ID, Level = B.Level + 1 FROM EST_RESOURCE A JOIN BASE_REFERENCE B ON B.ID = A.EST_ASSEMBLY_FK AND A.EST_HEADER_FK = {1} AND A.EST_RESOURCE_TYPE_FK = 4 ";
			queryStr += ")";
			queryStr += "SELECT ID AS AssemblyId, MAX(Level) AS Level FROM BASE_REFERENCE GROUP BY ID ORDER BY Level;";

			return string.Format(queryStr, idsStr, headerId, lineItemType);
		}

		private string GetReferenceAssemblyIdsSqlString(IEnumerable<int> assemblyIds, int headerId, int resourceType = (int)EstResourceType.Assembly)
		{
			var idsStr = string.Join(",", assemblyIds);

			string queryStr = "WITH ASSEMBLYS AS ";
			queryStr += "(";
			queryStr += "SELECT A.EST_ASSEMBLY_FK AS ASSEMBLYID, A.EST_HEADER_ASSEMBLY_FK AS ASSEMBLYHEADERID, A.CODE AS RESOURCECODE, A.ID AS RESOURCEID, A.EST_HEADER_FK AS HEADERID, A.EST_LINE_ITEM_FK AS LINEITEMID FROM EST_RESOURCE A WHERE A.EST_HEADER_FK = {1} AND A.EST_RESOURCE_TYPE_FK = {2} AND A.EST_LINE_ITEM_FK IN ({0}) ";
			queryStr += "UNION ALL ";
			queryStr += "SELECT A.EST_ASSEMBLY_FK AS ASSEMBLYID, A.EST_HEADER_ASSEMBLY_FK AS ASSEMBLYHEADERID, A.CODE AS RESOURCECODE, A.ID AS RESOURCEID, A.EST_HEADER_FK AS HEADERID, A.EST_LINE_ITEM_FK AS LINEITEMID FROM EST_RESOURCE A JOIN ASSEMBLYS B ON B.ASSEMBLYID = A.EST_LINE_ITEM_FK AND A.EST_HEADER_FK = B.ASSEMBLYHEADERID AND A.EST_RESOURCE_TYPE_FK = {2} ";
			queryStr += ")";
			queryStr += "SELECT * FROM ASSEMBLYS;";

			return string.Format(queryStr, idsStr, headerId, resourceType);
		}

		private string GetReferenceAssemblyIdsSqlString(int headerId)
		{
			string queryStr = "WITH ASSEMBLYS AS ";
			queryStr += "(";
			queryStr += "SELECT A.EST_ASSEMBLY_FK AS ASSEMBLYID, A.EST_HEADER_ASSEMBLY_FK AS ASSEMBLYHEADERID, A.CODE AS RESOURCECODE, A.ID AS RESOURCEID, A.EST_HEADER_FK AS HEADERID, A.EST_LINE_ITEM_FK AS LINEITEMID FROM EST_RESOURCE A WHERE A.EST_HEADER_FK = {0} AND A.EST_RESOURCE_TYPE_FK = 4 ";
			queryStr += "UNION ALL ";
			queryStr += "SELECT A.EST_ASSEMBLY_FK AS ASSEMBLYID, A.EST_HEADER_ASSEMBLY_FK AS ASSEMBLYHEADERID, A.CODE AS RESOURCECODE, A.ID AS RESOURCEID, A.EST_HEADER_FK AS HEADERID, A.EST_LINE_ITEM_FK AS LINEITEMID FROM EST_RESOURCE A JOIN ASSEMBLYS B ON B.ASSEMBLYID = A.EST_LINE_ITEM_FK AND A.EST_HEADER_FK = B.ASSEMBLYHEADERID AND A.EST_RESOURCE_TYPE_FK = 4 ";
			queryStr += ")";
			queryStr += "SELECT * FROM ASSEMBLYS;";

			return string.Format(queryStr, headerId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="assemblyIds"></param>
		/// <param name="headerId"></param>
		/// <param name="resourceType"></param>
		/// <returns></returns>
		public IEnumerable<AssemblyRefItems> GetReferenceAssemblyIds(IEnumerable<int> assemblyIds, int headerId, int resourceType = (int)EstResourceType.Assembly)
		{
			var result = new List<AssemblyRefItems>();

			object _lock = new object();

			if (assemblyIds == null || !assemblyIds.Any())
			{
				return result;
			}

			var assemblyGroup = assemblyIds.Select((e, i) => new { Index = i, Value = e }).GroupBy(e => e.Index / 1000).Select(e => e.Select(x => x.Value)).ToList();

			ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };

			Parallel.ForEach(assemblyGroup, parallelOptions, ids =>
			{
				using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{
					var entities = dbContext.SqlQuery<AssemblyRefItems>(GetReferenceAssemblyIdsSqlString(ids, headerId, resourceType)).ToList();

					lock (_lock)
					{
						result.AddRange(entities);
					}
				}
			});

			return result.ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerId"></param>
		/// <returns></returns>
		public IEnumerable<AssemblyRefItems> GetReferenceAssemblyIds(int headerId)
		{
			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				return dbContext.SqlQuery<AssemblyRefItems>(GetReferenceAssemblyIdsSqlString(headerId)).ToList();
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="assemblyIds"></param>
		/// <param name="headerId"></param>
		/// <param name="lineItemType"></param>
		/// <param name="chunks"></param>
		/// <returns></returns>
		public IEnumerable<AssemblyRefTreeStructure> GetAssemblyLevelByAssemblyIds(IEnumerable<int> assemblyIds, int headerId, int lineItemType = (int)CommonLogic.LineItemTypes.Assembly, int chunks = 1000)
		{
			var result = new List<AssemblyRefTreeStructure>();

			object _lock = new object();

			if (assemblyIds == null || !assemblyIds.Any())
			{
				return result;
			}

			var assemblyGroup = assemblyIds.Select((e, i) => new { Index = i, Value = e }).GroupBy(e => e.Index / chunks).Select(e => e.Select(x => x.Value)).ToList();

			ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };

			Parallel.ForEach(assemblyGroup, parallelOptions, ids =>
			{
				using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{
					var entities = dbContext.SqlQuery<AssemblyRefTreeStructure>(GetSqlString(ids, headerId, lineItemType)).ToList();

					lock (_lock)
					{
						result.AddRange(entities);
					}
				}
			});

			return result.GroupBy(e => e.AssemblyId).Select(e => e.OrderByDescending(i => i.Level).FirstOrDefault()).ToList();
		}

		private IEnumerable<AssemblyRefTreeStructure> GetAssemblyLevelOfHeader(int headerId, int lineItemType = (int)CommonLogic.LineItemTypes.Assembly)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var assemblyTreeIds = dbcontext.ExecuteStoredProcedure<AssemblyRefTreeStructure>("EST_ASSEMBLY_TREE_IDS_SP", headerId, lineItemType).ToList();

				if (assemblyTreeIds == null || !assemblyTreeIds.Any())
				{
					return new List<AssemblyRefTreeStructure>();
				}

				return assemblyTreeIds.ToList();
			}
		}

		private EstAssemblyTypeEntity GetAssemblyTypeByAssemblyCategoryId(int? assemblyCategoryId)
		{
			if (!assemblyCategoryId.HasValue)
			{
				return null;
			}

			if (this._AssemblyCategoryId2AssemblyTypeMap.ContainsKey(assemblyCategoryId.Value))
			{
				return this._AssemblyCategoryId2AssemblyTypeMap[assemblyCategoryId.Value];
			}

			var assemblyType = this._EstAssemblyTypeLogic.GetByAssemblyCategoryId(assemblyCategoryId);

			this._AssemblyCategoryId2AssemblyTypeMap.Add(assemblyCategoryId.Value, assemblyType);

			return assemblyType;
		}

		private MdcCommoditySearchVEntity GetMdcCommoditySearchVEntity(int materialId)
		{
			if (this._MdcCommoditySearchVEntityCache.ContainsKey(materialId))
			{
				return this._MdcCommoditySearchVEntityCache[materialId];
			}

			var mdcCommoditySearchVEntity = this._MdcCommoditySearchVLogic.GetItemByKey(materialId);

			this._MdcCommoditySearchVEntityCache.Add(materialId, mdcCommoditySearchVEntity);

			return mdcCommoditySearchVEntity;
		}

		private void UpdateResourcesFromAssembly(IEnumerable<EstResourceEntity> resourceRef2Assembly, EstLineItemEntity assembly)
		{
			if (assembly == null)
			{
				return;
			}

			if (resourceRef2Assembly != null && resourceRef2Assembly.Any())
			{
				// set the short key by the composite assembly or normal assembly
				var assemblyType = GetAssemblyTypeByAssemblyCategoryId(assembly.EstAssemblyCatFk);

				foreach (var refAssemblyRes in resourceRef2Assembly)
				{
					refAssemblyRes.CostUnit = assembly.CostUnit + assembly.MarkupCostUnit;

					refAssemblyRes.MarkupCostUnit = assembly.MarkupCostUnit;

					refAssemblyRes.BasUomFk = assembly.BasUomFk;

					refAssemblyRes.HoursUnit = assembly.HoursUnit;

					refAssemblyRes.DayWorkRateUnit = assembly.DayWorkRateUnit;

					if (!refAssemblyRes.EstHeaderAssemblyFk.HasValue)
					{
						refAssemblyRes.EstHeaderAssemblyFk = assembly.EstHeaderFk;
					}

					if (assemblyType != null)
					{
						UpdateAssemblyType(assemblyType, refAssemblyRes, assembly);
					}

					if (assembly.UserDefinedcolValEntity == null)
					{
						continue;
					}

					UpdateUserDefinedColValOfResource(refAssemblyRes, assembly.UserDefinedcolValEntity as UserDefinedcolValEntity);
				}
			}
		}

		private IEnumerable<EstLineItemParamEntity> GetAssemblyParametersByIds(int headerId, IEnumerable<int> assemblyIds)
		{
			return new EstimateParameterLineItemLogic().GetByFilter(e => e.EstHeaderFk == headerId && assemblyIds.Contains(e.EstLineItemFk));
		}

		private Dictionary<int, IEnumerable<IEstimateRuleCommonParamEntity>> GetAssemblyParameter2Dictionary(IEnumerable<EstLineItemParamEntity> parametersList)
		{
			return parametersList.GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.Select(i => i as IEstimateRuleCommonParamEntity));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="assemblySource"></param>
		/// <param name="filterInfo"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> UpdateAssembliesByBaseAssembly(EstLineItemEntity assemblySource, EstimateAssemblyResourcesUpdateData filterInfo = null)
		{
			//get the reference assembly id and ite reference level
			// var assemblyLevelMap = GetAssemblyLevelByAssemblyId(assemblySource.EstHeaderFk, assemblySource.Id);
			var assemblyLevelMap = this.GetAssemblyLevelByAssemblyIds(new List<int>() { assemblySource.Id }, assemblySource.EstHeaderFk).Where(e => e.AssemblyId != assemblySource.Id);

			var assemblyIdsToUpdate = assemblyLevelMap.Select(e => e.AssemblyId).ToList();

			//get the assemblies which reference to current assembly
			var assembliesToUpdate = new EstimateMainLineItemLogic().GetSearchList(e => assemblyIdsToUpdate.Contains(e.Id) && e.EstHeaderFk == assemblySource.EstHeaderFk).ToList();

			//get the resource of all reference assembly
			var resourcesToUpdate = new EstimateMainResourceLogic().GetListByFilter(e => assemblyIdsToUpdate.Contains(e.EstLineItemFk) && e.EstHeaderFk == assemblySource.EstHeaderFk).ToList();

			UpdateMarkupCostUnitFromAssembly(resourcesToUpdate, assemblySource.EstHeaderFk);

			var parametersDictionary = GetAssemblyParameter2Dictionary(GetAssemblyParametersByIds(assemblySource.EstHeaderFk, assemblyIdsToUpdate));

			//update current assembly to the resources which reference to this assembly
			var refAssemblyResources = resourcesToUpdate.Where(e => e.EstAssemblyFk == assemblySource.Id).ToList();

			_ResourceAttachmentInitializer.AttachUserDefinedColValEntityToResourcesInTree(refAssemblyResources);

			var allAssemblies = assembliesToUpdate.Append(assemblySource);

			_LineItemAttachmentInitializer.AttachUDPEntityToLineItems(allAssemblies);

			UpdateResourcesFromAssembly(refAssemblyResources, assemblySource);

			return UpdateAssembliesBase(assemblySource.EstHeaderFk, filterInfo, assemblyLevelMap, assembliesToUpdate, resourcesToUpdate, parametersDictionary);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerId"></param>
		/// <param name="filterInfo"></param>
		/// <param name="resources"></param>
		/// <param name="isPlantAssembly"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> UpdateAssembliesByHeader(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, List<EstResourceEntity> resources, bool isPlantAssembly = false)
		{
			// get assembly type
			var lineItemType = isPlantAssembly ? (int)CommonLogic.LineItemTypes.PlantAssembly : (int)CommonLogic.LineItemTypes.Assembly;

			// get map assembly Id to reference level
			var assemblyLevelMap = new List<AssemblyRefTreeStructure>();
			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var assemblyIds = dbContext.Entities<EstLineItemEntity>().Where(e => e.EstHeaderFk == headerId && e.LineItemType == lineItemType).Select(x => x.Id).ToList();
				assemblyLevelMap = this.GetAssemblyLevelByAssemblyIds(assemblyIds, headerId, lineItemType, GetChunkSizeBasedOnRecordCount(assemblyIds.Count())).OrderBy(x=>x.AssemblyId).ToList();
			}

			// get the assembly of header
			var assembliesToUpdate = new EstimateMainLineItemLogic().GetAssemblyList(headerId, false, lineItemType);

			if (filterInfo.IsPrjAssembly && filterInfo.UpdateAssemblyResources)
			{
				var resourceItems = new EstimateMainResourceLogic().GetSearchList(e => e.EstHeaderFk == headerId).ToList();
				AttachResourceToLineItem(assembliesToUpdate, resourceItems);
				new EstLineItemUpdateHelper(headerId, filterInfo.ProjectId, new EstLineItemUpdateOption()
				{
					IsUpdatePlantAssembly = true,
				}).UpdatePlantAssembly(assembliesToUpdate, resourceItems.ToList(), true);
			}

			// get assembly resources of header
			var resourcesToUpdate = new EstimateMainResourceLogic().GetSearchList(e => e.EstHeaderFk == headerId).ToList();

			//filter out the assemly which refer to the version job
			if (filterInfo.IsPrjAssembly)
			{
				var estHeaderLogic = new EstimateMainHeaderLogic();
				var versionHeaderids = estHeaderLogic.GetVersionHeaderIdsOfProjectIds(new List<int>() { filterInfo.ProjectId }).ToList();
				var versionJobIds = estHeaderLogic.GetJobIdsByEstHeaderIds(versionHeaderids).ToList();

				assembliesToUpdate = assembliesToUpdate.Where(e => !versionJobIds.Contains(e.LgmJobFk));

				var assemblyIds = assembliesToUpdate.Select(e => e.Id).ToList();
				resourcesToUpdate = resourcesToUpdate.Where(e => assemblyIds.Contains(e.EstLineItemFk)).ToList();
			}

			resources.AddRange(resourcesToUpdate);
			UpdateMarkupCostUnitFromAssembly(resourcesToUpdate, headerId, null, lineItemType);

			UpdatePlantAssemMultipliersFromWizard(resourcesToUpdate, filterInfo);

			var estAssemblyCatFkList = assembliesToUpdate.Where(e => e.EstAssemblyCatFk.HasValue).Select(s => s.EstAssemblyCatFk.Value).Distinct();

			//get the parameter of assembly, include from assembly catalog
			_AssemblyParamUpdateResult = UpdateAssemblyParam(headerId, filterInfo, assembliesToUpdate);

			return UpdateAssembliesBase(headerId, filterInfo, assemblyLevelMap, assembliesToUpdate, resourcesToUpdate, _AssemblyParamUpdateResult.AssemblyId2ParamsMap);

		}

		/// <summary>
		/// Determines an optimal chunk size based on the record count.
		/// </summary>
		/// <param name="recordCount"></param>
		/// <returns></returns>
		public int GetChunkSizeBasedOnRecordCount(int recordCount)
		{

			//todo - can be improved after proper analysis and testing
			if (recordCount <= 100)
			{
				return Math.Max(10, recordCount / 10);  // Minimum chunk size of 10
			}
			else if (recordCount <= 500)
			{
				return 30 + (20 * (recordCount - 100) / 400);  // Smooth transition from 30 to 50
			}
			else if (recordCount <= 1000)
			{
				return 50 + (50 * (recordCount - 500) / 500);  // Smooth transition from 50 to 100
			}
			else if (recordCount <= 5000)
			{
				return 100 + (50 * (recordCount - 1000) / 4000);  // Smooth transition from 100 to 150
			}
			else if (recordCount <= 10000)
			{
				return 150 + (50 * (recordCount - 5000) / 5000);  // Smooth transition from 150 to 200
			}
			else if (recordCount <= 50000)
			{
				return Math.Min(500, 200 + (300 * (recordCount - 10000) / 40000));  // Smooth transition from 200 to 500
			}
			else
			{
				return 500;  // Hard cap at 500
			}
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="headerId"></param>
		/// <param name="filterInfo"></param>
		/// <param name="assemblyIds"></param>
		/// <param name="isPlantAssembly"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> UpdateAssembliesByIds(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<int> assemblyIds, bool isPlantAssembly = false)
		{
			var lineItemType = isPlantAssembly ? (int)CommonLogic.LineItemTypes.PlantAssembly : (int)CommonLogic.LineItemTypes.Assembly;

			var assemblyLevelMap = this.GetAssemblyLevelByAssemblyIds(assemblyIds, headerId, lineItemType, GetChunkSizeBasedOnRecordCount(assemblyIds.Count()));

			var assemblyIdsToUpdate = assemblyLevelMap.Select(e => e.AssemblyId).ToList();

			var assembliesToUpdate = (filterInfo != null && filterInfo.IsPrjAssembly) ? new EstimateMainLineItemLogic().GetPrjAssembliesByIds(assemblyIdsToUpdate, filterInfo.ProjectId, isPlantAssembly) :
				new EstimateMainLineItemLogic().GetAssembliesByIds(assemblyIdsToUpdate);

			if (filterInfo.IsPrjAssembly && filterInfo.UpdateAssemblyResources)
			{
				var resources = new EstimateMainResourceLogic().GetSearchList(e => assemblyIdsToUpdate.Contains(e.EstLineItemFk)).ToList();
				AttachResourceToLineItem(assembliesToUpdate, resources);
				new EstLineItemUpdateHelper(headerId, filterInfo.ProjectId, new EstLineItemUpdateOption()
				{
					IsUpdatePlantAssembly = true,
				}).UpdatePlantAssembly(assembliesToUpdate, resources.ToList(), true);
			}


			var resourcesToUpdate = new EstimateMainResourceLogic().GetSearchList(e => assemblyIdsToUpdate.Contains(e.EstLineItemFk)).ToList();

			UpdateMarkupCostUnitFromAssembly(resourcesToUpdate, headerId);

			UpdatePlantAssemMultipliersFromWizard(resourcesToUpdate, filterInfo);

			//get the parameter of assembly, include from assembly catalog
			_AssemblyParamUpdateResult = UpdateAssemblyParam(headerId, filterInfo, assembliesToUpdate);

			return UpdateAssembliesBase(headerId, filterInfo, assemblyLevelMap, assembliesToUpdate, resourcesToUpdate, _AssemblyParamUpdateResult.AssemblyId2ParamsMap);
		}

		private void UpdateMarkupCostUnitFromAssembly(IEnumerable<EstResourceEntity> resourcesToUpdate, int estHeaderId, IEnumerable<EstLineItemEntity> assembliesOfHeader = null, int lineItemType = (int)CommonLogic.LineItemTypes.Assembly)
		{
			if (resourcesToUpdate == null || !resourcesToUpdate.Any())
			{
				return;
			}

			var referenceAssemblyIds = resourcesToUpdate.Where(e => e.EstAssemblyFk.HasValue && !e.EstAssemblyTypeFk.HasValue).Select(e => e.EstAssemblyFk.Value).ToList();

			var assemblies = assembliesOfHeader != null && assembliesOfHeader.Any() ? assembliesOfHeader : new EstimateMainLineItemLogic().GetAssembliesByIds(referenceAssemblyIds, lineItemType, estHeaderId);

			foreach (var resource in resourcesToUpdate)
			{
				if (!resource.EstAssemblyFk.HasValue || resource.EstAssemblyTypeFk.HasValue)
				{
					continue;
				}

				var referenceAssembly = assemblies.FirstOrDefault(e => e.Id == resource.EstAssemblyFk.Value);

				if (referenceAssembly == null)
				{
					continue;
				}

				resource.MarkupCostUnit = referenceAssembly.MarkupCostUnit;
			}
		}


		private void UpdatePlantAssemMultipliersFromWizard(IEnumerable<IScriptEstResource> resources, EstimateAssemblyResourcesUpdateData filterInfo)
		{
			//todo refactor logic in DEV-31527
			if (filterInfo.UpdateMultipliersFrmPlantEstimate)
			{
				foreach (var resource in resources)
				{
					var currentJobId = resource.LgmJobFk.HasValue ? resource.LgmJobFk : EstJobHelper.GetProjectJobId(filterInfo.ProjectId);
					new PlantAssemblyUpdateManager(null, resource.EstHeaderFk, null).UpdateMultipliersFrmPlantEstimate((EstResourceEntity)resource, currentJobId, resource.WorkOperationTypeFk, false, filterInfo.IsMasterAssembly);
				}
			}
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="headerId"></param>
		/// <param name="filterInfo"></param>
		/// <param name="assemblyLevelList"></param>
		/// <param name="lineItemsToUpdate"></param>
		/// <param name="resourcesToUpdate"></param>
		/// <param name="parametersOfAssemblies"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> UpdateAssembliesBase(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<AssemblyRefTreeStructure> assemblyLevelList, IEnumerable<EstLineItemEntity> lineItemsToUpdate, IEnumerable<EstResourceEntity> resourcesToUpdate, Dictionary<int, IEnumerable<IEstimateRuleCommonParamEntity>> parametersOfAssemblies)
		{
			var result = new List<EstLineItemEntity>();

			if (assemblyLevelList == null || !assemblyLevelList.Any())
			{
				return result;
			}

			if (lineItemsToUpdate == null || !lineItemsToUpdate.Any())
			{
				return result;
			}

			/* initialize the changeTracker */
			var lineItemChangedTracker = new ObjectChangedTracker<EstLineItemEntity>(lineItemsToUpdate);

			var resourceChangedTracker = new ObjectChangedTracker<EstResourceEntity>(resourcesToUpdate);

			_LineItemAttachmentInitializer.AttachUDPEntityToLineItems(lineItemsToUpdate);

			_ResourceAttachmentInitializer.AttachUDPEntityToResources(resourcesToUpdate);

			if (filterInfo != null && filterInfo.IsPrjAssembly)
			{
				// for old data, change resoure assigment: master assembly to project assembly
				List<EstLineItemEntity> prjAssemblies = new List<EstLineItemEntity>();
				var assemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();
				foreach (var resource in resourcesToUpdate)
				{
					if (resource.EstHeaderAssemblyFk.HasValue && resource.EstHeaderAssemblyFk.Value == assemblyHeaderId)
					{
						if (prjAssemblies.Count == 0)
						{
							prjAssemblies = new EstimateMainLineItemLogic().GetPrjAssembliesByPrjId(filterInfo.ProjectId).ToList();
						}

						var prjAssembly = prjAssemblies.Where(e => e.EstHeaderAssemblyFk == resource.EstHeaderAssemblyFk && e.EstAssemblyFk == resource.EstAssemblyFk).FirstOrDefault();
						if (prjAssembly != null)
						{
							resource.EstHeaderAssemblyFk = prjAssembly.EstHeaderFk;
							resource.EstAssemblyFk = prjAssembly.Id;
						}
					}
				}

				// for old data, change resoure assigment: master plant assembly to project plant assembly
				List<EstLineItemEntity> prjPlantAssemblies = new List<EstLineItemEntity>();
				var plantAssemblyHeaderId = new EstimateAssembliesLogic().GetHeaderPlantFkByCurrentLineItemContext();
				foreach (var resource in resourcesToUpdate)
				{
					if (resource.EstHeaderAssemblyFk.HasValue && resource.EstHeaderAssemblyFk.Value == plantAssemblyHeaderId)
					{
						if (prjPlantAssemblies.Count == 0)
						{
							prjPlantAssemblies = new EstimateMainLineItemLogic().GetPrjPlantAssembliesByPrjId(filterInfo.ProjectId).ToList();
						}

						var prjPlantAssembly = prjPlantAssemblies.Where(e => e.EstHeaderAssemblyFk == resource.EstHeaderAssemblyFk && e.EstAssemblyFk == resource.EstAssemblyFk).FirstOrDefault();
						if (prjPlantAssembly != null)
						{
							resource.EstHeaderAssemblyFk = prjPlantAssembly.EstHeaderFk;
							resource.EstAssemblyFk = prjPlantAssembly.Id;
						}
					}
				}
			}

			//generate a map (assembly id to assembly)
			var assembly2Dictionary = lineItemsToUpdate.ToDictionary(e => e.Id, e => e);

			/* update resources from costcode and material */
			if (filterInfo != null)
			{
				_mdcContextId = new EstimateMainLineItemLogic().GetCompanyInfoProvider().GetMasterDataContext();

				Dictionary<int, int?> resId2JobId = new Dictionary<int, int?>();
				// get resource job mapping
				if (filterInfo.IsPrjAssembly && (filterInfo.UpdateCostCodes || filterInfo.UpdateMaterials))
				{
					resId2JobId = GetResourceJobWithAssembly(resourcesToUpdate, assembly2Dictionary);
				}

				if (filterInfo.IsPrjAssembly && filterInfo.UpdateCostCodes)
				{
					UpdateCostCodeTypeResourcesFromPrjCostCode(filterInfo.ProjectId, resourcesToUpdate, resId2JobId, filterInfo.UpdateCostTypes);
				}
				else if (filterInfo.UpdateCostCodes)
				{
					UpdateCostCodeTypeResources(_mdcContextId, resourcesToUpdate, filterInfo.UpdateCostTypes);
				}

				if (filterInfo.IsPrjAssembly && filterInfo.UpdateMaterials)
				{
					var noMapPrjMaterialResIds = UpdateMaterialTypeResourcesFromPrjCostCode(filterInfo.ProjectId, resourcesToUpdate, resId2JobId, filterInfo.UpdateCostTypes);
					// update from master material
					if (noMapPrjMaterialResIds.Any())
					{
						var noMapPrjMaterialResources = resourcesToUpdate.Where(e => noMapPrjMaterialResIds.Contains(e.Id));
						UpdateMaterialTypeResources(_mdcContextId, noMapPrjMaterialResources, filterInfo.UpdateCostTypes, filterInfo.IsPrjAssembly, filterInfo.ProjectId);
					}
				}
				else if (filterInfo.UpdateMaterials)
				{
					UpdateMaterialTypeResources(_mdcContextId, resourcesToUpdate, filterInfo.UpdateCostTypes, filterInfo.IsPrjAssembly, filterInfo.ProjectId);
				}

				if (filterInfo.UpdateFromMasterEA)
				{
					var plantEstimateSettingsLogic = Injector.Get<ILogisticPlantEstimateSettings>();

					var estPriceListFk = plantEstimateSettingsLogic.DefaultEstimatePriceListFromCompany();

					var plant2EstimatePriceListLineItemLogic = RVPARB.BusinessEnvironment.GetExportedValue<IPlant2EstimatePriceListLineItemLogic>();
					var assemblyHeaderFk = new EstimateAssembliesLogic().GetHeaderPlantFkByCurrentLineItemContext();

					var sourceMasterAssemblies = plant2EstimatePriceListLineItemLogic.GetEstimate2PlantMapping(new List<int> { estPriceListFk.Value }, new List<int> { assemblyHeaderFk });
					var mdcAssemblyId2Info = sourceMasterAssemblies.GroupBy(e => e.EstimateLineItemFk).ToDictionary(e => e.Key, e => e.FirstOrDefault());

					var resources = resourcesToUpdate.Where(item => item.EstAssemblyFk.HasValue && mdcAssemblyId2Info.ContainsKey(item.EstAssemblyFk.Value) && item.EstHeaderAssemblyFk == assemblyHeaderFk).ToList();
					var assembliesToUpdateIds = resources.Select(e => e.EstLineItemFk).Distinct();
					var assembliesToUpdate = new EstimateMainLineItemLogic().GetAssembliesByIds(assembliesToUpdateIds, headerId);

					AttachResourceToLineItem(assembliesToUpdate, resourcesToUpdate);
					new EstLineItemUpdateHelper(assemblyHeaderFk, null, new EstLineItemUpdateOption()
					{
						IsUpdatePlantAssembly = true,
					}).UpdatePlantAssembly(assembliesToUpdate, resourcesToUpdate.ToList(), true);

				}
			}

			//generate a map (lineItem Id to its resources)
			var resourceOfLineItem2Dictionary = new ConcurrentDictionary<int, IEnumerable<EstResourceEntity>>(
				resourcesToUpdate
					.GroupBy(e => e.EstLineItemFk)
					.ToDictionary(e => e.Key, e => e.Select(i => i))
			);

			//generate a map (assembly id to resources reference this assembly)
			var resourceOfAssembly2Dictionary = new ConcurrentDictionary<int?, IEnumerable<EstResourceEntity>>(
				resourcesToUpdate
					.Where(e => e.EstAssemblyFk.HasValue)
					.GroupBy(e => e.EstAssemblyFk)
					.ToDictionary(e => e.Key, e => e.Select(i => i))
			);

			//load exchange rate
			new ExchangeRateHelper(filterInfo != null && filterInfo.IsPrjAssembly ? filterInfo.ProjectId : default(int?)).ExchangeRate(resourcesToUpdate);

			var userDefinedColumnValueLogic = new AssemblyUDPCalculationLogic(headerId);

			var estTotalCalculator = new AssemblyTotalCalculator(headerId, null);

			var assemblyCatIds = lineItemsToUpdate.Where(e => e.EstAssemblyCatFk.HasValue).Select(e => e.EstAssemblyCatFk.Value).Distinct().ToList();

			this._AssemblyCategoryId2AssemblyTypeMap = this._EstAssemblyTypeLogic.GetByAssemblyCategoryIds(assemblyCatIds, filterInfo != null && filterInfo.SelectUpdateScope == 1);

			var estDetailCalculateHelper = new EstDetailCalculateHelper(headerId, null);

			var _prjCostCodesLock = new Object();
			var udpLock = new Object();
			Parallel.ForEach(assemblyLevelList, new ParallelOptions { MaxDegreeOfParallelism = Environment.ProcessorCount }, assemblyLevelItem =>
			{
				if (!assembly2Dictionary.TryGetValue(assemblyLevelItem.AssemblyId, out var assembly) || assembly == null)
				{
					return;
				}

				//get resources of current assembly
				var resourcesOfLineItem = resourceOfLineItem2Dictionary.TryGetValue(assemblyLevelItem.AssemblyId, out var resources) ? resources : new List<EstResourceEntity>();

				//calculate detail property
				if (parametersOfAssemblies.ContainsKey(assemblyLevelItem.AssemblyId) && parametersOfAssemblies[assemblyLevelItem.AssemblyId] != null)
				{
					var parameter = parametersOfAssemblies[assemblyLevelItem.AssemblyId].GroupBy(e => e.Code).ToDictionary(e => e.Key, e => e.First().ParameterValue);

					Func<IScriptEstLineItem, Dictionary<string, decimal>> getParameters = (entity) =>
				{
					return parameter;
				};

					estDetailCalculateHelper.CalculateLineItemAndResourceDetails(assembly, resourcesOfLineItem, getParameters);
				}
				else
				{
					estDetailCalculateHelper.CalculateLineItemAndResourceDetails(assembly, resourcesOfLineItem, null);
				}

				//calculate quantity, cost unit, cost total and so on
				estTotalCalculator.CalculateLineItemAndResourcesInList(assembly, resourcesOfLineItem);

				_LineItemAttachmentInitializer.AttachUDPEntityToLineItem(assembly);

				lock (udpLock)
				{
					userDefinedColumnValueLogic.CalUserDefinedValOfLiAndResInList(assembly, resourcesOfLineItem);
				}

				//update the resource which reference to current assembly
				if (resourceOfAssembly2Dictionary.TryGetValue(assemblyLevelItem.AssemblyId, out var resourceRef2Assembly) && (filterInfo == null || !filterInfo.IsPrjPlantAssembly))
				{
					UpdateResourcesFromAssembly(resourceRef2Assembly, assembly);

					foreach (var refAssemblyRes in resourceRef2Assembly)
					{
						if (refAssemblyRes.MdcCostCodeFk.HasValue)
						{
							if (filterInfo != null && filterInfo.IsPrjAssembly)
							{
								if (_prjCostCodes == null)
								{
									lock (_prjCostCodesLock)
									{
										var prjCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();
										_prjCostCodes = prjCostCodeLogic.GetProjectCostCodes(filterInfo.ProjectId).ToList();
									}
								}

								var costCode = _prjCostCodes.FirstOrDefault(e => e.MdcCostCodeFk == refAssemblyRes.MdcCostCodeFk);
								if (costCode != null)
								{
									refAssemblyRes.IsBudget = costCode.IsBudget;
									refAssemblyRes.IsCost = costCode.IsCost;
								}
							}
							else
							{
								if (_masterCostCodes == null)
								{
									lock (_prjCostCodesLock)
									{
										var costCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<ICostCodesInfoProviderLogic>();
										_masterCostCodes = costCodeLogic.GetCostCodesByMdcContext(_mdcContextId).ToList();
									}
								}

								var costCode = _masterCostCodes.FirstOrDefault(e => e.Id == refAssemblyRes.MdcCostCodeFk.Value);
								if (costCode != null)
								{
									refAssemblyRes.IsBudget = costCode.IsBudget;
									refAssemblyRes.IsCost = costCode.IsCost;
								}
							}
						}
					}
				}
			});

			/* save the modification lineItems and resources*/
			var lineItemToSave = lineItemChangedTracker.GetChangedObjects();

			var resourceToSave = resourceChangedTracker.GetChangedObjects();

			using (var transactionScope = TransactionScopeFactory.Create())
			{
				new EstimateMainLineItemLogic().BulkSave(ModelBuilder.DbModel, lineItemToSave);

				new EstimateMainResourceLogic().BulkSave(ModelBuilder.DbModel, resourceToSave);

				userDefinedColumnValueLogic.SaveModifiedUDP();

				if (_AssemblyParamUpdateResult != null)
				{
					//if(_AssemblyParamUpdateResult.AssemblyParamsToDelete != null && _AssemblyParamUpdateResult.AssemblyParamsToDelete.Any())
					//{
					//	new EstimateParameterLineItemLogic().Delete(_AssemblyParamUpdateResult.AssemblyParamsToDelete);
					//}

					//if(_AssemblyParamUpdateResult.AssemblyCatParamsToDelete != null && _AssemblyParamUpdateResult.AssemblyCatParamsToDelete.Any())
					//{
					//	new EstimateParameterAssemblyCatLogic().Delete(_AssemblyParamUpdateResult.AssemblyCatParamsToDelete);
					//}

					if (_AssemblyParamUpdateResult.AssemblyParamsToUpdate != null && _AssemblyParamUpdateResult.AssemblyParamsToUpdate.Any())
					{
						var newItems = _AssemblyParamUpdateResult.AssemblyParamsToUpdate.Where(e => e.Id == 0 || e.Version == 0).ToList();

						if (newItems.Any())
						{
							var newIds = new Queue<int>(new EstimateParameterLineItemLogic().GetNextIds(newItems.Count()));

							foreach (var item in newItems)
							{
								item.Id = newIds.Dequeue();
							}
						}

						_AssemblyParamUpdateResult.AssemblyParamsToUpdate.SaveTranslate(this.UserLanguageId, new Func<EstLineItemParamEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

						new EstimateParameterLineItemLogic().Save(_AssemblyParamUpdateResult.AssemblyParamsToUpdate);
					}

					if (_AssemblyParamUpdateResult.AssemblyParamsValueToUpdate != null && _AssemblyParamUpdateResult.AssemblyParamsValueToUpdate.Any())
					{
						_AssemblyParamUpdateResult.AssemblyParamsValueToUpdate.Where(e => e.Version == 0).SaveTranslate(this.UserLanguageId, new Func<EstRuleParamValueEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

						new EstimateRulePrjEstRuleParamValueLogic().Save(_AssemblyParamUpdateResult.AssemblyParamsValueToUpdate);
					}

					if (_AssemblyParamUpdateResult.AssemblyCatParamsToUpdate != null && _AssemblyParamUpdateResult.AssemblyCatParamsToUpdate.Any())
					{
						var newItems = _AssemblyParamUpdateResult.AssemblyCatParamsToUpdate.Where(e => e.Id == 0 || e.Version == 0).ToList();

						if (newItems.Any())
						{
							var newIds = new Queue<int>(new EstimateParameterAssemblyCatLogic().GetNextIds(newItems.Count()));

							foreach (var item in newItems)
							{
								item.Id = newIds.Dequeue();
							}
						}

						_AssemblyParamUpdateResult.AssemblyCatParamsToUpdate.SaveTranslate(this.UserLanguageId, new Func<EstAssemblyParamEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

						new EstimateParameterAssemblyCatLogic().Save(_AssemblyParamUpdateResult.AssemblyCatParamsToUpdate);
					}
				}

				transactionScope.Complete();
			}

			return lineItemsToUpdate;
		}

		private void AttachResourceToLineItem(IEnumerable<EstLineItemEntity> lineItems, IEnumerable<EstResourceEntity> resources)
		{
			if (lineItems == null || !lineItems.Any() || resources == null || !resources.Any())
			{
				return;
			}

			var lineItemId2ResourcesMap = resources.GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var lineItem in lineItems)
			{
				if (!lineItemId2ResourcesMap.ContainsKey(lineItem.Id) || lineItemId2ResourcesMap[lineItem.Id] == null)
				{
					continue;
				}

				lineItem.EstResourceEntities = lineItemId2ResourcesMap[lineItem.Id].Where(e => !e.EstResourceFk.HasValue).ToList();
			}

			/* free for memory */
			lineItemId2ResourcesMap = null;
		}

		private bool CompareParam(IEstimateRuleCommonParamEntity source, BasicsCustomizeEstParameterEntity target)
		{
			var retVal = false;

			if (source == null || target == null)
			{
				return retVal;
			}

			if (source.ValueType != target.ParamvaluetypeFk)
			{
				source.ValueType = target.ParamvaluetypeFk;
				retVal = true;
			}

			switch (target.ParamvaluetypeFk)
			{
				case 1:// double
					{
						if (source.ParameterValue != target.DefaultValue)
						{
							source.ParameterValue = target.DefaultValue;
							retVal = true;
						}

						target.ValueDetail = target.DefaultValue.ToString(CultureInfo.InvariantCulture);
						target.ValueText = String.Empty;
						//target.ParameterText = String.Empty;
						break;
					}
				case 2: // bool
					{
						if (target.DefaultValue == 0)
						{
							if (source.ParameterValue != 0)
							{
								source.ParameterValue = 0;
								retVal = true;
							}
						}
						else
						{
							if (source.ParameterValue != 1)
							{
								source.ParameterValue = 1;
								retVal = true;
							}
						}

						target.ValueDetail = String.Empty;
						target.ValueText = String.Empty;
						//target.ParameterText = String.Empty;
						break;
					}
				case 3:// text
					{
						target.DefaultValue = 0;
						if (source.ParameterValue != 0)
						{
							source.ParameterValue = 0;
							retVal = true;
						}
						target.ValueDetail = target.ValueText;
						target.ParameterText = target.ValueText;
						break;
					}
			}

			if (source.DefaultValue != target.DefaultValue)
			{
				source.DefaultValue = target.DefaultValue;
				retVal = true;
			}

			if (source.ParameterText != target.ParameterText)
			{
				source.ParameterText = target.ParameterText;
				retVal = true;
			}

			if (source.ValueText != target.ValueText)
			{
				source.ValueText = target.ValueText;
				retVal = true;
			}

			if (source.ValueDetail != target.ValueDetail)
			{
				source.ValueDetail = target.ValueDetail;
				retVal = true;
			}

			if (source.EstParameterGroupFk != target.ParametergroupFk)
			{
				source.EstParameterGroupFk = target.ParametergroupFk;
				retVal = true;
			}

			if (source.UomFk != target.UomFk)
			{
				source.UomFk = target.UomFk;
				retVal = true;
			}

			if (source.IsLookup != target.Islookup)
			{
				source.IsLookup = target.Islookup;
				retVal = true;
			}

			if (source.EstRuleParamValueFk != target.RuleParamValueFk)
			{
				source.EstRuleParamValueFk = target.RuleParamValueFk;
			}

			if (source.DescriptionInfo.Description != target.DescriptionInfo.Description)
			{
				source.DescriptionInfo.Description = target.DescriptionInfo.Description;
				retVal = true;
			}

			return retVal;
		}

		private bool CompareParam(IEstimateRuleCommonParamEntity source, IEstimateRuleCommonParamEntity target)
		{
			var retVal = false;

			if (source == null || target == null)
			{
				return retVal;
			}

			if (source.DefaultValue != target.DefaultValue)
			{
				source.DefaultValue = target.DefaultValue;
				retVal = true;
			}

			if (source.EstParameterGroupFk != target.EstParameterGroupFk)
			{
				source.EstParameterGroupFk = target.EstParameterGroupFk;
				retVal = true;
			}

			if (source.ValueDetail != target.ValueDetail)
			{
				source.ValueDetail = target.ValueDetail;
				retVal = true;
			}

			if (source.UomFk != target.UomFk)
			{
				source.UomFk = target.UomFk;
				retVal = true;
			}

			if (source.IsLookup != target.IsLookup)
			{
				source.IsLookup = target.IsLookup;
				retVal = true;
			}

			if (source.EstRuleParamValueFk != target.EstRuleParamValueFk)
			{
				source.EstRuleParamValueFk = target.EstRuleParamValueFk;
			}

			if (source.ValueText != target.ValueText)
			{
				source.ValueText = target.ValueText;
				retVal = true;
			}

			if (source.ParameterText != target.ParameterText)
			{
				source.ParameterText = target.ParameterText;
				retVal = true;
			}

			if (source.ParameterValue != target.ParameterValue)
			{
				source.ParameterValue = target.ParameterValue;
				retVal = true;
			}

			if (source.ValueType != target.ValueType)
			{
				source.ValueType = target.ValueType;
				retVal = true;
			}

			if (source.DescriptionInfo.Description != target.DescriptionInfo.Description)
			{
				source.DescriptionInfo.Description = target.DescriptionInfo.Description;
				retVal = true;
			}

			return retVal;
		}

		private IEnumerable<EstLineItemParamEntity> GetAssemblyParams(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<EstLineItemEntity> lineItemsToUpdate)
		{
			if (filterInfo.SelectUpdateScope == 1)
			{
				return new EstimateParameterLineItemLogic().GetByFilter(e => e.EstHeaderFk == headerId);
			}
			else
			{
				return new EstimateParameterLineItemLogic().GetListByLineItemIds(lineItemsToUpdate.Select(e => e.Id).Distinct().ToList(), headerId);
			}
		}

		private IEnumerable<EstRuleParamValueEntity> GetRuleParamVaules(int projectFk, IEnumerable<string> codes)
		{
			var valueTypes = new List<int>();
			return new EstimateRulePrjEstRuleParamValueLogic().GetParamValueListByContextAndCodes(projectFk, codes, valueTypes);
		}

		private IEnumerable<BasicsCustomizeEstimationParameterValueEntity> GetCustomizeParamVaules(IEnumerable<int> paramFks)
		{
			return new BasicsCustomizeEstimationParameterValueLogic().GetListByFilter(e => paramFks.Contains(e.ParameterFk));
		}

		private IEnumerable<EstAssemblyParamEntity> GetAssemblyCatParams(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<EstAssemblyCatEntity> estAssemblyCats)
		{
			if (filterInfo.SelectUpdateScope == 1)
			{
				return new EstimateParameterAssemblyCatLogic().GetByFilter(e => e.EstHeaderFk == headerId);
			}
			else
			{
				if (estAssemblyCats == null || !estAssemblyCats.Any())
				{
					return new List<EstAssemblyParamEntity>();
				}

				var assemblyCatIds = estAssemblyCats.Select(e => e.Id).Distinct().ToList();

				return new EstimateParameterAssemblyCatLogic().GetCoresByFilter(e => e.EstHeaderFk == headerId && assemblyCatIds.Contains(e.EstAssemblyCatFk));
			}
		}

		private IEnumerable<EstLineItemParamEntity> CopyParamFromMaster(IEnumerable<EstLineItemParamEntity> sourceParams, EstLineItemEntity prjAssembly)
		{
			var retVal = new List<EstLineItemParamEntity>();

			foreach (var sourceParam in sourceParams)
			{
				var prjAssemblyParam = sourceParam.Clone() as EstLineItemParamEntity;
				prjAssemblyParam.Id = 0;
				prjAssemblyParam.UpdatedAt = null;
				prjAssemblyParam.UpdatedBy = null;
				prjAssemblyParam.Version = 0;

				prjAssemblyParam.EstHeaderFk = prjAssembly.EstHeaderFk;
				prjAssemblyParam.EstLineItemFk = prjAssembly.Id;

				retVal.Add(prjAssemblyParam);
			}

			return retVal;
		}

		private IEnumerable<EstAssemblyParamEntity> CopyParamFromMaster(int estHeaderId, IEnumerable<EstAssemblyParamEntity> sourceParams, EstAssemblyCatEntity prjAssemblyCat)
		{
			var retVal = new List<EstAssemblyParamEntity>();

			foreach (var sourceParam in sourceParams)
			{
				var prjAssemblyParam = sourceParam.Clone() as EstAssemblyParamEntity;
				prjAssemblyParam.Id = 0;
				prjAssemblyParam.UpdatedAt = null;
				prjAssemblyParam.UpdatedBy = null;
				prjAssemblyParam.Version = 0;

				prjAssemblyParam.EstAssemblyCatFk = prjAssemblyCat.Id;
				prjAssemblyParam.EstHeaderFk = estHeaderId;

				retVal.Add(prjAssemblyParam);
			}

			return retVal;
		}

		private AssemblyParamUpdateItem UpdateAssemblyParam(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<EstLineItemEntity> assembliesToUpdate)
		{
			if (filterInfo.IsPrjAssembly)
			{
				return UpdatePrjAssemblyParamFromMaster(headerId, filterInfo, assembliesToUpdate);
			}
			else
			{
				return UpdateAssemblyParamFromCustimazing(headerId, filterInfo, assembliesToUpdate);
			}
		}

		private AssemblyParamUpdateItem UpdatePrjAssemblyParamFromMaster(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<EstLineItemEntity> assembliesToUpdate)
		{
			var retVal = new AssemblyParamUpdateItem();

			var assemblyParamsToUpdate = new List<EstLineItemParamEntity>();

			var assemblyParamsToDelete = new List<EstLineItemParamEntity>();

			var assemblyCatParamsToUpdate = new List<EstAssemblyParamEntity>();

			var assemblyCatParamsToDelete = new List<EstAssemblyParamEntity>();

			var assemblyCats = GetEstAssemblyCats(headerId, filterInfo, assembliesToUpdate);

			var assemblyCatParams = GetAssemblyCatParams(headerId, filterInfo, assemblyCats).ToList();

			var assemblyParams = GetAssemblyParams(headerId, filterInfo, assembliesToUpdate).ToList();

			if (filterInfo.UpdateParameter)
			{
				var assemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();

				//update project assembly parameter
				var masterAssemblyIds = assembliesToUpdate.Where(e => e.EstAssemblyFk.HasValue).Select(e => e.EstAssemblyFk.Value).Distinct().ToList();

				if (masterAssemblyIds.Any())
				{
					var masterAssemblyId2ParamCodeMap = new EstimateParameterLineItemLogic().GetListByLineItemIds(masterAssemblyIds, assemblyHeaderId).GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.GroupBy(i => i.Code).ToDictionary(i => i.Key, i => i.FirstOrDefault()));

					var prjAssemblyId2ParamCodeMap = assemblyParams.GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.GroupBy(i => i.Code).ToDictionary(i => i.Key, i => i.ToList()));

					foreach (var assembly in assembliesToUpdate)
					{
						//no source master assembly
						if (!assembly.EstAssemblyFk.HasValue || !assembly.EstHeaderAssemblyFk.HasValue)
						{
							continue;
						}

						if (prjAssemblyId2ParamCodeMap.ContainsKey(assembly.Id))
						{
							var prjAssemblyParamCodeMap = prjAssemblyId2ParamCodeMap[assembly.Id];

							if (masterAssemblyId2ParamCodeMap.ContainsKey(assembly.EstAssemblyFk.Value))
							{
								var masterAssemblyParamCodeMap = masterAssemblyId2ParamCodeMap[assembly.EstAssemblyFk.Value];

								foreach (var prjAssemblyParamCode in prjAssemblyParamCodeMap)
								{
									//have master assembly parameter with same code, update it
									if (masterAssemblyParamCodeMap.ContainsKey(prjAssemblyParamCode.Key))
									{
										foreach (var prjAssemblyParamItem in prjAssemblyParamCode.Value)
										{
											if (CompareParam(prjAssemblyParamItem, masterAssemblyParamCodeMap[prjAssemblyParamCode.Key]))
											{
												assemblyParamsToUpdate.Add(prjAssemblyParamItem);
											}

											//remove the master assembly parameter
											masterAssemblyParamCodeMap.Remove(prjAssemblyParamCode.Key);
										}
									}
									//else
									//{
									//	assemblyParamsToDelete.AddRange(prjAssemblyParamCode.Value);

									//	foreach (var item in prjAssemblyParamCode.Value)
									//	{
									//		assemblyParams.Remove(item);
									//	}
									//}
								}

								var masterAssemblyParamsToInster = masterAssemblyParamCodeMap.Select(e => e.Value).ToList();

								if (masterAssemblyParamsToInster.Any())
								{
									var newPrjAssemblyParams = CopyParamFromMaster(masterAssemblyParamsToInster, assembly);

									assemblyParamsToUpdate.AddRange(newPrjAssemblyParams);

									assemblyParams.AddRange(newPrjAssemblyParams);
								}
							}
							//else
							//{
							//	//no master assembly parameters, delete project assembly parameter
							//	var prjAssemblyParamsOfCurrent = prjAssemblyParamCodeMap.SelectMany(e => e.Value).ToList();

							//	assemblyParamsToDelete.AddRange(prjAssemblyParamsOfCurrent);

							//	foreach (var item in prjAssemblyParamsOfCurrent)
							//	{
							//		assemblyParams.Remove(item);
							//	}
							//}
						}
						else
						{
							//no project assembly parameter, copy master assembly parameter to project assembly
							if (masterAssemblyId2ParamCodeMap.ContainsKey(assembly.EstAssemblyFk.Value))
							{
								var newPrjAssemblyParams = CopyParamFromMaster(masterAssemblyId2ParamCodeMap[assembly.EstAssemblyFk.Value].Values, assembly);

								assemblyParamsToUpdate.AddRange(newPrjAssemblyParams);

								assemblyParams.AddRange(newPrjAssemblyParams);
							}
						}
					}
				}

				//update project assemblyCat parameter
				var masterAssemblyCatIds = assemblyCats.Where(e => e.EstAssemblyCatSourceFk.HasValue).Select(e => e.EstAssemblyCatSourceFk.Value).Distinct().ToList();

				if (masterAssemblyCatIds.Any())
				{
					var mdcAssemblyCatId2ParamCodeMap = new EstimateParameterAssemblyCatLogic().GetCoresByFilter(e => e.EstHeaderFk == assemblyHeaderId && masterAssemblyCatIds.Contains(e.EstAssemblyCatFk)).GroupBy(e => e.EstAssemblyCatFk).ToDictionary(e => e.Key, e => e.GroupBy(i => i.Code).ToDictionary(i => i.Key, i => i.FirstOrDefault()));

					var prjAssemblyCatId2ParamCodeMap = assemblyCatParams.GroupBy(e => e.EstAssemblyCatFk).ToDictionary(e => e.Key, e => e.GroupBy(i => i.Code).ToDictionary(i => i.Key, i => i.ToList()));

					foreach (var prjAssemblyCatEntity in assemblyCats)
					{
						if (!prjAssemblyCatEntity.EstAssemblyCatSourceFk.HasValue)
						{
							continue;
						}

						if (prjAssemblyCatId2ParamCodeMap.ContainsKey(prjAssemblyCatEntity.Id))
						{
							var prjAssemblyCatParamCodeMap = prjAssemblyCatId2ParamCodeMap[prjAssemblyCatEntity.Id];

							if (mdcAssemblyCatId2ParamCodeMap.ContainsKey(prjAssemblyCatEntity.EstAssemblyCatSourceFk.Value))
							{
								var mdcAssemblyCatParamCodeMap = mdcAssemblyCatId2ParamCodeMap[prjAssemblyCatEntity.EstAssemblyCatSourceFk.Value];

								foreach (var prjAssemblyCatParamCode in prjAssemblyCatParamCodeMap)
								{
									if (mdcAssemblyCatParamCodeMap.ContainsKey(prjAssemblyCatParamCode.Key))
									{
										foreach (var prjAssemblyCatParamItem in prjAssemblyCatParamCode.Value)
										{
											if (CompareParam(prjAssemblyCatParamItem, mdcAssemblyCatParamCodeMap[prjAssemblyCatParamCode.Key]))
											{
												assemblyCatParamsToUpdate.Add(prjAssemblyCatParamItem);
											}

											mdcAssemblyCatParamCodeMap.Remove(prjAssemblyCatParamCode.Key);
										}
									}
									//else
									//{
									//	//delete
									//	assemblyCatParamsToDelete.AddRange(prjAssemblyCatParamCode.Value);

									//	foreach (var prjAssemblyCatParamItem in prjAssemblyCatParamCode.Value)
									//	{
									//		assemblyCatParams.Remove(prjAssemblyCatParamItem);
									//	}
									//}
								}

								var masterAssemblyCatParamsToInster = mdcAssemblyCatParamCodeMap.Select(e => e.Value).ToList();

								if (masterAssemblyCatParamsToInster.Any())
								{
									var newPrjAssemblyCatParams = CopyParamFromMaster(headerId, masterAssemblyCatParamsToInster, prjAssemblyCatEntity);

									assemblyCatParamsToUpdate.AddRange(newPrjAssemblyCatParams);

									assemblyCatParams.AddRange(newPrjAssemblyCatParams);
								}
							}
							//else
							//{
							//	//no master assemblyCat parameters, delete project assemblyCat parameter
							//	var prjAssemblyParamsOfCurrent = prjAssemblyCatParamCodeMap.SelectMany(e => e.Value).ToList();

							//	assemblyCatParamsToDelete.AddRange(prjAssemblyParamsOfCurrent);

							//	foreach (var item in prjAssemblyParamsOfCurrent)
							//	{
							//		assemblyCatParams.Remove(item);
							//	}
							//}
						}
						else
						{
							if (mdcAssemblyCatId2ParamCodeMap.ContainsKey(prjAssemblyCatEntity.EstAssemblyCatSourceFk.Value))
							{
								var newPrjAssemblyCatParams = CopyParamFromMaster(headerId, mdcAssemblyCatId2ParamCodeMap[prjAssemblyCatEntity.EstAssemblyCatSourceFk.Value].Values, prjAssemblyCatEntity);

								assemblyCatParamsToUpdate.AddRange(newPrjAssemblyCatParams);

								assemblyCatParams.AddRange(newPrjAssemblyCatParams);
							}
						}
					}
				}
			}

			retVal.AssemblyParamsToUpdate = assemblyParamsToUpdate;

			retVal.AssemblyParamsToDelete = assemblyParamsToDelete;

			retVal.AssemblyCatParamsToUpdate = assemblyCatParamsToUpdate;

			retVal.AssemblyCatParamsToDelete = assemblyCatParamsToDelete;

			retVal.AssemblyId2ParamsMap = CreateAssemblyId2ParamsMap(assembliesToUpdate, assemblyParams, assemblyCatParams, assemblyCats);

			return retVal;
		}

		private AssemblyParamUpdateItem UpdateAssemblyParamFromCustimazing(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<EstLineItemEntity> assembliesToUpdate)
		{
			var retVal = new AssemblyParamUpdateItem();

			var assemblyParamsToUpdate = new List<EstLineItemParamEntity>();

			var assemblyCatParamsToUpdate = new List<EstAssemblyParamEntity>();

			var assemblyParamValueToUpdate = new List<EstRuleParamValueEntity>();

			var assemblyCats = GetEstAssemblyCats(headerId, filterInfo, assembliesToUpdate);

			var assemblyCatParams = GetAssemblyCatParams(headerId, filterInfo, assemblyCats);

			var assemblyParams = GetAssemblyParams(headerId, filterInfo, assembliesToUpdate);

			if (filterInfo.UpdateParameter)
			{
				//update parameter
				var defaultEstParameters = new BasicsCustomizeEstParameterLogic().GetCoresByFilter(e => e.IsLive);

				if (defaultEstParameters != null && defaultEstParameters.Any())
				{
					var defaultParamValues = GetCustomizeParamVaules(defaultEstParameters.CollectIds(e => e.Id));
					// update assembly parameter
					if (assemblyParams != null && assemblyParams.Any())
					{
						var paramCode2ParamsList = assemblyParams.GroupBy(e => e.Code).ToDictionary(e => e.Key, e => e.ToList());

						var assParamValues = GetRuleParamVaules(filterInfo.ProjectId, assemblyParams.Select(e => e.Code));

						foreach (var defaultParam in defaultEstParameters)
						{
							if (!paramCode2ParamsList.ContainsKey(defaultParam.Code) || paramCode2ParamsList[defaultParam.Code] == null)
							{
								continue;
							}

							CollectUpdateParamValue(assemblyParamValueToUpdate, assParamValues, defaultParamValues, defaultParam);

							foreach (var paramWithSameCode in paramCode2ParamsList[defaultParam.Code])
							{
								if (CompareParam(paramWithSameCode, defaultParam))
								{
									paramWithSameCode.EstRuleParamValueFk = assemblyParamValueToUpdate.FirstOrDefault(e => e.Code == defaultParam.Code && e.IsDefault)?.Id ?? assParamValues.FirstOrDefault(e => e.Code == defaultParam.Code && e.IsDefault)?.Id;
									assemblyParamsToUpdate.Add(paramWithSameCode);
								}
							}
						}
					}

					// update assembly category parameter
					if (assemblyCatParams != null && assemblyCatParams.Any())
					{
						var paramCode2ParamsList = assemblyCatParams.GroupBy(e => e.Code).ToDictionary(e => e.Key, e => e.ToList());

						var assCatParamValues = GetRuleParamVaules(filterInfo.ProjectId, assemblyCatParams.Select(e => e.Code));

						foreach (var defaultParam in defaultEstParameters)
						{
							if (!paramCode2ParamsList.ContainsKey(defaultParam.Code) || paramCode2ParamsList[defaultParam.Code] == null)
							{
								continue;
							}

							CollectUpdateParamValue(assemblyParamValueToUpdate, assCatParamValues, defaultParamValues, defaultParam);

							foreach (var paramWithSameCode in paramCode2ParamsList[defaultParam.Code])
							{
								if (CompareParam(paramWithSameCode, defaultParam))
								{
									paramWithSameCode.EstRuleParamValueFk = assemblyParamValueToUpdate.FirstOrDefault(e => e.Code == defaultParam.Code && e.IsDefault)?.Id ?? assCatParamValues.FirstOrDefault(e => e.Code == defaultParam.Code && e.IsDefault)?.Id;

									assemblyCatParamsToUpdate.Add(paramWithSameCode);
								}
							}
						}
					}
				}
			}

			retVal.AssemblyParamsToUpdate = assemblyParamsToUpdate;

			retVal.AssemblyParamsValueToUpdate = assemblyParamValueToUpdate;

			retVal.AssemblyCatParamsToUpdate = assemblyCatParamsToUpdate;

			retVal.AssemblyId2ParamsMap = CreateAssemblyId2ParamsMap(assembliesToUpdate, assemblyParams, assemblyCatParams, assemblyCats);

			return retVal;
		}

		private bool CompareParamValue(EstRuleParamValueEntity source, BasicsCustomizeEstimationParameterValueEntity target)
		{
			bool result = false;
			if (source.Value != target.Value)
			{
				source.Value = target.Value;
				result = true;
			}
			if (source.ValueDetail != target.ValueDetail)
			{
				source.ValueDetail = target.ValueDetail;
				result = true;
			}
			if (source.ValueText != target.ValueText)
			{
				source.ValueText = target.ValueText;
				result = true;
			}
			if (source.IsDefault != target.IsDefault)
			{
				source.IsDefault = target.IsDefault;
				result = true;
			}
			return result;
		}

		private void CollectUpdateParamValue(List<EstRuleParamValueEntity> assemblyParamValueToUpdate, IEnumerable<EstRuleParamValueEntity> estparamValues, IEnumerable<BasicsCustomizeEstimationParameterValueEntity> defaultParamValues, BasicsCustomizeEstParameterEntity defaultParam)
		{
			if (defaultParam.Islookup)
			{
				// update param value
				foreach (var dpv in defaultParamValues.Where(e => e.ParameterFk == defaultParam.Id))
				{
					var pv = estparamValues.FirstOrDefault(e => e.Code == defaultParam.Code && e.DescriptionInfo.Translated == dpv.DescriptionInfo.Translated);
					if (pv != null)
					{
						if (assemblyParamValueToUpdate.FirstOrDefault(e => e.Id == pv.Id) == null && CompareParamValue(pv, dpv))
						{
							assemblyParamValueToUpdate.Add(pv);
						}
					}
					else
					{
						if (assemblyParamValueToUpdate.FirstOrDefault(e => e.DescriptionInfo.Translated == dpv.DescriptionInfo.Translated && e.Code == defaultParam.Code && e.Version == 0) == null)
						{
							var newParamValue = new EstRuleParamValueEntity()
							{
								DescriptionInfo = (DescriptionTranslateType)dpv.DescriptionInfo.Clone(),
								Id = 0,
								MdcLineitemcontextFk = EstimateContext.LineItemContextId,
								Code = defaultParam.Code,
								ValueDetail = dpv.ValueDetail,
								Value = dpv.Value,
								IsDefault = dpv.IsDefault,
								Version = 0,
								ValueText = dpv.ValueText,
								ValueType = dpv.ValueType
							};
							assemblyParamValueToUpdate.Add(newParamValue);
						}
					}
				}

				var newItems = assemblyParamValueToUpdate.Where(e => e.Id == 0).ToList();

				if (newItems.Any())
				{
					var newIds = new Queue<int>(new EstimateRulePrjEstRuleParamValueLogic().GetNextIds(newItems.Count()));

					foreach (var item in newItems)
					{
						item.Id = newIds.Dequeue();
					}
				}
			}
		}

		private Dictionary<int, IEnumerable<IEstimateRuleCommonParamEntity>> CreateAssemblyId2ParamsMap(IEnumerable<EstLineItemEntity> assembliesToUpdate, IEnumerable<EstLineItemParamEntity> assemblyParams, IEnumerable<EstAssemblyParamEntity> assemblyCatParams, IEnumerable<EstAssemblyCatEntity> assemblyCats)
		{
			var assemblyId2ParamsMap = new Dictionary<int, IEnumerable<IEstimateRuleCommonParamEntity>>();

			var assemblyId2AssemblyParamMap = assemblyParams.GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.ToList());

			var assemblyCatId2Entity = assemblyCats.GroupBy(e => e.Id).ToDictionary(e => e.Key, e => e.FirstOrDefault());

			var assemblyCatId2ParamsMap = assemblyCatParams.GroupBy(e => e.EstAssemblyCatFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var assembly in assembliesToUpdate)
			{
				if (assemblyId2ParamsMap.ContainsKey(assembly.Id))
				{
					continue;
				}

				var paramsOfCurrentAssembly = new List<IEstimateRuleCommonParamEntity>();

				//add assembly parameter
				if (assemblyId2AssemblyParamMap.ContainsKey(assembly.Id))
				{
					paramsOfCurrentAssembly.AddRange(assemblyId2AssemblyParamMap[assembly.Id]);
				}

				//add assemblyCat and its parent parameter
				if (assembly.EstAssemblyCatFk.HasValue && assemblyCatId2Entity.ContainsKey(assembly.EstAssemblyCatFk.Value))
				{
					if (assemblyCatId2ParamsMap.ContainsKey(assembly.EstAssemblyCatFk.Value))
					{
						paramsOfCurrentAssembly.AddRange(assemblyCatId2ParamsMap[assembly.EstAssemblyCatFk.Value]);
					}

					var assemblyCatParent = assemblyCatId2Entity[assembly.EstAssemblyCatFk.Value].AssemblyCatParent;

					while (assemblyCatParent != null)
					{
						if (assemblyCatId2ParamsMap.ContainsKey(assemblyCatParent.Id))
						{
							paramsOfCurrentAssembly.AddRange(assemblyCatId2ParamsMap[assemblyCatParent.Id]);
						}

						assemblyCatParent = assemblyCatParent.AssemblyCatParent;
					}
				}

				assemblyId2ParamsMap.Add(assembly.Id, DistinctParams(paramsOfCurrentAssembly));
			}

			return assemblyId2ParamsMap;
		}

		private List<IEstimateRuleCommonParamEntity> DistinctParams(List<IEstimateRuleCommonParamEntity> parameters)
		{
			var retVal = new List<IEstimateRuleCommonParamEntity>();

			var codeHash = new HashSet<string>();

			foreach (var param in parameters)
			{
				if (codeHash.Contains(param.Code))
				{
					continue;
				}

				retVal.Add(param);
			}

			return retVal;
		}

		private IEnumerable<EstAssemblyCatEntity> GetEstAssemblyCats(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<EstLineItemEntity> lineItemsToUpdate)
		{
			if (filterInfo.SelectUpdateScope == 1)
			{
				var rootAssemblyCats = new EstimateAssembliesStructureLogic().GetAssemblyCatHierchical(0, 999, new EstAssemblyStructureSearchData() { IsPrjAssembly = filterInfo.IsPrjAssembly, ProjectId = filterInfo.ProjectId });

				return rootAssemblyCats.Flatten(e => e.AssemblyCatChildren).Distinct().ToList();
			}
			else
			{
				var estAssemblyCatIds = lineItemsToUpdate.Where(e => e.EstAssemblyCatFk.HasValue).Select(e => e.EstAssemblyCatFk.Value).Distinct().ToList();

				return new EstimateAssembliesStructureLogic().GetAssemblyCatsWithParent(estAssemblyCatIds, true);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="mdcContextId"></param>
		/// <param name="resources"></param>
		/// <param name="updateCostTypes"></param>
		/// <param name="isPrjAssembly"></param>
		/// <param name="projectId"></param>
		public void UpdateMaterialTypeResources(int mdcContextId, IEnumerable<EstResourceEntity> resources, bool updateCostTypes, bool isPrjAssembly = false, int projectId = -1)
		{
			if (resources == null || !resources.Any())
			{
				return;
			}

			var materialResources = resources.Where(r => r.EstResourceTypeFk == (int)EstResourceType.Material).ToList();

			var materialList = new MaterialLogic().GetMaterialListLookupVByContext(mdcContextId);

			_metrials ??= materialList.ToList();

			var materialCostCodes = new CostCodeSearchService().GetMaterial2CostCodeMap(materialResources.Where(m => m.MdcMaterialFk.HasValue).Select(m => m.MdcMaterialFk.Value).Distinct());

			var id2MaterialMap = materialList.ToDictionary(e => e.Id, e => e);

			var showLastPrice = new SystemOptionLogic().ShowLastPriceForMaterial();
			var priceList = showLastPrice
				? new MaterialPriceListLogic().GetItemsByMdcMaterialIds(materialResources.Where(x => x.MdcMaterialFk.HasValue).Select(x => x.MdcMaterialFk.Value).ToList())
				: new List<MaterialPriceListEntity>();

			foreach (var resource in materialResources)
			{
				if (!resource.MdcMaterialFk.HasValue)
				{
					continue;
				}

				if (!id2MaterialMap.ContainsKey(resource.MdcMaterialFk.Value) || id2MaterialMap[resource.MdcMaterialFk.Value] == null)
				{
					continue;
				}

				var material = id2MaterialMap[resource.MdcMaterialFk.Value];

				if (material != null)
				{
					resource.Code = material.Code;

					resource.BasUomFk = material.BasUomFk;

					resource.BasCurrencyFk = material.BasCurrencyFk;

					resource.HourFactor = material.FactorHour;

					// co2
					resource.Co2Source = material.Co2Source;
					resource.Co2Project = material.Co2Project;

					var materialPriceListItem = priceList.LastOrDefault(e => e.MaterialFk == resource.MdcMaterialFk);
					var materialCommonPriceEntity = new MaterialCommonPriceEntity
					{
						EstimatePrice = materialPriceListItem != null ? materialPriceListItem.EstimatePrice : material.EstimatePrice,
						PriceUnit = material.PriceUnit,
						FactorPriceUnit = material.FactorPriceUnit
					};

					var resCostUnit = new EstProjectMaterialHelper().GetCostUnitFromMaterial(materialCommonPriceEntity);
					var dayWorkRateUnit = materialPriceListItem != null ? materialPriceListItem.DayworkRate : material.DayworkRate;

					if (material.IsLabour.HasValue && material.IsLabour.Value)
					{
						if (material.IsRate.HasValue && material.IsRate.Value)
						{
							//resource.CostUnit = material.EstimatePrice;
							resource.CostUnit = resCostUnit;

							resource.DayWorkRateUnit = dayWorkRateUnit;
						}

						resource.HoursUnit = _EstResourceUpdateHelper.GetUomConversionFactor(resource.BasUomFk);
					}
					else
					{
						resource.HoursUnit = 0;

						resource.CostUnit = resCostUnit;

						resource.DayWorkRateUnit = dayWorkRateUnit;
					}

					if (updateCostTypes)
					{
						resource.EstCostTypeFk = material.EstCostTypeFk;
					}

					int materialCostCodeId;

					if (materialCostCodes.TryGetValue(material.Id, out materialCostCodeId) && materialCostCodeId > 0)
					{
						resource.MdcCostCodeFk = materialCostCodeId;
					}

					// get project cost code, to set material isbuget and iscost
					if (isPrjAssembly)
					{
						if (_prjCostCodes == null)
						{
							var prjCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();

							_prjCostCodes = prjCostCodeLogic.GetProjectCostCodes(projectId).ToList();
						}

						var prjCostCode = _prjCostCodes.FirstOrDefault(e => e.MdcCostCodeFk == resource.MdcCostCodeFk);
						if (prjCostCode != null)
						{
							resource.IsBudget = prjCostCode.IsBudget;
							resource.IsCost = prjCostCode.IsCost;
						}
					}
				}
			}
		}

		private IEnumerable<UserDefinedcolValEntity> GetUserDefinedcolValsOfProjectCostCodes(int project, IEnumerable<EstResourceEntity> costCodeResources)
		{
			if (costCodeResources == null || !costCodeResources.Any())
			{
				return new List<UserDefinedcolValEntity>();
			}

			try
			{
				var uniquePrjCostCodeIds = costCodeResources.Where(e => e.ProjectCostCodeFk.HasValue).Select(e => e.ProjectCostCodeFk.Value).Distinct().ToList();

				var uniqueMdcCostCodeIds = costCodeResources.Where(e => e.MdcCostCodeFk.HasValue).Select(e => e.MdcCostCodeFk.Value).Distinct().ToList();

				if (uniqueMdcCostCodeIds != null && uniqueMdcCostCodeIds.Any())
				{
					var projectCostCodes = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>().GetProjectCostCodes(project, uniqueMdcCostCodeIds, null);

					if (projectCostCodes != null && projectCostCodes.Any())
					{
						uniquePrjCostCodeIds.AddRange(projectCostCodes.Select(e => e.Id).ToList());
					}
				}

				var prjCostCodeIdLists = uniquePrjCostCodeIds.Distinct().Select((x, i) => new { Index = i, Value = x }).GroupBy(x => x.Index / 100).Select(x => x.Select(v => v.Value).ToList()).ToList();

				IEnumerable<UserDefinedcolValEntity> result = new List<UserDefinedcolValEntity>();

				if (uniquePrjCostCodeIds.Any())
				{
					result = new UserDefinedColumnValueLogic().GetListByKeys((int)userDefinedColumnTableIds.ProjectCostCode, project, uniquePrjCostCodeIds).ToList();
				}

				return result;
			}
			catch (Exception e)
			{
				throw new BusinessLayerException(e.Message, e);
			}
		}

		private IEnumerable<UserDefinedcolValEntity> GetUserDefinedcolValsOfCostCodes(IEnumerable<int> costCodeIds)
		{
			if (costCodeIds == null || !costCodeIds.Any())
			{
				return new List<UserDefinedcolValEntity>();
			}

			try
			{
				var uniqueIds = costCodeIds.Distinct().ToList(); //consider huge amount of the costcode ids

				IEnumerable<UserDefinedcolValEntity> result = new List<UserDefinedcolValEntity>();

				if (uniqueIds.Any())
				{
					result = new UserDefinedColumnValueLogic().GetListByKeys((int)userDefinedColumnTableIds.BasicsCostCode, uniqueIds).ToList();
				}

				return result;
			}
			catch (Exception e)
			{
				throw new BusinessLayerException("GetUserDefinedcolValsOfCostCodes failed..." + e.Message, e);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="mdcContextId"></param>
		/// <param name="resources"></param>
		/// <param name="updateCostTypes"></param>
		public void UpdateCostCodeTypeResources(int mdcContextId, IEnumerable<EstResourceEntity> resources, bool updateCostTypes)
		{
			if (resources == null || !resources.Any())
			{
				return;
			}

			var costCodeResources = resources.Where(r => r.EstResourceTypeFk == (int)EstResourceType.CostCode).ToList();

			var costCodeIds = costCodeResources.Where(e => e.MdcCostCodeFk.HasValue).Select(e => e.MdcCostCodeFk.Value).Distinct().ToList();

			var userDefinedColValsOfCostCode = GetUserDefinedcolValsOfCostCodes(costCodeIds);

			var costCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<ICostCodesInfoProviderLogic>();

			if (_masterCostCodes == null)
			{
				_masterCostCodes = costCodeLogic.GetCostCodesByMdcContext(mdcContextId).ToList();
			}

			var id2CostCodeMap = _masterCostCodes.ToDictionary(e => e.Id, e => e);

			foreach (var resource in costCodeResources)
			{
				if (!resource.MdcCostCodeFk.HasValue)
				{
					continue;
				}

				if (!id2CostCodeMap.ContainsKey(resource.MdcCostCodeFk.Value) || id2CostCodeMap[resource.MdcCostCodeFk.Value] == null)
				{
					continue;
				}

				_EstResourceUpdateHelper.UpdatePropertiesFromMasterCostCode(resource, id2CostCodeMap[resource.MdcCostCodeFk.Value], true, updateCostTypes);

				//Update UserDefinedColVal
				if (!id2CostCodeMap[resource.MdcCostCodeFk.Value].IsRate) { continue; }

				var userDefinedColValOfCostCode = userDefinedColValsOfCostCode.FirstOrDefault(e => e.Pk1 == resource.MdcCostCodeFk.Value);

				if (userDefinedColValOfCostCode == null) { continue; }

				UpdateUserDefinedColValOfResource(resource, userDefinedColValOfCostCode);
			}
		}

		private void UpdateUserDefinedColValOfResource(EstResourceEntity resource, UserDefinedcolValEntity userDefinedcolValEntity)
		{
			if (resource == null || userDefinedcolValEntity == null) { return; }

			resource.UserDefinedcolValEntity = resource.UserDefinedcolValEntity ?? EstUDPUtilities.CreateUDPEntityFromResource(resource);

			if (!userDefinedcolValEntity.IsColValEqual(resource.UserDefinedcolValEntity))
			{
				this.GetResourceUDPModification().AddEntityToSave(resource.UserDefinedcolValEntity as UserDefinedcolValEntity);
			}

			userDefinedcolValEntity.CopyColValTo(resource.UserDefinedcolValEntity);
		}

		/// <summary>
		///
		/// </summary>
		public void UpdateCostCodeTypeResourcesFromPrjCostCode(int projectId, IEnumerable<EstResourceEntity> resources, Dictionary<int, int?> resId2JobId, bool updateCostTypes)
		{
			if (resources == null || !resources.Any())
			{
				return;
			}

			if (!_defaultJobId.HasValue)
			{
				_defaultJobId = EstJobHelper.GetProjectJobId(projectId);
			}

			List<int?> jobIds = new List<int?>() { _defaultJobId };

			var costCodeResources = resources.Where(r => r.EstResourceTypeFk == (int)EstResourceType.CostCode).ToList();
			foreach (var resource in costCodeResources)
			{
				if (resId2JobId.ContainsKey(resource.Id))
				{
					jobIds.Add(resId2JobId[resource.Id]);
				}
			}

			var userDefinedColValsOfCostCode = GetUserDefinedcolValsOfProjectCostCodes(projectId, costCodeResources);

			var prjCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();
			_prjCostCodes = prjCostCodeLogic.GetProjectCostCodes(projectId).ToList();
			var code2PrjCostCode = _prjCostCodes.GroupBy(e => e.Code).ToDictionary(e => e.Key, e => e.FirstOrDefault());

			var prjCostCodesJobRateLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesJobRateLogic>();
			var prjCostCodeRates = prjCostCodesJobRateLogic.GetItemsByProjectId(projectId).Where(e => jobIds.Distinct().Contains(e.LgmJobFk));

			foreach (var resource in costCodeResources)
			{
 			    if (resource == null || resource.Code == null)
				{
						continue;

				}
				var prjCostCode = code2PrjCostCode.ContainsKey(resource.Code) ? code2PrjCostCode[resource.Code] : null;

				if (prjCostCode == null)
				{
					continue;
				}

				var resJobId = resId2JobId.ContainsKey(resource.Id) ? resId2JobId[resource.Id] : _defaultJobId;
				var prjCostCodeRate = prjCostCodeRates.FirstOrDefault(e => e.ProjectCostCodeFk == prjCostCode.Id && e.LgmJobFk == resJobId);

				_EstResourceUpdateHelper.UpdatePropertiesFromProjectCostCode(resource, prjCostCode, true, updateCostTypes);
				if (prjCostCodeRate != null)
				{
					_EstResourceUpdateHelper.UpdatePropertiesFromJobRate(resource, prjCostCode, prjCostCodeRate, true);
				}
				else
				{
					if (prjCostCode.IsRate)
					{
						resource.CostUnit = prjCostCode.Rate;
						resource.DayWorkRateUnit = prjCostCode.DayWorkRate;
					}

					resource.BasCurrencyFk = prjCostCode.CurrencyFk;
					resource.QuantityFactorCc = prjCostCode.RealFactorQuantity;
					resource.CostFactorCc = prjCostCode.RealFactorCosts;
					resource.HourFactor = prjCostCode.FactorHour;
					resource.IsCost = prjCostCode.IsCost;
					resource.IsBudget = prjCostCode.IsBudget;
				}

				if (prjCostCode != null)
				{
					//Update UserDefinedColVal
					if (!prjCostCode.IsRate) { continue; }

					var userDefinedColValOfCostCode = userDefinedColValsOfCostCode.FirstOrDefault(e => e.Pk2 == prjCostCode.Id);

					if (userDefinedColValOfCostCode == null) { continue; }

					UpdateUserDefinedColValOfResource(resource, userDefinedColValOfCostCode);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		public List<int> UpdateMaterialTypeResourcesFromPrjCostCode(int projectId, IEnumerable<EstResourceEntity> resources, Dictionary<int, int?> resId2JobId, bool updateCostTypes)
		{
			List<int> noMapPrjMaterialResIds = new List<int>();

			if (resources == null || !resources.Any())
			{
				return noMapPrjMaterialResIds;
			}

			if (!_defaultJobId.HasValue)
			{
				_defaultJobId = EstJobHelper.GetProjectJobId(projectId);
			}

			List<int?> jobIds = new List<int?>() { _defaultJobId };

			var materialResources = resources.Where(r => r.EstResourceTypeFk == (int)EstResourceType.Material).ToList();
			var materialIds = materialResources.Where(e => e.MdcMaterialFk.HasValue).Select(i => i.MdcMaterialFk.Value).Distinct().ToArray();
			foreach (var resource in materialResources)
			{
				if (resId2JobId.ContainsKey(resource.Id))
				{
					jobIds.Add(resId2JobId[resource.Id]);
				}
			}

			var prjMaterialLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectMaterialLogic>();
			var projectMaterials = prjMaterialLogic.GetProjectMaterials(projectId).Where(e => jobIds.Distinct().Contains(e.LgmJobFk) && materialIds.Contains(e.MdcMaterialFk));

			foreach (var resource in materialResources)
			{
				var resJobId = resId2JobId.ContainsKey(resource.Id) ? resId2JobId[resource.Id] : _defaultJobId;
				var prjMaterial = projectMaterials.FirstOrDefault(e => e.MdcMaterialFk == resource.MdcMaterialFk && e.LgmJobFk == resJobId);

				if (prjMaterial == null)
				{
					noMapPrjMaterialResIds.Add(resource.Id);
					continue;
				}

				if (_prjCostCodes == null)
				{
					var prjCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();
					_prjCostCodes = prjCostCodeLogic.GetProjectCostCodes(projectId).ToList();
				}

				var prjCostCode = _prjCostCodes.FirstOrDefault(e => e.MdcCostCodeFk == resource.MdcCostCodeFk);
				if (prjCostCode != null)
				{
					prjMaterial.IsLabour = prjCostCode.IsLabour;
					prjMaterial.IsRate = prjCostCode.IsRate;
					prjMaterial.RealFactorQuantity = prjCostCode.RealFactorQuantity;
					prjMaterial.RealFactorCosts = prjCostCode.RealFactorCosts;
					prjMaterial.IsBudget = prjCostCode.IsBudget;
					prjMaterial.IsCost = prjCostCode.IsCost;
				}

				_EstResourceUpdateHelper.UpdatePropertiesFromProjectMaterial(resource, prjMaterial, true, updateCostTypes);
			}

			return noMapPrjMaterialResIds;
		}

		/// <summary>
		///
		/// </summary>
		public Dictionary<int, int?> GetResourceJobWithAssembly(IEnumerable<EstResourceEntity> resources, Dictionary<int, EstLineItemEntity> assembly2Dictionary)
		{
			Dictionary<int, int?> resId2JobId = new Dictionary<int, int?>();
			if (resources == null || !resources.Any())
			{
				return resId2JobId;
			}

			foreach (var resource in resources)
			{
				var lineItem = assembly2Dictionary.ContainsKey(resource.EstLineItemFk) ? assembly2Dictionary[resource.EstLineItemFk] : null;
				if (lineItem != null && lineItem.LgmJobFk.HasValue && !resId2JobId.ContainsKey(resource.Id))
				{
					resId2JobId.Add(resource.Id, lineItem.LgmJobFk);
				}
			}

			return resId2JobId;
		}

		/// <summary>
		/// Set ResourceType, AssemblyType and materialfk and costcodefk
		/// </summary>
		private void SetResourceTypeAndAssemblyType(int? mdcMaterialFk, EstResourceEntity newResourceCopy, EstAssemblyTypeEntity assemblyType)
		{
			var mdcMaterialEntity = this.GetMdcCommoditySearchVEntity(mdcMaterialFk.Value);

			var isLabor = false;

			if (mdcMaterialEntity != null && mdcMaterialEntity.MdcCostCodeFk.HasValue)
			{
				if (mdcMaterialEntity.IsLabour.HasValue && mdcMaterialEntity.IsLabour.Value)
				{
					isLabor = mdcMaterialEntity.IsLabour.Value;
				}
			}

			if (isLabor)
			{
				newResourceCopy.EstResourceTypeFk = (int)EstResourceType.Assembly;

				newResourceCopy.EstAssemblyTypeFk = assemblyType.Id;

				newResourceCopy.MdcMaterialFk = mdcMaterialFk;

				newResourceCopy.MdcCostCodeFk = mdcMaterialEntity.MdcCostCodeFk;
			}
			else
			{
				newResourceCopy.EstResourceTypeFk = (int)EstResourceType.Assembly;

				newResourceCopy.EstAssemblyTypeFk = null;

				newResourceCopy.MdcMaterialFk = null;

				newResourceCopy.MdcCostCodeFk = null;
			}
		}

		/// <summary>
		/// Update Assembly Type
		/// </summary>
		private void UpdateAssemblyType(EstAssemblyTypeEntity assemblyType, EstResourceEntity newResourceCopy, EstLineItemEntity assembly)
		{
			if (newResourceCopy == null || assembly == null)
			{
				return;
			}

			if (!assemblyType.EstAssemblyTypeLogicFk.HasValue)
			{
				newResourceCopy.EstResourceTypeFk = (int)EstResourceType.Assembly;
				newResourceCopy.EstAssemblyTypeFk = null;
				newResourceCopy.MdcMaterialFk = null;
				newResourceCopy.MdcCostCodeFk = null;

				return;
			}

			switch (assemblyType.EstAssemblyTypeLogicFk)
			{
				case (int)EstAssemblyLogicType.CrewAssembly:
				case (int)EstAssemblyLogicType.CrewAssemblyUpdated:
					{
						if (assembly.MdcCostCodeFk.HasValue)
						{
							newResourceCopy.EstResourceTypeFk = (int)EstResourceType.Assembly;
							newResourceCopy.EstAssemblyTypeFk = assemblyType.Id;
							newResourceCopy.MdcCostCodeFk = assembly.MdcCostCodeFk;
						}
						else if (assembly.MdcMaterialFk.HasValue)
						{
							SetResourceTypeAndAssemblyType(assembly.MdcMaterialFk, newResourceCopy, assemblyType);
						}
						else
						{
							newResourceCopy.EstResourceTypeFk = (int)EstResourceType.Assembly;
							newResourceCopy.EstAssemblyTypeFk = null;
							newResourceCopy.MdcMaterialFk = null;
							newResourceCopy.MdcCostCodeFk = null;
						}
						break;
					}

				case (int)EstAssemblyLogicType.MaterialAssembly:
				case (int)EstAssemblyLogicType.MaterialAssemblyUpdated:
					{
						if (assembly.MdcMaterialFk.HasValue)
						{
							newResourceCopy.EstResourceTypeFk = (int)EstResourceType.Assembly;
							newResourceCopy.EstAssemblyTypeFk = assemblyType.Id;
							newResourceCopy.MdcMaterialFk = assembly.MdcMaterialFk;
							newResourceCopy.MdcCostCodeFk = assembly.MdcCostCodeFk;

							if (_metrials == null)
							{
								_metrials = new MaterialLogic().GetMaterialListLookupVByContext(_mdcContextId).ToList();
							}

							var material = _metrials.FirstOrDefault(e => e.Id == assembly.MdcMaterialFk);
							if (material != null)
							{
								newResourceCopy.MdcCostCodeFk = material.MdcCostCodeFk;
							}
						}
						else
						{
							newResourceCopy.EstResourceTypeFk = (int)EstResourceType.Assembly;
							newResourceCopy.EstAssemblyTypeFk = null;
							newResourceCopy.MdcMaterialFk = null;
							newResourceCopy.MdcCostCodeFk = null;
						}
						break;
					}

				case (int)EstAssemblyLogicType.ProtectedAssembly:
					{
						newResourceCopy.EstResourceTypeFk = (int)EstResourceType.Assembly;
						newResourceCopy.EstAssemblyTypeFk = assemblyType.Id;
					}
					break;
				default:
					{
						newResourceCopy.EstResourceTypeFk = (int)EstResourceType.Assembly;
						newResourceCopy.EstAssemblyTypeFk = null;
						newResourceCopy.MdcMaterialFk = null;
						newResourceCopy.MdcCostCodeFk = null;
						break;
					}
			}
		}

	}

	/// <summary>
	///
	/// </summary>
	public class AssemblyParamUpdateItem
	{
		/// <summary>
		///
		/// </summary>
		public IEnumerable<EstLineItemParamEntity> AssemblyParamsToUpdate { get; set; }

		/// <summary>
		///
		/// </summary>

		public IEnumerable<EstLineItemParamEntity> AssemblyParamsToDelete { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<EstAssemblyParamEntity> AssemblyCatParamsToUpdate { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<EstAssemblyParamEntity> AssemblyCatParamsToDelete { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<EstRuleParamValueEntity> AssemblyParamsValueToUpdate { get; set; }

		/// <summary>
		///
		/// </summary>
		public Dictionary<int, IEnumerable<IEstimateRuleCommonParamEntity>> AssemblyId2ParamsMap { get; set; }
	}
}
