
import { tile, app, cnt, btn, commonLocators, sidebar } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _projectPage, _bidPage, _saleContractPage, _procurementPage, _wipPage, _estimatePage, _boqPage, _mainView, _modalView, _salesPage, _billPage, _package, _wicpage, _procurementConfig, _rfqPage, _validate, _controllingUnit, _materialPage, _commonAPI, _procurementContractPage } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import common from "mocha/lib/interfaces/common";

const PROJECT_NO = _common.generateRandomString(5)
const PROJECT_DESC = _common.generateRandomString(5)
const CU_DESC = _common.generateRandomString(5)
const PES_DESCRIPTION = _common.generateRandomString(5)

let PROJECTS_PARAMETERS: DataCells
let MODAL_PROJECTS
let CONTAINER_COLUMNS_HEADERS
let CONTAINERS_PERFORMANCE_ENTRY_SHEET

let CONTAINER_COLUMNS_CONTROLLING_UNIT
let CONTAINERS_CONTROLLING_UNIT
let CONTROLLING_UNIT_PARAMETERS: DataCells

let CONTAINER_COLUMNS_MATERIAL_CATALOG_FILTER
let CONTAINER_COLUMNS_MATERIAL_RECORD
let CONTAINERS_PES_ITEMS
let CONTAINER_COLUMNS_PES_ITEMS
let ITEM_PARAMETER
let CONTAINERS_PRICE_CONDITION
let CONTAINER_COLUMNS_PRICE_CONDITION


describe("PCM- 4.297 | Price condition container in pes module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

    before(function () {
        cy.fixture("pcm/pes-4.297-price-condition-container-in-pes-module.json").then((data) => {
            this.data = data;
            MODAL_PROJECTS = this.data.MODAL.PROJECTS
            PROJECTS_PARAMETERS = {
                [commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
                [commonLocators.CommonLabels.NAME]: PROJECT_DESC,
                [commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK[0]
            }
            CONTAINER_COLUMNS_HEADERS = this.data.CONTAINER_COLUMNS.HEADERS
            CONTAINERS_PERFORMANCE_ENTRY_SHEET = this.data.CONTAINERS.PERFORMANCE_ENTRY_SHEET
            CONTAINER_COLUMNS_MATERIAL_RECORD = this.data.CONTAINER_COLUMNS.MATERIAL_RECORD
            CONTAINER_COLUMNS_CONTROLLING_UNIT = this.data.CONTAINER_COLUMNS.CONTROLLING_UNIT
            CONTAINERS_CONTROLLING_UNIT = this.data.CONTAINERS.CONTROLLING_UNIT
            CONTROLLING_UNIT_PARAMETERS = {
                [app.GridCells.DESCRIPTION_INFO]: CU_DESC,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_CONTROLLING_UNIT.QUANTITY,
                [app.GridCells.UOM_FK]: CONTAINERS_CONTROLLING_UNIT.UOM
            }
            CONTAINER_COLUMNS_MATERIAL_CATALOG_FILTER = this.data.CONTAINER_COLUMNS.MATERIAL_CATALOG_FILTER
            CONTAINER_COLUMNS_MATERIAL_RECORD = this.data.CONTAINER_COLUMNS.MATERIAL_RECORD
            CONTAINERS_PES_ITEMS = this.data.CONTAINERS.PES_ITEMS
            CONTAINER_COLUMNS_PES_ITEMS = this.data.CONTAINER_COLUMNS.ITEMS
            ITEM_PARAMETER = {
                [app.GridCells.MDC_MATERIAL_FK]: CONTAINERS_PES_ITEMS.MATERIAL_NO[0],
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_PES_ITEMS.QUANTITY[0],

            };
            CONTAINERS_PRICE_CONDITION = this.data.CONTAINERS.PRICE_CONDITION
            CONTAINER_COLUMNS_PRICE_CONDITION = this.data.CONTAINER_COLUMNS.PRICE_CONDITION

        })
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear();
        _commonAPI.getAccessToken().then((result) => {
            cy.log(`Token Retrieved: ${result.token}`);
        });

    });

    after(() => {
    cy.LOGOUT();
    });

    it("TC - API: Create project with and controlling unit", function () {
        _commonAPI.createProject()
            .then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
                _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_PARAMETERS)
            });
    })

    it("TC - Search Existing material record andset vlaue to material in items contianer from price lsit price condtion", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.MATERIAL);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.setDefaultView(app.TabBar.RECORDS)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_CATALOG_FILTER, app.FooterTab.MATERIALFILTER)
            _common.setup_gridLayout(cnt.uuid.MATERIAL_CATALOG_FILTER, CONTAINER_COLUMNS_MATERIAL_CATALOG_FILTER)
        })
        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.openTab(app.TabBar.RECORDS).then(() => {
                _common.select_tabFromFooter(cnt.uuid.MATERIAL_RECORDS, app.FooterTab.MATERIAL_RECORDS, 1)
                _common.setup_gridLayout(cnt.uuid.MATERIAL_RECORDS, CONTAINER_COLUMNS_MATERIAL_RECORD)
                _common.set_columnAtTop([CONTAINER_COLUMNS_MATERIAL_RECORD.prcpriceconditionfk], cnt.uuid.MATERIAL_RECORDS)
            });
        })
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, CONTAINERS_PES_ITEMS.MATERIAL_NO[0])
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_RECORDS)
        _common.edit_dropdownCellWithCaret(cnt.uuid.MATERIAL_RECORDS, app.GridCells.PRC_PRICE_CONDITION_FK, commonLocators.CommonKeys.LIST, commonLocators.CommonKeys.SURCHARGE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Verify the vlaue to material in items contianer from price lsit price condtion", function () {
        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 1);
            _common.clear_subContainerFilter(cnt.uuid.MATERIAL_PRICE_CONDITION)
        });
        _common.select_rowHasValue(cnt.uuid.MATERIAL_PRICE_CONDITION, commonLocators.CommonKeys.SURCHARGE)
        _common.assert_cellData_insideActiveRow(cnt.uuid.MATERIAL_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.SURCHARGE)
    })

    it("TC - Create new pes record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PES);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
            _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_HEADERS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_HEADERS.businesspartnerfk, CONTAINER_COLUMNS_HEADERS.clerkprcfk], cnt.uuid.HEADERS)
        });
        _common.maximizeContainer(cnt.uuid.HEADERS)
        _common.clear_subContainerFilter(cnt.uuid.HEADERS);
        _common.create_newRecord(cnt.uuid.HEADERS);
        _common.select_activeRowInContainer(cnt.uuid.HEADERS)
        _common.edit_containerCell(cnt.uuid.HEADERS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, PES_DESCRIPTION)
        _common.edit_dropdownCellWithInput(cnt.uuid.HEADERS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PERFORMANCE_ENTRY_SHEET.BUSINESS_PARTNER)
        _common.edit_dropdownCellWithInput(cnt.uuid.HEADERS, app.GridCells.CLERK_PRC_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PERFORMANCE_ENTRY_SHEET.CLERK)
        _common.edit_dropdownCellWithInput(cnt.uuid.HEADERS, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.HEADERS)
        _common.search_inSubContainer(cnt.uuid.HEADERS, PES_DESCRIPTION)
        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _common.saveCellDataToEnv(cnt.uuid.HEADERS, app.GridCells.CODE, "HEADERS_CODE")
        cy.log(Cypress.env("HEADERS_CODE"))
        _common.select_rowInSubContainer(cnt.uuid.HEADERS)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create new items for pes", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('HEADERS_CODE')).pinnedItem();
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
        });
        _common.select_rowInSubContainer(cnt.uuid.HEADERS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 1);
            _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_PES_ITEMS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_PES_ITEMS.mdcmaterialfk, CONTAINER_COLUMNS_PES_ITEMS.quantity, CONTAINER_COLUMNS_PES_ITEMS.priceextra, CONTAINER_COLUMNS_PES_ITEMS.prcpriceconditionfk, CONTAINER_COLUMNS_PES_ITEMS.price, CONTAINER_COLUMNS_PES_ITEMS.priceextraoc], cnt.uuid.ITEMS)
            _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        });
        _common.maximizeContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _procurementContractPage.enterRecord_toCreateContractItems(cnt.uuid.ITEMS, ITEM_PARAMETER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('HEADERS_CODE')).pinnedItem();
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
            _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        });
        _common.select_rowInSubContainer(cnt.uuid.HEADERS)
    })

    it("TC - Verify material in items contianer from price list price condtion", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('HEADERS_CODE')).pinnedItem();
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
            _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        });
        _common.select_rowInSubContainer(cnt.uuid.HEADERS)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 1);
            _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        });
        _common.select_rowInContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION)
        })
        _common.clear_dataFromDropDown_typeInput_withoutGrid(cnt.uuid.PES_PRICE_CONDITION)
        _common.select_dataFromDropDown_typeInput_withoutGrid(cnt.uuid.PES_PRICE_CONDITION, commonLocators.CommonKeys.LIST_EXACT, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.SURCHARGE)
        _common.select_rowInSubContainer(cnt.uuid.PES_PRICE_CONDITION)
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.COPPER_WEIGHT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.select_rowInSubContainer(cnt.uuid.PES_PRICE_CONDITION)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.COPPER_WEIGHT)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Set new value to type, then assert value and total and total oc", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
            _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        });
        _common.select_rowInSubContainer(cnt.uuid.HEADERS)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS)
            _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        })
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION)
            _common.set_columnAtTop([CONTAINER_COLUMNS_PRICE_CONDITION.conditionTotal, CONTAINER_COLUMNS_PRICE_CONDITION.totaloc, CONTAINER_COLUMNS_PRICE_CONDITION.isactivated], cnt.uuid.PES_PRICE_CONDITION)
            _common.clear_subContainerFilter(cnt.uuid.PES_PRICE_CONDITION)
        })
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS)
            _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        })
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION)
        })
        _common.select_rowInSubContainer(cnt.uuid.PES_PRICE_CONDITION)
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.SURCHARGE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS)
        })
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION)
        })
        _common.select_rowInSubContainer(cnt.uuid.PES_PRICE_CONDITION)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.SURCHARGE)
        cy.wait(1000).then(() => {
        _common.enterRecord_inNewRow(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PES_ITEMS.QUANTITY[1])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        cy.wait(1000)//required wait to save record
        cy.SAVE()
        cy.wait(1000)//required wait for modal to appear
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.CANCEL)
        })
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasValue(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.SURCHARGE)
        _common.select_rowInSubContainer(cnt.uuid.PES_PRICE_CONDITION)
        cy.wait(1000)//required wait to assert record
        _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.PES_PRICE_CONDITION, CONTAINERS_PES_ITEMS.QUANTITY[2], CONTAINERS_PES_ITEMS.PRICE[1], app.GridCells.CONDITION_TOTAL)
        cy.wait(1000)//required wait to assert record
        _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.PES_PRICE_CONDITION, CONTAINERS_PES_ITEMS.QUANTITY[2], CONTAINERS_PES_ITEMS.PRICE[1], app.GridCells.TOTAL_OC)
    })

    it("TC - Set pricecomponent=false or islive=false", function () {
        _common.waitForLoaderToDisappear()
        _common.select_rowInSubContainer(cnt.uuid.PES_PRICE_CONDITION)
        _common.set_cellCheckboxValue(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.IS_ACTIVATED, commonLocators.CommonKeys.UNCHECK)
        cy.wait(1000)//required wait to save record
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS)
        })
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.ITEMS)
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.ITEMS, app.GridCells.PRICE_EXTRA)
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMS, app.GridCells.PRICE_EXTRA, CONTAINERS_PES_ITEMS.PRICE[2])
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMS, app.GridCells.PRICE_EXTRA_OC, CONTAINERS_PES_ITEMS.PRICE[2])
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMS)
    })

    it("TC - Create and delete button are working price condtion", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
            _common.clear_subContainerFilter(cnt.uuid.PES_PRICE_CONDITION)
        })
        cy.wait(1000).then(() => {
        _common.create_newRecord(cnt.uuid.PES_PRICE_CONDITION)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.COPPER_SURCHARGE)
        cy.wait(1000)//required wait to save record
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        })
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS)
        })
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
        })
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.PES_PRICE_CONDITION, commonLocators.CommonKeys.COPPER_SURCHARGE)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
        })
        _common.select_rowHasValue(cnt.uuid.PES_PRICE_CONDITION, commonLocators.CommonKeys.COPPER_SURCHARGE)
        _common.delete_recordFromContainer(cnt.uuid.PES_PRICE_CONDITION)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.YES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to validate record
        _validate.verify_isRecordDeleted(cnt.uuid.PES_PRICE_CONDITION, commonLocators.CommonKeys.COPPER_SURCHARGE)
    })

    it("TC - Verify If is pricecomponent=true or islive=true, this record's total should add to price extra of items", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS)
            _common.waitForLoaderToDisappear()
            _common.search_inSubContainer(cnt.uuid.ITEMS, commonLocators.CommonKeys.SURCHARGE)
        })
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION)
        })
        _common.waitForLoaderToDisappear()
        _common.select_rowInSubContainer(cnt.uuid.PES_PRICE_CONDITION)
        cy.wait(1000).then(() => {
            _common.set_cellCheckboxValue(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.IS_ACTIVATED, commonLocators.CommonKeys.CHECK)
            _common.waitForLoaderToDisappear()
            cy.SAVE()
        })
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2)
            _common.clear_subContainerFilter(cnt.uuid.ITEMS)
            _common.search_inSubContainer(cnt.uuid.ITEMS, commonLocators.CommonKeys.SURCHARGE)
        })
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMS, app.GridCells.PRICE_EXTRA, CONTAINERS_PES_ITEMS.PRICE[3])
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMS, app.GridCells.PRICE_EXTRA_OC, CONTAINERS_PES_ITEMS.PRICE[3])
    })

    it("TC - Verify select price condtion in container,it should auto generate records", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
        })
        _common.select_dataFromDropDown_typeInput_withoutGrid(cnt.uuid.PES_PRICE_CONDITION, commonLocators.CommonKeys.LIST_EXACT, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.COPPER_SURCHARGE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.PES_PRICE_CONDITION, CONTAINERS_PES_ITEMS.CSUR)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Verify the selected price condtion in items container, it should auto generate records to price condtion contianer", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2)
        })
        _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        _common.search_inSubContainer(cnt.uuid.ITEMS, CONTAINERS_PES_ITEMS.MATERIAL_NO[0])
        _common.select_rowInContainer(cnt.uuid.ITEMS)
        _common.edit_dropdownCellWithCaret(cnt.uuid.ITEMS, app.GridCells.PRC_PRICE_CONDITION_FK, commonLocators.CommonKeys.LIST, commonLocators.CommonKeys.COPPER_SURCHARGE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Assert price condtion in items container", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_PRICE_CONDITION)
        _validate.verify_isRecordPresent(cnt.uuid.PES_PRICE_CONDITION, commonLocators.CommonKeys.COPPER_WEIGHT)
    })

    it("TC - Verify if type hasvalue is false then Value is read only and blank", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION)
        })
        _common.clear_subContainerFilter(cnt.uuid.PES_PRICE_CONDITION)
        _common.select_rowInContainer(cnt.uuid.PES_PRICE_CONDITION)
        _validate.verify_isRecordNotEditable(cnt.uuid.PES_PRICE_CONDITION, app.GridCells.VALUE, 3)
    })
});