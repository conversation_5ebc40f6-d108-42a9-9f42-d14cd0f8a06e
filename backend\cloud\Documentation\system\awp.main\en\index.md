%Advanced Work Packaging

# Preliminary Remark

RIB 4.0 currently offers a strong Estimate module and Procurement module. However, the interfaces between the two modules are managed through "Package Item Assignment" container in Estimate module, and "Estimate Line Item" and "Estimate Resources" container in Package module.

Unfortunately, this means that when Estimators or Procurement Officers want to have an overview of the Estimate – Procurement interfaces at a Project level, the User Experience is sub-optimal.

Secondly, though Estimate Line Items can be manually added to existing Packages in Procurement module or Packages can be manually added to Estimate Line Items in Estimate module; the User Journey involves a lot of clicks and does not follow the ease of '*drag-and-drop*' method adapted across other areas of RIB 4.0.

To provide a good usability for our Users, "*Advanced Work Packaging*" module is introduced from 25.2.

> Info: For the purposes of simplicity, we will call this as the "AWP" module in this document.

# Navigation into AWP Module

![](./../../image/tiles/tile-advanced-work-package.png)

Users can log into the AWP Module in the following ways:

## Desktop Page

![Desktop Page](./../../image/awp.main/en/1-desktop-page.png){.lightbox}

## Project Navigator

![Project Navigator](./../../image/awp.main/en/2-project-navigator.png){.lightbox}

## GO TO from Estimate

Line Item container provides a 'GO TO' option to the AWP Module.

> **Hint**: 
>
> Keyboard shortcut ALT+W can be used to open the ‘GO TO’, after which UP or DOWN arrow keys on keyboard can be used to navigate to the necessary option.

![GO TO from Estimate](./../../image/awp.main/en/3-go-to-from-estimate.png){.lightbox}

## GO TO from Procurement

![GO TO from Procurement](./../../image/awp.main/en/4-go-to-from-procurement.png){.lightbox}

# Overview of the AWP Module

| Container Name                | Short Description                                            |
| ----------------------------- | ------------------------------------------------------------ |
| Projects                      | A list of the filtered Projects. Serves as the main entity for the module. |
| Package Structure – Line item | For the selected Estimate, the Line item will be displayed. User can group the structure based on BoQ/WIC or Line item. |
| Package Structure –Resource   | For the selected Line items, the Resources will be displayed. User can filter the result set. |
| Service Packages              | For the selected Project, the Service Packages will be displayed. |
| Material Packages             | For the selected Project, the Material Packages will be displayed. |
| Package Item Assignment       | For selected Line item, Resource or Package item, Item assignment will be displayed. |
| Specification                 | For selected BoQ item, WIC item, or Package BoQ item, Specification will be displayed. |

On opening the AWP Module, Users will see two tabs:

1.  Service Package Allocation: Recommended if Users want to work with Service Packages.

2.  Material Package Allocation: Recommended if Users want to work with Material Packages.

We'll review the details of each container based on these two tabs.

## Containers in Service Package Allocation

In this section, we will describe the Tabs available in Vanilla installation.

![Advanced Work Package Overview](./../../image/awp.main/en/5-advanced-work-package-overview.png){.lightbox}

<a id="project"></a>

### Project

This '*read-only*' container is the main entity for AWP Module, and it allows Users to navigate between different Projects without leaving the module. Users can also navigate to the Project module using the "GO TO" button in the same container or Keyboard Shortcut (ALT+W).

For more details, Project [Module](../../project.main/en/index.html).

<a id="package-structure-–-line-item"></a>

### Package Structure – Line Item

This container allows Users to load the data necessary for Estimate interfaces with Procurement.

![Package Structure Line Item Container](./../../image/awp.main/en/6-package-structure-line-item-container.png){.lightbox}

1.  The "+" icon allows dynamic loading of WIC or BoQ data with Line Items.

2.  The filter for "*All Items*" removes any filter that is active in the said container and displays all records.

3.  "*Item with Assignment"* filters all records in Package Structure Line Item container that are linked to Procurement Package (Service or Material). This filter is recommended when Users want to review the WIC/BoQ Positions and/or Line Items that are already assigned to Procurement Packages.

4.  "*Item without Assignment*" filters all records in Package Structure Line Item container that are not yet Packaged. This filter allows Users to work with unassigned WIC/ BoQ Positions and/or Line Items.

5.  The option to "*Refresh*" allows Users to load the dynamic data like WIC with Line Items or BoQ with Line Items.

6.  Using the "*GO TO Estimate*" button (or Keyboard Shortcut ALT+W), Users can navigate from AWP to Estimate module. If a specific Estimate Line Item is highlighted in this container before using the GO TO, Users will see the same Line Item when they land in Estimate module.

7.  "*Estimate Header*" option on the bottom right of the container allows Users to change the Header that must be used to load the data. Only Estimate Headers with "Is Active" = True will be available in the AWP Module.


| Column Name                       | Description                                                  |
| --------------------------------- | ------------------------------------------------------------ |
| Code                              | Reference No of WIC BoQ or Project BoQ or Code of Line Items. |
| Description                       | Outline Specification of WIC BoQ / Project BoQ or Description of Line Items. |
| Quantity                          | Quantity of BoQ Position, WIC Positions or Line Items.       |
| UoM                               | Unit of Measure of WIC or BoQ Positions, or UoM Item of Line Items. |
| UoM Description                   | Description of Unit of Measure.                              |
| Unit Rate                         | Unit Rate of WIC BoQ or Project BoQ Positions.               |
| Final Price                       | Final Price of WIC BoQ or Project BoQ Positions.             |
| WQ Quantity                       | WQ Quantity Item of Line items.                              |
| Quantity Total                    | Quantity Total of Line items.                                |
| Grand Cost/Unit Item              | Grand Cost/Unit Item of Line Items.                          |
| Grand Total                       | Grand Total of Line Items.                                   |
| Procurement Structure             | Procurement Structure of Line Items.                         |
| Procurement Structure–Description | Description of Procurement Structure.                        |

<a id="package-structure-–-resource"></a>

### Package Structure – Resource

This container displays the Resources that are used in the selections from Package Structure – Line Item container.

![Package Structure – Resource](./../../image/awp.main/en/7-package-structure-resource.png){.lightbox}

1.  "Filter by Cost Code" allows Users to filter the Cost Codes displayed in this container and allows filtering by Major Cost Codes or by Procurement Structures assigned to them.

2.  "Filter by Material" allows Users to filter the Materials displayed in this container and allows filtering by Material Catalog or by Procurement Structures assigned to them.

> **Hints**:
>
> 1. The container loads Resources assigned to BoQ (Root), Division or Position. 
>
> 2.	This works with multi-select, so if 2 BoQs are selected at root level, then this container shows Resources from all Estimate Line Items linked to all Positions of the 2 BoQs.
> 3.	Quantities, Cost Total, Budgets are summarized based on Resource Code (Cost Code/ Material).

| Column Name           | Description                                                  |
| --------------------- | ------------------------------------------------------------ |
| Selected              | Allows Users to select or unselected the Resources for assignments. |
| Short Key             | Resource Short Key (only Cost Code or Materials that can be Packaged are displayed in this container). |
| Code                  | Resource Code                                                |
| Description           | Description of Cost Code or Material.                        |
| Quantity              | Resource Quantity (this value is aggregated based on selection in Package Structure Line Items container). |
| Quantity Total        | Total Quantity (incl. Factors) of Resources (this value is aggregated based on selection in Package Structure Line Items container). |
| Cost/Unit             | Cost/Unit of Resource.                                       |
| UoM                   | Unit of Measure of Resource.                                 |
| UoM Description       | Description of the Unit of Measure.                          |
| Currency              | Currency of Resource.                                        |
| Currency- Description | Description of the Currency.                                 |
| Cost Total            | Cost Total of the Resource (calculated as Quantity Total * Cost/Unit). |
| Budget Total          | Total Budget of the Resource (this value is aggregated based on selection in Package Structure Line Items container). |

### Service Packages

This container lists all Packages whose Procurement Configuration has "Is Service" flag = True.

![Service Packages](./../../image/awp.main/en/8-service-packages.png){.lightbox}

1.  Using the "New Record" option, Users can create new Packages. This triggers the same input dialogue as that from Package module in Procurement. The first Subproject and Package Project BoQ (Header) is automatically created with this.

2.  Using the "New Subrecord" option, Users can create new <font color="red">Package BoQs</font>. This option is activated only when the focus is on a Sub Package.

3.  The option for "Package Creating Config" can be used to configure.

    a. Which Quantities must be taken over to Procurement BoQ. Users can select from WQ, AQ, Total Quantity from Estimate, or from Project BoQ (WQ/AQ).

    b. The flag "Consider BoQ Qty Relation" allows Users to also ensure that the latest Quantities are taken over from the source is taken while assigning BoQ Positions from Project BoQ to Package BoQ in the AWP Module. For example, if the BoQ Quantity Relation in Estimate module = *From Structure*, while assigning the Project BoQ from Package Structure Line Item container to Service Package, the latest quantities from Project BoQ <font color="red">will not be taken</font> into Package BoQ).

    c. Leaving the flag for "Compare Mode" = True allows Users to benefit from 4.0 comparing all positions belonging to a Subitem before creating new records in the target (Package). This option **only** works when a Division (or Subdivision) is dropped into Division (or Subdivision), and when the Root, Division and Position Numbers of Source and Target are same.

4.  "*All Items*" allows Users to remove all filters from the Service Packages container.

5.  Using the "*Item with Assignment*" filter, Users can see all records that are linked with Estimate.

6.  "*Item without Assignment*" filter allows Users to filter the Positions that are created in Procurement but are not linked with Estimate.

7.  'Refresh' fetches the latest values for all records in Service Packages container.

8.  "Refresh Selection" fetches the latest values for the selected entity. This option is enabled only for Subpackages.

| Column Name                       | Description                                                  |
| --------------------------------- | ------------------------------------------------------------ |
| Status                            | Status of the Service Package                                |
| Reference                         | Following values are displayed:<br>– "Code" of the Package is displayed at root level.<br>– The word ‘*SubPackage*’ is displayed to indicate each Subpackage within the Package.<br>– "Reference No." of the root Package BoQ is displayed for each Procurement BoQ.<br>– "Reference Nos" of BoQ Divisions, Subdivisions, Positions etc. are displayed for each BoQ Item type. |
| Outline Specification             | Description of Packages, Sub Packages, Procurement BoQ (root) and Outline Specification of each BoQ Item Type is displayed in this column. |
| Quantity                          | Quantity of Package BoQ is displayed for each BoQ Item Type. |
| UoM                               | Unit of Measure for Package BoQ.                             |
| Procurement Structure             | Procurement Structure assigned to Package BoQ.               |
| Procurement Structure Description | Description of Procurement Structure assigned to Package BoQ. |

<a id="package-item-assignment"></a>

### Package Item Assignment

Package Item Assignment container gives an overview of all Estimate Resources that are assigned to the Package BoQ. This container reacts to the selections made in Package Structure Line Item container, in Service Packages or in Material Packages container.

![Package Item Assignment](./../../image/awp.main/en/9-package-item-assignment.png){.lightbox}

1.  Using the "New Record" option in the container, Users can add new Resources that may have been added in the Estimate Line item.

2.  Using the "Delete Record" option, Users can remove the Package Assignments of Estimate Resources.


> **Hint**: 
>
> If System Option 10095 to “Protect Contracted Package Item Assignment” = True, then the Delete Record option will be disabled for Packages with Status IS CONTRACT = True.

| Column Name                | Description                                                  |
| -------------------------- | ------------------------------------------------------------ |
| Line Item Reference        | Code of the Estimate Line Item that the selection (the container reacts to selections made in Package Structure – Line Items, Package Structure Resources, Service Packages and Material Packages). |
| Line Item Ref. Description | Description of the Estimate Line Item that the selection is linked with. |
| Resource                   | Code of Estimate Resource for the selection.                 |
| Est. Resource Description  | Description of Estimate Resource for the selection.          |
| Is Contracted              | Indicates if the Package that the Estimate Resource is linked with is Contracted or not. |
| Status                     | Status of Procurement Package.                               |
| Procurement Package        | Code of the Procurement Package that the Estimate Resource is linked with. |
| Prc Package Description    | Description of the Procurement Package that the Estimate Resource is linked with. |
| Item No.                   | Item Number in the Procurement (Material) Package.           |
| BoQ Root Item Ref. No      | Root BoQ reference of Package BoQ.                           |
| BoQ Item Ref. No           | BoQ reference of Package BoQ.                                |
| BoQ Item Ref. Breif Info   | BoQ reference and Brief Info of Package BoQ.                 |

### Specification

Depending on whether the focus is on a BoQ position or WIC position selected in Package Structure Line Item container, or on Package BoQ position selected in Service Packages container, this read-only container allows Users to review the Specifications of Project BoQ positions or of Package BoQ positions.

![Specification](./../../image/awp.main/en/10-specification.png){.lightbox}

> **Hint**: 
>
> Selecting an Estimate Line Item will display the Specifications of Project BoQ Position that the Line Item is assigned to.

## Containers in Material Package Allocation

![Containers in Material Package](./../../image/awp.main/en/11-containers-material-packages.png){.lightbox}

The following containers are the same as that described in the previous section on Containers in Service Package Allocation:

- [Project](#project)


- [Package Structure – Line Item](#package-structure-–-line-item)


- [Package Structure – Resource](#package-structure-–-resource)


- [Package Item Assignment](#package-item-assignment)


This section gives an overview of other containers that are not already described above.

### Material Packages 

This container lists all Packages whose Procurement Configuration has "Is Material" flag = True.

![Material Packages](./../../image/awp.main/en/12-material-packages.png){.lightbox}

1.  Using the "New Record" option, Users can create new Packages. This triggers the same input dialogue as that from Package module in Procurement. The first Subproject and Package Project BoQ (Header) is automatically created with this.

2.  "*All Items*" allows Users to remove all filters from the Material Packages container.

3.  Using the "*Item with Assignment*" filter, Users can see all records that are linked with Estimate.

4.  "*Item without Assignment*" filter allows Users to filter the Materials that are created in Procurement but are not linked with Estimate.

5.  "Refresh" fetches the latest values for all records in Service Packages container.

6.  "Refresh Selection" fetches the latest values for the selected entity. This option is enabled only for Subpackages.


| Column Name                       | Description                                                  |
| --------------------------------- | ------------------------------------------------------------ |
| Status                            | Status of Material Package.                                  |
| Item No.                          | Item Number in the Procurement (Material) Package (can be for Cost Code or Material). |
| Material                          | Code of Material in Material Package.                        |
| Description                       | Description of Material Package, Subpackage and Procurement Resources (Cost Code or Materials) used in Package. |
| Quantity                          | Quantity of Cost Code or Material in Package.                |
| UoM                               | Unit of Measure for Package Resource.                        |
| Procurement Structure             | Procurement Structure assigned to Material Package and Procurement Resources (Cost Code or Material). |
| Procurement Structure Description | Description of Procurement Structure assigned to Material Package and Procurement Resources (Cost Code or Material). |

## Wizards in AWP Module 

Since the AWP Module is expected to be the source to manage the Estimate – Procurement interfaces, the following wizards from Estimate are copied to AWP Module from 25.2.

1.  Create/Update BoQ Package

2.  Update BoQ Package(s)

3.  Create/Update Material Package

4.  Update Material Package

5.  Remove Packages from Line Items/ Resources

![Wizards in AWP Module](./../../image/awp.main/en/13-wizards-awp-module.png){.lightbox}

Each of these wizards allows bulk creation / update Packages. All options except Scope Selection remain same as that documented in this Use Case Document for "*[Estimate data exchange with Procurement (Estimator Perspective)](../../usecase.estimatedataexchangewithprocurement/en/index.html)".*

The only difference between the wizards in Estimate and in AWP is that "Current Result Set" is not offered in the Scope Selection. However, Users can continue to work with multi-selection for Scope selection.

# Use Cases

## Overview of all Positions that are linked to a Package

![Overview of Packaged Positions](./../../image/awp.main/en/14-overview-packaged-positions.png){.lightbox}

1.  Using the filter in Package Structure – Line Item container, filter "Items with Assignment".

2.  To review details of any assignment, click on the record in the Package Structure – Line Item container.

3.  "Package Item Assignment" container shows the details of all Resources assigned to the Packages.


## Overview of all Positions that are not linked to a Package

Using the filter for "*Item without Assignment*", Users can automatically filter all Items that are not linked to any Package.

![Overview of Positions not Linked to any Package](./../../image/awp.main/en/15-overview-positions-not-linked-any-package.png){.lightbox}

## Create a new BoQ Package in AWP Module

Users can directly create new BoQ Package from AWP Module using the "New Record" option in the Service Packages container. This automatically creates the first Sub Package and the root BoQ.

![Create a new BoQ Package in AWP Module](./../../image/awp.main/en/16-create-new-boq-package-apw-module.png){.lightbox}

## Create new Positions in Procurement BoQ by drag and drop

### CTRL+ Drag and Drop Divisions with all Positions

Users can use CTRL + drag and drop Divisions (or Sub Divisions) of Project BoQ from Package Structure – Line Item container to the target BoQ in Service Packages container.

If the levels match, the icon will display green plus sign and the selected Division (or Subdivision) with all its children positions will be copied to the target Package BoQ.

![Create new Package Positions with drag-and drop Division or Subdivisions](./../../image/awp.main/en/17-create-new-package-positions-with-drag-drop-Division-Subdivisions.png){.lightbox}

If the levels do not match, 4.0 will display a corresponding icon and message for User Guidance. This message is displayed if User tries to copy (drag and drop by pressing Control).

![User Guidance if BoQ Divisions of Subdivisions levels mismatches](./../../image/awp.main/en/18-user-guidance-boq-divisions-Subdivisions-levels-mismatches.png){.lightbox}

And this message is displayed if User tries to assign (drag and drop without pressing Control).

![User Guidance if BoQ Divisions or Subdivisions levels are dragged without CTRL](./../../image/awp.main/en/19-user-guidance-boq-divisions-Subdivisions-levels-dragged-without-ctrl.png){.lightbox}

### CTRL+ Drag and Drop to create new Positions 

With Control + drag and drop of records from Package Structure Line Item container to the target location in Service Packages container, Users can insert new records in the Service Package BoQ.

![CTRL drag and drop to Position if you want to insert new Positions in existing Packages](./../../image/awp.main/en/20-ctrl-drag-drop-position-you-want-insert-new-positions-existing-packages.png){.lightbox}

## Update existing Positions in Procurement BoQ by drag and drop

With drag and drop of records from Package Structure Line Item container to the target BoQ Positions in Service Packages container, Users can update existing assignments of existing Service Package BoQs.

![Drag and drop to update existing assignments](./../../image/awp.main/en/21-drag-drop-update-existing-assignments.png){.lightbox}

## Edit Descriptions of Package BoQ Positions

Uses can directly adjust the Outline Specification of Division (or Subdivision) and Positions in Service Package container. These changes made in AWP Module are also saved against Package BoQs.

![Change outline specification of Package BoQ](./../../image/awp.main/en/22-change-outline-specification-package-boq.png){.lightbox}

## Create a new Material Package in AWP Module

Users can directly create new Material Package from AWP Module using the "New Record" option in the Material Packages container. This automatically creates the first Sub Package.

![Create a new Material Package in AWP Module](./../../image/awp.main/en/23-create-new-material-package-awp-module.png){.lightbox}

## Create new record in Material Package with drag-and-drop 

With multi-select and Control + drag and drop from Package Structure Resources container to desired target Material Package, Users can create new records directly in Material Packages.

![CTRL drag and drop Resources from Package Structure Resources to Material Package container](./../../image/awp.main/en/24-ctrl-drag-drop-resources-package-structure-resources-material-package-container.png){.lightbox}

## Filter BoQ Positions created in BoQ Packages but not linked to Estimate

Using the filter "*Item Without Assignment*" in the Service Packages container, Users can filter all Procurement BoQ Positions that are not linked with Estimate.

![Filtering 'Item Without Assignment' returns all Procurement BoQ Positions that are not inked with Estimate](./../../image/awp.main/en/25-filtering-item-without-assignment-returns-all-procurement-boq-positions-not-linked-with-estimate.png){.lightbox}

## Assigning BoQ Positions from Packages to Estimates

With drag and drop Package BoQ Positions to Estimate Line Items, any newly created Line Items in Procurement module can be assigned to Estimate.

![Assigning Package Positions to existing Estimate Line Items](./../../image/awp.main/en/26-assigning-package-positions-existing-estimate-line-items.png){.lightbox}

Appropriate User Guidance will be provided if Users try to assign Procurement BoQ Position to a Project BoQ Position (this is not allowed).

![User Guidance if unassigned Package BoQ Positions are dropped to Project BoQ Positions](./../../image/awp.main/en/27-user-guidance-unassigned-package-boq-positions-dropped-project-boq-positions.png){.lightbox}

## Filter Materials created in Material Packages but not linked to Estimate

Using the filter "*Item Without Assignment*" in the Material Packages container, Users can filter all Records that are not linked with Estimate.

![Filtering 'Item Without Assignment' returns all records in Material Packages that are not linked witzh Estimate](./../../image/awp.main/en/28-filtering-item-without-assignment-returns-all-records-material-packages-that-not-linked-with-estimate.png){.lightbox}

## Assigning Resources created in Procurement to Estimate Line Items

Users can use the drag and drop method to assign relations between Procurement Materials and [exact same]{.ul} Resource in Estimate. This assignment can be done from Materials Packages container to Package Structure Resource container.

This method can be used by Estimators if they want to avoid creating new Estimate Line Items (using the "Update Estimate" wizard in Procurement modules.

> **Hint**:
>
> ‘Package Structure – Resource’ container reacts to the selections of Root BoQ / Divisions / Subdivisions / Positions or Estimate Line Items. In all such selections, the quantities of Resources (along with Cost Total etc.) are aggregated.
>
> Though Users can drag and drop Procurement Resources from ‘Material Packages’ container, we recommend precaution as the assignment will be made to the selected scope.



![Assigning Resources from PRC to Estimate (hint- beware of aggregation)](./../../image/awp.main/en/29-assigning-resources-prc-estimate-hint-beware-aggregation.png){.lightbox}
