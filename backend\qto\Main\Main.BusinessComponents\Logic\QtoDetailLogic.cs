
using System;
using System.CodeDom;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data.Entity.Infrastructure;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Text.RegularExpressions;
using System.Transactions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using RIB.Visual.Basics.Api.BusinessComponents;
using RIB.Visual.Basics.Api.Common;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Qto.Formula.BusinessComponents;
using RIB.Visual.Qto.Main.Common;
using RIB.Visual.Qto.Main.Localization.Properties;
using static RIB.Visual.Boq.Main.Core.BoqConstants;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using COMMONNLS = RIB.Visual.Basics.Common.Localization.Properties.Resources;

namespace RIB.Visual.Qto.Main.BusinessComponents
{


	/// <summary>
	/// QtoMainDetailLogic
	/// </summary>
	[Export(typeof(IQtoDetailLogic))]
	[Export("qtodetail", typeof(IChangeStatus))]
	[EntityStatus("QTO_DETAIL_STATUS", "qto.main", "QTO Detail Status")]
	public class QtoDetailLogic : FlatListLookupLogicBase<QtoDetailEntity>, IQtoDetailLogic, IEntityFacade, IChangeStatus
	{
		private int? _QtoTypeFk { get; set; }

		private List<int> _sheetAreaList = new List<int>();
		private List<int> _indexAreaList = new List<int>();
		private List<string> _lineAreaList = new List<string>();
		int? _firstSheet = null;
		int? _lastSheet = null;
		int? _firstIndex = null;
		int? _lastIndex = null;
		string _firstLine = null;
		string _lastLine = null;

		private bool _isAllowCU = false;

		List<BoqLineItemQuantityEntity> _estLineItemToSave = new List<BoqLineItemQuantityEntity>();

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic()
		{
			PermissionGUID = "6d3013bd4af94808bec8d0ec864119c9";
			this.SetRelationInfoIdentifier(RelationInfoIdentifier.QtoDetail);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoTypeFk"></param>
		public QtoDetailLogic SetQtoTypeFk(int? qtoTypeFk)
		{
			_QtoTypeFk = qtoTypeFk;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="sheetAreaList"></param>
		/// <returns></returns>
		public QtoDetailLogic SetSheetAreaList(List<int> sheetAreaList)
		{
			_sheetAreaList = sheetAreaList;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="indexAreaList"></param>
		/// <returns></returns>
		public QtoDetailLogic SetIndexAreaList(List<int> indexAreaList)
		{
			_indexAreaList = indexAreaList;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineAreaList"></param>
		/// <returns></returns>
		public QtoDetailLogic SetLineAreaList(List<string> lineAreaList)
		{
			_lineAreaList = lineAreaList;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public QtoDetailLogic SetRanges()
		{
			_firstSheet = _sheetAreaList.Any() ? _sheetAreaList.First() : null;
			_lastSheet = _sheetAreaList.Any() ? _sheetAreaList.Last() : null;

			_firstIndex = _indexAreaList.Any() ? _indexAreaList.First() : null;
			_lastIndex = _indexAreaList.Any() ? _indexAreaList.Last() : null;

			_firstLine = _lineAreaList.Any() ? _lineAreaList.First() : null;
			_lastLine = _lineAreaList.Any() ? _lineAreaList.Last() : null;

			return this;
		}

		/// <summary>
		/// Allow Create/Update WIP/BILL without WIP/BILL Code assignment checking
		/// </summary>
		/// <param name="isAllowCU"></param>
		/// <returns></returns>
		public QtoDetailLogic SetAllowCUWipBillWithoutWipBillAssginment(bool isAllowCU)
		{
			this._isAllowCU = isAllowCU;
			return this;
		}

		#region set copy options

		bool _isLocation = false;
		bool _isAssetMaster = false;
		bool _isControllingUnit = false;
		bool _isSortCode = false;
		bool _isCostGroup = false;
		bool _isPrc = false;
		bool _isBillTo = false;
		bool _isContract = false;
		private bool _isType = false;
		private bool _isLineItem = false;
		private bool _isIq = false;
		private bool _isBq = false;
		private bool _isGq = false;
		private bool _isAq = false;
		private bool _isWq = false;
		private bool _isUserDefined = false;
		private bool _isPerformedDate = false;
		private bool _isV = false;
		private bool _isRemark = false;
		private bool _isRemark2 = false;
		private bool _isFactor = false;
		private bool _isValue1 = false;
		private bool _isValue2 = false;
		private bool _isValue3 = false;
		private bool _isValue4 = false;
		private bool _isValue5 = false;
		private QtoDetailCopyOption _copyOption = null;

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetIsLocation(bool isLocation)
		{
			_isLocation = isLocation;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetIsAssetMaster(bool isAssetMaster)
		{
			_isAssetMaster = isAssetMaster;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetIsControllingUnit(bool isControllingUnit)
		{
			_isControllingUnit = isControllingUnit;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetIsSortCode(bool isSortCode)
		{
			_isSortCode = isSortCode;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetIsCostGroup(bool isCostGroup)
		{
			_isCostGroup = isCostGroup;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetIsPrc(bool isPrc)
		{
			_isPrc = isPrc;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetIsBillTo(bool isBillTo)
		{
			_isBillTo = isBillTo;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetIsContract(bool isContract)
		{
			_isContract = isContract;
			return this;
		}

		#endregion

		#region qtoLines at project boq, prc, and sales

		string _moduleName = "";
		int? _wipHeaderFk = null;
		int? _bilHeaderFk = null;
		int? _pesHeaderFk = null;

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetModuleName(string moduleName)
		{
			_moduleName = moduleName;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetWipHeaderFk(int? wipHeaderFk)
		{
			_wipHeaderFk = wipHeaderFk;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetBilHeaderFk(int? bilHeaderFk)
		{
			_bilHeaderFk = bilHeaderFk;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetPesHeaderFk(int? pesHeaderFk)
		{
			_pesHeaderFk = pesHeaderFk;
			return this;
		}

		/// <summary>
		///
		/// </summary>
		public QtoDetailLogic SetQtoDetailCopyOption(QtoDetailCopyOption copyOption)
		{
			if (copyOption != null && copyOption.IsActivate)
			{
				_copyOption = copyOption;
				_isLocation = copyOption.IsLocation;
				_isAssetMaster = copyOption.IsAssetMaster;
				_isControllingUnit = copyOption.IsControllingUnit;
				_isSortCode = copyOption.IsSortCode;
				_isCostGroup = copyOption.IsCostGroup;
				_isPrc = copyOption.IsProcurementStructure;
				_isBillTo = copyOption.IsBillTo;
				_isContract = copyOption.IsContract;
				_isType = copyOption.IsType;
				_isLineItem = copyOption.IsLineItem;
				_isIq = copyOption.IsIQ;
				_isBq = copyOption.IsBQ;
				_isGq = copyOption.IsGQ;
				_isAq = copyOption.IsAQ;
				_isWq = copyOption.IsWQ;
				_isUserDefined = copyOption.IsUserDefined;
				_isPerformedDate = copyOption.IsPerformedDate;
				_isV = copyOption.IsV;
				_isRemark = copyOption.IsRemark;
				_isRemark2 = copyOption.IsRemark2;
				_isFactor = copyOption.IsFactor;
				_isValue1 = copyOption.IsValue1;
				_isValue2 = copyOption.IsValue2;
				_isValue3 = copyOption.IsValue3;
				_isValue4 = copyOption.IsValue4;
				_isValue5 = copyOption.IsValue5;
			}

			return this;
		}


		#endregion

		/// <summary>
		/// page number+line reference+line index, if lineReference.length > 1, it is Onorm qto
		/// </summary>
		/// <param name="item"></param>
		public string ConvertQtoDetailReference(QtoDetailEntity item)
		{
			// page number+line reference+line index, if lineReference.length > 1, it is Onorm qto
			if (item.LineReference !=null && item.LineReference.Length > 1)
			{
				item.QtoDetailReference = item.PageNumber.ToString().PadLeft(4, '0') + item.LineReference.ToString() + item.LineIndex.ToString().PadLeft(3, '0');
			}
			else
			{
				item.QtoDetailReference = item.PageNumber.ToString().PadLeft(4, '0') + item.LineReference + item.LineIndex;
			}

			return item.QtoDetailReference;
		}

		/// <summary>
		///
		/// </summary>
		private string IncreaseChar(char charValue, bool isInsert)
		{
			int ascii = (int)charValue;
			if ((!isInsert && ascii == 90) || (isInsert && ascii == 65))
			{
				return null;
			}
			else if (ascii < 65 || ascii > 90)
			{
				ascii = 65;
			}
			else
			{
				ascii = !isInsert ? ascii + 1 : ascii - 1;
			}

			string finalChar = ((char)ascii).ToString();
			if (_lineAreaList.Any() && !_lineAreaList.Contains(finalChar))
			{
				IncreateCharWithLineArea(finalChar[0], isInsert, out finalChar);
			}

			return finalChar;
		}

		/// <summary>
		///
		/// </summary>
		private string SetNextOnormQTOAddress(LineAddress lineAddress, int selectPageNumber, string line, int index, bool isInsert)
		{
			var strCurrentAddress = selectPageNumber.ToString().PadLeft(4, '0') + line + index.ToString().PadLeft(3, '0');

			long currentAddressNo = long.Parse(strCurrentAddress);

			if (!isInsert)
			{
				currentAddressNo += 100;
			}
			else
			{
				currentAddressNo -= 100;
			}

			strCurrentAddress = currentAddressNo.ToString().PadLeft(10, '0');

			if (strCurrentAddress.Length > 10)
			{
				lineAddress.IsOverflow = true;
			}

			int pageNumber = int.Parse(strCurrentAddress.Substring(0, 4));
			string lineReference = strCurrentAddress.Substring(4, 3);

			if (lineAddress != null)
			{
				lineAddress.PageNumber = pageNumber;
				lineAddress.LineReference = selectPageNumber != pageNumber ? null : lineReference;
				lineAddress.LineIndex = int.Parse(strCurrentAddress.Substring(7, 3).ToString());
			}

			return selectPageNumber != pageNumber ? null : lineReference;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="charValue"></param>
		/// <param name="isInsert"></param>
		/// <param name="finalChar"></param>
		private void IncreateCharWithLineArea(char charValue, bool isInsert, out string finalChar)
		{
			int ascii = (int)charValue;
			if ((!isInsert && ascii == 90) || (isInsert && ascii == 65))
			{
				finalChar = null;
				return;
			}
			else if (ascii < 65 || ascii > 90)
			{
				ascii = 65;
			}
			else
			{
				ascii = !isInsert ? ascii + 1 : ascii - 1;
			}

			string tempChar = ((char)ascii).ToString();
			finalChar = tempChar;
			if (!_lineAreaList.Contains(tempChar))
			{
				IncreateCharWithLineArea(tempChar[0], isInsert, out finalChar);
			}

		}

		/// <summary>
		///
		/// </summary>
		private LineAddress GetLastAddress(QtoDetailEntity lastItem, int finalPageNumber, bool isInsert, List<QtoSheetEntity> qtoSheets = null)
		{
			LineAddress lineAddress = new LineAddress();
			if (_lastSheet.HasValue && lastItem.PageNumber == _lastSheet.Value && (_lastLine ?? "Z") == lastItem.LineReference)
			{
				//  if the sheet and line are the last item, mask as Overflow
				lineAddress.IsOverflow = true;
			}
			else
			{
				if (qtoSheets != null && qtoSheets.Count > 0)
				{
					GetNewPageNumber(lastItem.PageNumber, qtoSheets, out int newPageNumber);
					finalPageNumber = newPageNumber;
				}

				if (finalPageNumber > lastItem.PageNumber)
				{
					if (this._QtoTypeFk == (int)QtoType.OnormQTO)
					{
						lineAddress.PageNumber = finalPageNumber;
						lineAddress.LineReference = "000";
						lineAddress.LineIndex = 100;
					}
					else
					{
						lineAddress.PageNumber = finalPageNumber;
						lineAddress.LineReference = _firstLine ?? "A";
						lineAddress.LineIndex = _firstIndex ?? 0;
					}
				}
				else
				{
					if (this._QtoTypeFk == (int)QtoType.OnormQTO)
					{
						SetNextOnormQTOAddress(lineAddress, lastItem.PageNumber, lastItem.LineReference, lastItem.LineIndex, isInsert);
					}
					else
					{
						lineAddress.LineReference = _lineAreaList.Any() && _lastLine == lastItem.LineReference ? null : IncreaseChar(lastItem.LineReference[0], isInsert);
						lineAddress.PageNumber = lastItem.PageNumber;
						lineAddress.LineIndex = lastItem.LineIndex;
					}

					if (lineAddress.LineReference == null)
					{
						lineAddress.PageNumber = !isInsert ? lastItem.PageNumber + 1 : lastItem.PageNumber - 1;
						GetNewPageNumber(lineAddress.PageNumber, qtoSheets, out int newPageNumber);
						lineAddress.PageNumber = newPageNumber;

						if (this._QtoTypeFk == (int)QtoType.OnormQTO)
						{
							lineAddress.LineReference = !isInsert ? "000" : "900";
							lineAddress.LineIndex = 100;
						}
						else
						{
							lineAddress.LineReference = !isInsert ? (_firstLine ?? "A") : (_lastLine ?? "Z");
							lineAddress.LineIndex = _firstIndex ?? 0;
						}
					}
				}
			}

			return lineAddress;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pageNumber"></param>
		/// <param name="qtoSheets"></param>
		/// <param name="newPageNumber"></param>
		private void GetNewPageNumber(int pageNumber, List<QtoSheetEntity> qtoSheets, out int newPageNumber)
		{
			newPageNumber = pageNumber;

			if (qtoSheets != null && qtoSheets.Any())
			{
				var qtoSheetsNotLock = qtoSheets.Where(e => !e.IsReadonly);
				if (qtoSheetsNotLock.Any())
				{
					var qtoSheetPageNumbers = !_sheetAreaList.Any() ? qtoSheetsNotLock.Where(e => e.PageNumber.HasValue).OrderBy(o => o.PageNumber).ToList() :
						qtoSheetsNotLock.Where(e => e.PageNumber.HasValue && _sheetAreaList.Contains(e.PageNumber.Value)).OrderBy(o => o.PageNumber).ToList();

					if (qtoSheetPageNumbers.Any())
					{
						var qtoSheetPageNumber = qtoSheetPageNumbers.FirstOrDefault(e => e.PageNumber == pageNumber);
						var newPageNumberTemp = qtoSheetPageNumber != null ? pageNumber : qtoSheetPageNumbers.FirstOrDefault().PageNumber.Value;

						if (newPageNumberTemp >= newPageNumber)
						{
							newPageNumber = newPageNumberTemp;
							return;
						}
					}
					else
					{
						var sheetNos = GetRootSheetNo();
						var qtoChildSheets = qtoSheetsNotLock.Where(e => !e.PageNumber.HasValue && !e.QtoSheets.Any() && !sheetNos.Contains(e.Description));
						var qtoRootSheets = qtoSheetsNotLock.Where(e => !e.PageNumber.HasValue && !e.QtoSheetFk.HasValue && !e.QtoSheets.Any());

						var qtoSheetPages = qtoChildSheets.Any() ? qtoChildSheets : qtoRootSheets;
						if (_sheetAreaList.Any() && qtoSheetPages.Any())
						{
							qtoSheetPages = GetQtoSheetPagesByAddress(qtoSheetPages);
						}

						if (qtoSheetPages.Any())
						{
							var newPageNumberTemp = qtoSheetPages.FirstOrDefault().From.Value;
							newPageNumberTemp = newPageNumberTemp == 0 ? 1 : newPageNumberTemp;
							if (newPageNumberTemp >= newPageNumber)
							{
								newPageNumber = newPageNumberTemp;
								return;
							}
						}
					}

					GetNotLockNewPageNumber(pageNumber, qtoSheets, out newPageNumber);
				}
				else
				{
					var qtoSheetsHasNoChild = qtoSheets.Where(e => !e.QtoSheets.Any()).OrderBy(o => o.Description).ToList();
					if (qtoSheetsHasNoChild.Any())
					{
						var lastQtoSheet = qtoSheetsHasNoChild.Last();
						if (lastQtoSheet.PageNumber.HasValue)
						{
							newPageNumber = lastQtoSheet.PageNumber.Value + 1;
						}
						else
						{
							var newPageNumberTemp = lastQtoSheet.From.Value;
							newPageNumberTemp = newPageNumberTemp == 0 ? 1 : newPageNumberTemp;
							newPageNumber = newPageNumberTemp;
						}
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		private List<string> GetRootSheetNo()
		{
			List<string> sheetNos = new List<string>() { "0000-9999" };
			if (_QtoTypeFk == 1)
			{
				int startNo = 10000;
				for (int i = 0; i < 9; i++)
				{
					int endNo = startNo + 9999;
					sheetNos.Add(startNo.ToString() + "-" + endNo.ToString());
					startNo = endNo + 1;
				}
			}

			return sheetNos;
		}

		/// <summary>
		///
		/// </summary>
		private List<QtoSheetEntity> GetQtoSheetPagesByAddress(IEnumerable<QtoSheetEntity> qtoSheetPages)
		{
			List<QtoSheetEntity> sheetList = new List<QtoSheetEntity>();

			foreach (var sheet in qtoSheetPages)
			{
				List<int> sheetNos = Enumerable.Range(sheet.From.Value, sheet.To.Value - sheet.From.Value + 1).ToList();
				var existItems = sheetNos.Where(e => _sheetAreaList.Contains(e));
				if (existItems.Any())
				{
					sheetList.Add(sheet);
				}
			}

			return sheetList;
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="pageNumber"></param>
		/// <param name="qtoSheets"></param>
		/// <param name="newPageNumber"></param>
		private void GetNotLockNewPageNumber(int pageNumber, List<QtoSheetEntity> qtoSheets, out int newPageNumber)
		{
			newPageNumber = pageNumber;

			var qtoSheet = qtoSheets.FirstOrDefault(e => e.PageNumber == pageNumber);
			if (qtoSheet != null && (qtoSheet.IsReadonly))
			{
				GetNotLockNewPageNumber(pageNumber + 1, qtoSheets, out newPageNumber);
			}
			else if (_sheetAreaList.Any())
			{
				var findSheet = _sheetAreaList.FirstOrDefault(e => e == pageNumber);
				if (findSheet == 0)
				{
					var sheetInAreea = _sheetAreaList.FirstOrDefault(e => e > pageNumber);
					if (sheetInAreea > 0)
					{
						GetNotLockNewPageNumber(sheetInAreea, qtoSheets, out newPageNumber);
					}
				}
			}
		}

		/// <summary>
		/// get last item; add index or create last item
		/// </summary>
		private QtoDetailEntity GetLastItemTemp(ref LineAddress existLineAdress, List<QtoDetailEntity> existIndexChangeItem, QtoDetailEntity selectItem, ref bool indexAdd, ref bool lastItemCreate, bool isInsert, string nextReference)
		{
			QtoDetailEntity lastTempItem = new QtoDetailEntity();
			if (existIndexChangeItem != null)
			{
				if (existIndexChangeItem.Count > 1)
				{
					existIndexChangeItem = !isInsert ? existIndexChangeItem : existIndexChangeItem.Where(e => e.Id != selectItem.Id && e.LineIndex < selectItem.LineIndex).ToList();
					lastTempItem = existIndexChangeItem.Count > 0 ? existIndexChangeItem[existIndexChangeItem.Count - 1] : null;

					if (lastTempItem != null)
					{
						// next index is last index or not
						int nextIndex = _indexAreaList.Any() ? GetNextIndexInRange(selectItem.LineIndex) : (selectItem.LineIndex + 1);

						int lastItemIndex = lastTempItem.LineIndex + 1;
						if (_indexAreaList.Any())
						{
							int indexTemp = _indexAreaList.FirstOrDefault(e => e > lastTempItem.LineIndex);
							lastItemIndex = indexTemp == 0 ? selectItem.LineIndex : indexTemp;
						}

						if (!isInsert)
						{
							if (lastTempItem.LineIndex > selectItem.LineIndex && lastTempItem.LineIndex != nextIndex)
							{
								indexAdd = true;
							}

							var nextTempItem = existIndexChangeItem.FirstOrDefault(e => e.LineIndex == nextIndex);
							if ((lastTempItem.LineIndex == nextIndex) || nextTempItem != null)
							{
								lastItemCreate = true;
								existLineAdress.PageNumber = selectItem.PageNumber;
								existLineAdress.LineReference = selectItem.LineReference;
								existLineAdress.LineIndex = nextIndex;
							}
						}
						else
						{
							if (lastItemIndex != selectItem.LineIndex)
							{
								indexAdd = true;
							}
							else
							{
								existLineAdress.PageNumber = selectItem.PageNumber;
								existLineAdress.LineReference = selectItem.LineReference;
								existLineAdress.LineIndex = lastItemIndex;
							}
						}
					}
				}
				else if (existIndexChangeItem.Count == 1 && string.IsNullOrEmpty(nextReference) && _lastIndex.HasValue)
				{
					if (!isInsert)
					{
						indexAdd = lastItemCreate = existIndexChangeItem[0].LineIndex == _lastIndex.Value;
					}
				}
			}

			return lastTempItem;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="orderItems"></param>
		/// <returns></returns>
		private int GetCurrentUserPageNumber(List<QtoDetailEntity> orderItems)
		{
			var currentUserId = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
			var items2currentUser = orderItems.Where(e => e.InsertedBy == currentUserId);
			var items2OtherUser = orderItems.Where(e => e.InsertedBy != currentUserId);
			int maxPageNumber = -1;
			if (items2currentUser != null && items2currentUser.Any())
			{
				maxPageNumber = items2currentUser.Max(i => i.PageNumber);
				if (items2OtherUser != null && items2OtherUser.Any())
				{
					var otherPageNumber = items2OtherUser.Max(i => i.PageNumber);
					if (otherPageNumber > maxPageNumber)
						maxPageNumber = otherPageNumber;
				}

				return maxPageNumber;
			}

			return maxPageNumber;
		}

		/// <summary>
		/// set line adderss
		/// </summary>
		private void SetLineAddress(List<QtoDetailEntity> orderItems, ref LineAddress CerlineAddress, ref LineAddress existLineAdress, ref LineAddress targetLineAdress,
			QtoDetailEntity lastItem, QtoDetailEntity selectItem, bool isInsert, List<QtoSheetEntity> qtoSheets = null)
		{
			var currentUserId = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
			int finalPageNumber = lastItem.PageNumber;
			var currentUserItems = orderItems.Where(e => e.InsertedBy == currentUserId || e.InsertedBy == 0).ToList();
			if (selectItem.InsertedBy == 0 && (currentUserItems.Count == 0 || currentUserItems.Any(e => e.Id != selectItem.Id)))
			{
				currentUserItems.Add(selectItem);
				currentUserItems = currentUserItems.OrderBy(e => e.PageNumber).ThenBy(e => e.LineReference).ThenBy(e => e.LineIndex).ToList();
			}
			var pageNumbers = currentUserItems.Select(e => e.PageNumber).Distinct().ToList();
			var currentUserLastItem = currentUserItems.Count > 0 ? currentUserItems[currentUserItems.Count - 1] : null;
			bool isCurrentSelectItem = selectItem != null && currentUserId == selectItem.InsertedBy;
			bool noMutliUser = orderItems.Count == currentUserItems.Count;

			// using Address
			if (selectItem == null)
			{
				selectItem = new QtoDetailEntity();
			}
			CerlineAddress.PageNumber = selectItem.PageNumber;
			CerlineAddress.LineReference = selectItem.LineReference;
			CerlineAddress.LineIndex = selectItem.LineIndex;

			// Append
			LineAddress lineAddressTemp = new LineAddress();
			var nextReference = currentUserItems.Count == 0 ? null : (this._QtoTypeFk == (int)QtoType.OnormQTO ? SetNextOnormQTOAddress(lineAddressTemp, selectItem.PageNumber, selectItem.LineReference, selectItem.LineIndex, isInsert) : IncreaseChar(selectItem.LineReference[0], isInsert));
			var currentMaxPageNumber = currentUserLastItem != null ? currentUserLastItem.PageNumber : (_firstSheet ?? 0);
			var currentNextReference = isCurrentSelectItem ? nextReference : (currentUserLastItem != null ? (this._QtoTypeFk == (int)QtoType.OnormQTO ? SetNextOnormQTOAddress(null, currentUserLastItem.PageNumber, currentUserLastItem.LineReference, currentUserLastItem.LineIndex, isInsert) : IncreaseChar(currentUserLastItem.LineReference[0], isInsert)) : (_firstLine ?? (this._QtoTypeFk == (int)QtoType.OnormQTO ? "000" : "A")));
			int maxPageNumber = 0;
			if (finalPageNumber > currentMaxPageNumber)
			{
				maxPageNumber = finalPageNumber + 1;
			}
			else if (finalPageNumber == currentMaxPageNumber)
			{
				int nextPageNumber = _sheetAreaList.Any() ? GetNextPageNumberInRange(finalPageNumber) : (finalPageNumber + 1);
				if (this._QtoTypeFk == (int)QtoType.OnormQTO)
				{
					maxPageNumber = nextReference != null ? finalPageNumber : nextPageNumber;
				}
				else
				{
					maxPageNumber = noMutliUser || currentNextReference != null ? finalPageNumber : nextPageNumber;
				}
			}

			finalPageNumber = maxPageNumber;
			var pageNumber = (selectItem.InsertedBy == currentUserId || selectItem.InsertedBy == 0) && nextReference != null ? selectItem.PageNumber : maxPageNumber;
			var lineReference = nextReference ?? (_firstLine ?? (this._QtoTypeFk == (int)QtoType.OnormQTO ? "000" : "A"));

			int lastIndex = this._QtoTypeFk == (int)QtoType.OnormQTO ? 999 : (_lastIndex ?? (_QtoTypeFk.HasValue && _QtoTypeFk == 1 ? 99 : 9));
			int defualtIndex = this._QtoTypeFk == (int)QtoType.OnormQTO ? 100 : 0;
			var lineIndex = nextReference != null ? (this._QtoTypeFk == (int)QtoType.OnormQTO ? lineAddressTemp.LineIndex : (selectItem.LineIndex == lastIndex ? (_firstIndex ?? defualtIndex) : selectItem.LineIndex)) : (_firstIndex ?? defualtIndex);

			// if lineIndex > 0 and exist same line references
			var existIndexChangeItem = orderItems.Where(e => e.PageNumber.Equals(selectItem.PageNumber) && e.LineReference.Equals(selectItem.LineReference)).OrderBy(e => e.LineIndex).ToList();

			bool indexAdd = false;
			bool lastItemCreate = false;
			QtoDetailEntity lastItemTemp = null;
			if (selectItem.InsertedBy == currentUserId || selectItem.InsertedBy == 0)
			{
				lastItemTemp = GetLastItemTemp(ref existLineAdress, existIndexChangeItem, selectItem, ref indexAdd, ref lastItemCreate, isInsert, nextReference);
			}

			if (!lastItemCreate)
			{
				if (!indexAdd)
				{
					// create item between select item and next item
					var existItems = orderItems.Where(e => e.PageNumber.Equals(pageNumber) && e.LineReference.Equals(lineReference)).ToList();
					if (this._QtoTypeFk != (int)QtoType.OnormQTO && existItems.Count > 0 && ((!isInsert && existItems[0].LineIndex <= selectItem.LineIndex) || (isInsert && existItems[existItems.Count - 1].LineIndex >= selectItem.LineIndex)))
					{
						if ((!isInsert && selectItem.LineIndex == lastIndex) || (isInsert && selectItem.LineIndex == (_firstIndex ?? defualtIndex)))
						{
							// the lineindex of select item is 9/99/0
							if ((isInsert && existItems[^1].LineIndex == lastIndex) || (!isInsert && existItems[0].LineIndex == (_firstIndex ?? defualtIndex)))
							{
								existLineAdress.PageNumber = !isInsert ? existItems[0].PageNumber : existItems[existItems.Count - 1].PageNumber;
								existLineAdress.LineReference = !isInsert ? existItems[0].LineReference : existItems[existItems.Count - 1].LineReference;
								existLineAdress.LineIndex = !isInsert ? existItems[0].LineIndex : existItems[existItems.Count - 1].LineIndex; ;

								if (!isInsert)
								{
									targetLineAdress = GetLastAddress(lastItem, finalPageNumber, isInsert, qtoSheets);
								}
							}
							else
							{
								if (!isInsert)
								{
									GetNewPageNumber(pageNumber, qtoSheets, out int newPageNumber);
									targetLineAdress.PageNumber = newPageNumber;
									targetLineAdress.LineReference = lineReference;
									targetLineAdress.LineIndex = lineIndex;
								}
								else
								{
									GetNewPageNumber(existItems[^1].PageNumber, qtoSheets, out int newPageNumber);
									targetLineAdress.PageNumber = newPageNumber;
									targetLineAdress.LineReference = existItems[^1].LineReference;
									int nextIndex = _indexAreaList.Any() ? GetNextIndexInRange(existItems[^1].LineIndex) : (existItems[^1].LineIndex + 1);
									targetLineAdress.LineIndex = existItems[^1].LineIndex == lastIndex ? (_lastIndex ?? defualtIndex) : nextIndex;
								}
							}
						}
						else
						{
							// create item
							if (!isInsert)
							{
								GetNewPageNumber(selectItem.PageNumber, qtoSheets, out int newPageNumber);
								targetLineAdress.PageNumber = newPageNumber;
								targetLineAdress.LineReference = selectItem.LineReference;
								int indexIncrement = GetIndexIncrement(orderItems, selectItem);
								int nextIndex = _indexAreaList.Any() ? GetNextIndexInRange(selectItem.LineIndex) : (selectItem.LineIndex + indexIncrement);
								targetLineAdress.LineIndex = selectItem.LineIndex == lastIndex ? defualtIndex : nextIndex;
							}
							else
							{
								if (existItems[^1].LineIndex == lastIndex)
								{
									GetNewPageNumber(selectItem.PageNumber, qtoSheets, out int newPageNumber);
									targetLineAdress.PageNumber = newPageNumber;
									targetLineAdress.LineReference = selectItem.LineReference;
									targetLineAdress.LineIndex = _firstIndex ?? defualtIndex;
								}
								else
								{
									GetNewPageNumber(existItems[^1].PageNumber, qtoSheets, out int newPageNumber);
									targetLineAdress.PageNumber = newPageNumber;
									targetLineAdress.LineReference = existItems[^1].LineReference;
									int indexIncrement = GetIndexIncrement(orderItems, selectItem);
									int nextIndex = _indexAreaList.Any() ? GetNextIndexInRange(existItems[^1].LineIndex) : (existItems[^1].LineIndex + indexIncrement);
									targetLineAdress.LineIndex = nextIndex;
								}
							}
						}
					}
					else
					{
						// create item of next/last item of select item
						if (!isInsert)
						{
							GetNewPageNumber(pageNumber, qtoSheets, out int newPageNumber);
							targetLineAdress.PageNumber = newPageNumber;
							targetLineAdress.LineReference = lineReference;
							targetLineAdress.LineIndex = lineIndex;
						}
						else
						{
							if (isInsert && nextReference == null && pageNumber == 0)
							{
								GetNewPageNumber((_firstSheet ?? 1), qtoSheets, out int newPageNumber);
								targetLineAdress.PageNumber = newPageNumber;
								targetLineAdress.LineReference = selectItem.LineReference;
								targetLineAdress.LineIndex = _firstIndex ?? defualtIndex;
							}
							else
							{
								GetNewPageNumber(pageNumber, qtoSheets, out int newPageNumber);
								targetLineAdress.PageNumber = newPageNumber;
								targetLineAdress.LineReference = lineReference;
								targetLineAdress.LineIndex = this._QtoTypeFk == (int)QtoType.OnormQTO ? lineIndex : selectItem.LineIndex;
							}
						}
					}
				}
				else
				{
					// add index
					if (!isInsert)
					{
						GetNewPageNumber(selectItem.PageNumber, qtoSheets, out int newPageNumber);
						targetLineAdress.PageNumber = newPageNumber;
						targetLineAdress.LineReference = selectItem.LineReference;
						int indexIncrement = GetIndexIncrement(orderItems, selectItem);
						int nextIndex = _indexAreaList.Any() ? GetNextIndexInRange(selectItem.LineIndex) : (selectItem.LineIndex + indexIncrement);
						targetLineAdress.LineIndex = nextIndex;
					}
					else if (lastItemTemp != null)
					{
						GetNewPageNumber(lastItemTemp.PageNumber, qtoSheets, out int newPageNumber);
						targetLineAdress.PageNumber = newPageNumber;
						targetLineAdress.LineReference = lastItemTemp.LineReference;
						int indexIncrement = GetIndexIncrement(orderItems, lastItemTemp);
						int nextIndex = _indexAreaList.Any() ? GetNextIndexInRange(lastItemTemp.LineIndex) : (lastItemTemp.LineIndex + indexIncrement);
						targetLineAdress.LineIndex = nextIndex;
					}

				}
			}
			else
			{
				// create last item
				targetLineAdress = GetLastAddress(lastItem, finalPageNumber, isInsert, qtoSheets);
			}

			if (this._QtoTypeFk != (int)QtoType.FreeQTO)
			{
				targetLineAdress.IsOverflow = targetLineAdress.PageNumber > 9999;
			}
		}

		private int GetNextPageNumberInRange(int pageNumber)
		{
			int temp = _sheetAreaList.FirstOrDefault(e => e > pageNumber);

			return temp == 0 ? pageNumber : temp;
		}

		private int GetNextIndexInRange(int index)
		{
			int temp = _indexAreaList.FirstOrDefault(e => e > index);

			return temp == 0 ? index : temp;
		}

		/// <summary>
		///
		/// </summary>
		private int GetIndexIncrement(List<QtoDetailEntity> orderItems, QtoDetailEntity selectItem)
		{
			int increment = 1;
			if (this._QtoTypeFk == (int)QtoType.OnormQTO)
			{
				var item = orderItems.FirstOrDefault(e => e.PageNumber == selectItem.PageNumber && e.LineReference == selectItem.LineReference && e.LineIndex == selectItem.LineIndex);
				if (item != null)
				{
					int index = orderItems.IndexOf(item);
					if (index != -1 && index < orderItems.Count - 1)
					{
						var strCurrentAddress = selectItem.PageNumber.ToString() + selectItem.LineReference + selectItem.LineIndex.ToString().PadLeft(3, '0');
						long currentAddressNo = long.Parse(strCurrentAddress);

						var nextItem = orderItems[index + 1];
						var strNextAddress = nextItem.PageNumber.ToString() + nextItem.LineReference + nextItem.LineIndex.ToString().PadLeft(3, '0');
						long nextAddressNo = long.Parse(strNextAddress);

						increment = nextAddressNo - currentAddressNo > 100 ? 100 : 1;
					}
					else
					{
						increment = 100;
					}
				}

				if (selectItem.LineIndex + increment > 999)
				{
					increment = 1;
				}
			}

			return increment;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoList"></param>
		/// <param name="qtoSheetFk"></param>
		/// <returns></returns>
		public QtoDetailEntity GetLastQtoDetailByPageNumber(IEnumerable<QtoDetailEntity> qtoList, int qtoSheetFk)
		{
			var qtoLines = qtoList.OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList();
			QtoDetailEntity entity = new QtoDetailEntity();
			var qtosFilter = qtoSheetFk == 0 ? qtoLines : qtoLines.Where(e => e.QtoSheetFk == qtoSheetFk).ToList();
			var returnObj = qtosFilter.Count > 0 ? qtosFilter[qtosFilter.Count - 1] : null;
			return returnObj;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoList"></param>
		/// <returns></returns>
		private QtoDetailEntity CompareQtoLine(List<QtoDetailEntity> qtoList)
		{
			if (qtoList.Any())
			{
				var qtoLines = qtoList.OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList();
				var returnObj = qtoList[qtoList.Count - 1];
				return returnObj;
			}
			else
			{
				return null;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoList"></param>
		/// <param name="selectItem"></param>
		/// <returns></returns>
		public QtoDetailEntity GetQtoDetailByUser(IEnumerable<QtoDetailEntity> qtoList, QtoDetailEntity selectItem)
		{
			var currentUserId = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
			if (currentUserId == selectItem.InsertedBy || selectItem.InsertedBy == 0)
			{
				return null;
			}
			else
			{
				var existLine = qtoList.Where(e => e.InsertedBy == currentUserId).ToList();
				if (existLine.Count > 0)
				{
					return this.CompareQtoLine(existLine);
				}
				else
				{
					var maxQtoPageNumber = qtoList.Max(e => e.PageNumber);
					QtoDetailEntity entity = new QtoDetailEntity();
					entity.PageNumber = maxQtoPageNumber + 1;
					return entity;
				}
			}
		}

		/// <summary>
		/// get the next line address
		/// </summary>
		public Dictionary<string, LineAddress> GetNextLineAddress(QtoDetailEntity selectItem, string selectedPageNumber, int qtoHeaderId, bool isInsert, List<QtoDetailEntity> qtoList = null, List<QtoDetailEntity> multiLines = null, List<QtoSheetEntity> qtoSheets = null, List<QtoDetailEntity> allQtoLines = null)
		{

			// if the selectitem sheet is readonly, set as null
			qtoSheets ??= new QtoStructrueLogic().GetSheetListWithStatusAccessRight(new List<int>() { qtoHeaderId }).ToList();

			Dictionary<string, LineAddress> LineAddressTypeDic = new Dictionary<string, LineAddress>();
			LineAddress CerlineAddress = new LineAddress();
			LineAddress existLineAdress = new LineAddress();
			LineAddress targetLineAdress = new LineAddress();
			var qtoAddressRangeLogic = new QtoAddressRangeLogic();

			// get the filter structure
			int finalPageNumber = -1;
			if (!string.IsNullOrEmpty(selectedPageNumber))
			{
				if (!selectedPageNumber.Contains('-'))
				{
					finalPageNumber = int.Parse(selectedPageNumber);
				}
				else
				{
					string[] pageNumberArray = selectedPageNumber.Split('-');
					finalPageNumber = int.Parse(pageNumberArray[0]);
				}
			}

			// !isInsert: Append, isInsert:Insert. Append: create new item behind select item; Insert: create new item ahead of select item
			using (var dbcontext = new RVPBizComp.DbContext(GetDbModel()))
			{
				// if has address range, will only focus on range qtolines
				var orderItems = qtoList ?? dbcontext.Entities<QtoDetailEntity>().Where(e => e.QtoHeaderFk == qtoHeaderId).ToList().OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList();

				if (qtoList == null)
				{
					orderItems = this.FilterQtoLinesByRange(orderItems).ToList();
				}

				// if the multilins did not save in database, will add to orderItems, for get the correct next line reference
				if (multiLines != null && multiLines.Count > 0)
				{
					foreach (var multiLine in multiLines)
					{
						if (multiLine.Version == 0)
						{
							orderItems.Add(multiLine);
						}
					}
				}

				string defaultLineReference = this._QtoTypeFk == (int)QtoType.OnormQTO ? "000" : "A";
				int defaultLineIndex = this._QtoTypeFk == (int)QtoType.OnormQTO ? 100 : 0;

				if (orderItems != null && orderItems.Count > 0)
				{
					var lastItem = orderItems[^1];

					if ((!isInsert && selectItem == null) || (!isInsert && selectItem != null && selectItem.Id == lastItem.Id)) // Append: create last item
					{
						if (selectItem != null)
						{
							lastItem.PageNumber = selectItem.PageNumber;
							lastItem.LineIndex = selectItem.LineIndex;
							lastItem.LineReference = selectItem.LineReference;
						}

						// if filter the sheet to add
						if (finalPageNumber != -1)
						{
							if (_lastSheet.HasValue && finalPageNumber > _lastSheet.Value)
							{
								targetLineAdress.IsOverflow = true;
							}
							else
							{
								var filterItems = orderItems.Where(e => e.PageNumber.Equals(finalPageNumber)).ToList();

								if (filterItems.Count > 0)
								{
									var filterLastItem = selectItem ?? filterItems[^1];
									SetLineAddress(orderItems, ref CerlineAddress, ref existLineAdress, ref targetLineAdress, lastItem, filterLastItem, isInsert, qtoSheets);
								}
								else
								{
									if (!_sheetAreaList.Any() || _sheetAreaList.Contains(finalPageNumber))
									{
										GetNewPageNumber(finalPageNumber, qtoSheets, out int newPageNumber);
										targetLineAdress.PageNumber = newPageNumber;

										targetLineAdress.LineReference = _firstLine ?? defaultLineReference;
										targetLineAdress.LineIndex = _firstIndex ?? defaultLineIndex;
									}
									else
									{
										targetLineAdress = GetLastAddress(lastItem, finalPageNumber, isInsert, qtoSheets);
									}
								}
							}
						}
						else
						{
							targetLineAdress = GetLastAddress(lastItem, finalPageNumber, isInsert, qtoSheets);
						}
					}
					else
					{
						selectItem ??= lastItem;
						SetLineAddress(orderItems, ref CerlineAddress, ref existLineAdress, ref targetLineAdress, lastItem, selectItem, isInsert, qtoSheets);
					}
				}
				else
				{
					var pageNumber = finalPageNumber == -1 ? (_sheetAreaList.Any() ? _firstSheet.Value : 1) : finalPageNumber;
					GetNewPageNumber(pageNumber, qtoSheets, out int newPageNumber);
					targetLineAdress.PageNumber = newPageNumber;
					if (_lastSheet.HasValue && newPageNumber > _lastSheet.Value)
					{
						targetLineAdress.IsOverflow = true;
					}

					var lineArea = _firstLine ?? defaultLineReference;
					var indexArea = _firstIndex ?? defaultLineIndex;

					if (_QtoTypeFk == (int)QtoType.OnormQTO)
					{
						if (selectItem == null)
						{
							targetLineAdress.LineIndex = indexArea;
							targetLineAdress.LineReference = lineArea;
						}
						else
						{
							SetNextOnormQTOAddress(targetLineAdress, selectItem.PageNumber, selectItem.LineReference, selectItem.LineIndex, isInsert);
						}
					}
					else
					{
						targetLineAdress.LineReference = selectItem != null ? (_lineAreaList.Any() && _lastLine == selectItem.LineReference ? null : IncreaseChar(selectItem.LineReference[0], isInsert)) ?? lineArea : lineArea;
						targetLineAdress.LineIndex = indexArea;
					}
				}

				var isNewPage = allQtoLines != null ? allQtoLines.FirstOrDefault(e => e.PageNumber == targetLineAdress.PageNumber) : null;
				if (isNewPage == null)
				{
					targetLineAdress.IsCheck = true;
				}
			}

			if (_QtoTypeFk == (int)QtoType.OnormQTO)
			{
				CerlineAddress.QtoDetailReference = CerlineAddress.PageNumber.ToString().PadLeft(4, '0') + CerlineAddress.LineReference + CerlineAddress.LineIndex.ToString().PadLeft(3, '0');
				existLineAdress.QtoDetailReference = existLineAdress.PageNumber.ToString().PadLeft(4, '0') + existLineAdress.LineReference + existLineAdress.LineIndex.ToString().PadLeft(3, '0');
				targetLineAdress.QtoDetailReference = targetLineAdress.PageNumber.ToString().PadLeft(4, '0') + targetLineAdress.LineReference + targetLineAdress.LineIndex.ToString().PadLeft(3, '0');
			}
			else
			{
				CerlineAddress.QtoDetailReference = CerlineAddress.PageNumber.ToString().PadLeft(4, '0') + CerlineAddress.LineReference + CerlineAddress.LineIndex.ToString();
				existLineAdress.QtoDetailReference = existLineAdress.PageNumber.ToString().PadLeft(4, '0') + existLineAdress.LineReference + existLineAdress.LineIndex.ToString();
				targetLineAdress.QtoDetailReference = targetLineAdress.PageNumber.ToString().PadLeft(4, '0') + targetLineAdress.LineReference + targetLineAdress.LineIndex.ToString();
			}

			LineAddressTypeDic.Add("CurrenctAddress", CerlineAddress);
			LineAddressTypeDic.Add("ExistAddress", existLineAdress);
			LineAddressTypeDic.Add("TargetAddress", targetLineAdress);

			return LineAddressTypeDic;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public QtoDetailEntity Create()
		{
			QtoDetailEntity entity = new QtoDetailEntity();
			entity.Id = SequenceManager.GetNext("QTO_DETAIL");
			return entity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <param name="lineAddress"></param>
		/// <returns></returns>
		public QtoDetailEntity Create(ParentIdentifier data, LineAddress lineAddress)
		{
			QtoDetailEntity entity = Create();
			entity.QtoHeaderFk = data.QtoHeaderFk;
			entity.BoqItemFk = data.BoqItemFk;
			var qtoHeaderEntity = new QtoHeaderLogic().GetItemByKey(data.QtoHeaderFk);
			if (qtoHeaderEntity != null)
			{
				entity.BoqHeaderFk = qtoHeaderEntity.BoqHeaderFk;
				entity.IsWQ = qtoHeaderEntity.IsWQ;
				entity.IsAQ = qtoHeaderEntity.IsAQ;
				entity.IsIQ = data.IsBillingBoq ? false : qtoHeaderEntity.IsIQ;
				entity.IsBQ = qtoHeaderEntity.IsBQ;
			}
			entity.LineIndex = lineAddress.LineIndex;
			entity.LineReference = lineAddress.LineReference;
			entity.PageNumber = lineAddress.PageNumber;
			entity.IsCheck = lineAddress.IsCheck;
			entity.IsModifyLineReference = true;

			entity.Factor = 1;
			entity.IsBlocked = false;
			entity.IsReadonly = false;
			entity.IsOK = true;
			entity.IsEstimate = false;
			entity.PerformedDate = DateTime.UtcNow;
			if (data.BasUomFk != null && data.BasUomFk.HasValue)
			{
				entity.BasUomFk = data.BasUomFk.Value;
			}

			if (data.BillToFk != null && data.BillToFk.HasValue)
			{
				entity.BillToFk = data.BillToFk;
				var companyId = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				string filter = string.Format("(CompanyFk={0}) and (ProjectFk={1})", companyId, qtoHeaderEntity.ProjectFk);
				var contracts = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesContractLogic>("Contract").GetSalesContract4Api(filter, 0, data.BillToFk).ToList();
				if (contracts.Count > 0)
				{
					if (qtoHeaderEntity.OrdHeaderFk.HasValue)
					{
						var ordHeaderFkValue = qtoHeaderEntity.OrdHeaderFk.Value;
						if (contracts.Exists(e => e.Id == ordHeaderFkValue))
						{
							entity.OrdHeaderFk = ordHeaderFkValue;
						}
						else
						{
							entity.BillToFk = null;
						}
					}
					else
					{
						entity.OrdHeaderFk = data.OrdHeaderFk.HasValue && contracts.Exists(e => e.Id == data.OrdHeaderFk.Value) ? data.OrdHeaderFk.Value : contracts[0].Id;
					}
				}
			}
			else if (!qtoHeaderEntity.OrdHeaderFk.HasValue && data.OrdHeaderFk != null && data.OrdHeaderFk.HasValue)
			{
				entity.OrdHeaderFk = data.OrdHeaderFk.Value;
			}


			entity.QtoLineTypeFk = new QtoLineTypeLogic().GetDefault().Id;

			var qtoFormulaEntity = new QtoFormulaLogic().GetSearchList(e => e.BasRubricCategoryFk == data.BasRubricCategoryFk && e.IsDefault).FirstOrDefault();
			if (qtoFormulaEntity != null)
			{
				entity.QtoFormulaFk = qtoFormulaEntity.Id;
			}

			entity.QtoDetailStatusFk = 1;

			// has boq split quantity
			var qtoList = this.GetListByQtoHeaderId(data.QtoHeaderFk);
			var mapQtoLines2BoqItem = qtoList.Where(e => e.BoqItemFk == entity.BoqItemFk).ToList();
			bool isLoadSplitQty = false;
			List<BoqSplitQuantityEntity> spQtyList = new List<BoqSplitQuantityEntity>();
			if (mapQtoLines2BoqItem.Count > 0)
			{
				var mapSplit2QtoLines = mapQtoLines2BoqItem.Where(e => e.BoqSplitQuantityFk.HasValue).ToList();
				entity.HasSplitQuantiy = false;
				if (mapSplit2QtoLines.Count > 0)
				{
					entity.HasSplitQuantiy = true;
					spQtyList = new BoqSplitQuantityLogic().GetList(entity.BoqHeaderFk, entity.BoqItemFk);
					isLoadSplitQty = true;
					if (spQtyList.Count != 0 && !data.BoqSplitQuantityFk.HasValue)
					{
						entity.BoqSplitQuantityFk = spQtyList[0].Id;
					}
				}

				var mapLineItem2QtoLines = mapQtoLines2BoqItem.Where(e => e.EstLineItemFk.HasValue).ToList();
				entity.HasEstLineItem = false;
				if (mapLineItem2QtoLines.Count > 0)
				{
					entity.EstHeaderFk = mapLineItem2QtoLines[^1].EstHeaderFk;
					entity.EstLineItemFk = mapLineItem2QtoLines[^1].EstLineItemFk;
					entity.HasEstLineItem = true;
				}
			}
			else
			{
				entity.HasSplitQuantiy = true;
				entity.HasEstLineItem = true;
			}

			if (entity.HasSplitQuantiy)
			{
				entity.BoqSplitQuantityFk ??= data.BoqSplitQuantityFk;
				if (!entity.BoqSplitQuantityFk.HasValue)
				{
					spQtyList = isLoadSplitQty ? spQtyList : new BoqSplitQuantityLogic().GetList(entity.BoqHeaderFk, entity.BoqItemFk);
					if (spQtyList.Any())
					{
						entity.BoqSplitQuantityFk = spQtyList[0].Id;
					}
				}

				if (entity.BoqSplitQuantityFk.HasValue)
				{
					var boqSplitEntity = new BoqSplitQuantityLogic().GetSplitQuantityById(entity.BoqHeaderFk, entity.BoqSplitQuantityFk.Value);
					if (boqSplitEntity != null)
					{
						entity.PrjLocationFk = boqSplitEntity.PrjLocationFk;
						entity.PrcStructureFk = boqSplitEntity.PrcStructureFk;
						entity.MdcControllingUnitFk = boqSplitEntity.MdcControllingUnitFk;
					}
				}
			}
			else if (entity.HasEstLineItem)
			{
				if (entity.EstLineItemFk.HasValue)
				{
					var lineItem = Injector.Get<IEstimateMainLineItemLogic>().GetLineItemById(entity.EstLineItemFk.Value, entity.EstHeaderFk.Value).ToList();
					if (lineItem != null && lineItem.Count > 0)
					{
						entity.PrjLocationFk = lineItem[0].PrjLocationFk;
						entity.PrcStructureFk = lineItem[0].PrcStructureFk;
						entity.MdcControllingUnitFk = lineItem[0].MdcControllingUnitFk;
						entity.AssetMasterFk = lineItem[0].MdcAssetMasterFk;
						entity.SortCode01Fk = lineItem[0].SortCode01Fk;
						entity.SortCode02Fk = lineItem[0].SortCode02Fk;
						entity.SortCode03Fk = lineItem[0].SortCode03Fk;
						entity.SortCode04Fk = lineItem[0].SortCode04Fk;
						entity.SortCode05Fk = lineItem[0].SortCode05Fk;
						entity.SortCode06Fk = lineItem[0].SortCode06Fk;
						entity.SortCode07Fk = lineItem[0].SortCode07Fk;
						entity.SortCode08Fk = lineItem[0].SortCode08Fk;
						entity.SortCode09Fk = lineItem[0].SortCode09Fk;
						entity.SortCode10Fk = lineItem[0].SortCode10Fk;
					}
				}
			}
			else
			{
				var boqItem = new BoqItemLogic().GetSearchList(e => e.Id == data.BoqItemFk && e.BoqHeaderFk == entity.BoqHeaderFk).FirstOrDefault();
				if (boqItem != null)
				{
					entity.PrjLocationFk = boqItem.PrjLocationFk;
					entity.PrcStructureFk = boqItem.PrcStructureFk;
					entity.MdcControllingUnitFk = boqItem.MdcControllingUnitFk;
				}
			}

			return entity;
		}

		/// <summary>
		/// Create items
		/// </summary>
		/// <returns></returns>
		public IEnumerable<QtoDetailEntity> Create(List<QtoDetailEntity> qtoLines, int qtoHeaderFk, int basRubricCategoryFk, List<MainItem2CostGroupEntity> costGroups = null, int? boqSplitQuantityFk = null,
			bool isDrag = false, List<QtoDetailEntity> qtoList = null)
		{
			int count = qtoLines.Count;
			List<QtoDetailEntity> entities = new List<QtoDetailEntity>();

			if (count > 0)
			{
				var ids = SequenceManager.GetNextList("QTO_DETAIL", count);

				var sourceQtoHeader = new QtoHeaderLogic().GetItemByKey(qtoHeaderFk);

				// get default qto formula
				basRubricCategoryFk = basRubricCategoryFk > 0 ? basRubricCategoryFk : (sourceQtoHeader != null ? sourceQtoHeader.BasRubricCategoryFk : -1);
				var qtoFormulaEntity = new QtoFormulaLogic().GetSearchList(e => e.BasRubricCategoryFk == basRubricCategoryFk && e.IsDefault).FirstOrDefault();

				// get qto 2 cost groups
				var qtoLineIds = qtoLines.Select(e => e.Id).ToList();
				var sourceCostGroups = costGroups != null ? GetQtoDetail2CostGroupEntities(qtoLineIds).ToList() : null;

				if (qtoList == null)
				{
					qtoList = this.GetListByQtoHeaderId(qtoHeaderFk).ToList();
				}

				var hasSplit = qtoLines.Any(e => e.BoqSplitQuantityFk.HasValue);
				for (int i = 0; i < qtoLines.Count; i++)
				{
					QtoDetailEntity entity = new QtoDetailEntity
					{
						Id = ids[i],
						SourceQtoDetailId = qtoLines[i].Id
					};

					if (qtoFormulaEntity != null && sourceQtoHeader != null && sourceQtoHeader.BasRubricCategoryFk != basRubricCategoryFk)
					{
						entity.QtoFormulaFk = qtoFormulaEntity.Id;
						entity.QtoFormula = qtoFormulaEntity;
					}
					entity.QtoDetailStatusFk = qtoLines[i].QtoDetailStatusFk;
					entity.IsAQ = qtoLines[i].IsAQ;
					entity.IsWQ = qtoLines[i].IsWQ;
					entity.IsIQ = qtoLines[i].IsIQ;
					entity.IsBQ = qtoLines[i].IsBQ;
					entity.IsOK = qtoLines[i].IsOK;
					entity.UserDefined1 = qtoLines[i].UserDefined1;
					entity.UserDefined2 = qtoLines[i].UserDefined2;
					entity.UserDefined3 = qtoLines[i].UserDefined3;
					entity.UserDefined4 = qtoLines[i].UserDefined4;
					entity.UserDefined5 = qtoLines[i].UserDefined5;

					entity.SortCode01Fk = qtoLines[i].SortCode01Fk;
					entity.SortCode02Fk = qtoLines[i].SortCode02Fk;
					entity.SortCode03Fk = qtoLines[i].SortCode03Fk;
					entity.SortCode04Fk = qtoLines[i].SortCode04Fk;
					entity.SortCode05Fk = qtoLines[i].SortCode05Fk;
					entity.SortCode06Fk = qtoLines[i].SortCode06Fk;
					entity.SortCode06Fk = qtoLines[i].SortCode07Fk;
					entity.SortCode07Fk = qtoLines[i].SortCode08Fk;
					entity.SortCode09Fk = qtoLines[i].SortCode09Fk;
					entity.SortCode10Fk = qtoLines[i].SortCode10Fk;
					entity.IsModifyLineReference = qtoLines[i].IsModifyLineReference;

					entity.QtoTypeFk = _QtoTypeFk;

					entity.PerformedDate = DateTime.UtcNow;

					if (isDrag)
					{
						var isExistSplit = qtoList.Any(e => e.BoqHeaderFk == qtoLines[i].BoqHeaderFk && e.BoqItemFk == qtoLines[i].BoqItemFk && e.BoqSplitQuantityFk.HasValue);
						if ((hasSplit || isExistSplit))
						{
							var isExistQtoLine = qtoList.Any(e => e.BoqHeaderFk == qtoLines[i].BoqHeaderFk && e.BoqItemFk == qtoLines[i].BoqItemFk && !e.BoqSplitQuantityFk.HasValue);
							if (isExistQtoLine)
							{
								throw new ValidationException(Resources.Warn_NoSplitAssignMent);
							}
							else
							{
								if (qtoLines[i].BoqSplitQuantityFk.HasValue)
								{

									if (qtoLines[i].BoqHeaderFk == qtoLines[i].SourceBoqHeaderFk && qtoLines[i].BoqItemFk == qtoLines[i].SourceBoqItemFk)
									{
										entity.BoqHeaderFk = qtoLines[i].BoqHeaderFk;
										entity.BoqItemFk = qtoLines[i].BoqItemFk;
										entity.HasSplitQuantiy = true;
										entity.BoqSplitQuantityFk = qtoLines[i].BoqSplitQuantityFk.Value;
									}
									else
									{
										var newSplits = new BoqSplitQuantityLogic().GetList(qtoLines[i].BoqHeaderFk, qtoLines[i].BoqItemFk);
										if (newSplits.Count > 0)
										{
											var sourceSplits = new BoqSplitQuantityLogic().GetList(qtoLines[i].SourceBoqHeaderFk, qtoLines[i].SourceBoqItemFk);
											var sourceSplit = sourceSplits.FirstOrDefault(e => e.Id == qtoLines[i].BoqSplitQuantityFk.Value);
											entity.HasSplitQuantiy = true;
											if (sourceSplit != null && sourceSplit.SplitNo <= newSplits.Count)
											{
												var newSplit = newSplits.FirstOrDefault(e => e.SplitNo == sourceSplit.SplitNo);
												if(newSplit != null)
												{
													entity.BoqSplitQuantityFk = newSplit.Id;
												}
											}
											else
											{
												var mewSplit = newSplits.FirstOrDefault();
												entity.BoqSplitQuantityFk = mewSplit.Id;
												entity.SplitNo = mewSplit.SplitNo;
											}
										}
									}
								}
								else
								{
									throw new ValidationException(Resources.Warn_MixSplitAssignMent);
								}
							}
						}
					}
					else
					{
						// has boq split quantity
						var mapQtoLines2BoqItem = qtoList.Where(e => e.BoqItemFk == qtoLines[i].BoqItemFk).ToList();
						if (mapQtoLines2BoqItem.Count > 0)
						{
							var mapQtoLines = mapQtoLines2BoqItem.Where(e => e.BoqSplitQuantityFk.HasValue).ToList();
							entity.HasSplitQuantiy = false;

							if (mapQtoLines.Count > 0)
							{
								entity.HasSplitQuantiy = true;
								var spQtyList = new BoqSplitQuantityLogic().GetList(entity.BoqHeaderFk, entity.BoqItemFk);
								if (spQtyList.Any())
								{
									entity.BoqSplitQuantityFk = boqSplitQuantityFk = spQtyList.FirstOrDefault().Id;
								}
							}

							var mapLineItem2QtoLines = mapQtoLines2BoqItem.Where(e => e.EstLineItemFk.HasValue).ToList();
							entity.HasEstLineItem = false;
							if (mapLineItem2QtoLines.Count > 0)
							{
								entity.HasEstLineItem = true;
							}
						}
						else
						{
							entity.HasSplitQuantiy = true;
							entity.HasEstLineItem = true;
						}

						if (entity.HasSplitQuantiy)
						{
							entity.BoqSplitQuantityFk = boqSplitQuantityFk;
						}
					}

					entities.Add(entity);

					// new qto 2 cost groups
					if (sourceCostGroups != null && sourceCostGroups.Count > 0)
					{
						var existCostGroups = sourceCostGroups.Where(e => e.MainItemId == qtoLines[i].Id).ToList();
						if (existCostGroups.Count > 0)
						{
							if (costGroups == null)
							{
								costGroups = new List<MainItem2CostGroupEntity>();
							}

							var newIds = GetCostGorupsNewIds(existCostGroups.Count);
							for (int j = 0; j < existCostGroups.Count; j++)
							{
								MainItem2CostGroupEntity costGroup = new MainItem2CostGroupEntity();
								costGroup.Id = newIds[j];
								costGroup.CostGroupCatFk = existCostGroups[j].CostGroupCatFk;
								costGroup.CostGroupFk = existCostGroups[j].CostGroupFk;
								costGroup.MainItemId = entity.Id;
								costGroup.Code = existCostGroups[j].Code;
								costGroups.Add(costGroup);
							}
						}
					}
				}
			}

			return entities;
		}

		private void applyCopyOption(QtoHeaderEntity targetHeader, QtoDetailEntity source, QtoDetailEntity target)
		{
			if(_copyOption == null || !_copyOption.IsActivate)
			{
				return;
			}

			#region qto line properties(QtoLineTypeFk, IsIQ, IsBQ, IsGQ, IsAQ, IsWQ, UserDefined1-5, PerformedDate, RemarkText, Remark1Text, V, Factor, Value1-5, PrjLocationReferenceFk, QtoDetailReferenceFk, BoqItemReferenceFk)

			if (_isType)
			{
				target.QtoLineTypeFk = source.QtoLineTypeFk;
			}

			var qtoTargetType = targetHeader != null ? targetHeader.QtoTargetType : -1;
			var typeIsAqWq = qtoTargetType == (int)QtoTargetType.PrcWqAq || qtoTargetType == (int)QtoTargetType.PrjWqAq;
			var typeIsIqBq = qtoTargetType == (int)QtoTargetType.WipOrBill || qtoTargetType == (int)QtoTargetType.PesBoq;
			var typeIsGq = new QtoHeaderLogic().IsGQAvailable(targetHeader);

			if (_isIq && typeIsIqBq)
			{
				target.IsIQ = source.IsIQ;
			}

			if (_isBq && typeIsIqBq)
			{
				target.IsBQ = source.IsBQ;
			}

			if (_isGq && typeIsGq)
			{
				target.IsGQ = source.IsGQ;
			}

			if (_isAq && typeIsAqWq)
			{
				target.IsAQ = source.IsAQ;
			}

			if (_isWq && typeIsAqWq)
			{
				target.IsWQ = source.IsWQ;
			}

			target.UserDefined1 = _isUserDefined ? source.UserDefined1 : null;
			target.UserDefined2 = _isUserDefined ? source.UserDefined2 : null;
			target.UserDefined3 = _isUserDefined ? source.UserDefined3 : null;
			target.UserDefined4 = _isUserDefined ? source.UserDefined4 : null;
			target.UserDefined5 = _isUserDefined ? source.UserDefined5 : null;

			target.PerformedDate = _isPerformedDate ? source.PerformedDate : target.PerformedDate;

			target.V = _isV ? source.V : null;

			target.RemarkText = _isRemark ? source.RemarkText : null;

			target.Remark1Text = _isRemark2 ? source.Remark1Text : null;

			target.Factor = _isFactor ? source.Factor : 1;

			if (_isValue1)
			{
				target.Value1 = source.Value1;
				target.Value1Detail = source.Value1Detail;
				target.LineText = source.LineText;

				if (target.QtoLineTypeFk == (int)EQtoLineType.RowReference)
				{
					target.QtoDetailReferenceFk = source.QtoDetailReferenceFk;
				}
				else if (target.QtoLineTypeFk == (int)EQtoLineType.BoqReference || target.QtoLineTypeFk == (int)EQtoLineType.BoqAndLocationReference)
				{
					target.BoqItemReferenceFk = source.BoqItemReferenceFk;
				}
			}
			else
			{
				target.Value1 = null;
				target.Value1Detail = null;
				target.LineText = null;
				target.QtoDetailReferenceFk = null;
				target.BoqItemReferenceFk = null;
			}

			if (_isValue2)
			{
				target.Value2 = source.Value2;
				target.Value2Detail = source.Value2Detail;

				if (target.QtoLineTypeFk == (int)EQtoLineType.BoqAndLocationReference)
				{
					target.PrjLocationReferenceFk = source.PrjLocationReferenceFk;
				}
			}
			else
			{
				target.Value2 = null;
				target.Value2Detail = null;
				target.PrjLocationReferenceFk = null;
			}

			target.Value3 = _isValue3 ? source.Value3 : null;
			target.Value4 = _isValue4 ? source.Value4 : null;
			target.Value5 = _isValue5 ? source.Value5 : null;
			target.Value3Detail = _isValue3 ? source.Value3Detail : null;
			target.Value4Detail = _isValue4 ? source.Value4Detail : null;
			target.Value5Detail = _isValue5 ? source.Value5Detail : null;

			#endregion


			#region qto line structure properties(PrjLocationFk, MdcControllingUnitFk, EstLineItemFk, PrcStructureFk, AssetMasterFk, SortCode01-10Fk, BillToFk, OrdHeaderFk, CostGroup)
			// todo: should consider source and target qto detail belongs the same project or not;
			// todo: support apply copy option for copy property from source to target
			#endregion


		}

		/// <summary>
		/// GetDbModel
		/// </summary>
		/// <returns></returns>
		public override DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="Ids"></param>
		/// <returns></returns>
		public IEnumerable<QtoDetailEntity> GetDetailsByIds(IEnumerable<int> Ids)
		{
			return GetSearchList(e => Ids.Contains(e.Id), Ids.Any()).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoHeaderId"></param>
		/// <returns></returns>
		public IEnumerable<QtoDetailEntity> GetListByQtoHeaderId(int qtoHeaderId)
		{
			IEnumerable<QtoDetailEntity> qtoDetailEntityList = GetSearchList(e => e.QtoHeaderFk == qtoHeaderId).OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList().ToList();
			return qtoDetailEntityList;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoHeaderIds"></param>
		/// <returns></returns>
		public IEnumerable<QtoDetailEntity> GetListByQtoHeaderIds(List<int> qtoHeaderIds)
		{
			IEnumerable<QtoDetailEntity> qtoDetailEntityList = GetSearchList(e => qtoHeaderIds.Contains(e.QtoHeaderFk)).ToList();
			return qtoDetailEntityList;
		}

		/// <summary>
		/// isExistUseLocation
		/// </summary>
		/// <param name="locationIds"></param>
		/// <returns></returns>
		public bool isExistUseLocation(IEnumerable<int> locationIds)
		{
			var enities = GetSearchList(e => locationIds.Contains(e.PrjLocationFk.Value) || locationIds.Contains(e.PrjLocationReferenceFk.Value), locationIds.Any()).ToList();
			if (enities.Count() > 0)
				return true;
			else
				return false;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="boqItemId"></param>
		/// <returns></returns>
		public decimal GetSumResult(int boqItemId)
		{
			return GetSearchList(e => e.BoqItemFk == boqItemId && !e.IsBlocked).Sum(e => e.Result);
		}

		/// <summary>
		///
		/// </summary>
		public IEnumerable<IQtoDetailEntity> GetQtoDetailsByQtoHeader(int qtoHeaderFk)
		{
			return GetSearchList(e => e.QtoHeaderFk == qtoHeaderFk && !e.IsBlocked).ToList();
		}

		/// <summary>
		/// Delete
		/// </summary>
		/// <param name="entity"></param>
		public void Delete(QtoDetailEntity entity)
		{
			this.Delete<QtoDetailEntity>(this.GetDbModel(), entity);
		}

		/// <summary>
		/// Delete
		/// </summary>
		/// <param name="entities"></param>
		public void Delete(IEnumerable<QtoDetailEntity> entities)
		{
			this.Delete<QtoDetailEntity>(this.GetDbModel(), entities);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <param name="value"></param>
		/// <param name="isWip"></param>
		/// <param name="isBilling"></param>
		/// <param name="isPes"></param>
		/// <returns></returns>
		public IEnumerable<QtoDetailEntity> UpdateDisabledAndReadonlyQtoDetail(CreateWipOrBillEntity data, int value, bool isWip, bool isBilling, bool isPes)
		{
			List<QtoDetailEntity> qtoDetailsToSave = [];

			foreach (var qtoHeaderFk in data.QtoHeaderFks)
			{
				var sourceQtoDetails = this.GetSearchList(e => e.QtoHeaderFk == qtoHeaderFk).ToList();
				List<QtoDetailEntity> disabledQtoDetails = sourceQtoDetails.Where(e => e.IsBlocked).ToList();
				List<QtoDetailEntity> activeQtoDetails = sourceQtoDetails.Where(e => !e.IsBlocked).ToList();
				List<QtoDetailEntity> readonlyQtoDetails = new List<QtoDetailEntity>();
				if (isWip)
				{
					disabledQtoDetails = disabledQtoDetails.Where(e => e.WipHeaderFk.HasValue && e.WipHeaderFk.Value == value).ToList();

					readonlyQtoDetails = activeQtoDetails.Where(e => !e.IsReadonly && e.WipHeaderFk.HasValue && e.WipHeaderFk.Value == value).ToList();
				}
				else if (isBilling)
				{
					disabledQtoDetails = disabledQtoDetails.Where(e => e.BilHeaderFk.HasValue && e.BilHeaderFk.Value == value).ToList();

					readonlyQtoDetails = activeQtoDetails.Where(e => !e.IsReadonly && e.BilHeaderFk.HasValue && e.BilHeaderFk.Value == value).ToList();
				}
				else if (isPes)
				{
					disabledQtoDetails = disabledQtoDetails.Where(e => e.PesHeaderFk.HasValue && e.PesHeaderFk.Value == value).ToList();

					readonlyQtoDetails = activeQtoDetails.Where(e => !e.IsReadonly && e.PesHeaderFk.HasValue && e.PesHeaderFk.Value == value).ToList();
				}

				disabledQtoDetails.ForEach(e =>
				{
					if (isWip)
					{
						e.WipHeaderFk = null;
					}
					else if (isBilling)
					{
						e.BilHeaderFk = null;
					}
					else if (isPes)
					{
						e.PesHeaderFk = null;
					}

				});
				qtoDetailsToSave.AddRange(disabledQtoDetails);

				readonlyQtoDetails.ForEach(e =>
				{
					e.IsReadonly = true;

				});
				qtoDetailsToSave.AddRange(readonlyQtoDetails);
			}

			if (qtoDetailsToSave.Count > 100)
			{
				this.BulkSave(GetDbModel(), qtoDetailsToSave);
			}
			else
			{
				this.Save(qtoDetailsToSave);
			}

			return qtoDetailsToSave;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <param name="value"></param>
		/// <param name="isWip"></param>
		/// <param name="isBilling"></param>
		/// <param name="isPes"></param>
		/// <param name="sw1"></param>
		/// <param name="timestr"> timestr</param>
		public IEnumerable<QtoDetailEntity> UpdateQTODetail(CreateWipOrBillEntity data, int value, bool isWip, bool isBilling, bool isPes, Stopwatch sw1, ref StringBuilder timestr)
		{
			if (sw1 == null)
			{
				sw1 = new Stopwatch();
			}
			sw1.Restart();

			var salesContractLogic = Injector.Get<ISalesContractLogic>("Contract");

			List<QtoDetailEntity> result = new List<QtoDetailEntity>();

			var qtoHeaderLogic = new QtoHeaderLogic();

			foreach (var qtoHeaderFk in data.QtoHeaderFks)
			{
				List<QtoDetailEntity> qtoDetails = new List<QtoDetailEntity>();

				var isOrdQuantityOnly = data.IsOrdQuantityOnly;

				if (value < 0 || qtoHeaderFk < 0) { return new List<QtoDetailEntity>(); }

				var qtoHeader = qtoHeaderLogic.GetItemByKey(qtoHeaderFk);

				data.HasOrdHeaderFk = isWip && qtoHeader.OrdHeaderFk.HasValue;

				var ordHeaderFk = data.BaseOrdHeaderFk;
				ISalesContractHeaderEntity contractEntity = null;
				if (isWip || isBilling)
				{
					ordHeaderFk = qtoHeader.OrdHeaderFk
						?? data.OrdHeader2BoqHeaderMapping.FirstOrDefault(e => e.BoqHeaderFks.Contains(qtoHeader.BoqHeaderFk))?.OrdHeaderFk
						?? data.BaseOrdHeaderFk;

					contractEntity = salesContractLogic.GetContractById(ordHeaderFk, false);
				}

				var sourceQtoDetails = this.GetListByQtoHeaderId(qtoHeaderFk);

				List<QtoDetailEntity> disabledQtoDetails = sourceQtoDetails.Where(e => e.IsBlocked).ToList();

				var activeQtoDetails = sourceQtoDetails.Where(e => !e.IsBlocked).ToList();

				List<QtoDetailEntity> readOnlyDetails = new List<QtoDetailEntity>();

				if (isWip)
				{
					// 1, no contract assined;
					// 2: has contract:
					//    2.1: no bill assigned
					//    2.2: has bill assigned, the contract should filter by RelatedOrdHeaderFks
					if (this._isAllowCU && !data.HasOrdHeaderFk)
					{
						sourceQtoDetails = sourceQtoDetails.Where(e => !e.OrdHeaderFk.HasValue
							 || (e.OrdHeaderFk.HasValue && !e.BilHeaderFk.HasValue)
							 || (e.BilHeaderFk.HasValue && e.OrdHeaderFk.Value == ordHeaderFk)).ToList();
					}

					if (data.HasOrdHeaderFk)
					{
						qtoDetails = sourceQtoDetails.Where(e => e.IsIQ
							&& e.QtoHeaderFk == qtoHeaderFk
							&& !e.WipHeaderFk.HasValue
							&& (this._isAllowCU || !e.BilHeaderFk.HasValue)
							&& !e.IsBlocked
							&& !e.PesHeaderFk.HasValue).ToList();
					}
					else if (isOrdQuantityOnly)
					{
						qtoDetails = sourceQtoDetails.Where(e => e.IsIQ
							&& e.QtoHeaderFk == qtoHeaderFk
							&& !e.WipHeaderFk.HasValue
							&& (this._isAllowCU || !e.BilHeaderFk.HasValue)
							&& !e.PesHeaderFk.HasValue
							&& !e.IsBlocked
							).ToList();

						qtoDetails = qtoDetails.Where(e => e.OrdHeaderFk.HasValue && data.RelatedOrdHeaderFks.Contains(e.OrdHeaderFk.Value)).ToList();
					}
					else
					{
						qtoDetails = sourceQtoDetails.Where(e => e.IsIQ
							&& e.QtoHeaderFk == qtoHeaderFk
							&& !e.WipHeaderFk.HasValue
							&& (this._isAllowCU || !e.BilHeaderFk.HasValue)
							&& !e.PesHeaderFk.HasValue
							&& !e.IsBlocked
							).ToList();

						var ids = qtoDetails.Select(e => e.Id).ToList();

						var list = sourceQtoDetails.Where(e => e.IsIQ
							&& e.QtoHeaderFk == qtoHeaderFk
							&& !e.IsBlocked
							&& (this._isAllowCU || !e.BilHeaderFk.HasValue)
							&& !e.WipHeaderFk.HasValue
							&& !e.PesHeaderFk.HasValue
							).ToList();


						list = list.Where(e => !ids.Contains(e.Id)
								&& e.OrdHeaderFk.HasValue
								&& data.RelatedOrdHeaderFks.Contains(e.OrdHeaderFk.Value)
						).ToList();

						qtoDetails.AddRange(list);
					}

					disabledQtoDetails = disabledQtoDetails.Where(e => e.WipHeaderFk.HasValue && e.WipHeaderFk.Value == value).ToList();

					readOnlyDetails = activeQtoDetails.Where(e => !e.IsReadonly && e.WipHeaderFk.HasValue && e.WipHeaderFk.Value == value).ToList();
				}
				else if (isBilling)
				{
					// 1, no contract assined;
					// 2: has contract:
					//    2.1: no wip assigned
					//    2.2: has wip assigned, the contract should filter by RelatedOrdHeaderFks
					if (this._isAllowCU && !data.HasOrdHeaderFk)
					{
						sourceQtoDetails = sourceQtoDetails.Where(e => !e.OrdHeaderFk.HasValue
							 || (e.OrdHeaderFk.HasValue && !e.WipHeaderFk.HasValue)
							 || (e.WipHeaderFk.HasValue && e.OrdHeaderFk.Value == ordHeaderFk)).ToList();
					}

					if (data.HasOrdHeaderFk)
					{
						qtoDetails = sourceQtoDetails.Where(e => e.IsBQ
							&& e.QtoHeaderFk == qtoHeaderFk
							&& !e.BilHeaderFk.HasValue
							&& !e.PesHeaderFk.HasValue
							&& !e.IsBlocked
							&& (this._isAllowCU || !e.WipHeaderFk.HasValue)).ToList();
					}
					else if (isOrdQuantityOnly)
					{
						qtoDetails = sourceQtoDetails.Where(e => e.IsBQ
							&& e.QtoHeaderFk == qtoHeaderFk
							&& !e.BilHeaderFk.HasValue
							&& !e.PesHeaderFk.HasValue
							&& !e.IsBlocked
							&& (this._isAllowCU || !e.WipHeaderFk.HasValue)).ToList();

						qtoDetails = qtoDetails.Where(e => e.OrdHeaderFk.HasValue && data.RelatedOrdHeaderFks.Contains(e.OrdHeaderFk.Value)).ToList();
					}
					else
					{
						qtoDetails = sourceQtoDetails.Where(e => e.IsBQ
							&& e.QtoHeaderFk == qtoHeaderFk
							&& !e.BilHeaderFk.HasValue
							&& !e.PesHeaderFk.HasValue
							&& !e.IsBlocked
							&& (this._isAllowCU || !e.WipHeaderFk.HasValue)).ToList();

						var ids = qtoDetails.Select(e => e.Id).ToList();

						var tempList = sourceQtoDetails.Where(e => e.IsBQ
							&& e.QtoHeaderFk == qtoHeaderFk
							&& !e.IsBlocked
							&& !e.BilHeaderFk.HasValue
							&& !e.PesHeaderFk.HasValue
							&& (this._isAllowCU || !e.WipHeaderFk.HasValue)).ToList();

						tempList = tempList.Where(e => !ids.Contains(e.Id)
								&& e.OrdHeaderFk.HasValue
								&& data.RelatedOrdHeaderFks.Contains(e.OrdHeaderFk.Value)
							).ToList();

						qtoDetails.AddRange(tempList);
					}

					disabledQtoDetails = disabledQtoDetails.Where(e => e.BilHeaderFk.HasValue && e.BilHeaderFk.Value == value).ToList();

					readOnlyDetails = activeQtoDetails.Where(e => !e.IsReadonly && e.BilHeaderFk.HasValue && e.BilHeaderFk.Value == value).ToList();
				}
				else if (isPes)
				{
					qtoDetails = sourceQtoDetails.Where(e => e.IsIQ && e.QtoHeaderFk == qtoHeaderFk && !e.WipHeaderFk.HasValue && !e.BilHeaderFk.HasValue && !e.PesHeaderFk.HasValue && !e.IsBlocked).ToList();

					disabledQtoDetails = disabledQtoDetails.Where(e => e.PesHeaderFk.HasValue && e.PesHeaderFk.Value == value).ToList();

					readOnlyDetails = activeQtoDetails.Where(e => !e.IsReadonly && e.PesHeaderFk.HasValue && e.PesHeaderFk.Value == value).ToList();
				}

				if ((data.QtoScope == 1 || data.QtoScope == 2) && data.QtoDetailIds.Any())
				{
					qtoDetails = qtoDetails.Where(e => data.QtoDetailIds.Contains(e.Id)).ToList();

					readOnlyDetails = readOnlyDetails.Where(e => data.QtoDetailIds.Contains(e.Id)).ToList();

					if (isWip || isBilling)
					{
						qtoDetails = GetMultiLineQtos(data, qtoHeaderFk, qtoDetails);

						readOnlyDetails = GetMultiLineQtos(data, qtoHeaderFk, readOnlyDetails);
					}
				}

				sw1.Stop();
				timestr.AppendLine("function  UpdateQTODetail :get qto lines :--------->" + sw1.ElapsedMilliseconds + " ms");

				sw1.Restart();

				// set the readonly for qtoLines
				readOnlyDetails.ForEach(e =>
				{
					e.IsReadonly = true;
				});

				// set WipHeaderFk, BilHeaderFk and PesHeaderFk to qto lines
				qtoDetails.ForEach(e =>
				{
					if (isWip)
					{
						e.WipHeaderFk = value;
						if (!isOrdQuantityOnly)
						{
							e.OrdHeaderFk = ordHeaderFk;
							e.BillToFk = contractEntity.BillToFk;
						}
					}
					else if (isBilling)
					{
						e.BilHeaderFk = value;
						if (!isOrdQuantityOnly)
						{
							e.OrdHeaderFk = ordHeaderFk;
							e.BillToFk = contractEntity.BillToFk;
						}
					}
					else
					{
						e.PesHeaderFk = value;
					}

					e.IsReadonly = true;

				});

				//if the qto is disabeld,need remove the wip/bill/pes reference
				disabledQtoDetails.ForEach(e =>
				{
					if (isWip)
					{
						e.WipHeaderFk = null;
					}
					else if (isBilling)
					{
						e.BilHeaderFk = null;
					}
					else
					{
						e.PesHeaderFk = null;
					}

				});

				sw1.Stop();
				timestr.AppendLine("function  UpdateQTODetail :foreach the  qto lines :--------->" + sw1.ElapsedMilliseconds + " ms");

				qtoDetails.AddRange(disabledQtoDetails);

				qtoDetails.AddRange(readOnlyDetails);

				result.AddRange(qtoDetails);
			}

			if (result.Count > 100)
			{
				this.BulkSave(GetDbModel(), result);
			}
			else
			{
				this.Save(result);
			}

			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <param name="qtoHeaderFk"></param>
		/// <param name="qtoDetails"></param>
		/// <param name="allQtoDetails"></param>
		/// <returns></returns>
		public List<QtoDetailEntity> GetMultiLineQtos(CreateWipOrBillEntity data, int qtoHeaderFk, List<QtoDetailEntity> qtoDetails,List<QtoDetailEntity> allQtoDetails = null)
		{
			//get the multiQtos within the same group
			if ((data.QtoScope == 1 || data.QtoScope == 2))
			{

				var qtoHeader = new QtoHeaderLogic().GetItemByKey(qtoHeaderFk);
				var qtoFormulas = new QtoFormulaLogic().GetSearchList(qf => qf.BasRubricCategoryFk == qtoHeader.BasRubricCategoryFk &&
																	 (qf.QtoFormulaTypeFk != (int)EQtoFormulaType.FreeInput ^ qf.Code == "91")).ToList();

				var mutltiQtos = new List<QtoDetailEntity>();
				var qtoGroups = new List<QtoDetailEntity>();
				if (qtoDetails != null && qtoDetails.Any())
				{
					var multiQtoFormulas = qtoFormulas.Where(e => e.IsMultiline).ToList();
					var multiQtoFormulasIds = multiQtoFormulas.Select(e => e.Id).ToList();

					var dicQtoFormulas = multiQtoFormulas.ToDictionary(e => e.Id, e => e);

					mutltiQtos = qtoDetails.Where(e => e.QtoFormulaFk.HasValue && multiQtoFormulasIds.Contains(e.QtoFormulaFk.Value)).ToList();
					if (mutltiQtos.Any())
					{
						if (allQtoDetails == null)
						{
							allQtoDetails = new QtoDetailLogic().GetSearchList(qd => qd.QtoHeaderFk == qtoHeaderFk).ToList();
						}
						allQtoDetails = allQtoDetails.OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList();

						var qtoDetailHelper = new QtoDetailHelper();
						foreach (var qto in mutltiQtos)
						{
							var qtoGroupsKeys = qtoGroups.Select(e => e.Id).ToList();
							if (!qtoGroupsKeys.Contains(qto.Id))
							{
								qto.QtoFormula = dicQtoFormulas[qto.QtoFormulaFk.Value];
								var groupQtos = qtoDetailHelper.GetReferencedMultiLineQtos(qto, allQtoDetails);
								qtoGroups.AddRange(groupQtos);
							}
						}

						foreach (var qto in qtoGroups)
						{
							var qtoGroupsKeys = qtoDetails.Select(e => e.Id).ToList();
							if (!qtoGroupsKeys.Contains(qto.Id))
							{
								qtoDetails = qtoDetails.Append(qto).ToList();
							}
						}
					}
				}
			}

			return qtoDetails;
		}

		/// <summary>
		/// get requested qto details by the request id
		/// </summary>
		public IEnumerable<QtoDetailEntity> GetQtoDetailsFromRequestItem(string requestId, int destQtoHeaderId)
		{
			var list = new List<QtoDetailEntity>();
			var lines = new List<QtoDetailEntity>();

			var requestItem = new BasicsApiInquiryLogic().GetInquiry(requestId);
			if (requestItem != null)
			{
				var items = InquiryFactory.GetInquireItemFromString<RequestEntity>(requestItem.ItemData);
				if (items != null && items.Any())
				{
					var ids = items.Select(e => e.Id).ToList();

					list.AddRange(this.GetDetailsByIds(ids).ToList());
				}
			}

			list = list.Distinct().ToList();
			lines.AddRange(list);

			if (list.Count > 0)
			{
				var qtoHeaderId = list[0].QtoHeaderFk;
				var sourceQtoHeader = new QtoHeaderLogic().GetItemByKey(qtoHeaderId);
				var destQtoHeader = new QtoHeaderLogic().GetItemByKey(destQtoHeaderId);
				var sourceQtoFormulas = new QtoFormulaLogic().GetSearchList(x => x.BasRubricCategoryFk == sourceQtoHeader.BasRubricCategoryFk).ToList();

				var qtoList = new List<QtoDetailEntity>();
				var qtoMultiLines = new List<QtoDetailEntity>();
				foreach (var line in lines)
				{
					var qtoFormula = sourceQtoFormulas.FirstOrDefault(e => e.Id == line.QtoFormulaFk);
					if (!qtoMultiLines.Any(e => e.Id == line.Id) && qtoFormula != null && qtoFormula.IsMultiline)
					{
						line.QtoFormula = qtoFormula;
						if (qtoList.Count <= 0)
						{
							qtoList = this.GetListByQtoHeaderId(qtoHeaderId).ToList();
						}

						qtoMultiLines = new QtoDetailHelper().GetReferencedMultiLineQtoData(line, qtoList).ToList();
						foreach (var multiLine in qtoMultiLines)
						{
							if (!list.Any(e => e.Id == multiLine.Id))
							{
								list.Add(multiLine);
							}
						}
					}
				}

				// mapping the boq reference and uom
				var soureBoqTrees = new BoqItemLogic().GetBoqItems(sourceQtoHeader.BoqHeaderFk, new BoqItemStructureOption() { AddWicNo = true }).Flatten(e => e.BoqItemChildren).ToList();
				var destBoqTrees = new BoqItemLogic().GetBoqItems(destQtoHeader.BoqHeaderFk, new BoqItemStructureOption() { AddWicNo = true }).Flatten(e => e.BoqItemChildren).ToList();
				if (qtoHeaderId != destQtoHeaderId)
				{
					foreach (var item in list)
					{
						item.SourceBoqHeaderFk = item.BoqHeaderFk;
						item.SourceBoqItemFk = item.BoqItemFk;
						var existSoureBoq = soureBoqTrees.Where(e => e.BoqHeaderFk == item.BoqHeaderFk && e.Id == item.BoqItemFk).FirstOrDefault();
						if (existSoureBoq != null)
						{
							var mapDestBoq = destBoqTrees.Where(e => e.Reference == existSoureBoq.Reference).FirstOrDefault();
							if (mapDestBoq != null)
							{
								item.IsSameUom = existSoureBoq.BasUomFk == mapDestBoq.BasUomFk;
								if (item.IsSameUom)
								{
									item.BoqHeaderFk = mapDestBoq.BoqHeaderFk;
									item.BoqItemFk = mapDestBoq.Id;
									item.BasUomFk = existSoureBoq.BasUomFk;
								}
								else
								{
									item.BoqHeaderFk = 0;
									item.BoqItemFk = 0;
								}

							}
							else
							{
								item.IsSameUom = true;
								item.BoqHeaderFk = 0;
								item.BoqItemFk = 0;
							}
						}
						else
						{
							item.IsSameUom = true;
							item.BoqHeaderFk = 0;
							item.BoqItemFk = 0;
						}
					}
				}
				else
				{
					foreach (var item in list)
					{
						item.SourceBoqHeaderFk = item.BoqHeaderFk;
						item.SourceBoqItemFk = item.BoqItemFk;
						item.IsSameUom = true;
					}
				}

				list = list.OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList();

				if (sourceQtoHeader.BasRubricCategoryFk != destQtoHeader.BasRubricCategoryFk)
				{
					var qtoFormulas = new QtoFormulaLogic().GetSearchList(x => x.BasRubricCategoryFk == destQtoHeader.BasRubricCategoryFk).ToList();
					var existFreeInputs = qtoFormulas.Where(e => e.QtoFormulaTypeFk == 2).ToList(); // free input
					var freeInputIds = existFreeInputs.Select(e => e.Id).ToList();
					var existFreeInput = existFreeInputs.FirstOrDefault(); // free input
					if (existFreeInput != null)
					{
						foreach (var item in list)
						{
							item.IsCopy = true;
							if (item.QtoFormulaFk.HasValue && !freeInputIds.Contains(item.QtoFormulaFk.Value))
							{
								item.QtoFormulaFk = existFreeInput.Id;
								string lineTextTemp = item.FormulaResult;
								if (lineTextTemp.Length > 2)
								{
									lineTextTemp = lineTextTemp.Trim().Substring(1, lineTextTemp.Length - 2);
									item.LineText = lineTextTemp + "=";
								}
								else
								{
									item.LineText = "";
								}

								item.FormulaResult = "";
								item.FormulaResultUI = "";
								item.Value1 = 0;
								item.Value1Detail = "";
								item.Operator1 = "";
								item.Value2 = 0;
								item.Value2Detail = "";
								item.Operator2 = "";
								item.Value3 = 0;
								item.Value3Detail = "";
								item.Operator3 = "";
								item.Value4 = 0;
								item.Value4Detail = "";
								item.Operator4 = "";
								item.Value5 = 0;
								item.Value5Detail = "";
								item.Operator5 = "";
							}
						}
					}
					else
						list[0].IsCopy = false;
				}
				else
				{
					list[0].IsCopy = true;
				}

				if (sourceQtoHeader.ProjectFk != destQtoHeader.ProjectFk)
				{
					foreach (var item in list)
					{
						item.PrjLocationFk = null;
						item.BoqSplitQuantityFk = null;
						item.PrcStructureFk = null;
						item.MdcControllingUnitFk = null;
						item.IsNotCpoyCostGrp = true;
					}

				}

				if (sourceQtoHeader.BoqHeaderFk != destQtoHeader.BoqHeaderFk)
				{
					foreach (var item in list)
					{
						item.BoqSplitQuantityFk = null;
					}
				}
			}

			return list;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public QtoDetailEntity GetDetailsById(int Id)
		{
			return GetSearchList(e => e.Id == Id).FirstOrDefault();
		}

		/// <summary>
		/// RequestEntity
		/// </summary>
		public class RequestEntity
		{
			/// <summary>
			/// </summary>
			public int Id { get; set; }
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="Id"></param>
		/// <param name="qtoHeaderId"></param>
		/// <param name="pageNumber"></param>
		/// <param name="lineReference"></param>
		/// <param name="lineIndex"></param>
		/// <returns></returns>
		public bool IsQtoDetailReferenceUnique(int[] Id, int qtoHeaderId, int[] pageNumber, string[] lineReference, int[] lineIndex)
		{
			bool result = true;
			List<QtoDetailEntity> qtoDetailEntityList = GetSearchList(e => !Id.Contains(e.Id) && e.QtoHeaderFk == qtoHeaderId
				&& pageNumber.Contains(e.PageNumber) && lineReference.Contains(e.LineReference) && lineIndex.Contains(e.LineIndex)).ToList();
			if (qtoDetailEntityList.Any())
			{
				result = false;
			}

			return result;
		}

		/// <summary>
		/// change the page number to get the new address
		/// </summary>
		public LineAddress GetAddressByPageNumber(int Id, int qtoHeaderId, int pageNumber)
		{
			LineAddress address = new LineAddress();

			using (var dbcontext = new RIB.Visual.Platform.BusinessComponents.DbContext(GetDbModel()))
			{
				var orderItems = dbcontext.Entities<QtoDetailEntity>().Where(e => e.Id != Id && e.QtoHeaderFk == qtoHeaderId && e.PageNumber == pageNumber)
					.OrderBy(n => n.LineReference)
					.ThenBy(n => n.LineIndex).ToList();

				if (orderItems.Count > 0)
				{
					var lastItem = orderItems[orderItems.Count - 1];
					address.LineReference = IncreaseChar(lastItem.LineReference[0], false);
					int lastIndex = _QtoTypeFk.HasValue && _QtoTypeFk == 1 ? 99 : 9;
					if (address.LineReference == null && lastItem.LineIndex == lastIndex)
					{
						address.PageNumber = 0;
						address.LineIndex = 0;
					}
					else
					{
						address.PageNumber = pageNumber;
						address.LineIndex = lastItem.LineIndex;
					}
				}
				else
				{
					address.PageNumber = pageNumber;
					address.LineReference = "A";
					address.LineIndex = 0;
				}
			}

			return address;
		}

		/// <summary>
		/// HasItemByPesId
		/// </summary>
		/// <param name="pesId">pesId</param>
		/// <returns>bool</returns>
		public bool HasItemByPesId(int pesId)
		{
			return GetSearchList(s => s.PesHeaderFk == pesId).Any();
		}

		/// <summary>
		/// HasItemByWipId
		/// </summary>
		/// <param name="wipId">pesId</param>
		/// <returns>bool</returns>
		public bool HasItemByWipId(int wipId)
		{
			return GetSearchList(s => s.WipHeaderFk == wipId).Any();
		}

		/// <summary>
		/// HasItemByQtoFormulaId
		/// </summary>
		/// <param name="formulaId">qto formula id</param>
		/// <returns>bool</returns>
		public bool HasItemByQtoFormulaId(int formulaId)
		{
			return GetSearchList(s => s.QtoFormulaFk == formulaId).Any();
		}

		/// <summary>
		///
		/// </summary>
		public IEnumerable<QtoDetailEntity> GetListByQtoSheetId(int qtoHeaderId, List<int> qtoSheetIds)
		{
			IEnumerable<QtoDetailEntity> qtoDetailEntityList = GetSearchList(e => e.QtoHeaderFk == qtoHeaderId && qtoSheetIds.Contains(e.QtoSheetFk)).ToList();
			return qtoDetailEntityList;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="wipFks"></param>
		/// <returns></returns>
		public IEnumerable<IQtoDetailEntity> GetQtoDetailByWipFks(List<int> wipFks)
		{
			IEnumerable<QtoDetailEntity> qtoDetailEntityList = GetSearchList(e => e.WipHeaderFk.HasValue && wipFks.Contains(e.WipHeaderFk.Value)).ToList();
			return qtoDetailEntityList;
		}
		/// <summary>
		/// Get Qto Details By Bill Ids
		/// </summary>
		/// <param name="billFks">list of bill ids</param>
		/// <returns>list of qto details</returns>
		public IEnumerable<IQtoDetailEntity> GetQtoDetailByBillFks(List<int> billFks)
		{
			IEnumerable<QtoDetailEntity> qtoDetailEntityList = GetSearchList(e => e.BilHeaderFk.HasValue && billFks.Contains(e.BilHeaderFk.Value)).ToList();
			return qtoDetailEntityList;
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="PesFks"></param>
		/// <returns></returns>
		public IEnumerable<IQtoDetailEntity> GetQtoDetailByPesFks(List<int> PesFks)
		{
			IEnumerable<QtoDetailEntity> qtoDetailEntityList = GetSearchList(e => e.PesHeaderFk.HasValue && PesFks.Contains(e.PesHeaderFk.Value)).ToList();
			return qtoDetailEntityList;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="orderFks"></param>
		/// <returns></returns>
		public IEnumerable<IQtoDetailEntity> GetQtoDetailByOrderFks(List<int> orderFks)
		{
			IEnumerable<QtoDetailEntity> qtoDetailEntityList = GetSearchList(e => e.OrdHeaderFk.HasValue && orderFks.Contains(e.OrdHeaderFk.Value)).ToList();
			return qtoDetailEntityList;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="estHeaderIdLineItemMap"></param>
		/// <returns></returns>
		/// <exception cref="RIB.Visual.Platform.OperationalManagement.BusinessLayerException"></exception>
		IEnumerable<IQtoDetailEntity> IQtoDetailLogic.GetQtoDetailByEstimateInfo(Dictionary<int, List<int>> estHeaderIdLineItemMap)
		{
			int batch = 1000;
			var estHeaderIds = estHeaderIdLineItemMap.Select(e => e.Key).ToList();
			var estLineItemIds = estHeaderIdLineItemMap.SelectMany(e => e.Value).ToList();

			List<QtoDetailEntity> entities = [];

			var lineItemIdCount = estLineItemIds.Count;
			if (lineItemIdCount < batch)
			{
				entities = this.GetSearchList(e => e.EstHeaderFk.HasValue && estHeaderIds.Contains(e.EstHeaderFk.Value) && e.EstLineItemFk.HasValue && estLineItemIds.Contains(e.EstLineItemFk.Value)).ToList();
			}
			else
			{
				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					try
					{
						var identificationData = estHeaderIdLineItemMap.SelectMany(e => e.Value.Select(i => new RIB.Visual.Platform.Core.IdentificationData() { PKey1 = i, PKey2 = e.Key }).ToList()).ToList();
						string requestId = BusinessApplication.BusinessEnvironment.RegisterTempIdsRequestUuid();
						BulkInsertIdentificationDataToTempTable(identificationData.ToArray(), requestId);

						var entityQuery = dbContext.Entities<QtoDetailEntity>().AsQueryable();
						entityQuery = new TempDataIntegrator<DdTempIdsEntity>((e, tmp) => e.EstLineItemFk == tmp.Key1 && e.EstHeaderFk == tmp.Key2).ReduceByTempData(entityQuery, dbContext, requestId);

						entities = entityQuery.ToList();
					}
					catch (Exception e)
					{
						throw new RIB.Visual.Platform.OperationalManagement.BusinessLayerException("Failed!..." + e.Message, e);
					}
				}
			}

			// page number+line reference+line index
			foreach (var entity in entities)
			{
				entity.QtoDetailReference = ConvertQtoDetailReference(entity);
			}
			return entities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="identificationData"></param>
		/// <param name="requestId"></param>
		public void BulkInsertIdentificationDataToTempTable(RIB.Visual.Platform.Core.IdentificationData[] identificationData, String requestId)
		{
			if (identificationData.Length < 1000)
			{
				AddIdentificationDataToTempTable(identificationData, requestId);
				return;
			}

			using (var connection = (System.Data.SqlClient.SqlConnection)RIB.Visual.Platform.BusinessComponents.DbContext.CreateConnection())
			{
				connection.Open();

				try
				{
					var ta = new System.Transactions.TransactionScope();

					try
					{
						var tableName = "BAS_DDTEMPIDS";
						using (
							var bulkCopy = new System.Data.SqlClient.SqlBulkCopy(connection, System.Data.SqlClient.SqlBulkCopyOptions.CheckConstraints | System.Data.SqlClient.SqlBulkCopyOptions.FireTriggers,
								null)
							{ DestinationTableName = tableName })
						{
							var dt = new System.Data.DataTable { TableName = tableName };
							dt.Columns.Add("REQUESTID", typeof(string));
							dt.Columns.Add("ID", typeof(int));
							dt.Columns.Add("KEY1", typeof(int));
							dt.Columns.Add("KEY2", typeof(int));
							dt.Columns.Add("KEY3", typeof(int));
							foreach (var data in identificationData)
							{
								dt.Rows.Add(requestId, data.Id, data.PKey1, data.PKey2, data.PKey3);
							}

							bulkCopy.WriteToServer(dt);
						}

						ta.Complete();
					}
					finally
					{
						ta.Dispose();
					}
				}
				finally
				{
					connection.Close();
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="QtoDetailEntities"></param>
		/// <returns></returns>
		public IEnumerable<IQtoDetailEntity> BatchSave(IEnumerable<IQtoDetailEntity> QtoDetailEntities)
		{
			var _QtoDetailEntities = new List<QtoDetailEntity>();
			foreach (var item in QtoDetailEntities)
			{
				_QtoDetailEntities.Add((QtoDetailEntity)item);

			}
			return this.Save(_QtoDetailEntities);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoDetailIds"></param>
		/// <returns></returns>
		public IEnumerable<MainItem2CostGroupEntity> GetQtoDetail2CostGroupEntities(List<int> qtoDetailIds)
		{
			var entities = new MainItem2CostGroupLogic("QTO_DETAIL2COSTGRP").GetByFilter(e => qtoDetailIds.Contains(e.MainItemId.Value));

			return entities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoHeaderIds"></param>
		public List<int> getQtoHeaderHasQtoDetail(int[] qtoHeaderIds)
		{
			var param = string.Join(", ", qtoHeaderIds);
			string sql = string.Format("select QTO_HEADER_FK as QCount from [QTO_DETAIL] where QTO_HEADER_FK in ({0}) group by QTO_HEADER_FK having COUNT(QTO_HEADER_FK)>0", param);
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var result = dbcontext.SqlQuery<int>(sql).ToList();
				return result;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="count"></param>
		/// <returns></returns>
		public List<int> GetCostGorupsNewIds(int count)
		{
			var newIds = this.SequenceManager.GetNextList("QTO_DETAIL2COSTGRP", count);
			return newIds.ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public int GetCostGroupNewId()
		{
			var newId = this.SequenceManager.GetNext("QTO_DETAIL2COSTGRP");
			return newId;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public CostGroupCompleteEntity GetCostGroupCats(int? projectId)
		{
			return new CostGroupCatLogic().GetCostGroupCatsByModule(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.QuantityTakeOff, projectId);
		}

		/// <summary>
		/// Update the status
		/// </summary>
		/// <param name="id"></param>
		/// <param name="statusId"></param>
		/// <returns></returns>
		public QtoDetailEntity UpdateStatus(int id, int statusId)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var item = dbcontext.Entities<QtoDetailEntity>().Where(e => e.Id == id).FirstOrDefault();
				if (item == null) return null;
				item.QtoDetailStatusFk = statusId;
				return Save(item);
			}
		}

		/// <summary>
		/// update the boq wq/aq by qto lines
		/// </summary>
		/// <param name="qtoHeaderId"></param>
		/// <param name="qtoScope"></param>
		/// <param name="qtoDetailIds"></param>
		/// <returns></returns>
		public bool UpdateQtoResult2BoqQty(int qtoHeaderId, int qtoScope, IEnumerable<int> qtoDetailIds)
		{
			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				var qtoHeader = new QtoHeaderLogic().GetItemByKey(qtoHeaderId);
				var allQtoDetails = this.GetListByQtoHeaderId(qtoHeaderId).ToList();
				var qtoLines = allQtoDetails;

				if (qtoScope == 1 || qtoScope == 2)
				{
					var qtoDetails = allQtoDetails.Where(e => qtoDetailIds.Contains(e.Id)).ToList();
					CreateWipOrBillEntity data = new CreateWipOrBillEntity();
					data.QtoScope = qtoScope;
					qtoLines = GetMultiLineQtos(data, qtoHeaderId, qtoDetails, allQtoDetails);
				}
				var boqItems = new BoqItemLogic().GetBoqItemsByHeaderId(qtoHeader.BoqHeaderFk);
				var spitItems = new BoqSplitQuantityLogic().GetList(qtoHeader.BoqHeaderFk);

				// update boq wq/aq
				UpadateBoqWqAqIqBqByQtoLines(qtoLines, boqItems, "", spitItems);

				// save boq
				new BoqItemLogic().SaveEntities(boqItems);

				// save boq split quantity
				new BoqSplitQuantityLogic().Save(spitItems);

				// recalculate boq
				var boqRootItem = boqItems.Where(e => !e.BoqItemFk.HasValue).FirstOrDefault(); //new BoqItemLogic().GetBoqRootItemByHeaderId(qtoHeader.BoqHeaderFk);
				if (qtoHeader.QtoTargetType == (int)QtoTargetType.PrcWqAq)
				{
					IPrcBoqLogic prcBoqLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcBoqLogic>();
					var prcBoqEntity = prcBoqLogic.GetPrcBoqItemByBoHeader(qtoHeader.BoqHeaderFk);
					var headerId = prcBoqEntity.PackageFk;
					new BoqItemLogic().CalculateBoqTreeAndSave(boqRootItem, "procurement.package", headerId);
				}
				else
					new BoqItemLogic().CalculateBoqTreeAndSave(boqRootItem);

				transaction.Complete();
			}

			return true;
		}

		/// <summary>
		/// update boq wq/aq
		/// </summary>
		private void UpadateBoqWqAqIqBqByQtoLines(IEnumerable<QtoDetailEntity> qtoLines, IEnumerable<BoqItemEntity> boqItems, string moduleName, List<BoqSplitQuantityEntity> spitItems = null, int headerId = 0)
		{
			var qtoLinesGroupByBoq = qtoLines.GroupBy(e => e.BoqItemFk).ToList();

			Dictionary<int, decimal> boqItem2ResultAQ = new Dictionary<int, decimal>();
			Dictionary<int, decimal> boqItem2ResultWQ = new Dictionary<int, decimal>();
			Dictionary<int, decimal> boqItem2ResultBQ = new Dictionary<int, decimal>();
			Dictionary<int, decimal> boqItem2ResultIQ = new Dictionary<int, decimal>();
			Dictionary<int, decimal> boqItem2ResultPesIQ = new Dictionary<int, decimal>();

			Dictionary<int, bool> boItemId2WqCheck = new Dictionary<int, bool>();
			Dictionary<int, bool> boItemId2AqCheck = new Dictionary<int, bool>();
			Dictionary<int, bool> boItemId2IqCheck = new Dictionary<int, bool>();
			Dictionary<int, bool> boItemId2BqCheck = new Dictionary<int, bool>();

			Dictionary<(int, int), (decimal, decimal)> boqItem2ResultQtyWithLineItem = new Dictionary<(int, int), (decimal, decimal)>();
			Dictionary<int, bool> isLineItemAssignedDic = new Dictionary<int, bool>();
			bool isLineItemAssigned = false;
			Dictionary<int, List<IEstLineItemEntity>> boqItem2LineItemsToSave = new Dictionary<int, List<IEstLineItemEntity>>();
			Dictionary<int, List<IEstLineItemEntity>> boqItem2LineItems = new Dictionary<int, List<IEstLineItemEntity>>();
			bool isSaleBoq = moduleName == "sales.wip" || moduleName == "sales.billing";

			// group sum wq/aq by boqitem
			foreach (var lineItems in qtoLinesGroupByBoq)
			{
				#region result for AQ, WQ, IQ and BQ

				var lines2AQ = lineItems.Where(i => i.IsAQ);
				if (!boItemId2AqCheck.ContainsKey(lineItems.Key))
				{
					boItemId2AqCheck.Add(lineItems.Key, lines2AQ.Any());
				}
				if (lines2AQ.Any())
				{
					lines2AQ = lines2AQ.Where(i => !i.IsBlocked && (!i.WipHeaderFk.HasValue && !i.PesHeaderFk.HasValue) && !i.QtoLineTypeFk.Equals(8));
					if (lines2AQ != null && lines2AQ.Any())
					{
						var resultAQ = lines2AQ.Sum(e => e.Result);
						if (!boqItem2ResultAQ.ContainsKey(lineItems.Key))
						{
							boqItem2ResultAQ.Add(lineItems.Key, resultAQ);
						}
					}
				}

				var lines2WQ = lineItems.Where(i => i.IsWQ);
				if (!boItemId2WqCheck.ContainsKey(lineItems.Key))
				{
					boItemId2WqCheck.Add(lineItems.Key, lines2WQ.Any());
				}
				if (lines2WQ.Any())
				{
					lines2WQ = lines2WQ.Where(i => !i.IsBlocked && (!i.WipHeaderFk.HasValue && !i.PesHeaderFk.HasValue) && !i.QtoLineTypeFk.Equals(8));
					if (lines2WQ != null && lines2WQ.Any())
					{
						var resultWQ = lines2WQ.Sum(e => e.Result);
						if (!boqItem2ResultWQ.ContainsKey(lineItems.Key))
						{
							boqItem2ResultWQ.Add(lineItems.Key, resultWQ);
						}
					}
				}

				var lines2IQ = lineItems.Where(i => i.IsIQ);
				if (!boItemId2IqCheck.ContainsKey(lineItems.Key))
				{
					boItemId2IqCheck.Add(lineItems.Key, lines2IQ.Any());
				}

				if (moduleName == "sales.wip")
				{
					if (lines2IQ.Any())
					{
						// if has lineitem assignment, then use the lineitem result
						var qtoLine = lines2IQ.FirstOrDefault(e => e.EstLineItemFk.HasValue);
						if (qtoLine != null)
						{
							isLineItemAssignedDic.TryAdd(lineItems.Key, true);
							isLineItemAssigned = true;
						}
						else
						{
							lines2IQ = lines2IQ.Where(i => !i.IsBlocked && i.WipHeaderFk.HasValue && !i.QtoLineTypeFk.Equals(8));
							if (lines2IQ != null && lines2IQ.Any())
							{
								var resultIQ = lines2IQ.Sum(e => e.Result);
								if (!boqItem2ResultIQ.ContainsKey(lineItems.Key))
								{
									boqItem2ResultIQ.Add(lineItems.Key, resultIQ);
								}
							}
						}
					}
				}

				if (moduleName == "procurement.pes")
				{
					if (lines2IQ.Any())
					{
						var lines2PesIQ = lines2IQ.Where(i => !i.IsBlocked && i.PesHeaderFk.HasValue && !i.QtoLineTypeFk.Equals(8));
						if (lines2PesIQ != null && lines2PesIQ.Any())
						{
							var resultIQ = lines2PesIQ.Sum(e => e.Result);
							if (!boqItem2ResultPesIQ.ContainsKey(lineItems.Key))
							{
								boqItem2ResultPesIQ.Add(lineItems.Key, resultIQ);
							}
						}
					}
				}

				var lines2BQ = lineItems.Where(i => i.IsBQ);
				if (!boItemId2BqCheck.ContainsKey(lineItems.Key))
				{
					boItemId2BqCheck.Add(lineItems.Key, lines2BQ.Any());
				}

				if (moduleName == "sales.billing")
				{
					if (lines2BQ.Any())
					{
						// if has lineitem assignment, then use the lineitem result
						var qtoLine = lines2BQ.FirstOrDefault(e => e.EstLineItemFk.HasValue);
						if (qtoLine != null)
						{
							isLineItemAssignedDic.TryAdd(lineItems.Key, true);
							isLineItemAssigned = true;
						}
						else
						{
							lines2BQ = lines2BQ.Where(i => !i.IsBlocked && i.BilHeaderFk.HasValue && !i.QtoLineTypeFk.Equals(8));
							if (lines2BQ != null && lines2BQ.Any())
							{
								var resultBQ = lines2BQ.Sum(e => e.Result);
								if (!boqItem2ResultBQ.ContainsKey(lineItems.Key))
								{
									boqItem2ResultBQ.Add(lineItems.Key, resultBQ);
								}
							}
						}
					}
				}
				#endregion
			}

			if (isLineItemAssigned && isSaleBoq)
			{
				bool isIq = moduleName == "sales.wip";
				SetSumResultWithLineItem(qtoLines, boqItem2ResultQtyWithLineItem, true, isIq);
				var lineItemLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();
				var boqItemFks = qtoLines.Select(e => e.BoqItemFk).Distinct().ToList();
				var boqHeaderFks = qtoLines.Select(e => e.BoqHeaderFk).Distinct().ToList();
				var lineItems = lineItemLogic.ByBoqs(boqItemFks, boqHeaderFks);
				boqItem2LineItems = lineItems.Where(e => e.BoqItemFk.HasValue).GroupBy(e => e.BoqItemFk.Value).ToDictionary(e => e.Key, e => e.ToList());

				var estHeaderFks = qtoLines.Where(e => e.EstHeaderFk.HasValue).Select(e => e.EstHeaderFk.Value).Distinct().ToList();
				var estLineItemFks = qtoLines.Where(e => e.EstLineItemFk.HasValue).Select(e => e.EstLineItemFk.Value).Distinct().ToList();
				var lineItemsToSave = lineItemLogic.GetLineItemsByIds(estLineItemFks, estHeaderFks);
				boqItem2LineItemsToSave = lineItemsToSave.Where(e => e.BoqItemFk.HasValue).GroupBy(e => e.BoqItemFk.Value).ToDictionary(e => e.Key, e => e.ToList());
			}

			List<BoqSplitQuantityEntity> newSplitItems = new List<BoqSplitQuantityEntity>();
			List<KeyValuePair<int, decimal>> wqResults;
			List<KeyValuePair<int, decimal>> aqResults;
			List<KeyValuePair<int, decimal>> bqResults;
			List<KeyValuePair<int, decimal>> iqResults;
			List<KeyValuePair<int, decimal>> pesIqResults;

			// update the qtoline result to boq wq/aq
			foreach (var boqItem in boqItems)
			{
				wqResults = new List<KeyValuePair<int, decimal>>();
				aqResults = new List<KeyValuePair<int, decimal>>();
				bqResults = new List<KeyValuePair<int, decimal>>();
				iqResults = new List<KeyValuePair<int, decimal>>();
				pesIqResults = new List<KeyValuePair<int, decimal>>();

				if (boqItem != null && (boqItem.IsItem || boqItem.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity))
				{
					bool hasBoqItems = boqItem.BoQItems != null && boqItem.BoQItems.Count != 0;
					bool hasCrbBoqItems = false;
					if (hasBoqItems)
					{
						hasCrbBoqItems = boqItem.BoQItems.Any(e => e is CrbBoqItemEntity && e.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity);
					}

					if (hasCrbBoqItems)
					{
						var crbEntities = boqItem.BoQItems.Where(e => e is CrbBoqItemEntity && e.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity).Select(e => e as CrbBoqItemEntity).Where(e => e.IsSubQuantityUsedForTheCalculation());

						var crbSubQuantitiyIds = crbEntities.Select(e => e.Id);

						if (boqItem2ResultWQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).Any())
						{
							wqResults = boqItem2ResultWQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).ToList();
						}

						if (boqItem2ResultAQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).Any())
						{
							aqResults = boqItem2ResultAQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).ToList();
						}

						if (boqItem.BoqItemPrjItemFk.HasValue)
						{
							var prjCrbEntities = boqItem.BoQItems.Where(e => e is CrbBoqItemEntity && e.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity && e.BoqItemPrjItemFk.HasValue).Select(e => e as CrbBoqItemEntity).Where(e => e.IsSubQuantityUsedForTheCalculation());

							var prjCrbSubQuantitiyIds = crbEntities.Select(e => e.BoqItemPrjItemFk.Value);

							switch (moduleName)
							{
								case "sales.billing":
									if (boqItem2ResultBQ.Any())
									{
										bqResults = boqItem2ResultBQ.Where(e => prjCrbSubQuantitiyIds.Contains(e.Key)).ToList();
									}
									break;
								case "sales.wip":
									if (boqItem2ResultIQ.Any())
									{
										iqResults = boqItem2ResultIQ.Where(e => prjCrbSubQuantitiyIds.Contains(e.Key)).ToList();
									}
									break;
								case "procurement.pes":
									if (boqItem2ResultPesIQ.Any())
									{
										pesIqResults = boqItem2ResultPesIQ.Where(e => prjCrbSubQuantitiyIds.Contains(e.Key)).ToList();
									}
									break;
								default:
									break;
							}
						}
					}
					else
					{
						if (boqItem2ResultWQ.ContainsKey(boqItem.Id))
						{
							wqResults = boqItem2ResultWQ.Where(e => e.Key == boqItem.Id).ToList();
						}

						if (boqItem2ResultAQ.ContainsKey(boqItem.Id))
						{
							aqResults = boqItem2ResultAQ.Where(e => e.Key == boqItem.Id).ToList();
						}

						if (boqItem.BoqItemPrjItemFk.HasValue)
						{
							switch (moduleName)
							{
								case "sales.billing":
									if (boqItem2ResultBQ.Any())
									{
										bqResults = boqItem2ResultBQ.Where(e => e.Key == boqItem.BoqItemPrjItemFk.Value).ToList();
									}
									break;
								case "sales.wip":
									if (boqItem2ResultIQ.Any())
									{
										iqResults = boqItem2ResultIQ.Where(e => e.Key == boqItem.BoqItemPrjItemFk.Value).ToList();
									}
									break;
								case "procurement.pes":
									if (boqItem2ResultPesIQ.Any())
									{
										pesIqResults = boqItem2ResultPesIQ.Where(e => e.Key == boqItem.BoqItemPrjItemFk.Value).ToList();
									}
									break;
								default:
									break;
							}
						}
					}

					var noQtoLine = qtoLines == null || !qtoLines.Any();
					// check wq, aq, iq and bq or not
					bool isWqCheckWithBoqItem = noQtoLine || boItemId2WqCheck.ContainsKey(boqItem.Id) && boItemId2WqCheck[boqItem.Id];
					bool isAqCheckWithBoqItem = noQtoLine || boItemId2AqCheck.ContainsKey(boqItem.Id) && boItemId2AqCheck[boqItem.Id];
					bool isIqCheckWithBoqItem = noQtoLine || boItemId2IqCheck.ContainsKey(boqItem.Id) && boItemId2IqCheck[boqItem.Id];
					bool isBqCheckWithBoqItem = noQtoLine || boItemId2BqCheck.ContainsKey(boqItem.Id) && boItemId2BqCheck[boqItem.Id];

					if (isWqCheckWithBoqItem)
					{
						boqItem.Quantity = wqResults.Any() ? wqResults.Sum(e => e.Value) : 0;
						boqItem.QuantityDetail = boqItem.Quantity > 0 ? boqItem.Quantity.ToString() : "";
					}

					if (isAqCheckWithBoqItem)
					{
						boqItem.QuantityAdj = aqResults.Any() ? aqResults.Sum(e => e.Value) : 0;
						boqItem.QuantityAdjDetail = boqItem.QuantityAdj > 0 ? boqItem.QuantityAdj.ToString() : "";
					}

					#region pes/wip/bill use the version boqitem

					if (boqItem.BoqItemPrjItemFk.HasValue)
					{
						isIqCheckWithBoqItem = noQtoLine || (boItemId2IqCheck.ContainsKey(boqItem.BoqItemPrjItemFk.Value) && boItemId2IqCheck[boqItem.BoqItemPrjItemFk.Value]);
						isBqCheckWithBoqItem = noQtoLine || (boItemId2BqCheck.ContainsKey(boqItem.BoqItemPrjItemFk.Value) && boItemId2BqCheck[boqItem.BoqItemPrjItemFk.Value]);

						switch (moduleName)
						{
							case "sales.billing":
								if (isBqCheckWithBoqItem)
								{
									if (isLineItemAssignedDic.TryGetValue(boqItem.BoqItemPrjItemFk.Value, out var value) && value)
									{
										var lineItemsToSave = boqItem2LineItemsToSave.TryGetValue(boqItem.BoqItemPrjItemFk.Value, out var itemsToSave) ? itemsToSave : [];
										CollectLineItemQtyToSave(boqItem, lineItemsToSave, boqItem2ResultQtyWithLineItem, headerId, 2);

										var lineItems = boqItem2LineItems.TryGetValue(boqItem.BoqItemPrjItemFk.Value, out var items) ? items : [];
										GetCurrentQuantityWithLineItemQty(boqItem, lineItems, boqItem2ResultQtyWithLineItem, 2);
									}
									else
									{
										boqItem.Quantity = bqResults.Any() ? bqResults.Sum(e => e.Value) : 0;
										boqItem.QuantityDetail = boqItem.Quantity > 0 ? boqItem.Quantity.ToString() : "";
									}
								}
								break;
							case "sales.wip":
								if (isIqCheckWithBoqItem)
								{
									if (isLineItemAssignedDic.TryGetValue(boqItem.BoqItemPrjItemFk.Value, out var value) && value)
									{
										var lineItemsToSave = boqItem2LineItemsToSave.TryGetValue(boqItem.BoqItemPrjItemFk.Value, out var itemsToSave) ? itemsToSave : [];
										CollectLineItemQtyToSave(boqItem, lineItemsToSave, boqItem2ResultQtyWithLineItem, headerId, 1);

										var lineItems = boqItem2LineItems.TryGetValue(boqItem.BoqItemPrjItemFk.Value, out var items) ? items : [];
										GetCurrentQuantityWithLineItemQty(boqItem, lineItems, boqItem2ResultQtyWithLineItem, 1);
									}
									else
									{
										boqItem.Quantity = iqResults.Any() ? iqResults.Sum(e => e.Value) : 0;
										boqItem.QuantityDetail = boqItem.Quantity > 0 ? boqItem.Quantity.ToString() : "";
									}
								}
								break;
							case "procurement.pes":
								if (isIqCheckWithBoqItem)
								{
									boqItem.Quantity = boqItem2ResultPesIQ.Any() ? boqItem2ResultPesIQ.Sum(e => e.Value) : 0;
									boqItem.QuantityDetail = boqItem.Quantity > 0 ? boqItem.Quantity.ToString() : "";
								}
								break;
							default:
								break;
						}

					}
					#endregion

					#region calculate boq split quantity
					var mapQtoSplitItems = spitItems.Where(e => e.BoqHeaderFk == boqItem.BoqHeaderFk && e.BoqItemFk == boqItem.Id).ToList();
					var mapQtoSplitItemsDic = mapQtoSplitItems.ToDictionary(e => e.Id, e => e);

					if (mapQtoSplitItems != null && mapQtoSplitItems.Count > 0)
					{
						List<QtoDetailEntity> assignQtoLines = new List<QtoDetailEntity>();
						if (moduleName == "sales.billing")
							assignQtoLines = qtoLines.Where(i => i.IsBQ && i.BoqItemFk == boqItem.BoqItemPrjItemFk.Value && !i.QtoLineTypeFk.Equals(8) && i.BilHeaderFk.HasValue).ToList();
						else if (moduleName == "sales.wip")
							assignQtoLines = qtoLines.Where(i => i.IsIQ && i.BoqItemFk == boqItem.BoqItemPrjItemFk.Value && !i.QtoLineTypeFk.Equals(8) && i.WipHeaderFk.HasValue).ToList();
						else if (moduleName == "procurement.pes")
							assignQtoLines = qtoLines.Where(i => i.IsIQ && i.BoqItemFk == boqItem.BoqItemPrjItemFk.Value && !i.QtoLineTypeFk.Equals(8) && i.PesHeaderFk.HasValue).ToList();

						var mapQtoLines = boqItem.BoqItemPrjItemFk.HasValue ? assignQtoLines :
							qtoLines.Where(i => i.BoqItemFk == boqItem.Id && (!i.WipHeaderFk.HasValue && !i.PesHeaderFk.HasValue) && !i.QtoLineTypeFk.Equals(8)).ToList();

						var assignedSplitQtoLines = mapQtoLines.Where(i => i.BoqSplitQuantityFk.HasValue).ToList();
						decimal quantitySum = 0.0m;
						decimal quantityAdjSum = 0.0m;
						if (assignedSplitQtoLines.Count > 0)
						{
							#region qto has boq split quantity assignment
							var split2QtoLinesGroup = mapQtoLines.Where(e => e.BoqSplitQuantityFk.HasValue).GroupBy(i => i.BoqSplitQuantityFk.Value).ToList();

							// has no boq split quantity assignment for qto
							if (mapQtoSplitItems.Count > split2QtoLinesGroup.Count)
							{
								if (moduleName == "sales.wip" || moduleName == "procurement.pes" || moduleName == "sales.billing")
								{
									for (int i = split2QtoLinesGroup.Count; i < mapQtoSplitItems.Count; i++)
									{
										quantitySum += mapQtoSplitItems[i].Quantity;
										quantityAdjSum += mapQtoSplitItems[i].QuantityAdj;
									}
								}
								else
								{
									var splitIds = split2QtoLinesGroup.Select(e => e.Key).ToList();
									var noBoqSplitAssignedQtos = mapQtoSplitItems.Where(e => !splitIds.Contains(e.Id)).ToList();
									foreach (var noBoqSplitAssignedQto in noBoqSplitAssignedQtos)
									{
										quantitySum += noBoqSplitAssignedQto.Quantity;
										quantityAdjSum += noBoqSplitAssignedQto.QuantityAdj;
									}
								}
							}

							// has boq split quantity assignment for qto
							for (int i = 0; i < split2QtoLinesGroup.Count; i++)
							{
								bool isIqCheckWithSplit = split2QtoLinesGroup[i].Any(e => e.IsIQ);
								bool isBqCheckWithSplit = split2QtoLinesGroup[i].Any(e => e.IsBQ);
								bool isWqCheckWithSplit = split2QtoLinesGroup[i].Any(e => e.IsWQ);
								bool isAqCheckWithSplit = split2QtoLinesGroup[i].Any(e => e.IsAQ);

								var splitItemQuantity = split2QtoLinesGroup[i].Where(e => e.IsWQ && !e.IsBlocked).Sum(e => e.Result);
								var splitItemAQuantity = split2QtoLinesGroup[i].Where(e => e.IsAQ && !e.IsBlocked).Sum(e => e.Result);

								if (moduleName == "sales.wip" || moduleName == "procurement.pes")
								{
									splitItemQuantity = split2QtoLinesGroup[i].Where(e => e.IsIQ && !e.IsBlocked).Sum(e => e.Result);
									splitItemAQuantity = split2QtoLinesGroup[i].Where(e => e.IsIQ && !e.IsBlocked).Sum(e => e.Result);
								}
								else if (moduleName == "sales.billing")
								{
									splitItemQuantity = split2QtoLinesGroup[i].Where(e => e.IsBQ && !e.IsBlocked).Sum(e => e.Result);
									splitItemAQuantity = split2QtoLinesGroup[i].Where(e => e.IsBQ && !e.IsBlocked).Sum(e => e.Result);
								}

								if (mapQtoSplitItems.Count > i)
								{
									if (moduleName == "sales.wip" || moduleName == "procurement.pes")
									{
										if (isIqCheckWithSplit)
										{
											mapQtoSplitItems[i].Quantity = splitItemQuantity;
											quantitySum += splitItemQuantity;

											mapQtoSplitItems[i].QuantityAdj = splitItemAQuantity;
											quantityAdjSum += splitItemAQuantity;
										}
									}
									else if (moduleName == "sales.billing")
									{
										if (isBqCheckWithSplit)
										{
											mapQtoSplitItems[i].Quantity = splitItemQuantity;
											quantitySum += splitItemQuantity;

											mapQtoSplitItems[i].QuantityAdj = splitItemAQuantity;
											quantityAdjSum += splitItemAQuantity;
										}
									}
									else
									{
										var splitItem = mapQtoSplitItemsDic.ContainsKey(split2QtoLinesGroup[i].Key) ? mapQtoSplitItemsDic[split2QtoLinesGroup[i].Key] : null;
										if (splitItem != null)
										{
											if (isWqCheckWithSplit)
											{
												splitItem.Quantity = splitItemQuantity;
												quantitySum += splitItemQuantity;
											}

											if (isAqCheckWithSplit)
											{
												splitItem.QuantityAdj = splitItemAQuantity;
												quantityAdjSum += splitItemAQuantity;
											}
										}
									}
								}
								else
								{
									splitItemQuantity = split2QtoLinesGroup[i].Sum(e => e.Result);
									if ((isIqCheckWithSplit && (moduleName == "sales.wip" || moduleName == "procurement.pes")) || (isBqCheckWithSplit && moduleName == "sales.billing"))
									{
										var newSplitItem = new BoqSplitQuantityLogic().InitEntity(boqItem.BoqHeaderFk, boqItem.Id);
										newSplitItem.Quantity = splitItemQuantity;
										newSplitItem.QuantityAdj = splitItemQuantity;
										spitItems.Add(newSplitItem);
										quantitySum += splitItemQuantity;
										quantityAdjSum += splitItemQuantity;
									}
								}
							}
							#endregion
						}
						else
						{
							#region qto has no boq split quantity assignment
							// handle split by qto detail
							if (mapQtoSplitItems.Count > 0)
							{
								for (int i = 0; i < mapQtoLines.Count; i++)
								{
									if (mapQtoSplitItems.Count > i)
									{
										if ((isIqCheckWithBoqItem && (moduleName == "sales.wip" || moduleName == "procurement.pes")) || (isBqCheckWithBoqItem && moduleName == "sales.billing"))
										{
											mapQtoSplitItems[i].Quantity = mapQtoLines[i].Result;
											quantitySum += mapQtoSplitItems[i].Quantity;

											mapQtoSplitItems[i].QuantityAdj = mapQtoLines[i].Result;
											quantityAdjSum += mapQtoSplitItems[i].QuantityAdj;
										}
										else
										{
											if (isWqCheckWithBoqItem)
											{
												mapQtoSplitItems[i].Quantity = mapQtoLines[i].IsWQ ? mapQtoLines[i].Result : 0;
												quantitySum += mapQtoSplitItems[i].Quantity;
											}

											if (isAqCheckWithBoqItem)
											{
												mapQtoSplitItems[i].QuantityAdj = mapQtoLines[i].IsAQ ? mapQtoLines[i].Result : 0;
												quantityAdjSum += mapQtoSplitItems[i].QuantityAdj;
											}
										}


									}
									else
									{
										var newSplitItem = new BoqSplitQuantityLogic().Create(boqItem.BoqHeaderFk, boqItem.Id);
										if ((isIqCheckWithBoqItem && (moduleName == "sales.wip" || moduleName == "procurement.pes")) || (isBqCheckWithBoqItem && moduleName == "sales.billing"))
										{
											newSplitItem.Quantity = mapQtoLines[i].Result;
											quantitySum += newSplitItem.Quantity;

											newSplitItem.QuantityAdj = mapQtoLines[i].Result;
											quantityAdjSum += newSplitItem.QuantityAdj;
										}
										else
										{
											if (isWqCheckWithBoqItem)
											{
												newSplitItem.Quantity = mapQtoLines[i].IsWQ ? mapQtoLines[i].Result : 0;
												quantitySum += newSplitItem.Quantity;
											}

											if (isAqCheckWithBoqItem)
											{
												newSplitItem.QuantityAdj = mapQtoLines[i].IsAQ ? mapQtoLines[i].Result : 0;
												quantityAdjSum += newSplitItem.QuantityAdj;
											}
										}

										newSplitItems.Add(newSplitItem);
									}
								}

								// set others as 0
								if (mapQtoSplitItems.Count > mapQtoLines.Count)
								{
									for (int i = mapQtoLines.Count; i < mapQtoSplitItems.Count; i++)
									{
										if ((isIqCheckWithBoqItem && (moduleName == "sales.wip" || moduleName == "procurement.pes")) || (isBqCheckWithBoqItem && moduleName == "sales.billing"))
										{
											mapQtoSplitItems[i].Quantity = 0;
											mapQtoSplitItems[i].QuantityAdj = 0;
										}
										else
										{
											if (isWqCheckWithBoqItem)
											{
												mapQtoSplitItems[i].Quantity = 0;
											}

											if (isAqCheckWithBoqItem)
											{
												mapQtoSplitItems[i].QuantityAdj = 0;
											}
										}
									}
								}

								spitItems.AddRange(newSplitItems);
							}

							#endregion
						}

						mapQtoSplitItems.AddRange(newSplitItems);
						boqItem.Quantity = mapQtoSplitItems.Sum(e => e.Quantity);
						boqItem.QuantityDetail = boqItem.Quantity > 0 ? boqItem.Quantity.ToString() : "";

						boqItem.QuantityAdj = mapQtoSplitItems.Sum(e => e.QuantityAdj);
						boqItem.QuantityAdjDetail = boqItem.QuantityAdj > 0 ? boqItem.QuantityAdj.ToString() : "";
					}
					#endregion
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		public IEnumerable<QtoDetailEntity> GetQtoDetailsByBoqId(int boqHeaderFK, int BoqItemId)
		{
			return GetSearchList(e => e.BoqHeaderFk == boqHeaderFK && e.BoqItemFk == BoqItemId);
		}

		/// <summary>
		///
		/// </summary>
		public IEnumerable<QtoDetailEntity> GetQtoDetailsByBoqIds(int boqHeaderFK, IEnumerable<int> BoqItemIds)
		{
			return GetSearchList(e => e.BoqHeaderFk == boqHeaderFK && BoqItemIds.Contains(e.BoqItemFk));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <returns></returns>
		public int GetQtoHeaderIdByBoq(int boqHeaderId)
		{
			int qtoHeaderId = -1;
			var qtoDetails = GetSearchList(e => e.BoqHeaderFk == boqHeaderId).ToList();
			qtoHeaderId = qtoDetails.Count > 0 ? qtoDetails.FirstOrDefault().QtoHeaderFk : -1;
			return qtoHeaderId;

		}

		/// <summary>
		///
		/// </summary>
		private BoqItemEntity GetBoqItemInfo(QtoDetailEntity item)
		{
			var boqItemLogic = new BoqItemLogic();
			int boqHeaderFk = item.BoqHeaderFk;
			int boqItemFk = item.BoqItemFk;
			if (item.ParentBoqHeaderFk.HasValue && item.ParentBoqItemFk.HasValue)
			{
				boqHeaderFk = item.ParentBoqHeaderFk.Value;
				boqItemFk = item.ParentBoqItemFk.Value;
			}

			var boqItem = boqItemLogic.GetPureBoqItemById(boqHeaderFk, boqItemFk);

			return boqItem;
		}

		/// <summary>
		///
		/// </summary>
		IEnumerable<IIdentifyable> IQtoDetailLogic.SaveQtoDetail(IIdentifyable boqItemComplete)
		{
			var boqItemToUpdate = boqItemComplete as BoqItemComplete;
			var boqItem = boqItemToUpdate.BoqItem;
			var ToSave = boqItemToUpdate.QtoDetailToSave;
			string moduleName = boqItemToUpdate.MainModuleName;
			int headerId = boqItemToUpdate.HeaderId ?? 0;

			if (ToSave != null && ToSave.Any())
			{
				List<QtoDetailEntity> qtoDetailsToSave = new List<QtoDetailEntity>();
				List<QtoDetailEntity> qtoDetailsToDelete = new List<QtoDetailEntity>();
				var costGroupLogic = new MainItem2CostGroupLogic("QTO_DETAIL2COSTGRP");
				var boqItemLogic = new BoqItemLogic();
				var boqSplitQuantityLogic = new BoqSplitQuantityLogic();

				var qtoDetailEntitys = GetDetailsByIds(
					 ToSave.OfType<QtoDetailCompleteEntity>()
					 .Where(qtoDetail => qtoDetail.QtoDetail != null)
					 .Select(qtoDetail => qtoDetail.QtoDetail.Id)).ToList();

				Dictionary<int, List<MainItem2CostGroupEntity>> costGroups2Dic = new Dictionary<int, List<MainItem2CostGroupEntity>>();
				int qtoHeaderId = -1;
				foreach (var item in ToSave)
				{
					var ent = (QtoDetailCompleteEntity)item;
					if (ent.QtoDetail != null)
					{
						if (boqItem == null)
						{
							boqItem = GetBoqItemInfo(ent.QtoDetail);
							boqItemToUpdate.BoqItem = boqItem;
						}
						var qtoDetail = qtoDetailEntitys.Find(e => e.Id == ent.QtoDetail.Id);
						if (qtoDetail != null && ent.QtoDetail.Version <= 0)
						{
							ent.QtoDetail.Version = qtoDetail.Version;
						}
						qtoHeaderId = ent.QtoDetail.QtoHeaderFk;
						ent.QtoDetail.BasUomFk = boqItem.BasUomFk > 0 ? boqItem.BasUomFk : ent.QtoDetail.BasUomFk;

						ent.QtoDetail.QtoDetailReference = ConvertQtoDetailReference(ent.QtoDetail);

						qtoDetailsToSave.Add(ent.QtoDetail);
					}

					if (ent.CostGroupToSave != null)
						costGroups2Dic.Add(ent.MainItemId, ent.CostGroupToSave.ToList());
				}

				/* collect cost groups from boq or boq split */
				var isSourceBoq = moduleName != "sales.wip" && moduleName != "procurement.pes" && moduleName != "sales.billing";
				SetLocationAndCostGroups2QtoLine(qtoDetailsToSave, costGroups2Dic, isSourceBoq, boqItem.BoqHeaderFk, boqItem.Id);

				QtoDetailHelper detailHelper = new QtoDetailHelper(qtoDetailsToSave, qtoDetailsToDelete, false, 6);

				var qtoHeader = new QtoHeaderLogic().GetQtoHeaderById(qtoHeaderId) as QtoHeaderEntity;

				StringBuilder timestr = new StringBuilder();
				var qtoDataBase = detailHelper.SaveWithCheck(qtoHeader.BasGoniometerTypeFk, ref timestr);
				var qtoDetails = qtoDataBase.QtoUpdateDataFromClient;

				var qtoLineTypeList = new QtoLineTypeLogic().GetList();
				var qtoLineTypeListDic = qtoLineTypeList.ToDictionary(e => e.Id, e => e);
				var costGroupToSave = new List<MainItem2CostGroupEntity>();
				// set the boqitemcode
				foreach (var item in ToSave)
				{
					var ent = (QtoDetailCompleteEntity)item;
					if (ent.QtoDetail != null)
					{
						ent.QtoDetail.BoqItemCode = boqItem.Reference;
						var qtoLineType = qtoLineTypeListDic.ContainsKey(ent.QtoDetail.QtoLineTypeFk) ? qtoLineTypeListDic[ent.QtoDetail.QtoLineTypeFk] : null;

						if (qtoLineType != null)
						{
							ent.QtoDetail.QtoLineTypeCode = qtoLineType.CodeInfo.Description;
						}
					}

					if (costGroups2Dic.ContainsKey(ent.MainItemId))
					{
						ent.CostGroupToSave = costGroups2Dic[ent.MainItemId];
						costGroupToSave.AddRange(costGroups2Dic[ent.MainItemId]);
					}
				}

				if (costGroupToSave.Count > 100)
				{
					costGroupLogic.BulkSave(costGroupLogic.GetDbModel(), costGroupToSave);
				}
				else
				{
					costGroupLogic.Save(costGroupToSave);
				}

				var qtoLines = GetQtoDetailsByBoqId(boqItem.BoqHeaderFk, boqItem.Id);

				switch (moduleName)
				{
					case "sales.billing":
						qtoLines = GetQtoDetailsByBoqId(boqItem.BoqItemPrjBoqFk.Value, boqItem.BoqItemPrjItemFk.Value);
						qtoLines = qtoLines.Where(e => e.BilHeaderFk.HasValue && e.BilHeaderFk.Value == headerId).ToList();
						break;
					case "sales.wip":
						qtoLines = GetQtoDetailsByBoqId(boqItem.BoqItemPrjBoqFk.Value, boqItem.BoqItemPrjItemFk.Value);
						qtoLines = qtoLines.Where(e => e.WipHeaderFk.HasValue && e.WipHeaderFk.Value == headerId).ToList();
						break;
					case "procurement.pes":
						qtoLines = GetQtoDetailsByBoqId(boqItem.BoqItemPrjBoqFk.Value, boqItem.BoqItemPrjItemFk.Value);
						qtoLines = qtoLines.Where(e => e.PesHeaderFk.HasValue && e.PesHeaderFk.Value == headerId).ToList();
						break;
					default:
						break;
				}

				qtoLines = qtoLines.Where(e => e.QtoHeaderFk == qtoHeaderId).ToList();

				var spitItems = new BoqSplitQuantityLogic().GetList(boqItem.BoqHeaderFk, boqItem.Id);

				// update boq wq/aq and save split quantity
				UpdateBoqItemQuantities(qtoLines, [boqItem], moduleName, spitItems, true, null, headerId);

				// update line item qty
				if (_estLineItemToSave.Count > 0)
				{
					int moduleTag = moduleName == "sales.wip" ? 1 : (moduleName == "sales.billing" ? 2 : 0);
					Injector.Get<IEstimateMainLineItemQuantityLogic>().OverwriteOrSaveLineItemQuantityFromQto(_estLineItemToSave, headerId, moduleTag);
					_estLineItemToSave.Clear();
				}

				var qtoFormularScriptEntities =
					(new QtoFormulaScriptLogic()).GetListScriptEntities(qtoDetailsToSave.Where(x => x.QtoFormula != null).Select(x => x.QtoFormula.Id).ToList())
						.ToList();
				foreach (var qto in qtoDetailsToSave)
				{
					if (qto.QtoFormula != null)
					{
						qto.QtoFormula.QtoFormulaScriptEntities = qtoFormularScriptEntities.Where(x => x.QtoFormulaFk == qto.QtoFormula.Id).ToList();
					}
				}

				#region multine group qto detail will saved so need return
				if (qtoDetails != null && qtoDetails.Any())
				{
					var qtoDetailCompleteEntities = ToSave.ToList().Select(e => (QtoDetailCompleteEntity)e);
					if (qtoDetailCompleteEntities != null)
					{
						var qto2SavedIds = qtoDetailCompleteEntities.Select(e => e.MainItemId).ToList();

						qtoDetails = qtoDetails.Where(e => !qto2SavedIds.Contains(e.Id)).ToList();
						var toSaveTemp = new List<IIdentifyable>();
						foreach (var item in qtoDetails)
						{
							var qtoDetailCompleteEntity = new QtoDetailCompleteEntity();

							qtoDetailCompleteEntity.MainItemId = item.Id;
							qtoDetailCompleteEntity.QtoDetail = item;
							qtoDetailCompleteEntity.QtoDetail.BoqItemCode = boqItem.Reference;
							qtoDetailCompleteEntity.QtoDetail.BasUomFk = boqItem.BasUomFk;

							qtoDetailCompleteEntity.QtoDetail.QtoDetailReference = ConvertQtoDetailReference(item);

							toSaveTemp.Add(qtoDetailCompleteEntity);
						}

						ToSave = ToSave.Concat(toSaveTemp);
					}
				}
				#endregion
			}

			return ToSave;
		}

		/// <summary>
		///
		/// </summary>
		private BoqItemEntity UpdateBoqItemQuantities(IEnumerable<QtoDetailEntity> qtoLines, List<BoqItemEntity> boqItems, string moduleName, List<BoqSplitQuantityEntity> spitItems, bool isSave = true, List<BoqSplitQuantityEntity> boqSplitQtyList = null, int headerId = 0)
		{
			var boqItemLogic = new BoqItemLogic();
			var boqSplitQuantityLogic = new BoqSplitQuantityLogic();
			var boqItem = boqItems.FirstOrDefault();

			// update boq wq/aq
			UpadateBoqWqAqIqBqByQtoLines(qtoLines, boqItems, moduleName, spitItems, headerId);

			// set the HasQtoQuantities
			boqItem.HasQtoDetails = qtoLines != null && qtoLines.Any();
			if (boqItem.HasQtoDetails)
			{
				var hasWqQtoLines = qtoLines.Where(e => e.IsWQ).ToList();
				var qtoLinesResult = hasWqQtoLines.Sum(e => e.Result);
				boqItem.IsQtoForQuantity = hasWqQtoLines.Count > 0 && boqItem.Quantity == qtoLinesResult;

				var hasAqQtoLines = qtoLines.Where(e => e.IsAQ).ToList();
				qtoLinesResult = hasAqQtoLines.Sum(e => e.Result);
				boqItem.IsQtoForQuantityAdj = hasAqQtoLines.Count > 0 && boqItem.QuantityAdj == qtoLinesResult;
			}

			if (isSave)
			{
				// save boq
				if (boqItems.Count > 100)
				{
					boqItemLogic.BulkSave(boqItemLogic.GetDbModel(), boqItems);
				}
				else
				{
					boqItemLogic.SaveEntities(boqItems);
				}
			}

			// save boq split quantity
			var newSpitItems = spitItems.Where(e => e.Id <= 0).ToList();
			if (newSpitItems.Any())
			{
				var ids = new Stack<int>(SequenceManager.GetNextList<Int32>("BOQ_SPLIT_QUANTITY", newSpitItems.Count));
				foreach (var item in newSpitItems)
				{
					item.Id = ids.Pop();
				}
			}

			if (isSave)
			{
				boqSplitQuantityLogic.Save(spitItems);
			}
			else if (boqSplitQtyList != null)
			{
				boqSplitQtyList.AddRange(spitItems);
			}

			return boqItem;
		}

		/// <summary>
		///
		/// </summary>
		IEnumerable<IIdentifyable> IQtoDetailLogic.DeleteQtoDetail(IIdentifyable boqItemComplete)
		{
			var boqItemToUpdate = boqItemComplete as BoqItemComplete;
			var boqItem = boqItemToUpdate.BoqItem;
			var ToDelete = boqItemToUpdate.QtoDetailToDelete;
			string moduleName = boqItemToUpdate.MainModuleName;
			int headerId = boqItemToUpdate.HeaderId ?? 0;

			if (ToDelete != null && ToDelete.Any())
			{
				// save and calculate qtolines
				List<QtoDetailEntity> qtoDetailsToSave = new List<QtoDetailEntity>();
				var qtoDetailsToDelete = ToDelete.Select(e => e as QtoDetailEntity);
				QtoDetailHelper detailHelper = new QtoDetailHelper(qtoDetailsToSave, qtoDetailsToDelete, false, 6);

				StringBuilder timestr = new StringBuilder();
				var qtoDataBase = detailHelper.SaveWithCheck(0.00m, ref timestr);
				var qtoDetails = qtoDataBase.QtoUpdateDataFromClient;

				var qtoDetail = qtoDetailsToDelete.FirstOrDefault();

				if (boqItem == null)
				{
					boqItem = GetBoqItemInfo(qtoDetail);
					boqItemToUpdate.BoqItem = boqItem;
				}

				int qtoHeaderId = qtoDetail != null ? qtoDetail.QtoHeaderFk : -1;

				var qtoLines = GetQtoDetailsByBoqId(boqItem.BoqHeaderFk, boqItem.Id);
				switch (moduleName)
				{
					case "sales.billing":
						qtoLines = GetQtoDetailsByBoqId(boqItem.BoqItemPrjBoqFk.Value, boqItem.BoqItemPrjItemFk.Value);
						qtoLines = qtoLines.Where(e => e.BilHeaderFk.HasValue && e.BilHeaderFk.Value == headerId).ToList();
						break;
					case "sales.wip":
						qtoLines = GetQtoDetailsByBoqId(boqItem.BoqItemPrjBoqFk.Value, boqItem.BoqItemPrjItemFk.Value);
						qtoLines = qtoLines.Where(e => e.WipHeaderFk.HasValue && e.WipHeaderFk.Value == headerId).ToList();
						break;
					case "procurement.pes":
						qtoLines = GetQtoDetailsByBoqId(boqItem.BoqItemPrjBoqFk.Value, boqItem.BoqItemPrjItemFk.Value);
						qtoLines = qtoLines.Where(e => e.PesHeaderFk.HasValue && e.PesHeaderFk.Value == headerId).ToList();
						break;
					default:
						break;
				}

				qtoLines = qtoLines.Where(e => e.QtoHeaderFk == qtoHeaderId).ToList();

				List<BoqItemEntity> boqItems = new List<BoqItemEntity>();
				boqItems.Add(boqItem as BoqItemEntity);
				var spitItems = new BoqSplitQuantityLogic().GetList(boqItem.BoqHeaderFk, boqItem.Id);
				// update boq wq/aq
				UpadateBoqWqAqIqBqByQtoLines(qtoLines, boqItems, moduleName, spitItems);

				// set the HasQtoQuantities
				boqItem.HasQtoDetails = qtoLines != null && qtoLines.Any();
				if (boqItem.HasQtoDetails)
				{
					var hasWqQtoLines = qtoLines.Where(e => e.IsWQ).ToList();
					var qtoLinesResult = hasWqQtoLines.Sum(e => e.Result);
					boqItem.IsQtoForQuantity = hasWqQtoLines.Count > 0 && boqItem.Quantity == qtoLinesResult;

					var hasAqQtoLines = qtoLines.Where(e => e.IsAQ).ToList();
					qtoLinesResult = hasAqQtoLines.Sum(e => e.Result);
					boqItem.IsQtoForQuantityAdj = hasAqQtoLines.Count > 0 && boqItem.QuantityAdj == qtoLinesResult;
				}

				// save boq
				new BoqItemLogic().SaveEntities(boqItems);

				// save boq split quantity
				new BoqSplitQuantityLogic().Save(spitItems);
			}

			return ToDelete;
		}

		/// <summary>
		/// set the boq has qtodetails or not
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <param name="boqItems"></param>
		/// <param name="filterWipOrBillBoq"></param>
		/// <param name="isPes"></param>
		void IQtoDetailLogic.SetBoqHasQtoDetail(int boqHeaderId, IList<IBoqItemEntity> boqItems, bool filterWipOrBillBoq, bool isPes)
		{
			var qtos = new List<QtoHeaderEntity>();
			var baseBoqItems = new List<BoqItemEntity>();
			if (filterWipOrBillBoq || isPes)
			{
				var _boqItem = boqItems.FirstOrDefault();
				if (_boqItem != null && _boqItem.BoqItemPrjBoqFk.HasValue)
				{
					qtos = new QtoHeaderLogic().GetQtoHeaderByBoqHeaderId(_boqItem.BoqItemPrjBoqFk.Value).ToList();
					baseBoqItems = new BoqItemLogic().GetBoqItemsByHeaderId(_boqItem.BoqItemPrjBoqFk.Value).ToList();
				}

			}
			else
			{
				qtos = new QtoHeaderLogic().GetQtoHeaderByBoqHeaderId(boqHeaderId).ToList();
				if (qtos.Any())
				{
					baseBoqItems = new BoqItemLogic().GetBoqItemsByHeaderId(boqHeaderId).ToList();
				}
			}

			var qto = qtos.Where(e => e.QtoTargetType == (int)QtoTargetType.PrcWqAq || e.QtoTargetType == (int)QtoTargetType.PrjWqAq).FirstOrDefault();
			if (filterWipOrBillBoq)
			{
				qto = qtos.Where(e => e.QtoTargetType == (int)QtoTargetType.WipOrBill).FirstOrDefault();
			}
			else if (isPes)
			{
				qto = qtos.Where(e => e.QtoTargetType == (int)QtoTargetType.PesBoq).FirstOrDefault();
			}

			if (qto != null)
			{
				var qtoDetails = this.GetSearchList(e => e.QtoHeaderFk == qto.Id).ToList();

				decimal qtoLinesResult = 0;

				if (filterWipOrBillBoq)
				{
					#region handle wip boq or billing boq

					var billBoqLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesBillingBoqLogic>("Billing");


					var wipWipLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesWipBoqLogic>("Wip");

					var currentBillBoqEntity = billBoqLogic.GetItemsByBoqHeaderId(boqHeaderId).FirstOrDefault();
					var currentWipBoqEntity = wipWipLogic.GetItemsByWipBoqIds(new List<int>() { boqHeaderId }).FirstOrDefault();

					foreach (var boqItem in baseBoqItems)
					{
						if (!boqItem.IsDivisionOrRootWithIT)
						{
							var _tempBoqItem = boqItems.Where(e => e.BoqItemPrjBoqFk == boqItem.BoqHeaderFk && e.BoqItemPrjItemFk == boqItem.Id).FirstOrDefault();
							if (_tempBoqItem != null)
							{
								var boq2QtoDetails = qtoDetails.Where(e => e.BoqHeaderFk == boqItem.BoqHeaderFk && e.BoqItemFk == boqItem.Id).ToList();
								boqItem.HasQtoDetails = boq2QtoDetails.Count > 0;

								_tempBoqItem.HasQtoDetails = boqItem.HasQtoDetails; // Also set this flag on version boq item to inform that there are qto details assigned on base boq level.

								if (boqItem.HasQtoDetails)
								{
									//wip
									var hasIqQtoLines = boq2QtoDetails.Where(e => e.IsIQ).ToList();

									boqItem.HasQtoDetailsAndIsIQ = hasIqQtoLines.Count > 0;
									_tempBoqItem.HasQtoDetailsAndIsIQ = boqItem.HasQtoDetailsAndIsIQ;

									if (currentWipBoqEntity != null)
									{
										hasIqQtoLines = hasIqQtoLines.Where(e => e.WipHeaderFk == currentWipBoqEntity.WipHeaderFk).ToList();
									}

									qtoLinesResult = hasIqQtoLines.Sum(e => e.Result);
									_tempBoqItem.IsQtoForQuantity = hasIqQtoLines.Count > 0 && _tempBoqItem.Quantity == qtoLinesResult;

									if (hasIqQtoLines.Count(e => !e.IsReadonly) == hasIqQtoLines.Count)
									{
										_tempBoqItem.IsQtoForQuantity = false;
									}


									//billing
									var hasBqQtoLines = boq2QtoDetails.Where(e => e.IsBQ).ToList();
									if (currentBillBoqEntity != null)
									{
										hasBqQtoLines = hasBqQtoLines.Where(e => e.BilHeaderFk == currentBillBoqEntity.BilHeaderFk).ToList();
									}

									qtoLinesResult = hasBqQtoLines.Sum(e => e.Result);
									_tempBoqItem.IsQtoForBillBoQQuantity = hasBqQtoLines.Count > 0 && _tempBoqItem.Quantity == qtoLinesResult;

									if (hasBqQtoLines.Count(e => !e.IsReadonly) == hasBqQtoLines.Count)
									{
										_tempBoqItem.IsQtoForBillBoQQuantity = false;
									}
								}
							}
						}
					}

					#endregion
				}
				else if (isPes)
				{
					var pesBoqLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPesBoqLogic>();
					var currentPesBoqEntity = pesBoqLogic.GetByBoqHeaders(new List<int>() { boqHeaderId }).FirstOrDefault();

					#region handle isPes
					foreach (var boqItem in baseBoqItems)
					{
						if (!boqItem.IsDivisionOrRootWithIT)
						{

							var _tempBoqItem = boqItems.Where(e => e.BoqItemPrjBoqFk == boqItem.BoqHeaderFk && e.BoqItemPrjItemFk == boqItem.Id).FirstOrDefault();
							if (_tempBoqItem != null)
							{
								var boq2QtoDetails = qtoDetails.Where(e => e.BoqHeaderFk == boqItem.BoqHeaderFk && e.BoqItemFk == boqItem.Id).ToList();
								boqItem.HasQtoDetails = boq2QtoDetails.Count > 0;

								if (boqItem.HasQtoDetails)
								{
									var hasIqQtoLines = boq2QtoDetails.Where(e => e.IsIQ).ToList();
									if (currentPesBoqEntity != null)
									{
										hasIqQtoLines = hasIqQtoLines.Where(e => e.PesHeaderFk == currentPesBoqEntity.PesHeaderFk).ToList();
									}

									qtoLinesResult = hasIqQtoLines.Sum(e => e.Result);
									_tempBoqItem.IsQtoForQuantity = hasIqQtoLines.Count > 0 && _tempBoqItem.Quantity == qtoLinesResult;

									qtoLinesResult = hasIqQtoLines.Sum(e => e.Result);
									_tempBoqItem.IsQtoForQuantityAdj = hasIqQtoLines.Count > 0 && _tempBoqItem.QuantityAdj == qtoLinesResult;

									if (hasIqQtoLines.Count(e => !e.IsReadonly) == hasIqQtoLines.Count)
									{
										_tempBoqItem.IsQtoForBillBoQQuantity = false;
										_tempBoqItem.IsQtoForQuantity = false;
									}
								}
							}
						}
					}
					#endregion
				}
				else
				{

					#region handle project boq or package boq
					foreach (var boqItem in baseBoqItems)
					{
						if (!boqItem.IsDivisionOrRootWithIT)
						{

							var _tempBoqItem = boqItems.Where(e => e.BoqHeaderFk == boqItem.BoqHeaderFk && e.Id == boqItem.Id).FirstOrDefault();
							if (_tempBoqItem != null)
							{
								var boq2QtoDetails = qtoDetails.Where(e => e.BoqHeaderFk == boqItem.BoqHeaderFk && e.BoqItemFk == boqItem.Id).ToList();
								boqItem.HasQtoDetails = boq2QtoDetails.Count > 0;

								if (boqItem.HasQtoDetails)
								{
									var hasWqQtoLines = boq2QtoDetails.Where(e => e.IsWQ).ToList();
									qtoLinesResult = hasWqQtoLines.Sum(e => e.Result);
									_tempBoqItem.IsQtoForQuantity = hasWqQtoLines.Count > 0 && _tempBoqItem.Quantity == qtoLinesResult;

									var hasAqQtoLines = boq2QtoDetails.Where(e => e.IsAQ).ToList();
									qtoLinesResult = hasAqQtoLines.Sum(e => e.Result);
									_tempBoqItem.IsQtoForQuantityAdj = hasAqQtoLines.Count > 0 && _tempBoqItem.QuantityAdj == qtoLinesResult;
								}
							}
						}
					}
					#endregion
				}
			}

		}

		/// <summary>
		/// set additional quantities and recalculate boq in qto boq
		/// </summary>
		void IQtoDetailLogic.CalculateWipRelatedPropertiesInQtoBoq(RVPBizComp.DbContext dbcontext, IList<IBoqItemEntity> entities, int headerId, int qtoHeaderFk)
		{
			bool isWip = false, isBill = false, isPes = false;

			var qtoHeader = new QtoHeaderLogic().GetItemByKey(qtoHeaderFk);
			if (qtoHeader == null)
			{
				return;
			}

			if (qtoHeader.QtoTargetType == (int)QtoTargetType.WipOrBill || qtoHeader.QtoTargetType == (int)QtoTargetType.PrjWqAq)
			{
				isWip = true;
				isBill = true;
				isPes = false;
			}
			else if (qtoHeader.QtoTargetType == (int)QtoTargetType.PesBoq || qtoHeader.QtoTargetType == (int)QtoTargetType.PrcWqAq)
			{
				isPes = true;
				isBill = false;
				isWip = false;
			}

			bool isForWqAq = qtoHeader.QtoTargetType == (int)QtoTargetType.PrcWqAq || qtoHeader.QtoTargetType == (int)QtoTargetType.PrjWqAq;
			bool isForIqBq = qtoHeader.QtoTargetType == (int)QtoTargetType.PesBoq || qtoHeader.QtoTargetType == (int)QtoTargetType.WipOrBill;

			var conHeaderFk = isPes ? qtoHeader.ConHeaderFk ?? 0 : 0;
			var ordHeaderFk = isWip ? qtoHeader.OrdHeaderFk ?? 0 : 0;
			if (ordHeaderFk > 0)
			{
				var salseContractLogic = Injector.Get<ISalesContractLogic>("Contract");
				var ordHeader = salseContractLogic.GetContractById(ordHeaderFk, false);
				if (ordHeader.OrdHeaderFk.HasValue)
				{
					ordHeaderFk = ordHeader.OrdHeaderFk.Value;
				}
			}

			if (entities.Any())
			{
				var qtoDetailLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IQtoDetailLogic>();
				var qtoDetails = qtoDetailLogic.GetQtoDetailsByQtoHeader(qtoHeaderFk);
				var qtoDetailGroup = qtoDetails.GroupBy(e => e.BoqItemFk).ToList();
				var prjId = qtoHeader.ProjectFk;

				#region current quantity
				// set current quantity
				Dictionary<int, decimal> boqItem2ResultIQ = new Dictionary<int, decimal>();
				Dictionary<int, decimal> boqItem2ResultBQ = new Dictionary<int, decimal>();

				// quantiy with lineitem assigment
				Dictionary<(int, int), (decimal, decimal)> boqItem2ResultQtyWithLineItem = new Dictionary<(int, int), (decimal, decimal)>();
				Dictionary<int, bool> isLineItemAssignedDic = new Dictionary<int, bool>();
				bool isLineItemAssigned = false;
				Dictionary<int, List<IEstLineItemEntity>> boqItem2LineItems = new Dictionary<int, List<IEstLineItemEntity>>();

				Dictionary<int, decimal> boqItem2ResultAQ = new Dictionary<int, decimal>();
				Dictionary<int, decimal> boqItem2ResultWQ = new Dictionary<int, decimal>();
				// collect qto lines group
				foreach (var detailItems in qtoDetailGroup)
				{
					decimal result = 0.00m;
					if (isForWqAq)
					{
						result = detailItems.Where(i => i.IsAQ && (!i.WipHeaderFk.HasValue && !i.BilHeaderFk.HasValue && !i.PesHeaderFk.HasValue) && !i.QtoLineTypeFk.Equals(8)).Sum(e => e.Result);
						if (!boqItem2ResultAQ.ContainsKey(detailItems.Key))
						{
							boqItem2ResultAQ.Add(detailItems.Key, result);
						}

						result = detailItems.Where(i => i.IsWQ && (!i.WipHeaderFk.HasValue && !i.BilHeaderFk.HasValue && !i.PesHeaderFk.HasValue) && !i.QtoLineTypeFk.Equals(8)).Sum(e => e.Result);
						if (!boqItem2ResultWQ.ContainsKey(detailItems.Key))
						{
							boqItem2ResultWQ.Add(detailItems.Key, result);
						}
					}
					else
					{
						result = detailItems.Where(i => i.IsBQ && (!i.BilHeaderFk.HasValue && !i.PesHeaderFk.HasValue) && !i.QtoLineTypeFk.Equals(8)).Sum(e => e.Result);
						if (!boqItem2ResultBQ.ContainsKey(detailItems.Key))
						{
							boqItem2ResultBQ.Add(detailItems.Key, result);
						}

						var qtoLine = detailItems.FirstOrDefault(e => e.EstLineItemFk.HasValue);
						if (qtoLine != null)
						{
							isLineItemAssignedDic.TryAdd(detailItems.Key, true);
							isLineItemAssigned = true;
						}

						result = detailItems.Where(i => i.IsIQ && (!i.WipHeaderFk.HasValue && !i.PesHeaderFk.HasValue) && !i.QtoLineTypeFk.Equals(8)).Sum(e => e.Result);
						if (!boqItem2ResultIQ.ContainsKey(detailItems.Key))
						{
							boqItem2ResultIQ.Add(detailItems.Key, result);
						}
					}
				}

				if (isForIqBq && isLineItemAssigned)
				{
					SetSumResultWithLineItem(qtoDetails.Select(e => e as QtoDetailEntity), boqItem2ResultQtyWithLineItem);
					var lineItemLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();
					var boqItemFks = qtoDetails.Select(e => e.BoqItemFk).Distinct().ToList();
					var boqHeaderFks = qtoDetails.Select(e => e.BoqHeaderFk).Distinct().ToList();
					var lineItems = lineItemLogic.ByBoqs(boqItemFks, boqHeaderFks);
					boqItem2LineItems = lineItems.Where(e => e.BoqItemFk.HasValue).GroupBy(e => e.BoqItemFk.Value).ToDictionary(e => e.Key, e => e.ToList());
				}

				foreach (var entity in entities)
				{
					if (entity != null && (entity.IsItem || entity.BoqLineTypeFk == 11))
					{
						if (entity.IsItem && entity.BoQItems != null && entity.BoQItems.Any() && entity.BoQItems.Any(e => e is CrbBoqItemEntity))
						{
							var crbEntities = entity.BoQItems.Where(e => e.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity).Cast<CrbBoqItemEntity>();

							var crbSubQuantitiyIds = crbEntities.Where(e => e.IsSubQuantityUsedForTheCalculation()).Select(e => e.Id);

							if (isForWqAq)
							{
								if (boqItem2ResultWQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).Any())
								{
									entity.ExQtoQuantity = boqItem2ResultWQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).Sum(e => e.Value);
								}
								else
								{
									entity.ExQtoQuantity = 0;
								}

								if (boqItem2ResultAQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).Any())
								{
									entity.ExQtoQuantityAdj = boqItem2ResultAQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).Sum(e => e.Value);
								}
								else
								{
									entity.ExQtoQuantityAdj = 0;
								}
							}
							else
							{
								entity.ExQtoQuantity = entity.Quantity;
								entity.ExQtoQuantityAdj = entity.QuantityAdj;

								if (boqItem2ResultBQ.Any(e => crbSubQuantitiyIds.Contains(e.Key)))
								{
									entity.BilledQuantity = boqItem2ResultBQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).Sum(e => e.Value);
								}
								else
								{
									entity.BilledQuantity = 0;
								}

								if (boqItem2ResultIQ.Any(e => crbSubQuantitiyIds.Contains(e.Key)))
								{
									entity.InstalledQuantity = boqItem2ResultIQ.Where(e => crbSubQuantitiyIds.Contains(e.Key)).Sum(e => e.Value);
								}
								else
								{
									entity.InstalledQuantity = 0;
								}

							}
						}
						else
						{
							if (isForWqAq)
							{
								if (boqItem2ResultWQ.ContainsKey(entity.Id))
								{
									entity.ExQtoQuantity = boqItem2ResultWQ[entity.Id];
								}
								else
								{
									entity.ExQtoQuantity = 0;
								}

								if (boqItem2ResultAQ.ContainsKey(entity.Id))
								{
									entity.ExQtoQuantityAdj = boqItem2ResultAQ[entity.Id];
								}
								else
								{
									entity.ExQtoQuantityAdj = 0;
								}
							}
							else
							{
								entity.ExQtoQuantity = entity.Quantity;
								entity.ExQtoQuantityAdj = entity.QuantityAdj;

								if (boqItem2ResultBQ.ContainsKey(entity.Id))
								{
									entity.BilledQuantity = boqItem2ResultBQ[entity.Id];
								}
								else
								{
									entity.BilledQuantity = 0;
								}

								if (boqItem2ResultIQ.ContainsKey(entity.Id))
								{
									entity.InstalledQuantity = boqItem2ResultIQ[entity.Id];
								}
								else
								{
									entity.InstalledQuantity = 0;
								}

							}
						}
					}
				}

				#endregion

				if (isForIqBq)
				{
					#region contract quantity
					// set contract quantity
					if (isWip || isBill)
					{
						if (ordHeaderFk != 0)
						{
							#region assign ord to qto
							var salesContractLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesContractLogic>("Contract");
							var relatedOrds = salesContractLogic.GetRelatedContracts(ordHeaderFk);
							//here only Contracts that are "Ordered" of the Project should be shown for normal user & portal-user
							BasicsCustomizeOrderStatusLogic conStatusLogic = new BasicsCustomizeOrderStatusLogic();
							var orderStatusEntities = conStatusLogic.GetByFilter(e => e.IsOrdered).ToList();

							var statusIds = orderStatusEntities.Select(e => e.Id).ToList();
							relatedOrds = relatedOrds.Where(e => statusIds.Contains(e.OrdStatusFk)).ToList();

							var relatedOrdIds = relatedOrds.Select(e => e.Id);
							var ordBoqLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesContractBoqLogic>();
							var ordBoqList = ordBoqLogic.GetBoQListByOrdHeaderFks(relatedOrdIds);
							if (ordBoqList != null && ordBoqList.Any())
							{
								var boqItemHeaderIds = ordBoqList.Where(e => e.BoqHeaderFk.HasValue).Select(i => i.BoqHeaderFk.Value).ToList();
								if (boqItemHeaderIds.Count > 0)
								{
									var boqItems = new BoqItemLogic().GetBoqItemsByHeaderIds(boqItemHeaderIds);
									if (boqItems != null && boqItems.Any())
									{
										foreach (var entity in entities)
										{
											if (entity != null && (entity.IsItem || entity.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity))
											{
												var ordQuantity = boqItems.Where(e => e.BoqItemPrjBoqFk == entity.BoqHeaderFk && e.BoqItemPrjItemFk == entity.Id).Sum(o => o.Quantity);

												entity.OrdQuantity = ordQuantity;

												// set current quantity by line item assignment
												if (isLineItemAssignedDic.TryGetValue(entity.Id, out var value) && value)
												{
													var lineItems = boqItem2LineItems.TryGetValue(entity.Id, out var items) ? items : [];
													GetCurrentQuantityWithLineItemQty(entity, lineItems, boqItem2ResultQtyWithLineItem);
												}
											}
										}
									}
								}
							}
							#endregion
						}
						else
						{
							#region no assign ord to qto
							var ordBoqItems = dbcontext.Entities<OrdBoqItemsVEntity>().Where(e => e.Id > 0 && e.BoqHeaderFk == headerId).ToList();
							if (ordBoqItems.Any())
							{
								foreach (var entity in entities)
								{
									if (entity != null && (entity.IsItem || entity.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity))
									{
										var ordBoqItem = ordBoqItems.FirstOrDefault(e => e.Id == entity.Id);

										entity.OrdQuantity = ordBoqItem != null ? ordBoqItem.OrdQuantity : 0;

										// set current quantity by line item assignment
										if (isLineItemAssignedDic.TryGetValue(entity.Id, out var value) && value)
										{
											var lineItems = boqItem2LineItems.TryGetValue(entity.Id, out var items) ? items : [];
											GetCurrentQuantityWithLineItemQty(entity, lineItems, boqItem2ResultQtyWithLineItem);
										}
									}
								}
							}
							#endregion
						}
					}
					else if (isPes)
					{
						if (conHeaderFk != 0)
						{
							#region assign ord to qto
							var conHeaderInfoProvider = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IContractHeaderInfoProvider>();
							var prcContract = conHeaderInfoProvider.GetRelatedContracts((Int32)conHeaderFk);


							//here only Contracts that are "Ordered" of the Project should be shown for normal user & portal-user
							BasicsCustomizeConStatusLogic conStatusLogic = new BasicsCustomizeConStatusLogic();
							var orderStatusEntities = conStatusLogic.GetByFilter(e => e.Isordered).ToList();
							if (orderStatusEntities.Any())
							{
								var statusIds = orderStatusEntities.Select(e => e.Id).ToList();
								prcContract = prcContract.Where(e => statusIds.Contains(e.ConStatusFk)).ToList();
							}

							var prcHeaderFks = prcContract.Select(e => e.PrcHeaderFk).ToList();
							var prcPakcageFks = prcContract.Where(e => e.PackageFk.HasValue).Select(e => e.PackageFk.Value).ToList();

							var prcBoqLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcBoqLogic>();
							var prcBoqList = prcBoqLogic.GetPrcBoqItemsByPrcHeaderIds(prcHeaderFks);

							prcBoqList = prcBoqList.Where(e => prcPakcageFks.Contains(e.PackageFk)).ToList();

							if (prcBoqList.Any())
							{
								var boqItemHeaderIds = prcBoqList.Select(i => i.BoqHeaderFk).ToList();
								if (boqItemHeaderIds.Count > 0)
								{
									var boqItems = new BoqItemLogic().GetBoqItemsByHeaderIds(boqItemHeaderIds);
									if (boqItems != null && boqItems.Any())
									{
										foreach (var entity in entities)
										{
											if (entity != null && (entity.IsItem || entity.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity))
											{
												var ordQuantity = boqItems.Where(e => e.BoqItemPrjBoqFk == entity.BoqHeaderFk && e.BoqItemPrjItemFk == entity.Id).Sum(o => o.Quantity);
												entity.OrdQuantity = ordQuantity;
											}
										}
									}
								}
							}
							#endregion

						}
						else
						{
							var conBoqItems = dbcontext.Entities<ConBoqItemQuantVEntity>().Where(e => e.Id > 0 && e.BoqHeaderFk == headerId).ToList();
							if (conBoqItems.Any())
							{
								foreach (var entity in entities)
								{
									if (entity != null && (entity.IsItem || entity.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity))
									{
										var conBoqItem = conBoqItems.FirstOrDefault(e => e.Id == entity.Id);

										entity.OrdQuantity = conBoqItem != null ? conBoqItem.OrdQuantity : 0;
									}
								}
							}

						}
					}
					#endregion

					#region wip or pes pervious quantity
					// set wip or pes pervious quantity

					List<ISalesBillingHeaderEntity> billHeaders = new List<ISalesBillingHeaderEntity>();
					List<int> billHeaderIds = new List<int>();

					List<ISalesWipHeaderEntity> wipHeaders = new List<ISalesWipHeaderEntity>();
					List<int> wipHeaderIds = new List<int>();
					List<IPesHeaderEntity> pesHeaders = new List<IPesHeaderEntity>();
					List<int> pesHeaderIds = new List<int>();


					List<BilBoqItemsVEntity> billBoqs = new List<BilBoqItemsVEntity>();
					List<BilBoqItemsPCQtyVEntity> billQuantities = new List<BilBoqItemsPCQtyVEntity>();

					List<WipBoqItemsVEntity> wipBoqs = new List<WipBoqItemsVEntity>();
					List<WipBoqItemsPCQtyOldVEntity> wipQuantities = new List<WipBoqItemsPCQtyOldVEntity>();

					List<PesBoqItemsVEntity> pesBoqs = new List<PesBoqItemsVEntity>();
					List<IBoqItemQuantityExtensionEntity> pesQuantities = new List<IBoqItemQuantityExtensionEntity>();

					if (isWip || isBill)
					{
						if (isWip)
						{
							// get the project boq
							var wipHeaderLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesWipHeaderLogic>();
							wipHeaders = wipHeaderLogic.GetWipHeaders(prjId, headerId).ToList();
							wipHeaderIds = wipHeaders.Select(e => e.Id).Distinct().ToList();
							if (wipHeaderIds.Count > 0)
							{
								wipBoqs = dbcontext.Entities<WipBoqItemsVEntity>().Where(e => wipHeaderIds.Contains(e.WipHeaderFk)).ToList();
								if (ordHeaderFk > 0)
								{
									wipBoqs = dbcontext.Entities<WipBoqItemsVEntity>().Where(e => e.OrdHeaderFk == ordHeaderFk && wipHeaderIds.Contains(e.WipHeaderFk)).ToList();
								}

								if (wipBoqs.Count > 0)
								{
									var _wipQuantities = dbcontext.Entities<WipBoqItemsPCQtyOldVEntity>().Where(e => e.ExtraPrevious.HasValue && e.BoqItemPrjBoqFk.HasValue && headerId == e.BoqItemPrjBoqFk).ToList();
									if (_wipQuantities != null && _wipQuantities.Any())
									{
										var wibBoqHeaders = wipBoqs.Select(e => e.BoqHeaderFk).Distinct().ToList();
										var wipBoqItems = wipBoqs.Select(e => e.Id).Distinct().ToList();

										wipQuantities = _wipQuantities.Where(e => wibBoqHeaders.Contains(e.BoqHeaderFk) && wipBoqItems.Contains(e.Id)).ToList();
									}
								}
							}
						}

						if (isBill)
						{
							// get the project boq
							var salesBillingHeaderLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesBillingLogic>("bill");
							billHeaders = salesBillingHeaderLogic.GetBillingHeadersByBoq(prjId, headerId).ToList();
							billHeaderIds = billHeaders.Select(e => e.Id).ToList();
							if (billHeaderIds.Count > 0)
							{
								billBoqs = dbcontext.Entities<BilBoqItemsVEntity>().Where(e => billHeaderIds.Contains(e.BilHeaderFk)).ToList();
								if (ordHeaderFk > 0)
								{
									billBoqs = dbcontext.Entities<BilBoqItemsVEntity>().Where(e => e.OrdHeaderFk == ordHeaderFk && billHeaderIds.Contains(e.BilHeaderFk)).ToList();
								}

								if (billBoqs.Count > 0)
								{
									// The origin way might crash : dbcontext.Entities<BilBoqItemsPCQtyVEntity>().Where(e => e.ExtraPrevious.HasValue && e.BoqItemPrjBoqFk.HasValue && headerId == e.BoqItemPrjBoqFk).ToList();
									var _billQuantities = dbcontext.Entities<BilBoqItemsPCQtyVEntity>().Where(e => e.ExtraPrevious.HasValue && e.BoqItemPrjBoqFk.HasValue && headerId == e.BoqItemPrjBoqFk).ToList();
									if (_billQuantities != null && _billQuantities.Any())
									{
										var billBoqHeaders = billBoqs.Select(e => e.BoqHeaderFk).Distinct().ToList();
										var billBoqItems = billBoqs.Select(e => e.Id).Distinct().ToList();

										billQuantities = _billQuantities.Where(e => billBoqHeaders.Contains(e.BoqHeaderFk) && billBoqItems.Contains(e.Id)).ToList();
									}
								}
							}
						}
					}
					else if (isPes)
					{
						var pesHeaderLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPesHeaderProvider>();
						IPrcBoqLogic prcBoqLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcBoqLogic>();
						var prcBoq = prcBoqLogic.GetPrcBoqItemByBoHeader(headerId);
						if (prcBoq != null)
						{
							pesHeaders = pesHeaderLogic.GetPesHeaders(prjId).ToList();
							if (conHeaderFk != 0)
								pesHeaders = pesHeaders.Where(e => e.PackageFk == prcBoq.PackageFk && e.ConHeaderFk == conHeaderFk).ToList(); // filter by package and contract
							else
								pesHeaders = pesHeaders.Where(e => e.PackageFk == prcBoq.PackageFk).ToList(); // filter by package

							if (pesHeaders != null)
							{
								pesHeaderIds = pesHeaders.Select(e => e.Id).ToList();
								pesBoqs = dbcontext.Entities<PesBoqItemsVEntity>().Where(e => pesHeaderIds.Contains(e.PesHeaderFk)).ToList();
								if (conHeaderFk > 0)
								{
									pesBoqs = dbcontext.Entities<PesBoqItemsVEntity>().Where(e => pesHeaderIds.Contains(e.PesHeaderFk) && e.ConHeaderFk == conHeaderFk).ToList();
								}

								if (pesBoqs.Count > 0)
								{
									var pesBoqHeaders = pesBoqs.Select(e => e.BoqHeaderFk).Distinct().ToList();
									var pesBoqItems = pesBoqs.Select(e => e.Id).ToList();

									pesQuantities = new PesBoqItemQuantityExtensionLogic().GetPesBoqItemQuantityExtensions(headerId).Where(pb => pesBoqHeaders.Contains(pb.BoqHeaderFk)).ToList();
								}
							}
						}
					}

					foreach (var entity in entities)
					{
						if (entity != null && (entity.IsItem || entity.BoqLineTypeFk == 11))
						{
							decimal perviousQuantity = 0m;
							decimal extraPrevious = 0m;

							if (entity.IsItem && entity.BoQItems != null && entity.BoQItems.Any() && entity.BoQItems.Any(e => e is CrbBoqItemEntity))
							{
								var crbEntities = entity.BoQItems.Cast<CrbBoqItemEntity>();

								var crbSubQuantitiyIds = crbEntities.Where(e => e.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity && e.IsSubQuantityUsedForTheCalculation()).Select(e => e.Id);

								if (isWip || isBill)
								{
									if (isWip)
									{
										var wip2BoqItems = wipBoqs.Where(e => e.BoqItemPrjBoqFk == entity.BoqHeaderFk && e.BoqItemPrjItemFk.HasValue && crbSubQuantitiyIds.Contains(e.BoqItemPrjItemFk.Value)).ToList();

										calculateIsWipItem(entity, wip2BoqItems, wipQuantities, out perviousQuantity, out extraPrevious);
									}

									if (isBill)
									{
										var bill2BoqItems = billBoqs.Where(e => e.BoqItemPrjBoqFk == entity.BoqHeaderFk && e.BoqItemPrjItemFk.HasValue && crbSubQuantitiyIds.Contains(e.BoqItemPrjItemFk.Value)).ToList();

										calculateIsBillItem(entity, bill2BoqItems, billQuantities, out perviousQuantity, out extraPrevious);
									}
								}
								else if (isPes)
								{
									var pes2BoqItems = pesBoqs.Where(e => e.BoqItemPrjBoqFk == entity.BoqHeaderFk && e.BoqItemPrjItemFk.HasValue && crbSubQuantitiyIds.Contains(e.BoqItemPrjItemFk.Value)).ToList();

									calculateIsPesItem(entity, pes2BoqItems, pesQuantities, out perviousQuantity, out extraPrevious);
								}
							}
							else
							{
								if (isWip || isBill)
								{
									if (isWip)
									{
										var wip2BoqItems = wipBoqs.Where(e => e.BoqItemPrjBoqFk == entity.BoqHeaderFk && e.BoqItemPrjItemFk == entity.Id).ToList();

										calculateIsWipItem(entity, wip2BoqItems, wipQuantities, out perviousQuantity, out extraPrevious);
									}

									if (isBill)
									{
										var bill2BoqItems = billBoqs.Where(e => e.BoqItemPrjBoqFk == entity.BoqHeaderFk && e.BoqItemPrjItemFk == entity.Id).ToList();

										calculateIsBillItem(entity, bill2BoqItems, billQuantities, out perviousQuantity, out extraPrevious);
									}
								}
								else if (isPes)
								{
									var pes2BoqItems = pesBoqs.Where(e => e.BoqItemPrjBoqFk == entity.BoqHeaderFk && e.BoqItemPrjItemFk == entity.Id).ToList();

									calculateIsPesItem(entity, pes2BoqItems, pesQuantities, out perviousQuantity, out extraPrevious);
								}
							}


							entity.PrevQuantity = perviousQuantity;
							entity.ExtraPrevious = extraPrevious;
						}
					}
					#endregion
				}
			}
		}

		/// <summary/>
		public void SetSumResultWithLineItem(IEnumerable<QtoDetailEntity> qtoDetails, Dictionary<(int, int), (decimal, decimal)> boqItem2ResultQtyWithLineItem, bool isUpdate = false, bool isIQ = false)
		{
			var qtoLinesGroupbyLineItem = qtoDetails.Where(e => e.EstHeaderFk.HasValue && e.EstLineItemFk.HasValue).GroupBy(e => new { e.EstHeaderFk, e.EstLineItemFk });
			foreach (var qtoLinesGroup in qtoLinesGroupbyLineItem)
			{
				var estHeaderFk = qtoLinesGroup.Key.EstHeaderFk.Value;
				var estLineItemFk = qtoLinesGroup.Key.EstLineItemFk.Value;
				if (!boqItem2ResultQtyWithLineItem.ContainsKey((estHeaderFk, estLineItemFk)))
				{
					if (!isUpdate)
					{
						boqItem2ResultQtyWithLineItem.Add((estHeaderFk, estLineItemFk),
						(qtoLinesGroup.Where(i => i.IsIQ && (!i.WipHeaderFk.HasValue && !i.PesHeaderFk.HasValue) && !i.IsBlocked && !i.QtoLineTypeFk.Equals(8)).Sum(e => e.Result),
						qtoLinesGroup.Where(i => i.IsBQ && (!i.BilHeaderFk.HasValue && !i.PesHeaderFk.HasValue) && !i.IsBlocked && !i.QtoLineTypeFk.Equals(8)).Sum(e => e.Result)));
					}
					else
					{
						boqItem2ResultQtyWithLineItem.Add((estHeaderFk, estLineItemFk),
						(isIQ ? qtoLinesGroup.Where(i => !i.IsBlocked && !i.QtoLineTypeFk.Equals(8)).Sum(e => e.Result) : 0,
						!isIQ ? qtoLinesGroup.Where(i => !i.IsBlocked && !i.QtoLineTypeFk.Equals(8)).Sum(e => e.Result) : 0));
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="boqItem"></param>
		/// <param name="lineItems"></param>
		/// <param name="boqItem2ResultQtyWithLineItem"></param>
		/// <param name="moduleTag">0 qto, 1 wip, 2 bill</param>
		public void GetCurrentQuantityWithLineItemQty(IBoqItemEntity boqItem, List<IEstLineItemEntity> lineItems, Dictionary<(int, int), (decimal, decimal)> boqItem2ResultQtyWithLineItem, int moduleTag = 0)
		{
			if (lineItems.Count == 0)
			{
				boqItem.InstalledQuantity = 0;
				boqItem.BilledQuantity = 0;
				return;
			}

			decimal ordQuantity = boqItem.OrdQuantity;
			decimal costTotalSum = lineItems.Sum(lineItem => lineItem.CostTotal);


			if (lineItems.Count == 1)
			{
				var estHeaderFk = lineItems[0].EstHeaderFk;
				var estLineItemFk = lineItems[0].Id;
				if (moduleTag == 0)
				{
					var iqQuantity = boqItem2ResultQtyWithLineItem.ContainsKey((estHeaderFk, estLineItemFk)) ? boqItem2ResultQtyWithLineItem[(estHeaderFk, estLineItemFk)].Item1 : 0;
					var bqQuantity = boqItem2ResultQtyWithLineItem.ContainsKey((estHeaderFk, estLineItemFk)) ? boqItem2ResultQtyWithLineItem[(estHeaderFk, estLineItemFk)].Item2 : 0;
					boqItem.InstalledQuantity = lineItems[0].QuantityTotal != 0 ? ordQuantity * iqQuantity / lineItems[0].QuantityTotal : 0;
					boqItem.BilledQuantity = lineItems[0].QuantityTotal != 0 ? ordQuantity * bqQuantity / lineItems[0].QuantityTotal : 0;
				}
				else
				{
					var quantity = boqItem2ResultQtyWithLineItem.TryGetValue((estHeaderFk, estLineItemFk), out var resultQty) ? (moduleTag == 1 ? resultQty.Item1 : resultQty.Item2) : 0;
					boqItem.Quantity = lineItems[0].QuantityTotal != 0 ? ordQuantity * quantity / lineItems[0].QuantityTotal : 0;
					boqItem.QuantityDetail = boqItem.Quantity > 0 ? boqItem.Quantity.ToString() : "";
					boqItem.QuantityAdj = boqItem.Quantity;
					boqItem.QuantityAdjDetail = boqItem.QuantityDetail;
				}
			}
			else
			{
				if (moduleTag == 0)
				{
					boqItem.InstalledQuantity = lineItems.Sum(lineItem =>
					{
						var estHeaderFk = lineItem.EstHeaderFk;
						var estLineItemFk = lineItem.Id;
						var iqQuantity = boqItem2ResultQtyWithLineItem.ContainsKey((estHeaderFk, estLineItemFk)) ? boqItem2ResultQtyWithLineItem[(estHeaderFk, estLineItemFk)].Item1 : 0;
						return lineItem.QuantityTotal != 0 && costTotalSum != 0
							? ordQuantity * lineItem.CostTotal * iqQuantity / lineItem.QuantityTotal / costTotalSum
							: 0;
					});

					boqItem.BilledQuantity = lineItems.Sum(lineItem =>
					{
						var estHeaderFk = lineItem.EstHeaderFk;
						var estLineItemFk = lineItem.Id;
						var bqQuantity = boqItem2ResultQtyWithLineItem.ContainsKey((estHeaderFk, estLineItemFk)) ? boqItem2ResultQtyWithLineItem[(estHeaderFk, estLineItemFk)].Item2 : 0;
						return lineItem.QuantityTotal != 0 && costTotalSum != 0
							? ordQuantity * lineItem.CostTotal * bqQuantity / lineItem.QuantityTotal / costTotalSum
							: 0;
					});
				}
				else
				{
					boqItem.Quantity = lineItems.Sum(lineItem =>
					{
						var estHeaderFk = lineItem.EstHeaderFk;
						var estLineItemFk = lineItem.Id;
						var quantity = boqItem2ResultQtyWithLineItem.TryGetValue((estHeaderFk, estLineItemFk), out var resultQty) ? (moduleTag == 1 ? resultQty.Item1 : resultQty.Item2) : 0;
						return lineItem.QuantityTotal != 0 && costTotalSum != 0
							? ordQuantity * lineItem.CostTotal * quantity / lineItem.QuantityTotal / costTotalSum
							: 0;
					});
					boqItem.QuantityDetail = boqItem.Quantity > 0 ? boqItem.Quantity.ToString() : "";
					boqItem.QuantityAdj = boqItem.Quantity;
					boqItem.QuantityAdjDetail = boqItem.QuantityDetail;
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="boqItem"></param>
		/// <param name="lineItems"></param>
		/// <param name="boqItem2ResultQtyWithLineItem"></param>
		/// <param name="headerId"></param>
		/// <param name="moduleTag">1 wip, 2 bill</param>
		private void CollectLineItemQtyToSave(IBoqItemEntity boqItem, List<IEstLineItemEntity> lineItems, Dictionary<(int, int), (decimal, decimal)> boqItem2ResultQtyWithLineItem, int headerId, int moduleTag)
		{
			foreach (var item in lineItems)
			{
				if (boqItem2ResultQtyWithLineItem.TryGetValue((item.EstHeaderFk, item.Id), out var resultQty))
				{
					_estLineItemToSave.Add(new BoqLineItemQuantityEntity()
					{
						Id = item.Id,
						EstHeaderFk = item.EstHeaderFk,
						BoqHeaderFk = boqItem.BoqHeaderFk,
						BoqItemFk = boqItem.Id,
						LiQuantity = moduleTag == 1 ? resultQty.Item1 : 0,
						LiBilledQuantity = moduleTag == 2 ? resultQty.Item2 : 0,
						WipHeaderFk = moduleTag == 1 ? headerId : null,
						BilHeaderFk = moduleTag == 2 ? headerId : null
					});
				}
			}
		}

		private void calculateIsWipItem(IBoqItemEntity entity, IEnumerable<WipBoqItemsVEntity> wip2BoqItems, IEnumerable<WipBoqItemsPCQtyOldVEntity> wipQuantities, out decimal perviousQuantity, out decimal extraPrevious)
		{
			perviousQuantity = 0m;
			extraPrevious = 0m;

			if (wip2BoqItems != null)
			{
				var _boqHeaderFks = wip2BoqItems.Select(e => e.BoqHeaderFk).ToList();
				var _ids = wip2BoqItems.Select(e => e.Id).ToList();

				var wib2BoqQuantities = wipQuantities.Where(e => _boqHeaderFks.Contains(e.BoqHeaderFk) && _ids.Contains(e.Id)).ToList();

				perviousQuantity = wip2BoqItems.Sum(e => e.Quantity);
				extraPrevious = 0;
				if (wib2BoqQuantities != null && wib2BoqQuantities.Any())
				{
					extraPrevious = wib2BoqQuantities.Where(e => e.ExtraPrevious.HasValue).Sum(e => e.ExtraPrevious.Value);
				}
			}
			entity.IQPrevQuantity = perviousQuantity;
			entity.IQRemainingQuantity = (entity.OrdQuantity - entity.IQPrevQuantity) < 0 ? 0 : entity.OrdQuantity - entity.IQPrevQuantity;
			entity.IQTotalQuantity = entity.IQPrevQuantity + entity.InstalledQuantity;
		}

		private void calculateIsBillItem(IBoqItemEntity entity, IEnumerable<BilBoqItemsVEntity> bill2BoqItems, IEnumerable<BilBoqItemsPCQtyVEntity> billQuantities, out decimal perviousQuantity, out decimal extraPrevious)
		{
			perviousQuantity = 0m;
			extraPrevious = 0m;

			if (bill2BoqItems != null)
			{
				var _boqHeaderFks = bill2BoqItems.Select(e => e.BoqHeaderFk).ToList();
				var _ids = bill2BoqItems.Select(e => e.Id).ToList();

				var bill2BoqQuantities = billQuantities.Where(e => _boqHeaderFks.Contains(e.BoqHeaderFk) && _ids.Contains(e.Id)).ToList();

				perviousQuantity = bill2BoqItems.Sum(e => e.Quantity);
				extraPrevious = 0;
				if (bill2BoqQuantities != null && bill2BoqQuantities.Any())
				{
					extraPrevious = bill2BoqQuantities.Where(e => e.ExtraPrevious.HasValue).Sum(e => e.ExtraPrevious.Value);
				}
			}
			entity.BQPrevQuantity = perviousQuantity;
			entity.BQRemainingQuantity = (entity.OrdQuantity - entity.BQPrevQuantity) < 0 ? 0 : entity.OrdQuantity - entity.BQPrevQuantity;
			entity.BQTotalQuantity = entity.BQPrevQuantity + entity.BilledQuantity;
		}

		private void calculateIsPesItem(IBoqItemEntity entity, IEnumerable<PesBoqItemsVEntity> pes2BoqItems, IEnumerable<IBoqItemQuantityExtensionEntity> pesQuantities, out decimal perviousQuantity, out decimal extraPrevious)
		{
			perviousQuantity = 0m;
			extraPrevious = 0m;

			if (pes2BoqItems != null)
			{
				var _boqheaderids = pes2BoqItems.Select(e => e.BoqHeaderFk).ToList();
				var _ids = pes2BoqItems.Select(e => e.Id).ToList();

				var pes2BoqQuantities = pesQuantities.Where(e => _boqheaderids.Contains(e.BoqHeaderFk) && _ids.Contains(e.Id)).ToList();
				perviousQuantity = pes2BoqItems.Sum(e => e.Quantity);
				if (pes2BoqQuantities != null && pes2BoqQuantities.Any())
				{
					extraPrevious = pes2BoqQuantities.Sum(e => e.PreviousExtraIncrement);
				}
			}
			entity.IQPrevQuantity = perviousQuantity;
			entity.IQRemainingQuantity = (entity.OrdQuantity - entity.IQPrevQuantity) < 0 ? 0 : entity.OrdQuantity - entity.IQPrevQuantity;
			entity.IQTotalQuantity = entity.IQPrevQuantity + entity.InstalledQuantity;
		}

		/// <summary>
		/// set loacation and cost groups for qto line, which are from boq item or boq split quantity
		/// </summary>
		public bool SetLocationAndCostGroups2QtoLine(List<QtoDetailEntity> qtoDetailsToSave, Dictionary<int, List<MainItem2CostGroupEntity>> costGroups2Dic,
				bool isSourceBoq = true, int boqHeaderId = -1, int boqItemId = -1, List<MainItem2CostGroupEntity> toDeleteCostGroupAssignments = null)
		{
			bool hasCostGroups = false;

			var qtoDetailIds = qtoDetailsToSave.Select(e => e.Id).ToList();
			List<MainItem2CostGroupEntity> qtoDetailCostGroupAssignmentsList = null;

			var boqHeaderFks = qtoDetailsToSave.Select(e => e.BoqHeaderFk).ToList();
			var boqItemFks = qtoDetailsToSave.Select(e => e.BoqItemFk).ToList();

			boqHeaderFks.Add(boqHeaderId);
			boqItemFks.Add(boqItemId);

			Dictionary<int, BoqItemData> boqItemsDic = null;

			List<MainItem2CostGroupEntity> boqItemCostGroupAssignments = null;

			var boqSplitQuantityFks = qtoDetailsToSave.Where(e => e.BoqSplitQuantityFk.HasValue).Select(e => e.BoqSplitQuantityFk.Value).ToList();
			List<int> splitBoqHeaderFks = new List<int>();
			List<int> splitBoqItemFks = new List<int>();
			Dictionary<int, BoqSplitQuantityEntity> boqSplitListDic = null;

			List<BoqSplitQuantity2CostGroupEntity> boqSplitQuantity2CostGroupList = null;

			if (toDeleteCostGroupAssignments == null)
			{
				toDeleteCostGroupAssignments = new List<MainItem2CostGroupEntity>();
			}

			List<MainItem2CostGroupEntity> lineItemCostGroupAssignmentList = null;
			List<int> estHeaderFks = qtoDetailsToSave.Where(e => e.EstHeaderFk.HasValue).Select(e => e.EstHeaderFk.Value).ToList();
			List<int> estLineItemFks = qtoDetailsToSave.Where(e => e.EstLineItemFk.HasValue).Select(e => e.EstLineItemFk.Value).ToList();
			List<IEstLineItemEntity> lineItems = null;

			foreach (var qtoDetail in qtoDetailsToSave)
			{
				if ((qtoDetail.IsSynced && !qtoDetail.IsBoqItemChange && !qtoDetail.IsBoqSplitChange && !qtoDetail.IsLineItemChange) || (_copyOption != null && !qtoDetail.ApplyCopyOption))
				{
					qtoDetail.IsSynced = false;
					continue;
				}

				qtoDetail.IsSynced = false;
				if ((qtoDetail.IsLineItemChange || (qtoDetail.Version == 0 && !qtoDetail.BoqSplitQuantityFk.HasValue)) && qtoDetail.EstLineItemFk.HasValue)
				{
					lineItems ??= Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>().GetLineItemsByIds(estLineItemFks, estHeaderFks).ToList();

					hasCostGroups = CopyAttributesFromLineItem(qtoDetail, qtoDetailIds, estHeaderFks, estLineItemFks, lineItemCostGroupAssignmentList, qtoDetailCostGroupAssignmentsList, costGroups2Dic, toDeleteCostGroupAssignments, lineItems);
				}
				else if (isSourceBoq && (qtoDetail.Version == 0 || qtoDetail.IsBoqSplitChange) && qtoDetail.BoqSplitQuantityFk.HasValue)
				{
					if (boqSplitListDic == null)
					{
						var boqSplitList = new BoqSplitQuantityLogic().GetSplitQuantitiesById(boqHeaderFks, boqSplitQuantityFks);
						boqSplitListDic = boqSplitList.DistinctBy(i => i.Id).ToDictionary(e => e.Id, e => e);

						splitBoqHeaderFks = boqSplitList.Select(e => e.BoqHeaderFk).ToList();
						boqSplitQuantityFks = boqSplitList.Select(e => e.Id).ToList();
						splitBoqItemFks = boqSplitList.Select(e => e.BoqItemFk).ToList();
					}

					// set location, PrcStructureFk and MdcControllingUnitFk
					var boqSplitEntity = boqSplitListDic.ContainsKey(qtoDetail.BoqSplitQuantityFk.Value) ? boqSplitListDic[qtoDetail.BoqSplitQuantityFk.Value] : null;

					if (boqSplitEntity != null && boqSplitEntity.BoqHeaderFk == qtoDetail.BoqHeaderFk)
					{
						qtoDetail.PrjLocationFk = qtoDetail.IsBoqSplitChange ? boqSplitEntity.PrjLocationFk : (qtoDetail.PrjLocationFk ?? boqSplitEntity.PrjLocationFk);
						qtoDetail.PrcStructureFk = qtoDetail.IsBoqSplitChange ? boqSplitEntity.PrcStructureFk : (qtoDetail.PrcStructureFk ?? boqSplitEntity.PrcStructureFk);
						qtoDetail.MdcControllingUnitFk = qtoDetail.IsBoqSplitChange ? boqSplitEntity.MdcControllingUnitFk : (qtoDetail.MdcControllingUnitFk ?? boqSplitEntity.MdcControllingUnitFk);

						// set cost group
						int splitBoqHeaderFk = boqSplitEntity.BoqHeaderFk;
						int splitBoqItemFk = boqSplitEntity.BoqItemFk;

						if (boqSplitQuantity2CostGroupList == null)
						{
							boqSplitQuantity2CostGroupList = new BoqSplitQuantityLogic().GetBoqSplitQuantity2CostGroupsByIds(splitBoqHeaderFks, splitBoqItemFks, boqSplitQuantityFks).ToList();
						}
						var splitCostGroupAssignments = boqSplitQuantity2CostGroupList.Where(e => e.BoqHeaderFk == splitBoqHeaderFk
														&& e.BoqItemFk == splitBoqItemFk
														&& e.BoqSplitQuantityFk == qtoDetail.BoqSplitQuantityFk.Value).ToList();

						if (qtoDetailCostGroupAssignmentsList == null)
						{
							qtoDetailCostGroupAssignmentsList = new MainItem2CostGroupLogic("QTO_DETAIL2COSTGRP").GetByFilter(e => e.MainItemId.HasValue
																&& qtoDetailIds.Contains(e.MainItemId.Value)).ToList();
						}
						var qtoDetailCostGroupAssignments = qtoDetailCostGroupAssignmentsList.Where(e => e.MainItemId == qtoDetail.Id).ToList();

						if (splitCostGroupAssignments.Count > 0 && (_copyOption == null || _isCostGroup))
						{
							var costGroups = new List<MainItem2CostGroupEntity>();
							foreach (var splitCostGroupAssignment in splitCostGroupAssignments)
							{
								var existQtoDetailAssignment = qtoDetailCostGroupAssignments.FirstOrDefault(e => e.MainItemId.HasValue && e.MainItemId.Value == qtoDetail.Id &&
									e.CostGroupCatFk == splitCostGroupAssignment.CostGroupCatFk);

								if (existQtoDetailAssignment == null)
								{
									existQtoDetailAssignment = costGroups2Dic.ContainsKey(qtoDetail.Id) ?
																costGroups2Dic[qtoDetail.Id].FirstOrDefault(e => e.MainItemId.HasValue && e.MainItemId.Value == qtoDetail.Id && e.CostGroupCatFk == splitCostGroupAssignment.CostGroupCatFk)
																: null;
								}

								if (existQtoDetailAssignment != null)
								{
									existQtoDetailAssignment.CostGroupCatFk = splitCostGroupAssignment.CostGroupCatFk;
									existQtoDetailAssignment.CostGroupFk = splitCostGroupAssignment.CostGroupFk;
									costGroups.Add(existQtoDetailAssignment);
								}
								else
								{

									MainItem2CostGroupEntity costGroup = new MainItem2CostGroupEntity();
									costGroup.Id = new QtoDetailLogic().GetCostGroupNewId();
									costGroup.CostGroupCatFk = splitCostGroupAssignment.CostGroupCatFk;
									costGroup.CostGroupFk = splitCostGroupAssignment.CostGroupFk;
									costGroup.MainItemId = qtoDetail.Id;
									costGroups.Add(costGroup);

								}
							}

							if (costGroups.Count > 0)
							{
								if (costGroups2Dic.ContainsKey(qtoDetail.Id))
								{
									costGroups2Dic[qtoDetail.Id].AddRange(costGroups);
								}
								else
								{
									costGroups2Dic.Add(qtoDetail.Id, costGroups);
								}

								hasCostGroups = true;
							}
						}

						var costGroupCatIdList = splitCostGroupAssignments.Select(e => e.CostGroupCatFk).ToList();
						toDeleteCostGroupAssignments.AddRange(qtoDetailCostGroupAssignments.Where(e => !costGroupCatIdList.Contains(e.CostGroupCatFk)));
						if (costGroups2Dic.ContainsKey(qtoDetail.Id))
						{
							var existItems = costGroups2Dic[qtoDetail.Id].Where(e => !costGroupCatIdList.Contains(e.CostGroupCatFk)).ToList();
							foreach (var existItem in existItems)
							{
								costGroups2Dic[qtoDetail.Id].Remove(existItem);
							}
						}
					}

					qtoDetail.IsBoqSplitChange = false;
				}
				else if ((qtoDetail.Version == 0 || qtoDetail.IsBoqItemChange) && (!isSourceBoq || !qtoDetail.BoqSplitQuantityFk.HasValue))
				{
					int boqHeaderFk = isSourceBoq ? qtoDetail.BoqHeaderFk : boqHeaderId;
					int boqItemFk = isSourceBoq ? qtoDetail.BoqItemFk : boqItemId;

					if (boqItemCostGroupAssignments == null)
					{
						boqItemCostGroupAssignments = new MainItem2CostGroupLogic("BOQ_ITEM2COSTGRP").GetByFilter(e => e.MainItemId.HasValue
												 && e.RootItemId.HasValue
												 && boqHeaderFks.Contains(e.RootItemId.Value)
												 && boqItemFks.Contains(e.MainItemId.Value)).ToList();
					}
					List<MainItem2CostGroupEntity> boqItemCostGroupAssignmentList = boqItemCostGroupAssignments;
					if (!isSourceBoq)
					{
						boqItemCostGroupAssignmentList = boqItemCostGroupAssignmentList.Where(e => e.RootItemId == qtoDetail.BoqHeaderFk).ToList();
					}
					boqItemCostGroupAssignmentList = boqItemCostGroupAssignmentList.Where(e => e.MainItemId.Value == qtoDetail.BoqItemFk).ToList();

					// set location, PrcStructureFk and MdcControllingUnitFk
					if (!qtoDetail.PrjLocationFk.HasValue)
					{
						if (boqItemsDic == null)
						{
							boqItemsDic = this.GetBoqItemDataByQtoLineBoqItemId(boqHeaderFk, boqItemFks);
						}

						var boqItem = boqItemsDic.ContainsKey(boqItemFk) ? boqItemsDic[boqItemFk] : null;
						if (boqItem != null)
						{
							qtoDetail.PrjLocationFk = boqItem.PRJ_LOCATION_FK;
							qtoDetail.PrcStructureFk = boqItem.PRC_STRUCTURE_FK;
							qtoDetail.MdcControllingUnitFk = boqItem.MDC_CONTROLLINGUNIT_FK;
						}
					}

					if (qtoDetailCostGroupAssignmentsList == null)
					{
						qtoDetailCostGroupAssignmentsList = new MainItem2CostGroupLogic("QTO_DETAIL2COSTGRP").GetByFilter(e => e.MainItemId.HasValue
															&& qtoDetailIds.Contains(e.MainItemId.Value)).ToList();
					}
					var qtoDetailCostGroupAssignments = qtoDetailCostGroupAssignmentsList.Where(e => e.MainItemId == qtoDetail.Id).ToList();

					// set cost group
					if (boqItemCostGroupAssignmentList.Count > 0 && (_copyOption == null || _isCostGroup))
					{
						var costGroups = new List<MainItem2CostGroupEntity>();
						foreach (var boqItemCostGroupAssignment in boqItemCostGroupAssignmentList)
						{
							var existQtoDetailAssignment = qtoDetailCostGroupAssignments.FirstOrDefault(e => e.MainItemId.HasValue && e.MainItemId.Value == qtoDetail.Id &&
									e.CostGroupCatFk == boqItemCostGroupAssignment.CostGroupCatFk);

							if (existQtoDetailAssignment != null)
							{
								existQtoDetailAssignment.CostGroupCatFk = boqItemCostGroupAssignment.CostGroupCatFk;
								existQtoDetailAssignment.CostGroupFk = boqItemCostGroupAssignment.CostGroupFk;
								costGroups.Add(existQtoDetailAssignment);
							}
							else
							{
								MainItem2CostGroupEntity costGroup = new MainItem2CostGroupEntity();
								costGroup.Id = new QtoDetailLogic().GetCostGroupNewId();
								costGroup.CostGroupCatFk = boqItemCostGroupAssignment.CostGroupCatFk;
								costGroup.CostGroupFk = boqItemCostGroupAssignment.CostGroupFk;
								costGroup.MainItemId = qtoDetail.Id;
								costGroups.Add(costGroup);

							}
						}

						if (costGroups.Count > 0)
						{
							if (costGroups2Dic.ContainsKey(qtoDetail.Id))
							{
								costGroups2Dic[qtoDetail.Id] = costGroups;
							}
							else
							{
								costGroups2Dic.Add(qtoDetail.Id, costGroups);
							}

							hasCostGroups = true;
						}
					}

					var costGroupCatIdList = boqItemCostGroupAssignmentList.Select(e => e.CostGroupCatFk).ToList();
					toDeleteCostGroupAssignments.AddRange(qtoDetailCostGroupAssignments.Where(e => !costGroupCatIdList.Contains(e.CostGroupCatFk)));
					if (costGroups2Dic.ContainsKey(qtoDetail.Id))
					{
						var existItems = costGroups2Dic[qtoDetail.Id].Where(e => !costGroupCatIdList.Contains(e.CostGroupCatFk)).ToList();
						foreach (var existItem in existItems)
						{
							costGroups2Dic[qtoDetail.Id].Remove(existItem);
						}
					}

					qtoDetail.IsBoqItemChange = false;
				}

				if (qtoDetail.ApplyCopyOption && _copyOption != null)
				{
					qtoDetail.PrjLocationFk = _isLocation ? qtoDetail.PrjLocationFk : null;
					qtoDetail.PrcStructureFk = _isPrc ? qtoDetail.PrcStructureFk : null;
					qtoDetail.MdcControllingUnitFk = _isControllingUnit ? qtoDetail.MdcControllingUnitFk : null;
					qtoDetail.ApplyCopyOption = false;
				}
			}

			return hasCostGroups;
		}

		/// <summary>
		/// copy location, prc, cost group...
		/// </summary>
		private bool CopyAttributesFromLineItem(QtoDetailEntity qtoDetail, List<int> qtoDetailIds, List<int> estHeaderFks, List<int> estLineItemFks, List<MainItem2CostGroupEntity> lineItemCostGroupAssignmentList, List<MainItem2CostGroupEntity> qtoDetailCostGroupAssignmentsList, Dictionary<int, List<MainItem2CostGroupEntity>> costGroups2Dic, List<MainItem2CostGroupEntity> toDeleteCostGroupAssignments, List<IEstLineItemEntity> lineItems)
		{
			bool hasCostGroups = false;

			var lineItem = lineItems.FirstOrDefault(e => e.EstHeaderFk == qtoDetail.EstHeaderFk && e.Id == qtoDetail.EstLineItemFk);
			if (lineItem != null)
			{
				qtoDetail.MdcControllingUnitFk ??= lineItem.MdcControllingUnitFk;
				qtoDetail.PrcStructureFk ??= lineItem.PrcStructureFk;
				qtoDetail.PrjLocationFk ??= lineItem.PrjLocationFk;
				qtoDetail.AssetMasterFk ??= lineItem.MdcAssetMasterFk;

				qtoDetail.SortCode01Fk ??= lineItem.SortCode01Fk;
				qtoDetail.SortCode02Fk ??= lineItem.SortCode02Fk;
				qtoDetail.SortCode03Fk ??= lineItem.SortCode03Fk;
				qtoDetail.SortCode04Fk ??= lineItem.SortCode04Fk;
				qtoDetail.SortCode05Fk ??= lineItem.SortCode05Fk;
				qtoDetail.SortCode06Fk ??= lineItem.SortCode06Fk;
				qtoDetail.SortCode07Fk ??= lineItem.SortCode07Fk;
				qtoDetail.SortCode08Fk ??= lineItem.SortCode08Fk;
				qtoDetail.SortCode09Fk ??= lineItem.SortCode09Fk;
				qtoDetail.SortCode10Fk ??= lineItem.SortCode10Fk;
			}

			lineItemCostGroupAssignmentList ??= new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => e.MainItemId.HasValue
										 && e.RootItemId.HasValue
										 && estHeaderFks.Contains(e.RootItemId.Value)
										 && estLineItemFks.Contains(e.MainItemId.Value)).ToList();

			qtoDetailCostGroupAssignmentsList ??= new MainItem2CostGroupLogic("QTO_DETAIL2COSTGRP").GetByFilter(e => e.MainItemId.HasValue
													&& qtoDetailIds.Contains(e.MainItemId.Value)).ToList();

			var qtoDetailCostGroupAssignments = qtoDetailCostGroupAssignmentsList.Where(e => e.MainItemId == qtoDetail.Id).ToList();

			// set cost group
			if (lineItemCostGroupAssignmentList.Count > 0 && (_copyOption == null || _isCostGroup))
			{
				var costGroups = new List<MainItem2CostGroupEntity>();
				var lineItem2QtoLineList = lineItemCostGroupAssignmentList.Where(e => e.MainItemId.HasValue && e.MainItemId.Value == qtoDetail.EstLineItemFk).ToList();
				foreach (var lineItem2QtoLine in lineItem2QtoLineList)
				{
					var existQtoDetailAssignment = qtoDetailCostGroupAssignments.FirstOrDefault(e => e.MainItemId.HasValue && e.MainItemId.Value == qtoDetail.Id && e.CostGroupCatFk == lineItem2QtoLine.CostGroupCatFk);

					if (existQtoDetailAssignment != null)
					{
						existQtoDetailAssignment.CostGroupFk = lineItem2QtoLine.CostGroupFk;
						costGroups.Add(existQtoDetailAssignment);
					}
					else
					{
						MainItem2CostGroupEntity costGroup = new MainItem2CostGroupEntity
						{
							Id = new QtoDetailLogic().GetCostGroupNewId(),
							CostGroupCatFk = lineItem2QtoLine.CostGroupCatFk,
							CostGroupFk = lineItem2QtoLine.CostGroupFk,
							MainItemId = qtoDetail.Id
						};
						costGroups.Add(costGroup);
					}
				}

				if (costGroups.Count > 0)
				{
					if (!costGroups2Dic.TryAdd(qtoDetail.Id, costGroups))
					{
						costGroups2Dic[qtoDetail.Id] = costGroups;
					}

					hasCostGroups = true;
				}
			}

			var costGroupCatIdList = lineItemCostGroupAssignmentList.Select(e => e.CostGroupCatFk).ToList();
			toDeleteCostGroupAssignments.AddRange(qtoDetailCostGroupAssignments.Where(e => !costGroupCatIdList.Contains(e.CostGroupCatFk)));
			if (costGroups2Dic.TryGetValue(qtoDetail.Id, out var value))
			{
				var existItems = value.Where(e => !costGroupCatIdList.Contains(e.CostGroupCatFk)).ToList();
				foreach (var existItem in existItems)
				{
					value.Remove(existItem);
				}
			}

			qtoDetail.IsLineItemChange = false;

			return hasCostGroups;
		}

		#region IEntityFacade members

		/// <summary>
		///
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public IDictionary<string, object> Get(int id)
		{
			var entity = GetItemByKey(id);
			var objectDic = ToDictionary(entity);
			return objectDic;
		}

		private IDictionary<string, object> ToDictionary(QtoDetailEntity entity)
		{
			var objectDic = entity.AsDictionary(_entityProperties);

			return objectDic;
		}

		/// <summary>
		///
		/// </summary>
		public string Id
		{
			get
			{
				return EntitiyFacadeIdentifier.QTO_DETAIL_MODEL;
			}
		}
		/// <summary>
		///
		/// </summary>
		public string ModuleName
		{
			get
			{
				return "qto.main";
			}
		}
		/// <summary>
		///
		/// </summary>
		public string Name
		{
			get
			{
				return "QtoDetailEntity";
			}
		}
		/// <summary>
		///
		/// </summary>
		public string[] Properties
		{
			get
			{
				return _entityProperties.GetPropertyNames();
			}
		}

		private static ConvertProperties _entityProperties = new ConvertProperties()
			.Add("Id", true)
			.Add("QtoHeaderFk", true)
			.Add("BoqHeaderFk", true)
			.Add("BoqItemFk", true)
			.Add("BoqSubItemFk", true)
			.Add("PrjLocationFk", true)
			.Add("AssetMasterFk", true)
			.Add("BudgetCodeFk", true)
			.Add("ClassificationFk", true)
			.Add("WorkCategoryFk", true)
			.Add("Vindex")
			.Add("QtoLineTypeFk", true)
			.Add("QtoCommentFk", true)
			.Add("LineText")
			.Add("RemarkText")
			.Add("Remark1Text")
			.Add("Factor")
			.Add("QtoFormulaFk", true)
			.Add("QtoDetailContinuationFk", true)
			.Add("BasBlobsFk", true)
			.Add("Value1")
			.Add("Value2")
			.Add("Value3")
			.Add("Value4")
			.Add("Value5")
			.Add("Operator1")
			.Add("Operator2")
			.Add("Operator3")
			.Add("Operator4")
			.Add("Operator5")
			.Add("PrjLocationReferenceFk", true)
			.Add("QtoDetailReferenceFk", true)
			.Add("BoqItemReferenceFk", true)
			.Add("BoqSubitemReferenceFk", true)
			.Add("QtoDetailStatusFk", true);

		/// <summary>
		///
		/// </summary>
		/// <param name="entityDictionary"></param>
		/// <returns></returns>
		public IDictionary<string, object> Save(IDictionary<string, object> entityDictionary)
		{
			QtoDetailEntity res = null;
			using (
				TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				int id = entityDictionary.GetId<int>();
				var entity = GetItemByKey(id);
				entity.SetObject(entityDictionary, _entityProperties);
				res = this.Save(entity);
				transaction.Complete();
			}
			return ToDictionary(res);
		}
		#endregion

		#region IChangeStatus members
		/// <summary>
		///
		/// </summary>
		/// <param name="identification"></param>
		/// <param name="statusId"></param>
		/// <returns></returns>
		public RVPBizComp.EntityBase ChangeStatus(IStatusIdentifyable identification, int statusId)
		{
			return UpdateStatus(identification.Id, statusId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="identifications"></param>
		/// <returns></returns>
		public IEnumerable<EntityBase> BulkChangeStatus(IEnumerable<IStatusIdentifyable> identifications)
		{
			var ids = identifications.Select(e => e.Id).ToList();
			
			var entities = this.GetItemsByKey(ids);
			if (entities == null || !entities.Any())
			{
				return Enumerable.Empty<EntityBase>();
			}
			var currentStatuIds = entities.Select(e => e.QtoDetailStatusFk).Distinct().ToList();

			var fromStatusId = identifications.First().OldStatusId;
			var toStatusId = identifications.First().CurrentStatusId;

			// Validate all entities have the expected fromStatusId
			var invalidStatusIds = entities
				 .Select(e => e.QtoDetailStatusFk)
				 .Where(statusId => statusId != fromStatusId)
				 .Distinct()
				 .ToList();

			if (invalidStatusIds.Any())
			{
				var invalidStatusIdsStr = string.Join(",", invalidStatusIds);
				throw new BusinessLayerException(
					 string.Format(COMMONNLS.ERR_CurrentStatusNotMatch, invalidStatusIdsStr, fromStatusId)
				);
			}

			// Update status
			foreach (var entity in entities)
			{
				entity.QtoDetailStatusFk = toStatusId.Value;
			}
			
			this.BulkSave(entities);
			return entities;

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="identification"></param>
		/// <returns></returns>
		public int GetCurrentStatus(IStatusIdentifyable identification)
		{
			var item = this.GetItemByKey(identification.Id);
			return item != null ? item.QtoDetailStatusFk : 0;
		}

		/// <summary>
		/// Get the status of the entities according to the given ids
		/// </summary>
		/// <param name="identifications"></param>
		/// <returns></returns>
		public IDictionary<int, int> GetBatchCurrenttStatus(IEnumerable<IStatusIdentifyable> identifications)
		{
			var ids = identifications.Select(e => e.Id).ToList();
			var items = this.GetItemsByKey(ids);
			return items
				 .Where(e => e != null)
				 .ToDictionary(e => e.Id, e => e.QtoDetailStatusFk);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="identifications"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public IEnumerable<int> GetCanChangeStatusEntities(IEnumerable<IStatusIdentifyable> identifications)
		{
			throw new NotImplementedException();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="BoqSplitQuantityIds"></param>
		/// <returns></returns>
		public IEnumerable<IQtoDetailEntity> GetQtoDetailsByBoqSplitQuantityIds(List<int> BoqSplitQuantityIds)
		{
			var entities = new List<QtoDetailEntity>();
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				entities = dbcontext.Entities<QtoDetailEntity>().Where(i => i.BoqSplitQuantityFk.HasValue && BoqSplitQuantityIds.Contains(i.BoqSplitQuantityFk.Value)).ToList();
			}

			return entities as IEnumerable<IQtoDetailEntity>;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="wipIds"></param>
		/// <param name="billingHeaderEntity"></param>
		public void AssignQtoLineToBillFromWips(List<int> wipIds, ISalesBillingHeaderEntity billingHeaderEntity)
		{
			var entities = new List<QtoDetailEntity>();
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				entities = dbcontext.Entities<QtoDetailEntity>().Where(i => !i.IsBlocked && !i.BilHeaderFk.HasValue && i.WipHeaderFk.HasValue && wipIds.Contains(i.WipHeaderFk.Value)).ToList();
				if (entities.Any())
				{
					foreach (var item in entities)
					{
						item.BilHeaderFk = billingHeaderEntity.Id;
						item.IsBQ = true;
						item.IsReadonly = true;
					}
				}
			}
			this.Save(entities);
		}

		#endregion


		/// <summary>
		/// get the qto lines that only has bill assginment and no wip assginment
		/// </summary>
		/// <param name="wipIds"></param>
		public IEnumerable<IQtoDetailEntity> GetQtoLinesBillNoRelatedWip(List<int> wipIds)
		{
			var qtoLines = new List<QtoDetailEntity>();
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				//find the qtolines that releated to the wip
				qtoLines = dbcontext.Entities<QtoDetailEntity>().Where(e => e.WipHeaderFk.HasValue && wipIds.Contains(e.WipHeaderFk.Value)).ToList();

				if (qtoLines.Any())
				{
					var qtoHeaderId = qtoLines.Select(e => e.QtoHeaderFk).Distinct().FirstOrDefault();
					qtoLines = GetSearchList(e => e.QtoHeaderFk == qtoHeaderId).ToList();

					qtoLines = qtoLines.Where(e => e.BilHeaderFk.HasValue && !e.WipHeaderFk.HasValue).ToList();

					/*var bilHeaderFks = qtoLines.Where(e => e.BilHeaderFk.HasValue).Select(e => e.BilHeaderFk.Value).ToList();

					var salesBilling2WipLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesBilling2WipLogic>();
					var bil2WipEntities = salesBilling2WipLogic.GetByBillIds(bilHeaderFks);

					var billIds = new List<int>();// the bill which no related wip
					if (!bil2WipEntities.Any())
					{
						billIds = bilHeaderFks;
					}
					else
					{
						var _bilHeaderIds = bil2WipEntities.Select(e => e.BilHeaderFk).Distinct().ToList();
						billIds.AddRange(bilHeaderFks.Where(e => !_bilHeaderIds.Contains(e)).ToList());
					}

					qtoLines = billIds.Any() ?
						qtoLines.Where(e => e.BilHeaderFk.HasValue && billIds.Contains(e.BilHeaderFk.Value)).ToList() :
						qtoLines.Where(e => !e.WipHeaderFk.HasValue).ToList();*/


				}
			}
			return qtoLines;
		}

		/// <summary>
		/// collect inculde mutilines and sort copy items
		/// </summary>
		/// <param name="qtoLines"></param>
		/// <param name="qtoList"></param>
		/// <param name="hasMultiLine"></param>
		/// <returns></returns>
		public IEnumerable<QtoDetailEntity> CollectAndSortQtoDetails(IEnumerable<QtoDetailEntity> qtoLines, List<QtoDetailEntity> qtoList, ref bool hasMultiLine)
		{
			List<QtoDetailEntity> qtoDetails = new List<QtoDetailEntity>();
			hasMultiLine = false;
			foreach (var qtoLine in qtoLines)
			{
				if (!qtoDetails.Any(e => e.Id == qtoLine.Id))
				{
					if (qtoLine.QtoFormula != null && qtoLine.QtoFormula.IsMultiline)
					{
						var qtoMultiLines = new QtoDetailHelper().GetReferencedMultiLineQtoData(qtoLine, qtoList);
						qtoDetails.AddRange(qtoMultiLines);
						hasMultiLine = true;
					}
					else
					{
						qtoDetails.Add(qtoLine);
					}
				}
			}

			qtoDetails = qtoDetails.OrderBy(n => n.PageNumber)
			.ThenBy(n => n.LineReference)
			.ThenBy(n => n.LineIndex).ToList();

			return qtoDetails;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoLines"></param>
		/// <param name="selectItem"></param>
		/// <returns></returns>
		public QtoDetailEntity GetLastItemByAdrressRange(IEnumerable<QtoDetailEntity> qtoLines, QtoDetailEntity selectItem)
		{
			QtoDetailEntity lastItemByRange = null;
			if (selectItem != null)
			{
				if (_sheetAreaList.Any() && !_sheetAreaList.Contains(selectItem.PageNumber) || _indexAreaList.Any() && !_indexAreaList.Contains(selectItem.LineIndex) || _lineAreaList.Any() && !_lineAreaList.Contains(selectItem.LineReference))
				{
					if (_sheetAreaList.Any())
					{
						qtoLines = qtoLines.Where(e => _sheetAreaList.Contains(e.PageNumber));
					}

					if (_lineAreaList.Any())
					{
						qtoLines = qtoLines.Where(e => _lineAreaList.Contains(e.LineReference));
					}

					if (_indexAreaList.Any())
					{
						qtoLines = qtoLines.Where(e => _indexAreaList.Contains(e.LineIndex));
					}

					lastItemByRange = qtoLines.LastOrDefault();
				}
				else
				{
					lastItemByRange = selectItem;
				}
			}

			return lastItemByRange;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoLines"></param>
		/// <returns></returns>
		public IEnumerable<QtoDetailEntity> FilterQtoLinesByRange(IEnumerable<QtoDetailEntity> qtoLines)
		{
			if (_sheetAreaList.Any())
			{
				qtoLines = qtoLines.Where(e => _sheetAreaList.Contains(e.PageNumber));
			}

			if (_lineAreaList.Any())
			{
				qtoLines = qtoLines.Where(e => _lineAreaList.Contains(e.LineReference));
			}

			if (_indexAreaList.Any())
			{
				qtoLines = qtoLines.Where(e => _indexAreaList.Contains(e.LineIndex));
			}

			return qtoLines;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineAddressDic"></param>
		/// <param name="isInsert"></param>
		/// <exception cref="Exception"></exception>
		public bool CheckAddressIsOverflow(Dictionary<string, LineAddress> lineAddressDic, bool isInsert)
		{
			var lineAddress = new LineAddress();
			lineAddressDic.TryGetValue("TargetAddress", out lineAddress);
			var existAddress = new LineAddress();
			lineAddressDic.TryGetValue("ExistAddress", out existAddress);
			if (lineAddress.IsOverflow)
			{
				return true;
			}
			else if (isInsert && !string.IsNullOrEmpty(existAddress.LineReference))
			{
				return true;
			}

			return false;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <param name="boqItemIds"></param>
		/// <returns></returns>
		public Dictionary<int, string> GetBoqItemsByQtoLineBoqItemId(int boqHeaderId, List<int> boqItemIds)
		{
			var entities = GetBoqItem2References(boqHeaderId, boqItemIds);

			var boqItem2RefDic = entities.ToDictionary(e => e.Id, e => e.Reference);

			return boqItem2RefDic;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <param name="boqItemIds"></param>
		/// <returns></returns>
		public Dictionary<int, BoqItemData> GetBoqItemDataByQtoLineBoqItemId(int boqHeaderId, List<int> boqItemIds)
		{
			var entities = GetBoqItem2References(boqHeaderId, boqItemIds);

			var Id2BoqItemData = entities.ToDictionary(e => e.Id, e => e);

			return Id2BoqItemData;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <param name="boqItemIds"></param>
		/// <returns></returns>
		private IEnumerable<BoqItemData> GetBoqItem2References(int boqHeaderId, List<int> boqItemIds)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				string boqItemListStr = "";
				boqItemIds = boqItemIds.Where(e => e > 0).Distinct().ToList();
				foreach (var item in boqItemIds)
				{
					boqItemListStr += string.IsNullOrEmpty(boqItemListStr) ? item.ToString() : "," + item.ToString();
				}

				string queryStr = "SELECT ID, REFERENCE, PRJ_LOCATION_FK, PRC_STRUCTURE_FK, MDC_CONTROLLINGUNIT_FK FROM BOQ_ITEM WHERE BOQ_HEADER_FK = {0} AND ID IN ({1})";
				var sql = string.Format(queryStr, boqHeaderId, boqItemListStr);
				var entities = dbcontext.SqlQuery<BoqItemData>(sql).ToList();

				return entities;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoHeaderId"></param>
		/// <param name="qtoDetailToSave"></param>
		/// <returns></returns>
		public string GetDuplicateQtos(int qtoHeaderId, List<QtoDetailEntity> qtoDetailToSave)
		{
			string error = "";

			var dupQtoGroups = qtoDetailToSave.GroupBy(e => QtoLineReferenceHelper.GetQtoReference(e)).ToList();

			var dupQtos = dupQtoGroups.Where(e => e.Count() > 1).ToList();
			if (dupQtos != null && dupQtos.Any())
			{
				var updatedBys = dupQtos[0].Select(e => e.UpdatedBy.HasValue ? e.UpdatedBy.Value : e.InsertedBy).ToList();
				error = AppendUpdteUserInfo(updatedBys);
				return error;
			}

			var qtoLineReferences = qtoDetailToSave.Where(e => e.IsModifyLineReference).Select(e => QtoLineReferenceHelper.GetQtoReference(e)).ToList();

			if (!qtoLineReferences.Any())
			{
				return error;
			}

			var duplicateQtos = QtoLineReferenceHelper.IsUniqeAddressRange(qtoHeaderId, qtoDetailToSave);
			if (duplicateQtos != null && duplicateQtos.Any())
			{
				var updatedBys = duplicateQtos.Select(e => e.UpdatedBy.HasValue ? e.UpdatedBy.Value : e.InsertedBy).ToList();
				error = AppendUpdteUserInfo(updatedBys);
				return error;
			}

			var lineIndexs = qtoDetailToSave.Select(e => e.LineIndex).ToList();
			var lineReferences = qtoDetailToSave.Select(e => e.LineReference).ToList();
			var pageNumbers = qtoDetailToSave.Select(e => e.PageNumber).ToList();
			var qtoDetailToSaveIds = qtoDetailToSave.Select(e => e.Id).ToList();

			string sql = string.Format(@"SELECT ID,
												WHOUPD AS UpdatedBy,
												WHOISR AS InsertedBy
											FROM QTO_DETAIL
											WHERE QTO_HEADER_FK ={0}
											AND LINE_INDEX IN ({1})
											AND LINE_REFERENCE IN ('{2}')
											AND PAGE_NUMBER IN ({3})
											AND ID NOT IN ({4})",
											qtoHeaderId,
											String.Join(",", lineIndexs),
											String.Join(",", lineReferences),
											String.Join(",", pageNumbers),
											String.Join(",", qtoDetailToSaveIds));

			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var tempQtos = dbContext.SqlQuery<TempQto>(sql).ToList();
				if (tempQtos.Any())
				{
					var updatedBys = tempQtos.Select(e => e.UpdatedBy.HasValue ? e.UpdatedBy.Value : e.InsertedBy).ToList();
					error = AppendUpdteUserInfo(updatedBys);
				}
			}

			return error;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="userIds"></param>
		/// <returns></returns>
		public string AppendUpdteUserInfo(List<int> userIds)
		{
			userIds = userIds.Distinct().ToList();
			StringBuilder errorString = new StringBuilder("Data was stored before by ");
			userIds = userIds.Distinct().ToList();
			if (userIds.Any())
			{
				using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
				{
					var userList = dbContext.SqlQuery<TempUser>("SELECT ID, LOGONNAME FROM FRM_USER WHERE ID IN (" + String.Join(",", userIds) + ")").ToList();
					foreach (var userId in userIds)
					{
						var user = userList.FirstOrDefault(x => x.Id == userId);
						if (user != null)
						{
							errorString.Append(user.LogonName + " ");
						}
					}
				}
			}

			return errorString.ToString();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="tableName"></param>
		/// <param name="count"></param>
		/// <returns></returns>
		public IList<int> GetNextIds(string tableName, int count)
		{
			var ids = SequenceManager.GetNextList(tableName, count);
			return ids;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtoHeader"></param>
		/// <param name="qtoDetailsToSave"></param>
		/// <param name="costGroupsToSave"></param>
		public void SaveImportXmlData(QtoHeaderEntity qtoHeader, IEnumerable<QtoDetailEntity> qtoDetailsToSave, IEnumerable<MainItem2CostGroupEntity> costGroupsToSave)
		{
			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				// save the referece lines first
				var referQtoLineIds = qtoDetailsToSave.Where(e => e.QtoDetailReferenceFk.HasValue).Select(e => e.QtoDetailReferenceFk.Value);
				SaveReferenceQtoLines(qtoDetailsToSave.Where(e => referQtoLineIds.Contains(e.Id)));
				// save qto Lines
				new QtoDetailHelper().UpdateQtoDetailGroupInfo(qtoDetailsToSave);
				StringBuilder timeStr = new StringBuilder();
				QtoDetailHelper detailHelper = new QtoDetailHelper(qtoDetailsToSave, null, false, qtoHeader.NoDecimals.Value);
				detailHelper.SaveWithCheck(qtoHeader.BasGoniometerTypeFk, ref timeStr);

				// save costgroups
				new MainItem2CostGroupLogic("QTO_DETAIL2COSTGRP").Save(costGroupsToSave);

				transaction.Complete();
			}
		}

		/// <summary>
		///
		/// </summary>
		private void SaveReferenceQtoLines(IEnumerable<QtoDetailEntity> referQtoLines)
		{
			if (referQtoLines.Any())
			{
				this.Save(referQtoLines);
			}
		}

		#region Copy Source Qto Lines

		/// <summary>
		/// source qto lines copy funciton
		/// </summary>
		/// <param name="isOneProject"></param>
		/// <param name="sourceQtoHeaderFk"></param>
		/// <param name="tagetQtoHeaderFk"></param>
		/// <param name="boqItem"></param>
		/// <param name="qtoLineIds"></param>
		/// <param name="isOverflow"></param>
		/// <param name="isNewSheet"></param>
		/// <param name="allQtolinesByBoq"></param>
		/// <param name="costGroups"></param>
		/// <param name="updateBoqitems"></param>
		/// <param name="boqSplitQtyList"></param>
		/// <returns></returns>
		public IEnumerable<QtoDetailEntity> DoCopySourceQtoLines(bool isOneProject, int sourceQtoHeaderFk, int tagetQtoHeaderFk, BoqItemEntity boqItem, IEnumerable<int> qtoLineIds, out bool isOverflow, out bool isNewSheet, List<QtoDetailEntity> allQtolinesByBoq, List<MainItem2CostGroupEntity> costGroups, List<BoqItemEntity> updateBoqitems, List<BoqSplitQuantityEntity> boqSplitQtyList)
		{
			bool isOneQtoHeader = sourceQtoHeaderFk == tagetQtoHeaderFk;
			var currentQtoLines = this.GetListByQtoHeaderId(tagetQtoHeaderFk).ToList();
			var sourceQtoLines = isOneQtoHeader ? currentQtoLines : this.GetListByQtoHeaderId(sourceQtoHeaderFk).ToList();
			var currentQtoHeader = new QtoHeaderLogic().GetItemByKey(tagetQtoHeaderFk);

			int boqHeaderFk = 0, boqItemFk = 0;
			if (_moduleName == "sales.billing" || _moduleName == "sales.wip" || _moduleName == "procurement.pes")
			{
				boqHeaderFk = boqItem.BoqItemPrjBoqFk ?? 0;
				boqItemFk = boqItem.BoqItemPrjItemFk ?? 0;
			}
			else
			{
				boqHeaderFk = boqItem.BoqHeaderFk;
				boqItemFk = boqItem.Id;
			}

			var filterQtoLines = currentQtoLines.Where(e => e.BoqHeaderFk == boqHeaderFk && e.BoqItemFk == boqItemFk);
			allQtolinesByBoq.AddRange(filterQtoLines);

			var qtoSheets = new QtoStructrueLogic().GetSheetListWithStatusAccessRight(new List<int>() { tagetQtoHeaderFk }).ToList();

			var qtoList = sourceQtoLines.Where(e => qtoLineIds.Contains(e.Id));

			// the qtoLines were included multilines, order qtoLines
			var sourceLines = qtoList.OrderBy(e => e.PageNumber).ThenBy(e => e.LineReference).ThenBy(e => e.LineIndex).ToList();

			// set the value reference as result
			SetValueReferenceAsValue(sourceQtoLines, sourceLines);

			// Set Adress With TargetHeader
			SetAdressWithTargetHeader(tagetQtoHeaderFk, sourceLines, out isOverflow, currentQtoLines, qtoSheets);

			if (isOverflow)
			{
				isNewSheet = false;
				return new List<QtoDetailEntity>();
			}

			var cloneLines = sourceLines.Select(e => e.Clone() as QtoDetailEntity).ToList();
			var sourceIds = sourceLines.Select(e => e.Id).ToList();
			var ids = SequenceManager.GetNextList("QTO_DETAIL", sourceIds.Count);
			var old2NewIds = new Dictionary<int, int>();
			for (int i = 0; i < sourceIds.Count; i++)
			{
				if (!old2NewIds.ContainsKey(sourceIds[i]))
				{
					old2NewIds.Add(sourceIds[i], ids[i]);
				}
			}

			var costGroupLogic = new MainItem2CostGroupLogic("QTO_DETAIL2COSTGRP");
			List<MainItem2CostGroupEntity> sourceLine2CostGroups = null;
			var costGroupsToSave = new List<MainItem2CostGroupEntity>();

			var qtoSheetLogic = new QtoStructrueLogic();
			var sheets = qtoSheets.Where(e => e.PageNumber.HasValue).DistinctBy(e => e.PageNumber.Value);
			var pageNumber2IdTaget = sheets.ToDictionary(e => e.PageNumber.Value, e => e.Id);
			var sheetNo2ToSave = new List<int>();

			// qto for boqitem and boq split
			var qtoLineByBoqitem = currentQtoLines.LastOrDefault(e => e.BoqItemFk == boqItemFk);
			BoqSplitQuantityEntity defaultBoqSplit = null;
			if (qtoLineByBoqitem == null)
			{
				var boqQtySplits = new BoqSplitQuantityLogic().GetList(boqHeaderFk, boqItemFk);
				defaultBoqSplit = boqQtySplits.FirstOrDefault();
			}

			var defaultLineTypeFk = -1;
			var defaultLineType = new QtoLineTypeLogic().GetDefault();
			if (defaultLineType != null)
			{
				defaultLineTypeFk = defaultLineType.Id;
			}

			for (int i = 0; i < cloneLines.Count; i++)
			{
				var cloneLine = cloneLines[i];

				int sourceId = cloneLine.Id;

				cloneLine.Id = old2NewIds[sourceId];
				cloneLine.QtoHeaderFk = tagetQtoHeaderFk;
				cloneLine.BoqHeaderFk = boqHeaderFk;
				cloneLine.BoqItemFk = boqItemFk;
				cloneLine.BasUomFk = boqItem.BasUomFk;
				cloneLine.BoqItemCode = boqItem.Reference;
				cloneLine.Version = 0;

				cloneLine.QtoDetailReference = ConvertQtoDetailReference(cloneLine);

				// set as null
				cloneLine.IsReadonly = false;
				cloneLine.QtoCommentFk = null;
				cloneLine.ClassificationFk = null;
				cloneLine.WorkCategoryFk = null;
				cloneLine.BudgetCodeFk = null;
				cloneLine.QtoCommentFk = null;
				cloneLine.QtoDetailSplitFromFk = null;
				cloneLine.IsSplitted = false;

				// WipHeaderFk, BilHeaderFk and PesHeaderFk
				SetHeadersFk(cloneLine);

				// isAq, isWq, isBq, isIq, isGq
				AssignQuantyType(cloneLine, currentQtoHeader);

				#region copy options: location, assetMaster, controlling unit, sort code, cost group, prc, Bill To, Contract, Qto Detail Copy Options

				if (!_isAssetMaster)
				{
					cloneLine.AssetMasterFk = null;
				}

				if (!_isPrc)
				{
					cloneLine.PrcStructureFk = null;
				}

				// if same project, it will copy location, controlling unit, sort code, cost group, Bill To, Contract
				cloneLine.PrjLocationFk = isOneProject && _isLocation ? cloneLine.PrjLocationFk : null;
				cloneLine.MdcControllingUnitFk = isOneProject && _isControllingUnit ? cloneLine.MdcControllingUnitFk : null;

				cloneLine.SortCode01Fk = isOneProject && _isSortCode ? cloneLine.SortCode01Fk : null;
				cloneLine.SortCode02Fk = isOneProject && _isSortCode ? cloneLine.SortCode02Fk : null;
				cloneLine.SortCode03Fk = isOneProject && _isSortCode ? cloneLine.SortCode03Fk : null;
				cloneLine.SortCode04Fk = isOneProject && _isSortCode ? cloneLine.SortCode04Fk : null;
				cloneLine.SortCode05Fk = isOneProject && _isSortCode ? cloneLine.SortCode05Fk : null;
				cloneLine.SortCode06Fk = isOneProject && _isSortCode ? cloneLine.SortCode06Fk : null;
				cloneLine.SortCode07Fk = isOneProject && _isSortCode ? cloneLine.SortCode07Fk : null;
				cloneLine.SortCode08Fk = isOneProject && _isSortCode ? cloneLine.SortCode08Fk : null;
				cloneLine.SortCode09Fk = isOneProject && _isSortCode ? cloneLine.SortCode09Fk : null;
				cloneLine.SortCode10Fk = isOneProject && _isSortCode ? cloneLine.SortCode10Fk : null;

				// copy cost group
				if (isOneProject && _isCostGroup)
				{
					if (sourceLine2CostGroups == null)
					{
						sourceLine2CostGroups = costGroupLogic.GetByFilter(e => sourceIds.Contains(e.MainItemId.Value)).ToList();
					}

					AddQtoLines2CostGroup(sourceId, cloneLine.Id, sourceLine2CostGroups, costGroupsToSave);
				}

				// bill to
				if (!_isBillTo || !isOneProject || currentQtoHeader.OrdHeaderFk.HasValue || (cloneLine.BillToFk.HasValue && currentQtoHeader.QtoTargetType != (int)QtoTargetType.WipOrBill))
				{
					cloneLine.BillToFk = null;
				}

				// contractfk
				if (!_isContract || !isOneProject || currentQtoHeader.OrdHeaderFk.HasValue || (cloneLine.OrdHeaderFk.HasValue && currentQtoHeader.QtoTargetType != (int)QtoTargetType.WipOrBill))
				{
					cloneLine.OrdHeaderFk = null;
				}

				if (!_isType && defaultLineTypeFk > 0)
				{
					cloneLine.QtoLineTypeFk = defaultLineTypeFk;
				}

				if (!_isLineItem || !isOneProject)
				{
					cloneLine.EstLineItemFk = null;
				}

				if (!_isUserDefined)
				{
					cloneLine.UserDefined1 = null;
					cloneLine.UserDefined2 = null;
					cloneLine.UserDefined3 = null;
					cloneLine.UserDefined4 = null;
					cloneLine.UserDefined5 = null;
				}

				if (!_isPerformedDate)
				{
					cloneLine.PerformedDate = DateTime.UtcNow;
				}

				if (!_isV)
				{
					cloneLine.V = null;
				}

				if (!_isRemark)
				{
					cloneLine.RemarkText = null;
				}

				if (!_isRemark2)
				{
					cloneLine.Remark1Text = null;
				}

				if (!_isFactor)
				{
					cloneLine.Factor = null;
				}


				if (!_isValue1)
				{
					cloneLine.Value1 = null;
					cloneLine.Value1Detail = null;
					cloneLine.LineText = null;

					if (cloneLine.QtoLineTypeFk == (int)EQtoLineType.RowReference)
					{
						cloneLine.QtoDetailReferenceFk = null;
					}
					else if(cloneLine.QtoLineTypeFk == (int)EQtoLineType.BoqAndLocationReference)
					{
						cloneLine.BoqItemReferenceFk = null;
					}
				}

				if (!_isValue2)
				{
					cloneLine.Value2 = null;
					cloneLine.Value2Detail = null;

					if (cloneLine.QtoLineTypeFk == (int)EQtoLineType.BoqAndLocationReference)
					{
						cloneLine.PrjLocationReferenceFk = null;
					}
				}

				if (!_isValue3)
				{
					cloneLine.Value3 = null;
					cloneLine.Value3Detail = null;
				}

				if (!_isValue4)
				{
					cloneLine.Value4 = null;
					cloneLine.Value4Detail = null;
				}

				if (!_isValue5)
				{
					cloneLine.Value5 = null;
					cloneLine.Value5Detail = null;
				}

				#endregion

				// boq split
				AssignBoqSplit(cloneLine, qtoLineByBoqitem, defaultBoqSplit);

				// sheet
				assignQtoSheetAndNew(cloneLine, pageNumber2IdTaget, sheetNo2ToSave);

				// line reference, location reference and boqItem reference
				if (!isOneQtoHeader)
				{
					// qto detail reference fk
					cloneLine.QtoDetailReferenceFk = null;

					// location reference
					cloneLine.PrjLocationReferenceFk = null;

					// boqItem reference
					cloneLine.BoqItemReferenceFk = null;
				}
			}

			// exist, Yes: assgin sheet, No: new.
			isNewSheet = sheetNo2ToSave.Any();
			if (sheetNo2ToSave.Any())
			{
				Dictionary<int, int> newPageNumber2IdTaget = new Dictionary<int, int>();
				qtoSheetLogic.CreateByQtoLine(tagetQtoHeaderFk, 0, true, sheetNo2ToSave.Distinct(), 1, true, true, newPageNumber2IdTaget);
				foreach (var cloneLine in cloneLines)
				{
					if (newPageNumber2IdTaget.ContainsKey(cloneLine.PageNumber))
					{
						cloneLine.QtoSheetFk = newPageNumber2IdTaget[cloneLine.PageNumber];
					}
				}
			}

			// save clone qto Lines
			StringBuilder timeStr = new StringBuilder();
			QtoDetailHelper detailHelper = new QtoDetailHelper(cloneLines, null, false, currentQtoHeader.NoDecimals.Value);
			var qtoDataBase = detailHelper.SaveWithCheck(currentQtoHeader.BasGoniometerTypeFk, ref timeStr);
			var qtoDetailsToSave = qtoDataBase.QtoUpdateDataFromClient.DistinctBy(e => e.Id).OrderBy(e => e.PageNumber).ThenBy(e => e.LineReference).ThenBy(e => e.LineIndex).ToList();

			// update the group info
			detailHelper.UpdateQtoDetailGroupInfo(qtoDetailsToSave);

			// save qto lines to cost group
			if (costGroupsToSave.Any())
			{
				var newIds = this.SequenceManager.GetNextList("QTO_DETAIL2COSTGRP", costGroupsToSave.Count);
				for (int i = 0; i < newIds.Count; i++)
				{
					costGroupsToSave[i].Id = newIds[i];
				}

				costGroupsToSave = costGroupLogic.Save(costGroupsToSave).ToList();

				costGroups.AddRange(costGroupsToSave);
			}

			allQtolinesByBoq.AddRange(qtoDetailsToSave);

			if (_moduleName != "qto.main")
			{
				// calculate boqItems
				List<BoqItemEntity> boqItems = new List<BoqItemEntity>
				{
					boqItem
				};
				var spitItems = new BoqSplitQuantityLogic().GetList(boqItem.BoqHeaderFk, boqItem.Id);

				var qtoLines2Moudule = new List<QtoDetailEntity>();
				var qtoDetailToSave = qtoDetailsToSave.FirstOrDefault();
				int headerId = 0;
				if (_moduleName == "sales.wip")
				{
					headerId = qtoDetailToSave.WipHeaderFk ?? 0;
					qtoLines2Moudule.AddRange(allQtolinesByBoq.Where(e => e.WipHeaderFk == headerId));
				}
				else if (_moduleName == "procurement.pes")
				{
					qtoLines2Moudule.AddRange(allQtolinesByBoq.Where(e => e.PesHeaderFk == qtoDetailToSave.PesHeaderFk));
				}
				else if (_moduleName == "sales.billing")
				{
					headerId = qtoDetailToSave.BilHeaderFk ?? 0;
					qtoLines2Moudule.AddRange(allQtolinesByBoq.Where(e => e.BilHeaderFk == headerId));
				}
				else
				{
					qtoLines2Moudule.AddRange(allQtolinesByBoq);
				}

				// update boq wq/aq and save split quantity
				updateBoqitems.Add(UpdateBoqItemQuantities(qtoLines2Moudule, boqItems, _moduleName, spitItems, false, boqSplitQtyList, headerId));
			}

			return qtoDetailsToSave;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="sourceQtoLines"></param>
		/// <param name="qtoLines"></param>
		private void SetValueReferenceAsValue(List<QtoDetailEntity> sourceQtoLines, List<QtoDetailEntity> qtoLines)
		{
			var code2ResultMap = QtoCalculateHelper.GetQtoDetailResultMap(sourceQtoLines);

			foreach (var qtoLine in qtoLines)
			{
				if (!string.IsNullOrWhiteSpace(qtoLine.LineText))
				{
					var detailString = qtoLine.LineText.Replace("=", "").ToUpper();

					var matches = Regex.Matches(detailString, "(\\d{0,4}[a-zA-Z]\\d)");

					if (matches.Count > 0)
					{
						foreach (Match item in matches)
						{
							string strCode = item.Value.PadLeft(6, '0');

							var result = code2ResultMap.ContainsKey(strCode) ? CalculationHelper.QuantityRound(code2ResultMap[strCode]) : CalculationHelper.QuantityRound(0);

							qtoLine.LineText = qtoLine.LineText.ToUpper().Replace(item.Value, result.ToString());
						}
					}
				}

				if (!string.IsNullOrWhiteSpace(qtoLine.Value1Detail))
				{
					var detailString = qtoLine.Value1Detail.Replace("=", "").ToUpper();

					var matches = Regex.Matches(detailString, "(\\d{0,4}[a-zA-Z]\\d)");

					if (matches.Count > 0)
					{
						foreach (Match item in matches)
						{
							string strCode = item.Value.PadLeft(6, '0');

							var result = code2ResultMap.ContainsKey(strCode) ? CalculationHelper.QuantityRound(code2ResultMap[strCode]) : CalculationHelper.QuantityRound(0);

							qtoLine.Value1Detail = qtoLine.Value1Detail.Replace(item.Value, result.ToString());
						}
					}
				}

				if (!string.IsNullOrWhiteSpace(qtoLine.Value2Detail))
				{
					var detailString = qtoLine.Value2Detail.Replace("=", "").ToUpper();

					var matches = Regex.Matches(detailString, "(\\d{0,4}[a-zA-Z]\\d)");

					if (matches.Count > 0)
					{
						foreach (Match item in matches)
						{
							string strCode = item.Value.PadLeft(6, '0');

							var result = code2ResultMap.ContainsKey(strCode) ? CalculationHelper.QuantityRound(code2ResultMap[strCode]) : CalculationHelper.QuantityRound(0);

							qtoLine.Value2Detail = qtoLine.Value2Detail.Replace(item.Value, result.ToString());
						}
					}
				}

				if (!string.IsNullOrWhiteSpace(qtoLine.Value3Detail))
				{
					var detailString = qtoLine.Value3Detail.Replace("=", "").ToUpper();

					var matches = Regex.Matches(detailString, "(\\d{0,4}[a-zA-Z]\\d)");

					if (matches.Count > 0)
					{
						foreach (Match item in matches)
						{
							string strCode = item.Value.PadLeft(6, '0');

							var result = code2ResultMap.ContainsKey(strCode) ? CalculationHelper.QuantityRound(code2ResultMap[strCode]) : CalculationHelper.QuantityRound(0);

							qtoLine.Value3Detail = qtoLine.Value3Detail.Replace(item.Value, result.ToString());
						}
					}
				}

				if (!string.IsNullOrWhiteSpace(qtoLine.Value4Detail))
				{
					var detailString = qtoLine.Value4Detail.Replace("=", "").ToUpper();

					var matches = Regex.Matches(detailString, "(\\d{0,4}[a-zA-Z]\\d)");

					if (matches.Count > 0)
					{
						foreach (Match item in matches)
						{
							string strCode = item.Value.PadLeft(6, '0');

							var result = code2ResultMap.ContainsKey(strCode) ? CalculationHelper.QuantityRound(code2ResultMap[strCode]) : CalculationHelper.QuantityRound(0);

							qtoLine.Value4Detail = qtoLine.Value4Detail.Replace(item.Value, result.ToString());
						}
					}
				}

				if (!string.IsNullOrWhiteSpace(qtoLine.Value5Detail))
				{
					var detailString = qtoLine.Value5Detail.Replace("=", "").ToUpper();

					var matches = Regex.Matches(detailString, "(\\d{0,4}[a-zA-Z]\\d)");

					if (matches.Count > 0)
					{
						foreach (Match item in matches)
						{
							string strCode = item.Value.PadLeft(6, '0');

							var result = code2ResultMap.ContainsKey(strCode) ? CalculationHelper.QuantityRound(code2ResultMap[strCode]) : CalculationHelper.QuantityRound(0);

							qtoLine.Value5Detail = qtoLine.Value5Detail.Replace(item.Value, result.ToString());
						}
					}
				}
			}
		}

		/// <summary>
		/// set WipHeaderFk, BilHeaderFk and PesHeaderFk
		/// </summary>
		private void SetHeadersFk(QtoDetailEntity qtoLine)
		{
			qtoLine.WipHeaderFk = null;
			qtoLine.BilHeaderFk = null;
			qtoLine.PesHeaderFk = null;

			switch (_moduleName)
			{
				case "sales.wip":
					qtoLine.WipHeaderFk = _wipHeaderFk;
					break;
				case "sales.billing":
					qtoLine.BilHeaderFk = _bilHeaderFk;
					break;
				case "procurement.pes":
					qtoLine.PesHeaderFk = _pesHeaderFk;
					break;
			}
		}

		/// <summary>
		///
		/// </summary>
		private void AssignQuantyType(QtoDetailEntity qtoLine, QtoHeaderEntity currentQtoHeader)
		{
			if (currentQtoHeader == null)
			{
				return;
			}

			var qtoTargetType = currentQtoHeader.QtoTargetType;
			var typeIsAqWq = qtoTargetType == (int)QtoTargetType.PrcWqAq || qtoTargetType == (int)QtoTargetType.PrjWqAq;
			var typeIsIqBq = qtoTargetType == (int)QtoTargetType.WipOrBill || qtoTargetType == (int)QtoTargetType.PesBoq;
			var typeIsGq = new QtoHeaderLogic().IsGQAvailable(currentQtoHeader);

			if (!_isAq || !typeIsAqWq)
			{
				qtoLine.IsAQ = false;
			}

			if (!_isWq || !typeIsAqWq)
			{
				qtoLine.IsWQ = false;
			}

			if (!_isBq || !typeIsIqBq)
			{
				qtoLine.IsBQ = false;
			}

			if (!_isIq || !typeIsIqBq)
			{
				qtoLine.IsIQ = false;
			}

			if (!_isGq || !typeIsGq)
			{
				qtoLine.IsGQ = false;
			}
		}

		/// <summary>
		///
		/// </summary>
		private void AddQtoLines2CostGroup(int sourceId, int targetId, List<MainItem2CostGroupEntity> sourceLine2CostGroups, List<MainItem2CostGroupEntity> costGroupsToSave)
		{
			var cloneLine2CostGroups = sourceLine2CostGroups.Where(e => e.MainItemId.Value == sourceId);

			foreach (var cloneLine2CostGroup in cloneLine2CostGroups)
			{
				MainItem2CostGroupEntity costGroupTosave = new MainItem2CostGroupEntity
				{
					CostGroupCatFk = cloneLine2CostGroup.CostGroupCatFk,
					CostGroupFk = cloneLine2CostGroup.CostGroupFk,
					MainItemId = targetId
				};

				costGroupsToSave.Add(costGroupTosave);
			}
		}

		/// <summary>
		///
		/// </summary>
		private void AssignBoqSplit(QtoDetailEntity qtoLine, QtoDetailEntity qtoLineByBoqitem, BoqSplitQuantityEntity defaultBoqSplit)
		{
			if (qtoLineByBoqitem != null)
			{
				qtoLine.BoqSplitQuantityFk = qtoLineByBoqitem.BoqSplitQuantityFk;
			}
			else if (defaultBoqSplit != null)
			{
				qtoLine.BoqSplitQuantityFk = defaultBoqSplit.Id;
			}
			else
			{
				qtoLine.BoqSplitQuantityFk = null;
			}
		}

		/// <summary>
		///
		/// </summary>
		private void assignQtoSheetAndNew(QtoDetailEntity qtoLine, Dictionary<int, int> pageNumber2IdTaget, List<int> sheetNo2ToSave)
		{
			if (pageNumber2IdTaget.ContainsKey(qtoLine.PageNumber))
			{
				qtoLine.QtoSheetFk = pageNumber2IdTaget[qtoLine.PageNumber];
			}
			else
			{
				sheetNo2ToSave.Add(qtoLine.PageNumber);
			}
		}

		#endregion

		/// <summary>
		///
		/// </summary>
		public void SetAdressWithTargetHeader(int qtoHeaderFk, List<QtoDetailEntity> qtoLines, out bool isOverflow, List<QtoDetailEntity> qtoList, List<QtoSheetEntity> qtoSheets, bool isGenerated = true)
		{
			isOverflow = false;
			var filterQtoList = new List<QtoDetailEntity>();
			string selectedPageNumber = "";

			var lastQtoLine = qtoList.LastOrDefault();

			var qtoAddressRangeDetails = new QtoAddressRangeDetailLogic().GetQtoAddressRangeDetails(qtoHeaderFk, QtoAddressRangeType.Dialog);
			var qtoAddressRange = new QtoAddressRangeDetailLogic().GetQtoAddressRangeString(qtoAddressRangeDetails);

			if (qtoAddressRange != null)
			{
				var sheetAreaList = new QtoAddressRangeLogic().getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogSheetArea).Select(e => int.Parse(e.ToString())).OrderBy(e => e).ToList();
				var indexAreaList = new QtoAddressRangeLogic().getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogIndexArea).Select(e => int.Parse(e.ToString())).OrderBy(e => e).ToList();
				var lineAreaList = new QtoAddressRangeLogic().getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogLineArea).OrderBy(e => e).ToList();
				this.SetSheetAreaList(sheetAreaList)
					.SetIndexAreaList(indexAreaList)
					.SetLineAreaList(lineAreaList)
					.SetRanges();

				filterQtoList.AddRange(this.FilterQtoLinesByRange(qtoList).ToList());
			}
			else
			{
				filterQtoList.AddRange(qtoList);
			}

			filterQtoList = filterQtoList.OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList();

			// not automatic generate, check the address is matchable or not
			if (!isGenerated)
			{
				string errorMessage = "";
				var qtoLine = qtoLines.First();
				if (this._sheetAreaList.Any() && !this._sheetAreaList.Contains(qtoLine.PageNumber))
				{
					errorMessage = "The PagePageNumber Overflowed Sheet Area.";
				}

				if (this._lineAreaList.Any() && !this._lineAreaList.Contains(qtoLine.LineReference))
				{
					errorMessage += "\r\nThe LineReference Overflowed LineReference Area.";
				}

				if (this._indexAreaList.Any() && !this._indexAreaList.Contains(qtoLine.LineIndex))
				{
					errorMessage += "\r\nThe LineIndex Overflowed Index Area.";
				}

				var sheet = qtoSheets.FirstOrDefault(e => e.PageNumber == qtoLine.PageNumber && e.IsReadonly);
				if (sheet != null)
				{
					errorMessage += "\r\nThe Sheet was readonly.";
				}

				if (!string.IsNullOrEmpty(errorMessage))
				{
					throw new FormatException(errorMessage);
				}

				return;
			}

			// check multi or not
			if (filterQtoList != null && filterQtoList.Any() && lastQtoLine != null)
			{
				var qtoLine = this.GetQtoDetailByUser(filterQtoList, lastQtoLine);
				if (qtoLine != null)
				{
					if (qtoLine.Id > 0)
					{
						lastQtoLine = qtoLine;
					}
					else
					{
						selectedPageNumber = qtoLine.PageNumber.ToString();
						lastQtoLine = null;
					}
				}
			}

			// already config address range
			if (qtoAddressRange != null)
			{
				lastQtoLine = this.GetLastItemByAdrressRange(filterQtoList, lastQtoLine);
			}

			if (lastQtoLine != null)
			{
				lastQtoLine.PageNumber = string.IsNullOrEmpty(selectedPageNumber) ? lastQtoLine.PageNumber : int.Parse(selectedPageNumber);
			}

			Dictionary<string, LineAddress> lineAddressDic = this.GetNextLineAddress(lastQtoLine, selectedPageNumber, qtoHeaderFk, false, filterQtoList, null, qtoSheets);

			List<int> qtoIds = new List<int>();
			foreach (var qtoEntity in qtoLines)
			{
				if (!qtoIds.Contains(qtoEntity.Id))
				{
					qtoEntity.IsModifyLineReference = true;

					qtoIds.Add(qtoEntity.Id);

					var lineAddress = new LineAddress();
					lineAddressDic.TryGetValue("TargetAddress", out lineAddress);
					isOverflow = this.CheckAddressIsOverflow(lineAddressDic, false);
					if (isOverflow)
					{
						return;
					}

					if (lineAddress != null)
					{
						qtoEntity.LineIndex = lineAddress.LineIndex;
						qtoEntity.LineReference = lineAddress.LineReference;
						qtoEntity.PageNumber = lineAddress.PageNumber;
						if (qtoLines.Count > qtoIds.Count)
						{
							filterQtoList.Add(qtoEntity);
							lineAddressDic = this.GetNextLineAddress(qtoEntity, selectedPageNumber, qtoHeaderFk, false, filterQtoList, null, qtoSheets);

							isOverflow = this.CheckAddressIsOverflow(lineAddressDic, false);
							if (isOverflow)
							{
								return;
							}
						}
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		public class BoqItemData
		{
			/// <summary>
			///
			/// </summary>
			public int Id { get; set; }
			/// <summary>
			///
			/// </summary>
			public string Reference { get; set; }

			/// <summary>
			///
			/// </summary>
			public int? PRJ_LOCATION_FK { get; set; }

			/// <summary>
			///
			/// </summary>
			public int? PRC_STRUCTURE_FK { get; set; }

			/// <summary>
			///
			/// </summary>
			public int? MDC_CONTROLLINGUNIT_FK { get; set; }
		}

		/// <summary>
		/// can create qtoline or not
		/// </summary>
		/// <param name="qtoHeaderId"></param>
		/// <param name="boqHeaderId"></param>
		/// <param name="boqItemId"></param>
		/// <param name="errorMessage"></param>
		/// <returns></returns>
		public bool CanCreateQtoLine(int qtoHeaderId, int boqHeaderId, int boqItemId, out string errorMessage)
		{
			var qtoHeader = new QtoHeaderLogic().GetItemByKey(qtoHeaderId);
			if (qtoHeader == null)
			{
				errorMessage = string.Format("No Qto Header found.");
				return false;
			}

			// do validation for qtoheader status
			if (!DoQtoHeaderStatusValidate(qtoHeader, out errorMessage))
			{
				return false;
			}

			if (qtoHeader.BoqHeaderFk != boqHeaderId)
			{
				errorMessage = string.Format("No matching BoQ with Qto Header.");
				return false;
			}

			var boqItem = new BoqItemLogic().GetBoqItemTreeById(boqHeaderId, boqItemId);
			if (boqItem == null)
			{
				errorMessage = string.Format("No BoQ Item found.");
				return false;
			}

			if (boqItem.IsItem && boqItem.BoQItems.Any())
			{
				errorMessage = string.Format("BoQ Item is CRB BoQ contains sub quantity items.");
				return false;
			}

			if (boqItem.IsItem || boqItem.BoqLineTypeFk == (int)EBoqLineType.CrbSubQuantity)
			{
				return true;
			}
			else
			{
				errorMessage = string.Format("BoQ Item is not Position.");
				return false;
			}
		}

		/// <summary>
		/// do validation for qtoheader status
		/// </summary>
		/// <param name="qtoHeader"></param>
		/// <param name="errorMessage"></param>
		/// <returns></returns>
		public bool DoQtoHeaderStatusValidate(QtoHeaderEntity qtoHeader, out string errorMessage)
		{
			errorMessage = "";

			if (qtoHeader.QTOStatusFk.HasValue)
			{
				var qtoStatus = new BasicsCustomizeQtOStatusLogic().GetById(qtoHeader.QTOStatusFk.Value);
				if (qtoStatus != null && (qtoStatus as BasicsCustomizeQtOStatusEntity).IsReadOnly)
				{
					errorMessage = string.Format("The Qto Status is readonly for the Qto Header.");
					return false;
				}
			}

			if (qtoHeader.OrdHeaderFk.HasValue)
			{
				ISalesHeaderLogic salesHeaderLogic = Injector.Get<ISalesHeaderLogic>("Contract");
				var ordHeader = salesHeaderLogic.GetSalesHeaderById(qtoHeader.OrdHeaderFk.Value) as ISalesContractHeaderEntity;
				if (ordHeader != null && ordHeader.PrjChangeFk.HasValue)
				{
					var prjChanges = Injector.Get<IChangeMainLogic>("Change.Main.ChangeEntity").GetChangeWithStatusByIds(new List<int> { ordHeader.PrjChangeFk.Value });
					if (prjChanges.Any() && !prjChanges.FirstOrDefault().IsAllowedQtoForSales)
					{
						errorMessage = string.Format("The Header of Project Change Status is not allow Qto for Sales.");
						return false;
					}

				}
			}
			else if (qtoHeader.ConHeaderFk.HasValue)
			{
				IContractHeaderInfoProvider procurementContractLogic = Injector.Get<IContractHeaderInfoProvider>();
				var conHeader = procurementContractLogic.GetConHeaderById(qtoHeader.ConHeaderFk.Value);
				if (conHeader != null && conHeader.ProjectChangeFk.HasValue)
				{
					var prjChanges = Injector.Get<IChangeMainLogic>("Change.Main.ChangeEntity").GetChangeWithStatusByIds(new List<int> { conHeader.ProjectChangeFk.Value });
					if (prjChanges.Any() && !prjChanges.FirstOrDefault().IsAllowedQtoForProc)
					{
						errorMessage = string.Format("The Header of Project Change Status is not allow Qto for Prc.");
						return false;
					}
				}
			}

			return true;
		}

		/// <summary>
		/// find matched qto detail, and exchange to the CrbBoqSubQuantityDetailEntity to return
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <param name="qtoHeaderId">only be used for qto module</param>
		/// <returns></returns>
		public IEnumerable<ICrbBoqSubQuantityDetailEntity> GetCrbQtoDetails(int boqHeaderId, int? qtoHeaderId = null)
		{
			return new QtoDataExchangeLogic().GetCrbQtoDetails(boqHeaderId, qtoHeaderId);
		}

		/// <summary>
		/// import crb boq sub quantity to qto details
		/// </summary>
		/// <param name="boqHeaderId"></param>
		/// <param name="subQuantityDetails"></param>
		/// <param name="targetQtoHeaderId">only be used for qto module</param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		public int? SaveCrbQtoDetails(int boqHeaderId, IEnumerable<ICrbBoqSubQuantityDetailEntity> subQuantityDetails, int? targetQtoHeaderId = null)
		{
			return new QtoDataExchangeLogic().SaveCrbQtoDetails(boqHeaderId, subQuantityDetails, targetQtoHeaderId);
		}

		/// <summary>
		/// Gets the QTO details by BOQ header ID.
		/// </summary>
		/// <param name="boqHeaderId">The BOQ header ID.</param>
		/// <param name="boqItemIds">The BOQ Item IDs (optional).</param>
		/// <param name="qtoHeaderId">The QTO header ID (optional).</param>
		/// <param name="isForRemoval">Is use for delete</param>
		/// <returns>A collection of QTO detail entities.</returns>
		public IEnumerable<QtoDetailEntity> GetQtoDetailsByBoqHeaderId(int boqHeaderId, IEnumerable<int> boqItemIds = null, int? qtoHeaderId = null, bool isForRemoval = false)
		{
			var boqHeaderLogic = new BoqHeaderLogic();
			var boqHeaderContext = qtoHeaderId.HasValue ? null : boqHeaderLogic.GetBoqHeaderContext(boqHeaderId);

			if (boqHeaderContext == null && !qtoHeaderId.HasValue)
			{
				var exception = new Exception("No BoQ header context can be found!");
				throw exception;
			}

			var qtoDetails = new List<QtoDetailEntity>();
			var qtoHeaderIds = new List<int>();
			var qtoHeaderLogic = new QtoHeaderLogic();
			var moduleName = qtoHeaderId.HasValue ? "qto.main" : boqHeaderContext.GetModuleId();
			var moduleHeaderId = qtoHeaderId.HasValue ? qtoHeaderId.Value : boqHeaderContext.ContextMainHeaderId;
			if (moduleName != "boq.project" && moduleName != "qto.main")
			{
				if (boqItemIds != null && boqItemIds.Any())
				{
					var boqItems = new BoqItemLogic().GetBoqItemsByHeaderId(boqHeaderId).Where(e => boqItemIds.Contains(e.Id) && e.BoqItemPrjItemFk.HasValue);
					boqItemIds = boqItems.Select(e => e.BoqItemPrjItemFk.Value).ToList();
				}

				var boqHeader = boqHeaderLogic.GetBoqHeaderByKey(boqHeaderId);
				boqHeaderId = boqHeader.BoqHeaderFk??boqHeaderId;
			}

			switch (moduleName)
			{
				case "boq.project":
					if (isForRemoval)
					{
						qtoHeaderIds = qtoHeaderLogic.GetSearchList(x => x.BoqHeaderFk == boqHeaderId && x.ProjectFk == moduleHeaderId).Select(x => x.Id).ToList();
					}
					else
					{
						qtoHeaderIds = qtoHeaderLogic.GetSearchList(x => x.BoqHeaderFk == boqHeaderId && x.ProjectFk == moduleHeaderId && x.QtoTargetType == (int)QtoTargetType.PrjWqAq).Select(x => x.Id).ToList();
					}

					qtoDetails = this.GetSearchList(x => qtoHeaderIds.Contains(x.QtoHeaderFk)).ToList();
					break;
				case "sales.wip":
					qtoHeaderIds = qtoHeaderLogic.GetSearchList(x => x.BoqHeaderFk == boqHeaderId && x.QtoTargetType == (int)QtoTargetType.WipOrBill).Select(x => x.Id).ToList();
					qtoDetails = this.GetSearchList(x => qtoHeaderIds.Contains(x.QtoHeaderFk) && x.IsIQ && x.WipHeaderFk == moduleHeaderId).ToList();
					break;
				case "sales.billing":
					qtoHeaderIds = qtoHeaderLogic.GetSearchList(x => x.BoqHeaderFk == boqHeaderId && x.QtoTargetType == (int)QtoTargetType.WipOrBill).Select(x => x.Id).ToList();
					qtoDetails = this.GetSearchList(x => qtoHeaderIds.Contains(x.QtoHeaderFk) && x.IsBQ && x.BilHeaderFk == moduleHeaderId).ToList();
					break;
				case "procurement.package":
					if (isForRemoval)
					{
						qtoHeaderIds = qtoHeaderLogic.GetSearchList(x => x.BoqHeaderFk == boqHeaderId).Select(x => x.Id).ToList();
					}
					else
					{
						qtoHeaderIds = qtoHeaderLogic.GetSearchList(x => x.BoqHeaderFk == boqHeaderId && x.QtoTargetType == (int)QtoTargetType.PrcWqAq).Select(x => x.Id).ToList();
					}

					qtoDetails = this.GetSearchList(x => qtoHeaderIds.Contains(x.QtoHeaderFk)).ToList();
					break;
				case "procurement.pes":
					qtoHeaderIds = qtoHeaderLogic.GetSearchList(x => x.BoqHeaderFk == boqHeaderId && x.QtoTargetType == (int)QtoTargetType.PesBoq).Select(x => x.Id).ToList();
					qtoDetails = this.GetSearchList(x => qtoHeaderIds.Contains(x.QtoHeaderFk) && x.PesHeaderFk == moduleHeaderId).ToList();
					break;
				case "qto.main":
					qtoDetails = this.GetSearchList(x => x.QtoHeaderFk == moduleHeaderId).ToList();
					break;
			}

			if (boqItemIds != null && boqItemIds.Any())
			{
				qtoDetails = qtoDetails.Where(x => boqItemIds.Contains(x.BoqItemFk) || (isForRemoval && x.BoqItemReferenceFk.HasValue && boqItemIds.Contains(x.BoqItemReferenceFk.Value))).ToList();
			}

			if (!isForRemoval)
			{
				qtoDetails = qtoDetails.Where(x => !x.IsBlocked && x.QtoLineTypeFk != (int)EQtoLineType.Comment).ToList();
			}

			return qtoDetails;
		}

		/// <summary>
		/// Gets the BoQ item referencing QTO details.
		/// </summary>
		/// <param name="boqHeaderId">The BoQ header ID.</param>
		/// <param name="boqItemIds">The BoQ item IDs.</param>
		/// <returns>A collection of tuples containing the QTO detail reference and BoQ item ID.</returns>
		public IEnumerable<Tuple<String, Int32>> GetBoqItemReferencingQtoDetails(Int32 boqHeaderId, IEnumerable<Int32> boqItemIds)
		{
			var result = new List<Tuple<String, Int32>>();

			var details = GetQtoDetailsByBoqHeaderId(boqHeaderId, boqItemIds, null, true);

			if (details != null && details.Any())
			{
				result = details.Select(q => new Tuple<String, Int32>(ConvertQtoDetailReference(q), q.BoqItemFk)).ToList();
			}

			return result.ToList();
		}

		/// <summary>
		/// Deletes the BoQ item referencing QTO details.
		/// </summary>
		/// <param name="boqHeaderId">The BoQ header ID.</param>
		/// <param name="boqItemIds">The BoQ item IDs.</param>
		public void DeleteBoqItemReferencingQtoDetails(Int32 boqHeaderId, IEnumerable<Int32> boqItemIds)
		{
			var details = GetQtoDetailsByBoqHeaderId(boqHeaderId, boqItemIds, null, true);

			if (details != null && details.Any())
			{
				var groups = details.GroupBy(e => e.QtoHeaderFk).ToDictionary(g => g.Key, g => g.ToList());
				var timestr = new StringBuilder();
				var qtoHeaderLogic = new QtoHeaderLogic();
				foreach (var group in groups)
				{
					var qtoHeaderId = group.Key;
					var toDeleteDetails = group.Value;
					var qtoHeader = qtoHeaderLogic.GetQtoHeaderById(qtoHeaderId) as QtoHeaderEntity;

					QtoDetailHelper detailHelper = new QtoDetailHelper(new List<QtoDetailEntity>(), toDeleteDetails, false, 6);
					detailHelper.SaveWithCheck(qtoHeader.BasGoniometerTypeFk, ref timestr);
				}
			}
		}

		#region publice Api CopySpecialEntityProperties

			/// <summary>
			///
			/// </summary>
			/// <param name="qtoHeader"></param>
			/// <param name="qtoLine"></param>
			/// <param name="source"></param>
		public void CopySpecialEntityProperties(QtoHeaderEntity qtoHeader, QtoDetailEntity qtoLine, JObject source)
		{
			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				#region simple Properties

				// facter
				if (!source.ContainsKey("Factor"))
				{
					qtoLine.Factor = 1;
				}

				// validate for String, cut out for the mapping length
				qtoLine.SpecialUse = !string.IsNullOrEmpty(qtoLine.SpecialUse) ? RIB.Visual.Platform.Common.StringExtension.Truncate(qtoLine.SpecialUse, 16) : "";

				// V
				qtoLine.V = string.IsNullOrEmpty(qtoLine.V) ? null : qtoLine.V;

				// PerformedDate
				if (!source.ContainsKey("PerformedDate"))
				{
					qtoLine.PerformedDate = DateTime.Now;
				}

				#endregion

				// copy the quantity type
				CopyQuantyType(qtoHeader, qtoLine, source);

				// Set Adress With TargetHeader
				var sourceLines = new List<QtoDetailEntity> { qtoLine };
				var qtoSheets = new QtoStructrueLogic().GetSheetListWithStatusAccessRight(new List<int>() { qtoHeader.Id }).ToList();
				var currentQtoLines = this.GetListByQtoHeaderId(qtoHeader.Id).ToList();
				bool isGenerated = !source.ContainsKey("PageNumber");
				SetAdressWithTargetHeader(qtoHeader.Id, sourceLines, out bool isOverflow, currentQtoLines, qtoSheets, isGenerated);

				// Address Overflow
				if (isOverflow)
				{
					throw new ArgumentException("Address Overflow occured.", nameof(qtoLine));
				}

				// exist, Yes: assgin sheet, No: new.
				var sheets = qtoSheets.Where(e => e.PageNumber.HasValue).DistinctBy(e => e.PageNumber.Value);
				var pageNumber2IdTaget = sheets.ToDictionary(e => e.PageNumber.Value, e => e.Id);
				var sheetNo2ToSave = new List<int>();
				assignQtoSheetAndNew(qtoLine, pageNumber2IdTaget, sheetNo2ToSave);

				if (sheetNo2ToSave.Any())
				{
					Dictionary<int, int> newPageNumber2IdTaget = new Dictionary<int, int>();
					new QtoStructrueLogic().CreateByQtoLine(qtoHeader.Id, 0, true, sheetNo2ToSave.Distinct(), 1, true, true, newPageNumber2IdTaget);

					foreach (var sourceLine in sourceLines)
					{
						if (newPageNumber2IdTaget.ContainsKey(sourceLine.PageNumber))
						{
							sourceLine.QtoSheetFk = newPageNumber2IdTaget[sourceLine.PageNumber];
						}
					}
				}

				// qto for boqitem and boq split
				if (!qtoLine.BoqSplitQuantityFk.HasValue)
				{
					AssignBoqQtySplit(qtoLine, currentQtoLines);
				}

				// save clone qto Lines
				StringBuilder timeStr = new StringBuilder();
				QtoDetailHelper detailHelper = new QtoDetailHelper(sourceLines, null, false, qtoHeader.NoDecimals.Value);
				var qtoDataBase = detailHelper.SaveWithCheck(qtoHeader.BasGoniometerTypeFk, ref timeStr);
				var qtoDetailsToSave = qtoDataBase.QtoUpdateDataFromClient.DistinctBy(e => e.Id).ToList();

				qtoLine = qtoDetailsToSave.FirstOrDefault(e => e.Id == qtoLine.Id);

				transaction.Complete();
			}
		}

		/// <summary>
		///
		/// </summary>
		private static void CopyQuantyType(QtoHeaderEntity qtoHeader, QtoDetailEntity qtoLine, JObject source)
		{
			bool isWqAp = qtoHeader.QtoTargetType == (int)QtoTargetType.PrcWqAq || qtoHeader.QtoTargetType == (int)QtoTargetType.PrjWqAq;
			if (isWqAp)
			{
				if (!source.ContainsKey("IsAQ"))
				{
					qtoLine.IsAQ = qtoHeader.IsAQ;
				}

				if (!source.ContainsKey("IsWQ"))
				{
					qtoLine.IsWQ = qtoHeader.IsWQ;
				}

				qtoLine.IsBQ = false;
				qtoLine.IsIQ = false;
			}
			else
			{
				qtoLine.IsAQ = false;
				qtoLine.IsWQ = false;

				if (!source.ContainsKey("IsBQ"))
				{
					qtoLine.IsBQ = qtoHeader.IsBQ;
				}

				if (!source.ContainsKey("IsIQ"))
				{
					qtoLine.IsIQ = qtoHeader.IsIQ;
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		private static void AssignBoqQtySplit(QtoDetailEntity qtoLine, List<QtoDetailEntity> currentQtoLines)
		{
			var lines2BoqItem = currentQtoLines.Where(e => e.BoqItemFk == qtoLine.BoqItemFk);
			if (lines2BoqItem.Any())
			{
				var lines2BoqSplit = lines2BoqItem.Where(e => e.BoqSplitQuantityFk.HasValue);
				if (lines2BoqSplit.Any())
				{
					qtoLine.BoqSplitQuantityFk = lines2BoqSplit.Last().BoqSplitQuantityFk;
				}
			}
			else
			{
				var boqQtySplits = new BoqSplitQuantityLogic().GetList(qtoLine.BoqHeaderFk, qtoLine.BoqItemFk);
				qtoLine.BoqSplitQuantityFk = boqQtySplits.Any() ? boqQtySplits[0].Id : null;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="originalBillId"></param>
		/// <param name="newBillId"></param>
		/// <param name="factor"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public bool CreateQtoLinesForCreditMemo(Int32 originalBillId, Int32 newBillId,Decimal factor = 1)
		{

			try
			{
				bool isOverflow = false;
				StringBuilder timeStr = new StringBuilder();

				//sourceQtoLines :these qto lines have billHeaderFk
				var qtoLinesOfBillingList = this.GetSearchList(e => e.BilHeaderFk == originalBillId).ToList();
				if (qtoLinesOfBillingList.Any())
				{

					var qtoHeaderIds = qtoLinesOfBillingList.Select(e => e.QtoHeaderFk).Distinct().ToList();
					var qtoHeaders = new QtoHeaderLogic().GetItemsByKey(qtoHeaderIds);

					List<QtoCommentEntity> qtoCommentsToSave = new List<QtoCommentEntity>();
					List<QtoDetailCommentsEntity> qtoDetailCommentsToSave = new List<QtoDetailCommentsEntity>();
					List<QtoDetailDocumentEntity> qtoDetailDocumentsToSave = new List<QtoDetailDocumentEntity>();
					List<MainItem2CostGroupEntity> costGroupsToSave = new List<MainItem2CostGroupEntity>();


					var costGroupLogic = new MainItem2CostGroupLogic("QTO_DETAIL2COSTGRP");
					List<MainItem2CostGroupEntity> sourceLine2CostGroups = null;
					var sheetNo2ToSave = new List<int>();
					var qtoSheetLogic = new QtoStructrueLogic();


					var defaultLineTypeFk = -1;
					var defaultLineType = new QtoLineTypeLogic().GetDefault();
					if (defaultLineType != null)
					{
						defaultLineTypeFk = defaultLineType.Id;
					}

					var allQtoLines = this.GetSearchList(e => qtoHeaderIds.Contains(e.QtoHeaderFk)).ToList();
					allQtoLines = allQtoLines.OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList().ToList();

					var allQtoSheets = new QtoStructrueLogic().GetSheetListWithStatusAccessRight(qtoHeaderIds).ToList();

					var sourceIds = qtoLinesOfBillingList.Select(e => e.Id).ToList();
					var ids = SequenceManager.GetNextList("QTO_DETAIL", sourceIds.Count);
					var old2NewIds = new Dictionary<int, int>();

					for (int i = 0; i < sourceIds.Count; i++)
					{
						if (!old2NewIds.ContainsKey(sourceIds[i]))
						{
							old2NewIds.Add(sourceIds[i], ids[i]);
						}
					}

					foreach (var qtoHeaderId in qtoHeaderIds)
					{
						var currentQtoHeader = qtoHeaders.FirstOrDefault(e => e.Id == qtoHeaderId);

						//qto by qtoHeaderId
						var sourceQtoLines = allQtoLines.Where(e => e.QtoHeaderFk == qtoHeaderId).ToList();
						var qtoSheets = allQtoSheets.Where(e => e.QtoHeaderFk == qtoHeaderId).ToList();

						//source qtolines wihch have bil_header_Fk
						var sourceBillingQtoLines = qtoLinesOfBillingList.Where(e => e.BilHeaderFk.HasValue).ToList();
						var cloneLines = sourceBillingQtoLines.Select(e => e.Clone() as QtoDetailEntity).ToList();

						if (sourceQtoLines.Any() && sourceBillingQtoLines.Any())
						{
							SetValueReferenceAsValue(sourceQtoLines, sourceBillingQtoLines);
							SetAdressWithTargetHeader(qtoHeaderId, cloneLines, out isOverflow, sourceQtoLines, qtoSheets);

							var sheets = qtoSheets.Where(e => e.PageNumber.HasValue).DistinctBy(e => e.PageNumber.Value);
							var pageNumber2IdTaget = sheets.ToDictionary(e => e.PageNumber.Value, e => e.Id);

							for (int i = 0; i < cloneLines.Count; i++)
							{
								var cloneLine = cloneLines[i];
								int sourceId = cloneLine.Id;
								cloneLine.Id = old2NewIds[sourceId];
								cloneLine.Factor = cloneLine.Factor * factor;
								cloneLine.BilHeaderFk = newBillId;
								cloneLine.Version = 0;
								cloneLine.IsCalculated = true;

								assignQtoSheetAndNew(cloneLine, pageNumber2IdTaget, sheetNo2ToSave);

								if (sourceLine2CostGroups == null)
								{
									sourceLine2CostGroups = costGroupLogic.GetByFilter(e => sourceIds.Contains(e.MainItemId.Value)).ToList();
								}

								AddQtoLines2CostGroup(sourceId, cloneLine.Id, sourceLine2CostGroups, costGroupsToSave);
							}

							QtoDetailHelper detailHelper = new QtoDetailHelper(cloneLines, null, false, currentQtoHeader.NoDecimals.Value);
							var qtoDataBase = detailHelper.SaveWithCheck(currentQtoHeader.BasGoniometerTypeFk, ref timeStr);
							var qtoDetailsToSave = qtoDataBase.QtoUpdateDataFromClient.DistinctBy(e => e.Id).OrderBy(e => e.PageNumber).ThenBy(e => e.LineReference).ThenBy(e => e.LineIndex).ToList();

							// update the group info
							detailHelper.UpdateQtoDetailGroupInfo(qtoDetailsToSave);
						}

						if (sheetNo2ToSave.Any())
						{
							Dictionary<int, int> newPageNumber2IdTaget = new Dictionary<int, int>();
							qtoSheetLogic.CreateByQtoLine(qtoHeaderId, 0, true, sheetNo2ToSave.Distinct(), 1, true, true, newPageNumber2IdTaget);
							foreach (var cloneLine in cloneLines)
							{
								if (newPageNumber2IdTaget.ContainsKey(cloneLine.PageNumber))
								{
									cloneLine.QtoSheetFk = newPageNumber2IdTaget[cloneLine.PageNumber];
								}
							}
						}
					}

					// save qto lines to cost group
					if (costGroupsToSave.Any())
					{
						var newIds = this.SequenceManager.GetNextList("QTO_DETAIL2COSTGRP", costGroupsToSave.Count);
						for (int i = 0; i < newIds.Count; i++)
						{
							costGroupsToSave[i].Id = newIds[i];
						}
						costGroupsToSave = costGroupLogic.Save(costGroupsToSave).ToList();
					}

				}

				return true;

			}
			catch
			{
				return false;
			}
		}


		#endregion

		/// <summary>
		///
		/// </summary>
		/// <param name="wipId"></param>
		/// <returns></returns>
		public IEnumerable<QtoHeaderEntity> GetQtoHeadersByWipId(int wipId)
		{
			using (var dbcontext = new RIB.Visual.Platform.BusinessComponents.DbContext(GetDbModel()))
			{
				var qtoHeaderIds = dbcontext.Entities<QtoDetailEntity>().Where(d => d.WipHeaderFk == wipId).Select(d => d.QtoHeaderFk).Distinct().ToList();

				var qtoHeaders = new QtoHeaderLogic().GetItemsByKey(qtoHeaderIds);

				return qtoHeaders;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="bilHeaderFk"></param>
		/// <returns></returns>
		public IEnumerable<QtoHeaderEntity> GetQtoHeadersByBillId(int bilHeaderFk)
		{
			using (var dbcontext = new RIB.Visual.Platform.BusinessComponents.DbContext(GetDbModel()))
			{
				var qtoHeaderIds = dbcontext.Entities<QtoDetailEntity>().Where(d => d.BilHeaderFk == bilHeaderFk).Select(d => d.QtoHeaderFk).Distinct().ToList();

				var qtoHeaders = new QtoHeaderLogic().GetItemsByKey(qtoHeaderIds);

				return qtoHeaders;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="billIds"></param>
		/// <returns></returns>
		public List<int> GetCorrectionBillFks(List<int> billIds)
		{
			var result = new List<int>();
			var basicsCustomizeBillTypeLogic = new BasicsCustomizeBillTypeLogic();
			var ids = basicsCustomizeBillTypeLogic.GetByFilter(e => e.IsCreditMemo).Select(e => e.Id);
			if (ids.Any() && billIds.Any())
			{
				var salesBillingLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesBillingLogic>("bill");
				result = salesBillingLogic.GetSalesBillingsByIds(billIds).Where(e => ids.Contains(e.TypeFk)).CollectIds(e => e.Id).ToList();
			}
			return result;
		}

		 
	}
}

