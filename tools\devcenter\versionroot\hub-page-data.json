{"title": "%%versionTitle%% Resources", "description": "These are all resources available for RIB 4.0 %%versionTitle%% (%%versionName%%).", "tiles": [{"id": "instance", "title": "RIB 4.0 %%versionTitle%%", "description": "The executable instance of RIB 4.0", "icon": "hub-page-media/rib40.svg", "url": "https://apps-int.itwo40.eu/itwo40/daily/client/#/", "color": "rgb(64,183,208,0.9);"}, {"id": "instanceNG", "title": "RIB 4.0 %%versionTitle%% (TS)", "description": "The new TypeScript/Angular front-end of RIB 4.0", "icon": "hub-page-media/rib40.svg", "url": "https://apps-int.itwo40.eu/itwo40/daily/frontend/#/", "color": "rgb(48,172,197,0.9);"}, {"id": "gitlog", "title": "Git Changelog", "description": "All recent changes that are a part of this version, extracted from Git", "icon": "hub-page-media/changelog.svg", "url": "https://devcenter.itwo40.eu/ccnet-data/versions/25.3/gitlog/", "color": "rgb(42,151,172,0.9);"}, {"id": "relnotes", "title": "Release Notes Documents", "description": "Release Notes, What's New, etc. with all entries relevant for this version", "icon": "hub-page-media/relnotes.svg", "url": "relnotes", "color": "rgb(48,172,197,0.9);"}, {"id": "typedoc", "title": "Typedoc", "description": "Source code documentation for the client-side code", "icon": "hub-page-media/logo-typedoc-monochrome.svg", "url": "docs/typedoc", "color": "rgb(111,202,220,0.9);"}, {"id": "storybook", "title": "Storybook", "description": "Documentation and live testing of Angular UI components", "icon": "hub-page-media/logo-storybook-monochrome.svg", "url": "docs/storybook", "color": "rgb(35,126,144,0.9);"}, {"id": "viewertest-web", "title": "3D Viewer Test on Web Server", "description": "The 3D viewer test page on the web server", "icon": "hub-page-media/model.svg", "url": "https://apps-int.itwo40.eu/itwo40/daily/model-server/viewertest/", "color": "rgb(91,194,215,0.9);"}, {"id": "viewertest-render", "title": "3D Viewer Test on Render Server", "description": "The 3D viewer test page on the render server", "icon": "hub-page-media/model.svg", "url": "https://ribprdrdragw001.itwo40.eu/itwo40/daily/renderservices/model-server/viewertest/", "color": "rgb(40,143,164,0.9);"}]}