using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.Linq;
using System.Numerics;
using Microsoft.Graph;
using Microsoft.SharePoint.News.DataModel;
using Newtonsoft.Json;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.BulkExpressions.Criteria.CriteriaToExpression;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Documents.Project.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.OperationalManagement;
using static System.Diagnostics.Activity;
using NLS = RIB.Visual.Documents.Project.Localization.Properties.Resources;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
#pragma warning disable S3267 // Loops should be simplified with "LINQ" expressions
namespace RIB.Visual.Documents.Project.BusinessComponents
{
	/// <summary>
	///
	/// </summary>
	public class DocSpprjDetailStructLogic : DocSpCommonLogic<DocSpprjDetailStructEntity>
	{

		private DocSpprjFolderStructLogic docSpprjFolderStructLogic = new DocSpprjFolderStructLogic();
		private DocSpprjFolderSettingLogic docSpprjFolderSettingLogic = new DocSpprjFolderSettingLogic();
		private DocMetadata2extEntityLogic docMetadata2ExtEntityLogic = new DocMetadata2extEntityLogic();
		private BasicsCustomizeProjectDocumentCategory2TypeLogic documentCategory2TypeLogic = new BasicsCustomizeProjectDocumentCategory2TypeLogic();

		private List<BasicsCustomizeRubricCategoryEntity> RubricCatagoryList;
		private List<BasicsCustomizeProjectDocumentCategoryEntity> PrjDocCatagoryList;
		private List<DocumentTypeEntity> PrjDocTypeList;
		private List<BasicsCustomizeProjectDocumentCategory2TypeEntity> prjDocCategory2TypeList;

		/// <summary>
		/// key: level, value: detailstruc in level
		/// </summary>
		private Dictionary<int, List<DocSpprjDetailStructEntity>> DetailStrucDic = new Dictionary<int, List<DocSpprjDetailStructEntity>>();

		private Dictionary<int, Dictionary<int, string>> levelMetaTypeDataDic = new Dictionary<int, Dictionary<int, string>>();

		/// <summary>
		/// GetEntityTableName
		/// </summary>
		/// <returns></returns>
		protected override string GetEntityTableName()
		{
			return "PRJ_DOC_SPPRJDETAILSTRUCT";
		}

		/// <summary>
		/// get data from DocSpprjDetailStruct
		/// </summary>
		/// <param name="fsId">folder setting id</param>
		/// <param name="isGetNew">reget the entire detail struct</param>
		/// <param name="isUpdateToNew">base the old detail struct, add new metadate</param>
		/// <param name="isUseFakeId">use fake id in ui before saving</param>
		/// <returns></returns>
		public IEnumerable<DocSpprjDetailStructEntity> GetDetailStrucByFs(int fsId, bool isGetNew = false, bool isUpdateToNew = false, bool isUseFakeId = false)
		{
			IEnumerable<DocSpprjDetailStructEntity> detailStrucs = null;
			var folderStructs = docSpprjFolderStructLogic.GetByFilter(e => e.PrjDocSpprjFolderSettingFk == fsId).OrderBy(e => e.Level);
			if (!folderStructs.Any())
			{
				throw new BusinessLayerException(NLS.ERR_PrjDocSpNoFolderSetting);
			}

			DetailStrucDic.Clear();
			detailStrucs = GetListByFilter(e => e.PrjDocSpprjFolderSettingFk == fsId);
			if (isGetNew || !detailStrucs.Any())
			{
				return GetDefaultDetailStruc(folderStructs, fsId, isUseFakeId);
			}
			else
			{
				var level2MetaTypeDic = detailStrucs.DistinctBy(e => e.Level).ToDictionary(e => e.Level, e => e.MetadataType);
				levelMetaTypeDataDic = GetMetaTypeDataByLevelMetaType(level2MetaTypeDic);
				List<FolderStruct> folderTreeStruct = null;
				List<int> newMetadataIds = null;
				if (isUpdateToNew)
				{
					folderTreeStruct = new List<FolderStruct>();
					var entireCount = GetFolderStructs(level2MetaTypeDic.Values.Select(e => (MetaDataType)e).ToList(), folderTreeStruct);
					int lackMetadataCount = entireCount - detailStrucs.Count();

					if (lackMetadataCount > 0)
					{
						newMetadataIds = isUseFakeId ? GetFakeIds(lackMetadataCount) : GetNextSequences(lackMetadataCount);

						FillLackDetailStruct(fsId, newMetadataIds, folderTreeStruct, ref detailStrucs, 0, folderStructs.ToList());
					}
				}

				List<DocSpprjDetailStructEntity> result = new List<DocSpprjDetailStructEntity>();
				GetDBDetailStruc(fsId, level2MetaTypeDic, 0, detailStrucs, ref result, null);
				return result;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="fsId"></param>
		/// <param name="level2MetaTypeDic"></param>
		/// <param name="curLevel"></param>
		/// <param name="detailStructs"></param>
		/// <param name="result"></param>
		/// <param name="parentId"></param>
		/// <returns></returns>
		public void GetDBDetailStruc(int fsId, Dictionary<int, int> level2MetaTypeDic, int curLevel, IEnumerable<DocSpprjDetailStructEntity> detailStructs,
					ref List<DocSpprjDetailStructEntity> result, int? parentId = null)
		{
			int maxLevel = level2MetaTypeDic.Max(e => e.Key);
			if (curLevel > maxLevel)
			{
				return;
			}

			var levelDatas = detailStructs.Where(e => e.Level == curLevel);
			if (null != parentId)
			{
				levelDatas = levelDatas.Where(e => e.ObjectParentFk.HasValue && e.ObjectParentFk.Value == parentId.Value);
			}

			var metaTypeDatas = levelMetaTypeDataDic[curLevel];
			foreach (var levelData in levelDatas)
			{
				if (metaTypeDatas.ContainsKey(levelData.Objectid))
				{
					levelData.ObjectDescription = metaTypeDatas[levelData.Objectid];
				}

				if (!string.IsNullOrWhiteSpace(levelData.SpUsers))
				{
					levelData.UsersAssigned = JsonConvert.DeserializeObject<List<AadUser>>(levelData.SpUsers).Select(e => e.Id);
				}

				result.Add(levelData);
				GetDBDetailStruc(fsId, level2MetaTypeDic, curLevel + 1, detailStructs, ref result, levelData.Id);
			}
		}

		private void FillLackDetailStruct(int fsId, List<int> newMetaDataIds, List<FolderStruct> folderStructList, ref IEnumerable<DocSpprjDetailStructEntity> detailStructs,
				int curLevel, List<DocSpprjFolderStructEntity> folderStructEntities, DocSpprjDetailStructEntity parentDetailStruct = null)
		{
			var curLevelFolderStructEntity = folderStructEntities.FirstOrDefault(e => e.Level == curLevel);
			if (null == curLevelFolderStructEntity)
			{
				return;
			}

			foreach (var folderStruct in folderStructList)
			{
				var mapDS = detailStructs.Where(e => e.Level == folderStruct.Level && e.MetadataType == (int)folderStruct.Type && e.Objectid == folderStruct.Id);
				if (null != parentDetailStruct)
				{
					mapDS = mapDS.Where(e => null != e.ObjectParentFk && e.ObjectParentFk == parentDetailStruct.Id);
				}
				var detailStruct = mapDS.FirstOrDefault();
				if (null == detailStruct)
				{
					detailStruct = new DocSpprjDetailStructEntity()
					{
						Id = SeqPop(newMetaDataIds),
						Level = curLevel,
						ShareOption = 0,
						MetadataType = (int)folderStruct.Type,
						ObjectParentFk = null == parentDetailStruct? null: parentDetailStruct.Id,
						PrjDocSpprjFolderSettingFk = fsId,
						Objectid = folderStruct.Id,
						ObjectDescription = folderStruct.Description,
						SpUsers = curLevelFolderStructEntity.SpUsers
					};
					detailStructs = detailStructs.Append(detailStruct);
				}

				FillLackDetailStruct(fsId, newMetaDataIds, folderStruct.Children,ref detailStructs, curLevel + 1, folderStructEntities, detailStruct);
			}
		}		

		/// <summary>
		/// 
		/// </summary>
		/// <param name="folderStructEntities"></param>
		/// <param name="fsId"></param>
		/// <param name="isUseFakeId"></param>
		/// <returns></returns>
		public IEnumerable<DocSpprjDetailStructEntity> GetDefaultDetailStruc(IEnumerable<DocSpprjFolderStructEntity> folderStructEntities, int fsId = -1, bool isUseFakeId = false)
		{
			List<DocSpprjDetailStructEntity> result = new List<DocSpprjDetailStructEntity>();

			var levelMetadataTypeList = folderStructEntities.OrderBy(e => e.Level).Select(e => (MetaDataType)e.MetaDataType).ToList();
			List<FolderStruct> folderStructList = new List<FolderStruct>();
			var newCount = GetFolderStructs(levelMetadataTypeList, folderStructList, true);

			var ids = isUseFakeId ? GetFakeIds(newCount) : GetNextSequences(newCount);

			GenerateDetailStruc(0, ids, folderStructList, folderStructEntities, fsId, ref result, null);

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="CurLevel"></param>
		/// <param name="ids"></param>
		/// <param name="folderStructList"></param>
		/// <param name="folderStructEntities"></param>
		/// <param name="fsId"></param>
		/// <param name="result"></param>
		/// <param name="parentId"></param>
		public void GenerateDetailStruc(int CurLevel, List<int> ids, List<FolderStruct> folderStructList, IEnumerable<DocSpprjFolderStructEntity> folderStructEntities,
			 int fsId, ref List<DocSpprjDetailStructEntity> result, int? parentId = null)
		{
			var levelMetadataType = folderStructEntities.FirstOrDefault(e => e.Level == CurLevel);
			if (null == levelMetadataType){
				return;
			}

			foreach (var item in folderStructList)
			{
				List<string> userAssigned = new List<string>();
				if (fsId != -1 && !string.IsNullOrWhiteSpace(levelMetadataType.SpUsers))
				{
					userAssigned = JsonConvert.DeserializeObject<List<AadUser>>(levelMetadataType.SpUsers).Select(e => e.Id).ToList();
				}

				var id = SeqPop(ids);

				var detailStruct = new DocSpprjDetailStructEntity()
				{
					Id = id,
					MetadataType = (int)item.Type,
					Objectid = item.Id,
					ObjectParentFk = parentId,
					ObjectDescription = item.Description,
					ShareOption = levelMetadataType.ShareOption,
					SpUsers = levelMetadataType.SpUsers,
					Level = levelMetadataType.Level,									
					PrjDocSpprjFolderSettingFk = fsId
				};
				if (userAssigned.Any())
				{
					detailStruct.UsersAssigned = userAssigned;
				}

				result.Add(detailStruct);

				if (item.Children.Any())
				{
					var childrenLevel = CurLevel + 1;
					GenerateDetailStruc(childrenLevel, ids, item.Children, folderStructEntities, fsId, ref result, id);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="CurLevel"></param>
		/// <param name="ids"></param>
		/// <param name="result"></param>
		/// <param name="parentId"></param>
		/// <param name="fsId"></param>
		public void GenerateDetailStruc(int CurLevel, List<int> ids, ref List<DocSpprjDetailStructEntity> result, int? parentId = null, int? fsId = null)
		{
			if (CurLevel == DetailStrucDic.Count)
			{
				return;
			}

			foreach (var item in DetailStrucDic[CurLevel])
			{
				int id = SeqPop(ids);
				var newItem = CopyToNew(item);
				newItem.Id = id;
				newItem.ObjectParentFk = parentId;
				newItem.PrjDocSpprjFolderSettingFk = fsId.HasValue ? fsId.Value : item.PrjDocSpprjFolderSettingFk;
				result.Add(newItem);

				GenerateDetailStruc(CurLevel + 1, ids, ref result, id);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="id"></param>
		/// <returns></returns>
		public DocSpprjDetailStructEntity CopyToNew(DocSpprjDetailStructEntity entity, int? id = null)
		{
			DocSpprjDetailStructEntity newEntity = new DocSpprjDetailStructEntity()
			{
				MetadataType = entity.MetadataType,
				Objectid = entity.Objectid,
				ObjectDescription = entity.ObjectDescription,
				ShareOption = entity.ShareOption,
				SpUsers = entity.SpUsers,
				Level = entity.Level,
				PrjDocSpprjFolderSettingFk = entity.PrjDocSpprjFolderSettingFk,
				UsersAssigned = entity.UsersAssigned,
			};
			if (id.HasValue)
			{
				newEntity.Id = id.Value;
			}

			return newEntity;
		}

		/// <summary>
		/// levelMetaTypes: key is Level, value is MetaType
		/// </summary>
		/// <param name="levelMetaTypes"></param>
		/// <returns></returns>
		public Dictionary<int, Dictionary<int, string>> GetMetaTypeDataByLevelMetaType(Dictionary<int, int> levelMetaTypes)
		{
			Dictionary<int, Dictionary<int, string>> dic = new Dictionary<int, Dictionary<int, string>>();
			foreach (var kv in levelMetaTypes)
			{
				var metaTypes = GetMetaTypeDataDic((MetaDataType)kv.Value, true, true);
				dic.Add(kv.Key, metaTypes);
			}
			return dic;
		}

		/// <summary>
		/// metaDataTypes: must order by level
		/// </summary>
		/// <param name="metaDataTypes"></param>
		/// <param name="result"></param>
		/// <param name="isTranslate"></param>
		public int GetFolderStructs(List<MetaDataType> metaDataTypes, List<FolderStruct> result = null, bool isTranslate = true)
		{
			if (!metaDataTypes.Any())
			{
				return 0;
			}

			if (null == result)
			{
				result = new List<FolderStruct>();
			}

			prjDocCategory2TypeList = documentCategory2TypeLogic.GetByFilter(e => true).ToList();
			RubricCatagoryList = GetRubricCatagorytMetadata().ToList();
			PrjDocCatagoryList = GetPrjDocCategoryMetadata().ToList();
			var validRubriCataoryIds = PrjDocCatagoryList.CollectIds(e => e.RubricCategoryFk);
			RubricCatagoryList = RubricCatagoryList.Where(e => validRubriCataoryIds.Contains(e.Id)).ToList();
			PrjDocTypeList = GetDocTypeMetadata().ToList();

			var rootMetadataType = metaDataTypes[0];
			var level0Metadata = GetMetaTypeDataDic(rootMetadataType, isTranslate).Select(e => new FolderStruct()
			{
				Id = e.Key,
				Type = rootMetadataType,
				Level = 0,
				Description = e.Value
			});

			if (rootMetadataType == MetaDataType.RubricCategory)
			{
				level0Metadata = level0Metadata.Where(e => validRubriCataoryIds.Contains(e.Id));
			}			

			int metadataCount = level0Metadata.Count();

			foreach (var level0FolderConfig in level0Metadata)
			{
				if (metaDataTypes.Count > level0FolderConfig.Level + 1)
				{
					var level1metaDataType = metaDataTypes[level0FolderConfig.Level + 1];
					GetMapChildMetadata(rootMetadataType, level1metaDataType, level0FolderConfig, ref metadataCount);
					foreach (var level1FolderConfig in level0FolderConfig.Children)
					{
						if (metaDataTypes.Count > level1FolderConfig.Level + 1)
						{
							var level2metaDataType = metaDataTypes[level1FolderConfig.Level + 1];
							GetMapChildMetadata(level1metaDataType, level2metaDataType, level1FolderConfig, ref metadataCount, true, level0FolderConfig.Id);
						}
						
					}
				}
				result.Add(level0FolderConfig);
			}

			return metadataCount;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="parentMetadataType"></param>
		/// <param name="childMetadataType"></param>
		/// <param name="parentFolder"></param>
		/// <param name="isTranslated"></param>
		/// <param name="rootId"></param>
		/// <param name="metadataCount"></param>
		private void GetMapChildMetadata(MetaDataType parentMetadataType, MetaDataType childMetadataType, FolderStruct parentFolder, ref int metadataCount, bool isTranslated = true,
			int? rootId = null)
		{
			Func<DescriptionTranslateType, string> func = (descriptionInfo) =>
			{
				return isTranslated ? descriptionInfo.Translated : descriptionInfo.Description;
			};

			var parentId = parentFolder.Id;
			IEnumerable<int> childrenIds = null;

			Dictionary<int, string> childrenDic = new Dictionary<int, string>();
			List<int> rootFilterIds = new List<int>();
			if (parentMetadataType == MetaDataType.RubricCategory)
			{
				if (childMetadataType == MetaDataType.ProjectDocumentCategory)
				{
					childrenDic = PrjDocCatagoryList.Where(e => e.RubricCategoryFk == parentId).ToDictionary(e => e.Id, e => func(e.DescriptionInfo));
					if (rootId.HasValue)
					{
						rootFilterIds = prjDocCategory2TypeList.Where(e => e.DocumentTypeFk == rootId.Value).CollectIds(e => e.DocumentCategoryFk).ToList();						
					}
				}
				else if (childMetadataType == MetaDataType.ProjectDocumentType)
				{
					childrenDic = PrjDocTypeList.ToDictionary(e => e.Id, e => func(e.DescriptionInfo));
					if (rootId.HasValue)
					{
						rootFilterIds = prjDocCategory2TypeList.Where(e => e.DocumentCategoryFk == rootId.Value).CollectIds(e => e.DocumentTypeFk).ToList();
					}
				}
			}
			else if (parentMetadataType == MetaDataType.ProjectDocumentCategory)
			{
				if (childMetadataType == MetaDataType.RubricCategory)
				{
					childrenIds = PrjDocCatagoryList.Where(e => e.Id == parentId).CollectIds(e => e.RubricCategoryFk);
					childrenDic = RubricCatagoryList.Where(e => childrenIds.Contains(e.Id)).ToDictionary(e => e.Id, e => func(e.DescriptionInfo));		
				}
				else if (childMetadataType == MetaDataType.ProjectDocumentType)
				{
					childrenIds = prjDocCategory2TypeList.Where(e => e.DocumentCategoryFk == parentId).CollectIds(e => e.DocumentTypeFk);
					if (null == childrenIds || !childrenIds.Any())
					{
						childrenDic = PrjDocTypeList.ToDictionary(e => e.Id, e => func(e.DescriptionInfo));
					}
					else
					{
						childrenDic = PrjDocTypeList.Where(e => childrenIds.Contains(e.Id)).ToDictionary(e => e.Id, e => func(e.DescriptionInfo));
					}
				}
			}
			else if (parentMetadataType == MetaDataType.ProjectDocumentType)
			{
				if (childMetadataType == MetaDataType.RubricCategory)
				{
					childrenDic = RubricCatagoryList.ToDictionary(e => e.Id, e => func(e.DescriptionInfo));
					if (rootId.HasValue)
					{
						rootFilterIds = PrjDocCatagoryList.Where(e => e.Id == rootId).CollectIds(e => e.RubricCategoryFk).ToList();
					}
				}
				else if (childMetadataType == MetaDataType.ProjectDocumentCategory)
				{
					childrenIds = prjDocCategory2TypeList.Where(e => e.DocumentTypeFk == parentId).CollectIds(e => e.DocumentCategoryFk);
					if (null == childrenIds || !childrenIds.Any())
					{
						childrenDic = PrjDocCatagoryList.ToDictionary(e => e.Id, e => func(e.DescriptionInfo));
					}
					else
					{
						childrenDic = PrjDocCatagoryList.Where(e => childrenIds.Contains(e.Id)).ToDictionary(e => e.Id, e => func(e.DescriptionInfo));
					}

					if (rootId.HasValue)
					{
						rootFilterIds = PrjDocCatagoryList.Where(e => null != e.RubricCategoryFk && e.RubricCategoryFk == rootId).CollectIds(e => e.Id).ToList();
					}
				}
			}			

			if (childrenDic.Any())
			{
				if (rootFilterIds.Any())
				{
					childrenDic = childrenDic.Where(e => rootFilterIds.Contains(e.Key)).ToDictionary(e => e.Key, e => e.Value);
				}

				parentFolder.Children.AddRange(childrenDic.Select(e => new FolderStruct()
				{
					Id = e.Key,
					Level = parentFolder.Level + 1,
					Type = childMetadataType,
					Description = e.Value
				}));

				metadataCount += childrenDic.Count;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="DetailStrucs"></param>
		/// <param name="IsAllProjects"></param>
		/// <returns></returns>
		public bool SaveDetailStructs(IEnumerable<DocSpprjDetailStructEntity> DetailStrucs, bool IsAllProjects = false)
		{
			List<int> fsIds = new List<int>();
			List<int> otherFsIds = new List<int>();

			var otherFolderStrucs = new List<DocSpprjFolderStructEntity>();
			var otherFolderSettings = new List<DocSpprjFolderSettingEntity>();

			int curFsId = DetailStrucs.First().PrjDocSpprjFolderSettingFk;

			if (IsAllProjects)
			{
				var docMetadata2extEntityLogic = new DocMetadata2extEntityLogic();
				var extPrjIds = docMetadata2extEntityLogic.GetMetadata2extDicByType(MetaDataType.Project).Keys.ToList();
				var companyInfo = GetCurrentCompany();
				var projectLogic = Injector.Get<IGetProjectLogic>();
				var projectIds = projectLogic.GetProjectsByCompany(companyInfo.Id).CollectIds(e => e.Id);
				extPrjIds = extPrjIds.Where(e => projectIds.Contains(e)).ToList();

				DocSpProjectConfigLogic docSpProjectConfigLogic = new DocSpProjectConfigLogic();
				otherFsIds = docSpProjectConfigLogic.GetByFilter(e => extPrjIds.Contains(e.PrjProjectFk) && e.PrjDocSpprjFolderSettingFk.HasValue && e.PrjDocSpprjFolderSettingFk != curFsId)
					.CollectIds(e => e.PrjDocSpprjFolderSettingFk).ToList();

				if (otherFsIds.Any())
				{
					List<DocSpprjDetailStructEntity> otherFsDetailStrucs = new List<DocSpprjDetailStructEntity>();

					var curFolderStruc = docSpprjFolderStructLogic.GetByFilter(e => e.PrjDocSpprjFolderSettingFk == curFsId).ToList();

					var templateName = docSpprjFolderSettingLogic.GetItemByKey(curFsId).Name;

					var detailStrucIdsCount = otherFsIds.Count * DetailStrucs.Count();
					var detailStrucids = GetNextSequences(detailStrucIdsCount);

					var folderStrucIdsCount = otherFsIds.Count * curFolderStruc.Count;
					var folderStrucIds = docSpprjFolderStructLogic.GetNextSequences(folderStrucIdsCount);

					var toSave = DeepCopy(DetailStrucs);

					DetailStrucDic = DetailStrucs.GroupBy(e => e.Level).ToDictionary(e => e.Key, e => e.ToList());

					foreach (var fsId in otherFsIds)
					{
						var otherfolderSetting = docSpprjFolderSettingLogic.GetItemByKey(fsId);
						otherfolderSetting.Name = templateName;
						otherFolderSettings.Add(otherfolderSetting);

						var otherFsDetailStruc = DeepCopy(DetailStrucs);
						foreach (var item in otherFsDetailStruc)
						{
							var newId = SeqPop(detailStrucids);
							var oldId = item.Id;
							item.Id = newId;
							item.PrjDocSpprjFolderSettingFk = fsId;

							var itemChildren = otherFsDetailStruc.Where(e => null != e.ObjectParentFk && e.ObjectParentFk == oldId);
							foreach (var child in itemChildren)
							{
								child.ObjectParentFk = newId;
							}
						}

						otherFsDetailStrucs.AddRange(otherFsDetailStruc);

						foreach (var fs in curFolderStruc)
						{
							DocSpprjFolderStructEntity docSpprjFolderStructEntity = new DocSpprjFolderStructEntity()
							{
								Id = SeqPop(folderStrucIds),
								PrjDocSpprjFolderSettingFk = fsId,
								MetaDataType = fs.MetaDataType,
								ShareOption = fs.ShareOption,
								SpUsers = fs.SpUsers,
								Level = fs.Level,
							};
							otherFolderStrucs.Add(docSpprjFolderStructEntity);
						}
					}

					DetailStrucs = DetailStrucs.Concat(otherFsDetailStrucs);

					fsIds.AddRange(otherFsIds);
				}
			}

			var detailStrucsWithFakeId = DetailStrucs.Where(e => e.Id < 0).ToList();
			var detailCount = detailStrucsWithFakeId.Count;
			var defaultId2DetailIdMap = new Dictionary<string, int>();
			var detailStrucIds = detailCount > 0 ?
				GetNextSequences(detailCount).GetEnumerator() : new List<int>().GetEnumerator();
			foreach (var item in detailStrucsWithFakeId)
			{
				var fakeId = item.Id;
				var fakeIdKey = GetSettingIdStructureIdKey(fakeId, item.PrjDocSpprjFolderSettingFk);
				detailStrucIds.MoveNext();
				item.Id = detailStrucIds.Current;
				if (!defaultId2DetailIdMap.ContainsKey(fakeIdKey))
				{
					defaultId2DetailIdMap.Add(fakeIdKey, item.Id);
				}
			}

			foreach (var item in detailStrucsWithFakeId)
			{
				if (item.ObjectParentFk.HasValue && item.ObjectParentFk.Value < 0)
				{
					var fakeIdKey = GetSettingIdStructureIdKey(item.ObjectParentFk.Value, item.PrjDocSpprjFolderSettingFk);
					if (defaultId2DetailIdMap.TryGetValue(fakeIdKey, out var detailId))
					{
						item.ObjectParentFk = detailId;
					}
				}
			}

			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				if (otherFsIds.Any())
				{
					docSpprjFolderStructLogic.DeleteFolderStrucByFs(otherFsIds);
				}

				fsIds.Add(curFsId);
				DeleteDetailStrucByFs(fsIds);

				if (otherFolderStrucs.Any())
				{
					docSpprjFolderStructLogic.Save(otherFolderStrucs);
				}

				if (otherFolderSettings.Any())
				{
					docSpprjFolderSettingLogic.Save(otherFolderSettings);
				}

				Save(DetailStrucs);

				transaction.Complete();
			}

			return true;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="fsIds"></param>
		public void DeleteDetailStrucByFs(IEnumerable<int> fsIds)
		{
			var entities = GetByFsIds(fsIds);
			Delete(entities);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="fsIds"></param>
		/// <returns></returns>
		public IEnumerable<DocSpprjDetailStructEntity> GetByFsIds(IEnumerable<int> fsIds)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				return dbcontext.Entities<DocSpprjDetailStructEntity>().Where(e => fsIds.Contains(e.PrjDocSpprjFolderSettingFk)).ToList();

			}
		}

		/// <summary>
		/// key: PrjDocSpprjFolderSettingFk, value: displayName or id collection of SpUsers
		/// </summary>
		/// <param name="fsIds"></param>
		/// <param name="isGetDisplaysOrIds">true, get getdisplayNames, false: getIds </param>
		/// <returns></returns>
		public Dictionary<int, string> GetAllAadUserDisplaysOrIdsForFs(IEnumerable<int> fsIds, bool isGetDisplaysOrIds = true)
		{
			var result = new Dictionary<int, string>();

			var fsList = GetAadUserByFs(fsIds);

			string aadUsers = string.Empty;
			foreach (var item in fsList)
			{
				var displayNames = string.Empty;
				var fsId = item.Key;

				if (item.Value.Any())
				{
					var values = item.Value.Select(e => isGetDisplaysOrIds ? e.DisplayName : e.Id).Order();
					displayNames = string.Join("; ", values);
				}
				result.Add(fsId, displayNames);
			}

			return result;
		}

		/// <summary>
		/// key : folder setting id, value : {key: SpUserID, value: isGuest}
		/// </summary>
		/// <param name="fsIds"></param>
		/// <param name="pubSpUserId"></param>
		/// <returns></returns>
		public Dictionary<int, Dictionary<string, MemberRoleType>> GetAllAadUseIdsWithGuestForFs(IEnumerable<int> fsIds, string pubSpUserId = "")
		{
			var result = new Dictionary<int, Dictionary<string, MemberRoleType>>();

			var fsList = GetAadUserByFs(fsIds);

			string aadUsers = string.Empty;
			foreach (var item in fsList)
			{
				var fsId = item.Key;
				var dic = new Dictionary<string, MemberRoleType>();

				if (item.Value.Any())
				{
					dic = item.Value.ToDictionary(e => e.Id, e => e.IsGuest ? MemberRoleType.guest : MemberRoleType.member);
				}

				if (!string.IsNullOrWhiteSpace(pubSpUserId) && dic.ContainsKey(pubSpUserId))
				{
					continue;
				}

				result.Add(fsId, dic);
			}

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="fsIds"></param>
		/// <returns></returns>
		public Dictionary<int, List<AadUser>> GetAadUserByFs(IEnumerable<int> fsIds)
		{
			var result = new Dictionary<int, IEnumerable<AadUser>>();

			var fsList = GetByFsIds(fsIds).GroupBy(e => e.PrjDocSpprjFolderSettingFk).ToDictionary(e => e.Key, e =>
			{
				return e.Select(x => JsonConvert.DeserializeObject<IEnumerable<AadUser>>(!string.IsNullOrWhiteSpace(x.SpUsers) ? x.SpUsers : "[]"));
			});

			return fsList.ToDictionary(e => e.Key, e => {
				var list = new List<AadUser>();
				var listMerge = e.Value.Merge();
				foreach(var item in listMerge)
				{
					var aadUser = list.FirstOrDefault(e => e.Id == item.Id);
					if(null == aadUser)
					{
						list.Add(item);
					}
				}

				return list;
			});			
		}

		/// <summary>
		/// Converts a DocSpprjDetailStructEntity tree structure to an array of SharePoint FolderConfig.
		/// </summary>
		/// <param name="entities"></param>
		/// <returns></returns>
		/// <exception cref="ArgumentNullException"></exception>
		public FolderConfig[] ConvertToSharePointFolderConfigs(IEnumerable<DocSpprjDetailStructEntity> entities)
		{
			if (entities == null)
			{
				throw new ArgumentNullException(nameof(entities), "Root entity cannot be null");
			}

			var visitedNodes = new HashSet<int>();
			var folders = new List<FolderConfig>();
			foreach (var entity in entities)
			{
				// Convert the tree structure recursively

				var folder = ConvertEntityToFolderConfigRecursive(entity, visitedNodes);
				folders.Add(folder);
			}
			return folders.ToArray();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="folderSettingIds"></param>
		/// <param name="bigDataCount"></param>
		/// <returns></returns>
		public IEnumerable<DocSpprjDetailStructEntity> GetItemsByFolderSettingIds(IEnumerable<int> folderSettingIds, int bigDataCount = 500)
		{
			if (folderSettingIds == null || !folderSettingIds.Any())
			{
				return new List<DocSpprjDetailStructEntity>();
			}
			string requestId = null;
			try
			{
				if (folderSettingIds.Count() > bigDataCount)
				{
					var identificationData = folderSettingIds.Select(e => new Platform.Core.IdentificationData() { PKey1 = e }).ToArray();
					using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
					{
						try
						{
							requestId = Guid.NewGuid().ToString("N"); // call RegisterTempIdsRequestUuid at the end
							AddIdentificationDataToTempTable(identificationData, requestId);
							var query = dbContext.Entities<DocSpprjDetailStructEntity>().AsQueryable();

							query = new TempDataIntegrator<BusinessComponents.DdTempIdsEntity>((e, tmp) => e.PrjDocSpprjFolderSettingFk == tmp.Key1)
								.ReduceByTempData(query, dbContext, requestId);
							return query.ToList();
						}
						catch (Exception e)
						{
							throw new BusinessLayerException("Get Detail Structure by TempTable Failed!..." + e.Message, e);
						}
					}
				}
				else
				{
					return GetByFilter(e => folderSettingIds.Contains(e.PrjDocSpprjFolderSettingFk)).ToList();
				}
			}
			catch (Exception e)
			{
				throw new BusinessLayerException(e.Message, e);
			}
			finally
			{
				if (!string.IsNullOrEmpty(requestId))
				{
					BusinessApplication.BusinessEnvironment.RegisterTempIdsRequestUuid(requestId);
				}
			}
		}

		/// <summary>
		/// Recursively converts a DocSpprjDetailStructEntity and its children to FolderConfig array.
		/// </summary>
		/// <param name="entity">The entity to convert</param>
		/// <param name="visitedNodes">Set to track visited nodes and prevent circular references</param>
		/// <returns>Array of FolderConfig representing the entity and its children</returns>
		/// <exception cref="InvalidOperationException">Thrown when circular reference is detected</exception>
		/// <exception cref="JsonException">Thrown when SpUsers JSON cannot be deserialized</exception>
		private FolderConfig ConvertEntityToFolderConfigRecursive(
			DocSpprjDetailStructEntity entity,
			HashSet<int> visitedNodes)
		{
			if (entity == null)
			{
				return null;
			}

			//// Check for circular references
			//if (visitedNodes.Contains(entity.Id))
			//{
			//	throw new InvalidOperationException($"Circular reference detected at node {entity.Id}");
			//}

			//visitedNodes.Add(entity.Id);

			try
			{
				var folderConfig = ConvertSingleEntityToFolderConfig(entity);

				// Process children recursively
				var childFolders = new List<FolderConfig>();
				if (entity.Children != null && entity.Children.Any())
				{
					foreach (var child in entity.Children)
					{
						var childConfig = ConvertEntityToFolderConfigRecursive(child, visitedNodes);
						childFolders.Add(childConfig);
					}
				}
				folderConfig.Folders = childFolders.ToArray();
				return folderConfig;
			}
			finally
			{
				//visitedNodes.Remove(entity.Id);
			}
		}

		/// <summary>
		/// Converts a single DocSpprjDetailStructEntity to FolderConfig.
		/// </summary>
		/// <param name="entity">The entity to convert</param>
		/// <returns>A FolderConfig representing the entity</returns>
		/// <exception cref="JsonException">Thrown when SpUsers JSON cannot be deserialized</exception>
		private FolderConfig ConvertSingleEntityToFolderConfig(DocSpprjDetailStructEntity entity)
		{

			// Parse DriveRecipients from SpUsers JSON
			DriveRecipient[] driveRecipients = new DriveRecipient[0];
			if (!string.IsNullOrEmpty(entity.SpUsers))
			{
				try
				{
					var aadUsers = entity.AssignedUsers;
					if (aadUsers != null)
					{
						driveRecipients = aadUsers.Select(user =>
						{
							var driveRecipient = new DriveRecipient();
							if (string.IsNullOrWhiteSpace(user.Mail))
							{
								// TODO: not sure it is working
								driveRecipient.Alias = user.DisplayName;
							}
							else
							{
								driveRecipient.Email = user.Mail;
							}
							return driveRecipient;
						}).ToArray();
					}
				}
				catch (JsonException ex)
				{
					throw new JsonException($"Failed to deserialize SpUsers JSON for {entity.Objectid}: {entity.SpUsers}", ex);
				}
			}

			// Map ShareOption to roles
			var roles = new [] { ((ShareOption)entity.ShareOption).ToSharePointRole() };

			return new FolderConfig
			{
				MetaDataType = (MetaDataType)entity.MetadataType,
				Id = entity.Objectid,
				Description = entity.ObjectDescription,
				Folders = new FolderConfig[0], // Will be populated by recursive call
				Roles = roles,
				DetailStructId = entity.Id,
				DriveRecipients = driveRecipients
			};
		}

		/// <summary>
		/// key: detailStruc id, value: isSync
		/// </summary>
		/// <param name="dic"></param>
		/// <param name="curLevel"></param>
		/// <param name="siteId"></param>
		/// <param name="parentId"></param>
		/// <param name="parentDriveItemId"></param>
		/// <param name="parentPath"></param>
		/// <param name="channelFolderDriveItemId"></param>
		/// <param name="ids"></param>
		/// <returns></returns>
		public void SyncDetailStructToSharePointFolder(Dictionary<int, List<DocSpprjDetailStructEntity>> dic, List<int> ids, string siteId,
							int curLevel, string channelFolderDriveItemId = "", string parentPath = "", int? parentId = null, string parentDriveItemId = "")
		{
			if (!dic.ContainsKey(curLevel))
			{
				return;
			}

			var folderType = (int)MetaDataType.Folder;
			var curLevelDetailStruct = dic[curLevel];
			if (!string.IsNullOrWhiteSpace(parentDriveItemId))
			{
				curLevelDetailStruct = curLevelDetailStruct.Where(e => null != e.ObjectParentFk && e.ObjectParentFk.Value == parentId.Value).ToList();
			}

			if (!curLevelDetailStruct.Any())
			{
				return;
			}

			var typeId = curLevelDetailStruct.First().MetadataType;
			var curLevelMetaDic = GetMetaTypeDataDic((MetaDataType)typeId, false, true);
			var curLevelDetailStructIds = curLevelDetailStruct.CollectIds(e => e.Id);
			var syncedDetaiStructDic = docMetadata2ExtEntityLogic.GetByFilter(e => curLevelDetailStructIds.Contains(e.Objectid) && e.BasExternalsourceFk == ExternalSourceFk &&
						e.ExternalId != "" && e.Type == folderType).ToDictionary(e => e.Objectid, e => e.ExternalId);

			foreach (var item in curLevelDetailStruct)
			{
				var id = item.Id;
				string driveItemId = string.Empty;
				var folderName = curLevelMetaDic[item.Objectid];

				if (string.IsNullOrWhiteSpace(folderName))
				{
					continue;
				}

				var path = parentPath + "/" + folderName;

				if (!syncedDetaiStructDic.ContainsKey(id))
				{

					MsOfficeApiResult<DriveItem> response = SharePointApi.GetDriveItemByPath(siteId, path);
					if (!response.Success)
					{

						if (item.Level == 0 && !string.IsNullOrWhiteSpace(channelFolderDriveItemId))
						{
							parentDriveItemId = channelFolderDriveItemId;
						}
						response = SharePointApi.CreateFolder(siteId, folderName, parentDriveItemId);


						if (!response.Success)
						{
							continue;
						}
					}

					DocMetadata2extEntity docMetadata2ExtEntity = new DocMetadata2extEntity();
					docMetadata2ExtEntity.Id = SeqPop(ids);
					docMetadata2ExtEntity.Type = folderType;
					driveItemId = response.Item.Id;
					docMetadata2ExtEntity.Objectid = id;
					docMetadata2ExtEntity.ExternalId = driveItemId;
					docMetadata2ExtEntity.BasExternalsourceFk = ExternalSourceFk;
					docMetadata2ExtEntityLogic.Save(docMetadata2ExtEntity);

					if (item.Level == 0)
					{
						AddPermissionForDetail(siteId, item, driveItemId, false);
					}					
				}
				else
				{
					driveItemId = syncedDetaiStructDic[id];
				}
				SyncDetailStructToSharePointFolder(dic, ids, siteId, curLevel + 1, "", path, id, driveItemId);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="siteId"></param>
		/// <param name="item"></param>
		/// <param name="driveItemId"></param>
		/// <param name="inheritePermission">inheritePermission</param>
		public void AddPermissionForDetail(string siteId, DocSpprjDetailStructEntity item, string driveItemId = "", bool inheritePermission = true)
		{
			if (string.IsNullOrWhiteSpace(driveItemId))
			{
				var team2Ext = docMetadata2ExtEntityLogic.GetByFilter(e => e.Objectid == item.Id && e.Type == (int)MetaDataType.Team && e.ExternalId != null && e.ExternalId != "").FirstOrDefault();
				if (null == team2Ext)
				{
					driveItemId = team2Ext.ExternalId;
				}
			}

			List<string> emails = new List<string>();

			if (!string.IsNullOrWhiteSpace(item.SpUsers))
			{
				var spUsers = JsonConvert.DeserializeObject<List<AadUser>>(item.SpUsers);
				emails.AddRange(spUsers.Select(e => e.Mail));				
			}

			if (emails.Any())
			{
				var recipients = emails.Distinct().Select(e =>
				{
					return new DriveRecipient()
					{
						Email = e
					};
				});

				var role = ShareOptionExtensions.ToSharePointRole((ShareOption)item.ShareOption);
				SharePointApi.InviteAuthorize(siteId, driveItemId, recipients, role, true, false, "", inheritePermission);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="parentId"></param>
		/// <param name="detailStrucs"></param>
		/// <param name="aadUserDic"></param>
		public void GetAllParentAadUsers(int parentId, IEnumerable<DocSpprjDetailStructEntity> detailStrucs, ref Dictionary<string, string> aadUserDic)
		{
			var detailStruct = detailStrucs.FirstOrDefault(e => e.Id == parentId);
			if(null == detailStruct)
			{
				return;
			}

			if (!string.IsNullOrWhiteSpace(detailStruct.SpUsers))
			{
				var spUsers =  JsonConvert.DeserializeObject<IEnumerable<AadUser>>(detailStruct.SpUsers);
				foreach(var item in spUsers)
				{
					if (!aadUserDic.ContainsKey(item.Id))
					{
						aadUserDic.Add(item.Id,	item.Mail);
					}
				}
			}

			if (null != detailStruct.ObjectParentFk)
			{
				GetAllParentAadUsers(detailStruct.ObjectParentFk.Value, detailStrucs, ref aadUserDic);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="target"></param>
		/// <param name="source"></param>
		/// <returns></returns>
		public static DocSpprjDetailStructEntity UpdateDetailStructure(DocSpprjDetailStructEntity target, DocSpprjDetailStructEntity source)
		{
			if (target == null || source == null)
			{
				return target;
			}
			target.MetadataType = source.MetadataType;
			target.Objectid = source.Objectid;
			target.ShareOption = source.ShareOption;
			target.SpUsers = source.SpUsers;
			target.Level = source.Level;
			return target;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="source1"></param>
		/// <param name="source2"></param>
		/// <returns>true: the same; false difference</returns>
		public static bool CompareDetailStructure(DocSpprjDetailStructEntity source1, DocSpprjDetailStructEntity source2)
		{
			if (source1 == source2)
			{
				return true;
			}

			if (source1 == null || source2 == null)
			{
				return false;
			}

			if (source1.MetadataType == source2.MetadataType &&
				source1.Objectid == source2.Objectid &&
				source1.Level == source2.Level &&
				source1.SpUsers == source2.SpUsers &&
				source1.ShareOption == source2.ShareOption)
			{
				return true;
			}

			return false;
		}		

		private static List<int> GetFakeIds(int count)
		{
			var list = new List<int>();
			for (var i = 1; i <= count; ++i)
			{
				list.Add(-i);
			}

			return list;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="folderSettingIds"></param>
		/// <param name="folderStructures"></param>
		/// <param name="detailStructureToUpdate"></param>
		/// <param name="detailStructureToCreate"></param>
		/// <param name="detailStructureToDelete"></param>
		public void CollectDetailStructureChanges(
			IEnumerable<int> folderSettingIds,
			IEnumerable<DocSpprjFolderStructEntity> folderStructures,
			ref List<DocSpprjDetailStructEntity> detailStructureToUpdate,
			ref List<DocSpprjDetailStructEntity> detailStructureToCreate,
			ref List<DocSpprjDetailStructEntity> detailStructureToDelete)
		{
			if (HasParametersNull(folderSettingIds, folderStructures,
				detailStructureToUpdate, detailStructureToCreate, detailStructureToDelete))
			{
				return;
			}

			var detailStructuresInDb = GetItemsByFolderSettingIds(folderSettingIds);
			var detailStructuresMap = detailStructuresInDb.GroupBy(e => e.PrjDocSpprjFolderSettingFk)
				.ToDictionary(e => e.Key, e => e.ToList());

			var defaultDetailStructures = GetDefaultDetailStruc(folderStructures, -1, true);
			defaultDetailStructures = defaultDetailStructures.OrderBy(e => e.Level).ToList();
			var defaultDetailCount = defaultDetailStructures.Count();
			var defaultId2DetailIdMap = new Dictionary<string, int>();

			// handle the collection one folder setting at a time
			foreach (var settingId in folderSettingIds)
			{
				if (!detailStructuresMap.TryGetValue(settingId, out var detailStructureListInDb))
				{
					continue;
				}

				detailStructureListInDb = detailStructureListInDb.OrderBy(e => e.Level).ToList();
				var detailCount = detailStructureListInDb.Count;
				var count = Math.Max(defaultDetailCount, detailCount);
				for (var i = 0; i < count; ++i)
				{
					var defaultItem = defaultDetailStructures.ElementAtOrDefault(i);
					var detailItemInDb = detailStructureListInDb.ElementAtOrDefault(i);
					if (defaultItem != null && detailItemInDb != null)
					{
						var defaultItemId = GetSettingIdStructureIdKey(defaultItem.Id, settingId);
						if (!defaultId2DetailIdMap.ContainsKey(defaultItemId))
						{
							defaultId2DetailIdMap.Add(defaultItemId, detailItemInDb.Id);
						}
					}
				}

				for (var i = 0; i < count; ++i)
				{
					var defaultItem = defaultDetailStructures.ElementAtOrDefault(i);
					var detailItemInDb = detailStructureListInDb.ElementAtOrDefault(i);
					if (defaultItem != null && detailItemInDb != null)
					{
						var defaultItemId = GetSettingIdStructureIdKey(defaultItem.Id, settingId);
						if (!defaultId2DetailIdMap.ContainsKey(defaultItemId))
						{
							defaultId2DetailIdMap.Add(defaultItemId, detailItemInDb.Id);
						}
						var isChanged = false;
						// if not the same, update values
						if (!CompareDetailStructure(detailItemInDb, defaultItem))
						{
							detailItemInDb = UpdateDetailStructure(detailItemInDb, defaultItem);
							isChanged = true;
						}

						int? detailObjectParentId = defaultItem.ObjectParentFk;
						if (defaultItem.ObjectParentFk.HasValue &&
							defaultId2DetailIdMap.TryGetValue(GetSettingIdStructureIdKey(defaultItem.ObjectParentFk.Value, settingId), out var detailId))
						{
							detailObjectParentId = detailId;
						}

						if (detailItemInDb.ObjectParentFk != detailObjectParentId)
						{
							detailItemInDb.ObjectParentFk = detailObjectParentId;
							isChanged = true;
						}

						if (isChanged)
						{
							detailStructureToUpdate.Add(detailItemInDb);
						}
					}
					else if (defaultItem != null) // new data
					{
						// keep the detail structure id as default one
						detailItemInDb = defaultItem.Clone() as DocSpprjDetailStructEntity;
						detailItemInDb.Version = 0;
						detailItemInDb.PrjDocSpprjFolderSettingFk = settingId;
						detailStructureToCreate.Add(detailItemInDb);
					}
					else if (detailItemInDb != null) // delete data
					{
						detailStructureToDelete.Add(detailItemInDb);
					}
				}
			}

			var newDetailStructCount = detailStructureToCreate.Count;
			var detailStrucIds = newDetailStructCount > 0 ?
				GetNextSequences(newDetailStructCount).GetEnumerator() : new List<int>().GetEnumerator();

			foreach (var item in detailStructureToCreate)
			{
				var defaultId = item.Id;
				detailStrucIds.MoveNext();
				item.Id = detailStrucIds.Current;
				var defaultIdKey = GetSettingIdStructureIdKey(defaultId, item.PrjDocSpprjFolderSettingFk);
				if (!defaultId2DetailIdMap.ContainsKey(defaultIdKey))
				{
					defaultId2DetailIdMap.Add(defaultIdKey, item.Id);
				}
			}

			var toSave = detailStructureToUpdate.Concat(detailStructureToCreate).ToList();
			foreach (var item in toSave)
			{
				if (item.ObjectParentFk.HasValue && item.ObjectParentFk.Value < 0)
				{
					var idKey = GetSettingIdStructureIdKey(item.ObjectParentFk.Value, item.PrjDocSpprjFolderSettingFk);
					if (defaultId2DetailIdMap.TryGetValue(idKey, out var detailId))
					{
						item.ObjectParentFk = detailId;
					}
				}
			}
		}

		private static string GetSettingIdStructureIdKey(int id, int settingId)
		{
			return $"{settingId.ToString()}_{id.ToString()}";
		}

		private static bool HasParametersNull(params object[] parameters)
		{
			var hasNull = false;
			foreach (var param in parameters)
			{
				if (param == null)
				{
					hasNull = true;
					break;
				}
			}
			return hasNull;
		}
	}
}
