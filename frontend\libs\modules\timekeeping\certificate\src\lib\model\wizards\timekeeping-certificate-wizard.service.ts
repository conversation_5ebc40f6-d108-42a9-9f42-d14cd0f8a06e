/*
 * Copyright(c) RIB Software GmbH
 */
import { inject, Injectable } from '@angular/core';
import { BasicsSharedChangeStatusService, IStatusChangeOptions } from '@libs/basics/shared';
import { ICertificateEntity } from '@libs/resource/interfaces';
import { TimekeepingCertificateDataService } from '../../services/timekeeping-certificate-data.service';
import { ITimekeepingCertificateComplete } from '../entities/timekeeping-certificate-complete.interface';
import { IInitializationContext } from '@libs/platform/common';

@Injectable({
	providedIn: 'root'
})
export class TimekeepingCertificateStatusWizardService extends BasicsSharedChangeStatusService<
	ICertificateEntity,
	ICertificateEntity,
	ITimekeepingCertificateComplete
	> {
	protected readonly dataService = inject(TimekeepingCertificateDataService);

	protected statusConfiguration: IStatusChangeOptions<ICertificateEntity, ITimekeepingCertificateComplete> = {
		title: 'timekeeping.certificate.changeStatus',
		guid: '382c0f1a7456446890600c601237c7ca',
		isSimpleStatus: false,
		statusName: 'timekeepingemployeecertificatestatus',
		checkAccessRight: true,
		statusField: 'CertificateStatusFk',
		updateUrl: '',
		rootDataService: this.dataService
	};


	public startChangeCertificateStatusWizard(context: IInitializationContext): void {
		if (!this.dataService.getSelectedEntity()) {
			return;
		}
		this.startChangeStatusWizard();
	}


	public override afterStatusChanged(): void {
		if (this.dataService.refreshSelected) {
			this.dataService.refreshSelected();
		} else {
			this.dataService.refreshAll();
		}
	}
}