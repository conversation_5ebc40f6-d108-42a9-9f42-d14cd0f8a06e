import { Injectable } from '@angular/core';
import { IWipStatusEntity } from '@libs/sales/interfaces';
import { FieldType, ILookupConfig, UiCommonLookupEndpointDataService } from '@libs/ui/common';

@Injectable({
    providedIn: 'root'
})

export class SalesWipStatusLookupDataService<TEntity extends object> extends UiCommonLookupEndpointDataService<IWipStatusEntity, TEntity> {
    public constructor() {

        const endpoint = { httpRead: { route: 'sales/wip/status/', endPointRead: 'list'} };

        const gridConfig: ILookupConfig<IWipStatusEntity, TEntity> = {
            uuid: 'bd2a6aa453a64a01907b18efcd2cb14f',
            idProperty: 'Id',
            valueMember: 'Id',
            displayMember: 'DescriptionInfo.Translated',

            gridConfig: {
                uuid: 'bd2a6aa453a64a01907b18efcd2cb14f',
                columns: [
                    {id: 'Description', model: 'DescriptionInfo.Translated', type: FieldType.Translation, label: {text: 'Description', key:'cloud.common.entityDescription' }, sortable: true, visible: true },
                ],
            }
        };

        super(endpoint, gridConfig);
    }
}