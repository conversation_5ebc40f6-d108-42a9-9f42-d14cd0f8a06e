/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import {
	DataServiceFlatLeaf,
	IDataServiceOptions,
	IDataServiceChildRoleOptions,
	ServiceRole,
	IDataServiceEndPointOptions
} from '@libs/platform/data-access';
import { ICertifiedEmployeeEntity } from '@libs/timekeeping/interfaces';
import { ICertificateEntity } from '@libs/resource/interfaces';
import { TimekeepingCertificateDataService } from './timekeeping-certificate-data.service';
import { ITimekeepingCertificateComplete } from '../model/entities/timekeeping-certificate-complete.interface';
import {IIdentificationDataMutable } from '@libs/platform/common';

@Injectable({ providedIn: 'root' })
export class TimekeepingCertifiedEmployeeDataService extends DataServiceFlatLeaf<ICertifiedEmployeeEntity, ICertificateEntity, ITimekeepingCertificateComplete> {
	public constructor(
		private certDataService: TimekeepingCertificateDataService
	) {
		const options: IDataServiceOptions<ICertifiedEmployeeEntity> = {
			apiUrl: 'timekeeping/certified/employee',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'listbyparent',
				usePost: true,
				prepareParam: (): Readonly<IIdentificationDataMutable> => {
					const selectedCertificate = this.certDataService.getSelectedEntity();
					if (selectedCertificate && selectedCertificate.Id != null) {
						return { id: selectedCertificate.Id, PKey1: selectedCertificate.Id } as Readonly<IIdentificationDataMutable>;
					}
					return { id: 0, PKey1: null } as Readonly<IIdentificationDataMutable>;
				}
			},
			deleteInfo: {
				endPoint: 'multidelete'
			},
			roleInfo: <IDataServiceChildRoleOptions<ICertifiedEmployeeEntity, ICertificateEntity, ITimekeepingCertificateComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'CertifiedEmployee',
				parent: certDataService
			},
		};
		super(options);
	}

	public override isParentFn(parentKey: ICertificateEntity, entity: ICertifiedEmployeeEntity): boolean {
		return entity.EmpCertificateFk === parentKey.Id;
	}

	public override registerModificationsToParentUpdate(complete: ITimekeepingCertificateComplete, modified: ICertifiedEmployeeEntity[], deleted: ICertifiedEmployeeEntity[]): void {
		if (modified && modified.length > 0) {
			complete.CertifiedEmployeeToSave = modified;
		}
		if (deleted && deleted.length > 0) {
			complete.CertifiedEmployeeToDelete = deleted;
		}
	}

	public override getSavedEntitiesFromUpdate(complete: ITimekeepingCertificateComplete): ICertifiedEmployeeEntity[] {
		return complete.CertifiedEmployeeToSave || [];
	}
}