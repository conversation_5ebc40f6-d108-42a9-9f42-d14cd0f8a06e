using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Dtos;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Basics.CostGroups.ServiceFacade.WebApi;
using RIB.Visual.Basics.LookupData.ServiceFacade.WebApi;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Boq.Main.ServiceFacade.WebApi;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using RIB.Visual.Qto.Formula.BusinessComponents;
using RIB.Visual.Qto.Formula.ServiceFacade.WebApi;
using RIB.Visual.Qto.Main.BusinessComponents;
using RIB.Visual.Qto.Main.Localization.Properties;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Linq.Dynamic;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Web.Http;

namespace RIB.Visual.Qto.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// QtoMainDetailController
	/// </summary>
	[RoutePrefix("qto/main/detail")]
	public class QtoDetailController : ApiControllerBase<QtoDetailLogic>
	{
		/// <summary>
		/// GetList
		/// </summary>
		/// <returns></returns>
		[Route("list"), HttpPost]
		public Dictionary<string, object> GetList(QtoDetailReadDto readDto)
		{
			Dictionary<string, object> result = new Dictionary<string, object>();
			int qtoHeaderId = -1;

			var main = GetMainDtos(readDto,ref qtoHeaderId);
			if (main != null && main.Any())
			{
				// take the qto formula and to return
				var allQtoFormulaList = new QtoFormulaLogic().GetList();
				var qtoFormulaFks = main.CollectIds(e => e.QtoFormulaFk).ToList();
				var defaultQtoFormula = allQtoFormulaList.Where(e => e.IsDefault);
				var qtoFormulaEntities = allQtoFormulaList;
				var qtoFormularScriptEntities = (new QtoFormulaScriptLogic()).GetListScriptEntities(qtoFormulaFks).ToList();

				
				var boqItemReferenceFks = main.Where(e => e.BoqItemReferenceFk.HasValue).Select(e => e.BoqItemReferenceFk.Value);

				List<BoqItemEntity> boqItems = new List<BoqItemEntity>();
				boqItems = readDto.IsPrjBoq || readDto.IsPrcBoq || readDto.IsBillingBoq || readDto.IsPesBoq || readDto.IsWipBoq
					? new BoqItemLogic().GetSearchList(e => e.BoqHeaderFk == readDto.BoqHeaderFk && (e.Id == readDto.MainItemId  || boqItemReferenceFks.Contains(e.Id))).ToList()
					: new BoqItemLogic().GetSearchList(e => e.BoqHeaderFk == readDto.BoqHeaderFk).ToList();
				var boqItemDtos = boqItems.ToDtos(e => new BoqItemDto(e)).ToList();

				FixQtoDetail(main.ToList(), qtoFormulaEntities, boqItemDtos, qtoFormularScriptEntities);

				FilterFormulaOperatorByUom(main.ToList());

				#region collect lookups json

				// add boq lookup data
				List<object> boqLookupDatas = new List<object>();
				foreach (var item in boqItemDtos)
				{
					object boqLookupData = new
					{
						Id = item.Id,
						BoqHeaderFk = item.BoqHeaderFk,
						BriefInfo = item.BriefInfo,
						Reference = item.Reference,
						BasUomFk = item.BasUomFk,
						BoqLineTypeFk = item.BoqLineTypeFk
					};

					boqLookupDatas.Add(boqLookupData);
				}
				var jsonData = main.CollectLookups(collector => collector.Add("boqItemLookupDataService", () => { return boqLookupDatas; }));
				result["boqItemLookupDataService"] = jsonData["boqItemLookupDataService"];

				jsonData = main.CollectLookups(collector => collector.Add("AssertMaster", e => e.AssetMasterFk));
				result["AssertMaster"] = jsonData["AssertMaster"];

				jsonData = main.CollectLookups(collector => collector.Add("BudgetCode", e => e.BudgetCodeFk));
				result["BudgetCode"] = jsonData["BudgetCode"];

				jsonData = main.CollectLookups(collector => collector.Add("Classification", e => e.ClassificationFk));
				result["Classification"] = jsonData["Classification"];

				jsonData = main.CollectLookups(collector => collector.Add("WorkCategory", e => e.WorkCategoryFk));
				result["WorkCategory"] = jsonData["WorkCategory"];

				// add qtoformula
				foreach (var qtoFormula in allQtoFormulaList)
				{
					var mapFormulaScripts = qtoFormularScriptEntities.Where(e => e.QtoFormulaFk == qtoFormula.Id).ToList();
					if (mapFormulaScripts.Count > 0)
					{
						qtoFormula.QtoFormulaScriptEntities = mapFormulaScripts;
					}
				}

				jsonData = main.CollectLookups(collector => collector.Add("QtoFormula", () => allQtoFormulaList.Select(e => new QtoFormulaDto(e))));
				result["QtoFormula"] = jsonData["QtoFormula"];

				jsonData = main.CollectLookups(collector => collector.Add("QtoFormulaUom", () => new QtoFormulaUomLogic().GetSearchList(e => qtoFormulaFks.Contains(e.QtoFormulaFk)).Select(e => new QtoFormulaUomDto(e))));
				result["QtoFormulaUom"] = jsonData["QtoFormulaUom"];

				jsonData = main.CollectLookups(collector => collector.Add("Uom", e => e.BasUomFk));
				result["Uom"] = jsonData["Uom"];

				jsonData = main.CollectLookups(collector => collector.Add("QtoDetail", e => e.QtoDetailReferenceFk));
				result["QtoDetail"] = jsonData["QtoDetail"];

				var qtoComments = new QtoCommentLogic().GetList().ToDtos(e => new QtoCommentDto(e));
				jsonData = main.CollectLookups(collector => collector.Add("QtoComment", () => { return qtoComments; }));
				result["QtoComment"] = jsonData["QtoComment"];

				jsonData = main.CollectLookups(collector => collector.Add("QtoLineType", e => e.QtoLineTypeFk));
				result["QtoLineType"] = jsonData["QtoLineType"];

				jsonData = main.CollectLookups(collector => collector.Add("DefaultQtoFormula", () => { return defaultQtoFormula; }));
				result["DefaultQtoFormula"] = jsonData["DefaultQtoFormula"];

				jsonData = main.CollectLookups(collector => collector.Add("QtoDetailStatus", e => e.QtoDetailStatusFk));
				result["QtoDetailStatus"] = jsonData["QtoDetailStatus"];

				var qtoDetailIds = main.Select(e => e.Id).ToList();
				var qtoDetail2CostGroups = Logic.GetQtoDetail2CostGroupEntities(qtoDetailIds).Select(e => new MainItem2CostGroupDto(e)).ToList();
				jsonData = main.CollectLookups(collector => collector.Add("QtoDetail2CostGroups", () => { return qtoDetail2CostGroups; }));
				result["QtoDetail2CostGroups"] = jsonData["QtoDetail2CostGroups"];

				result["Main"] = jsonData["Main"];

				#endregion
			}
			// take the qto line type and to return
			var qtoLineTypeList = new QtoLineTypeLogic().GetList();

			result["qtoLineTypeCodeLookupService"] = qtoLineTypeList;
			// event no exist qto lines, should attach the cost groups
			var costGroupCats = new CostGroupCompleteEntityDto(Logic.GetCostGroupCats(readDto.PrjProjectFk));
			result["CostGroupCats"] = costGroupCats;

			var basRubricCategoryFk = 0;
			if (!readDto.IsQtoBoq)
			{
				#region get qtoHeader to control the button in qto container on wip/pes/bill module

				var qtoHeades = new QtoHeaderLogic().GetQtoHeaderByBoqHeaderId(readDto.BoqHeaderFk);

				var qtoHeader = qtoHeades.Where(e => e.QtoTargetType == (int)QtoTargetType.PrjWqAq || e.QtoTargetType == (int)QtoTargetType.PrcWqAq).FirstOrDefault();

				// set qtoheader as isbackup or not
				var boqHeader = new BoqHeaderLogic().GetItemByKey(readDto.BoqHeaderFk);
				if (qtoHeader != null && boqHeader != null && boqHeader.IsBackup)
				{
					qtoHeader.IsBackup = true;
				}

				if (readDto.IsBillingBoq)
				{
					qtoHeader = qtoHeades.Where(e => e.QtoTargetType == (int)QtoTargetType.WipOrBill).FirstOrDefault();
				}
				else if (readDto.IsWipBoq)
				{
					qtoHeader = qtoHeades.Where(e => e.QtoTargetType == (int)QtoTargetType.WipOrBill).FirstOrDefault();
				}
				else if (readDto.IsPesBoq)
				{
					qtoHeader = qtoHeades.Where(e => e.QtoTargetType == (int)QtoTargetType.PesBoq).FirstOrDefault();
				}

				var qtoStatusItems = new QtoStatusLogic().GetList().ToList();
				var qtoHeaderDto = qtoHeader != null ? new QtoHeaderDto(qtoHeader) : new QtoHeaderDto();
				basRubricCategoryFk = qtoHeaderDto.BasRubricCategoryFk;

				if (qtoHeaderDto != null)
				{
					IPrcHeaderLogic prcHeaderLogic = Injector.Get<IPrcHeaderLogic>();
					var prjChangeFks = new List<int>();
					var prjChanges = new List<IChangeEntity>();
					if (qtoHeaderDto.ConHeaderFk != null && qtoHeaderDto.ConHeaderFk.HasValue)
					{
						IContractHeaderInfoProvider procurementContractLogic = Injector.Get<IContractHeaderInfoProvider>();

						var proContractEntities = procurementContractLogic.GetConHeadersByKey(new List<int?>() { qtoHeaderDto.ConHeaderFk }).Distinct().ToList();

						// get project change stutas
						prjChangeFks = proContractEntities.Where(x => x.ProjectChangeFk.HasValue).Select(x => x.ProjectChangeFk.Value).ToList();
						prjChanges = Injector.Get<IChangeMainLogic>("Change.Main.ChangeEntity").GetChangeWithStatusByIds(prjChangeFks).ToList();

						var contract = proContractEntities.FirstOrDefault(x => x.Id == qtoHeaderDto.ConHeaderFk);
						qtoHeaderDto.PrjChangeStutasReadonly = contract != null && contract.ProjectChangeFk.HasValue && prjChanges.Any(x => x.Id == contract.ProjectChangeFk) && !prjChanges.FirstOrDefault(x => x.Id == contract.ProjectChangeFk).IsAllowedQtoForProc;
					}
					else if (qtoHeaderDto.OrdHeaderFk != null && qtoHeaderDto.OrdHeaderFk.HasValue)
					{
						ISalesHeaderLogic salesHeaderLogic = Injector.Get<ISalesHeaderLogic>("Contract");

						var salesContractEntities = salesHeaderLogic.GetSalesContractsByKey(new List<int?>() { qtoHeaderDto.OrdHeaderFk }).Distinct().ToList();

						prjChangeFks = salesContractEntities.Where(x => x.PrjChangeFk.HasValue).Select(x => x.PrjChangeFk.Value).ToList();
						prjChanges = Injector.Get<IChangeMainLogic>("Change.Main.ChangeEntity").GetChangeWithStatusByIds(prjChangeFks).ToList();
						var contract = salesContractEntities.FirstOrDefault(x => x.Id == qtoHeaderDto.OrdHeaderFk);
						qtoHeaderDto.PrjChangeStutasReadonly = contract != null && contract.PrjChangeFk.HasValue && prjChanges.Any(x => x.Id == contract.PrjChangeFk) && !prjChanges.FirstOrDefault(x => x.Id == contract.PrjChangeFk).IsAllowedQtoForSales;
					}
				}

				result["qtoHeader"] = qtoHeaderDto;
				result["qtoStatusItems"] = qtoStatusItems;
				#endregion

			}
			else if(main != null && main.Any())
			{
				#region get the wip/bill/pes status 

				var currentQtoHeader = new QtoHeaderLogic().GetQtoHeaderById(readDto.MainItemId);
				if (currentQtoHeader != null)
				{
					switch (currentQtoHeader.QtoTargetType)
					{
						case (int)QtoTargetType.PesBoq:
							var pesHeaderFks = main.Where(e => e.PesHeaderFk.HasValue).Select(e => e.PesHeaderFk.Value).Distinct().ToList();
							var pesHeaderProvider = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IPesHeaderProvider>();
							var pesList = pesHeaderProvider.GetPesHeaderListByIDs(pesHeaderFks, true);

							if (pesList.Any())
							{
								foreach (var qto in main)
								{
									if (qto.PesHeaderFk != null && qto.PesHeaderFk.HasValue)
									{
										var pes = pesList.Where(e => e.Id == qto.PesHeaderFk.Value).FirstOrDefault();
										qto.PesReadOnly = pes != null ? pes.IsInvoicedStatus || pes.IsReadOnly : qto.PesReadOnly;
									}
								}
							}

							break;
						case (int)QtoTargetType.WipOrBill:

							var bilHeaderFks = main.Where(e => e.BilHeaderFk.HasValue).Select(e => e.BilHeaderFk.Value).Distinct().ToList();
							var wipHeaderFks = main.Where(e => e.WipHeaderFk.HasValue).Select(e => e.WipHeaderFk.Value).Distinct().ToList();

							var salesWipLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesWipHeaderLogic>();
							var wipList = salesWipLogic.GetWipsByIds(wipHeaderFks, true);

							var salesBillingLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesBillingLogic>("bill");
							var billList = salesBillingLogic.GetSalesBillingsByIds(bilHeaderFks, true);

							foreach (var qto in main)
							{
								if (wipList.Any() && qto.WipHeaderFk != null && qto.WipHeaderFk.HasValue)
								{
									var wip = wipList.Where(e => e.Id == qto.WipHeaderFk.Value).FirstOrDefault();
									qto.WipReadOnly = wip != null ? wip.IsOrderedStatus || wip.IsReadOnly : qto.WipReadOnly;
									qto.PerformedFromWip = wip != null ? wip.PerformedFrom : qto.PerformedFromWip;
									qto.PerformedToWip = wip != null ? wip.PerformedTo : qto.PerformedToWip;
								}

								if (billList.Any() && qto.BilHeaderFk != null && qto.BilHeaderFk.HasValue)
								{
									var bill = billList.Where(e => e.Id == qto.BilHeaderFk.Value).FirstOrDefault();
									qto.BillReadOnly = bill != null ? bill.IsOrderedStatus || bill.IsReadOnly : qto.BillReadOnly;
									qto.PerformedFromBil = bill != null ? bill.PerformedFrom : qto.PerformedFromBil;
									qto.PerformedToBil = bill != null ? bill.PerformedTo : qto.PerformedToBil;
									qto.ProgressInvoiceNo = bill != null ? bill.ProgressInvoiceNo : qto.ProgressInvoiceNo;
								}

								if (qto.IsSplitted || (qto.QtoDetailSplitFromFk != null && qto.QtoDetailSplitFromFk.HasValue))
								{
									if (!qto.IsIQ && !qto.IsBQ && qto.WipHeaderFk == null && qto.BilHeaderFk == null)
									{
										QtoDetailDto referenceItem;
										if (qto.IsSplitted)
										{
											referenceItem = main.FirstOrDefault(e => e.QtoDetailSplitFromFk.HasValue && e.QtoDetailSplitFromFk.Value == qto.Id);
										}
										else
										{
											referenceItem = main.FirstOrDefault(e => e.Id == qto.QtoDetailSplitFromFk.Value);
										}

										qto.SplitItemBQReadOnly = referenceItem.IsBQ || (referenceItem.BilHeaderFk != null && referenceItem.BilHeaderFk.HasValue);
										qto.SplitItemIQReadOnly = referenceItem.IsIQ || (referenceItem.WipHeaderFk != null && referenceItem.WipHeaderFk.HasValue);
									}
									else
									{
										qto.SplitItemIQReadOnly = qto.IsBQ || (qto.BilHeaderFk != null && qto.BilHeaderFk.HasValue);
										qto.SplitItemBQReadOnly = qto.IsIQ || (qto.WipHeaderFk != null && qto.WipHeaderFk.HasValue);
									}
								}
							}

							break;

						default:
							break;
					}
				}
				#endregion
			}

			if (main.Any(x => x.BasUomFk > 0))
			{
				basRubricCategoryFk = readDto.BasRubricCategoryFk > 0 ? readDto.BasRubricCategoryFk : basRubricCategoryFk;
				result["QtoFormulaAllUom"] =
					 (new QtoFormulaUomController()).GetListByRubricCategoryFk(basRubricCategoryFk);
			}

			//var qtoAddressRange = new QtoAddressRangeLogic().GetQtoAddressRangeByQtoHeader(readDto.MainItemId);

			var qtoAddressRangeDetails = new QtoAddressRangeDetailLogic().GetQtoAddressRangeDetails(qtoHeaderId, QtoAddressRangeType.Dialog);
			if (qtoAddressRangeDetails.Any())
			{
				var qtoAddressRange = new QtoAddressRangeDetailLogic().GetQtoAddressRangeString(qtoAddressRangeDetails);
				result["qtoAddressRange"] = qtoAddressRange;
			}

			var isAutomaticallyCreateQTO = false;
			var sOption = new SystemOptionLogic().GetSearchList(e => e.Id == SystemOption.AutomaticallyCreateQTO).FirstOrDefault();
			if (sOption != null)
			{
				isAutomaticallyCreateQTO = ConverterHelper.GetBooleanFromString(sOption.ParameterValue);
			}
			result["isAutomaticallyCreateQTO"] = isAutomaticallyCreateQTO;


			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="readDto"></param>
		/// <returns></returns>
		[Route("checkDataBeforeDelete"), HttpPost]
		public bool CheckDataBeforeDelete(QtoDetailReadDto readDto)
		{
			var result = true;
			var qtoComments = new QtoDetailCommentsLogic().GetByFilter(e => readDto.QtoDetailIds.Contains(e.QtoDetailFk));
			if (qtoComments.Any())
			{
				result = false;
			}
			var qtoDocuments = new QtoDetailDocumentLogic().GetByFilter(e => readDto.QtoDetailIds.Contains(e.QtoDetailFk));
			if (qtoDocuments.Any())
			{
				result = false;
			}
			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		private IEnumerable<QtoDetailDto> GetMainDtos(QtoDetailReadDto readDto, ref int qtoHeaderId)
		{
			List<int> pageNumbers = new List<int>();
			if (readDto.PageNumberIds != null)
			{
				pageNumbers = readDto.PageNumberIds.ToList();
			}

			List<QtoDetailEntity> allQtoDetails = new List<QtoDetailEntity>();
			List<QtoDetailEntity> qtoDetails = new List<QtoDetailEntity>();

			QtoHeaderEntity qto = null;

			var qtoDetailHelper = new QtoDetailHelper();
			if (readDto.IsPrjBoq || readDto.IsPrcBoq || readDto.IsBillingBoq || readDto.IsPesBoq || readDto.IsWipBoq)
			{
				var qtos = new QtoHeaderLogic().GetQtoHeaderByBoqHeaderId(readDto.BoqHeaderFk);

				var qtoHeaderIds = qtos.Select(e => e.Id).ToList();
				allQtoDetails = Logic.GetListByQtoHeaderIds(qtoHeaderIds).ToList();
				qtoDetailHelper.UpdateQtoDetailGroupInfo(allQtoDetails);

				qto = qtos.Where(e => e.QtoTargetType == (int)QtoTargetType.PrjWqAq || e.QtoTargetType == (int)QtoTargetType.PrcWqAq).FirstOrDefault();
				var filteredBoqIds = readDto.IsCrbBoq && readDto.SubQuantityBoqItemFks != null && readDto.SubQuantityBoqItemFks.Any() ?
											readDto.SubQuantityBoqItemFks.Append(readDto.MainItemId) : new List<int>() { readDto.MainItemId };

				if (readDto.IsBillingBoq)
				{
					qto = qtos.Where(e => e.QtoTargetType == (int)QtoTargetType.WipOrBill).FirstOrDefault();
					qtoDetails = allQtoDetails.Where(e => e.BilHeaderFk.HasValue).Where(e => e.QtoHeaderFk == qto.Id && e.IsBQ && e.BilHeaderFk.Value == readDto.BilHeaderFk && e.BoqHeaderFk == readDto.BoqHeaderFk && filteredBoqIds.Contains(e.BoqItemFk)).ToList();
				}
				else if (readDto.IsWipBoq)
				{
					qto = qtos.Where(e => e.QtoTargetType == (int)QtoTargetType.WipOrBill).FirstOrDefault();
					qtoDetails = allQtoDetails.Where(e => e.WipHeaderFk.HasValue).Where(e => e.QtoHeaderFk == qto.Id && e.IsIQ && e.WipHeaderFk.Value == readDto.WipHeaderFk && e.BoqHeaderFk == readDto.BoqHeaderFk && filteredBoqIds.Contains(e.BoqItemFk)).ToList();
				}
				else if (readDto.IsPesBoq)
				{
					qto = qtos.Where(e => e.QtoTargetType == (int)QtoTargetType.PesBoq).FirstOrDefault();
					qtoDetails = allQtoDetails.Where(e => e.PesHeaderFk.HasValue).Where(e => e.QtoHeaderFk == qto.Id && e.PesHeaderFk.Value == readDto.PesHeaderFk && e.IsIQ && e.BoqHeaderFk == readDto.BoqHeaderFk && filteredBoqIds.Contains(e.BoqItemFk)).ToList();
				}
				else if (readDto.IsPrjBoq || readDto.IsPrcBoq)
				{
					qtoDetails = qto != null ? allQtoDetails.Where(e => e.QtoHeaderFk == qto.Id && e.BoqHeaderFk == readDto.BoqHeaderFk && filteredBoqIds.Contains(e.BoqItemFk)).ToList() : new List<QtoDetailEntity>();
				}
				else
				{
					qtoDetails = allQtoDetails;
				}

				GenerateQtoDetailSplitFromReference(qtoDetails, allQtoDetails);
			}
			else
			{
				qto = new QtoHeaderLogic().GetItemByKey(readDto.MainItemId);
				qtoDetails = Logic.GetListByQtoHeaderId(readDto.MainItemId).Where(e => (
					 (readDto.Locations.Count() == 0) || (e.PrjLocationFk.HasValue && readDto.Locations.Contains(e.PrjLocationFk.Value)))
					 && ((readDto.BillTos.Count() == 0) || (e.BillToFk.HasValue && readDto.BillTos.Contains(e.BillToFk.Value)))
					 && ((readDto.Boqs.Count() == 0) || (readDto.Boqs.Contains(e.BoqItemFk)))).ToList();

				GenerateQtoDetailSplitFromReference(qtoDetails);
				qtoDetailHelper.UpdateQtoDetailGroupInfo(qtoDetails);
			}

			// filter by boq split item
			if (readDto.BoqSplitQuantityFk.HasValue)
			{
				qtoDetails = qtoDetails.Where(e => e.BoqSplitQuantityFk == readDto.BoqSplitQuantityFk).ToList();
			}

			// filter by costGroup
			if (readDto.CostGroupFks != null && readDto.CostGroupFks.Any())
			{
				var prjCostGroupLogic = Injector.Get<IPrjCostGroupLogic>();
				var costCalatogFks = prjCostGroupLogic.GetCostGroupByIds(readDto.CostGroupFks.Select(x => (int?)x).ToList()).Select(e => e.CostGroupCatalogFk).Distinct().ToList();

				var qtoDetailIds = qtoDetails.Select(e => e.Id).ToList();
				var qtoDetail2CostGroups = Logic.GetQtoDetail2CostGroupEntities(qtoDetailIds).Select(e => new MainItem2CostGroupDto(e)).ToList();

				foreach (var costCalatogFk in costCalatogFks)
				{
					var mainids = qtoDetail2CostGroups.Where(x => x.CostGroupCatFk == costCalatogFk && readDto.CostGroupFks.Contains(x.CostGroupFk)).Select(x => x.MainItemId);
					qtoDetails = qtoDetails.Where(e => mainids.Contains(e.Id)).ToList();
				}
			}

			if (pageNumbers.Count > 0)
			{
				var qtoSheets = new QtoStructrueLogic().GetItemsByKey(pageNumbers);
				var qtoSheetNums = qtoSheets.Select(e => e.PageNumber).ToList();
				qtoDetails = qtoDetails.Where(e => (pageNumbers.Contains(e.QtoSheetFk) && qtoSheetNums.Contains(e.PageNumber)) || qtoSheetNums.Contains(e.PageNumber)).ToList();
			}

			if (readDto.LineItemIds != null && readDto.LineItemIds.Any() && readDto.EstHeaderIds != null && readDto.EstHeaderIds.Any())
			{
				var estHeaderIdList = readDto.EstHeaderIds.ToList();
				var lineItemIdsList = readDto.LineItemIds.ToList();
				qtoDetails = qtoDetails.Where(e => e.EstHeaderFk.HasValue && estHeaderIdList.Contains(e.EstHeaderFk.Value)
				&& e.EstLineItemFk.HasValue && lineItemIdsList.Contains(e.EstLineItemFk.Value)).ToList();
			}

			// set qto atr
			int qtoTypeFk = qto != null ? qto.QtoTypeFk : 0;
			foreach (var qtoDetail in qtoDetails)
			{
				qtoDetail.HasSplitQuantiy = qtoDetail.BoqSplitQuantityFk.HasValue;
				qtoDetail.HasEstLineItem = qtoDetail.EstLineItemFk.HasValue;
				qtoDetail.QtoTypeFk = qtoTypeFk;
			}

			// filter by wip or bill reference
			if (readDto.IsFilterByNoWipOrBilActive)
			{
				qtoDetails = qtoDetails.Where(e => !e.WipHeaderFk.HasValue && !e.BilHeaderFk.HasValue).ToList();
			}

			// Filter by cancellation / correction Bill
			if (readDto.IsFilterByCorrectionBillActive)
			{
				var billFks = qtoDetails.Where(e => e.BilHeaderFk.HasValue).CollectIds(e => e.BilHeaderFk.Value).ToList();
				var filterBillFks = Logic.GetCorrectionBillFks(billFks);
				if (filterBillFks != null)
				{
					qtoDetails = qtoDetails.Where(e => !e.BilHeaderFk.HasValue || (e.BilHeaderFk.HasValue && !filterBillFks.Contains(e.BilHeaderFk.Value))).ToList();
				}
			}

			var main = qtoDetails.OrderBy(n => n.PageNumber)
			.ThenBy(n => n.LineReference)
			.ThenBy(n => n.LineIndex)
			.ToDtos(e => new QtoDetailDto(e)).ToList();

			GenerateQtoDetailReference(main);

			if (main.Any())
			{
				var qtoHeader = new QtoHeaderLogic().GetItemByKey(main[0].QtoHeaderFk);
				int? prjChangeId = null;
				if (qtoHeader.ConHeaderFk.HasValue)
				{
					var contract = Injector.Get<IContractHeaderInfoProvider>().GetConHeaderById(qtoHeader.ConHeaderFk.Value);
					prjChangeId = contract != null ? contract.ProjectChangeFk : null;
				}
				if (qtoHeader.OrdHeaderFk.HasValue)
				{
					var contract =
						 Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesHeaderLogic>
								 ("Contract").GetSalesContractsByKey(new List<int?>() { qtoHeader.OrdHeaderFk }).ToList();
					prjChangeId = contract.Any() ? contract[0].PrjChangeFk : null;
				}
				if (prjChangeId.HasValue)
				{
					var prjChange =
						 Injector.Get<IChangeMainLogic>("Change.Main.ChangeEntity")
								 .GetChangeWithStatusByIds(new List<int>() { prjChangeId.Value })
							  .FirstOrDefault();

					if (prjChange != null)
					{
						var changeStateIsReadonly = (qtoHeader.OrdHeaderFk.HasValue && !prjChange.IsAllowedQtoForSales) ||
															 (qtoHeader.ConHeaderFk.HasValue && !prjChange.IsAllowedQtoForProc);
						foreach (var qtoDetailDto in main)
						{
							qtoDetailDto.PrjChangeStutasReadonly = changeStateIsReadonly;
						}
					}

				}

				var saleContractIds = main.Where(x => !x.PrjChangeStutasReadonly && x.OrdHeaderFk.HasValue).Select(x => x.OrdHeaderFk).ToList();
				if (saleContractIds.Any())
				{
					var salesContractEntities = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesHeaderLogic>
							  ("Contract").GetSalesContractsByKey(saleContractIds.Distinct()).ToList();
					var prjChangeFks = salesContractEntities.Where(x => x.PrjChangeFk.HasValue).Select(x => x.PrjChangeFk.Value).ToList();
					var prjChanges =
						 Injector.Get<IChangeMainLogic>("Change.Main.ChangeEntity")
								 .GetChangeWithStatusByIds(prjChangeFks).ToList();
					foreach (var dto in main)
					{
						if (dto.OrdHeaderFk.HasValue)
						{
							var contract = salesContractEntities.FirstOrDefault(x => x.Id == dto.OrdHeaderFk);
							dto.PrjChangeStutasReadonly = contract != null && contract.PrjChangeFk.HasValue && prjChanges.Any(x => x.Id == contract.PrjChangeFk) && !prjChanges.FirstOrDefault(x => x.Id == contract.PrjChangeFk).IsAllowedQtoForSales;
						}
					}
				}
			}

			qtoHeaderId = qto != null ? qto.Id : qtoHeaderId;

			return main;
		}

		private void GenerateQtoDetailReference(IEnumerable<QtoDetailDto> items)
		{
			var qtoDetailDtos = items as IList<QtoDetailDto> ?? items.ToList();
			if (qtoDetailDtos.Any())
			{
				foreach (var item in qtoDetailDtos)
				{
					//page number+line reference+line index
					item.QtoDetailReference = Logic.ConvertQtoDetailReference(item.Copy());
				}
			}
		}

		private void GenerateQtoDetailSplitFromReference(IEnumerable<QtoDetailEntity> items, IEnumerable<QtoDetailEntity> allItems = null)
		{
			if(allItems == null)
			{
				allItems = items;
			}

			var qtoDetailDtos = items as IList<QtoDetailEntity> ?? items.ToList();
			var splitFromQtoDetailDtos = qtoDetailDtos.Where(e => e.QtoDetailSplitFromFk.HasValue);

			if (qtoDetailDtos.Any() && splitFromQtoDetailDtos.Any())
			{
				foreach (var item in splitFromQtoDetailDtos)
				{
					var splitFromReference = allItems.FirstOrDefault(e => e.Id == item.QtoDetailSplitFromFk.Value);

					//page number+line reference+line
					if(splitFromReference != null)
					{
						item.QtoDetailSplitFromReference = Logic.ConvertQtoDetailReference(splitFromReference);
					}
				}
			}
		}

		private void FilterFormulaOperatorByUom(IEnumerable<QtoDetailDto> items)
		 {
			  var itemIncludsUom = items.Where(x => x.BasUomFk > 0).ToList();
				if (!itemIncludsUom.Any()) return;

			  var qtoFormulaUomLogic = new QtoFormulaUomLogic();
			  var formulaFks = itemIncludsUom.Select(e => e.QtoFormulaFk).Distinct().ToList();
				var formulaUoms = qtoFormulaUomLogic.GetSearchList(x => formulaFks.Contains(x.QtoFormulaFk)).ToList();

				foreach (var qtoDetailDto in itemIncludsUom)
			  {
				if (qtoDetailDto.QtoFormula != null)
				{
					var formulaUom = formulaUoms.FirstOrDefault(x => x.QtoFormulaFk == qtoDetailDto.QtoFormulaFk && x.UomFk == qtoDetailDto.BasUomFk);
					if (formulaUom != null)
					{
						qtoDetailDto.QtoFormula.Operator1 = formulaUom.Operator1;
						qtoDetailDto.QtoFormula.Operator2 = formulaUom.Operator2;
						qtoDetailDto.QtoFormula.Operator3 = formulaUom.Operator3;
						qtoDetailDto.QtoFormula.Operator4 = formulaUom.Operator4;
						qtoDetailDto.QtoFormula.Operator5 = formulaUom.Operator5;
						qtoDetailDto.QtoFormula.Value1IsActive = formulaUom.Value1IsActive;
						qtoDetailDto.QtoFormula.Value2IsActive = formulaUom.Value2IsActive;
						qtoDetailDto.QtoFormula.Value3IsActive = formulaUom.Value3IsActive;
						qtoDetailDto.QtoFormula.Value4IsActive = formulaUom.Value4IsActive;
						qtoDetailDto.QtoFormula.Value5IsActive = formulaUom.Value5IsActive;
					}
				}
			  }
		 }

		 /// <summary>
		/// GetListByQtoHeaderId
		/// </summary>
		/// <returns></returns>
		[Route("getListByQtoHeaderId")]
		[HttpGet]
		public object GetListByQtoHeaderId(int qtoHeaderId, string type)
		{
			Stopwatch sw1 = new Stopwatch();
			StringBuilder timeStr = new StringBuilder();
			sw1.Start(); 
			var entities = this.Logic.GetListByQtoHeaderId(qtoHeaderId);
			sw1.Stop();
			timeStr.AppendLine("function  GetListByQtoHeaderId :--------->" + sw1.ElapsedMilliseconds + " ms");

			var qtoDetailEntityList = new List<object>();
			var qtoLinesLength = 0;
			var hasEmtpyQtos = false;
			if (entities.Any())
			{
				qtoLinesLength = entities.Count();

				// Allow Create/Update WIP/BILL without WIP/BILL Code assignment checking
				var allowCUWipBillWithoutWipBillAssginment = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.AllowCUWipBillWithoutWipBillAssginment, false);

				if (type == "wip")
				{
					hasEmtpyQtos = (allowCUWipBillWithoutWipBillAssginment && entities.Any(qto => !qto.WipHeaderFk.HasValue)) || entities.Any(qto => !qto.BilHeaderFk.HasValue && !qto.WipHeaderFk.HasValue);
				}
				else if (type == "bill")
				{
					hasEmtpyQtos = (allowCUWipBillWithoutWipBillAssginment && entities.Any(qto => !qto.BilHeaderFk.HasValue)) || entities.Any(qto => !qto.BilHeaderFk.HasValue && !qto.WipHeaderFk.HasValue);
				}
				else if(type == "pes")
				{
					hasEmtpyQtos = entities.Any(qto => !qto.PesHeaderFk.HasValue);
				}

				foreach (var entity in entities)
				{
					var qtoDetail = new
					{
						Id = entity.Id,
						WipHeaderFk = entity.WipHeaderFk,
						BilHeaderFk = entity.BilHeaderFk,
						PesHeaderFk = entity.PesHeaderFk,
						QtoHeaderFk = entity.QtoHeaderFk,
						PageNumber= entity.PageNumber,
						LineReference=entity.LineReference,
						LineIndex = entity.LineIndex ,
						QtoDetailReference = entity.QtoDetailReference,
						Version = entity.Version
					};
					qtoDetailEntityList.Add(qtoDetail);
				}
			}

			return new
			{
				qtoLinesLength = qtoLinesLength,
				hasEmtpyQtos = hasEmtpyQtos,
				timeStr = timeStr.ToString(),
				QtoDetailEntityList = qtoDetailEntityList
			};
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="qtoHeaderId"></param>
		/// <param name="wipHeaderFk"></param>
		/// <returns></returns>
		[Route("getQtoCountByWipId")]
		[HttpGet]
		public int GetQtoCountByWipId(int qtoHeaderId, int wipHeaderFk)
		{
			int count = -1;
			var entities = this.Logic.GetSearchList(e => e.QtoHeaderFk == qtoHeaderId);

			if (entities.Any())
			{
				count = entities.Where(e => e.WipHeaderFk.HasValue && e.WipHeaderFk == wipHeaderFk).Count();
			}

			return count;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="qtoHeaderId"></param>
		/// <param name="bilHeaderFk"></param>
		/// <returns></returns>
		[Route("getQtoCountByBillId")]
		[HttpGet]
		public int GetQtoCountByBillId(int qtoHeaderId, int bilHeaderFk)
		{
			int count = -1;
			var entities = this.Logic.GetSearchList(e => e.QtoHeaderFk == qtoHeaderId);

			if (entities.Any())
			{
				count = entities.Where(e => e.BilHeaderFk.HasValue && e.BilHeaderFk == bilHeaderFk).Count();
			}

			return count;
		}

		/// <summary>
		/// is unique for address
		/// </summary>
		/// <returns></returns>
		[Route("ismapqtoaddress"), HttpPost]
		[HttpPost]
		public object IsMapQtoAddress(CheckQtoDetailReferenceUniqueParam paramObject)
		{
			var qtoAddressRangeLogic = new QtoAddressRangeLogic();
			var qtoAddressRangeDetails = new QtoAddressRangeDetailLogic().GetQtoAddressRangeDetails(paramObject.QtoHeaderId, QtoAddressRangeType.Dialog);
			var qtoAddressRange = new QtoAddressRangeDetailLogic().GetQtoAddressRangeString(qtoAddressRangeDetails);

			bool errorSheet = false;
			bool errorIndex = false;
			bool errorLine = false;

			if (qtoAddressRange != null)
			{
				var sheetAreaList = qtoAddressRangeLogic.getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogSheetArea).Select(e => int.Parse(e.ToString())).ToList();
				var indexAreaList = qtoAddressRangeLogic.getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogIndexArea).Select(e => int.Parse(e.ToString())).ToList();
				var lineAreaList = qtoAddressRangeLogic.getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogLineArea).ToList();

				errorSheet = sheetAreaList.Any() && !sheetAreaList.Contains(paramObject.PageNumber[0]);
				errorIndex = indexAreaList.Any() && !indexAreaList.Contains(paramObject.LineIndex[0]);
				errorLine = lineAreaList.Any() && !lineAreaList.Contains(paramObject.LineReference[0]);
			}

			bool isUnique = Logic.IsQtoDetailReferenceUnique(paramObject.Id, paramObject.QtoHeaderId, paramObject.PageNumber, paramObject.LineReference, paramObject.LineIndex);

			bool isSheetReadonly = false;
			bool isLiveOrReadable = true;
			if (paramObject.IsCheckedSheet && paramObject.PageNumber.Length > 0)
			{
				int pageNumver = paramObject.PageNumber[0];
				var qtoStructureLogic = new QtoStructrueLogic();
				var sheet = qtoStructureLogic.GetSearchList(e => e.QtoHeaderFk == paramObject.QtoHeaderId && e.PageNumber.HasValue && e.PageNumber.Value == pageNumver).FirstOrDefault();
				if (sheet != null)
				{
					isSheetReadonly = sheet != null && sheet.IsReadonly;

					if (!isSheetReadonly)
					{
						isLiveOrReadable = qtoStructureLogic.IsLiveOrReadable(sheet);
					}
				}
			}

			return new
			{
				IsUnique = isUnique,
				IsSheetReadonly = isSheetReadonly,
				ErrorSheet = errorSheet,
				ErrorIndex = errorIndex,
				ErrorLine = errorLine,
				IsLiveOrReadable = isLiveOrReadable
			};
		}

		/// <summary>
		/// get the next line address
		/// </summary>
		[Route("getnextlineaddress"), HttpPost]
		public object GetLastQtoDetail(ParamForGettingLastQtoDetail param)
		{
			int? qtoTypeFk = null;
			if (param.IsPrjBoq || param.IsPrcBoq || param.IsBillingBoq || param.IsWipBoq || param.IsPesBoq)
			{
				var qtoHeaders = new QtoHeaderLogic().GetQtoHeaderByBoqHeaderId(param.BoqHeaderFk);
				if (qtoHeaders.Any())
				{
					if (param.IsPrcBoq || param.IsPrjBoq)
					{
						var qtoHeader = qtoHeaders.FirstOrDefault(e => e.QtoTargetType == (int)QtoTargetType.PrcWqAq || e.QtoTargetType == (int)QtoTargetType.PrjWqAq);
						param.QtoHeaderFk = qtoHeader.Id;
						qtoTypeFk = qtoHeader.QtoTypeFk;
					}
					else if (param.IsPesBoq)
					{
						var qtoHeader = qtoHeaders.FirstOrDefault(e => e.QtoTargetType == (int)QtoTargetType.PesBoq);
						param.QtoHeaderFk = qtoHeader.Id;
						qtoTypeFk = qtoHeader.QtoTypeFk;
					}
					else if (param.IsWipBoq || param.IsBillingBoq)
					{
						var qtoHeader = qtoHeaders.FirstOrDefault(e => e.QtoTargetType == (int)QtoTargetType.WipOrBill);
						param.QtoHeaderFk = qtoHeader.Id;
						qtoTypeFk = qtoHeader.QtoTypeFk;
					}
				}
			}

			var qtoAddressRangeDetails = new QtoAddressRangeDetailLogic().GetQtoAddressRangeDetails(param.QtoHeaderFk ?? 0, QtoAddressRangeType.Dialog);
			var qtoAddressRange = new QtoAddressRangeDetailLogic().GetQtoAddressRangeString(qtoAddressRangeDetails);

			var qtoList = Logic.GetListByQtoHeaderId(param.QtoHeaderFk ?? 0);
			var filterQtoList = new List<QtoDetailEntity>();
			if (qtoAddressRange!=null)
			{
				var sheetAreaList = new QtoAddressRangeLogic().getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogSheetArea).Select(e => int.Parse(e.ToString())).OrderBy(e => e).ToList();
				var indexAreaList = new QtoAddressRangeLogic().getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogIndexArea).Select(e => int.Parse(e.ToString())).OrderBy(e => e).ToList();
				var lineAreaList = new QtoAddressRangeLogic().getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogLineArea).OrderBy(e => e).ToList();
				Logic.SetSheetAreaList(sheetAreaList)
					.SetIndexAreaList(indexAreaList)
					.SetLineAreaList(lineAreaList)
					.SetRanges();

				filterQtoList.AddRange(Logic.FilterQtoLinesByRange(qtoList));
			}
			else
			{
				filterQtoList.AddRange(qtoList);
			}

			QtoDetailEntity selectItem = param.SelectItem != null ? param.SelectItem.Copy() : Logic.GetLastQtoDetailByPageNumber(qtoList, param.QtoSheetFk ?? 0);

			// if the selectitem sheet is readonly, set as null
			var qtoSheets = new QtoStructrueLogic().GetSheetReadonlyByStatus(param.QtoHeaderFk ?? 0).ToList();

			if (selectItem != null)
			{
				var qtoSheet = qtoSheets.FirstOrDefault(e => e.Id == selectItem.QtoSheetFk);
				if (qtoSheet != null && qtoSheet.IsReadonly)
				{
					selectItem = null;
					param.IsInsert = false;
				}
			}

			// check multi or not
			if (filterQtoList != null && filterQtoList.Any() && selectItem != null)
			{
				var qtoLine = Logic.GetQtoDetailByUser(filterQtoList, selectItem);
				if (qtoLine != null)
				{
					if (qtoLine.Id > 0)
					{
						selectItem = qtoLine;
					}
					else
					{
						param.SelectedPageNumber = qtoLine.PageNumber.ToString();
						selectItem = null;
					}
				}
			}

			// already config address range
			if (qtoAddressRange != null)
			{
				selectItem = Logic.GetLastItemByAdrressRange(filterQtoList, selectItem);
			}

			if (!qtoTypeFk.HasValue)
			{
				var qtoHeader = new QtoHeaderLogic().GetItemByKey(param.QtoHeaderFk);
				qtoTypeFk = qtoHeader != null ? qtoHeader.QtoTypeFk : null;
			}

			var multiLines = param != null && param.MultiLines != null ? param.MultiLines.Select(e => e.Copy()).ToList() : null;
			Dictionary<string, LineAddress> lineAddressDic =Logic.SetQtoTypeFk(qtoTypeFk)
				.GetNextLineAddress(selectItem, param.SelectedPageNumber, param.QtoHeaderFk ?? 0, param.IsInsert, filterQtoList.ToList(), multiLines, qtoSheets, qtoList.ToList());
			;
			return lineAddressDic;
		}

		/// <summary>
		/// New item
		/// </summary>
		[Route("create")]
		[HttpPost]
		public QtoDetailDto Create(ParentIdentifier data)
		{
			LineAddress lineAddress = data.LastLineAddress;

			if (data.IsPrjBoq || data.IsPrcBoq || data.IsBillingBoq || data.IsWipBoq || data.IsPesBoq)
			{
				var qtoHeaders = new QtoHeaderLogic().GetQtoHeaderByBoqHeaderId(data.BoqHeaderFk);

				if (data.IsPrjBoq || data.IsPrcBoq)
				{
					var qtoHeader = qtoHeaders.FirstOrDefault(e => e.QtoTargetType == (int)QtoTargetType.PrcWqAq || e.QtoTargetType == (int)QtoTargetType.PrjWqAq);
					if (qtoHeader == null)
					{
						Exception exception = null;
						if (data.IsPrjBoq)
						{
							exception = new Exception(Resources.ERR_Prj_CreateQtoLineFailed);
						}
						else if (data.IsPrcBoq)
						{
							exception = new Exception(Resources.ERR_Prc_CreateQtoLineFailed);
						}

						if(exception != null)
						{
							throw exception;
						}
					}
					else
					{
						data.QtoHeaderFk = qtoHeader.Id;
						data.BasRubricCategoryFk = qtoHeader.BasRubricCategoryFk;
						data.QtoTypeFk = qtoHeader.QtoTypeFk;
					}									
				}
				else if (data.IsBillingBoq || data.IsWipBoq)
				{
					var qtoHeader = qtoHeaders.FirstOrDefault(e => e.QtoTargetType == (int)QtoTargetType.WipOrBill);
					if (qtoHeader == null)
					{
						var exception = new Exception(Resources.ERR_wipOrBilling_CreateQtoLineFailed);
						throw exception;
					}
					data.QtoHeaderFk = qtoHeader.Id;
					data.BasRubricCategoryFk = qtoHeader.BasRubricCategoryFk;
					data.QtoTypeFk = qtoHeader.QtoTypeFk;
				}
				else if (data.IsPesBoq)
				{
					var qtoHeader = qtoHeaders.FirstOrDefault(e => e.QtoTargetType == (int)QtoTargetType.PesBoq);
					if (qtoHeader == null)
					{
						var exception = new Exception(Resources.ERR_pes_CreateQtoLineFailed);
						throw exception;
					}
					data.QtoHeaderFk = qtoHeader.Id;
					data.BasRubricCategoryFk = qtoHeader.BasRubricCategoryFk;
					data.QtoTypeFk = qtoHeader.QtoTypeFk;
				}
			}

			var qtoDetailDto = new QtoDetailDto(Logic.Create(data, lineAddress))
			{
				BilHeaderFk = data.BilHeaderFk,
				WipHeaderFk = data.WipHeaderFk,
				PesHeaderFk = data.PesHeaderFk,
				QtoTypeFk = data.QtoTypeFk
			};

			if (qtoDetailDto.BoqItemFk > 0)
			{
				var boqItemEntity = new BoqItemLogic().GetBoqItemById(qtoDetailDto.BoqItemFk);
				// set boqItem Code for boq item lookup
				if (boqItemEntity != null && (boqItemEntity.BoqLineTypeFk == 0 || boqItemEntity.BoqLineTypeFk == 11))
				{
					var boqItemDto = new BoqItemDto(boqItemEntity);
					qtoDetailDto.BoqItemCode = boqItemDto.Reference;
				}
			}

			var qtoLineTypeEntity = new QtoLineTypeLogic().GetByLongId(qtoDetailDto.QtoLineTypeFk);
			if (qtoLineTypeEntity != null)
			{
				qtoDetailDto.QtoLineTypeCode = qtoLineTypeEntity.CodeInfo.Description;
			}

			if (qtoDetailDto.QtoFormulaFk.HasValue)
			{
				var qtoFormula = new QtoFormulaDto(new QtoFormulaLogic().GetItemByKey(qtoDetailDto.QtoFormulaFk));

				if (qtoFormula != null)
				{
					qtoFormula.QtoFormulaScriptEntities = new QtoFormulaScriptLogic().GetSearchList(qtoFormula.Id).Select(e => new QtoFormulaScriptDto(e)).ToList();
					qtoDetailDto.QtoFormula = qtoFormula;
				}
			}

			if (!qtoDetailDto.QtoTypeFk.HasValue)
			{
				var qtoHeader = new QtoHeaderLogic().GetItemByKey(qtoDetailDto.QtoHeaderFk);
				qtoDetailDto.QtoTypeFk = qtoHeader.QtoTypeFk;
			}

			qtoDetailDto.IsModifyLineReference = true;
			return qtoDetailDto;
		}

		/// <summary>
		/// get the json validation schema of milestone entity.
		/// </summary>
		/// <returns>a json validation schema of milestone entity.</returns>
		[Route("schema")]
		public HttpResponseMessage GetSchema()
		{
			return GetJsonSchema(typeof(QtoDetailDto));
		}

		/// <summary>
		/// IsExistLocationUse
		/// </summary>
		/// <param name="locationIds"></param>
		/// <returns></returns>
		[Route("isExistLocationUse")]
		[HttpPost]
		public bool IsExistLocationUse(IEnumerable<int> locationIds)
		{
			return Logic.isExistUseLocation(locationIds);
		}

		/// <summary>
		/// GetNew item
		/// </summary>
		/// <returns></returns>
		[Route("createitems")]
		[HttpPost]
		public object Create(ParamForGettingLastQtoDetail data)
		{
			var qtoLines = data.items.Select(e => e.Copy()).ToList();
			var qtoHeaderFk = data.QtoHeaderFk ?? 0;

			if (data.QtoTypeFk.HasValue)
			{
				Logic.SetQtoTypeFk(data.QtoTypeFk);
			}
			else
			{
				var qtoHeader = new QtoHeaderLogic().GetItemByKey(data.QtoHeaderFk);
				Logic.SetQtoTypeFk(qtoHeader.QtoTypeFk);
			}

			// collect inculde mutilines and sort copy items
			var qtoList = Logic.GetListByQtoHeaderId(qtoHeaderFk).ToList();

			var qtoAddressRangeDetails = new QtoAddressRangeDetailLogic().GetQtoAddressRangeDetails(qtoHeaderFk, QtoAddressRangeType.Dialog);
			var qtoAddressRange = new QtoAddressRangeDetailLogic().GetQtoAddressRangeString(qtoAddressRangeDetails);		

			bool hasMultiLine = false;
			if (!data.IsFromUserForm && !data.IsSearchCopy && !data.IsSplitLine)
			{
				qtoLines = Logic.CollectAndSortQtoDetails(qtoLines, qtoList, ref hasMultiLine).ToList();
			}

			List<MainItem2CostGroupEntity> costGroups = null;
			IEnumerable<QtoDetailEntity> qtoDetailDtos;
			IEnumerable<QtoDetailDto> qtoDetails;
			bool isOverflow = false;

			if (data.SelectItem != null)  // copy qto detail in qto container
			{
				costGroups = new List<MainItem2CostGroupEntity>();
				qtoDetailDtos = Logic.Create(
					 qtoLines,
					 qtoHeaderFk,
					 data.BasRubricCategoryFk,
					 data.IsNotCpoyCostGrp ? null : costGroups,
					 data.BoqSplitQuantityFk
				);

				var selectItem = data.SelectItem?.Copy();
				qtoDetails = GetTargetAddressQtoDetailDto(qtoDetailDtos, selectItem, data, out isOverflow, qtoAddressRange, qtoList);
			}
			else if (data.QtoSheetFk > 0)  // copy detail to qto sheet
			{
				qtoDetailDtos = Logic.Create(
					qtoLines,
					qtoHeaderFk,
					data.BasRubricCategoryFk,
					null,
					data.BoqSplitQuantityFk
				);

				QtoDetailEntity qtoDetail = Logic.GetLastQtoDetailByPageNumber(qtoList, data.QtoSheetFk ?? 0);
				qtoDetails = GetTargetAddressQtoDetailDto(qtoDetailDtos, qtoDetail, data, out isOverflow, qtoAddressRange, qtoList);
			}
			else  // copy qto detail to BOQ
			{
				costGroups = new List<MainItem2CostGroupEntity>();
				qtoDetailDtos = Logic.Create(
					qtoLines,
					qtoHeaderFk,
					data.BasRubricCategoryFk,
					data.IsNotCpoyCostGrp ? null : costGroups,
					data.BoqSplitQuantityFk,
					data.IsNotCpoyCostGrp ? false : data.IsDrag,
					qtoList
				);

				var qtoLine = data.IsInsert ? qtoLines.LastOrDefault() : null;
				qtoDetails = GetTargetAddressQtoDetailDto(qtoDetailDtos, qtoLine, data, out isOverflow, qtoAddressRange, qtoList);
			}

			return new
			{
				QtoLines = qtoDetails,
				CostGroups = costGroups,
				CopyItems = hasMultiLine ? qtoLines : null,
				IsGeneratedNo = qtoAddressRange != null,
				IsOverflow = isOverflow
			};
		}

		/// <summary>
		/// get qto details by search and copy window 
		/// </summary>
		[Route("requestqtodetails")]
		[HttpPost]
		public IEnumerable<QtoDetailDto> GetRequestData(QtoTypeReQuest request)
		{
			var qtoDetailList = Logic.GetQtoDetailsFromRequestItem(request.Value, request.SelectQtoHeaderId).Select(e => new QtoDetailDto(e)).ToList();
			if (qtoDetailList.Any())
			{
				FixQtoDetail(qtoDetailList);
			}
			return qtoDetailList;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="qtoDetailList"></param>
		/// <param name="qtoFormulas"></param>
		/// <param name="boqItemDtos"></param>
		/// <param name="qtoFormularScriptEntities"></param>
		/// <param name="qtoLineTypeList"></param>
		private void FixQtoDetail(List<QtoDetailDto> qtoDetailList, IEnumerable<QtoFormulaEntity> qtoFormulas = null, List<BoqItemDto> boqItemDtos = null, IEnumerable<QtoFormulaScriptEntity> qtoFormularScriptEntities = null, IEnumerable<QtoLineTypeEntity> qtoLineTypeList = null)
		{
			var boqItemIds = qtoDetailList.CollectIds(e => e.BoqItemFk, e => e.BoqItemReferenceFk).ToList();
			if (boqItemDtos == null)
			{
				boqItemDtos = new BoqItemLogic().GetSearchList(e => boqItemIds.Contains(e.Id), boqItemIds.Count > 0).ToDtos(e => new BoqItemDto(e)).ToList();
			}

			var qtoFormulaFks = qtoDetailList.CollectIds(e => e.QtoFormulaFk).ToList();
			if (qtoFormulas == null)
			{
				qtoFormulas = new QtoFormulaLogic().GetItemsByKey(qtoFormulaFks).ToList();
			}

			if (qtoFormularScriptEntities == null)
			{
				qtoFormularScriptEntities = (new QtoFormulaScriptLogic()).GetListScriptEntities(qtoFormulaFks).ToList();
			}

			if (qtoLineTypeList == null)
			{
				qtoLineTypeList = new QtoLineTypeLogic().GetList();
			}

			// set detail by culture
			bool isSet = false;
			var transLogic = new RIB.Visual.Cloud.Common.BusinessComponents.TranslationLogic();
			string numberDecimalSep = "";
			if (transLogic.Context != null)
			{
				numberDecimalSep = transLogic.Context.CultureInfo.NumberFormat.NumberDecimalSeparator;
				if (!string.IsNullOrEmpty(numberDecimalSep))
				{
					isSet = true;
				}
			}

			Dictionary<int, BoqItemDto> boqItemDtoDic = boqItemDtos.ToDictionary(e => e.Id, e => e);

			var qtoHeaderId = qtoDetailList.FirstOrDefault().QtoHeaderFk;
			var qtoSheets = new QtoStructrueLogic().GetSearchList(e => e.QtoHeaderFk == qtoHeaderId);
			var qtoSheetDic = qtoSheets.ToDictionary(e => e.Id, e=> e);

			foreach (var item in qtoDetailList)
			{
				// var boqItem = boqItemDtos.FirstOrDefault(e => e.Id == item.BoqItemFk);
				var boqItem = boqItemDtoDic.ContainsKey(item.BoqItemFk) ? boqItemDtoDic[item.BoqItemFk] : null;
				if (boqItem != null)
				{
					item.BasUomFk = boqItem.BasUomFk;

					//Assign BoqItemCode for lookup
					item.BoqItemCode = boqItem.Reference;
				}

				var qtoLineType = qtoLineTypeList.FirstOrDefault(e => e.Id == item.QtoLineTypeFk);
				if (qtoLineType != null)
				{
					item.QtoLineTypeCode = qtoLineType.CodeInfo.Description;
				}

				if (item.QtoLineTypeFk != (int)EQtoLineType.Comment)
				{
					var qtoformula = qtoFormulas.FirstOrDefault(e => e.Id == item.QtoFormulaFk.GetValueOrDefault());
					if (qtoformula != null)
					{
						item.QtoFormula = new QtoFormulaDto(qtoformula);
						item.QtoFormula.QtoFormulaScriptEntities = qtoFormularScriptEntities.Where(x => x.QtoFormulaFk == item.QtoFormula.Id).Select(x => new QtoFormulaScriptDto(x)).ToList();

						if ((string.IsNullOrWhiteSpace(item.Value1Detail) &&
							 string.IsNullOrWhiteSpace(item.Value2Detail) &&
							 string.IsNullOrWhiteSpace(item.Value3Detail) &&
					 string.IsNullOrWhiteSpace(item.Value4Detail) &&
							 string.IsNullOrWhiteSpace(item.Value5Detail)) && string.IsNullOrWhiteSpace(item.LineText))
						{
							item.FormulaResult = "";
						}
					}
				}
				else
				{
					item.QtoFormulaFk = null;
				}

				// set value1 detail by culture
				if (isSet)
				{
					var decimalExpressRegex = new Regex(@"(?<=\d+)\.(?=\d+)");
					if (item.QtoLineTypeFk != (int)EQtoLineType.Comment && !String.IsNullOrEmpty(item.LineText))
					{
						string tempString = item.LineText.ToString();
						item.LineText = numberDecimalSep == "," ? tempString.Replace(".", numberDecimalSep) : tempString.Replace(",", numberDecimalSep);
					}

					if (!String.IsNullOrEmpty(item.FormulaResult))
					{
						string tempFormulaResult = item.FormulaResult.ToString();
						item.FormulaResult = decimalExpressRegex.Replace(tempFormulaResult, numberDecimalSep);
					}

				}

				// set sheet as readonly
				item.IsSheetReadonly = qtoSheetDic.ContainsKey(item.QtoSheetFk) ? qtoSheetDic[item.QtoSheetFk].IsReadonly : item.IsSheetReadonly;
			}

			// append the formulaResult were splited
			var groupId2QtoLines = qtoDetailList.GroupBy(e => e.QtoDetailGroupId).ToDictionary(e => e.Key, e => e.ToList());
			foreach (var item in groupId2QtoLines)
			{
				if (item.Value.Count > 0)
				{
					string formulaResult = "";
					foreach (var qtoLine in item.Value)
					{
						formulaResult += qtoLine.FormulaResult;
					}

					item.Value[item.Value.Count - 1].FormulaResultUI = formulaResult;
				}
			}
		}

		/// <summary>
		/// Validate formula and get detail reference No.
		/// </summary>
		/// <param name="qtoHeaderId"></param>
		/// <param name="exp"></param>
		/// <returns></returns>
		[Route("checkformulaandgetdetailrefs")]
		[HttpGet]
		public object CheckFormulaAndGetDetailReferences(int qtoHeaderId, string exp)
		{
			// QtoDetailReference is special legal chars, remove these chars first, then validate it.
			if (!string.IsNullOrWhiteSpace(exp)) { exp = (new Regex(@"(?<=\d*)[a-zA-Z]+\d")).Replace(exp, "1").Trim().Trim('='); }

			var errors = CalculateExpression.GetExpressionError(exp).ToList();

			var entities = this.Logic.GetListByQtoHeaderId(qtoHeaderId);
			if (entities.Any())
			{
				GenerateQtoDetailSplitFromReference(entities);

				foreach (var item in entities)
				{
					// page number+line reference+line index, if lineReference.length > 1, it is Onorm qto
					item.QtoDetailReference = Logic.ConvertQtoDetailReference(item);
				}
			}

			return new
			{
				CodeMap = entities.Select(x => new { id = x.Id, code = x.QtoDetailReference, result = x.Result, qtoDetailReferenceFk = x.QtoDetailReferenceFk }).ToList(),
				ReferenceDic = GetReferenceDic(entities),
				formulaError = errors
			};
		}

		private IEnumerable<QtoDetailDto> GetTargetAddressQtoDetailDto(IEnumerable<QtoDetailEntity> qtoDetailDtos, QtoDetailEntity entity, ParamForGettingLastQtoDetail data, out bool isOverflow, QtoAddressRangeStringHelper qtoAddressRange = null, List<QtoDetailEntity> qtoList = null)
		{
			isOverflow = false;
			var filterQtoList = new List<QtoDetailEntity>();
			if (qtoAddressRange != null)
			{
				var sheetAreaList = new QtoAddressRangeLogic().getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogSheetArea).Select(e => int.Parse(e.ToString())).OrderBy(e => e).ToList();
				var indexAreaList = new QtoAddressRangeLogic().getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogIndexArea).Select(e => int.Parse(e.ToString())).OrderBy(e => e).ToList();
				var lineAreaList = new QtoAddressRangeLogic().getSheetIndexLineAreaArray(qtoAddressRange, QtoAddressRangeAreaType.DialogLineArea).OrderBy(e => e).ToList();
				Logic.SetSheetAreaList(sheetAreaList)
					.SetIndexAreaList(indexAreaList)
					.SetLineAreaList(lineAreaList)
					.SetRanges();

				filterQtoList.AddRange(Logic.FilterQtoLinesByRange(qtoList).ToList());
			}
			else
			{
				filterQtoList.AddRange(qtoList);
			}

			// check multi or not
			if (filterQtoList != null && filterQtoList.Any() && entity != null)
			{
				var qtoLine = Logic.GetQtoDetailByUser(filterQtoList, entity);
				if (qtoLine != null)
				{
					if (qtoLine.Id > 0)
					{
						entity = qtoLine;
					}
					else
					{
						data.SelectedPageNumber = qtoLine.PageNumber.ToString();
						entity = null;
					}
				}
			}

			// already config address range
			if (qtoAddressRange != null)
			{
				entity = Logic.GetLastItemByAdrressRange(filterQtoList, entity);
			}

			if (entity != null)
			{
				entity.PageNumber = string.IsNullOrEmpty(data.SelectedPageNumber) ? entity.PageNumber : int.Parse(data.SelectedPageNumber);
			}

			// if the selectitem sheet is readonly, set as null
			var qtoSheets = new QtoStructrueLogic().GetSheetListWithStatusAccessRight(new List<int>() { data.QtoHeaderFk ?? 0 } ).ToList();

			if (entity != null && entity.Version == 0)
			{
				qtoList.Add(entity);
				qtoList = filterQtoList.OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList();
				data.IsInsert = false;
			}

			Dictionary<string, LineAddress> lineAddressDic = Logic.GetNextLineAddress(entity, data.SelectedPageNumber, data.QtoHeaderFk ?? 0, data.IsInsert, filterQtoList, null, qtoSheets, qtoList);

			List<QtoDetailDto> qtoDetails = (qtoDetailDtos != null && qtoDetailDtos.Any()) ? qtoDetailDtos.Select(e => new QtoDetailDto(e)).ToList() : new List<QtoDetailDto>();
			List<int> qtoIds = new List<int>();
			foreach (var qto in qtoDetails)
			{
				if (!qtoIds.Contains(qto.Id))
				{
					qto.IsModifyLineReference = true;

					qtoIds.Add(qto.Id);

					var lineAddress = new LineAddress();
					lineAddressDic.TryGetValue("TargetAddress", out lineAddress);
					isOverflow = Logic.CheckAddressIsOverflow(lineAddressDic, data.IsInsert);
					if (isOverflow)
					{
						return qtoDetails;
					}

					if (lineAddress != null)
					{
						qto.LineIndex = lineAddress.LineIndex;
						qto.LineReference = lineAddress.LineReference;
						qto.PageNumber = lineAddress.PageNumber;
						if (qtoDetails.Count > qtoIds.Count)
						{
							var qtoEntity = qto.Copy();
							filterQtoList.Add(qtoEntity);
							filterQtoList = filterQtoList.OrderBy(n => n.PageNumber).ThenBy(n => n.LineReference).ThenBy(n => n.LineIndex).ToList();
							qtoList.Add(qtoEntity);
							lineAddressDic = Logic.GetNextLineAddress(qtoEntity, data.SelectedPageNumber, data.QtoHeaderFk ?? 0, false, filterQtoList, null, qtoSheets, qtoList);

							isOverflow = Logic.CheckAddressIsOverflow(lineAddressDic, data.IsInsert);
							if (isOverflow)
							{
								return qtoDetails;
							}
						}
					}
				}
			}

			return qtoDetails;
		}

		/// <summary>
		/// get the dictionary:key: current qto detail reference; value: reference qto detail.
		/// </summary>
		private Dictionary<string, List<string>> GetReferenceDic(IEnumerable<QtoDetailEntity> entities)
		{
			Dictionary<string, List<string>> strReferenceDic = new Dictionary<string, List<string>>();
			foreach (var entity in entities)
			{
				string qtoReference = entity.QtoDetailReference;
				var codes = QtoDetailHelper.GetReferenceCodes(entity).ToList();
				if (codes.Count > 0 && !strReferenceDic.ContainsKey(qtoReference))
				{
					strReferenceDic.Add(qtoReference, codes);
				}
			}

			return strReferenceDic;
		}

		/// <summary>
		/// getpreviewcalresult
		/// </summary>
		/// <param name="req"></param>
		/// <returns></returns>
		[Route("getpreviewcalresult")]
		[HttpPost]
		public object GetPreviewCalResult(QtoDetailPreviewResultReq req)
		{
			bool useRoundedResult = false;

			if (req != null && (!req.QtoFormulaFk.HasValue || req.qtoDetails == null))
			{
				return null;
			}

			var qtoFormula = new QtoFormulaLogic().GetItemByKey(req.QtoFormulaFk.Value);

			ScriptQtoDetail endDetail;

			if (qtoFormula.IsMultiline)
			{
				endDetail = req.qtoDetails.FirstOrDefault(e => (e.Operator1 != null && e.Operator1.Equals("=")) || (e.Operator2 != null && e.Operator2.Equals("=")) || (e.Operator3 != null && e.Operator3.Equals("=")) ||
																			(e.Operator4 != null && e.Operator4.Equals("=")) || (e.Operator5 != null && e.Operator5.Equals("=")));
			}
			else
			{
				endDetail = req.qtoDetails.FirstOrDefault();
			}

			if (endDetail != null && qtoFormula != null)
			{
				QtoDetailEntity tmpDetail = new QtoDetailEntity()
				{
					Operator1 = endDetail.Operator1,
					Operator2 = endDetail.Operator2,
					Operator3 = endDetail.Operator3,
					Operator4 = endDetail.Operator4,
					Operator5 = endDetail.Operator5,
					Value1 = endDetail.Value1,
					Value2 = endDetail.Value2,
					Value3 = endDetail.Value3,
					Value4 = endDetail.Value4,
					Value5 = endDetail.Value5,
					Factor = endDetail.Factor,
					QtoFormulaFk = req.QtoFormulaFk,
					QtoFormula = qtoFormula
				};

				QtoFormulaCalculate formulaCalculator = QtoCalculatorFactory.Create((EQtoFormulaType)qtoFormula.QtoFormulaTypeFk, useRoundedResult, req.NoDecimals, new Dictionary<string, decimal>(), qtoFormula.BasRubricCategoryFk);
				var result = formulaCalculator.Calculate(tmpDetail, req.Goniometer, req.qtoDetails);

				return new
				{
					result = result,
					resultDetail = req.UseRoundedResults ? result.GetUseRoundedResults(req.UseRoundedResults, req.NoDecimals).ToString(string.Format("f{0}", req.NoDecimals)) : result.ToString(),
					executeResutl = formulaCalculator._executeResult,
					factorDetail = req.UseRoundedResults ? req.Factor.GetUseRoundedResults(req.UseRoundedResults, req.NoDecimals).ToString(string.Format("f{0}", req.NoDecimals)) : req.Factor.ToString()
				};
			}


			return null;
		}

		/// <summary>
		/// createorupdateformdata
		/// </summary>
		/// <param name="qtoHeaderFk"></param>
		/// /// <param name="formulaFormFk"></param>
		/// <returns></returns>
		[Route("createorupdateformdata")]
		  [HttpGet]
		  public void CreateOrUpdateFormData(int qtoHeaderFk, int formulaFormFk)
		  {
				if(qtoHeaderFk > 0 && formulaFormFk > 0)
				{
					 var userFormDataLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IUserFormDataLogic>();

					 userFormDataLogic.SaveRuleFormData(RubricConstant.Qto, qtoHeaderFk, formulaFormFk);
				}
			  
		  }

		/// <summary>
		/// calculate
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[Route("calculate")]
		[HttpPost]
		public IEnumerable<QtoDetailDto> Calculate(QtoCompleteDto dto)
		{
			bool useRoundedResult = false;
			var entity = dto.Copy();
			var qtoDetailToSave = new List<QtoDetailEntity>();
			if (entity.QtoDetailToSave != null && entity.QtoDetailToSave.Any())
			{
				foreach (var item in entity.QtoDetailToSave)
				{
					if (item.QtoDetail != null)
						qtoDetailToSave.Add(item.QtoDetail);
				}

				if (qtoDetailToSave.Count > 0)
				{
					QtoDetailHelper detailsToSave = new QtoDetailHelper(qtoDetailToSave, entity.QtoDetailToDelete, useRoundedResult, entity.NoDecimals);
					detailsToSave.SaveNoCalculate();
				}
			}

			qtoDetailToSave = Logic.GetSearchList(e => e.QtoHeaderFk == dto.QtoHeaderId).ToList();

			QtoDetailHelper detailHelper = new QtoDetailHelper(qtoDetailToSave, entity.QtoDetailToDelete, useRoundedResult, entity.NoDecimals);
			StringBuilder timestr = new StringBuilder();
			var qtoDataBase = detailHelper.SaveWithCheck(dto.Goniometer, ref timestr);
			var QtoDetailToSave = qtoDataBase.QtoUpdateDataFromClient;

			return QtoDetailToSave.Select(e => new QtoDetailDto(e)).ToList();
		}

		  /// <summary>
		  /// change status
		  /// </summary>
		  /// <returns></returns>
		  [Route("changestatus")]
		  [HttpPost]
		  public QtoDetailDto ChangeStatus(ChangeStatusDto dto)
		  {
				var entityStatus = new QtoDetailLogic().UpdateStatus(dto.EntityId, dto.NewStatusId);
				if (entityStatus == null)
				{
					 return null;
				}
				//Get full info
				var entity = Logic.GetItemByKey(entityStatus.Id);
				return entity == null ? null : new QtoDetailDto(entity);
		  }

		/// <summary>
		/// update the boq wq/aq by qto lines
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		[Route("updateqtoresult2boqqty")]
		[HttpPost]
		public bool UpdateQtoResult2BoqQty(QtoDetailReadDto param)
		{
			Logic.UpdateQtoResult2BoqQty(param.MainItemId, param.QtoScope, param.QtoDetailIds);
			return true;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="boqHeaderFk"></param>
		/// <param name="boqItemFk"></param>
		/// <returns></returns>
		[Route("checkQtoDetailHasSplitQtyFkOfBoqItem")]
		[HttpGet]
		public bool CheckQtoDetailHasSplitQtyFkOfBoqItem(int boqHeaderFk, int boqItemFk)
		{
			var result = false;
			var qtoDetailList = new QtoDetailLogic().GetQtoDetailsByBoqId(boqHeaderFk, boqItemFk).ToList();
			var hasSplitQtyList = qtoDetailList.Where(e => e.BoqSplitQuantityFk.HasValue).ToList();
			if (hasSplitQtyList.Count > 1) 
			{
				result = true;
			}
			return result;
		}

		/// <summary>
		/// get valid boq split quantities for the current boq item
		/// </summary>
		/// <param name="boqItemId"></param>
		/// <param name="boqHeaderId"></param>
		/// <returns></returns>
		[Route("getboqsplitquantitiesForQto")]
		public IEnumerable<BoqSplitQuantityDto> GetBoqSplitQuantities(int boqItemId, int boqHeaderId)
		{
			var qtoDetailList = new QtoDetailLogic().GetQtoDetailsByBoqId(boqHeaderId, boqItemId).ToList();
			var hasSplitQtyList = qtoDetailList.Where(e => e.BoqSplitQuantityFk.HasValue).ToList();
			if (qtoDetailList.Any() && !hasSplitQtyList.Any())
			{
				return new List<BoqSplitQuantityDto>();
			}

			var splitItems = new BoqSplitQuantityLogic().GetList(boqHeaderId, boqItemId).ToList();
			for (int i = 0; i < splitItems.Count; i++)
			{
				splitItems[i].SplitNo = i + 1;
			}
			return splitItems.Select(e => new BoqSplitQuantityDto(e)).ToList();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		[Route("getBoqReferenceForType7")]
		[HttpPost]
		public IEnumerable<BoqRefereHelper> getBoqReferenceForType7(QtoTypeReQuest param)
		{
			var qtoDetailList = this.Logic.GetListByQtoHeaderId(param.SelectQtoHeaderId);
			var boqItems = new BoqItemLogic().GetBoqItemsByBoqItemIds(param.BoqItemIds).ToList();

			var qtolist1 = qtoDetailList.GroupBy(e => e.BoqItemFk).ToDictionary(e => e.Key, e => e.ToList());
			var boqItemIds = qtoDetailList.Select(e => e.BoqItemFk).Distinct();
			var qtoRefereceBoqItem = new List<BoqRefereHelper>();
			foreach (var id in boqItemIds)
			{
				var boq = new BoqRefereHelper();
				if (id != param.SelectQtoDetail.BoqItemFk)
				{
					if (qtolist1.ContainsKey(id))
					{
						var qList = qtolist1[id].ToList();

						if (param.SelectQtoDetail.QtoLineTypeFk == 6 && param.SelectQtoDetail.PrjLocationReferenceFk.HasValue)
						{ 
							qList = qList.Where(e=>e.PrjLocationFk == param.SelectQtoDetail.PrjLocationReferenceFk).ToList(); 
						}
						if (qList.Any())
						{
							var resultTotal = qList.Sum(e => e.Result);
							if (param.SelectQtoDetail.QtoLineTypeFk == 6 && !param.SelectQtoDetail.PrjLocationReferenceFk.HasValue)
							{
								resultTotal = 0;
							}
							boq.Result = resultTotal;
							boq.Id = qList[0].BoqItemFk;
							var boqItem = boqItems.FirstOrDefault(e => e.BoqItemPrjItemFk == boq.Id );
							if (param.QtoBoqType == (int)QtoBoqType.PrcBoq || param.QtoBoqType == (int)QtoBoqType.PrjBoq)
							{
								boqItem = boqItems.FirstOrDefault(e => e.Id == boq.Id);
							}
							if (boqItem != null)
							{
								boq.Reference = boqItem.Reference;
								boq.BriefInfo = boqItem.BriefInfo.Description;
								boq.BasUomFk = boqItem.BasUomFk;
								qtoRefereceBoqItem.Add(boq);
							}
						}
					}
				}
			}
			return qtoRefereceBoqItem;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="copyParam"></param>
		/// <returns></returns>
		[Route("docopysourceqtolines")]
		[HttpPost]
		public object DoCopySourceQtoLines(SourceLinesCopyData copyParam)
		{
			List<QtoDetailEntity> qtoList = new List<QtoDetailEntity>();
			List<BoqItemEntity> boqItems = new List<BoqItemEntity>();
			List<BoqSplitQuantityEntity> boqSplitQtyList = new List<BoqSplitQuantityEntity>();
			List<MainItem2CostGroupEntity> costGroups = new List<MainItem2CostGroupEntity>();

			var newQtoLines = Logic.SetIsLocation(copyParam.IsLocation)
				.SetIsAssetMaster(copyParam.IsAssetMaster)
				.SetIsControllingUnit(copyParam.IsControllingUnit)
				.SetIsSortCode(copyParam.IsSortCode)
				.SetIsCostGroup(copyParam.IsCostGroup)
				.SetIsPrc(copyParam.IsPrc)
				.SetIsBillTo(copyParam.IsBillTo)
				.SetIsContract(copyParam.IsContract)
				.SetModuleName(copyParam.ModuleName)
				.SetWipHeaderFk(copyParam.WipHeaderFk)
				.SetBilHeaderFk(copyParam.BilHeaderFk)
				.SetPesHeaderFk(copyParam.PesHeaderFk)
				.SetQtoDetailCopyOption(copyParam.CopyOption)
				.DoCopySourceQtoLines(copyParam.IsOneProject, copyParam.SourceQtoHeaderFk, copyParam.TagetQtoHeaderFk, copyParam.BoqItem.Copy(), copyParam.QtoLineIds, out bool isOverflow, out bool isNewSheet, qtoList, costGroups, boqItems, boqSplitQtyList);

			var dtos = newQtoLines.Select(e => new QtoDetailDto(e));

			GenerateQtoDetailReference(dtos);

			var qtoDetialsOfAffectedBoq = qtoList.Any() ? qtoList.Select(e => new QtoDetailBoqCalculateInfo(e)) : new List<QtoDetailBoqCalculateInfo>();

			var costGroupDtos = costGroups.Select(e => new MainItem2CostGroupDto(e)).ToList();

			var boqItemDto = boqItems.Any() ? new BoqItemDto(boqItems.First()) : null;

			var boqSplitQtyItems = boqSplitQtyList.Select(e => new BoqSplitQuantityDto(e));

			return new
			{
				IsOverflow = isOverflow,
				IsNewSheet = isNewSheet,
				QtoLines = dtos,
				QtoDetialsOfAffectedBoq = qtoDetialsOfAffectedBoq,
				CostGroups = costGroupDtos,
				BoqItem = boqItemDto,
				BoqSplitQtyItems = boqSplitQtyItems
			};
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="id"></param>
		/// <param name="wipOrBill"></param>
		/// <returns></returns>
		[Route("getcontractinfo")]
		[HttpGet]
		public object getContractInfo(int id, string wipOrBill)
		{
			int? contractId = null;
			int? billToId = null;
			var ordHeaderIds = new List<int>();
			var salseContractLogic = Injector.Get<ISalesContractLogic>("Contract");
			if (wipOrBill.Equals("wip", StringComparison.OrdinalIgnoreCase))
			{
				var salesWipLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesWipHeaderLogic>();
				var wip = salesWipLogic.GetWipsByIds(new List<int>() { id }).FirstOrDefault();
				if (wip != null)
				{
					contractId = wip.OrdHeaderFk > 0 ? wip.OrdHeaderFk : null;
					var salesContractHeaders = salseContractLogic.GetRelatedContractsByWipId(wip.Id).ToList();
					if (salesContractHeaders.Any())
					{
						ordHeaderIds = salesContractHeaders.Select(e => e.Id).ToList();
					}
				}

			}
			else if (wipOrBill.ToLower() == "bill")
			{
				var salesBillingLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<ISalesBillingLogic>("bill");
				var bill = salesBillingLogic.GetBillById(id);
				if (bill != null)
				{
					contractId = bill.OrdHeaderFk;
				}
			}
			else if (wipOrBill.ToLower() == "billto")
			{
				var contract = salseContractLogic.GetContractByBillToId(id);
				if (contract != null)
				{
					contractId = contract.Id;					
				}
			}

			if (contractId.HasValue)
			{
				var ordHeader = salseContractLogic.GetContractById(contractId.Value, false);
				if (ordHeader != null && ordHeader.BillToFk.HasValue)
				{
					billToId = ordHeader.BillToFk.Value;
				}
			}

			return new
			{
				OrdHeaderFk = contractId,
				OrdHeaderIds = ordHeaderIds,
				BillToFk = billToId
			};
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="identificationData"></param>
		/// <returns></returns>
		[Route("getqtoheadersbywipid")]
		[HttpPost]
		public IEnumerable<QtoHeaderDto> GetQtoHeadersByWipId(RIB.Visual.Platform.Core.IdentificationData identificationData)
		{
			if (identificationData.PKey1 == null)
			{
				return [];
			}

			var wipId = identificationData.PKey1.Value;

			return Logic.GetQtoHeadersByWipId(wipId).Select(e => new QtoHeaderDto(e));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="identificationData"></param>
		/// <returns></returns>
		[Route("getqtoheadersbybillid")]
		[HttpPost]
		public IEnumerable<QtoHeaderDto> GetQtoHeadersByBillId(RIB.Visual.Platform.Core.IdentificationData identificationData)
		{
			if (identificationData.PKey1 == null)
			{
				return [];
			}

			var bilHeaderFk = identificationData.PKey1.Value;

			return Logic.GetQtoHeadersByBillId(bilHeaderFk).Select(e => new QtoHeaderDto(e));
		}

		/// <summary>
		/// 
		/// </summary>
		public class BoqRefereHelper {
			/// <summary>
			/// 
			/// </summary>
			public string Reference { get; set; }
			/// <summary>
			/// 
			/// </summary>
			public string BriefInfo { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int? BasUomFk { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public decimal Result { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int Id { get; set; }

		}


		/// <summary>
		/// 
		/// </summary>
		public class QtoTypeReQuest
		{
			/// <summary>
			/// 
			/// </summary>
			public string Value { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int SelectQtoHeaderId { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public List<int> BoqItemIds { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public QtoDetailDto SelectQtoDetail { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int QtoBoqType { get; set; }
		}

		  /// <summary>
		  /// 
		  /// </summary>
		  public class QtoDetailPreviewResultReq
		  {
				/// <summary>
				/// 
				/// </summary>
				public List<ScriptQtoDetail> qtoDetails { get; set; }

				/// <summary>
				/// 
				/// </summary>
				public int? QtoFormulaFk { get; set; }

				/// <summary>
				/// 
				/// </summary>
				public int NoDecimals { get; set; }

				/// <summary>
				/// 
				/// </summary>
				public decimal? Goniometer { get; set; }

				/// <summary>
				/// 
				/// </summary>
				public bool UseRoundedResults { get; set; }

				/// <summary>
				/// 
				/// </summary>
				public decimal Factor { get; set; }
		}

		/// <summary>
		/// 
		/// </summary>
		public class SourceLinesCopyData
		{
			/// <summary>
			/// 
			/// </summary>
			public string ModuleName { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int? WipHeaderFk { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int? BilHeaderFk { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int? PesHeaderFk { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public bool IsLocation { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public bool IsAssetMaster { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public bool IsControllingUnit { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public bool IsSortCode { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public bool IsCostGroup { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public bool IsPrc { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public bool IsBillTo { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public bool IsContract { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public bool IsOneProject { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int SourceQtoHeaderFk { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public int TagetQtoHeaderFk { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public BoqItemDto BoqItem { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public IEnumerable<int> QtoLineIds { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public QtoDetailCopyOption CopyOption { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public QtoDetailEntity SelectedSourceQtoDetail  { get; set; }			
		}		
	}
}
