﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Procurement.PublicApi.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Procurement.PublicApi.BusinessComponents.RfqHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("RFQ_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(4)]
    public partial class RfqHeaderApiEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new RfqHeaderApiEntity object.
        /// </summary>
        public RfqHeaderApiEntity()
        {
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RfqStatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int RfqStatusId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string RfqStatusDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string CompanyCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public virtual int? ProjectId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ProjectCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ProjectDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public virtual int? ClerkPrcId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkPrcCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkPrcDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public virtual int? ClerkReqId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkReqCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkReqDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CurrencyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string CurrencyDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_ID", TypeName = "int", Order = 16)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermFiFk")]
        public virtual int? PaymentTermFiId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_CODE", TypeName = "nvarchar(16)", Order = 17)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermFiCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_DESC", TypeName = "nvarchar(2000)", Order = 18)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermFiDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_ID", TypeName = "int", Order = 19)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermPaFk")]
        public virtual int? PaymentTermPaId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_CODE", TypeName = "nvarchar(16)", Order = 20)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermPaCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_DESC", TypeName = "nvarchar(2000)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermPaDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 22)]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 24)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public virtual string SearchPattern {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AwardReference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AWARD_REFERENCE", TypeName = "nvarchar(252)", Order = 25)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AwardReference")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string AwardReference {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REQUESTED", TypeName = "date", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateRequested")]
        public virtual System.DateTime? DateRequested {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateCanceled in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CANCELED", TypeName = "date", Order = 27)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCanceled")]
        public virtual System.DateTime? DateCanceled {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateQuotedeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_QUOTEDEADLINE", TypeName = "date", Order = 28)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateQuoteDeadline")]
        public virtual System.DateTime? DateQuotedeadline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TimeQuotedeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TIME_QUOTEDEADLINE", TypeName = "time", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TimeQuoteDeadline")]
        public virtual global::System.TimeSpan? TimeQuotedeadline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for LocaQuotedeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LOCA_QUOTEDEADLINE", TypeName = "nvarchar(252)", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("LocaQuoteDeadline")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string LocaQuotedeadline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateAwarddeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_AWARDDEADLINE", TypeName = "date", Order = 31)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateAwardDeadline")]
        public virtual System.DateTime? DateAwarddeadline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_TYPE_ID", TypeName = "int", Order = 32)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RfqTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int RfqTypeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 33)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string RfqTypeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcAwardMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_ID", TypeName = "int", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcAwardMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcAwardMethodId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcAwardMethodDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_DESC", TypeName = "nvarchar(2000)", Order = 35)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcAwardMethodDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 36)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcConfigurationFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcConfigurationId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcConfigurationDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_DESC", TypeName = "nvarchar(2000)", Order = 37)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcConfigurationDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcContractTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcContractTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcContractTypeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcContractTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_DESC", TypeName = "nvarchar(2000)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcContractTypeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcStrategyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRATEGY_ID", TypeName = "int", Order = 40)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcStrategyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcStrategyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcStrategyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRATEGY_DESC", TypeName = "nvarchar(2000)", Order = 41)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcStrategyDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_ID", TypeName = "int", Order = 42)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RfqHeaderFk")]
        public virtual int? RfqHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_CODE", TypeName = "nvarchar(16)", Order = 43)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string RfqHeaderCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_DESC", TypeName = "nvarchar(252)", Order = 44)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string RfqHeaderDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined1 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 47)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined2 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined3 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined4 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined5 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlannedStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_START", TypeName = "date", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedStart")]
        public virtual System.DateTime? PlannedStart {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlannedEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_END", TypeName = "date", Order = 52)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedEnd")]
        public virtual System.DateTime? PlannedEnd {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdEvaluationSchemaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATIONSCHEMA_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("EvaluationSchemaFk")]
        public virtual int? BpdEvaluationSchemaId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdEvaluationSchemaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATIONSCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BpdEvaluationSchemaDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_ID", TypeName = "int", Order = 60)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermAdFk")]
        public virtual int? PaymentTermAdId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_CODE", TypeName = "nvarchar(16)", Order = 61)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermAdCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_DESC", TypeName = "nvarchar(2000)", Order = 62)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermAdDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcBillingSchemaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_ID", TypeName = "int", Order = 63)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BillingSchemaFk")]
        public virtual int? MdcBillingSchemaId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcBillingSchemaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 64)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcBillingSchemaDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 65)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int LanguageId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? DateDelivery {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 67)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual int? BasLanguageFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 68)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BasLanguageDesc {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            RfqHeaderApiEntity obj = new RfqHeaderApiEntity();
            obj.Id = Id;
            obj.RfqStatusId = RfqStatusId;
            obj.RfqStatusDescription = RfqStatusDescription;
            obj.CompanyId = CompanyId;
            obj.CompanyCode = CompanyCode;
            obj.ProjectId = ProjectId;
            obj.ProjectCode = ProjectCode;
            obj.ProjectDescription = ProjectDescription;
            obj.ClerkPrcId = ClerkPrcId;
            obj.ClerkPrcCode = ClerkPrcCode;
            obj.ClerkPrcDescription = ClerkPrcDescription;
            obj.ClerkReqId = ClerkReqId;
            obj.ClerkReqCode = ClerkReqCode;
            obj.ClerkReqDescription = ClerkReqDescription;
            obj.CurrencyId = CurrencyId;
            obj.CurrencyDescription = CurrencyDescription;
            obj.PaymentTermFiId = PaymentTermFiId;
            obj.PaymentTermFiCode = PaymentTermFiCode;
            obj.PaymentTermFiDescription = PaymentTermFiDescription;
            obj.PaymentTermPaId = PaymentTermPaId;
            obj.PaymentTermPaCode = PaymentTermPaCode;
            obj.PaymentTermPaDescription = PaymentTermPaDescription;
            obj.Code = Code;
            obj.Description = Description;
            obj.SearchPattern = SearchPattern;
            obj.AwardReference = AwardReference;
            obj.DateRequested = DateRequested;
            obj.DateCanceled = DateCanceled;
            obj.DateQuotedeadline = DateQuotedeadline;
            obj.TimeQuotedeadline = TimeQuotedeadline;
            obj.LocaQuotedeadline = LocaQuotedeadline;
            obj.DateAwarddeadline = DateAwarddeadline;
            obj.RfqTypeId = RfqTypeId;
            obj.RfqTypeDescription = RfqTypeDescription;
            obj.PrcAwardMethodId = PrcAwardMethodId;
            obj.PrcAwardMethodDescription = PrcAwardMethodDescription;
            obj.PrcConfigurationId = PrcConfigurationId;
            obj.PrcConfigurationDescription = PrcConfigurationDescription;
            obj.PrcContractTypeId = PrcContractTypeId;
            obj.PrcContractTypeDescription = PrcContractTypeDescription;
            obj.PrcStrategyId = PrcStrategyId;
            obj.PrcStrategyDescription = PrcStrategyDescription;
            obj.RfqHeaderId = RfqHeaderId;
            obj.RfqHeaderCode = RfqHeaderCode;
            obj.RfqHeaderDescription = RfqHeaderDescription;
            obj.Remark = Remark;
            obj.UserDefined1 = UserDefined1;
            obj.UserDefined2 = UserDefined2;
            obj.UserDefined3 = UserDefined3;
            obj.UserDefined4 = UserDefined4;
            obj.UserDefined5 = UserDefined5;
            obj.PlannedStart = PlannedStart;
            obj.PlannedEnd = PlannedEnd;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            obj.BpdEvaluationSchemaId = BpdEvaluationSchemaId;
            obj.BpdEvaluationSchemaDescription = BpdEvaluationSchemaDescription;
            obj.PaymentTermAdId = PaymentTermAdId;
            obj.PaymentTermAdCode = PaymentTermAdCode;
            obj.PaymentTermAdDescription = PaymentTermAdDescription;
            obj.MdcBillingSchemaId = MdcBillingSchemaId;
            obj.MdcBillingSchemaDescription = MdcBillingSchemaDescription;
            obj.LanguageId = LanguageId;
            obj.DateDelivery = DateDelivery;
            obj.BasLanguageFk = BasLanguageFk;
            obj.BasLanguageDesc = BasLanguageDesc;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(RfqHeaderApiEntity clonedEntity);

    }


}
