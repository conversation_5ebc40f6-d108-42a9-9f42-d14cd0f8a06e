/*
 * Copyright(c) RIB Software GmbH
 */

import { IInitializationContext } from '@libs/platform/common';
import { SalesContractWizardService } from '../../wizards/sales-contract-wizard.service';
import { SalesContractCreateWipWizardService } from '../../wizards/sales-contract-create-wip-wizard.service';
import { SalesContractUpdateEstimateWizardService } from '../../wizards/sales-contract-update-estimate-wizard.service';
import { SalesContractChangeContractStatusWizardService } from '../../wizards/sales-contract-change-contract-status-wizard.service';
import { SalesContractChangeCodeWizardService } from '../../wizards/sales-contract-change-code-wizard.service';
import { SalesContractChangeAdvanceLineStatusWizardService } from '../../wizards/sales-contract-change-advance-line-status-wizard.service';
import { SalesContractGenerateAdvancePaymentBillWizardService } from '../../wizards/sales-contract-generate-advance-payment-bill-wizard.service';
import { SalesContractGeneratePaymentScheduleWizardService } from '../../wizards/sales-contract-generate-payment-schedule-wizard.service';
import { SalesContractChangePaymentScheduleStatusWizardService } from '../../wizards/sales-contract-change-payment-schedule-status-wizard.service';
import { SalesContractGenerateBillFromPaymentScheduleWizardService } from '../../wizards/sales-contract-generate-bill-from-payment-schedule-wizard.service';
import { SalesContractGenerateTransactionsWizardService } from '../../wizards/sales-contract-generate-transactions-wizard.service';
import { SalesContractGeneratePaymentScheduleFromScheduleWizardService } from '../../wizards/sales-contract-generate-payment-schedule-from-schedule-wizard.service';
import { SalesContractUpdatePaymentScheduleDocWizardService } from '../../wizards/sales-contract-update-payment-schedule-doc-wizard.service';
import { SalesContractMaintainPaymentScheduleVersionWizardService } from '../../wizards/sales-contract-maintain-payment-schedule-version-wizard.service';
import { SalesContractUpdateEstimateBudgetWizardService } from '../../wizards/sales-contract-update-estimate-budget-wizard.service';



export class SalesContractMainWizard {
	public createBill(context: IInitializationContext) {
		const service = context.injector.get(SalesContractWizardService);
		service.createBill();
	}

	public createWip(context: IInitializationContext) {
		const service = context.injector.get(SalesContractCreateWipWizardService);
		service.createWip();
	}

    public updatEstimate(context: IInitializationContext) {
        const service = context.injector.get(SalesContractUpdateEstimateWizardService);
        service.updateEstimate();
    }

	public changeContractStatus(context: IInitializationContext) {
		const service = context.injector.get(SalesContractChangeContractStatusWizardService);
		service.changeContractStatus();
	}

	public changeContractCode(context: IInitializationContext) {
		const service = context.injector.get(SalesContractChangeCodeWizardService);
		service.changeContractCode();
	}

	public changeAdvanceLineStatus(context: IInitializationContext) {
		const service = context.injector.get(SalesContractChangeAdvanceLineStatusWizardService);
		service.changeAdvanceLineStatus();
	}

	public generateAdvancePaymentBill(context: IInitializationContext) {
		const service = context.injector.get(SalesContractGenerateAdvancePaymentBillWizardService);
		service.generateAdvancePaymentBill();
	}

	public generatePaymentSchedule(context: IInitializationContext) {
		const service = context.injector.get(SalesContractGeneratePaymentScheduleWizardService);
		service.generatePaymentSchedule();
	}

	public changePaymentScheduleStatus(context: IInitializationContext) {
		const service = context.injector.get(SalesContractChangePaymentScheduleStatusWizardService);
		service.changePaymentScheduleStatus();
	}

	public generateBillFromPaymentSchedule(context: IInitializationContext) {
		const service = context.injector.get(SalesContractGenerateBillFromPaymentScheduleWizardService);
		service.generateBillFromPaymentSchedule();
	}

	public generateTransactionsForOrders(context: IInitializationContext) {
		const service = context.injector.get(SalesContractGenerateTransactionsWizardService);
		service.generateTransaction();
	}

	public generatePaymentScheduleFromSchedule(context: IInitializationContext) {
		const service = context.injector.get(SalesContractGeneratePaymentScheduleFromScheduleWizardService);
		service.generatePaymentScheduleFromSchedule();
	}

	public updatePaymentScheduleDoc(context: IInitializationContext) {
		const service = context.injector.get(SalesContractUpdatePaymentScheduleDocWizardService);
		service.updatePaymentScheduleDoc();
	}

	public maintainPaymentScheduleVersion(context: IInitializationContext) {
		const service = context.injector.get(SalesContractMaintainPaymentScheduleVersionWizardService);
		service.maintainPaymentScheduleVersion();
	}
	
	public updateEstimateBudget(context: IInitializationContext) {
		const service = context.injector.get(SalesContractUpdateEstimateBudgetWizardService);
		service.updateEstimateBudget();
	}
}