using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RIB.Visual.Documents.Project.BusinessComponents
{
	///// <summary>
	/////
	///// </summary>
	//public class MetaDataTermMapping
	//{
	//	/// <summary>
	//	///
	//	/// </summary>
	//	public MetaDataType MetaDataType { get; set; }
	//	/// <summary>
	//	///
	//	/// </summary>
	//	public int RibId { get; set; }
	//	/// <summary>
	//	///
	//	/// </summary>
	//	public string SharePointTermName { get; set; }
	//	/// <summary>
	//	///
	//	/// </summary>
	//	public string SharePointTermId { get; set; }

	//}
	/// <summary>
	///
	/// </summary>
	public enum MetaDataType
	{
		/// <summary>
		///
		/// </summary>
		RubricCategory = 0,
		/// <summary>
		///
		/// </summary>
		ProjectDocumentCategory,
		/// <summary>
		///
		/// </summary>
		ProjectDocumentType,
		/// <summary>
		///
		/// </summary>
		ProcurementStructure,
		/// <summary>
		///
		/// </summary>
		BusinessPartner,
		/// <summary>
		///
		/// </summary>
		ControllingUnit,
		/// <summary>
		///
		/// </summary>
		Project = 10000,

		/// <summary>
		///
		/// </summary>
		Document = 10001,
		/// <summary>
		///
		/// </summary>
		GlobalTermGroup = 10002,
		/// <summary>
		///
		/// </summary>
		SiteTermGroup = 10003,

		/// <summary>
		/// if is this value, object id in PRJ_DOC_METADATA2EXT maps PRJ_DOC_SPPRJDETAILSTRUCT ID
		/// </summary>
		Folder = 10004,

		/// <summary>
		/// match team id of project in sharepoint
		/// </summary>
		Team = 10005,

		/// <summary>
		/// match the default channel in team
		/// </summary>
		Channel = 10006,

		/// <summary>
		/// match the default channel folder in team
		/// </summary>
		ChannelFolder = 10007
	}


	/// <summary>
	/// These share point data are not relate to any 4.0 entity, so use a special negative value to store it.
	/// </summary>
	public enum SpecialMetaDataKey
	{
		/// <summary>
		/// 
		/// </summary>
		TermGroup = -1,
		/// <summary>
		/// 
		/// </summary>
		TermSet = -2,
		/// <summary>
		/// 
		/// </summary>
		Column = -3,
		/// <summary>
		/// 
		/// </summary>
		GlobalTermSet = -4,
	}
	/// <summary>
	///
	/// </summary>
	public static class MetaDataTypeHelper
	{
		/// <summary>
		///
		/// </summary>
		/// <param name="metaDataType"></param>
		/// <param name="extString"></param>
		/// <returns></returns>
		/// <exception cref="NotSupportedException"></exception>
		public static (string RibName, string SpInternalName, string SpDisplayName, bool ReadOnly) GetMetaDataTypeMapper(this MetaDataType metaDataType, string extString = null)
		{
			// TODO: translation? readonly?

			(string RibName, string SpInternalName, string SpDisplayName, bool ReadOnly) mapper;

			switch (metaDataType)
			{
				case MetaDataType.RubricCategory:
					mapper = ("RubricCategoryFk", "Rib_RubricCategory", "Rubric Category", true);
					break;
				case MetaDataType.ProjectDocumentCategory:
					mapper = ("PrjDocumentCategoryFk", "Rib_ProjectDocumentCategory", "Project Document Category", true);
					break;
				case MetaDataType.ProjectDocumentType:
					mapper = ("PrjDocumentTypeFk", "Rib_ProjectDocumentType", "Project Document Type", true);
					break;
				case MetaDataType.ProcurementStructure:
					mapper = ("PrcStructureFk", "Rib_ProcurementStructure", "Procurement Structure", false);
					break;
				case MetaDataType.BusinessPartner:
					mapper = ("BpdBusinessPartnerFk", "Rib_BusinessPartner", "Business Partner", false);
					break;
				case MetaDataType.ControllingUnit:
					mapper = ("MdcControllingUnitFk", "Rib_ControllingUnit", "Controlling Unit", false);
					break;
				default:
					throw new NotSupportedException($"MetaDataType '{metaDataType}' is not supported.");
			}

			if(!string.IsNullOrEmpty(extString))
			{
				mapper.SpInternalName = $"{mapper.SpInternalName}_{extString}";
				mapper.SpDisplayName = $"{mapper.SpDisplayName} {extString}";
				// If extString is not provided, return the default mapping
				return mapper;
			}

			return mapper;
		}
	}
}
