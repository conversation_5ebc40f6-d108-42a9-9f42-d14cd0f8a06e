﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RIB.Visual.Estimate.Main.Localization.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("RIB.Visual.Estimate.Main.Localization.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Export to RIB 4.0 successful!.
        /// </summary>
        public static string _ImportSuccess {
            get {
                return ResourceManager.GetString("_ImportSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimate.Main.
        /// </summary>
        public static string _ModuleName {
            get {
                return ResourceManager.GetString("_ModuleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeepCopyEstimateAction.
        /// </summary>
        public static string Action_WF_DeepCopy_Estimate {
            get {
                return ResourceManager.GetString("Action_WF_DeepCopy_Estimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeepCopy Fialed!.
        /// </summary>
        public static string Action_WF_DeepCopy_Fialed {
            get {
                return ResourceManager.GetString("Action_WF_DeepCopy_Fialed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Resources Action.
        /// </summary>
        public static string Action_WF_DeleteResources_Estimate {
            get {
                return ResourceManager.GetString("Action_WF_DeleteResources_Estimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Resources Failed.
        /// </summary>
        public static string Action_WF_DeleteResources_Estimate_Failed {
            get {
                return ResourceManager.GetString("Action_WF_DeleteResources_Estimate_Failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EstHeaderId.
        /// </summary>
        public static string Action_WF_Est_HeaderId {
            get {
                return ResourceManager.GetString("Action_WF_Est_HeaderId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1, No EstHeader Defined; 2, The EstHeader is Version Estimate or GC Estimate..
        /// </summary>
        public static string Action_WF_EstHeader_No_Defined {
            get {
                return ResourceManager.GetString("Action_WF_EstHeader_No_Defined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy Budget.
        /// </summary>
        public static string Action_WF_IsCpoy_Budget {
            get {
                return ResourceManager.GetString("Action_WF_IsCpoy_Budget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameter EstHeaderId is missing!.
        /// </summary>
        public static string Action_WF_Miss_EstHeaderId {
            get {
                return ResourceManager.GetString("Action_WF_Miss_EstHeaderId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameter LineItem Ids is missing!.
        /// </summary>
        public static string Action_WF_Miss_EstLineItemIds {
            get {
                return ResourceManager.GetString("Action_WF_Miss_EstLineItemIds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameter ProjecctId is missing!.
        /// </summary>
        public static string Action_WF_Miss_ProjecctId {
            get {
                return ResourceManager.GetString("Action_WF_Miss_ProjecctId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Estimate Type.
        /// </summary>
        public static string Action_WF_New_Estimate_Type {
            get {
                return ResourceManager.GetString("Action_WF_New_Estimate_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The current line item (or one of the Line items) that you want to assign a BoQ item has a link to one of the BoQ Split Quantities.
        /// </summary>
        public static string Bulk_Err_BoqSplitQuantityFkExist {
            get {
                return ResourceManager.GetString("Bulk_Err_BoqSplitQuantityFkExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a BoQ Item.
        /// </summary>
        public static string Bulk_Err_SelectBoqItemRef {
            get {
                return ResourceManager.GetString("Bulk_Err_SelectBoqItemRef", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not assign LineItem to BoQ items which contains sub quantity item(s)..
        /// </summary>
        public static string Bulk_Err_SubQuantityBoQItemsErrormsg {
            get {
                return ResourceManager.GetString("Bulk_Err_SubQuantityBoQItemsErrormsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CostCodeless Package.
        /// </summary>
        public static string CreatePackage_CostCodeless {
            get {
                return ResourceManager.GetString("CreatePackage_CostCodeless", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Criteria Matched.
        /// </summary>
        public static string CreatePackage_MatchnessType_CriteriaMatched {
            get {
                return ResourceManager.GetString("CreatePackage_MatchnessType_CriteriaMatched", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        public static string CreatePackage_MatchnessType_New {
            get {
                return ResourceManager.GetString("CreatePackage_MatchnessType_New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Perfectly Matched.
        /// </summary>
        public static string CreatePackage_MatchnessType_PerfectlyMatched {
            get {
                return ResourceManager.GetString("CreatePackage_MatchnessType_PerfectlyMatched", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Structureless Package.
        /// </summary>
        public static string CreatePackage_Structureless {
            get {
                return ResourceManager.GetString("CreatePackage_Structureless", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Item Assignment.
        /// </summary>
        public static string Del_LineItem_PrcItemAssinged {
            get {
                return ResourceManager.GetString("Del_LineItem_PrcItemAssinged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Takeoff.
        /// </summary>
        public static string Del_LineItem_QtoLine {
            get {
                return ResourceManager.GetString("Del_LineItem_QtoLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Item Quantity.
        /// </summary>
        public static string Del_LineItem_Qty {
            get {
                return ResourceManager.GetString("Del_LineItem_Qty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Item Reference.
        /// </summary>
        public static string Del_LineItem_Reference {
            get {
                return ResourceManager.GetString("Del_LineItem_Reference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not switch to company.
        /// </summary>
        public static string Err_CanNotSwitchToCompany {
            get {
                return ResourceManager.GetString("Err_CanNotSwitchToCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Construction System Instance cannot be deleted as it is used in Line Items..
        /// </summary>
        public static string ERR_ConSysInstanceCannotBeDeletedAsItIsUsedInLineItem {
            get {
                return ResourceManager.GetString("ERR_ConSysInstanceCannotBeDeletedAsItIsUsedInLineItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Construction System Instance is used in Line Items. Do you really want to delete it?.
        /// </summary>
        public static string ERR_ConSysInstanceIsUsedInLineItemDoYouDelete {
            get {
                return ResourceManager.GetString("ERR_ConSysInstanceIsUsedInLineItemDoYouDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Construction System Master cannot be deleted as it is used in Line Items..
        /// </summary>
        public static string ERR_ConSysMasterCannotBeDeletedAsItIsUsedInLineItem {
            get {
                return ResourceManager.GetString("ERR_ConSysMasterCannotBeDeletedAsItIsUsedInLineItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create log failed:.
        /// </summary>
        public static string Err_CreateLogFailed {
            get {
                return ResourceManager.GetString("Err_CreateLogFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A version conflict occurred, please retry your action or refresh whole estimate...
        /// </summary>
        public static string ERR_Current_estimate_updating {
            get {
                return ResourceManager.GetString("ERR_Current_estimate_updating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User &apos;{0}&apos; is also using this data to generate lineItem in this Estimate Header..
        /// </summary>
        public static string ERR_CurrentDataIsUsing {
            get {
                return ResourceManager.GetString("ERR_CurrentDataIsUsing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Resources could not be deleted!.
        /// </summary>
        public static string Err_DeleteAllResFailed {
            get {
                return ResourceManager.GetString("Err_DeleteAllResFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected Line Item cannot be deleted!.
        /// </summary>
        public static string Err_DeleteLineItem {
            get {
                return ResourceManager.GetString("Err_DeleteLineItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected Resource Item can not be deleted!.
        /// </summary>
        public static string Err_DeleteResFailed {
            get {
                return ResourceManager.GetString("Err_DeleteResFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Resource or Sub Items can not be deleted! First delete dependency and then try to delete again!.
        /// </summary>
        public static string Err_DeleteResWithDependencies {
            get {
                return ResourceManager.GetString("Err_DeleteResWithDependencies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The project({0}) has been deleted. Please enable it manually!.
        /// </summary>
        public static string Err_DisableProject {
            get {
                return ResourceManager.GetString("Err_DisableProject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no activate Estimate, please check the isActivate flag.
        /// </summary>
        public static string Err_Estimate_is_not_active {
            get {
                return ResourceManager.GetString("Err_Estimate_is_not_active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The failed reason is:.
        /// </summary>
        public static string Err_FailedReason {
            get {
                return ResourceManager.GetString("Err_FailedReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generate the line item  failed reason:.
        /// </summary>
        public static string Err_GenrateLineItemFailed {
            get {
                return ResourceManager.GetString("Err_GenrateLineItemFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t find materialGroup by material({0}) in project({1})!.
        /// </summary>
        public static string Err_GetMaterailGroupByMaterailIdFailed {
            get {
                return ResourceManager.GetString("Err_GetMaterailGroupByMaterailIdFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Either the project number or the project ID has to be passed to this Url.
        /// </summary>
        public static string Err_HasToBePassedUrl {
            get {
                return ResourceManager.GetString("Err_HasToBePassedUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Estimate.
        /// </summary>
        public static string Err_Invalid_Estimate {
            get {
                return ResourceManager.GetString("Err_Invalid_Estimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid company for project({0})!.
        /// </summary>
        public static string Err_InvalidCompany {
            get {
                return ResourceManager.GetString("Err_InvalidCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Company for Project Public Api.
        /// </summary>
        public static string Err_InvalidCompanyForProjectPublicApi {
            get {
                return ResourceManager.GetString("Err_InvalidCompanyForProjectPublicApi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Line Item!.
        /// </summary>
        public static string Err_InvalidLineItem {
            get {
                return ResourceManager.GetString("Err_InvalidLineItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The status of Packages [{0}] is  contracted,.
        /// </summary>
        public static string Err_Is_contract_package {
            get {
                return ResourceManager.GetString("Err_Is_contract_package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The status of Packages [{0}] is  not IsEstimate,.
        /// </summary>
        public static string Err_Isnot_estimate_package {
            get {
                return ResourceManager.GetString("Err_Isnot_estimate_package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Material({0})&apos;s CostCodeFk in PrcStructure is Null in project({1})!.
        /// </summary>
        public static string Err_MaterailCostCodeFkIsNull {
            get {
                return ResourceManager.GetString("Err_MaterailCostCodeFkIsNull", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no data to update..
        /// </summary>
        public static string Err_No_data_to_update {
            get {
                return ResourceManager.GetString("Err_No_data_to_update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not find the Project({0})!.
        /// </summary>
        public static string Err_No_Defined_Project {
            get {
                return ResourceManager.GetString("Err_No_Defined_Project", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no matched LineItem in current scope can be found to create Package info.
        /// </summary>
        public static string Err_No_lineItem_Create_Package_info {
            get {
                return ResourceManager.GetString("Err_No_lineItem_Create_Package_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No activity Id({0}) Defined!.
        /// </summary>
        public static string Err_NoActivityIdDefined {
            get {
                return ResourceManager.GetString("Err_NoActivityIdDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ&apos;s currency is missing in project({0})!.
        /// </summary>
        public static string Err_NoBoQCurrencyDefined {
            get {
                return ResourceManager.GetString("Err_NoBoQCurrencyDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No BoQ defined in project({0})!.
        /// </summary>
        public static string Err_NoBoQDefined {
            get {
                return ResourceManager.GetString("Err_NoBoQDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No BoQ&apos;s Key defined in project({0})!.
        /// </summary>
        public static string Err_NoBoQKeyDefined {
            get {
                return ResourceManager.GetString("Err_NoBoQKeyDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ&apos;s Name is not set in project({0})!.
        /// </summary>
        public static string Err_NoBoQNameSet {
            get {
                return ResourceManager.GetString("Err_NoBoQNameSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Cost Code({0}) and its parent(not the root cost code) defined or not use in company in project({1})!.
        /// </summary>
        public static string Err_NoCoCAndParentDefined {
            get {
                return ResourceManager.GetString("Err_NoCoCAndParentDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Code({0})&apos;s Currency doesn&apos;t defined in project({1})!.
        /// </summary>
        public static string Err_NoCoCCurrencyDefined {
            get {
                return ResourceManager.GetString("Err_NoCoCCurrencyDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Code({0})&apos;s ForeignCurrency({1}) doesn&apos;t defined in project({2})!.
        /// </summary>
        public static string Err_NoCoCForeignCurrencyDefined {
            get {
                return ResourceManager.GetString("Err_NoCoCForeignCurrencyDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project{0}&apos;s companyNo({1}) \r\n is not found in RIB 4.0!.
        /// </summary>
        public static string Err_NoCompanyNoFound {
            get {
                return ResourceManager.GetString("Err_NoCompanyNoFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No CompanyNo found in xml file:{0}!.
        /// </summary>
        public static string Err_NoCompanyNOFoundInXmlFile {
            get {
                return ResourceManager.GetString("Err_NoCompanyNOFoundInXmlFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not found currency conversion({0}) in Home Currency ({1})..
        /// </summary>
        public static string Err_NoCurrencyDefinded {
            get {
                return ResourceManager.GetString("Err_NoCurrencyDefinded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No currency exchange define in RIB 4.0!.
        /// </summary>
        public static string Err_NoCurrencyExchangeDefined {
            get {
                return ResourceManager.GetString("Err_NoCurrencyExchangeDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RIB 4.0 Bid Estimate Folder is not in sysoption table.Please custormizing system option!.
        /// </summary>
        public static string Err_NoDefineBidEstimateInSystemOption {
            get {
                return ResourceManager.GetString("Err_NoDefineBidEstimateInSystemOption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No estimate to create!.
        /// </summary>
        public static string Err_NoEstimate {
            get {
                return ResourceManager.GetString("Err_NoEstimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No estimate&apos;s PACode defined in project({0})!.
        /// </summary>
        public static string Err_NoEstimatePACodeDefined {
            get {
                return ResourceManager.GetString("Err_NoEstimatePACodeDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No execute permission for Use Project Public Api!.
        /// </summary>
        public static string Err_NoExecutePermissionForUseProjecPublicApi {
            get {
                return ResourceManager.GetString("Err_NoExecutePermissionForUseProjecPublicApi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t find the folder({0}).Please check RIB 4.0 Bid Estimate in system option!.
        /// </summary>
        public static string Err_NoFolder {
            get {
                return ResourceManager.GetString("Err_NoFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Material({0}) not Found in project({1}) in RIB 4.0!.
        /// </summary>
        public static string Err_NoMaterailDefined {
            get {
                return ResourceManager.GetString("Err_NoMaterailDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Material&apos;s ExternalID({0})  defined in xml&apos;s flie!.
        /// </summary>
        public static string Err_NoMaterialIdDefined {
            get {
                return ResourceManager.GetString("Err_NoMaterialIdDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Project failed:projectGroup({0}) is not found in RIB 4.0!.
        /// </summary>
        public static string Err_NoPrjGroupDefine {
            get {
                return ResourceManager.GetString("Err_NoPrjGroupDefine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No subitem to create line item!.
        /// </summary>
        public static string Err_NoSubitemToCreateLineItem {
            get {
                return ResourceManager.GetString("Err_NoSubitemToCreateLineItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Uom({0}) define in RIB 4.0!.
        /// </summary>
        public static string Err_NoUoMDefind {
            get {
                return ResourceManager.GetString("Err_NoUoMDefind", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t find the xml&apos;s file({0}).Please check xml’s file path setting both baseline and 4.0!  .
        /// </summary>
        public static string Err_NoXmlFile {
            get {
                return ResourceManager.GetString("Err_NoXmlFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} file can be found!.
        /// </summary>
        public static string Err_NoXmlFileFound {
            get {
                return ResourceManager.GetString("Err_NoXmlFileFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RIB 4.0 Bid Estimate is null in system option!.
        /// </summary>
        public static string Err_NullBidEstimateInSystemOption {
            get {
                return ResourceManager.GetString("Err_NullBidEstimateInSystemOption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An exception is thrown when updating estimation because the calculation value is out of range. Some data can&apos;t be saved to system. Please recheck your input..
        /// </summary>
        public static string Err_Out_Of_Range_Update_Estimate {
            get {
                return ResourceManager.GetString("Err_Out_Of_Range_Update_Estimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource cannot be deleted as it is used in Procurement Package. .
        /// </summary>
        public static string Err_OverwriteLineitemWhenChangeAssemblyTemplate {
            get {
                return ResourceManager.GetString("Err_OverwriteLineitemWhenChangeAssemblyTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Parameter Code({0}) must be a string with a maximum length of 16 in project var..
        /// </summary>
        public static string Err_Parameter_Code_Maximum_Length {
            get {
                return ResourceManager.GetString("Err_Parameter_Code_Maximum_Length", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The parameter code ({0}) is too long. The maximum length is {1}..
        /// </summary>
        public static string Err_ParameterCodeTooLong {
            get {
                return ResourceManager.GetString("Err_ParameterCodeTooLong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Update Estimate action is rejected..
        /// </summary>
        public static string Err_Prc_update_estimat_reject {
            get {
                return ResourceManager.GetString("Err_Prc_update_estimat_reject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ Driven Estimate,An error occurred in create line items from project BoQ:.
        /// </summary>
        public static string Err_Prj_Boq_Driven {
            get {
                return ResourceManager.GetString("Err_Prj_Boq_Driven", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ Driven Estimate,Error during bulk save in create line items from project BoQ :.
        /// </summary>
        public static string Err_Prj_Boq_Driven_Bulk_Save {
            get {
                return ResourceManager.GetString("Err_Prj_Boq_Driven_Bulk_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ Driven Estimate,Error processing project boq assembly Id {0} in create line items from project BoQ :.
        /// </summary>
        public static string Err_Prj_Boq_Driven_Process_Assembly {
            get {
                return ResourceManager.GetString("Err_Prj_Boq_Driven_Process_Assembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned Resource or Sub Items can not be saved!.
        /// </summary>
        public static string Err_SaveAssignResOrSubItemFailed {
            get {
                return ResourceManager.GetString("Err_SaveAssignResOrSubItemFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Improt the BoQ Item failed!.
        /// </summary>
        public static string Err_SaveBoQFailed {
            get {
                return ResourceManager.GetString("Err_SaveBoQFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project({0}) is not found in RIB 4.0!\n No project&apos;s PrjGroup(ProjectGroup) to ceate new project!.
        /// </summary>
        public static string Err_SaveProjectFailedNoPrjPrjGroup {
            get {
                return ResourceManager.GetString("Err_SaveProjectFailedNoPrjPrjGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project({0}) is not found in RIB 4.0!\n No project&apos;s Rubric(ProjectCat) to create new project!.
        /// </summary>
        public static string Err_SaveProjectFailedNoPrjRubric {
            get {
                return ResourceManager.GetString("Err_SaveProjectFailedNoPrjRubric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The selected Resource Item could not be saved!!.
        /// </summary>
        public static string Err_SaveResFailed {
            get {
                return ResourceManager.GetString("Err_SaveResFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The source and target boq are not compatible.
        /// </summary>
        public static string Err_SourceAndTargetBoqsNotCompatible {
            get {
                return ResourceManager.GetString("Err_SourceAndTargetBoqsNotCompatible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Conflicting data is inserted by User {0}. This conflict cannot be resolved, so the data is not saved.
        /// </summary>
        public static string Err_SplitBudget_ConcurrencyIssue {
            get {
                return ResourceManager.GetString("Err_SplitBudget_ConcurrencyIssue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split Budget Failed.
        /// </summary>
        public static string Err_SplitBudget_Failed {
            get {
                return ResourceManager.GetString("Err_SplitBudget_Failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed To Transfer Revenue To Estimate, Error Message is ({0})..
        /// </summary>
        public static string Err_TransferRevenueFailed {
            get {
                return ResourceManager.GetString("Err_TransferRevenueFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ Driven Estimate, an error occurred in create line items from WiC BoQ:.
        /// </summary>
        public static string Err_Wic_Boq_Driven {
            get {
                return ResourceManager.GetString("Err_Wic_Boq_Driven", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ Driven Estimate, Error during bulk save in create line items from WiC BoQ :.
        /// </summary>
        public static string Err_Wic_Boq_Driven_Bulk_Save {
            get {
                return ResourceManager.GetString("Err_Wic_Boq_Driven_Bulk_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ Driven Estimate, Error processing wic assembly id {0} in create line items from WIC BoQ :.
        /// </summary>
        public static string Err_Wic_Boq_Driven_Process_Assembly {
            get {
                return ResourceManager.GetString("Err_Wic_Boq_Driven_Process_Assembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The XML&apos;s formatting error: .
        /// </summary>
        public static string Err_XMLExceptionTips {
            get {
                return ResourceManager.GetString("Err_XMLExceptionTips", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The CostCode({0}) has no Currency..
        /// </summary>
        public static string Error_CostCode_NoCurrency {
            get {
                return ResourceManager.GetString("Error_CostCode_NoCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Must define a obj property in property object.
        /// </summary>
        public static string Error_ModelObjectId {
            get {
                return ResourceManager.GetString("Error_ModelObjectId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type Error: Assign a model object to obj property.
        /// </summary>
        public static string Error_ModelObjectId_Type {
            get {
                return ResourceManager.GetString("Error_ModelObjectId_Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No assigned Cost Code to Material({0}) at BoQ({1})!.
        /// </summary>
        public static string Error_No_CostCode_Assign2Material {
            get {
                return ResourceManager.GetString("Error_No_CostCode_Assign2Material", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No defualt CostGroup Configuration Type to create Project..
        /// </summary>
        public static string Error_No_default_CostGroupConfigType {
            get {
                return ResourceManager.GetString("Error_No_default_CostGroupConfigType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No estimate&apos;s EstimateRoot defined in project({0})!.
        /// </summary>
        public static string Error_NoEstimateRoot {
            get {
                return ResourceManager.GetString("Error_NoEstimateRoot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Defined Data to export, please check the cost group assignment..
        /// </summary>
        public static string Error_Not_Defined_Data {
            get {
                return ResourceManager.GetString("Error_Not_Defined_Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not assigned to BoQ.
        /// </summary>
        public static string Est_Confidence_BoqItemFk {
            get {
                return ResourceManager.GetString("Est_Confidence_BoqItemFk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zero Budget (budget = 0).
        /// </summary>
        public static string Est_Confidence_Budget {
            get {
                return ResourceManager.GetString("Est_Confidence_Budget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Budgeting.
        /// </summary>
        public static string Est_Confidence_Budgeting {
            get {
                return ResourceManager.GetString("Est_Confidence_Budgeting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Costing.
        /// </summary>
        public static string Est_Confidence_Costing {
            get {
                return ResourceManager.GetString("Est_Confidence_Costing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unpriced (grand total = 0).
        /// </summary>
        public static string Est_Confidence_Grand_Total {
            get {
                return ResourceManager.GetString("Est_Confidence_Grand_Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled (disabled = true).
        /// </summary>
        public static string Est_Confidence_IsDisabled {
            get {
                return ResourceManager.GetString("Est_Confidence_IsDisabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fixed Budget (fixed budget = true or fixed budget unit = true).
        /// </summary>
        public static string Est_Confidence_IsFixedBudget {
            get {
                return ResourceManager.GetString("Est_Confidence_IsFixedBudget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plug Rate(fixed price = true).
        /// </summary>
        public static string Est_Confidence_IsFixedPrice {
            get {
                return ResourceManager.GetString("Est_Confidence_IsFixedPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Direct Cost Items (general cost = false).
        /// </summary>
        public static string Est_Confidence_IsGc_False {
            get {
                return ResourceManager.GetString("Est_Confidence_IsGc_False", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Cost Items (general cost = true).
        /// </summary>
        public static string Est_Confidence_IsGc_True {
            get {
                return ResourceManager.GetString("Est_Confidence_IsGc_True", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Included Items(included = true).
        /// </summary>
        public static string Est_Confidence_IsIncluded {
            get {
                return ResourceManager.GetString("Est_Confidence_IsIncluded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lumpsum (lumpsum = true).
        /// </summary>
        public static string Est_Confidence_IsLumpsum {
            get {
                return ResourceManager.GetString("Est_Confidence_IsLumpsum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Escalation (no escalation = true).
        /// </summary>
        public static string Est_Confidence_IsNoEscalation {
            get {
                return ResourceManager.GetString("Est_Confidence_IsNoEscalation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Markup(no mark up = true).
        /// </summary>
        public static string Est_Confidence_IsNoMarkup {
            get {
                return ResourceManager.GetString("Est_Confidence_IsNoMarkup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not assigned to Controlling Unit.
        /// </summary>
        public static string Est_Confidence_MdcControllingUnitFk {
            get {
                return ResourceManager.GetString("Est_Confidence_MdcControllingUnitFk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Metadata Association.
        /// </summary>
        public static string Est_Confidence_Metadata_Association {
            get {
                return ResourceManager.GetString("Est_Confidence_Metadata_Association", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packages.
        /// </summary>
        public static string Est_Confidence_Packages {
            get {
                return ResourceManager.GetString("Est_Confidence_Packages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Material Packages.
        /// </summary>
        public static string Est_Confidence_PrcPackageFk_IsMaterial {
            get {
                return ResourceManager.GetString("Est_Confidence_PrcPackageFk_IsMaterial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Service Packages.
        /// </summary>
        public static string Est_Confidence_PrcPackageFk_IsService {
            get {
                return ResourceManager.GetString("Est_Confidence_PrcPackageFk_IsService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Created but Price not updated from Prc.
        /// </summary>
        public static string Est_Confidence_PrcPackageFk_PrcPackageGeneratedPrc {
            get {
                return ResourceManager.GetString("Est_Confidence_PrcPackageFk_PrcPackageGeneratedPrc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pricing.
        /// </summary>
        public static string Est_Confidence_Pricing {
            get {
                return ResourceManager.GetString("Est_Confidence_Pricing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accepted in principle.
        /// </summary>
        public static string Est_Confidence_PrjChanges_AcceptedInPrinciple {
            get {
                return ResourceManager.GetString("Est_Confidence_PrjChanges_AcceptedInPrinciple", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Announced.
        /// </summary>
        public static string Est_Confidence_PrjChanges_Announced {
            get {
                return ResourceManager.GetString("Est_Confidence_PrjChanges_Announced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved.
        /// </summary>
        public static string Est_Confidence_PrjChanges_Approved {
            get {
                return ResourceManager.GetString("Est_Confidence_PrjChanges_Approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Identified.
        /// </summary>
        public static string Est_Confidence_PrjChanges_Identified {
            get {
                return ResourceManager.GetString("Est_Confidence_PrjChanges_Identified", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejected.
        /// </summary>
        public static string Est_Confidence_PrjChanges_Rejected {
            get {
                return ResourceManager.GetString("Est_Confidence_PrjChanges_Rejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejected with protest.
        /// </summary>
        public static string Est_Confidence_PrjChanges_RejectedWithProtest {
            get {
                return ResourceManager.GetString("Est_Confidence_PrjChanges_RejectedWithProtest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Submitted.
        /// </summary>
        public static string Est_Confidence_PrjChanges_Submitted {
            get {
                return ResourceManager.GetString("Est_Confidence_PrjChanges_Submitted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Withdrawn.
        /// </summary>
        public static string Est_Confidence_PrjChanges_Withdrawn {
            get {
                return ResourceManager.GetString("Est_Confidence_PrjChanges_Withdrawn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project Changes.
        /// </summary>
        public static string Est_Confidence_Project_Changes {
            get {
                return ResourceManager.GetString("Est_Confidence_Project_Changes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not assigned to Activities.
        /// </summary>
        public static string Est_Confidence_PsdActivityFk {
            get {
                return ResourceManager.GetString("Est_Confidence_PsdActivityFk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Budget &gt; Grand Total.
        /// </summary>
        public static string Est_Confidence_Total_Budget {
            get {
                return ResourceManager.GetString("Est_Confidence_Total_Budget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Structure.
        /// </summary>
        public static string EstIcoDown {
            get {
                return ResourceManager.GetString("EstIcoDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Relation.
        /// </summary>
        public static string EstIcoMinus {
            get {
                return ResourceManager.GetString("EstIcoMinus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Structure.
        /// </summary>
        public static string EstIcoUp {
            get {
                return ResourceManager.GetString("EstIcoUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimate Confidence.
        /// </summary>
        public static string Estimate_Confidence {
            get {
                return ResourceManager.GetString("Estimate_Confidence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculate Estimate..
        /// </summary>
        public static string Hint_Calculate_Estimate {
            get {
                return ResourceManager.GetString("Hint_Calculate_Estimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculate detail cost: {0} seconds..
        /// </summary>
        public static string Hint_CalculateDetail {
            get {
                return ResourceManager.GetString("Hint_CalculateDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculate Risk And Escalation: cost: {0} seconds..
        /// </summary>
        public static string Hint_CalculateRiskEscalation {
            get {
                return ResourceManager.GetString("Hint_CalculateRiskEscalation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not found costType[{0}].
        /// </summary>
        public static string Hint_Can_Not_Found_CostType {
            get {
                return ResourceManager.GetString("Hint_Can_Not_Found_CostType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not found resourceFlag[{0}].
        /// </summary>
        public static string Hint_Can_Not_Found_ResourceFlag {
            get {
                return ResourceManager.GetString("Hint_Can_Not_Found_ResourceFlag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Sales Contract..
        /// </summary>
        public static string Hint_Create_Ord {
            get {
                return ResourceManager.GetString("Hint_Create_Ord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Before impor BoQ, delete BoQ first..
        /// </summary>
        public static string Hint_Delete_BoQ {
            get {
                return ResourceManager.GetString("Hint_Delete_BoQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Before import LineItems and Boqs, Delete the Estimate first..
        /// </summary>
        public static string Hint_Delete_Estimate {
            get {
                return ResourceManager.GetString("Hint_Delete_Estimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete old data cost: {0} seconds..
        /// </summary>
        public static string Hint_DeleteOldData {
            get {
                return ResourceManager.GetString("Hint_DeleteOldData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finised: {0} / Total: {1}.
        /// </summary>
        public static string Hint_ExecuteEveryone {
            get {
                return ResourceManager.GetString("Hint_ExecuteEveryone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Execute for line item adjustment.
        /// </summary>
        public static string Hint_ExecuteForLineItemAdjustment {
            get {
                return ResourceManager.GetString("Hint_ExecuteForLineItemAdjustment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Execute only for current level once.
        /// </summary>
        public static string Hint_ExecuteOnce {
            get {
                return ResourceManager.GetString("Hint_ExecuteOnce", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Execute script cost: {0} seconds.
        /// </summary>
        public static string Hint_ExecuteScript {
            get {
                return ResourceManager.GetString("Hint_ExecuteScript", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to this function need {0} parameters..
        /// </summary>
        public static string Hint_Function_Need_Parameters {
            get {
                return ResourceManager.GetString("Hint_Function_Need_Parameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to this function must provide {0} parameters at least.
        /// </summary>
        public static string Hint_Function_Need_Parameters_At_least {
            get {
                return ResourceManager.GetString("Hint_Function_Need_Parameters_At_least", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} function must provide {1} parameters at least.
        /// </summary>
        public static string Hint_Function_Need_Parameters_At_least_Enhance {
            get {
                return ResourceManager.GetString("Hint_Function_Need_Parameters_At_least_Enhance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Generating LineItems and Resources.
        /// </summary>
        public static string Hint_Generated_LineItems {
            get {
                return ResourceManager.GetString("Hint_Generated_LineItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Boqs..
        /// </summary>
        public static string Hint_Insert_boqs {
            get {
                return ResourceManager.GetString("Hint_Insert_boqs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create/Update Parameters, Location and Classification..
        /// </summary>
        public static string Hint_Insert_Catalogs {
            get {
                return ResourceManager.GetString("Hint_Insert_Catalogs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Line Items..
        /// </summary>
        public static string Hint_Insert_LineItems {
            get {
                return ResourceManager.GetString("Hint_Insert_LineItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This costCode is fixed unit price,can not be modify.
        /// </summary>
        public static string Hint_Is_Fixed_Unit_Rate {
            get {
                return ResourceManager.GetString("Hint_Is_Fixed_Unit_Rate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The line item causing the error has a code of {0}.
        /// </summary>
        public static string Hint_LineItem_Code {
            get {
                return ResourceManager.GetString("Hint_LineItem_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The  {0} with a value of {1} cannot be parsed.  {2}.  Detail: {3}.
        /// </summary>
        public static string Hint_LineItem_Parse_Error {
            get {
                return ResourceManager.GetString("Hint_LineItem_Parse_Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameter code {0} with a value of {1} cannot be parsed.  {2}. Detail: {3}.
        /// </summary>
        public static string Hint_Param_Parse_Error {
            get {
                return ResourceManager.GetString("Hint_Param_Parse_Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read the Catalogs: Activity, Controlling Unit, SubPackage, Location and Classification infos..
        /// </summary>
        public static string Hint_Read_Catalogs {
            get {
                return ResourceManager.GetString("Hint_Read_Catalogs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read Estimate  Resource Info..
        /// </summary>
        public static string Hint_Read_Esimate_Resource {
            get {
                return ResourceManager.GetString("Hint_Read_Esimate_Resource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Read Project Info..
        /// </summary>
        public static string Hint_Read_Project_Info {
            get {
                return ResourceManager.GetString("Hint_Read_Project_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recalculate lineItems and resources cost: {0} seconds..
        /// </summary>
        public static string Hint_RecalculateLineItems {
            get {
                return ResourceManager.GetString("Hint_RecalculateLineItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Register rules cost: {0} seconds..
        /// </summary>
        public static string Hint_RegisterRule {
            get {
                return ResourceManager.GetString("Hint_RegisterRule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The code [{0}] is exeist , and replace with code [{1}].
        /// </summary>
        public static string Hint_Replace_Code {
            get {
                return ResourceManager.GetString("Hint_Replace_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current resource isn&apos;t a sub item.
        /// </summary>
        public static string Hint_Resource_Is_Not_SubItem {
            get {
                return ResourceManager.GetString("Hint_Resource_Is_Not_SubItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not found any resources.
        /// </summary>
        public static string Hint_Resource_Not_Found {
            get {
                return ResourceManager.GetString("Hint_Resource_Not_Found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not found any resources which code = {0}.
        /// </summary>
        public static string Hint_Resource_With_Code_Not_Found {
            get {
                return ResourceManager.GetString("Hint_Resource_With_Code_Not_Found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rule executed success.
        /// </summary>
        public static string Hint_Rule_Executed_Success {
            get {
                return ResourceManager.GetString("Hint_Rule_Executed_Success", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It costs {0} seconds to run current script..
        /// </summary>
        public static string Hint_Rule_Execution_Cost {
            get {
                return ResourceManager.GetString("Hint_Rule_Execution_Cost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saving Lineitems and Resouces.
        /// </summary>
        public static string Hint_Save_LineItems {
            get {
                return ResourceManager.GetString("Hint_Save_LineItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save changes to database cost: {0} seconds.
        /// </summary>
        public static string Hint_SaveChanges {
            get {
                return ResourceManager.GetString("Hint_SaveChanges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Export to 4.0..
        /// </summary>
        public static string Hint_Start_BidEstimate {
            get {
                return ResourceManager.GetString("Hint_Start_BidEstimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current rule executed in machine {0}, ProductVersion is {1}, BuildVersion is {2}..
        /// </summary>
        public static string Hint_System_Info {
            get {
                return ResourceManager.GetString("Hint_System_Info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update estimate cost: {0} hours, {1} minutes, and {2} seconds..
        /// </summary>
        public static string Hint_UpdateEstimate {
            get {
                return ResourceManager.GetString("Hint_UpdateEstimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activity Schedule.
        /// </summary>
        public static string Label_ActivitySchedule {
            get {
                return ResourceManager.GetString("Label_ActivitySchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ.
        /// </summary>
        public static string Label_Boq {
            get {
                return ResourceManager.GetString("Label_Boq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency.
        /// </summary>
        public static string Label_ColumId_BasCurrencyFk {
            get {
                return ResourceManager.GetString("Label_ColumId_BasCurrencyFk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Budget.
        /// </summary>
        public static string Label_ColumId_Budget {
            get {
                return ResourceManager.GetString("Label_ColumId_Budget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code.
        /// </summary>
        public static string Label_ColumId_Code {
            get {
                return ResourceManager.GetString("Label_ColumId_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comment.
        /// </summary>
        public static string Label_ColumId_CommentText {
            get {
                return ResourceManager.GetString("Label_ColumId_CommentText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost-Factor 1.
        /// </summary>
        public static string Label_ColumId_CostFactor1 {
            get {
                return ResourceManager.GetString("Label_ColumId_CostFactor1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost-Factor 2.
        /// </summary>
        public static string Label_ColumId_CostFactor2 {
            get {
                return ResourceManager.GetString("Label_ColumId_CostFactor2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost-Factor CC.
        /// </summary>
        public static string Label_ColumId_CostFactorCc {
            get {
                return ResourceManager.GetString("Label_ColumId_CostFactorCc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost-Factor Details 1.
        /// </summary>
        public static string Label_ColumId_CostFactorDetail1 {
            get {
                return ResourceManager.GetString("Label_ColumId_CostFactorDetail1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost-Factor Details 2.
        /// </summary>
        public static string Label_ColumId_CostFactorDetail2 {
            get {
                return ResourceManager.GetString("Label_ColumId_CostFactorDetail2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Factors.
        /// </summary>
        public static string Label_ColumId_CostFactors {
            get {
                return ResourceManager.GetString("Label_ColumId_CostFactors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Total.
        /// </summary>
        public static string Label_ColumId_CostTotal {
            get {
                return ResourceManager.GetString("Label_ColumId_CostTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Type.
        /// </summary>
        public static string Label_ColumId_CostTypeFk {
            get {
                return ResourceManager.GetString("Label_ColumId_CostTypeFk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost/Unit.
        /// </summary>
        public static string Label_ColumId_CostUnit {
            get {
                return ResourceManager.GetString("Label_ColumId_CostUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost/Unit Line Item.
        /// </summary>
        public static string Label_ColumId_CostUnitLineitem {
            get {
                return ResourceManager.GetString("Label_ColumId_CostUnitLineitem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost/Unit Original.
        /// </summary>
        public static string Label_ColumId_CostUnitOriginal {
            get {
                return ResourceManager.GetString("Label_ColumId_CostUnitOriginal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost/Unit Sub-Item.
        /// </summary>
        public static string Label_ColumId_CostUnitSubitem {
            get {
                return ResourceManager.GetString("Label_ColumId_CostUnitSubitem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost/Unit Item.
        /// </summary>
        public static string Label_ColumId_CostUnitTarget {
            get {
                return ResourceManager.GetString("Label_ColumId_CostUnitTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Decription.
        /// </summary>
        public static string Label_ColumId_DescriptionInfo {
            get {
                return ResourceManager.GetString("Label_ColumId_DescriptionInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Efficiency-Factor 1.
        /// </summary>
        public static string Label_ColumId_EfficiencyFactor1 {
            get {
                return ResourceManager.GetString("Label_ColumId_EfficiencyFactor1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Efficiency-Factor 2.
        /// </summary>
        public static string Label_ColumId_EfficiencyFactor2 {
            get {
                return ResourceManager.GetString("Label_ColumId_EfficiencyFactor2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Efficiency-Factor Details 1.
        /// </summary>
        public static string Label_ColumId_EfficiencyFactorDetail1 {
            get {
                return ResourceManager.GetString("Label_ColumId_EfficiencyFactorDetail1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Efficiency-Factor Details 2.
        /// </summary>
        public static string Label_ColumId_EfficiencyFactorDetail2 {
            get {
                return ResourceManager.GetString("Label_ColumId_EfficiencyFactorDetail2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hours Total.
        /// </summary>
        public static string Label_ColumId_HoursTotal {
            get {
                return ResourceManager.GetString("Label_ColumId_HoursTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hours/Unit.
        /// </summary>
        public static string Label_ColumId_HoursUnit {
            get {
                return ResourceManager.GetString("Label_ColumId_HoursUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hours/Unit Line Item.
        /// </summary>
        public static string Label_ColumId_HoursUnitLineitem {
            get {
                return ResourceManager.GetString("Label_ColumId_HoursUnitLineitem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hours/Unit Sub-Item.
        /// </summary>
        public static string Label_ColumId_HoursUnitSubitem {
            get {
                return ResourceManager.GetString("Label_ColumId_HoursUnitSubitem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hours/Unit Item.
        /// </summary>
        public static string Label_ColumId_HoursUnitTarget {
            get {
                return ResourceManager.GetString("Label_ColumId_HoursUnitTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indirect Cost.
        /// </summary>
        public static string Label_ColumId_IndirectCost {
            get {
                return ResourceManager.GetString("Label_ColumId_IndirectCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled.
        /// </summary>
        public static string Label_ColumId_IsDisabled {
            get {
                return ResourceManager.GetString("Label_ColumId_IsDisabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IsGerneratePrc.
        /// </summary>
        public static string Label_ColumId_IsGerneratePrc {
            get {
                return ResourceManager.GetString("Label_ColumId_IsGerneratePrc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lump Sum.
        /// </summary>
        public static string Label_ColumId_IsLumpSum {
            get {
                return ResourceManager.GetString("Label_ColumId_IsLumpSum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item Price Total.
        /// </summary>
        public static string Label_ColumId_ItemPriceTotal {
            get {
                return ResourceManager.GetString("Label_ColumId_ItemPriceTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job.
        /// </summary>
        public static string Label_ColumId_Job {
            get {
                return ResourceManager.GetString("Label_ColumId_Job", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price/Unit Item.
        /// </summary>
        public static string Label_ColumId_PriceUnitItem {
            get {
                return ResourceManager.GetString("Label_ColumId_PriceUnitItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Procurement-Package.
        /// </summary>
        public static string Label_ColumId_ProcurementPackage {
            get {
                return ResourceManager.GetString("Label_ColumId_ProcurementPackage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Procurement Structure.
        /// </summary>
        public static string Label_ColumId_ProcurementStructure {
            get {
                return ResourceManager.GetString("Label_ColumId_ProcurementStructure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Procurement-Sub-Package.
        /// </summary>
        public static string Label_ColumId_ProcurementSubPackage {
            get {
                return ResourceManager.GetString("Label_ColumId_ProcurementSubPackage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Productivity-Factor.
        /// </summary>
        public static string Label_ColumId_ProductivityFactor {
            get {
                return ResourceManager.GetString("Label_ColumId_ProductivityFactor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Productivity-Factor Detail.
        /// </summary>
        public static string Label_ColumId_ProductivityFactorDetail {
            get {
                return ResourceManager.GetString("Label_ColumId_ProductivityFactorDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        public static string Label_ColumId_Quantity {
            get {
                return ResourceManager.GetString("Label_ColumId_Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Details.
        /// </summary>
        public static string Label_ColumId_QuantityDetail {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity-Factor 1.
        /// </summary>
        public static string Label_ColumId_QuantityFactor1 {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityFactor1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity-Factor 2.
        /// </summary>
        public static string Label_ColumId_QuantityFactor2 {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityFactor2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity-Factor 3.
        /// </summary>
        public static string Label_ColumId_QuantityFactor3 {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityFactor3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity-Factor 4.
        /// </summary>
        public static string Label_ColumId_QuantityFactor4 {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityFactor4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity-Factor CC.
        /// </summary>
        public static string Label_ColumId_QuantityFactorCc {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityFactorCc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity-Factor Details 1.
        /// </summary>
        public static string Label_ColumId_QuantityFactorDetail1 {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityFactorDetail1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity-Factor Details 2.
        /// </summary>
        public static string Label_ColumId_QuantityFactorDetail2 {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityFactorDetail2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Factors.
        /// </summary>
        public static string Label_ColumId_QuantityFactors {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityFactors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Internal.
        /// </summary>
        public static string Label_ColumId_QuantityInternal {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityInternal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Real.
        /// </summary>
        public static string Label_ColumId_QuantityReal {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityReal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AQ Quantity.
        /// </summary>
        public static string Label_ColumId_QuantityTarget {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Total.
        /// </summary>
        public static string Label_ColumId_QuantityTotal {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity/Unit Item.
        /// </summary>
        public static string Label_ColumId_QuantityUnitTarget {
            get {
                return ResourceManager.GetString("Label_ColumId_QuantityUnitTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource Flag.
        /// </summary>
        public static string Label_ColumId_ResourceFlagFk {
            get {
                return ResourceManager.GetString("Label_ColumId_ResourceFlagFk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rule Generated.
        /// </summary>
        public static string Label_ColumId_RuleGenerated {
            get {
                return ResourceManager.GetString("Label_ColumId_RuleGenerated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Key.
        /// </summary>
        public static string Label_ColumId_ShortKey {
            get {
                return ResourceManager.GetString("Label_ColumId_ShortKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uom.
        /// </summary>
        public static string Label_ColumId_UomFk {
            get {
                return ResourceManager.GetString("Label_ColumId_UomFk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to WQ Quantity.
        /// </summary>
        public static string Label_ColumId_WqQuantityTarget {
            get {
                return ResourceManager.GetString("Label_ColumId_WqQuantityTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activity Code.
        /// </summary>
        public static string Label_ExportColumn_Activity {
            get {
                return ResourceManager.GetString("Label_ExportColumn_Activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assembly Category.
        /// </summary>
        public static string Label_ExportColumn_AssemblyCategory {
            get {
                return ResourceManager.GetString("Label_ExportColumn_AssemblyCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ Header RefNo.
        /// </summary>
        public static string Label_ExportColumn_BoQHeader {
            get {
                return ResourceManager.GetString("Label_ExportColumn_BoQHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent Level of Assembly Category.
        /// </summary>
        public static string Label_ExportColumn_ParentLevelAssembly {
            get {
                return ResourceManager.GetString("Label_ExportColumn_ParentLevelAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record Type.
        /// </summary>
        public static string Label_ExportColumn_RecordType {
            get {
                return ResourceManager.GetString("Label_ExportColumn_RecordType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedule Code.
        /// </summary>
        public static string Label_ExportColumn_Schedule {
            get {
                return ResourceManager.GetString("Label_ExportColumn_Schedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Structure FK.
        /// </summary>
        public static string Label_ExportColumn_Structure {
            get {
                return ResourceManager.GetString("Label_ExportColumn_Structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mapping Record Type is not integer!.
        /// </summary>
        public static string Label_ExportColumn_WarnningRecordType {
            get {
                return ResourceManager.GetString("Label_ExportColumn_WarnningRecordType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to WIC BoQ Root Item Ref No.
        /// </summary>
        public static string Label_ExportColumn_WICCategory {
            get {
                return ResourceManager.GetString("Label_ExportColumn_WICCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to WIC Group Ref No.
        /// </summary>
        public static string Label_ExportColumn_WICGruop {
            get {
                return ResourceManager.GetString("Label_ExportColumn_WICGruop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Code should not be setted default Value!.
        /// </summary>
        public static string Label_ImportColumn_Code {
            get {
                return ResourceManager.GetString("Label_ImportColumn_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic Cost Group 1.
        /// </summary>
        public static string Label_LeadingStructure_BasicCostGroup1 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_BasicCostGroup1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic Cost Group 2.
        /// </summary>
        public static string Label_LeadingStructure_BasicCostGroup2 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_BasicCostGroup2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic Cost Group 3.
        /// </summary>
        public static string Label_LeadingStructure_BasicCostGroup3 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_BasicCostGroup3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic Cost Group 4.
        /// </summary>
        public static string Label_LeadingStructure_BasicCostGroup4 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_BasicCostGroup4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic Cost Group 5.
        /// </summary>
        public static string Label_LeadingStructure_BasicCostGroup5 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_BasicCostGroup5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Controlling Unit.
        /// </summary>
        public static string Label_LeadingStructure_ControllingUnit {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_ControllingUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string Label_LeadingStructure_Location {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project Cost Group 1.
        /// </summary>
        public static string Label_LeadingStructure_ProjectCostGroup1 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_ProjectCostGroup1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project Cost Group 2.
        /// </summary>
        public static string Label_LeadingStructure_ProjectCostGroup2 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_ProjectCostGroup2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project Cost Group 3.
        /// </summary>
        public static string Label_LeadingStructure_ProjectCostGroup3 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_ProjectCostGroup3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project Cost Group 4.
        /// </summary>
        public static string Label_LeadingStructure_ProjectCostGroup4 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_ProjectCostGroup4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project Cost Group 5.
        /// </summary>
        public static string Label_LeadingStructure_ProjectCostGroup5 {
            get {
                return ResourceManager.GetString("Label_LeadingStructure_ProjectCostGroup5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Model Code.
        /// </summary>
        public static string Lable_ExportColumn_Model {
            get {
                return ResourceManager.GetString("Lable_ExportColumn_Model", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Item without Structure.
        /// </summary>
        public static string LineItem_Without_Structure {
            get {
                return ResourceManager.GetString("LineItem_Without_Structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Items.
        /// </summary>
        public static string LineItemRefName {
            get {
                return ResourceManager.GetString("LineItemRefName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assembly.
        /// </summary>
        public static string Modify_Function_Replace_Assembly {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_Assembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Assembly By Assembly.
        /// </summary>
        public static string Modify_Function_Replace_AssemblyByAssembly {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_AssemblyByAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Assembly By CostCode.
        /// </summary>
        public static string Modify_Function_Replace_AssemblyByCostCode {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_AssemblyByCostCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Assembly By Material.
        /// </summary>
        public static string Modify_Function_Replace_AssemblyByMaterial {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_AssemblyByMaterial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Cost Code By Assembly.
        /// </summary>
        public static string Modify_Function_Replace_CCByAssembly {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_CCByAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Cost Code By Cost Code.
        /// </summary>
        public static string Modify_Function_Replace_CCByCostCode {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_CCByCostCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Cost Code By Material.
        /// </summary>
        public static string Modify_Function_Replace_CCByMaterial {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_CCByMaterial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Code.
        /// </summary>
        public static string Modify_Function_Replace_CostCode {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_CostCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Cost Code By Plant.
        /// </summary>
        public static string Modify_Function_Replace_CostCodeByPlant {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_CostCodeByPlant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Equipment Assembly By Equipment Assembly.
        /// </summary>
        public static string Modify_Function_Replace_EquipmentAssemblyByEquipmentAssembly {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_EquipmentAssemblyByEquipmentAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Material By Assembly.
        /// </summary>
        public static string Modify_Function_Replace_MAByAssembly {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_MAByAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Material By Cost Code.
        /// </summary>
        public static string Modify_Function_Replace_MAByCostCode {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_MAByCostCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Material By Material.
        /// </summary>
        public static string Modify_Function_Replace_MAByMaterial {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_MAByMaterial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Material.
        /// </summary>
        public static string Modify_Function_Replace_Material {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_Material", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Material By Plant.
        /// </summary>
        public static string Modify_Function_Replace_MaterialByPlant {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_MaterialByPlant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Plant by Plant.
        /// </summary>
        public static string Modify_Function_Replace_PlantByPlant {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_PlantByPlant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimate - Elements.
        /// </summary>
        public static string Modify_Function_Replace_Root {
            get {
                return ResourceManager.GetString("Modify_Function_Replace_Root", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Resources.
        /// </summary>
        public static string Modify_Function_Revmoe_Resource {
            get {
                return ResourceManager.GetString("Modify_Function_Revmoe_Resource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Object properties.
        /// </summary>
        public static string ObjectFilter_ObjectProps_Parent {
            get {
                return ResourceManager.GetString("ObjectFilter_ObjectProps_Parent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Plant Assemblies.
        /// </summary>
        public static string Plant_Totals_Assembly {
            get {
                return ResourceManager.GetString("Plant_Totals_Assembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Difference.
        /// </summary>
        public static string Plant_Totals_Difference {
            get {
                return ResourceManager.GetString("Plant_Totals_Difference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Stand-by Plant.
        /// </summary>
        public static string Plant_Totals_Stand {
            get {
                return ResourceManager.GetString("Plant_Totals_Stand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Estimate finished !.
        /// </summary>
        public static string Prc_Update_Estimate_Ok {
            get {
                return ResourceManager.GetString("Prc_Update_Estimate_Ok", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reported Progress.
        /// </summary>
        public static string ProgressReportsRefName {
            get {
                return ResourceManager.GetString("ProgressReportsRefName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        public static string String1 {
            get {
                return ResourceManager.GetString("String1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activity.
        /// </summary>
        public static string Structure_Type_LeadingStructure_Activity {
            get {
                return ResourceManager.GetString("Structure_Type_LeadingStructure_Activity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assembly Category.
        /// </summary>
        public static string Structure_Type_LeadingStructure_Assembly_Category {
            get {
                return ResourceManager.GetString("Structure_Type_LeadingStructure_Assembly_Category", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BoQ.
        /// </summary>
        public static string Structure_Type_LeadingStructure_BoQ {
            get {
                return ResourceManager.GetString("Structure_Type_LeadingStructure_BoQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Controlling Unit.
        /// </summary>
        public static string Structure_Type_LeadingStructure_Controlling_Unit {
            get {
                return ResourceManager.GetString("Structure_Type_LeadingStructure_Controlling_Unit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost Group.
        /// </summary>
        public static string Structure_Type_LeadingStructure_Cost_Group {
            get {
                return ResourceManager.GetString("Structure_Type_LeadingStructure_Cost_Group", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimate Root.
        /// </summary>
        public static string Structure_Type_LeadingStructure_Estimate_Root {
            get {
                return ResourceManager.GetString("Structure_Type_LeadingStructure_Estimate_Root", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        public static string Structure_Type_LeadingStructure_Location {
            get {
                return ResourceManager.GetString("Structure_Type_LeadingStructure_Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Procurement Structure.
        /// </summary>
        public static string Structure_Type_LeadingStructure_Procurement_Structure {
            get {
                return ResourceManager.GetString("Structure_Type_LeadingStructure_Procurement_Structure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project BoQ.
        /// </summary>
        public static string Structure_Type_LeadingStructure_Project_BoQ {
            get {
                return ResourceManager.GetString("Structure_Type_LeadingStructure_Project_BoQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LineItem.
        /// </summary>
        public static string Structure_Type_LineItem {
            get {
                return ResourceManager.GetString("Structure_Type_LineItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System.
        /// </summary>
        public static string Structure_Type_System {
            get {
                return ResourceManager.GetString("Structure_Type_System", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning:AA (Advanced AllowanceCc) has not been transferred, Cost Code Definition of AA was not found in RIB 4.0..
        /// </summary>
        public static string Warn_AA {
            get {
                return ResourceManager.GetString("Warn_AA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All the line item and resource are assigned to prcItem assignment..
        /// </summary>
        public static string Warn_All_Data_Are_Assigned_PrcItem {
            get {
                return ResourceManager.GetString("Warn_All_Data_Are_Assigned_PrcItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} assembly [{1}] is found.
        /// </summary>
        public static string Warn_AssemblyFound {
            get {
                return ResourceManager.GetString("Warn_AssemblyFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assembly [{0}] is missing, create new assembly automatically.
        /// </summary>
        public static string Warn_AssemblyMissing {
            get {
                return ResourceManager.GetString("Warn_AssemblyMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning:.
        /// </summary>
        public static string Warn_BidImport {
            get {
                return ResourceManager.GetString("Warn_BidImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source BoQ item or sub package is not found.
        /// </summary>
        public static string Warn_BoqOrSubpackage_Not_Found {
            get {
                return ResourceManager.GetString("Warn_BoqOrSubpackage_Not_Found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The BoQ Items with ids [{0}] are not allowed to delete!.
        /// </summary>
        public static string Warn_BoQs_NotAllowToDelete {
            get {
                return ResourceManager.GetString("Warn_BoQs_NotAllowToDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning:Fixed unit Rate and Labor Cost Code in costcode({0}) are different from its parent({1}) in baseLine!.
        /// </summary>
        public static string Warn_CoCParentReplace {
            get {
                return ResourceManager.GetString("Warn_CoCParentReplace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Some Cost Group Codes are empty!.
        /// </summary>
        public static string Warn_CostGroup_EmptyCode {
            get {
                return ResourceManager.GetString("Warn_CostGroup_EmptyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Esitmate is General Contractor.
        /// </summary>
        public static string Warn_Estimate_IsGc {
            get {
                return ResourceManager.GetString("Warn_Estimate_IsGc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimate is readonly.
        /// </summary>
        public static string Warn_Estimate_IsReadonly {
            get {
                return ResourceManager.GetString("Warn_Estimate_IsReadonly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Characteristic {0} already exists in line item .
        /// </summary>
        public static string Warn_ExistingCharacteristic {
            get {
                return ResourceManager.GetString("Warn_ExistingCharacteristic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning:FM (Final Markup) has not been transferred, Cost Code Definition of FM was not found in RIB 4.0..
        /// </summary>
        public static string Warn_FM {
            get {
                return ResourceManager.GetString("Warn_FM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning:GC (General Cost) has not been transferred, Cost Code Definition of GC was not found in RIB 4.0..
        /// </summary>
        public static string Warn_GC {
            get {
                return ResourceManager.GetString("Warn_GC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please setting the Number Rangs in Company module..
        /// </summary>
        public static string Warn_ItemCode_NoSetting_NumberRangs {
            get {
                return ResourceManager.GetString("Warn_ItemCode_NoSetting_NumberRangs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The code is Null or Empty.Please check Number Rangs setting..
        /// </summary>
        public static string Warn_ItemCode_NullOrEmpty {
            get {
                return ResourceManager.GetString("Warn_ItemCode_NullOrEmpty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The code is same with existing item({0}).Please check Number Rangs setting..
        /// </summary>
        public static string Warn_ItemCode_Same {
            get {
                return ResourceManager.GetString("Warn_ItemCode_Same", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning:No Language({0}) Defined in RIB 4.0..
        /// </summary>
        public static string Warn_Language_NoDefined {
            get {
                return ResourceManager.GetString("Warn_Language_NoDefined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning: {0} converted as {1}, but {1} language already has added, the translation({0}: {2}) will be ignored.
        /// </summary>
        public static string Warn_Language_Reduplicate {
            get {
                return ResourceManager.GetString("Warn_Language_Reduplicate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}  is readonly.
        /// </summary>
        public static string Warn_LineItem_IsReadonly {
            get {
                return ResourceManager.GetString("Warn_LineItem_IsReadonly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The LineItems references to the BoQ items cannot be deleted, because the status of Estimate Headers with codes [{0}] is Locked or Readonly.
        /// </summary>
        public static string Warn_LineItems_NotDelete_Ref_EstHeader_Readonly {
            get {
                return ResourceManager.GetString("Warn_LineItems_NotDelete_Ref_EstHeader_Readonly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The LineItems with codes [{0}] can not be deleted because .
        /// </summary>
        public static string Warn_LineItems_NotDeleted {
            get {
                return ResourceManager.GetString("Warn_LineItems_NotDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The LineItems cannot be deleted because the status of Estimate Headers with codes [{0}] is Locked or Readonly.
        /// </summary>
        public static string Warn_LineItems_NotDeleted_EstHeader_Readonly {
            get {
                return ResourceManager.GetString("Warn_LineItems_NotDeleted_EstHeader_Readonly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The LineItems with codes [{0}] are assigned to either Activity or Procurement Package! Not allowed to delete!.
        /// </summary>
        public static string Warn_LineItemsWithActivity_Package_Found {
            get {
                return ResourceManager.GetString("Warn_LineItemsWithActivity_Package_Found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Boq RN({0}) and SubItemNo({1})..
        /// </summary>
        public static string Warn_Lumpsum_Absolute_Detail {
            get {
                return ResourceManager.GetString("Warn_Lumpsum_Absolute_Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are lumpsum absolute records in following Items/sub items in the Project which needs user attention in Line Items in RIB 4.0. User has to manually update the cost for all such line items in RIB 4.0, Detail Info:.
        /// </summary>
        public static string Warn_Lumpsum_Absolute_Title {
            get {
                return ResourceManager.GetString("Warn_Lumpsum_Absolute_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boq [{0}] is missing.
        /// </summary>
        public static string Warn_MissBoq {
            get {
                return ResourceManager.GetString("Warn_MissBoq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Missing project, please select a project.
        /// </summary>
        public static string Warn_MissProject {
            get {
                return ResourceManager.GetString("Warn_MissProject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning: The Cost Code({0}) not find and the parent Cost Code({1}) not Project Child Allowed..
        /// </summary>
        public static string Warn_No_CostCode_AllowedChild {
            get {
                return ResourceManager.GetString("Warn_No_CostCode_AllowedChild", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Some Cost Groups were not assigned.Please do synchronization for enterprise Cost Group..
        /// </summary>
        public static string Warn_No_CostGroup_Assigned {
            get {
                return ResourceManager.GetString("Warn_No_CostGroup_Assigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DeepCopy Failed! Estimate Type does not exist, please check Input Parameters..
        /// </summary>
        public static string Warn_Not_Exist_EstType {
            get {
                return ResourceManager.GetString("Warn_Not_Exist_EstType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The parameter is null.
        /// </summary>
        public static string Warn_Parameter_Is_Null {
            get {
                return ResourceManager.GetString("Warn_Parameter_Is_Null", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project Cost Code({0}) CostFactorCC is Zero..
        /// </summary>
        public static string Warn_Prj_CostFacterCC_IsZero {
            get {
                return ResourceManager.GetString("Warn_Prj_CostFacterCC_IsZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Project Cost Code({0}) QuantityFactorCC is Zero..
        /// </summary>
        public static string Warn_Prj_QuantityFacterCC_IsZero {
            get {
                return ResourceManager.GetString("Warn_Prj_QuantityFacterCC_IsZero", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IScriptResourceTypeFacade [{0}] is missing.
        /// </summary>
        public static string Warn_ResFacade {
            get {
                return ResourceManager.GetString("Warn_ResFacade", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} [{1}] is missing, create new {0} automatically.
        /// </summary>
        public static string Warn_ResMissing {
            get {
                return ResourceManager.GetString("Warn_ResMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Related Job is in Read-Only Status.
        /// </summary>
        public static string Warn_Resouce_Job_ReadOnly_Msg {
            get {
                return ResourceManager.GetString("Warn_Resouce_Job_ReadOnly_Msg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No modifiable resource can be found in this leading structure filter and resource filter condition ! Please recheck your resources or filter..
        /// </summary>
        public static string Warn_Resource_NotFound {
            get {
                return ResourceManager.GetString("Warn_Resource_NotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Some resources are used by package..
        /// </summary>
        public static string Warn_ResourceUsedByPackage {
            get {
                return ResourceManager.GetString("Warn_ResourceUsedByPackage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No BoQ root items found in the sub package.
        /// </summary>
        public static string Warn_RootBoqItems_Not_Found {
            get {
                return ResourceManager.GetString("Warn_RootBoqItems_Not_Found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} can not find or not exist which code is [{1}].
        /// </summary>
        public static string Warn_Rules_Resource_Create_Not_Found {
            get {
                return ResourceManager.GetString("Warn_Rules_Resource_Create_Not_Found", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There already exists a [{1}] code for the {0}.
        /// </summary>
        public static string Warn_Rules_Resource_Create_Unique_Code {
            get {
                return ResourceManager.GetString("Warn_Rules_Resource_Create_Unique_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not modify the {0} of raw resource.
        /// </summary>
        public static string Warn_Rules_Resource_Modified_Prototype {
            get {
                return ResourceManager.GetString("Warn_Rules_Resource_Modified_Prototype", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can not create Plant, The Estimate Job no set Plant Estimate Pricelist.
        /// </summary>
        public static string Warn_Rules_Resource_Plant_Not_Create {
            get {
                return ResourceManager.GetString("Warn_Rules_Resource_Plant_Not_Create", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This transaction is currently not allowed. Please try again after Rules are executed completely..
        /// </summary>
        public static string Warn_Rules_Running_Background {
            get {
                return ResourceManager.GetString("Warn_Rules_Running_Background", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning:Surcharge Cost Code has not been transferred, Cost Code Definition of Surcharge Item was not found in System Option..
        /// </summary>
        public static string Warn_SurchargeCostCode {
            get {
                return ResourceManager.GetString("Warn_SurchargeCostCode", resourceCulture);
            }
        }
    }
}
