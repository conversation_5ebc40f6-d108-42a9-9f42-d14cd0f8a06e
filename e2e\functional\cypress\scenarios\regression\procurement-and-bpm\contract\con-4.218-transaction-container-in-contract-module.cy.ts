import { app, btn, cnt, sidebar,commonLocators, tile } from "cypress/locators";
import Buttons from "cypress/locators/buttons";
import CommonLocators from "cypress/locators/common-locators";
import Sidebar from "cypress/locators/sidebar";
import {_projectPage,_saleContractPage, _common,_package,_controllingUnit,_validate,_mainView,_procurementContractPage, _sidebar} from "cypress/pages";
import type { DataCells } from 'cypress/pages/interfaces.d.ts'
import { PACKAGE_TOTAL_TRANSLATION } from 'cypress/pages/variables';


const PROJECT_NO=_common.generateRandomString(2)
const PROJECT_DESC="PRDESC-" + _common.generateRandomString(2);
const CONTROLLING_UNIT_DESCRIPTION = 'CU-DESC-' + _common.generateRandomString(2);

let CONTRACT_PARAMETER,PROJECTS_PARAMETERS:DataCells;
let CONTAINERS_CONTRACT;
let CONTAINERS_ITEM,CONTAINER_COLUMNS_TRANSACTION;

let CONTAINERS_CONTROLLING_UNIT,CONTAINER_COLUMNS_CONTROLLING_UNIT,CONTAINER_COLUMNS_TOTAL
let CONTROLLING_UNIT_PARAMETERS:DataCells
let CONTAINER_COLUMNS_ITEM,CONTAINER_COLUMNS_CONTRACT,CONTAINERS_PROJECT,CONTAINERS_CUSTOMIZING;

describe('PCM- 4.218 | Transaction container in Contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('pcm/con-4.218-transaction-container-in-contract-module.json').then((data) => {
            this.data = data;
            CONTAINERS_PROJECT = this.data.CONTAINERS.PROJECT;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
            CONTAINERS_CUSTOMIZING = this.data.CONTAINERS.CUSTOMIZING;
            CONTAINERS_ITEM = this.data.CONTAINERS.ITEM;       
            
            CONTAINER_COLUMNS_ITEM = this.data.CONTAINER_COLUMNS.ITEM
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            
            CONTRACT_PARAMETER = {
              [commonLocators.CommonLabels.CONFIGURATION]:CONTAINERS_CONTRACT.CONFIGURATION,
              [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESSPARTNER

          };
          CONTAINERS_CONTROLLING_UNIT=this.data.CONTAINERS.CONTROLLING_UNIT
          CONTAINER_COLUMNS_CONTROLLING_UNIT= this.data.CONTAINER_COLUMNS.CONTROLLING_UNIT
          CONTAINER_COLUMNS_TOTAL= this.data.CONTAINER_COLUMNS.TOTAL
          CONTAINER_COLUMNS_TRANSACTION= this.data.CONTAINER_COLUMNS.TRANSACTION
          
          CONTROLLING_UNIT_PARAMETERS={
            [app.GridCells.DESCRIPTION_INFO]:CONTROLLING_UNIT_DESCRIPTION,
            [app.GridCells.QUANTITY_SMALL]:CONTAINERS_CONTROLLING_UNIT.QUANTITY,
            [app.GridCells.UOM_FK]:CONTAINERS_CONTROLLING_UNIT.UOM
          }
          PROJECTS_PARAMETERS={
            [commonLocators.CommonLabels.PROJECT_NUMBER]:PROJECT_NO,
            [commonLocators.CommonLabels.NAME]:PROJECT_DESC,
            [commonLocators.CommonLabels.CLERK]:CONTAINERS_PROJECT.CLERK_NAME
        }
        });
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
       
     });
  
    after(() => {
        cy.LOGOUT();
    });

    it('TC - Precondition - Delete record from controlling unit assignment in customizing', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING); 
        _common.waitForLoaderToDisappear()

        cy.wait(2000) //required wait to load page
        cy.REFRESH_CONTAINER();
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES, 1);
            _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES);
        });
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CONTAINERS_CUSTOMIZING.DATA_TYPE_LABEL);
        cy.REFRESH_CONTAINER();
        _common.clickOn_cellHasUniqueValue(cnt.uuid.DATA_TYPES, app.GridCells.NAME,  CONTAINERS_CUSTOMIZING.DATA_TYPE_LABEL);
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORD, 1);
            _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS);
        });
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.DATA_RECORDS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
    })

    it('TC - Create project', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT); 
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PROJECT).then(() => {
        _common.setDefaultView(app.TabBar.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
        _common.create_newRecord(cnt.uuid.PROJECTS);
        _projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS);
        cy.SAVE(); 
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO).pinnedItem();
        _common.waitForLoaderToDisappear()
       
    })

    it("TC - Create controlling unit", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTROLLING_UNITS); 
		_common.waitForLoaderToDisappear()

		_common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT_PROJECTS, app.FooterTab.PROJECTS, 0);
            _common.setup_gridLayout(cnt.uuid.CONTROLLING_UNIT, CONTAINER_COLUMNS_CONTROLLING_UNIT)
		});
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO).pinnedItem();  

		_common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
			_common.setDefaultView(app.TabBar.CONTROLLINGSTRUCTURE)
			_common.waitForLoaderToDisappear()
			_common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT, app.FooterTab.CONTROLLING_UNITS, 2);
			
		});
		_common.waitForLoaderToDisappear()
		_common.maximizeContainer(cnt.uuid.CONTROLLING_UNIT)
		_controllingUnit.enterRecord_toCreateControllingUnit(CONTROLLING_UNIT_PARAMETERS);
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.CONTROLLING_UNIT,CONTROLLING_UNIT_DESCRIPTION)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT,app.GridCells.ISA_ACCOUNTING_ELEMENT,commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT,app.GridCells.IS_BILLING_ELEMENT,commonLocators.CommonKeys.CHECK)
        cy.SAVE()
		_common.minimizeContainer(cnt.uuid.CONTROLLING_UNIT)
		_common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
			_common.setDefaultView(app.TabBar.CONTROLLINGSTRUCTURE)
            _common.waitForLoaderToDisappear()
			_common.waitForLoaderToDisappear()
			_common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT_DETAILS, app.FooterTab.CONTROLLING_UNIT_DETAILS, 1);
		});
    
        _common.maximizeContainer(cnt.uuid.CONTROLLING_UNIT_DETAILS)
        _common.clickOn_expandCollapseButton(cnt.uuid.CONTROLLING_UNIT_DETAILS, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.COLLAPSE_ALL)
        _common.waitForLoaderToDisappear()
        _common.clickOn_lableDownArrow_inContainer(CONTAINERS_CONTROLLING_UNIT.LABLE_NAME)
        _common.waitForLoaderToDisappear()
        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[0],PROJECT_NO)
        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[1],CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[0])
        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[2],CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[1])
       
        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[3],CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[2])
        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[4],CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[3])
        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[5],CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[4])
        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[6],CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[5])

        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[7],CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[6])
        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[8],CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[7])
        _common.edit_containerLabelCell_withDynamicInput(cnt.uuid.CONTROLLING_UNIT_DETAILS,app.InputFields.DOMAIN_TYPE_CODE,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET_LABEL[9],CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[8])
       
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONTROLLING_UNIT_DETAILS)
	});

    it("TC - Create new contract record ", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
    
        _common.openTab(app.TabBar.CONTRACT).then(()=>{

        _common.setDefaultView(app.TabBar.CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT,app.FooterTab.CONTRACTS,0)
        _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT,CONTAINER_COLUMNS_CONTRACT)
        _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.code,CONTAINER_COLUMNS_CONTRACT.bpdvatgroupfk,
            CONTAINER_COLUMNS_CONTRACT.paymenttermfifk,CONTAINER_COLUMNS_CONTRACT.bascurrencyfk,CONTAINER_COLUMNS_CONTRACT.dateordered,
            CONTAINER_COLUMNS_CONTRACT.dateeffective,CONTAINER_COLUMNS_CONTRACT.businesspartnerfk,CONTAINER_COLUMNS_CONTRACT.subsidiaryfk,
            CONTAINER_COLUMNS_CONTRACT.contactfk,CONTAINER_COLUMNS_CONTRACT.datedelivery], cnt.uuid.PROCUREMENTCONTRACT)
        })
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO)
        _common.waitForLoaderToDisappear()

        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT,0)
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.CONTROLLING_UNIT_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTROLLING_UNIT_DESCRIPTION)
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.BUSINESS_PARTNER_FK, "CONTRACT_BP")
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.SUBSIDIARY_FK, "CONTRACT_BRANCH")
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.BPD_VAT_GROUP_FK, "CONTRACT_VAT_GRP")
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTACT_FK, "CONTRACT_CONTACT")
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DATE_EFFECTIVE, "CONTRACT_DATE_E")
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DATE_ORDERED, "CONTRACT_DATE_O")
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DATE_DELIVERY, "CONTRACT_DATE_D")
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.BAS_CURRENCY_FK, "CONTRACT_CURRENCY")
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PAYMENT_TERM_FI_FK, "CONTRACT_PT")
        _common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);

        _common.waitForLoaderToDisappear()
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED);
        _common.waitForLoaderToDisappear()

    })
   
    it("TC - Create Items for Contract",function(){
    
        _common.openTab(app.TabBar.ORDER_ITEM).then(()=>{
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT,app.FooterTab.ITEMS,0)
        _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT,CONTAINER_COLUMNS_ITEM)
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        })
        _common.maximizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT,app.GridCells.MDC_MATERIAL_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEM.MATERIAL_NO)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.ITEMSCONTRACT,app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ITEM.QUANTITY[1])
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT, app.GridCells.MDC_CONTROLLING_UNIT_FK_SMALL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTROLLING_UNIT_DESCRIPTION)
		
        cy.SAVE();
		_common.waitForLoaderToDisappear()
        _common.clickOn_cellHasValue(cnt.uuid.ITEMSCONTRACT,app.GridCells.MDC_MATERIAL_FK,CONTAINERS_ITEM.MATERIAL_NO)
        _common.saveCellDataToEnv(cnt.uuid.ITEMSCONTRACT, app.GridCells.MDC_TAX_CODE_FK_SMALL, "ITEM_TAX_CODE")
        _common.saveCellDataToEnv(cnt.uuid.ITEMSCONTRACT, app.GridCells.MDC_CONTROLLING_UNIT_FK_SMALL, "C_UNIT")
		_common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Fetch values from totals container",function(){
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_TOTALS, app.FooterTab.TOTALS, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_TOTALS,CONTAINER_COLUMNS_TOTAL)
        });
       
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_TOTALS, app.GridCells.TRANSLATED, commonLocators.CommonKeys.TOTAL, PACKAGE_TOTAL_TRANSLATION);
		_common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_TOTALS, app.GridCells.VALUE_NET, "TOTAL_VALUE_NET")
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_TOTALS, app.GridCells.VALUE_TAX, "TOTAL_VALUE_TAX")
		_common.waitForLoaderToDisappear()
       
    })
    
    it("TC - Verify generate transactions for contract using wizard, record gets created in transaction with readonly fields",function(){

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.GENERATING_TRANSACTIONS_FOR_CONTRACTS);
        _common.waitForLoaderToDisappear()
    
        _common.openTab(app.TabBar.CONTRACT).then(()=>{
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.FooterTab.TRANSACTIONS,0)
        _common.clear_subContainerFilter(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.setup_gridLayout(cnt.uuid.TRANSACTIONS_IN_CONTRACT,CONTAINER_COLUMNS_TRANSACTION)
        _common.set_columnAtTop([CONTAINER_COLUMNS_TRANSACTION.controllingunitfk,CONTAINER_COLUMNS_TRANSACTION.vatgroupfk,
            CONTAINER_COLUMNS_TRANSACTION.taxcodefk,CONTAINER_COLUMNS_TRANSACTION.incamount,CONTAINER_COLUMNS_TRANSACTION.incvatamount,
            CONTAINER_COLUMNS_TRANSACTION.amount,CONTAINER_COLUMNS_TRANSACTION.vatamount,CONTAINER_COLUMNS_TRANSACTION.companyfk], cnt.uuid.TRANSACTIONS_IN_CONTRACT)
    
        })
        _common.maximizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.select_rowInContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _validate.verify_isRecordNotEditable(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.COMPANY_FK,0)
        _validate.verify_isRecordNotEditable(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.VAT_AMOUNT,0)
        _validate.verify_isRecordNotEditable(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_FK,0)
        _validate.verify_isRecordNotEditable(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.VAT_GROUP_FK,0)
        _validate.verify_isRecordNotEditable(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.TAX_CODE_FK,0)
        _validate.verify_isRecordNotEditable(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.AMOUNT_SMALL,0)
        _validate.verify_isRecordNotEditable(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.INC_AMOUNT,0)
        _validate.verify_isRecordNotEditable(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.INC_VAT_AMOUNT,0)
        _common.minimizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Verify transaction record values are equal to login values ",function(){
       
        _common.openTab(app.TabBar.CONTRACT).then(()=>{
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.FooterTab.TRANSACTIONS,0)
        _common.set_columnAtTop([CONTAINER_COLUMNS_TRANSACTION.companyfk,CONTAINER_COLUMNS_TRANSACTION.CompanyFkCompanyName], cnt.uuid.TRANSACTIONS_IN_CONTRACT)
    
        })
        _common.maximizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.select_rowInContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.COMPANY_FK,CONTAINERS_PROJECT.COMPANY)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.COMPANY_FK_COMPANY_NAME,CONTAINERS_PROJECT.COMPANY_NAME)
        _common.minimizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.waitForLoaderToDisappear()
    })
    
    it("TC - Verify transaction record values are equal to contract ",function(){
       
        _common.openTab(app.TabBar.CONTRACT).then(()=>{
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.FooterTab.TRANSACTIONS,0)
        _common.clear_subContainerFilter(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.set_columnAtTop([CONTAINER_COLUMNS_TRANSACTION.code,CONTAINER_COLUMNS_TRANSACTION.vatgroupfk,CONTAINER_COLUMNS_TRANSACTION.businesspartnerfk,
            CONTAINER_COLUMNS_TRANSACTION.subsidiaryfk,CONTAINER_COLUMNS_TRANSACTION.contactfk,CONTAINER_COLUMNS_TRANSACTION.dateeffective,
            CONTAINER_COLUMNS_TRANSACTION.dateordered,CONTAINER_COLUMNS_TRANSACTION.datedelivery,CONTAINER_COLUMNS_TRANSACTION.paymenttermfifk,
            CONTAINER_COLUMNS_TRANSACTION.currency], cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        })
        _common.maximizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.select_rowInContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CODE,Cypress.env("CONTRACT_CODE"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.BUSINESS_PARTNER_FK,Cypress.env("CONTRACT_BP"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.SUBSIDIARY_FK,Cypress.env("CONTRACT_BRANCH"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.VAT_GROUP_FK,Cypress.env("CONTRACT_VAT_GRP"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTACT_FK,Cypress.env("CONTRACT_CONTACT"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.DATE_EFFECTIVE,Cypress.env("CONTRACT_DATE_E"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.DATE_DELIVERY,Cypress.env("CONTRACT_DATE_D"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.DATE_ORDERED,Cypress.env("CONTRACT_DATE_O"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CURRENCY,Cypress.env("CONTRACT_CURRENCY"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.PAYMENT_TERM_FI_FK,Cypress.env("CONTRACT_PT"))
        _common.minimizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.waitForLoaderToDisappear()
    })
   
    it("TC - Verify transaction record values are equal to item container ",function(){
       
        _common.openTab(app.TabBar.CONTRACT).then(()=>{
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.FooterTab.TRANSACTIONS,0)
        _common.clear_subContainerFilter(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.set_columnAtTop([CONTAINER_COLUMNS_TRANSACTION.taxcodefk,CONTAINER_COLUMNS_TRANSACTION.controllingunitfk], cnt.uuid.TRANSACTIONS_IN_CONTRACT)
    
        })
        _common.maximizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.select_rowInContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.TAX_CODE_FK,Cypress.env("ITEM_TAX_CODE"))
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_FK,Cypress.env("C_UNIT"))
        _common.minimizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Verify transaction record values are equal to totals container ",function(){
       
        _common.openTab(app.TabBar.CONTRACT).then(()=>{
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.FooterTab.TRANSACTIONS,0)
            _common.clear_subContainerFilter(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
            _common.set_columnAtTop([CONTAINER_COLUMNS_TRANSACTION.amount,CONTAINER_COLUMNS_TRANSACTION.vatamount,CONTAINER_COLUMNS_TRANSACTION.incamount,
                CONTAINER_COLUMNS_TRANSACTION.incvatamount,CONTAINER_COLUMNS_TRANSACTION.amountoc,CONTAINER_COLUMNS_TRANSACTION.vatamountoc,
                CONTAINER_COLUMNS_TRANSACTION.incamountoc,CONTAINER_COLUMNS_TRANSACTION.incvatamountoc], cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        })

        _common.maximizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.select_rowInContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.assert_forValues_with_singleDigitAfterDecimal(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.AMOUNT_SMALL,Cypress.env("TOTAL_VALUE_NET"))
        _common.assert_forValues_with_singleDigitAfterDecimal(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.VAT_AMOUNT,Cypress.env("TOTAL_VALUE_TAX"))
        _common.assert_forValues_with_singleDigitAfterDecimal(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.INC_AMOUNT,Cypress.env("TOTAL_VALUE_NET"))
        _common.assert_forValues_with_singleDigitAfterDecimal(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.INC_VAT_AMOUNT,Cypress.env("TOTAL_VALUE_TAX"))
        _common.assert_forValues_with_singleDigitAfterDecimal(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.AMOUNT_OC,Cypress.env("TOTAL_VALUE_NET"))
        _common.assert_forValues_with_singleDigitAfterDecimal(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.VAT_AMOUNT_OC,Cypress.env("TOTAL_VALUE_TAX"))
        _common.assert_forValues_with_singleDigitAfterDecimal(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.INC_AMOUNT_OC,Cypress.env("TOTAL_VALUE_NET"))
        _common.assert_forValues_with_singleDigitAfterDecimal(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.INC_VAT_AMOUNT_OC,Cypress.env("TOTAL_VALUE_TAX"))

        _common.minimizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.waitForLoaderToDisappear()
    })
    it("TC - Verify transaction record values are equal to controlling unit",function(){
       
        _common.openTab(app.TabBar.CONTRACT).then(()=>{
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.FooterTab.TRANSACTIONS,0)
            _common.clear_subContainerFilter(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
            _common.set_columnAtTop([CONTAINER_COLUMNS_TRANSACTION.controllingunitassign01,CONTAINER_COLUMNS_TRANSACTION.controllingunitassign02,CONTAINER_COLUMNS_TRANSACTION.controllingunitassign03,
                CONTAINER_COLUMNS_TRANSACTION.controllingunitassign04,CONTAINER_COLUMNS_TRANSACTION.controllingunitassign05,CONTAINER_COLUMNS_TRANSACTION.controllingunitassign06,
                CONTAINER_COLUMNS_TRANSACTION.controllingunitassign07,CONTAINER_COLUMNS_TRANSACTION.controllingunitassign08,CONTAINER_COLUMNS_TRANSACTION.controllingunitassign09,
                CONTAINER_COLUMNS_TRANSACTION.controllingunitassign10], cnt.uuid.TRANSACTIONS_IN_CONTRACT)
    
        })
        _common.maximizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.select_rowInContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_01,PROJECT_NO)
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_02,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[0])
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_03,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[1])
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_04,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[2])

        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_05,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[3])
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_06,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[4])
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_07,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[5])
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_08,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[6])
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_09,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[7])
        _common.assert_cellDataByContent_inContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT,app.GridCells.CONTROLLING_UNIT_ASSIGN_10,CONTAINERS_CONTROLLING_UNIT.ASSIGNMNET[8])
      
        _common.minimizeContainer(cnt.uuid.TRANSACTIONS_IN_CONTRACT)
        _common.waitForLoaderToDisappear()
    })
 })
