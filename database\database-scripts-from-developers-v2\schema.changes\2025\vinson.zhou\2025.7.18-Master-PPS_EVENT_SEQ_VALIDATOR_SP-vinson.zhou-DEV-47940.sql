-------------------------------------------------------   
-- <PERSON><PERSON> Ticket (REQUIRED): DEV-47940
-- Script Type (REQUIRED): Required Schema Change  
-- Reason (REQUIRED): make it compitable use case of PU creation
-- Install On (OPTIONAL): Trunk, 25.2.1  
-------------------------------------------------------  
  
CREATE or alter PROCEDURE [dbo].[PPS_EVENT_SEQ_VALIDATOR_SP]  
 @PUId INT,    
 @NewMaterialId INT = NULL,    
 @NewMaterialGroupId INT,    
 @NewSiteId INT,  
 @IsCompatible BIT OUTPUT
AS    
BEGIN    
    SET NOCOUNT ON;    
  
    DECLARE   
        @puEventSeq NVARCHAR(200),    
        @compatibleEventSeqId INT,  
        @SiteGroupId INT;  
  
    -- Step 0: Get PU event sequence
    SELECT TOP 1  
        @puEventSeq = (  
            SELECT ',' + CAST(e.PPS_EVENTTYPE_FK AS NVARCHAR)  
            FROM pps_item2event i2e_inner  
            JOIN pps_event e ON e.id = i2e_inner.pps_event_fk  
            WHERE i2e_inner.pps_item_fk = i2e.pps_item_fk  
            ORDER BY i2e_inner.SEQUENCEORDER  
            FOR XML PATH('')  
        )  
    FROM pps_item2event i2e  
    WHERE i2e.pps_item_fk = @PUId;

	 -- Step 1: set @SiteGroupId
	 SELECT top 1 @SiteGroupId = PPS_MAT_SITE_GRP_FK  
        FROM pps_material  
        WHERE MDC_MATERIAL_FK = @NewMaterialId;  
  
    ------------------------------------  
    -- Step 2: Match 3 use cases (material, siteGroup, materialGroup)
    ------------------------------------  
    ;WITH eventSequences AS (  
        SELECT c.ID, t.PPS_EVENT_TYPE_FK, t.SEQUENCEORDER
        FROM PPS_EVENT_SEQ_CONFIG c  
        LEFT JOIN PPS_EVENT_TEMPLATE t ON t.PPS_EVENT_SEQ_CONFIG_FK = c.ID  
        WHERE c.ISLIVE=1
		    AND c.MDC_MATERIAL_GROUP_FK = @NewMaterialGroupId  -- material group must be equal
          AND (c.BAS_SITE_FK = @NewSiteId or @NewSiteId is null) -- in case of PU creation, site Ids should be return, so @NewSiteId is null
			 AND (
				c.MDC_MATERIAL_FK = @NewMaterialId	 -- material match
				OR (c.MDC_MATERIAL_FK IS NULL AND @SiteGroupId is not null and c.PPS_MAT_SITE_GRP_FK = @SiteGroupId) -- site group match
				OR (c.MDC_MATERIAL_FK IS NULL AND @SiteGroupId is null and c.PPS_MAT_SITE_GRP_FK IS NULL) -- material group match: material+siteGroup are null in seq_cfg, @NewMaterialId not link to siteGroup
			 )
  
        UNION ALL  
  
        SELECT c.ID, tOfp.PPS_EVENT_TYPE_FK, tOfp.SEQUENCEORDER 
        FROM PPS_EVENT_SEQ_CONFIG c  -- child EVENT_SEQ_CONFIG
        CROSS APPLY (					-- join EVENT_SEQ_CONFIG from parent, excluding same SEQUENCEORDER
            SELECT * FROM PPS_EVENT_TEMPLATE tOfp  
            WHERE tOfp.PPS_EVENT_SEQ_CONFIG_FK = c.PPS_EVENT_SEQ_CONFIG_FK
              AND NOT EXISTS (  
                  SELECT 1  
                  FROM PPS_EVENT_TEMPLATE tOfc  
                  WHERE tOfc.PPS_EVENT_SEQ_CONFIG_FK = c.ID  
                    AND tOfp.SEQUENCEORDER = tOfc.SEQUENCEORDER  
              )  
        ) tOfp  
        WHERE c.ISLIVE=1
		    AND c.PPS_EVENT_SEQ_CONFIG_FK IS NOT NULL
			 AND c.MDC_MATERIAL_GROUP_FK = @NewMaterialGroupId  -- material group must be equal
			 AND (c.BAS_SITE_FK = @NewSiteId or @NewSiteId is null) -- in case of PU creation, site Ids should be return, so @NewSiteId is null
			 AND (
				c.MDC_MATERIAL_FK = @NewMaterialId	 -- material match
				OR (c.MDC_MATERIAL_FK IS NULL AND @SiteGroupId is not null and c.PPS_MAT_SITE_GRP_FK = @SiteGroupId) -- site group match
				OR (c.MDC_MATERIAL_FK IS NULL AND @SiteGroupId is null and c.PPS_MAT_SITE_GRP_FK IS NULL) -- material group match: material+siteGroup are null in seq_cfg, @NewMaterialId not link to siteGroup
			 )
    )
	 SELECT t.ID,	-- store result into #tmpSuitedSeq
				EventSeq=(  
						SELECT ',' + CONVERT(NVARCHAR, t2.PPS_EVENT_TYPE_FK)  
						FROM eventSequences t2  
						WHERE t2.ID = t.ID  
						ORDER BY t2.SEQUENCEORDER  
						FOR XML PATH('')  
				  )
		  into #tmpSuitedSeq
        FROM eventSequences t  
        GROUP BY t.ID  

	 ------------------------------------  
    -- Step 3: Check compatible by event sequence
    ------------------------------------  
    SELECT TOP 1 @compatibleEventSeqId = r.ID  
    FROM #tmpSuitedSeq r  
    WHERE r.EventSeq = @puEventSeq OR (r.EventSeq = '' AND @puEventSeq IS NULL);

    IF @compatibleEventSeqId IS NOT NULL  
        SET @IsCompatible = 1  
    ELSE  
        SET @IsCompatible = 0


    ------------------------------------  
    -- Output possible site ids
    ------------------------------------
	 select c.ID, c.DESCRIPTION, c.BAS_SITE_FK
	 from PPS_EVENT_SEQ_CONFIG c
	 join #tmpSuitedSeq s on s.ID=c.ID

    ------------------------------------  
    -- drop temp tables  
    ------------------------------------  
	 drop table if exists #tmpSuitedSeq
END  
  
  
          