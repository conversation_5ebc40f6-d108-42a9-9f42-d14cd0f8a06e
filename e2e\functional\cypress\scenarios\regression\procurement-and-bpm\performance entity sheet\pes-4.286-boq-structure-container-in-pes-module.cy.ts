import apiConstantData from "cypress/constantData/apiConstantData";
import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _procurementContractPage, _boqPage, _validate, _procurementPage, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";


const OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const BOQ_OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const BOQ_STRCU_DESC2 = _common.generateRandomString(4);
const CONTRACT_DESC = _common.generateRandomString(4);
const BOQ_STRCU_DESC4 = _common.generateRandomString(4);
const BOQ_STRCU_DESC5 = _common.generateRandomString(4);
const BOQ_STRCU_DESC6 = _common.generateRandomString(4);

let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_PROCUREMENT_BOQ, CONTAINER_COLUMNS_BOQ_STRUCTURE;
let PROCUREMENT_CONTRACT_PARAMETER: DataCells, BOQ_STRUCTURE_PARAMETERS: DataCells, CONTROLLING_UNIT_PARAMETERS: DataCells
let CONTAINERS_DATA_RECORDS, CONTAINERS_PROCUREMENT_STRUCTURE, CONTAINERS_CONFIGURATION, CONTAINERS_BOQ_STRUCTURE;

describe('PCM- 4.286 | Boq structure container in pes module', () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture('pcm/pes-4.286-boq-structure-container-in-pes-module.json').then((data) => {
            this.data = data;
            CONTAINERS_DATA_RECORDS = this.data.CONTAINERS.DATA_RECORDS
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            CONTAINERS_CONFIGURATION = this.data.CONTAINERS.CONFIGURATION
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: commonLocators.CommonKeys.SERVICE,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONFIGURATION.BUSINESS_PARTNER
            }
            CONTAINERS_PROCUREMENT_STRUCTURE = this.data.CONTAINERS.PROCUREMENT_STRUCTURE
            CONTAINER_COLUMNS_PROCUREMENT_BOQ = this.data.CONTAINER_COLUMNS.PROCUREMENT_BOQ;
            CONTAINER_COLUMNS_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.BOQ_STRUCTURE
            CONTAINERS_BOQ_STRUCTURE = this.data.CONTAINERS.BOQ_STRUCTURE
            BOQ_STRUCTURE_PARAMETERS = {
                [commonLocators.CommonLabels.TYPE]: commonLocators.CommonLabels.NEW_RECORD,
                [app.GridCells.BRIEF_INFO_SMALL]: BOQ_OUTLINE_SPECIFICATION,
                [app.GridCells.BAS_UOM_FK]: CONTAINERS_BOQ_STRUCTURE.UOM,
                [app.GridCells.PRICE_SMALL]: CONTAINERS_BOQ_STRUCTURE.CORRECTION,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_BOQ_STRUCTURE.QUANTITY
            }
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            cy.WaitUntilLoaderComplete_Trial();
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT);
                cy.WaitUntilLoaderComplete_Trial();
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
            
        });
        
    });

    it('TC - API: Create controlling units', function () {

        CONTROLLING_UNIT_PARAMETERS = {
            [app.GridCells.UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS]
        }
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 3, CONTROLLING_UNIT_PARAMETERS)
    })


    it('TC - Create record in Sales Tax Group from customizing', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ENTITY_TYPES, app.FooterTab.DATA_TYPES, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.ENTITY_TYPES)
        _common.clear_subContainerFilter(cnt.uuid.ENTITY_TYPES)
        _common.search_inSubContainer(cnt.uuid.ENTITY_TYPES, commonLocators.CommonKeys.PES_STATUS)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.ENTITY_TYPES, app.GridCells.NAME, commonLocators.CommonKeys.PES_STATUS)
        cy.wait(1000)//required wait
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORD, 0);
        });
        _common.clickOn_cellHasUniqueValue(cnt.uuid.DATA_RECORDS, app.GridCells.DESCRIPTION_INFO, CommonLocators.CommonKeys.NEW)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_PROTECTED, commonLocators.CommonKeys.UNCHECK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    });

    it('TC - Create Contract for respective project and with structure', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT);
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT);
        });
        
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_REQ_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONFIGURATION.CLERK);
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONFIGURATION.CLERK);
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_CNT_CODE_1'));
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.STRUCTURE_CODE, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PROCUREMENT_STRUCTURE.PROCUREMENT_STRUCTURE);
        cy.SAVE();
        _common.waitForLoaderToDisappear
        _common.clickOn_modalFooterButton(btn.ButtonText.YES)
        cy.SAVE();
        _common.waitForLoaderToDisappear()
         _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
       
    });

    it('TC - Create Procurement BOQ for the respective contract', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS);
        _common.waitForLoaderToDisappear()
        _boqPage.enterRecord_ToCreate_procurementBoQs(CommonLocators.CommonKeys.SERVICE, OUTLINE_SPECIFICATION, commonLocators.CommonLabels.CREATE_NEW_BOQ);
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.GridCells.PACKAGE_CODE, "Package_Code");
        cy.SAVE()
    });

    it('TC - Verify create BOQ structure', function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINER_COLUMNS_BOQ_STRUCTURE);
            _common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.briefinfo, CONTAINER_COLUMNS_BOQ_STRUCTURE.quantity, CONTAINER_COLUMNS_BOQ_STRUCTURE.basuomfk, CONTAINER_COLUMNS_BOQ_STRUCTURE.price, CONTAINER_COLUMNS_BOQ_STRUCTURE.boqlinetypefk], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)

        });
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.waitForLoaderToDisappear()
        //record_1
        _boqPage.enterRecord_toCreateBoQStructure(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRUCTURE_PARAMETERS);
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.MDC_TAX_CODE_FK_SMALL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.TAX_CODE);

        cy.SAVE()
        _common.waitForLoaderToDisappear()
        //record_2
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE);
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC2);
        _common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.QUANTITY);
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UNIT_RATE);
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BAS_UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UOM)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.MDC_TAX_CODE_FK_SMALL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.TAX_CODE);

        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)

    });

    it('TC - Create pes from contract wizard option', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)


        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.PART_DELIVERED)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES);
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_PES)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0)
            _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        })
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.setDefaultView(app.TabBar.PERFORMANCEENTRYSHEET)
            _common.set_containerLayoutUnderEditView(commonLocators.CommonLabels.LAYOUT_6)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQs, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PES_ITEMS)
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
        _common.waitForLoaderToDisappear()
    });

    it('TC - Verify boq structure in pes module and verify the root item cannot be deleted', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
            _common.setup_gridLayout(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINER_COLUMNS_BOQ_STRUCTURE);
        })
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQs, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PES_ITEMS)

        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE)
        })

        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.clickOn_expandCollapseButton(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_SELECTED)

        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, commonLocators.CommonKeys.ROOT)
        _common.waitForLoaderToDisappear()
        _validate.verify_ToolbarButtonsDisabledEnabled(cnt.uuid.PES_BOQS_STRUCTURE, btn.IconButtons.ICO_REC_DELETE, commonLocators.CommonKeys.DISABLED)
    });

    it('TC - Verify each button is working', function () {
     _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();

        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
        _common.clickOn_toolbarButton(cnt.uuid.PES_BOQS_STRUCTURE, btn.ToolBar.ICO_REC_NEW)
        _common.select_rowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.enterRecord_inNewRow(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC4)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
        _common.clickOn_toolbarButton(cnt.uuid.PES_BOQS_STRUCTURE, btn.ToolBar.ICO_FLD_INS_BELOW)
        _common.select_rowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.enterRecord_inNewRow(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC5)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
        _common.clickOn_toolbarButton(cnt.uuid.PES_BOQS_STRUCTURE, btn.IconButtons.ICO_SUB_FLD_NEW)
        _common.select_rowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.enterRecord_inNewRow(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC6)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_STRCU_DESC4)
        _validate.verify_isRecordPresent(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_STRCU_DESC4)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_STRCU_DESC5)
        _validate.verify_isRecordPresent(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_STRCU_DESC5)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_STRCU_DESC6)
        _validate.verify_isRecordPresent(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_STRCU_DESC6)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_STRCU_DESC4)
        _common.waitForLoaderToDisappear()
        _common.delete_recordFromContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _validate.verify_recordNotPresentInContainer(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_STRCU_DESC4)
        _common.waitForLoaderToDisappear()
        _validate.verify_recordNotPresentInContainer(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_STRCU_DESC4)
        _common.waitForLoaderToDisappear()

    });

    it('TC - Modify quantity or unit rate or correction & assert final price as a multyplication of quantity or unit rate or correction', function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
            _common.setup_gridLayout(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINER_COLUMNS_BOQ_STRUCTURE);
            _common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.quantity,  CONTAINER_COLUMNS_BOQ_STRUCTURE.finalprice, CONTAINER_COLUMNS_BOQ_STRUCTURE.price], cnt.uuid.PES_BOQS_STRUCTURE)
        });
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_OUTLINE_SPECIFICATION)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_OUTLINE_SPECIFICATION)
        _common.edit_containerCell(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.QUANTITY_1);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINERS_BOQ_STRUCTURE.QUANTITY_1, CONTAINERS_BOQ_STRUCTURE.CORRECTION, app.GridCells.FINAL_PRICE_SMALL)
        _common.saveCellDataToEnv(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.FINAL_PRICE_SMALL, "FINAL_PRICE")
        _common.saveCellDataToEnv(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.MDC_TAX_CODE_FK_SMALL, "TAX_CODE")
        _common.saveCellDataToEnv(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.REM_QUANTITY, "REMAINING_QUANTITY")
        _common.saveCellDataToEnv(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.ORD_QUANTITY_SMALL, "CONTRACTED_QUANTITY")

        _common.minimizeContainer(cnt.uuid.PES_BOQS_STRUCTURE)
    });

    it('TC - Check lumpsum checkbox & assert final price=lumpsum price', function () {
        _validate.verify_SubtractionOfTwoFieldsWith_ThirdFieldInActiveRow(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.QUANTITY_SMALL, app.GridCells.ORD_QUANTITY_SMALL, Cypress.env("REMAINING_QUANTITY"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
            _common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.lumpsumprice, CONTAINER_COLUMNS_BOQ_STRUCTURE.finalprice, CONTAINER_COLUMNS_BOQ_STRUCTURE.islumpsum], cnt.uuid.PES_BOQS_STRUCTURE)
        });
        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
        _common.set_cellCheckboxValue(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.IS_LUMP_SUM, commonLocators.CommonKeys.CHECK)
        _common.edit_containerCell(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.LUMP_SUM_PRICE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.LUMP_SUM_PRICE);
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
        _common.assert_activeRow_cellDataByContent_inContainer(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.FINAL_PRICE_SMALL, CONTAINERS_BOQ_STRUCTURE.LUMP_SUM_PRICE)
    });


});