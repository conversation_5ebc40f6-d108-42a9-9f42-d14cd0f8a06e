import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _package, _procurementContractPage, _procurementPage, _projectPage, _schedulePage, _validate, _wizardCreateRequest } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const ALLURE = Cypress.Allure.reporter.getInterface();
const COMMENT_TEXT = _common.generateRandomString(4);
const COMMENT_TEXT_1 = _common.generateRandomString(4);
const COMMENT_TEXT_2 = _common.generateRandomString(4);
const COMMENT_TEXT_3 = _common.generateRandomString(4);

let PROCUREMENT_CONTRACT_PARAMETER: DataCells, CONTRACT_CLERK_PARAMETER: DataCells;

let CONTAINERS_CONTRACT, CONTAINERS_CONTRACT_CLERK, CONTAINERS_DATA_RECORDS;

let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_CONTRACT_CLERK, CONTAINER_COLUMNS_DATA_TYPES, CONTAINER_COLUMNS_DATA_RECORDS;

ALLURE.epic("PROCUREMENT AND BPM")
ALLURE.feature("Contract")
ALLURE.story("PCM- 4.134 | Clerk role container in Contract module")

describe("PCM- 4.134 | Clerk role container in Contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/pcm-4.134-clerk-role-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }
            CONTAINERS_CONTRACT_CLERK = this.data.CONTAINERS.CONTRACT_CLERK
            CONTAINER_COLUMNS_CONTRACT_CLERK = this.data.CONTAINER_COLUMNS.CONTRACT_CLERK
            CONTAINER_COLUMNS_DATA_TYPES = this.data.CONTAINER_COLUMNS.DATA_TYPES
            CONTAINERS_DATA_RECORDS = this.data.CONTAINERS.DATA_RECORDS
            CONTAINER_COLUMNS_DATA_RECORDS = this.data.CONTAINER_COLUMNS.DATA_RECORDS
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.projectfk, CONTAINER_COLUMNS_CONTRACT.ProjectFkProjectName, CONTAINER_COLUMNS_CONTRACT.code, CONTAINER_COLUMNS_CONTRACT.businesspartnerfk], cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create contract clerk & verify each field is editable", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CLERK, app.FooterTab.CONTRACT_CLERK, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_CLERK, CONTAINER_COLUMNS_CONTRACT_CLERK)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CLERK)
        _common.create_newRecord(cnt.uuid.CONTRACT_CLERK)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CLERK, app.GridCells.CLERK_ROLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CLERK.CLERK_ROLE[0])
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CLERK, app.GridCells.CLERK_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CLERK.CLERK)
        _common.edit_containerCell(cnt.uuid.CONTRACT_CLERK, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, COMMENT_TEXT)
        _common.edit_containerCell(cnt.uuid.CONTRACT_CLERK, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.edit_containerCell(cnt.uuid.CONTRACT_CLERK, app.GridCells.VALID_TO, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.INCREMENTED, 1))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Delete contract clerk & verify record is not present", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CLERK, app.FooterTab.CONTRACT_CLERK, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_CLERK, CONTAINER_COLUMNS_CONTRACT_CLERK)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CLERK)
        _common.create_newRecord(cnt.uuid.CONTRACT_CLERK)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CLERK, app.GridCells.CLERK_ROLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CLERK.CLERK_ROLE[1])
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CLERK, app.GridCells.CLERK_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CLERK.CLERK)
        _common.edit_containerCell(cnt.uuid.CONTRACT_CLERK, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, COMMENT_TEXT_1)
        _common.edit_containerCell(cnt.uuid.CONTRACT_CLERK, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.edit_containerCell(cnt.uuid.CONTRACT_CLERK, app.GridCells.VALID_TO, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.INCREMENTED, 1))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_CLERK, COMMENT_TEXT_1)
        _common.select_rowHasValue(cnt.uuid.CONTRACT_CLERK, COMMENT_TEXT_1)
        _common.clickOn_toolbarButton(cnt.uuid.CONTRACT_CLERK, btn.IconButtons.ICO_REC_DELETE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env('CONTRACT_CODE'))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CLERK, app.FooterTab.CONTRACT_CLERK, 2);
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CLERK)
        });
        _common.waitForLoaderToDisappear()
        // _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, COMMENT_TEXT_1)
        _validate.verify_recordNotPresentInContainer(cnt.uuid.CONTRACT_CLERK, COMMENT_TEXT_1)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CLERK)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Verify the clerk role filter with isforcontract=true", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES, 0);
            _common.setup_gridLayout(cnt.uuid.DATA_TYPES, CONTAINER_COLUMNS_DATA_TYPES)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, commonLocators.CommonLabels.ROLE)
        _common.select_rowHasValue(cnt.uuid.DATA_TYPES, commonLocators.CommonLabels.ROLE)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORDS, 1);
            _common.setup_gridLayout(cnt.uuid.DATA_RECORDS, CONTAINER_COLUMNS_DATA_RECORDS)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
        _common.search_inSubContainer(cnt.uuid.DATA_RECORDS, CONTAINERS_DATA_RECORDS.DATA_RECORD_DESC[1])
        _common.select_rowHasValue(cnt.uuid.DATA_RECORDS, CONTAINERS_DATA_RECORDS.DATA_RECORD_DESC[1])
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_FOR_CONTRACT, commonLocators.CommonKeys.UNCHECK)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CLERK, app.FooterTab.CONTRACT_CLERK, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_CLERK, CONTAINER_COLUMNS_CONTRACT_CLERK)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CLERK)
        _common.create_newRecord(cnt.uuid.CONTRACT_CLERK)
        _validate.validateData_fromCellPopUpIsVisibleOrNot(cnt.uuid.CONTRACT_CLERK, app.GridCells.CLERK_ROLE_FK, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.NOT_SPACE_VISIBLE, CONTAINERS_CONTRACT_CLERK.CLERK_ROLE[3])
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(2000) // Required wait to load the data
        _common.clickOn_modalFooterButton(btn.ButtonText.CANCEL)
        _common.select_activeRowInContainer(cnt.uuid.CONTRACT_CLERK)
        _common.clickOn_toolbarButton(cnt.uuid.CONTRACT_CLERK, btn.IconButtons.ICO_REC_DELETE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env('CONTRACT_CODE'))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CLERK, app.FooterTab.CONTRACT_CLERK, 2);
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CLERK)
        });
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_CLERK, CONTAINERS_CONTRACT_CLERK.CLERK_ROLE[3])
        _common.waitForLoaderToDisappear()
        _validate.verify_recordNotPresentInContainer(cnt.uuid.CONTRACT_CLERK, CONTAINERS_CONTRACT_CLERK.CLERK_ROLE[3])
        _common.waitForLoaderToDisappear()
    });

    it("TC - Verify if clerk role's isunique=true, it only allow one record with this role for select contract", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES, 0);
            _common.setup_gridLayout(cnt.uuid.DATA_TYPES, CONTAINER_COLUMNS_DATA_TYPES)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, commonLocators.CommonLabels.ROLE)
        _common.select_rowHasValue(cnt.uuid.DATA_TYPES, commonLocators.CommonLabels.ROLE)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORDS, 1);
            _common.setup_gridLayout(cnt.uuid.DATA_RECORDS, CONTAINER_COLUMNS_DATA_RECORDS)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
        _common.search_inSubContainer(cnt.uuid.DATA_RECORDS, CONTAINERS_DATA_RECORDS.DATA_RECORD_DESC[0])
        _common.select_rowHasValue(cnt.uuid.DATA_RECORDS, CONTAINERS_DATA_RECORDS.DATA_RECORD_DESC[0])
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_UNIQUE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CLERK, app.FooterTab.CONTRACT_CLERK, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_CLERK, CONTAINER_COLUMNS_CONTRACT_CLERK)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CLERK)
        _common.create_newRecord(cnt.uuid.CONTRACT_CLERK)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CLERK, app.GridCells.CLERK_ROLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CLERK.CLERK_ROLE[2])
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CLERK, app.GridCells.CLERK_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CLERK.CLERK)
        _common.edit_containerCell(cnt.uuid.CONTRACT_CLERK, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, COMMENT_TEXT_2)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CLERK)
        _common.create_newRecord(cnt.uuid.CONTRACT_CLERK)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CLERK, app.GridCells.CLERK_ROLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CLERK.CLERK_ROLE[2])
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CLERK, app.GridCells.CLERK_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_CLERK.CLERK)
        _common.edit_containerCell(cnt.uuid.CONTRACT_CLERK, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, COMMENT_TEXT_3)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _validate.validate_Text_message_In_PopUp('0x3ffffff - Only one clerk with role "Project Director" can exist!')
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        cy.SAVE()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        cy.SAVE()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CLERK, app.FooterTab.CONTRACT_CLERK, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_CLERK, CONTAINER_COLUMNS_CONTRACT_CLERK)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CLERK)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_CLERK, COMMENT_TEXT_2)
        _common.select_rowHasValue(cnt.uuid.CONTRACT_CLERK, COMMENT_TEXT_2)
        _common.waitForLoaderToDisappear()
    });
});