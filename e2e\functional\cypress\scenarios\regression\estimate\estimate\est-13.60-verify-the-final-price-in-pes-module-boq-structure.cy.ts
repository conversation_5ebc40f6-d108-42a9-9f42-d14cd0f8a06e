import apiConstantData from "cypress/constantData/apiConstantData";
import { commonLocators, sidebar, app, cnt, tile, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _boqPage, _common, _commonAPI, _controllingUnit, _estimatePage, _procurementContractPage, _procurementPage, _projectPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";


const OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const BOQ_OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const COMMENT = _common.generateRandomString(4);
const LOCATION_CODE = _common.generateRandomString(4);
const LOCATION_DESC = _common.generateRandomString(4);
const PES_DESCRIPTION = "PES-2-" + _common.generateRandomString(4);
const CONTRACT_SPLIT_QUANTITY_COMMENT_1 = _common.generateRandomString(4);
const CONTRACT_SPLIT_QUANTITY_COMMENT_2 = _common.generateRandomString(4);
const PES_DESC = "PES-1-" + _common.generateRandomString(4);

let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let API_CONTROLLING_UNIT_PARAMETERS, BOQ_STRUCTURE_PARAMETERS: DataCells
let CONTAINERS_CONTRACT, CONTAINER_COLUMNS_LOCATION;

let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINER_COLUMNS_CONTRACT_SPLIT_QUANTITIES;
let CONTAINERS_PERFORMANCE_ENTRY_SHEET, CONTAINER_COLUMNS_HEADERS
describe("est- 13.60 | Verify final price in pes module boq structure", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture("estimate/est-13.60-verify-the-final-price-in-pes-module-boq-structure.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            CONTAINER_COLUMNS_LOCATION = this.data.CONTAINER_COLUMNS.LOCATION
            CONTAINER_COLUMNS_PROCUREMENTCONTRACT_BOQSTRUCTURE = this.data.CONTAINER_COLUMNS.PROCUREMENT_BOQ_STRUCTURE
            CONTAINER_COLUMNS_CONTRACT_SPLIT_QUANTITIES = this.data.CONTAINER_COLUMNS.CONTRACT_SPLIT_QUANTITIES

            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }
            BOQ_STRUCTURE_PARAMETERS = {
                [commonLocators.CommonLabels.TYPE]: commonLocators.CommonLabels.NEW_RECORD,
                [app.GridCells.BRIEF_INFO_SMALL]: BOQ_OUTLINE_SPECIFICATION,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_CONTRACT.BOQ_QUANTITY,
            }
            CONTAINER_COLUMNS_HEADERS = this.data.CONTAINER_COLUMNS.HEADERS
            CONTAINERS_PERFORMANCE_ENTRY_SHEET = this.data.CONTAINERS.PERFORMANCE_ENTRY_SHEET
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear();
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT);
                _common.waitForLoaderToDisappear();
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });
    });
    after(() => {
        cy.LOGOUT();
    });
    it('TC - API: Create project and location', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_LOCATION, app.FooterTab.LOCATIONS, 1);
            _common.setup_gridLayout(cnt.uuid.PROJECT_LOCATION, CONTAINER_COLUMNS_LOCATION);
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROJECT_LOCATION, app.FooterTab.LOCATIONS, 1);
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

        _common.clear_subContainerFilter(cnt.uuid.PROJECT_LOCATION)
        _common.create_newRecord(cnt.uuid.PROJECT_LOCATION);
        _estimatePage.enterRecord_toCreateLocation(LOCATION_CODE, LOCATION_DESC);
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_LOCATION)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    });

    it("TC - API: Create controlling unit", function () {
        API_CONTROLLING_UNIT_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTRACT.QUANTITY1, CONTAINERS_CONTRACT.QUANTITY1],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS]
        }
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, API_CONTROLLING_UNIT_PARAMETERS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTROLLING_UNITS)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT, app.FooterTab.CONTROLLING_UNITS, 1);
        }).then(() => {
            cy.log(Cypress.env(`API_CNT_CODE_0`))
            cy.log(Cypress.env(`API_CNT_ID_0`))
        })
        _common.clickOn_expandCollapseButton(cnt.uuid.CONTROLLING_UNIT, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowInContainer(cnt.uuid.CONTROLLING_UNIT)
        _common.saveCellDataToEnv(cnt.uuid.CONTROLLING_UNIT, app.GridCells.CODE, "CU_CODE")


    })

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_3)
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        })
        cy.log(Cypress.env(`API_CNT_CODE_0`))
        cy.log(Cypress.env(`API_CNT_ID_0`))
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal_byClass(PROCUREMENT_CONTRACT_PARAMETER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.STRUCTURE_CODE, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.MATERIAL)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.YES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    });

    it('TC - Create procurement boq for the respective contract', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE")).pinnedItem()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS);
        _boqPage.enterRecord_ToCreate_procurementBoQs(CommonLocators.CommonKeys.MATERIAL, OUTLINE_SPECIFICATION, commonLocators.CommonLabels.CREATE_NEW_BOQ);
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it('TC - Verify create boq structure', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINER_COLUMNS_PROCUREMENTCONTRACT_BOQSTRUCTURE)
            _common.set_columnAtTop([CONTAINER_COLUMNS_PROCUREMENTCONTRACT_BOQSTRUCTURE.quantity, CONTAINER_COLUMNS_PROCUREMENTCONTRACT_BOQSTRUCTURE.briefinfo, CONTAINER_COLUMNS_PROCUREMENTCONTRACT_BOQSTRUCTURE.boqlinetypefk], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)

        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, commonLocators.CommonKeys.ROOT)
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.waitForLoaderToDisappear()
        _boqPage.enterRecord_toCreateBoQStructure(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRUCTURE_PARAMETERS);
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, commonLocators.CommonKeys.ROOT)
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.waitForLoaderToDisappear()

    });
    it('TC - Create new record in split quantities container', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.FooterTab.SPLIT_QUANTITY);
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.CONTRACT_SPLIT_QUANTITIES)
        _common.create_newRecord(cnt.uuid.CONTRACT_SPLIT_QUANTITIES)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.QUANTITY1);
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.PRJ_LOCATION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, LOCATION_CODE)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.PRC_STRUCTURE_FK, CommonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CommonLocators.CommonKeys.MATERIAL)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.MDC_CONTROLLING_UNIT_FK_SMALL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env("CU_CODE"))
        _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.DELIVERY_DATE, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(commonLocators.CommonKeys.CURRENT_SMALL))
        _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, CONTRACT_SPLIT_QUANTITY_COMMENT_1)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACT_SPLIT_QUANTITIES)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.QUANTITY1);
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.PRJ_LOCATION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, LOCATION_CODE)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.PRC_STRUCTURE_FK, CommonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CommonLocators.CommonKeys.MATERIAL)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.MDC_CONTROLLING_UNIT_FK_SMALL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env("CU_CODE"))
        _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.DELIVERY_DATE, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(commonLocators.CommonKeys.CURRENT_SMALL))
        _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_SPLIT_QUANTITIES, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, CONTRACT_SPLIT_QUANTITY_COMMENT_2)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONTRACT_SPLIT_QUANTITIES)
        _common.waitForLoaderToDisappear()

    })
    it("TC - Create new pes record from wizard option", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES);
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)
        _procurementPage.enterRecord_toCreatePesFromWizard_ifDescriptionExists(PES_DESC)
        _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_PES)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
            _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_HEADERS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_HEADERS.conheaderfk], cnt.uuid.HEADERS)
        });
        _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();

        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _common.saveCellDataToEnv(cnt.uuid.HEADERS, app.GridCells.CODE, "HEADERS_CODE")
        cy.log(Cypress.env("HEADERS_CODE"))
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS, 1);
        });
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_SPLIT_QUANTITY, app.FooterTab.SPLIT_QUANTITY, 2);
            _common.setup_gridLayout(cnt.uuid.PES_SPLIT_QUANTITY, CONTAINER_COLUMNS_CONTRACT_SPLIT_QUANTITIES)
        });

    });
    it('TC - Verify sum of split quantities in boq structure', function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE);
            _common.set_columnAtTop([CONTAINER_COLUMNS_PROCUREMENTCONTRACT_BOQSTRUCTURE.quantityadj], cnt.uuid.PES_BOQS_STRUCTURE)

        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_OUTLINE_SPECIFICATION)

        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINERS_CONTRACT.QUANTITY1, CONTAINERS_CONTRACT.QUANTITY1, app.GridCells.QUANTITY_ADJ)
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PES_BOQS_STRUCTURE)

    })

    it('TC - Verify split quantities values', function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_SPLIT_QUANTITY, app.FooterTab.SPLIT_QUANTITY);
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PES_SPLIT_QUANTITY)
        _common.select_rowHasValue(cnt.uuid.PES_SPLIT_QUANTITY, CONTRACT_SPLIT_QUANTITY_COMMENT_1)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.PES_SPLIT_QUANTITY, app.GridCells.QUANTITY_ADJ, CONTAINERS_CONTRACT.QUANTITY1);
        cy.wait(1000)
        _common.select_rowHasValue(cnt.uuid.PES_SPLIT_QUANTITY, CONTRACT_SPLIT_QUANTITY_COMMENT_2)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.PES_SPLIT_QUANTITY, app.GridCells.QUANTITY_ADJ, CONTAINERS_CONTRACT.QUANTITY1);
        _common.minimizeContainer(cnt.uuid.PES_SPLIT_QUANTITY)

    })

    it("TC - Verify split quantites for manually created pes", function () {

        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
        });

        _common.maximizeContainer(cnt.uuid.HEADERS)
        _common.clear_subContainerFilter(cnt.uuid.HEADERS);
        _common.create_newRecord(cnt.uuid.HEADERS);
        _common.select_activeRowInContainer(cnt.uuid.HEADERS)
        _common.edit_containerCell(cnt.uuid.HEADERS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, PES_DESCRIPTION)
        // _common.edit_dropdownCellWithInput(cnt.uuid.HEADERS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PERFORMANCE_ENTRY_SHEET.BUSINESS_PARTNER)
        _common.edit_dropdownCellWithInput(cnt.uuid.HEADERS, app.GridCells.CON_HEADER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env("CONTRACT_CODE"))
        _common.clickOn_activeRowCell(cnt.uuid.HEADERS, app.GridCells.DESCRIPTION)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.HEADERS)
        _common.search_inSubContainer(cnt.uuid.HEADERS, PES_DESCRIPTION)
        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _common.saveCellDataToEnv(cnt.uuid.HEADERS, app.GridCells.CODE, "HEADERS_CODE_2")
        cy.log(Cypress.env("HEADERS_CODE_2"))
        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS, 1);
        });
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE);
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE, BOQ_OUTLINE_SPECIFICATION)

        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINERS_CONTRACT.QUANTITY1, CONTAINERS_CONTRACT.QUANTITY1, app.GridCells.QUANTITY_ADJ)
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_SPLIT_QUANTITY, app.FooterTab.SPLIT_QUANTITY);
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PES_SPLIT_QUANTITY)
        _common.select_rowHasValue(cnt.uuid.PES_SPLIT_QUANTITY, CONTRACT_SPLIT_QUANTITY_COMMENT_1)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.PES_SPLIT_QUANTITY, app.GridCells.QUANTITY_ADJ, CONTAINERS_CONTRACT.QUANTITY1);
        _common.select_rowHasValue(cnt.uuid.PES_SPLIT_QUANTITY, CONTRACT_SPLIT_QUANTITY_COMMENT_2)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.PES_SPLIT_QUANTITY, app.GridCells.QUANTITY_ADJ, CONTAINERS_CONTRACT.QUANTITY1);

    });

});