import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _package, _procurementContractPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const WARRANTY_DESC1 = _common.generateRandomString(4);
const WARRANTY_DESC2 = _common.generateRandomString(4);
const WARRANTY_DESC3 = _common.generateRandomString(4);
const COMMENT_TEXT = _common.generateRandomString(4);


let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let CONTAINERS_CONTRACT,
    CONTAINERS_WARRANTY
let CONTAINER_COLUMNS_CONTRACT,
    CONTAINER_COLUMNS_WARRANTY
let PLANNED_START,
    PLANNED_FINISH

describe("PCM- 4.217 | Warranty container in contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
	before(function () {
		cy.fixture("pcm/con-4.217-warranty-container-in-contract-module.json").then((data) => {
			this.data = data;
			CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
			CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
			CONTAINERS_WARRANTY = this.data.CONTAINERS.WARRANTY
			CONTAINER_COLUMNS_WARRANTY = this.data.CONTAINER_COLUMNS.WARRANTY
			PLANNED_START=_common.getDate(commonLocators.CommonKeys.CURRENT_SMALL)
			PLANNED_FINISH=_common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL,CONTAINERS_WARRANTY.DATE_DAYS[0])
			PROCUREMENT_CONTRACT_PARAMETER = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
			}
		}).then(() => {
			cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
		});
	})
	after(() => {
		cy.LOGOUT();
	});

	it("TC - Create new contract record", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
		});
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		cy.SAVE()
		cy.wait(1000)//wait required to load value
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	});

	it("TC - Verify create new warranty record", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env("CONTRACT_CODE")).pinnedItem();
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CONTRACT_CODE"))
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONTRACT_WARRANTY, app.FooterTab.WARRANTY, 1);
			_common.setup_gridLayout(cnt.uuid.CONTRACT_WARRANTY, CONTAINER_COLUMNS_WARRANTY)
		});
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.CONTRACT_WARRANTY)
		_common.create_newRecord(cnt.uuid.CONTRACT_WARRANTY)
		_common.waitForLoaderToDisappear()
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION,WARRANTY_DESC1)
        _common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_SECURITY_FK,commonLocators.CommonKeys.LIST,CONTAINERS_WARRANTY.WARRANTY_SECURITY)
		_common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_OBLIGATION_FK,commonLocators.CommonKeys.LIST,CONTAINERS_WARRANTY.WARRANTY_OBLIGATION)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.search_inSubContainer(cnt.uuid.CONTRACT_WARRANTY,WARRANTY_DESC1)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.DESCRIPTION,WARRANTY_DESC1)
	});

	it("TC - Verify delete warranty record", function () {
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.CONTRACT_WARRANTY)
		_common.search_inSubContainer(cnt.uuid.CONTRACT_WARRANTY,WARRANTY_DESC1)
        _common.select_rowHasValue(cnt.uuid.CONTRACT_WARRANTY,WARRANTY_DESC1)
		_common.delete_recordFromContainer(cnt.uuid.CONTRACT_WARRANTY)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()	
		_common.waitForLoaderToDisappear()	
		_common.search_inSubContainer(cnt.uuid.CONTRACT_WARRANTY,WARRANTY_DESC1)
		_validate.verify_recordNotPresentInContainer(cnt.uuid.CONTRACT_WARRANTY,WARRANTY_DESC1)
	});

	it("TC - Verify each field can be input and work", function () {
		_common.waitForLoaderToDisappear()
		_common.maximizeContainer(cnt.uuid.CONTRACT_WARRANTY)
		_common.clear_subContainerFilter(cnt.uuid.CONTRACT_WARRANTY)
		_common.create_newRecord(cnt.uuid.CONTRACT_WARRANTY)
		_common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_SECURITY_FK,commonLocators.CommonKeys.LIST,CONTAINERS_WARRANTY.WARRANTY_SECURITY)
		_common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_OBLIGATION_FK,commonLocators.CommonKeys.LIST,CONTAINERS_WARRANTY.WARRANTY_OBLIGATION)
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, WARRANTY_DESC2)
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.HAND_OVER_DATE, app.InputFields.INPUT_GROUP_CONTENT,PLANNED_START)
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.WARRANTY_END_DATE, app.InputFields.INPUT_GROUP_CONTENT,PLANNED_FINISH)
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, COMMENT_TEXT)
		
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_1, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_TEXT[0])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_2, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_TEXT[1])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_3, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_TEXT[2])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_4, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_TEXT[3])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_5, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_TEXT[4])

		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_DATE_1, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_WARRANTY.USER_DATE[0])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_DATE_2, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_WARRANTY.USER_DATE[1])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_DATE_3, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_WARRANTY.USER_DATE[2])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_DATE_4, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_WARRANTY.USER_DATE[3])

		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_1, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_NUMBER[0])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_2, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_NUMBER[1])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_3, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_NUMBER[2])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_4, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_NUMBER[3])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_5, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.USER_NUMBER[4])		
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.search_inSubContainer(cnt.uuid.CONTRACT_WARRANTY,WARRANTY_DESC2)

		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_SECURITY_FK,CONTAINERS_WARRANTY.WARRANTY_SECURITY)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_OBLIGATION_FK,CONTAINERS_WARRANTY.WARRANTY_OBLIGATION)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.DESCRIPTION, WARRANTY_DESC2)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.HAND_OVER_DATE,PLANNED_START)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.WARRANTY_END_DATE, PLANNED_FINISH)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.COMMENT_TEXT,COMMENT_TEXT)

		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_1, CONTAINERS_WARRANTY.USER_TEXT[0])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_2, CONTAINERS_WARRANTY.USER_TEXT[1])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_3, CONTAINERS_WARRANTY.USER_TEXT[2])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_4, CONTAINERS_WARRANTY.USER_TEXT[3])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_TEXT_5, CONTAINERS_WARRANTY.USER_TEXT[4])

		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_DATE_1, CONTAINERS_WARRANTY.USER_DATE[0])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_DATE_2, CONTAINERS_WARRANTY.USER_DATE[1])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_DATE_3, CONTAINERS_WARRANTY.USER_DATE[2])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_DATE_4, CONTAINERS_WARRANTY.USER_DATE[3])

		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_1, CONTAINERS_WARRANTY.USER_NUMBER[0])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_2, CONTAINERS_WARRANTY.USER_NUMBER[1])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_3, CONTAINERS_WARRANTY.USER_NUMBER[2])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_4, CONTAINERS_WARRANTY.USER_NUMBER[3])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.USER_DEFINED_NUMBER_5, CONTAINERS_WARRANTY.USER_NUMBER[4])
		_common.minimizeContainer(cnt.uuid.CONTRACT_WARRANTY)
	});

	it("TC - Verify If set value to duration months, it should auto calculate for warrant end date", function () {
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.CONTRACT_WARRANTY)
		_common.create_newRecord(cnt.uuid.CONTRACT_WARRANTY)
		_common.waitForLoaderToDisappear()
		_common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_SECURITY_FK,commonLocators.CommonKeys.LIST,CONTAINERS_WARRANTY.WARRANTY_SECURITY)
		_common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_OBLIGATION_FK,commonLocators.CommonKeys.LIST,CONTAINERS_WARRANTY.WARRANTY_OBLIGATION)
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, WARRANTY_DESC3)
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.HAND_OVER_DATE, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_WARRANTY.DATE_DAYS[1])
		_common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.DURATION_MONTHS, app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_WARRANTY.MONTHS[1])
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.CONTRACT_WARRANTY,WARRANTY_DESC3)
		_validate.verify_isRecordPresent(cnt.uuid.CONTRACT_WARRANTY,WARRANTY_DESC3)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_WARRANTY,app.GridCells.WARRANTY_END_DATE,CONTAINERS_WARRANTY.DATE_DAYS[2])
	});

});


	