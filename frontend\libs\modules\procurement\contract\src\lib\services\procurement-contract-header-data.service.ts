/*
 * Copyright(c) RIB Software GmbH
 */
import { Subject } from 'rxjs';
import { inject, Injectable, InjectionToken } from '@angular/core';
import {
	DataServiceHierarchicalRoot,
	IDataServiceEndPointOptions,
	IDataServiceOptions,
	IDataServiceRoleOptions,
	ServiceRole
} from '@libs/platform/data-access';
import {
	IBasisContractChangeEvent,
	IContractCreateCompleteResponse,
	IExchangeRateChangedEvent,
	IFrameworkCatalogChangeEvent,
	IModifyExchangeRate,
	IPaymentTermChangedEvent,
	IPrcCommonMainDataService,
	IPrcCommonReadonlyService,
	IPrcHeaderContext,
	IPrcHeaderDataService,
	IPrcModuleValidatorService,
	ProcurementCommonCascadeDeleteConfirmService, ProcurementCommonEntityProxyHelper,
	ProcurementContractPurchaseOrderTypeService,
	ProcurementOverviewSearchlevel
} from '@libs/procurement/common';
import { IPrcHeaderEntity } from '@libs/procurement/interfaces';
import { IConHeaderEntity } from '../model/entities';
import { ProcurementCreateContractDialogService } from './procurement-create-contract-dialog.service';
import { EntityProxy, FieldKind, ProcurementInternalModule } from '@libs/procurement/shared';
import {
	IExceptionResponse,
	ISearchResult,
	PlatformHttpService,
	PlatformTranslateService,
	ServiceLocator
} from '@libs/platform/common';
import { get, union } from 'lodash';
import {
	BasicsSharedCompanyContextService,
	BasicsSharedConStatusLookupService,
	BasicsSharedMaterialCatalogTypeLookupService,
	BasicsSharedNumberGenerationService,
	BasicsSharedTreeDataHelperService,
	CreationDataProvider,
	RubricIndexEnum
} from '@libs/basics/shared';
import {
	StandardDialogButtonId,
	UiCommonGridDialogService,
	UiCommonMessageBoxService
} from '@libs/ui/common';
import {
	ProcurementContractHeaderReadonlyProcessor
} from './processors/procurement-contract-header-readonly-processor.class';
import { ProcurementContractCallOffsDataService } from './procurement-contract-call-offs-data.service';
import { ProcurementContractOverallDiscountService } from './procurement-contract-overall-discount.service';
import { IBasicsCustomizeConStatusEntity, IBasicsCustomizeMaterialCatalogTypeEntity } from '@libs/basics/interfaces';
import { ContractComplete } from '../model/contract-complete.class';

export const PROCUREMENT_CONTRACTHEADER_DATA_TOKEN = new InjectionToken<ProcurementContractHeaderDataService>('procurementContractDataToken');

/**
 * Contract data service
 */
@Injectable({
	providedIn: 'root',
})
export class ProcurementContractHeaderDataService
	extends DataServiceHierarchicalRoot<IConHeaderEntity, ContractComplete>
	implements
		IPrcHeaderDataService<IConHeaderEntity, ContractComplete>,
		IPrcModuleValidatorService<IConHeaderEntity, ContractComplete>,
		IPrcCommonReadonlyService<IConHeaderEntity>,
		IModifyExchangeRate<IConHeaderEntity>,
		IPrcCommonMainDataService<IConHeaderEntity, ContractComplete> {
	private readonly http = inject(PlatformHttpService);

	public readonly readonlyProcessor: ProcurementContractHeaderReadonlyProcessor;
	protected readonly companyContext = inject(BasicsSharedCompanyContextService);
	protected readonly numberGenerationService = inject(BasicsSharedNumberGenerationService);

	/**
	 * Dialog form config service.
	 */
	private createDialogService = inject(ProcurementCreateContractDialogService);
	public conStatusLookupService = inject(BasicsSharedConStatusLookupService);

	protected treeHelper = inject(BasicsSharedTreeDataHelperService);

	protected readonly translateService = inject(PlatformTranslateService);
	protected readonly messageBoxService = inject(UiCommonMessageBoxService);

	public readonly entityProxy = new EntityProxy(this, [
		['MaterialCatalogFk', FieldKind.MdcMaterialCatalogFk],
		['TaxCodeFk', FieldKind.MdcTaxCodeFk],
		['ControllingUnitFk', FieldKind.MdcControllingUnitFk]
	]);

	public readonly paymentTermChanged$ = new Subject<IPaymentTermChangedEvent>();
	public readonly totalsInvalidated$ = new Subject<void>();
	public readonly exchangeRateChanged$ = new Subject<IExchangeRateChangedEvent>();
	public readonly readonlyChanged$ = new Subject<boolean>();

	public readonly frameworkMdcCatalogChanged$ = new Subject<IFrameworkCatalogChangeEvent>();
	public readonly purchaseUpdatedMessage$ = new Subject<IConHeaderEntity>();
	public readonly projectFkChanged$ = new Subject<number>();
	public readonly controllingUnitChanged$ = new Subject<number>();
	public readonly basisChanged$ = new Subject<IBasisContractChangeEvent>();
	public readonly billingSchemaChanged$ = new Subject<number>();
	public readonly onHeaderUpdated$ = new Subject<ContractComplete>();

	public readonly gridDialogService = inject(UiCommonGridDialogService);
	private readonly cascadeDeleteHelperService = inject(ProcurementCommonCascadeDeleteConfirmService);

	public readonly totalCreationProvider = new CreationDataProvider<IConHeaderEntity>(this.converter);

	private frameworkCatalogTypes: IBasicsCustomizeMaterialCatalogTypeEntity[] = [];

	/**
	 * The constructor
	 */
	public constructor() {
		const options: IDataServiceOptions<IConHeaderEntity> = {
			apiUrl: 'procurement/contract/header',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'listcontract',
				usePost: true,
			},
			createInfo: {
				endPoint: 'createcontract',
				usePost: true,
				// todo - creation dialog
				// preparePopupDialogData:() =>{
				// 	return  this.createDialogService.openCreateDialogForm();
				// }
			},
			updateInfo: <IDataServiceEndPointOptions>{
				endPoint: 'updatecontract',
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'deletecontracts',
			},
			roleInfo: <IDataServiceRoleOptions<IConHeaderEntity>>{
				role: ServiceRole.Root,
				itemName: 'ConHeader',
			},
		};

		super(options);

		this.readonlyProcessor = this.createReadonlyProcessor();
		this.processor.addProcessor([this.readonlyProcessor]);
		this.init();
	}

	private init() {
		this.selectionChanged$.subscribe((e) => {
			this.onSelectionChanged();
		});

		ProcurementCommonEntityProxyHelper.registerPropertyChangedConfirm(this.entityProxy);
	}

	public async invalidateTotals(): Promise<void> {
		this.totalsInvalidated$.next();
	}

	private onSelectionChanged() {
		const currentItem = this.getSelectedEntity();
		if (!currentItem) {
			return;
		}

		if (currentItem.Id) {
			//todo:task(DEV-13988) wait the boqMainLookupFilterService ready in 'boq.main' module, not sure the boq still need this solution
			//service.maintainBoqMainLookupFilter(currentItem);
		}
		//todo: wait framework 'header-information-service.js' ready
		//procurementCommonOverrideHeaderInfoService.updateModuleHeaderInfo(service,'cloud.desktop.moduleDisplayNameContract');
	}

	protected createReadonlyProcessor() {
		return new ProcurementContractHeaderReadonlyProcessor(this);
	}

	/**
	 * Provide the load payload here
	 * @protected
	 */
	protected override provideLoadPayload(): object {
		return {};
	}

	/**
	 * Convert http response of searching to standard search result
	 * @param loaded
	 * @protected
	 */
	protected override onLoadByFilterSucceeded(loaded: object): ISearchResult<IConHeaderEntity> {
		// todo - use the general FilterResult interface?
		const fr = get(loaded, 'FilterResult')! as {
			ExecutionInfo: string;
			RecordsFound: number;
			RecordsRetrieved: number;
			ResultIds: number[];
		};

		let dtos = get(loaded, 'Main')! as IConHeaderEntity[];

		dtos = this.treeHelper.flatTreeArray(dtos, (e) => e.ChildItems);
		//todo: wait the dynamic Characteristic service ready
		// var exist = platformGridAPI.grids.exist('e5b91a61dbdd4276b3d92ddc84470162');
		// if (exist) {
		// 	var characterColumnService = service.characterColumnService();
		// 	var allList=getContractList(readData.dtos);
		// 	characterColumnService.appendCharacteristicCols(allList);
		// }

		return {
			FilterResult: {
				ExecutionInfo: fr.ExecutionInfo,
				RecordsFound: fr.RecordsFound,
				RecordsRetrieved: fr.RecordsRetrieved,
				ResultIds: fr.ResultIds,
			},
			dtos: dtos,
		};
	}

	protected override provideCreatePayload(): object {
		//TODO DEV-43563 should get the create parameter from create dialog
		return {
			Code: 'new contract',
			ProjectFk: 1019183,
			ConfigurationFk: 11,
			BusinessPartnerFk: 195,
			SubsidiaryFk: 1000881,
		};
	}

	public override canDelete(): boolean {
		if (!super.canDelete() || !this.getSelectedEntity()) {
			return false;
		}

		const status = this.getStatus();
		return !status || !status.IsReadOnly;
	}

	/**
	 * equal to updateDone
	 * @param updated
	 */
	public override takeOverUpdatedChildEntities(updated: ContractComplete): void {
		super.takeOverUpdatedChildEntities(updated);
		//todo: wait the contract change container ready
		//updateChange(updateData);

		const currentItem = this.flatList().find((candidate) => candidate.Id === updated.MainItemId);
		if (currentItem) {
			// set basis contract as readOnly when it has items
			if (updated.ConHeader && updated.ConHeader.Id === currentItem.Id) {
				updated.ConHeader.HasItems = currentItem.HasItems;
			}
			this.readonlyProcessor.process(currentItem);
			//todo: lvy, task(DEV-13988)  relate to boq, the boq module
			//service.maintainBoqMainLookupFilter(currentItem);

			// for ALM(#92196),if user overwrites line no to a line no already used in the basis contract or a previous change order
			// then MDC_MATERIAL_FK, PRC_STRUCTURE_FK, DESCRIPTION1, DESCRIPTION2, BAS_UOM_FK must be taken from the previous line and set to read only10
			const selectedContract = this.getSelectedEntity();
			if (selectedContract) {
				const conHeaderFk = selectedContract.ContractHeaderFk;
				if (conHeaderFk) {
					//not sure this logic, if  the prcItem change will impact the contract header? so need to get the latest contract header?
					if (updated.PrcItemToSave || updated.PrcItemToDelete) {
						this.getHeaderContract();
					}
				}
			}
			/*if (selectedContract && currentItem.Id !== selectedContract.Id) {
				//todo: lvy, task(DEV-13988)  relate to boq, the boq module
				//service.maintainBoqMainLookupFilter(selectItem);
			} else {
				//service.maintainBoqMainLookupFilter(currentItem);
			}*/
		}
		//the sub container can subscribe this event: reload prcItems, update prcItem readonly, SubContractor container and so on.
		this.onHeaderUpdated$.next(updated);
	}

	protected override onCreateSucceeded(created: object): IConHeaderEntity {
		const resp = created as IContractCreateCompleteResponse;

		if(resp.ConHeaderDto && resp.PrcTotalsDto) {
			this.totalCreationProvider.setSubEntities(resp.ConHeaderDto, resp.PrcTotalsDto);
		}

		//todo:  wait the dynamic Characteristic service ready
		// var exist = platformGridAPI.grids.exist('e5b91a61dbdd4276b3d92ddc84470162');
		// if (exist) {
		// 	var characterColumnService = service.characterColumnService();
		// 	var allList=getContractList(readData.dtos);
		// 	characterColumnService.appendCharacteristicCols(allList);
		// }
		const createdContract = resp.ConHeaderDto;
		//todo:t ask(DEV-14015) wait header text container ready
		// $timeout(function () {
		// 	reloadHeaderText(conHeaderCreated);
		// }, 500);

		if (createdContract) {
			if (this.shouldGenerateNumber(createdContract)) {
				createdContract.Code = this.getNumberDefaultText(createdContract);
			}
		}

		return createdContract!;
	}

	public override createUpdateEntity(modified: IConHeaderEntity | null): ContractComplete {
		const complete = new ContractComplete();
		if (modified === null && this.hasSelection()) {
			const selected = this.getSelection()[0];
			//TODO: workaround to set the new created entity as modified, will remove in future, need framework support.
			if (selected && selected.Version === 0) {
				if (selected.BusinessPartnerFk === 0) {
					selected.BusinessPartnerFk = 1004548;
				}
				if (selected.SupplierFk === 0) {
					selected.SupplierFk = undefined;
				}
				if (selected.SubsidiaryFk === 0) {
					selected.SubsidiaryFk = undefined;
				}
				if (selected.ContactFk === 0) {
					selected.ContactFk = undefined;
				}
				modified = selected;
			}
		}
		if (modified !== null) {
			complete.ConHeader = modified;

			//TODO: workaround now cause framework can't support.
			complete.ConHeaders = [modified];
		}

		if (this.hasSelection()) {
			complete.MainItemId = this.getSelection()[0].Id;
		}

		return complete;
	}

	public override delete(entities: IConHeaderEntity[] | IConHeaderEntity) {
		const selectedItem = this.getSelectedEntity();
		if (!selectedItem) {
			throw new Error('Please select a record first');
		}
		this.cascadeDeleteHelperService
			.openDialog({
				filter: '',
				mainItemId: selectedItem.Id,
				moduleIdentifier: ProcurementInternalModule.Contract,
				searchLevel: ProcurementOverviewSearchlevel.RootContainer,
			})
			.then((result) => {
				if (result && result.closingButtonId === StandardDialogButtonId.Yes) {
					try {
						super.delete(entities);
					} catch (e) {
						this.messageBoxService.showErrorDialog(e as IExceptionResponse)?.then();
					}
				}
			});
	}

	public override getModificationsFromUpdate(complete: ContractComplete): IConHeaderEntity[] {
		if (complete.ConHeaders) {
			return complete.ConHeaders;
		}
		if (complete.ConHeader) {
			return [complete.ConHeader];
		}
		return [];
	}

	/**
	 * check the contract whether is change order
	 * @param contractEntity
	 */
	public isChangeOrder(contractEntity: IConHeaderEntity): boolean {
		return ProcurementContractPurchaseOrderTypeService.isChangeOrder(contractEntity);
	}

	/**
	 * check the contract whether is call off
	 * @param contractEntity
	 */
	public isCallOff(contractEntity: IConHeaderEntity): boolean {
		return ProcurementContractPurchaseOrderTypeService.isCallOff(contractEntity);
	}

	public override childrenOf(element: IConHeaderEntity): IConHeaderEntity[] {
		return element.ChildItems ?? [];
	}

	public override parentOf(element: IConHeaderEntity): IConHeaderEntity | null {
		if (element.ConHeaderFk == null) {
			return null;
		}

		const parentId = element.ConHeaderFk;
		const parent = this.flatList().find((candidate) => candidate.Id === parentId);
		return parent === undefined ? null : parent;
	}

	public getHeaderContract(): void {
		const selectedEntity = this.getSelectedEntity();
		if (!selectedEntity || !selectedEntity.ConHeaderFk) {
			return;
		}

		const conHeaderId = selectedEntity.ConHeaderFk;
		this.getContractById(conHeaderId).then((response) => {
			const conHeader = response as IConHeaderEntity;
			const contracts = this.getList();

			const updatedContracts = contracts.map((item) => {
				return item.Id === conHeaderId ? conHeader : item;
			});
			const result = this.getFlattenContracts(updatedContracts);
			this.setList(result);
		});
	}

	// #region IPrcHeaderDataService

	public getHeaderContext(entity?: IConHeaderEntity): IPrcHeaderContext {
		const contract = entity ?? this.getSelectedEntity();
		if (!contract) {
			throw new Error('please selected record first');
		}

		return {
			prcHeaderFk: contract.PrcHeaderFk,
			projectFk: contract.ProjectFk!,
			controllingUnitFk: contract.ControllingUnitFk,
			currencyFk: contract.BasCurrencyFk,
			exchangeRate: contract.ExchangeRate,
			taxCodeFk: contract.TaxCodeFk,
			vatGroupFk: contract.BpdVatGroupFk,
			prcConfigFk: contract.PrcHeaderEntity?.ConfigurationFk,
			structureFk: contract.PrcHeaderEntity?.StructureFk,
			businessPartnerFk: contract.BusinessPartnerFk,
			dateOrdered: contract.DateOrdered,
			readonly: contract.IsStatusReadonly,
			packageFk: contract.PackageFk,
			boqWicCatFk: contract.BoqWicCatFk,
			boqWicCatBoqFk: contract.BoqWicCatBoqFk,
			contractFk: contract.Id
		};
	}

	public getSelectedPrcHeaderEntity(): IPrcHeaderEntity {
		const selectedEntity = this.getSelectedEntity();
		if (!selectedEntity) {
			throw new Error('please selected record first');
		}
		return this.getPrcHeaderEntity(selectedEntity);
	}

	public getPrcHeaderEntity(entity: IConHeaderEntity): IPrcHeaderEntity {
		return entity.PrcHeaderEntity;
	}

	public updateTotalLeadTime(value: number) {
		const entity = this.getSelectedEntity()!;
		entity.TotalLeadTime = value;
	}

	// #endregion

	public isValidForSubModule(): boolean {
		const entity = this.getSelectedEntity()!;
		return !(entity !== null && entity.Id !== null && entity.ConHeaderFk !== null && entity.ProjectChangeFk !== null);
	}

	public getInternalModuleName(): string {
		return ProcurementInternalModule.Contract;
	}

	public isEntityReadonly(entity?: IConHeaderEntity): boolean {
		return this.getStatus(entity)?.IsReadOnly ?? false;
	}

	public get loginCompanyEntity() {
		return this.companyContext.loginCompanyEntity;
	}

	/**
	 * When generating code, it is necessary to rely on rubric index. Different rubric indexes represent different contract types, and different contract types can be configured with different number ranges
	 */
	public getRubricIndex(entity: IConHeaderEntity) {
		let rubricIndex: RubricIndexEnum = RubricIndexEnum.PurchaseOrder;
		if (entity.ConHeaderFk != null) {
			rubricIndex = entity.ProjectChangeFk != null ? RubricIndexEnum.ChangeOrder : RubricIndexEnum.CallOff;
		}
		return rubricIndex;
	}

	public getContractById(id: number): Promise<IConHeaderEntity> {
		return this.http.get<IConHeaderEntity>('procurement/contract/header/getitembyId', { params: { id: id } });
	}

	public getStatus(entity?: IConHeaderEntity): IBasicsCustomizeConStatusEntity | undefined {
		const conHeader = entity ?? this.getSelectedEntity();
		if (!conHeader) {
			return undefined;
		}

		const status = this.conStatusLookupService.cache.getItem({ id: conHeader.ConStatusFk });
		return status || undefined;
	}

	// dto is a tree,need get list
	public getFlattenContracts(dtos: IConHeaderEntity[]) {
		return this.treeHelper.flatTreeArray(dtos, (e) => e.ChildItems);
	}

	public getSelectedProjectId(): number | undefined {
		return this.getSelectedEntity()?.ProjectFk ?? undefined;
	}

	public getConfigurationFk(): number | undefined {
		return this.getSelectedEntity()?.PrcHeaderEntity?.ConfigurationFk;
	}

	public isFrameworkContract(): boolean {
		const selectedEntity = this.getSelectedEntity();
		return selectedEntity !== null && ProcurementContractPurchaseOrderTypeService.isFramework(selectedEntity);
	}

	public isFrameworkContractCallOffByWic(): boolean {
		const selectedEntity = this.getSelectedEntity();
		return selectedEntity !== null && ProcurementContractPurchaseOrderTypeService.isFrameworkContractCallOffByWic(selectedEntity);
	}

	public isFrameworkContractCallOffByMdc(): boolean {
		const selectedEntity = this.getSelectedEntity();
		return selectedEntity !== null && ProcurementContractPurchaseOrderTypeService.isFrameworkContractCallOffByMdc(selectedEntity);
	}

	public async createDeepCopy() {
		const selectItem = this.getSelectedEntity();
		if (!selectItem) {
			return;
		}

		const contractComplete = await this.http.post<ContractComplete>('procurement/contract/header/deepcopy', selectItem);
		this.onCreateSucceeded(contractComplete);
		if (contractComplete.ConHeader !== undefined && contractComplete.ConHeader.ConHeaderFk !== null) {
			this.addToChildItems(contractComplete?.ConHeader);
			const created = contractComplete as IContractCreateCompleteResponse;

			if(created.ConHeaderDto && created.PrcTotalsDto) {
				this.totalCreationProvider.setSubEntities(created.ConHeaderDto, created.PrcTotalsDto);
			}

			// load call offs
			const callOffService = inject(ProcurementContractCallOffsDataService);
			if (callOffService) {
				await callOffService.load({ id: contractComplete.ConHeader.Id });
			}
		}
		if (contractComplete.ConHeader !== undefined) {
			this.setList(union(this.getList(), [contractComplete.ConHeader]));
			return this.goToLast();
		}
	}

	public async createChangeOrder() {
		const selectItem = this.getSelectedEntity();
		if (!selectItem) {
			return;
		}
		const conHeaderId = selectItem.ConHeaderFk ? selectItem.ConHeaderFk : selectItem.Id;
		const result = await this.http.get('procurement/contract/change/getchangeid', { params: { mainItemId: conHeaderId } });
		if (!result) {
			await this.messageBoxService.showMsgBox('procurement.contract.createChangeNotFound', 'Issue', 'ico-info');
		} else {
			const created = await this.http.post<IConHeaderEntity>('procurement/contract/change/create', { mainItemId: conHeaderId });
			if (created) {
				this.onCreateSucceeded(created);
				this.addToChildItems(created);
			}
		}
	}

	public async createCallOff() {
		const selectItem = this.getSelectedEntity();
		if (!selectItem) {
			return;
		}
		const conHeaderId = selectItem.ConHeaderFk ? selectItem.ConHeaderFk : selectItem.Id;
		const created = await this.http.post<IConHeaderEntity>('procurement/contract/callOffs/create', { mainItemId: conHeaderId });
		if (created) {
			this.onCreateSucceeded(created);
			this.addToChildItems(created);
			const completeDto = { ConHeaderDto: created } as IContractCreateCompleteResponse;

			if(completeDto.ConHeaderDto && completeDto.PrcTotalsDto) {
				this.totalCreationProvider.setSubEntities(completeDto.ConHeaderDto, completeDto.PrcTotalsDto);
			}

			// load call offs
			const callOffService = inject(ProcurementContractCallOffsDataService);
			if (callOffService) {
				await callOffService.load({ id: created.Id });
			}
		}
	}

	public addToChildItems(newContract: IConHeaderEntity) {
		if (!newContract.ConHeaderFk) {
			return;
		}
		const contracts = this.getList();
		const parentItem = contracts.find((candidate) => candidate.Id === newContract.ConHeaderFk);
		if (parentItem) {
			parentItem.HasChildren = true;
			if (parentItem.ChildItems === null) {
				parentItem.ChildItems = [];
			}
			parentItem.ChildItems?.push(newContract);
		}
		this.setList(contracts);
	}

	public getBasisContractInfo() {
		const selectedEntity = this.getSelectedEntity();
		if (!selectedEntity || selectedEntity.ConHeaderFk === null || selectedEntity.ProjectChangeFk !== null) {
			return null;
		}
		return {
			basisContractId: selectedEntity.ConHeaderFk,
		};
	}

	public async getContractStatus(contractId: number) {
		let entity = this.getList().find((e) => e.Id === contractId);

		if (!entity) {
			// In case contract is not loaded
			entity = await this.http.get<IConHeaderEntity>('procurement/contract/header/get', {
				params: {
					id: contractId,
				},
			});
		}

		return await this.conStatusLookupService.getItemByKeyAsync({
			id: entity.ConStatusFk,
		});
	}


	//todo: lvy,task(DEV-14015) wait boq module function ready
	// service.parentBoqItems = [];
	// service.getParentBoqItems = function () {
	// 	if (service.getSelected() && service.getSelected().ConHeaderFk !== null && service.getSelected().ConHeaderFk > 0) {
	// 		$http.get(globals.webApiBaseUrl + 'procurement/common/boq/getboqitemsbymodule?module=3&headerId=' + service.getSelected().ConHeaderFk).then(function (result) {
	// 			service.parentBoqItems = result.data;
	// 		});
	// 	}
	// };

	private shouldGenerateNumber(createdContract: IConHeaderEntity): boolean {
		const rubricIndex = this.getRubricIndex(createdContract);
		return this.numberGenerationService.hasNumberGenerateConfig(createdContract.RubricCategoryFk, rubricIndex);
	}

	private getNumberDefaultText(createdContract: IConHeaderEntity): string {
		const rubricIndex = this.getRubricIndex(createdContract);
		return this.numberGenerationService.provideNumberDefaultText(createdContract.RubricCategoryFk, rubricIndex);
	}

	/**
	 * Handle on exchange rate changed
	 * @param entity
	 * @param exchangeRate
	 * @param isUpdateByCurrency
	 * @param isRemainHomeCurrency
	 */
	public onExchangeRateChanged(entity: IConHeaderEntity, exchangeRate: number, isUpdateByCurrency: boolean, isRemainHomeCurrency: boolean = false): void {
		ServiceLocator.injector.get(ProcurementContractOverallDiscountService).updateOverallDiscountAfterExchangeRateChanged(entity, exchangeRate, isRemainHomeCurrency);
		if (isUpdateByCurrency) {
			this.readonlyProcessor.process(entity);
		}
	}

	public getIsFrameworkTypes() {
		const materialCatTypeLookupService = ServiceLocator.injector.get(BasicsSharedMaterialCatalogTypeLookupService);
		const materialCatTypes = materialCatTypeLookupService.syncService?.getListSync();
		if (!materialCatTypes) {
			return;
		}
		if (materialCatTypes?.length) {
			const frmCatTypes = materialCatTypes.filter((i) => i.Sorting && i.IsLive && i.IsFramework);
			if (frmCatTypes?.length) {
				this.setFrameworkCatalogTypes(frmCatTypes);
			}
		}
		materialCatTypeLookupService.setFrameworkCatlogTypes(this.frameworkCatalogTypes);
		return this.frameworkCatalogTypes;
	}

	/**
	 * Gets the framework catalog types.
	 */
	public getFrameworkCatalogTypes(): IBasicsCustomizeMaterialCatalogTypeEntity[] {
		return this.frameworkCatalogTypes;
	}

	/**
	 * Sets the framework catalog types.
	 * @param types The new framework catalog types.
	 */
	public setFrameworkCatalogTypes(types: IBasicsCustomizeMaterialCatalogTypeEntity[]): void {
		this.frameworkCatalogTypes = types;
	}

	//todo: Lincoin,task(DEV-14032) wait the contract change container ready, not sure the new angular solution still need to save the 'Change' separately, js: change-main-contract-change-data-service.js
	// function updateChange(updateData) {
	// 	if (updateData.ChangeToDelete && updateData.ChangeToDelete.length > 0) {
	// 		$http.post(globals.webApiBaseUrl + 'change/main/multidelete', updateData.ChangeToDelete);
	// 	}
	// 	if (updateData.ChangeToSave && updateData.ChangeToSave.length > 0) {
	// 		$http.post(globals.webApiBaseUrl + 'change/main/update', {Change: updateData.ChangeToSave}).then(function (response) {
	// 			if (response.data && response.data.Change && response.data.Change.length > 0) {
	// 				var changeDataService = $injector.get('changeMainContractChangeDataService');
	// 				var changes = changeDataService.getList();
	// 				var selectedEntity = changeDataService.getSelected();
	// 				var isExist = _.find(response.data.Change, function (item) {
	// 					return _.find(changes, {Id: item.Id});
	// 				});
	// 				if (isExist) {
	// 					var entity = _.find(response.data.Change, {Id: selectedEntity.Id});
	// 					if (entity) {
	// 						selectedEntity = entity;
	// 					}
	// 					changeDataService.load().then(function () {
	// 						changeDataService.setSelected(selectedEntity);
	// 					});
	// 				}
	// 			}
	// 		});
	// 	}
	// }

	//todo: task(DEV-14015) wait the procurement Common HeaderText container and service ready
	// public reloadHeaderText(item, options){
	// 	var headerTextDataService = procurementCommonHeaderTextNewDataService.getService(service);
	// 	headerTextDataService.reloadData({
	// 		prcHeaderId: item.PrcHeaderEntity.Id,
	// 		prcConfigurationId: item.PrcHeaderEntity.ConfigurationFk,
	// 		projectId: item.ProjectFk,
	// 		isOverride: options !== null && !angular.isUndefined(options) ? options.isOverride : false
	// 	});
	// }

	//todo: lvy, task(DEV-13988)  relate to boq, the boq module call this function, not sure the boq solution in new angular as the same as the old logic
	// service.isChangeHeader = function (boqItem) {
	// 	var baseBoqItem = _.indexOf(service.parentBoqItems, boqItem.BoqItemPrjItemFk);
	// 	// eslint-disable-next-line eqeqeq
	// 	return service.getSelected() && service.getSelected().ConHeaderFk !== null &&
	// 		service.getSelected().ConHeaderFk > 0 && baseBoqItem >= 0; // if is change order and the base req have this item, readonly
	// };
}
