/*
 * Copyright(c) RIB Software GmbH
 */

import {IEntityProcessor, IReadOnlyField} from '@libs/platform/data-access';
import { IPaymentEntity } from '@libs/sales/interfaces';
import { SalesBillingPaymentDataService } from './sales-billing-payment-data.service';
import { inject } from '@angular/core';
import { SalesCommonContextService } from '@libs/sales/common';

/**
 * Sales billing payment entity readonly processor
 */
export class SalesBillingPaymentReadonlyProcessor<T extends IPaymentEntity> implements IEntityProcessor<T> {

    private readonly salesCommonContextService = inject(SalesCommonContextService);

    public constructor(protected dataService: SalesBillingPaymentDataService) {
    }
    /**
     * Process payment readonly logic
     * @param item
     */
    public async process(item: T) {
        if (!item){
             return;
        }

        let readOnly = true;

        if (item.CurrencyFk) {
            const company = await this.salesCommonContextService.getCompany();
            const isSameCurrency = company?.CurrencyFk === item.CurrencyFk;
            readOnly = isSameCurrency;
        }

        const readonlyFields: IReadOnlyField<T>[] = [
            { field: 'ExchangeRate', readOnly }
        ];

        this.dataService.setEntityReadOnlyFields(item, readonlyFields);
    }

    /**
     * Revert process item
     * @param item
     */
    public revertProcess(item: T) {
    }
}
