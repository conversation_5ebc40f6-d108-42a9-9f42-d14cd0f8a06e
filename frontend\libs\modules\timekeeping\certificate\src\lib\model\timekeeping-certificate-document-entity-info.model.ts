/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { TimekeepingCertificateDocumentDataService } from '../services/timekeeping-certificate-document-data.service';
import { TimekeepingCertificateDocumentValidationService } from '../services/timekeeping-certificate-document-validation.service';
import { ICertificateDocumentEntity } from '@libs/resource/interfaces';
import { EMPLOYEE_CERTIFICATION_LOOKUP_PROVIDER_TOKEN } from '@libs/timekeeping/interfaces';
import { ILayoutConfiguration } from '@libs/ui/common';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN } from '@libs/basics/interfaces';


export const TIMEKEEPING_CERTIFICATE_DOCUMENT_ENTITY_INFO: EntityInfo =
	EntityInfo.create<ICertificateDocumentEntity>({
		grid: {
			title: { key: 'timekeeping.certificate' + '.employeeDocumentListTitle' },
		},
		form: {
			title: { key: 'timekeeping.certificate' + '.employeeDocumentDetailTitle' },
			containerUuid: '6e476e45fc076573698722593466n523',
		},
		dataService: ctx => ctx.injector.get(TimekeepingCertificateDocumentDataService),
		validationService: ctx => ctx.injector.get(TimekeepingCertificateDocumentValidationService),
		dtoSchemeId: { moduleSubModule: 'Timekeeping.Certificate', typeName: 'EmployeeCertifiedDocumentDto' },
		permissionUuid: 'a68ac5da946948r1abaa204629a91048',
		layoutConfiguration: async ctx=> {
			const employeeCertificationLookupProvider = await ctx.lazyInjector.inject(EMPLOYEE_CERTIFICATION_LOOKUP_PROVIDER_TOKEN);
			const customizeLookupProvider = await ctx.lazyInjector.inject(BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN);
			return <ILayoutConfiguration<ICertificateDocumentEntity>>{
				groups:[
					{
						gid: 'default-group',
						attributes: [
							'CertificateFk',
							'DocumentTypeFk',
							'Date',
							'BarCode',
							'OriginFileName',
							'CertificateDocumentTypeFk'
						]
					}
				],
				overloads: {
					CertificateDocumentTypeFk: customizeLookupProvider.providePlantCertificateDocumentTypeLookupOverload({showClearButton: true}),
					CertificateFk: employeeCertificationLookupProvider.generateEmployeeCertificationLookup(),
					DocumentTypeFk: customizeLookupProvider.provideDocumentTypeLookupOverload({ showClearButton: true }),

				},
			labels: {
			...prefixAllTranslationKeys('timekeeping.certificate.', {
					CertificateFk: { key: 'entityCertificateFk' },
					DocumentTypeFk: { key: 'entityDocumentTypeFk' },
					Date: { key: 'entityDate' },
					BarCode: { key: 'entityBarcode' },
					OriginFileName: { key: 'entityOriginFileName' },
					CertificateDocumentTypeFk:{key: 'certificateDoctypeFk'}
				})
			}
		};

	}
});
