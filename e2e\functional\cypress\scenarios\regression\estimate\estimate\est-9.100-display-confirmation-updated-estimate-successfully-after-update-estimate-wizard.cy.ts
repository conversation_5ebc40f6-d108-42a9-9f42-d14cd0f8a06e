import { _common, _estimatePage, _validate, _mainView, _boqPage, _commonAPI } from 'cypress/pages';
import { app, tile, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import { DataCells } from 'cypress/pages/interfaces';


const BOQ_DESC = "BOQ-DESC-" + Cypress._.random(0, 999);
const BOQ_STRUCTURE_DESC = "BOQ-STR-" + Cypress._.random(0, 999);


let CONTAINER_COLUMNS_BOQ;
let CONTAINER_COLUMNS_BOQ_STRUCTURE;
let CONTAINER_COLUMNS_LINE_ITEM

let CONTAINERS_BOQ_STRUCTURE
let MODAL_UPDATE_ESTIMATE_WIZARD;
let MODAL_UPDATE_ESTIMATE;
let CONTAINERS_LINE_ITEM

let BOQ_API_PARAMETERS:DataCells
let UPDATE_ESTIMATE_PARAMETER:DataCells

describe('EST- 9.100 | Display confirmation "Updated Estimate Successfully!" after Update Estimate wizard', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

    before(function () {
        cy.fixture('estimate/est-9.100-display-confirmation-updated-estimate-successfully-after-update-estimate-wizard.json')
          .then((data) => {
            this.data = data;
            CONTAINERS_BOQ_STRUCTURE=this.data.CONTAINERS.BOQ_STRUCTURE
            MODAL_UPDATE_ESTIMATE_WIZARD=this.data.MODAL.UPDATE_ESTIMATE_WIZARD
            MODAL_UPDATE_ESTIMATE=this.data.MODAL.UPDATE_ESTIMATE
            CONTAINERS_LINE_ITEM=this.data.CONTAINERS.LINE_ITEM

            CONTAINER_COLUMNS_LINE_ITEM=this.data.CONTAINER_COLUMNS.LINE_ITEM
            CONTAINER_COLUMNS_BOQ=this.data.CONTAINER_COLUMNS.BOQ
            CONTAINER_COLUMNS_BOQ_STRUCTURE=this.data.CONTAINER_COLUMNS.BOQ_STRUCTURE
           
           
            UPDATE_ESTIMATE_PARAMETER={
              [commonLocators.CommonKeys.CHECKBOX]:MODAL_UPDATE_ESTIMATE_WIZARD
            }
          }).then(()=>{
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
            _common.openTab(app.TabBar.PROJECT).then(() => {
              _common.setDefaultView(app.TabBar.PROJECT)
              _common.waitForLoaderToDisappear()
              _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            })

            _common.waitForLoaderToDisappear();
			_commonAPI.getAccessToken().then((result) => {
				cy.log(`Token Retrieved: ${result.token}`);
			});
          })
    });

    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
            _commonAPI.createProject().then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
            });
    });
    
    it('TC - API: Create BoQ header and BoQ structure', function () {
        BOQ_API_PARAMETERS = {
            [app.GridCells.BRIEF_INFO_SMALL]: BOQ_DESC,
            [app.GridCells.BRIEF_INFO]: BOQ_STRUCTURE_DESC,
            [app.GridCells.QUANTITY_SMALL]: CONTAINERS_BOQ_STRUCTURE.QUANTITY,
            [app.GridCells.PRICE_SMALL]: CONTAINERS_BOQ_STRUCTURE.UNIT_RATE,
            [app.GridCells.BAS_UOM_FK]: CONTAINERS_BOQ_STRUCTURE.UOM,
            [app.GridCells.PROJECT_CODE]: Cypress.env('API_PROJECT_NUMBER_1'),
        };
        _commonAPI.createBoQHeaderWithItems(BOQ_API_PARAMETERS);
        _commonAPI.getBoQHeaderList(Cypress.env('API_PROJECT_ID_1')).then(() => {
            const boqIds = Cypress.env('API_BOQ_HEADER_ID');
            Cypress.env(`BOQ_HEADER_ID`, boqIds[0]);
        });
    });

    it('TC - API: Create estimate header', function () {
        _commonAPI.createEstimateHeader(Cypress.env('API_PROJECT_ID_1'));
    });

    it('TC - API: Generate boq line item', function () {
        _common.openTab(app.TabBar.ESTIMATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
        _common.search_inSubContainer(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));
        _common.select_rowHasValue(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));

        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
        _common.waitForLoaderToDisappear();
        _commonAPI.generateBOQFromLeadingStructure(Cypress.env(`API_EST_ID_1`), Cypress.env(`BOQ_HEADER_ID`), Cypress.env('API_PROJECT_ID_1'));

        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATELINEITEM)
            _common.waitForLoaderToDisappear();
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS,CONTAINER_COLUMNS_LINE_ITEM)
            _common.waitForLoaderToDisappear();
            _common.set_columnAtTop([CONTAINER_COLUMNS_LINE_ITEM.estqtyrelboqfk,CONTAINER_COLUMNS_LINE_ITEM.quantitytarget,CONTAINER_COLUMNS_LINE_ITEM.wqquantitytarget,CONTAINER_COLUMNS_LINE_ITEM.quantity],cnt.uuid.ESTIMATE_LINEITEMS)
        });
        _common.waitForLoaderToDisappear();
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.clear_searchInSidebar();
        _common.waitForLoaderToDisappear();
        cy.REFRESH_CONTAINER();
        _common.waitForLoaderToDisappear();
        _common.select_rowInContainer(cnt.uuid.ESTIMATE_LINEITEMS);
    });
        
    it('TC - Verify "Updated Estimate Successfully!" from update estimate wizard (To Structure)', function () {
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
        });
        _common.maximizeContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS,BOQ_STRUCTURE_DESC)
        _common.edit_dropdownCellWithCaret(cnt.uuid.ESTIMATE_LINEITEMS,app.GridCells.EST_QTY_REL_BOQ_FK,commonLocators.CommonKeys.SPAN,CONTAINERS_LINE_ITEM.BOQ_RELATION[0])
        _common.select_activeRowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.enterRecord_inNewRow(cnt.uuid.ESTIMATE_LINEITEMS,app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_LINE_ITEM.QUANTITY)
        _common.select_activeRowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.enterRecord_inNewRow(cnt.uuid.ESTIMATE_LINEITEMS,app.GridCells.QUANTITY_TARGET,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_LINE_ITEM.AQ_QUANTITY)
        _common.select_activeRowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.enterRecord_inNewRow(cnt.uuid.ESTIMATE_LINEITEMS,app.GridCells.WQ_QUANTITY_TARGET,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_LINE_ITEM.WQ_QUANTITY)
        _common.select_activeRowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE);
        _estimatePage.update_estimate_fromWizard(UPDATE_ESTIMATE_PARAMETER);
        cy.wait(1000)
        _validate.verify_ModalMessage("Updated Estimate Successfully!")
                          cy.wait(2000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
    });

    it('TC - Update quantity of BoQ structure', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem(); 	

        _common.openTab(app.TabBar.BOQS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BOQS, app.FooterTab.BOQs, 2);
        });
    
        _common.clear_subContainerFilter(cnt.uuid.BOQS);
        _common.search_inSubContainer(cnt.uuid.BOQS, BOQ_DESC);
        _common.select_rowHasValue(cnt.uuid.BOQS, BOQ_DESC)
        _common.clickOn_toolbarButton(cnt.uuid.BOQS,btn.ToolBar.ICO_GO_TO);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.BOQSTRUCTURE).then(() => {
            _common.setDefaultView(app.TabBar.BOQSTRUCTURE)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.BOQ_STRUCTURES, app.FooterTab.BOQ_STRUCTURE, 0);
            _common.setup_gridLayout(cnt.uuid.BOQ_STRUCTURES, CONTAINER_COLUMNS_BOQ_STRUCTURE)
            _common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.quantityadj,CONTAINER_COLUMNS_BOQ_STRUCTURE.briefinfo,CONTAINER_COLUMNS_BOQ_STRUCTURE.quantity,CONTAINER_COLUMNS_BOQ_STRUCTURE.basuomfk,CONTAINER_COLUMNS_BOQ_STRUCTURE.price,CONTAINER_COLUMNS_BOQ_STRUCTURE.boqlinetypefk],cnt.uuid.BOQ_STRUCTURES)
        });

        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

        _common.maximizeContainer(cnt.uuid.BOQ_STRUCTURES)
        _common.clear_subContainerFilter(cnt.uuid.BOQ_STRUCTURES)
        _common.select_allContainerData(cnt.uuid.BOQ_STRUCTURES)
        _common.clickOn_expandCollapseButton(cnt.uuid.BOQ_STRUCTURES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowHasValue(cnt.uuid.BOQ_STRUCTURES,BOQ_STRUCTURE_DESC)
        _common.enterRecord_inNewRow(cnt.uuid.BOQ_STRUCTURES,app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_BOQ_STRUCTURE.UPDATED_QUANTITY)
        _common.select_activeRowInContainer(cnt.uuid.BOQ_STRUCTURES)

        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    });

    it('TC - Verify "Updated Estimate Successfully!" from update estimate wizard (From Structure)', function () {
        UPDATE_ESTIMATE_PARAMETER={
            [commonLocators.CommonKeys.CHECKBOX]:MODAL_UPDATE_ESTIMATE
        }
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem(); 	

        
        _common.openTab(app.TabBar.ESTIMATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
        _common.search_inSubContainer(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));
        _common.select_rowHasValue(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));

        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
        _common.waitForLoaderToDisappear();

        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
        });
        _common.waitForLoaderToDisappear();
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.clear_searchInSidebar();
        _common.waitForLoaderToDisappear();
        cy.REFRESH_CONTAINER();
        _common.waitForLoaderToDisappear();
        _common.select_rowInContainer(cnt.uuid.ESTIMATE_LINEITEMS);

        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
        });
        _common.maximizeContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS,BOQ_STRUCTURE_DESC)
        _common.edit_dropdownCellWithCaret(cnt.uuid.ESTIMATE_LINEITEMS,app.GridCells.EST_QTY_REL_BOQ_FK,commonLocators.CommonKeys.SPAN,CONTAINERS_LINE_ITEM.BOQ_RELATION[1])
        _common.select_activeRowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE);
        _estimatePage.update_estimate_fromWizard(UPDATE_ESTIMATE_PARAMETER);
        cy.wait(1000)
        _validate.verify_ModalMessage("Updated Estimate Successfully!")
    
    });
});
