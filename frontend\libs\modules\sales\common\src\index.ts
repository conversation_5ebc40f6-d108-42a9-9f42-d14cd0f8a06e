/*
 * Copyright(c) RIB Software GmbH
 */

import { IApplicationModuleInfo } from '@libs/platform/common';
import { SalesCommonModuleInfo } from './lib/model/sales-common-module-info.class';

export * from './lib/sales-common.module';
export * from './lib/model/sales-common-labels.class';
export * from './lib/services/sales-common-change-code.service';
export * from './lib/services/sales-common-default-sidebar-options.service';
export * from './lib/services/layout-services/sales-common-certificate-layout.service';
export * from './lib/services/sales-common-number-generation.service';
export * from './lib/services/lazy-injections/sales-common-number-generation-provider.service';

/**
 * Returns the module info object for the sales common module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
	return SalesCommonModuleInfo.instance;
}
export * from './lib/services/validations/sales-common-certificate-validation.service';
export * from './lib/services/layout-services/sales-common-document-layout.service';
export * from './lib/services/layout-services/sales-common-warranty-layout.service';
export * from './lib/services/lookups/sales-common-bills-dialog-v2-lookup.service';

export * from './lib/model/classes/sales-common-create-accrual-wizard.class';
export * from './lib/model/interfaces/sales-common-create-accrual-entities.interface';
export * from './lib/model/interfaces/sales-common-create-accrual-form-entity.interface';
export * from './lib/model/interfaces/sales-common-create-accrual-grid-data.interface';
export * from './lib/model/interfaces/sales-common-create-accrual-invalid-entities.interface';
export * from './lib/wizards/sales-common-update-estimate-wizard.service';
export * from './lib/services/layout-services/sales-common-validation-layout.service';
export * from './lib/model/entity-info/sales-common-general-entity-info.service';
export * from './lib/services/sales-common-generals-data.service';
export * from './lib/model/entities/sales-common-generals-entity.interface';

// Warranty services
export * from './lib/model/entities/sales-common-warranty-entity.interface';
export * from './lib/services/sales-common-warranty-data.service';
export * from './lib/services/sales-common-warranty-validation.service';
export * from './lib/model/entity-info/sales-common-warranty-entity-info.model';
export * from './lib/behaviors/sales-common-warranty-behavior.service';

// Milestone common services
export * from './lib/services/sales-common-milestone-data.service';
export * from './lib/services/sales-common-milestone-layout.service';
export * from './lib/model/entities/sales-common-milestone-entity.interface';
export * from './lib/model/entity-info/sales-common-milestone-entity-info.model';

export * from './lib/services/sales-common-document-data.service';
export * from './lib/services/lookups/sales-common-wip-lookup-data.service';

export * from './lib/model/classes/sales-common-change-sales-type-or-configuration-wizard.class';

export * from './lib/services/lookups/sales-common-bid-lookup-data.service';
export * from './lib/services/sales-common-context.service';

export * from './lib/services/layout-services/sales-common-billing-schema-layout.service';

export * from './lib/services/sales-common-context.service';
