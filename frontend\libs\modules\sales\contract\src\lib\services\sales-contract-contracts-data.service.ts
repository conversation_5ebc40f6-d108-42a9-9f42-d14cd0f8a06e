/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { DataServiceFlatRoot, ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, IDataServiceRoleOptions } from '@libs/platform/data-access';
import { SalesContractContractsComplete } from '../model/complete-class/sales-contract-contracts-complete.class';
import { IOrdHeaderEntity, SalesContractContractsEntityToken } from '@libs/sales/interfaces';
import { LazyInjectable, PlatformTranslateService } from '@libs/platform/common';
import { SalesCommonNumberGenerationService } from '@libs/sales/common';

@Injectable({
	providedIn: 'root'
})
@LazyInjectable({
	token: SalesContractContractsEntityToken,
	useAngularInjection: true
})
export class SalesContractContractsDataService extends DataServiceFlatRoot<IOrdHeaderEntity, SalesContractContractsComplete> {
	private readonly numberGenerationService = inject(SalesCommonNumberGenerationService);
	private readonly translateService = inject(PlatformTranslateService);

	public constructor() {
		const options: IDataServiceOptions<IOrdHeaderEntity> = {
			apiUrl: 'sales/contract',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'listfiltered',
				usePost: true
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'multidelete'
			},
			roleInfo: <IDataServiceRoleOptions<IOrdHeaderEntity>>{
				role: ServiceRole.Root,
				itemName: 'OrdHeader',
			}
		};

		super(options);
	}

	protected override onCreateSucceeded(created: object): IOrdHeaderEntity {
		const contractComplete = created as SalesContractContractsComplete;

		if (contractComplete.OrdHeaders) {
			const contract = contractComplete.OrdHeaders;

			if (contract && contract.RubricCategoryFk) {
				const rubricCategoryId = contract.RubricCategoryFk;
				const rubricIndex = this.getRubricIndex(contract);

				if (this.numberGenerationService.hasToGenerateForRubricCategory('contract', rubricCategoryId, rubricIndex)) {
					contract.Code = this.translateService.instant({ key: 'cloud.common.isGenerated' }).text;
				}
			}

			return contract;
		}

		return contractComplete.MainItemId ? { Id: contractComplete.MainItemId } as IOrdHeaderEntity : {} as IOrdHeaderEntity;
	}

	public override createUpdateEntity(modified: IOrdHeaderEntity | null): SalesContractContractsComplete {
		const complete = new SalesContractContractsComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.OrdHeaders = modified;
		}

		return complete;
	}

	/**
	 * Gets the rubric index for a contract header entity
	 *
	 * @param entity The contract header entity
	 * @returns The rubric index
	 */
	private getRubricIndex(entity: IOrdHeaderEntity): number {
		// For child contracts (with a parent contract)
		if (entity.OrdHeaderFk) {
			return entity.PrjChangeFk ? 1 : 2; // 1 = Change Order, 2 = Side Order
		}

		// For main contracts
		return 0;
	}

	public override getModificationsFromUpdate(complete: SalesContractContractsComplete): IOrdHeaderEntity[] {
		if (complete.OrdHeaders === null) {
			return [];
		}
		return [complete.OrdHeaders];
	}
	
}












