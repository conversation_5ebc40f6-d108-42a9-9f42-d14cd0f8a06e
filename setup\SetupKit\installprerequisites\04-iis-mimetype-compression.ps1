function AddMissingMimeType(  [string] $ext, [string] $mimetype) {

	$frx = Get-WebConfigurationProperty //staticContent -Name collection[fileExtension="$ext"]
	if (-not $frx) {
		write-host "Add missing $ext mimetype to Internet Information Server Configuration" -ForegroundColor magenta
		Add-WebConfigurationProperty '//staticContent' -name collection -Value @{fileExtension = "$ext"; mimeType = "$mimetype" }
		$frx = Get-WebConfigurationProperty //staticContent -Name collection[fileExtension="$ext"]
		if (-not $frx) {
			write-host "!Info Added missing $ext mimetype to Internet Information Server Configuration failed" -ForegroundColor red
		}
	} else {
		write-host "Info: $ext mimetype $mimetype already installed." -ForegroundColor green
	}

}

function AddWebConfig([string]$subsection, [string]$property, $value) {

	Get-WebConfigurationProperty /system.webserver -name sections["urlCompression"]
	$frx = Get-WebConfigurationProperty //staticContent -Name collection[fileExtension="$ext"]
	if (-not $frx) {
		write-host "Add missing $ext mimetype to Internet Information Server Configuration" -ForegroundColor magenta
		Add-WebConfigurationProperty '//staticContent' -name collection -Value @{fileExtension = "$ext"; mimeType = "$mimetype" }
		$frx = Get-WebConfigurationProperty //staticContent -Name collection[fileExtension="$ext"]
		if (-not $frx) {
			write-host "!Info Added missing $ext mimetype to Internet Information Server Configuration failed" -ForegroundColor red
		}
	} else {
		write-host "Info: $ext mimetype $mimetype already installed." -ForegroundColor green
	}
}

AddMissingMimeType -ext '.mjs' -mimetype 'application/javascript'
AddMissingMimeType -ext '.wasm' -mimetype 'application/wasm'
AddMissingMimeType -ext '.data' -mimetype 'application/octet-stream'
AddMissingMimeType -ext '.json' -mimetype 'application/json'
AddMissingMimeType -ext '.frx' -mimetype 'text/xml'
AddMissingMimeType -ext '.woff' -mimetype 'application/x-font-woff'
AddMissingMimeType -ext '.md' -mimetype 'text/markdown'
AddMissingMimeType -ext '.bcf' -mimetype 'application/octet-stream'
AddMissingMimeType -ext '.bcfzip' -mimetype 'application/octet-stream'
AddMissingMimeType -ext '.bcmap' -mimetype 'application/octet-stream'
AddMissingMimeType -ext '.vs' -mimetype 'shader/vertex-shader'
AddMissingMimeType -ext '.fs' -mimetype 'shader/fragment-shader'
AddMissingMimeType -ext '.properties' -mimetype 'application/octet-stream'
AddMissingMimeType -ext '.bc3' -mimetype 'application/octet-stream'
AddMissingMimeType -ext '.ftl' -mimetype 'application/octet-stream'

$u = Get-WebConfigurationProperty /system.webServer/urlCompression -name doDynamicCompression
if (-not $u.Value) {
	write-host 'Info: Set urlCompression.doDynamicCompression to true' -ForegroundColor green
	set-webconfigurationproperty '/system.webServer/urlCompression' -name doDynamicCompression -value 'True'
}

$u = Get-WebConfigurationProperty /system.webServer/urlCompression -name doStaticCompression
if (-not $u.Value) {
	write-host 'Info: Set urlCompression.doStaticCompression to true' -ForegroundColor green
	set-webconfigurationproperty '/system.webServer/urlCompression' -name doStaticCompression -value 'True'
}

$u = Get-WebConfigurationProperty -pspath 'MACHINE/WEBROOT/APPHOST' -filter 'system.webServer/httpCompression/dynamicTypes' -name 'Collection' | Where-Object { $_.mimetype -eq 'application/json' }
if (-not $u) {
	write-host "Info: Add httpCompression.doStaticCompression for 'application/json'" -ForegroundColor green
	Add-WebConfigurationProperty -pspath 'MACHINE/WEBROOT/APPHOST' -filter 'system.webServer/httpCompression/dynamicTypes' -name '.' -value @{mimeType = 'application/json'; enabled = 'True' }
}

$u = Get-WebConfigurationProperty -pspath 'MACHINE/WEBROOT/APPHOST' -filter 'system.webServer/httpCompression/dynamicTypes' -name 'Collection' | Where-Object { $_.mimetype -eq 'application/json; charset=utf-8' }
if (-not $u) {
	write-host "Info: Add httpCompression.doStaticCompression for 'application/json; charset=utf-8'" -ForegroundColor green
	Add-WebConfigurationProperty -pspath 'MACHINE/WEBROOT/APPHOST' -filter 'system.webServer/httpCompression/dynamicTypes' -name '.' -value @{mimeType = 'application/json; charset=utf-8'; enabled = 'True' }
}
