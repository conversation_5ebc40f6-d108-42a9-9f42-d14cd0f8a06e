/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ILazyInjectionContext, PlatformModuleManagerService } from '@libs/platform/common';


import { SALES_SHARED_LOOKUP_PROVIDER_TOKEN, SALES_NUMBER_GENERATION_PROVIDER, SalesBidEntityToken, SalesContractContractsEntityToken } from '@libs/sales/interfaces';




export const LAZY_INJECTABLES: LazyInjectableInfo[] =[
LazyInjectableInfo.create('sales.shared.SalesSharedCustomizeLookupOverloadProvider', SALES_SHARED_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/sales/shared');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.SalesSharedCustomizeLookupOverloadProvider) : null;
		
	}),
LazyInjectableInfo.create('sales.bid.SalesBidBidsDataService', SalesBidEntityToken, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/sales/bid');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.SalesBidBidsDataService) : null;
		
	}),
LazyInjectableInfo.create('sales.contract.SalesContractContractsDataService', SalesContractContractsEntityToken, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/sales/contract');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.SalesContractContractsDataService) : null;
		
	}),
LazyInjectableInfo.create('sales.common.SalesCommonNumberGenerationProviderService', SALES_NUMBER_GENERATION_PROVIDER, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/sales/common');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.SalesCommonNumberGenerationProviderService) : null;
		
	}),
];
 