
import { _common, _commonAPI, _logesticPage } from "cypress/pages";
import { app, tile, cnt, commonLocators, sidebar, btn } from "cypress/locators";
import { DataCells } from "cypress/pages/interfaces";
import apiConstantData from "cypress/constantData/apiConstantData";

let CONTAINER_COLUMNS_CONTROLLING_UNIT
let CONTAINERS_CONTROLLING_UNIT
let CONTROLLING_UNIT_A_PARAMETERS:DataCells
let CONTROLLING_UNIT_B_PARAMETERS:DataCells
let CONTAINERS_PLANT_GROUP,
    CONTAINER_COLUMNS_PLANT_GROUP
let CONTAINERS_PLANT,
    CONTAINER_COLUMNS_PLANT,
    PLANT_PARAMETERS,
    CONTAINER_COLUMNS_PLANT_CONTROLLING;
let CONTAINERS_PLANT_PRICE_LISTS
let ALLOCATION_FOR_PLANTS_PARAMETER
let MODAL_PLANT_ALLOCATION
let CONTAINER_COLUMNS_JOBS;
let CONTAINER_JOBS;
let CONTAINER_COLUMNS_DISPATCHING_HEADER,
    CONTAINERS_DISPATCHING_HEADER,
    CONTAINER_COLUMNS_SETTLEMENT_ITEMS,
    CONTAINER_COLUMNS_DISPATCHING_RECORD

const PLANT_GROUP = _common.generateRandomString(3)
const PLANT_CODE = _common.generateRandomString(3)
const PLANT_GROUP_DESC = _common.generateRandomString(3)
const SUB_PLANT_GROUP = _common.generateRandomString(3)
const SUB_PLANT_GROUP_DESC = _common.generateRandomString(3)
const PLANT_DESCRIPTION = _common.generateRandomString(3)
const PROJECT_B_JOB_CODE = _common.generateRandomString(3)
const DISPATCHING_HEADER_1=_common.generateRandomString(3)
const DISPATCHING_HEADER_2=_common.generateRandomString(3)


describe("LRM- 1.84 | Verify settlement item for bulk plant grouped by controlling unit /logistic Job of single project", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
   
    before(function () {
        cy.fixture("LRM/lgm-1.84-verify-settlement-item-for-bulk-plant-grouped-by-controlling-unit-logistic-job-of-single-project.json")
          .then((data) => {
            this.data = data;
            CONTAINER_COLUMNS_CONTROLLING_UNIT=this.data.CONTAINER_COLUMNS.CONTROLLING_UNIT
            CONTAINERS_CONTROLLING_UNIT=this.data.CONTAINERS.CONTROLLING_UNIT
            CONTROLLING_UNIT_A_PARAMETERS={
                [app.GridCells.QUANTITY_SMALL]:[CONTAINERS_CONTROLLING_UNIT.QUANTITY[0],CONTAINERS_CONTROLLING_UNIT.QUANTITY[0]],
                [app.GridCells.BAS_UOM_FK]:[apiConstantData.ID.UOM_BAGS,apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]:["true","true","true"]
            }
            CONTROLLING_UNIT_B_PARAMETERS={
                [app.GridCells.QUANTITY_SMALL]:[CONTAINERS_CONTROLLING_UNIT.QUANTITY[0],CONTAINERS_CONTROLLING_UNIT.QUANTITY[1],CONTAINERS_CONTROLLING_UNIT.QUANTITY[2]],
                [app.GridCells.UOM_FK]:[apiConstantData.ID.UOM_BAGS,apiConstantData.ID.UOM_BAGS,apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]:["true","true","true"]
            }
            CONTAINERS_PLANT_GROUP = this.data.CONTAINERS.PLANT_GROUP;
            CONTAINER_COLUMNS_PLANT_GROUP = this.data.CONTAINER_COLUMNS.PLANT_GROUP;
            CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;
            CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
            PLANT_PARAMETERS = {
                [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION,
                [app.GridCells.PLANT_GROUP_FK]: SUB_PLANT_GROUP,
                [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
                [app.GridCells.PLANT_TYPE_FK]: CONTAINERS_PLANT.PLANT_TYPE,
                [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
                [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
            }
            CONTAINER_COLUMNS_PLANT_CONTROLLING = this.data.CONTAINER_COLUMNS.PLANT_CONTROLLING;
            CONTAINERS_PLANT_PRICE_LISTS = this.data.CONTAINERS.PLANT_PRICE_LISTS;
            CONTAINER_COLUMNS_DISPATCHING_HEADER = this.data.CONTAINER_COLUMNS.DISPATCHING_HEADER
            CONTAINERS_DISPATCHING_HEADER = this.data.CONTAINERS.DISPATCHING_HEADER
            CONTAINER_COLUMNS_DISPATCHING_RECORD = this.data.CONTAINER_COLUMNS.DISPATCHING_RECORD
            CONTAINER_COLUMNS_SETTLEMENT_ITEMS = this.data.CONTAINER_COLUMNS.SETTLEMENT_ITEMS
            MODAL_PLANT_ALLOCATION=this.data.MODAL.PLANT_ALLOCATION
            CONTAINER_JOBS=this.data.CONTAINERS.JOBS
            CONTAINER_COLUMNS_JOBS=this.data.CONTAINER_COLUMNS.JOBS
          })
          .then(()=>{
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
			_commonAPI.getAccessToken()
                      .then((result) => {
                        cy.log(`Token Retrieved: ${result.token}`);
                      });
          })
    });

    after(() => {
        cy.LOGOUT();
    })

    it("TC - API call to assign logged-in user a clerk", function () {
        _commonAPI.getULoggedInUserId(Cypress.env("USER_NAME"))
                  .then(()=>{
                    _commonAPI.assignUserToClerk(Cypress.env("API_LOGGED_IN_USER_ID"),Cypress.env("USER_NAME"),apiConstantData.CONSTANT.SMIJ)
                  })
    });

    it("TC - API: Create project A with logistic job and controlling unit", function () {
        _commonAPI.createProject()
                  .then(() => {
			        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
			        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
                    _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'),2,CONTROLLING_UNIT_A_PARAMETERS)
                  });
    })

    it("TC - API: Create project B with two logistic job and with two different controlling unit", function () {
        _commonAPI.createProject()
                  .then(() => {
			        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
			        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2')).pinnedItem();
                    _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_2'),3,CONTROLLING_UNIT_B_PARAMETERS)
		          });
    })

    it("TC - Create new plant group and sub record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_GROUP)
        _common.openTab(app.TabBar.PLANT_GROUP_AND_LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_GROUP, app.FooterTab.PLANT_GROUPS, 0)
            _common.setup_gridLayout(cnt.uuid.PLANT_GROUP, CONTAINER_COLUMNS_PLANT_GROUP)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_GROUP)
        })
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.PLANT_GROUP)
        _common.clickOn_expandCollapseButton(cnt.uuid.PLANT_GROUP, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.COLLAPSE_ALL)
        _common.create_newRecord(cnt.uuid.PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, PLANT_GROUP_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT_GROUP, PLANT_GROUP_DESC)
        _common.create_newSubRecord(cnt.uuid.PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, SUB_PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, SUB_PLANT_GROUP_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create new plant", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
            _common.setup_gridLayout(cnt.uuid.PLANT, CONTAINER_COLUMNS_PLANT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PLANT)
        _common.create_newRecord(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS)
        _common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE);
        cy.wait(1000) // Added this wait as script is getting failed as loader is taking time to load
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
    })

    it('TC - Add controlling unit record to plant in plant master module', function () {
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            cy.REFRESH_CONTAINER()
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PLANT_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.PLANT)
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_CONTROLLING, app.FooterTab.CONTROLLING_UNIT)
            _common.setup_gridLayout(cnt.uuid.PLANT_CONTROLLING, CONTAINER_COLUMNS_PLANT_CONTROLLING)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_CONTROLLING)
        });
        _common.create_newRecord(cnt.uuid.PLANT_CONTROLLING)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to appear dropdown
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_CONTROLLING, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Assign price list', function () {
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICE_LISTS, app.FooterTab.PRICE_LISTS_SMALL, 1)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICE_LISTS)
        });
        _common.create_newRecord(cnt.uuid.PLANT_PRICE_LISTS)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//  Added this wait as script is getting failed as loader is taking time to load
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, commonLocators.CommonKeys.DAY_RENTAL_INTERNALLY)
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Create initial allocation for plants from wizard', function () {
        ALLOCATION_FOR_PLANTS_PARAMETER = {
            [commonLocators.CommonLabels.JOB]: Cypress.env(`API_PROJECT_NUMBER_1`),
            [commonLocators.CommonLabels.ALLOCATED_FROM]: MODAL_PLANT_ALLOCATION.ALLOCATED_FROM,
            [app.GridCells.WORK_OPERATION_TYPE_FK]: MODAL_PLANT_ALLOCATION.WORK_OPERATION_TYPE,
            [app.GridCells.PLANTS_QUANTITY]:MODAL_PLANT_ALLOCATION.QUANTITY
        }

        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_INITIAL_ALLOCATION_FOR_PLANTS)
        _common.waitForLoaderToDisappear()
        _logesticPage.create_initialAllocationForPlants_fromWizard(ALLOCATION_FOR_PLANTS_PARAMETER,CONTAINERS_PLANT.PLANT_TYPE)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
        });
        _common.select_allContainerData(cnt.uuid.PLANT_ALLOCATIONS)

        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
        });
        _common.select_allContainerData(cnt.uuid.PLANT_LOCATION)

        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
    })

    it("TC - Create job record in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000) // Added this wait as script is getting failed as loader is taking time to load
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS, 0);
            _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_JOBS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_JOBS.controllingunitfk], cnt.uuid.JOBS)

        });
        _common.clear_subContainerFilter(cnt.uuid.JOBS)
        _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.maximizeContainer(cnt.uuid.JOBS)

        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
        _common.select_activeRowInContainer(cnt.uuid.JOBS)


        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,CONTAINER_JOBS.BUSINESS_PARTNER);
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,Cypress.env(`API_CNT_CODE_1`));
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.create_newRecord(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,Cypress.env(`API_PROJECT_NUMBER_2`));
        _common.select_activeRowInContainer(cnt.uuid.JOBS)

        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,Cypress.env(`API_CNT_CODE_2`));
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,CONTAINER_JOBS.BUSINESS_PARTNER);
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()

        
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.JOBS, app.GridCells.CODE,app.InputFields.DOMAIN_TYPE_CODE,PROJECT_B_JOB_CODE);
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.minimizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create record in dispatching notes", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.create_newRecord(cnt.uuid.DISPATCHING_HEADER)
        cy.wait(2000) // Added this wait as script is getting failed as loader is taking time to load

        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, CONTAINERS_DISPATCHING_HEADER.EFFECTIVE_DATE[0])
        _common.clickOn_activeContainerButton(cnt.uuid.DISPATCHING_HEADER,btn.IconButtons.BTN_DEFAULT)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.DISPATCHING_HEADER, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, DISPATCHING_HEADER_1)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB1_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_PROJECT_NUMBER_1`))
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB2_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_PROJECT_NUMBER_2`))
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.create_newRecord(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        cy.wait(2000) // Added this wait as script is getting failed as loader is taking time to load
        _common.enterRecord_inNewRow(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, CONTAINERS_DISPATCHING_HEADER.EFFECTIVE_DATE[1])
        _common.clickOn_activeContainerButton(cnt.uuid.DISPATCHING_HEADER,btn.IconButtons.BTN_DEFAULT)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.DISPATCHING_HEADER, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, DISPATCHING_HEADER_2)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB1_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_PROJECT_NUMBER_1`))
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB2_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_B_JOB_CODE)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.DISPATCHING_HEADER)

        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, app.FooterTab.PERFORMING_JOB_PLANT_LOCATIONS);
        });
        _common.waitForLoaderToDisappear()
        
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_RECORD, CONTAINER_COLUMNS_DISPATCHING_RECORD)
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
            _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER,DISPATCHING_HEADER_1)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()

        _common.create_newRecord(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.RECORD_TYPE_FK, commonLocators.CommonKeys.GRID, CONTAINERS_DISPATCHING_HEADER.RECORD_TYPE)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_RECORD, app.GridCells.ARTICLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
        cy.wait(1000) //required wait to enable work operations type cell

        _common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.enterRecord_inNewRow(cnt.uuid.DISPATCHING_RECORD, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_DISPATCHING_HEADER.QUANTITY[0])
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRE_CALCULATED_WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_DISPATCHING_HEADER.PRE_CALCULATED_WORK_OPERATION_TYPE_FK)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()

        cy.wait(1000) //required wait to enable work operations type cell
        cy.SAVE()   
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
            _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER,DISPATCHING_HEADER_2)
            _common.waitForLoaderToDisappear()
        });

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()

        _common.create_newRecord(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.RECORD_TYPE_FK, commonLocators.CommonKeys.GRID, CONTAINERS_DISPATCHING_HEADER.RECORD_TYPE)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_RECORD, app.GridCells.ARTICLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
        cy.wait(1000) //required wait to enable work operations type cell

        _common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.enterRecord_inNewRow(cnt.uuid.DISPATCHING_RECORD, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_DISPATCHING_HEADER.QUANTITY[1])
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRE_CALCULATED_WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_DISPATCHING_HEADER.PRE_CALCULATED_WORK_OPERATION_TYPE_FK)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()

        cy.wait(1000) //required wait to enable work operations type cell
        cy.SAVE()   
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        });

        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER,DISPATCHING_HEADER_1)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE, "DISPATCH_CODE_1")
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_TOTAL_OC, "PRICE_TOTAL_OC_1")
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        });

        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER,DISPATCHING_HEADER_2)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE, "DISPATCH_CODE_2")
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_TOTAL_OC, "PRICE_TOTAL_OC_2")
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER,Cypress.env(`API_PROJECT_NUMBER_1`))
        _common.waitForLoaderToDisappear()

        _common.select_allContainerData(cnt.uuid.DISPATCHING_HEADER)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_ofMultipleRecord_fromModal(commonLocators.CommonKeys.TRANSPORT_PLANNING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_ofMultipleRecord_fromModal(commonLocators.CommonKeys.PICKING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_ofMultipleRecord_fromModal(commonLocators.CommonKeys.LOADED)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_ofMultipleRecord_fromModal(commonLocators.CommonKeys.DELIVERED)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create settlement in settlement module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.SETTLEMENT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.START_SETTLEMENT_BATCH)
        _common.inputField_fromModal(commonLocators.CommonElements.ROW, commonLocators.CommonLabels.DUE_DATE, 0, app.InputFields.INPUT_GROUP_CONTENT).clear({ force: true }).type("31/01/2024", { force: true })
        _common.waitForLoaderToDisappear()
        cy.wait(2000) // Added this wait as script is getting failed due to modal is taking time to load
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        cy.wait(2000) // Added this wait as script is getting failed due to modal is taking time to load
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)

        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_ITEMS, app.FooterTab.SETTLEMENT_ITEMS)
            _common.setup_gridLayout(cnt.uuid.SETTLEMENT_ITEMS, CONTAINER_COLUMNS_SETTLEMENT_ITEMS)
        });

    })

    it("TC - External/sales settlement should get created with settlement items for both dispatch notes even though they belong to different Jobs/Controlling Units", function () {
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        _common.clear_subContainerFilter(cnt.uuid.SETTLEMENT)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT,Cypress.env(`API_PROJECT_NUMBER_2`))
        _common.waitForLoaderToDisappear()
        cy.wait(2000)
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT,Cypress.env(`API_PROJECT_NUMBER_2`))
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT,app.GridCells.SETTLEMENT_STATUS_FK,commonLocators.CommonKeys.TEST_SALES_SETTLEMENT)
    })

    it("TC - There is a settlement item for each DN since they all belong to different jobs/controlling units", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_ITEMS, app.FooterTab.SETTLEMENT_ITEMS)
        });
        _common.clear_subContainerFilter(cnt.uuid.SETTLEMENT_ITEMS)
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT_ITEMS,Cypress.env(`DISPATCH_CODE_1`))
        cy.wait(2000) // Added this wait as script is getting failed as loader is taking time to load
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.DISPATCH_HEADER_FK,Cypress.env(`DISPATCH_CODE_1`))

        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_ITEMS, app.FooterTab.SETTLEMENT_ITEMS)
        });
        _common.clear_subContainerFilter(cnt.uuid.SETTLEMENT_ITEMS)
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT_ITEMS,Cypress.env(`DISPATCH_CODE_2`))
        cy.wait(2000) // Added this wait as script is getting failed as loader is taking time to load
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.DISPATCH_HEADER_FK,Cypress.env(`DISPATCH_CODE_2`))
    })

    it("TC - Verify Quantity, Quantity multiplier, Dispatch header, Total price", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_ITEMS, app.FooterTab.SETTLEMENT_ITEMS)
        });
        _common.clear_subContainerFilter(cnt.uuid.SETTLEMENT_ITEMS)
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT_ITEMS,Cypress.env(`DISPATCH_CODE_1`))
        cy.wait(2000) // Added this wait as script is getting failed as loader is taking time to load
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.QUANTITY_MULTIPLIER,CONTAINERS_DISPATCHING_HEADER.QUANTITY[0])
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.DISPATCH_HEADER_FK,Cypress.env(`DISPATCH_CODE_1`))
        _common.assert_forNumericValues(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.QUANTITY_SMALL,CONTAINERS_DISPATCHING_HEADER.SETTLEMENT_QUANTITY[0])
        let priceTotalFirst:any=parseFloat(Cypress.env(`PRICE_TOTAL_OC_1`).replace(',',"")).toFixed(2)
        let settlementFirst:any=parseFloat(CONTAINERS_DISPATCHING_HEADER.SETTLEMENT_QUANTITY[0]).toFixed(2)

        let priceTotalFinalFirst:any=priceTotalFirst*settlementFirst
        _common.assert_forNumericValues(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.PRICE_TOTAL_QTY,priceTotalFinalFirst.toString())

        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_ITEMS, app.FooterTab.SETTLEMENT_ITEMS)
        });
        _common.clear_subContainerFilter(cnt.uuid.SETTLEMENT_ITEMS)
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT_ITEMS,Cypress.env(`DISPATCH_CODE_2`))
        cy.wait(2000) // Added this wait as script is getting failed as loader is taking time to load

        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.QUANTITY_SMALL,CONTAINERS_DISPATCHING_HEADER.SETTLEMENT_QUANTITY[1])
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.DISPATCH_HEADER_FK,Cypress.env(`DISPATCH_CODE_2`))
        _common.assert_forNumericValues(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.QUANTITY_MULTIPLIER,CONTAINERS_DISPATCHING_HEADER.QUANTITY[1])

        let priceTotalSecond:any=parseFloat(Cypress.env(`PRICE_TOTAL_OC_2`).replace(',',"")).toFixed(2)
        let settlementSecond:any=parseFloat(CONTAINERS_DISPATCHING_HEADER.SETTLEMENT_QUANTITY[1]).toFixed(2)

        let priceTotalFinalSecond:any=priceTotalSecond*settlementSecond
        _common.assert_forNumericValues(cnt.uuid.SETTLEMENT_ITEMS,app.GridCells.PRICE_TOTAL_QTY,priceTotalFinalSecond.toString())
    })

})