using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using System;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;
using System.Collections.Generic;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using System.Linq;
using RIB.Visual.Basics.Common.Core;

namespace RIB.Visual.Documents.Project.BusinessComponents
{
	/// <summary>
	/// DocMetadata2extEntityLogic
	/// </summary>
	public class DocMetadata2extEntityLogic : DocSpCommonLogic<DocMetadata2extEntity>
	{
		/// <summary>
		/// GetEntityTableName
		/// </summary>
		/// <returns></returns>
		protected override string GetEntityTableName()
		{
			return "PRJ_DOC_METADATA2EXT";
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="metaDataType"></param>
		/// <returns></returns>
		public Dictionary<int, string> GetMetadata2extDicByType(MetaDataType metaDataType)
		{
			var metaDataId = (int)metaDataType;

			var result = GetByFilter(e => e.BasExternalsourceFk == ExternalSourceFk && e.Type == metaDataId)
				.ToDictionary(e => e.Objectid, e => e.ExternalId);

			return result;
		}

		/// <summary>
		/// key: prj id, value: site id
		/// </summary>
		/// <param name="prjIds"></param>
		/// <returns></returns>
		public Dictionary<int, string> GetSiteIdByPrjIds(IEnumerable<int> prjIds)
		{
			var prjMetaTypeId = (int)MetaDataType.Project;

			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var result = dbcontext.Entities<DocMetadata2extEntity>().Where(e => prjIds.Contains(e.Objectid) && e.Type == prjMetaTypeId
								&& e.BasExternalsourceFk == ExternalSourceFk).ToDictionary(e => e.Objectid, e => e.ExternalId);
				return result;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="prjIds"></param>
		/// <returns></returns>
		public IEnumerable<DocMetadata2extEntity> GetTeamByPrjIds(IEnumerable<int> prjIds)
		{
			var prjMetaTypeId = (int)MetaDataType.Team;

			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var result = dbcontext.Entities<DocMetadata2extEntity>().Where(e => prjIds.Contains(e.Objectid) && e.Type == prjMetaTypeId
								&& e.BasExternalsourceFk == ExternalSourceFk).ToList();
				return result;
			}
		}

		/// <summary>
		/// key: project id, value: channel folder driveitem id
		/// </summary>
		/// <param name="prjIds"></param>
		/// <returns></returns>
		public Dictionary<int, string> GetChannelFolderDicByPrjIds(IEnumerable<int> prjIds)
		{
			var prjMetaTypeId = (int)MetaDataType.ChannelFolder;

			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var result = dbcontext.Entities<DocMetadata2extEntity>().Where(e => prjIds.Contains(e.Objectid) && e.Type == prjMetaTypeId
								&& e.BasExternalsourceFk == ExternalSourceFk).ToDictionary(e => e.Objectid, e => e.ExternalId);
				return result;
			}
		}

		/// <summary>
		/// key: prj id, value: team id
		/// </summary>
		/// <param name="prjIds"></param>
		/// <returns></returns>
		public Dictionary<int, string> GetTeamIdByPrjIds(IEnumerable<int> prjIds)
		{
			return GetTeamByPrjIds(prjIds).ToDictionary(e => e.Objectid, e => e.ExternalId);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="detailStructIds"></param>
		/// <returns></returns>
		public Dictionary<int, string> GetSyncedFolderDic(IEnumerable<int> detailStructIds)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var result = dbcontext.Entities<DocMetadata2extEntity>().Where(e => detailStructIds.Contains(e.Objectid) && e.Type == (int)MetaDataType.Folder
							&& e.ExternalId != "" && e.BasExternalsourceFk == ExternalSourceFk).ToDictionary(e => e.Objectid, e => e.ExternalId);
				return result;
			}
		}
	}
}
