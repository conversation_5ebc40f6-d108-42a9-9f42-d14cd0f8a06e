import { app, tile, cnt, sidebar, btn } from "cypress/locators";
import commonLocators from "cypress/locators/common-locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _materialPage, _validate, _procurementContractPage } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const ALLURE = Cypress.Allure.reporter.getInterface()
const MATERIAL_CATALOG_CODE = "CAT-CD-" + Cypress._.random(0, 999);
const MATERIAL_CATALOG_DESC = "CAT-DESC-" + Cypress._.random(0, 999);
const MATERIAL_GROUPS_CODE = "GRP_CD-" + Cypress._.random(0, 999);
const MATERIAL_GROUPS_DESC = "GRP-DESC-" + Cypress._.random(0, 999);
const MATERIAL_RECORD_CODE = "MAT-CD-" + Cypress._.random(0, 999);
const MATERIAL_RECORD_DESC = "MAT-DESC-" + Cypress._.random(0, 999);

let CONTAINERS_DATA_TYPES, CONTAINERS_DATA_RECORDS, CONTAINERS_MATERIAL_CATALOGS, CONTAINERS_MATERIAL_CATALOG_FILTER, CONTAINERS_MATERIAL_RECORDS, CONTAINERS_CONTRACT, CONTAINERS_COMPANY;

let CONTAINER_COLUMNS_MATERIAL_CATALOGS, CONTAINER_COLUMNS_MATERIAL_GROUPS, CONTAINER_COLUMNS_CATLOG_TO_COMPANIES, CONTAINER_COLUMNS_MATERIAL_RECORDS, CONTAINER_COLUMNS_MATERIAL_CATALOG_FILTER, CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_ITEMS;

let MATERIAL_CATALOGS_PARAMETERS: DataCells, MATERIAL_GROUPS_PARAMETERS: DataCells, MATERIAL_RECORDS_PARAMETERS: DataCells, CONTRACT_PARAMETER: DataCells;

ALLURE.epic("PROCUREMENT AND BPM");
ALLURE.feature("Package");
ALLURE.story("PCM- 2.144 | Assign Catalog to Company and check result")
describe("PCM- 2.144 | Assign Catalog to Company and check result", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

    before(function () {

        cy.fixture("pcm/pcm-2.144-assign-catalog-to-company-and-check-result.json").then((data) => {
            this.data = data
            CONTAINERS_DATA_TYPES = this.data.CONTAINERS.DATA_TYPES
            CONTAINERS_DATA_RECORDS = this.data.CONTAINERS.DATA_RECORDS
            CONTAINER_COLUMNS_MATERIAL_CATALOGS = this.data.CONTAINER_COLUMNS.MATERIAL_CATALOGS
            CONTAINERS_MATERIAL_CATALOGS = this.data.CONTAINERS.MATERIAL_CATALOGS
            MATERIAL_CATALOGS_PARAMETERS = {
                [app.GridCells.CODE]: MATERIAL_CATALOG_CODE,
                [app.GridCells.BUSINESS_PARTNER_FK]: CONTAINERS_MATERIAL_CATALOGS.BUSINESS_PARTNER,
            }
            CONTAINER_COLUMNS_MATERIAL_GROUPS = this.data.CONTAINER_COLUMNS.MATERIAL_GROUPS
            MATERIAL_GROUPS_PARAMETERS = {
                [app.GridCells.CODE]: MATERIAL_GROUPS_CODE,
                [app.GridCells.DESCRIPTION_INFO]: MATERIAL_GROUPS_DESC,
                [app.GridCells.PRC_STRUCTURE_FK]: CommonLocators.CommonKeys.MATERIAL
            }
            CONTAINER_COLUMNS_CATLOG_TO_COMPANIES = this.data.CONTAINER_COLUMNS.CATLOG_TO_COMPANIES
            CONTAINERS_MATERIAL_CATALOG_FILTER = this.data.CONTAINERS.MATERIAL_CATALOG_FILTER
            CONTAINER_COLUMNS_MATERIAL_CATALOG_FILTER = this.data.CONTAINER_COLUMNS.MATERIAL_CATALOG_FILTER
            CONTAINER_COLUMNS_MATERIAL_RECORDS = this.data.CONTAINER_COLUMNS.MATERIAL_RECORDS
            CONTAINERS_MATERIAL_RECORDS = this.data.CONTAINERS.MATERIAL_RECORDS
            MATERIAL_RECORDS_PARAMETERS = {
                [app.GridCells.CODE]: MATERIAL_RECORD_CODE,
                [app.GridCells.DESCRIPTION_INFO_1]: MATERIAL_RECORD_DESC,
                [app.GridCells.UOM_FK]: CONTAINERS_MATERIAL_RECORDS.UOM,
                [app.GridCells.RETAIL_PRICE]: CONTAINERS_MATERIAL_RECORDS.RETAIL_PRICE,
                [app.GridCells.LIST_PRICE]: CONTAINERS_MATERIAL_RECORDS.LIST_PRICE
            }
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER
            };
            CONTAINER_COLUMNS_ITEMS = this.data.CONTAINER_COLUMNS.ITEMS
            CONTAINERS_COMPANY = this.data.CONTAINERS.COMPANY
        }).then(() => {
            cy.preLoading(Cypress.env("adminUserName"), Cypress.env("adminPassword"), Cypress.env("parentCompanyName"), Cypress.env("childCompanyName"));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
        })
    })

    after(() => {
        cy.LOGOUT();
    });

    it('TC - Verify create material catalog and Material groups', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES, 0);
        });
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CONTAINERS_DATA_TYPES.MATERIAL_CATALOG_TYPE)
        cy.REFRESH_CONTAINER()
        _common.select_rowHasValue(cnt.uuid.DATA_TYPES, CONTAINERS_DATA_TYPES.MATERIAL_CATALOG_TYPE)
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.INSTANCES, app.FooterTab.DATA_RECORDS, 1);
        });
        _common.clickOn_cellHasUniqueValue(cnt.uuid.INSTANCES, app.GridCells.DESCRIPTION_INFO, CONTAINERS_DATA_RECORDS.NEUTRAL_MATERIAL)
        _common.set_cellCheckboxValue(cnt.uuid.INSTANCES, app.GridCells.IS_FRAMEWORK, CommonLocators.CommonKeys.CHECK)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.INSTANCES, app.GridCells.DESCRIPTION_INFO, CONTAINERS_DATA_RECORDS.FRAMEWORK_AGREEMENTS)
        _common.set_cellCheckboxValue(cnt.uuid.INSTANCES, app.GridCells.IS_FRAMEWORK, CommonLocators.CommonKeys.CHECK)
        cy.SAVE().wait(1000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.MATERIAL_CATALOG);

        _common.openTab(app.TabBar.MATERIAL_CATALOG).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_CATALOGS, app.FooterTab.MATERIALCATALOG, 0);
            _common.setup_gridLayout(cnt.uuid.MATERIAL_CATALOGS, CONTAINER_COLUMNS_MATERIAL_CATALOGS)
        });
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_CATALOGS)
        _common.create_newRecord(cnt.uuid.MATERIAL_CATALOGS)
        _materialPage.enterRecord_toCreateMaterialCatalogs(cnt.uuid.MATERIAL_CATALOGS, MATERIAL_CATALOGS_PARAMETERS)
        cy.SAVE().wait(1000)
        _common.openTab(app.TabBar.MATERIAL_CATALOG).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_GROUPS, app.FooterTab.MATERIALGROUP, 3);
            _common.setup_gridLayout(cnt.uuid.MATERIAL_GROUPS, CONTAINER_COLUMNS_MATERIAL_GROUPS)
        });
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_GROUPS)
        _common.create_newRecord(cnt.uuid.MATERIAL_GROUPS)
        _materialPage.enterRecord_toCreateNewMaterialGroups(cnt.uuid.MATERIAL_GROUPS, MATERIAL_GROUPS_PARAMETERS)
        cy.SAVE().wait(1000)
        cy.REFRESH_CONTAINER()
    })

    it('TC - Verify set company for "Is Owner","Can Edit","Can Lookup"', function () {
        _common.openTab(app.TabBar.MATERIAL_CATALOG).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CATALOG_TO_COMPANIES, app.FooterTab.CATALOGS_TO_COMPANIES, 2);
            _common.setup_gridLayout(cnt.uuid.CATALOG_TO_COMPANIES, CONTAINER_COLUMNS_CATLOG_TO_COMPANIES)
        });
        _common.clear_subContainerFilter(cnt.uuid.CATALOG_TO_COMPANIES)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CATALOG_TO_COMPANIES, app.GridCells.COMPANY_NAME, Cypress.env("parentCompanyName"))
				_common.clickOn_expandCollapseButton(cnt.uuid.CATALOG_TO_COMPANIES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL);
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CATALOG_TO_COMPANIES, app.GridCells.COMPANY_NAME, Cypress.env("childCompanyName"))
        _common.set_cellCheckboxValue(cnt.uuid.CATALOG_TO_COMPANIES, app.GridCells.CAN_EDIT, CommonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CATALOG_TO_COMPANIES, app.GridCells.CAN_LOOKUP, CommonLocators.CommonKeys.CHECK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Verify create material records-1', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.MATERIAL);
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_CATALOG_FILTER, app.FooterTab.MATERIALFILTER, 0)
            _common.setup_gridLayout(cnt.uuid.MATERIAL_CATALOG_FILTER, CONTAINER_COLUMNS_MATERIAL_CATALOG_FILTER)
        });
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_CATALOG_FILTER)
        cy.REFRESH_CONTAINER()
        _common.search_inSubContainer(cnt.uuid.MATERIAL_CATALOG_FILTER, MATERIAL_CATALOG_CODE)
        _common.select_rowHasValue(cnt.uuid.MATERIAL_CATALOG_FILTER, MATERIAL_CATALOG_CODE)
        _common.assert_cellData_insideActiveRow(cnt.uuid.MATERIAL_CATALOG_FILTER, app.GridCells.CODE, MATERIAL_CATALOG_CODE)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.MATERIAL_CATALOG_FILTER, app.GridCells.CODE, MATERIAL_CATALOG_CODE)
        _common.set_cellCheckboxValue(cnt.uuid.MATERIAL_CATALOG_FILTER, app.GridCells.IS_CHECKED, CommonLocators.CommonKeys.CHECK)
    })

    it('TC - Verify create material records-2', function () {
        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_GROUP_FILTER, app.FooterTab.MATERIALGROUPFILTER, 1)
        });
        _common.search_inSubContainer(cnt.uuid.MATERIAL_GROUP_FILTER, MATERIAL_GROUPS_CODE)
        _common.assert_cellData_insideActiveRow(cnt.uuid.MATERIAL_GROUP_FILTER, app.GridCells.CODE, MATERIAL_GROUPS_CODE)
        _common.set_cellCheckboxValue(cnt.uuid.MATERIAL_GROUP_FILTER, app.GridCells.IS_CHECKED, CommonLocators.CommonKeys.CHECK)

        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_RECORDS, app.FooterTab.MATERIAL_RECORDS, 2)
            _common.setup_gridLayout(cnt.uuid.MATERIAL_RECORDS, CONTAINER_COLUMNS_MATERIAL_RECORDS)
        });
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_RECORDS)
        _common.create_newRecord(cnt.uuid.MATERIAL_RECORDS)
        _materialPage.enterRecord_toCreateNewMaterialRecord(cnt.uuid.MATERIAL_RECORDS, MATERIAL_RECORDS_PARAMETERS)
        cy.SAVE().wait(1000)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.MATERIAL_RECORDS, app.GridCells.DESCRIPTION_INFO_1, MATERIAL_RECORD_DESC)
        _validate.verify_inputFieldVisibility(cnt.uuid.MATERIAL_RECORDS, app.GridCells.DESCRIPTION_INFO_1, CommonLocators.CommonKeys.VISIBLE, app.InputFields.DOMAIN_TYPE_TRANSLATION)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.MATERIAL_RECORDS, app.GridCells.CODE, MATERIAL_RECORD_CODE)
        _validate.verify_inputFieldVisibility(cnt.uuid.MATERIAL_RECORDS, app.GridCells.CODE, CommonLocators.CommonKeys.VISIBLE, app.InputFields.DOMAIN_TYPE_CODE)
    })

    it('TC - Verify material catalog and the record can lookup', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        cy.wait(1000) //required wait to load page
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 1)
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _validate.verify_dataUnderStructurelookups(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.MATERIAL_CATALOG_FK, app.GridCells.CODE, MATERIAL_CATALOG_CODE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.ORDER_ITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 0)
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEMS)
        });
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT, app.GridCells.BAS_UOM_FK, CommonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_MATERIAL_RECORDS.UOM)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT, app.GridCells.MDC_MATERIAL_FK, CommonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, MATERIAL_RECORD_CODE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Verify switch company', function () {
        cy.switchCompany(Cypress.env("parentCompanyName"),"990 RIB Vanilla Daten")
        cy.wait(8000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.MATERIAL);
        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_CATALOG_FILTER, app.FooterTab.MATERIALFILTER, 0)
        });
        _common.search_inSubContainer(cnt.uuid.MATERIAL_CATALOG_FILTER, MATERIAL_CATALOG_CODE)
        _validate.verify_isRecordDeleted(cnt.uuid.MATERIAL_CATALOG_FILTER, MATERIAL_CATALOG_CODE)
    })


})

