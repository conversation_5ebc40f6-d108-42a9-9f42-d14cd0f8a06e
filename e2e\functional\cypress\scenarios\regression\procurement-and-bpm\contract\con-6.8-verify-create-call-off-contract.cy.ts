import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _commonAPI, _package, _procurementContractPage, _procurementPage, _projectPage, _schedulePage, _validate, _wizardCreateRequest } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
const CONTRACT_DESCRIPTION = _common.generateRandomString(4);
const PROJECT_NO = _common.generateRandomString(4);
const PROJECT_DESC = _common.generateRandomString(4);


let PROCUREMENT_CONTRACT_PARAMETER, PROJECTS_PARAMETERS: DataCells
let CONTAINER_COLUMNS_CONTRACT
let CONTAINERS_CONTRACT

describe("PCM- 6.8 | Verify create call off contract", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/con-6.8-verify-create-call-off-contract.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }
            PROJECTS_PARAMETERS = {
                [commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
                [commonLocators.CommonLabels.NAME]: PROJECT_DESC,
                [commonLocators.CommonLabels.CLERK]: CONTAINERS_CONTRACT.CLERK
            }

        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT)
                _common.waitForLoaderToDisappear()
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
            _common.create_newRecord(cnt.uuid.PROJECTS);
            _projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS);
            _common.waitForLoaderToDisappear()
            cy.SAVE();
            _common.waitForLoaderToDisappear()
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem();
        })
    });

       after(() => {
           cy.LOGOUT();
       });

    it("TC - Verify status option in customizing", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ENTITY_TYPES, app.FooterTab.DATA_TYPES, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.ENTITY_TYPES);
        cy.REFRESH_CONTAINER();
        _common.search_inSubContainer(cnt.uuid.ENTITY_TYPES, commonLocators.CommonLabels.CONTRACT_STATUS);
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.ENTITY_TYPES, app.GridCells.NAME, commonLocators.CommonLabels.CONTRACT_STATUS);
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.INSTANCES, app.FooterTab.DATA_RECORDS, 2);
        });
        _common.clear_subContainerFilter(cnt.uuid.INSTANCES);
        _common.clickOn_cellHasUniqueValue(cnt.uuid.INSTANCES, app.GridCells.DESCRIPTION_INFO, commonLocators.CommonKeys.RECORDED);
        _validate.customizing_DataRecordCheckBox(app.GridCells.IS_VIRTUAL, commonLocators.CommonKeys.UNCHECK);
        _validate.customizing_DataRecordCheckBox(app.GridCells.IS_CANCELED, commonLocators.CommonKeys.UNCHECK);
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.INSTANCES);
        _common.clickOn_cellHasUniqueValue(cnt.uuid.INSTANCES, app.GridCells.DESCRIPTION_INFO, commonLocators.CommonKeys.APPROVED);
        _validate.customizing_DataRecordCheckBox(app.GridCells.IS_VIRTUAL, commonLocators.CommonKeys.CHECK);
        _validate.customizing_DataRecordCheckBox(app.GridCells.IS_CANCELED, commonLocators.CommonKeys.CHECK);
        cy.SAVE();
        _common.waitForLoaderToDisappear()
    })
    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)

        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO)
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, CONTRACT_DESCRIPTION)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, CONTRACT_DESCRIPTION)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "MAIN_CONTRACT_CODE")

    })

    it("TC -  Verify call offs for recorded status record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem();
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.findRadio_byLabel_fromModal(commonLocators.CommonLabels.CALL_OFF_CONTRACT, commonLocators.CommonKeys.RADIO, 1, CommonLocators.CommonElements.PLATFORM_FORM_ROW)
        _common.clickOn_modalFooterButton(btn.ButtonText.NEXT)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open modal
        _validate.assert_labelText_directly_fromModal_ByClass(commonLocators.CommonElements.PLATFORM_FORM_ROW, app.InputFields.INPUT_GROUP_CONTENT, 0, PROJECT_NO)
        _validate.assert_labelText_directly_fromModal_ByClass(commonLocators.CommonElements.PLATFORM_FORM_ROW, app.InputFields.INPUT_GROUP_CONTENT, 1, PROJECT_DESC)
        _validate.validateData_fromModalPopUpIsVisibleOrNot(commonLocators.CommonLabels.MAIN_CONTRACT, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.VISIBLE_SMALL, app.GridCells.CODE, Cypress.env("MAIN_CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.CANCEL)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.findRadio_byLabel_fromModal(commonLocators.CommonLabels.CALL_OFF_CONTRACT, commonLocators.CommonKeys.RADIO, 1, CommonLocators.CommonElements.PLATFORM_FORM_ROW)
        _common.clickOn_modalFooterButton(btn.ButtonText.NEXT)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open modal
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.CON_HEADER_FK, Cypress.env("MAIN_CONTRACT_CODE"), commonLocators.CommonKeys.GRID)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.CREATE)
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, commonLocators.CommonKeys.CALL_OFF)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.PURCHASE_ORDERS,commonLocators.CommonKeys.CALL_OFF)
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.PURCHASE_ORDERS,commonLocators.CommonKeys.CALL_OFF)
        _validate.verify_checkBoxDisabled_forActiveCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.IS_FRAMEWORK)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.IS_FRAMEWORK, commonLocators.CommonKeys.UNCHECK)

    })
    it("TC - Verify call offs for approved status record", function () {
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("MAIN_CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
        _common.waitForLoaderToDisappear()
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED);
        cy.SAVE()
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("MAIN_CONTRACT_CODE"))
        _common.pinnedItem()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.findRadio_byLabel_fromModal(commonLocators.CommonLabels.CALL_OFF_CONTRACT, commonLocators.CommonKeys.RADIO, 1, CommonLocators.CommonElements.PLATFORM_FORM_ROW)
        _common.clickOn_modalFooterButton(btn.ButtonText.NEXT)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open modal
        _validate.assert_labelText_directly_fromModal_ByClass(commonLocators.CommonElements.PLATFORM_FORM_ROW, app.InputFields.INPUT_GROUP_CONTENT, 0, PROJECT_NO)
        _validate.assert_labelText_directly_fromModal_ByClass(commonLocators.CommonElements.PLATFORM_FORM_ROW, app.InputFields.INPUT_GROUP_CONTENT, 1, PROJECT_DESC)
        _validate.validateData_fromModalPopUpIsVisibleOrNot(commonLocators.CommonLabels.MAIN_CONTRACT, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.NOT_SPACE_VISIBLE, app.GridCells.CODE, Cypress.env("MAIN_CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()

        _common.clickOn_modalFooterButton(btn.ButtonText.CANCEL)
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("MAIN_CONTRACT_CODE"))
        _validate.verify_checkBoxDisabled_forActiveCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.IS_FRAMEWORK)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.IS_FRAMEWORK, commonLocators.CommonKeys.UNCHECK)

    })

});