﻿<?xml version="1.0" encoding="utf-8"?>
<Report ScriptLanguage="CSharp" StartReportEvent="_StartReport" ReportInfo.Created="10/23/2015 10:47:32" ReportInfo.Modified="07/21/2025 16:36:32" ReportInfo.CreatorVersion="2025.1.14.0">
  <ScriptText>using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Drawing;
using System.Data;
using FastReport;
using FastReport.Data;
using FastReport.Dialog;
using FastReport.Barcode;
using FastReport.Table;
using FastReport.Utils;

namespace FastReport
{
  public class ReportScript
  {
    
    private void _StartReport(object sender, EventArgs e)
    {
      updateSelectCommand(&quot;Annotations_General&quot;);
      updateSelectCommand(&quot;Snapshots&quot;);
      updateSelectCommand(&quot;Comments&quot;);
    }
    
    private void updateSelectCommand(string SourceName)
    {
      String filterKey = @&quot;'/*ASSFILTER*/'&quot;;
      TableDataSource table = Report.GetDataSource(SourceName) as TableDataSource;      // get the data source object
      String Param = Report.GetParameterValue(&quot;Mdl_AnnotationID&quot;).ToString();                      // get the list of IDS
      if(string.IsNullOrEmpty(Param))Param=&quot;0&quot;;
      table.SelectCommand=table.SelectCommand.Replace(filterKey, Param);

    }

    

    


    private void Rich1_AfterData(object sender, EventArgs e)
    {
      //Convert HTML to rtf and set rtf text to control.
      if(Report.GetColumnValue(&quot;Annotations_General.REMARK&quot;) == null)
      {
        Rich1.Text = string.Empty;
      }
      else
      {
        if(Report.GetColumnValue(&quot;Annotations_General.REMARK&quot;) != null)
        {
          string text = (string)Report.GetColumnValue(&quot;Annotations_General.REMARK&quot;);
          if(text != null)
          {
            try
            {
              string t =  ToRtf(text);
              if(string.IsNullOrWhiteSpace(t))
              {
                Rich1.Text = string.Empty;
              }
              else
              {
                Rich1.Text = t;
                
              }
            }
            catch(Exception ex)
            {
              Rich1.Text = ex.Message;
            }
          }
        }
      }
     }
    

    private void Picture2_BeforePrint(object sender, EventArgs e)
    {
      Picture2.ImageLocation=((String)Report.GetColumnValue(&quot;Annotations_General.SNAPSHOT_FILEFULLPATH&quot;));
    }

    

    private void Rich2_BeforePrint(object sender, EventArgs e)
    {
      //Convert HTML to rtf and set rtf text to control.
      if(Report.GetColumnValue(&quot;Comments.COMMENT&quot;) == null)
      {
        Rich2.Text = string.Empty;
      }
      else
      {
        if(Report.GetColumnValue(&quot;Comments.COMMENT&quot;) != null)
        {
          string text = (string)Report.GetColumnValue(&quot;Comments.COMMENT&quot;);
          if(text != null)
          {
            try
            {
              string t =  ToRtf(text);
              if(string.IsNullOrWhiteSpace(t))
              {
                Rich2.Text = string.Empty;
              }
              else
              {
                Rich2.Text = t;
                
              }
            }
            catch(Exception ex)
            {
              Rich2.Text = ex.Message;
            }
          }
        }
      }
    }
  }
}
</ScriptText>
  <Dictionary>
    <MsSqlDataConnection Name="Connection" ConnectionString="rijcmlqDu5C5bj2Eklj4gsb6cv3Qi5QTptfbvlJ/3KgTHR+UP15nw+AMz7KgDkoCSBYah287b9fP/DcwOZ/JMJn1vmkdWpMBFO5pEdoLig+5/dKv/jMcZSvTB76BJ3hOKjzS6f5jAtZ/6NWHPaGONrvmdZcHe+hkpnQiAcU0IFmu1Bd6ZOAzxjp4NnqJvkagfhFmiksedmNHX5iaUymf5rzK6Xx5pljjFUdpMDiNF717nShrW6xbu/tdss2xiBRSX1w5N10NXvxdjn9pfUBX7+rukvm8CzPJa1L7n9qENhSU2dD8WOrF58yt0JDlTit09s2FZun">
      <TableDataSource Name="Table2" Alias="Annotations_General" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="select CASE WHEN TYPE=0 THEN 'Annotation'&#13;&#10;WHEN TYPE=1 THEN 'Request for Information'&#13;&#10;WHEN TYPE=2 THEN 'Defect' &#13;&#10;WHEN TYPE=3 THEN 'Check List'&#13;&#10;WHEN TYPE=4 THEN 'Viewpoint'&#13;&#10;ELSE NULL END AS TYPE &#13;&#10;, ann_cat.DESCRIPTION as ANNOTATION_CATEGORY&#13;&#10;, ann_cat.DESCRIPTION_TR as ANNOTATION_CATEGORY_TR&#13;&#10;, ann.DESCRIPTION&#13;&#10;, ann.DESCRIPTION_TR&#13;&#10;, ann.REMARK&#13;&#10;, ann_stat.DESCRIPTION as STATUS&#13;&#10;, ann_stat.DESCRIPTION_TR as STATUS_TR&#13;&#10;, prty.DESCRIPTION as PRIORITY&#13;&#10;, prty.DESCRIPTION_TR as PRIORITY_TR&#13;&#10;, DUEDATE&#13;&#10;, prj.PROJECT_NAME&#13;&#10;, ann.INSERTED&#13;&#10;, ann.WHOISR&#13;&#10;, us.LOGONNAME&#13;&#10;, cl.FAMILY_NAME as CLERK&#13;&#10;, bp.BP_NAME1&#13;&#10;, con.FAMILY_NAME as CONTACT&#13;&#10;, mdl.DESCRIPTION as MODEL&#13;&#10;, mdl.CODE as MODEL_CODE&#13;&#10;, ann.ID as ANNOTATION_ID&#13;&#10;, def.DESCRIPTION as DEFECT&#13;&#10;, def_typ.DESCRIPTION as DEFECT_TYPE&#13;&#10;, def_typ.DESCRIPTION_TR as DEFECT_TYPE_TR&#13;&#10;, infreq.DESCRIPTION as RFI&#13;&#10;, rfityp.DESCRIPTION as RFI_TYPE&#13;&#10;, rfityp.DESCRIPTION_TR as RFI_TYPE_TR&#13;&#10;, TYPE as TYPE_NUMBER&#13;&#10;, vwpt.DESCRIPTION as VIEWPOINT&#13;&#10;, snap_root.ROOTPATH&#13;&#10;, snap.FILERELPATH&#13;&#10;, snap.FILEARCHIVEDOCID&#13;&#10;, snap_root.ROOTPATH + '\' + snap.FILERELPATH + '\' + snap.FILEARCHIVEDOCID as SNAPSHOT_FILEFULLPATH&#13;&#10;from MDL_ANNOTATION ann&#13;&#10;left join MDL_MODEL mdl&#13;&#10;on ann.MDL_MODEL_FK=mdl.ID&#13;&#10;left join PRJ_PROJECT prj&#13;&#10;on mdl.PRJ_PROJECT_FK=prj.ID&#13;&#10;left join MDL_ANNOCATEGORY ann_cat&#13;&#10;on ann.MDL_ANNOCATEGORY_FK=ann_cat.ID&#13;&#10;left join DFM_DEFECT def&#13;&#10;on ann.DFM_DEFECT_FK=def.ID&#13;&#10;left join BAS_DEFECT_TYPE def_typ&#13;&#10;on def.BAS_DEFECT_TYPE_FK=def_typ.ID&#13;&#10;left join MDL_VIEWPOINT vwpt&#13;&#10;on ann.MDL_VIEWPOINT_FK=vwpt.ID&#13;&#10;left join MDL_ANNOSTATUS ann_stat&#13;&#10;on ann.MDL_ANNOSTATUS_FK=ann_stat.ID&#13;&#10;left join BAS_PRIORITY prty&#13;&#10;on ann.BAS_PRIORITY_FK=prty.ID&#13;&#10;left join BAS_CLERK cl&#13;&#10;on ann.BAS_CLERK_FK=cl.ID&#13;&#10;left join BPD_BUSINESSPARTNER bp&#13;&#10;on ann.BPD_BUSINESSPARTNER_FK=bp.ID&#13;&#10;left join BPD_CONTACT con&#13;&#10;on ann.BPD_CONTACT_FK=con.ID&#13;&#10;left join PRJ_INFOREQUEST infreq&#13;&#10;on ann.PRJ_INFOREQUEST_FK=infreq.ID&#13;&#10;left join PRJ_RFITYPE rfityp&#13;&#10;on infreq.PRJ_RFITYPE_FK=rfityp.ID&#13;&#10;left join FRM_USER us&#13;&#10;on ann.WHOISR=us.ID&#13;&#10;left join MDL_ANNOCAMERA annocam&#13;&#10;on ann.ID=annocam.MDL_ANNOTATION_FK&#13;&#10;left join BAS_FILEARCHIVEDOC snap&#13;&#10;on annocam.BAS_FILEARCHIVEDOC_IMG_FK=snap.ID&#13;&#10;left join BAS_FILEARCHIVEROOT snap_root&#13;&#10;on snap.FILEARCHIVEROOT_FK=snap_root.FILEARCHIVEROOTID&#13;&#10;&#13;&#10;where ann.ID IN ('/*ASSFILTER*/') and TYPE=0 and snap_root.ROOTPATH is not null&#13;&#10;&#13;&#10;">
        <Column Name="TYPE" DataType="System.String"/>
        <Column Name="ANNOTATION_CATEGORY" DataType="System.String"/>
        <Column Name="ANNOTATION_CATEGORY_TR" DataType="System.Int32"/>
        <Column Name="DESCRIPTION" DataType="System.String"/>
        <Column Name="DESCRIPTION_TR" DataType="System.Int32"/>
        <Column Name="REMARK" DataType="System.String"/>
        <Column Name="STATUS" DataType="System.String"/>
        <Column Name="STATUS_TR" DataType="System.Int32"/>
        <Column Name="PRIORITY" DataType="System.String"/>
        <Column Name="PRIORITY_TR" DataType="System.Int32"/>
        <Column Name="DUEDATE" DataType="System.DateTime"/>
        <Column Name="PROJECT_NAME" DataType="System.String"/>
        <Column Name="INSERTED" DataType="System.DateTime"/>
        <Column Name="WHOISR" DataType="System.Int32"/>
        <Column Name="CLERK" DataType="System.String"/>
        <Column Name="BP_NAME1" DataType="System.String"/>
        <Column Name="CONTACT" DataType="System.String"/>
        <Column Name="MODEL" DataType="System.String"/>
        <Column Name="ANNOTATION_ID" DataType="System.Int32"/>
        <Column Name="DEFECT" DataType="System.String"/>
        <Column Name="DEFECT_TYPE" DataType="System.String"/>
        <Column Name="DEFECT_TYPE_TR" DataType="System.Int32"/>
        <Column Name="RFI" DataType="System.String"/>
        <Column Name="RFI_TYPE" DataType="System.String"/>
        <Column Name="RFI_TYPE_TR" DataType="System.Int32"/>
        <Column Name="TYPE_NUMBER" DataType="System.Int32"/>
        <Column Name="VIEWPOINT" DataType="System.String"/>
        <Column Name="SNAPSHOT_FILEFULLPATH" DataType="System.String"/>
        <Column Name="MODEL_CODE" DataType="System.String"/>
        <Column Name="LOGONNAME" DataType="System.String"/>
        <Column Name="ROOTPATH" DataType="System.String"/>
        <Column Name="FILERELPATH" DataType="System.String"/>
        <Column Name="FILEARCHIVEDOCID" DataType="System.String"/>
      </TableDataSource>
      <TableDataSource Name="Table3" Alias="Comments" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="select ann_com.INSERTED&#13;&#10;, com.COMMENT&#13;&#10;, ann_com.WHOISR&#13;&#10;, MDL_ANNOTATION_FK&#13;&#10;, us.DESCRIPTION as WHOISR_DESC&#13;&#10;, us.LOGONNAME&#13;&#10;from MDL_ANNOCOMMENT ann_com&#13;&#10;left join BAS_COMMENT com&#13;&#10;on ann_com.BAS_COMMENT_FK=com.ID&#13;&#10;left join FRM_USER us&#13;&#10;on ann_com.WHOISR=us.ID&#13;&#10;&#13;&#10;where MDL_ANNOTATION_FK IN ('/*ASSFILTER*/')">
        <Column Name="INSERTED" DataType="System.DateTime"/>
        <Column Name="COMMENT" DataType="System.String"/>
        <Column Name="WHOISR" DataType="System.Int32"/>
        <Column Name="MDL_ANNOTATION_FK" DataType="System.Int32"/>
        <Column Name="WHOISR_DESC" DataType="System.String"/>
        <Column Name="LOGONNAME" DataType="System.String"/>
      </TableDataSource>
      <TableDataSource Name="Table4" Alias="Snapshots" DataType="System.Int32" Enabled="true" ForceLoadData="true" SelectCommand="select MDL_ANNOTATION_FK&#13;&#10;, snap_root.ROOTPATH + '\' + snap.FILERELPATH + '\' + snap.FILEARCHIVEDOCID as SNAPSHOT_FILEFULLPATH&#13;&#10;from MDL_ANNOCAMERA annocam&#13;&#10;left join BAS_FILEARCHIVEDOC snap&#13;&#10;on annocam.BAS_FILEARCHIVEDOC_IMG_FK=snap.ID&#13;&#10;left join BAS_FILEARCHIVEROOT snap_root&#13;&#10;on snap.FILEARCHIVEROOT_FK=snap_root.FILEARCHIVEROOTID&#13;&#10;&#13;&#10;where annocam.MDL_ANNOTATION_FK IN ('/*ASSFILTER*/')-- and TYPE=0">
        <Column Name="MDL_ANNOTATION_FK" DataType="System.Int32"/>
        <Column Name="SNAPSHOT_FILEFULLPATH" DataType="System.String"/>
      </TableDataSource>
      <TableDataSource Name="Table" Alias="Company Info" DataType="System.Int32" Enabled="true" SelectCommand="select c.ID,c.CODE,&#13;&#10;       c.COMPANY_NAME as CompanyName,&#13;&#10;       a.ADDRESS_LINE as AddressLine, &#13;&#10;       b.CONTENT        as Logo,&#13;&#10;       cu.CURRENCY&#13;&#10;&#13;&#10; from BAS_COMPANY c&#13;&#10;  left join BAS_ADDRESS a on c.BAS_ADDRESS_FK = a.ID &#13;&#10;  left join BAS_BLOBS b  on b.ID = c.BAS_BLOBS_FK&#13;&#10;  left join BAS_CURRENCY cu on c.BAS_CURRENCY_FK = cu.ID &#13;&#10;&#13;&#10;&#13;&#10;&#13;&#10;where c.ID =@CompanyID&#13;&#10;">
        <Column Name="ID" DataType="System.Int32"/>
        <Column Name="CODE" DataType="System.String"/>
        <Column Name="CompanyName" DataType="System.String"/>
        <Column Name="AddressLine" DataType="System.String"/>
        <Column Name="Logo" DataType="System.Byte[]" BindableControl="Picture"/>
        <Column Name="CURRENCY" DataType="System.String"/>
        <CommandParameter Name="CompanyID" DataType="8" Expression="[CompanyID]" DefaultValue="1"/>
      </TableDataSource>
      <TableDataSource Name="Table1" Alias="Footer" DataType="System.Int32" Enabled="true" SelectCommand="WITH CTE AS &#13;&#10;(SELECT ID,CLIENT_FK,NAME FROM FRM_CLIENT WHERE ID=@CompanyID &#13;&#10;&#13;&#10;UNION ALL&#13;&#10;&#13;&#10;SELECT FRM_CLIENT.ID,FRM_CLIENT.CLIENT_FK,FRM_CLIENT.NAME FROM CTE&#13;&#10;JOIN FRM_CLIENT ON CTE.CLIENT_FK=FRM_CLIENT.ID)  &#13;&#10;&#13;&#10;SELECT CTE.*,&#13;&#10;FRM_ACCESSGROUP.NAME AS USER_GROUP,&#13;&#10;FRM_ACCESSROLE.NAME AS USER_ROLE,&#13;&#10;FRM_USER.ID AS USER_ID,&#13;&#10;FRM_USER.NAME,&#13;&#10;FRM_USER.LOGONNAME &#13;&#10;&#13;&#10;FROM CTE &#13;&#10;INNER JOIN FRM_ACCESSGROUP2ROLE ON FRM_ACCESSGROUP2ROLE.CLIENT_FK=CTE.ID &#13;&#10;LEFT JOIN FRM_ACCESSROLE ON FRM_ACCESSROLE.ID=FRM_ACCESSGROUP2ROLE.ACCESSROLE_FK&#13;&#10;LEFT JOIN FRM_ACCESSUSER2GROUP ON FRM_ACCESSUSER2GROUP.ACCESSGROUP_FK=FRM_ACCESSGROUP2ROLE.ACCESSGROUP_FK&#13;&#10;LEFT JOIN FRM_ACCESSGROUP ON FRM_ACCESSGROUP2ROLE.ACCESSGROUP_FK=FRM_ACCESSGROUP.ID&#13;&#10;LEFT JOIN FRM_USER ON FRM_USER.ID=FRM_ACCESSUSER2GROUP.USER_FK&#13;&#10;&#13;&#10;WHERE FRM_USER.ID=@UserID  AND CTE.ID =@CompanyID&#13;&#10;">
        <Column Name="ID" DataType="System.Int32"/>
        <Column Name="CLIENT_FK" DataType="System.Int32"/>
        <Column Name="NAME" DataType="System.String"/>
        <Column Name="USER_GROUP" DataType="System.String"/>
        <Column Name="USER_ROLE" DataType="System.String"/>
        <Column Name="USER_ID" DataType="System.Int32"/>
        <Column Name="NAME1" DataType="System.String"/>
        <Column Name="LOGONNAME" DataType="System.String"/>
        <CommandParameter Name="UserID" DataType="8" Expression="[UserID]" DefaultValue="1"/>
        <CommandParameter Name="CompanyID" DataType="8" Expression="[CompanyID]" DefaultValue="1"/>
      </TableDataSource>
    </MsSqlDataConnection>
    <Parameter Name="CompanyID" DataType="System.Int32" AsString="" UsedInOptions="true"/>
    <Parameter Name="UserID" DataType="System.Int32" AsString="" UsedInOptions="true"/>
    <Parameter Name="Mdl_AnnotationID" DataType="System.String" Expression="&quot;1004661,1004662,1004663,1004664&quot;" UsedInOptions="true"/>
  </Dictionary>
  <ReportPage Name="Page1" Landscape="true" PaperWidth="297" PaperHeight="210" RawPaperSize="9" Guides="0,415.8,869.4,841.05,302.4,444.15,831.6" Watermark.Font="Arial, 60pt">
    <PageHeaderBand Name="PageHeader1" Width="1047.06" Height="75.6" Border.Lines="Bottom" Guides="75.6,103.95,0,18.9">
      <TextObject Name="Text2" Top="18.9" Width="444.15" Height="28.35" Text="Model Annotation Report" Font="Tahoma, 14pt, style=Bold"/>
      <TextObject Name="Text1" Width="756" Height="18.9" Text="[Company Info.CODE] [Company Info.CompanyName] ([Company Info.CURRENCY])" Font="Tahoma, 9pt, style=Bold"/>
      <PictureObject Name="Picture1" Left="878.85" Width="160.65" Height="64.26" DataColumn="Company Info.Logo"/>
    </PageHeaderBand>
    <DataBand Name="Data1" Top="79.6" Width="1047.06" Height="226.8" CanGrow="true" DataSource="Table2" PrintIfDetailEmpty="true">
      <TextObject Name="Text6" Left="387.45" Top="28.35" Width="283.5" Height="28.35" Border.Lines="Top" CanGrow="true" Text="[Annotations_General.TYPE]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text7" Left="302.4" Top="28.35" Width="85.05" Height="28.35" Border.Lines="Left, Top" CanGrow="true" Text="Type:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text8" Left="302.4" Top="56.7" Width="85.05" Height="28.35" Border.Lines="Left" CanGrow="true" Text="Category:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text9" Left="302.4" Top="85.05" Width="85.05" Height="28.35" Border.Lines="Left" CanGrow="true" Text="Description:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text11" Left="302.4" Top="113.4" Width="85.05" Height="28.35" Border.Lines="Left" CanGrow="true" Text="Status:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text12" Left="302.4" Top="141.75" Width="85.05" Height="28.35" Border.Lines="Left" CanGrow="true" Text="Priority:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text13" Left="302.4" Top="170.1" Width="85.05" Height="28.35" Border.Lines="Left" CanGrow="true" Text="Due Date:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text14" Left="387.45" Top="56.7" Width="283.5" Height="28.35" CanGrow="true" Text="[IIf([Annotations_General.TYPE_NUMBER]==0,Translate([Annotations_General.ANNOTATION_CATEGORY],[Annotations_General.ANNOTATION_CATEGORY_TR]),IIf([Annotations_General.TYPE_NUMBER]==1,Translate([Annotations_General.RFI_TYPE],[Annotations_General.RFI_TYPE_TR]),IIf([Annotations_General.TYPE_NUMBER]==2,Translate([Annotations_General.DEFECT_TYPE],[Annotations_General.DEFECT_TYPE_TR]),&quot;&quot;)))]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text15" Left="387.45" Top="85.05" Width="283.5" Height="28.35" CanGrow="true" Text="[Translate([Annotations_General.DESCRIPTION],[Annotations_General.DESCRIPTION_TR])]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text17" Left="387.45" Top="113.4" Width="283.5" Height="28.35" CanGrow="true" Text="[Translate([Annotations_General.STATUS],[Annotations_General.STATUS_TR])]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text18" Left="387.45" Top="141.75" Width="283.5" Height="28.35" CanGrow="true" Text="[Translate([Annotations_General.PRIORITY],[Annotations_General.PRIORITY_TR])]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text19" Left="387.45" Top="170.1" Width="283.5" Height="28.35" CanGrow="true" Text="[Annotations_General.DUEDATE]" Format="Date" Format.Format="d" Font="Tahoma, 9pt" HideNullFields="true"/>
      <TextObject Name="Text21" Left="670.95" Top="56.7" Width="85.05" Height="28.35" CanGrow="true" Text="Model:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text22" Left="670.95" Top="85.05" Width="85.05" Height="28.35" CanGrow="true" Text="Inserted At:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text23" Left="670.95" Top="113.4" Width="85.05" Height="28.35" CanGrow="true" Text="Inserted by:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text24" Left="670.95" Top="141.75" Width="85.05" Height="28.35" CanGrow="true" Text="Clerk:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text25" Left="670.95" Top="170.1" Width="85.05" Height="28.35" CanGrow="true" Text="BP:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text26" Left="670.95" Top="198.45" Width="85.05" Height="28.35" Border.Lines="Bottom" CanGrow="true" Text="BP contact:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text20" Left="670.95" Top="28.35" Width="85.05" Height="28.35" Border.Lines="Top" CanGrow="true" Text="Project:" Font="Tahoma, 9pt, style=Bold"/>
      <TextObject Name="Text27" Left="756" Top="28.35" Width="291.06" Height="28.35" Border.Lines="Right, Top" CanGrow="true" Text="[Annotations_General.PROJECT_NAME]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text28" Left="756" Top="85.05" Width="291.06" Height="28.35" Border.Lines="Right" CanGrow="true" GrowToBottom="true" Text="[Annotations_General.INSERTED]" Format="Date" Format.Format="d" Font="Tahoma, 9pt"/>
      <TextObject Name="Text29" Left="756" Top="113.4" Width="291.06" Height="28.35" Border.Lines="Right" CanGrow="true" Text="[Annotations_General.LOGONNAME]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text30" Left="756" Top="141.75" Width="291.06" Height="28.35" Border.Lines="Right" CanGrow="true" Text="[Annotations_General.CLERK]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text31" Left="756" Top="170.1" Width="291.06" Height="28.35" Border.Lines="Right" CanGrow="true" Text="[Annotations_General.BP_NAME1]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text32" Left="756" Top="198.45" Width="291.06" Height="28.35" Border.Lines="Right, Bottom" CanGrow="true" Text="[Annotations_General.CONTACT]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text33" Left="756" Top="56.7" Width="291.06" Height="28.35" Border.Lines="Right" CanGrow="true" Text="[Annotations_General.MODEL_CODE]" Font="Tahoma, 9pt"/>
      <PictureObject Name="Picture2" Top="28.35" Width="302.4" Height="198.45" Border.Lines="All" CanGrow="true" GrowToBottom="true" BeforePrintEvent="Picture2_BeforePrint" Image=""/>
      <TextObject Name="Text40" Left="302.4" Top="198.45" Width="368.55" Height="28.35" Border.Lines="Bottom" Font="Arial, 10pt"/>
      <ChildBand Name="Child1" Top="310.4" Width="1047.06" Height="47.25" CanGrow="true">
        <TextObject Name="Text10" Top="9.45" Width="85.05" Height="37.8" Border.Lines="Left, Top, Bottom" CanGrow="true" GrowToBottom="true" Text="Remark:" Font="Tahoma, 9pt, style=Bold"/>
        <RichObject Name="Rich1" Left="85.05" Top="9.45" Width="962.01" Height="37.8" Border.Lines="Right, Top, Bottom" CanGrow="true" AfterDataEvent="Rich1_AfterData" ConvertRichText="true"/>
      </ChildBand>
      <DataBand Name="Data2" Top="394" Width="1047.06" Height="56.7" CanGrow="true" DataSource="Table3" Filter="[Comments.MDL_ANNOTATION_FK]==[Annotations_General.ANNOTATION_ID]">
        <TextObject Name="Text34" Width="94.5" Height="56.7" Border.Lines="All" CanGrow="true" GrowToBottom="true" Text="[Comments.INSERTED]" Format="Date" Format.Format="d" Font="Tahoma, 9pt"/>
        <TextObject Name="Text39" Left="888.3" Width="158.76" Height="56.7" Border.Lines="Right, Top, Bottom" CanGrow="true" GrowToBottom="true" Text="[Comments.LOGONNAME]" Font="Tahoma, 9pt"/>
        <RichObject Name="Rich2" Left="94.5" Width="793.8" Height="56.7" Border.Lines="All" CanGrow="true" GrowToBottom="true" BeforePrintEvent="Rich2_BeforePrint" ConvertRichText="true"/>
        <DataHeaderBand Name="DataHeader1" Top="361.65" Width="1047.06" Height="28.35" CanGrow="true" KeepWithData="true" RepeatOnEveryPage="true">
          <TextObject Name="Text35" Top="9.45" Width="94.5" Height="18.9" Border.Lines="All" CanGrow="true" GrowToBottom="true" Text="Inserted At:" Font="Tahoma, 9pt, style=Bold"/>
          <TextObject Name="Text36" Left="888.3" Top="9.45" Width="158.76" Height="18.9" Border.Lines="Right, Top, Bottom" CanGrow="true" GrowToBottom="true" Text="Inserted by:" Font="Tahoma, 9pt, style=Bold"/>
          <TextObject Name="Text37" Left="94.5" Top="9.45" Width="793.8" Height="18.9" Border.Lines="Right, Top, Bottom" CanGrow="true" GrowToBottom="true" Text="Comment:" Font="Tahoma, 9pt, style=Bold"/>
        </DataHeaderBand>
      </DataBand>
    </DataBand>
    <PageFooterBand Name="PageFooter1" Top="454.7" Width="1047.06" Height="37.8" Border.Lines="Top" Guides="0,18.9,37.8">
      <TextObject Name="Text3" Width="415.8" Height="18.9" Text="[Footer.LOGONNAME]" Font="Tahoma, 9pt"/>
      <TextObject Name="Text4" Top="18.9" Width="415.8" Height="18.9" Text="[Date] [Format(&quot;{0:HH:mm}&quot;,[Date])] " Format="Date" Format.Format="d" Font="Tahoma, 9pt"/>
      <TextObject Name="Text5" Left="841.05" Top="18.9" Width="207.9" Height="18.9" Text="[Page]/[TotalPages#]" HorzAlign="Right" Font="Tahoma, 9pt"/>
    </PageFooterBand>
  </ReportPage>
</Report>
