/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { IEntityContainerBehavior, IGridContainerLink } from '@libs/ui/business-base';
import { ICharacteristicGroupEntity } from '@libs/basics/interfaces';
import { BasicsCharacteristicGroupDataService } from '../services/basics-characteristic-group-data.service';

/**
 * characteristic group behavior service
 */
@Injectable({
	providedIn: 'root',
})
export class BasicsCharacteristicGroupBehavior implements IEntityContainerBehavior<IGridContainerLink<ICharacteristicGroupEntity>, ICharacteristicGroupEntity> {
	private dataService: BasicsCharacteristicGroupDataService;

	public constructor() {
		this.dataService = inject(BasicsCharacteristicGroupDataService);
	}

	public onCreate(containerLink: IGridContainerLink<ICharacteristicGroupEntity>): void {
		// TODO: bug? refresh and filer are different approaches
		// Performance issue: this solution will cause reload the container again once switch container back
		//this.dataService.filter(this.searchPayload).then((data) => (containerLink.gridData = data.dtos));
	}
}
