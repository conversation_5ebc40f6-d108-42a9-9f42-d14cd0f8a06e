import apiConstantData from "cypress/constantData/apiConstantData";
import { tile, sidebar, commonLocators, app, cnt, btn } from "cypress/locators";
import { _common, _commonAPI, _controllingUnit, _package, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const INVOICE_NO_1 = _common.generateRandomString(4)
const INVOICE_NO_2 = _common.generateRandomString(4)
const INVOICE_NO_3 = _common.generateRandomString(4)
const DATA_TYPE_DESC = _common.generateRandomString(4);
const DATA_TYPE_CODE = _common.generateRandomString(4);

let CONTAINER_COLUMNS_INVOICE
let CONTAINERS_INVOICE
let CONTAINER_COLUMNS_CONTROLLING_UNITS
let CONTAINERS_CONTROLLING_UNITS
let CONTROLLING_UNIT_A_PARAMETERS: DataCells
let CONTAINERS_DATA_TYPES

describe("PCM- 4.383 | Wizard change invoice status in invoice module", () => {
    before(function () {
        cy.fixture("pcm/inv-4.383-wizard-change-invoice-status-in-invoice-module.json").then((data) => {
            this.data = data;
            CONTAINERS_DATA_TYPES = this.data.CONTAINERS.DATA_TYPES
            CONTAINER_COLUMNS_INVOICE = this.data.CONTAINER_COLUMNS.INVOICE
            CONTAINERS_INVOICE = this.data.CONTAINERS.INVOICE
            CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNIT
            CONTAINER_COLUMNS_CONTROLLING_UNITS = this.data.CONTAINER_COLUMNS.CONTROLLING_UNITS;
            CONTROLLING_UNIT_A_PARAMETERS = {
                [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
                [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true", "true"]
            }
        })
            .then(() => {
                cy.preLoading(
                    Cypress.env("adminUserName"), Cypress.env("adminPassword"), Cypress.env("parentCompanyName"), Cypress.env("childCompanyName")
                )
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                _common.waitForLoaderToDisappear()
                _commonAPI.getAccessToken()
                    .then((result) => {
                        cy.log(`Token Retrieved: ${result.token}`);
                    });
            })
    });

    after(() => {
        cy.LOGOUT();
    });

    it("TC - API: Create project and controlling unit", function () {
        _commonAPI.createProject()
            .then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
                _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_A_PARAMETERS)
            });
    })

    it("TC - Create invoice record in invoice container", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.INVOICE);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.INVOICEHEADER, app.FooterTab.INVOICEHEADER, 1)
            _common.setup_gridLayout(cnt.uuid.INVOICEHEADER, CONTAINER_COLUMNS_INVOICE);
            _common.set_columnAtTop([CONTAINER_COLUMNS_INVOICE.invstatusfk, CONTAINER_COLUMNS_INVOICE.businesspartnerfk, CONTAINER_COLUMNS_INVOICE.code, CONTAINER_COLUMNS_INVOICE.controllingunitfk, CONTAINER_COLUMNS_INVOICE.prcstructurefk], cnt.uuid.INVOICEHEADER)
        })
        _common.maximizeContainer(cnt.uuid.INVOICEHEADER)
        _common.clear_subContainerFilter(cnt.uuid.INVOICEHEADER)
        _common.create_newRecord(cnt.uuid.INVOICEHEADER)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.INVOICEHEADER, app.GridCells.REFERENCE, app.InputFields.DOMAIN_TYPE_DESCRIPTION, INVOICE_NO_1)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.BUSINESS_PARTNER)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.PRC_CONFIGURATION_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.CONFIGURATION)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`)
        )
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.PRC_STRUCTURE_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.STRUCTURE_CODE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.INVOICEHEADER)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.INVOICEHEADER, app.GridCells.REFERENCE, app.InputFields.DOMAIN_TYPE_DESCRIPTION, INVOICE_NO_2)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.BUSINESS_PARTNER)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.PRC_CONFIGURATION_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.CONFIGURATION)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`)
        )
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.PRC_STRUCTURE_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.STRUCTURE_CODE)
        cy.SAVE()
        _common.create_newRecord(cnt.uuid.INVOICEHEADER)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.INVOICEHEADER, app.GridCells.REFERENCE, app.InputFields.DOMAIN_TYPE_DESCRIPTION, INVOICE_NO_3)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.BUSINESS_PARTNER)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.PRC_CONFIGURATION_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.CONFIGURATION)
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`)
        )
        _common.edit_dropdownCellWithInput(cnt.uuid.INVOICEHEADER, app.GridCells.PRC_STRUCTURE_FK, commonLocators.CommonKeys.DIV, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_INVOICE.STRUCTURE_CODE)
        cy.SAVE()
    });

    it("TC - Verify to change multiple reocrd by wizard at one time ", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.select_allContainerData(cnt.uuid.INVOICEHEADER)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_INVOICE_STATUS);
        _common.changeStatus_ofMultipleRecord_fromModal(commonLocators.CommonKeys.TO_BE_APPROVED);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.INVOICEHEADER, INVOICE_NO_1)
        _common.assert_cellData_insideActiveRow(cnt.uuid.INVOICEHEADER, app.GridCells.INV_STATUS_FK, commonLocators.CommonKeys.TO_BE_APPROVED);
        _common.select_rowHasValue(cnt.uuid.INVOICEHEADER, INVOICE_NO_2)
        _common.assert_cellData_insideActiveRow(cnt.uuid.INVOICEHEADER, app.GridCells.INV_STATUS_FK, commonLocators.CommonKeys.TO_BE_APPROVED);
        _common.select_rowHasValue(cnt.uuid.INVOICEHEADER, INVOICE_NO_3)
        _common.assert_cellData_insideActiveRow(cnt.uuid.INVOICEHEADER, app.GridCells.INV_STATUS_FK, commonLocators.CommonKeys.TO_BE_APPROVED);
    });

    it("TC - Verify to change one reocrd by wizard at one time ", function () {
        _common.select_rowHasValue(cnt.uuid.INVOICEHEADER, INVOICE_NO_3)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_INVOICE_STATUS);
        _common.changeStatus_fromModal(commonLocators.CommonKeys.POSTED);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.select_rowHasValue(cnt.uuid.INVOICEHEADER, INVOICE_NO_3)
        _common.assert_cellData_insideActiveRow(cnt.uuid.INVOICEHEADER, app.GridCells.INV_STATUS_FK, commonLocators.CommonKeys.POSTED);
        cy.SAVE()
    });

    it('TC - Verify message added to history of invoice', function () {
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.INVOICEHEADER, app.FooterTab.INVOICEHEADER, 0)
            _common.clear_subContainerFilter(cnt.uuid.INVOICEHEADER)
        })
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, INVOICE_NO_1);
        _common.select_rowInContainer(cnt.uuid.INVOICEHEADER)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_INVOICE_STATUS);
        _common.changeStatus_fromModal(commonLocators.CommonKeys.POSTED, CONTAINERS_INVOICE.BUSINESS_PARTNER)
        cy.SAVE()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.INVOICEHEADER, commonLocators.CommonKeys.POSTED)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_INVOICE_STATUS);
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.HISTORY)
        _common.clickOn_cellHasValue_fromModal(app.GridCells.REMARK, CONTAINERS_INVOICE.BUSINESS_PARTNER)
        _validate.validate_activeRowText_inModal(app.GridCells.REMARK, CONTAINERS_INVOICE.BUSINESS_PARTNER);
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
    });

    it("TC - Change status ui, check the status filter", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES, 0);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CONTAINERS_DATA_TYPES.DATA_TYPE)
        _common.select_rowHasValue_onIndexBased(cnt.uuid.DATA_TYPES, CONTAINERS_DATA_TYPES.DATA_TYPE, 1)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORDS, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
        _common.create_newRecord(cnt.uuid.DATA_RECORDS)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, DATA_TYPE_DESC)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, DATA_TYPE_CODE)
        cy.SAVE()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.INVOICE);
        _common.openTab(app.TabBar.INVOICES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.INVOICEHEADER, app.FooterTab.INVOICEHEADER, 1);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, INVOICE_NO_1);
        _common.select_rowInContainer(cnt.uuid.INVOICEHEADER)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_INVOICE_STATUS);
        _common.clickOn_checkboxUnderModal_byClass(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonKeys.CHECK)

        _validate.verify_recordNotPresentInmodal(DATA_TYPE_DESC)
        _common.waitForLoaderToDisappear()
        _common.clickOn_checkboxUnderModal_byClass(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonKeys.UNCHECK)
        _validate.verify_recordPresentInmodal(DATA_TYPE_DESC)
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
    });
})
