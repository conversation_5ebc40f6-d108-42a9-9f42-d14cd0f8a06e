/*
 * Copyright(c) RIB Software GmbH
 */

import { IInitializationContext, IWizard, PlatformModuleManagerService } from '@libs/platform/common';

export const SALES_WIP_WIZARDS: IWizard[] = [
	{
		uuid: 'de851d1a516a444c85458b5ac722b939',
		name: 'changeStatusForProjectDocument',
		async execute(context: IInitializationContext) {
			const m = await import('@libs/sales/wip');
			m.SalesWipChangeStatusForProjectDocumentWizardService.execute(context);
		},
	},
	{
		uuid: '3064f8fed2bd47189cd35a5582b2a483',
		name: 'changeSalesConfiguration',
		execute(context: IInitializationContext) {
			const currentModule = context.injector.get(PlatformModuleManagerService).activeModule?.internalModuleName ?? '';
			return import('@libs/sales/wip').then((m) => {
				context.injector.get(m.SalesWipChangeTypeOrConfigWizardService).changeSalesConfiguration(currentModule);
			});
		},
	},
	{
		uuid: 'b08281a460f2442a94c00eb195b4147b',
		name: 'updateDirectCostPerUnit',
		execute(context: IInitializationContext): Promise<void> | undefined {
			return import('@libs/sales/wip').then((m) => {
				return context.injector.get(m.SalesWipUpdateDirectCostPerUnitWizardService).updateDirectCostPerUnit();
			});
		},
	}
];