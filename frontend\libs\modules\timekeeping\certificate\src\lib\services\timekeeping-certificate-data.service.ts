/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { DataServiceFlatRoot, IDataServiceOptions, ServiceRole, IDataServiceRoleOptions } from '@libs/platform/data-access';
import { ICertificateEntity } from '@libs/resource/interfaces';
import { ITimekeepingCertificateComplete } from '../model/entities/timekeeping-certificate-complete.interface';

@Injectable({
	providedIn: 'root',
})
export class TimekeepingCertificateDataService extends DataServiceFlatRoot<ICertificateEntity, ITimekeepingCertificateComplete> {
	public constructor() {
		const options: IDataServiceOptions<ICertificateEntity> = {
			apiUrl: 'timekeeping/certificate',
			readInfo: {
				endPoint: 'filtered',
				usePost: true
			},
			deleteInfo: {
				endPoint: 'multidelete'
			},
			roleInfo: <IDataServiceRoleOptions<ICertificateEntity>>{
				role: ServiceRole.Root,
				itemName: 'Certificates',
			},
		};
		super(options);
	}

	public override createUpdateEntity(modified: ICertificateEntity | null): ITimekeepingCertificateComplete {

		const complete = { MainItemId: modified?.Id ?? null } as ITimekeepingCertificateComplete;
		if (modified) {
			complete.Certificates = [modified];
		} else {
			complete.Certificates = [];
		}
		return complete;
	}

	public override getModificationsFromUpdate(complete: ITimekeepingCertificateComplete): ICertificateEntity[] {
		return complete?.Certificates || [];
	}
}