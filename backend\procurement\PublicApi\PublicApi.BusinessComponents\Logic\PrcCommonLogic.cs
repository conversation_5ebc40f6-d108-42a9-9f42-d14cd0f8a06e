using RIB.Visual.Procurement.Contract.BusinessComponents;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents;
using RIB.Visual.Procurement.Package.BusinessComponents;
using RIB.Visual.Procurement.Requisition.BusinessComponents;
using RIB.Visual.Procurement.Common.Core;
using RIB.Visual.Cloud.Common.BusinessComponents;

namespace RIB.Visual.Procurement.PublicApi.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public enum _ProcurementModuleOptions
	{
		/// <summary>
		/// Process for Package module.
		/// </summary>
		Package = 1,

		/// <summary>
		/// Process for Requisition module.
		/// </summary>
		Requisition = 2,

		/// <summary>
		/// Process for Contract module.
		/// </summary>
		Contract = 3,

	}

	/// <summary>
	/// 
	/// </summary>
	public class TotalRequest
	{
		/// <summary>
		/// 
		/// </summary>
		[Required]
		public int MainItemId { get; set; }
		/// <summary>
		/// 
		/// </summary>
		[Required]
		public int PrcConfigurationFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		[Required]
		public _ProcurementModuleOptions Phase { get; set; }

	}

	/// <summary>
	/// 
	/// </summary>
	public class TotalHeaderInfo : ITotalHeaderEntity
	{
		/// <summary>
		/// 
		/// </summary>
		public int? BpdVatGroupFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int CurrencyFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal ExchangeRate { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public bool IsPrcPackageModule { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PackageFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int PrcHeaderFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public IPrcHeaderEntity PrcHeaderInstance { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int? ProjectFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int TaxCodeFk { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public int Id { get; set; }
		
	}
	/// <summary>
	/// 
	/// </summary>
	public class PrcCommonLogic
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		public void ChangeConfiguration(TotalRequest request)
		{
			if (request.Phase == _ProcurementModuleOptions.Contract)
			{
				#region logic
				var headerLogic = new ConHeaderLogic();
				var header = headerLogic.GetItemByKey(request.MainItemId);

				if (header != null)
				{
					var totalLogic = new ConTotalLogic();
					//delete old data
					var delEntities = totalLogic.GetSearchList(e => e.HeaderFk == request.MainItemId).ToList();
					totalLogic.Delete(delEntities);
					//add new data
					var configuration = new PrcConfigurationLogic().GetSearchList(x => x.Id == request.PrcConfigurationFk).FirstOrDefault();
					if (configuration != null)
					{
						var totalHeaderInfo = new TotalHeaderInfo();
						totalHeaderInfo.Id= request.MainItemId;
						totalHeaderInfo.PackageFk = header.PackageFk;
						totalHeaderInfo.PrcHeaderFk = header.PrcHeaderFk;
						totalHeaderInfo.ExchangeRate = header.ExchangeRate;
						totalHeaderInfo.TaxCodeFk = header.TaxCodeFk;

						var addEntities = totalLogic.CopyFromConfiguration(totalHeaderInfo, configuration.PrcConfigHeaderFk);
						totalLogic.Save(addEntities);
					}

					//update header==>PrcConfigurationFk
					header.ConfigurationFk = request.PrcConfigurationFk;
					headerLogic.Save(header);
				}
				#endregion
			}
			else if (request.Phase == _ProcurementModuleOptions.Package)
			{
				#region logic
				var headerLogic = new PrcPackageLogic();
				var header = headerLogic.GetItemByKey(request.MainItemId);

				if (header != null)
				{
					var totalLogic = new PrcPackageTotalLogic();
					//delete old data
					var delEntities = totalLogic.GetSearchList(e => e.HeaderFk == request.MainItemId).ToList();
					totalLogic.Delete(delEntities);
					//add new data
					var configuration = new PrcConfigurationLogic().GetSearchList(x => x.Id == request.PrcConfigurationFk).FirstOrDefault();
					if (configuration != null)
					{
						var totalHeaderInfo = new TotalHeaderInfo();
						totalHeaderInfo.Id = request.MainItemId;
						totalHeaderInfo.PackageFk = header.PackageFk;
						totalHeaderInfo.PrcHeaderFk = header.PrcHeaderFk;
						totalHeaderInfo.ExchangeRate = header.ExchangeRate;
						totalHeaderInfo.TaxCodeFk = header.TaxCodeFk;

						var addEntities = totalLogic.CopyFromConfiguration(totalHeaderInfo, configuration.PrcConfigHeaderFk);
						totalLogic.Save(addEntities);
						totalLogic.RecalculateTotalsFromHeader(header, addEntities);
						totalLogic.Save(addEntities);
					}
					//update header==>PrcConfigurationFk
					header.ConfigurationFk = request.PrcConfigurationFk;
					headerLogic.Save(header);
				}
				#endregion
			}
			else if (request.Phase == _ProcurementModuleOptions.Requisition)
			{
				#region logic
				var headerLogic = new ReqHeaderLogic();
				var header = headerLogic.GetItemByKey(request.MainItemId);
				if (header != null)
				{
					var totalLogic = new ReqTotalLogic();
					//delete old data
					var delEntities = totalLogic.GetSearchList(e => e.HeaderFk == request.MainItemId).ToList();
					totalLogic.Delete(delEntities);
					//add new data
					var configuration = new PrcConfigurationLogic().GetSearchList(x => x.Id == request.PrcConfigurationFk).FirstOrDefault();
					if (configuration != null)
					{
						var totalHeaderInfo = new TotalHeaderInfo();
						totalHeaderInfo.Id = request.MainItemId;
						totalHeaderInfo.PackageFk = header.PackageFk;
						totalHeaderInfo.PrcHeaderFk = header.PrcHeaderFk;
						totalHeaderInfo.ExchangeRate = header.ExchangeRate;
						totalHeaderInfo.TaxCodeFk = header.TaxCodeFk;

						var addEntities = totalLogic.CopyFromConfiguration(totalHeaderInfo, configuration.PrcConfigHeaderFk);
						totalLogic.Save(addEntities);
					}
					//update header==>PrcConfigurationFk
					header.PrcHeaderEntity.ConfigurationFk = request.PrcConfigurationFk;
					headerLogic.Save(header);
				}
				#endregion
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="languageFk"></param>
		/// <returns></returns>
		/// <exception cref="ArgumentException"></exception>
		public static int? CheckLanguageFK(int? languageFk)
		{
			if (languageFk.HasValue)
			{
				var languageLogic = new LanguageLogic();
				var languages = languageLogic.GetLanguages().ToList();
				if (languages.Find(e => e.Id == languageFk.Value) != null)
				{
					return languageFk.Value;
				}
				throw new ArgumentException("The data contains an invalid languageFk.");
			}
			return languageFk;
		}
	}
}
