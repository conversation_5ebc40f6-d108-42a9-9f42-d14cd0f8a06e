// 
// $Id$
// Copyright (c) RIB Software SE
// 

using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Dynamic;
using System.Linq;
using System.Linq.Expressions;
using System.Transactions;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Sales.Common.BusinessComponents;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Sales.Billing.Common;

namespace RIB.Visual.Sales.Billing.BusinessComponents
{
	/// <summary>
	/// Sales Billing Boq logic
	/// </summary>
	[Export("Billing", typeof(IBoqCompositeLogic))]
    [Export("Billing", typeof(ISalesBillingBoqLogic))]
	[Export("Sales.Billing.BilBoqEntity", typeof(IDataBaseLogic))]
	[Export("Sales.Billing.BilBoqEntity", typeof(IDataBaseCreateLogic))]
	public class SalesBillingBoqLogic : SalesCommonBoqLogic<BilBoqEntity, BilBoqCompositeEntity, SalesBillingLogic, BillingBoq2ModelObjectVEntity>, ISalesBillingBoqLogic,
		IDataBaseLogic, IDataBaseCreateLogic, IEntityCopier
	{
		/// <summary>
		/// state info for deep copy process
		/// </summary>
		public BillingCopyTypes copyType = BillingCopyTypes.DefaultCopy;

		/// <summary>
		/// deep copy Boq option
		/// </summary>
		public bool IsResetBoqQuantitiesOnCopy = false;

		/// <summary>
		/// deep copy Boq option (use negated total quantity as new quantities)
		/// </summary>
		public bool IsTotalQuantityOnCopy = false;

		/// <summary>
		/// Default Constructor
		/// </summary>
		public SalesBillingBoqLogic()
			: base(ModelBuilder.DbModel, "BIL_BOQ")
		{ }

		/// <summary>
		/// 
		/// </summary>
		public const int QuantityTypeBilling = 4;

		/// <summary>
		/// Creates BilBoqComposite entity from BilBoqEntity object
		/// </summary>
		/// <param name="bilBoqEntity">BilBoqEntity object</param>
		/// <returns>BilBoqComposite</returns>
		public BilBoqCompositeEntity GetBilBoqComposite(BilBoqEntity bilBoqEntity)
		{
			BilBoqCompositeEntity entity = null;

			if (bilBoqEntity != null)
			{
				var boqHeader = new BoqHeaderLogic().GetEntityById(bilBoqEntity.BoqHeaderFk);
				if (boqHeader != null)
				{
					var boqRootItem = new BoqItemLogic().GetBoqRootItemByHeaderId(boqHeader.Id);

					if (boqRootItem != null)
					{
						entity = new BilBoqCompositeEntity(bilBoqEntity, boqHeader, boqRootItem);
					}
				}
			}

			return entity;
		}

		/// <summary>
		/// Create new bil boq based on project boq
		/// </summary>
		/// <param name="bilId">id of bil</param>
		/// <param name="projectBoqHeaderId">Id of project boq header</param>
		/// <returns>created bil boq entity</returns>
		public BilBoqCompositeEntity CreateBasedOnProjectBoQ(int bilId, int projectBoqHeaderId)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				BilBoqCompositeEntity createdBilBoq = null;

				// init default values here
				var entity = new BilBoqEntity { Id = SequenceManager.GetNext("BIL_BOQ"), BilHeaderFk = bilId };

				var boqItemLogic = new BoqItemLogic();

				// Copy the project boq into a bil boq.
				// In this copy function a new boq header is already created together with the new boq.
				// Furthermore all elements are saved after being created.
				var boqItemCopyInfo = new BoqItemCopyInfo() { HandlePrcStrcutureCopyViaSysOp = true, TargetModuleHeaderId = bilId, TargetModuleName = "sales.billing" };
				var copiedBoq = boqItemLogic.CopyBoq(projectBoqHeaderId, boqItemCopyInfo);

				if (copiedBoq != null)
				{
					var copiedBoqHeader = copiedBoq.BoqHeader as BoqHeaderEntity;
					var copiedBoqRootItem = copiedBoq.BoqRootItem as BoqItemEntity;

					if (copiedBoqHeader != null && copiedBoqRootItem != null)
					{
						createdBilBoq = new BilBoqCompositeEntity(entity, copiedBoqHeader, copiedBoqRootItem);
						dbcontext.Save(entity);
					}
				}

				return createdBilBoq;
			}
		}

		/// <summary>
		/// Create bil boqs based on the given creation data, i.e. do a partial boq copy of the items to be copied.
		/// </summary>
		/// <param name="data">creation data</param>
		public IEnumerable<BilBoqCompositeEntity> CreateBoqs(BilBoqsCreationData data)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				// First transform the creation data object to a corresponding dictionary
				var partialCopy = new Dictionary<int, IEnumerable<ExpandoObject>>();

				foreach (var bilBoqCopyData in data.Boqs)
				{
					if (bilBoqCopyData.BoqItems.Any())
					{
						var boqItemIdList = bilBoqCopyData.BoqItems.Select(bilBoqItemCreationData =>
						{
							dynamic boqItemCreationData = new ExpandoObject();
							boqItemCreationData.Id = bilBoqItemCreationData.Id;
							return (ExpandoObject)boqItemCreationData;
						}).ToList();

						partialCopy.Add(bilBoqCopyData.BoqHeaderFk, boqItemIdList);

					}
				}

				var boqItemLogic = new BoqItemLogic();

				var boqItemCopyInfo = new BoqItemCopyInfo() { HandlePrcStrcutureCopyViaSysOp = true, TargetModuleHeaderId = data.BilId, TargetModuleName = "sales.billing" };
				var boqCopyData = boqItemLogic.CopyBoqs(null, boqItemCopyInfo, partialCopy);
				boqItemLogic.CalculateBoqs(boqCopyData);
				boqItemLogic.SaveBoqs(boqCopyData);

				var copiedBilBoqComposites = new List<BilBoqCompositeEntity>();

				foreach (var copyData in boqCopyData)
				{
					var bilBoqComposite = new BilBoqCompositeEntity();
					var entity = new BilBoqEntity { Id = SequenceManager.GetNext("BIL_BOQ"), BilHeaderFk = data.BilId };
					bilBoqComposite.BilBoq = entity;
					bilBoqComposite.BoqHeader = copyData.BoqHeader as BoqHeaderEntity;
					bilBoqComposite.BoqRootItem = copyData.BoqRootItem as BoqItemEntity;
					if (bilBoqComposite.BoqHeader != null) bilBoqComposite.BilBoq.BoqHeaderFk = bilBoqComposite.BoqHeader.Id;
					dbcontext.Save(entity);

					copiedBilBoqComposites.Add(bilBoqComposite);
				}

				return copiedBilBoqComposites;
			}
		}

		void ISalesBillingBoqLogic.TakeoverBoqFromAction(int targetBillHeaderId, int sourceBoqHeaderId, IBoqItemCopyInfo boqItemCopyInfo)
		{
			CopyBoqs(targetBillHeaderId, new Dictionary<Int32,IEnumerable<Int32>>{{sourceBoqHeaderId, new int[]{}}}, boqItemCopyInfo);
		}

		/// <summary>
		/// Copy given boqs (full or partial, support for mixed request)
		/// </summary>
		/// <param name="billId">destination bill id</param>
		/// <param name="partialCopy">ids of boqs to copy (with boq item ids if partial copy)</param>
		/// <param name="boqItemCopyInfo"></param>
		/// <returns>updated bill header entity</returns>
		public BilHeaderEntity CopyBoqs(int billId, IDictionary<int, IEnumerable<int>> partialCopy, IBoqItemCopyInfo boqItemCopyInfo)
		{
			// separate full and partial boqs
			// if no boq items are assigned (boq header id => [<no ids>]), full boq will be copied, otherwise partial copy
			var dict = GetExistingBoqHeaderIdByReference(billId, partialCopy.Keys);
			var mergeIds = dict.Keys;
			var copyIds = partialCopy.Keys.Except(mergeIds).ToList();

			// get tax code and vat group
			// TODO: at the moment contract is fetched twice
			var salesBillingLogic = new SalesBillingLogic();
			BilHeaderEntity billHeaderEntity = salesBillingLogic.GetById(billId);
			if (boqItemCopyInfo != null)
			{
				boqItemCopyInfo.TaxCodeId = billHeaderEntity.TaxCodeFk;
				boqItemCopyInfo.VatGroupId = billHeaderEntity.VatGroupFk;
				boqItemCopyInfo.ExchangeRate = billHeaderEntity.ExchangeRate;
				boqItemCopyInfo.IncludePriceConditions = true; // forces calculation of Boq
			}

			if (copyIds.Any())
			{
				var partial2Copy = partialCopy.Where(kv => copyIds.Contains(kv.Key)).ToDictionary(e => e.Key, e => e.Value);
				var fullBoqIds = partial2Copy.Where(kv => !kv.Value.Any()).Select(kv => kv.Key);
				CopyBoqs(billHeaderEntity, fullBoqIds.Cast<int?>(), boqItemCopyInfo, billHeaderEntity.TaxCodeFk, billHeaderEntity.VatGroupFk);
				CopyBoqsPartially(billHeaderEntity, partial2Copy, boqItemCopyInfo);
			}
			if (mergeIds.Any())
			{
				var partial2Merge = partialCopy.Where(kv => mergeIds.Contains(kv.Key)).ToDictionary(e => e.Key, e => e.Value);
				MergeBoqs(billId, partial2Merge, dict, false, billHeaderEntity.TaxCodeFk, billHeaderEntity.VatGroupFk);
			}

			// we need to update the amounts on the bill header
			var salesBillingBoqLogic = new SalesBillingBoqLogic();

			var boqList = salesBillingBoqLogic.GetList(billHeaderEntity.Id);
			salesBillingLogic.UpdateAmountByBoQs(billHeaderEntity, boqList);
			return billHeaderEntity;
		}

		/// <summary>
		/// Copys boqs to given bill id
		/// </summary>
		/// <param name="billHeader">destination bill</param>
		/// <param name="sourceBoqHeaderNullableIds">ids of boqs to copy</param>
		/// <param name="boqItemCopyInfo"></param>/// 
		/// <param name="taxCodeFk"></param>/// 
		/// <param name="vatGroupFk"></param>///
		/// <param name="useTransferContractQuantityOpt">option: 'Transfer Contract Quantity as Bill Quantity'</param>
		public void CopyBoqs(BilHeaderEntity billHeader, IEnumerable<int?> sourceBoqHeaderNullableIds, IBoqItemCopyInfo boqItemCopyInfo = null, int taxCodeFk = -1, int? vatGroupFk = null, bool useTransferContractQuantityOpt = false)
		{
			var boqItemLogic = new BoqItemLogic();
			List<Int32> sourceBoqHeaderIds = sourceBoqHeaderNullableIds.OfType<Int32>().ToList();

			// GC BOQs should not be contained in sales.billing
			foreach (var sourceGcBoqHeader in new BoqHeaderLogic().GetSearchList(bh => sourceBoqHeaderIds.Contains(bh.Id) && bh.IsGCBoq))
			{
				sourceBoqHeaderIds.Remove(sourceGcBoqHeader.Id);
			}

			foreach (var sourceBoqHeaderId in sourceBoqHeaderIds)
			{
				var _boqItemCopyInfo = boqItemCopyInfo?.Clone() ?? new BoqItemCopyInfo();
				_boqItemCopyInfo.ResetQuantities = false;
				_boqItemCopyInfo.ResetUnitRate = false;
				_boqItemCopyInfo.IncludePriceConditions = true;
				_boqItemCopyInfo.CopyBoqRootItemOnly = false;
				_boqItemCopyInfo.TargetModuleName = "sales.billing";
				_boqItemCopyInfo.TargetModuleHeaderId = billHeader.Id;
				_boqItemCopyInfo.HandlePrcStrcutureCopyViaSysOp = true;
				_boqItemCopyInfo.TaxCodeId = taxCodeFk!=-1 ? taxCodeFk : boqItemCopyInfo.TaxCodeId;
				_boqItemCopyInfo.VatGroupId = vatGroupFk ?? boqItemCopyInfo.VatGroupId;
				_boqItemCopyInfo.CurrencyId = billHeader.CurrencyFk;
				_boqItemCopyInfo.ExchangeRate = billHeader.ExchangeRate;

				var newBoq = boqItemLogic.CopyBoq((int)sourceBoqHeaderId, _boqItemCopyInfo);

				var bilBoqEntity = new BilBoqEntity
				{
					Id = SequenceManager.GetNext("BIL_BOQ"),
					BilHeaderFk = billHeader.Id,
					BoqHeaderFk = newBoq.BoqHeader.Id
				};

				SaveBoqEntity(bilBoqEntity);

				if (!useTransferContractQuantityOpt)
				{
					var hasPreviousBill = billHeader.PreviousBillFk != null;
					var basicsCustomizeBillTypeLogic = new Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeLogic();
					var billTypeSelected = (Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeEntity)basicsCustomizeBillTypeLogic.GetById(billHeader.TypeFk);
					var isFirstBillForGivenBoq = IsFirstBillingBoq(newBoq.BoqHeader.Id, billHeader.Id);

					// Now we adjust the billing quantity so that the billing quantity total equals the current wip total
					boqItemLogic.AdjustBilledQuantityToPerformedQuantityAndSave((IBoqItemEntity)newBoq.BoqRootItem, null, taxCodeFk, vatGroupFk, true, hasPreviousBill, billTypeSelected.Isprogress, isFirstBillForGivenBoq, _boqItemCopyInfo.UseNegatedTotalQuantities);
				}
			}
		}

		private bool IsFirstBillingBoq(int billBoqHeaderFk, int billHeaderFk)
		{
			bool isFirstBillingBoq = false;
			var salesBillingLogic = new SalesBillingLogic();
			BilHeaderEntity billHeaderEntity = salesBillingLogic.GetById(billHeaderFk);
			var ordHeaderFk = billHeaderEntity.OrdHeaderFk;
			var projectId = billHeaderEntity.ProjectFk;

			// Get all bills belonging to the same contract and project
			var filteredBills = salesBillingLogic.GetSearchList(e => e.OrdHeaderFk == ordHeaderFk && e.ProjectFk == projectId);
			var filteredBillHeaderIDs = filteredBills.Select(e => e.Id);

			// Get the related billing boqs
			var filteredBillingBoqs = GetSearchList(e => filteredBillHeaderIDs.Contains(e.BilHeaderFk)).ToList();

			if (filteredBillingBoqs.Any())
			{
				if (filteredBillingBoqs.Count() == 1)
				{
					if (filteredBillingBoqs.FirstOrDefault() != null)
					{
						isFirstBillingBoq = filteredBillingBoqs.FirstOrDefault().BoqHeaderFk == billBoqHeaderFk;
					}
				}
				else if (filteredBillingBoqs.Count() > 1)
				{
					var boqHeader = new BoqHeaderLogic().GetEntityById(billBoqHeaderFk);
					if (boqHeader != null)
					{
						var projectBoqHeaderFk = boqHeader.BoqHeaderFk;
						var filteredBillingBoqHeaderIDs = filteredBillingBoqs.Select(e => e.BoqHeaderFk);
						var billingBoqHeaders = new BoqHeaderLogic().GetSearchList(e => filteredBillingBoqHeaderIDs.Contains(e.Id));
						var billingBoqsWithSamePrjBoq = billingBoqHeaders.Where(e => e.BoqHeaderFk == projectBoqHeaderFk).ToList();
						isFirstBillingBoq = billingBoqsWithSamePrjBoq.Count <= 1;
					}
				}
			}

			return isFirstBillingBoq;
		}

		/// <summary>
		/// Partial copy of boqs
		/// </summary>
		/// <param name="billHeader">destination bill</param>
		/// <param name="itemsToBeCopied"></param>
		/// <param name="boqItemCopyInfo"></param>
		public void CopyBoqsPartially(BilHeaderEntity billHeader, IDictionary<int, IEnumerable<int>> itemsToBeCopied, IBoqItemCopyInfo boqItemCopyInfo = null)
		{
			var boqItemLogic = Injector.Get<IBoqItemLogic>();
			if (boqItemCopyInfo == null)
				boqItemCopyInfo = new BoqItemCopyInfo();
			boqItemCopyInfo.HandlePrcStrcutureCopyViaSysOp = true;
			boqItemCopyInfo.CurrencyId = billHeader.CurrencyFk;
			var createdBoQs = boqItemLogic.CopyBoqs(null, boqItemCopyInfo, CreateExpandoPartialCopyDict(itemsToBeCopied)).ToList();
			boqItemLogic.CalculateBoqs(createdBoQs);
			boqItemLogic.SaveBoqs(createdBoQs);

			SaveBoqEntities(createdBoQs.Select(createdBoQ => new BilBoqEntity
			{
				Id = SequenceManager.GetNext("BIL_BOQ"),
				BilHeaderFk = billHeader.Id,
				BoqHeaderFk = createdBoQ.BoqHeader.Id
			}));
		}

		/// <summary>
		/// Partial copy of boqs for Generate Bill From contract
		/// </summary>
		/// <param name="bilHeader"></param>
		/// <param name="itemsToBeCopied"></param>
		/// <param name="paymentScheduleEntity"></param>
		/// <param name="boqItemCopyInfo"></param>
		/// <param name="paymentBalanceNet"></param>
		/// <param name="paymentBalanceNetOC"></param>
		public void CopyBoqsPartiallyForPaymentSchedule(BilHeaderEntity bilHeader,IDictionary<int, IEnumerable<int>> itemsToBeCopied, IPaymentScheduleBaseEntity paymentScheduleEntity, IBoqItemCopyInfo boqItemCopyInfo = null, decimal? paymentBalanceNet = null, decimal? paymentBalanceNetOC = null)
        {
			var boqItemLogic = Injector.Get<IBoqItemLogic>();
			if (boqItemCopyInfo == null)
			{
				boqItemCopyInfo = new BoqItemCopyInfo();
			}
			// add target information if missing
			if (string.IsNullOrEmpty(boqItemCopyInfo.TargetModuleName))
			{
				boqItemCopyInfo.TargetModuleName = "sales.billing";
				boqItemCopyInfo.TargetModuleHeaderId = bilHeader.Id;
			}

			boqItemCopyInfo.HandlePrcStrcutureCopyViaSysOp = true;
            var createdBoQs = boqItemLogic.CopyBoqs(null, boqItemCopyInfo, CreateExpandoPartialCopyDict(itemsToBeCopied)).ToList();

			//If BilType ISPROGRESS 
			//BilTypeFk
			var basicsCustomizeBillTypeLogic = new Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeLogic();
			var billTypeSelected = (Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeEntity)basicsCustomizeBillTypeLogic.GetById(bilHeader.TypeFk);

			decimal amountNet = paymentScheduleEntity.AmountNet;
			decimal amountNetOc = paymentScheduleEntity.AmountNetOc;

			if (billTypeSelected.Isprogress == false)
            {
				amountNet = paymentBalanceNet ?? 0;
				amountNetOc = paymentBalanceNetOC ?? 0;
			}


			//Update because of Generate Bill From contract condition
			foreach (var item in createdBoQs)
            {
                var boqRootItemBoqEntity = item.BoqRootItem as BoqItemEntity;
                boqRootItemBoqEntity.IsLumpsum = true;
                boqRootItemBoqEntity.LumpsumPrice = amountNet;
                boqRootItemBoqEntity.LumpsumPriceOc = amountNetOc;

                item.ExchangeRate = bilHeader.ExchangeRate;
                item.TaxCodeFk = bilHeader.TaxCodeFk;
                item.VatGroupFk = bilHeader.VatGroupFk;
            }

            boqItemLogic.CalculateBoqs(createdBoQs);
            boqItemLogic.SaveBoqs(createdBoQs);

            SaveBoqEntities(createdBoQs.Select(createdBoQ => new BilBoqEntity
            {
                Id = SequenceManager.GetNext("BIL_BOQ"),
                BilHeaderFk = bilHeader.Id,
                BoqHeaderFk = createdBoQ.BoqHeader.Id
            }));
        }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="bilHeader"></param>
		/// <param name="itemsToBeCopied"></param>
		/// <param name="contractAdvanceLineId"></param>
		/// <param name="boqItemCopyInfo"></param>
		/// <param name="paymentBalanceNet"></param>
		/// <param name="paymentBalanceNetOC"></param>
		public void CopyBoqsPartiallyForContractAdvance(BilHeaderEntity bilHeader, IDictionary<int, IEnumerable<int>> itemsToBeCopied, int contractAdvanceLineId, IBoqItemCopyInfo boqItemCopyInfo = null, decimal? paymentBalanceNet = null, decimal? paymentBalanceNetOC = null)
		{
			var boqItemLogic = Injector.Get<IBoqItemLogic>();
			if (boqItemCopyInfo == null)
			{
				boqItemCopyInfo = new BoqItemCopyInfo();
			}
			// add target information if missing
			if (string.IsNullOrEmpty(boqItemCopyInfo.TargetModuleName))
			{
				boqItemCopyInfo.TargetModuleName = "sales.billing";
				boqItemCopyInfo.TargetModuleHeaderId = bilHeader.Id;
			}

			boqItemCopyInfo.HandlePrcStrcutureCopyViaSysOp = true;
			var createdBoQs = boqItemLogic.CopyBoqs(null, boqItemCopyInfo, CreateExpandoPartialCopyDict(itemsToBeCopied)).ToList();

			//If BilType ISPROGRESS 
			//BilTypeFk
			var basicsCustomizeBillTypeLogic = new Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeLogic();
			var billTypeSelected = (Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeEntity)basicsCustomizeBillTypeLogic.GetById(bilHeader.TypeFk);

			var salesContractAdvance = Injector.Get<ISalesContractAdvanceLogic>().GetById(contractAdvanceLineId);

			decimal amountNet = salesContractAdvance.AmountDue;
			decimal amountNetOc = salesContractAdvance.AmountDueOc;

			if (billTypeSelected.Isprogress == false)
			{
				amountNet = paymentBalanceNet ?? 0;
				amountNetOc = paymentBalanceNetOC ?? 0;
			}


			//Update because of Generate Bill From contract condition
			foreach (var item in createdBoQs)
			{
				var boqRootItemBoqEntity = item.BoqRootItem as BoqItemEntity;
				boqRootItemBoqEntity.IsLumpsum = true;
				boqRootItemBoqEntity.LumpsumPrice = amountNet;
				boqRootItemBoqEntity.LumpsumPriceOc = amountNetOc;

				item.ExchangeRate = bilHeader.ExchangeRate;
				item.TaxCodeFk = bilHeader.TaxCodeFk;
				item.VatGroupFk = bilHeader.VatGroupFk;
			}

			boqItemLogic.CalculateBoqs(createdBoQs);
			boqItemLogic.SaveBoqs(createdBoQs);

			SaveBoqEntities(createdBoQs.Select(createdBoQ => new BilBoqEntity
			{
				Id = SequenceManager.GetNext("BIL_BOQ"),
				BilHeaderFk = bilHeader.Id,
				BoqHeaderFk = createdBoQ.BoqHeader.Id
			}));
		}

		/// <summary>
		/// Merge boqs to given bill id
		/// </summary>
		/// <param name="billId">destination wip id</param>
		/// <param name="partialCopyDict">ids of boqs to merge</param>
		/// <param name="sourceBoqHeaderIds2RootItems">ids of boqs to merge (with mapping to target root boq items)</param>
		/// <param name="resetQuantities">specifies if quantities have to be reset</param>
		/// <param name="taxCodeFk">tax code id</param>
		/// <param name="vatGroupFk">vat group id</param>
		/// TODO: merge both methods? (MergeBoqs)
		public void MergeBoqs(int billId, IDictionary<int, IEnumerable<int>> partialCopyDict, Dictionary<int, IBoqItemEntity> sourceBoqHeaderIds2RootItems, bool resetQuantities = false, int taxCodeFk = -1, int? vatGroupFk = null)
		{
			var boqItemCopyInfo = new BoqItemCopyInfo()
			{
				TargetModuleName = "sales.billing",
				TargetModuleHeaderId = billId,
				ResetQuantities = resetQuantities,
				TaxCodeId = taxCodeFk,
				VatGroupId = vatGroupFk,
				CumulateQuantities = false
			};

			var boqItemLogic = Injector.Get<IBoqItemLogic>();
			foreach (var partialCopy in partialCopyDict)
			{
				var targetRootItem = sourceBoqHeaderIds2RootItems[partialCopy.Key];
				var expandoPartialCopyDict = CreateExpandoPartialCopyDict(partialCopyDict);
				boqItemLogic.MergeBoqs(new List<int>() { partialCopy.Key }, boqItemCopyInfo, expandoPartialCopyDict.Any() ? expandoPartialCopyDict : null, targetRootItem);
			}
		}

		// helper
		private Dictionary<int, List<int>> GetPrjBoqHeader2BoqHeaderDict(IEnumerable<IBoqListEntity> boqListEntities)
		{
			var prjBoqHeader2BoqHeaderDict = new Dictionary<int, List<int>>();

			foreach (var curBoqEntity in boqListEntities)
			{
				var boqItemLogic = Injector.Get<IBoqItemLogic>();
				if (curBoqEntity.BoqHeaderFk != null)
				{
					var curBoqHeaderId = (int)curBoqEntity.BoqHeaderFk;
					var boqRootItem = boqItemLogic.GetBoqRootItemAsInterface(curBoqHeaderId);
					// GetBoqRootItemAsInterface for BoqItemPrjBoqFk -> BoqHeaderFk of Base-BoQs (key)
					if (boqRootItem.BoqItemPrjBoqFk != null)
					{
						var prjBoqHeaderId = (int)boqRootItem.BoqItemPrjBoqFk;
						// make sure key contains a list
						if (!prjBoqHeader2BoqHeaderDict.ContainsKey(prjBoqHeaderId))
						{
							prjBoqHeader2BoqHeaderDict.Add(prjBoqHeaderId, new List<int>());
						}

						// add Boq header of contract to dictionary
						prjBoqHeader2BoqHeaderDict[prjBoqHeaderId].Add(curBoqHeaderId);
					}
				}
			}

			return prjBoqHeader2BoqHeaderDict;
		}

		// TODO: common method for all cases needed to simplify code
		/// <summary>
		/// merge boq wips to existing bill with existing + non-existing boqs
		/// </summary>
		/// <param name="wipHeader"></param>
		/// <param name="bilHeader"></param>
		public void PostMergeBoqsWipToBill(ISalesWipHeaderEntity wipHeader, BilHeaderEntity bilHeader)
		{
			var boqItemCopyInfo = new BoqItemCopyInfo()
			{
				TargetModuleName = "sales.billing",
				TargetModuleHeaderId = bilHeader.Id
			};

			// from wip boqs...
			var salesWipBoqLogic = Injector.Get<ISalesWipBoqLogic>();
			var curWipBoqEntities = salesWipBoqLogic.GetList(wipHeader.Id).ToList();

			// exclude GC BOQs => should not be contained in sales.billing
			List<Int32> sourceBoqHeaderIds = curWipBoqEntities.Where(b => b.BoqHeaderFk != null).Select(b => (int)b.BoqHeaderFk).ToList();
			List<Int32> sourceGcBoqHeaderIds = new BoqHeaderLogic().GetSearchList(bh => sourceBoqHeaderIds.Contains(bh.Id) && bh.IsGCBoq).Select(bh => bh.Id).ToList();

			sourceBoqHeaderIds = sourceBoqHeaderIds.Where(id => !sourceGcBoqHeaderIds.Contains(id)).ToList();
			curWipBoqEntities = curWipBoqEntities.Where(e => e.BoqHeaderFk != null && sourceBoqHeaderIds.Contains((int)e.BoqHeaderFk)).ToList();

			var wipPrjBoqHeader2BoqHeaderDict = GetPrjBoqHeader2BoqHeaderDict(curWipBoqEntities);

			// if no boqs left we can stop here
			if (!wipPrjBoqHeader2BoqHeaderDict.Any()) { return; }

			var salesBillingBoqLogic = new SalesBillingBoqLogic();
			var curBillBoqEntities = salesBillingBoqLogic.GetList(bilHeader.Id);
			var billPrjBoqHeader2BoqHeaderDict = GetPrjBoqHeader2BoqHeaderDict(curBillBoqEntities);


			#region MergeBoQs
			var boqItemLogic = new BoqItemLogic();
			List<IBoqCopyData> mergedBoqList = new List<IBoqCopyData>();

			// TODO: 5. If the Bill already has a BoQ, and the WIP to be added does not use the same BoQ (but refers to same contract) the WIP's BoQ should be added automatically to the bill.
			// TODO: 6. If the Bill already has a BoQ, and the WIP to be added has the same BoQ (but refers to same contract) but additional BoQ elements, the BoQ elements should be added automatically to the Bill BoQ.
			foreach (var prj2Boq in wipPrjBoqHeader2BoqHeaderDict)
			{
				var prj2BoqId = prj2Boq.Key;
				var curSourceBoqId = prj2Boq.Value[0]; // helper method (GetPrjBoqHeader2BoqHeaderDict) has common logic => in our case we only have one boq => take first

				// if null, a new boq will be created otherwise we merge boq to existing boq
				IBoqItemEntity rootBoqItem = null;
				// get corresponding bill boq (by prj boq : wip boq header -> proj boq header -> bill boq header)
				if (billPrjBoqHeader2BoqHeaderDict.ContainsKey(prj2BoqId))
				{
					var billBoqId = billPrjBoqHeader2BoqHeaderDict[prj2BoqId][0]; // see helper method comment above => take first
					rootBoqItem = boqItemLogic.GetPureBoqRootItem(new List<Int32>() { billBoqId }).First();
				}

				IBoqCopyData newMergedBoq = boqItemLogic.MergeBoqs(new List<Int32>() { curSourceBoqId }, boqItemCopyInfo, null, rootBoqItem);
				mergedBoqList.Add(newMergedBoq);

				#region NewBoqInBill
				if (rootBoqItem == null) // we have a new boq header here
				{
					// bill boq does not exist => created new bill boq 
					var bilBoqEntity = new BilBoqEntity
					{
						Id = SequenceManager.GetNext("BIL_BOQ"),
						BilHeaderFk = bilHeader.Id,
						BoqHeaderFk = newMergedBoq.BoqHeader.Id
					};
					SaveBoqEntity(bilBoqEntity);
				}
				#endregion
			}
			#endregion

			#region PostProcessing
			// Determine related WIPs
			var bil2WIPs = new SalesBilling2WipLogic().GetByBillId(bilHeader.Id).ToList();

			if (!bil2WIPs.Any()) { return; }

			var hasPreviousBill = bilHeader.PreviousBillFk != null; // TODO: check if PreviousBillFk filled correctly!
			var basicsCustomizeBillTypeLogic = new Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeLogic();
			var billTypeSelected = (Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeEntity)basicsCustomizeBillTypeLogic.GetById(bilHeader.TypeFk);

			foreach (IBoqCopyData mergedBoqCopyData in mergedBoqList)
			{
				var isFirstBillForGivenBoq = IsFirstBillingBoq(mergedBoqCopyData.BoqHeader.Id, bilHeader.Id);

				// Now we adjust the billing quantity so that the billing quantity total equals the current wip total
				boqItemLogic.AdjustBilledQuantityToPerformedQuantityAndSave((IBoqItemEntity)mergedBoqCopyData.BoqRootItem, bil2WIPs.OrderBy(e => e.WipHeaderFk).Select(e => e.WipHeaderFk), bilHeader.TaxCodeFk, bilHeader.VatGroupFk, hasPreviousBill: hasPreviousBill, isProgress: billTypeSelected.Isprogress, isFirst: isFirstBillForGivenBoq, useNegatedTotalQuantities: boqItemCopyInfo.UseNegatedTotalQuantities);
			}
			#endregion
		}

		/// <summary>
		/// Merge boqs to given bill id
		/// </summary>
		/// <param name="billId">destination bill id</param>
		/// <param name="sourceBoqHeaderIds">ids of boqs to merge</param>
		/// <param name="taxCodeFk"></param>
		/// <param name="vatGroupFk"></param>
		/// <param name="boqItemCopyInfo">further information to copy the boq</param>
		public void MergeBoqs(int billId, IEnumerable<int> sourceBoqHeaderIds, int taxCodeFk, int? vatGroupFk, BoqItemCopyInfo boqItemCopyInfo = null)
		{
			BoqItemLogic boqItemLogic = new BoqItemLogic();
			IBoqCopyData newMergedBoq = null;
			List<Int32> sourceGcBoqHeaderIds;

			if (boqItemCopyInfo == null)
			{
				boqItemCopyInfo = new BoqItemCopyInfo()
				{
					TargetModuleName = "sales.billing",
					TargetModuleHeaderId = billId
				};
			}

			// GC BOQs should not be contained in sales.billing
			sourceGcBoqHeaderIds = new BoqHeaderLogic().GetSearchList(bh => sourceBoqHeaderIds.Contains(bh.Id) && bh.IsGCBoq).Select(bh=>bh.Id).ToList();
			sourceBoqHeaderIds = sourceBoqHeaderIds.Where(id=>!sourceGcBoqHeaderIds.Contains(id)).ToList();

			if (sourceBoqHeaderIds.Any())
			{
				newMergedBoq = boqItemLogic.MergeBoqs(sourceBoqHeaderIds, boqItemCopyInfo);
			}

			if (newMergedBoq == null)
				return;

			var bilBoqEntity = new BilBoqEntity
			{
				Id = SequenceManager.GetNext("BIL_BOQ"),
				BilHeaderFk = billId,
				BoqHeaderFk = newMergedBoq.BoqHeader.Id
			};

			SaveBoqEntity(bilBoqEntity);

			// Determine related WIPs
			var bil2WIPs = new SalesBilling2WipLogic().GetByBillId(billId).ToList();

			if (!bil2WIPs.Any())
				return;

			var salesBillingLogic = new SalesBillingLogic();
			BilHeaderEntity billHeaderEntity = salesBillingLogic.GetById(billId);
			var hasPreviousBill = billHeaderEntity.PreviousBillFk != null;
			var basicsCustomizeBillTypeLogic = new Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeLogic();
			var billTypeSelected = (Basics.Customize.BusinessComponents.BasicsCustomizeBillTypeEntity)basicsCustomizeBillTypeLogic.GetById(billHeaderEntity.TypeFk);
			var isFirstBillForGivenBoq = IsFirstBillingBoq(newMergedBoq.BoqHeader.Id, billId);

			// Now we adjust the billing quantity so that the billing quantity total equals the current wip total
			boqItemLogic.AdjustBilledQuantityToPerformedQuantityAndSave((IBoqItemEntity)newMergedBoq.BoqRootItem, bil2WIPs.OrderBy(e => e.WipHeaderFk).Select(e => e.WipHeaderFk), taxCodeFk, vatGroupFk, hasPreviousBill: hasPreviousBill, isProgress: billTypeSelected.Isprogress, isFirst: isFirstBillForGivenBoq, useNegatedTotalQuantities: boqItemCopyInfo.UseNegatedTotalQuantities);
		}

        /// <summary>
        /// 
        /// </summary>
        /// <param name="filter"></param>
        /// <returns></returns>
        public IEnumerable<BilBoqEntity> GetSearchList(Expression<Func<BilBoqEntity, bool>> filter)
        {
            using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
            {
                IQueryable<BilBoqEntity> entities = dbcontext.Entities<BilBoqEntity>();

                if (filter != null)
                {
                    return entities.Where(filter).ToList();
                }

                return entities.ToList();

            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public IBillBoqEntity Create()
        {
            var bilBoqEntity = new BilBoqEntity();
            bilBoqEntity.Id = SequenceManager.GetNext("BIL_BOQ");
            bilBoqEntity.BoqHeaderFk = 0;
            bilBoqEntity.BilHeaderFk = 0;
            return bilBoqEntity;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public IBillBoqEntity Save(IBillBoqEntity entity) 
        {
            var bilBoqEntity = entity as BilBoqEntity;

            if (bilBoqEntity != null)
            {
                this.SaveBoqEntity(bilBoqEntity);
            }

            return entity;
        }

		/// <summary>
		/// 
		/// </summary>
		/// <param name="billHeaderId"></param>
		/// <returns></returns>
		public IEnumerable<IBillBoqEntity> GetItemsByBillHeaderId(int billHeaderId)
		{
			return this.GetSearchList(e => e.BilHeaderFk == billHeaderId);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="boqHeaderIds"></param>
		/// <returns></returns>
		IEnumerable<IBillBoqEntity> ISalesBillingBoqLogic.GetItemsByBoqHeaderIds(List<int> boqHeaderIds)
		{
			var entities = new List<BilBoqEntity>();

			if (boqHeaderIds != null && boqHeaderIds.Count > 0)
			{
				entities = this.GetSearchList(e => boqHeaderIds.Contains(e.BoqHeaderFk)).ToList();
			}

			return entities;
		}

		#region Estimate Billing Quantity Update from Sales Billing Boqs

		/// <summary>
		/// Get Quantity update complete data from Sales Billing by LineItems
		/// </summary>
		/// <param name="lineItems"></param>
		/// <returns></returns>
		private IEnumerable<QuantityUpdateCompleteEntity> GetQuantitiesByEstimate(IEnumerable<IEstLineItemEntity> lineItems)
		  {
			  #region

			  var bilHeaderLogic = new SalesBillingLogic();
			  var bilBoqsPcQtyVLogic = Injector.Get<IBilBoqItemsPcQtyVLogic>();
			  var boqItemLogic = Injector.Get<IBoqItemLogic>();
			  var boqHeaderIds = lineItems.Select(e => e.BoqHeaderFk ?? 0).Distinct();//project boq header ids
			  var boqItems = boqItemLogic.GetBoqsByPrjBoqHeaderIds(boqHeaderIds);
			  if (boqItems == null)
			  {	  boqItems = new List<IBoqItemEntity>(); }
			  var bilBoqHeaderIds = boqItems.Any() ? boqItems.Select(e => e.BoqHeaderFk).ToList() : new List<int>();

			  var salesBillingBoqLogic = new SalesBillingBoqLogic();
			  var bilBoqs = salesBillingBoqLogic.GetSearchList(e=>bilBoqHeaderIds.Contains(e.BoqHeaderFk));

			  var bilHeaderItems = bilHeaderLogic.GetList();

			  var bilOrderedStatusList = new SalesBillingStatusLogic().GetBilledStatusList();

			  var filteredList = from li in lineItems
										join bo in boqItems
										on new { boqHeaderId = li.BoqHeaderFk, boqItemId = li.BoqItemFk } equals
										new { boqHeaderId = bo.BoqItemPrjBoqFk, boqItemId = bo.BoqItemPrjItemFk }
										join bb in bilBoqs
										on bo.BoqHeaderFk equals bb.BoqHeaderFk
										join bh in bilHeaderItems
										on bb.BilHeaderFk equals bh.Id
										join bilBilledStatus in bilOrderedStatusList
										on bh.StatusFk equals bilBilledStatus.Id
										select new
										{
											BoqItemFk = bo.Id,
											BoqHeaderFk = bo.BoqHeaderFk,
											BoqQuantity = bo.Quantity,
											LineItemFk = li.Id,
											LineItemQuantityTotal = li.QuantityTotal,
											EstimateHeaderFk = li.EstHeaderFk,
											BoqPrjHeaderFk = bo.BoqItemPrjBoqFk,
											BoqPrjItemFk = bo.BoqItemPrjItemFk,
											BilHeaderFk = bh.Id,
											Date = bh.PerformedTo,
											ActivityFk = li.PsdActivityFk,
											RecordingLevel = bo.RecordingLevel
										};

			  var boqHeaderIdList = filteredList.Select(e => e.BoqHeaderFk).Distinct().ToList();
			  var bilBoqQtyVItemsByHeader = bilBoqsPcQtyVLogic.GetBilBoqPcQtyVListByHeader(boqHeaderIdList);

			  var bilItems = from lb in filteredList
								  join bbqty in bilBoqQtyVItemsByHeader
								  on new { boqHeaderId = lb.BoqHeaderFk, boqItemId = lb.BoqItemFk } equals
								  new { boqHeaderId = bbqty.BoqHeaderFk, boqItemId = bbqty.Id }
								  select new QuantityUpdateCompleteEntity
								  {
									  BoqItemFk = lb.BoqItemFk,
									  BoqHeaderFk = lb.BoqHeaderFk,
									  BilHeaderFk = lb.BilHeaderFk,
									  Date = lb.Date,
									  BoqQunatity = lb.BoqQuantity,
									  Quantity = bbqty.OrdQuantity != 0 ? (lb.LineItemQuantityTotal * lb.BoqQuantity) / bbqty.OrdQuantity : 0m,
									  PCo = bbqty.OrdQuantity != 0 ? (lb.BoqQuantity + bbqty.PrevQuantity) * 100 / bbqty.OrdQuantity : 0m,
									  EstLineItemFk = lb.LineItemFk,
									  EstHeaderFk = lb.EstimateHeaderFk,
									  PerformanceDate = lb.Date,
									  ActivityFk = lb.ActivityFk,
									  RecordingLevel = lb.RecordingLevel
								  };

			  #endregion

			  return bilItems.GroupBy(r => new { r.BoqHeaderFk, r.BoqItemFk, r.PerformanceDate, r.BoqQunatity, r.Quantity, r.BilHeaderFk, r.ActivityFk, r.PCo, r.EstHeaderFk, r.EstLineItemFk })
									.Select(g => g.First())
									.ToList();

		  }

		  /// <summary>
		  /// Get LineItem Billing Quantity update data from Sales Billing By given LineItems
		  /// </summary>
		  /// <param name="lineItems"></param>
		  /// <returns></returns>
		IEnumerable<IQuantityUpdateCompleteEntity> ISalesBillingBoqLogic.UpdateLineItemQuantitiesFromBil(IEnumerable<IEstLineItemEntity> lineItems)
		  {
			  var qtyCompleteEntities = GetQuantitiesByEstimate(lineItems);
			  var liqLogic = Injector.Get<IEntityCarrier>("estimate.main.lineitemquantity");
			  var liqs = new List<LineItemQuantity>();
			var listByLiRecordingLevel = new List<QuantityUpdateCompleteEntity>();
			foreach (var qty in qtyCompleteEntities)
			  {
				  if (qty.EstLineItemFk != null && qty.EstHeaderFk != null)
				  {

					var entity = new LineItemQuantity()
					  {
						  LineItemFk = (int)qty.EstLineItemFk,
						  EstimateHeaderFk = (int)qty.EstHeaderFk,
						  BoqItemFk = qty.BoqItemFk,
						  BoqHeaderFk = qty.BoqHeaderFk,
						  BilHeaderFk = qty.BilHeaderFk,
						  Quantity = qty.Quantity,
						  Date = qty.PerformanceDate,
						  QuantityTypeFk = QuantityTypeBilling
					  };
					  liqs.Add(entity);
					listByLiRecordingLevel.Add(qty);
				  }
			  }
			  var list = liqs.GroupBy(r => new { r.BoqHeaderFk, r.BoqItemFk, r.Date, r.Quantity, r.BilHeaderFk, r.EstimateHeaderFk, r.LineItemFk })
				  .Select(g => g.First())
				  .ToList();

			  liqLogic.TakeDelivery(list);

			return listByLiRecordingLevel;
		  }

		#endregion

        /// <summary>
        /// 
        /// </summary>
        /// <param name="boqHeaderId"></param>
        /// <returns></returns>
        public IEnumerable<IBillBoqEntity> GetItemsByBoqHeaderId(int boqHeaderId)
        {
            return this.GetSearchList(e => e.BoqHeaderFk == boqHeaderId);
        }



		#region IDataBaseLogic, IDataBaseCreateLogic
		IEnumerable<IIdentifyable> IDataBaseLogic.Save(IEnumerable<IIdentifyable> ToSave)
		{
			IEnumerable<BilBoqEntity> list = ToSave.OfType<BilBoqEntity>().ToArray();

			return SaveBoqEntities(list);
		}

		void IDataBaseLogic.Delete(IEnumerable<IIdentifyable> ToDelete)
		{
			//DeleteEntities(ToDelete.Select(e => (BilBoqEntity)e).Where(e => e != null));
		}

		IIdentifyable IDataBaseCreateLogic.Create(Platform.Core.IdentificationData identificationData)
		{
			return Create();
		}
		#endregion

		#region IEntityCopier
		/// <summary>
		/// TODO: add comments
		/// </summary>
		/// <returns></returns>
		public IEntityAggregator GetAggregator() => null;

		/// <summary>
		/// TODO: fill in doc
		/// </summary>
		/// <param name="responsible"></param>
		/// <param name="entity"></param>
		/// <param name="intermediate"></param>
		/// <returns></returns>
		public IEnumerable<IIdentifyable> GetAggregatedForDeepCopy(IEntityRelationInfo responsible, IIdentifyable entity, IEntityRelationInfo intermediate)
		{
			List<IIdentifyable> res = null;
			if (responsible.GetIdentifier() == "sales.billing.header")
			{
				var bilHeaderId = entity.Id;
				return GetCompositeList(bilHeaderId);
			}
			return res;
		}

		/// <summary>
		/// TODO: add comments
		/// </summary>
		/// <param name="copy"></param>
		/// <param name="responsible"></param>
		public void ValidateCopy(IIdentifyable copy, IEntityRelationInfo responsible)
		{
		}

		/// <summary>
		/// TODO: add comments
		/// </summary>
		/// <param name="copies"></param>
		/// <param name="responsible"></param>
		public void ValidateCopies(IEnumerable<IIdentifyable> copies, IEntityRelationInfo responsible)
		{
		}

		/// <summary>
		/// TODO: add comments
		/// </summary>
		/// <param name="toBeCopied"></param>
		/// <returns></returns>
		public IIdentifyable Copy(IIdentifyable toBeCopied) => throw new NotImplementedException();

		/// <summary>
		/// TODO: add comments
		/// </summary>
		/// <param name="toBeCopied"></param>
		/// <param name="newOwner"></param>
		/// <returns></returns>
		public IIdentifyable Copy(IIdentifyable toBeCopied, IIdentifyable newOwner) => throw new NotImplementedException();

		/// <summary>
		/// TODO: add comments
		/// </summary>
		/// <param name="toBeCopied"></param>
		/// <param name="newOwner"></param>
		/// <param name="ownerMapping"></param>
		/// <returns></returns>
		public IDictionary<IIdentifyable, IIdentifyable> Copy(IEnumerable<IIdentifyable> toBeCopied, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping = null) => throw new NotImplementedException();

		/// <summary>
		/// TODO: fill in doc
		/// </summary>
		/// <param name="toBeCopied"></param>
		/// <returns></returns>
		public IDictionary<IIdentifyable, IIdentifyable> DeepCopy(IIdentifyable toBeCopied)
		{
			List<IIdentifyable> tbc = new List<IIdentifyable> { toBeCopied };
			return DoDeepCopy(tbc, null, null);
		}

		/// <summary>
		/// TODO: fill in doc
		/// </summary>
		/// <param name="toBeCopied"></param>
		/// <param name="newOwner"></param>
		/// <param name="ownerMapping"></param>
		/// <returns></returns>
		public IDictionary<IIdentifyable, IIdentifyable> DeepCopy(IIdentifyable toBeCopied, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping = null)
		{
			List<IIdentifyable> tbc = new List<IIdentifyable > { toBeCopied };
			return DoDeepCopy(tbc, newOwner, ownerMapping);
		}

		/// <summary>
		/// TODO: fill in doc
		/// </summary>
		/// <param name="toBeCopied"></param>
		/// <param name="newOwner"></param>
		/// <param name="ownerMapping"></param>
		/// <returns></returns>
		public IDictionary<IIdentifyable, IIdentifyable> DeepCopy(IEnumerable<IIdentifyable> toBeCopied, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping = null)
		{
			return DoDeepCopy(toBeCopied, newOwner, ownerMapping);
		}

		private IDictionary<IIdentifyable, IIdentifyable> DoDeepCopy(IEnumerable<IIdentifyable> toBeCopied, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping = null)
		{
			var res = new Dictionary<IIdentifyable, IIdentifyable>();

			var headerIds = toBeCopied.Select(e => (e as BilBoqCompositeEntity).BoqHeader.Id).ToList();
			var boqItemLogic = new BoqItemLogic();

			var boqItemCopyInfo = new BoqItemCopyInfo
			{
				MaintainBaseBoqLink = false,
				SyncBaseBoq = false,
				OwnerMapping = ownerMapping,
				NegateBoqValues = (copyType == BillingCopyTypes.CreditMemoCopy) && !IsTotalQuantityOnCopy,   // for BillCancellationCopy negation is done as post process using boqItemLogic.NegateBoqQuantities // TODO: could be improved, if boq logic copy option is adjusted
				UseNegatedTotalQuantities = copyType == BillingCopyTypes.BillCancellationCopy && IsTotalQuantityOnCopy,
				ResetQuantities = IsResetBoqQuantitiesOnCopy,
				TargetModuleName = "sales.billing"
			};
			var copyData = boqItemLogic.CopyBoqs(headerIds, boqItemCopyInfo);
			if (copyType == BillingCopyTypes.CreditMemoCopy || copyType == BillingCopyTypes.BillCancellationCopy)
			{
				boqItemLogic.CalculateBoqs(copyData);
			}

			int ix = 0;
			foreach (var orig in toBeCopied)
			{
				var cpy = copyData.ElementAt(ix);
				var bilBoq = Create() as BilBoqEntity;
				bilBoq.BilHeaderFk = newOwner.Id;
				bilBoq.BoqHeaderFk = cpy.BoqHeader.Id;
				var boqCompositeEntity = new BilBoqCompositeEntity(bilBoq, cpy.BoqHeader as BoqHeaderEntity, cpy.BoqRootItem as BoqItemEntity);
				res.Add(orig, boqCompositeEntity);

				if (ownerMapping == null)
				{
					ownerMapping = new Dictionary<IIdentifyable, IIdentifyable>();
				}
				ownerMapping.Add(orig, boqCompositeEntity);

				ix++;
			};
			boqItemLogic.SaveBoqs(copyData);

			return res;
		}

		/// <summary>
		/// TODO: add comments
		/// </summary>
		/// <param name="copies"></param>
		/// <returns></returns>
		public IEnumerable<IIdentifyable> AsFlatList(IEnumerable<IIdentifyable> copies) => throw new NotImplementedException();
		#endregion

		#region IndirectCostsBalancing

		/// <summary>
		/// Applies given 'Indirect Costs Balancing' Configuration to all BoQs of given bill id
		/// </summary>
		/// <param name="billId">bill header id</param>
		/// <param name="config">configuration</param>
		public void ApplyIndirectCostsBalancingConfig(int billId, IIndirectCostBalancingConfigDetail config)
		{
			var billBoqEntities = GetItemsByBillHeaderId(billId).ToList();
			if (!billBoqEntities.Any())
			{
				return;
			}

			using (var transaction = TransactionScopeFactory.Create())
			{
				var boqItemLogic = new BoqItemLogic();
				foreach (var boq in billBoqEntities.Where(boq => boq.BoqHeaderFk != null))
				{
					boqItemLogic.ApplyIndirectCostsBalancingConfig((int)boq.BoqHeaderFk, config);
				}
				transaction.Complete();
			}
		}

		/// <summary>
		/// Updates direct costs per unit to all BoQs of given bill id.
		/// </summary>
		/// <param name="billId">bill header id</param>
		public void UpdateDirectCostPerUnit(int billId)
		{
			var billBoqEntities = GetItemsByBillHeaderId(billId).ToList();
			if (!billBoqEntities.Any())
			{
				return;
			}

			using (var transaction = TransactionScopeFactory.Create())
			{
				var boqItemLogic = new BoqItemLogic();
				foreach (var boq in billBoqEntities.Where(boq => boq.BoqHeaderFk != null))
				{
					boqItemLogic.UpdateDirectCostPerUnit((int)boq.BoqHeaderFk);
				}
				transaction.Complete();
			}
		}

		#endregion

		#region IBoqCompositeLogic

		/// <summary>CreateNewBoqComposite</summary>
		public override IBoqCompositeEntity CreateNewBoqComposite(int billHeaderId, string boqReference, DescriptionTranslateType briefInfo, bool isGcBoq=false)
		{
			var billHeader = new SalesBillingLogic().GetBillById(billHeaderId);
			int projectId = -1;
			int? currencyFk = null;

			if (billHeader != null)
			{
				projectId  = billHeader.ProjectFk;
				currencyFk = billHeader.CurrencyFk;
			}

			var billBoqComposite = CreateSalesBoq(billHeaderId, projectId, boqReference, briefInfo, currencyFk, true);
			return billBoqComposite;
		}

		#endregion
	}
}
