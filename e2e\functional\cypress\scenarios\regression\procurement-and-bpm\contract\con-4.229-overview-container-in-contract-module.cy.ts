import { app, btn, cnt, sidebar, commonLocators, tile } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _estimatePage, _validate, _mainView, _procurementContractPage, _package, _modalView, _sidebar, _boqPage, _procurementPage } from "cypress/pages";
import type { DataCells } from 'cypress/pages/interfaces.d.ts'

const DOCUMENTS_PROJECT_DESC = _common.generateRandomString(4);
const WARRANTY_DESC = _common.generateRandomString(4);
const OUTLINE_SPECIFICATION = _common.generateRandomString(4);

let CONTRACT_PARAMETER: DataCells;
let CONTAINERS_CONTRACT;
let CONTAINER_COLUMNS_ITEM;
let CONTAINERS_ITEM;
let CONTAINERS_OVERVIEW;
let CONTAINERS_RECORD;
let CONTAINER_COLUMNS_ADVANCES
let CONTAINERS_ADVANCES
let CONTAINER_COLUMNS_WARRANTY
let CONTAINERS_WARRANTY
let CONTAINER_COLUMNS_PROCUREMENT_BOQ
let CONTAINER_COLUMNS_PACKAGE
let SUBCONTRACT_PARAMETER

describe('PCM- 4.229 | Overview container in contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('pcm/con-4.229-overview-container-in-contract-module.json').then((data) => {
            this.data = data;

            CONTAINER_COLUMNS_PACKAGE = this.data.CONTAINER_COLUMNS.PACKAGE
            CONTAINER_COLUMNS_PROCUREMENT_BOQ = this.data.CONTAINER_COLUMNS.PROCUREMENT_BOQ
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
            CONTAINERS_ITEM = this.data.CONTAINERS.ITEM;
            CONTAINERS_RECORD = this.data.CONTAINERS.RECORD;
            CONTAINERS_OVERVIEW = this.data.CONTAINERS.OVERVIEW;
            CONTAINER_COLUMNS_ADVANCES = this.data.CONTAINER_COLUMNS.ADVANCES
            CONTAINERS_ADVANCES = this.data.CONTAINERS.ADVANCES
            CONTAINER_COLUMNS_WARRANTY = this.data.CONTAINER_COLUMNS.WARRANTY
            CONTAINERS_WARRANTY = this.data.CONTAINERS.WARRANTY
            CONTAINER_COLUMNS_ITEM = this.data.CONTAINER_COLUMNS.ITEM

            CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESSPARTNER
            };
            SUBCONTRACT_PARAMETER = {
                [app.GridCells.PRC_STRUCTURE_FK]: CONTAINERS_CONTRACT.STRUCTURE_SERVICE,
                [app.GridCells.BPD_BUSINESS_PARTNER_FK]: CONTAINERS_CONTRACT.BUSINESS_PARTNER
            }
        });
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('PROJECT_NUMBER')).pinnedItem();
    });

    after(() => {
        cy.LOGOUT();
    });

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT)
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
        })
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT, 0)
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.STRUCTURE_CODE, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_RECORD.PROCUREMENT_STRUCTURE);
        _common.waitForLoaderToDisappear()

        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton("Yes")
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONSTATUS_FK)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
    })

    it('TC - Create Procurment BOQ for the respective contract', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 1);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
            _common.waitForLoaderToDisappear()
            _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS);
            _common.waitForLoaderToDisappear()
            _boqPage.enterRecord_ToCreate_procurementBoQs(CommonLocators.CommonKeys.MATERIAL, OUTLINE_SPECIFICATION, commonLocators.CommonLabels.CREATE_NEW_BOQ);
            _common.waitForLoaderToDisappear()
        });
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.PROCUREMENT_BOQS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.PROCUREMENT_BOQS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.PROCUREMENT_BOQS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.PROCUREMENT_BOQS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create items record and validate status in overview container", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.set_containerLayoutUnderEditView(commonLocators.CommonLabels.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 0)
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEM)
            _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.basuomfk], cnt.uuid.ITEMSCONTRACT)
        })
        _common.maximizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.ITEMSCONTRACT, app.GridCells.BAS_UOM_FK)
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT, app.GridCells.BAS_UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEM.UOM)
        _common.select_activeRowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.ITEMS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.ITEMS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.ITEMS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.ITEMS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create Document and verify status in overview", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_DOCUMENT, app.FooterTab.DOCUMENT, 1)
        })
        _common.create_newRecord(cnt.uuid.CONTRACT_DOCUMENT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.DOCUMENT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.DOCUMENT)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_DOCUMENT, app.FooterTab.DOCUMENT);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACT_DOCUMENT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.DOCUMENT)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.DOCUMENT)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create characteristics and verify status in overview", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.CHARACTERISTICS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.CHARACTERISTICS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CHARACTERISTICS, app.FooterTab.CHARATERISTICS);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACT_CHARACTERISTICS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.CHARACTERISTICS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.CHARACTERISTICS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create text assemblies module formatted text and verify status in overview", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.TEXT_ASSEMBLIES_MODULE_FORMATTED_TEXT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.TEXT_ASSEMBLIES_MODULE_FORMATTED_TEXT)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACTHEADERTEXT, app.FooterTab.TEXT_ASSEMBLIES_MODULE_FORMATTED_TEXT);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACTHEADERTEXT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.TEXT_ASSEMBLIES_MODULE_FORMATTED_TEXT)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.TEXT_ASSEMBLIES_MODULE_FORMATTED_TEXT)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()
    })


    it("TC - Create Certificate and Verify the Overview", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CERTIFICATES, app.FooterTab.CERTIFICATES, 1)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CERTIFICATES)
        })
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.COMMENT_TEXT)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.CERTIFICATES)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.CERTIFICATES)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.CERTIFICATES)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CERTIFICATES, app.FooterTab.CERTIFICATES);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.CERTIFICATES)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.CERTIFICATES)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create Generals and Verify the Overview", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.GENERALS_CONTRACT, app.FooterTab.GENERALS, 1)
        })
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.GENERALS_CONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.GENERALS_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.GENERALS_CONTRACT, app.GridCells.PRC_GENERALS_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, "Nachlass %")
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.GENERALS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.GENERALS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.GENERALS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.GENERALS_CONTRACT, app.FooterTab.GENERALS);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.GENERALS_CONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.GENERALS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.GENERALS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create record in milestones and verify status in overview container", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_MILESTONE, app.FooterTab.MILESTONES, 1)
        })
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACT_MILESTONE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACT_MILESTONE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_MILESTONE, app.FooterTab.MILESTONES, 1)
        })
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.MILESTONES)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.MILESTONES)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.MILESTONES)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_MILESTONE, app.FooterTab.MILESTONES);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACT_MILESTONE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.MILESTONES)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.MILESTONES)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create record in documents project and verify status in overview container", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_DOCUMENTS, app.FooterTab.DOCUMENTS_PROJECT, 1);
            _common.clear_subContainerFilter(cnt.uuid.PROJECT_DOCUMENTS)
        });
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROJECT_DOCUMENTS)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.edit_containerCell(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, DOCUMENTS_PROJECT_DESC)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.DOCUMENTS_PROJECT)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.DOCUMENTS_PROJECT)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.DOCUMENTS_PROJECT)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_DOCUMENTS, app.FooterTab.DOCUMENTS_PROJECT);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.PROJECT_DOCUMENTS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.DOCUMENTS_PROJECT)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.DOCUMENTS_PROJECT)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create subcontractors and verify status in overview container", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.FooterTab.SUBCONTRACTOR, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        _common.create_newRecord(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        _procurementPage.enterRecord_toCreateSubcontractor(cnt.uuid.CONTRACT_SUBCONTRACTOR, SUBCONTRACT_PARAMETER)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.GridCells.BPD_CONTACT_FK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.SUBCONTRACTORS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.SUBCONTRACTORS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.SUBCONTRACTORS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.FooterTab.SUBCONTRACTOR);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.SUBCONTRACTORS)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.SUBCONTRACTORS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create record in advances and verify status in overview container", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ADVANCES, app.FooterTab.ADVANCES);
            _common.setup_gridLayout(cnt.uuid.ADVANCES, CONTAINER_COLUMNS_ADVANCES)
        });
        _common.clear_subContainerFilter(cnt.uuid.ADVANCES)
        _common.create_newRecord(cnt.uuid.ADVANCES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.ADVANCES, app.GridCells.PRC_ADVANCE_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINERS_ADVANCES.ADVANCE_TYPE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.ADVANCES)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.ADVANCES)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.ADVANCES)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ADVANCES, app.FooterTab.ADVANCES);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.ADVANCES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.ADVANCES)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.ADVANCES)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create record in warranty and verify status in overview container", function () {

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_WARRANTY, app.FooterTab.WARRANTY, 1);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_WARRANTY, CONTAINER_COLUMNS_WARRANTY)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_WARRANTY)
        _common.create_newRecord(cnt.uuid.CONTRACT_WARRANTY)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, WARRANTY_DESC)
        _common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_SECURITY_FK, commonLocators.CommonKeys.LIST, CONTAINERS_WARRANTY.WARRANTY_SECURITY)
        _common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_WARRANTY, app.GridCells.BAS_WARRANTY_OBLIGATION_FK, commonLocators.CommonKeys.LIST, CONTAINERS_WARRANTY.WARRANTY_OBLIGATION)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.WARRANTY)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.WARRANTY)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.WARRANTY)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_WARRANTY, app.FooterTab.WARRANTY);
        });
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACT_WARRANTY)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_OVERVIEW, app.FooterTab.OVERVIEW, 2)
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        })
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.CONTRACT_OVERVIEW, CONTAINERS_OVERVIEW.WARRANTY)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_OVERVIEW)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.DESCRIPTION, CONTAINERS_OVERVIEW.WARRANTY)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT)
        _validate.verify_isIconPresentInCell(cnt.uuid.CONTRACT_OVERVIEW, app.GridCells.COUNT, app.GridCellIcons.ICO_TICK, CONTAINERS_RECORD.INVISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_OVERVIEW)
        _common.waitForLoaderToDisappear()
    })

})