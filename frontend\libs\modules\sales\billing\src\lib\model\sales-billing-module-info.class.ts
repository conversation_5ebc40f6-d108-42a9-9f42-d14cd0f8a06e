/*
 * Copyright(c) RIB Software GmbH
 */

import { DocumentProjectEntityInfoService } from '@libs/documents/shared';
import { IBilHeaderEntity } from '@libs/sales/interfaces';
import { BusinessModuleInfoBase, EntityInfo, ITranslationContainerInfo } from '@libs/ui/business-base';
import { SALES_BILLING_FORM_DATA_ENTITY_INFO } from './entity-info/sales-billing-form-data-entity-info.model';
import { SalesBillingDocumentProjectDataService } from '../services/sales-billing-document-project-data.service';
import { SALES_BILLING_BILLS_ENTITY_INFO } from './entity-info/sales-billing-bills-entity-info.model';
import { SALES_BILLING_GENERALS_ENTITY_INFO } from './entity-info/sales-billing-generals-entity-info.model';
import { SALES_BILLING_CHARACTERISTIC2_ENTITY_INFO } from './entity-info/sales-billing-characteristic2-entity-info.model';
import { SALES_BILLING_CHARACTERISTICS_ENTITY_INFO } from './entity-info/sales-billing-characteristics-entity-info.model';
import { SALES_BILLING_SCHEMA_ENTITY_INFO } from './entity-info/sales-billing-schema-entity-info.model';
import { SALES_BILLING_ACTUAL_CERTIFICATE_ENTITY_INFO } from './entity-info/sales-billing-actual-certificate-entity-info.model';
import { SALES_BILLING_ITEM_ENTITY_INFO } from './entity-info/sales-billing-item-entity-info.model';
import { SALES_BILLING_TRANSACTION_ENTITY_INFO } from './entity-info/sales-billing-transaction-entity-info.model';
import { SALES_BILLING_DOCUMENT_ENTITY_INFO } from './entity-info/sales-billing-document-entity-info.model';
import { SALES_BILLING_VALIDATION_ENTITY_INFO } from './entity-info/sales-billing-validation-entity-info.model';
import { ContainerDefinition, IContainerDefinition } from '@libs/ui/container-system';
import { SALES_BILLING_PIN_BOARD_CONTAINER_DEFINITION } from './entity-info/sales-billing-pin-board-container-info.model';
import { DrawingContainerDefinition } from '@libs/model/shared';
import { SALES_BILLING_ACCRUAL_ENTITY_INFO } from './entity-info/sales-billing-accrual-entity-info.model';

import { SALES_BILLING_PREVIOUS_BILLS_ENTITY_INFO } from './entity-info/sales-billing-previous-bills-entity-info.model';
import { SALES_BILLING_CLERK_ENTITY_INFO } from './entity-info/sales-billing-clerk-entity-info.model';
import { SALES_BILLING_BOQ_ENTITY_INFO } from './entity-info/sales-billing-boq-entity-info.model';
import { SALES_BILLING_PAYMENT_ENTITY_INFO } from './entity-info/sales-billing-payment-entity-info.model';
import { inject, Injector, runInInjectionContext } from '@angular/core';
import { BasicSharedChangeProjectDocumentRubricCategoryFeatureRegisterService, BasicsSharedChangeCertificateStatusWizardRegisterService } from '@libs/basics/shared';
import { ChangeProjectDocumentRubricCategoryWizardService } from '../wizards/sales-billing-change-project-document-wizard.service';
import { SalesBillingChangeCertificateStatusService } from '../wizards/sales-billing-change-certificate-status.service';
import { SALES_BILLING_PRICE_CONDITION_ENTITY_INFO } from './entity-info/sales-billing-price-condition-entity-info.model';
import { SalesBillingBoqItemDataService } from '../services/sales-billing-boq-item-data.service';
import { BoqMainModuleInfo, BoqSplitQuantityDataService } from '@libs/boq/main';
import { SALES_BILLING_INDIRECTS_BALANCING_ENTITY_INFO } from './entity-info/sales-billing-indirects-balancing-entity-info.model';
import { SALES_BILLING_WIP_DOCUMENT_ENTITY_INFO } from './entity-info/sales-billing-wip-document-entity-info.model';

/**
 * The module info object for the `sales.billing` content module.
 */
export class SalesBillingModuleInfo extends BusinessModuleInfoBase {
	private static _instance?: SalesBillingModuleInfo;

	private readonly salesBillingDocumentProjectEntityInfo = DocumentProjectEntityInfoService.create<IBilHeaderEntity>(this.internalPascalCasedModuleName, SalesBillingDocumentProjectDataService);
	/**
	 * Returns the singleton instance of the class.
	 *
	 * @return The singleton instance.
	 */
	public static get instance(): SalesBillingModuleInfo {
		if (!this._instance) {
			this._instance = new SalesBillingModuleInfo();
		}

		return this._instance;
	}

	private constructor() {
		super();
	}

	public override initializeModule(injector: Injector) {
		super.initializeModule(injector);
		injector.get(BasicSharedChangeProjectDocumentRubricCategoryFeatureRegisterService).registerFeature(injector, this.internalModuleName, this.featureRegistry, ChangeProjectDocumentRubricCategoryWizardService);
		injector.get(BasicsSharedChangeCertificateStatusWizardRegisterService).registerFeature(injector, this.internalModuleName, this.featureRegistry, SalesBillingChangeCertificateStatusService);
	}
	/**
	 * Returns the internal name of the module.
	 *

	}

	/**
	 * Returns the internal name of the module.
	 *
	 * @return The internal module name.
	 */
	public override get internalModuleName(): string {
		return 'sales.billing';
	}

	/**
	 * Returns the internal pascal case name of the module.
	 *
	 * @return The internal pascal case module name.
	 */
	public override get internalPascalCasedModuleName(): string {
		return 'Sales.Billing';
	}

	/**
	 * Returns the entity definitions in the module.
	 *
	 * @return The entity definitions.
	 */
	public override get entities(): EntityInfo[] {
		return [
			SALES_BILLING_BILLS_ENTITY_INFO,
			SALES_BILLING_FORM_DATA_ENTITY_INFO,
			SALES_BILLING_CHARACTERISTICS_ENTITY_INFO,
			...this.salesBillingDocumentProjectEntityInfo,
			SALES_BILLING_ACTUAL_CERTIFICATE_ENTITY_INFO,
			SALES_BILLING_TRANSACTION_ENTITY_INFO,
			SALES_BILLING_GENERALS_ENTITY_INFO,
			SALES_BILLING_CHARACTERISTIC2_ENTITY_INFO,
			SALES_BILLING_SCHEMA_ENTITY_INFO,
			SALES_BILLING_DOCUMENT_ENTITY_INFO,
			SALES_BILLING_CLERK_ENTITY_INFO,
			SALES_BILLING_PAYMENT_ENTITY_INFO,
			SALES_BILLING_ACCRUAL_ENTITY_INFO,
			SALES_BILLING_VALIDATION_ENTITY_INFO,
			SALES_BILLING_PREVIOUS_BILLS_ENTITY_INFO,
			SALES_BILLING_ITEM_ENTITY_INFO,
			...SALES_BILLING_PRICE_CONDITION_ENTITY_INFO,
			this.boqItemEntityInfo,
			SALES_BILLING_BOQ_ENTITY_INFO,
			SALES_BILLING_INDIRECTS_BALANCING_ENTITY_INFO,
			SALES_BILLING_WIP_DOCUMENT_ENTITY_INFO,
			this.boqSplitQuantityEntityInfo,
		];
	}

	public override get preloadedTranslations(): string[] {
		return super.preloadedTranslations.concat([
			'sales.common',
			'sales.wip',
			'procurement.invoice',
			'basics.customize',
			'project.main',
			'object.main',
			'basics.bank',
			'basics.characteristic',
			'documents.shared',
			'basics.shared',
			'businesspartner.certificate',
			'model.wdeviewer',
			'documents.shared',
			'procurement.common',
			'estimate.main',
			'basics.costcodes',
			'boq.main',
			'basics.material',
			'sales.bid'
		]);
	}

	protected override get containers():  (ContainerDefinition | IContainerDefinition)[]{
		return super.containers.concat([
			DrawingContainerDefinition.createPDFViewer({
				uuid: '20ee0cb4411e4d159a030efd0c562fcb'
			}),
			SALES_BILLING_PIN_BOARD_CONTAINER_DEFINITION
		]);
	}

	protected override get translationContainer(): string | ITranslationContainerInfo | undefined {
        return '0c1c039b62e8439a8c82eb287bac3441';
    }

	private readonly boqItemEntityInfo: EntityInfo = BoqMainModuleInfo.createBoqItemEntityInfo((ctx) => ctx.injector.get(SalesBillingBoqItemDataService), '65294188ea2f4aeea7f1243ecf096434');

	private readonly boqSplitQuantityEntityInfo = BoqMainModuleInfo.createBoqSplitQuantityEntityInfo(
		ctx => runInInjectionContext(ctx.injector, () =>
			new BoqSplitQuantityDataService(inject(SalesBillingBoqItemDataService))
		),
		'd5f64179c92a489d9df9adce90bc657e',
		'65294188ea2f4aeea7f1243ecf096434'
	);
}
