/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ILazyInjectionContext, PlatformModuleManagerService } from '@libs/platform/common';

import { PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN, PPS_COMMON_CHARACTERISTIC_LOOKUP_PROVIDER_TOKEN, PPS_PLANNED_QUANTITY_PROPERTY_LOOKUP_PROVIDER_TOKEN, PPS_MATERIAL_PRODUCT_DESCRIPTION_LOOKUP_PROVIDER_TOKEN, ENGINEERING_HEADER_LOOKUP_PROVIDER_TOKEN } from '@libs/productionplanning/interfaces';








export const LAZY_INJECTABLES: LazyInjectableInfo[] =[
LazyInjectableInfo.create('productionplanning.engineering.EngineeringHeaderLookupProviderService', ENGINEERING_HEADER_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/productionplanning/engineering');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.EngineeringHeaderLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('productionplanning.processconfiguration.PpsProcessTemplateLookupProviderService', PPS_PROCESS_TEMPLATE_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/productionplanning/processconfiguration');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.PpsProcessTemplateLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('productionplanning.common.PpsCommonCharacteristicLookupProviderService', PPS_COMMON_CHARACTERISTIC_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/productionplanning/common');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.PpsCommonCharacteristicLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('productionplanning.formulaconfiguration.PpsPlannedQuantityPropertyLookupProviderService', PPS_PLANNED_QUANTITY_PROPERTY_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/productionplanning/formulaconfiguration');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.PpsPlannedQuantityPropertyLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('productionplanning.ppsmaterial.MdcProductDescriptionLookupProviderService', PPS_MATERIAL_PRODUCT_DESCRIPTION_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/productionplanning/ppsmaterial');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.MdcProductDescriptionLookupProviderService) : null;
		
	}),
];
 