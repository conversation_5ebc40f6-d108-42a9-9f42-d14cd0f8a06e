/*
 * Copyright(c) RIB Software GmbH
 */

import { InjectionToken } from '@angular/core';

/**
 * Procurement common payment schedule total source entity interface
 */
export interface IPrcCommonPaymentScheduleTotalSourceEntity {
	TypeCode?: string;
	TypeDescription?: string;
	TypeId?: number | null;
	Id: number;
	ValueNet: number;
	ValueNetOc: number;
	ValueTax: number;
	ValueTaxOc: number;
	ValueGross: number;
	ValueGrossOc: number;
}

/**
 * Procurement common payment schedule total source context entity
 */
export interface IPrcCommonPaymentScheduleTotalSourceContextEntity {
	ParentConfigurationFk?: number, // will be deleted in DEV-21356
	ParentId?: number,
	VatPercent: number,
	SourceNetOc: number,
	SourceGrossOc: number,
	SourceId?: number | null,
	Url?: string, // will be deleted in DEV-21356
	ModuleName?: string // will be updated to ModuleName: string in DEV-21356
}

/**
 * Injection token of procurement common payment schedule context entity
 */
export const PRC_PAYMENT_SCHEDULE_TOTAL_SOURCE_CONTEXT_ENTITY = new InjectionToken<IPrcCommonPaymentScheduleTotalSourceContextEntity>('PRC_PAYMENT_SCHEDULE_TOTAL_SOURCE_CONTEXT_ENTITY');