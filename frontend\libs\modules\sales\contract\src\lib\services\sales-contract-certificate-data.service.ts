/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, DataServiceFlatLeaf, IDataServiceChildRoleOptions, IEntityProcessor, EntityDateProcessorFactory, IEntitySchemaId } from '@libs/platform/data-access';
import { SalesContractContractsDataService } from './sales-contract-contracts-data.service';
import { SalesContractContractsComplete } from '../model/complete-class/sales-contract-contracts-complete.class';
import { IOrdCertificateEntity, IOrdHeaderEntity } from '@libs/sales/interfaces';
import { BasicsSharedNewEntityValidationProcessorFactory } from '@libs/basics/shared';
import { SalesContractCertificateValidationService } from './validations/sales-contract-certificate-validation.service';
import _ from 'lodash';

@Injectable({
	providedIn: 'root'
})

export class SalesContractCertificatesDataService extends DataServiceFlatLeaf<IOrdCertificateEntity, IOrdHeaderEntity, SalesContractContractsComplete> {
	private readonly validationProcessor = inject(BasicsSharedNewEntityValidationProcessorFactory);
	private readonly parentService = inject(SalesContractContractsDataService);

	public constructor(salesContractContractsDataService: SalesContractContractsDataService) {
		const options: IDataServiceOptions<IOrdCertificateEntity> = {
			apiUrl: 'sales/contract/certificate',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'listByParent',
				usePost: true
			},
			createInfo:{
				prepareParam: ident => {
                    return { pKey1 : ident.pKey1!};
                }
			},
			roleInfo: <IDataServiceChildRoleOptions<IOrdCertificateEntity, IOrdHeaderEntity, SalesContractContractsComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'OrdCertificate',
				parent: salesContractContractsDataService
			}
		};

		super(options);

		this.processor.addProcessor(this.provideDateProcessor());
		this.processor.addProcessor([this.provideNewEntityValidationProcessor()]);
	}

	protected get entitySchemaId(): IEntitySchemaId {
		return {moduleSubModule: 'Sales.Contract', typeName: 'OrdCertificateDto'};
	}

	public override isParentFn(parentKey: IOrdHeaderEntity, entity: IOrdCertificateEntity): boolean {
		return entity.HeaderFk === parentKey.Id;
	}

	public override registerModificationsToParentUpdate(parentUpdate: SalesContractContractsComplete, modified: IOrdCertificateEntity[], deleted: IOrdCertificateEntity[]): void {
		if (modified && modified.some(() => true)) {
			parentUpdate.OrdCertificateToSave = modified;
		}
		if (deleted && deleted.some(() => true)) {
			parentUpdate.OrdCertificateToDelete = deleted;
		}
	}

	public override getSavedEntitiesFromUpdate(complete: SalesContractContractsComplete): IOrdCertificateEntity[] {
		return (complete && complete.OrdCertificateToSave) ? complete.OrdCertificateToSave : [];
	}

	private isReadonly() {
		return _.get(this.parentService.getSelection()[0], 'IsReadonlyStatus') || false;
	}

	public override canCreate(): boolean {
		return super.canCreate() && !this.isReadonly();
	}

	public override canDelete(): boolean {
		return super.canDelete() && !this.isReadonly();
	}

	private provideNewEntityValidationProcessor() {
		return this.validationProcessor.createProcessor(SalesContractCertificateValidationService, this.entitySchemaId);
	}

	private provideDateProcessor(): IEntityProcessor<IOrdCertificateEntity> {
		const dateProcessorFactory = inject(EntityDateProcessorFactory);
		return dateProcessorFactory.createProcessorFromSchemaInfo<IOrdCertificateEntity>(this.entitySchemaId);
	}

}












