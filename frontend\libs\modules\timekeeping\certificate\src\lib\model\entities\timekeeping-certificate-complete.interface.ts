/*
 * Copyright(c) RIB Software GmbH
 */
import { CompleteIdentification } from '@libs/platform/common';
import { ICertificateEntity, ICertificateDocumentEntity } from '@libs/resource/interfaces';
import { ICertifiedEmployeeEntity } from '@libs/timekeeping/interfaces';


export interface ITimekeepingCertificateComplete extends CompleteIdentification<ICertificateEntity> {
	MainItemId: number | null;

	Certificates?: ICertificateEntity[] | null;

	CertifiedEmployeeToSave?: ICertifiedEmployeeEntity[] | null;
	CertifiedEmployeeToDelete?: ICertifiedEmployeeEntity[] | null;

	DocumentsToSave?: ICertificateDocumentEntity[] | null;
	DocumentsToDelete?: ICertificateDocumentEntity[] | null;
}