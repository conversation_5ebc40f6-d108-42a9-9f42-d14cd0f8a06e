import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _boqPage, _common, _materialPage, _package, _procurementContractPage, _procurementPage, _projectPage, _schedulePage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const CONTRACT_DESCRIPTION_1 = _common.generateRandomString(5);
const CODE = _common.generateRandomString(4);
const CODE_1 = _common.generateRandomString(4);

let CONTAINERS_CONTRACT;
let PROCUREMENT_CONTRACT_PARAMETER: DataCells;
let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_BP_EVALUATION_CHART;

describe('PCM- 4.126 | Business partner evaluation chart container in Contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/con-4.126-business-partner-evaluation-chart-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }
            CONTAINER_COLUMNS_BP_EVALUATION_CHART = this.data.CONTAINER_COLUMNS.BP_EVALUATION_CHART
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it('TC - Create new contract', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('projectName')).pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESCRIPTION_1)
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, CONTRACT_DESCRIPTION_1).pinnedItem()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Verify create new record & delete record in business partner evaluation container', function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART, app.FooterTab.BUSINESS_PARTNER_EVALUATION_CHART, 2)
            _common.set_columnAtTop([CONTAINER_COLUMNS_BP_EVALUATION_CHART.code, CONTAINER_COLUMNS_BP_EVALUATION_CHART.checked], cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART)
        _common.waitForLoaderToDisappear()
        _common.inputField_fromModal(commonLocators.CommonElements.ROW, commonLocators.CommonLabels.CODE, 0, app.InputFields.DOMAIN_TYPE_CODE).clear({ force: true })
        _common.waitForLoaderToDisappear()
        _common.inputField_fromModal(commonLocators.CommonElements.ROW, commonLocators.CommonLabels.CODE, 0, app.InputFields.DOMAIN_TYPE_CODE).type(CODE, { force: true })
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART, CODE)
        _common.select_rowHasValue(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART, CODE)
        _common.clickOn_toolbarButton(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART, btn.IconButtons.ICO_REC_DELETE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, CONTRACT_DESCRIPTION_1)
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, CONTRACT_DESCRIPTION_1)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART, app.FooterTab.BUSINESS_PARTNER_EVALUATION_CHART, 2)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART)
        _validate.verify_recordNotPresentInContainer(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART, CODE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART)
        _common.create_newRecord(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART)
        _common.waitForLoaderToDisappear()
        _common.inputField_fromModal(commonLocators.CommonElements.ROW, commonLocators.CommonLabels.CODE, 0, app.InputFields.DOMAIN_TYPE_CODE).clear({ force: true })
        _common.waitForLoaderToDisappear()
        _common.inputField_fromModal(commonLocators.CommonElements.ROW, commonLocators.CommonLabels.CODE, 0, app.InputFields.DOMAIN_TYPE_CODE).type(CODE_1, { force: true })
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART, CODE_1)
        _common.select_rowHasValue(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART, CODE_1)
        _common.set_cellCheckboxValue(cnt.uuid.BUSINESS_PARTNER_EVALUATION_CHART, app.GridCells.CHECKED, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })
});