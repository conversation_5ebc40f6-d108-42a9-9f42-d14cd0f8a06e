/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable } from '@angular/core';
import { LookupSimpleEntity, UiCommonLookupEndpointDataService } from '@libs/ui/common';

/**
 * Service to provide lookup data for Sales Date Type
 */
@Injectable({
    providedIn: 'root'
})

export class SalesCommonSalesDateTypeLookupService<TEntity extends object> extends UiCommonLookupEndpointDataService<LookupSimpleEntity, TEntity> {
    public constructor() {
        super({
            httpRead: {route: 'basics/lookupdata/master/', endPointRead: 'getlist?lookup=salesdatetype'}
        }, {
            uuid: '4bd227e08bbf4982a84be0a1c6dbcf27',
            valueMember: 'Id',
            displayMember: 'DescriptionInfo.Translated'
        });
    }
}