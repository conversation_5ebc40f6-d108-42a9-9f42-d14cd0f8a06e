﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Procurement.PublicApi.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Procurement.PublicApi.BusinessComponents.ConHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("CON_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(5)]
    public partial class ConHeaderApiEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new ConHeaderApiEntity object.
        /// </summary>
        public ConHeaderApiEntity()
        {
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConStatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ConStatusId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ConStatusDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string CompanyCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public virtual int? ProjectId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ProjectCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ProjectDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PackageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PackageFk")]
        public virtual int? PackageId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PackageCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PackageCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PackageDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_DESC", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string PackageDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TaxCodeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TaxCodeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int TaxCodeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TaxCodeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_CODE", TypeName = "nvarchar(16)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string TaxCodeCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TaxCodeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_DESC", TypeName = "nvarchar(2000)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string TaxCodeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public virtual int? ClerkPrcId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkPrcCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkPrcDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 17)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public virtual int? ClerkReqId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 18)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkReqCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 19)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkReqDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasCurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CurrencyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string CurrencyDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Exchangerate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 22)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal Exchangerate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrjChangeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_ID", TypeName = "int", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectChangeFk")]
        public virtual int? PrjChangeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrjChangeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_CODE", TypeName = "nvarchar(16)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PrjChangeCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrjChangeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_DESC", TypeName = "nvarchar(252)", Order = 25)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string PrjChangeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_ID", TypeName = "int", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConHeaderFk")]
        public virtual int? ConHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_CODE", TypeName = "nvarchar(16)", Order = 27)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ConHeaderCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_DESC", TypeName = "nvarchar(252)", Order = 28)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ConHeaderDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Haschanges in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("HASCHANGES", TypeName = "bit", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("HasChanges")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Haschanges {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MaterialCatalogId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_ID", TypeName = "int", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("MaterialCatalogFk")]
        public virtual int? MaterialCatalogId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MaterialCatalogCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_CODE", TypeName = "nvarchar(16)", Order = 31)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string MaterialCatalogCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MaterialCatalogDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_DESC", TypeName = "nvarchar(2000)", Order = 32)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MaterialCatalogDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_HEADER_ID", TypeName = "int", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcHeaderFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 34)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcConfigurationId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_ID", TypeName = "int", Order = 35)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermFiFk")]
        public virtual int? PaymentTermFiId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_CODE", TypeName = "nvarchar(16)", Order = 36)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermFiCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_DESC", TypeName = "nvarchar(2000)", Order = 37)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermFiDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermPaFk")]
        public virtual int? PaymentTermPaId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_CODE", TypeName = "nvarchar(16)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermPaCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_DESC", TypeName = "nvarchar(2000)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermPaDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 42)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public virtual string SearchPattern {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateOrdered in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_ORDERED", TypeName = "date", Order = 44)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateOrdered")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateOrdered {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateReported in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REPORTED", TypeName = "date", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateReported")]
        public virtual System.DateTime? DateReported {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateCanceled in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CANCELED", TypeName = "date", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCanceled")]
        public virtual System.DateTime? DateCanceled {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 47)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDelivery")]
        public virtual System.DateTime? DateDelivery {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateCallofffrom in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CALLOFFFROM", TypeName = "date", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCallofffrom")]
        public virtual System.DateTime? DateCallofffrom {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateCalloffto in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CALLOFFTO", TypeName = "date", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCalloffto")]
        public virtual System.DateTime? DateCalloffto {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsNotAccrualPrr in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISNOTACCRUAL_PRR", TypeName = "bit", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsNotAccrualPrr {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_TYPE_ID", TypeName = "int", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ConTypeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 52)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ConTypeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcAwardmethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_ID", TypeName = "int", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AwardmethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcAwardmethodId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcAwardmethodDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_DESC", TypeName = "nvarchar(2000)", Order = 54)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcAwardmethodDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcContracttypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_ID", TypeName = "int", Order = 55)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContracttypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcContracttypeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcContracttypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_DESC", TypeName = "nvarchar(2000)", Order = 56)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcContracttypeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcControllingunitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingUnitFk")]
        public virtual int? MdcControllingunitId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcControllingunitCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(16)", Order = 58)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string MdcControllingunitCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcControllingunitDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcControllingunitDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinesspartnerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 60)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int BusinesspartnerId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinesspartnerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 61)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string BusinesspartnerDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SubsidiaryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 62)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public virtual int? SubsidiaryId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SubsidiaryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SubsidiaryDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 64)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public virtual int? SupplierId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 65)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 66)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ContactId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_ID", TypeName = "int", Order = 67)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContactFk")]
        public virtual int? ContactId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Businesspartner2Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER2_ID", TypeName = "int", Order = 68)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartner2Fk")]
        public virtual int? Businesspartner2Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Businesspartner2Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER2_DESC", TypeName = "nvarchar(252)", Order = 69)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Businesspartner2Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Subsidiary2Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY2_ID", TypeName = "int", Order = 70)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Subsidiary2Fk")]
        public virtual int? Subsidiary2Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Subsidiary2Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY2_DESC", TypeName = "nvarchar(252)", Order = 71)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Subsidiary2Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Supplier2Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER2_ID", TypeName = "int", Order = 72)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Supplier2Fk")]
        public virtual int? Supplier2Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Supplier2Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER2_CODE", TypeName = "nvarchar(252)", Order = 73)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Supplier2Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Supplier2Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER2_DESC", TypeName = "nvarchar(252)", Order = 74)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Supplier2Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Contact2Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT2_ID", TypeName = "int", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Contact2Fk")]
        public virtual int? Contact2Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcIncotermId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_ID", TypeName = "int", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IncotermFk")]
        public virtual int? PrcIncotermId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcIncotermDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_DESC", TypeName = "nvarchar(2000)", Order = 77)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcIncotermDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyInvoiceId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_INVOICE_ID", TypeName = "int", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyInvoiceFk")]
        public virtual int? CompanyInvoiceId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyInvoiceCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_INVOICE_CODE", TypeName = "nvarchar(16)", Order = 79)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string CompanyInvoiceCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AddressId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_ID", TypeName = "int", Order = 80)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AddressFk")]
        public virtual int? AddressId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AddressDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_DESC", TypeName = "nvarchar(2000)", Order = 81)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string AddressDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcBillingSchemaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_ID", TypeName = "int", Order = 82)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BillingSchemaFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int MdcBillingSchemaId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcBillingSchemaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 83)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcBillingSchemaDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinesspartnerAgentId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_AGENT_ID", TypeName = "int", Order = 84)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerAgentFk")]
        public virtual int? BusinesspartnerAgentId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinesspartnerAgentDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_AGENT_DESC", TypeName = "nvarchar(252)", Order = 85)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BusinesspartnerAgentDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcPackage2headerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_ID", TypeName = "int", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Package2HeaderFk")]
        public virtual int? PrcPackage2headerId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcPackage2headerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_DESC", TypeName = "nvarchar(252)", Order = 87)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string PrcPackage2headerDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CodeQuotation in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE_QUOTATION", TypeName = "nvarchar(20)", Order = 88)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CodeQuotation")]
        [System.ComponentModel.DataAnnotations.StringLength(20)]
        public virtual string CodeQuotation {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateQuotation in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_QUOTATION", TypeName = "date", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateQuotation")]
        public virtual System.DateTime? DateQuotation {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 90)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 91)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined1 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 92)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined2 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 93)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined3 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 94)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined4 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 95)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined5 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConfirmationCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONFIRMATION_CODE", TypeName = "nvarchar(252)", Order = 96)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConfirmationCode")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ConfirmationCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConfirmationDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONFIRMATION_DATE", TypeName = "date", Order = 97)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConfirmationDate")]
        public virtual System.DateTime? ConfirmationDate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ExternalCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXTERNAL_CODE", TypeName = "nvarchar(252)", Order = 98)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExternalCode")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ExternalCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_ID", TypeName = "int", Order = 104)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermAdFk")]
        public virtual int? PaymentTermAdId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_CODE", TypeName = "nvarchar(16)", Order = 105)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermAdCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_DESC", TypeName = "nvarchar(2000)", Order = 106)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermAdDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcCopyModeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_ID", TypeName = "int", Order = 107)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcCopyModeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcCopyModeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcCopymodeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_DESC", TypeName = "nvarchar(2000)", Order = 108)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcCopymodeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateEffective in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_EFFECTIVE", TypeName = "date", Order = 109)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateEffective")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateEffective {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DatePenalty in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_PENALTY", TypeName = "date", Order = 110)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DatePenalty")]
        public virtual System.DateTime? DatePenalty {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PenaltyPercentperday in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PENALTY_PERCENTPERDAY", TypeName = "numeric(9,3)", Order = 111)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PenaltyPercentPerDay")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal PenaltyPercentperday {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PenaltyPercentmax in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PENALTY_PERCENTMAX", TypeName = "numeric(9,3)", Order = 112)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PenaltyPercentMax")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal PenaltyPercentmax {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PenaltyComment in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PENALTY_COMMENT", TypeName = "nvarchar(255)", Order = 113)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PenaltyComment")]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public virtual string PenaltyComment {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for VatGroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 114)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public virtual int? VatGroupId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for VatGroupDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 115)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string VatGroupDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProvingPeriod in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROVING_PERIOD", TypeName = "int", Order = 116)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProvingPeriod")]
        public virtual int? ProvingPeriod {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProvingDealdline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROVING_DEALDLINE", TypeName = "int", Order = 117)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProvingDealdline")]
        public virtual int? ProvingDealdline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ApprovalPeriod in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("APPROVAL_PERIOD", TypeName = "int", Order = 118)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ApprovalPeriod")]
        public virtual int? ApprovalPeriod {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ApprovalDealdline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("APPROVAL_DEALDLINE", TypeName = "int", Order = 119)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ApprovalDealdline")]
        public virtual int? ApprovalDealdline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Isfreeitemsallowed in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFREEITEMSALLOWED", TypeName = "bit", Order = 120)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsFreeItemsAllowed")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Isfreeitemsallowed {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BankId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_ID", TypeName = "int", Order = 121)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BankFk")]
        public virtual int? BankId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcPriceListId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_PRICE_LIST_ID", TypeName = "int", Order = 122)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("MdcPriceListFk")]
        public virtual int? MdcPriceListId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcPriceListDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_PRICE_LIST_DESC", TypeName = "nvarchar(2000)", Order = 123)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcPriceListDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_ID", TypeName = "int", Order = 124)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("QtnHeaderFk")]
        public virtual int? QtnHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_CODE", TypeName = "nvarchar(16)", Order = 125)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string QtnHeaderCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_DESC", TypeName = "nvarchar(252)", Order = 126)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string QtnHeaderDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 127)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int LanguageId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignBusinessId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_BUSINESS_ID", TypeName = "int", Order = 128)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignBusinessFk")]
        public virtual int? BasAccassignBusinessId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignBusinessDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_BUSINESS_DESC", TypeName = "nvarchar(2000)", Order = 129)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasAccassignBusinessDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignControlId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CONTROL_ID", TypeName = "int", Order = 130)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignControlFk")]
        public virtual int? BasAccassignControlId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignControlDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CONTROL_DESC", TypeName = "nvarchar(2000)", Order = 131)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasAccassignControlDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignAccountId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_ACCOUNT_ID", TypeName = "int", Order = 132)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignAccountFk")]
        public virtual int? BasAccassignAccountId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignAccountDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_ACCOUNT_DESC", TypeName = "nvarchar(2000)", Order = 133)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasAccassignAccountDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignConTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_ID", TypeName = "int", Order = 134)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignConTypeFk")]
        public virtual int? BasAccassignConTypeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasAccassignConTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 135)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BasAccassignConTypeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ExecutionStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXECUTION_START", TypeName = "date", Order = 136)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExecutionStart")]
        public virtual System.DateTime? ExecutionStart {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ExecutionEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXECUTION_END", TypeName = "date", Order = 137)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExecutionEnd")]
        public virtual System.DateTime? ExecutionEnd {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OrdHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ORD_HEADER_ID", TypeName = "int", Order = 138)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("OrdHeaderFk")]
        public virtual int? OrdHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OrdHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ORD_HEADER_CODE", TypeName = "nvarchar(16)", Order = 139)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string OrdHeaderCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OrdHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ORD_HEADER_DESC", TypeName = "nvarchar(252)", Order = 140)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string OrdHeaderDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OverallDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT", TypeName = "numeric(19,7)", Order = 141)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscount {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OverallDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 142)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscountOc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OverallDiscountPercent in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_PERCENT", TypeName = "numeric(9,3)", Order = 143)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscountPercent {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 144)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int SalesTaxMethodId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 145)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string SalesTaxMethodDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_ID", TypeName = "int", Order = 146)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BoqWicCatFk")]
        public virtual int? BoqWicCatId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_CODE", TypeName = "nvarchar(16)", Order = 147)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string BoqWicCatCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatBoqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_BOQ_ID", TypeName = "int", Order = 148)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BoqWicCatBoqFk")]
        public virtual int? BoqWicCatBoqId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatBoqReference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_BOQ_REFERENCE", TypeName = "nvarchar(252)", Order = 149)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BoqWicCatBoqReference {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ReqHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_HEADER_ID", TypeName = "int", Order = 150)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReqHeaderFk")]
        public virtual int? ReqHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ValidFrom in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VALIDFROM", TypeName = "datetime", Order = 151)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? ValidFrom {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ValidTo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VALIDTO", TypeName = "datetime", Order = 152)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? ValidTo {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BaselineUpdate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_UPDATE", TypeName = "datetime", Order = 153)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselineUpdate")]
        public virtual System.DateTime? BaselineUpdate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StructureDes in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STRUCTURE_DES", TypeName = "nvarchar(2000)", Order = 154)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string StructureDes {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StructureCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STRUCTURE_CODE", TypeName = "nvarchar(16)", Order = 155)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string StructureCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StructureId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STRUCTURE_ID", TypeName = "int", Order = 156)]
        public virtual int? StructureId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsFramework in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFRAMEWORK", TypeName = "bit", Order = 157)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsFramework {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 158)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual int? BasLanguageFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 159)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BasLanguageDesc {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            ConHeaderApiEntity obj = new ConHeaderApiEntity();
            obj.Id = Id;
            obj.ConStatusId = ConStatusId;
            obj.ConStatusDescription = ConStatusDescription;
            obj.CompanyId = CompanyId;
            obj.CompanyCode = CompanyCode;
            obj.ProjectId = ProjectId;
            obj.ProjectCode = ProjectCode;
            obj.ProjectDescription = ProjectDescription;
            obj.PackageId = PackageId;
            obj.PackageCode = PackageCode;
            obj.PackageDescription = PackageDescription;
            obj.TaxCodeId = TaxCodeId;
            obj.TaxCodeCode = TaxCodeCode;
            obj.TaxCodeDescription = TaxCodeDescription;
            obj.ClerkPrcId = ClerkPrcId;
            obj.ClerkPrcCode = ClerkPrcCode;
            obj.ClerkPrcDescription = ClerkPrcDescription;
            obj.ClerkReqId = ClerkReqId;
            obj.ClerkReqCode = ClerkReqCode;
            obj.ClerkReqDescription = ClerkReqDescription;
            obj.CurrencyId = CurrencyId;
            obj.CurrencyDescription = CurrencyDescription;
            obj.Exchangerate = Exchangerate;
            obj.PrjChangeId = PrjChangeId;
            obj.PrjChangeCode = PrjChangeCode;
            obj.PrjChangeDescription = PrjChangeDescription;
            obj.ConHeaderId = ConHeaderId;
            obj.ConHeaderCode = ConHeaderCode;
            obj.ConHeaderDescription = ConHeaderDescription;
            obj.Haschanges = Haschanges;
            obj.MaterialCatalogId = MaterialCatalogId;
            obj.MaterialCatalogCode = MaterialCatalogCode;
            obj.MaterialCatalogDescription = MaterialCatalogDescription;
            obj.PrcHeaderId = PrcHeaderId;
            obj.PrcConfigurationId = PrcConfigurationId;
            obj.PaymentTermFiId = PaymentTermFiId;
            obj.PaymentTermFiCode = PaymentTermFiCode;
            obj.PaymentTermFiDescription = PaymentTermFiDescription;
            obj.PaymentTermPaId = PaymentTermPaId;
            obj.PaymentTermPaCode = PaymentTermPaCode;
            obj.PaymentTermPaDescription = PaymentTermPaDescription;
            obj.Code = Code;
            obj.Description = Description;
            obj.SearchPattern = SearchPattern;
            obj.DateOrdered = DateOrdered;
            obj.DateReported = DateReported;
            obj.DateCanceled = DateCanceled;
            obj.DateDelivery = DateDelivery;
            obj.DateCallofffrom = DateCallofffrom;
            obj.DateCalloffto = DateCalloffto;
            obj.IsNotAccrualPrr = IsNotAccrualPrr;
            obj.ConTypeId = ConTypeId;
            obj.ConTypeDescription = ConTypeDescription;
            obj.PrcAwardmethodId = PrcAwardmethodId;
            obj.PrcAwardmethodDescription = PrcAwardmethodDescription;
            obj.PrcContracttypeId = PrcContracttypeId;
            obj.PrcContracttypeDescription = PrcContracttypeDescription;
            obj.MdcControllingunitId = MdcControllingunitId;
            obj.MdcControllingunitCode = MdcControllingunitCode;
            obj.MdcControllingunitDescription = MdcControllingunitDescription;
            obj.BusinesspartnerId = BusinesspartnerId;
            obj.BusinesspartnerDescription = BusinesspartnerDescription;
            obj.SubsidiaryId = SubsidiaryId;
            obj.SubsidiaryDescription = SubsidiaryDescription;
            obj.SupplierId = SupplierId;
            obj.SupplierCode = SupplierCode;
            obj.SupplierDescription = SupplierDescription;
            obj.ContactId = ContactId;
            obj.Businesspartner2Id = Businesspartner2Id;
            obj.Businesspartner2Description = Businesspartner2Description;
            obj.Subsidiary2Id = Subsidiary2Id;
            obj.Subsidiary2Description = Subsidiary2Description;
            obj.Supplier2Id = Supplier2Id;
            obj.Supplier2Code = Supplier2Code;
            obj.Supplier2Description = Supplier2Description;
            obj.Contact2Id = Contact2Id;
            obj.PrcIncotermId = PrcIncotermId;
            obj.PrcIncotermDescription = PrcIncotermDescription;
            obj.CompanyInvoiceId = CompanyInvoiceId;
            obj.CompanyInvoiceCode = CompanyInvoiceCode;
            obj.AddressId = AddressId;
            obj.AddressDescription = AddressDescription;
            obj.MdcBillingSchemaId = MdcBillingSchemaId;
            obj.MdcBillingSchemaDescription = MdcBillingSchemaDescription;
            obj.BusinesspartnerAgentId = BusinesspartnerAgentId;
            obj.BusinesspartnerAgentDescription = BusinesspartnerAgentDescription;
            obj.PrcPackage2headerId = PrcPackage2headerId;
            obj.PrcPackage2headerDescription = PrcPackage2headerDescription;
            obj.CodeQuotation = CodeQuotation;
            obj.DateQuotation = DateQuotation;
            obj.Remark = Remark;
            obj.UserDefined1 = UserDefined1;
            obj.UserDefined2 = UserDefined2;
            obj.UserDefined3 = UserDefined3;
            obj.UserDefined4 = UserDefined4;
            obj.UserDefined5 = UserDefined5;
            obj.ConfirmationCode = ConfirmationCode;
            obj.ConfirmationDate = ConfirmationDate;
            obj.ExternalCode = ExternalCode;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            obj.PaymentTermAdId = PaymentTermAdId;
            obj.PaymentTermAdCode = PaymentTermAdCode;
            obj.PaymentTermAdDescription = PaymentTermAdDescription;
            obj.PrcCopyModeId = PrcCopyModeId;
            obj.PrcCopymodeDescription = PrcCopymodeDescription;
            obj.DateEffective = DateEffective;
            obj.DatePenalty = DatePenalty;
            obj.PenaltyPercentperday = PenaltyPercentperday;
            obj.PenaltyPercentmax = PenaltyPercentmax;
            obj.PenaltyComment = PenaltyComment;
            obj.VatGroupId = VatGroupId;
            obj.VatGroupDescription = VatGroupDescription;
            obj.ProvingPeriod = ProvingPeriod;
            obj.ProvingDealdline = ProvingDealdline;
            obj.ApprovalPeriod = ApprovalPeriod;
            obj.ApprovalDealdline = ApprovalDealdline;
            obj.Isfreeitemsallowed = Isfreeitemsallowed;
            obj.BankId = BankId;
            obj.MdcPriceListId = MdcPriceListId;
            obj.MdcPriceListDescription = MdcPriceListDescription;
            obj.QtnHeaderId = QtnHeaderId;
            obj.QtnHeaderCode = QtnHeaderCode;
            obj.QtnHeaderDescription = QtnHeaderDescription;
            obj.LanguageId = LanguageId;
            obj.BasAccassignBusinessId = BasAccassignBusinessId;
            obj.BasAccassignBusinessDescription = BasAccassignBusinessDescription;
            obj.BasAccassignControlId = BasAccassignControlId;
            obj.BasAccassignControlDescription = BasAccassignControlDescription;
            obj.BasAccassignAccountId = BasAccassignAccountId;
            obj.BasAccassignAccountDescription = BasAccassignAccountDescription;
            obj.BasAccassignConTypeId = BasAccassignConTypeId;
            obj.BasAccassignConTypeDescription = BasAccassignConTypeDescription;
            obj.ExecutionStart = ExecutionStart;
            obj.ExecutionEnd = ExecutionEnd;
            obj.OrdHeaderId = OrdHeaderId;
            obj.OrdHeaderCode = OrdHeaderCode;
            obj.OrdHeaderDescription = OrdHeaderDescription;
            obj.OverallDiscount = OverallDiscount;
            obj.OverallDiscountOc = OverallDiscountOc;
            obj.OverallDiscountPercent = OverallDiscountPercent;
            obj.SalesTaxMethodId = SalesTaxMethodId;
            obj.SalesTaxMethodDesc = SalesTaxMethodDesc;
            obj.BoqWicCatId = BoqWicCatId;
            obj.BoqWicCatCode = BoqWicCatCode;
            obj.BoqWicCatBoqId = BoqWicCatBoqId;
            obj.BoqWicCatBoqReference = BoqWicCatBoqReference;
            obj.ReqHeaderId = ReqHeaderId;
            obj.ValidFrom = ValidFrom;
            obj.ValidTo = ValidTo;
            obj.BaselineUpdate = BaselineUpdate;
            obj.StructureDes = StructureDes;
            obj.StructureCode = StructureCode;
            obj.StructureId = StructureId;
            obj.IsFramework = IsFramework;
            obj.BasLanguageFk = BasLanguageFk;
            obj.BasLanguageDesc = BasLanguageDesc;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(ConHeaderApiEntity clonedEntity);

    }


}
