{"CONTAINERS": {"CONTROLLING_UNIT": {"QUANTITY": "1", "UOM": "10M3"}, "ESTIMATE": {"RUBRIC_CATEGORY": "Planning", "ESTIMATE_TYPE": "Bid Estimate"}, "RESOURCE": {"SHORT_KEY": "M", "CODE": "DÜB08PVC", "QUANTITY": "20"}, "LINE_ITEM": {"QUANTITY": "300", "UOM": "10M3"}, "ITEMS": {"QUANTITY": "40", "PRICE": "12"}, "QUOTE": {"BUSINESS_PARTNER_1": "<PERSON>", "BUSINESS_PARTNER_2": "AGO Baumaschinen GmbH", "PRICE_1": "20", "PRICE_2": "10"}, "PROCUREMENTCONTRACT": {"BUISNESS_PARTNER": "<PERSON>", "RUBERIC_CATAGORY": "Standard", "CHANGE_TYPE": "Design Change", "CHANGE_TYPE_REASONS": "Change Request", "STATUS": "Recorded", "BUISNESS_PARTNER_2": "Bremer Betonwerk Friedrich Thielen", "PARENT_ELEMENT": "Business Partner", "ITEM": "Name"}, "PES": {"QUANTITY": "100", "QUANTITY_1": "0.000"}, "INVOICE": {"LABEL": "Create one invoice for all PES independently of the contract", "invoiceNo": "test"}}, "MODAL": {"CREATE_UPDATE_MATERIAL_PACKAGE": {"MATERIAL_AND_COST_CODE": "Material & Cost Code", "PROCUREMENT_STRUCTURE": "M", "CONFIGURATION": "Material", "BUSINESS_PARTNER": "<PERSON>", "SCOPE": "Entire Estimate", "SCOPE_ID": "entireEstimate_2", "CRITERIA_SELECTION": "Material & Cost Code", "configurationInput": "Material", "QUANTITY": "40", "PRICE": "12"}}, "CONTAINER_COLUMNS": {"CONTROLLING_UNIT": {"descriptioninfo": "Description", "quantity": "Quantity", "uomfk": "UoM"}, "ESTIMATE": {"estheader.code": "Code", "estheader.descriptioninfo": "Description", "estheader.esttypefk": "Estimate Type", "estheader.rubriccategoryfk": "Category"}, "LINE_ITEM": {"descriptioninfo": "Description", "quantity": "Quantity", "basuomfk": "UoM", "costtotal": "Cost Total"}, "RESOURCE": {"estresourcetypeshortkey": "Short Key", "code": "Code", "descriptioninfo": "Description", "quantity": "Quantity", "basuomfk": "UoM"}, "ITEMS": {"quantity": "Quantity", "totaloc": "Total (OC)", "vat": "VAT", "totalgross": "Total (Gross)", "quantityremaining": "Remaining Quantity", "quantitycontracted": "Contracted Quantity"}, "PROCUREMENTCONTRACT": {"code": "Code", "businesspartnerfk": "Business Partner", "structureCode": "Procurement structure", "constatusfk": "Contract Status", "configurationfk": "Configuration", "clerkprcfk": "Responsible", "projectfk": "Project No.", "description": "Reference Name", "reqheaderfk": "Requisition", "billingschemafk": "Billing <PERSON><PERSON>", "paymenttermfifk": "Payment Term (FI)"}, "QUOTES": {"statusfk": "Status", "packagenumber": "Package Number", "supplierfk": "Supplier No.", "businesspartnerfk": "Business Partner", "subsidiaryfk": "Branch"}, "QUOTES_ITEM": {"price": "Price", "quantity": "Quantity", "basuomfk": "UoM"}, "CONTRACT": {"codequotation": "Quotation Code", "packagefk": "Package Code", "supplierfk": "Supplier No.", "businesspartnerfk": "Business Partner", "subsidiaryfk": "Branch"}, "PESITEM": {"quantity": "Quantity", "quantitycontracted": "Contracted Quantity", "quantityremaining": "Remaining Quantity", "mdcmaterialfk": "Material No."}, "INVOICEHEADER": {"prcconfigurationfk": "Configuration", "invtypefk": "Type", "reference": "Invoice No.", "businesspartnerfk": "Business Partner", "conheaderfk": "Contract", "billingschemafk": "Billing <PERSON><PERSON>", "paymenttermfk": "Payment Term"}, "PERFORMANCE_ENTRY_SHEETS": {"pesheaderfk": "PES", "pesvat": "VAT", "pesvalue": "Value"}, "CONTRACT_ITEMS": {"quantity": "Quantity", "quantityremaining": "Remaining Quantity"}}}