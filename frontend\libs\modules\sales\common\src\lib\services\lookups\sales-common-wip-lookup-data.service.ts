/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable } from '@angular/core';
import { get } from 'lodash';
import { UiCommonLookupEndpointDataService } from '@libs/ui/common';
import { IEntityContext } from '@libs/platform/common';
import { IWipHeaderEntity } from '@libs/sales/interfaces';

@Injectable({
	providedIn: 'root',
})
/**
 * SalesCommonWipLookupDataService is a service that provides lookup data for WIP headers.
 */
export class SalesCommonWipLookupDataService<TEntity extends object> extends UiCommonLookupEndpointDataService<IWipHeaderEntity, TEntity> {
	public constructor() {
		super(
			{
				httpRead: { route: 'sales/wip/', endPointRead: 'list' },
				filterParam: 'projectId',
				prepareListFilter: (context?: IEntityContext<TEntity>) => {
					return context ? 'projectId=' + get(context.entity, 'ProjectFk') : '';
				},
			},
			{
				uuid: '',
				valueMember: 'Id',
				displayMember: 'Code',
			},
		);
	}
}
