/*
 * $Id$
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { EMPLOYEE_CERTIFICATION_LOOKUP_PROVIDER_TOKEN, ICertifiedEmployeeEntity } from '@libs/timekeeping/interfaces';
import { TimekeepingCertifiedEmployeeDataService } from '../services/timekeeping-certified-employee-data.service';
import { TimekeepingCertifiedEmployeeValidationService } from '../services/timekeeping-certified-employee-validation.service';
import {ILayoutConfiguration } from '@libs/ui/common';
import { prefixAllTranslationKeys } from '@libs/platform/common';


export const TIMEKEEPING_CERTIFIED_EMPLOYEE_ENTITY_INFO: EntityInfo = EntityInfo.create<ICertifiedEmployeeEntity> ({
	grid: {
		title: {key: 'timekeeping.certificate' + '.certifiedEmployeeListTitle'},
	},
	form: {
		title: { key: 'timekeeping.certificate' + '.certifiedEmployeeDetailTitle' },
		containerUuid: '2d585d88dc054491922176340016f112',
	},
	dataService: ctx => ctx.injector.get(TimekeepingCertifiedEmployeeDataService),
	validationService: ctx => ctx.injector.get(TimekeepingCertifiedEmployeeValidationService),
	dtoSchemeId: {moduleSubModule: 'Timekeeping.Certificate', typeName: 'CertifiedEmployeeDto'},
	permissionUuid: 'e69fc5da946948f1abaa204629a91067',
	layoutConfiguration: async ctx => {
		const employeeCertificationLookupProvider = await ctx.lazyInjector.inject(EMPLOYEE_CERTIFICATION_LOOKUP_PROVIDER_TOKEN);
		return <ILayoutConfiguration<ICertifiedEmployeeEntity>>{
			groups: [
				{
					gid: 'default-group',
					attributes: ['EmployeeFk', 'EmpCertificateFk', 'EmpCertificateStatusFk', 'Comment' , 'ValidFrom', 'ValidTo', 'EmployeeStatusFk']
				}
			],
			overloads: {
				EmpCertificateFk: employeeCertificationLookupProvider.generateEmployeeCertificationLookup(),
				//Todo EmpFk
	},
			labels: {
				...prefixAllTranslationKeys('timekeeping.certificate.', {
					EmployeeFk: { key: 'entityEmployeeFk' },
					EmpCertificateFk: { key: 'entityCertificateFk' },
					EmpCertificateStatusFk: {key: 'entityEmployeeStatusFk' },
					Comment: {key: 'entityCommentText'},
					ValidFrom: {key: 'entityValidFrom'},
					ValidTo: {key: 'entityValidTo'},
					EmployeeStatusFk: {key: 'entityEmployeeStatusFk'}
				})
			}
		};

	}
});
