import { tile, app, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _boqPage, _common, _commonAPI, _materialPage, _package, _procurementConfig, _procurementContractPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const BOQ_DESC = _common.generateRandomString(5);
const BOQ_STRUCTURE_DESC = _common.generateRandomString(5);
const PROCUREMENT_BOQ_NAME = _common.generateRandomString(5);

let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_PROCUREMENT_BOQS;

let CONTAINERS_BOQ_STRUCTURE, CONTAINERS_CONTRACT, CONTAINERS_SOURCE_BOQ;

let BOQ_API_PARAMETERS: DataCells, CONTRACT_PARAMETERS: DataCells, CONTRACT_BOQ_PARAMETER: DataCells;

describe('EST- 9.143 | Verify contract Module -source BOQ lookup field name should be visible', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

    before(function () {
        cy.fixture('estimate/est-9.143-verify-contract-module-source-boq-lookup-field-name-should-be-visible.json')
            .then((data) => {
                this.data = data;
                CONTAINERS_BOQ_STRUCTURE = this.data.CONTAINERS.BOQ_STRUCTURE

                CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT

                CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT

                CONTRACT_PARAMETERS = {
                    [commonLocators.CommonLabels.CONFIGURATION]: CommonLocators.CommonKeys.SERVICE,
                    [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUISNESS_PARTNER
                };

                CONTAINER_COLUMNS_PROCUREMENT_BOQS = this.data.CONTAINER_COLUMNS.PROCUREMENT_BOQS

                CONTRACT_BOQ_PARAMETER = {
                    [commonLocators.CommonLabels.PROCUREMENT_STRUCTURE_SMALL]: commonLocators.CommonKeys.SERVICE,
                    [commonLocators.CommonLabels.SUB_PACKAGE]: commonLocators.CommonKeys.SERVICE,
                    [commonLocators.CommonLabels.OUTLINE_SPECIFICATION]: PROCUREMENT_BOQ_NAME
                }

                CONTAINERS_SOURCE_BOQ = this.data.CONTAINERS.SOURCE_BOQ


            }).then(() => {
                cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                _common.waitForLoaderToDisappear()
                _common.openTab(app.TabBar.PROJECT).then(() => {
                    _common.setDefaultView(app.TabBar.PROJECT)
                    _common.waitForLoaderToDisappear()
                    _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
                })
                _common.waitForLoaderToDisappear();
                _commonAPI.getAccessToken().then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                });
            })
    });

    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

    it('TC - API: Create BoQ header and BoQ structure', function () {
        BOQ_API_PARAMETERS = {
            [app.GridCells.BRIEF_INFO_SMALL]: BOQ_DESC,
            [app.GridCells.BRIEF_INFO]: BOQ_STRUCTURE_DESC,
            [app.GridCells.QUANTITY_SMALL]: CONTAINERS_BOQ_STRUCTURE.QUANTITY,
            [app.GridCells.PRICE_SMALL]: CONTAINERS_BOQ_STRUCTURE.UNIT_RATE,
            [app.GridCells.BAS_UOM_FK]: CONTAINERS_BOQ_STRUCTURE.UOM,
            [app.GridCells.PROJECT_CODE]: Cypress.env('API_PROJECT_NUMBER_1')
        };
        _commonAPI.createBoQHeaderWithItems(BOQ_API_PARAMETERS);
        _commonAPI.getBoQHeaderList(Cypress.env('API_PROJECT_ID_1')).then(() => {
            const boqIds = Cypress.env('API_BOQ_HEADER_ID');
            Cypress.env(`BOQ_HEADER_ID`, boqIds[0]);
        });
    });

    it('TC - Creation Of contract', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT);
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETERS);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.CLERK)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
            _common.setDefaultView(app.TabBar.PROCUREMENTCONTRACTBOQ);
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQS, CONTAINER_COLUMNS_PROCUREMENT_BOQS)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.waitForLoaderToDisappear()
        _package.create_ProcuremenBoQswithNewReocrd_in_Requisition_module(CONTRACT_BOQ_PARAMETER)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 0)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQS, PROCUREMENT_BOQ_NAME)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BOQ_COPY, app.FooterTab.SOURCE_BOQ, 2)
        });
        _common.waitForLoaderToDisappear()
        _common.select_dataFromContainersForm_caret(cnt.uuid.BOQ_COPY, CommonLocators.CommonLabels.COPY_FROM, CommonLocators.CommonKeys.GRID_1, CONTAINERS_SOURCE_BOQ.COPY_FROM[0])
        _common.waitForLoaderToDisappear()
        _common.findCaretByLabel(CommonLocators.CommonLabels.BOQ_SELECTION)
        _common.waitForLoaderToDisappear()
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[0])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[1])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[2])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[3])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[4])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[5])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[6])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[7])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[8])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[9])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[10])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[11])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[12])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[13])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[14])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[15])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[16])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_dataFromContainersForm_caret(cnt.uuid.BOQ_COPY, CommonLocators.CommonLabels.COPY_FROM, CommonLocators.CommonKeys.GRID_1, CONTAINERS_SOURCE_BOQ.COPY_FROM[1])
        cy.wait(3000) //mandatory wait required to load container data
        _common.waitForLoaderToDisappear()
        _common.findCaretByLabel(CommonLocators.CommonLabels.BOQ_SELECTION)
        _common.waitForLoaderToDisappear()
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[0])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[1])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[2])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[13])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[14])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[15])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[16])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_dataFromContainersForm_caret(cnt.uuid.BOQ_COPY, CommonLocators.CommonLabels.COPY_FROM, CommonLocators.CommonKeys.GRID_1, CONTAINERS_SOURCE_BOQ.COPY_FROM[2])
        cy.wait(3000) //mandatory wait required to load container data
        _common.waitForLoaderToDisappear()
        _common.findCaretByLabel(CommonLocators.CommonLabels.BOQ_SELECTION)
        _common.waitForLoaderToDisappear()
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[0])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[1])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[13])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[14])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[15])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[16])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[17])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[18])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[19])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[20])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[21])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_dataFromContainersForm_caret(cnt.uuid.BOQ_COPY, CommonLocators.CommonLabels.COPY_FROM, CommonLocators.CommonKeys.GRID_1, CONTAINERS_SOURCE_BOQ.COPY_FROM[3])
        cy.wait(3000) //mandatory wait required to load container data
        _common.waitForLoaderToDisappear()
        _common.findCaretByLabel(CommonLocators.CommonLabels.BOQ_SELECTION)
        _common.waitForLoaderToDisappear()
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[0])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[1])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[13])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[14])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[15])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[16])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[17])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[18])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[19])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[20])
        _validate.validate_columnsUnderPopup(CONTAINERS_SOURCE_BOQ.POPUP_COLUMNS[21])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });


});
