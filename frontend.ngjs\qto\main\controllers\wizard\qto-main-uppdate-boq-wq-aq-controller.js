(function (angular) {
	'use strict';
	let moduleName = 'qto.main';



	angular.module(moduleName).controller('qtoMainUpdateBoqWqAqController', [
		'$scope','$translate','qtoMainHeaderDataService', 'platformTranslateService',
		function ($scope,$translate,qtoMainHeaderDataService,platformTranslateService) {

			$scope.options = $scope.$parent.modalOptions;

			$scope.currentItem = $scope.options.dataItem;

			$scope.modalOptions = {
				headerText: $scope.options.headerText,
				prevStepText: $translate.instant('cloud.common.previousStep'),
				nextStepText: $translate.instant('cloud.common.nextStep'),
				closeButtonText: $translate.instant('basics.common.cancel'),
				actionButtonText: $translate.instant('basics.common.ok')
			};

			let QTO_SCOPE = {
				RESULT_HIGHLIGHTED: {
					value: 1,
					label:$translate.instant('qto.main.wizard.HighlightedQto')
				},
				RESULT_SET: {
					value: 2,
					label: $translate.instant('qto.main.wizard.ResultSet')
				},
				ALL_QTO: {
					value: 3,
					label: $translate.instant('qto.main.wizard.EntireQto')
				}
			};

			let formConfig =    {
				'fid': 'qto.main.updateBoqWqAq',
				'version': '1.1.0',
				'showGrouping': true,
				'groups': [
					{
						gid: 'default',
						header$tr$: 'qto.main.baseGroup',
						header: 'Basic Setting',
						isOpen: true,
						attributes: [
							'qtoscope'
						]
					}
				],
				rows: [
					{
						gid: 'default',
						rid: 'qtoScope',
						model: 'QtoScope',
						type: 'radio',
						visible: true,
						label: $translate.instant('qto.main.wizard.QtoScope'),
						label$tr$: $translate.instant('qto.main.wizard.QtoScope'),
						options: {
							valueMember: 'value',
							labelMember: 'label',
							items: [
								{
									value: QTO_SCOPE.RESULT_HIGHLIGHTED.value,
									label: $translate.instant('qto.main.wizard.HighlightedQto'),
									label$tr$: QTO_SCOPE.RESULT_HIGHLIGHTED.label,
									checked: true,
								},
								{
									value: QTO_SCOPE.RESULT_SET.value,
									label: $translate.instant('qto.main.wizard.ResultSet'),
									label$tr$: QTO_SCOPE.RESULT_SET.label
								},
								{
									value: QTO_SCOPE.ALL_QTO.value,
									label: $translate.instant('qto.main.wizard.EntireQto'),
									label$tr$: QTO_SCOPE.ALL_QTO.label
								}
							]
						}
					}
				]
			};

			$scope.options.formRows = $scope.options.formRows || [];

			// translate form config.
			platformTranslateService.translateFormConfig(formConfig);

			$scope.formContainerOptions = {
				statusInfo: function () {
				}
			};

			$scope.formContainerOptions.formOptions = {
				configure: formConfig,
				showButtons: [],
				validationMethod: function () {
				}
			};

			$scope.modalOptions.ok = function onOK() {
				$scope.$close(
					{
						ok: true,
						data: $scope.currentItem
					});
			};

			$scope.modalOptions.close = function onCancel() {
				$scope.$close(false);
			};

			$scope.modalOptions.cancel = function () {
				$scope.$close(false);
			};

			$scope.$on('$destroy', function () {
				$scope.currentItem = {};
			});
		}
	]);
})(angular);