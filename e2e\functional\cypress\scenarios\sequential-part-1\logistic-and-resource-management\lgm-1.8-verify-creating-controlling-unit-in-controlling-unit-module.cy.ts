import apiConstantData from "cypress/constantData/apiConstantData";
import { tile, app, cnt, commonLocators, sidebar } from "cypress/locators";
import { _common, _estimatePage, _validate, _mainView, _boqPage, _modalView, _logesticPage, _controllingUnit, _projectPage, _procurementContractPage, _saleContractPage, _materialPage, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

// VARIABLES----------------------------------------------------------------

let CONTAINERS_CONTROLLING_UNITS
let CONTROLLING_UNIT_A_PARAMETERS: DataCells

describe("LRM- 1.8 | Verify Creating Controlling Unit In Controlling Unit Module", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    beforeEach(function () {
        cy.fixture("LRM/lrm-1.8-verify-creating-controlling-unit-in-controlling-unit-module.json").then((data) => {
            this.data = data;
        });
    });
    before(function () {
        cy.fixture("LRM/lrm-1.4-verify-creation-of-plant-from-contract-module-using-wizard.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNIT;
            CONTROLLING_UNIT_A_PARAMETERS = {
                [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
                [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true", "true"]
            }
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
            _commonAPI.getAccessToken()
                .then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                });
        });
    })

    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.setDefaultView(app.TabBar.PROJECT)
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _commonAPI.createProject()
            .then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
            });
    })

    it("TC - Add Controlling Unit", function () {
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_A_PARAMETERS)
    })

    it("TC - Verify Added Controlling Unit", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTROLLING_UNITS);
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.clear_searchInSidebar()
        _common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT, app.FooterTab.CONTROLLING_UNITS, 2);
            _common.clear_subContainerFilter(cnt.uuid.CONTROLLING_UNIT)
            cy.WaitUntilLoaderComplete_Trial();
            cy.wait(1000) //required wait to select cell
        });
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.DESCRIPTION_INFO, Cypress.env('API_CNT_CODE_0'))
        cy.SAVE()
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTROLLING_UNIT, app.GridCells.DESCRIPTION_INFO, Cypress.env('API_CNT_CODE_0'))
    })
});