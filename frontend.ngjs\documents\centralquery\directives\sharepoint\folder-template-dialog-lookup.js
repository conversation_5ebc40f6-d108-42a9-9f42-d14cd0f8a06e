/**
 * Created by hzh on 6/6/2025.
 */
(function (angular, globals) {
	'use strict';

	let moduleName = 'documents.centralquery';
	angular.module(moduleName).directive('documentsCentralquerySpFolderTemplateLookup',
		['_', '$q', '$injector', 'BasicsLookupdataLookupDirectiveDefinition', 'businessPartnerMainBusinessPartnerDialogService',
			'documentsCentralquerySpFolderTemplateContantService', 'documentsCentralquerySpConfigurationService',
			'documentsCentralquerySpFolderTemplateService', 'basicsLookupdataLookupDataService',
			function (_, $q, $injector, lookupDirectiveDefinition, businessPartnerDialogService, folderTemplateContantService,
			          spConfigurationService, spFolderTemplateService, basicsLookupdataLookupDataService) {
				function gridDialogController(_, $http, $scope, $injector, $translate, platformModalService, basicsLookupdataLookupFilterService) {
					const spConfSelected = spConfigurationService.getSelected();
					const prjId = spConfSelected.PrjProjectFk;
					let folderSettingIdInPrjConfig = spConfSelected.PrjDocSpprjFolderSettingFk ?? 0;
					let isProjectSync = false;
					let projectsSelected = _.filter(spConfigurationService.getList(), function (item) {
						return item.IsSelected;
					});
					let projectIdsSelected = _.map(projectsSelected, 'PrjProjectFk');
					let projectsSelectedCount = projectIdsSelected.length;
					let templateDialogTitle = $translate.instant('documents.centralquery.sharepoint.folderTemplate.saveAsTemplate');
					let resultDialogTitle = $translate.instant('documents.centralquery.sharepoint.folderTemplate.applyResultDialogTitle');
					let isChanged = false;
					$scope.modalOptions = {
						availableTemplateName: $translate.instant('documents.centralquery.sharepoint.folderTemplate.availableTemplateName'),
						newTemplateName: $translate.instant('documents.centralquery.sharepoint.folderTemplate.newTemplateName'),
						applyToProjectUrl: globals.appBaseUrl + moduleName + '/templates/sharepoint/folder-template-apply-to-project.html',
						resultDialogUrl: globals.appBaseUrl + moduleName + '/templates/sharepoint/folder-template-apply-result-dialog.html',
						headerText: templateDialogTitle,
						resultMessage: $translate.instant('documents.centralquery.sharepoint.folderTemplate.applyToAllRsult', {count: projectsSelectedCount}),
						resultOk: function () {
							showResultDialog(false);
						},
						ok: function () {
							let folderSettingSelected = $scope.applyFolderSettings.find(e => e.Id === $scope.curFolderSettingId);
							if ($scope.newFolderSetting.name) {
								folderSettingSelected = {
									Id: 0,
									Name: $scope.newFolderSetting.name
								};
							}
							if (!validTemplateIdOrName(folderSettingSelected)) {
								return;
							}
							let request = {
								FolderSetting: {
									Id: folderSettingSelected?.Id ?? 0,
									Name: folderSettingSelected?.Name
								},
								FolderStructures: getFolderStructuresToSave(),
								MetaDataType: getMetaDataToSave()
							};

							$scope.applying = true;
							$http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/foldersetting/saveastemplate', request)
								.then((response) => {
									$scope.folderSetting = response?.data || {Id: 0};
								})
								.finally(() => {
									$scope.applying = false;
									showTemplateDialog(false);
								});
						},
						cancel: function () {
							resetApplyError();
							closeAdditionalDialog();
						},
						windowClass: 'popup-container'
					};

					$scope.folderSetting = {
						Id: 0
					};
					$scope.newFolderSetting = {
						name: ''
					};

					$scope.applyToProjectsSelectedBtnText = $translate.instant('documents.centralquery.sharepoint.folderTemplate.applyToProjectsSelected', {count: projectsSelectedCount});
					$scope.isShowApplyPop = false;
					$scope.loading = false;
					$scope.applying = false;
					$scope.applyError = {
						show: false,
						type: 0,
						message: null
					};

					$scope.folderStructures = [defaultFolderStructure()];

					$scope.userAssignment = {
						options: {
							multipleSelection: true,
							showFilterRow: false
						}
					};

					$scope.addFolderStructure = function () {
						const newFolderStructure = defaultFolderStructure();
						if ($scope.folderStructures.length === 0) {
							$scope.folderStructures.push(newFolderStructure);
						} else {
							newFolderStructure.Level = _.maxBy($scope.folderStructures, 'Level').Level + 1;
							$scope.folderStructures.push(newFolderStructure);
						}
					};

					$scope.removeFolderStructure = function (item) {
						const delLevel = item.Level;
						_.remove($scope.folderStructures, {Level: delLevel});

						_.forEach($scope.folderStructures, function (item) {
							if (item.Level > delLevel) {
								item.Level--;
							}
						});


						const newFolderStructure = defaultFolderStructure();
						if ($scope.folderStructures.length === 0) {
							$scope.folderStructures.push(newFolderStructure);
						}
					};

					$scope.isAddFolderStrucBtnDisabled = function () {
						return $scope.folderStructures.length > 2 || isProjectSync;
					};

					$scope.isFolderStructDeleteBtnDisabled = function () {
						return isProjectSync;
					};

					$scope.isDisabledBtn = function () {
						return !validateFolderStructures($scope.folderStructures);
					};

					$scope.isDisabledApplyToProjectsSelectedBtn = function () {
						return !validateFolderStructures($scope.folderStructures) || projectIdsSelected.length === 0;
					};

					$scope.metaDataType = angular.copy(folderTemplateContantService.getProjectMetaDataTypes());

					$scope.applyFolderSettingChange = function () {
						$scope.curFolderSettingId = this.curFolderSettingId;
						resetApplyError();
					};

					$scope.onTemplateNameChanged = function () {
						resetApplyError();
					};

					function doApply(isApplyToAllProjects) {
						let dataToApply = {
							ProjectId: prjId,
							FolderSettingComplete: {
								FolderSetting: getFolderSettingToSave(),
								FolderStructures: getFolderStructuresToSave(),
								MetaDataType: getMetaDataToSave()
							}
						};

						if (isApplyToAllProjects) {
							dataToApply.ProjectIdsSelected = projectIdsSelected;
						}
						let url = getApplyUrl(isApplyToAllProjects);

						$scope.applying = true;
						return $http.post(globals.webApiBaseUrl + url, dataToApply)
							.then((response) => {
								if (!response?.data) {
									return;
								}
								$scope.folderSetting = response.data;
								if (isApplyToAllProjects) {
									showResultDialog(true);
								}
								isChanged = true;
							})
							.finally(() => {
								$scope.applying = false;
							});
					}

					$scope.onOk = function onOk() {
						doApply(false).then(() => {
							$scope.$close(false);
						});
					};

					$scope.onApplyToAllProjects = function () {
						doApply(true);
					};

					$scope.onSaveAsTemplate = function () {
						popSaveAsTemplateDialog();
					};

					$scope.onClose = function () {
						$scope.$close(false);
					};

					$scope.onFolderSettingChanged = function () {
						init();
					};

					$scope.folderSettingLookupConfig = {
						rt$readonly: function () {
							return isProjectSync;
						}
					};

					$scope.metaDataTypeLookupConfig = {
						rt$readonly: function () {
							return isProjectSync;
						}
					};

					init();
					initAdditionalDialog();

					// Get folder setting
					function init() {
						showTemplateDialog(false);
						$scope.loading = true;
						let settingId = $scope.folderSetting.Id || folderSettingIdInPrjConfig;
						$http.get(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/foldersetting/get?folderSettingId=' + settingId +
							'&projectId=' + prjId)
							.then(function (response) {
								let data = response.data;
								if (data) {
									$scope.folderSetting = data.FolderSetting ?? {Id: 0};
									updateFolderStructures(data.FolderStructures);
									updateMetaData(data.MetaData);
									isProjectSync = data.IsProjectSync;
								}
							})
							.finally(() => {
								$scope.loading = false;
							});
					}

					const filters = [
						{
							key: 'SpFolderStructureFilter',
							serverSide: false,
							fn: function (item) {
								const filter = _.filter($scope.folderStructures, {MetaDataType: item.id});
								return filter?.length === 0;
							}
						},
						{
							key: 'SpFolderSettingFilter',
							serverSide: true,
							serverKey: 'document-project-sp-folder-setting-filter',
							fn: function () {
								return {
									projectFk: prjId,
								};
							}
						}
					];
					basicsLookupdataLookupFilterService.registerFilter(filters);

					$scope.$on('$destroy', function () {
						basicsLookupdataLookupFilterService.unregisterFilter(filters);

						closeAdditionalDialog();
						unregisterEvent4AdditionalDialog();
						if (isChanged) {
							spConfigurationService.refreshData.fire();
						}
					});

					function updateFolderStructures(folderStructures) {
						if (!folderStructures || folderStructures.length === 0) {
							$scope.folderStructures = [defaultFolderStructure()];
							return;
						}
						$scope.folderStructures = [];
						folderStructures.forEach(folderStruc => {
							let temp = _.assign({}, folderStruc);
							temp.users = temp.UsersAssigned ?? [];
							$scope.folderStructures.push(temp);
						});
					}

					function getApplyUrl(isApplyAllProject) {
						let url;
						if (isApplyAllProject) {
							url = 'documents/centralquery/sharepoint/foldersetting/savetoprojectsselected';
						} else {
							url = 'documents/centralquery/sharepoint/foldersetting/save';
						}
						return url;
					}

					function getFolderSettingToSave() {
						let folderSetting = $scope.folderSetting;
						if (!$scope.folderSetting) {
							folderSetting = {
								Id: 0,
								Name: 'With Setting',
								PrjProjectFk: prjId
							};
						}
						return folderSetting;
					}

					function getFolderStructuresToSave() {
						if (!$scope.folderStructures || $scope.folderStructures.length === 0) {
							return [];
						}
						let toSave = [];
						let aadUsers = spConfigurationService.aadUsers;
						$scope.folderStructures.forEach(structure => {
							let temp = _.assign({}, structure);
							let usersAssigned = temp.users;
							temp.UsersAssigned = usersAssigned;
							let userList = aadUsers.filter(e => {
								return usersAssigned.some(u => u === e.Id);
							});
							temp.SpUsers = userList.length > 0 ? JSON.stringify(userList) : '';

							toSave.push(temp);
						});
						return toSave;
					}

					function getMetaDataToSave() {
						if (!$scope.metaDataType || $scope.metaDataType.length === 0) {
							return null;
						}

						let metaData = $scope.metaDataType.find(meta => meta.isChecked);
						return metaData ? metaData.id : null;
					}

					function validTemplateIdOrName(folderSetting) {
						let isValid = folderSetting && (folderSetting.Id > 0 || folderSetting.Name);

						if (isValid) {
							resetApplyError();
						} else {
							$scope.applyError = {
								show: true,
								type: 3,
								message: $translate.instant('documents.centralquery.sharepoint.folderTemplate.error.templateNameRequired'),
								messageCol: 1
							};
						}
						return isValid;
					}

					function validateFolderStructures(structures) {
						if (!structures || structures.length === 0) {
							return true;
						}

						let result = true;
						structures.forEach(structure => {
							if (result === true && !validateMetaDataInStructure(structure)) {
								result = false;
							}
						});

						return result;
					}

					function validateMetaDataInStructure(structure) {
						if (!structure) {
							return true;
						}
						return angular.isNumber(structure.MetaDataType) && structure.MetaDataType >= 0;
					}

					function updateMetaData(metaData) {
						if (angular.isUndefined(metaData) || metaData === null) {
							$scope.metaDataType.forEach(meta => {
								meta.isChecked = false;
							});
						} else {
							$scope.metaDataType.forEach(meta => {
								meta.isChecked = meta.id === metaData.MetadataType;
							});
						}
					}

					function resetApplyError() {
						$scope.applyError = {
							show: false,
							type: 0,
							message: null
						};
					}

					function defaultFolderStructure() {
						return {
							Level: 0,
							MetaDataType: -1,
							ShareOption: 0,
							users: []
						};
					}

					function showTemplateDialog(isShow) {
						if (showFade(isShow)) {
							$scope.modalOptions.headerText = templateDialogTitle;
							$scope.isShowApplyPop = isShow;
						}
					}

					function showResultDialog(isShow) {
						if (showFade(isShow)) {
							$scope.modalOptions.headerText = resultDialogTitle;
							$scope.isShowResultPop = isShow;
						}
					}

					function showFade(isShow) {
						let ele = document.getElementById('applyFade');
						if (ele) {
							let style = 'z-index: 1051; display:';
							style += isShow ? '' : 'none';
							ele.style = style;
							return true;
						}
						return false;
					}

					function initAdditionalDialog() {
						let ele = document.getElementById('applyFade');
						if (ele) {
							ele.addEventListener('keydown', handleKeyDown);
						}
					}

					function unregisterEvent4AdditionalDialog() {
						let ele = document.getElementById('applyFade');
						if (ele) {
							ele.removeEventListener('keydown', handleKeyDown);
						}
					}

					function handleKeyDown(event) {
						if (event.key === 'ESC') {
							event.preventDefault();
							event.stopPropagation();
							closeAdditionalDialog();
						}
					}

					function closeAdditionalDialog() {
						if ($scope.isShowApplyPop) {
							showTemplateDialog(false);
						}
						if ($scope.isShowResultPop) {
							showResultDialog(false);
						}
					}

					function popSaveAsTemplateDialog() {
						let filterRequest = {
							AdditionalParameters: {},
							FilterKey: '',
						};
						basicsLookupdataLookupDataService.getSearchList('SharePointFolderSetting', filterRequest).then(function (response) {
							$scope.applyFolderSettings = response?.items ?? [];
							$scope.curFolderSettingId = 0;
							$scope.newFolderSetting.name = '';

							showTemplateDialog(true);
						});
					}
				}

				const defaults = {
					lookupType: 'SharePointFolderTemplate',
					version: 3,
					valueMember: 'Id',
					displayMember: 'Name',
					resizeable: true,
					minWidth: '860px',
					maxWidth: '90%',
					uuid: '26df72d2012443aea5dd2f812d38e062',
					popupOptions: {
						minWidth: 860,
						width: 860,
						height: 660,
						template: '',
						templateUrl: globals.appBaseUrl + 'documents.centralquery/templates/sharepoint/folder-template-lookup-dialog.html',
						controller: ['_', '$http', '$scope', '$injector', '$translate', 'platformModalService', 'basicsLookupdataLookupFilterService', gridDialogController],
						showLastSize: true
					},
					disableDataCaching: true,
					events: [{
						name: 'onPopupOpened',
						handler: function (evt, popupRef) {
							popupRef.popup.element.find('button.ico-refresh').remove();
						},
					}]
				};

				return new lookupDirectiveDefinition('lookup-edit', defaults,
					{
						dataProvider: {
							getList: function () {
								return spFolderTemplateService.getList();
							},

							getItemByKey: function (value) {
								return spFolderTemplateService.getList().then(function (data) {
									return _.find(data, {Id: value});
								});

							}
						}
					}
				);

			}]);
// eslint-disable-next-line no-undef
})(angular, globals);