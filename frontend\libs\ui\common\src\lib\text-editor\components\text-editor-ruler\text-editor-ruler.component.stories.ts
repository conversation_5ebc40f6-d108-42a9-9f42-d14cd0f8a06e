/*
 * Copyright(c) RIB Software GmbH
 */

import { NgModule, Component } from '@angular/core';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { TextEditorRulerComponent } from './text-editor-ruler.component';
import { RulerUnitCaption } from '../../model/ruler-unit.enum';

// Demo wrapper component for interactive Storybook demo
@Component({
	selector: 'ui-common-ruler-demo',
	template: '<ui-common-text-editor-ruler [unitCaption]="selectedUnit" [editorWidth]="editorWidth"> </ui-common-text-editor-ruler> ',
	standalone: true,
	imports: [CommonModule, FormsModule, TextEditorRulerComponent],
})
class RulerDemoComponent {
	public selectedUnit = RulerUnitCaption.in;
	public editorWidth = 300;

	public presets = [
		{ label: 'A4 Width (210mm)', unit: RulerUnitCaption.mm, width: 210 },
		{ label: 'Letter Width (8.5in)', unit: RulerUnitCaption.in, width: 8.5 },
		{ label: 'Legal Width (8.5in)', unit: RulerUnitCaption.in, width: 8.5 },
		{ label: 'A3 Width (297mm)', unit: RulerUnitCaption.mm, width: 297 },
		{ label: 'Custom (15cm)', unit: RulerUnitCaption.cm, width: 15 },
		{ label: 'Web Width (800px)', unit: RulerUnitCaption.px, width: 800 },
	];

	public configurations: Record<RulerUnitCaption, { interval: number; subInterval: number }> = {
		[RulerUnitCaption.in]: { interval: 1, subInterval: 0.1 },
		[RulerUnitCaption.mm]: { interval: 10, subInterval: 5 },
		[RulerUnitCaption.cm]: { interval: 1, subInterval: 0.5 },
		[RulerUnitCaption.px]: { interval: 100, subInterval: 50 },
	};

	public onUnitChange() {
		console.log('Unit changed to:', this.selectedUnit);
	}

	public onWidthChange() {
		console.log('Width changed to:', this.editorWidth, this.selectedUnit);
	}

	public getCurrentConfig() {
		return {
			unit: this.selectedUnit,
			width: this.editorWidth,
			configuration: this.configurations[this.selectedUnit],
		};
	}
}

// Test Module for Storybook
@NgModule({
	declarations: [TextEditorRulerComponent],
	imports: [CommonModule, FormsModule, RulerDemoComponent],
	exports: [TextEditorRulerComponent],
})
class TestRulerModule {}

export default {
	title: 'UI Common/Text Editor/RulerComponent',
	component: TextEditorRulerComponent,
	decorators: [
		moduleMetadata({
			imports: [TestRulerModule],
		}),
	],
	parameters: {
		docs: {
			description: {
				component: `
A precision measurement ruler component for text editors that provides visual measurement guides with support for multiple units and dynamic sizing.

## Overview

The Text Editor Ruler Component creates an interactive measurement ruler that helps users understand document dimensions and spacing within a text editor environment. It's designed to provide accurate visual feedback for document layout and positioning.

###  **Multi-Unit Support**
- **Inches (in)**: Standard imperial measurement with decimal subdivisions
- **Centimeters (cm)**: Metric measurement with half-centimeter subdivisions  
- **Millimeters (mm)**: Precise metric measurement with 5mm subdivisions
- **Pixels (px)**: Digital measurement for web and screen-based layouts
- **Extensible**: Easy to add new measurement units

### **Input Properties**
- \`unitCaption\`: Sets the measurement unit (in, cm, mm, px)
- \`editorWidth\`: Sets the width of the editor area to measure

                `,
			},
		},
	},
	argTypes: {
		unitCaption: {
			control: 'select',
			options: [RulerUnitCaption.in, RulerUnitCaption.cm, RulerUnitCaption.mm, RulerUnitCaption.px],
			description: 'The unit of measurement for the ruler',
		},
		editorWidth: {
			control: { type: 'number', min: 50, max: 1000, step: 10 },
			description: 'The width of the editor area in the specified units',
		},
	},
} as Meta<TextEditorRulerComponent>;

type Story = StoryObj<TextEditorRulerComponent>;

export const InchRuler: Story = {
	args: {
		unitCaption: RulerUnitCaption.in,
		editorWidth: 8.5,
	},
	render: (args) => ({
		template: `           
                    <ui-common-text-editor-ruler 
                        [unitCaption]="unitCaption"
                        [editorWidth]="editorWidth">
                    </ui-common-text-editor-ruler>                
        `,
		props: args,
		component: null,
	}),
	parameters: {
		docs: {
			description: {
				story: 'Standard inch ruler perfect for US document formats. Shows major inch marks with fine 0.1" subdivisions for precise measurements.',
			},
		},
	},
};

export const CentimeterRuler: Story = {
	args: {
		unitCaption: RulerUnitCaption.cm,
		editorWidth: 21,
	},
	render: (args) => ({
		template: `           
                    <ui-common-text-editor-ruler 
                        [unitCaption]="unitCaption"
                        [editorWidth]="editorWidth">
                    </ui-common-text-editor-ruler>               
        `,
		props: args,
		component: null,
	}),
	parameters: {
		docs: {
			description: {
				story: 'Metric centimeter ruler ideal for international document formats. Features clear centimeter marks with half-centimeter subdivisions.',
			},
		},
	},
};
