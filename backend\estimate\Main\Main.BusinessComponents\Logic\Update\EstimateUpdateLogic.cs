using System;
using System.Collections.Generic;
using System.Linq;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Estimate.Assemblies.BusinessComponents;
using RIB.Visual.Platform.Common;
using System.Transactions;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Customize.BusinessComponents;
using System.Data.Entity;
using RIB.Visual.Basics.CostCodes.BusinessComponents;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using RVEABE = RIB.Visual.Estimate.Assemblies.BusinessComponents.EstLineItemEntity;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Core.Core.Basics;
using RIB.Visual.Platform.Core;
using System.Globalization;
namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// class for estimate update
	/// </summary>
	public class EstimateUpdateLogic : LogicBase
	{
		private readonly EstMainCompleteEntity estMainComplete;

		private Dictionary<int, int> header2GruopDic;

		private readonly EstimateMainLineItemLogic estimateMainLineItemLogic = new EstimateMainLineItemLogic();

		/// <summary>
		/// ctor
		/// </summary>
		/// <param name="estimateMainComplete"></param>
		public EstimateUpdateLogic(EstMainCompleteEntity estimateMainComplete)
		{
			estMainComplete = estimateMainComplete;
		}

		/// <summary>
		///
		/// </summary>
		public void UpdateEstimate()
		{
			var estHeaderId = GetEstHeaderId(estMainComplete);

			var currentEstLineItem = GetCurrentLineItem(estMainComplete, estHeaderId);

			InitializeLineItems();

			var resourcesToSave = GetResourcesToSave();

			GenerateProjectRelateData(currentEstLineItem, resourcesToSave);

			/* delete or save entitied on database */
			using (var transaction = TransactionScopeFactory.Create())
			{
				using (var updateContextScope = new UpdateContextScope(new EstimateUpdateContextObject(estMainComplete)))
				{
					//apply entity changes from client side
					//updateContextScope.ApplyManualMerge(estMainComplete.ApplyChangeDatas);

					SaveEstimateAllowance();

					/* Estimate price adjusment */
					SavePriceAdjustment();

					/* Assembly Catagory */
					SaveEstAssemblyCat();

					/* save sort code info */
					SaveSortCodeInfo(currentEstLineItem, estMainComplete.SortCodeInfoToSave, estMainComplete.ProjectId);

					/* save lineItems */
					estimateMainLineItemLogic.BulkSave(estMainComplete.EstLineItems, 1000);

					/* to show the detail by culture */
					EstCultureHelper.SetLineItemDetailByCulture(estMainComplete.EstLineItems);

					/* resources to delete */
					if (estMainComplete.EstResourceToDelete != null && estMainComplete.EstResourceToDelete.Any())
					{
						/* delete resource escalation data if any*/
						DeleteEscalationData(estMainComplete.EstResourceToDelete);

						/*delete the PRC ITEMASSIGNMENT */
						DeleteResourcePrcItemAssignment();

						/* delete characteristic data */
						DeleteCharacteristicDataOfResources(estMainComplete.EstResourceToDelete);

						/* clear the EstResourceRuleFk of resources which cloned form EstResourceToDelete */
						new EstimateMainResourceLogic().UpdateResourcesCloned(estMainComplete.EstResourceToDelete);

						/* delete resources */
						new EstimateMainResourceLogic().Delete(estMainComplete.EstResourceToDelete);
					}

					/* save resources */
					new EstimateMainResourceLogic().SaveResources(resourcesToSave, true);

					/* User Defined Price to save */
					SaveUserDefinedColumnValue();

					/* LineItem2MdlObject */
					SaveLineItem2MdlObject(estHeaderId, currentEstLineItem);

					/* Controlling Groups for Assemblies */
					SaveAssembliesCtrlGrp();

					/* LineItem Cost Group */
					SaveLineItemCostGroup();

					/* WIC Item for Assemblies */
					SaveEstAssemblyWicItem();

					/* Line Item Quantity update */
					SaveEstLineItemQuantity();

					/* Line Item Selection Statement */
					SaveEstLineItemSelStatements();

					/* RiskRegisters */
					SaveRiskRegisters();

					/* Combined LineItems */
					SaveCombinedLineItems();

					/* Estimate Allowance */
					SaveEstimateAllowanceStep2();

					/* EstimateMainPrcItemAssignments */
					CalculatePrcItemAssignments();

					/* Plant List */
					SavePlantList();

					/* Form Data */
					SaveFormData();

					/* need to consider multiple lineItems */
					Recalculate(currentEstLineItem);

					updateContextScope.Complete();
				}

				transaction.Complete();
			}

			//update reference linitem or assembly
			if(currentEstLineItem != null)
			{
				if (currentEstLineItem.LineItemType == (int)CommonLogic.LineItemTypes.LineItem)
				{
					UpdateReferenceLineItem(currentEstLineItem);
				}
				else
				{
					UpdateReferenceAssembly(currentEstLineItem);
				}
			}

			if (estMainComplete.EstBoq != null && estMainComplete.EstBoq.Any())
			{
				var isEstimateBoqDriven = estMainComplete.ProjectId.HasValue && Injector.Get<IProjectInfoProvider>().GetProjectById(estMainComplete.ProjectId.Value).IsEstimateBoqDriven;

				if (isEstimateBoqDriven)
				{
					var data = estimateMainLineItemLogic.SaveBoqItems(estMainComplete.EstBoq, estMainComplete.EstLineItems, estMainComplete.ProjectId.Value, estMainComplete.EstHeaderId);

					estMainComplete.EstBoq = data["BoqItems"] as IEnumerable<BoqItemEntity>;

					estMainComplete.EstLineItems = data["LineItems"] as IEnumerable<EstLineItemEntity>;
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		public void UpdateEstimateCombined()
		{
			/* use to get the resources of current lineitem */
			EstProjectRelEntityCollectionResult projectRelEntityCollection = new EstProjectRelEntityCollectionResult();

			var projectExchangeRateToSave = new List<int>();

			var lineItemIds = new List<int>();

			int? estHeaderId = null;

			if (estMainComplete.EstResourceToDelete != null && estMainComplete.EstResourceToDelete.Any())
			{
				estHeaderId = estMainComplete.EstResourceToDelete.First().EstHeaderFk;

				lineItemIds.AddRange(estMainComplete.EstResourceToDelete.Select(e => e.EstLineItemFk).ToList());
			}

			if (estMainComplete.EstResourceToSave != null && estMainComplete.EstResourceToSave.Any())
			{
				var resourceEntity = estMainComplete.EstResourceToSave.FirstOrDefault();

				if (resourceEntity != null && resourceEntity.EstResource != null)
				{
					estHeaderId = resourceEntity.EstResource.EstHeaderFk;
				}

				lineItemIds.AddRange(estMainComplete.EstResourceToSave.Where(e => e.EstResource != null).Select(e => e.EstResource.EstLineItemFk).ToList());
			}

			var resourcesOfLineItems = estHeaderId.HasValue ? new EstimateMainResourceLogic().GetResourcesByLineItemIdsCore(lineItemIds, estHeaderId.Value, new EstResourceSearchOption()) : new List<EstResourceEntity>();

			var estimateHeaderId = estHeaderId.HasValue ? estHeaderId.Value : estMainComplete.EstHeaderId;

			EstLineItemEntity currentEstLineItem = GetCurrentLineItem(estMainComplete, estimateHeaderId);

			/* whether need to update reference lineItem */
			bool needToUpdateReferenceLineItem = false;

			/* whether need to recalculate the current lineItem */
			bool needToRecalculateCurrentLineItem = false;

			var estMainResourceLogic = new EstimateMainResourceLogic();

			/* collect the resources to delete */
			if (estMainComplete.EstResourceToDelete != null && estMainComplete.EstResourceToDelete.Any())
			{
				needToUpdateReferenceLineItem = true;

				estMainComplete.EstResourceToDelete = estimateMainLineItemLogic.CollectResourcesToDelete(estMainComplete.EstResourceToDelete, out needToRecalculateCurrentLineItem);
			}

			/* collect resources to save */
			if (estMainComplete.EstResourceToSave != null && estMainComplete.EstResourceToSave.Any())
			{
				needToUpdateReferenceLineItem = true;

				/* remove the resource(to be save) when it is in the list(to be delete) */
				if (estMainComplete.EstResourceToDelete != null && estMainComplete.EstResourceToDelete.Any())
				{
					var resourceIdsToDelete = estMainComplete.EstResourceToDelete.Select(e => e.Id).ToList();

					estMainComplete.EstResourceToSave = estMainComplete.EstResourceToSave.Where(e => e.EstResource != null && !resourceIdsToDelete.Contains(e.EstResource.Id)).ToList();
				}

				var resourcesToSave = estMainComplete.EstResourceToSave.Where(e => e.EstResource != null).Select(e => e.EstResource).ToList();

				if (resourcesToSave.Any())
				{
					/* get current lineItem and current header */
					var headerId = resourcesToSave.First().EstHeaderFk;

					var header = new EstimateMainHeaderLogic().GetItemById(headerId);

					var projectRelationEntityCollector = new EstProjectRelationEntityCollector();

					if (currentEstLineItem.CombinedLineItems == null)
					{
						var jobId = EstJobHelper.GetJobId(currentEstLineItem, header, estMainComplete.ProjectId);

						/* collect project costcode */
						projectRelEntityCollection.ProjectCostCodesToSave = projectRelationEntityCollector.CollectProjectEntityToSave(resourcesToSave, resourcesOfLineItems, jobId, e => e.MdcCostCodeFk).Select(e => new Tuple<int, int>(e.Item1, e.Item2)).ToList();// getResourcesOfLineItem(lineItemId, headerId)

						/* collect project material */
						projectRelEntityCollection.ProjectMaterialsToSave = projectRelationEntityCollector.CollectProjectEntityToSave(resourcesToSave, resourcesOfLineItems, jobId, e => e.MdcMaterialFk);// getResourcesOfLineItem(lineItemId, headerId)

						/* collect project assembly */
						projectRelEntityCollection.ProjectAssembliesToSave = projectRelationEntityCollector.CollectProjectAssemblyToSave(currentEstLineItem, resourcesToSave, resourcesOfLineItems, jobId, e => e.EstAssemblyFk, e => e.EstHeaderAssemblyFk);

						/* collect foreign currency */
						projectExchangeRateToSave.AddRange(resourcesToSave.Where(e => e.BasCurrencyFk.HasValue).Select(e => (int)e.BasCurrencyFk));
					}
					else
					{
						foreach (var lineItem in currentEstLineItem.CombinedLineItems)
						{
							var jobId = EstJobHelper.GetJobId(lineItem, header, estMainComplete.ProjectId);

							/* collect project costcode */
							projectRelEntityCollection.ProjectCostCodesToSave = projectRelationEntityCollector.CollectProjectEntityToSave(resourcesToSave, resourcesOfLineItems, jobId, e => e.MdcCostCodeFk).Select(e => new Tuple<int, int>(e.Item1, e.Item2)).ToList();// getResourcesOfLineItem(lineItemId, headerId)

							/* collect project material */
							projectRelEntityCollection.ProjectMaterialsToSave = projectRelationEntityCollector.CollectProjectEntityToSave(resourcesToSave, resourcesOfLineItems, jobId, e => e.MdcMaterialFk);// getResourcesOfLineItem(lineItemId, headerId)

							/* collect project assembly */
							projectRelEntityCollection.ProjectAssembliesToSave = projectRelationEntityCollector.CollectProjectAssemblyToSave(lineItem, resourcesToSave, resourcesOfLineItems, jobId, e => e.EstAssemblyFk, e => e.EstHeaderAssemblyFk);

							/* collect foreign currency */
							projectExchangeRateToSave.AddRange(resourcesToSave.Where(e => e.BasCurrencyFk.HasValue).Select(e => (int)e.BasCurrencyFk));
						}
					}
				}
			}

			/* if current lineItem is reference lineitem, then calculate the advanced allowance */
			if (currentEstLineItem != null && currentEstLineItem.EstLineItemFk.HasValue)
			{
				if (currentEstLineItem.CombinedLineItems == null)
				{
					var rootParentId = estimateMainLineItemLogic.GetLineItemRootParentId(currentEstLineItem.EstHeaderFk, currentEstLineItem.Id);

					if (rootParentId != null && rootParentId.EstLineItemFk.HasValue)
					{
						var rootParent = estimateMainLineItemLogic.GetLineItemByFk(rootParentId.EstLineItemFk.Value, currentEstLineItem.EstHeaderFk);

						if (rootParent != null)
						{
							new EstimateTotalCalculator(rootParent.EstHeaderFk, estMainComplete.ProjectId).CalculateReferenceLineItems(rootParent, currentEstLineItem);
						}
					}
				}
				else
				{
					foreach (var lineItem in currentEstLineItem.CombinedLineItems)
					{
						var rootParentId = estimateMainLineItemLogic.GetLineItemRootParentId(lineItem.EstHeaderFk, lineItem.Id);

						if (rootParentId != null && rootParentId.EstLineItemFk.HasValue)
						{
							var rootParent = estimateMainLineItemLogic.GetLineItemByFk(rootParentId.EstLineItemFk.Value, lineItem.EstHeaderFk);

							if (rootParent != null)
							{
								new EstimateTotalCalculator(rootParent.EstHeaderFk, estMainComplete.ProjectId).CalculateReferenceLineItems(rootParent, lineItem);
							}
						}
					}
				}
			}

			/* update reference lineitem */
			if (needToUpdateReferenceLineItem && currentEstLineItem != null && currentEstLineItem.LineItemType == 0)
			{
				var refLineItems = new EstimateMainCalculator().UpdateRefLineItems(currentEstLineItem, estMainComplete.ProjectId, false).Select(e => e as EstLineItemEntity).ToList();

				ConcatRefLineItemsIntoCompleteEntity(estMainComplete, refLineItems);
			}

			/* check with the BoqWicCatFk value, will get from the GetWicCatsIdByBoqHeaderIds in Boq-Wic module */
			if (estMainComplete.EstLineItems != null)
			{
				var wicBoqHeadFks = estMainComplete.EstLineItems.Where(e => e.WicBoqHeaderFk.HasValue && e.WicBoqItemFk.HasValue).Select(w => (int)w.WicBoqHeaderFk);

				var wicBoqLgic = RVPARB.BusinessEnvironment.GetExportedValue<IWicBoqLogic>();

				header2GruopDic = wicBoqLgic.GetWicGroupIdByBoqHeaderIds(wicBoqHeadFks) as Dictionary<int, int>;

				foreach (var lineItem in estMainComplete.EstLineItems.First().CombinedLineItems)
				{
					if (lineItem.WicBoqHeaderFk.HasValue)
					{
						lineItem.BoqWicCatFk = header2GruopDic[lineItem.WicBoqHeaderFk.Value];
					}
				}
			}

			/* delete or save entitied on database */
			using (var transaction = TransactionScopeFactory.Create())
			{
				/* Assembly Catagory */
				if (estMainComplete.EstAssemblyCat != null)
				{
					estMainComplete.EstAssemblyCat = new EstimateAssembliesStructureLogic().Save(estMainComplete.EstAssemblyCat);
				}

				/* lineItems to save */
				if (estMainComplete.EstLineItems != null && estMainComplete.EstLineItems.Any() && currentEstLineItem != null)
				{
					/* save sort code info */
					if (currentEstLineItem.CombinedLineItems == null)
					{
						SaveSortCodeInfo(currentEstLineItem, estMainComplete.SortCodeInfoToSave, estMainComplete.ProjectId);
					}
					else
					{
						foreach (var lineItem in currentEstLineItem.CombinedLineItems)
						{
							SaveSortCodeInfo(lineItem, estMainComplete.SortCodeInfoToSave, estMainComplete.ProjectId);
						}
					}

					/* save translation */
					foreach (var lineItem in currentEstLineItem.CombinedLineItems)
					{
						lineItem.DescriptionInfo = estMainComplete.EstLineItems.First().DescriptionInfo;
						lineItem.SaveTranslate(estimateMainLineItemLogic.UserLanguageId, new Func<EstLineItemEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
					}

					/* save lineItems */
					estimateMainLineItemLogic.BulkSave(currentEstLineItem.CombinedLineItems);

					/* to show the detail by culture */
					EstCultureHelper.SetLineItemDetailByCulture(currentEstLineItem.CombinedLineItems);
				}

				/* resources to delete */
				if (estMainComplete.EstResourceToDelete != null && estMainComplete.EstResourceToDelete.Any())
				{
					/* delete resources */
					estMainResourceLogic.Delete(estMainComplete.EstResourceToDelete);

					/* delete characteristic data */
					DeleteCharacteristicDataOfResources(estMainComplete.EstResourceToDelete);
				}

				/* resources to save */
				if (estMainComplete.EstResourceToSave != null && estMainComplete.EstResourceToSave.Any())
				{
					var resourcesToSave = estMainComplete.EstResourceToSave.Where(e => e.EstResource != null).Select(e => e.EstResource).ToList();

					// set project assembly to resource
					if (estMainComplete.ProjectId.HasValue && projectRelEntityCollection.ProjectAssembliesToSave != null && projectRelEntityCollection.ProjectAssembliesToSave.Any())
					{
						//Assembly Header Id
						var assemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();

						//Assembly Ids
						var assemblyIds = projectRelEntityCollection.ProjectAssembliesToSave.Select(e => e.Item1).ToList();

						//1. Save assemblies to Project
						//create project assembly from master, changed sp as the c# code
						new EstProjectAssemblyLogic().CreateProjectAssemblyFromMaster(assemblyIds, (int)estMainComplete.ProjectId, assemblyHeaderId);

						// set the project assembly to resource,
						new EstimateMainResourceLogic().SetPrjAssemblyToResource((int)estMainComplete.ProjectId, resourcesToSave);
					}

					/* save resources */
					estMainResourceLogic.SaveResources(resourcesToSave, true);

					/* to show the detail by culture */
					EstCultureHelper.SetResourceDetailByCulture(resourcesToSave);

					if (estMainComplete.ProjectId.HasValue && projectRelEntityCollection.ProjectCostCodesToSave != null && projectRelEntityCollection.ProjectCostCodesToSave.Any())
					{
						var prjCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();

						prjCostCodeLogic.SaveCostCodes(projectRelEntityCollection.ProjectCostCodesToSave, (int)estMainComplete.ProjectId);
					}

					if (estMainComplete.ProjectId.HasValue && projectRelEntityCollection.ProjectMaterialsToSave != null && projectRelEntityCollection.ProjectMaterialsToSave.Any())
					{
						var prjMaterialLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectMaterialLogic>();

						prjMaterialLogic.SaveMaterials(projectRelEntityCollection.ProjectMaterialsToSave, (int)estMainComplete.ProjectId);
					}

					//Project Currency Exchange Rate per Project

					if (estMainComplete.ProjectId.HasValue && projectExchangeRateToSave != null && projectExchangeRateToSave.Any() && estMainComplete.ProjectId > 0)
					{
						var prjInfoProvider = RVPARB.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();
						prjInfoProvider.SaveCurrencyRateItems(projectExchangeRateToSave, (int)estMainComplete.ProjectId);
					}
				}

				/* if it is assembly, update the reference assemblies which use it as resource */
				if (currentEstLineItem != null && currentEstLineItem.LineItemType == 1)
				{
					if (currentEstLineItem.CombinedLineItems == null)
					{
						var refLineItems4Concat = new EstimateAssemblyUpdateLogic(currentEstLineItem.EstHeaderFk).UpdateAssembliesByBaseAssembly(currentEstLineItem);

						ConcatRefLineItemsIntoCompleteEntity(estMainComplete, refLineItems4Concat);
					}
					else
					{
						foreach (var lineItem in currentEstLineItem.CombinedLineItems)
						{
							var refLineItems4Concat = new EstimateAssemblyUpdateLogic(lineItem.EstHeaderFk).UpdateAssembliesByBaseAssembly(lineItem);

							ConcatRefLineItemsIntoCompleteEntity(estMainComplete, refLineItems4Concat);
						}
					}
				}

				/* model object */
				if (estMainComplete.EstLineItem2MdlObjectToSave != null && estMainComplete.EstLineItem2MdlObjectToSave.Any())
				{
					new EstLineItem2MdlObjectLogic().Save(estMainComplete.EstLineItem2MdlObjectToSave, true);
				}
				else if (estMainComplete.EstLineItems != null && estMainComplete.EstLineItems.Any())
				{
					estMainComplete.EstLineItem2MdlObjectToSave = estimateMainLineItemLogic.UpdateEstLineItem2MdlObjects(estMainComplete.EstLineItems, estMainComplete.EstHeaderId);

					estMainComplete.IsReLoadMdlObject = estMainComplete.EstLineItem2MdlObjectToSave.Any();
				}

				if (estMainComplete.EstLineItem2MdlObjectToDelete != null && estMainComplete.EstLineItem2MdlObjectToDelete.Any())
				{
					new EstLineItem2ObjectQtyLogic().DeleteQuantities(estMainComplete.EstLineItem2MdlObjectToDelete);

					new EstLineItem2MdlObjectLogic().DeleteEntities(estMainComplete.EstLineItem2MdlObjectToDelete);
				}

				/* Controlling Groups for Assemblies */
				var ctrlGrpLogic = new EstimateAssembliesCtrlGrpLogic();

				if (estMainComplete.EstAssembliesCtrlGrpToSave != null && estMainComplete.EstAssembliesCtrlGrpToSave.Any())
				{
					foreach (var ctrlGrpEntity in estMainComplete.EstAssembliesCtrlGrpToSave)
					{
						ctrlGrpLogic.Save(ctrlGrpEntity.EstAssembliesCtrlGrp);
					}
				}

				if (estMainComplete.EstAssembliesCtrlGrpToDelete != null && estMainComplete.EstAssembliesCtrlGrpToDelete.Any())
				{
					foreach (var ctrlGrpEntity in estMainComplete.EstAssembliesCtrlGrpToDelete)
					{
						ctrlGrpLogic.Delete(ctrlGrpEntity);
					}
				}

				/* WIC Item for Assemblies */
				var wicItemLogic = new EstAssemblyWicItemLogic();

				if (estMainComplete.EstAssemblyWicItemToSave != null && estMainComplete.EstAssemblyWicItemToSave.Any())
				{
					foreach (var wicitem in estMainComplete.EstAssemblyWicItemToSave)
					{
						if (wicitem.BoqItemFk.HasValue && wicitem.BoqItemFk > 0)
						{
							wicitem.EstAssemblyWicItem = wicItemLogic.Save(wicitem.EstAssemblyWicItem);
						}
					}
				}

				if (estMainComplete.EstAssemblyWicItemToDelete != null && estMainComplete.EstAssemblyWicItemToDelete.Any())
				{
					foreach (var wicItemEntity in estMainComplete.EstAssemblyWicItemToDelete.Where(e => e.EstAssemblyWicItem != null && e.EstAssemblyWicItem.Version > 0).Select(e => e.EstAssemblyWicItem))
					{
						wicItemLogic.Delete(wicItemEntity);
					}
				}

				/* Line Item Quantity update */
				if (estMainComplete.EstLineItemQuantityToSave != null && estMainComplete.EstLineItemQuantityToSave.Any())
				{
					new EstimateMainLineItemQuantityLogic().SaveEntities(estMainComplete.EstLineItemQuantityToSave);
				}

				if (estMainComplete.EstLineItemQuantityToDelete != null && estMainComplete.EstLineItemQuantityToDelete.Any())
				{
					new EstimateMainLineItemQuantityLogic().DeleteEntities(estMainComplete.EstLineItemQuantityToDelete);
				}

				/* Line Item Selection Statement */
				if (estMainComplete.EstLineItemSelStatements != null && estMainComplete.EstLineItemSelStatements.Any())
				{
					var selectionStatementLogic = new EstimateLineItemSelStatementLogic();

					var selectionStatementStateLogic = new EstimateLineItemSelStStateLogic();

					foreach (var estLineItemSelStatement in estMainComplete.EstLineItemSelStatements)
					{
						var selStatementEntity = selectionStatementLogic.Save(estLineItemSelStatement);

						var selStatementStateToSave = selectionStatementStateLogic.CreateOrUpdate(selStatementEntity, estMainComplete.EstHeaderId);

						if (selStatementStateToSave != null)
						{
							selectionStatementStateLogic.Save(selStatementStateToSave);
						}

						if (selStatementEntity.ChildrenIsExecute != null && selStatementEntity.ChildrenIsExecute.Any())
						{
							foreach (var selStatement in selStatementEntity.ChildrenIsExecute)
							{
								var childSelStatementToSave = selectionStatementStateLogic.CreateOrUpdate(selStatement, estMainComplete.EstHeaderId);

								if (childSelStatementToSave != null)
								{
									selectionStatementStateLogic.Save(childSelStatementToSave);
								}
							}
						}
					}
				}

				transaction.Complete();
			}

			var formEst = estMainComplete.EstLineItems != null ? estMainComplete.EstLineItems.Where(x => x.FormFk.HasValue).ToList() : new List<EstLineItemEntity>();

			foreach (var estLineItemEntity in formEst)
			{
				BusinessApplication.BusinessEnvironment.GetExportedValue<IUserFormDataLogic>().SaveRuleFormData(RubricConstant.EstimateLineItem, estLineItemEntity.Id, estLineItemEntity.FormFk.Value, estLineItemEntity.EstHeaderFk);
			}

			/*Calculate lineitem and resources on drag drop*/
			if (estMainComplete.DoUpdate && currentEstLineItem != null)
			{
				if (currentEstLineItem.CombinedLineItems == null)
				{
					new EstimateMainCalculator().CalculateLineItem(currentEstLineItem, estMainComplete.ProjectId, true, false);
				}
				else
				{
					foreach (var lineItem in currentEstLineItem.CombinedLineItems)
					{
						new EstimateMainCalculator().CalculateLineItem(lineItem, estMainComplete.ProjectId, true, false);
					}
				}
			}
		}

		private void InitializeLineItems()
		{
			if (estMainComplete.CombinedLineItems != null)
			{
				estMainComplete.EstLineItems = null;
				estMainComplete.EstResourceToSave = null;
			}

			if (estMainComplete.EstLineItems == null || !estMainComplete.EstLineItems.Any())
			{
				return;
			}

			foreach (var item in estMainComplete.EstLineItems)
			{
				if (item.Quantity == 0)
				{
					item.EscalationCostTotal = 0;
					item.EscalationCostUnit = 0;
					item.RiskCostTotal = 0;
					item.RiskCostUnit = 0;
				}
			}

			/* check with the BoqWicCatFk value, will get from the GetWicCatsIdByBoqHeaderIds in Boq-Wic module */
			var wicBoqHeadFks = estMainComplete.EstLineItems.Where(e => e.WicBoqHeaderFk.HasValue && e.WicBoqItemFk.HasValue).Select(w => (int)w.WicBoqHeaderFk);

			header2GruopDic = RVPARB.BusinessEnvironment.GetExportedValue<IWicBoqLogic>().GetWicGroupIdByBoqHeaderIds(wicBoqHeadFks) as Dictionary<int, int>;

			AssignBoqWicCat(estMainComplete.EstLineItems, header2GruopDic);
		}

		private IEnumerable<EstResourceEntity> GetResourcesToSave()
		{
			if(estMainComplete.EstResourceToSave == null || !estMainComplete.EstResourceToSave.Any())
			{
				return Enumerable.Empty<EstResourceEntity>();
			}

			var resourcesToSave = estMainComplete.EstResourceToSave.Where(e => e.EstResource != null).Select(e => e.EstResource).ToList();

			/* remove the resource(to be save) when it is in the list(to be delete) */
			if (estMainComplete.EstResourceToDelete != null && estMainComplete.EstResourceToDelete.Any())
			{
				var resourceIdsToDelete = estMainComplete.EstResourceToDelete.Select(e => e.Id).ToHashSet();

				resourcesToSave = resourcesToSave.Where(e => !resourceIdsToDelete.Contains(e.Id)).ToList();
			}

			/* add protected assembly resources */
			var showProtectedAssembliesAsOneRecordSystemOption = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ShowProtectedAssembliesAsOneRecord);

			if (showProtectedAssembliesAsOneRecordSystemOption)
			{
				resourcesToSave.AddRange(CopyProtectedAssemblyResources(estMainComplete, resourcesToSave));

				/* Sort children and save */
				EstResourceEntity selectedItem = null;

				foreach (var targetRes in resourcesToSave.Where(e => e.EstResourceFk == null))
				{
					SortingGenerator.SetSorting(selectedItem, targetRes, resourcesToSave, true);
					selectedItem = targetRes;
				}
			}

			UpdatePlanCode(resourcesToSave);

			return resourcesToSave;
		}

		private void Recalculate(EstLineItemEntity currentEstLineItem)
		{
			if (!this.estMainComplete.NeedCalculate)
			{
				return;
			}

			var lineItems = estMainComplete.EstLineItems != null && estMainComplete.EstLineItems.Any() ? estMainComplete.EstLineItems.ToList() : new List<EstLineItemEntity>() { currentEstLineItem };

			/* use to update the client side cache */
			estMainComplete.EstLineItems = lineItems;

			var resourcesOfLineItem = new EstimateMainResourceLogic().GetResourcesByLineItemsCore(lineItems, new EstResourceSearchOption()
			{
				IncludeChildren = false,
				IgnoreRuleGenerated = false,
				IncludeExchangeRates = true,
				IncludeIsInformation = true,
				IncludeUserDefinedColumnVal = false,
				TranslateDescription = true,
				ProjectId = estMainComplete.ProjectId,
			});

			var resources2LineItemIdMap = resourcesOfLineItem.GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var lineItem in lineItems)
			{
				if (!resources2LineItemIdMap.ContainsKey(lineItem.Id))
				{
					continue;
				}

				lineItem.Resources = resources2LineItemIdMap[lineItem.Id].OfType<IScriptEstResource>().ToList();

				//update resource properties from lineitem(such as IsGc)
				if (lineItem.IsGc)
				{
					foreach (var resource in resources2LineItemIdMap[lineItem.Id])
					{
						resource.IsIndirectCost = false;
					}
				}
			}

			//calculate
			EstimateCalculatorService.Calculate(new EstimateCalculationOption(currentEstLineItem.EstHeaderFk, estMainComplete.ProjectId)
			{
				IsTreeStructure = false,
				IsAssembly = currentEstLineItem.LineItemType != (int)CommonLogic.LineItemTypes.LineItem,
				CheckModification = true
			}, lineItems)
				.Save(true, true, true)
				.AttachToCompleteEntity(this.estMainComplete);
		}

		private void GenerateProjectRelateData(EstLineItemEntity currentEstLineItem, IEnumerable<EstResourceEntity> resourcesToSave)
		{
			if(currentEstLineItem == null || resourcesToSave == null || !resourcesToSave.Any())
			{
				return;
			}

			//create project relation entity
			var jobId = currentEstLineItem.LgmJobFk ?? EstJobHelper.GetDefaultJobId(currentEstLineItem.EstHeaderFk, estMainComplete.ProjectId);

			var lineItemIds = resourcesToSave.Where(e => e.EstResourceFk.HasValue && !e.LgmJobFk.HasValue).Select(e => e.EstLineItemFk).Distinct().ToList();

			var resourcesOfLineItems = lineItemIds.Any() ? new EstimateMainResourceLogic().GetResourcesByLineItemIdsCore(lineItemIds, currentEstLineItem.EstHeaderFk, new EstResourceSearchOption()
			{
				TranslateDescription = false
			}) : new List<EstResourceEntity>();

			var estProjectRelEntityCollectionResult = new EstProjectRelationEntityCollector().CollectProjectRelEntity(currentEstLineItem, resourcesToSave, resourcesOfLineItems, jobId, estMainComplete.IsPrjectAssembly);

			var estProjectAssemblyRelCollection = estProjectRelEntityCollectionResult.Save(resourcesToSave.ToList(), estMainComplete.ProjectId, jobId, estMainComplete);

			// saved resoures done, calucate the project assemblies
			if (estProjectAssemblyRelCollection != null && estProjectAssemblyRelCollection.PrjAssemblyIdsToUpdate.Count > 0)
			{
				new EstimateMainResourceLogic().UpdatePrjAssemblyResources((int)estMainComplete.ProjectId, estProjectAssemblyRelCollection.PrjAssemblyIdsToUpdate, estProjectAssemblyRelCollection.PrjAssemblyHeaderId);
			}

			// saved resoures done, calucate the project assemblies
			if (estProjectAssemblyRelCollection != null && estProjectAssemblyRelCollection.PrjPlantAssemblyIdsToUpdate.Count > 0)
			{
				new EstimateMainResourceLogic().UpdatePrjPlantAssemblyResources((int)estMainComplete.ProjectId, estProjectAssemblyRelCollection.PrjPlantAssemblyIdsToUpdate, estProjectAssemblyRelCollection.PrjPlantAssemblyHeaderId);
			}
		}

		private void UpdateReferenceAssembly(EstLineItemEntity currentEstLineItem)
		{
			/* if it is assembly, update the reference assemblies which use it as resource */
			if (currentEstLineItem != null && currentEstLineItem.LineItemType == 1)
			{
				var filterInfo = estMainComplete.IsPrjectAssembly ? new EstimateAssemblyResourcesUpdateData()
				{
					IsPrjAssembly = true,
					ProjectId = (int)estMainComplete.ProjectId
				} : null;

				var refLineItems = new EstimateAssemblyUpdateLogic(currentEstLineItem.EstHeaderFk).UpdateAssembliesByBaseAssembly(currentEstLineItem, filterInfo);

				new EstSaveResult()
				{
					LineItemsUpdated = refLineItems,
					UserDefinedValsSaveResult = new UdpSaveResult()
					{
						LineItemUdpsUpdated = refLineItems.Where(e => e.UserDefinedcolValEntity != null).Select(e => new UserDefinedcolValEntity(e.UserDefinedcolValEntity))
					}
				}.AttachToCompleteEntity(estMainComplete);
			}
		}

		private void UpdateReferenceLineItem(EstLineItemEntity currentEstLineItem)
		{
			if (estMainComplete.EstLineItems == null || !estMainComplete.EstLineItems.Any())
			{
				return;
			}

			bool needToUpdateReferenceLineItem = (estMainComplete.EstResourceToDelete != null && estMainComplete.EstResourceToDelete.Any()) || (estMainComplete.EstResourceToSave != null && estMainComplete.EstResourceToSave.Any());

			/* update reference lineitem */
			if (!needToUpdateReferenceLineItem || currentEstLineItem == null || currentEstLineItem.EstLineItemFk.HasValue || currentEstLineItem.LineItemType != 0 || estMainComplete.IgnoreCalculation)
			{
				return;
			}

			var refLineItems = new EstimateMainCalculator().GetReferenceLineItems(currentEstLineItem);

			if (!refLineItems.Any())
			{
				return;
			}

			var referenceIdsExist = estMainComplete.EstLineItems.Select(e => e.Id).Distinct().ToHashSet();

			var refLineItemToUpdate = refLineItems.Where(e => !referenceIdsExist.Contains(e.Id)).ToList();

			if (!refLineItemToUpdate.Any())
			{
				return;
			}

			currentEstLineItem.Resources = currentEstLineItem.Resources != null && currentEstLineItem.Resources.Any() ? currentEstLineItem.Resources : new EstimateMainResourceLogic().GetTreeByLineItemId(currentEstLineItem.Id, currentEstLineItem.EstHeaderFk).OfType<IScriptEstResource>().ToList();

			using (var updateContextScope = new UpdateContextScope(new ReferenceLineItemUpdateContextObject(refLineItemToUpdate, currentEstLineItem, estMainComplete)))
			{
				new EstimateTotalCalculator(currentEstLineItem.EstHeaderFk, estMainComplete.ProjectId).UpdateReferenceLineItemsCore(currentEstLineItem, refLineItemToUpdate);

				AssignBoqWicCat(refLineItemToUpdate, header2GruopDic);

				estimateMainLineItemLogic.BulkSave(refLineItemToUpdate);

				new EstSaveResult()
				{
					LineItemsUpdated = refLineItemToUpdate,
					UserDefinedValsSaveResult = new UdpSaveResult()
					{
						LineItemUdpsUpdated = new UserDefinedColumnValueLogic().GetListByKeys((int)userDefinedColumnTableIds.EstimateLineItem, currentEstLineItem.EstHeaderFk, refLineItemToUpdate.Select(e => e.Id).Distinct().ToList())
					}
				}.AttachToCompleteEntity(estMainComplete);
			}
		}

		private static int? GetCurrentLineItemId(EstMainCompleteEntity estMainComplete)
		{
			if (estMainComplete.MainItemId > 0)
			{
				return estMainComplete.MainItemId;
			}

			if (estMainComplete.EstResourceToDelete != null && estMainComplete.EstResourceToDelete.Any())
			{
				return estMainComplete.EstResourceToDelete.First().EstLineItemFk;
			}

			if (estMainComplete.EstResourceToSave != null && estMainComplete.EstResourceToSave.Any())
			{
				var first = estMainComplete.EstResourceToSave.FirstOrDefault(e => e.EstResource != null);

				if (first != null)
				{
					return first.EstResource.EstLineItemFk;
				}
			}

			if(estMainComplete.EstLineItems != null && estMainComplete.EstLineItems.Any())
			{
				return estMainComplete.EstLineItems.First().Id;
			}

			return null;
		}

		private EstLineItemEntity GetCurrentLineItem(EstMainCompleteEntity estMainComplete, int estHeaderId)
		{
			int? currentLineItemId = GetCurrentLineItemId(estMainComplete);

			if (!currentLineItemId.HasValue)
			{
				return null;
			}

			if (estMainComplete.EstLineItems != null && estMainComplete.EstLineItems.Any())
			{
				var currentEstLineItem = estMainComplete.EstLineItems.FirstOrDefault(e => e.Id == currentLineItemId.Value);

				if (currentEstLineItem != null)
				{
					return currentEstLineItem;
				}
			}

			return estimateMainLineItemLogic.GetLineItemByFk(currentLineItemId.Value, estHeaderId);
		}

		private void SaveCombinedLineItems()
		{
			if (estMainComplete.CombinedLineItemsToSave != null && estMainComplete.CombinedLineItemsToSave.Any())
			{
				foreach (var combinedLineItems in estMainComplete.CombinedLineItemsToSave.Select(e => e.CombinedLineItems))
				{
					foreach (var lineitem in combinedLineItems)
					{
						/* save sort code info */
						SaveSortCodeInfo(lineitem, estMainComplete.SortCodeInfoToSave, estMainComplete.ProjectId);
					}

					/* save translation */
					combinedLineItems.SaveTranslate(estimateMainLineItemLogic.UserLanguageId, new Func<EstLineItemEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

					/* save lineItems */
					estimateMainLineItemLogic.BulkSave(combinedLineItems);

					/* to show the detail by culture */
					EstCultureHelper.SetLineItemDetailByCulture(combinedLineItems);
				}
			}

			if (estMainComplete.CombinedLineItems != null && estMainComplete.CombinedLineItems.Any())
			{
				#region Save Combined Line items

				var estLineItem2CostGroupLogic = new EstLineItem2CostGroupLogic();

				// Cost Total
				decimal originalCostTotal = 0;
				// Hour Total
				decimal originalHourTotal = 0;
				var headerEntity = new EstHeaderEntity
				{
					Id = estMainComplete.CombinedLineItems.First().EstHeaderFk
				};

				var columnConfigDetails = new List<EstColumnConfigDetailEntity>();
				if (estMainComplete.confDetail != null && estMainComplete.confDetail.Any())
				{
					columnConfigDetails = new EstLineItemColumnConfigLogic(headerEntity, null).GetList();
				}

				foreach (var items in estMainComplete.CombinedLineItems)
				{
					if (items.CombinedLineItems != null && items.CombinedLineItems.Any())
					{
						int? costCodeFk = null;
						decimal oldValue = 0.0m;
						decimal dynamicValue = 0.0m;
						int dynamicType = 0;
						int columnId = 0;
						List<int> costCodes = new List<int>();

						IProjectCostCodesLogic projCoCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();

						if (estMainComplete.confDetail != null && estMainComplete.confDetail.Any())
						{
							foreach (var item in estMainComplete.confDetail)
							{
								int id = System.Convert.ToInt32(item.key.Remove(0, 10));
								var colConfig = columnConfigDetails.First(e => e.Id == id);
								costCodeFk = colConfig.MdcCostCodeFk;
								dynamicValue = item.value;
								columnId = colConfig.ColumnId;

								if (colConfig.LineType == 2)
								{
									dynamicType = 2;
								}
								else
								{
									var costCode = projCoCodeLogic.GetProjectCostCodes(estMainComplete.ProjectId ?? 0, new List<int> { costCodeFk ?? 0 }, null).FirstOrDefault();
									dynamicType = costCode != null && costCode.IsLabour ? 1 : 3;
								}
							}

							// get the costcodes by costCodeFk
							if (costCodeFk != null)
							{
								costCodes = new BasicsCostCodesLogic().GetChildrenById(costCodeFk ?? 0).Select(e => e.Id).ToList();
								costCodes.Add(costCodeFk ?? 0);
							}
						}

						if (estMainComplete.CombinedLineItems.Any())
						{
                            items.SaveTranslate(estimateMainLineItemLogic.UserLanguageId, new Func<EstLineItemEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
                        }

                        var lineItems = estimateMainLineItemLogic.GetLineItemByIds(items.CombinedLineItems.Select(e => e.Id), items.EstHeaderFk).Select(e => e as EstLineItemEntity).ToList();
						var resourcesByLineitems = new EstimateMainResourceLogic().GetResourcesByLineItemsCore(items.CombinedLineItems, new EstResourceSearchOption()
						{
							IncludeChildren = true,
							IgnoreRuleGenerated = false,
							TranslateDescription = false,
							IncludeExchangeRates = true
						}).ToList();

						// change costgroup for combined lineitems
						List<EstLineItem2CostGroupEntity> item2CostGroups = new List<EstLineItem2CostGroupEntity>();
						if (estMainComplete.CostGroupToSave != null)
						{
							item2CostGroups = estLineItem2CostGroupLogic.GetListByLineItemIds(lineItems.Select(x => x.Id), items.EstHeaderFk).ToList();
						}

						// load the project costcodes
						var mdcCostCodeIds = resourcesByLineitems.Where(e => e.MdcCostCodeFk.HasValue).Select(i => i.MdcCostCodeFk.Value).Distinct();
						var prjCoCodeList = projCoCodeLogic.GetProjectCostCodes(estMainComplete.ProjectId ?? 0, mdcCostCodeIds);

						foreach (var itemId in items.CombinedLineItems.Select(e => e.Id))
						{
							var resources = resourcesByLineitems.Where(e => e.EstLineItemFk == itemId).ToList();
							var lineitem = lineItems.First(e => e.Id == itemId);

							// sum costtotal and hourstotal
							originalCostTotal += lineitem.CostTotal;
							originalHourTotal += lineitem.HoursTotal;

							if (costCodeFk != null && costCodes.Any())
							{
								if (columnId == (int)ColumnId.CostUnitLineitem)
								{
									if (oldValue == 0)
									{
										oldValue = resources.Where(e => costCodes.Contains(e.MdcCostCodeFk ?? 0)).Sum(e => e.CostUnitLineItem);
									}
								}
								else
								{
									oldValue += resources.Where(e => costCodes.Contains(e.MdcCostCodeFk ?? 0)).Sum(e => e.CostTotal);
								}
							}
						}

						List<EstLineItemEntity> lineItemsToSave = new List<EstLineItemEntity>();
						List<EstResourceEntity> resourcesToSave = new List<EstResourceEntity>();

						List<EstLineItem2CostGroupEntity> lineItem2CostGroupsToSave = new List<EstLineItem2CostGroupEntity>();
						List<EstLineItem2CostGroupEntity> lineItem2CostGroupsToDelete = new List<EstLineItem2CostGroupEntity>();

						foreach (var combinedLineItem in items.CombinedLineItems)
						{
							var item = lineItems.First(e => e.Id == combinedLineItem.Id);
							item.SortCode01Fk = items.SortCode01Fk;
							item.SortCode02Fk = items.SortCode02Fk;
							item.SortCode03Fk = items.SortCode03Fk;
							item.SortCode04Fk = items.SortCode04Fk;
							item.SortCode05Fk = items.SortCode05Fk;
							item.SortCode06Fk = items.SortCode06Fk;
							item.SortCode07Fk = items.SortCode07Fk;
							item.SortCode08Fk = items.SortCode08Fk;
							item.SortCode09Fk = items.SortCode09Fk;
							item.SortCode10Fk = items.SortCode10Fk;
							item.BasUomFk = items.BasUomFk;
							item.PrjLocationFk = items.PrjLocationFk;

							if (item.DescriptionInfo.Translated != combinedLineItem.DescriptionInfo.Translated)
							{
								item.DescriptionInfo.Translated = combinedLineItem.DescriptionInfo.Translated;
								item.DescriptionInfo.Modified = true;
							}

                            item.QuantityFactor4 = combinedLineItem.QuantityFactor4;

							if (item.Quantity != combinedLineItem.Quantity)
							{
								item.Quantity = combinedLineItem.Quantity;
								item.QuantityDetail = item.Quantity.ToString();
							}

							if (item.WqQuantityTarget != combinedLineItem.WqQuantityTarget)
							{
								item.WqQuantityTarget = combinedLineItem.WqQuantityTarget;
								item.WqQuantityTargetDetail = item.WqQuantityTarget.ToString();
							}

							if (item.QuantityTarget != combinedLineItem.QuantityTarget)
							{
								item.QuantityTarget = combinedLineItem.QuantityTarget;
								item.QuantityTargetDetail = item.QuantityTarget.ToString();
							}

							lineItemsToSave.Add(item);

							decimal unitCost = item.CostUnit;
							decimal hourCost = item.HoursUnit;
							decimal dynamicfactor = 1.0m;

							bool isCostUnitChange = false;
							bool isHoursUnitChange = false;

							decimal newCostFactor = 1;
							decimal newQuantityFactor = 1;

							if (dynamicValue != oldValue && oldValue != 0.0m)
							{
								dynamicfactor = dynamicValue / oldValue;
							}

							if (items.CostUnit != unitCost && unitCost != 0)
							{
								newCostFactor = items.CostUnit / unitCost;
								isCostUnitChange = true;
							}

							if (items.CostTotal != originalCostTotal && originalCostTotal != 0)
							{
								if (!isCostUnitChange)
								{
									newCostFactor = items.CostTotal / originalCostTotal;
								}
								else
								{
									newCostFactor *= items.CostTotal / originalCostTotal;
								}
							}

							if (items.HoursUnit != hourCost && hourCost != 0)
							{
								newQuantityFactor = items.HoursUnit / hourCost;
								isHoursUnitChange = true;
							}

							if (items.HoursTotal != originalHourTotal && originalHourTotal != 0)
							{
								if (!isHoursUnitChange)
								{
									newQuantityFactor = items.HoursTotal / originalHourTotal;
								}
								else
								{
									newQuantityFactor *= items.HoursTotal / originalHourTotal;
								}
							}

							var resources = resourcesByLineitems.Where(e => e.EstLineItemFk == item.Id).ToList();
							if (newQuantityFactor != 1 || newCostFactor != 1 || dynamicfactor != 1)
							{
								var resCostCodeIds = resources.Where(e => e.MdcCostCodeFk.HasValue).Select(i => i.MdcCostCodeFk.Value).ToList();
								var costCodesByResource = prjCoCodeList.Where(e => e.MdcCostCodeFk.HasValue && resCostCodeIds.Contains(e.MdcCostCodeFk.Value));

								foreach (var resource in resources)
								{
									if (resource.MdcCostCodeFk.HasValue)
									{
										var isLabor = costCodesByResource.FirstOrDefault(e => e.MdcCostCodeFk.Value == resource.MdcCostCodeFk.Value).IsLabour;

										if (newQuantityFactor != 1 && isLabor && resource.MdcMaterialFk == null)
										{
											resource.QuantityFactor4 = Math.Round(newQuantityFactor * resource.QuantityFactor4, 6);
										}

										if (newCostFactor != 1)
										{
											if (isLabor && resource.MdcMaterialFk == null)
											{
												resource.QuantityFactor4 = Math.Round(newCostFactor * resource.QuantityFactor4, 6);
											}
											else
											{
												resource.CostFactor2 = Math.Round(newCostFactor * resource.CostFactor2, 6);
												resource.CostFactorDetail2 = resource.CostFactor2.ToString();
											}
										}

										if (dynamicfactor != 1)
										{
											estMainComplete.IsReload = true;

											if (dynamicType == 1 && costCodeFk != null)
											{
												if (costCodes.Contains(resource.MdcCostCodeFk ?? 0))
												{
													resource.QuantityFactor4 = Math.Round(dynamicfactor * resource.QuantityFactor4, 6);
												}
											}
											else if ((dynamicType == 2 && resource.MdcMaterialFk != null) || (dynamicType == 3 && costCodes.Contains(resource.MdcCostCodeFk ?? 0)))
											{
												resource.CostFactor2 = Math.Round(dynamicfactor * resource.CostFactor2, 6);
												resource.CostFactorDetail2 = resource.CostFactor2.ToString();
											}
										}
									}
								}
							}

							// calculate lineitem and resources, while resources changed.
							var estTotalCalculator = new EstimateTotalCalculator(item.EstHeaderFk, estMainComplete.ProjectId);
							estTotalCalculator.CalculateLineItemAndResourcesInList(item, resources);

							resourcesToSave.AddRange(resources);

							// update costgroup to lineitems
							if (estMainComplete.CostGroupToSave != null && estMainComplete.CostGroupToSave.Any())
							{
								foreach (var cg in estMainComplete.CostGroupToSave)
								{
									var cGrp = item2CostGroups.Find(e => e.EstLineItemFk == item.Id && e.CostGroupCatFk == cg.CostGroupCatFk);
									if (cGrp != null)
									{
										cGrp.CostGroupFk = cg.CostGroupFk;
										lineItem2CostGroupsToSave.Add(cGrp);
									}
									else
									{
										var newCostGroup = estLineItem2CostGroupLogic.CreateEntity(item.Id, item.EstHeaderFk, cg.CostGroupCatFk, cg.CostGroupFk);
										lineItem2CostGroupsToSave.Add(newCostGroup);
									}

									estMainComplete.IsReload = true;
								}
							}

							// delete costgroup to lineitems
							if (estMainComplete.CostGroupToDelete != null && estMainComplete.CostGroupToDelete.Any())
							{
								foreach (var cg in estMainComplete.CostGroupToDelete)
								{
									var cGrp = item2CostGroups.Find(e => e.EstLineItemFk == item.Id && e.CostGroupCatFk == cg.CostGroupCatFk);
									if (cGrp != null)
									{
										lineItem2CostGroupsToDelete.Add(cGrp);
										estMainComplete.IsReload = true;
									}
								}
							}
						}

						// calculate system parameter detail while lineItem's quantity be changes.
						foreach (var line in lineItemsToSave)
						{
							line.EstResourceEntities = resourcesByLineitems.Where(y => y.EstLineItemFk == line.Id && y.EstHeaderFk == line.EstHeaderFk).ToList();
						}
						var lineItemUpdateHelper = new EstLineItemUpdateHelper(estMainComplete.EstHeaderId, estMainComplete.ProjectId, new EstLineItemUpdateOption { IsCalculateDetail = false, IsPrjAssembly = estMainComplete.IsPrjectAssembly });
						lineItemUpdateHelper.CalculateDetails(lineItems);

						estLineItem2CostGroupLogic.SaveEntities(lineItem2CostGroupsToSave);
						estLineItem2CostGroupLogic.Delete(lineItem2CostGroupsToDelete);
						new EstimateMainResourceLogic().SaveResources(resourcesToSave);
						estimateMainLineItemLogic.BulkSave(lineItemsToSave);
						items.CombinedLineItems = lineItemsToSave;
						estMainComplete.EstLineItems = items.CombinedLineItems;
						// the lineitems have changed, need to combined again
						if (estMainComplete.CombinedViewList.Any())
						{
							var combinedLineItem = new EstimateCombinedLineItemLogic().CreateCombinedEntities(estMainComplete.EstLineItems, estMainComplete.CombinedViewList.ToList(), lineItem2CostGroupsToSave).FirstOrDefault();
							if (combinedLineItem != null)
							{
								var properties = items.GetType().GetProperties();
								foreach (var property in properties)
								{
									if (property.PropertyType.Name == "Decimal")
									{
										var propInfo = items.GetType().GetProperty(property.Name);
										var combinedPropInfo = combinedLineItem.GetType().GetProperty(property.Name);
										if (propInfo != null && combinedPropInfo != null)
										{
											propInfo.SetValue(items, combinedPropInfo.GetValue(combinedLineItem, null));
										}
									}
								}
							}
						}
					}
					else
					{
						if (estMainComplete.CostGroupToSave != null)
						{
							foreach (var cg in estMainComplete.CostGroupToSave)
							{
								var cGrp = new EstLineItem2CostGroupLogic().GetList(items.Id, items.EstHeaderFk).First(e => e.CostGroupCatFk == cg.CostGroupCatFk);
								cGrp.CostGroupFk = cg.CostGroupFk;
								new EstLineItem2CostGroupLogic().SaveEntity(cGrp);
							}
						}

						var lineItems = new List<EstLineItemEntity>() { items };
						var resources = new EstimateMainResourceLogic().GetResourcesByLineItems(lineItems).Where(e => e.EstAssemblyFk == null).ToList();
						// sum total should not be include the children
						var parentLevelResources = resources.Where(e => !e.EstResourceFk.HasValue && e.EstAssemblyFk == null).ToList();
						originalCostTotal = parentLevelResources.Sum(e => e.CostTotal);
						originalHourTotal = parentLevelResources.Sum(e => e.HoursTotal);

						decimal unitCost = items.CostUnit;
						decimal hourCost = items.HoursUnit;

						bool isCostUnitChange = false;
						bool isHoursUnitChange = false;

						decimal newCostFactor = 1;
						decimal newQuantityFactor = 1;

						if (items.CostUnit != unitCost && unitCost != 0)
						{
							newCostFactor = items.CostUnit / unitCost;
							isCostUnitChange = true;
						}

						if (items.CostTotal != originalCostTotal && originalCostTotal != 0)
						{
							if (!isCostUnitChange)
							{
								newCostFactor = items.CostTotal / originalCostTotal;
							}
							else
							{
								newCostFactor *= items.CostTotal / originalCostTotal;
							}
						}

						if (items.HoursUnit != hourCost && hourCost != 0)
						{
							newQuantityFactor = items.HoursUnit / hourCost;
							isHoursUnitChange = true;
						}

						if (items.HoursTotal != originalHourTotal && originalHourTotal != 0)
						{
							if (!isHoursUnitChange)
							{
								newQuantityFactor = items.HoursTotal / originalHourTotal;
							}
							else
							{
								newQuantityFactor *= items.HoursTotal / originalHourTotal;
							}
						}

						if (newQuantityFactor != 1 || newCostFactor != 1)
						{
							IProjectCostCodesLogic projCoCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();
							var mdcCostCodeIds = resources.Where(e => e.MdcCostCodeFk.HasValue).Select(i => i.MdcCostCodeFk.Value).Distinct();
							var costCodesByResource = projCoCodeLogic.GetProjectCostCodes(estMainComplete.ProjectId ?? 0, mdcCostCodeIds);

							foreach (var resource in resources)
							{
								if (resource.MdcCostCodeFk.HasValue)
								{
									var isLabor = costCodesByResource.FirstOrDefault(e => e.MdcCostCodeFk.Value == resource.MdcCostCodeFk.Value).IsLabour;

									if (newQuantityFactor != 1)
									{
										if (isLabor && resource.MdcMaterialFk == null)
										{
											resource.QuantityFactor4 = Math.Round(newQuantityFactor * resource.QuantityFactor4, 6);
										}
										else
										{
											resource.CostFactor2 = Math.Round(newQuantityFactor * resource.CostFactor2, 6);
											resource.CostFactorDetail2 = resource.CostFactor2.ToString();
										}
									}

									if (newCostFactor != 1)
									{
										if (isLabor && resource.MdcMaterialFk == null)
										{
											resource.QuantityFactor4 = Math.Round(newCostFactor * resource.QuantityFactor4, 6);
										}
										else
										{
											resource.CostFactor2 = Math.Round(newCostFactor * resource.CostFactor2, 6);
											resource.CostFactorDetail2 = resource.CostFactor2.ToString();
										}
									}
								}
							}
						}

						// calculate lineitem and resources, while resources changed.
						var estTotalCalculator = new EstimateTotalCalculator(items.EstHeaderFk, estMainComplete.ProjectId);
						estTotalCalculator.CalculateLineItemAndResourcesInList(items, resources);

						new EstimateMainResourceLogic().SaveResources(resources);
						estimateMainLineItemLogic.Save(items);
					}
				}

				#endregion
			}
		}

		private void DeleteResourcePrcItemAssignment()
		{
			var prcItemAssignmentLogic = RVPARB.BusinessEnvironment.GetExportedValue<IPrcItemAssignmentLogic>();

			var rids = estMainComplete.EstResourceToDelete.Select(e => e.Id).ToList();

			var prcItemAssignmentEntitys = prcItemAssignmentLogic.GetEntitiesByEstHeaderId(estMainComplete.EstResourceToDelete.First().EstHeaderFk);

			if (prcItemAssignmentEntitys != null && prcItemAssignmentEntitys.Any())
			{
				prcItemAssignmentEntitys = prcItemAssignmentEntitys.Where(e => e.EstResourceFk.HasValue && rids.Contains(e.EstResourceFk.Value)).ToList();
			}

			var prcItemAssignmentDeletes = prcItemAssignmentLogic.DeleteByEstimateInfo(estMainComplete.EstHeaderId, estMainComplete.MainItemId, rids);

			if (prcItemAssignmentDeletes.Any())
			{
				estimateMainLineItemLogic.CalculatePackage(prcItemAssignmentEntitys);
			}
		}

		/// <summary>
		/// Delete Escalation Data Of Resources
		/// </summary>
		/// <param name="resources"></param>
		private static void DeleteEscalationData(IEnumerable<EstResourceEntity> resources)
		{
			if (resources == null || !resources.Any())
			{
				return;
			}

			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				foreach (var item in resources)
				{
					var entities = dbcontext.Entities<EstEscalationAmountEntity>().Where(e => e.LineitemFk == item.EstLineItemFk && e.EstResourceFk == item.Id).ToList();
					if (entities.Count > 0)
					{
						for (int i = 0; i < entities.Count; i++)
						{
							EstEscalationAmountEntity estEscalationAmountResultsItem = entities[i];
							if (estEscalationAmountResultsItem != null)
							{
								dbcontext.Entities<EstEscalationAmountEntity>().Attach(estEscalationAmountResultsItem);
								dbcontext.Entry<EstEscalationAmountEntity>(estEscalationAmountResultsItem).State = EntityState.Deleted;
								dbcontext.Delete(estEscalationAmountResultsItem);
							}
						}
					}
				}
			}
		}

		private static void SaveSortCodeInfo(EstLineItemEntity lineItem, IEnumerable<EstSortCodes> sortCodeInfoToSave, int? project)
		{
			if (lineItem == null || sortCodeInfoToSave == null || !project.HasValue || project.Value <= 0)
			{
				return;
			}

			var prjStructLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectStructuresLogic>();

			foreach (var sortCode in sortCodeInfoToSave.Where(e => !e.IsExist))
			{
				var sortCodeFk = prjStructLogic.Save(sortCode.Code, sortCode.Field, project.Value);

				var propInfo = lineItem.GetType().GetProperty(sortCode.Field + "Fk");

				if (propInfo == null)
				{
					continue;
				}

				propInfo.SetValue(lineItem, sortCodeFk);
			}
		}

		private void SaveEstimateAllowance()
		{
			var allowancesToCreateRestArea = estMainComplete.EstimateAllowanceToSave != null ? estMainComplete.EstimateAllowanceToSave.Where(e => e.Version == 0 && e.MdcAllowanceTypeFk == 3).ToList() : new List<EstAllowanceEntity>();

			if (estMainComplete.EstimateAllowanceToSave != null && estMainComplete.EstimateAllowanceToSave.Any())
			{
				List<EstAllowanceEntity> estAllowanceToUpdate = new List<EstAllowanceEntity>();

				var allowanceTypeDefault = new BasicsCustomizeAllowanceTypeLogic().GetDefault();

				var markupCalculationTypeDefault = new BasicsCustomizeMarkupCalculationTypeLogic().GetDefault();

				foreach (var item in estMainComplete.EstimateAllowanceToSave)
				{
					if (item.MdcAllowanceTypeFk == default(int))
					{
						item.MdcAllowanceTypeFk = allowanceTypeDefault.Id;
					}

					if (item.MdcMarkUpCalcTypeFk == default(int))
					{
						item.MdcMarkUpCalcTypeFk = markupCalculationTypeDefault.Id;
					}

					estAllowanceToUpdate.Add(item);
				}

				new EstAllowanceLogic().UpdateEstAllowance(estAllowanceToUpdate);

				new EstAllowanceLogic().HandleAllowanceResource(estAllowanceToUpdate, estMainComplete.EstHeaderId);
			}

			if (estMainComplete.AllowanceAreaToSave != null && estMainComplete.AllowanceAreaToSave.Any())
			{
				var allowancesToSave = estMainComplete.AllowanceAreaToSave.Where(e => e.AllowanceArea != null).Select(e => e.AllowanceArea).ToList();

				if (allowancesToSave.Any())
				{
					new EstAllowanceAreaLogic().Save(allowancesToSave);
				}

				var boqAreaRangesToSave = estMainComplete.AllowanceAreaToSave.Where(e => e.BoqAreaAssigmentToSave != null).SelectMany(e => e.BoqAreaAssigmentToSave).ToList();

				if (boqAreaRangesToSave.Any())
				{
					new EstAllAreaBoqRangeLogic().Save(boqAreaRangesToSave);
				}

				var boqAreaRangesToDelete = estMainComplete.AllowanceAreaToSave.Where(e => e.BoqAreaAssigmentToDelete != null).SelectMany(e => e.BoqAreaAssigmentToDelete).ToList();

				if (boqAreaRangesToDelete.Any())
				{
					new EstAllAreaBoqRangeLogic().Delete(boqAreaRangesToDelete);
				}
			}

			// create rest area
			if (allowancesToCreateRestArea.Any())
			{
				var restAreaValues = new List<EstAllArea2GcAreaValueEntity>();

				var estAllowanceAreaLogic = new EstAllowanceAreaLogic();

				var restAreasToSave = new List<EstAllowanceAreaEntity>();

				foreach (var allowanceId in allowancesToCreateRestArea.Select(e => e.Id))
				{
					var normalRestArea = estAllowanceAreaLogic.Create(allowanceId, (int)EstAreaType.NormalRestArea, -2);

					normalRestArea.Code = "Rest";

					restAreasToSave.Add(normalRestArea);

					var gcRestArea = estAllowanceAreaLogic.Create(allowanceId, (int)EstAreaType.GcRestArea, -3);

					gcRestArea.Code = "Rest";

					restAreasToSave.Add(gcRestArea);

					//create rest area value
					restAreaValues.Add(new EstAllArea2GcAreaValueLogic().Create(new EstAllAreaValueCreateData()
					{
						EstAllowanceFk = allowanceId,
						EstAllowanceAreaFk = normalRestArea.Id,
						EstAllowanceGcAreaFk = gcRestArea.Id,
						Value = 100
					}));
				}

				estAllowanceAreaLogic.Save(restAreasToSave);

				var restAreasDtos = restAreasToSave.Select(e => new EstAllowanceAreaComplete()
				{
					MainItemId = e.Id,
					AllowanceArea = e
				}).ToList();

				if (estMainComplete.AllowanceAreaToSave != null)
				{
					restAreasDtos.AddRange(estMainComplete.AllowanceAreaToSave);
				}

				estMainComplete.AllowanceAreaToSave = restAreasDtos;

				if (estMainComplete.Area2GcAreaValueToSave == null)
				{
					estMainComplete.Area2GcAreaValueToSave = restAreaValues;
				}
				else
				{
					estMainComplete.Area2GcAreaValueToSave = estMainComplete.Area2GcAreaValueToSave.Concat(restAreaValues);
				}
			}

			if (estMainComplete.Area2GcAreaValueToSave != null && estMainComplete.Area2GcAreaValueToSave.Any())
			{
				new EstAllArea2GcAreaValueLogic().Save(estMainComplete.Area2GcAreaValueToSave);
			}
		}

		private void SaveEstimateAllowanceStep2()
		{
			if (estMainComplete.EstimateAllowanceToDelete != null && estMainComplete.EstimateAllowanceToDelete.Any())
			{
				new EstAllowanceLogic().DeleteEstAllowance(estMainComplete.EstimateAllowanceToDelete);
			}

			if (estMainComplete.AllowanceMarkUp2CostCodeToSave != null && estMainComplete.AllowanceMarkUp2CostCodeToSave.Any())
			{
				new EstAllMarkup2CostCodeLogic().Save(estMainComplete.AllowanceMarkUp2CostCodeToSave.Where(e => e.AllowanceMarkUp2CostCode != null && e.AllowanceMarkUp2CostCode.Id != -2).Select(e => e.AllowanceMarkUp2CostCode).ToList());
			}

			if (estMainComplete.AllowanceMarkUp2CostCodeToDelete != null && estMainComplete.AllowanceMarkUp2CostCodeToDelete.Any())
			{
				new EstAllMarkup2CostCodeLogic().Delete(estMainComplete.AllowanceMarkUp2CostCodeToDelete);
			}

			if (estMainComplete.AllowanceAreaToDelete != null && estMainComplete.AllowanceAreaToDelete.Any())
			{
				new EstAllowanceAreaLogic().DeleteEstAllowanceArea(estMainComplete.AllowanceAreaToDelete);
			}
		}

		private void SavePlantList()
		{
			if (estMainComplete.EstPlantListToSave != null && estMainComplete.EstPlantListToSave.Any())
			{
				new EstPlantListLogic().Update(estMainComplete.EstPlantListToSave);
			}
		}

		private static void AssignBoqWicCat(IEnumerable<EstLineItemEntity> lineItems, Dictionary<int, int> header2GruopDic)
		{
			if (lineItems == null || !lineItems.Any() || header2GruopDic == null || !header2GruopDic.Any())
			{
				return;
			}

			foreach (var lineItem in lineItems)
			{
				if (lineItem != null && lineItem.WicBoqHeaderFk.HasValue && header2GruopDic.ContainsKey(lineItem.WicBoqHeaderFk.Value))
				{
					lineItem.BoqWicCatFk = header2GruopDic[lineItem.WicBoqHeaderFk.Value];
				}
			}
		}

		private static int GetEstHeaderId(EstMainCompleteEntity estMainComplete)
		{
			int estHeaderId = estMainComplete.EstHeaderId;

			if (estMainComplete.EstHeaderId <= 0)
			{
				if (estMainComplete.EstResourceToDelete != null && estMainComplete.EstResourceToDelete.Any())
				{
					estHeaderId = estMainComplete.EstResourceToDelete.First().EstHeaderFk;
				}
				else if (estMainComplete.EstResourceToSave != null && estMainComplete.EstResourceToSave.Any(e => e.EstResource != null))
				{
					estHeaderId = estMainComplete.EstResourceToSave.First(e => e.EstResource != null).EstResource.EstHeaderFk;
				}
			}

			return estHeaderId;
		}

		private List<EstResourceEntity> CopyProtectedAssemblyResources(EstMainCompleteEntity estMainComplete, IEnumerable<EstResourceEntity> resourcesToSave)
		{
			var protectedAssemblyResourcesToSave = new List<EstResourceEntity>();

			var estLineItemUpdateFrmPrjLogic = new EstLineItemUpdateFrmPrjLogic(estMainComplete.ProjectId, new EstResourceUpdateOption());

			var estResourceCopyLogic = new EstResourceCopyLogic(estMainComplete.ProjectId);

			foreach (var resourceToSave in resourcesToSave.Where(x => x.EstResourceTypeFk == (int)EstResourceType.Assembly && estLineItemUpdateFrmPrjLogic.IsProtectedAssemblyUpdated(x)))
			{
				var resource = new EstimateMainResourceLogic().GetSearchList(x => x.EstResourceFk == resourceToSave.Id && x.EstLineItemFk == resourceToSave.EstLineItemFk);

				if (!resource.Any())
				{
					var resources = new EstimateMainResourceLogic().GetSearchList(x => x.EstLineItemFk == resourceToSave.EstAssemblyFk && x.EstResourceFk == null);

					var resourcesCopy = estResourceCopyLogic.CopyResources(resources, new ResourceCopyOption()
					{
						LineItemId = resourceToSave.EstLineItemFk,
						HeaderId = resourceToSave.EstHeaderFk,
						ParentResourceId = resourceToSave.Id,
						SetResourceJobFk = false,
						IncludeMarkupAsCostUnit = false,
						IsResolveAssembly = true,
						IsSetProjectCurrency = true,
						ResourceCopyFrom = ResourceModuleType.MasterAssembly,
						ResourceCopyTo = ResourceModuleType.Estimate,
						CharacteristicCopyOption = new CharacteristicCopyOption()
						{
							IsAutoAssignCharacteristic = true,
							IsCopyCharacteristic = true,
							AutoGenerateId = true
						}
					});

					protectedAssemblyResourcesToSave.AddRange(resourcesCopy.FlattenResources().Select(e => e as EstResourceEntity).ToList());
				}
			}

			return protectedAssemblyResourcesToSave;
		}

		private static void UpdatePlanCode(IEnumerable<EstResourceEntity> resourcesToSave)
		{
			var plantParentResourceIds = resourcesToSave.Where(e => e.EtmPlantFk != null).Select(e => e.EtmPlantFk);

			var withoutPlantParentResourceIds = new HashSet<int>(resourcesToSave.Where(e => e.EtmPlantFk == null && (e.EstResourceTypeFk == (int)EstResourceType.Plant || e.EstResourceTypeFk == (int)EstResourceType.PlantDissolved)).Select(e => e.Id));

			var plantEntities = new List<IEquipmentPlantEntity>();

			var prjPlantassemblies = new List<RVEABE>();

			if (plantParentResourceIds.Any())
			{
				var identificationData = plantParentResourceIds.Select(e => new IdentificationData() { Id = e.Value }).ToArray();

				plantEntities = Injector.Get<IEquipmentPlantLogic>().GetPlantByIds(identificationData).ToList();
			}

			if (withoutPlantParentResourceIds.Any())
			{
				var prjPlantAssembliesResources = resourcesToSave.Where(e => e.EstResourceFk.HasValue && e.EstResourceTypeFk == (int)EstResourceType.EquipmentAssembly && withoutPlantParentResourceIds.Contains(e.EstResourceFk.Value));

				var prjPlantAssemblyFkIds = prjPlantAssembliesResources.Select(e => e.EstAssemblyFk).Where(e => e.HasValue).Select(e => e.Value).ToArray();

				prjPlantassemblies = new EstimateAssembliesLogic().GetListByIds(prjPlantAssemblyFkIds, (int)CommonLogic.LineItemTypes.PlantAssembly).ToList();

			}

			var plantDictionary = plantEntities.ToDictionary(e => e.Id, e => e.Code); // Convert plant entities to a dictionary for faster lookup

			var withoutPlantDictionary = prjPlantassemblies.ToDictionary(e => e.Id, e => e.Code);

			foreach (var item in resourcesToSave)
			{
				if (item.QuantityTotal == 0)
				{
					item.EscResourceCostTotal = 0;
					item.EscResourceCostUnit = 0;
					item.RiskCostTotal = 0;
					item.RiskCostUnit = 0;
				}

				if (item.DescriptionInfo != null && string.IsNullOrEmpty(item.DescriptionInfo.Description) && (item.EstResourceTypeFk == (int)EstResourceType.SubItem || item.EstResourceTypeFk == (int)EstResourceType.TextLine || item.EstResourceTypeFk == (int)EstResourceType.InternalTextLine))
				{
					item.DescriptionInfo.Description = null;
				}

				if (item.EtmPlantFk != null && plantDictionary.TryGetValue(item.EtmPlantFk.Value, out var plantCode))
				{
					item.Code = plantCode;
				}

				if (item.EtmPlantFk == null && (item.EstResourceTypeFk == (int)EstResourceType.Plant || item.EstResourceTypeFk == (int)EstResourceType.PlantDissolved))
				{
					var prjPlantAssemblyResource = resourcesToSave.FirstOrDefault(e => e.EstResourceFk == item.Id);

					if(prjPlantAssemblyResource != null)
					{
						var assemblyCode = withoutPlantDictionary.TryGetValue(prjPlantAssemblyResource.EstAssemblyFk.Value, out var prjPlantCode);

						item.Code = string.Join(" - ", "W/O Plant", prjPlantCode);
					}
				}
			}
		}

		private void SaveUserDefinedColumnValue()
		{
			var allUserDefinedColumnValueToSave = new UserDefinedColumnValueComplete();

			allUserDefinedColumnValueToSave.merge(estMainComplete.UserDefinedcolsOfResourceToUpdate);

			allUserDefinedColumnValueToSave.merge(estMainComplete.UserDefinedcolsOfLineItemToUpdate);

			new UserDefinedColumnValueLogic().Update(allUserDefinedColumnValueToSave);
		}

		private void SavePriceAdjustment()
		{
			if (estMainComplete.EstimatePriceAdjustmentToSave != null && estMainComplete.EstimatePriceAdjustmentToSave.Any())
			{
				new EstPriceAdjustmentLogic().UpdateEstimatePriceAdjustment(estMainComplete.EstimatePriceAdjustmentToSave);
			}
		}

        private static readonly LazyExportedValue<IActiveCollaboratorsManager> ActiveCollaboratorsMgr = new();
        private void SaveEstAssemblyCat()
		{
			if (estMainComplete.EstAssemblyCat != null)
			{
				estMainComplete.EstAssemblyCat.SaveTranslate(estimateMainLineItemLogic.UserLanguageId, new Func<EstAssemblyCatEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

				estMainComplete.EstAssemblyCat = new EstimateAssembliesStructureLogic().Save(estMainComplete.EstAssemblyCat);
            ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(estMainComplete.EstAssemblyCat));
            }

			if (estMainComplete.EstAssemblyCatToSave != null && estMainComplete.EstAssemblyCatToSave.Any())
			{
				estMainComplete.EstAssemblyCatToSave = new EstimateAssembliesStructureLogic().Save(estMainComplete.EstAssemblyCatToSave);
				ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(estMainComplete.EstAssemblyCat));
			}
		}

        private void SaveAssembliesCtrlGrp()
		{
			var ctrlGrpLogic = new EstimateAssembliesCtrlGrpLogic();

			if (estMainComplete.EstAssembliesCtrlGrpToSave != null && estMainComplete.EstAssembliesCtrlGrpToSave.Any())
			{
				foreach (var ctrlGrpEntity in estMainComplete.EstAssembliesCtrlGrpToSave)
				{
					ctrlGrpLogic.Save(ctrlGrpEntity.EstAssembliesCtrlGrp);
				}
			}

			if (estMainComplete.EstAssembliesCtrlGrpToDelete != null && estMainComplete.EstAssembliesCtrlGrpToDelete.Any())
			{
				foreach (var ctrlGrpEntity in estMainComplete.EstAssembliesCtrlGrpToDelete)
				{
					ctrlGrpLogic.Delete(ctrlGrpEntity);
				}
			}
		}

		private void SaveLineItemCostGroup()
		{
			if (estMainComplete.CombinedLineItems == null)
			{
				/* CostGroup */
				if (estMainComplete.CostGroupToDelete != null && estMainComplete.CostGroupToDelete.Any())
				{
					new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").Delete(estMainComplete.CostGroupToDelete);
				}

				if (estMainComplete.CostGroupToSave != null && estMainComplete.CostGroupToSave.Any())
				{
					new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").Save(estMainComplete.CostGroupToSave);
				}
			}
		}

		private void SaveEstLineItemQuantity()
		{
			if (estMainComplete.EstLineItemQuantityToSave != null && estMainComplete.EstLineItemQuantityToSave.Any())
			{
				new EstimateMainLineItemQuantityLogic().SaveEntities(estMainComplete.EstLineItemQuantityToSave);
			}

			if (estMainComplete.EstLineItemQuantityToDelete != null && estMainComplete.EstLineItemQuantityToDelete.Any())
			{
				var estLineItemQuantityNotDeleted = new EstimateMainLineItemQuantityLogic().DeleteLineItemQuantityEntities(estMainComplete.EstLineItemQuantityToDelete, estMainComplete.ProjectId);

				if (estLineItemQuantityNotDeleted != null)
				{
					estMainComplete.EstLineItemQuantityNotDeleted = estLineItemQuantityNotDeleted;
				}
			}
		}

		private void SaveEstAssemblyWicItem()
		{
			var wicItemLogic = new EstAssemblyWicItemLogic();

			if (estMainComplete.EstAssemblyWicItemToSave != null && estMainComplete.EstAssemblyWicItemToSave.Any())
			{
				foreach (var wicitem in estMainComplete.EstAssemblyWicItemToSave)
				{
					if (wicitem.BoqItemFk.HasValue && wicitem.BoqItemFk > 0)
					{
						wicitem.EstAssemblyWicItem = wicItemLogic.Save(wicitem.EstAssemblyWicItem);
					}
				}
			}

			if (estMainComplete.EstAssemblyWicItemToDelete != null && estMainComplete.EstAssemblyWicItemToDelete.Any())
			{
				var entitiesToDelete = estMainComplete.EstAssemblyWicItemToDelete.Where(e => e.EstAssemblyWicItem != null && e.EstAssemblyWicItem.Version > 0).Select(e => e.EstAssemblyWicItem).ToList();

				if(entitiesToDelete.Any())
				{
					wicItemLogic.Delete(entitiesToDelete);
				}
			}
		}

		private void CalculatePrcItemAssignments()
		{
			if (estMainComplete.EstimateMainPrcItemAssignmentsToSave != null && estMainComplete.EstimateMainPrcItemAssignmentsToSave.Any())
			{
				var identificationData = estMainComplete.EstimateMainPrcItemAssignmentsToSave.Select(e => new IdentificationData() { Id = e.Id });

				var prcItemAssignmentEntitys = Injector.Get<IPrcItemAssignmentLogic>().GetByIdentificationData(identificationData);

				Injector.Get<IPrcItemAssignmentLogic>().SavePrcItemAssignments(estMainComplete.EstimateMainPrcItemAssignmentsToSave);

				estimateMainLineItemLogic.CalculatePackage(estMainComplete.EstimateMainPrcItemAssignmentsToSave, prcItemAssignmentEntitys);

				List<IPrcItemAssignmentEntity> prcItemAssignmentEntities = new List<IPrcItemAssignmentEntity>();

				if (prcItemAssignmentEntitys != null)
				{
					foreach (var item in estMainComplete.EstimateMainPrcItemAssignmentsToSave.OfType<IPrcItemAssignmentEntity>())
					{
						var entity = prcItemAssignmentEntitys.FirstOrDefault(e => e.Id == item.Id && (e.PrcPackageFk != item.PrcPackageFk || e.BoqItemFk != item.BoqItemFk || e.PrcItemFk != item.PrcItemFk));

						if (entity != null)
						{
							prcItemAssignmentEntities.Add(entity);
						}
					}
				}

				if (prcItemAssignmentEntities.Any())
				{
					estimateMainLineItemLogic.CalculatePackage(prcItemAssignmentEntitys);
				}
			}

			if (estMainComplete.EstimateMainPrcItemAssignmentsToDelete != null && estMainComplete.EstimateMainPrcItemAssignmentsToDelete.Any())
			{
				Injector.Get<IPrcItemAssignmentLogic>().DeletePrcItemAssignments(estMainComplete.EstimateMainPrcItemAssignmentsToDelete);

				estimateMainLineItemLogic.CalculatePackage(estMainComplete.EstimateMainPrcItemAssignmentsToDelete);
			}
		}

		private void SaveFormData()
		{
			var formEst = estMainComplete.EstLineItems != null ? estMainComplete.EstLineItems.Where(x => x.FormFk.HasValue).ToList() : new List<EstLineItemEntity>();

			foreach (var estLineItemEntity in formEst)
			{
				BusinessApplication.BusinessEnvironment.GetExportedValue<IUserFormDataLogic>().SaveRuleFormData(RubricConstant.EstimateLineItem, estLineItemEntity.Id, estLineItemEntity.FormFk.Value, estLineItemEntity.EstHeaderFk);
			}
		}

		private void SaveRiskRegisters()
		{
			if (estMainComplete.EstRiskRegistersToSave != null && estMainComplete.EstRiskRegistersToSave.Any())
			{
				var riskRegisterLogic = new EstRiskRegisterLogic();

				foreach (var riskRegisterToSave in estMainComplete.EstRiskRegistersToSave)
				{
					if (riskRegisterToSave.RiskRegisters != null && riskRegisterToSave.RiskRegisters.Any() && riskRegisterToSave.RiskRegisters.Any(e => e.Code != null))
					{
						riskRegisterLogic.Save(riskRegisterToSave.RiskRegisters, estMainComplete.EstHeaderId);
					}
					if (riskRegisterToSave.RiskResourceToSave != null && riskRegisterToSave.RiskResourceToSave.Any())
					{
						riskRegisterLogic.SaveRiskResource(riskRegisterToSave.RiskResourceToSave);
					}
					if (riskRegisterToSave.RiskResourceToDelete != null && riskRegisterToSave.RiskResourceToDelete.Any())
					{
						var resourceLogic = Injector.Get<IScriptBasRiskRegisterResourceLogic>();

						resourceLogic.Delete(riskRegisterToSave.RiskResourceToDelete);
					}
				}
			}

			if (estMainComplete.RiskRegistersToDelete != null && estMainComplete.RiskRegistersToDelete.Any())
			{
				new EstRiskRegisterLogic().Delete(estMainComplete.RiskRegistersToDelete.OrderByDescending(e => e.RiskRegisterParentFk).ToList());
			}
		}

		private void SaveEstLineItemSelStatements()
		{
			if (estMainComplete.EstLineItemSelStatements != null && estMainComplete.EstLineItemSelStatements.Any())
			{
				var selectionStatementLogic = new EstimateLineItemSelStatementLogic();

				var selectionStatementStateLogic = new EstimateLineItemSelStStateLogic();

				foreach (var estLineItemSelStatement in estMainComplete.EstLineItemSelStatements)
				{
					var selStatementEntity = selectionStatementLogic.Save(estLineItemSelStatement);

					var selStatementStateToSave = selectionStatementStateLogic.CreateOrUpdate(selStatementEntity, estMainComplete.EstHeaderId);

					if (selStatementStateToSave != null)
					{
						selectionStatementStateLogic.Save(selStatementStateToSave);
					}

					if (selStatementEntity.ChildrenIsExecute != null && selStatementEntity.ChildrenIsExecute.Any())
					{
						foreach (var selStatement in selStatementEntity.ChildrenIsExecute)
						{
							var childSelStatementToSave = selectionStatementStateLogic.CreateOrUpdate(selStatement, estMainComplete.EstHeaderId);

							if (childSelStatementToSave != null)
							{
								selectionStatementStateLogic.Save(childSelStatementToSave);
							}
						}
					}
				}
			}
		}

		private void SaveLineItem2MdlObject(int? estHeaderId, EstLineItemEntity currentEstLineItem)
		{
			var uomId = currentEstLineItem != null ? currentEstLineItem.BasUomFk : new EstimateMainLineItemLogic().GetListByFilter(e => e.Id == estMainComplete.MainItemId).Select(e => e.BasUomFk).FirstOrDefault();

			/* model object */
			if (estMainComplete.EstLineItem2MdlObjectToSave != null && estMainComplete.EstLineItem2MdlObjectToSave.Any())
			{
				var estLineItem2MdlObjectToSave = new EstLineItem2MdlObjectLogic().Save(estMainComplete.EstLineItem2MdlObjectToSave, true);
				//save Scheduling Model Objects
				SaveAndDeleteSchedulingModelObjects(estLineItem2MdlObjectToSave, false, uomId, estMainComplete);
			}
			else if (estMainComplete.EstLineItems != null && estMainComplete.EstLineItems.Any())
			{
				estMainComplete.EstLineItem2MdlObjectToSave = estimateMainLineItemLogic.UpdateEstLineItem2MdlObjects(estMainComplete.EstLineItems, estMainComplete.EstHeaderId);

				estMainComplete.IsReLoadMdlObject = estMainComplete.EstLineItem2MdlObjectToSave.Any();

				UpdateSchedulingModelObjects(estMainComplete, estHeaderId);
			}

			if (estMainComplete.EstLineItem2MdlObjectToDelete != null && estMainComplete.EstLineItem2MdlObjectToDelete.Any())
			{
				//delete Scheduling Model Objects
				SaveAndDeleteSchedulingModelObjects(estMainComplete.EstLineItem2MdlObjectToDelete, true, uomId, estMainComplete);

				new EstLineItem2ObjectQtyLogic().DeleteQuantities(estMainComplete.EstLineItem2MdlObjectToDelete);

				new EstLineItem2MdlObjectLogic().DeleteEntities(estMainComplete.EstLineItem2MdlObjectToDelete);
			}
		}

		private void UpdateSchedulingModelObjects(EstMainCompleteEntity estMainComplete, int? estHeaderId)
		{
			if (estMainComplete == null || estMainComplete.EstLineItems == null || estMainComplete.EstLineItem2MdlObjectToSave == null)
			{
				return;
			}

			var schedulingOjectModelSimulationLogic = RVPARB.BusinessEnvironment.GetExportedValue<ISchedulingOjectModelSimulationLogic>();

			foreach (var lineItem in estMainComplete.EstLineItems)
			{
				var lineItem2MdlObjectEntities = estMainComplete.EstLineItem2MdlObjectToSave.Where(e => e.EstLineItemFk == lineItem.Id && e.EstHeaderFk == lineItem.EstHeaderFk);

				if (lineItem.ActivityFk.HasValue)
				{
					var activityData = schedulingOjectModelSimulationLogic.GetListByFilter(e => e.EstLineItemFk == lineItem.Id && e.EstHeaderFk == estHeaderId);

					if (lineItem2MdlObjectEntities.Any() && activityData.IsNullOrEmpty())
					{
						//save model objects when assign  activityFk to Line item
						SaveAndDeleteSchedulingModelObjects(lineItem2MdlObjectEntities, false, lineItem.BasUomFk, estMainComplete);
					}
					else
					{
						if (lineItem2MdlObjectEntities.Any())
						{
							foreach (var lineItem2MdlObjectEntity in lineItem2MdlObjectEntities)
							{
								var estLineItem2ObjectQty = new EstLineItem2ObjectQtyLogic().GetCoresByFilter(e => e.EstLineItem2MdlObjectFk == lineItem2MdlObjectEntity.Id && e.EstHeaderFk == lineItem2MdlObjectEntity.EstHeaderFk);
								var plannedQuantity = estLineItem2ObjectQty.First().WqQuantityTarget;
								var quantity = estLineItem2ObjectQty.First().Quantity;
								var modelObjectData = schedulingOjectModelSimulationLogic.GetByFilter(e => e.EstLineItemFk == lineItem.Id && e.ObjectFk == lineItem2MdlObjectEntity.MdlObjectFk).ToList();
								if (modelObjectData.Any())
								{
									schedulingOjectModelSimulationLogic.UpdatePsdModelObjectsQuanity((Int32)lineItem.ActivityFk, lineItem2MdlObjectEntity.MdlObjectFk, plannedQuantity, lineItem2MdlObjectEntity.WqQuantityTarget.GetValueOrDefault(), quantity, lineItem.BasUomFk);
								}
							}

						}
					}
				}
				else if (!lineItem.ActivityFk.HasValue && lineItem2MdlObjectEntities.Any())
				{
					//delete model objects when remove activityFk to Line item
					SaveAndDeleteSchedulingModelObjects(lineItem2MdlObjectEntities, true, lineItem.BasUomFk, estMainComplete);
				}
			}
		}

		/// <summary>
		/// Save And Delete Scheduling Model Objects
		/// </summary>
		/// <param name="estLineItem2MdlObjectEntities"></param>
		/// <param name="isDelete"></param>
		/// <param name="uomId"></param>
		/// <param name="complete"></param>
		public void SaveAndDeleteSchedulingModelObjects(IEnumerable<EstLineItem2MdlObjectEntity> estLineItem2MdlObjectEntities, bool isDelete, int uomId, EstMainCompleteEntity complete)
		{
			var estLineItemIds = estLineItem2MdlObjectEntities.Select(e => e.EstLineItemFk).ToList();
			var estHeaderIds = estLineItem2MdlObjectEntities.Select(e => e.EstHeaderFk).ToList();
			var schedulingOjectModelSimulationLogic = RVPARB.BusinessEnvironment.GetExportedValue<ISchedulingOjectModelSimulationLogic>();
			var activityData = schedulingOjectModelSimulationLogic.GetListByFilter(e => estLineItemIds.Contains(e.EstLineItemFk) && estHeaderIds.Contains(e.EstHeaderFk));
			EstLineItemEntity lineItemData = null;
			var lineItemId = estLineItem2MdlObjectEntities.First().EstLineItemFk;
			var headerId = estLineItem2MdlObjectEntities.First().EstHeaderFk;

			if (complete.EstLineItems != null && complete.EstLineItems.Any())
			{
				lineItemData = complete.EstLineItems.FirstOrDefault(e => e.Id == lineItemId && e.EstHeaderFk == headerId);
			}

			if (lineItemData == null)
			{
				lineItemData = estimateMainLineItemLogic.GetLineItemByIdNHeader(lineItemId, headerId);
			}

			int activityId = 0;
			if (activityData.Any())
			{
				activityId = activityData.First(e => e.EstLineItemFk == lineItemId && e.EstHeaderFk == headerId).ActivityFk;
			}
			if (lineItemData != null && activityData.IsNullOrEmpty())
			{
				activityId = lineItemData.ActivityFk.GetValueOrDefault();
			}

			if (activityData.Any() || (lineItemData != null && activityId > 0))
			{
				foreach (var lineItem2MdlObjectEntity in estLineItem2MdlObjectEntities)
				{
					if (isDelete)
					{
						schedulingOjectModelSimulationLogic.DeleteByPsdModelObject(activityId, lineItem2MdlObjectEntity.MdlObjectFk);
					}
					else
					{
						//Update quantity for existing data in scheduling model objects
						var modelObjectData = schedulingOjectModelSimulationLogic.GetByFilter(e => e.EstLineItemFk == lineItem2MdlObjectEntity.EstLineItemFk && e.ObjectFk == lineItem2MdlObjectEntity.MdlObjectFk).ToList();

						var estLineItem2ObjectQty = new EstLineItem2ObjectQtyLogic().GetCoresByFilter(e => e.EstLineItem2MdlObjectFk == lineItem2MdlObjectEntity.Id && e.EstHeaderFk == lineItem2MdlObjectEntity.EstHeaderFk);

						if (estLineItem2ObjectQty != null && estLineItem2ObjectQty.Any())
						{
							var plannedQuantity = estLineItem2ObjectQty.First().WqQuantityTarget;

							var quantity = estLineItem2ObjectQty.First().Quantity;

							if (modelObjectData.Any())
							{
								schedulingOjectModelSimulationLogic.UpdatePsdModelObjectsQuanity(activityId, lineItem2MdlObjectEntity.MdlObjectFk, plannedQuantity, lineItem2MdlObjectEntity.WqQuantityTarget.GetValueOrDefault(), quantity, uomId);
							}
							else
							{
								//save data in scheduling model objects
								schedulingOjectModelSimulationLogic.SaveActivity2ModelObjectData(activityId, lineItem2MdlObjectEntity.Id, lineItem2MdlObjectEntity.EstHeaderFk, lineItem2MdlObjectEntity.EstLineItemFk, lineItem2MdlObjectEntity.MdlModelFk, lineItem2MdlObjectEntity.MdlObjectFk, lineItem2MdlObjectEntity.WqQuantityTarget.GetValueOrDefault(), lineItem2MdlObjectEntity.Quantity.GetValueOrDefault(), lineItem2MdlObjectEntity.Remark, uomId, plannedQuantity);
							}
						}

					}
				}
			}
		}

		/// <summary>
		/// Delete Characteristic Data Of Resources
		/// </summary>
		/// <param name="resources"></param>
		private static void DeleteCharacteristicDataOfResources(IEnumerable<EstResourceEntity> resources)
		{
			if (resources == null || !resources.Any())
			{
				return;
			}

			RVPARB.BusinessEnvironment.GetExportedValue<ICharacteristicDataLogic>().DeleteListBySectionIdAndObjectIds(33, resources.Select(e => e.Id).ToList());
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="estMainCompleteEntity"></param>
		/// <param name="refLineItems"></param>
		public static void ConcatRefLineItemsIntoCompleteEntity(EstMainCompleteEntity estMainCompleteEntity, IEnumerable<EstLineItemEntity> refLineItems)
		{
			if (refLineItems != null && refLineItems.Any())
			{
				if (estMainCompleteEntity.EstLineItems == null || !estMainCompleteEntity.EstLineItems.Any())
				{
					estMainCompleteEntity.EstLineItems = refLineItems;
				}
				else
				{
					//the refLineItems is the newest data from database
					List<int> refLineItemIds = refLineItems.Select(e => e.Id).ToList();

					var tempList = estMainCompleteEntity.EstLineItems.Where(e => !refLineItemIds.Contains(e.Id)).ToList();

					tempList.AddRange(refLineItems);

					estMainCompleteEntity.EstLineItems = tempList;
				}
			}
		}

		/// <summary>
		/// Returns the collaboration contexts for a given set of complete update entity.
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		protected IEnumerable<(string Area, string Context)> GetCollaborationContexts(IIdentifyable entity)
		{
			ArgumentNullException.ThrowIfNull(entity);
			var headerIds = entity switch
			{
				EstMainCompleteEntity ce => ce.EstLineItems.Select(li => li.EstHeaderFk),
				EstAssemblyCatEntity li => new[] { li.EstHeaderFk },
				_ => Enumerable.Empty<int>()
			};

			return headerIds
		.Distinct()
		.Select(headerId => ("estimate.assemblies", headerId.ToString(CultureInfo.InvariantCulture)));
		}
	}
}
