/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { SalesCommonMilestoneEntityInfoFactory } from '@libs/sales/common';
import { IOrdHeaderEntity, IOrdMilestoneEntity } from '@libs/sales/interfaces';
import { SalesContractMilestonesDataService } from '../../services/sales-contract-milestones-data.service';
import { SalesContractMilestonesBehavior } from '../../behaviors/sales-contract-milestone-behavior.service';
import { SalesContractContractsComplete } from '../complete-class/sales-contract-contracts-complete.class';

/**
 * Sales contract Milestones entity info
 */
export const SALES_CONTRACT_MILESTONES_ENTITY_INFO: EntityInfo = SalesCommonMilestoneEntityInfoFactory.create<IOrdMilestoneEntity, IOrdHeaderEntity, SalesContractContractsComplete>({
	containerUuid: '9488e003bb3942ff886b9514abb71534',
	dataServiceToken: SalesContractMilestonesDataService,
	behaviorToken: SalesContractMilestonesBehavior,
	dtoSchemeId: { moduleSubModule: 'Sales.Contract', typeName: 'OrdMilestoneDto' },
	permissionUuid: '2ad62fb20c0641f2a5051616bcdd0593',
});