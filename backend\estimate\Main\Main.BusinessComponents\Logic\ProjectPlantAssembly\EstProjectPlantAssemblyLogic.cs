using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using RIB.Visual.Basics.Characteristic.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Estimate.Assemblies.BusinessComponents;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Cloud.Common.BusinessComponents;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// Project Equipment Assembly container logic classes
	/// </summary>
	public class EstProjectPlantAssemblyLogic : RVPBizComp.LogicBase
	{
		List<EstResourceEntity> _resourcesToSave;
		List<EstLineItemEntity> _lineItemsToSave;
		int? _jobId = null;
		List<EstLineItemEntity> _prjPlantAssembliesToSave = new List<EstLineItemEntity>();
		ConcurrentDictionary<int, int> _old2NewJobIdMapping = new ConcurrentDictionary<int, int>();
		Dictionary<int, int> _oldAssemblyId2JobIdMapping = new Dictionary<int, int>();
		List<int> existPrjPlantAssemblyId = new List<int>();
		private Dictionary<int, int> _ResId2PlantAssemblyIdMapping = new Dictionary<int, int>();
		private bool _isDeepCopy = false;

		/// <summary>
		/// 
		/// </summary>
		/// <param name="resourcesToSave"></param>
		public EstProjectPlantAssemblyLogic(List<EstResourceEntity> resourcesToSave)
		{
			_resourcesToSave = resourcesToSave;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="lineItemsToSave"></param>
		/// <param name="resourcesToSave"></param>
		/// <param name="jobId"></param>
		public EstProjectPlantAssemblyLogic(List<EstLineItemEntity> lineItemsToSave, List<EstResourceEntity> resourcesToSave, int? jobId)
		{
			_lineItemsToSave = lineItemsToSave;
			_resourcesToSave = resourcesToSave;
			_jobId = jobId;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="old2NewJobIdMapping"></param>
		/// <param name="oldAssemblyId2JobIdMapping"></param>
		/// <param name="isDeepCopy"></param>
		public EstProjectPlantAssemblyLogic(ConcurrentDictionary<int, int> old2NewJobIdMapping, Dictionary<int, int> oldAssemblyId2JobIdMapping, bool isDeepCopy)
		{
			this._old2NewJobIdMapping = old2NewJobIdMapping;
			this._oldAssemblyId2JobIdMapping = oldAssemblyId2JobIdMapping;
			this._isDeepCopy = isDeepCopy;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="resId2AssemblyIdMapping"></param>
		public EstProjectPlantAssemblyLogic(Dictionary<int, int> resId2AssemblyIdMapping)
		{
			this._ResId2PlantAssemblyIdMapping = resId2AssemblyIdMapping;
		}

		/// <summary>
		/// 
		/// </summary>
		public EstProjectPlantAssemblyLogic()
		{
		}

		/// <summary>
		/// Provides access to the database model.
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel() => ModelBuilder.DbModel;

		/// <summary>
		/// 
		/// </summary>
		public IEnumerable<IProjectPlantAssemblyData> CreateProjectPlantAssemblyFromMaster(List<int> assemblyIds, int projectId, int assemblyHeaderId, bool isCalucatedLater = false, int? jobId = null, List<Tuple<int, int>> projectCostCodesToSave = null, List<Tuple<int, int, IScriptEstResource>> projectMaterialsToSave = null)
		{
			List<IProjectPlantAssemblyData> projectPlantAssemblyDatas = new List<IProjectPlantAssemblyData>();

			var prjPlantAssemblyHeader = Injector.Get<IProjectPlantAssemblyLogic>().GetByProject(projectId);

			if (prjPlantAssemblyHeader != null && prjPlantAssemblyHeader.EstHeaderFk == assemblyHeaderId)
			{
				return projectPlantAssemblyDatas;
			}

			var lineItemLogic = new EstimateMainLineItemLogic();

			var masterPlantAssemblies = new EstimateMainLineItemLogic().GetLineItemByIdsAndHeader(assemblyIds, assemblyHeaderId, (int)CommonLogic.LineItemTypes.PlantAssembly).OfType<EstLineItemEntity>().ToList();

			if (!masterPlantAssemblies.Any())
			{
				return projectPlantAssemblyDatas;
			}

			// creare project equipment assembly Header
			int prjAssemblyHeaderId = CreateAndSaveProjectPlantAssemblyHeader(projectId);

			// remove the exist master equipment assembly item at project equipment assemblies
			var existPrjPlantAssemblies = lineItemLogic.GetPrjPlantAssembliesByPrjId(-1, prjAssemblyHeaderId);
			_prjPlantAssembliesToSave = existPrjPlantAssemblies.ToList();

			var existPrjPlantAssemblyIds = existPrjPlantAssemblies.Where(e => e.EstAssemblyFk.HasValue && e.LgmJobFk == jobId).Select(i => i.EstAssemblyFk.Value).Distinct().ToList();
			var existPrjAssemblies2Id = existPrjPlantAssemblies.Where(e => e.EstAssemblyFk.HasValue && e.LgmJobFk == jobId).GroupBy(e => e.EstAssemblyFk.Value).ToDictionary(x => x.Key, x => x.FirstOrDefault());
			var newAtPrjAssembies2Id = existPrjPlantAssemblies.Where(e => !e.EstAssemblyFk.HasValue).ToDictionary(e => e.Id, e => e);
			existPrjAssemblies2Id.AddRange(newAtPrjAssembies2Id);

			masterPlantAssemblies = masterPlantAssemblies.Where(e => !existPrjPlantAssemblyIds.Contains(e.Id)).ToList();
			if (!masterPlantAssemblies.Any())
			{
				return projectPlantAssemblyDatas;
			}

			// collect udp to copy from assemlis and resource
			var UDPsToCopy = new ConcurrentBag<CopyRequest>();

			// create project equipment assembly
			var prjPlantAssemblies = !this._isDeepCopy ? CreateAndSavePrjPlantAssembies(masterPlantAssemblies, prjAssemblyHeaderId, UDPsToCopy) : CreateAndSavePrjPlantAssembiesByDeepCopy(masterPlantAssemblies, prjAssemblyHeaderId, UDPsToCopy);

			// collect from equipment assemblies
			projectPlantAssemblyDatas.AddRange(Injector.Get<IProjectPlantAssemblyLogic>().CreateProjectPlantAssemblyData(prjPlantAssemblies));

			// create project assembly resources
			if (prjPlantAssemblies.Any())
			{
				Dictionary<IdentificationData, IdentificationData> oldNewResIdMapping = new Dictionary<IdentificationData, IdentificationData>();

				_prjPlantAssembliesToSave.AddRange(prjPlantAssemblies);

				var prjPlantAssemblyResources = !this._isDeepCopy ? CreateAndSavePrjPlantAssemblyResouces(prjPlantAssemblies, UDPsToCopy, assemblyHeaderId, prjAssemblyHeaderId, existPrjAssemblies2Id, oldNewResIdMapping, projectId, jobId) : CreateAndSavePrjPlantAssemblyResoucesByDeepCopy(prjPlantAssemblies, UDPsToCopy, assemblyHeaderId, prjAssemblyHeaderId, existPrjAssemblies2Id, oldNewResIdMapping);

				using (var dbContext = CreateDbContext())
				{
					dbContext.Configuration.AutoDetectChangesEnabled = false;

					foreach (var prjPlantAssembly in prjPlantAssemblies)
					{
						prjPlantAssembly.LineItemType = (int)CommonLogic.LineItemTypes.PlantAssembly;

						// set the mapping job for project Plant Assembly
						if (!prjPlantAssembly.LgmJobFk.HasValue && this._oldAssemblyId2JobIdMapping.Any() && prjPlantAssembly.EstAssemblyFk.HasValue)
						{
							int plantAssemblyOldId = prjPlantAssembly.EstAssemblyFk.Value;
							if (this._oldAssemblyId2JobIdMapping.ContainsKey(plantAssemblyOldId))
							{
								prjPlantAssembly.LgmJobFk = this._oldAssemblyId2JobIdMapping[plantAssemblyOldId];
							}
						}

						// if no job for plant assembly, will set default job
						prjPlantAssembly.LgmJobFk = prjPlantAssembly.LgmJobFk.HasValue ? prjPlantAssembly.LgmJobFk : jobId;

						dbContext.Entities<EstLineItemEntity>().Add(prjPlantAssembly);
					}

					foreach (var prjPlantAssemblyResource in prjPlantAssemblyResources)
					{
						dbContext.Entities<EstResourceEntity>().Add(prjPlantAssemblyResource);
					}

					dbContext.SaveChanges();
				}

				var characterLogic = new CharacteristicDataLogic();
				// create project equipment assembly characters
				var prjAssemblyCharacters = CreatePrjPlantAssemblyCharacter(prjPlantAssemblies);
				characterLogic.BatchSave(prjAssemblyCharacters, true);

				// create project assembly resource characters
				var prjAssemblyResourceCharacters = CreatePrjPlantAssemblyResourceCharacter(oldNewResIdMapping);
				characterLogic.BatchSave(prjAssemblyResourceCharacters, true);

				// collect project cost code and material
				if (prjPlantAssemblyResources.Any() && jobId.HasValue)
				{
					CollectProjectEntityToSave(prjPlantAssemblies, prjPlantAssemblyResources, jobId, projectCostCodesToSave, projectMaterialsToSave);
				}
			}

			// create and save udp from assemblies and resources
			new UserDefinedColumnValueLogic().Copy(UDPsToCopy, true);

			// update project assemblies for project cost, project material... calculate...
			var assemblyNewIds = projectPlantAssemblyDatas.Where(e => e.PlantAssemblyNewId.HasValue).Select(e => e.PlantAssemblyNewId.Value).ToList();

			if (!isCalucatedLater && assemblyNewIds.Count > 0)
			{
				assemblyNewIds.AddRange(existPrjPlantAssemblyId);
				Injector.Get<IEstimateMainResourceLogic>().UpdatePrjAssemblyResources(projectId, assemblyNewIds, prjAssemblyHeaderId, true);
			}

			foreach (var projectAssemblyData in projectPlantAssemblyDatas)
			{
				projectAssemblyData.PrjPlantAssemblyHeaderId = prjAssemblyHeaderId;
			}

			return projectPlantAssemblyDatas;
		}

		/// <summary>
		/// create Project plant assembly and copy the complete info: characteristic for assembly and resouce, rule and param, cost group
		/// </summary>
		/// <returns></returns>
		public IEnumerable<IProjectPlantAssemblyData> CreateProjectPlantAssemblyAndCopyCompleteInfo(List<int> assemblyIds, int projectId, int assemblyHeaderId, bool isCalucatedLater = false, int? jobId = null, List<Tuple<int, int>> projectCostCodesToSave = null, List<Tuple<int, int, IScriptEstResource>> projectMaterialsToSave = null, List<int> prjPlantAssemblyIdsToUpdate = null)
		{
			projectCostCodesToSave ??= new List<Tuple<int, int>>();
			projectMaterialsToSave ??= new List<Tuple<int, int, IScriptEstResource>>();

			// create project assembly
			var projPlantAssemblyDatas = CreateProjectPlantAssemblyFromMaster(assemblyIds, projectId, assemblyHeaderId, isCalucatedLater, jobId, projectCostCodesToSave, projectMaterialsToSave);

			if (projPlantAssemblyDatas.Any())
			{
				var prjAssemblyHeaderId = projPlantAssemblyDatas.FirstOrDefault().PrjPlantAssemblyHeaderId;
				List<EstResourceEntity> newResources = new List<EstResourceEntity>();
				EstimateMainResourceLogic estimateMainResourceLogic = new EstimateMainResourceLogic();
				var newIds = projPlantAssemblyDatas.Where(e => e.PlantAssemblyNewId.HasValue).Select(e => e.PlantAssemblyNewId.Value).ToList();
				var oldIds = projPlantAssemblyDatas.Where(e => e.PlantAssemblyOldId.HasValue).Select(e => e.PlantAssemblyOldId.Value).ToList();

				if (_lineItemsToSave != null && _lineItemsToSave.Any())
				{
					foreach (var lineItemToSave in _lineItemsToSave)
					{
						if (lineItemToSave.EstHeaderAssemblyFk.HasValue && lineItemToSave.EstAssemblyFk.HasValue)
						{
							var matchItem = _prjPlantAssembliesToSave.FirstOrDefault(e => e.EstHeaderAssemblyFk == lineItemToSave.EstHeaderAssemblyFk && e.EstAssemblyFk == lineItemToSave.EstAssemblyFk);
							if (matchItem != null)
							{
								lineItemToSave.EstHeaderAssemblyFk = matchItem.EstHeaderFk;
								lineItemToSave.EstAssemblyFk = matchItem.Id;
								lineItemToSave.EstAssemblyCatFk = matchItem.EstAssemblyCatFk;
							}
						}
					}
				}

				// set the project assembly back to resource, after save project assembly
				if (_resourcesToSave != null && _resourcesToSave.Any())
				{
					// new EstimateMainResourceLogic().SetPrjAssemblyToResource(projectId, _resourcesToSave);
					foreach (var resourceToSave in _resourcesToSave)
					{
						if (resourceToSave.EstHeaderAssemblyFk.HasValue && resourceToSave.EstAssemblyFk.HasValue)
						{
							var matchItems = _prjPlantAssembliesToSave.Where(e => e.EstHeaderAssemblyFk == resourceToSave.EstHeaderAssemblyFk && e.EstAssemblyFk == resourceToSave.EstAssemblyFk);
							var matchItem = matchItems.Any(x => x.LgmJobFk == jobId) ? matchItems.FirstOrDefault(x => x.LgmJobFk == jobId) : matchItems.FirstOrDefault();
							if (matchItem != null && !(resourceToSave.EstLineItemFk == matchItem.Id && resourceToSave.EstHeaderFk == matchItem.EstHeaderFk))
							{
								resourceToSave.EstHeaderAssemblyFk = matchItem.EstHeaderFk;
								resourceToSave.EstAssemblyFk = matchItem.Id;
							}
						}
					}
				}
			}

			if (prjPlantAssemblyIdsToUpdate != null && projPlantAssemblyDatas.Any())
			{
				var assemblyNewIds = projPlantAssemblyDatas.Where(e => e.PlantAssemblyNewId.HasValue).Select(e => e.PlantAssemblyNewId.Value);
				prjPlantAssemblyIdsToUpdate.AddRange(assemblyNewIds);
				prjPlantAssemblyIdsToUpdate.AddRange(existPrjPlantAssemblyId);
			}

			return projPlantAssemblyDatas;
		}

		/// <summary>
		/// creare project assembly Header
		/// </summary>
		private int CreateAndSaveProjectPlantAssemblyHeader(int projectId)
		{
			var prjPlantAssembly = Injector.Get<IProjectPlantAssemblyLogic>().GetByProject(projectId);
			if (prjPlantAssembly == null)
			{
				prjPlantAssembly = Injector.Get<IProjectPlantAssemblyLogic>().CreateAndSave(projectId);
			}

			var prjAssemblyHeaderId = prjPlantAssembly.EstHeaderFk;
			return prjAssemblyHeaderId;
		}


		/// <summary>
		/// 
		/// </summary>
		private List<EstLineItemEntity> CreateAndSavePrjPlantAssembies(List<EstLineItemEntity> assemblies, int prjAssemblyHeaderId, ConcurrentBag<CopyRequest> UDPsToCopy)
		{
			var prjPlantAssemblies = new List<EstLineItemEntity>();

			// copy the master assemblies to project assemblies
			foreach (var masterPlantAssembly in assemblies)
			{
				var creationData = new EstLineItemCreationData() { EstHeaderFk = prjAssemblyHeaderId };
				var prjPlantAssemblyEntity = new EstimateMainLineItemLogic().Create(creationData, false);
				var lineItemLogic = new EstimateMainLineItemLogic();

				prjPlantAssemblyEntity.Code = masterPlantAssembly.Code;
				prjPlantAssemblyEntity.EstAssemblyFk = masterPlantAssembly.Id;
				prjPlantAssemblyEntity.EstHeaderAssemblyFk = masterPlantAssembly.EstHeaderFk;

				// reset description
				if (prjPlantAssemblyEntity.DescriptionInfo != null)
				{
					if (_resourcesToSave != null && _resourcesToSave.Any())
					{
						var matchResource = _resourcesToSave.FirstOrDefault(e => e.EstResourceTypeFk == (int)EstResourceType.EquipmentAssembly && e.EstHeaderAssemblyFk == prjPlantAssemblyEntity.EstHeaderAssemblyFk && e.EstAssemblyFk == prjPlantAssemblyEntity.EstAssemblyFk);
						if (matchResource != null)
						{
							prjPlantAssemblyEntity.DescriptionInfo = masterPlantAssembly.CopyTranslate<EstLineItemEntity>(this.UserLanguageId, new Func<EstLineItemEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo }).DescriptionInfo;
							matchResource.DescriptionInfo = prjPlantAssemblyEntity.DescriptionInfo;
						}

					}
					else
					{
						lineItemLogic.CopyLineItemTranslation(prjPlantAssemblyEntity);
					}
				}

				if (_old2NewJobIdMapping.Any())
				{
					if (prjPlantAssemblyEntity.LgmJobFk.HasValue && _old2NewJobIdMapping.ContainsKey(prjPlantAssemblyEntity.LgmJobFk.Value))
					{
						prjPlantAssemblyEntity.LgmJobFk = _old2NewJobIdMapping[prjPlantAssemblyEntity.LgmJobFk.Value];
					}
				}
				else
				{
					prjPlantAssemblyEntity.LgmJobFk = _jobId;
				}

				// collect upd from assemblies
				UDPsToCopy.Add(new CopyRequest
				{
					SourceTableId = (int)userDefinedColumnTableIds.EstimateLineItem,
					SourcePk1 = prjPlantAssemblyEntity.EstHeaderAssemblyFk ?? 0,
					SourcePk2 = prjPlantAssemblyEntity.EstAssemblyFk ?? 0,
					Pk1 = prjPlantAssemblyEntity.EstHeaderFk,
					Pk2 = prjPlantAssemblyEntity.Id,
					TableId = (int)userDefinedColumnTableIds.EstimateLineItem
				});

				prjPlantAssemblies.Add(prjPlantAssemblyEntity);
			}

			return prjPlantAssemblies;
		}

		/// <summary>
		/// for deep copy
		/// </summary>
		private List<EstLineItemEntity> CreateAndSavePrjPlantAssembiesByDeepCopy(List<EstLineItemEntity> assemblies, int prjPlantAssemblyHeaderId, ConcurrentBag<CopyRequest> UDPsToCopy)
		{
			var prjPlantAssemblies = assemblies.Select(e => e.Clone() as EstLineItemEntity).ToList();

			var ids = new EstimateMainLineItemLogic().SequenceManager.GetNextList("EST_LINE_ITEM", prjPlantAssemblies.Count);

			// copy the master assemblies to project assemblies
			var currentUserId = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
			for (int i = 0; i < ids.Count; i++)
			{
				var prjPlantAssemblyEntity = prjPlantAssemblies[i];

				prjPlantAssemblyEntity.EstAssemblyFk = prjPlantAssemblyEntity.Id;
				prjPlantAssemblyEntity.EstHeaderAssemblyFk = prjPlantAssemblyEntity.EstHeaderFk;
				prjPlantAssemblyEntity.EstHeaderFk = prjPlantAssemblyHeaderId;
				prjPlantAssemblyEntity.Id = ids[i];

				// reset the mapping job
				if (_old2NewJobIdMapping.Any())
				{
					if (prjPlantAssemblyEntity.LgmJobFk.HasValue && _old2NewJobIdMapping.ContainsKey(prjPlantAssemblyEntity.LgmJobFk.Value))
					{
						prjPlantAssemblyEntity.LgmJobFk = _old2NewJobIdMapping[prjPlantAssemblyEntity.LgmJobFk.Value];
					}
				}
				else
				{
					prjPlantAssemblyEntity.LgmJobFk = _jobId;
				}

				// update info
				prjPlantAssemblyEntity.UpdatedAt = null;
				prjPlantAssemblyEntity.UpdatedBy = null;
				prjPlantAssemblyEntity.InsertedBy = currentUserId;
				prjPlantAssemblyEntity.InsertedAt = DateTime.UtcNow;
				prjPlantAssemblyEntity.Version = 0;

				// collect upd from assemblies
				UDPsToCopy.Add(new CopyRequest
				{
					SourceTableId = (int)userDefinedColumnTableIds.EstimateLineItem,
					SourcePk1 = prjPlantAssemblyEntity.EstHeaderAssemblyFk ?? 0,
					SourcePk2 = prjPlantAssemblyEntity.EstAssemblyFk ?? 0,
					Pk1 = prjPlantAssemblyEntity.EstHeaderFk,
					Pk2 = prjPlantAssemblyEntity.Id,
					TableId = (int)userDefinedColumnTableIds.EstimateLineItem
				});
			}

			return prjPlantAssemblies;
		}

		/// <summary>
		/// 
		/// </summary>
		private List<EstResourceEntity> CreateAndSavePrjPlantAssemblyResouces(List<EstLineItemEntity> prjPlantAssemblies, ConcurrentBag<CopyRequest> UDPsToCopy, int assemblyHeaderId, int prjPlantAssemblyHeaderId, Dictionary<int, EstLineItemEntity> existPrjPlantAssemblies2Id, Dictionary<IdentificationData, IdentificationData> oldNewResIdMapping, int projectId, int? jobId = null)
		{
			List<EstResourceEntity> targetResources = new List<EstResourceEntity>();

			foreach (var prjPlantAssembly in prjPlantAssemblies)
			{
				var resourcesLookupItemsPostData = new ResourcesLookupItemsPostData() { HeaderFk = prjPlantAssemblyHeaderId, ItemIds = new List<int?> { prjPlantAssembly.EstAssemblyFk }, MainItemId = prjPlantAssembly.Id, ProjectId = projectId, ResourceType = 3, SectionId = 45, IsCreateForProjectAssembly = true, JobId = jobId ?? 0 };
				var estimateMainResourceLogic = new EstimateMainResourceLogic();
				var resourcesCopy = estimateMainResourceLogic.GetAssemblyResourcesToLineItem(resourcesLookupItemsPostData).ToList();

				targetResources.AddRange(resourcesCopy);
			}

			// get master assembly resources
			var estLineItemIds = prjPlantAssemblies.Select(i => i.EstAssemblyFk.Value).ToList();

			var sourceResouces = new EstimateMainResourceLogic().GetListByFilter(e => e.EstHeaderFk == assemblyHeaderId && estLineItemIds.Contains(e.EstLineItemFk)).ToList();

			var resourcesGroup = targetResources.Where(e => e.EstResourceFk.HasValue).GroupBy(i => i.EstResourceFk.Value).ToDictionary(x => x.Key, y => y.ToList());

			var prjPlantAssemblies2Id = prjPlantAssemblies.ToDictionary(x => x.EstAssemblyFk.Value, x => x);

			var currentUserId = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.UserId;

			foreach (var targetResource in targetResources)
			{
				// source fks to copy the udps
				int sourceId = targetResource.Id;
				int sourceEstLineItemId = targetResource.EstLineItemFk;

				// Reset TargetResource some Propeties
				ResetTargetResourcePropeties(targetResource, sourceId, prjPlantAssemblyHeaderId, prjPlantAssemblies2Id, existPrjPlantAssemblies2Id, resourcesGroup);

				// assign project plant assembly description tr to plant assembly resource
				var assemblyMap = prjPlantAssemblies.Where(pa => pa.EstAssemblyFk.HasValue && pa.EstHeaderAssemblyFk.HasValue).ToDictionary(pa => (pa.EstHeaderAssemblyFk.Value, pa.EstAssemblyFk.Value), pa => pa.DescriptionInfo);

				// Local function to assign description info
				void AssignPlantAssemblyDescription(IEnumerable<IScriptEstResource> resources)
				{
					foreach (var resource in resources)
					{
						if (resource.EstAssemblyFk.HasValue && assemblyMap.TryGetValue((resource.EstHeaderAssemblyFk.Value, resource.EstAssemblyFk.Value), out var descInfo))
						{
							resource.DescriptionInfo = descInfo;
						}
					}
				}

				// Apply to both collections
				AssignPlantAssemblyDescription(targetResource.ResourceChildren.Where(e => e.EstResourceTypeFk == (int)EstResourceType.EquipmentAssembly));

				// collect upd from assembly resources
				UDPsToCopy.Add(new CopyRequest
				{
					SourceTableId = (int)userDefinedColumnTableIds.EstimateResource,
					SourcePk1 = assemblyHeaderId,
					SourcePk2 = sourceEstLineItemId,
					SourcePk3 = sourceId,
					Pk1 = prjPlantAssemblyHeaderId,
					Pk2 = targetResource.EstLineItemFk,
					Pk3 = targetResource.Id,
					TableId = (int)userDefinedColumnTableIds.EstimateResource
				});

				var sourceIdentity = new IdentificationData() { Id = sourceId, PKey1 = assemblyHeaderId, PKey2 = sourceEstLineItemId };

				if (!oldNewResIdMapping.ContainsKey(sourceIdentity))
				{
					oldNewResIdMapping.Add(sourceIdentity, new IdentificationData() { Id = targetResource.Id, PKey1 = prjPlantAssemblyHeaderId, PKey2 = targetResource.EstLineItemFk });
				}
			}

			return targetResources;
		}

		/// <summary>
		/// 
		/// </summary>
		private void ResetTargetResourcePropeties(EstResourceEntity targetResource, int sourceId, int prjPlantAssemblyHeaderId, Dictionary<int, EstLineItemEntity> prjPlantAssemblies2Id, Dictionary<int, EstLineItemEntity> existPrjPlantAssemblies2Id, Dictionary<int, List<EstResourceEntity>> resourcesGroup)
		{
			// project plant assembly resource value assignmetn
			targetResource.EstHeaderFk = prjPlantAssemblyHeaderId;

			// set the master plant assembly resource as project plant assembly resource
			if (prjPlantAssemblies2Id.ContainsKey(targetResource.EstLineItemFk))
			{
				targetResource.EstLineItemFk = prjPlantAssemblies2Id[targetResource.EstLineItemFk].Id;
			}

			// set the assembly type resource as the project plant assembly one
			if (targetResource.EstAssemblyFk.HasValue)
			{
				if (prjPlantAssemblies2Id.ContainsKey(targetResource.EstAssemblyFk.Value))
				{
					targetResource.EstHeaderAssemblyFk = prjPlantAssemblies2Id[targetResource.EstAssemblyFk.Value].EstHeaderFk;
					targetResource.EstAssemblyFk = prjPlantAssemblies2Id[targetResource.EstAssemblyFk.Value].Id;
				}
				else if (existPrjPlantAssemblies2Id.ContainsKey(targetResource.EstAssemblyFk.Value))
				{
					targetResource.EstHeaderAssemblyFk = existPrjPlantAssemblies2Id[targetResource.EstAssemblyFk.Value].EstHeaderFk;
					targetResource.EstAssemblyFk = existPrjPlantAssemblies2Id[targetResource.EstAssemblyFk.Value].Id;

					existPrjPlantAssemblyId.Add(targetResource.EstAssemblyFk.Value);
				}
				else if (_ResId2PlantAssemblyIdMapping.ContainsKey(sourceId) && existPrjPlantAssemblies2Id.ContainsKey(_ResId2PlantAssemblyIdMapping[sourceId]))
				{
					// for only create at project plant assembly, has no related with master plant assembly
					targetResource.EstHeaderAssemblyFk = existPrjPlantAssemblies2Id[_ResId2PlantAssemblyIdMapping[sourceId]].EstHeaderFk;
					targetResource.EstAssemblyFk = existPrjPlantAssemblies2Id[_ResId2PlantAssemblyIdMapping[sourceId]].Id;

					existPrjPlantAssemblyId.Add(targetResource.EstAssemblyFk.Value);
				}
			}

			// reset the parent key as the new Id
			if (resourcesGroup.ContainsKey(sourceId))
			{
				foreach (var res in resourcesGroup[sourceId])
				{
					res.EstResourceFk = targetResource.Id;
				}
			}
		}

		/// <summary>
		/// deep copy project plant assembly
		/// </summary>
		private List<EstResourceEntity> CreateAndSavePrjPlantAssemblyResoucesByDeepCopy(List<EstLineItemEntity> prjPlantAssemblies, ConcurrentBag<CopyRequest> UDPsToCopy, int assemblyHeaderId, int prjPlantAssemblyHeaderId, Dictionary<int, EstLineItemEntity> existPrjPlantAssemblies2Id, Dictionary<IdentificationData, IdentificationData> oldNewResIdMapping)
		{
			List<EstResourceEntity> targetResources = new List<EstResourceEntity>();

			// get master assembly resources
			var estLineItemIds = prjPlantAssemblies.Select(i => i.EstAssemblyFk.Value).ToList();

			var sourceResouces = new EstimateMainResourceLogic().GetListByFilter(e => e.EstHeaderFk == assemblyHeaderId && estLineItemIds.Contains(e.EstLineItemFk)).ToList();

			if (sourceResouces.Any())
			{
				// Clone new items
				targetResources = sourceResouces.Select(e => e.Clone() as EstResourceEntity).ToList();

				var resourcesGroup = targetResources.Where(e => e.EstResourceFk.HasValue).GroupBy(i => i.EstResourceFk.Value).ToDictionary(x => x.Key, y => y.ToList());

				var ids = new EstimateMainResourceLogic().SequenceManager.GetNextList("EST_RESOURCE", targetResources.Count);

				var prjPlantAssemblies2Id = prjPlantAssemblies.ToDictionary(x => x.EstAssemblyFk.Value, x => x);

				var currentUserId = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.UserId;

				for (int i = 0; i < ids.Count; i++)
				{
					var targetResource = targetResources[i];

					// source fks to copy the udps
					int sourceId = targetResource.Id;
					int sourceEstLineItemId = targetResource.EstLineItemFk;

					targetResource.Id = ids[i];

					// Reset TargetResource some Propeties
					ResetTargetResourcePropeties(targetResource, sourceId, prjPlantAssemblyHeaderId, prjPlantAssemblies2Id, existPrjPlantAssemblies2Id, resourcesGroup);

					// update info
					targetResource.UpdatedAt = null;
					targetResource.UpdatedBy = null;
					targetResource.InsertedBy = currentUserId;
					targetResource.InsertedAt = DateTime.UtcNow;
					targetResource.Version = 0;

					// reset the parent key as the new Id
					if (resourcesGroup.ContainsKey(sourceId))
					{
						foreach (var res in resourcesGroup[sourceId])
						{
							res.EstResourceFk = targetResource.Id;
						}
					}

					// collect upd from assembly resources
					UDPsToCopy.Add(new CopyRequest
					{
						SourceTableId = (int)userDefinedColumnTableIds.EstimateResource,
						SourcePk1 = assemblyHeaderId,
						SourcePk2 = sourceEstLineItemId,
						SourcePk3 = sourceId,
						Pk1 = prjPlantAssemblyHeaderId,
						Pk2 = targetResource.EstLineItemFk,
						Pk3 = targetResource.Id,
						TableId = (int)userDefinedColumnTableIds.EstimateResource
					});

					var sourceIdentity = new IdentificationData() { Id = sourceId, PKey1 = assemblyHeaderId, PKey2 = sourceEstLineItemId };

					if (!oldNewResIdMapping.ContainsKey(sourceIdentity))
					{
						oldNewResIdMapping.Add(sourceIdentity, new IdentificationData() { Id = targetResource.Id, PKey1 = prjPlantAssemblyHeaderId, PKey2 = targetResource.EstLineItemFk });
					}
				}
			}

			return targetResources;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="prjPlantAssemblies"></param>
		/// <returns></returns>
		private List<CharacteristicDataEntity> CreatePrjPlantAssemblyCharacter(List<EstLineItemEntity> prjPlantAssemblies)
		{
			List<CharacteristicDataEntity> targetCharacters = new List<CharacteristicDataEntity>();

			var charcterLogic = new CharacteristicDataLogic();

			// get master assembly character
			var estLineItemIds = prjPlantAssemblies.Select(i => new IdentificationData() { Id = i.EstAssemblyFk.Value, PKey1 = i.EstHeaderAssemblyFk }).Distinct();

			var sourceAssemblyCharacters = new List<CharacteristicDataEntity>();

			sourceAssemblyCharacters.AddRange(new CharacteristicDataLogic().GetListBySectionIdAndObjectIds(29, estLineItemIds).OfType<CharacteristicDataEntity>().ToList());

			sourceAssemblyCharacters.AddRange(new CharacteristicDataLogic().GetListBySectionIdAndObjectIds(30, estLineItemIds).OfType<CharacteristicDataEntity>().ToList());

			if (sourceAssemblyCharacters.Any())
			{
				// Clone new items
				targetCharacters = sourceAssemblyCharacters.Select(e => e.Clone() as CharacteristicDataEntity).ToList();

				var prjAssemblies2Id = prjPlantAssemblies.ToDictionary(x => new IdentificationData() { Id = x.EstAssemblyFk.Value, PKey1 = x.EstHeaderAssemblyFk }, x => x.IdentificationData);

				var ids = charcterLogic.SequenceManager.GetNextList("BAS_CHARACTERISTIC_DATA", targetCharacters.Count);

				for (int i = 0; i < ids.Count; i++)
				{
					targetCharacters[i].Id = ids[i];

					if (prjAssemblies2Id.ContainsKey(targetCharacters[i].CompleteObjectFk))
					{
						targetCharacters[i].CompleteObjectFk = prjAssemblies2Id[targetCharacters[i].CompleteObjectFk];
					}

					targetCharacters[i].Version = 0;
				}
			}

			return targetCharacters;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="oldNewResIdMapping"></param>
		/// <returns></returns>
		private List<CharacteristicDataEntity> CreatePrjPlantAssemblyResourceCharacter(Dictionary<IdentificationData, IdentificationData> oldNewResIdMapping)
		{
			List<CharacteristicDataEntity> targetCharacters = new List<CharacteristicDataEntity>();

			if (oldNewResIdMapping.Count <= 0)
			{
				return targetCharacters;
			}

			// get master assembly resource character
			var sourceAssemblyCharacters = new CharacteristicDataLogic().GetList(45, oldNewResIdMapping.Keys);

			if (sourceAssemblyCharacters.Any())
			{
				// Clone new items
				targetCharacters = sourceAssemblyCharacters.Select(e => e.Clone() as CharacteristicDataEntity).ToList();

				var ids = new CharacteristicDataLogic().SequenceManager.GetNextList("BAS_CHARACTERISTIC_DATA", targetCharacters.Count);

				for (int i = 0; i < ids.Count; i++)
				{
					targetCharacters[i].Id = ids[i];

					if (oldNewResIdMapping.ContainsKey(targetCharacters[i].CompleteObjectFk))
					{
						targetCharacters[i].CompleteObjectFk = oldNewResIdMapping[targetCharacters[i].CompleteObjectFk];
					}

					targetCharacters[i].Version = 0;
				}
			}

			return targetCharacters;
		}

		/// <summary>
		/// 
		/// </summary>
		private void CollectProjectEntityToSave(IEnumerable<EstLineItemEntity> prjPlantAssemblies, IEnumerable<EstResourceEntity> resourcesToSave, int? jobId, List<Tuple<int, int>> projectCostCodesToSave = null, List<Tuple<int, int, IScriptEstResource>> projectMaterialsToSave = null)
		{
			List<int> materialIds = new List<int>();
			if (prjPlantAssemblies != null)
			{
				foreach (var prjPlantAssembly in prjPlantAssemblies)
				{
					if (projectCostCodesToSave != null && prjPlantAssembly.MdcCostCodeFk.HasValue)
					{
						if (projectCostCodesToSave.FirstOrDefault(e => e.Item1 == prjPlantAssembly.MdcCostCodeFk.Value) == null)
						{
							projectCostCodesToSave.Add(new Tuple<int, int>(prjPlantAssembly.MdcCostCodeFk.Value, jobId.Value));
						}
					}

					if (projectMaterialsToSave != null && prjPlantAssembly.MdcMaterialFk.HasValue)
					{
						if (projectMaterialsToSave.FirstOrDefault(e => e.Item1 == prjPlantAssembly.MdcMaterialFk.Value) == null)
						{
							projectMaterialsToSave.Add(new Tuple<int, int, IScriptEstResource>(prjPlantAssembly.MdcMaterialFk.Value, jobId.Value, null));
							materialIds.Add(prjPlantAssembly.MdcMaterialFk.Value);
						}
					}
				}
			}

			if (resourcesToSave != null)
			{
				foreach (var item in resourcesToSave)
				{
					if (projectCostCodesToSave != null && item.EstResourceTypeFk == (int)EstResourceType.CostCode && item.MdcCostCodeFk.HasValue)
					{
						if (projectCostCodesToSave.FirstOrDefault(e => e.Item1 == item.MdcCostCodeFk.Value) == null)
						{
							projectCostCodesToSave.Add(new Tuple<int, int>(item.MdcCostCodeFk.Value, jobId.Value));
						}
					}

					if (projectMaterialsToSave != null && item.EstResourceTypeFk == (int)EstResourceType.Material && item.MdcMaterialFk.HasValue)
					{
						if (projectMaterialsToSave.FirstOrDefault(e => e.Item1 == item.MdcMaterialFk.Value) == null)
						{
							projectMaterialsToSave.Add(new Tuple<int, int, IScriptEstResource>(item.MdcMaterialFk.Value, jobId.Value, item));
							materialIds.Add(item.MdcMaterialFk.Value);
						}
					}
				}
			}

			if (materialIds.Count > 0)
			{
				var materials = new MaterialLogic().GetListByIds(materialIds.Distinct());
				foreach (var material in materials)
				{
					if (projectCostCodesToSave.FirstOrDefault(e => e.Item1 == material.MdcCostCodeFk) == null)
					{
						projectCostCodesToSave.Add(new Tuple<int, int>(material.MdcCostCodeFk, jobId.Value));
					}
				}
			}
		}
	}
}
