using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostCodes.BusinessComponents;
using RIB.Visual.Platform.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Estimate.Common.BusinessComponents;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using System.ComponentModel.Composition;
using RIB.Visual.Basics.Unit.BusinessComponents;
using RIB.Visual.Estimate.Assemblies.BusinessComponents;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Estimate.Main.Core;
using RIB.Visual.Basics.Characteristic.Common;
using RIB.Visual.Estimate.Parameter.BusinessComponents;
using LocalizationProps = RIB.Visual.Estimate.Main.Localization.Properties;
using MathNet.Numerics.Statistics.Mcmc;
using RIB.Visual.Basics.Characteristic.BusinessComponents;
using RIB.Visual.Basics.Common.Core;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	///
	/// </summary>
	[Export(typeof(IEstimateResourceFactory))]
	public class EstimateResourceFactory : ProjectScopeService, IEstimateResourceFactory
	{
		private readonly List<Tuple<string, string, MaterialEntity>> _MaterialTupleList = new List<Tuple<string, string, MaterialEntity>>();

		private readonly List<Tuple<string, string, string, IScriptEstAssembly>> _AssemblyTupleList = new List<Tuple<string, string, string, IScriptEstAssembly>>();

		private readonly EstimateMainResourceLogic _EstimateMainResourceLogic = new EstimateMainResourceLogic();

		private readonly EstResourceUpdateHelper _EstResourceUpdateHelper;

		private readonly EstLineItemUpdateFrmPrjLogic _EstLineItemUpdateFrmPrjLogic;

		private readonly EstResourceCopyLogic _EstResourceCopyLogic;

		/// <summary>
		/// 
		/// </summary>
		public readonly EstCharacteristicLogic _EstCharacteristicLogic;

		private bool _IsPlantAssembly;

		private bool _IsResolveAssembly;

		private bool _IsSetProjectCurrency;

		private bool _IsResolvePlantAssembly;

		private readonly EstJobHelper _EstJobHelper;

		/// <summary>
		/// Use for MEF
		/// </summary>
		public EstimateResourceFactory() : base(0)
		{

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="isResolveAssembly"></param>
		/// <param name="isSetProjectCurrency"></param>
		/// <param name="resourceUpdateOption"></param>
		/// <param name="isResolvePlantAssembly"></param>
		/// <param name="projectScopeObject"></param>
		public EstimateResourceFactory(int? projectId, bool isResolveAssembly = true, bool isSetProjectCurrency = true, EstResourceUpdateOption resourceUpdateOption = null, bool isResolvePlantAssembly = false, ProjectScopeObject projectScopeObject = null) : base(projectId, projectScopeObject)
		{
			this._IsResolveAssembly = isResolveAssembly;

			this._IsSetProjectCurrency = isSetProjectCurrency;

			this._IsResolvePlantAssembly = isResolvePlantAssembly;

			var estResourceUpdateOption = resourceUpdateOption ?? new EstResourceUpdateOption()
			{
				ConsiderIsRate = false,
				IsUpdateOriginalValue = true,
				IsUpdateCostType = true,
				IsUpdatePlantAssembly = true
			};

			this._EstResourceUpdateHelper = new EstResourceUpdateHelper(this.GetProjectScopeObject());

			this._EstLineItemUpdateFrmPrjLogic = new EstLineItemUpdateFrmPrjLogic(projectId, estResourceUpdateOption, this.GetProjectScopeObject());

			this._EstResourceCopyLogic = new EstResourceCopyLogic(projectId, this.GetProjectScopeObject())
				.SetShowLastPriceList(new SystemOptionLogic().ShowLastPriceForMaterial());
			this._EstResourceCopyLogic.SetPrjAssemblies(this._EstLineItemUpdateFrmPrjLogic._ProjectAssemblies.ToList());

			this._EstResourceCopyLogic.SetPrjPlantAssemblies(this._EstLineItemUpdateFrmPrjLogic._ProjectPlantAssemblies.ToList());

			this._EstCharacteristicLogic = new EstCharacteristicLogic(this.GetProjectScopeObject());

			this._EstJobHelper = new EstJobHelper(projectId ?? 0);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="isResolveAssembly"></param>
		/// <param name="isSetProjectCurrency"></param>
		/// <returns></returns>
		public IEstimateResourceFactory CreateInstance(int? projectId, bool isResolveAssembly = true, bool isSetProjectCurrency = true)
		{
			return new EstimateResourceFactory(projectId, isResolveAssembly, isSetProjectCurrency);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public int GetCharacteristicSection()
		{
			return this._IsResolveAssembly ? (int)CharacteristicSectionConstants.EstimateResources : (int)CharacteristicSectionConstants.AssemblyResources;
		}

		private void Process(EstResourceEntity resource, int? jobId, bool considerInRate = false)
		{
			if (resource == null || !jobId.HasValue || (jobId.HasValue && jobId == 0) || !this.ProjectId.HasValue || (this.ProjectId.HasValue && this.ProjectId == 0))
			{
				return;
			}

			/* update information from project */
			this._EstLineItemUpdateFrmPrjLogic.UpdateRateFromProject(resource, jobId, considerInRate);

			/* update exchange rate */
			this.GetExchangeRateHelper().ExchangeRate(resource);
		}

		/// <summary>
		/// Create Resource By ResourceType
		/// </summary>
		/// <param name="resourceType"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		public IScriptEstResource CreateResource(int resourceType, IEstResourceCreationInfo creationInfo)
		{
			/* create new resource */
			var resourceEntity = _EstimateMainResourceLogic.Create(creationInfo.ParentId ?? 0, creationInfo.EstHeaderId, creationInfo.EstLineItemId, creationInfo.DescriptionInfo);

			resourceEntity.EstResourceTypeFk = resourceType;

			//auto assign characteristic
			if (creationInfo.IsGenerateDefaultCharacteristic)
			{
				this._EstCharacteristicLogic.AutoAssignDefaultCharacteristic(new List<EstResourceEntity>() { resourceEntity }, GetCharacteristicSection(), GetCharacteristicSection());
			}

			if (creationInfo.AfterCreated != null)
			{
				creationInfo.AfterCreated(resourceEntity);
			}

			return resourceEntity;
		}

		/// <summary>
		/// Create Resource By ResourceType
		/// </summary>
		/// <param name="resourceType"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		public IScriptEstResource CreateResource(EstResourceType resourceType, IEstResourceCreationInfo creationInfo)
		{
			return CreateResource((int)resourceType, creationInfo);
		}

		private EstResourceEntity CreateCostCode(IEstResourceCreationInfo creationInfo)
		{
			if (creationInfo.CostCodeEntity == null)
			{
				throw new Exception("Can not found the costcode entity!");
			}

			/* translate */
			creationInfo.CostCodeEntity.Translate(_EstimateMainResourceLogic.UserLanguageId, new Func<ICostCodeEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo, e => e.Description2Info });

			/* create new resource */
			var resourceEntity = _EstimateMainResourceLogic.Create(creationInfo.ParentId ?? 0, creationInfo.EstHeaderId, creationInfo.EstLineItemId);

			resourceEntity.EstResourceTypeFk = (int)EstResourceType.CostCode;

			/* copy information from master costcode entity */
			this._EstResourceUpdateHelper.UpdatePropertiesFromMasterCostCode(resourceEntity, creationInfo.CostCodeEntity, false, true, false);

			resourceEntity.CostUnitOriginal = creationInfo.CostCodeEntity.Rate;

			//Copy UDP values
			if (creationInfo.CostCodeEntity.UserDefinedcolValEntity != null)
			{
				resourceEntity.UserDefinedcolValEntity = EstUDPUtilities.CreateUDPEntityFromResource(resourceEntity);

				creationInfo.CostCodeEntity.UserDefinedcolValEntity.CopyTo(resourceEntity.UserDefinedcolValEntity);
			}

			//auto assign characteristic
			this._EstCharacteristicLogic.AutoAssignDefaultCharacteristic(new List<EstResourceEntity>() { resourceEntity }, GetCharacteristicSection(), GetCharacteristicSection());

			this._EstCharacteristicLogic.AutoAssignCostCodeCharacteristic(resourceEntity, GetCharacteristicSection());

			if (creationInfo.GenerateCharacteristicId)
			{
				this._EstCharacteristicLogic.GeneratedCharacteristicId(resourceEntity.Characteristic1List);
			}

            /* copy information from project costCode entity, and exchange rate */
            // DEV-37951 change considerInRate from true to creationInfo.ConsiderInRate, please consider this optioin when use CreateResource funtion
            this.Process(resourceEntity, creationInfo.JobId, creationInfo.ConsiderInRate);

			/* enhance */
			if (creationInfo.AfterCreated != null)
			{
				creationInfo.AfterCreated(resourceEntity);
			}

			return resourceEntity;
		}

		private EstResourceEntity CreatePrjCostCode(IEstResourceCreationInfo creationInfo)
		{
			if (creationInfo.PrjCostCodeEntity == null)
			{
				throw new Exception("Can not found the prccostcode entity!");
			}

			/* create new resource */
			var resourceEntity = _EstimateMainResourceLogic.Create(creationInfo.ParentId ?? 0, creationInfo.EstHeaderId, creationInfo.EstLineItemId);

			resourceEntity.EstResourceTypeFk = (int)EstResourceType.CostCode;

			if (creationInfo.PrjCostCodeEntity.MdcCostCodeFk.HasValue)
			{
				resourceEntity.MdcCostCodeFk = creationInfo.PrjCostCodeEntity.MdcCostCodeFk;
			}
			else
			{
				resourceEntity.ProjectCostCodeFk = creationInfo.PrjCostCodeEntity.Id;
			}

			resourceEntity.Code = creationInfo.PrjCostCodeEntity.Code;

			/* copy information from master costcode entity */
			this._EstResourceUpdateHelper.UpdatePropertiesFromProjectCostCode(resourceEntity, creationInfo.PrjCostCodeEntity, true, true, false);

			resourceEntity.CostUnitOriginal = creationInfo.PrjCostCodeEntity.Rate;
			//auto assign characteristic
			this._EstCharacteristicLogic.AutoAssignDefaultCharacteristic(new List<EstResourceEntity>() { resourceEntity }, GetCharacteristicSection(), GetCharacteristicSection());

			this._EstCharacteristicLogic.AutoAssignCostCodeCharacteristic(resourceEntity, GetCharacteristicSection());

			if (creationInfo.GenerateCharacteristicId)
			{
				this._EstCharacteristicLogic.GeneratedCharacteristicId(resourceEntity.Characteristic1List);
			}

			/* copy information from project costCode entity, and exchange rate */
			this.Process(resourceEntity, creationInfo.JobId);

			/* enhance */
			if (creationInfo.AfterCreated != null)
			{
				creationInfo.AfterCreated(resourceEntity);
			}

			return resourceEntity;
		}

		/// <summary>
		/// Create Resource By CostCode Id
		/// </summary>
		/// <param name="mdcCostCodeId"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		public IScriptEstResource CreateCostCodeById(int mdcCostCodeId, IEstResourceCreationInfo creationInfo)
		{
			/* search costCode entity by id */
			creationInfo.CostCodeEntity = this.GetCostCodeSearchService().GetCostCodeById(mdcCostCodeId);

			return CreateCostCode(creationInfo);
		}

		/// <summary>
		/// Create Resource By CostCode Ids
		/// </summary>
		/// <param name="mdcCostCodeIds"></param>
		/// <param name="prjCostCodeIds"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		public IEnumerable<EstResourceEntity> CreateCostCodeByIds(IEnumerable<int> mdcCostCodeIds, IEnumerable<int> prjCostCodeIds, IEstResourceCreationInfo creationInfo)
		{
			var result = new List<EstResourceEntity>();

			// master costcode
			if (mdcCostCodeIds != null && mdcCostCodeIds.Any())
			{
				var costCodes = this.GetCostCodeSearchService().GetCostCodesByIds(mdcCostCodeIds);
				foreach (var costCode in costCodes)
				{
					var newCostCodeResource = CreateCostCode(
						new EstResourceCreationInfo()
						{
							EstHeaderId = creationInfo.EstHeaderId,
							EstLineItemId = creationInfo.EstLineItemId,
							ParentId = creationInfo.ParentId,
							JobId = creationInfo.JobId,
							CostCodeEntity = costCode,
							ConsiderInRate = creationInfo.ConsiderInRate,
							AfterCreated = creationInfo.AfterCreated
						});

					if (newCostCodeResource == null)
					{
						continue;
					}

					result.Add(newCostCodeResource);
				}
			}
			// project costcode
			if (prjCostCodeIds != null && prjCostCodeIds.Any())
			{
				var prjcostcodes = this.GetProjectCostCodeSearchService().GetProjectCostCodeByIds(prjCostCodeIds);

				foreach (var prjcostcode in prjcostcodes)
				{
					var newPrjCostCodeResource = CreatePrjCostCode(new EstResourceCreationInfo()
					{
						EstHeaderId = creationInfo.EstHeaderId,
						EstLineItemId = creationInfo.EstLineItemId,
						ParentId = creationInfo.ParentId,
						JobId = creationInfo.JobId,
						PrjCostCodeEntity = prjcostcode,
						AfterCreated = creationInfo.AfterCreated
					});

					if (newPrjCostCodeResource == null)
					{
						continue;
					}

					result.Add(newPrjCostCodeResource);
				}
			}
			return result;
		}

		/// <summary>
		/// Create Resource By CostCode Code
		/// </summary>
		/// <param name="code"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		public IScriptEstResource CreateCostCodeByCode(string code, IEstResourceCreationInfo creationInfo)
		{
			if (creationInfo == null || !this._ProjectId.HasValue || !creationInfo.JobId.HasValue)
			{
				return null;
			}

			var errorMessage = string.Empty;

			var mdcCostCodeId = 0;
			/* search costCode in project cost code */
			if (creationInfo.PrjCostCodeEntity == null && creationInfo.CostCodeEntity == null)
			{
				creationInfo.PrjCostCodeEntity = this.GetProjectCostCodeSearchService().GetProjectCostCodeByCode(code);
			}

			if (creationInfo.PrjCostCodeEntity == null)
			{
				if (creationInfo.CostCodeEntity == null)
				{
					/* search costCode in master cost code */
					creationInfo.CostCodeEntity = this.GetCostCodeSearchService().GetCostCodesByCode(code, out errorMessage);
				}

				if (creationInfo.CostCodeEntity == null)
				{
					throw new Exception(errorMessage);
				}

				mdcCostCodeId = creationInfo.CostCodeEntity.Id;
			}

			if (creationInfo.PrjCostCodeEntity != null)
			{
				return CreatePrjCostCode(creationInfo);
			}
			return CreateCostCode(creationInfo);
		}

		/// <summary>
		/// Create Resource By Material
		/// </summary>
		/// <param name="materialEntity"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		public EstResourceEntity CreateMaterial(MaterialEntity materialEntity, IEstResourceCreationInfo creationInfo)
		{
			if (materialEntity == null)
			{
				throw new Exception("Can not found the material entity!");
			}

			materialEntity.Translate(_EstimateMainResourceLogic.UserLanguageId, new Func<MaterialEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo1, e => e.DescriptionInfo2 });

			/* create new resource */
			var resourceEntity = _EstimateMainResourceLogic.Create(creationInfo.ParentId ?? 0, creationInfo.EstHeaderId, creationInfo.EstLineItemId);

			resourceEntity.EstResourceTypeFk = (int)EstResourceType.Material;

			/* copy information from master material entity */
			resourceEntity.Code = materialEntity.Code;

			resourceEntity.DescriptionInfo = materialEntity.DescriptionInfo1;
			resourceEntity.DescriptionInfo1 = materialEntity.DescriptionInfo2;

			resourceEntity.MdcMaterialFk = materialEntity.Id;

			resourceEntity.BasUomFk = materialEntity.UomFk;

			resourceEntity.BasCurrencyFk = materialEntity.BasCurrencyFk;

			resourceEntity.EstCostTypeFk = materialEntity.EstCostTypeFk;

			resourceEntity.HourFactor = materialEntity.FactorHour;

			resourceEntity.DayWorkRateUnit = materialEntity.DayworkRate;

			resourceEntity.Co2Source = materialEntity.Co2Source;

			resourceEntity.Co2Project = materialEntity.Co2Project;

			var isLabour = materialEntity.IsLabour;

			/* get the MdcCostCodeFk from MdcCommoditySearchVLogic and set to est resource. */
			var materialCommodity = this.GetMdcCommoditySearchService().GetEntityByKey(materialEntity.Id);

			if (materialCommodity != null)
			{
				resourceEntity.MdcCostCodeFk = materialCommodity.MdcCostCodeFk;

				var costCode = resourceEntity.MdcCostCodeFk.HasValue ? this.GetCostCodeSearchService().GetCostCodeById(resourceEntity.MdcCostCodeFk.Value) : null;

				if (costCode != null)
				{
					resourceEntity.IsCost = costCode.IsCost;

					resourceEntity.IsBudget = costCode.IsBudget;

					isLabour = costCode.IsLabour;

					resourceEntity.IsRate = !isLabour.Value || costCode.IsRate;
				}
			}

			creationInfo.MaterialCommodity = materialCommodity;

			resourceEntity.HoursUnit = isLabour.HasValue && isLabour.Value ? this.GetUoMSearchService().GetUomConversionFactor(resourceEntity.BasUomFk) : 0;

			var materialCommonPriceEntity = new MaterialCommonPriceEntity
			{
				EstimatePrice = materialEntity.EstimatePrice,
				PriceUnit = materialEntity.PriceUnit,
				FactorPriceUnit = materialEntity.FactorPriceUnit
			};

			var resCostUnit = new EstProjectMaterialHelper().GetCostUnitFromMaterial(materialCommonPriceEntity);

			resourceEntity.CostUnit = resCostUnit;

			resourceEntity.CostUnitOriginal = resCostUnit;

			//auto assign characteristic
			this._EstCharacteristicLogic.AutoAssignDefaultCharacteristic(new List<EstResourceEntity>() { resourceEntity }, GetCharacteristicSection(), GetCharacteristicSection());

			this._EstCharacteristicLogic.AutoAssignMaterialCharacteristic(resourceEntity, GetCharacteristicSection());

			if (creationInfo.GenerateCharacteristicId)
			{
				this._EstCharacteristicLogic.GeneratedCharacteristicId(resourceEntity.Characteristic1List);
			}

			/* copy information from project material entity, and exchange rate */
			this.Process(resourceEntity, creationInfo.JobId);

			/* enhance */
			if (creationInfo.AfterCreated != null)
			{
				creationInfo.AfterCreated(resourceEntity);
			}

			return resourceEntity;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="materialEntity"></param>
		/// <param name="resourceEntity"></param>
		/// <param name="creationInfo"></param>
		public void SetResourceByeMaterial(MaterialEntity materialEntity, EstResourceEntity resourceEntity, IEstResourceCreationInfo creationInfo)
		{
			materialEntity.Translate(_EstimateMainResourceLogic.UserLanguageId, new Func<MaterialEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo1, e => e.DescriptionInfo2 });

			resourceEntity.EstResourceTypeFk = (int)EstResourceType.Material;

			/* copy information from master material entity */
			resourceEntity.Code = materialEntity.Code;

			resourceEntity.DescriptionInfo = materialEntity.DescriptionInfo1;
			resourceEntity.DescriptionInfo1 = materialEntity.DescriptionInfo2;

			resourceEntity.MdcMaterialFk = materialEntity.Id;

			resourceEntity.BasUomFk = materialEntity.UomFk;

			resourceEntity.BasCurrencyFk = materialEntity.BasCurrencyFk;

			resourceEntity.EstCostTypeFk = materialEntity.EstCostTypeFk;

			resourceEntity.HourFactor = materialEntity.FactorHour;

			resourceEntity.DayWorkRateUnit = materialEntity.DayworkRate;

			resourceEntity.Co2Source = materialEntity.Co2Source;

			resourceEntity.Co2Project = materialEntity.Co2Project;

			var isLabour = materialEntity.IsLabour;

			/* get the MdcCostCodeFk from MdcCommoditySearchVLogic and set to est resource. */
			var materialCommodity = this.GetMdcCommoditySearchService().GetEntityByKey(materialEntity.Id);

			if (materialCommodity != null)
			{
				resourceEntity.MdcCostCodeFk = materialCommodity.MdcCostCodeFk;

				var costCode = resourceEntity.MdcCostCodeFk.HasValue ? this.GetCostCodeSearchService().GetCostCodeById(resourceEntity.MdcCostCodeFk.Value) : null;

				if (costCode != null)
				{
					resourceEntity.IsCost = costCode.IsCost;

					resourceEntity.IsBudget = costCode.IsBudget;

					isLabour = costCode.IsLabour;

					resourceEntity.IsRate = !isLabour.Value || costCode.IsRate;
				}
			}

			resourceEntity.HoursUnit = isLabour.HasValue && isLabour.Value ? this.GetUoMSearchService().GetUomConversionFactor(resourceEntity.BasUomFk) : 0;

			var materialCommonPriceEntity = new MaterialCommonPriceEntity
			{
				EstimatePrice = materialEntity.EstimatePrice,
				PriceUnit = materialEntity.PriceUnit,
				FactorPriceUnit = materialEntity.FactorPriceUnit
			};

			var resCostUnit = new EstProjectMaterialHelper().GetCostUnitFromMaterial(materialCommonPriceEntity);

			resourceEntity.CostUnit = resCostUnit;

			resourceEntity.CostUnitOriginal = resCostUnit;

			/* delete old characticts */
			new CharacteristicDataLogic().DeleteListBySectionIdAndObjectIds((int)SelectFunction.EstimateResourceCharSectionId, new List<RIB.Visual.Platform.Core.IdentificationData> { resourceEntity.IdentificationData });

			//auto assign material characteristic
			this._EstCharacteristicLogic.AutoAssignMaterialCharacteristic(resourceEntity, GetCharacteristicSection());

			/* copy information from project material entity, and exchange rate */
			this.Process(resourceEntity, creationInfo.JobId);
		}

		/// <summary>
		/// Create Resource By Material Id
		/// </summary>
		/// <param name="mdcMaterialId"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		public IScriptEstResource CreateMaterialById(int mdcMaterialId, IEstResourceCreationInfo creationInfo)
		{
			var materialEntity = new MaterialLogic().GetItemByKey(mdcMaterialId);

			return CreateMaterial(materialEntity, creationInfo);
		}

		/// <summary>
		/// Create Resource By Material Ids
		/// </summary>
		/// <param name="mdcMaterialIds"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		public IEnumerable<EstResourceEntity> CreateMaterialByIds(IEnumerable<int> mdcMaterialIds, IEstResourceCreationInfo creationInfo)
		{
			var result = new List<EstResourceEntity>();

			if (mdcMaterialIds == null || !mdcMaterialIds.Any())
			{
				return result;
			}

			var materials = new MaterialLogic().GetListByIds(mdcMaterialIds);

			foreach (var material in materials)
			{
				var newMaterialResource = this.CreateMaterial(material, creationInfo);

				if (newMaterialResource == null)
				{
					continue;
				}

				result.Add(newMaterialResource);
			}

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="materialCatCode"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		/// <exception cref="EstRuleWarningException"></exception>
		public IScriptEstResource CreateMaterialByCode(string code, string materialCatCode, IEstResourceCreationInfo creationInfo)
		{
			MaterialEntity materialEntity = null;

			/* filter by master data filter */
			Func<IMdcCommoditySearchVEntity, bool> enhanceFilterFunc = null;

			if (_ProjectId.HasValue && this.GetEstRateBookLogic() != null && this.GetProjectMaterialSearchService() != null)
			{
				var projectMaterials = GetProjectMaterialSearchService().GetProjectMaterials();

				enhanceFilterFunc = e =>
		  {
			  bool result = GetEstRateBookLogic().IsMaterialInMasterFilter(e);
			  if (!result)
			  {
				  var prjMaterials = projectMaterials.Where(m => m.MdcMaterialFk == e.Id && m.LgmJobFk == creationInfo.JobId);
				  if (prjMaterials.Any())
				  {
					  return true;
				  }
			  }
			  return result;
		  };
			}

			var materialTuple = _MaterialTupleList.OrderByDescending(e => e.Item3.Id).FirstOrDefault(e => e.Item1 == code && e.Item2 == materialCatCode);

			if (materialTuple == null)
			{
				materialEntity = new MaterialLogic().GetMaterialByCode(code, string.Empty, materialCatCode, enhanceFilterFunc);

				if (materialEntity != null)
				{
					this._MaterialTupleList.Add(new Tuple<string, string, MaterialEntity>(code, materialCatCode, materialEntity));
				}
			}
			else
			{
				materialEntity = materialTuple.Item3;
			}

			if (materialEntity == null)
			{
				throw new Exception(string.Format("Can not found material which code is [{0}]", code));
			}

			var isReadOnly = this._EstJobHelper.CheckCurrentJobIsReadOnly((int)EstResourceType.Material, creationInfo.ProjectId ?? 0, creationInfo.JobId ?? 0, new Tuple<int, int?>(materialEntity.Id, null));
			if (isReadOnly)
			{
				throw new EstRuleWarningException(LocalizationProps.Resources.Warn_Resouce_Job_ReadOnly_Msg);

			}

			return CreateMaterial(materialEntity, creationInfo);
		}

		/// <summary>
		/// Create Resource By Plan Code
		/// </summary>
		/// <param name="code"></param>
		/// <param name="plantMasterCode"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		public IScriptEstResource CreatePlanByCode(string code, string plantMasterCode, IEstResourceCreationInfo creationInfo)
		{
			if (string.IsNullOrWhiteSpace(code) && string.IsNullOrWhiteSpace(plantMasterCode))
			{
				return null;
			}

			this._IsPlantAssembly = true;

			this._IsResolvePlantAssembly = true;

			var estimateAssemblyLogic = new EstimateAssembliesLogic();

			int? plantMasterFk = null;

			string filterByCatStructure = "[]";

			if (!string.IsNullOrWhiteSpace(plantMasterCode))
			{
				plantMasterFk = estimateAssemblyLogic.GetPlantAssemblyGroupList(creationInfo.JobId, creationInfo.ProjectId, 0).FirstOrDefault(e => e.Code == plantMasterCode)?.Id;
				if (!plantMasterFk.HasValue)
				{
					plantMasterFk = -2;
				}
				filterByCatStructure = "[{\"Key\":1,\"Value\":[]},{\"Key\":0,\"Value\":[" + plantMasterFk.Value + "]}]";
			}

			var filterResult = new EstAssemblySearchResult() { SearchValue = code };

			var filterData = new EstAssemblySearchInfo()
			{
				LgmJobFk = creationInfo.JobId,
				ProjectId = creationInfo.ProjectId,
				SearchValue = code,
				ItemsPerPage = 1000,
				CurrentPage = 0,
				FilterByEstimate = false,
				Field = plantMasterFk == -2 ? "Code" : "",
				FilterByCatStructure = filterByCatStructure
			};

			var plantAssemblies = estimateAssemblyLogic.GetPlantAssemblySearchList(filterData, ref filterResult);

			var plantAssemblyDict = new Dictionary<int, int>();

			var plantAssemblyIds = new List<int>();

			if (!string.IsNullOrWhiteSpace(code))
			{
				// search by assembly code
				var plantAssebmly = plantMasterFk.HasValue ? plantAssemblies.FirstOrDefault(e => e.Code == code && e.PlantFk == plantMasterFk) : plantAssemblies.FirstOrDefault(e => e.Code == code);

				if (plantAssebmly == null)
				{
					throw new EstRuleWarningException(String.Format(LocalizationProps.Resources.Warn_Rules_Resource_Create_Not_Found, "Plant Assembly", code));
				}

				plantMasterFk = plantMasterFk ?? (plantAssebmly.PlantFk.HasValue ? plantAssebmly.PlantFk.Value : -2);

				plantAssemblyDict.Add(plantAssebmly.Id, plantMasterFk.Value);

				plantAssemblyIds.Add(plantAssebmly.Id);
			}
			else if (plantMasterFk.HasValue && plantAssemblies.Any())
			{
				// search by plant master code
				foreach (var id in plantAssemblies.CollectIds(e => e.Id))
				{
					plantAssemblyDict.Add(id, plantMasterFk.Value);

					plantAssemblyIds.Add(id);
				}
			}
			else
			{
				return null;
			}

			EstResourceEntity existPlantResource = null;

			if (IsExistPlantAssembly(code, plantMasterFk.Value, creationInfo, ref existPlantResource))
			{
				// already exist EA
				throw new EstRuleWarningException(String.Format(LocalizationProps.Resources.Warn_Rules_Resource_Create_Unique_Code, "Plant Assembly", code));
			}

			if (existPlantResource != null && existPlantResource.Version == 0 && string.IsNullOrWhiteSpace(code))
			{
				var existAssIds = existPlantResource.Resources?.CollectIds(e => e.EstAssemblyFk);
				if (existAssIds.Any() && plantAssemblyDict.Count > 0)
				{
					foreach (var id in existAssIds.Where(id => plantAssemblyDict.ContainsKey(id)))
					{
						plantAssemblyDict.Remove(id);
						plantAssemblyIds.Remove(id);
					}
					if (plantAssemblyDict.Count <= 0)
					{
						throw new EstRuleWarningException(String.Format(LocalizationProps.Resources.Warn_Rules_Resource_Create_Unique_Code, "Plant", plantMasterCode));
					}
				}
			}
			else if (existPlantResource != null && existPlantResource.Version > 0 && !string.IsNullOrWhiteSpace(plantMasterCode))
			{
				// already exist plant 
				throw new EstRuleWarningException(String.Format(LocalizationProps.Resources.Warn_Rules_Resource_Create_Unique_Code, "Plant", plantMasterCode));
			}

			var newPlantAssemblys = this.CreatePlantAssemblyByIds(plantAssemblyIds, creationInfo, false, true, plantAssemblyDict);

			var newPlantAssembly = newPlantAssemblys?.FirstOrDefault();

			/* enhance */
			if (creationInfo.AfterCreatedEnhance != null && newPlantAssemblys.Any())
			{
				return creationInfo.AfterCreatedEnhance(newPlantAssemblys.Select(e => e as IScriptEstResource).ToList(), existPlantResource);
			}

			return newPlantAssembly;
		}

        /// <summary>
        /// 
        /// </summary>
        /// <param name="code"></param>
        /// <param name="plantFk"></param>
        /// <param name="creationInfo"></param>
        /// <param name="existPlantResource"></param>
        /// <returns></returns>
        private static bool IsExistPlantAssembly(string code, int plantFk, IEstResourceCreationInfo creationInfo, ref EstResourceEntity existPlantResource)
        {
            var plantResources = new List<IScriptEstResource>();
            var _existPlantResources = creationInfo?.EstLineItem?.Resources?.FlattenResources().Where(x => x.EstResourceTypeFk == (int)EstResourceType.Plant && x.EstResourceFk == creationInfo.ParentId).ToList();
            if (_existPlantResources != null && _existPlantResources.Any())
            {
                plantResources.AddRange(_existPlantResources);
            }
            var _newNoSavePlantResources = creationInfo?.UnsavedResourceTree?.FlattenResources().Where(x => x.EstResourceTypeFk == (int)EstResourceType.Plant && x.EstLineItemFk == creationInfo.EstLineItemId && x.EstHeaderFk == creationInfo.EstHeaderId && x.EstResourceFk == creationInfo.ParentId).ToList();
            if (_newNoSavePlantResources != null && _newNoSavePlantResources.Any())
            {
                plantResources.AddRange(_newNoSavePlantResources);
            }
            if (plantResources.Any())
            {
                var existPlantResources = plantResources.FlattenResources();

                if (plantFk == -2)
                {
                    // not plant group
                    return existPlantResources.Any(x => x.Code == code && x.EstResourceTypeFk == (int)EstResourceType.EquipmentAssembly && !x.EtmPlantFk.HasValue);
                }
                else
                {
                    // exist plant group
                    var plantResouce = plantResources.FirstOrDefault(x => x.EtmPlantFk == plantFk);
                    if (plantResouce != null)
                    {
                        existPlantResource = plantResouce as EstResourceEntity;
                        return existPlantResources.Any(x => x.Code == code && x.EstResourceTypeFk == (int)EstResourceType.EquipmentAssembly && x.EstResourceFk == plantResouce.Id);
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// Create SubItem Resource
        /// </summary>
        /// <param name="code"></param>
        /// <param name="creationInfo"></param>
        /// <returns></returns>
        public IScriptEstResource CreateSubItemByCode(string code, IEstResourceCreationInfo creationInfo)
		{
			/* create new resource */
			var resourceEntity = _EstimateMainResourceLogic.Create(creationInfo.ParentId ?? 0, creationInfo.EstHeaderId, creationInfo.EstLineItemId);

			resourceEntity.EstResourceTypeFk = (int)EstResourceType.SubItem;

			resourceEntity.Code = code;

			//auto assign characteristic
			if (creationInfo.IsGenerateDefaultCharacteristic)
			{
				this._EstCharacteristicLogic.AutoAssignDefaultCharacteristic(new List<EstResourceEntity>() { resourceEntity }, GetCharacteristicSection(), GetCharacteristicSection());
			}

			/* enhance */
			if (creationInfo.AfterCreated != null)
			{
				creationInfo.AfterCreated(resourceEntity);
			}

			return resourceEntity;
		}

		/// <summary>
		/// Create Resource By Assembly Ids
		/// </summary>
		/// <param name="assemblyIds"></param>
		/// <param name="creationInfo"></param>
		/// <param name="includeMarkupAsCostUnit"></param>
		/// <param name="IsCopyByDragDropSearchWizard"></param>
		/// <param name="isDrag"></param>
		/// <param name="isTakeOverFromLineItem"></param>
		/// <returns></returns>
		public IEnumerable<EstResourceEntity> CreateAssemblyByIds(IEnumerable<int> assemblyIds, IEstResourceCreationInfo creationInfo, bool includeMarkupAsCostUnit = false, bool isDrag = false, bool IsCopyByDragDropSearchWizard = false, bool isTakeOverFromLineItem = false)
		{
			var result = new List<EstResourceEntity>();

			if (assemblyIds == null || !assemblyIds.Any())
			{
				return result;
			}

			var assemblies = !isDrag ? new ScriptEstAssemblyLogic().GetFilteredList(assemblyIds.ToArray()) : new ScriptEstAssemblyLogic().GetMasterOrPrjAssemblyById(assemblyIds, this.ProjectId ?? 0, creationInfo.JobId);

			return CreateAssemblies(assemblies, creationInfo, includeMarkupAsCostUnit, IsCopyByDragDropSearchWizard: IsCopyByDragDropSearchWizard, isTakeOverFromLineItem: isTakeOverFromLineItem);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="assemblyEntity"></param>
		/// <param name="creationInfo"></param>
		/// <param name="includeMarkupAsCostUnit"></param>
		/// <param name="isCreateForProjectAssembly"></param>
		/// <param name="workOperationTypeFk"></param>
		/// <param name="IsCopyByDragDropSearchWizard"></param>
		/// <param name="resourceCopyOption"></param>
		/// <returns></returns>
		public EstResourceEntity CreateAssembly(IScriptEstAssembly assemblyEntity, IEstResourceCreationInfo creationInfo, bool includeMarkupAsCostUnit = false, bool isCreateForProjectAssembly = false, int? workOperationTypeFk = null, bool IsCopyByDragDropSearchWizard = false, ResourceCopyOption resourceCopyOption = null)
		{
			if (assemblyEntity == null)
			{
				return null;
			}

			/* create new resource */
			var resourceEntity = _EstimateMainResourceLogic.CreateNewResourceCore(creationInfo.EstLineItemId, creationInfo.EstHeaderId, creationInfo.ParentId);

			if (this._IsPlantAssembly)
			{
				resourceEntity.EstResourceTypeFk = (int)EstResourceType.EquipmentAssembly;
				//if (!workOperationTypeFk.HasValue && creationInfo.JobId.HasValue)
				//{
				//	var plantEstimateSettingsLogic = Injector.Get<ILogisticPlantEstimateSettings>();
				//	workOperationTypeFk = plantEstimateSettingsLogic.DefaultPlantEstimateWorkOperationType(creationInfo.JobId.Value);
				//}
				//resourceEntity.WorkOperationTypeFk = workOperationTypeFk;
			}
			else
			{
				resourceEntity.EstResourceTypeFk = (int)EstResourceType.Assembly;
			}

			resourceEntity.UserDefinedcolValEntity = assemblyEntity.UserDefinedcolValEntity;

			/* copy assembly information to resource */
			new ScriptEstResourceLogic().CopyPropertyFromAssembly(resourceEntity, assemblyEntity);

			/*Fix ALM 113236 Assembly resources get default job assigned to them automatically*/
			var resourcesCopy = this._EstResourceCopyLogic.CopyResources(new List<EstResourceEntity>() { resourceEntity }, resourceCopyOption ?? new ResourceCopyOption()
			{
				LineItemId = creationInfo.EstLineItemId,
				HeaderId = creationInfo.EstHeaderId,
				ParentResourceId = creationInfo.ParentId,
				JobId = creationInfo.JobId,
				SetResourceJobFk = false,
				IncludeMarkupAsCostUnit = includeMarkupAsCostUnit,
				IsResolveAssembly = this._IsResolveAssembly,
				IsSetProjectCurrency = this._IsSetProjectCurrency,
				IsResolvePlantAssembly = this._IsResolvePlantAssembly,
				ResourceCopyFrom = this._IsPlantAssembly ? ResourceModuleType.PlanAssembly : ResourceModuleType.MasterAssembly,
				ResourceCopyTo = this._IsResolveAssembly ? ResourceModuleType.Estimate : ResourceModuleType.MasterAssembly,
				isCreateForProjectAssembly = isCreateForProjectAssembly,
				WorkOperationTypeFk = workOperationTypeFk,
				UserDefinedColumnValCopyOption = new UserDefinedColumnValCopyOption()
				{
					IsCopyUserDefinedColumnVal = true,
					AutoGenerateId = true,
				},
				CharacteristicCopyOption = new CharacteristicCopyOption()
				{
					IsCopyCharacteristic = true,
					IsAutoAssignCharacteristic = true,
					AutoGenerateId = true
				}
			});

			var assemblyResource = resourcesCopy != null && resourcesCopy.Any() ? resourcesCopy.First() : null;

			if (IsCopyByDragDropSearchWizard)
			{
				var estResourceLogic = new EstimateMainResourceLogic();

				if (assemblyResource != null)
				{
					assemblyResource.ResourceChildren = estResourceLogic.SortResourcesRecursively(assemblyResource.ResourceChildren);
				}
			}

			/* get costType from costCode or material */
			SetCostTypeToAssembly(assemblyResource, creationInfo.JobId);

			// load cache data, to enchance performance the function: UpdateRateFromProject
			var assemblyResourceList = assemblyResource.FlattenResources().Where(e => e.EstResourceTypeFk == (int)EstResourceType.CostCode);
			this.GetProjectScopeObject().LoadCacheDataInBulk(assemblyResourceList);

			/* copy information from project costcode/material entity */
			this._EstLineItemUpdateFrmPrjLogic.UpdateRateFromProject(assemblyResource, creationInfo.JobId, true, true);

			/* enhance */
			if (creationInfo.AfterCreated != null)
			{
				creationInfo.AfterCreated(assemblyResource);
			}

			return assemblyResource;
		}



		/// <summary>
		/// This method corrects the resource factor values
		/// </summary>
		/// <param name="resource"></param>
		public void CorrectResourceFactors(EstResourceEntity resource)
		{
			resource.CostFactor1 = resource.CostFactor1 == 0 ? 1 : resource.CostFactor1;
			resource.CostFactor2 = resource.CostFactor2 == 0 ? 1 : resource.CostFactor2;
			resource.CostFactorDetail1 = resource.CostFactor1.ToString();
			resource.CostFactorDetail2 = resource.CostFactor2.ToString();
			resource.CostFactorCc = resource.CostFactorCc == 0 ? 1 : resource.CostFactorCc;
			resource.EfficiencyFactor1 = resource.EfficiencyFactor1 == 0 ? 1 : resource.EfficiencyFactor1;
			resource.EfficiencyFactor2 = resource.EfficiencyFactor2 == 0 ? 1 : resource.EfficiencyFactor2;
			resource.EfficiencyFactorDetail1 = resource.EfficiencyFactor1.ToString();
			resource.EfficiencyFactorDetail2 = resource.EfficiencyFactor2.ToString();
			resource.HourFactor = resource.HourFactor == 0 ? 1 : resource.HourFactor;
			resource.QuantityFactor1 = resource.QuantityFactor1 == 0 ? 1 : resource.QuantityFactor1;
			resource.QuantityFactor2 = resource.QuantityFactor2 == 0 ? 1 : resource.QuantityFactor2;
			resource.QuantityFactor3 = resource.QuantityFactor3 == 0 ? 1 : resource.QuantityFactor3;
			resource.QuantityFactor4 = resource.QuantityFactor4 == 0 ? 1 : resource.QuantityFactor4;
			resource.QuantityFactorDetail1 = resource.QuantityFactor1.ToString();
			resource.QuantityFactorDetail2 = resource.QuantityFactor2.ToString();
			resource.QuantityFactorCc = resource.QuantityFactorCc == 0 ? 1 : resource.QuantityFactorCc;
			resource.AnscetorCostFactor = resource.AnscetorCostFactor == 0 ? 1 : resource.AnscetorCostFactor;
			resource.ProductivityFactor = resource.ProductivityFactor == 0 ? 1 : resource.ProductivityFactor;
			resource.ProductivityFactorDetail = resource.ProductivityFactor.ToString();
		}

		private void SetIsCostFromCostCode(EstResourceEntity assemblyResource, IProjectCostCodesEntity projectCostCodeEntity, bool setCostType = true)
		{
			if (projectCostCodeEntity != null)
			{
				if (setCostType)
				{
					assemblyResource.EstCostTypeFk = projectCostCodeEntity.EstCostTypeFk;
				}

				if (assemblyResource.EstResourceTypeFk == (int)EstResourceType.Assembly)
				{
					assemblyResource.IsCost = projectCostCodeEntity.IsCost;

					assemblyResource.IsBudget = projectCostCodeEntity.IsBudget;
				}
			}
			else
			{
				var mdcCostCodeEntity = assemblyResource.MdcCostCodeFk.HasValue ? this.GetCostCodeSearchService().GetCostCodeById(assemblyResource.MdcCostCodeFk.Value) : null;

				if (mdcCostCodeEntity != null)
				{
					if (setCostType)
					{
						assemblyResource.EstCostTypeFk = mdcCostCodeEntity.EstCostTypeFk;
					}

					if (assemblyResource.EstResourceTypeFk == (int)EstResourceType.Assembly)
					{
						assemblyResource.IsCost = mdcCostCodeEntity.IsCost;

						assemblyResource.IsBudget = mdcCostCodeEntity.IsBudget;
					}
				}
			}
		}

		private void SetCostTypeToAssembly(EstResourceEntity assemblyResource, int? jobId)
		{
			if (assemblyResource == null)
			{
				return;
			}

			if (assemblyResource.MdcMaterialFk.HasValue)
			{
				var mdcMaterialEntity = this.GetMdcCommoditySearchService().GetEntityByKey(assemblyResource.MdcMaterialFk.Value);

				if (mdcMaterialEntity != null)
				{
					assemblyResource.EstCostTypeFk = mdcMaterialEntity.EstCostTypeFk;
				}

				if (this._ProjectId.HasValue && jobId.HasValue)
				{
					var projectMaterialEntity = this.GetProjectMaterialSearchService().GetProjectMaterialById(this._ProjectId, assemblyResource.MdcMaterialFk.Value, jobId, null);

					if (projectMaterialEntity != null)
					{
						assemblyResource.EstCostTypeFk = projectMaterialEntity.EstCostTypeFk;
					}
				}

				var mdcCostCodeFk = assemblyResource.MdcCostCodeFk.HasValue
					 ? assemblyResource.MdcCostCodeFk.Value
					 : mdcMaterialEntity != null && mdcMaterialEntity.MdcCostCodeFk.HasValue ? mdcMaterialEntity.MdcCostCodeFk.Value : 0;

				if (mdcCostCodeFk > 0)
				{
					IProjectCostCodesEntity projectCostCodeEntity = null;

					if (this._ProjectId.HasValue && jobId.HasValue)
					{
						projectCostCodeEntity = this.GetProjectCostCodeSearchService().GetProjectCostCodeById(this._ProjectId, mdcCostCodeFk, jobId);
					}

					SetIsCostFromCostCode(assemblyResource, projectCostCodeEntity, false);
				}
			}
			else if (assemblyResource.MdcCostCodeFk.HasValue || assemblyResource.ProjectCostCodeFk.HasValue)
			{
				IProjectCostCodesEntity projectCostCodeEntity = null;

				if (!assemblyResource.MdcCostCodeFk.HasValue && !assemblyResource.ProjectCostCodeFk.HasValue)
				{
					return;
				}

				if (this._ProjectId.HasValue && jobId.HasValue)
				{
					projectCostCodeEntity = assemblyResource.MdcCostCodeFk.HasValue ?
						 this.GetProjectCostCodeSearchService().GetProjectCostCodeById(this._ProjectId, assemblyResource.MdcCostCodeFk.Value, jobId)
						 : this.GetProjectCostCodeSearchService().GetProjectCostCodeById(this._ProjectId, null, assemblyResource.ProjectCostCodeFk.Value);
				}

				SetIsCostFromCostCode(assemblyResource, projectCostCodeEntity);
			}
		}

		/// <summary>
		/// Create Resource By Assembly Code
		/// </summary>
		/// <param name="code"></param>
		/// <param name="assemblyCatCode"></param>
		/// <param name="rootAssemblyCatCode"></param>
		/// <param name="projectId"></param>
		/// <param name="creationInfo"></param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		public IScriptEstResource CreateAssemblyByCode(string code, string assemblyCatCode, string rootAssemblyCatCode, int? projectId, IEstResourceCreationInfo creationInfo)
		{
			this._IsPlantAssembly = false;
			this._IsResolvePlantAssembly = false;
			this._IsResolveAssembly = true;

			Func<IScriptEstAssembly, bool> enhanceFilterFunc = null;

			if (this.GetEstRateBookLogic() != null)
			{
				enhanceFilterFunc = this.GetEstRateBookLogic().IsAssemblyInMasterFilter;
			}

			IScriptEstAssembly assemblyEntity = null;

			var assemblyTuple = _AssemblyTupleList.FirstOrDefault(e => e.Item1 == code && e.Item2 == assemblyCatCode && e.Item3 == rootAssemblyCatCode);

			if (assemblyTuple == null)
			{
				IEnumerable<IScriptEstAssembly> estAssemblyList = Injector.Get<IScriptEstAssemblyLogic>().GetAssemblyByRef(new EstAssemblySearchOption()
				{
					Code = code,
					AssemblyCatCode = assemblyCatCode,
					RootAssemblyCatCode = rootAssemblyCatCode,
					ProjectId = projectId,
					IncludeCharact = true,
					IncludeParameter = true,
					EnhanceFilterFunc = enhanceFilterFunc,
				});

				int count = estAssemblyList.Count();

				if (count <= 0)
				{
					throw new Exception(string.Format("Can not found assembly which code is [{0}]", code));
				}

				if (count > 1)
				{
					throw new Exception(string.Format("More than one assembly which code is [{0}]", code));
				}

				assemblyEntity = estAssemblyList.FirstOrDefault();

				_AssemblyTupleList.Add(new Tuple<string, string, string, IScriptEstAssembly>(code, assemblyCatCode, rootAssemblyCatCode, assemblyEntity));
			}
			else
			{
				assemblyEntity = assemblyTuple.Item4;
			}

			if (assemblyEntity != null)
			{
				var isReadOnly = this._EstJobHelper.CheckCurrentJobIsReadOnly((int)EstResourceType.Assembly, creationInfo.ProjectId ?? 0, creationInfo.JobId ?? 0, new Tuple<int, int?>(assemblyEntity.Id, assemblyEntity.EstHeaderFk));
				if (isReadOnly)
				{
					throw new EstRuleWarningException(LocalizationProps.Resources.Warn_Resouce_Job_ReadOnly_Msg);
				}
			}

			return this.CreateAssembly(assemblyEntity, creationInfo, false);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="code"></param>
		/// <param name="assemblyCode"></param>
		/// <param name="rootAssemblyCatCode"></param>
		/// <param name="projectId"></param>
		/// <param name="replaceResources"></param>
		/// <returns></returns>
		public IEnumerable<IScriptEstResource> CopyAssemblyResourcesByCode(IScriptEstLineItem entity, string code, string assemblyCode, string rootAssemblyCatCode, int? projectId, bool replaceResources = true)
		{
			/* filter by master data filter */
			Func<IScriptEstAssembly, bool> enhanceFilterFunc = null;

			if (this.GetEstRateBookLogic() != null)
			{
				enhanceFilterFunc = this.GetEstRateBookLogic().IsAssemblyInMasterFilter;
			}

			/* get assembly by code */
			IEnumerable<IScriptEstAssembly> estAssemblyList = Injector.Get<IScriptEstAssemblyLogic>().GetAssemblyByRef(new EstAssemblySearchOption()
			{
				Code = code,
				AssemblyCatCode = assemblyCode,
				RootAssemblyCatCode = rootAssemblyCatCode,
				ProjectId = projectId,
				IncludeCharact = true,
				IncludeParameter = true,
				EnhanceFilterFunc = enhanceFilterFunc
			});

			int count = estAssemblyList.Count();

			IScriptEstAssembly estAssembly = null;

			if (count <= 0)
			{
				throw new Exception(string.Format("Can not found the assembly which code is [{0}]", code));
			}

			if (count > 0)
			{
				estAssembly = estAssemblyList.FirstOrDefault();
			}

			/* copy information from assembly */
			EstimateContext.ScriptEstLineItemLogic.CopyPropertyFromAssembly(entity, estAssembly, true, true);

			CopyCostGroupOfAssemby(entity, estAssembly);

			/* copy resources from assembly */
			IEnumerable<IScriptEstResource> assemblyResourcesCopy = this._EstimateMainResourceLogic.GetResourceTreeForCopy(estAssembly.Id, estAssembly.EstHeaderFk, this._ProjectId, new ResourceCopyOption()
			{
				LineItemId = entity.Id,
				HeaderId = entity.EstHeaderFk,
				ParentResourceId = null,
				JobId = entity.LgmJobFk,
				IsResolveAssembly = true,
				IsSetProjectCurrency = true,
				SetResourceJobFk = false,
				IncludeMarkupAsCostUnit = false,
				ResourceCopyTo = ResourceModuleType.Estimate,
				UserDefinedColumnValCopyOption = new UserDefinedColumnValCopyOption()
				{
					IsCopyUserDefinedColumnVal = true
				},
				CharacteristicCopyOption = new CharacteristicCopyOption()
				{
					IsAutoAssignCharacteristic = true,
					IsCopyCharacteristic = true
				}
			});

			/* copy information from project costcode/material entity */
			this._EstLineItemUpdateFrmPrjLogic.UpdateRateFromProject(assemblyResourcesCopy, entity, true);

			/* attach resources to lineItem */
			if (replaceResources == true)
			{
				entity.Resources = assemblyResourcesCopy.ToList();
			}
			else
			{
				foreach (var resource in assemblyResourcesCopy)
				{
					entity.Resources.Add(resource);
				}
			}

			return assemblyResourcesCopy;
		}

		/// <summary>
		/// From WIC Boq Assembly,creates Assembly resource in target line item
		/// </summary>
		/// <param name="assemblyEntity"></param>
		/// <param name="creationInfo"></param>
		/// <param name="includeMarkupAsCostUnit"></param>
		/// <param name="isCreateForProjectAssembly"></param>
		/// <param name="doEquipAssemblyEditable"></param>
		/// <returns></returns>
		public EstResourceEntity CreateAssemblyResourceForWicBoq(EstLineItemEntity assemblyEntity, IEstResourceCreationInfo creationInfo, bool includeMarkupAsCostUnit = false, bool isCreateForProjectAssembly = false, bool? doEquipAssemblyEditable = null)
		{
			if (assemblyEntity == null)
			{
				return null;
			}

			/* create new resource */
			var resourceEntity = _EstimateMainResourceLogic.CreateNewResourceCore(creationInfo.EstLineItemId, creationInfo.EstHeaderId, creationInfo.ParentId);
			resourceEntity.EstResourceTypeFk = (int)EstResourceType.Assembly;

			resourceEntity.UserDefinedcolValEntity = assemblyEntity.UserDefinedcolValEntity;

			/* copy assembly information to resource */
			new ScriptEstResourceLogic().CopyPropertyFromLineItem(resourceEntity, assemblyEntity);

			var resourcesCopy = this._EstResourceCopyLogic.CopyResources(new List<EstResourceEntity>() { resourceEntity }, new ResourceCopyOption()
			{
				LineItemId = creationInfo.EstLineItemId,
				HeaderId = creationInfo.EstHeaderId,
				ParentResourceId = creationInfo.ParentId,
				JobId = creationInfo.JobId,
				SetResourceJobFk = false,
				IncludeMarkupAsCostUnit = includeMarkupAsCostUnit,
				IsResolveAssembly = this._IsResolveAssembly,
				IsSetProjectCurrency = this._IsSetProjectCurrency,
				IsResolvePlantAssembly = this._IsResolvePlantAssembly,
				ResourceCopyFrom = this._IsPlantAssembly ? ResourceModuleType.PlanAssembly : ResourceModuleType.MasterAssembly,
				ResourceCopyTo = this._IsResolveAssembly ? ResourceModuleType.Estimate : ResourceModuleType.MasterAssembly,
				UserDefinedColumnValCopyOption = new UserDefinedColumnValCopyOption()
				{
					IsCopyUserDefinedColumnVal = true,
					AutoGenerateId = true,
				},
				CharacteristicCopyOption = new CharacteristicCopyOption()
				{
					IsCopyCharacteristic = true,
					IsAutoAssignCharacteristic = true,
					AutoGenerateId = true
				},
				IsForWicBoq = true
			});

			var assemblyResource = resourcesCopy != null && resourcesCopy.Any() ? resourcesCopy.First() : null;

			// load cache data, to enhance performance the function: UpdateRateFromProject
			this.GetProjectScopeObject().LoadCacheDataInBulk(resourcesCopy);

			/* copy information from project cost code/material entity */
			this._EstLineItemUpdateFrmPrjLogic.UpdateRateFromProjectInTree(resourcesCopy, creationInfo.JobId, true, doEquipAssemblyEditable);

			/* enhance */
			if (creationInfo.AfterCreated != null)
			{
				creationInfo.AfterCreated(assemblyResource);
			}

			return assemblyResource;
		}

		private void CopyCostGroupOfAssemby(IScriptEstLineItem entity, IScriptEstAssembly estAssembly)
		{
			if (entity == null || estAssembly == null) { return; }

			var lineItem2CostGroupOfAssembly = new EstLineItem2CostGroupLogic().GetListByLineItemIds(new int[] { estAssembly.Id }, estAssembly.EstHeaderFk);

			if (lineItem2CostGroupOfAssembly == null || !lineItem2CostGroupOfAssembly.Any()) { return; }

			var costGroupIds = lineItem2CostGroupOfAssembly.Select(e => e.CostGroupFk).ToList();

			var costGroups = new CostGroupLogic().GetByFilter(e => costGroupIds.Contains(e.Id));

			entity.CostGroupList = costGroups.Select(e => e.Clone() as ICostGroupEntity).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="sourceLineItem"></param>
		/// <param name="targetLineItem"></param>
		/// <returns></returns>
		public IScriptEstLineItem CloneResourcesOfLineItem(IScriptEstLineItem sourceLineItem, IScriptEstLineItem targetLineItem)
		{
			/* copy resources from assembly */
			IEnumerable<IScriptEstResource> assemblyResourcesCopy = this._EstimateMainResourceLogic.GetResourceTreeForCopy(sourceLineItem.Id, sourceLineItem.EstHeaderFk, this._ProjectId, new ResourceCopyOption()
			{
				LineItemId = targetLineItem.Id,
				HeaderId = targetLineItem.EstHeaderFk,
				ParentResourceId = null,
				JobId = targetLineItem.LgmJobFk,
				IsResolveAssembly = true,
				IsSetProjectCurrency = true,
				SetResourceJobFk = false,
				IncludeMarkupAsCostUnit = false,
				ResourceCopyTo = ResourceModuleType.Estimate,
				UserDefinedColumnValCopyOption = new UserDefinedColumnValCopyOption()
				{
					IsCopyUserDefinedColumnVal = true
				},
				CharacteristicCopyOption = new CharacteristicCopyOption()
				{
					IsAutoAssignCharacteristic = true,
					IsCopyCharacteristic = true
				}
			});

			/* copy information from project costcode/material entity */
			this._EstLineItemUpdateFrmPrjLogic.UpdateRateFromProject(assemblyResourcesCopy, targetLineItem, true);

			if (assemblyResourcesCopy != null)
			{
				targetLineItem.Resources = assemblyResourcesCopy.ToList();
			}

			return targetLineItem;
		}

		/// <summary>
		///
		/// </summary>
		public void ClearNewOldResourceMapping()
		{
			this._EstResourceCopyLogic.ClearNewOldResourceMapping();
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public Dictionary<EstResourceEntity, EstResourceEntity> GetNewOldResourceMapping()
		{
			return this._EstResourceCopyLogic.GetNewOldResourceMapping();
		}

		private IEnumerable<EstResourceEntity> CreateAssemblies(IEnumerable<IScriptEstAssembly> assemblies, IEstResourceCreationInfo creationInfo, bool includeMarkupAsCostUnit = false, bool isCreateForProjectAssembly = false, bool IsCopyByDragDropSearchWizard = false, bool isTakeOverFromLineItem = false)
		{
			var result = new List<EstResourceEntity>();

			if (assemblies == null || !assemblies.Any())
			{
				return result;
			}

			int? parentIdForPlantAssembly = null;

			if (creationInfo?.ParentId != null)
			{
				parentIdForPlantAssembly = creationInfo.ParentId;
				creationInfo.ParentId = null; // Reset to null, since, initial code for other assembly resources it was null only 
			}

			foreach (var assembly in assemblies)
			{
				var newAssemblyResource = this.CreateAssembly(assembly, creationInfo, includeMarkupAsCostUnit, isCreateForProjectAssembly, IsCopyByDragDropSearchWizard: IsCopyByDragDropSearchWizard);

				if (newAssemblyResource == null)
				{
					continue;
				}

				/* copy information from project material entity, and exchange rate */
				this.Process(newAssemblyResource, creationInfo.JobId, true);

				result.Add(newAssemblyResource);
			}

			this.GetExchangeRateHelper().ExchangeRate(result);

			var showEquipmentAssembliesAsOneRecordSystemOption = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ShowEquipmentAssembliesAsOneRecord);

			if (showEquipmentAssembliesAsOneRecordSystemOption)
			{
				if (_EstimateMainResourceLogic.IsPlantPresentInAssembly(result))
				{
					result[0].EstResourceFk = parentIdForPlantAssembly;
				}
			}

			if (showEquipmentAssembliesAsOneRecordSystemOption && !isCreateForProjectAssembly && (isTakeOverFromLineItem || creationInfo.IsLineItemResource))
			{
				var estimateMainLineItemLogic = new EstimateMainLineItemLogic();
				if (_EstimateMainResourceLogic.IsPlantPresentInAssembly(result))
				{
					var estMainCompleteEntity = new EstMainCompleteEntity();

					if (!result.Any())
					{
						return new List<EstResourceEntity>();
					}

					var estResourceToSave = result.FlattenResources().Select(e => new EstResourceCompleteEntity() { EstResource = e as EstResourceEntity }).ToList();
					if (creationInfo.UnsavedResourceTree != null && creationInfo.UnsavedResourceTree.Any())
					{
						estResourceToSave.AddRange(creationInfo.UnsavedResourceTree.Select(e => new EstResourceCompleteEntity() { EstResource = e as EstResourceEntity }).ToList());
					}
					estMainCompleteEntity.EstResourceToSave = estResourceToSave;
					if (creationInfo.EstLineItem != null)
					{
						estMainCompleteEntity.EstLineItems = new List<EstLineItemEntity> { creationInfo.EstLineItem as EstLineItemEntity };
					}
					estMainCompleteEntity.ProjectId = creationInfo.ProjectId; estimateMainLineItemLogic.Update(estMainCompleteEntity);
					creationInfo.RefreshUnsavedData = true;
					var allResources = _EstimateMainResourceLogic.GetListByFilter(x => x.EstHeaderFk == creationInfo.EstHeaderId && x.EstLineItemFk == creationInfo.EstLineItemId).ToList();
					Func<IScriptEstResource, IEnumerable<IScriptEstResource>> getChildrenFunction = e => { return allResources.Where(i => i.EstResourceFk.HasValue && i.EstResourceFk.Value == e.Id).ToList(); };
					var assemblyIdsToFilter = result.Select(s => s.Id).ToHashSet();
					var estimateCostTotalCalculator = new EstimateCostTotalCalculator(creationInfo.EstHeaderId, creationInfo.ProjectId, null, false);
					var lineItem = creationInfo.EstLineItem != null ? creationInfo.EstLineItem as EstLineItemEntity : estimateMainLineItemLogic.GetLineItemByIdNHeader(creationInfo.EstLineItemId, creationInfo.EstHeaderId);
					estimateCostTotalCalculator.CalculateResources(lineItem, allResources.Where(x => x.EstResourceFk == null), getChildrenFunction);
					_EstimateMainResourceLogic.SaveResources(allResources);
					estimateMainLineItemLogic.Save(lineItem);
					var assemblyResources = allResources.Where(x => assemblyIdsToFilter.Contains(x.Id)).ToList();
					_EstimateMainResourceLogic.ShowPlantAssemblyAsOneRecord(assemblyResources, true);
					return assemblyResources;
				}
			}
			return result;
		}

		private IEnumerable<EstResourceEntity> CreateEquipmentAssemblies(IEnumerable<IScriptEstAssembly> assemblies, IEstResourceCreationInfo creationInfo, bool includeMarkupAsCostUnit = false, bool isCreateForProjectAssembly = false, bool isTakeOverFromLineItem = false, bool isTakeOverFromMasterAssembly = false, int? selectedItemId = null, int? sortNo = null)
		{
			var showEaAsOneRecordSystemOption = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ShowEquipmentAssembliesAsOneRecord);
			var plantEstimateSettingsLogic = Injector.Get<ILogisticPlantEstimateSettings>();
			var result = new List<EstResourceEntity>();
			string withoutPlantCodePrefix = "W/O Plant";
			var etmPlantLogic = RVPARB.BusinessEnvironment.GetExportedValue<IEquipmentPlantLogic>();
			creationInfo.JobId = creationInfo.JobId != null && creationInfo.JobId > 0 ? creationInfo.JobId : null;
			var parent = creationInfo.ParentId.HasValue
				? _EstimateMainResourceLogic.GetResourcesByIds(new List<int> { creationInfo.ParentId.Value }, new List<int> { creationInfo.EstLineItemId }, creationInfo.EstHeaderId).FirstOrDefault()
				  ?? creationInfo.UnsavedResourceTree.FirstOrDefault(x => x.Id == creationInfo.ParentId.Value && x.EstLineItemFk == creationInfo.EstLineItemId && x.EstHeaderFk == creationInfo.EstHeaderId) as EstResourceEntity
				: null;

			if (assemblies == null || !assemblies.Any())
			{
				return result;
			}

			var assemblyIds = assemblies.Select(x => x.Id).ToList();

			//master plant assemblies
			var masterAssemblies = assemblies.Where(x => x.EstHeaderAssemblyFk is null && x.EstAssemblyFk is null).ToList();

			// project plant assemblies//Todo: check PlantDissolved PD type needed?
			List<EstResourceEntity> allPlants = _EstimateMainResourceLogic.GetListByFilter(x => assemblyIds.Contains(x.EstLineItemFk)).ToList()
				.FlattenResources().Where(x => x.EstResourceTypeFk == (int)EstResourceType.Plant).Select(e => e as EstResourceEntity).ToList();

			foreach (var scriptEstResource in allPlants)
			{
				scriptEstResource.LgmJobFk = EstJobHelper.GetResourceJobId(scriptEstResource);
			}

			int? defaultWot = null;
			if (creationInfo.JobId.HasValue && !isTakeOverFromMasterAssembly)
			{
				defaultWot = plantEstimateSettingsLogic.DefaultPlantEstimateWorkOperationType(creationInfo.JobId.Value);
			}
			defaultWot ??= plantEstimateSettingsLogic.DefaultPlantEstimateWorkOperationType();

			bool isParentPlant = parent != null && (parent.EstResourceTypeFk == (int)EstResourceType.Plant || parent.EstResourceTypeFk == (int)EstResourceType.PlantDissolved);

			// get all sibling plant resources
			var allExistingResources = _EstimateMainResourceLogic.GetListByFilter(x =>
				x.EstResourceFk == creationInfo.ParentId && (x.EstResourceTypeFk == (int)EstResourceType.Plant || x.EstResourceTypeFk == (int)EstResourceType.PlantDissolved) &&
				x.EstLineItemFk == creationInfo.EstLineItemId && x.EstHeaderFk == creationInfo.EstHeaderId).ToList() ?? new List<EstResourceEntity>();
			var existingResourceIds = allExistingResources.Select(x => x.Id);
			var unsavedSiblings = new List<EstResourceEntity>();
			if (creationInfo.UnsavedResourceTree != null)
			{
				unsavedSiblings = creationInfo.UnsavedResourceTree.Where(x => x.EstResourceFk == creationInfo.ParentId && !existingResourceIds.Contains(x.Id)).Select(s => s as EstResourceEntity).ToList();
				if (unsavedSiblings.Any())
				{
					allExistingResources.AddRange(unsavedSiblings);
				}
			}

			if (isParentPlant)
			{
				//get and add parent plant resources if parent is plant
				var parentResources = _EstimateMainResourceLogic.GetListByFilter(x =>
					x.EstResourceFk == parent.EstResourceFk && (x.EstResourceTypeFk == (int)EstResourceType.Plant || x.EstResourceTypeFk == (int)EstResourceType.PlantDissolved) &&
					x.EstLineItemFk == creationInfo.EstLineItemId && x.EstHeaderFk == creationInfo.EstHeaderId).ToList();
				allExistingResources.AddRange(parentResources);
			}

			List<EstResourceEntity> existingResources = [];
			foreach (var existingResource in allExistingResources)
			{
				var projectResource = allPlants.FirstOrDefault(x => x.LgmJobFk == existingResource.LgmJobFk && x.WorkOperationTypeFk == existingResource.WorkOperationTypeFk && x.EtmPlantFk == existingResource.EtmPlantFk);
				if (projectResource != null)
				{
					existingResources.Add(existingResource);
				}
				var masterResource = masterAssemblies.FirstOrDefault(x => x.PlantFk == existingResource.EtmPlantFk);
				if (masterResource != null && existingResource.WorkOperationTypeFk == defaultWot && existingResource.LgmJobFk == creationInfo.JobId)
				{
					existingResources.Add(existingResource);
				}
			}

			var assembly2PlantMapping = new Dictionary<IScriptEstAssembly, EstResourceEntity>();
			var plantList = new List<EstResourceEntity>();
			//var existingResourceEtmPlantFk = existingResources.Select(s => s.EtmPlantFk).ToHashSet();

			var plantToCreateFromProject = new List<EstResourceEntity>();

			foreach (var item in allPlants)
			{
				bool existsInResources = existingResources.Any(res => res.EtmPlantFk == item.EtmPlantFk && res.WorkOperationTypeFk == item.WorkOperationTypeFk);

				bool existsInPlantToCreate = plantToCreateFromProject.Any(p => p.EtmPlantFk == item.EtmPlantFk && p.WorkOperationTypeFk == item.WorkOperationTypeFk);

				if (!existsInResources && !existsInPlantToCreate && item.EtmPlantFk.HasValue)
				{
					plantToCreateFromProject.Add(item);
				}
			}

			if (isTakeOverFromLineItem)
			{
				plantToCreateFromProject = plantToCreateFromProject.Concat(allPlants.Where(x => x.EtmPlantFk.HasValue).ToList()).ToList();
			}

			var plantToCreateFromMaster = new List<IScriptEstAssembly>();
			foreach (var item in masterAssemblies)
			{
				bool existsInResources = existingResources.Any(res => res.EtmPlantFk == item.PlantFk && res.WorkOperationTypeFk == defaultWot);

				if (!existsInResources && item.PlantFk.HasValue)
				{
					plantToCreateFromMaster.Add(item);
				}
			}

			plantToCreateFromMaster = plantToCreateFromMaster.Distinct().ToList();

			//create plant resources (root) from project and master assemblies
			if (plantToCreateFromProject.Any() || plantToCreateFromMaster.Any())
			{
				var projectEtmPlantIds = plantToCreateFromProject.Where(x => x.EtmPlantFk.HasValue).Select(s => s.EtmPlantFk.Value);
				var projectEtmPlants = etmPlantLogic.GetPlantByIds(projectEtmPlantIds.Select(x => new Platform.Core.IdentificationData() { Id = x }).Distinct().ToList()).ToList();

				var masterEtmPlantIds = plantToCreateFromMaster.Where(x => x.PlantFk.HasValue).Select(s => s.PlantFk.Value);
				var masterEtmPlants = etmPlantLogic.GetPlantByIds(masterEtmPlantIds.Select(x => new Platform.Core.IdentificationData() { Id = x }).Distinct().ToList()).ToList();

				var count = masterEtmPlants.Count() + plantToCreateFromProject.Count();

				var resourceIds = showEaAsOneRecordSystemOption && count > 0 && selectedItemId.HasValue && selectedItemId > 0
					? new Stack<int>((count > 1
							? new[] { selectedItemId.Value }.Concat(this._EstimateMainResourceLogic.SequenceManager.GetNextList("EST_RESOURCE", count - 1))
							: new[] { selectedItemId.Value }).Reverse())
					: new Stack<int>(this._EstimateMainResourceLogic.SequenceManager
						.GetNextList("EST_RESOURCE", count)
						.Reverse());

				foreach (var item in plantToCreateFromProject)
				{
					var projectEtmPlant = projectEtmPlants.FirstOrDefault(x => x.Id == item.EtmPlantFk);
					if (projectEtmPlant == null) { continue; }

					var parentId = parent != null && isParentPlant ? item.WorkOperationTypeFk == parent.WorkOperationTypeFk && projectEtmPlant.Id == parent.EtmPlantFk ? parent.Id : parent.EstResourceFk : creationInfo.ParentId;
					var plant = _EstimateMainResourceLogic.CreateNewResourceCore(creationInfo.EstLineItemId, creationInfo.EstHeaderId, parentId);

					plant.Id = resourceIds.Pop();
					plant.Code = projectEtmPlant.Code;
					plant.DescriptionInfo = projectEtmPlant.DescriptionInfo;
					plant.EtmPlantFk = projectEtmPlant.Id;
					plant.PlantFk = projectEtmPlant.Id; // todo - remove from other places (JS)
					plant.EstResourceFk = parentId;
					plant.EstResourceTypeFk = (int)EstResourceType.Plant;
					plant.WorkOperationTypeFk = item.WorkOperationTypeFk ?? defaultWot;
					if (creationInfo.JobId != null)
					{
						plant.LgmJobFk = item.LgmJobFk ?? creationInfo.JobId.Value;
					}
					plantList.Add(plant);
				}
				foreach (var masterEtmPlant in masterEtmPlants)
				{
					var parentId = parent != null && isParentPlant ? masterEtmPlant.Id == parent.EtmPlantFk ? parent.Id : parent.EstResourceFk : creationInfo.ParentId;
					var plant = _EstimateMainResourceLogic.CreateNewResourceCore(creationInfo.EstLineItemId, creationInfo.EstHeaderId, parentId);

					plant.Id = resourceIds.Pop();
					plant.Code = masterEtmPlant.Code;
					plant.DescriptionInfo = masterEtmPlant.DescriptionInfo;
					plant.EtmPlantFk = masterEtmPlant.Id;
					plant.PlantFk = masterEtmPlant.Id; // todo - remove from other places (JS)
					plant.EstResourceFk = parentId;
					plant.EstResourceTypeFk = (int)EstResourceType.Plant;
					plant.WorkOperationTypeFk = defaultWot;
					if (creationInfo.JobId != null)
					{
						plant.LgmJobFk = creationInfo.JobId.Value;
					}
					plantList.Add(plant);
				}
			}

			// create plant resource for assemblies without plant (root)
			var withoutPlantAssemblies = assemblies.Where(x => x.PlantFk == null).ToList();
			if (withoutPlantAssemblies.Any() && !isTakeOverFromMasterAssembly)
			{
				var resourceIds = new Stack<int>(this._EstimateMainResourceLogic.SequenceManager.GetNextList("EST_RESOURCE", withoutPlantAssemblies.Count()).Reverse());
				foreach (var withoutPlantAssembly in withoutPlantAssemblies)
				{
					var withoutPlantCodeName = string.Join(" - ", withoutPlantCodePrefix, withoutPlantAssembly.Code);
					bool exists = existingResources.Any(x => x.PlantFk == null && x.Code.Contains(withoutPlantCodeName));
					if (!exists)
					{
						var plant = _EstimateMainResourceLogic.CreateNewResourceCore(creationInfo.EstLineItemId, creationInfo.EstHeaderId, creationInfo.ParentId);
						plant.Id = resourceIds.Pop();
						plant.Code = withoutPlantCodeName;
						plant.DescriptionInfo = new DescriptionTranslateType(withoutPlantCodeName, withoutPlantCodeName);
						plant.EtmPlantFk = null;
						plant.PlantFk = null;
						plant.EstResourceFk = creationInfo.ParentId;
						plant.EstResourceTypeFk = (int)EstResourceType.Plant;
						plant.WorkOperationTypeFk = null;
						if (creationInfo.JobId != null)
						{
							plant.LgmJobFk = creationInfo.JobId.Value;
						}
						plantList.Add(plant);
					}
				}

			}

			// combined plants (existing and new)
			var combinedPlants = plantList.Concat(existingResources);

			foreach (var item in assemblies)
			{
				if (combinedPlants.Any(x => x.EtmPlantFk == item.PlantFk))
				{
					var plant = allPlants.FirstOrDefault(x => x.EstLineItemFk == item.Id && x.EtmPlantFk == item.PlantFk);
					var workOperationType = plant != null ? plant.WorkOperationTypeFk : defaultWot;
					var parentPlant = combinedPlants.FirstOrDefault(x => x.EtmPlantFk == item.PlantFk && x.WorkOperationTypeFk == workOperationType && x.EstResourceFk == parent?.Id)
											?? combinedPlants.FirstOrDefault(x => x.EtmPlantFk == item.PlantFk && x.WorkOperationTypeFk == workOperationType && x.EstResourceFk == parent?.EstResourceFk);
					//for without plant
					parentPlant = parentPlant ?? combinedPlants.FirstOrDefault(x => x.EtmPlantFk == item.PlantFk && x.WorkOperationTypeFk == null && x.EstResourceFk == parent?.Id && string.Join(" - ", withoutPlantCodePrefix, item.Code) == x.Code);
					assembly2PlantMapping.Add(item, parentPlant);
				}
			}

			foreach (var assembly in assemblies)
			{
				if (assembly2PlantMapping.TryGetValue(assembly, out var plantResource))
				{
					if (plantResource != null)
					{
						var resourceCopyOption = new ResourceCopyOption()
						{
							LineItemId = creationInfo.EstLineItemId,
							HeaderId = creationInfo.EstHeaderId,
							ParentResourceId = creationInfo.ParentId,
							JobId = creationInfo.JobId,
							SetResourceJobFk = false,
							IncludeMarkupAsCostUnit = includeMarkupAsCostUnit,
							IsResolveAssembly = this._IsResolveAssembly,
							IsSetProjectCurrency = this._IsSetProjectCurrency,
							IsResolvePlantAssembly = this._IsResolvePlantAssembly,
							ResourceCopyFrom = this._IsPlantAssembly ? ResourceModuleType.PlanAssembly : ResourceModuleType.MasterAssembly,
							ResourceCopyTo = this._IsResolveAssembly ? ResourceModuleType.Estimate : ResourceModuleType.MasterAssembly,
							isCreateForProjectAssembly = isCreateForProjectAssembly,
							WorkOperationTypeFk = plantResource.WorkOperationTypeFk,
							UserDefinedColumnValCopyOption = new UserDefinedColumnValCopyOption() { IsCopyUserDefinedColumnVal = true, AutoGenerateId = true, },
							CharacteristicCopyOption = new CharacteristicCopyOption() { IsCopyCharacteristic = true, IsAutoAssignCharacteristic = true, AutoGenerateId = true }
						};
						var childrenResources = _EstimateMainResourceLogic.GetListByFilter(x => x.EstResourceFk == plantResource.Id && x.EstResourceTypeFk == (int)EstResourceType.EquipmentAssembly &&
																														x.EstLineItemFk == creationInfo.EstLineItemId && x.EstHeaderFk == creationInfo.EstHeaderId).ToList();

						var matchingChild = childrenResources.FirstOrDefault(child => child != null && child.Code == assembly.Code);
						if (matchingChild != null)
						{
							continue;
						}

						var newAssemblyResource = this.CreateAssembly(assembly, creationInfo, includeMarkupAsCostUnit, isCreateForProjectAssembly, plantResource.WorkOperationTypeFk, false, resourceCopyOption);
						var otherEquipmentAssembliesInPlant = _EstResourceCopyLogic.GetOtherEquipmentAssemblyResourcesForTakeover(true, assembly, false, creationInfo, resourceCopyOption);

						if (newAssemblyResource == null)
						{
							continue;
						}

						var resources = new List<EstResourceEntity>();
						resources.Add(newAssemblyResource);
						resources.AddRange(otherEquipmentAssembliesInPlant);
						/* copy information from project material entity, and exchange rate */
						foreach (var estResourceEntity in resources)
						{
							this.Process(estResourceEntity, creationInfo.JobId, true);
							estResourceEntity.EstResourceFk = plantResource?.Id;
						}

						if (plantResource != null)
						{
							if (plantResource.Version == 0 && !unsavedSiblings.Select(x=>x.Id).Contains(plantResource.Id))
							{
								plantResource.Resources ??= new List<IScriptEstResource>();
								plantResource.ResourceChildren ??= new List<EstResourceEntity>();

								var existingPlantResource = result.FirstOrDefault(x => x.Id == plantResource.Id && x.EtmPlantFk == plantResource.EtmPlantFk);
								if (existingPlantResource != null)
								{
									existingPlantResource.Resources ??= new List<IScriptEstResource>();
									existingPlantResource.ResourceChildren ??= new List<EstResourceEntity>();

									foreach (var resource in resources) { existingPlantResource.Resources.Add(resource); }

									foreach (var resource in resources) { existingPlantResource.ResourceChildren.Add(resource); }
								}
								else
								{
									foreach (var resource in resources) { plantResource.Resources.Add(resource); }

									foreach (var resource in resources) { plantResource.ResourceChildren.Add(resource); }

									result.Add(plantResource);
								}
							}
							else
							{
								result.AddRange(resources);
							}
						}
						else
						{
							result.AddRange(resources);
						}
					}
				}
			}

			result.Where(resource => resource != null).ToList().ForEach(resource => CorrectResourceFactors(resource));

			foreach (var item in result) { UpdateMultipliersFrmPlantEstimate(item); }

			var resourcesIdToEstResourceFkMapping = result.ToDictionary(r => r.Id, r => r.EstResourceFk);

			var estimateCostTotalCalculator = new EstimateCostTotalCalculator(creationInfo.EstHeaderId, creationInfo.ProjectId, null, false);

			var allPlantResources = result.FlattenResources().Select(e => e as EstResourceEntity).ToList();

			Func<IScriptEstResource, IEnumerable<IScriptEstResource>> getChildrenFunc = e => { return allPlantResources.Where(i => i.EstResourceFk.HasValue && i.EstResourceFk.Value == e.Id).ToList(); };

			var lineItem = creationInfo.EstLineItem != null ? creationInfo.EstLineItem as EstLineItemEntity : new EstimateMainLineItemLogic().GetLineItemByIdNHeader(creationInfo.EstLineItemId, creationInfo.EstHeaderId);

			foreach (var item in result) { item.EstResourceFk = null; }

			estimateCostTotalCalculator.CalculateResources(lineItem, result, getChildrenFunc);

			foreach (var item in result)
			{
				if (resourcesIdToEstResourceFkMapping.TryGetValue(item.Id, out var estResourceFk)) { item.EstResourceFk = estResourceFk; }
			}

			this.GetExchangeRateHelper().ExchangeRate(result);

			if (showEaAsOneRecordSystemOption && !isCreateForProjectAssembly && (isTakeOverFromLineItem || creationInfo.IsLineItemResource))
			{
				var estimateMainLineItemLogic = new EstimateMainLineItemLogic();
				var estMainCompleteEntity = new EstMainCompleteEntity();

				if (!result.Any())
				{
					return new List<EstResourceEntity>();
				}
				/* Sort */
				EstResourceEntity selectedItem = null;

				if (sortNo.HasValue)
				{
					result.FirstOrDefault().Sorting = sortNo.Value;
				}
				foreach (var targetRes in result)
				{
					SortingGenerator.SetSorting(selectedItem, targetRes, result.FlattenResources(), true);
					selectedItem = targetRes;
				}

				var estResourceToSave = creationInfo.UnsavedResourceTree.Select(e => new EstResourceCompleteEntity() { EstResource = e as EstResourceEntity }).ToList();
				estResourceToSave.AddRange(result.FlattenResources().Select(e => new EstResourceCompleteEntity() { EstResource = e as EstResourceEntity }).ToList());
				estMainCompleteEntity.EstResourceToSave = estResourceToSave;
				if (creationInfo.EstLineItem != null)
				{
					estMainCompleteEntity.EstLineItems = new List<EstLineItemEntity> { creationInfo.EstLineItem as EstLineItemEntity };
				}
				estMainCompleteEntity.ProjectId = creationInfo.ProjectId;
				estimateMainLineItemLogic.Update(estMainCompleteEntity);
				creationInfo.RefreshUnsavedData = true;
				var plantIds = result.Where(x => x.EstResourceTypeFk == (int)EstResourceType.EquipmentAssembly).Select(x => x.EstResourceFk).Distinct().ToHashSet();
				if (plantIds.Any())
				{
				   lineItem = creationInfo.EstLineItem != null ? creationInfo.EstLineItem as EstLineItemEntity : new EstimateMainLineItemLogic().GetLineItemByIdNHeader(creationInfo.EstLineItemId, creationInfo.EstHeaderId);

					var parentPlants = _EstimateMainResourceLogic.GetListByFilter(x => plantIds.Contains(x.Id) && x.EstResourceTypeFk == (int)EstResourceType.Plant && x.EstLineItemFk == creationInfo.EstLineItemId && x.EstHeaderFk == creationInfo.EstHeaderId).ToList();
					result.AddRange(parentPlants);
					var allResources = _EstimateMainResourceLogic.GetListByFilter(x => x.EstHeaderFk == creationInfo.EstHeaderId && x.EstLineItemFk == creationInfo.EstLineItemId).ToList();
					Func<IScriptEstResource, IEnumerable<IScriptEstResource>> getChildrenFunction = e => { return allResources.Where(i => i.EstResourceFk.HasValue && i.EstResourceFk.Value == e.Id).ToList(); };
					var plantIdsToFilter = result.Where(x => x.EstResourceTypeFk == (int)EstResourceType.Plant).Select(s => s.Id).ToHashSet();
					estimateCostTotalCalculator.CalculateResources(lineItem, allResources.Where(x => x.EstResourceFk == null), getChildrenFunction);
					_EstimateMainResourceLogic.SaveResources(allResources);
					estimateMainLineItemLogic.Save(lineItem);
					var plants = allResources.Where(x => plantIdsToFilter.Contains(x.Id)).ToList();
					foreach (var plant in plants)
					{
						plant.ResourceChildren = null;
						plant.Resources = null;
					}
					return plants;
				}
				foreach (var plant in result)
				{
					plant.ResourceChildren = null;
					plant.Resources = null;
				}
				return result;
			}
			return result;
		}

		private void UpdateMultipliersFrmPlantEstimate(EstResourceEntity resource)
		{
			if (resource is null || (resource.EstResourceTypeFk != (int)EstResourceType.Plant && resource.EstResourceTypeFk != (int)EstResourceType.PlantDissolved))
			{
				return;
			}

			if (resource.ResourceChildren != null)
			{
				foreach (var resChild in resource.ResourceChildren)
				{
					if (resChild.EstResourceTypeFk == (int)EstResourceType.Plant || resChild.EstResourceTypeFk == (int)EstResourceType.PlantDissolved)
					{
						UpdateMultipliersFrmPlantEstimate(resChild);
					}
					else if (resChild.EstResourceTypeFk == (int)EstResourceType.EquipmentAssembly)
					{
						var currentJobId = resource.LgmJobFk ?? _EstJobHelper.GetProjectJobId();

						/* QuantityFactor1 by Logistic Job and Work Operation Type */
						new PlantAssemblyUpdateManager(this._ProjectId, resource.EstHeaderFk, null).UpdateMultipliersFrmPlantEstimate(resChild, currentJobId);
					}
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="assemblyIds"></param>
		/// <param name="creationInfo"></param>
		/// <param name="includeMarkupAsCostUnit"></param>
		/// <param name="isCreateForProjectAssembly"></param>
		/// <param name="plantAssemblyMapping"></param>
		/// <param name="isTakeOverFromLineItem"></param>
		/// <param name="isTakeOverFromMasterAssembly"></param>
		/// <param name="selectedItemId"></param>
		/// <param name="sortNo"></param>
		/// <returns></returns>
		public IEnumerable<EstResourceEntity> CreatePlantAssemblyByIds(IEnumerable<int> assemblyIds, IEstResourceCreationInfo creationInfo, bool includeMarkupAsCostUnit = false, bool isCreateForProjectAssembly = false, Dictionary<int, int> plantAssemblyMapping = null, bool isTakeOverFromLineItem = false, bool isTakeOverFromMasterAssembly = false, int? selectedItemId = null, int? sortNo = null)
		{
			var result = new List<EstResourceEntity>();

			if (assemblyIds == null || !assemblyIds.Any())
			{
				return result;
			}

			this._IsPlantAssembly = true;

			var assemblies = new EstimateAssembliesLogic().GetListByIds(assemblyIds.ToArray(), (int)CommonLogic.LineItemTypes.PlantAssembly).ToList();

			// TODO improvements to be done if plantAssemblyMapping is null
			GetPlantFkFromAssembly(assemblies, plantAssemblyMapping);

			return CreateEquipmentAssemblies(assemblies, creationInfo, includeMarkupAsCostUnit, isCreateForProjectAssembly, isTakeOverFromLineItem, isTakeOverFromMasterAssembly, selectedItemId, sortNo);
		}

		private void GetPlantFkFromAssembly(IEnumerable<Assemblies.BusinessComponents.EstLineItemEntity> assemblies, Dictionary<Int32, Int32> plantAssemblyMapping = null)
		{
			var plant2EstimatePriceListLineItemLogic = RVPARB.BusinessEnvironment.GetExportedValue<IPlant2EstimatePriceListLineItemLogic>();

			var missingMappings = new Dictionary<int, List<int>>();
			var plantEstimateInformationList = new List<PlantEstimateInformation>();
			plantAssemblyMapping = plantAssemblyMapping ?? new Dictionary<int, int>();
			plantAssemblyMapping = plantAssemblyMapping.Where(x => x.Value > 0).ToDictionary();
			foreach (var item in assemblies)
			{
				if (plantAssemblyMapping.TryGetValue(item.Id, out int plantId))
				{
					item.PlantFk = plantId;
				}
				else
				{

					if (missingMappings.TryGetValue(item.EstHeaderFk, out var values))
					{
						values.Add(item.Id);
					}
					else
					{
						missingMappings[item.EstHeaderFk] = new List<int> { item.Id };
					}

				}
			}
			if (missingMappings.Any())
			{
				foreach (var item in missingMappings)
				{
					var estimate2PlantMapping = plant2EstimatePriceListLineItemLogic.GetEstimate2PlantMapping(item.Key, item.Value);
					plantEstimateInformationList.AddRange(estimate2PlantMapping);
				}
				var fetchedMappings = plantEstimateInformationList.ToDictionary(x => new { x.EstimateHeaderFk, x.EstimateLineItemFk }, x => x.PlantFk);

				if (fetchedMappings.Any())
				{
					foreach (var item in assemblies)
					{
						if (item.PlantFk == null)
						{
							if (fetchedMappings.TryGetValue(new { EstimateHeaderFk = item.EstHeaderFk, EstimateLineItemFk = item.Id }, out int plantId))
							{
								item.PlantFk = plantId;
							}
						}
					}
				}
			}
		}
	}
}
