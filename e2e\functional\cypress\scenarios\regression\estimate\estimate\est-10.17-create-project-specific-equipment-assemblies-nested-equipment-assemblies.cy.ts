import { _common, _estimatePage, _validate, _mainView, _boqPage, _bidPage, _saleContractPage, _modalView, _salesPage, _wipPage, _package, _projectPage } from "cypress/pages";
import { app, tile, cnt, btn, sidebar, commonLocators } from "cypress/locators";
import { DataCells } from "cypress/pages/interfaces";
import CommonLocators from "cypress/locators/common-locators";
import { EST_HEADER } from "cypress/pages/variables";

const allure = Cypress.Allure.reporter.getInterface();
const ESTIMATE_CODE = "1" + Cypress._.random(0, 999);
const ESTIMATE_DESCRIPTION = "EST-DESC-" + Cypress._.random(0, 999);
const LINE_ITEM_DESCRIPTION = "LINE-ITEM-DESC-" + Cypress._.random(0, 999);
const PROJECT_NO = _common.generateRandomString(3)
const PROJECT_DESC = "PRDESC-" + Cypress._.random(0, 999);
const CLERK = "HS"

let ESTIMATE_PARAMETERS: DataCells,
  LINE_ITEM_PARAMETERS: DataCells,
  RESOURCE_PARAMETERS: DataCells,
  PROJECTS_PARAMETERS: DataCells,
  RESOURCE_PLANT_MASTER_PARAMETER: DataCells,
  UPDATE_ESTIMATE_PARAMETER: DataCells


let CONTAINERS_ESTIMATE,
  CONTAINERS_LINE_ITEM,
  CONTAINER_COLUMNS_ESTIMATE,
  CONTAINERS_RESOURCE,
  CONTAINER_COLUMNS_RESOURCE,
  CONTAINER_COLUMNS_LINE_ITEM,
  CONTAINERS_DATA_RECORDS,
  CONTAINERS_LOGISTIC_JOBS,
  CONTAINER_COLUMNS_LOGISTICS_JOBS,
  CONTAINER_COLUMNS_DATA_RECORDS,
  CONTAINERS_EQUIPMENT_ASSEMBLY_RESOURCE,
  MODAL_UPDATE_ESTIMATE_WIZARD


allure.epic("ESTIMATE");
allure.feature("Line Item");
allure.story("EST- 10.17 | Create Project Specific Equipment Assemblies nested equipment assemblies");

describe("EST- 10.17 | Create Project Specific Equipment Assemblies nested equipment assemblies", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  before(function () {
    cy.fixture("estimate/est-10.17-create-project-specific-equipment-assemblies-nested-equipment-assemblies.json")
      .then((data) => {
        this.data = data;

        CONTAINERS_ESTIMATE = this.data.CONTAINERS.ESTIMATE;
        CONTAINER_COLUMNS_ESTIMATE = this.data.CONTAINER_COLUMNS.ESTIMATE;
        CONTAINERS_LINE_ITEM = this.data.CONTAINERS.LINE_ITEM;
        CONTAINER_COLUMNS_LINE_ITEM = this.data.CONTAINER_COLUMNS.LINE_ITEM;
        CONTAINERS_RESOURCE = this.data.CONTAINERS.RESOURCE;
        CONTAINER_COLUMNS_RESOURCE = this.data.CONTAINER_COLUMNS.RESOURCE;
        CONTAINERS_LOGISTIC_JOBS = this.data.CONTAINERS.LOGISTIC_JOBS;
        CONTAINER_COLUMNS_LOGISTICS_JOBS = this.data.CONTAINER_COLUMNS.LOGISTICS_JOBS;
        CONTAINER_COLUMNS_DATA_RECORDS = this.data.CONTAINER_COLUMNS.DATA_RECORDS;
        CONTAINERS_DATA_RECORDS = this.data.CONTAINERS.DATA_RECORDS
        CONTAINERS_EQUIPMENT_ASSEMBLY_RESOURCE = this.data.CONTAINERS.EQUIPMENT_ASSEMBLY_RESOURCE
        MODAL_UPDATE_ESTIMATE_WIZARD = this.data.MODAL.UPDATE_ESTIMATE_WIZARD

        PROJECTS_PARAMETERS = {
          [commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
          [commonLocators.CommonLabels.NAME]: PROJECT_DESC,
          [commonLocators.CommonLabels.CLERK]: CLERK
        }
        ESTIMATE_PARAMETERS = {
          [app.GridCells.CODE]: ESTIMATE_CODE,
          [app.GridCells.DESCRIPTION_INFO]: ESTIMATE_DESCRIPTION,
          [app.GridCells.RUBRIC_CATEGORY_FK]: CONTAINERS_ESTIMATE.RUBRIC_CATEGORY,
          [app.GridCells.EST_TYPE_FK]: CONTAINERS_ESTIMATE.ESTIMATE_TYPE,
        };
        LINE_ITEM_PARAMETERS = {
          [app.GridCells.DESCRIPTION_INFO]: LINE_ITEM_DESCRIPTION,
          [app.GridCells.QUANTITY_SMALL]: CONTAINERS_LINE_ITEM.QUANTITY,
          [app.GridCells.BAS_UOM_FK]: CONTAINERS_LINE_ITEM.UOM,
        };

        RESOURCE_PARAMETERS = {
          [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY,
        }
        RESOURCE_PLANT_MASTER_PARAMETER = {
          [commonLocators.CommonKeys.CODE]: CONTAINERS_RESOURCE.CODE
        }
        UPDATE_ESTIMATE_PARAMETER = {
          [commonLocators.CommonKeys.CHECKBOX]: MODAL_UPDATE_ESTIMATE_WIZARD
        }
      })
      .then(() => {
        cy.preLoading(Cypress.env("adminUserName"), Cypress.env("adminPassword"), Cypress.env("parentCompanyName"), Cypress.env("childCompanyName"));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear();

      });
  });

  it("TC - Precondition : system option 10115 should be false", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
    _common.waitForLoaderToDisappear()
    cy.wait(2000) //required wait to open sidebar
    _common.openTab(app.TabBar.MASTER_DATA).then(() => {
      _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES, 0);
    });
    _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
    _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CommonLocators.CommonLabels.SYSTEM_OPTION)
    _common.clickOn_cellHasUniqueValue(cnt.uuid.DATA_TYPES, app.GridCells.NAME, CommonLocators.CommonLabels.SYSTEM_OPTION)
    _common.openTab(app.TabBar.MASTER_DATA).then(() => {
      _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORD, 0);
      _common.setup_gridLayout(cnt.uuid.DATA_RECORDS, CONTAINER_COLUMNS_DATA_RECORDS);
    });
    _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
    _common.search_inSubContainer(cnt.uuid.DATA_RECORDS, CONTAINERS_DATA_RECORDS.SYSTEM_OPTION_10115)
    _common.waitForLoaderToDisappear()
    _common.select_rowHasValue(cnt.uuid.DATA_RECORDS, CONTAINERS_DATA_RECORDS.SYSTEM_OPTION_10115)
    _common.edit_containerCell(cnt.uuid.DATA_RECORDS, app.GridCells.PARAMETER_VALUE, app.InputFields.DOMAIN_TYPE_COMMENT, "False")
    cy.SAVE()
    _common.waitForLoaderToDisappear()
  });

  it("TC - Create new project", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
    _common.openTab(app.TabBar.PROJECT).then(() => {
      _common.setDefaultView(app.TabBar.PROJECT)
      _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0)
    });
    _common.create_newRecord(cnt.uuid.PROJECTS)
    _projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
    _common.waitForLoaderToDisappear();
  })

  it("TC - Precondition : Logistic Job should have assigned with Price condition, Calender, Plant estimate price list fileds.", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
    _common.waitForLoaderToDisappear()
    cy.wait(1000)//required wait to open sidebar
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO)
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
      _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS);
      _common.clear_subContainerFilter(cnt.uuid.JOBS)
      _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_LOGISTICS_JOBS)
      _common.waitForLoaderToDisappear()
      _common.select_rowHasValue(cnt.uuid.JOBS, PROJECT_NO)
      _common.waitForLoaderToDisappear()
      _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.CALENDAR_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_LOGISTIC_JOBS.CALENDER_CODE)
      _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PRICE_CONDITION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_LOGISTIC_JOBS.PRICE_CONDITION)
      _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.PLANT_ESTIMATE_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, CONTAINERS_LOGISTIC_JOBS.PLANT_ESTIMATE_PRICELIST)
      _common.waitForLoaderToDisappear()
      cy.SAVE()
      _common.waitForLoaderToDisappear()
    });
  });


  it("TC - Create new estimate", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
    cy.wait(2000) //required wait to open sidebar
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO)
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.setDefaultView(app.TabBar.ESTIMATE);
      _common.waitForLoaderToDisappear();
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE, CONTAINER_COLUMNS_ESTIMATE);
      _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
      _common.create_newRecord(cnt.uuid.ESTIMATE);
      _estimatePage.enterRecord_toCreateEstimate(cnt.uuid.ESTIMATE, ESTIMATE_PARAMETERS);
      _common.waitForLoaderToDisappear();
      cy.SAVE();
      _common.waitForLoaderToDisappear();
      _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
      _common.waitForLoaderToDisappear();
    });
  });

  it("TC - Create new line item with quantity", function () {
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.setDefaultView(app.TabBar.ESTIMATELINEITEM);
      _common.waitForLoaderToDisappear();
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS, CONTAINER_COLUMNS_LINE_ITEM);
      _common.maximizeContainer(cnt.uuid.ESTIMATE_LINEITEMS);
      _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
      _common.create_newRecord(cnt.uuid.ESTIMATE_LINEITEMS);
      _estimatePage.enterRecord_toCreateLineItem(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_PARAMETERS);
      _common.waitForLoaderToDisappear();
      cy.SAVE();
      _common.waitForLoaderToDisappear();
      _common.minimizeContainer(cnt.uuid.ESTIMATE_LINEITEMS);
    });
  });

  it("TC - Assign resource to the line item", function () {
    _common.waitForLoaderToDisappear();
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
      _common.setup_gridLayout(cnt.uuid.RESOURCES, CONTAINER_COLUMNS_RESOURCE);
      _common.maximizeContainer(cnt.uuid.RESOURCES);
      _common.clear_subContainerFilter(cnt.uuid.RESOURCES);
      _common.create_newRecord(cnt.uuid.RESOURCES);
      _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS);
      _common.clickOn_activeRowCell(cnt.uuid.RESOURCES, app.GridCells.CODE)
      _estimatePage.assign_plantGroup_fromLookUpModal(cnt.uuid.RESOURCES, RESOURCE_PLANT_MASTER_PARAMETER)
      _common.waitForLoaderToDisappear();
      cy.SAVE();
      _common.waitForLoaderToDisappear();
    });
  });

  it("TC - Equipment assembly record should be added in the project module", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO)
    _common.openTab(app.TabBar.PROJECT).then(() => {
      _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_6)
      _common.select_tabFromFooter(cnt.uuid.PROJECT_PLANT_ASSEMBLIES, app.FooterTab.EQUIPMENT_ASSEMBLIES, 1)
      _common.clickOn_cellHasValue(cnt.uuid.PROJECT_PLANT_ASSEMBLIES, app.GridCells.CODE, CONTAINERS_RESOURCE.CODE)
      _common.select_tabFromFooter(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, app.FooterTab.EQUIPMENT_ASSEMBLY_RESOURCES, 2)
      _common.clickOn_cellHasValue(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, app.GridCells.CODE, CONTAINERS_RESOURCE.CODE)
      _common.edit_dropdownCellWithInput(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_EQUIPMENT_ASSEMBLY_RESOURCE.WORK_OPERATION_TYPE)
      _common.waitForLoaderToDisappear()
      _common.create_newRecord(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES)
      _common.edit_dropdownCellWithInput(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, app.GridCells.CODE, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_EQUIPMENT_ASSEMBLY_RESOURCE.CODE)
      _common.waitForLoaderToDisappear()
      cy.SAVE()
      cy.wait(2000) //required wait to open sidebar
      _common.waitForLoaderToDisappear()
      cy.SAVE()
      _common.waitForLoaderToDisappear()
      _common.clickOn_cellHasValue(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES,app.GridCells.CODE,CONTAINERS_EQUIPMENT_ASSEMBLY_RESOURCE.CODE)
      _common.saveCellDataToEnv(cnt.uuid.PROJECT_EQUIPMENT_ASSEMBLY_RESOURCES, app.GridCells.DESCRIPTION_INFO, "EQUIPMENT_ASSEMBLY_RESOURCE_DESCRIPTION")
    });
  })

  it("TC - Verify Created equipment assembly in the project module should be assign to the estimate resources", function () {
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.select_rowInContainer(cnt.uuid.ESTIMATE)
      _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
      _common.waitForLoaderToDisappear()
      cy.wait(2000) //required wait to open sidebar
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
      _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE)
      _estimatePage.update_estimate_fromWizard(UPDATE_ESTIMATE_PARAMETER)
                            cy.wait(2000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
      _common.waitForLoaderToDisappear()
      cy.REFRESH_CONTAINER()
      _common.waitForLoaderToDisappear()
      _common.select_rowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
      _common.select_allContainerData(cnt.uuid.RESOURCES)
      _common.clickOn_expandCollapseButton(cnt.uuid.RESOURCES, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
      cy.wait(1000).then(()=>{ //required wait to open sidebar
        _common.clickOn_cellHasValue(cnt.uuid.RESOURCES, app.GridCells.DESCRIPTION_INFO, Cypress.env("EQUIPMENT_ASSEMBLY_RESOURCE_DESCRIPTION"))
        _common.assert_cellData_insideActiveRow(cnt.uuid.RESOURCES, app.GridCells.CODE, CONTAINERS_EQUIPMENT_ASSEMBLY_RESOURCE.CODE)
      })
    })
  });

  after(() => {
    cy.LOGOUT();
  });

});
