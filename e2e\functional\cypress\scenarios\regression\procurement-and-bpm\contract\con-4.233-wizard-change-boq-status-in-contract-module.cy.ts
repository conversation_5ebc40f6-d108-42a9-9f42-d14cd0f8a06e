import {_businessPartnerPage,_projectPage, _common, _controllingUnit, _package, _sidebar, _mainView, _validate, _modalView, _rfqPage, _saleContractPage, _procurementContractPage, _materialPage } from 'cypress/pages';
import { cnt, tile, app, sidebar, commonLocators, btn } from 'cypress/locators';
import type { DataCells } from 'cypress/pages/interfaces.d.ts'


let PROCUREMENT_CONTRACT_PARAMETER:DataCells,CONTRACT_BOQ_PARAMETER
let CONTAINERS_CONTRACT
let CONTAINER_COLUMNS_CONTRACT
let CONTAINERS_BOQ
let CONTAINER_COLUMNS_BOQ

describe('PCM- 4.233 | Wizard change boq status in Contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('pcm/con-4.233-wizard-change-boq-status-in-contract-module.json').then((data) => {
            this.data = data;             
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT             
            CONTAINERS_BOQ = this.data.CONTAINERS.BOQ;
            CONTAINER_COLUMNS_BOQ = this.data.CONTAINER_COLUMNS.BOQ;
            PROCUREMENT_CONTRACT_PARAMETER = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIG,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BP[0]
			}     
			CONTRACT_BOQ_PARAMETER = {
                [commonLocators.CommonLabels.SUB_PACKAGE]: commonLocators.CommonKeys.MATERIAL,
            }
        });
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.waitForLoaderToDisappear()
        _common.openDesktopTile(tile.DesktopTiles.PROJECT)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("projectName")).pinnedItem()
    });
    after(() => {
        cy.LOGOUT();
    });       
    it("TC - Create new contract record", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear() 
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("projectName"))      
		_common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT)
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)		
		});
        cy.REFRESH_CONTAINER()        
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
       // _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESC)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.PROJECT_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_CONTRACT.PROJECT)
		_common.waitForLoaderToDisappear()
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.STRUCTURE_CODE,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_CONTRACT.PROCUREMENT)
        cy.SAVE()
		_common.clickOn_modalFooterButton(btn.ButtonText.YES)
		_common.waitForLoaderToDisappear()
		_common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE)
		cy.SAVE()        
        _common.waitForLoaderToDisappear()        
        cy.REFRESH_SELECTED_ENTITIES()
        _common.waitForLoaderToDisappear()
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, CONTAINERS_CONTRACT.CONTRACT_CODE)
              
	});
    it("TC - Create boq for contract module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env(CONTAINERS_CONTRACT.CONTRACT_CODE));
       _common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.CONTRACT).then(() => {
		  _common.waitForLoaderToDisappear()
          _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ,1)
		  _common.waitForLoaderToDisappear() 
		  _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQS, CONTAINER_COLUMNS_BOQ)
        })
		_common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
		_common.waitForLoaderToDisappear()
		_package.create_ProcuremenBoQswithNewReocrd_in_Requisition_module(CONTRACT_BOQ_PARAMETER)
        _common.waitForLoaderToDisappear()
		cy.SAVE()
        _common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
		_common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
		_package.create_ProcuremenBoQswithNewReocrd_in_Requisition_module(CONTRACT_BOQ_PARAMETER)
        _common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
		cy.SAVE()
        _common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
		_common.waitForLoaderToDisappear()
		_package.create_ProcuremenBoQswithNewReocrd_in_Requisition_module(CONTRACT_BOQ_PARAMETER)
        _common.waitForLoaderToDisappear()
		cy.SAVE()
        _common.waitForLoaderToDisappear()
    })   
	
	it("TC - Change status ui, check the status filter", function () {
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.ITEMS, 1);
		});	
		cy.REFRESH_SELECTED_ENTITIES()
		_common.waitForLoaderToDisappear()	
		_common.select_rowHasValue_onIndexBased(cnt.uuid.PROCUREMENTCONTRACT_BOQS,CONTAINERS_CONTRACT.MATERIAL,0)
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
        _common.clickOn_checkboxByLabel_fromModal(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonElements.ONLY_SHOW_AVAILABLE_STATUS, 0)
		_common.waitForLoaderToDisappear()
		_validate.verify_checkboxValue_instatusModal(commonLocators.CommonKeys.CHECK)
		cy.wait(1000) // Wait is required to complete the assertion
		_validate.validate_Status_In_Modal(commonLocators.CommonKeys.LOCKED)
		_validate.validate_Status_In_Modal(commonLocators.CommonKeys.IN_PROGRESS_SPACE)
		_validate.verify_recordNotPresentInmodal(commonLocators.CommonKeys.DELIVERED)
		_common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()      
		_common.select_rowHasValue_onIndexBased(cnt.uuid.PROCUREMENTCONTRACT_BOQS,CONTAINERS_CONTRACT.MATERIAL,0)
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
		_common.waitForLoaderToDisappear()
		_validate.verify_checkboxValue_instatusModal(commonLocators.CommonKeys.UNCHECK)
		cy.wait(1000) // Wait is required to complete the assertion
		_validate.validate_Status_In_Modal(commonLocators.CommonKeys.LOCKED)
		_validate.validate_Status_In_Modal(commonLocators.CommonKeys.DELIVERED)
		_validate.validate_Status_In_Modal(commonLocators.CommonKeys.IN_PROGRESS_SPACE)
	    _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()
    });  
	it("TC - Verify to change mupltile records by wizard at one time ", function () {
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.ITEMS, 1);
		});	
		_common.waitForLoaderToDisappear()	
		_common.select_allContainerData(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
		_common.changeStatus_fromModal(commonLocators.CommonKeys.LOCKED);
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
    	_common.select_rowHasValue_onIndexBased(cnt.uuid.PROCUREMENTCONTRACT_BOQS,CONTAINERS_CONTRACT.MATERIAL,0)
		_common.waitForLoaderToDisappear() 
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.GridCells.BOQ_STATUS_FK, commonLocators.CommonKeys.LOCKED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.PROCUREMENTCONTRACT_BOQS,CONTAINERS_CONTRACT.MATERIAL,1)
		_common.waitForLoaderToDisappear()
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.GridCells.BOQ_STATUS_FK, commonLocators.CommonKeys.LOCKED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.PROCUREMENTCONTRACT_BOQS,CONTAINERS_CONTRACT.MATERIAL,2)
		_common.waitForLoaderToDisappear() 
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.GridCells.BOQ_STATUS_FK, commonLocators.CommonKeys.LOCKED);
	})
	it("TC - Verify change status one record by wizard at one time ", function () {
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.ITEMS, 1);
		});	
		_common.waitForLoaderToDisappear()	
		_common.select_rowHasValue_onIndexBased(cnt.uuid.PROCUREMENTCONTRACT_BOQS,CONTAINERS_CONTRACT.MATERIAL,2)
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
		_common.changeStatus_fromModal(commonLocators.CommonKeys.IN_PROGRESS_SPACE);
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
    	_common.select_rowHasValue_onIndexBased(cnt.uuid.PROCUREMENTCONTRACT_BOQS,CONTAINERS_CONTRACT.MATERIAL,2)
		_common.waitForLoaderToDisappear() 
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.GridCells.BOQ_STATUS_FK, commonLocators.CommonKeys.IN_PROGRESS_SPACE);
	})

	it('TC - Verify message added to history of package', function () {
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.ITEMS, 1);
		});	
		_common.waitForLoaderToDisappear()	
		_common.select_rowHasValue_onIndexBased(cnt.uuid.PROCUREMENTCONTRACT_BOQS,CONTAINERS_CONTRACT.MATERIAL,2)
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
		_common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.HISTORY)
		_common.waitForLoaderToDisappear()
        _common.clickOn_cellHasValue_fromModal(app.GridCells.OLD_STATUS,commonLocators.CommonKeys.LOCKED)
		_validate.validate_activeRowText_inModal(app.GridCells.OLD_STATUS, commonLocators.CommonKeys.LOCKED);
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
	});
})