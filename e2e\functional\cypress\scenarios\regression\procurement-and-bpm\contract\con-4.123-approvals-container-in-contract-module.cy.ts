import { app, btn, cnt, sidebar,commonLocators, tile } from "cypress/locators";
import Buttons from "cypress/locators/buttons";
import Sidebar from "cypress/locators/sidebar";
import { _common,_validate,_mainView,_procurementContractPage, _sidebar, _commonAPI} from "cypress/pages";
import type { DataCells } from 'cypress/pages/interfaces.d.ts'

const APPROVALS_COMMENT1 = _common.generateRandomString(5);
const APPROVALS_COMMENT2= _common.generateRandomString(5);

let CONTRACT_PARAMETER:DataCells;
let CONTAINERS_CONTRACT;

let CONTAINER_COLUMNS_APPROVALS;
let CONTAINERS_APPROVALS

describe('PCM- 4.123 | Approvals container in contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('procurement-and-bpm/pcm-4.123-approvals-container-in-contract-module.json').then((data) => {
            this.data = data;
           
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINERS_APPROVALS = this.data.CONTAINERS.APPROVALS
            CONTAINER_COLUMNS_APPROVALS=this.data.CONTAINER_COLUMNS.APPROVALS
            CONTRACT_PARAMETER = {
              [commonLocators.CommonLabels.CONFIGURATION]:CONTAINERS_CONTRACT.CONFIGURATION,
              [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER

          };
        }).then(() => {
                cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                cy.WaitUntilLoaderComplete_Trial();
                _common.openTab(app.TabBar.PROJECT).then(() => {
                  _common.setDefaultView(app.TabBar.PROJECT);
                  cy.WaitUntilLoaderComplete_Trial();
                  _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
                });
                _commonAPI.getAccessToken().then((result) => {
                  cy.log(`Token Retrieved: ${result.token}`);
                });
              });
          })
          after(() => {
            cy.LOGOUT();
          });
        
          it('TC - API: Create project', function () {
            _commonAPI.createProject().then(() => {
              _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
              _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
            });
          });

  it("TC - Create new contract record", function () { 
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env('API_PROJECT_NUMBER_1'))
    _common.openTab(app.TabBar.CONTRACT).then(()=>{
      cy.REFRESH_CONTAINER()
      _common.setDefaultView(app.TabBar.CONTRACT)
      _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT,app.FooterTab.CONTRACTS,0)
    })
    _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
    _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT,0)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.SAVE();
    cy.wait(2000)//required to genrate code
    _common.getText_fromCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE).then(($ele1: JQuery<HTMLElement>) => {
        Cypress.env("CODE", $ele1.text())
    })
    cy.SAVE();
    _common.waitForLoaderToDisappear()
    })

  it("TC - Verify create Approvals for contracts",function(){
    _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
    _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CODE"))
    _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CODE"))
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.CONTRACT).then(()=>{
      _common.select_tabFromFooter(cnt.uuid.APPROVALS,app.FooterTab.APPROVALS,1)
      _common.setup_gridLayout(cnt.uuid.APPROVALS,CONTAINER_COLUMNS_APPROVALS)
    })
    _common.clear_subContainerFilter(cnt.uuid.APPROVALS)
    _common.create_newRecord(cnt.uuid.APPROVALS)
    _common.waitForLoaderToDisappear()
    _common.edit_dropdownCellWithInput(cnt.uuid.APPROVALS,app.GridCells.CLERK_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_APPROVALS.CLERK[0])
    _common.edit_containerCell(cnt.uuid.APPROVALS,app.GridCells.COMMENT,app.InputFields.DOMAIN_TYPE_COMMENT, APPROVALS_COMMENT1)
    cy.SAVE()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT);
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.clear_searchInSidebar()
    _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CODE"))
    _common.select_rowInContainer(cnt.uuid.APPROVALS)
    _validate.verify_isRecordPresent(cnt.uuid.APPROVALS,APPROVALS_COMMENT1)
  })

  it("TC - Verify delete Approvals from contracts",function(){
    _common.openTab(app.TabBar.CONTRACT).then(()=>{
      _common.select_tabFromFooter(cnt.uuid.APPROVALS,app.FooterTab.APPROVALS,1)
    })
    _common.select_rowHasValue(cnt.uuid.APPROVALS,APPROVALS_COMMENT1)
    _common.waitForLoaderToDisappear()
    _common.clickOn_toolbarButton(cnt.uuid.APPROVALS,btn.IconButtons.ICO_REC_DELETE)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.CONTRACT).then(()=>{
      _common.select_tabFromFooter(cnt.uuid.APPROVALS,app.FooterTab.APPROVALS,1)
      _common.clear_subContainerFilter(cnt.uuid.APPROVALS)
    })
    _common.search_inSubContainer(cnt.uuid.APPROVALS,APPROVALS_COMMENT1)
    _validate.verify_recordNotPresentInContainer(cnt.uuid.APPROVALS,APPROVALS_COMMENT1)
  })

  it("TC - Verify Create Approvals with all details for contracts",function(){
    _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT,app.FooterTab.CONTRACTS,0)
    _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
    _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CODE"))
    _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CODE"))
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.CONTRACT).then(()=>{
      _common.select_tabFromFooter(cnt.uuid.APPROVALS,app.FooterTab.APPROVALS,1)
    })
    _common.clear_subContainerFilter(cnt.uuid.APPROVALS)
    _common.create_newRecord(cnt.uuid.APPROVALS)
    _common.waitForLoaderToDisappear()
    _common.select_dataFromLookups_fromModal(cnt.uuid.APPROVALS,app.GridCells.CLERK_FK,CONTAINERS_APPROVALS.CLERK[1],app.InputFields.FORM_CONTROL)
    _common.waitForLoaderToDisappear()
    _common.edit_containerCell(cnt.uuid.APPROVALS,app.GridCells.COMMENT,app.InputFields.DOMAIN_TYPE_COMMENT, APPROVALS_COMMENT2)
    _common.edit_dropdownCellWithCaret(cnt.uuid.APPROVALS,app.GridCells.CLERK_ROLE_FK,commonLocators.CommonKeys.GRID, CONTAINERS_APPROVALS.ROLE)
    _common.waitForLoaderToDisappear()
    _common.edit_containerCell(cnt.uuid.APPROVALS,app.GridCells.DUE_DATE,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_APPROVALS.DUE_DATE)
    _common.edit_containerCell(cnt.uuid.APPROVALS,app.GridCells.EVALUATED_ON,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_APPROVALS.EVALUATED_ON)
    _common.edit_containerCell(cnt.uuid.APPROVALS,app.GridCells.EVEALUATION_LEVEL,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_APPROVALS.LEVEL)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _validate.verify_isRecordPresent(cnt.uuid.APPROVALS,APPROVALS_COMMENT2)
  })

})
