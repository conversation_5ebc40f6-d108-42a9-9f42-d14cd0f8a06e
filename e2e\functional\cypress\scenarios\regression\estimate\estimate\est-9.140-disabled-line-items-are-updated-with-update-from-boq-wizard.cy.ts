import { commonLocators, tile, app, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _commonAPI, _estimatePage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const BOQ_DESC = _common.generateRandomString(5);
const BOQ_STRUCTURE_DESC1 = _common.generateRandomString(5);
const BOQ_STRUCTURE_DESC2 = _common.generateRandomString(5);
const BOQ_STRUCTURE_DESC3 = _common.generateRandomString(5);

let CONTAINER_COLUMNS_BOQ, CONTAINER_COLUMNS_BOQ_STRUCTURE, CONTAINER_COLUMNS_ESTIMATE, CONTAINER_COLUMNS_LINE_ITEM;

let CONTAINERS_BOQ_STRUCTURE;

let MODAL_UPDATE_ESTIMATE_WIZARD;

let UPDATE_ESTIMATE_PARAMETER: DataCells;

describe('EST- 9.140 | Disabled line items are updated with Update from BoQ wizard', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

    before(function () {
        cy.fixture('estimate/est-9.140-disabled-line-items-are-updated-with-update-from-boq-wizard.json')
            .then((data) => {
                this.data = data;
                CONTAINER_COLUMNS_BOQ = this.data.CONTAINER_COLUMNS.BOQ
                CONTAINERS_BOQ_STRUCTURE = this.data.CONTAINERS.BOQ_STRUCTURE
                CONTAINER_COLUMNS_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.BOQ_STRUCTURE
                CONTAINER_COLUMNS_ESTIMATE = this.data.CONTAINER_COLUMNS.ESTIMATE
                CONTAINER_COLUMNS_LINE_ITEM = this.data.CONTAINER_COLUMNS.LINE_ITEM
                MODAL_UPDATE_ESTIMATE_WIZARD = this.data.MODAL.UPDATE_ESTIMATE_WIZARD;
                UPDATE_ESTIMATE_PARAMETER = {
                    [commonLocators.CommonKeys.CHECKBOX]: MODAL_UPDATE_ESTIMATE_WIZARD,
                };
            }).then(() => {
                cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                _common.waitForLoaderToDisappear()
                _common.openTab(app.TabBar.PROJECT).then(() => {
                    _common.setDefaultView(app.TabBar.PROJECT)
                    _common.waitForLoaderToDisappear()
                    _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
                })
                _common.waitForLoaderToDisappear();
                _commonAPI.getAccessToken().then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                });
            })
    });

    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

    it('TC - API: Create BoQ header and BoQ structure', function () {
        const BOQ_API_PARAMETERS = {
            [app.GridCells.BRIEF_INFO_SMALL]: BOQ_DESC,
            [app.GridCells.BRIEF_INFO]: [BOQ_STRUCTURE_DESC1, BOQ_STRUCTURE_DESC2, BOQ_STRUCTURE_DESC3],
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_BOQ_STRUCTURE.QUANTITY[0], CONTAINERS_BOQ_STRUCTURE.QUANTITY[1], CONTAINERS_BOQ_STRUCTURE.QUANTITY[2]],
            [app.GridCells.PRICE_SMALL]: [CONTAINERS_BOQ_STRUCTURE.UNIT_RATE, CONTAINERS_BOQ_STRUCTURE.UNIT_RATE, CONTAINERS_BOQ_STRUCTURE.UNIT_RATE],
            [app.GridCells.BAS_UOM_FK]: [CONTAINERS_BOQ_STRUCTURE.UOM, CONTAINERS_BOQ_STRUCTURE.UOM, CONTAINERS_BOQ_STRUCTURE.UOM],
            [app.GridCells.PROJECT_CODE]: Cypress.env('API_PROJECT_NUMBER_1'),
        };
        _commonAPI.createBoQHeaderWithItems(BOQ_API_PARAMETERS, 3);
        _commonAPI.getBoQHeaderList(Cypress.env('API_PROJECT_ID_1')).then(() => {
            const boqIds = Cypress.env('API_BOQ_HEADER_ID');
            Cypress.env(`BOQ_HEADER_ID`, boqIds[0]);
        });
    });

    it('TC - API: Create estimate header', function () {
        _commonAPI.createEstimateHeader(Cypress.env('API_PROJECT_ID_1'));
    });

    it('TC - API: Generate boq line item', function () {
        _common.openTab(app.TabBar.ESTIMATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
            _common.setup_gridLayout(cnt.uuid.ESTIMATE, CONTAINER_COLUMNS_ESTIMATE)
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
        _common.search_inSubContainer(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));
        _common.select_rowHasValue(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));
        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
        _common.waitForLoaderToDisappear();
        _commonAPI.generateBOQFromLeadingStructure(Cypress.env(`API_EST_ID_1`), Cypress.env(`BOQ_HEADER_ID`), Cypress.env('API_PROJECT_ID_1'));
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATELINEITEM, commonLocators.CommonKeys.DEFAULT)
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS, CONTAINER_COLUMNS_LINE_ITEM)
        });
        _common.waitForLoaderToDisappear();
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.clear_searchInSidebar();
        _common.waitForLoaderToDisappear();
        cy.REFRESH_CONTAINER();
        _common.waitForLoaderToDisappear();
    });

    it('TC - Update boq items with given condition and updated the same using update estimate wizard in estimate module', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.setDefaultView(app.TabBar.PROJECT)
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _common.openTab(app.TabBar.BOQS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BOQS, app.FooterTab.BOQs, 1);
            _common.setup_gridLayout(cnt.uuid.BOQS, CONTAINER_COLUMNS_BOQ)
        });
        _common.clear_subContainerFilter(cnt.uuid.BOQS);
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.BOQS, BOQ_DESC)
        _common.clickOn_toolbarButton(cnt.uuid.BOQS, btn.ToolBar.ICO_GO_TO);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.BOQSTRUCTURE).then(() => {
            _common.setDefaultView(app.TabBar.BOQSTRUCTURE)
            _common.select_tabFromFooter(cnt.uuid.BOQ_STRUCTURES, app.FooterTab.BOQ_STRUCTURE, 0);
            _common.setup_gridLayout(cnt.uuid.BOQ_STRUCTURES, CONTAINER_COLUMNS_BOQ_STRUCTURE)
            _common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.isdisabled, CONTAINER_COLUMNS_BOQ_STRUCTURE.isfixedprice, CONTAINER_COLUMNS_BOQ_STRUCTURE.briefinfo], cnt.uuid.BOQ_STRUCTURES)
        });
        _common.clear_subContainerFilter(cnt.uuid.BOQ_STRUCTURES)
        _common.select_rowHasValue(cnt.uuid.BOQ_STRUCTURES, BOQ_STRUCTURE_DESC1)
        _common.edit_containerCell(cnt.uuid.BOQ_STRUCTURES, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[0])
        _common.select_activeRowInContainer(cnt.uuid.BOQ_STRUCTURES)
        _common.select_rowHasValue(cnt.uuid.BOQ_STRUCTURES, BOQ_STRUCTURE_DESC2)
        _common.edit_containerCell(cnt.uuid.BOQ_STRUCTURES, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[1])
        _common.select_activeRowInContainer(cnt.uuid.BOQ_STRUCTURES)
        _common.set_cellCheckboxValue(cnt.uuid.BOQ_STRUCTURES, app.GridCells.IS_DISABLED, commonLocators.CommonKeys.CHECK)
        _common.select_activeRowInContainer(cnt.uuid.BOQ_STRUCTURES)
        _common.select_rowHasValue(cnt.uuid.BOQ_STRUCTURES, BOQ_STRUCTURE_DESC3)
        _common.edit_containerCell(cnt.uuid.BOQ_STRUCTURES, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[2])
        _common.select_activeRowInContainer(cnt.uuid.BOQ_STRUCTURES)
        _common.set_cellCheckboxValue(cnt.uuid.BOQ_STRUCTURES, app.GridCells.IS_FIXED_PRICE, commonLocators.CommonKeys.CHECK)
        _common.select_activeRowInContainer(cnt.uuid.BOQ_STRUCTURES)
        _common.waitForLoaderToDisappear();
        cy.SAVE();
        _common.waitForLoaderToDisappear();
        _common.select_rowHasValue(cnt.uuid.BOQ_STRUCTURES, BOQ_STRUCTURE_DESC1)
        _common.waitForLoaderToDisappear();
        _common.clickOn_toolbarButton(cnt.uuid.BOQ_STRUCTURES, btn.ToolBar.ICO_GO_TO);
        _common.waitForLoaderToDisappear();
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_BOQS, app.FooterTab.BOQs, 0)
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_BOQS)
        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE_BOQS, btn.ToolBar.ICO_FILTER_OFF);
        _common.waitForLoaderToDisappear();

        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATELINEITEM, commonLocators.CommonKeys.DEFAULT)
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS, CONTAINER_COLUMNS_LINE_ITEM)
            _common.set_columnAtTop([CONTAINER_COLUMNS_LINE_ITEM.isfixedprice, CONTAINER_COLUMNS_LINE_ITEM.isdisabled, CONTAINER_COLUMNS_LINE_ITEM.descriptioninfo, CONTAINER_COLUMNS_LINE_ITEM.code], cnt.uuid.ESTIMATE_LINEITEMS)
        });
        _common.waitForLoaderToDisappear();
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE)
        _estimatePage.update_estimate_fromWizard(UPDATE_ESTIMATE_PARAMETER);
                      cy.wait(2000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear();
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear();
        cy.SAVE()
        _common.waitForLoaderToDisappear();
    });

    it('TC - Validate the line items are updated with the checkboxes and quantities from boq module', function () {
        _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC1)
        _common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.WQ_QUANTITY_TARGET, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[0])
        _common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.QUANTITY_TARGET, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[0])
        _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC2)
        _common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.WQ_QUANTITY_TARGET, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[1])
        _common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.QUANTITY_TARGET, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[1])
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.IS_DISABLED, CommonLocators.CommonKeys.CHECK)
        _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC3)
        _common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.WQ_QUANTITY_TARGET, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[2])
        _common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.QUANTITY_TARGET, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[2])
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.IS_FIXED_PRICE, CommonLocators.CommonKeys.CHECK)
        _common.goToButton_inActiveRowWithText(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.CODE, btn.ButtonText.GO_TO_BOQ)
    })

    it('TC - Update quantity of a record in the boq container and validate the quantity is updated using update estimate button in estimate module', function () {
        _common.openTab(app.TabBar.BOQSTRUCTURE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BOQ_STRUCTURES, app.FooterTab.BOQ_STRUCTURE, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.BOQ_STRUCTURES)
        _common.select_rowHasValue(cnt.uuid.BOQ_STRUCTURES, BOQ_STRUCTURE_DESC2)
        _common.edit_containerCell(cnt.uuid.BOQ_STRUCTURES, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY2)
        _common.waitForLoaderToDisappear();
        cy.SAVE();
        _common.waitForLoaderToDisappear();
        _common.select_rowHasValue(cnt.uuid.BOQ_STRUCTURES, BOQ_STRUCTURE_DESC2)
        _common.waitForLoaderToDisappear();
        _common.clickOn_toolbarButton(cnt.uuid.BOQ_STRUCTURES, btn.ToolBar.ICO_GO_TO);
        _common.waitForLoaderToDisappear();
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_BOQS, app.FooterTab.BOQs, 0)
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_BOQS)
        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE_BOQS, btn.ToolBar.ICO_FILTER_OFF);
        _common.waitForLoaderToDisappear();

        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
        });
        _common.waitForLoaderToDisappear();
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC2)
        _common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.WQ_QUANTITY_TARGET, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[1])
        _common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.QUANTITY_TARGET, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY1[1])
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.IS_DISABLED, CommonLocators.CommonKeys.CHECK)
        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE_LINEITEMS, btn.ToolBar.ICO_UPDATE_ESTIMATE_FROM_BOQ);
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear();
        cy.SAVE();
        _common.waitForLoaderToDisappear();
        _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC2)
        _common.assert_forNumericValues(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.WQ_QUANTITY_TARGET, CONTAINERS_BOQ_STRUCTURE.UPDATED_BOQ_QUANTITY2)
    })

});
