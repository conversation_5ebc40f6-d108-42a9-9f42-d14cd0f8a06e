﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Procurement.PublicApi.BusinessComponents;

namespace RIB.Visual.Procurement.PublicApi.ServiceFacade.WebApi.V6
{


    /// <summary>
    /// There are no comments for PrcPackageApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("PRC_PACKAGEAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(6)]
    public partial class PrcPackageApiDto : RIB.Visual.Platform.Core.ITypedDto<PrcPackageApiEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class PrcPackageApiDto.
        /// </summary>
        public PrcPackageApiDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class PrcPackageApiDto.
        /// </summary>
        /// <param name="entity">the instance of class PrcPackageApiEntity</param>
        public PrcPackageApiDto(PrcPackageApiEntity entity)
        {
            Id = entity.Id;
            ProjectId = entity.ProjectId;
            ProjectCode = entity.ProjectCode;
            ProjectDescription = entity.ProjectDescription;
            PrcPackageStatusId = entity.PrcPackageStatusId;
            PrcPackageStatusDescription = entity.PrcPackageStatusDescription;
            Code = entity.Code;
            Description = entity.Description;
            PrcStructureId = entity.PrcStructureId;
            PrcStructureCode = entity.PrcStructureCode;
            PrcStructureDescription = entity.PrcStructureDescription;
            CurrencyId = entity.CurrencyId;
            CurrencyDescription = entity.CurrencyDescription;
            Reference = entity.Reference;
            PlannedStart = entity.PlannedStart;
            PlannedEnd = entity.PlannedEnd;
            ActualStart = entity.ActualStart;
            ActualEnd = entity.ActualEnd;
            Quantity = entity.Quantity;
            UomId = entity.UomId;
            UomDescription = entity.UomDescription;
            PrcPackageTypeId = entity.PrcPackageTypeId;
            PrcPackageTypeDescription = entity.PrcPackageTypeDescription;
            ClerkPrcId = entity.ClerkPrcId;
            ClerkPrcCode = entity.ClerkPrcCode;
            ClerkPrcDescription = entity.ClerkPrcDescription;
            ClerkReqId = entity.ClerkReqId;
            ClerkReqCode = entity.ClerkReqCode;
            ClerkReqDescription = entity.ClerkReqDescription;
            TaxCodeId = entity.TaxCodeId;
            TaxCodeCode = entity.TaxCodeCode;
            TaxCodeDescription = entity.TaxCodeDescription;
            Remark = entity.Remark;
            Remark2 = entity.Remark2;
            Remark3 = entity.Remark3;
            PsdActivityId = entity.PsdActivityId;
            PsdActivityCode = entity.PsdActivityCode;
            PsdActivityDescription = entity.PsdActivityDescription;
            PsdScheduleId = entity.PsdScheduleId;
            PsdScheduleCode = entity.PsdScheduleCode;
            PsdScheduleDescription = entity.PsdScheduleDescription;
            AddressId = entity.AddressId;
            AddressDescription = entity.AddressDescription;
            IsLive = entity.IsLive;
            UserDefined1 = entity.UserDefined1;
            UserDefined2 = entity.UserDefined2;
            UserDefined3 = entity.UserDefined3;
            UserDefined4 = entity.UserDefined4;
            UserDefined5 = entity.UserDefined5;
            OverallDiscount = entity.OverallDiscount;
            OverallDiscountOc = entity.OverallDiscountOc;
            OverallDiscountPercent = entity.OverallDiscountPercent;
            PrcConfigurationId = entity.PrcConfigurationId;
            PrcConfigurationDescription = entity.PrcConfigurationDescription;
            CompanyId = entity.CompanyId;
            CompanyCode = entity.CompanyCode;
            ExchangeRate = entity.ExchangeRate;
            SearchPattern = entity.SearchPattern;
            BusinessPartnerId = entity.BusinessPartnerId;
            BusinessPartnerDescription = entity.BusinessPartnerDescription;
            SubsidiaryId = entity.SubsidiaryId;
            SubsidiaryDescription = entity.SubsidiaryDescription;
            SupplierId = entity.SupplierId;
            SupplierCode = entity.SupplierCode;
            SupplierDescription = entity.SupplierDescription;
            BasCashProjectionId = entity.BasCashProjectionId;
            MdcAssetMasterId = entity.MdcAssetMasterId;
            MdcAssetMasterCode = entity.MdcAssetMasterCode;
            MdcAssetMasterDescription = entity.MdcAssetMasterDescription;
            PrcContractTypeId = entity.PrcContractTypeId;
            PrcContractTypeDescription = entity.PrcContractTypeDescription;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            BaselinePath = entity.BaselinePath;
            BaselineUpdate = entity.BaselineUpdate;
            UserDefinedDate1 = entity.UserDefinedDate1;
            UserDefinedDate2 = entity.UserDefinedDate2;
            UserDefinedDate3 = entity.UserDefinedDate3;
            UserDefinedDate4 = entity.UserDefinedDate4;
            UserDefinedDate5 = entity.UserDefinedDate5;
            DateEffective = entity.DateEffective;
            VatGroupId = entity.VatGroupId;
            VatGroupDescription = entity.VatGroupDescription;
            BaselinePhase = entity.BaselinePhase;
            BasCountryId = entity.BasCountryId;
            BasCountryDescription = entity.BasCountryDescription;
            PrjRegionId = entity.PrjRegionId;
            PrjRegionCode = entity.PrjRegionCode;
            PrjRegionDescription = entity.PrjRegionDescription;
            BasTelephoneNumberId = entity.BasTelephoneNumberId;
            BasTelephoneNumberDescription = entity.BasTelephoneNumberDescription;
            BasTelephoneTelefaxId = entity.BasTelephoneTelefaxId;
            BasTelephoneTelefaxDescription = entity.BasTelephoneTelefaxDescription;
            BasTelephoneMobileId = entity.BasTelephoneMobileId;
            BasTelephoneMobileDescription = entity.BasTelephoneMobileDescription;
            Email = entity.Email;
            TextInfo = entity.TextInfo;
            PrcCopyModeId = entity.PrcCopyModeId;
            PrcCopyModeDesc = entity.PrcCopyModeDesc;
            LanguageId = entity.LanguageId;
            DateDelivery = entity.DateDelivery;
            DeadlineDate = entity.DeadlineDate;
            SalesTaxMethodId = entity.SalesTaxMethodId;
            SalesTaxMethodDesc = entity.SalesTaxMethodDesc;
            DeadlineTime = entity.DeadlineTime;
            MdcControllingunitId = entity.MdcControllingunitId;
            MdcControllingunitCode = entity.MdcControllingunitCode;
            MdcControllingunitDesc = entity.MdcControllingunitDesc;
            BaselineUpdateStatus = entity.BaselineUpdateStatus;
            BaselineCOMgntUpdate = entity.BaselineCOMgntUpdate;
            DateAwardDeadline = entity.DateAwardDeadline;
            DateRequested = entity.DateRequested;
            BasLanguageFk = entity.BasLanguageFk;
            BasLanguageDesc = entity.BasLanguageDesc;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ProjectId { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string ProjectCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 3)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ProjectDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackageStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGESTATUS_ID", TypeName = "int", Order = 4)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PackageStatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcPackageStatusId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackageStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGESTATUS_DESC", TypeName = "nvarchar(2000)", Order = 5)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcPackageStatusDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 6)]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 7)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }
    
        /// <summary>
        /// There are no comments for PrcStructureId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("StructureFk")]
        public int? PrcStructureId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcStructureCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PrcStructureCode { get; set; }
    
        /// <summary>
        /// There are no comments for PrcStructureDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_DESC", TypeName = "nvarchar(2000)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcStructureDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CurrencyId { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string CurrencyDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Reference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REFERENCE", TypeName = "nvarchar(252)", Order = 13)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Reference { get; set; }
    
        /// <summary>
        /// There are no comments for PlannedStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_START", TypeName = "date", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedStart")]
        public System.DateTime? PlannedStart { get; set; }
    
        /// <summary>
        /// There are no comments for PlannedEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_END", TypeName = "date", Order = 15)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedEnd")]
        public System.DateTime? PlannedEnd { get; set; }
    
        /// <summary>
        /// There are no comments for ActualStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ACTUAL_START", TypeName = "date", Order = 16)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ActualStart")]
        public System.DateTime? ActualStart { get; set; }
    
        /// <summary>
        /// There are no comments for ActualEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ACTUAL_END", TypeName = "date", Order = 17)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ActualEnd")]
        public System.DateTime? ActualEnd { get; set; }
    
        /// <summary>
        /// There are no comments for Quantity in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QUANTITY", TypeName = "numeric(19,6)", Order = 18)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Quantity")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal Quantity { get; set; }
    
        /// <summary>
        /// There are no comments for UomId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOM_ID", TypeName = "int", Order = 19)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UomFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int UomId { get; set; }
    
        /// <summary>
        /// There are no comments for UomDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_UOM_DESC", TypeName = "nvarchar(2000)", Order = 20)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string UomDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackageTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGETYPE_ID", TypeName = "int", Order = 21)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PackageTypeFk")]
        public int? PrcPackageTypeId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackageTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGETYPE_DESC", TypeName = "nvarchar(2000)", Order = 22)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcPackageTypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public int? ClerkPrcId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkPrcCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 25)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkPrcDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public int? ClerkReqId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 27)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkReqCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 28)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkReqDescription { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_ID", TypeName = "int", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TaxCodeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int TaxCodeId { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_CODE", TypeName = "nvarchar(16)", Order = 30)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string TaxCodeCode { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_DESC", TypeName = "nvarchar(2000)", Order = 31)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string TaxCodeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 32)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark { get; set; }
    
        /// <summary>
        /// There are no comments for Remark2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK2", TypeName = "nvarchar(2000)", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark2")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark2 { get; set; }
    
        /// <summary>
        /// There are no comments for Remark3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK3", TypeName = "nvarchar(2000)", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark3")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark3 { get; set; }
    
        /// <summary>
        /// There are no comments for PsdActivityId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_ACTIVITY_ID", TypeName = "int", Order = 35)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ActivityFk")]
        public int? PsdActivityId { get; set; }
    
        /// <summary>
        /// There are no comments for PsdActivityCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_ACTIVITY_CODE", TypeName = "nvarchar(16)", Order = 36)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PsdActivityCode { get; set; }
    
        /// <summary>
        /// There are no comments for PsdActivityDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_ACTIVITY_DESC", TypeName = "nvarchar(255)", Order = 37)]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public string PsdActivityDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PsdScheduleId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_SCHEDULE_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ScheduleFk")]
        public int? PsdScheduleId { get; set; }
    
        /// <summary>
        /// There are no comments for PsdScheduleCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_SCHEDULE_CODE", TypeName = "nvarchar(16)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PsdScheduleCode { get; set; }
    
        /// <summary>
        /// There are no comments for PsdScheduleDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PSD_SCHEDULE_DESC", TypeName = "nvarchar(2000)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PsdScheduleDescription { get; set; }
    
        /// <summary>
        /// There are no comments for AddressId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_ID", TypeName = "int", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AddressFk")]
        public int? AddressId { get; set; }
    
        /// <summary>
        /// There are no comments for AddressDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_DESC", TypeName = "nvarchar(2000)", Order = 42)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string AddressDescription { get; set; }
    
        /// <summary>
        /// There are no comments for IsLive in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISLIVE", TypeName = "bit", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsLive")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsLive { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 44)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined1 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined2 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined3 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 47)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined4 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined5 { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT", TypeName = "numeric(19,7)", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscount { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscountOc { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscountPercent in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_PERCENT", TypeName = "numeric(9,3)", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscountPercent { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 52)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConfigurationFk")]
        public int? PrcConfigurationId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_DESC", TypeName = "nvarchar(2000)", Order = 53)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcConfigurationDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyId { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 55)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string CompanyCode { get; set; }
    
        /// <summary>
        /// There are no comments for ExchangeRate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 56)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal ExchangeRate { get; set; }
    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public string SearchPattern { get; set; }
    
        /// <summary>
        /// There are no comments for BusinessPartnerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        public int? BusinessPartnerId { get; set; }
    
        /// <summary>
        /// There are no comments for BusinessPartnerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BusinessPartnerDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 60)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public int? SubsidiaryId { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 61)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SubsidiaryDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 62)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public int? SupplierId { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierCode { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 64)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasCashProjectionId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CASHPROJECTION_ID", TypeName = "int", Order = 65)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CashProjectionFk")]
        public int? BasCashProjectionId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcAssetMasterId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_ID", TypeName = "int", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AssetMasterFk")]
        public int? MdcAssetMasterId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcAssetMasterCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_CODE", TypeName = "nvarchar(16)", Order = 67)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string MdcAssetMasterCode { get; set; }
    
        /// <summary>
        /// There are no comments for MdcAssetMasterDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_ASSET_MASTER_DESC", TypeName = "nvarchar(2000)", Order = 68)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcAssetMasterDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcContractTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_ID", TypeName = "int", Order = 69)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcContractTypeFk")]
        public int? PrcContractTypeId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcContractTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_DESC", TypeName = "nvarchar(2000)", Order = 70)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcContractTypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 71)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 72)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 73)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 74)]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 75)]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }
    
        /// <summary>
        /// There are no comments for BaselinePath in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_PATH", TypeName = "nvarchar(255)", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselinePath")]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public string BaselinePath { get; set; }
    
        /// <summary>
        /// There are no comments for BaselineUpdate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_UPDATE", TypeName = "datetime", Order = 77)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselineUpdate")]
        public System.DateTime? BaselineUpdate { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefinedDate1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE1", TypeName = "date", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate1")]
        public System.DateTime? UserDefinedDate1 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefinedDate2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE2", TypeName = "date", Order = 79)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate2")]
        public System.DateTime? UserDefinedDate2 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefinedDate3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE3", TypeName = "date", Order = 80)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate3")]
        public System.DateTime? UserDefinedDate3 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefinedDate4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE4", TypeName = "date", Order = 81)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate4")]
        public System.DateTime? UserDefinedDate4 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefinedDate5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE5", TypeName = "date", Order = 82)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefinedDate5")]
        public System.DateTime? UserDefinedDate5 { get; set; }
    
        /// <summary>
        /// There are no comments for DateEffective in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_EFFECTIVE", TypeName = "date", Order = 83)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateEffective")]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateEffective { get; set; }
    
        /// <summary>
        /// There are no comments for VatGroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 84)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public int? VatGroupId { get; set; }
    
        /// <summary>
        /// There are no comments for VatGroupDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 85)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string VatGroupDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BaselinePhase in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_PHASE", TypeName = "int", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselinePhase")]
        public int? BaselinePhase { get; set; }
    
        /// <summary>
        /// There are no comments for BasCountryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COUNTRY_ID", TypeName = "int", Order = 87)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CountryFk")]
        public int? BasCountryId { get; set; }
    
        /// <summary>
        /// There are no comments for BasCountryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COUNTRY_DESC", TypeName = "nvarchar(2000)", Order = 88)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasCountryDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrjRegionId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_REGION_ID", TypeName = "int", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RegionFk")]
        public int? PrjRegionId { get; set; }
    
        /// <summary>
        /// There are no comments for PrjRegionCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_REGION_CODE", TypeName = "nvarchar(5)", Order = 90)]
        [System.ComponentModel.DataAnnotations.StringLength(5)]
        public string PrjRegionCode { get; set; }
    
        /// <summary>
        /// There are no comments for PrjRegionDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_REGION_DESC", TypeName = "nvarchar(2000)", Order = 91)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrjRegionDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasTelephoneNumberId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_NUMBER_ID", TypeName = "int", Order = 92)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TelephoneNumberFk")]
        public int? BasTelephoneNumberId { get; set; }
    
        /// <summary>
        /// There are no comments for BasTelephoneNumberDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_NUMBER_DESC", TypeName = "nvarchar(100)", Order = 93)]
        [System.ComponentModel.DataAnnotations.StringLength(100)]
        public string BasTelephoneNumberDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasTelephoneTelefaxId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_TELEFAX_ID", TypeName = "int", Order = 94)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TelephoneTelefaxFk")]
        public int? BasTelephoneTelefaxId { get; set; }
    
        /// <summary>
        /// There are no comments for BasTelephoneTelefaxDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_TELEFAX_DESC", TypeName = "nvarchar(100)", Order = 95)]
        [System.ComponentModel.DataAnnotations.StringLength(100)]
        public string BasTelephoneTelefaxDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasTelephoneMobileId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_MOBILE_ID", TypeName = "int", Order = 96)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TelephoneMobileFk")]
        public int? BasTelephoneMobileId { get; set; }
    
        /// <summary>
        /// There are no comments for BasTelephoneMobileDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_TELEPHONE_MOBILE_DESC", TypeName = "nvarchar(100)", Order = 97)]
        [System.ComponentModel.DataAnnotations.StringLength(100)]
        public string BasTelephoneMobileDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Email in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EMAIL", TypeName = "nvarchar(100)", Order = 98)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Email")]
        [System.ComponentModel.DataAnnotations.StringLength(100)]
        public string Email { get; set; }
    
        /// <summary>
        /// There are no comments for TextInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TEXT_INFO", TypeName = "nvarchar(252)", Order = 99)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TextInfo")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string TextInfo { get; set; }
    
        /// <summary>
        /// There are no comments for PrcCopyModeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_ID", TypeName = "int", Order = 100)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcCopyModeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcCopyModeId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcCopyModeDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_DESC", TypeName = "nvarchar(2000)", Order = 101)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcCopyModeDesc { get; set; }
    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 102)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int LanguageId { get; set; }
    
        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 103)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDelivery")]
        public System.DateTime? DateDelivery { get; set; }
    
        /// <summary>
        /// There are no comments for DeadlineDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DEADLINE_DATE", TypeName = "date", Order = 104)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DeadlineDate")]
        public System.DateTime? DeadlineDate { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 105)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int SalesTaxMethodId { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 106)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string SalesTaxMethodDesc { get; set; }
    
        /// <summary>
        /// There are no comments for DeadlineTime in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DEADLINE_TIME", TypeName = "time", Order = 107)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DeadlineTime")]
        public global::System.TimeSpan? DeadlineTime { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 108)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("MdcControllingUnitFk")]
        public int? MdcControllingunitId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(32)", Order = 109)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public string MdcControllingunitCode { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 110)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcControllingunitDesc { get; set; }
    
        /// <summary>
        /// There are no comments for BaselineUpdateStatus in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_UPDATE_STATUS", TypeName = "nvarchar(252)", Order = 111)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselineUpdateStatus")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BaselineUpdateStatus { get; set; }
    
        /// <summary>
        /// There are no comments for BaselineCOMgntUpdate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_CO_MGNT_UPDATE", TypeName = "datetime", Order = 112)]
        [RIB.Visual.Platform.Common.InternalApiField("BaselineCOMgntUpdate")]
        public System.DateTime? BaselineCOMgntUpdate { get; set; }
    
        /// <summary>
        /// There are no comments for DateAwardDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_AWARDDEADLINE", TypeName = "date", Order = 113)]
        [RIB.Visual.Platform.Common.InternalApiField("DateAwardDeadline")]
        public System.DateTime? DateAwardDeadline { get; set; }
    
        /// <summary>
        /// There are no comments for DateRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REQUESTED", TypeName = "date", Order = 114)]
        [RIB.Visual.Platform.Common.InternalApiField("DateRequested")]
        public System.DateTime? DateRequested { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 115)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public int? BasLanguageFk { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 116)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BasLanguageDesc { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(PrcPackageApiEntity); }
        }

        /// <summary>
        /// Copy the current PrcPackageApiDto instance to a new PrcPackageApiEntity instance.
        /// </summary>
        /// <returns>a new instance of class PrcPackageApiEntity</returns>
        public PrcPackageApiEntity Copy()
        {
          var entity = new PrcPackageApiEntity();

          entity.Id = this.Id;
          entity.ProjectId = this.ProjectId;
          entity.ProjectCode = this.ProjectCode;
          entity.ProjectDescription = this.ProjectDescription;
          entity.PrcPackageStatusId = this.PrcPackageStatusId;
          entity.PrcPackageStatusDescription = this.PrcPackageStatusDescription;
          entity.Code = this.Code;
          entity.Description = this.Description;
          entity.PrcStructureId = this.PrcStructureId;
          entity.PrcStructureCode = this.PrcStructureCode;
          entity.PrcStructureDescription = this.PrcStructureDescription;
          entity.CurrencyId = this.CurrencyId;
          entity.CurrencyDescription = this.CurrencyDescription;
          entity.Reference = this.Reference;
          entity.PlannedStart = this.PlannedStart;
          entity.PlannedEnd = this.PlannedEnd;
          entity.ActualStart = this.ActualStart;
          entity.ActualEnd = this.ActualEnd;
          entity.Quantity = this.Quantity;
          entity.UomId = this.UomId;
          entity.UomDescription = this.UomDescription;
          entity.PrcPackageTypeId = this.PrcPackageTypeId;
          entity.PrcPackageTypeDescription = this.PrcPackageTypeDescription;
          entity.ClerkPrcId = this.ClerkPrcId;
          entity.ClerkPrcCode = this.ClerkPrcCode;
          entity.ClerkPrcDescription = this.ClerkPrcDescription;
          entity.ClerkReqId = this.ClerkReqId;
          entity.ClerkReqCode = this.ClerkReqCode;
          entity.ClerkReqDescription = this.ClerkReqDescription;
          entity.TaxCodeId = this.TaxCodeId;
          entity.TaxCodeCode = this.TaxCodeCode;
          entity.TaxCodeDescription = this.TaxCodeDescription;
          entity.Remark = this.Remark;
          entity.Remark2 = this.Remark2;
          entity.Remark3 = this.Remark3;
          entity.PsdActivityId = this.PsdActivityId;
          entity.PsdActivityCode = this.PsdActivityCode;
          entity.PsdActivityDescription = this.PsdActivityDescription;
          entity.PsdScheduleId = this.PsdScheduleId;
          entity.PsdScheduleCode = this.PsdScheduleCode;
          entity.PsdScheduleDescription = this.PsdScheduleDescription;
          entity.AddressId = this.AddressId;
          entity.AddressDescription = this.AddressDescription;
          entity.IsLive = this.IsLive;
          entity.UserDefined1 = this.UserDefined1;
          entity.UserDefined2 = this.UserDefined2;
          entity.UserDefined3 = this.UserDefined3;
          entity.UserDefined4 = this.UserDefined4;
          entity.UserDefined5 = this.UserDefined5;
          entity.OverallDiscount = this.OverallDiscount;
          entity.OverallDiscountOc = this.OverallDiscountOc;
          entity.OverallDiscountPercent = this.OverallDiscountPercent;
          entity.PrcConfigurationId = this.PrcConfigurationId;
          entity.PrcConfigurationDescription = this.PrcConfigurationDescription;
          entity.CompanyId = this.CompanyId;
          entity.CompanyCode = this.CompanyCode;
          entity.ExchangeRate = this.ExchangeRate;
          entity.SearchPattern = this.SearchPattern;
          entity.BusinessPartnerId = this.BusinessPartnerId;
          entity.BusinessPartnerDescription = this.BusinessPartnerDescription;
          entity.SubsidiaryId = this.SubsidiaryId;
          entity.SubsidiaryDescription = this.SubsidiaryDescription;
          entity.SupplierId = this.SupplierId;
          entity.SupplierCode = this.SupplierCode;
          entity.SupplierDescription = this.SupplierDescription;
          entity.BasCashProjectionId = this.BasCashProjectionId;
          entity.MdcAssetMasterId = this.MdcAssetMasterId;
          entity.MdcAssetMasterCode = this.MdcAssetMasterCode;
          entity.MdcAssetMasterDescription = this.MdcAssetMasterDescription;
          entity.PrcContractTypeId = this.PrcContractTypeId;
          entity.PrcContractTypeDescription = this.PrcContractTypeDescription;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;
          entity.BaselinePath = this.BaselinePath;
          entity.BaselineUpdate = this.BaselineUpdate;
          entity.UserDefinedDate1 = this.UserDefinedDate1;
          entity.UserDefinedDate2 = this.UserDefinedDate2;
          entity.UserDefinedDate3 = this.UserDefinedDate3;
          entity.UserDefinedDate4 = this.UserDefinedDate4;
          entity.UserDefinedDate5 = this.UserDefinedDate5;
          entity.DateEffective = this.DateEffective;
          entity.VatGroupId = this.VatGroupId;
          entity.VatGroupDescription = this.VatGroupDescription;
          entity.BaselinePhase = this.BaselinePhase;
          entity.BasCountryId = this.BasCountryId;
          entity.BasCountryDescription = this.BasCountryDescription;
          entity.PrjRegionId = this.PrjRegionId;
          entity.PrjRegionCode = this.PrjRegionCode;
          entity.PrjRegionDescription = this.PrjRegionDescription;
          entity.BasTelephoneNumberId = this.BasTelephoneNumberId;
          entity.BasTelephoneNumberDescription = this.BasTelephoneNumberDescription;
          entity.BasTelephoneTelefaxId = this.BasTelephoneTelefaxId;
          entity.BasTelephoneTelefaxDescription = this.BasTelephoneTelefaxDescription;
          entity.BasTelephoneMobileId = this.BasTelephoneMobileId;
          entity.BasTelephoneMobileDescription = this.BasTelephoneMobileDescription;
          entity.Email = this.Email;
          entity.TextInfo = this.TextInfo;
          entity.PrcCopyModeId = this.PrcCopyModeId;
          entity.PrcCopyModeDesc = this.PrcCopyModeDesc;
          entity.LanguageId = this.LanguageId;
          entity.DateDelivery = this.DateDelivery;
          entity.DeadlineDate = this.DeadlineDate;
          entity.SalesTaxMethodId = this.SalesTaxMethodId;
          entity.SalesTaxMethodDesc = this.SalesTaxMethodDesc;
          entity.DeadlineTime = this.DeadlineTime;
          entity.MdcControllingunitId = this.MdcControllingunitId;
          entity.MdcControllingunitCode = this.MdcControllingunitCode;
          entity.MdcControllingunitDesc = this.MdcControllingunitDesc;
          entity.BaselineUpdateStatus = this.BaselineUpdateStatus;
          entity.BaselineCOMgntUpdate = this.BaselineCOMgntUpdate;
          entity.DateAwardDeadline = this.DateAwardDeadline;
          entity.DateRequested = this.DateRequested;
          entity.BasLanguageFk = this.BasLanguageFk;
          entity.BasLanguageDesc = this.BasLanguageDesc;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(PrcPackageApiEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(PrcPackageApiEntity entity);
    }

}
