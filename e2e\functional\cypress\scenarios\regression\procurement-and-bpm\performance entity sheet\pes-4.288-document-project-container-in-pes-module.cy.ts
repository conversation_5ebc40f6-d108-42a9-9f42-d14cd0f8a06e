import apiConstantData from "cypress/constantData/apiConstantData"
import { commonLocators, sidebar, app, cnt, btn, tile } from "cypress/locators"
import { _common, _projectPage, _package, _validate, _commonAPI, _procurementPage } from "cypress/pages"
import { DataCells } from "cypress/pages/interfaces"

let CONTAINER_COLUMNS_CLERK, CONTAINER_COLUMNS_HEADERS, CONTAINER_COLUMNS_DOCUMENT_PROJECT, CONTAINER_COLUMNS_CONTRACT
let CONTAINERS_PROJECT, CONTAINERS_PERFORMANCE_ENTRY_SHEET, CONTAINERS_DOCUMENT_PROJECT

let  CONTRACT_PARAMETERS, CONTROLLING_UNIT_PARAMETERS


describe("PCM- 4.288 | Document project container in pes module", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture('pcm/pes-4.288-document-project-container-in-pes-module.json')
            .then((data) => {
                this.data = data;
                CONTAINERS_PROJECT = this.data.CONTAINERS.PROJECT
               

                CONTROLLING_UNIT_PARAMETERS = {
                    [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
                }
                CONTAINER_COLUMNS_HEADERS = this.data.CONTAINER_COLUMNS.HEADERS
                CONTAINER_COLUMNS_DOCUMENT_PROJECT = this.data.CONTAINER_COLUMNS.DOCUMENT_PROJECT
                CONTAINERS_PERFORMANCE_ENTRY_SHEET = this.data.CONTAINERS.PERFORMANCE_ENTRY_SHEET
                CONTAINER_COLUMNS_CLERK = this.data.CONTAINER_COLUMNS.CLERK
                CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
                CONTAINERS_DOCUMENT_PROJECT = this.data.CONTAINERS.DOCUMENT_PROJECT
            }).then(() => {
                cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                cy.WaitUntilLoaderComplete_Trial();
                _common.openTab(app.TabBar.PROJECT).then(() => {
                    _common.setDefaultView(app.TabBar.PROJECT);
                    cy.WaitUntilLoaderComplete_Trial();
                    _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
                });
                _commonAPI.getAccessToken().then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                });
            });
    })
    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });
      it("TC - API: Create controlling unit", function () {
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_PARAMETERS)
    })


    it('TC - API: Create Contract', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        cy.WaitUntilLoaderComplete_Trial();
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 2);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT);
        });
        cy.WaitUntilLoaderComplete_Trial();
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.clear_searchInSidebar();
        cy.WaitUntilLoaderComplete_Trial();
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT);
        cy.WaitUntilLoaderComplete_Trial();
        CONTRACT_PARAMETERS = {
            [app.GridCells.BUSINESS_PARTNER_FK]: apiConstantData.ID.BUSINESS_PARTNER_ADOLF_KOCH,
            [app.GridCells.PRC_CONFIGURATION_FK]: apiConstantData.ID.CONFIGURATION_SERVICE,
            [app.GridCells.PROJECT_FK]: Cypress.env('API_PROJECT_ID_1'),
            [app.GridCells.CLERK_PRC_FK]: apiConstantData.ID.CLERK_SMIJ,
            [app.GridCells.CLERK_REQ_FK]: apiConstantData.ID.CLERK_FI,
        };
        _commonAPI.createContract(CONTRACT_PARAMETERS);
        cy.WaitUntilLoaderComplete_Trial();
    });

  

    it('TC - Create PES', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 2);
        });
        cy.WaitUntilLoaderComplete_Trial();
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT);
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env('API_CONTRACT_CODE_1'));
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'));
        cy.WaitUntilLoaderComplete_Trial();
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT);
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_CNT_CODE_0'))
        cy.WaitUntilLoaderComplete_Trial();
        cy.SAVE()
        cy.WaitUntilLoaderComplete_Trial();
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED);
        cy.WaitUntilLoaderComplete_Trial();
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES);
        cy.WaitUntilLoaderComplete_Trial();
        cy.wait(2000); // Added this wait script is getting failed
        _common.clickOn_validationModalFooterButton_ifExists(btn.ButtonText.OK);
        cy.wait(2000); // Added this wait script is getting failed
        _procurementPage.getCode_fromPESModal('PES_CODE');
        cy.WaitUntilLoaderComplete_Trial();
        _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_PES);
        cy.WaitUntilLoaderComplete_Trial();
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.setDefaultView(app.TabBar.PERFORMANCEENTRYSHEET)
            _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
            _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_HEADERS)
        });

    });

    it("TC - Create a document project in pes module", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_DOCUMENTS, app.FooterTab.DOCUMENTS_PROJECT, 1);
            _common.setup_gridLayout(cnt.uuid.PROJECT_DOCUMENTS, CONTAINER_COLUMNS_DOCUMENT_PROJECT)
            _common.set_columnAtTop([CONTAINER_COLUMNS_DOCUMENT_PROJECT.barcode, CONTAINER_COLUMNS_DOCUMENT_PROJECT.prjprojectfk, CONTAINER_COLUMNS_DOCUMENT_PROJECT.prcpackagefk, CONTAINER_COLUMNS_DOCUMENT_PROJECT.conheaderfk, CONTAINER_COLUMNS_DOCUMENT_PROJECT.rubriccategoryfk, CONTAINER_COLUMNS_DOCUMENT_PROJECT.pesheaderfk, CONTAINER_COLUMNS_DOCUMENT_PROJECT.description], cnt.uuid.PROJECT_DOCUMENTS)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('PES_CODE'));
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PROJECT_DOCUMENTS)
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_DOCUMENTS);
        _common.create_newRecord(cnt.uuid.PROJECT_DOCUMENTS);
        cy.WaitUntilLoaderComplete_Trial();
        cy.wait(2000) //required wait to load page
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        cy.WaitUntilLoaderComplete_Trial();
        cy.wait(2000) //required wait to load page
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PROJECT_DOCUMENTS)
        _validate.verify_isRecordPresent(cnt.uuid.PROJECT_DOCUMENTS, Cypress.env("PES_CODE"))
        _common.select_rowInContainer(cnt.uuid.PROJECT_DOCUMENTS)
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_DOCUMENTS)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.PRJ_DOCUMENT_STATUS_FK, CONTAINERS_DOCUMENT_PROJECT.STATUS[1])
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.RUBRIC_CATEGORY_FK, CONTAINERS_DOCUMENT_PROJECT.RUBRIC_CATEGORY[1])
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.PRJ_DOCUMENT_TYPE_FK, CONTAINERS_DOCUMENT_PROJECT.PROJECT_DOCUMENT_TYPE[1])
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROJECT_DOCUMENTS, app.GridCells.PRJ_DOCUMENT_CATAGORY_FK, CONTAINERS_DOCUMENT_PROJECT.DOCUMENT_CATEGORY[1])
        _common.select_rowInContainer(cnt.uuid.PROJECT_DOCUMENTS)
        _common.delete_recordFromContainer(cnt.uuid.PROJECT_DOCUMENTS)
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('PES_CODE'));
        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _validate.verify_recordNotPresentInContainer(cnt.uuid.PROJECT_DOCUMENTS, Cypress.env("PES_CODE"))
    })
})