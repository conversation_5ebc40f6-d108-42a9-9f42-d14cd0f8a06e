using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Boq.Main.Core;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Procurement.Common.BusinessComponents;
using BOQMAIN = RIB.Visual.Boq.Main.BusinessComponents;
using RVPC = RIB.Visual.Platform.Core;

namespace RIB.Visual.Procurement.Package.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public partial class PrcPackageGenerateBoQWizardLogic
	{
		/// <summary>
		/// GetDbModel
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		private class CreationPreparationFromResource
		{
			#region collect data before creation
			/// <summary>
			/// 
			/// </summary>
			public Dictionary<int, Dictionary<RVPC.IdentificationData, BoqItemEntity>> PackageBoqItemsByHeaderMap { get; set; }
			/// <summary>
			/// 
			/// </summary>
			public Dictionary<int, BOQMAIN.BoqStructureEntity> PackageBoqStructuresByHeaderMap { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public Dictionary<int, List<BoqItemEntity>> SourceBoqItemsByHeaderMap { get; set; }
			#endregion collect data before creation

			#region data to save
			/// <summary>
			/// For BoqCopyData currently only carries the copied boq header and corresponding sub entities this SaveBoqs only saves them !!
			/// </summary>
			public List<IBoqCopyData> BoqCopyData { get; set; } //boqItemLogic.SaveBoqs(boqCopyDatas);
			/// <summary>
			/// 
			/// </summary>
			public List<BOQMAIN.BoqStructureEntity> BoqStructureToSave { get; set; }
			/// <summary>
			/// 
			/// </summary>
			public List<BOQMAIN.BoqStructureDetailEntity> BoqStructureDetailToSave { get; set; }
			/// <summary>
			/// 
			/// </summary>
			public List<BoqHeaderEntity> BoqHeaderToSave { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public List<BoqItemEntity> BoqItemToSave { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public List<PrcBoqEntity> PrcBoqToSave { get; set; }

			/// <summary>
			/// map for resource and new package boq item, use for creating item assignment.
			/// </summary>	
			public Dictionary<RVPC.IdentificationData, BoqItemEntity> ResourceId2NewBoqItemMap { get; set; }

			/// <summary>
			/// 
			/// </summary>
			public List<PrcItemAssignmentEntity> ItemAssignmentToSave { get; set; }
			#endregion data to save

			/// <summary>
			/// 
			/// </summary>
			public CreationPreparationFromResource()
			{
				BoqCopyData = new List<IBoqCopyData>();
				BoqStructureToSave = new List<BoqStructureEntity>();
				BoqStructureDetailToSave = new List<BoqStructureDetailEntity>();
				BoqHeaderToSave = new List<BoqHeaderEntity>();
				BoqItemToSave = new List<BoqItemEntity>();
				PrcBoqToSave = new List<PrcBoqEntity>();
				ResourceId2NewBoqItemMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
				ItemAssignmentToSave = new List<PrcItemAssignmentEntity>();
				PackageBoqItemsByHeaderMap = new Dictionary<int, Dictionary<RVPC.IdentificationData, BoqItemEntity>>();
				PackageBoqStructuresByHeaderMap = new Dictionary<int, BoqStructureEntity>();
				SourceBoqItemsByHeaderMap = new Dictionary<int, List<BoqItemEntity>>();
			}
		}

		private void UpdateToPackageBoqFromResource(CreateOrUpdatePackageBoqContext context)
		{
			CheckContext(context);
			PrcItemAssignmentLogic prcItemAssignmentLogic = new PrcItemAssignmentLogic();
			var boqItemLogic = new BoqItemLogic();
			var package = context.Package ?? new PrcPackageLogic().GetItemByKey(context.PackageId);
			var projectId = package.ProjectFk;
			var estHeader = Injector.Get<IEstimateMainHeaderLogic>().Get(context.EstHeaderId);
			var allItemAssignments = prcItemAssignmentLogic.GetEntitiesByPackageId(context.PackageId);
			var allValidItemAssignments = allItemAssignments.Where(e => !e.PrcItemFk.HasValue && e.EstHeaderFk == context.EstHeaderId).ToList();

			var itemAssignmentWithResourceToUpdate = allValidItemAssignments.Where(e => e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue && e.EstResourceFk.HasValue).ToList();
			var itemAssignmentWithResourceToCreate = allValidItemAssignments.Where(e => (!e.BoqHeaderFk.HasValue || !e.BoqItemFk.HasValue) && e.EstResourceFk.HasValue).ToList();
			var lineItemIds4ResourcesToUpdate = itemAssignmentWithResourceToUpdate.CollectIds(e => e.EstLineItemFk);
			var lineItemIds4ResourcesToCreate = itemAssignmentWithResourceToCreate.CollectIds(e => e.EstLineItemFk);
			var allLineItemIds = lineItemIds4ResourcesToUpdate.Concat(lineItemIds4ResourcesToCreate);
			var resources = Injector.Get<IEstimateMainWizardLogic>().GetEligibleResourceList(allLineItemIds, context.EstHeaderId, 5);
			var resourcesToCreate = new List<IScriptEstResource>();
			var boqHeaderIds = itemAssignmentWithResourceToUpdate.CollectIds(e => e.BoqHeaderFk);
			var packageBoqItems = new List<BoqItemEntity>();
			foreach (var boqHeaderId in boqHeaderIds)
			{
				packageBoqItems.AddRange(boqItemLogic.GetBoqItemList(boqHeaderId, 0, 99));
			}

			var packageBoqItemsMap = packageBoqItems.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.BoqHeaderFk }, e => e);
			var resourcesMap = resources.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.EstHeaderFk, PKey2 = e.EstLineItemFk });

			var itemAssignmentChildrenMap = new Dictionary<int, List<PrcItemAssignmentEntity>>();
			var newChildrenMap = new Dictionary<PrcItemAssignmentEntity, IEnumerable<PrcItemAssignmentEntity>>();

			var updateBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			var itemAssignmentToSave = new List<PrcItemAssignmentEntity>();

			// collect children of item assignment for replacement boq items
			if (itemAssignmentWithResourceToUpdate != null && itemAssignmentWithResourceToUpdate.Any())
			{
				foreach (var itemAssign in itemAssignmentWithResourceToUpdate)
				{
					if (itemAssign.BoqHeaderFk.HasValue && itemAssign.BoqItemFk.HasValue)
					{
						if (itemAssign.PrcItemAssignmentFk.HasValue)
						{
							if (!itemAssignmentChildrenMap.ContainsKey(itemAssign.PrcItemAssignmentFk.Value))
							{
								itemAssignmentChildrenMap.Add(itemAssign.PrcItemAssignmentFk.Value, new List<PrcItemAssignmentEntity>() { itemAssign });
							}
							else
							{
								itemAssignmentChildrenMap[itemAssign.PrcItemAssignmentFk.Value].Add(itemAssign);
							}
						}
					}
				}
			}

			// update package boq items with resources linked
			foreach (var item in itemAssignmentWithResourceToUpdate)
			{
				if (!item.EstResourceFk.HasValue || !item.BoqHeaderFk.HasValue || !item.BoqItemFk.HasValue)
				{
					continue;
				}
				var resourceId = new RVPC.IdentificationData() { Id = item.EstResourceFk.Value, PKey1 = item.EstHeaderFk, PKey2 = item.EstLineItemFk };
				var boqId = new RVPC.IdentificationData() { Id = item.BoqItemFk.Value, PKey1 = item.BoqHeaderFk.Value };
				if (!resourcesMap.TryGetValue(resourceId, out var resource))
				{
					continue;
				}
				if (!packageBoqItemsMap.TryGetValue(boqId, out var boqItem))
				{
					continue;
				}

				if (!context.DoesUpdateBudgetOnly4AssignmentExist)
				{
					var proportion = boqItem.Quantity != 0 ? resource.QuantityTotal / boqItem.Quantity : resource.QuantityTotal;
					boqItem.Quantity = resource.QuantityTotal;
					boqItem.QuantityDetail = QuantityToString(resource.QuantityTotal);

					boqItem.QuantityAdj = resource.QuantityTotal;
					boqItem.QuantityAdjDetail = QuantityToString(resource.QuantityTotal);

					var finalPrice = resource.IsCost ? resource.CostTotal : 0;
					boqItem.PriceOc = boqItem.Price = boqItem.Quantity != 0 ? finalPrice / boqItem.Quantity : finalPrice;
					boqItem.CostOc = boqItem.Cost = boqItem.PriceOc;

					boqItem.BudgetTotal = resource.Budget;
					boqItem.BudgetPerUnit = resource.BudgetUnit;
					boqItem.BudgetFixedTotal = resource.IsFixedBudget;
					boqItem.BudgetFixedUnit = resource.IsFixedBudgetUnit;

					itemAssignmentChildrenMap.TryGetValue(item.Id, out var subItemAssignments);
					if (subItemAssignments != null)
					{
						foreach (var assign in subItemAssignments)
						{
							if (!assign.BoqHeaderFk.HasValue || !assign.BoqItemFk.HasValue)
							{
								continue;
							}
							var replaceboqId = new RVPC.IdentificationData() { Id = assign.BoqItemFk.Value, PKey1 = assign.BoqHeaderFk.Value };
							packageBoqItemsMap.TryGetValue(replaceboqId, out var replacement);
							if (replacement == null)
							{
								continue;
							}
							replacement.Quantity *= proportion;
							replacement.QuantityDetail = QuantityToString(replacement.Quantity);
							if (!updateBoqMap.TryGetValue(replaceboqId, out var temp))
							{
								updateBoqMap.Add(replaceboqId, replacement);
							}
						}
					}
				}
				else
				{
					boqItem.BudgetTotal = resource.Budget;
					boqItem.BudgetPerUnit = resource.BudgetUnit;
					boqItem.BudgetFixedTotal = resource.IsFixedBudget;
					boqItem.BudgetFixedUnit = resource.IsFixedBudgetUnit;
				}

				if (!updateBoqMap.TryGetValue(boqId, out var boqItemTemp))
				{
					updateBoqMap.Add(boqId, boqItem);
				}
			}

			var boqToSave = updateBoqMap.Values.ToList();

			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				SaveAndRecalculateBoqItems(package, boqHeaderIds, boqToSave, package.ExchangeRate);
				// package totals	
				CalculateTotals(package);
				// package update option
				new PrcPackageUpdateOptionLogic().CreateOrSaveUpdateOption(context);
				transaction.Complete();
			}

			if (!context.DoesUpdateBudgetOnly4AssignmentExist)
			{
				foreach (var item in itemAssignmentWithResourceToCreate)
				{
					var resourceId = new RVPC.IdentificationData() { Id = item.EstResourceFk.Value, PKey1 = item.EstHeaderFk, PKey2 = item.EstLineItemFk };
					if (resourcesMap.TryGetValue(resourceId, out var resource))
					{
						resourcesToCreate.Add(resource);
					}
				}

				var boqStructure = context.UpdateOption.ResourceBoqStructure.HasValue ? context.UpdateOption.ResourceBoqStructure.Value : 1;
				Injector.Get<IPackageGenerateBoQWizardLogic>().CreatePackageBoqFromResource(package.Id, 0, resourcesToCreate, boqStructure, context.IsSkipPositionBoqAsDivisionBoq, estHeader);
			}
		}

		int IPackageGenerateBoQWizardLogic.CreatePackageBoqFromResource(
			int packageId,
			int prcHeaderId,
			IEnumerable<IScriptEstResource> newResources,
			int boqStructure4SourceResource,
			bool isSkipBoqPositionAsDivisionBoq,
			IEstHeaderEntity estHeader
		)
		{
			if (newResources == null || !newResources.Any())
			{
				return -1;
			}

			var parameter = new GeneratePackageBoQParameter()
			{
				PackageId = packageId,
				PrcHeaderId = prcHeaderId,
				Source = UpdateOptionForBoq.Criteria.Resource,
				BoqStructure4SourceResource = boqStructure4SourceResource,
				IsSkipBoqPositionAsDivisionBoq = isSkipBoqPositionAsDivisionBoq
			};
			var context = new CreateOrUpdatePackageBoqContext(parameter, false);

			var prcItemAssignmentLogic = new PrcItemAssignmentLogic();
			var package = context.Package ?? new PrcPackageLogic().GetItemByKey(context.PackageId);
			var projectId = package.ProjectFk;
			var allItemAssignments = prcItemAssignmentLogic.GetEntitiesByPackageId(context.PackageId);
			var allValidItemAssignments = allItemAssignments.Where(e => !e.PrcItemFk.HasValue).ToList();
			var invalidItemAssigns = allItemAssignments.Where(e => e.PrcPackageFk == context.PackageId && e.PrcItemFk.HasValue).ToList();
			var validResources = FetchValidReasources(invalidItemAssigns, newResources);

			if (validResources == null || !validResources.Any())
			{
				return -1;
			}

			var estHeaderId = validResources.ElementAt(0).EstHeaderFk;
			allValidItemAssignments = allValidItemAssignments.Where(e => e.EstHeaderFk == estHeaderId).ToList();
			var itemAssignmentWithResourceToCreate = allValidItemAssignments.Where(e => (!e.BoqHeaderFk.HasValue || !e.BoqItemFk.HasValue) &&
				e.EstResourceFk.HasValue).ToList();
			var lineItemsWithAssignment = GetLineItemsByItemAssignment(allValidItemAssignments);
			var validLineItemsWithAssignment = new List<IEstLineItemEntity>();
			var lineItemsByLineItemIdMap = new Dictionary<RVPC.IdentificationData, IEstLineItemEntity>();
			var lineItemsByBoqHeaderIdMap = new Dictionary<int, List<IEstLineItemEntity>>();
			var lineItemsByBoqItemIdMap = new Dictionary<RVPC.IdentificationData, List<IEstLineItemEntity>>();

			var lineItemId2ItemAssignmentsMap = new Dictionary<int, PrcItemAssignmentEntity>();
			var itemAssignments = allValidItemAssignments.Where(e => !e.PrcItemAssignmentFk.HasValue &&
				e.EstResourceFk.HasValue && e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue).ToList();
			foreach (var item in itemAssignments)
			{
				if (!lineItemId2ItemAssignmentsMap.ContainsKey(item.EstLineItemFk))
				{
					lineItemId2ItemAssignmentsMap.Add(item.EstLineItemFk, item);
				}
			}
			foreach (var lineItem in lineItemsWithAssignment)
			{
				if (!lineItemId2ItemAssignmentsMap.TryGetValue(lineItem.Id, out var assignment))
				{
					continue;
				}

				validLineItemsWithAssignment.Add(lineItem);
				var lineItemId = new RVPC.IdentificationData() { Id = lineItem.Id, PKey1 = lineItem.EstHeaderFk };
				if (!lineItemsByLineItemIdMap.TryGetValue(lineItemId, out var lineItemTemp))
				{
					lineItemsByLineItemIdMap.Add(lineItemId, lineItem);
				}

				if (lineItem.BoqHeaderFk.HasValue && lineItem.BoqItemFk.HasValue)
				{
					var boqItemId = new RVPC.IdentificationData() { Id = lineItem.BoqItemFk.Value, PKey1 = lineItem.BoqHeaderFk.Value };
					if (!lineItemsByBoqItemIdMap.TryGetValue(boqItemId, out var lineItemList))
					{
						lineItemsByBoqItemIdMap.Add(boqItemId, new List<IEstLineItemEntity>() { lineItem });
					}
					else
					{
						lineItemList.Add(lineItem);
					}

					if (!lineItemsByBoqHeaderIdMap.ContainsKey(lineItem.BoqHeaderFk.Value))
					{
						lineItemsByBoqHeaderIdMap.Add(lineItem.BoqHeaderFk.Value, new List<IEstLineItemEntity>() { lineItem });
					}
					else
					{
						lineItemsByBoqHeaderIdMap[lineItem.BoqHeaderFk.Value].Add(lineItem);
					}
				}
			}

			var lineItems = GetLineItemsByResources(validResources).OrderBy(e => e.Code).ToList();
			RecalculateCostTotalIfLineItemIsOptional(null, validResources);

			// projet boq is assigned to these line items
			// --------------------
			var newlineItemsNotRelatedToPackageBoq = new List<IEstLineItemEntity>();
			var newLineItemGroupByLineItemId = new List<IEstLineItemEntity>();
			var newLineItemsGroupByBoqItemId = new List<IEstLineItemEntity>();
			var newLineItemsGroupByBoqHeaderId = new List<IEstLineItemEntity>();
			// ---------------------

			var newNonPrjBoqLineItemsNotRelatedToPackageBoq = new List<IEstLineItemEntity>();

			foreach (var lineItem in lineItems)
			{
				var lineItemId = new RVPC.IdentificationData() { Id = lineItem.Id, PKey1 = lineItem.EstHeaderFk };
				if (boqStructure4SourceResource == (int)UpdateOptionForBoq.BoqStructureOption4Resources.ProjectBoqAndLineItem)
				{
					if (lineItem.BoqHeaderFk.HasValue && lineItem.BoqItemFk.HasValue)
					{
						var boqItemId = new RVPC.IdentificationData() { Id = lineItem.BoqItemFk.Value, PKey1 = lineItem.BoqHeaderFk.Value };

						if (lineItemsByLineItemIdMap.TryGetValue(lineItemId, out var lineItemTemp))
						{
							newLineItemGroupByLineItemId.Add(lineItem);
						}
						else if (lineItemsByBoqItemIdMap.TryGetValue(boqItemId, out var lineItemList))
						{
							newLineItemsGroupByBoqItemId.Add(lineItem);
						}
						else if (lineItemsByBoqHeaderIdMap.ContainsKey(lineItem.BoqHeaderFk.Value))
						{
							newLineItemsGroupByBoqHeaderId.Add(lineItem);
						}
						else
						{
							newlineItemsNotRelatedToPackageBoq.Add(lineItem);
						}
					}
					else
					{
						if (lineItemsByLineItemIdMap.TryGetValue(lineItemId, out var lineItemTemp))
						{
							newLineItemGroupByLineItemId.Add(lineItem);
						}
						else
						{
							newNonPrjBoqLineItemsNotRelatedToPackageBoq.Add(lineItem);
						}
					}
				}
				else if (boqStructure4SourceResource == (int)UpdateOptionForBoq.BoqStructureOption4Resources.LineItem)
				{
					if (lineItemsByLineItemIdMap.TryGetValue(lineItemId, out var lineItemTemp))
					{
						newLineItemGroupByLineItemId.Add(lineItem);
					}
					else
					{
						newNonPrjBoqLineItemsNotRelatedToPackageBoq.Add(lineItem);
					}
				}
			}

			var preparationData = new CreationPreparationFromResource();

			preparationData = CreatePackageBoq4ResourceWithProjectBoqAssigned(newlineItemsNotRelatedToPackageBoq,
				validResources.Where(e => newlineItemsNotRelatedToPackageBoq.CollectIds(r => r.Id).Contains(e.EstLineItemFk)).ToList(),
				package, context.IsSkipPositionBoqAsDivisionBoq, context, preparationData);

			preparationData = CreateOrUpdateToPackageBoq4ResourceWithEstHeaderAsBoqHeader(newNonPrjBoqLineItemsNotRelatedToPackageBoq,
				validResources.Where(e => newNonPrjBoqLineItemsNotRelatedToPackageBoq.CollectIds(r => r.Id).Contains(e.EstLineItemFk)).ToList(),
				itemAssignments, validLineItemsWithAssignment, package, estHeader, context, preparationData
				);

			preparationData = this.UpdateToPackageBoq4NewResourceWithLineItemAssigned(newLineItemGroupByLineItemId,
				validResources.Where(e => newLineItemGroupByLineItemId.CollectIds(r => r.Id).Contains(e.EstLineItemFk)).ToList(),
				itemAssignments,preparationData);

			preparationData = UpdateToPackageBoq4NewLineItemAndResourceWithSameProjectBoqItem(newLineItemsGroupByBoqItemId,
				validResources.Where(e => newLineItemsGroupByBoqItemId.CollectIds(r => r.Id).Contains(e.EstLineItemFk)).ToList(),
				itemAssignments, validLineItemsWithAssignment,preparationData);

			preparationData = UpdateToPackageBoq4NewLineItemAndResourceWithSameProjectBoqHeader(newLineItemsGroupByBoqHeaderId,
				validResources.Where(e => newLineItemsGroupByBoqHeaderId.CollectIds(r => r.Id).Contains(e.EstLineItemFk)).ToList(),
				itemAssignments, validLineItemsWithAssignment, context.IsSkipPositionBoqAsDivisionBoq, preparationData);

			var prcBoqLogic = new PrcBoqLogic();
			var boqTypeLogic = new BOQMAIN.BoqTypeLogic();

			// create prc boq
			foreach (var boqCopy in preparationData.BoqCopyData)
			{
				if (boqCopy.BoqHeader != null)
				{
					PrcBoqEntity prcBoqItem = prcBoqLogic.Create(context.PrcHeaderId, boqCopy.BoqHeader.Id, context.PackageId);
					preparationData.PrcBoqToSave.Add(prcBoqItem);
				}
			}
			foreach (BoqHeaderEntity boqHeader in preparationData.BoqHeaderToSave)
			{
				PrcBoqEntity prcBoqItem = prcBoqLogic.Create(context.PrcHeaderId, boqHeader.Id, context.PackageId);
				preparationData.PrcBoqToSave.Add(prcBoqItem);
			}

			// create item assignment
			var resourceId2ItemAssignment = new Dictionary<RVPC.IdentificationData, PrcItemAssignmentEntity>();
			foreach (var item in itemAssignmentWithResourceToCreate)
			{
				var resourceId = new RVPC.IdentificationData() { Id = item.EstResourceFk.Value, PKey1 = item.EstHeaderFk, PKey2 = item.EstLineItemFk };

				if (!resourceId2ItemAssignment.TryGetValue(resourceId, out var assignTemp))
				{
					resourceId2ItemAssignment.Add(resourceId, item);
				}
			}

			if (resourceId2ItemAssignment != null && resourceId2ItemAssignment.Any())
			{
				foreach (var resource2BoqItem in preparationData.ResourceId2NewBoqItemMap)
				{
					if (resourceId2ItemAssignment.TryGetValue(resource2BoqItem.Key, out var itemAssignment))
					{
						itemAssignment.BoqHeaderFk = resource2BoqItem.Value.BoqHeaderFk;
						itemAssignment.BoqItemFk = resource2BoqItem.Value.Id;
						preparationData.ItemAssignmentToSave.Add(itemAssignment);
					}
					else
					{
						var prcItemAssignment = new PrcItemAssignmentEntity();
						prcItemAssignment.EstHeaderFk = resource2BoqItem.Key.PKey1.Value;
						prcItemAssignment.EstLineItemFk = resource2BoqItem.Key.PKey2.Value;
						prcItemAssignment.PrcPackageFk = context.PackageId;
						prcItemAssignment.EstResourceFk = resource2BoqItem.Key.Id;
						prcItemAssignment.BoqHeaderFk = resource2BoqItem.Value.BoqHeaderFk;
						prcItemAssignment.BoqItemFk = resource2BoqItem.Value.Id;
						preparationData.ItemAssignmentToSave.Add(prcItemAssignment);
					}
				}
			}
			else
			{
				foreach (var resource2BoqItem in preparationData.ResourceId2NewBoqItemMap)
				{
					var prcItemAssignment = new PrcItemAssignmentEntity();
					prcItemAssignment.EstHeaderFk = resource2BoqItem.Key.PKey1.Value;
					prcItemAssignment.EstLineItemFk = resource2BoqItem.Key.PKey2.Value;
					prcItemAssignment.PrcPackageFk = context.PackageId;
					prcItemAssignment.EstResourceFk = resource2BoqItem.Key.Id;
					prcItemAssignment.BoqHeaderFk = resource2BoqItem.Value.BoqHeaderFk;
					prcItemAssignment.BoqItemFk = resource2BoqItem.Value.Id;
					preparationData.ItemAssignmentToSave.Add(prcItemAssignment);
				}
			}

			var itemAssignIds = prcItemAssignmentLogic.GetNextIds(preparationData.ItemAssignmentToSave.Count(e => e.Version == 0)).GetEnumerator();
			foreach (var item in preparationData.ItemAssignmentToSave)
			{
				if (item.Version > 0)
				{
					continue;
				}
				itemAssignIds.MoveNext();
				item.Id = itemAssignIds.Current;
			}

			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				//new BoqItemLogic().SaveBoqs(preparationData.BoqCopyData);

				// boq structure
				foreach (var boqStrcture in preparationData.BoqStructureToSave)
				{
					boqStrcture.BoqRoundingConfig = null;
					boqTypeLogic.SaveEntity(boqStrcture);
					boqTypeLogic.SaveBoqStructureDetail(preparationData.BoqStructureDetailToSave, boqStrcture.Id);
				}
				// boq header
				new BoqHeaderLogic().Save(preparationData.BoqHeaderToSave);
				// prc boq
				prcBoqLogic.Save(preparationData.PrcBoqToSave);
				var boqHeaderIds = preparationData.BoqItemToSave.CollectIds(e => e.BoqHeaderFk);
				var boqItemsToSaveTranslation = preparationData.BoqItemToSave.Where(e => e.Version == 0 && e.BriefInfo != null && e.BriefInfo.Modified).ToList();
				SaveBoqItemTranslation(new BoqItemLogic(), boqItemsToSaveTranslation);
				SaveAndRecalculateBoqItems(package, boqHeaderIds, preparationData.BoqItemToSave, package.ExchangeRate);

				// prc item assignment
				prcItemAssignmentLogic.BulkSave(prcItemAssignmentLogic.GetDbModel(), preparationData.ItemAssignmentToSave);
				// package totals	
				CalculateTotals(package);
				// package update option
				new PrcPackageUpdateOptionLogic().CreateOrSaveUpdateOption(context);
				transaction.Complete();
			}
			return 1;
		}

		/// <summary>
		/// create package boq items from line items which have project boq item assigned and resources with project boq items assigned. it is for project boq and line item as hierarchy
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="resourceList"></param>
		/// <param name="package"></param>
		/// <param name="isSkipPositionBoqAsDivisionBoq"></param>
		/// <param name="context"></param>
		/// <param name="preparationData"></param>
		/// <returns></returns>
		private CreationPreparationFromResource CreatePackageBoq4ResourceWithProjectBoqAssigned(
			IEnumerable<IEstLineItemEntity> lineItems,
			IEnumerable<IScriptEstResource> resourceList,
			PrcPackageEntity package,
			bool isSkipPositionBoqAsDivisionBoq,
			 CreateOrUpdatePackageBoqContext context,
			CreationPreparationFromResource preparationData
			)
		{

			if (lineItems == null || !lineItems.Any() || resourceList == null || !resourceList.Any())
			{
				return preparationData;
			}

			var sourceBoqItemsByHeaderMap = preparationData.SourceBoqItemsByHeaderMap;
			// copy boq header from project boq
			var boqItemLogic = new BoqItemLogic();
			var boqHeaderLogic = new BoqHeaderLogic();
			var boqTypeLogic = new BOQMAIN.BoqTypeLogic();

			// get boq headers
			var sourceBoqHeaderIds = lineItems.CollectIds(e => e.BoqHeaderFk);
			IList<BoqHeaderEntity> sourceHeaderList = boqHeaderLogic.GetSearchList(e => sourceBoqHeaderIds.Contains(e.Id));

			// get source boq structures
			var boqStructureIds = sourceHeaderList.CollectIds(e => e.BoqStructureFk);
			var sourceBoqStructures = boqTypeLogic.GetBoqStructureList(boqStructureIds).ToDictionary(e => e.Id);

			// get source boq Items
			var sourceBoqItems = new List<BoqItemEntity>();

			// define boq copy info
			var boqItemCopyInfo = new BoqItemCopyInfo();
			boqItemCopyInfo.MaintainBaseBoqLink = false;
			boqItemCopyInfo.CurrencyId = package.CurrencyFk;

			IList<IBoqCopyData> boqCopyDatas = new List<IBoqCopyData>();

			// copy boq header
			var sourceBoqHeaderId2target = new Dictionary<int, int>();
			var targetBoqHeaderId2Structure = new Dictionary<int, BOQMAIN.BoqStructureEntity>();
			var targetBoqHeaderId2StructureDetail = new Dictionary<int, List<BOQMAIN.BoqStructureDetailEntity>>();

			var additionDivisionCount = isSkipPositionBoqAsDivisionBoq ? 1 : 2;
			foreach (var boqHeaderId in sourceBoqHeaderIds)
			{
				var sourceHeader = sourceHeaderList.FirstOrDefault(e => e.Id == boqHeaderId);
				if (sourceHeader == null || !sourceHeader.BoqStructureFk.HasValue)
				{
					continue;
				}
				List<BoqItemEntity> boqItems;
				if (!sourceBoqItemsByHeaderMap.ContainsKey(boqHeaderId))
				{
					boqItems = boqItemLogic.GetBoqItemList(boqHeaderId, 0, 99).ToList();
					sourceBoqItemsByHeaderMap.Add(boqHeaderId, boqItems.ToList());
				}
				else
				{
					boqItems = sourceBoqItemsByHeaderMap[boqHeaderId];
				}

				sourceBoqItems.AddRange(boqItems);
				var sourceRootBoqItem = boqItemLogic.GetBoqRootItemByHeaderId(sourceHeader.Id);
				BoqStructureEntity sourceBoqStructure = sourceHeader.BoqStructureFk.HasValue ? sourceBoqStructures.GetValueOrDefault(sourceHeader.BoqStructureFk.Value) : null;
				var boqCopyData = boqHeaderLogic.CopyBoqHeaderAndCreateBoqStructureAndDetails(package.Id, sourceHeader, boqItemCopyInfo, additionDivisionCount);
				var targetBoqStructure = boqCopyData.BoqStructure as BoqStructureEntity;
				if (targetBoqStructure != null && sourceBoqStructure != null)
				{
					targetBoqStructure.EnforceStructure = sourceBoqStructure.EnforceStructure;
				}

				// copy boq structure and boq structure detail
				var targetBoqHeader = boqCopyData.BoqHeader as BoqHeaderEntity;

				PrcBoqEntity prcBoqItem = new PrcBoqLogic().Create(context.PrcHeaderId, targetBoqHeader.Id, context.PackageId);
				preparationData.PrcBoqToSave.Add(prcBoqItem);

				if (!targetBoqHeaderId2Structure.ContainsKey(targetBoqHeader.Id))
				{
					targetBoqHeaderId2Structure.Add(targetBoqHeader.Id, boqCopyData.BoqStructure as BoqStructureEntity);
				}
				if (!targetBoqHeaderId2StructureDetail.ContainsKey(targetBoqHeader.Id))
				{
					targetBoqHeaderId2StructureDetail.Add(targetBoqHeader.Id, boqCopyData.BoqStructureDetails.OfType<BoqStructureDetailEntity>().ToList());
				}
				if (!sourceBoqHeaderId2target.ContainsKey(targetBoqHeader.Id))
				{
					sourceBoqHeaderId2target.Add(boqHeaderId, boqCopyData.BoqHeader.Id);
				}
				boqCopyDatas.Add(boqCopyData);
			}

			//preparationData.BoqCopyData.AddRange(boqCopyDatas);

			// save boq header, boq structure and boq structure details
			boqItemLogic.SaveBoqs(boqCopyDatas); // For boqCopyDatas currently only carries the copied boq header and corresponding sub entities this SaveBoqs only saves them !!

			// copy from project boq levels
			var sourceBoqItemMap = sourceBoqItems.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.BoqHeaderFk }, e => e);
			var sourceBoqItemToCopyMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			var sourceBoqParentId2ChildrenToCopyMap = new Dictionary<RVPC.IdentificationData, Dictionary<RVPC.IdentificationData, BoqItemEntity>>();

			// collect project boq items to copy
			Action<BoqItemEntity> collectBoqItemToCopy = (boqItem) =>
			{
				var boqId = new RVPC.IdentificationData() { Id = boqItem.Id, PKey1 = boqItem.BoqHeaderFk };
				if (!sourceBoqItemToCopyMap.TryGetValue(boqId, out var tempBoqItem))
				{
					sourceBoqItemToCopyMap.Add(boqId, boqItem);
				}

				if (!boqItem.BoqItemFk.HasValue)
				{
					return;
				}
				var parentId = new RVPC.IdentificationData() { Id = boqItem.BoqItemFk.Value, PKey1 = boqItem.BoqHeaderFk };
				if (!sourceBoqParentId2ChildrenToCopyMap.TryGetValue(parentId, out var map))
				{
					sourceBoqParentId2ChildrenToCopyMap.Add(parentId, new Dictionary<RVPC.IdentificationData, BoqItemEntity>() { { boqId, boqItem } });
				}
				else if (!map.TryGetValue(boqId, out var tempboq))
				{
					map.Add(boqId, boqItem);
				}
			};
			foreach (var lineItem in lineItems)
			{
				if (!lineItem.BoqItemFk.HasValue || !lineItem.BoqHeaderFk.HasValue)
				{
					continue;
				}

				var boqId = new RVPC.IdentificationData() { Id = lineItem.BoqItemFk.Value, PKey1 = lineItem.BoqHeaderFk.Value };
				BoqItemEntity boqItem = null;
				if (!sourceBoqItemMap.TryGetValue(boqId, out boqItem))
				{
					continue;
				}
				ForEachBoqItem(boqItem, collectBoqItemToCopy);
			}

			// create hierarchy boq items
			var boqItemIdEnumerator = boqItemLogic.GetNextIds(sourceBoqItemToCopyMap.Count).GetEnumerator();
			var targetUpdateBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			Dictionary<int, int> targetBoQFks = new Dictionary<int, int>();
			var sourceBoqRootItmesToCopy = sourceBoqItemToCopyMap.Values.Where(e => !e.BoqItemFk.HasValue).ToList();
			foreach (var boqRootItem in sourceBoqRootItmesToCopy)
			{
				if (!sourceBoqHeaderId2target.ContainsKey(boqRootItem.BoqHeaderFk))
				{
					continue;
				}
				List<List<BoqItemEntity>> boqItemsToHandle = new List<List<BoqItemEntity>>() { new List<BoqItemEntity>() { boqRootItem } };
				var targetBoQHeaderId = sourceBoqHeaderId2target[boqRootItem.BoqHeaderFk];
				while (boqItemsToHandle.Count > 0)
				{
					var tempToHandle = new List<List<BoqItemEntity>>();

					foreach (var itemsToHandle in boqItemsToHandle)
					{
						foreach (var currentBoQItem in itemsToHandle)
						{
							if (isSkipPositionBoqAsDivisionBoq && !((currentBoQItem.BoqLineTypeFk >= (int)BoqConstants.EBoqLineType.DivisionLevelFirst &&
								currentBoQItem.BoqLineTypeFk <= (int)BoqConstants.EBoqLineType.DivisionLevelLast) ||
								currentBoQItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root))
							{
								continue;
							}

							BoqItemEntity targetParentBoqItem = null;
							if (currentBoQItem.BoqItemParent != null && targetBoQFks.ContainsKey(currentBoQItem.BoqItemParent.Id))
							{
								var targetBoqItem = GetValueFromDictionary(targetUpdateBoqMap, targetBoQFks[currentBoQItem.BoqItemParent.Id], targetBoQHeaderId);

								if (targetBoqItem != null)
								{
									targetParentBoqItem = targetBoqItem;
								}
							}

							IList<BoqItemEntity> existTargetBoqItem = null;

							if (existTargetBoqItem == null || !existTargetBoqItem.Any())
							{
								BoqItemEntity newBoQItemEntity = CloneSourceBoqItem(currentBoQItem, targetBoQHeaderId, targetParentBoqItem);
								boqItemIdEnumerator.MoveNext();
								newBoQItemEntity.Id = boqItemIdEnumerator.Current;

								targetBoQFks.Add(currentBoQItem.Id, newBoQItemEntity.Id);

								var updateId = new RVPC.IdentificationData() { Id = newBoQItemEntity.Id, PKey1 = newBoQItemEntity.BoqHeaderFk };
								BoqItemEntity update = null;
								if (!targetUpdateBoqMap.TryGetValue(updateId, out update))
								{
									targetUpdateBoqMap.Add(updateId, newBoQItemEntity);
								}
							}

							if (sourceBoqParentId2ChildrenToCopyMap.TryGetValue(
								new RVPC.IdentificationData() { Id = currentBoQItem.Id, PKey1 = currentBoQItem.BoqHeaderFk }, out var tempBoqItem))
							{
								tempToHandle.Add(tempBoqItem.Values.ToList());
							}
						}
					}
					boqItemsToHandle = tempToHandle;
				}
			}

			// create boq items for line items and resources
			var lineItemId2ResourcesMap = BuildLineItemId2ResourcesMap(resourceList);

			var resourceId2NewBoqItemMap = preparationData.ResourceId2NewBoqItemMap;
			//BoqItemEntity previous = null;
			//Dictionary<RVPC.IdentificationData, BoqItemEntity> boqId2LineItemPreviousMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			foreach (var lineItem in lineItems)
			{
				var lineItemId = new RVPC.IdentificationData() { Id = lineItem.Id, PKey1 = lineItem.EstHeaderFk };
				if (!lineItemId2ResourcesMap.TryGetValue(lineItemId, out var resources))
				{
					continue;
				}

				if (!lineItem.BoqItemFk.HasValue || !lineItem.BoqHeaderFk.HasValue)
				{
					continue;
				}

				var sourceBoqId = lineItem.BoqItemFk.Value;
				var sourceBoqHeaderId = lineItem.BoqHeaderFk.Value;

				var sourceBoqIdData = new RVPC.IdentificationData() { Id = sourceBoqId, PKey1 = sourceBoqHeaderId };
				if (isSkipPositionBoqAsDivisionBoq)
				{
					if (sourceBoqItemMap.TryGetValue(sourceBoqIdData, out var sourceBoq))
					{
						if (!sourceBoq.BoqItemFk.HasValue)
						{
							continue;
						}
						sourceBoqId = sourceBoq.BoqItemFk.Value;
					}
				}

				if (!targetBoQFks.ContainsKey(sourceBoqId) || !sourceBoqHeaderId2target.ContainsKey(sourceBoqHeaderId))
				{
					continue;
				}
				var targetBoqHeaderId = sourceBoqHeaderId2target[sourceBoqHeaderId];
				var targetBoqId = targetBoQFks[sourceBoqId];
				var targetBoqIdData = new RVPC.IdentificationData() { Id = targetBoqId, PKey1 = targetBoqHeaderId };
				if (!targetUpdateBoqMap.TryGetValue(targetBoqIdData, out var targetParentBoq) ||
					!targetBoqHeaderId2Structure.ContainsKey(targetBoqHeaderId) ||
					!targetBoqHeaderId2StructureDetail.ContainsKey(targetBoqHeaderId))
				{
					continue;
				}
				//var targetBoqStructure = targetBoqHeaderId2Structure[targetBoqHeaderId];
				//targetBoqStructure.BoqStructureDetailEntities = targetBoqHeaderId2StructureDetail[targetBoqHeaderId];
				//boqId2LineItemPreviousMap.TryGetValue(sourceBoqIdData, out previous);
				var newLevelBoqItemResult = CreateDivisionBoqItemFromLineItem(lineItem, targetParentBoq/*, previous, targetBoqStructure*/);
				var newLevelBoqItem = newLevelBoqItemResult.Item1;
				var level = newLevelBoqItemResult.Item2;
				var levelBoqId = new RVPC.IdentificationData() { Id = newLevelBoqItem.Id, PKey1 = newLevelBoqItem.BoqHeaderFk };
				if (!targetUpdateBoqMap.TryGetValue(levelBoqId, out var levelBoq))
				{
					targetUpdateBoqMap.Add(levelBoqId, newLevelBoqItem);
				}

				//BoqItemEntity resourcePrevious = null;
				foreach (var resource in resources)
				{
					var resourceId = new RVPC.IdentificationData() { Id = resource.Id, PKey1 = resource.EstHeaderFk, PKey2 = resource.EstLineItemFk };
					var newBoqItem = CreateBoqItemPositionFromResource(resource, newLevelBoqItem, lineItem); //, resourcePrevious, targetBoqStructure, level + 1, estHeaderCurrencyId);
					var updateId = new RVPC.IdentificationData() { Id = newBoqItem.Id, PKey1 = newBoqItem.BoqHeaderFk };
					if (!targetUpdateBoqMap.TryGetValue(updateId, out var update))
					{
						targetUpdateBoqMap.Add(updateId, newBoqItem);
					}

					if (!resourceId2NewBoqItemMap.TryGetValue(resourceId, out var boqItem))
					{
						resourceId2NewBoqItemMap.Add(resourceId, newBoqItem);
					}
					//resourcePrevious = newBoqItem;
				}

				//if (boqId2LineItemPreviousMap.TryGetValue(sourceBoqIdData, out var lineItemPreviousTemp))
				//{
				//	boqId2LineItemPreviousMap.Remove(sourceBoqIdData);
				//}
				//boqId2LineItemPreviousMap.Add(sourceBoqIdData, newLevelBoqItem);
			}

			preparationData.BoqItemToSave.AddRange(targetUpdateBoqMap.Values.ToList());
			return preparationData;
		}

		/// <summary>
		/// create or update to package boq items for line items and resources without project boq assigned or it is for only line item as hierarchy
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="resourceList"></param>
		/// <param name="itemAssignments"></param>
		/// <param name="lineItemsWithItemAssignment"></param>
		/// <param name="package"></param>
		/// <param name="estHeader"></param>
		/// <param name="context"></param>
		/// <param name="preparationData"></param>
		/// <returns></returns>
		private CreationPreparationFromResource CreateOrUpdateToPackageBoq4ResourceWithEstHeaderAsBoqHeader(
			IEnumerable<IEstLineItemEntity> lineItems,
			IEnumerable<IScriptEstResource> resourceList,
			IEnumerable<PrcItemAssignmentEntity> itemAssignments,
			IEnumerable<IEstLineItemEntity> lineItemsWithItemAssignment,
			PrcPackageEntity package,
			IEstHeaderEntity estHeader,
			CreateOrUpdatePackageBoqContext context,
			CreationPreparationFromResource preparationData
			)
		{
			if (lineItems == null || !lineItems.Any() || resourceList == null || !resourceList.Any())
			{
				return preparationData;
			}

			// copy boq header from project boq
			var boqHeaderLogic = new BoqHeaderLogic();
			var boqItemLogic = new BoqItemLogic();
			var boqTypeLogic = new BOQMAIN.BoqTypeLogic();

			var lineItemId2ItemAssignmentsMap = new Dictionary<int, PrcItemAssignmentEntity>();
			var packageBoqHeaderIds = new List<int>();
			foreach (var item in itemAssignments)
			{
				if (!lineItemId2ItemAssignmentsMap.ContainsKey(item.EstLineItemFk))
				{
					lineItemId2ItemAssignmentsMap.Add(item.EstLineItemFk, item);
				}
			}

			// collect the package boq headers which are related to estimate.
			foreach (var item in lineItemsWithItemAssignment)
			{
				if (item.BoqHeaderFk.HasValue || item.BoqItemFk.HasValue || !lineItemId2ItemAssignmentsMap.TryGetValue(item.Id, out var assignment))
				{
					continue;
				}

				if (assignment.BoqHeaderFk.HasValue)
				{
					packageBoqHeaderIds.Add(assignment.BoqHeaderFk.Value);
				}
			}

			// find out the package boq header and boq structure which is the one for line item as boq hierarchy
			packageBoqHeaderIds = packageBoqHeaderIds.Distinct().ToList();
			CollectPackageBoqHeaderAndStructures(preparationData, null, packageBoqHeaderIds);

			var packageBoqRootItems = preparationData.PackageBoqItemsByHeaderMap.SelectMany(e => e.Value.Values)
				.Where(e => !e.BoqItemFk.HasValue && packageBoqHeaderIds.Contains(e.BoqHeaderFk));
			var packageBoqRootItem = packageBoqRootItems.FirstOrDefault(e => e.Reference == estHeader.Code);
			var firstPackageRootItem = packageBoqRootItems.FirstOrDefault();

			BOQMAIN.BoqStructureEntity packageBoqStructure = null;

			if (packageBoqRootItem != null)
			{
				preparationData.PackageBoqStructuresByHeaderMap.TryGetValue(packageBoqRootItem.BoqHeaderFk, out packageBoqStructure);
			}

			if (packageBoqStructure == null && firstPackageRootItem != null)
			{
				if (preparationData.PackageBoqStructuresByHeaderMap.TryGetValue(firstPackageRootItem.BoqHeaderFk, out packageBoqStructure))
				{
					packageBoqRootItem = firstPackageRootItem;
				}
			}

			if (packageBoqRootItem == null)
			{
				// Create Boq Header
				var creationParam = new BoqHeaderLogic.BoqHeaderCreationParam { BasCurrencyFk = package.CurrencyFk };
				var packageBoqHeader = boqHeaderLogic.CreateEntity(false, creationParam);

				// Create and assigns Boq Structure
				var boqStructure = boqHeaderLogic.CreateAndAssignNewGaebBoqStructureToPackageBoqHeader(packageBoqHeader);
				var boQStandardLogic = new BasicsCustomizeBoQStandardLogic();
				if (boqStructure.BoqStandardFk.HasValue)
				{
					var boqStandard = boQStandardLogic.GetById(boqStructure.BoqStandardFk.Value);
					if (boqStandard != null)
					{
						boqStructure.Description = ((boqStandard as BasicsCustomizeBoQStandardEntity).DescriptionInfo.Translated ?? String.Empty) + " ";
					}
				}
				boqStructure.Description += boqStructure.Boqmask;
				boqStructure.BoqRoundingConfigFk = 0;
				boqStructure.BoqRoundingConfig = null;
				boqTypeLogic.SaveEntity(boqStructure);
				boqTypeLogic.SaveBoqStructureDetail(preparationData.BoqStructureDetailToSave, boqStructure.Id);

				boqHeaderLogic.Save(packageBoqHeader);

				// create prc boq
				PrcBoqEntity prcBoqItem = new PrcBoqLogic().Create(context.PrcHeaderId, packageBoqHeader.Id, context.PackageId);
				preparationData.PrcBoqToSave.Add(prcBoqItem);

				// Create Boq root item
				packageBoqRootItem = boqItemLogic.CreateBoqRootItem(packageBoqHeader, false, estHeader.Code);
				packageBoqRootItem.BriefInfo = new DescriptionTranslateType();
				if (estHeader.DescriptionInfo != null)
				{
					packageBoqRootItem.BriefInfo = new Platform.Common.DescriptionTranslateType(estHeader.DescriptionInfo.Description);

					if (estHeader.DescriptionInfo.VersionTr > 0)
					{
						packageBoqRootItem.BriefInfo.Translated = estHeader.DescriptionInfo.Translated;
						packageBoqRootItem.BriefInfo.Modified = true;
					}
				}
				preparationData.BoqItemToSave.Add(packageBoqRootItem);
			}

			// create package boq items for line items and resources
			var lineItemId2ResourcesMap = BuildLineItemId2ResourcesMap(resourceList);

			//BoqItemEntity previous = null; // to mark the previous boq item for generating reference no.
			//if (preparationData.PackageBoqItemsByHeaderMap.TryGetValue(packageBoqRootItem.BoqHeaderFk, out var packageBoqItemsMap))
			//{
			//	var packageBoqRootItemId = new RVPC.IdentificationData() { Id = packageBoqRootItem.Id, PKey1 = packageBoqRootItem.BoqHeaderFk };
			//	if (packageBoqItemsMap.TryGetValue(packageBoqRootItemId, out var rootItemWithChildren))
			//	{
			//		var tempChildren = boqItemLogic.GetSortedChildren(rootItemWithChildren, null, null, true);
			//		previous = tempChildren.LastOrDefault();
			//	}
			//}
			var targetUpdateBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>(); // new package boq item collection
			var resourceId2NewBoqItemMap = preparationData.ResourceId2NewBoqItemMap; // map for resource and new package boq item, use for creating item assignment.

			foreach (var lineItem in lineItems)
			{
				var lineItemId = new RVPC.IdentificationData() { Id = lineItem.Id, PKey1 = lineItem.EstHeaderFk };
				if (!lineItemId2ResourcesMap.TryGetValue(lineItemId, out var resources))
				{
					continue;
				}

				var newLevelBoqItemResult = CreateDivisionBoqItemFromLineItem(lineItem, packageBoqRootItem/*, previous, packageBoqStructure*/);
				var newLevelBoqItem = newLevelBoqItemResult.Item1;
				var level = newLevelBoqItemResult.Item2;
				var levelBoqId = new RVPC.IdentificationData() { Id = newLevelBoqItem.Id, PKey1 = newLevelBoqItem.BoqHeaderFk };
				if (!targetUpdateBoqMap.TryGetValue(levelBoqId, out var levelBoq))
				{
					targetUpdateBoqMap.Add(levelBoqId, newLevelBoqItem);
				}

				//BoqItemEntity resourcePrevious = null;
				foreach (var resource in resources)
				{
					var resourceId = new RVPC.IdentificationData() { Id = resource.Id, PKey1 = resource.EstHeaderFk, PKey2 = resource.EstLineItemFk };
					var newBoqItem = CreateBoqItemPositionFromResource(resource, newLevelBoqItem,lineItem); //, resourcePrevious, packageBoqStructure, level + 1, estHeader.BasCurrencyFk);
					var updateId = new RVPC.IdentificationData() { Id = newBoqItem.Id, PKey1 = newBoqItem.BoqHeaderFk };
					BoqItemEntity update = null;
					if (!targetUpdateBoqMap.TryGetValue(updateId, out update))
					{
						targetUpdateBoqMap.Add(updateId, newBoqItem);
					}

					if (!resourceId2NewBoqItemMap.TryGetValue(resourceId, out var boqItem))
					{
						resourceId2NewBoqItemMap.Add(resourceId, newBoqItem);
					}
					//resourcePrevious = newBoqItem;
				}

				//previous = newLevelBoqItem;
			}

			preparationData.BoqItemToSave.AddRange(targetUpdateBoqMap.Values.ToList());
			return preparationData;
		}

		/// <summary>
		/// update to package boq with new resources. The line items which have project boq item assigned of these resources have item assignments. It is for project boq and line item as boq structure.
		/// </summary>
		/// <param name="lineItems">within the same est header</param>
		/// <param name="resources">within the same est header</param>
		/// <param name="itemAssignmentList">within the same est header and should include boq assigned</param>
		/// <param name="preparationData"></param>
		private CreationPreparationFromResource UpdateToPackageBoq4NewResourceWithLineItemAssigned(
			IEnumerable<IEstLineItemEntity> lineItems,
			IEnumerable<IScriptEstResource> resources,
			IEnumerable<PrcItemAssignmentEntity> itemAssignmentList,
			CreationPreparationFromResource preparationData
			)
		{
			if (lineItems == null || !lineItems.Any() || resources == null || !resources.Any())
			{
				return preparationData;
			}

			var boqItemLogic = new BoqItemLogic();

			var itemAssignments = itemAssignmentList.Where(e => lineItems.CollectIds(f => f.Id).Contains(e.EstLineItemFk)).ToList();

			// Get Package Boq Items and Boq Structures
			var packageBoqHeaderIds = itemAssignments.CollectIds(e => e.BoqHeaderFk);
			preparationData = CollectPackageBoqHeaderAndStructures(preparationData, null, packageBoqHeaderIds);

			var packageBoqItemsByHeaderMap = preparationData.PackageBoqItemsByHeaderMap;
			var packageBoqStructuresByHeaderMap = preparationData.PackageBoqStructuresByHeaderMap;

			// collect Parent boq item and last boq Item under the parent boq item
			var lineItem2ParentBoqItem = new Dictionary<int, BoqItemEntity>(); // int -> line item id; BoqItemEntity -> virtual map between line item and boq item (division) which as the parent boq item
			//var lineitem2PreviousBoqItem = new Dictionary<int, BoqItemEntity>();  // int -> line item id; BoqItemEntity -> virtual map between line item and last boq item (position) of parent boq item

			foreach (var assign in itemAssignments)
			{
				if (!packageBoqItemsByHeaderMap.ContainsKey(assign.BoqHeaderFk.Value))
				{
					continue;
				}

				if (!lineItem2ParentBoqItem.ContainsKey(assign.EstLineItemFk))
				{
					var packageBoqItems = packageBoqItemsByHeaderMap[assign.BoqHeaderFk.Value];
					var boqId = new RVPC.IdentificationData() { Id = assign.BoqItemFk.Value, PKey1 = assign.BoqHeaderFk.Value };
					if (!packageBoqItems.TryGetValue(boqId, out var boqItem))
					{
						continue;
					}

					if (!boqItem.BoqItemFk.HasValue)
					{
						continue;
					}
					var parentBoqId = new RVPC.IdentificationData() { Id = boqItem.BoqItemFk.Value, PKey1 = boqItem.BoqHeaderFk };
					if (!packageBoqItems.TryGetValue(parentBoqId, out var parentBoqItem))
					{
						continue;
					}
					lineItem2ParentBoqItem.Add(assign.EstLineItemFk, parentBoqItem);

					var tempChildren = boqItemLogic.GetSortedChildren(parentBoqItem, null, null, true);
					var previous = tempChildren.LastOrDefault();

					//if (!lineitem2PreviousBoqItem.ContainsKey(assign.EstLineItemFk))
					//{
					//	lineitem2PreviousBoqItem.Add(assign.EstLineItemFk, previous);
					//}
				}
			}

			// create boq items for resources
			var resourceId2NewBoqItemMap = preparationData.ResourceId2NewBoqItemMap; // map for resource and new package boq item, use for creating item assignment.
			var targetUpdateBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>(); // new package boq item collection
			foreach (var resource in resources)
			{
				var resourceId = new RVPC.IdentificationData() { Id = resource.Id, PKey1 = resource.EstHeaderFk, PKey2 = resource.EstLineItemFk };

				if (!lineItem2ParentBoqItem.ContainsKey(resource.EstLineItemFk))
				{
					continue;
				}

				var parentItem = lineItem2ParentBoqItem[resource.EstLineItemFk];

				if (!packageBoqStructuresByHeaderMap.ContainsKey(parentItem.BoqHeaderFk))
				{
					continue;
				}
				//BoqItemEntity previous = lineitem2PreviousBoqItem.ContainsKey(resource.EstLineItemFk) ? lineitem2PreviousBoqItem[resource.EstLineItemFk] : null;
				// level should be from 1 to 9
				//int level;
				//if (parentItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root)
				//{
				//	level = 1;
				//}
				//else
				//{
				//	level = parentItem.BoqLineTypeFk + 1;
				//}
				//var targetBoqStructure = packageBoqStructuresByHeaderMap[parentItem.BoqHeaderFk];
				var lineItem = lineItems.FirstOrDefault(e => e.Id == resource.EstLineItemFk);
				var newBoqItem = CreateBoqItemPositionFromResource(resource, parentItem, lineItem); //, previous, targetBoqStructure, level, estHeaderCurrencyId);
				var updateId = new RVPC.IdentificationData() { Id = newBoqItem.Id, PKey1 = newBoqItem.BoqHeaderFk };
				BoqItemEntity update = null;
				if (!targetUpdateBoqMap.TryGetValue(updateId, out update))
				{
					targetUpdateBoqMap.Add(updateId, newBoqItem);
				}

				if (!resourceId2NewBoqItemMap.TryGetValue(resourceId, out var boqItem))
				{
					resourceId2NewBoqItemMap.Add(resourceId, newBoqItem);
				}

				//if (lineitem2PreviousBoqItem.ContainsKey(resource.EstLineItemFk))
				//{
				//	lineitem2PreviousBoqItem.Remove(resource.EstLineItemFk);
				//}

				//lineitem2PreviousBoqItem.Add(resource.EstLineItemFk, newBoqItem);
			}

			preparationData.BoqItemToSave.AddRange(targetUpdateBoqMap.Values.ToList());
			return preparationData;
		}

		/// <summary>
		/// Update to package boq items for new line items which have project boq item assigned and new resources.
		/// And such kind of project boq item is assigned to line items which have item assignment.
		/// </summary>
		/// <param name="lineItems">should linked to project boq. it means BoqHeaderFk and BoqItemFk should not be null.</param>
		/// <param name="resources"></param>
		/// <param name="itemAssignments"></param>
		/// <param name="lineItemsWithAssignment">should linked to project boq. it means BoqHeaderFk and BoqItemFk should not be null.</param>
		/// <param name="preparationData"></param>
		private CreationPreparationFromResource UpdateToPackageBoq4NewLineItemAndResourceWithSameProjectBoqItem(
			IEnumerable<IEstLineItemEntity> lineItems,
			IEnumerable<IScriptEstResource> resources,
			IEnumerable<PrcItemAssignmentEntity> itemAssignments,
			IEnumerable<IEstLineItemEntity> lineItemsWithAssignment,
			CreationPreparationFromResource preparationData
			)
		{
			if (lineItems == null || !lineItems.Any() || resources == null || !resources.Any())
			{
				return preparationData;
			}

			var boqItemLogic = new BoqItemLogic();

			lineItemsWithAssignment = lineItemsWithAssignment.Where(e => e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue).ToList();

			// Get Package Boq Items and Boq Structures
			var packageBoqHeaderIds = itemAssignments.CollectIds(e => e.BoqHeaderFk);
			preparationData = CollectPackageBoqHeaderAndStructures(preparationData, null, packageBoqHeaderIds);
			var packageBoqItemsByHeaderMap = preparationData.PackageBoqItemsByHeaderMap;
			var packageBoqStructuresByHeaderMap = preparationData.PackageBoqStructuresByHeaderMap;

			// map project boq item id and line item
			var sourceBoqId2LineItem = new Dictionary<RVPC.IdentificationData, IEstLineItemEntity>();
			foreach (var lineItem in lineItemsWithAssignment)
			{
				var boqId = new RVPC.IdentificationData() { Id = lineItem.BoqItemFk.Value, PKey1 = lineItem.BoqHeaderFk.Value };
				if (!sourceBoqId2LineItem.TryGetValue(boqId, out var lineItemTemp))
				{
					sourceBoqId2LineItem.Add(boqId, lineItem);
				}
			}

			// map the line item with item assignment and the new line item with the same project boq item id
			var lineItemIdWithAssignment2NewLineItemId2Map = new Dictionary<int, List<int>>();
			foreach (var lineItem in lineItems)
			{
				var boqId = new RVPC.IdentificationData() { Id = lineItem.BoqItemFk.Value, PKey1 = lineItem.BoqHeaderFk.Value };
				if (!sourceBoqId2LineItem.TryGetValue(boqId, out var lineItemWithAssignment))
				{
					continue;
				}

				if (!lineItemIdWithAssignment2NewLineItemId2Map.ContainsKey(lineItemWithAssignment.Id))
				{
					lineItemIdWithAssignment2NewLineItemId2Map.Add(lineItemWithAssignment.Id, new List<int>() { lineItem.Id });
				}
				else
				{
					lineItemIdWithAssignment2NewLineItemId2Map[lineItemWithAssignment.Id].Add(lineItem.Id);
				}
			}

			// collect Parent boq item and last boq Item under the parent boq item
			var lineItem2ParentBoqItem = new Dictionary<int, BoqItemEntity>(); // int -> line item id; BoqItemEntity -> virtual map between line item and package boq item (division) which as the parent boq item
			//var parentBoqId2PreviousBoqItem = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();  // IdentificationData -> package parent boq id; BoqItemEntity -> virtual map between parent boq and last boq item (position) of parent boq item

			foreach (var assign in itemAssignments)
			{
				if (!packageBoqItemsByHeaderMap.ContainsKey(assign.BoqHeaderFk.Value))
				{
					continue;
				}

				if (!lineItemIdWithAssignment2NewLineItemId2Map.ContainsKey(assign.EstLineItemFk))
				{
					continue;
				}

				var newLineItemIds = lineItemIdWithAssignment2NewLineItemId2Map[assign.EstLineItemFk];
				var exist = false;
				foreach (var newLineItemId in newLineItemIds)
				{
					if (lineItem2ParentBoqItem.ContainsKey(newLineItemId))
					{
						exist = true;
						break;
					}
				}

				if (!exist)
				{
					var packageBoqItems = packageBoqItemsByHeaderMap[assign.BoqHeaderFk.Value];
					var boqId = new RVPC.IdentificationData() { Id = assign.BoqItemFk.Value, PKey1 = assign.BoqHeaderFk.Value };
					if (!packageBoqItems.TryGetValue(boqId, out var boqItem))
					{
						continue;
					}

					if (!boqItem.BoqItemFk.HasValue)
					{
						continue;
					}

					// get parent boq item (from line item)
					var parentBoqId = new RVPC.IdentificationData() { Id = boqItem.BoqItemFk.Value, PKey1 = boqItem.BoqHeaderFk };
					if (!packageBoqItems.TryGetValue(parentBoqId, out var parentBoqItem))
					{
						continue;
					}

					if (!parentBoqItem.BoqItemFk.HasValue)
					{
						continue;
					}

					// get grand parent boq item (maybe from project boq position)
					parentBoqId = new RVPC.IdentificationData() { Id = parentBoqItem.BoqItemFk.Value, PKey1 = parentBoqItem.BoqHeaderFk };
					if (!packageBoqItems.TryGetValue(parentBoqId, out parentBoqItem) && parentBoqItem == null)
					{
						continue;
					}

					//var tempChildren = boqItemLogic.GetSortedChildren(parentBoqItem, null, null, true);
					//var previous = tempChildren.LastOrDefault();

					foreach (var newLineItemId in newLineItemIds)
					{
						lineItem2ParentBoqItem.Add(newLineItemId, parentBoqItem);
					}

					//if (!parentBoqId2PreviousBoqItem.TryGetValue(parentBoqId, out var previousTemp))
					//{
					//	parentBoqId2PreviousBoqItem.Add(parentBoqId, previous);
					//}
				}
			}

			var lineItemId2ResourcesMap = BuildLineItemId2ResourcesMap(resources);

			//BoqItemEntity lineItemPrevious = null; // use for generation reference no.
			var targetUpdateBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>(); // new package boq item collection
			var resourceId2NewBoqItemMap = preparationData.ResourceId2NewBoqItemMap; // map for resource and new package boq item, use for creating item assignment.

			foreach (var lineItem in lineItems)
			{
				var lineItemId = new RVPC.IdentificationData() { Id = lineItem.Id, PKey1 = lineItem.EstHeaderFk };
				if (!lineItemId2ResourcesMap.TryGetValue(lineItemId, out var resourcesTemp))
				{
					continue;
				}
				if (!lineItem.BoqHeaderFk.HasValue || !lineItem.BoqItemFk.HasValue || !lineItem2ParentBoqItem.ContainsKey(lineItem.Id))
				{
					continue;
				}

				var targetParentBoq = lineItem2ParentBoqItem[lineItem.Id];
				var targetBoQHeaderId = targetParentBoq.BoqHeaderFk;

				if (!packageBoqStructuresByHeaderMap.ContainsKey(targetBoQHeaderId))
				{
					continue;
				}

				//var parentBoqId = new RVPC.IdentificationData() { Id = targetParentBoq.Id, PKey1 = targetParentBoq.BoqHeaderFk };
				//parentBoqId2PreviousBoqItem.TryGetValue(parentBoqId, out lineItemPrevious);

				//var targetBoqStructure = packageBoqStructuresByHeaderMap[targetBoQHeaderId];
				var newLevelBoqItemResult = CreateDivisionBoqItemFromLineItem(lineItem, targetParentBoq/*, lineItemPrevious, targetBoqStructure*/);
				var newLevelBoqItem = newLevelBoqItemResult.Item1;
				var level = newLevelBoqItemResult.Item2;
				var levelBoqId = new RVPC.IdentificationData() { Id = newLevelBoqItem.Id, PKey1 = newLevelBoqItem.BoqHeaderFk };
				if (!targetUpdateBoqMap.TryGetValue(levelBoqId, out var levelBoq))
				{
					targetUpdateBoqMap.Add(levelBoqId, newLevelBoqItem);
				}

				//BoqItemEntity resourcePrevious = null;
				foreach (var resource in resourcesTemp)
				{
					var resourceId = new RVPC.IdentificationData() { Id = resource.Id, PKey1 = resource.EstHeaderFk, PKey2 = resource.EstLineItemFk };
					var newBoqItem = CreateBoqItemPositionFromResource(resource, newLevelBoqItem, lineItem); //, resourcePrevious, targetBoqStructure, level + 1, estHeaderCurrencyId);
					var updateId = new RVPC.IdentificationData() { Id = newBoqItem.Id, PKey1 = newBoqItem.BoqHeaderFk };
					if (!targetUpdateBoqMap.TryGetValue(updateId, out var update))
					{
						targetUpdateBoqMap.Add(updateId, newBoqItem);
					}

					if (!resourceId2NewBoqItemMap.TryGetValue(resourceId, out var boqItem))
					{
						resourceId2NewBoqItemMap.Add(resourceId, newBoqItem);
					}
					//resourcePrevious = newBoqItem;
				}

				//if (parentBoqId2PreviousBoqItem.TryGetValue(parentBoqId, out var previousTemp))
				//{
				//	parentBoqId2PreviousBoqItem.Remove(parentBoqId);
				//}
				//parentBoqId2PreviousBoqItem.Add(parentBoqId, newLevelBoqItem);
			}

			preparationData.BoqItemToSave.AddRange(targetUpdateBoqMap.Values.ToList());
			return preparationData;
		}

		/// <summary>
		/// Update to package boq items for new line items which have project boq item assigned and new resources.
		/// And such kind of boq header of project boq item is assigned to line items which have item assignment.
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="resources"></param>
		/// <param name="itemAssignments"></param>
		/// <param name="lineItemsWithAssignment"></param>
		/// <param name="isSkipPositionBoqAsDivisionBoq"></param>
		/// <param name="preparationData"></param>
		/// <returns></returns>
		private CreationPreparationFromResource UpdateToPackageBoq4NewLineItemAndResourceWithSameProjectBoqHeader(
			IEnumerable<IEstLineItemEntity> lineItems,
			IEnumerable<IScriptEstResource> resources,
			IEnumerable<PrcItemAssignmentEntity> itemAssignments,
			IEnumerable<IEstLineItemEntity> lineItemsWithAssignment,
			bool isSkipPositionBoqAsDivisionBoq,
			CreationPreparationFromResource preparationData
			)
		{
			if (lineItems == null || !lineItems.Any() || resources == null || !resources.Any())
			{
				return preparationData;
			}

			var boqItemLogic = new BoqItemLogic();

			lineItemsWithAssignment = lineItemsWithAssignment.Where(e => e.BoqHeaderFk.HasValue && e.BoqItemFk.HasValue).ToList();

			// Get Package Boq Items and Boq Structures
			var packageBoqHeaderIds = itemAssignments.CollectIds(e => e.BoqHeaderFk);
			var sourceBoqHeaderIds = lineItems.CollectIds(e => e.BoqHeaderFk);

			preparationData = CollectPackageBoqHeaderAndStructures(preparationData, sourceBoqHeaderIds, packageBoqHeaderIds);

			var sourceBoqItemsByHeaderMap = preparationData.SourceBoqItemsByHeaderMap;
			var packageBoqItemsByHeaderMap = preparationData.PackageBoqItemsByHeaderMap;
			var packageBoqStructuresByHeaderMap = preparationData.PackageBoqStructuresByHeaderMap;

			var sourceBoqItems = sourceBoqItemsByHeaderMap.SelectMany(e => e.Value).ToList();
			//var packageBoqItems = packageBoqItemsByHeaderMap.SelectMany(e => e.Value.Values).ToList();
			var sourceBoqId2ItemMap = sourceBoqItems.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.BoqHeaderFk }, e => e);
			var packageRef2ItemMap = new Dictionary<int, Dictionary<string, BoqItemEntity>>();
			var lineItemId2ItemAssignmentsMap = new Dictionary<int, PrcItemAssignmentEntity>(); // line items with item assignment.
			var sourceBoqHeaderId2LineItemWithAssignmentMap = new Dictionary<int, IEstLineItemEntity>(); // int -> source boq header id; IEstLineItemEntity -> line item with item assignment
			var packageHeader2RootItemMap = new Dictionary<int, BoqItemEntity>();

			foreach (var header2Items in packageBoqItemsByHeaderMap)
			{
				if (!packageRef2ItemMap.ContainsKey(header2Items.Key))
				{
					packageRef2ItemMap.Add(header2Items.Key, new Dictionary<string, BoqItemEntity>());
				}

				var packageBoqItems = header2Items.Value;
				var ref2ItemMap = packageRef2ItemMap[header2Items.Key];
				foreach (var item in packageBoqItems)
				{
					if (item.Value.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root)
					{
						if (!packageHeader2RootItemMap.ContainsKey(item.Value.BoqHeaderFk))
						{
							packageHeader2RootItemMap.Add(item.Value.BoqHeaderFk, item.Value);
						}
						continue;
					}

					if (!ref2ItemMap.TryGetValue(item.Value.Reference, out var boqTemp))
					{
						ref2ItemMap.Add(item.Value.Reference, item.Value);
					}
				}
			}

			foreach (var item in itemAssignments)
			{
				if (!lineItemId2ItemAssignmentsMap.ContainsKey(item.EstLineItemFk))
				{
					lineItemId2ItemAssignmentsMap.Add(item.EstLineItemFk, item);
				}
			}

			foreach (var item in lineItemsWithAssignment)
			{
				if (!item.BoqHeaderFk.HasValue)
				{
					continue;
				}
				if (!sourceBoqHeaderId2LineItemWithAssignmentMap.ContainsKey(item.BoqHeaderFk.Value))
				{
					sourceBoqHeaderId2LineItemWithAssignmentMap.Add(item.BoqHeaderFk.Value, item);
				}
			}

			var lineItemId2ResourcesMap = BuildLineItemId2ResourcesMap(resources);

			var targetUpdateBoqMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>(); // new package boq item collection
			var sourceBoqItemId2TargetBoqItemId = new Dictionary<RVPC.IdentificationData, RVPC.IdentificationData>();
			//var packageParentBoqId2LineItemPreviousMap = new Dictionary<RVPC.IdentificationData, BoqItemEntity>();
			var resourceId2NewBoqItemMap = preparationData.ResourceId2NewBoqItemMap;
			foreach (var lineItem in lineItems)
			{
				var lineItemId = new RVPC.IdentificationData() { Id = lineItem.Id, PKey1 = lineItem.EstHeaderFk };
				if (!lineItemId2ResourcesMap.TryGetValue(lineItemId, out var resourcesTemp))
				{
					continue;
				}
				if (!lineItem.BoqHeaderFk.HasValue || !lineItem.BoqItemFk.HasValue)
				{
					continue;
				}
				var sourceBoqId = new RVPC.IdentificationData() { Id = lineItem.BoqItemFk.Value, PKey1 = lineItem.BoqHeaderFk.Value };
				if (isSkipPositionBoqAsDivisionBoq)
				{
					if (sourceBoqId2ItemMap.TryGetValue(sourceBoqId, out var sourceBoqTemp))
					{
						if (!sourceBoqTemp.BoqItemFk.HasValue)
						{
							continue;
						}
						sourceBoqId = new RVPC.IdentificationData() { Id = sourceBoqTemp.BoqItemFk.Value, PKey1 = lineItem.BoqHeaderFk.Value };
					}
				}
				if (!sourceBoqId2ItemMap.TryGetValue(sourceBoqId, out var sourceBoq))
				{
					continue;
				}
				if (!sourceBoqHeaderId2LineItemWithAssignmentMap.TryGetValue(lineItem.BoqHeaderFk.Value, out var lineItemWithAssignment))
				{
					continue;
				}
				if (!lineItemId2ItemAssignmentsMap.TryGetValue(lineItemWithAssignment.Id, out var itemAssignment))
				{
					continue;
				}
				var packageBoqHeaderId = itemAssignment.BoqHeaderFk.Value;
				if (!packageBoqStructuresByHeaderMap.ContainsKey(packageBoqHeaderId))
				{
					continue;
				}
				var sourceHierarchyItems = new List<BoqItemEntity>();
				BoqItemEntity packageParentBoqItem = null;
				while (sourceBoq != null)
				{
					if (sourceBoq.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root)
					{
						packageParentBoqItem = packageHeader2RootItemMap.ContainsKey(packageBoqHeaderId) ? packageHeader2RootItemMap[packageBoqHeaderId] : null;
						break;
					}

					if (packageRef2ItemMap.ContainsKey(packageBoqHeaderId))
					{
						var ref2Item = packageRef2ItemMap[packageBoqHeaderId];
						if (ref2Item.ContainsKey(sourceBoq.Reference))
						{
							packageParentBoqItem = ref2Item[sourceBoq.Reference];
							break;
						}
					}

					var sourceBoqIdTemp = new RVPC.IdentificationData() { Id = sourceBoq.Id, PKey1 = sourceBoq.BoqHeaderFk };
					if (sourceBoqItemId2TargetBoqItemId.TryGetValue(sourceBoqIdTemp, out var packageBoqId))
					{
						if (targetUpdateBoqMap.TryGetValue(packageBoqId, out var targetBoqItem))
						{
							packageParentBoqItem = targetBoqItem;
							break;
						}
					}
					sourceHierarchyItems.Add(sourceBoq);
					sourceBoq = sourceBoq.BoqItemParent;
				}

				if (packageParentBoqItem == null)
				{
					continue;
				}

				sourceHierarchyItems.Reverse();

				var boqItemIdEnumerator = boqItemLogic.GetNextIds(sourceHierarchyItems.Count).GetEnumerator();
				foreach (var source in sourceHierarchyItems)
				{
					var sourceHierarchyItemId = new RVPC.IdentificationData() { Id = source.Id, PKey1 = source.BoqHeaderFk };
					if (sourceBoqItemId2TargetBoqItemId.TryGetValue(sourceHierarchyItemId, out var tempItem))
					{
						continue;
					}

					if (isSkipPositionBoqAsDivisionBoq && !((source.BoqLineTypeFk >= (int)BoqConstants.EBoqLineType.DivisionLevelFirst &&
						source.BoqLineTypeFk <= (int)BoqConstants.EBoqLineType.DivisionLevelLast) ||
						source.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root))
					{
						continue;
					}

					BoqItemEntity newBoQItemEntity = CloneSourceBoqItem(source, packageBoqHeaderId, packageParentBoqItem);
					boqItemIdEnumerator.MoveNext();
					newBoQItemEntity.Id = boqItemIdEnumerator.Current;
					var updateId = new RVPC.IdentificationData() { Id = newBoQItemEntity.Id, PKey1 = newBoQItemEntity.BoqHeaderFk };
					sourceBoqItemId2TargetBoqItemId.Add(sourceHierarchyItemId, updateId);

					if (!targetUpdateBoqMap.TryGetValue(updateId, out var update))
					{
						targetUpdateBoqMap.Add(updateId, newBoQItemEntity);
					}
					packageParentBoqItem = newBoQItemEntity;
				}
				//var packageBoqStructure = packageBoqStructuresByHeaderMap[packageBoqHeaderId];
				var packageParentId = new RVPC.IdentificationData() { Id = packageParentBoqItem.Id, PKey1 = packageParentBoqItem.BoqHeaderFk };
				//packageParentBoqId2LineItemPreviousMap.TryGetValue(packageParentId, out var lineItemPrevious);
				var newLevelBoqItemResult = CreateDivisionBoqItemFromLineItem(lineItem, packageParentBoqItem/*, lineItemPrevious, packageBoqStructure*/);
				var newLevelBoqItem = newLevelBoqItemResult.Item1;
				var level = newLevelBoqItemResult.Item2;
				var levelBoqId = new RVPC.IdentificationData() { Id = newLevelBoqItem.Id, PKey1 = newLevelBoqItem.BoqHeaderFk };
				if (!targetUpdateBoqMap.TryGetValue(levelBoqId, out var levelBoq))
				{
					targetUpdateBoqMap.Add(levelBoqId, newLevelBoqItem);
				}

				//BoqItemEntity resourcePrevious = null;
				foreach (var resource in resourcesTemp)
				{
					var resourceId = new RVPC.IdentificationData() { Id = resource.Id, PKey1 = resource.EstHeaderFk, PKey2 = resource.EstLineItemFk };
					var newBoqItem = CreateBoqItemPositionFromResource(resource, newLevelBoqItem,lineItem); //, resourcePrevious, packageBoqStructure, level + 1, estHeaderCurrencyId);
					var updateIdTemp = new RVPC.IdentificationData() { Id = newBoqItem.Id, PKey1 = newBoqItem.BoqHeaderFk };
					if (!targetUpdateBoqMap.TryGetValue(updateIdTemp, out var updateTemp))
					{
						targetUpdateBoqMap.Add(updateIdTemp, newBoqItem);
					}

					if (!resourceId2NewBoqItemMap.TryGetValue(resourceId, out var boqItem))
					{
						resourceId2NewBoqItemMap.Add(resourceId, newBoqItem);
					}
					//resourcePrevious = newBoqItem;
				}
				//if (packageParentBoqId2LineItemPreviousMap.TryGetValue(packageParentId, out var previousTemp))
				//{
				//	packageParentBoqId2LineItemPreviousMap.Remove(packageParentId);
				//}
				//packageParentBoqId2LineItemPreviousMap.Add(packageParentId, newLevelBoqItem);
			}

			preparationData.BoqItemToSave.AddRange(targetUpdateBoqMap.Values.ToList());
			return preparationData;
		}

		private BoqItemEntity CloneSourceBoqItem(BoqItemEntity source, int packageBoqHeaderId, BoqItemEntity packageParentBoqItem)
		{
			BoqItemEntity newBoQItemEntity = source.Clone(true);
			newBoQItemEntity.BoqHeaderFk = packageBoqHeaderId;

			newBoQItemEntity.BasBlobsSpecificationFk = null;
			newBoQItemEntity.SurchargePercent = null;
			newBoQItemEntity.FactorDetail = null;

			newBoQItemEntity.BoqCharacterContentPrjFk = null;
			newBoQItemEntity.BoqCharacterContentWorkFk = null;
			newBoQItemEntity.PrcPriceConditionFk = null;
			newBoQItemEntity.BoqItemBasisFk = null;
			newBoQItemEntity.Version = 0;

			if (packageParentBoqItem != null)
			{
				newBoQItemEntity.BoqItemFk = packageParentBoqItem.Id;

				newBoQItemEntity.BoqItemParent = packageParentBoqItem;
				packageParentBoqItem.BoqItemChildren.Add(newBoQItemEntity);
			}
			if ((source.BoqLineTypeFk >= (int)BoqConstants.EBoqLineType.DivisionLevelFirst &&
				source.BoqLineTypeFk <= (int)BoqConstants.EBoqLineType.DivisionLevelLast) ||
				source.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root)
			{
				newBoQItemEntity.BoqLineTypeFk = source.BoqLineTypeFk;
			}
			else
			{
				if (packageParentBoqItem != null)
				{
					if (packageParentBoqItem.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root)
					{
						newBoQItemEntity.BoqLineTypeFk = 1;
					}
					else
					{
						newBoQItemEntity.BoqLineTypeFk = packageParentBoqItem.BoqLineTypeFk + 1;
					}
				}
				newBoQItemEntity.Quantity = 1;
				newBoQItemEntity.QuantityAdj = 1;
				newBoQItemEntity.QuantityDetail = null;
				newBoQItemEntity.SurchargeFactor = null;
				newBoQItemEntity.QuantityAdjDetail = null;
				newBoQItemEntity.BasItemStatusFk = null;

				newBoQItemEntity.Urb1 = newBoQItemEntity.Urb1Oc = newBoQItemEntity.Urb2 =
					newBoQItemEntity.Urb2Oc = newBoQItemEntity.Urb3 = newBoQItemEntity.Urb3Oc =
					newBoQItemEntity.Urb4 = newBoQItemEntity.Urb4Oc = newBoQItemEntity.Urb5 =
					newBoQItemEntity.Urb5Oc = newBoQItemEntity.Urb6 = newBoQItemEntity.Urb6Oc =
					newBoQItemEntity.Price = newBoQItemEntity.PriceOc = newBoQItemEntity.UnitRateFrom =
					newBoQItemEntity.UnitRateFromOc = newBoQItemEntity.UnitRateTo = newBoQItemEntity.UnitRateToOc =
					newBoQItemEntity.Hours = newBoQItemEntity.HoursUnit = newBoQItemEntity.DiscountedUnitprice =
					newBoQItemEntity.DiscountedUnitprice = newBoQItemEntity.DiscountedUnitpriceOc = newBoQItemEntity.DiscountedPrice =
					newBoQItemEntity.DiscountedPriceOc = newBoQItemEntity.LumpsumPrice = newBoQItemEntity.LumpsumPriceOc =
					newBoQItemEntity.Discount = newBoQItemEntity.DiscountOc = newBoQItemEntity.DiscountPercentIt =
					newBoQItemEntity.Finaldiscount = newBoQItemEntity.FinaldiscountOc = 0;
				newBoQItemEntity.IsLumpsum = false;
				newBoQItemEntity.DiscountText = null;
				newBoQItemEntity.DiscountTextTr = null;
				newBoQItemEntity.AAN = null;
				newBoQItemEntity.AGN = null;

				newBoQItemEntity.CommentContractor = null;
				newBoQItemEntity.CommentClient = null;
				newBoQItemEntity.DesignDescriptionNo = null;
				newBoQItemEntity.TransactionId = 0;
				newBoQItemEntity.OrdinalNo = null;
				newBoQItemEntity.PercentComplete = null;
				newBoQItemEntity.ContributionPercent = 0;
				newBoQItemEntity.Approx = false;
			}
			return newBoQItemEntity;
		}

		private Dictionary<RVPC.IdentificationData, List<IScriptEstResource>> BuildLineItemId2ResourcesMap(IEnumerable<IScriptEstResource> resourceList)
		{
			var lineItemId2ResourcesMap = new Dictionary<RVPC.IdentificationData, List<IScriptEstResource>>();

			foreach (var resource in resourceList)
			{
				var lineItemId = new RVPC.IdentificationData() { Id = resource.EstLineItemFk, PKey1 = resource.EstHeaderFk };
				if (!lineItemId2ResourcesMap.TryGetValue(lineItemId, out var resources))
				{
					lineItemId2ResourcesMap.Add(lineItemId, new List<IScriptEstResource>() { resource });
				}
				else
				{
					resources.Add(resource);
				}
			}

			return lineItemId2ResourcesMap;
		}

		private Tuple<BoqItemEntity, int> CreateDivisionBoqItemFromLineItem(
			IEstLineItemEntity lineItem,
			BoqItemEntity packageParentBoq)
			//BoqItemEntity previous,
			//BOQMAIN.BoqStructureEntity packageBoqStructure)
		{
			if (lineItem == null || packageParentBoq == null)
			{
				throw new ArgumentNullException(nameof(lineItem));
			}

			var boqItemLogic = new BoqItemLogic();
			// level should be from 1 to 9
			var level = 1;
			if (packageParentBoq.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root)
			{
				level = 1;
			}
			else
			{
				level = packageParentBoq.BoqLineTypeFk + 1;
			}

			var creationData = new BoqItemCreationData();
			creationData.BoqHeaderFk = packageParentBoq.BoqHeaderFk;
			creationData.LineType = level;
			creationData.ParentItemId = packageParentBoq.Id;
			creationData.DoSave = false;
			var newLevelBoqItem = boqItemLogic.Create(creationData);
			boqItemLogic.GenerateBoqReferenceForItem(newLevelBoqItem, packageParentBoq, packageParentBoq);
			newLevelBoqItem.BriefInfo = new DescriptionTranslateType();
			if (lineItem.DescriptionInfo != null)
			{
				newLevelBoqItem.BriefInfo = new Platform.Common.DescriptionTranslateType(lineItem.DescriptionInfo.Description);

				if (lineItem.DescriptionInfo.VersionTr > 0)
				{
					newLevelBoqItem.BriefInfo.Translated = lineItem.DescriptionInfo.Translated;
					newLevelBoqItem.BriefInfo.Modified = true;
				}
			}

			newLevelBoqItem.BoqItemParent = packageParentBoq;
			newLevelBoqItem.BasUomFk = lineItem.BasUomFk;
			newLevelBoqItem.PrjLocationFk = lineItem.PrjLocationFk;
			newLevelBoqItem.MdcMaterialFk = lineItem.MdcMaterialFk;
			newLevelBoqItem.MdcCostCodeFk = lineItem.MdcCostCodeFk;
			newLevelBoqItem.PrcStructureFk = lineItem.PrcStructureFk;
			newLevelBoqItem.MdcControllingUnitFk = lineItem.MdcControllingUnitFk;
			newLevelBoqItem.Userdefined1 = lineItem.UserDefined1;
			newLevelBoqItem.Userdefined2 = lineItem.UserDefined2;
			newLevelBoqItem.Userdefined3 = lineItem.UserDefined3;
			newLevelBoqItem.Userdefined4 = lineItem.UserDefined4;
			newLevelBoqItem.Userdefined5 = lineItem.UserDefined5;
			newLevelBoqItem.BudgetFixedTotal = lineItem.IsFixedBudget;
			newLevelBoqItem.BudgetFixedUnit = lineItem.IsFixedBudgetUnit;
			newLevelBoqItem.ExternalCode = lineItem.ExternalCode;
			packageParentBoq.BoqItemChildren.Add(newLevelBoqItem);
			return Tuple.Create(newLevelBoqItem, level);
		}

		private BoqItemEntity CreateBoqItemPositionFromResource(
			IScriptEstResource resource,
			BoqItemEntity parent,
			IEstLineItemEntity lineItem
		//,
		//BoqItemEntity previous,
		//BOQMAIN.BoqStructureEntity packageBoqStructure,
		//int level,
		//int? estHeaderCurrencyId
		)
		{
			if (resource == null || parent == null)
			{
				throw new ArgumentNullException(nameof(resource));
			}
			var boqItemLogic = new BoqItemLogic();

			var creationData = new BoqItemCreationData();
			creationData.BoqHeaderFk = parent.BoqHeaderFk;
			creationData.LineType = (int)BoqConstants.EBoqLineType.Position;
			creationData.ParentItemId = parent.Id;
			creationData.DoSave = false;
			var newBoqItem = boqItemLogic.Create(creationData);
			boqItemLogic.GenerateBoqReferenceForItem(newBoqItem, parent, parent);
			newBoqItem.BriefInfo = new DescriptionTranslateType();
			if (resource.DescriptionInfo != null)
			{
				newBoqItem.BriefInfo = new Platform.Common.DescriptionTranslateType(resource.DescriptionInfo.Description);

				if (resource.DescriptionInfo.VersionTr > 0)
				{
					newBoqItem.BriefInfo.Translated = resource.DescriptionInfo.Translated;
					newBoqItem.BriefInfo.Modified = true;
				}
			}

			newBoqItem.Factor = 1;
			newBoqItem.FactorDetail = QuantityToString(newBoqItem.Factor);
			newBoqItem.Quantity = resource.QuantityTotal;
			newBoqItem.QuantityDetail = QuantityToString(newBoqItem.Quantity);
			newBoqItem.QuantityAdj = resource.QuantityTotal;
			newBoqItem.QuantityAdjDetail = QuantityToString(newBoqItem.QuantityAdj);
			newBoqItem.SurchargePercent = null;
			newBoqItem.BasUomFk = resource.BasUomFk;

			var finalPrice = resource.IsCost ? resource.CostTotal : 0;
			newBoqItem.PriceOc = newBoqItem.Price = newBoqItem.Quantity != 0 ? finalPrice / newBoqItem.Quantity : finalPrice;
			newBoqItem.CostOc = newBoqItem.Cost = newBoqItem.PriceOc;
			newBoqItem.CorrectionOc = newBoqItem.Correction = 0;
			newBoqItem.HoursUnit = resource.HoursUnit;
			newBoqItem.Hours = resource.HoursTotal;
			newBoqItem.MdcMaterialFk = resource.MdcMaterialFk;
			newBoqItem.MdcCostCodeFk = resource.MdcCostCodeFk;
			newBoqItem.BudgetTotal = resource.Budget;
			newBoqItem.BudgetPerUnit = resource.BudgetUnit;
			newBoqItem.BudgetFixedTotal = resource.IsFixedBudget;
			newBoqItem.BudgetFixedUnit = resource.IsFixedBudgetUnit;

			newBoqItem.BoqItemParent = parent;
			if (lineItem != null)
			{
				newBoqItem.ExternalCode = lineItem.ExternalCode;
			}
			parent.BoqItemChildren.Add(newBoqItem);
			return newBoqItem;
		}

		private CreationPreparationFromResource CollectPackageBoqHeaderAndStructures(
			CreationPreparationFromResource preparationData,
			IEnumerable<int> sourceBoqHeaderIds, IEnumerable<int> packageBoqHeaderIds)
		{
			var boqHeaderLogic = new BoqHeaderLogic();
			var boqItemLogic = new BoqItemLogic();
			var boqTypeLogic = new BOQMAIN.BoqTypeLogic();

			var sourceBoqItemsByHeaderMap = preparationData.SourceBoqItemsByHeaderMap;
			var packageBoqItemsByHeaderMap = preparationData.PackageBoqItemsByHeaderMap;
			var packageBoqStructuresByHeaderMap = preparationData.PackageBoqStructuresByHeaderMap;

			if (sourceBoqHeaderIds != null && sourceBoqHeaderIds.Any())
			{
				foreach (var boqHeaderId in sourceBoqHeaderIds)
				{
					if (!sourceBoqItemsByHeaderMap.ContainsKey(boqHeaderId))
					{
						var boqItems = boqItemLogic.GetBoqItemList(boqHeaderId, 0, 99);
						sourceBoqItemsByHeaderMap.Add(boqHeaderId, boqItems.ToList());
					}
				}
			}

			if (packageBoqHeaderIds != null && packageBoqHeaderIds.Any())
			{
				// Get Boq Headers
				var packageBoqHeaderIdsLoaded = packageBoqItemsByHeaderMap.Keys;
				var packageBoqHeaderIdsToLoad = packageBoqHeaderIds.Where(e => !packageBoqHeaderIdsLoaded.Contains(e)).ToList();
				var packageBoqHeadersToLoad = boqHeaderLogic.GetSearchList(e => packageBoqHeaderIdsToLoad.Contains(e.Id));
				var packageBoqStructureIdsLoaded = packageBoqStructuresByHeaderMap.Values.CollectIds(e => e.Id);
				var boqStructureIdsToLoad = packageBoqHeadersToLoad.CollectIds(e => e.BoqStructureFk).Where(r => !packageBoqStructureIdsLoaded.Contains(r)).ToList();
				var boqStructuresToLoad = boqTypeLogic.GetBoqStructureList(boqStructureIdsToLoad);
				var boqStructureDetailsToLoad = boqTypeLogic.GetBoqStructureDetails(boqStructureIdsToLoad);

				foreach (var boqHeaderId in packageBoqHeaderIdsToLoad)
				{
					// get Boq Items
					if (!packageBoqItemsByHeaderMap.ContainsKey(boqHeaderId))
					{
						var boqItems = boqItemLogic.GetBoqItemList(boqHeaderId, 0, 99).ToList();
						packageBoqItemsByHeaderMap.Add(boqHeaderId, boqItems.ToDictionary(e => new RVPC.IdentificationData() { Id = e.Id, PKey1 = e.BoqHeaderFk }));
					}

					// Get Boq Structures and Details
					var boqHeader = packageBoqHeadersToLoad.FirstOrDefault(e => e.Id == boqHeaderId);
					if (boqHeader == null || !boqHeader.BoqStructureFk.HasValue)
					{
						continue;
					}
					var boqStructure = boqStructuresToLoad.FirstOrDefault(e => e.Id == boqHeader.BoqStructureFk.Value);
					if (boqStructure != null)
					{
						var details = boqStructureDetailsToLoad.Where(e => e.BoqStructureFk == boqHeader.BoqStructureFk.Value).ToList();
						if (details != null)
						{
							boqStructure.BoqStructureDetailEntities = details;
						}

						if (!packageBoqStructuresByHeaderMap.ContainsKey(boqHeaderId))
						{
							packageBoqStructuresByHeaderMap.Add(boqHeaderId, boqStructure);
						}
					}
				}
			}
			return preparationData;
		}
	}
}
