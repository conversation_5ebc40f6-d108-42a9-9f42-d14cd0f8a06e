import cypress from "cypress";
import { tile, app, cnt, sidebar, commonLocators, btn, apiParameters } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _commonAPI, _estimatePage, _validate, _mainView, _boqPage, _modalView, _logesticPage, _controllingUnit, _projectPage } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import apiConstantData from "cypress/constantData/apiConstantData";
import Buttons from "cypress/locators/buttons";

const CONDITION_CODE = _common.generateRandomString(3)
const CONDITION_DESC = _common.generateRandomString(3)
const DISPATCH_HDESC1 = _common.generateRandomString(3)
const LOGISTICB_JOB1 = _common.generateRandomString(3)

let CONTAINER_DATA_RECORD
let CONTAINERS_CONTROLLING_UNITS
let CONTROLLING_UNIT_A_PARAMETERS: DataCells, CONTROLLING_UNIT_B_PARAMETERS: DataCells
let ALLOCATION_FOR_PLANTS_PARAMETER: DataCells
let MODAL_PLANT_ALLOCATION
let CONTAINERS_PLANT,
    CONTAINER_COLUMNS_PLANT, CONTAINER_COLUMNS_SETTLEMENT_STRUCTURE
let CONTAINER_COLUMNS_JOBS;
let CONTAINER_JOBS;
let CONTAINER_COLUMNS_CONDITIONS
let CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS
let CONTAINER_COLUMNS_DISPATCHING_HEADER,
    CONTAINERS_DISPATCHING_HEADER, CONTAINERS_EXPECTED,
    CONTAINERS_PLANT_PRICE_LISTS

describe("LRM- 1.100| Verify the structure container in settlement module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("LRM/lgm-1.100-verify-the-structure-container-in-settlement-module.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNITS
            CONTROLLING_UNIT_A_PARAMETERS = {
                [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
                [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true", "true"]
            }
            CONTROLLING_UNIT_B_PARAMETERS = {
                [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY0, CONTAINERS_CONTROLLING_UNITS.QUANTITY1, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
                [app.GridCells.UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true", "true"],
                [app.GridCells.IS_BILLING_ELEMENT]: ["true", "true", "true"]
            }
            CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;
            CONTAINER_COLUMNS_SETTLEMENT_STRUCTURE = this.data.CONTAINER_COLUMNS.SETTLEMENT_STRUCTURE
            CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
            MODAL_PLANT_ALLOCATION = this.data.MODAL.PLANT_ALLOCATION;
            CONTAINER_COLUMNS_JOBS = this.data.CONTAINER_COLUMNS.JOBS
            CONTAINER_JOBS = this.data.CONTAINERS.JOBS
            CONTAINER_COLUMNS_CONDITIONS = this.data.CONTAINER_COLUMNS.CONDITIONS
            CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS = this.data.CONTAINERS.WORK_OPERATION_TYPE_CONDITIONS
            CONTAINER_COLUMNS_DISPATCHING_HEADER = this.data.CONTAINER_COLUMNS.DISPATCHING_HEADER
            CONTAINERS_DISPATCHING_HEADER = this.data.CONTAINERS.DISPATCHING_HEADER
            CONTAINER_DATA_RECORD = this.data.CONTAINERS.DATA_RECORD;
            CONTAINERS_EXPECTED = this.data.CONTAINERS.EXPECTED
            CONTAINERS_PLANT_PRICE_LISTS = this.data.CONTAINERS.PLANT_PRICE_LISTS
        })
            .then(() => {
                cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                _common.waitForLoaderToDisappear()
                _commonAPI.getAccessToken()
                    .then((result) => {
                        cy.log(`Token Retrieved: ${result.token}`);
                    });
            })
    })

    after(() => {
        cy.LOGOUT();
    });

    it("TC - API call to assign logged-in user a clerk", function () {
        _commonAPI.getULoggedInUserId(Cypress.env("USER_NAME"))
            .then(() => {
                _commonAPI.assignUserToClerk(Cypress.env("API_LOGGED_IN_USER_ID"), Cypress.env("USER_NAME"), apiConstantData.CONSTANT.SMIJ)
            })
    });
    it("TC - Assign uom to the calender", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CALENDAR)
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CALENDER).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SCHEDULE, app.FooterTab.CALENDARS)
        })
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.SCHEDULE)
        _common.search_inSubContainer(cnt.uuid.SCHEDULE, CONTAINER_DATA_RECORD.DE)
        _common.select_rowHasValue(cnt.uuid.SCHEDULE, CONTAINER_DATA_RECORD.DE)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.SCHEDULE, app.GridCells.BAS_UOM_DAY_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT.UOM);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })
    it('TC - API: Create plant list type and plant price list under customizing', function () {
        let PLANT_LIST_TYPE: DataCells = {
            [app.GridCells.IS_LIVE]: "true"
        }
        _commonAPI.createPlantListType(PLANT_LIST_TYPE)

            .then(() => {
                cy.log(`API_PLANT_LIST_TYPE_ID_1: ${Cypress.env('API_PLANT_LIST_TYPE_ID_1')}`)
                let PLANT_PRICE_LIST: DataCells = {
                    [app.GridCells.CONTEXT_FK]: apiConstantData.ID.PLANT_CONTEXT_RIB_DEMO,
                    [app.GridCells.PRICE_LIST_TYPE_FK]: Cypress.env('API_PLANT_LIST_TYPE_ID_1'),
                    [app.GridCells.CURRENCY]: apiConstantData.ID.CURRENCY_EUR,
                    [app.GridCells.UOM_FK]: apiConstantData.ID.UOM_BAGS,
                    [app.GridCells.CALCULATION_TYPE_FK]: apiConstantData.ID.CALCULATION_TYPE_AVERAGE_CATALOG_VALUE,
                    [app.GridCells.PERCENT]: CONTAINER_DATA_RECORD.PERCENT,
                    [app.GridCells.REFERENCE_YEAR]: CONTAINER_DATA_RECORD.REFERENCE_YEAR

                }
                _commonAPI.createPlantPriceList(PLANT_PRICE_LIST)
            })
    })

    it('TC - API: Create plant type', function () {
        let PLANT_TYPE: DataCells = {
            [app.GridCells.IS_CLUSTER]: "true"
        }
        _commonAPI.createPlantType(PLANT_TYPE)
    })
    it('TC - API: Create work operation type and plant type', function () {
        let WORK_OPERATION_TYPE: DataCells = {
            [app.GridCells.IS_HIRE]: "true",
            [app.GridCells.UOM]: apiConstantData.ID.UOM_DAY,
            [app.GridCells.IS_LIVE]: "true",
            [app.GridCells.IS_DEFAULT]: "true"

        }
        _commonAPI.createWorkOperationType(WORK_OPERATION_TYPE)
            .then(() => {
                let PLANT_TYPE: DataCells = {
                    [app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_ID_1`),
                    [app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_ID_1`),
                    [app.GridCells.IS_TIMEKEEPING_DEFAULT]: "true",
                    [app.GridCells.IS_DEFAULT]: "true"
                }
                _commonAPI.createWorkOperationPlantType(PLANT_TYPE)
            })
    })
    it("TC - API: Create project A with logistic job and controlling unit", function () {
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _commonAPI.createProject()
            .then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
                _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_A_PARAMETERS)
            });
    })
    it("TC - API: Create project B with logistic job and controlling unit", function () {
        _commonAPI.createProject()
            .then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2')).pinnedItem();
                cy.wait(1000)
                _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_2'), 2, CONTROLLING_UNIT_B_PARAMETERS)
            });
    })
    it('TC - API: Create plant and controlling unit, price list under plant', function () {
        let PLANT: DataCells = {
            [apiParameters.Keys.PLANT_STATUS_ID]: apiConstantData.ID.PLANT_STATUS_PURCHASING,
            [apiParameters.Keys.PLANT_GROUP_ID]: apiConstantData.ID.PLANT_GROUP_CU_01,
            [apiParameters.Keys.IS_LIVE]: "true",
            [apiParameters.Keys.PLANT_TYPE_ID]: Cypress.env(`API_PLANT_TYPE_ID_1`),
            [apiParameters.Keys.PRC_STRUCTURE_ID]: apiConstantData.ID.PROCUREMENT_STRUCTURE_G_EQUIPMENT,
            [apiParameters.Keys.UOM_ID]: apiConstantData.ID.UOM_DAY,
            [apiParameters.Keys.PLANT_KIND_ID]: apiConstantData.ID.PLANT_KIND_OWNERSHIP,
            [apiParameters.Keys.CLERK_OWNER_ID]: apiConstantData.ID.CLERK_SMIJ,
            [apiParameters.Keys.CLERK_RESPONSIBLE_ID]: apiConstantData.ID.CLERK_FI
        }
        _commonAPI.createPlant(PLANT)
            .then(() => {
                let CNT_PLANT: DataCells = {
                    [apiParameters.Keys.PLANT_ID]: Cypress.env(`API_PLANT_ID_1`),
                    [apiParameters.Keys.CONTEXT_ID]: apiConstantData.ID.PLANT_CONTEXT_RIB_DEMO_PROJECT_CONTEXT,
                    [apiParameters.Keys.PROJECT_ID]: Cypress.env('API_PROJECT_ID_1'),
                    [apiParameters.Keys.CONTROLLING_UNIT_ID]: Cypress.env(`API_CNT_ID_0`)

                }
                _commonAPI.createControllingUnitUnderPlant(CNT_PLANT)
            })
            .then(() => {
                let PLANT_PRICE_LIST: DataCells = {
                    [apiParameters.Keys.PLANT_ID]: Cypress.env(`API_PLANT_ID_1`),
                    [apiParameters.Keys.PRICE_LIST_ID]: Cypress.env(`API_PLANT_PRICE_LIST_ID_1`),
                    [apiParameters.Keys.IS_MANUAL]: "true",
                    [apiParameters.Keys.PRICE_PORTION_1]: "20",
                    [apiParameters.Keys.PRICE_PORTION_2]: "30",
                    [apiParameters.Keys.PRICE_PORTION_3]: "40",
                    [apiParameters.Keys.PRICE_PORTION_4]: "50",
                    [apiParameters.Keys.PRICE_PORTION_5]: "60",
                    [apiParameters.Keys.PRICE_PORTION_6]: "70",
                    [apiParameters.Keys.UOM_ID]: apiConstantData.ID.UOM_DAY

                }
                _commonAPI.createPriceListUnderPlant(PLANT_PRICE_LIST)
            })
    })

    it('TC - Create plant location record from wizard', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        cy.wait(1000)//required to load page
        ALLOCATION_FOR_PLANTS_PARAMETER = {
            [commonLocators.CommonLabels.JOB]: Cypress.env('API_PROJECT_NUMBER_1'),
            [app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_CODE_1`),
            [commonLocators.CommonLabels.ALLOCATED_FROM]: MODAL_PLANT_ALLOCATION.ALLOCATED_FROM
        },
            _common.openTab(app.TabBar.LOCATIONS).then(() => {
                _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
                _common.clear_subContainerFilter(cnt.uuid.PLANT)
                _common.search_inSubContainer(cnt.uuid.PLANT, Cypress.env(`API_PLANT_DESC_1`))
            });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, Cypress.env(`API_PLANT_DESC_1`))
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_INITIAL_ALLOCATION_FOR_PLANTS)
        _common.waitForLoaderToDisappear()
        _logesticPage.create_initialAllocationForPlants_fromWizard(ALLOCATION_FOR_PLANTS_PARAMETER,Cypress.env(`API_PLANT_TYPE_DESC_1`))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT,Cypress.env(`API_PLANT_DESC_1`))
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create job record in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS, 0);
            _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_JOBS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_JOBS.controllingunitfk, CONTAINER_COLUMNS_JOBS.pricinggroupfk], cnt.uuid.JOBS)
        });
        _common.clear_subContainerFilter(cnt.uuid.JOBS)
        _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.maximizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
        cy.wait(1000)//required wait to enable data input fields
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to save data
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.CONTROLLING_UNIT_FK, Cypress.env(`API_CNT_CODE_1`), commonLocators.CommonKeys.GRID)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.minimizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
    })
    it("TC - Create job record in logistic price condition module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_PRICE_CONDITION)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONDITIONS, app.FooterTab.CONDITIONS);
            _common.setup_gridLayout(cnt.uuid.CONDITIONS, CONTAINER_COLUMNS_CONDITIONS)
            _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        });
        _common.maximizeContainer(cnt.uuid.CONDITIONS)
        _common.create_newRecord(cnt.uuid.CONDITIONS)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, CONDITION_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, CONDITION_DESC)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.DEPARTURE_RATING_PERCENT, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PERCENT)
        _common.set_cellCheckboxValue(cnt.uuid.CONDITIONS, app.GridCells.IS_HANDLING_CHARGE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONDITIONS)
        _common.select_rowHasValue(cnt.uuid.CONDITIONS, CONDITION_CODE)
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.FooterTab.PLANT_CATALOG_PRICELISTS);
        });
        _common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, Cypress.env(`API_PLANT_PRICE_LIST_DESC_1`))

        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_ITEM, app.FooterTab.WORK_OPERATION_TYPE_CONDITIONS);
            _common.create_newRecord(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_WORK_OPERATION_TYPE_CODE_1`))
        });
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        _common.editModalDropdown_WithCaret(commonLocators.CommonLabels.PLANT_PRICING_GROUP)
        _common.select_ItemFromPopUpList(commonLocators.CommonKeys.LIST, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PLANT_PRICING_GROUP)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Assign price condition in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS);
            _common.clear_subContainerFilter(cnt.uuid.JOBS)
            _common.waitForLoaderToDisappear()
            _common.maximizeContainer(cnt.uuid.JOBS)
            _common.waitForLoaderToDisappear()
            _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        });
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.JOBS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, LOGISTICB_JOB1)
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PRICE_CONDITION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONDITION_CODE)
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_JOBS.BUSINESS_PARTNER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create record in dispatching notes", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.create_newRecord(cnt.uuid.DISPATCHING_HEADER)
        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, DISPATCH_HDESC1)
        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, CONTAINERS_DISPATCHING_HEADER.EFFECTIVE_DATE[0])
        _common.clickOn_activeContainerButton(cnt.uuid.DISPATCHING_HEADER, btn.IconButtons.BTN_DEFAULT)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB1_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.JOB_2_FK, LOGISTICB_JOB1, commonLocators.CommonKeys.GRID)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        cy.wait(1000)//wait required to load value
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE, "DISPATCH_CODE1")
        cy.wait(1000)//wait required to load value
        _common.minimizeContainer(cnt.uuid.DISPATCHING_HEADER)
    })
    it("TC - Create record in dispatching records", function () {
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        cy.wait(1000)//wait required to load search record
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_HEADER, Cypress.env("DISPATCH_CODE1"))
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
        });
        _common.create_newRecord(cnt.uuid.DISPATCHING_RECORD)
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.RECORD_TYPE_FK, commonLocators.CommonKeys.GRID, CONTAINERS_DISPATCHING_HEADER.RECORD_TYPE)
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_RECORD, app.GridCells.ARTICLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,Cypress.env(`API_PLANT_CODE_1`))
        cy.wait(1000) //required wait to enable work operations type cell
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })
    it("TC - Change dispatch header status", function () {
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env("DISPATCH_CODE1"))
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.TRANSPORT_PLANNING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.PICKING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.LOADED)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        cy.wait(1000)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.DELIVERED)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
    })
    it("TC - Create settlement in settlement module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.SETTLEMENT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.START_SETTLEMENT_BATCH)
        _common.edit_inputField_fromModal_byClass(app.ModalInputFields.DUE_DATE, CONTAINERS_EXPECTED.DUE_DATE[0])
        _common.clickOn_checkboxUnderModal_byClass(app.ModalInputFields.IS_TEST_RUN, commonLocators.CommonKeys.UNCHECK)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.wait(2000) // Added this wait as script is getting failed due to modal is taking time to load
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        cy.wait(2000) // Added this wait as script is getting failed due to modal is taking time to load
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        _common.clear_subContainerFilter(cnt.uuid.SETTLEMENT)
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
    })
    it("TC - Verify settlemet structure filter for project in settlement module", function () {
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_STRUCTURE, app.FooterTab.SETTLEMENT_STRUCTURE, 1);
            _common.setup_gridLayout(cnt.uuid.SETTLEMENT_STRUCTURE, CONTAINER_COLUMNS_SETTLEMENT_STRUCTURE)
            _common.waitForLoaderToDisappear()
        });
        _common.add_filterHierachicalStructure(cnt.uuid.SETTLEMENT_STRUCTURE, commonLocators.CommonLabels.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.clear_containerFilter(cnt.uuid.SETTLEMENT_STRUCTURE)
        _common.search_inContainer(cnt.uuid.SETTLEMENT_STRUCTURE, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT_STRUCTURE, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.toggle_radioFiledInContainer(commonLocators.CommonKeys.SELECT_RADIO_BUTTON, cnt.uuid.SETTLEMENT_STRUCTURE, app.GridCells.MARKER)
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT, app.GridCells.PROJECT_FK, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_ITEMS, app.FooterTab.SETTLEMENT_ITEMS)
        });
        cy.REFRESH_CONTAINER()
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT_ITEMS, Cypress.env("DISPATCH_CODE1"))
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load value
        _common.saveCellDataToEnv(cnt.uuid.SETTLEMENT_ITEMS, app.GridCells.PRICE_TOTAL_QTY, "PRICE_TOTAL")
        cy.wait(1000)//wait required to load value
    })
    it("TC - Verify settlemet structure filter for project with price total", function () {
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_STRUCTURE, app.FooterTab.PACKAGE_STRUCTURE, 1);
        });
        _common.add_filterHierachicalStructure(cnt.uuid.SETTLEMENT_STRUCTURE, commonLocators.CommonLabels.PROJECT)
        _common.clear_containerFilter(cnt.uuid.SETTLEMENT_STRUCTURE)
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.activateToolbarButton_inContainer(cnt.uuid.SETTLEMENT_STRUCTURE, Buttons.IconButtons.ICO_SELECTION_MULTI)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT_STRUCTURE, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.toggle_radioFiledInContainer(commonLocators.CommonKeys.SELECT_RADIO_BUTTON, cnt.uuid.SETTLEMENT_STRUCTURE, app.GridCells.MARKER)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT, app.GridCells.PROJECT_FK, Cypress.env('API_PROJECT_NUMBER_2'))

        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_STRUCTURE, app.FooterTab.PACKAGE_STRUCTURE, 1);
        });
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT_STRUCTURE, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.SETTLEMENT_STRUCTURE, app.GridCells.PRICE_TOTAL_QTY, Cypress.env("PRICE_TOTAL"))

    })

})
