/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable, InjectionToken } from '@angular/core';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import { IPrcPaymentScheduleEntity } from '../model/entities';
import {
	BasicsSharedInvoiceTypeLookupService,
	BasicsSharedPaymentScheduleLayoutService,
	BasicsSharedProcurementPaymentScheduleStatusLookupService,
	BasicsSharedStatusIconService
} from '@libs/basics/shared';
import { createLookup, FieldType, ILayoutConfiguration } from '@libs/ui/common';
import { IBasicsCustomizeInvoiceTypeEntity, IBasicsCustomizeProcurementPaymentScheduleStatusEntity } from '@libs/basics/interfaces';

/**
 * Procurement common payment schedule layout service token
 */
export const PROCUREMENT_COMMON_PAYMENT_SCHEDULE_LAYOUT_TOKEN = new InjectionToken<ProcurementCommonPaymentScheduleLayoutService>('ProcurementCommonPaymentScheduleLayoutService');

/**
 * Procurement common payment schedule layout service
 */
@Injectable({
	providedIn: 'root'
})
export class ProcurementCommonPaymentScheduleLayoutService extends BasicsSharedPaymentScheduleLayoutService<IPrcPaymentScheduleEntity> {
	protected constructor(statusIconService: BasicsSharedStatusIconService<IBasicsCustomizeProcurementPaymentScheduleStatusEntity, IPrcPaymentScheduleEntity>) {
		const customLayout = {
			groups: [{
				gid: 'basicData',
				title: {text: 'Basic Data', key: 'cloud.common.entityProperties'},
				attributes: [
					'InvTypeFk',
					'Description',
					'PrcPsStatusFk'
				]
			}],
			labels: {
				...prefixAllTranslationKeys('procurement.common.', {
					'Description': {text: 'Description', key: 'paymentDescription'},
					'InvTypeFk': {text: 'Invoice Type', key: 'invoiceType'}
				}),
				...prefixAllTranslationKeys('cloud.common.', {
					'PrcPsStatusFk': {text: 'Status', key: 'entityStatus'}
				})
			},
			overloads: {
				PrcPsStatusFk: {
					readonly: true,
					type: FieldType.Lookup,
					lookupOptions: createLookup({
						dataServiceToken: BasicsSharedProcurementPaymentScheduleStatusLookupService,
						displayMember: 'DescriptionInfo.Translated',
						imageSelector: statusIconService,
					})
				},
				InvTypeFk: {
					type: FieldType.Lookup,
					lookupOptions: createLookup<IPrcPaymentScheduleEntity, IBasicsCustomizeInvoiceTypeEntity>({
						dataServiceToken: BasicsSharedInvoiceTypeLookupService,
						showClearButton: true
					})
				}
			}
		};
		super(customLayout as ILayoutConfiguration<IPrcPaymentScheduleEntity>);
	}
}