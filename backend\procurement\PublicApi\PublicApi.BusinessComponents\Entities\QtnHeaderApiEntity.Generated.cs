﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Procurement.PublicApi.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Procurement.PublicApi.BusinessComponents.QtnHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("QTN_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(7)]
    public partial class QtnHeaderApiEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new QtnHeaderApiEntity object.
        /// </summary>
        public QtnHeaderApiEntity()
        {
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("StatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int QtnStatusId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string QtnStatusDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string CompanyCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public virtual int? ProjectId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ProjectCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ProjectDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public virtual int? ClerkPrcId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkPrcCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkPrcDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public virtual int? ClerkReqId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkReqCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkReqDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_ID", TypeName = "int", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RfqHeaderFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int RfqHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_CODE", TypeName = "nvarchar(16)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string RfqHeaderCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for RfqHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_DESC", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string RfqHeaderDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 17)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CurrencyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 18)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string CurrencyDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Exchangerate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 19)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal Exchangerate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_ID", TypeName = "int", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermFiFk")]
        public virtual int? PaymentTermFiId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_CODE", TypeName = "nvarchar(16)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermFiCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_DESC", TypeName = "nvarchar(2000)", Order = 22)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermFiDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_ID", TypeName = "int", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermPaFk")]
        public virtual int? PaymentTermPaId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_CODE", TypeName = "nvarchar(16)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermPaCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_DESC", TypeName = "nvarchar(2000)", Order = 25)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermPaDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 26)]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 27)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 28)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public virtual string SearchPattern {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateQuoted in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_QUOTED", TypeName = "date", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateQuoted")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateQuoted {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateReceived in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_RECEIVED", TypeName = "date", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateReceived")]
        public virtual System.DateTime? DateReceived {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DatePricefixing in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_PRICEFIXING", TypeName = "date", Order = 31)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DatePricefixing")]
        public virtual System.DateTime? DatePricefixing {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_TYPE_ID", TypeName = "int", Order = 32)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int QtnTypeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 33)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string QtnTypeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinesspartnerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int BusinesspartnerId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinesspartnerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 35)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string BusinesspartnerDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SubsidiaryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 36)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public virtual int? SubsidiaryId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SubsidiaryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 37)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SubsidiaryDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public virtual int? SupplierId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcIncotermId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_ID", TypeName = "int", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IncotermFk")]
        public virtual int? PrcIncotermId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcIncotermDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_DESC", TypeName = "nvarchar(2000)", Order = 42)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcIncotermDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Isvalidated in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISVALIDATED", TypeName = "bit", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsValidated")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Isvalidated {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Isexcluded in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISEXCLUDED", TypeName = "bit", Order = 44)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsExcluded")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Isexcluded {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Isshortlisted in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISSHORTLISTED", TypeName = "bit", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsShortlisted")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Isshortlisted {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 47)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined1 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined2 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined3 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined4 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined5 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OverallDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT", TypeName = "numeric(19,7)", Order = 52)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscount {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OverallDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscountOc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OverallDiscountPercent in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_PERCENT", TypeName = "numeric(9,3)", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscountPercent {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 55)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? DateDelivery {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ExternalCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXTERNAL_CODE", TypeName = "nvarchar(252)", Order = 56)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ExternalCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdEvaluationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATION_ID", TypeName = "int", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("EvaluationFk")]
        public virtual int? BpdEvaluationId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdEvaluationCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATION_CODE", TypeName = "nvarchar(16)", Order = 58)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string BpdEvaluationCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdEvaluationDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATION_DESC", TypeName = "nvarchar(252)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BpdEvaluationDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_ID", TypeName = "int", Order = 60)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("QtnHeaderFk")]
        public virtual int? QtnHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_CODE", TypeName = "nvarchar(16)", Order = 61)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string QtnHeaderCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_DESC", TypeName = "nvarchar(252)", Order = 62)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string QtnHeaderDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for QtnVersion in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_VERSION", TypeName = "int", Order = 63)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("QuoteVersion")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int QtnVersion {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcBillingSchemaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_ID", TypeName = "int", Order = 69)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BillingSchemaFk")]
        public virtual int? MdcBillingSchemaId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcBillingSchemaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 70)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcBillingSchemaDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_ID", TypeName = "int", Order = 71)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermAdFk")]
        public virtual int? PaymentTermAdId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_CODE", TypeName = "nvarchar(16)", Order = 72)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermAdCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_DESC", TypeName = "nvarchar(2000)", Order = 73)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermAdDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Isidealbidder in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISIDEALBIDDER", TypeName = "bit", Order = 74)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsIdealBidder")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Isidealbidder {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateEffective in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_EFFECTIVE", TypeName = "date", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateEffective")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateEffective {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for VatgroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public virtual int? VatgroupId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for VatgroupDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 77)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string VatgroupDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int SalesTaxMethodId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 79)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string SalesTaxMethodDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefinedDate01 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE01", TypeName = "date", Order = 80)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? UserDefinedDate01 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 81)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int LanguageId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AmountDiscountBasis in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNTBASIS", TypeName = "numeric(19,7)", Order = 82)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountDiscountBasis {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AmountDiscountBasisOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNTBASIS_OC", TypeName = "numeric(19,7)", Order = 83)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountDiscountBasisOc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PercentDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PERCENT_DISCOUNT", TypeName = "numeric(10,2)", Order = 84)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal PercentDiscount {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AmountDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNT", TypeName = "numeric(19,7)", Order = 85)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountDiscount {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AmountDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 86)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal AmountDiscountOc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 87)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcConfigurationId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcConfigurationDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_DESC", TypeName = "nvarchar(2000)", Order = 88)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcConfigurationDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual int? BasLanguageFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateAwardDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_AWARDDEADLINE", TypeName = "date", Order = 90)]
        public virtual System.DateTime? DateAwardDeadline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateQuoteDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_QUOTEDEADLINE", TypeName = "date", Order = 91)]
        public virtual System.DateTime? DateQuoteDeadline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TimeQuoteDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TIME_QUOTEDEADLINE", TypeName = "time", Order = 92)]
        public virtual global::System.TimeSpan? TimeQuoteDeadline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ContactId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_FK", TypeName = "int", Order = 93)]
        [RIB.Visual.Platform.Common.InternalApiField("ContactFk")]
        public virtual int? ContactId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ContactFirstName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_FIRST_NAME", TypeName = "nvarchar(252)", Order = 94)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ContactFirstName {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ContactFamilyName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_FAMILY_NAME", TypeName = "nvarchar(252)", Order = 95)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ContactFamilyName {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 96)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BasLanguageDesc {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            QtnHeaderApiEntity obj = new QtnHeaderApiEntity();
            obj.Id = Id;
            obj.QtnStatusId = QtnStatusId;
            obj.QtnStatusDescription = QtnStatusDescription;
            obj.CompanyId = CompanyId;
            obj.CompanyCode = CompanyCode;
            obj.ProjectId = ProjectId;
            obj.ProjectCode = ProjectCode;
            obj.ProjectDescription = ProjectDescription;
            obj.ClerkPrcId = ClerkPrcId;
            obj.ClerkPrcCode = ClerkPrcCode;
            obj.ClerkPrcDescription = ClerkPrcDescription;
            obj.ClerkReqId = ClerkReqId;
            obj.ClerkReqCode = ClerkReqCode;
            obj.ClerkReqDescription = ClerkReqDescription;
            obj.RfqHeaderId = RfqHeaderId;
            obj.RfqHeaderCode = RfqHeaderCode;
            obj.RfqHeaderDescription = RfqHeaderDescription;
            obj.CurrencyId = CurrencyId;
            obj.CurrencyDescription = CurrencyDescription;
            obj.Exchangerate = Exchangerate;
            obj.PaymentTermFiId = PaymentTermFiId;
            obj.PaymentTermFiCode = PaymentTermFiCode;
            obj.PaymentTermFiDescription = PaymentTermFiDescription;
            obj.PaymentTermPaId = PaymentTermPaId;
            obj.PaymentTermPaCode = PaymentTermPaCode;
            obj.PaymentTermPaDescription = PaymentTermPaDescription;
            obj.Code = Code;
            obj.Description = Description;
            obj.SearchPattern = SearchPattern;
            obj.DateQuoted = DateQuoted;
            obj.DateReceived = DateReceived;
            obj.DatePricefixing = DatePricefixing;
            obj.QtnTypeId = QtnTypeId;
            obj.QtnTypeDescription = QtnTypeDescription;
            obj.BusinesspartnerId = BusinesspartnerId;
            obj.BusinesspartnerDescription = BusinesspartnerDescription;
            obj.SubsidiaryId = SubsidiaryId;
            obj.SubsidiaryDescription = SubsidiaryDescription;
            obj.SupplierId = SupplierId;
            obj.SupplierCode = SupplierCode;
            obj.SupplierDescription = SupplierDescription;
            obj.PrcIncotermId = PrcIncotermId;
            obj.PrcIncotermDescription = PrcIncotermDescription;
            obj.Isvalidated = Isvalidated;
            obj.Isexcluded = Isexcluded;
            obj.Isshortlisted = Isshortlisted;
            obj.Remark = Remark;
            obj.UserDefined1 = UserDefined1;
            obj.UserDefined2 = UserDefined2;
            obj.UserDefined3 = UserDefined3;
            obj.UserDefined4 = UserDefined4;
            obj.UserDefined5 = UserDefined5;
            obj.OverallDiscount = OverallDiscount;
            obj.OverallDiscountOc = OverallDiscountOc;
            obj.OverallDiscountPercent = OverallDiscountPercent;
            obj.DateDelivery = DateDelivery;
            obj.ExternalCode = ExternalCode;
            obj.BpdEvaluationId = BpdEvaluationId;
            obj.BpdEvaluationCode = BpdEvaluationCode;
            obj.BpdEvaluationDescription = BpdEvaluationDescription;
            obj.QtnHeaderId = QtnHeaderId;
            obj.QtnHeaderCode = QtnHeaderCode;
            obj.QtnHeaderDescription = QtnHeaderDescription;
            obj.QtnVersion = QtnVersion;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            obj.MdcBillingSchemaId = MdcBillingSchemaId;
            obj.MdcBillingSchemaDescription = MdcBillingSchemaDescription;
            obj.PaymentTermAdId = PaymentTermAdId;
            obj.PaymentTermAdCode = PaymentTermAdCode;
            obj.PaymentTermAdDescription = PaymentTermAdDescription;
            obj.Isidealbidder = Isidealbidder;
            obj.DateEffective = DateEffective;
            obj.VatgroupId = VatgroupId;
            obj.VatgroupDescription = VatgroupDescription;
            obj.SalesTaxMethodId = SalesTaxMethodId;
            obj.SalesTaxMethodDesc = SalesTaxMethodDesc;
            obj.UserDefinedDate01 = UserDefinedDate01;
            obj.LanguageId = LanguageId;
            obj.AmountDiscountBasis = AmountDiscountBasis;
            obj.AmountDiscountBasisOc = AmountDiscountBasisOc;
            obj.PercentDiscount = PercentDiscount;
            obj.AmountDiscount = AmountDiscount;
            obj.AmountDiscountOc = AmountDiscountOc;
            obj.PrcConfigurationId = PrcConfigurationId;
            obj.PrcConfigurationDescription = PrcConfigurationDescription;
            obj.BasLanguageFk = BasLanguageFk;
            obj.DateAwardDeadline = DateAwardDeadline;
            obj.DateQuoteDeadline = DateQuoteDeadline;
            obj.TimeQuoteDeadline = TimeQuoteDeadline;
            obj.ContactId = ContactId;
            obj.ContactFirstName = ContactFirstName;
            obj.ContactFamilyName = ContactFamilyName;
            obj.BasLanguageDesc = BasLanguageDesc;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(QtnHeaderApiEntity clonedEntity);

    }


}
