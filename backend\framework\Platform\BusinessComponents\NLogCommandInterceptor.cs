using System;
using Microsoft.AspNetCore.Http;
using NLog;
using RIB.Visual.Platform.Core;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.Entity.Infrastructure.Interception;
using System.Linq;

namespace RIB.Visual.Platform.BusinessComponents
{
	/// <summary>
	/// NLogCommandInterceptor interceptor is used to log all dbContext related db command into nlog.
	/// This enable us to log all dbContext sql commands into a single target like a database table
	/// since rei@2.2.22
	/// </summary>
	public class NLogCommandInterceptor : IDbCommandInterceptor
	{
		private static readonly Logger Logger = LogManager.GetLogger("nlog-dbContext");
		private static IBusinessEnvironment _businessEnvironment;
		private static IHttpContextAccessor _httpContextAccessor;
		private static bool _interceptorActive;

		/// <summary>
		///
		/// </summary>
		/// <param name="httpContextAccessor"></param>
		/// <param name="businessEnvironment"></param>
		/// <param name="interceptorActive"></param>
		public NLogCommandInterceptor(IHttpContextAccessor httpContextAccessor, IBusinessEnvironment businessEnvironment, bool interceptorActive)
		{
			_businessEnvironment = businessEnvironment;
			_httpContextAccessor = httpContextAccessor;
			_interceptorActive = interceptorActive;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="command"></param>
		/// <param name="interceptionContext"></param>
		public void NonQueryExecuting(DbCommand command, DbCommandInterceptionContext<int> interceptionContext)
		{
			LogMessage(command);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="command"></param>
		/// <param name="interceptionContext"></param>
		public void NonQueryExecuted(DbCommand command, DbCommandInterceptionContext<int> interceptionContext)
		{
			LogIfError(command, interceptionContext);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="command"></param>
		/// <param name="interceptionContext"></param>
		public void ReaderExecuting(DbCommand command, DbCommandInterceptionContext<DbDataReader> interceptionContext)
		{
			LogMessage(command);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="command"></param>
		/// <param name="interceptionContext"></param>
		public void ReaderExecuted(DbCommand command, DbCommandInterceptionContext<DbDataReader> interceptionContext)
		{
			LogIfError(command, interceptionContext);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="command"></param>
		/// <param name="interceptionContext"></param>
		public void ScalarExecuting(DbCommand command, DbCommandInterceptionContext<object> interceptionContext)
		{
			LogMessage(command);
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="command"></param>
		/// <param name="interceptionContext"></param>
		public void ScalarExecuted(DbCommand command, DbCommandInterceptionContext<object> interceptionContext)
		{
			LogIfError(command, interceptionContext);
		}

		// all logmessage which will be suppressed
		static readonly string[] _startsWithList = {
			"-- Completed",
			"-- Executing",
			"Closed connection",
			"Opened connection",
			"Committed transaction",
			"Started transaction" };

		private static bool FilterOutLogMessages(string msg)
		{
			if (string.IsNullOrWhiteSpace(msg)) { return false; }
			var matchFound = _startsWithList.FirstOrDefault(msg.StartsWith);
			return (matchFound != null);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="msg"></param>
		public static void LogMessage(string msg)
		{
			if (!_interceptorActive || FilterOutLogMessages(msg)) { return;}

			// do analyze only if logging is active....
			var logEvent = new LogEventInfo(LogLevel.Info, Logger.Name, msg);
			if (Logger.IsTraceEnabled) { logEvent.Level = LogLevel.Trace; }
			GetContextInfo(logEvent, withStack: Logger.IsTraceEnabled);
			Logger.Log(logEvent); //Logger.Trace(CommandWithParameter(command, true), interceptionContext);
		}

		private void LogMessage(DbCommand command)
		{
			var logEvent = new LogEventInfo(LogLevel.Info, Logger.Name, "");
			if (Logger.IsTraceEnabled) { logEvent.Level = LogLevel.Trace; }
			GetContextInfo(logEvent, withStack: Logger.IsTraceEnabled);
			logEvent.Message = CommandWithParameter(command, logEvent);
			Logger.Log(logEvent); //Logger.Trace(CommandWithParameter(command, true), interceptionContext);
		}

		private static void GetContextInfo(LogEventInfo logEventInfo, bool withStack = false)
		{
			if (_businessEnvironment != null)
			{
				// ReSharper disable once StringLiteralTypo
				logEventInfo.Properties["serverbaseuri"] = _businessEnvironment.ServerBaseUri;
			}
			if (withStack)
			{
				var stackTrace = System.Environment.StackTrace;
				logEventInfo.Properties["stacktrace"] = stackTrace;
			}

			if (!GetContextInfoFromHttpContext()) 
			{ 
				GetContextInfoFromEnvironment();
			}

			return;

			#region Local Functions

			bool GetContextInfoFromHttpContext()
			{
				try
				{
					if (_httpContextAccessor is not { HttpContext: { Request: { } request } httpContext }) { return false; }

					// if the timing is "right" then on evaluating the httpContext in the if-statement above, the httpContext is still available
					// but in the next line the httpContext is already disposed --> Exception
					// this can happen when the interceptor is called after the request has been finished already but e.g. a workflow-thread is still ongoing
					httpContext.Items.TryGetValue("Platform:WebApi:MessageUuid", out var messageUuId);
					logEventInfo.Properties["messageuuid"] = messageUuId;
					var requestInfo = $"{request.Method.ToLower()} {request.Scheme}://{request.Host}{request.PathBase}{request.Path}{request.QueryString}";
					logEventInfo.Properties["request"] = requestInfo;
					return true;
				}
				catch (Exception)
				{
					return false;
				}
			}

			void GetContextInfoFromEnvironment()
			{
				var env = AppServer.Runtime.BusinessApplication.BusinessEnvironment;
				var contextInfo = env is { CurrentContext: { } cCtx } ? $"ctx:{cCtx.UserId}={cCtx.UserName}" : $"noctx:";
				logEventInfo.Properties["context"] = contextInfo;
			}
			#endregion

		}
		private string CommandWithParameter(DbCommand cmd, LogEventInfo logEventInfo)
		{
			return cmd.Parameters.Count > 0
				? $"{cmd.CommandText}{GetParameterNameValues(" >Params: ", cmd.Parameters)}"
				: $"{cmd.CommandText}";
		}

		private static string GetParameterNameValues(string info, DbParameterCollection collection)
		{
			if (collection == null || collection.Count == 0)
			{
				return "";
			}
			var res = new List<string>();
			foreach (DbParameter item in collection)
			{
				res.Add($"{item.ParameterName}={item.Value}");
			}
			return info + string.Join(",", res);
		}

		private void LogIfError<TResult>(DbCommand command, DbCommandInterceptionContext<TResult> interceptionContext)
		{
			if (interceptionContext.Exception != null)
			{
				Logger.Error("Command {0} failed with exception {1}",
					command.CommandText, interceptionContext.Exception);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="info"></param>
		public void LogInfo(string info)
		{
			Logger.Info(info);
		}
	}
}