import { Injectable } from '@angular/core';
import { createLookup, FieldType, UiCommonLookupTypeLegacyDataService } from '@libs/ui/common';
import { IOrdHeaderEntity } from '@libs/sales/interfaces';
import { BasicsSharedContractStatusLookupService } from '@libs/basics/shared';

@Injectable({
	providedIn: 'root',
})
export class salesContractContractLookupService<TEntity extends object> extends UiCommonLookupTypeLegacyDataService<IOrdHeaderEntity, TEntity> {
	public constructor() {
		super('SalesContractV2', {
			uuid: '5f8065306a4e4e968e58d99109324e80',
			idProperty: 'Id',
			valueMember: 'Id',
			displayMember: 'Code',
			gridConfig: {
				columns: [
					{
						id: 'code',
						model: 'Code',
						type: FieldType.Code,
						label: {
							text: 'Code',
							key: 'cloud.common.entityCode',
						},
						visible: true,
						sortable: false,
					},
					{
						id: 'Description',
						model: 'DescriptionInfo',
						type: FieldType.Translation,
						label: {
							text: 'Description',
							key: 'cloud.common.entityDescription',
						},
						visible: true,
						sortable: false,
					},
					{
						id: 'ordStatusFk',
						model: 'OrdStatusFk',
						type: FieldType.Lookup,
						lookupOptions: createLookup({
							dataServiceToken: BasicsSharedContractStatusLookupService,
							displayMember: 'Description',
							valueMember: 'Id',
						}),
						label: { key: 'cloud.common.entityState', text: 'Status' },
						sortable: true,
						visible: true,
						readonly: true,
					},
				],
			},
			dialogOptions: {
                headerText: {text:'Assign Sales Contract', key: 'sales.common.dialogTitleAssignContract'}
            },
			showDialog:true,

		});
	}

	public override isItemLive(item: IOrdHeaderEntity): boolean {
		return true;
	}
}