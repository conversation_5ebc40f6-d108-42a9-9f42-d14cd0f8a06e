/*
 * Copyright(c) RIB Software GmbH
 */

import { IEntityRuntimeDataRegistry, IValidationFunctions, ValidationInfo, ValidationResult } from '@libs/platform/data-access';
import { ProcurementBaseValidationService } from '@libs/procurement/shared';
import { IConCrewEntity } from '../model/entities/con-crew-entity.interface';
import { ProcurementContractCrewDataService } from './procurement-contract-crew-data.service';
import { inject, Injectable } from '@angular/core';
import { IDescriptionInfo, PlatformTranslateService } from '@libs/platform/common';

/**
 * Procurement contract crew validation service
 */
@Injectable({
	providedIn: 'root',
})
export class ProcurementContractCrewValidationService extends ProcurementBaseValidationService<IConCrewEntity> {
	private readonly translateService = inject(PlatformTranslateService);

	/**
	 *
	 * @param dataService
	 */
	protected constructor(protected dataService: ProcurementContractCrewDataService) {
		super();
	}

	protected generateValidationFunctions(): IValidationFunctions<IConCrewEntity> {
		return {
			DescriptionInfo: this.validateDescriptionInfo,
			Sorting: this.validateSorting,
			IsDefault: this.validateIsDefault,
		};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<IConCrewEntity> {
		return this.dataService;
	}

	protected validateDescriptionInfo(info: ValidationInfo<IConCrewEntity>) {
		return this.validateIsRequired({
			entity: info.entity,
			value: info.value ? (info.value as IDescriptionInfo).Translated : '',
			field: this.translateService.instant('procurement.contract.updateFrameworkMaterialCatalog.entityDescription').text,
		});
	}

	protected validateSorting(info: ValidationInfo<IConCrewEntity>) {
		return this.validateIsMandatory(info);
	}

	protected validateIsDefault(info: ValidationInfo<IConCrewEntity>) {
		const itemList = this.dataService.getList();
		if (info.value) {
			const updatedList = itemList.map(e => ({
				...e,
				IsDefault: e.Id === info.entity.Id
			}));
			this.dataService.setList(updatedList);
		}
		return new ValidationResult();
	}
}
