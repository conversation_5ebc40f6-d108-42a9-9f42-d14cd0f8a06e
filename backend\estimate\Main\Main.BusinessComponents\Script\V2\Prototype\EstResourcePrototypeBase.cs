using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Microsoft.ClearScript;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	/// <typeparam name="TEntity"></typeparam>
	public class EstResourcePrototypeBase<TEntity> : ScriptEntityPrototype<TEntity> where TEntity : IIdentifyable
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="engine"></param>
		/// <param name="request"></param>
		/// <param name="response"></param>
		public EstResourcePrototypeBase(ScriptEngine engine, IScriptRequest request, IScriptResponse response) : base(engine, request, response)
		{

		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="key"></param>
		/// <returns></returns>
		[ScriptProperty("getSingleSubItem", JsObjectInstanceConstant.EstResource, "key", MinArgsLength = 0)]
		public IEstResourceScriptObject GetSingleSubItem(EstResourceHandlerV2<TEntity> self, string key)
		{
			return self.GetSingleSubItem(key);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		/// <exception cref="Exception"></exception>
		[ScriptProperty("createCostCode", JsObjectInstanceConstant.EstResource, "code", MinArgsLength = 0)]
		public IEstResourceScriptObject CreateCostCode(EstResourceHandlerV2<TEntity> self, string code)
		{
			try
			{
				return self.CreateCostCode(code);
			}
			catch (Exception e)
			{
				this.WriteError(new BusinessLayerException(string.Format("Crete costcode {0} failed due to inner exception!", code), e).ToString());
				return null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <param name="materialCatalogCode"></param>
		/// <returns></returns>
		[ScriptProperty("createMaterial", JsObjectInstanceConstant.EstResource, "code", "materialCatalogCode", MinArgsLength = 0)]
		public IEstResourceScriptObject CreateMaterial(EstResourceHandlerV2<TEntity> self, string code, string materialCatalogCode = "")
		{
			try
			{
				return self.CreateMaterial(code, materialCatalogCode);//materialGroupCode, 
			}
			catch (Exception e)
			{
				this.WriteError(new BusinessLayerException(string.Format("Create material {0} in catalog {1} failed due to inner exception!", code, materialCatalogCode), e).ToString());
				return null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="materialCodes"></param>
		/// <param name="catalogCodes"></param>
		/// <returns></returns>
		[ScriptProperty("createMaterials")]
		public EstResourceObject[] CreateMaterials(EstResourceHandlerV2<TEntity> self, object[] materialCodes, object[] catalogCodes = null)
		{
			return self.CreateMaterials(materialCodes, catalogCodes);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <param name="plantMasterCode"></param>
		/// <returns></returns>
		[ScriptProperty("createPlant", JsObjectInstanceConstant.EstResource, "code", MinArgsLength = 1)]
		public IEstResourceScriptObject CreatePlant(EstResourceHandlerV2<TEntity> self, string code, string plantMasterCode = "")
		{
			try
			{
				return self.CreatePlant(code, plantMasterCode);
			}
			catch (Exception e)
			{
				this.WriteError(new BusinessLayerException(string.Format("Create plant {0} failed due to inner exception!", code), e).ToString());
				return null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="quantitydetail"></param>
		/// <returns></returns>
		[ScriptProperty("createComputationalLine", JsObjectInstanceConstant.EstResource, "quantitydetail", MinArgsLength = 0)]
		public IEstResourceScriptObject CreateComputationalLine(EstResourceHandlerV2<TEntity> self, string quantitydetail)
		{
			try
			{
				return self.CreateComputationalLine(quantitydetail);
			}
			catch (Exception e)
			{
				this.WriteError(new BusinessLayerException(string.Format("Create Computational Line failed due to inner exception!"), e).ToString());
				return null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <param name="assemblyCatCode"></param>
		/// <param name="rootAssemblyCatCode"></param>
		/// <returns></returns>
		[ScriptProperty("createAssembly", JsObjectInstanceConstant.EstResource, "code", "AssemblyCatCode", "RootAssemblyCatCode", MinArgsLength = 0)]
		public IEstResourceScriptObject CreateAssembly(EstResourceHandlerV2<TEntity> self, string code, string assemblyCatCode = "", string rootAssemblyCatCode = "")
		{
			try
			{
				return self.CreateAssembly(code, assemblyCatCode, rootAssemblyCatCode, (self.Request as ScriptRequestBase).ProjectFk);
			}
			catch (Exception e)
			{
				this.WriteError(new BusinessLayerException(string.Format("Create assembly {0} failed due to inner exception!", code), e).ToString());
				return null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		[ScriptProperty("createSubItem", JsObjectInstanceConstant.EstResource, "code", MinArgsLength = 0)]
		public IEstResourceScriptObject CreateSubItem(EstResourceHandlerV2<TEntity> self, string code = "")
		{
			if (!String.IsNullOrEmpty(code))
			{
				return self.CreateSubItem(code);
			}
			else
			{
				return self.CreateResource(EstResourceType.SubItem);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="desc"></param>
		/// <returns></returns>
		[ScriptProperty("createText", JsObjectInstanceConstant.EstResource, "description", MinArgsLength = 0)]
		public IEstResourceScriptObject CreateText(EstResourceHandlerV2<TEntity> self, string desc)
		{
			return self.CreateResourceText(desc, EstResourceType.TextLine);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="desc"></param>
		/// <returns></returns>
		[ScriptProperty("createInternalText", JsObjectInstanceConstant.EstResource, "description", MinArgsLength = 0)]
		public IEstResourceScriptObject CreateInternalText(EstResourceHandlerV2<TEntity> self, string desc)
		{
			return self.CreateResourceText(desc, EstResourceType.InternalTextLine);
		}

		/// <summary>
		/// todo-wui: Obsolete, would delete
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		[ScriptProperty("getResource", Doc = "Obsolete, please using getResourceByCode instead")]
		public EstResourceObject GetResource(EstResourceHandlerV2<TEntity> self, string code)
		{
			return self.GetResource(code);
		}

		/// <summary>
		/// todo-wui: Obsolete, would delete
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		[ScriptProperty("removeResource", Doc = "Obsolete, please using removeResourceByCode instead")]
		public EstResourceObject RemoveResource(EstResourceHandlerV2<TEntity> self, string code)
		{
			return self.RemoveResource(code);
		}

		/// <summary>
		/// Get all resource
		/// </summary>
		/// <param name="self"></param>
		/// <returns></returns>
		[ScriptProperty("getResources", Type = "fn() -> [+EstResourceInstance]")]
		public IEstResourceScriptObject[] GetResources(EstResourceHandlerV2<TEntity> self)
		{
			return self.GetResources();
		}

		/// <summary>
		/// Get resource by code
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		[ScriptProperty("getResourceByCode", JsObjectInstanceConstant.EstResource)]
		public IEstResourceScriptObject GetResourceByCode(EstResourceHandlerV2<TEntity> self, string code)
		{
			return self.GetResourceByCode(code);
		}

		/// <summary>
		/// Remove resource by code
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		[ScriptProperty("removeResourceByCode", JsObjectInstanceConstant.EstResource)]
		public IEstResourceScriptObject RemoveResourceByCode(EstResourceHandlerV2<TEntity> self, string code)
		{
			return self.RemoveResourceByCode(code);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		[ScriptProperty("getResourcesByMajorCostCode", Type = "fn(majorCostcode: string) -> [+EstResourceInstance]")]
		public IEstResourceScriptObject[] GetResourceByMajorCostCode(EstResourceHandlerV2<TEntity> self, string code)
		{
			return self.GetResourceByMajorCostCode(code);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <returns></returns>
		[ScriptProperty("replaceCostCode", JsObjectInstanceConstant.EstResource, "oldCode", "newCode")]
		public IEstResourceScriptObject ReplaceCostCode(EstResourceHandlerV2<TEntity> self, string oldCode, string newCode)
		{
			return self.ReplaceCostCode(oldCode, newCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <param name="newCatalogCode"></param>
		/// <returns></returns>
		[ScriptProperty("replaceMaterial", JsObjectInstanceConstant.EstResource, "oldCode", "newCode", "newCatalogCode")]
		public IEstResourceScriptObject ReplaceMaterial(EstResourceHandlerV2<TEntity> self, string oldCode, string newCode, string newCatalogCode = "")
		{
			return self.ReplaceMaterial(oldCode, newCode, newCatalogCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <returns></returns>
		[ScriptProperty("replacePlant", JsObjectInstanceConstant.EstResource, "oldCode", "newCode")]
		public IEstResourceScriptObject ReplacePlant(EstResourceHandlerV2<TEntity> self, string oldCode, string newCode)
		{
			return self.ReplacePlant(oldCode, newCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <param name="newCatalogCode"></param>
		/// <param name="newRootAssemblyCatCode"></param>
		/// <returns></returns>
		[ScriptProperty("replaceAssembly", JsObjectInstanceConstant.EstResource, "oldCode", "newCode", "newCatalogCode", "newRootAssemblyCatCode")]
		public IEstResourceScriptObject ReplaceAssembly(EstResourceHandlerV2<TEntity> self, string oldCode, string newCode, string newCatalogCode = "", string newRootAssemblyCatCode = "")
		{
			return self.ReplaceAssembly(oldCode, newCode, newCatalogCode, newRootAssemblyCatCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="self"></param>
		/// <param name="oldCode"></param>
		/// <param name="newCode"></param>
		/// <returns></returns>
		[ScriptProperty("replaceSubItem", JsObjectInstanceConstant.EstResource, "oldCode", "newCode")]
		public IEstResourceScriptObject ReplaceSubItem(EstResourceHandlerV2<TEntity> self, string oldCode, string newCode)
		{
			return self.ReplaceSubItem(oldCode, newCode);
		}
	}
}
