/*
 * $Id$
 * Copyright (c) RIB Software GmbH
 */

using System;
using System.ComponentModel.Composition;
using System.Transactions;
using RIB.Visual.Platform.Core;

namespace RIB.Visual.Platform.BusinessComponents
{
	/// <summary>
	/// Removes all entries of BAS_DDTEMPIDS having given request uuid
	/// </summary>
	/// <param name="uuid"></param>
	[Export(typeof(IDdTempIdsRequestUuidCleanup))]
	public class DdTempIdsRequestUuidCleanup : IDdTempIdsRequestUuidCleanup
	{
		void IDdTempIdsRequestUuidCleanup.Cleanup(String uuid)
		{
			using var context = new DbContext();
			using var ta = TransactionScopeFactory.CreateSuppress();

			_ = context.ExecuteStoredProcedure("BAS_DDTEMPIDS_CLEANBYUUID_SP", uuid);
			ta.Complete();
		}
	}
}
