using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Core.Core;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public interface IEstResourceModificationTracker
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="modificationInfo"></param>
		void Add(EstResourceModificationInfo modificationInfo);

		/// <summary>
		/// 
		/// </summary>
		void HandleModification();

		/// <summary>
		/// 
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		IScriptEstResource GetResourceFromModification(int id);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="lineItemId"></param>
		/// <returns></returns>
		IEnumerable<IScriptEstResource> GetResourcesByLineItemId(int lineItemId);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="lineItem"></param>
		/// <param name="parent"></param>
		/// <param name="createResourceFunc"></param>
		/// <returns></returns>
		IScriptEstResource GetSingleLineItemByCode(string code, IScriptEstLineItem lineItem, IScriptEstResource parent, Func<IScriptEstResource> createResourceFunc);
	}

	/// <summary>
	/// 
	/// </summary>
	public class EstResourceModificationTracker : IEstResourceModificationTracker
	{
		private readonly object _Lock = new object();

		private IList<EstResourceModificationInfo> _ResourceModificationInfoAll = new List<EstResourceModificationInfo>();

		private Dictionary<int, EstResourceModificationInfo> _ResourceModificationToHandle = new Dictionary<int, EstResourceModificationInfo>();

		private IDictionary<string, IList<EstResourceModificationInfo>> _SingleResourceCache = new Dictionary<string, IList<EstResourceModificationInfo>>();

		/// <summary>
		/// 
		/// </summary>
		/// <param name="modificationInfo"></param>
		public void Add(EstResourceModificationInfo modificationInfo)
		{
			lock (_Lock)
			{
				if (!this._ResourceModificationToHandle.ContainsKey(modificationInfo.Entity.Id))
				{
					if (modificationInfo.Parent != null && modificationInfo.Entity.EstRuleSourceFk == modificationInfo.Parent.EstRuleSourceFk)
					{
						if (modificationInfo.Parent.Resources == null)
						{
							modificationInfo.Parent.Resources = new List<IScriptEstResource>();
						}

						modificationInfo.Parent.Resources.Add(modificationInfo.Entity);
                    }
					else if (modificationInfo.Entity.EstResourceFk.HasValue && this._ResourceModificationToHandle.TryGetValue(modificationInfo.Entity.EstResourceFk.Value, out EstResourceModificationInfo parentModificationInfo))
					{
						if (parentModificationInfo.Entity.Resources == null)
						{
                            parentModificationInfo.Entity.Resources = new List<IScriptEstResource>();
						}
						if (!parentModificationInfo.Entity.Resources.Any(e => e.Id == modificationInfo.Entity.Id))
						{
                            modificationInfo.Parent = parentModificationInfo.Entity;
                            modificationInfo.Parent.Resources.Add(modificationInfo.Entity);
							
                        }
					}
					else
					{
						this._ResourceModificationToHandle.Add(modificationInfo.Entity.Id, modificationInfo);
					}

					this._ResourceModificationInfoAll.Add(modificationInfo);
				}
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="lineItem"></param>
		/// <param name="parent"></param>
		/// <param name="createResourceFunc"></param>
		/// <returns></returns>
		public IScriptEstResource GetSingleLineItemByCode(string code, IScriptEstLineItem lineItem, IScriptEstResource parent, Func<IScriptEstResource> createResourceFunc)
		{
			if (lineItem == null)
			{
				return null;
			}

			lock (_Lock)
			{
				if (this._SingleResourceCache.ContainsKey(code))
				{
					var modificationList = this._SingleResourceCache[code];

					var singleResources = modificationList.Where(e => e.LineItem != null && e.LineItem.Id == lineItem.Id).ToList();

					if (singleResources != null && singleResources.Any())
					{
						EstResourceModificationInfo resourceExisting = null;

						if (parent != null)
						{
							resourceExisting = singleResources.FirstOrDefault(e => e.Parent != null && e.Parent.Id == parent.Id);
						}
						else
						{
							resourceExisting = singleResources.FirstOrDefault(e => e.Parent == null);
						}

						if (resourceExisting != null)
						{
							return resourceExisting.Entity;
						}
					}

					var newResource = createResourceFunc != null ? createResourceFunc() : default(IScriptEstResource);

					if (newResource != null)
					{
						var modificationInfo = new EstResourceModificationInfo()
						{
							Entity = newResource,
							Parent = parent,
							LineItem = lineItem,
							ModificationType = ModificationType.Add
						};

						modificationList.Add(modificationInfo);

						if (!this._ResourceModificationToHandle.ContainsKey(newResource.Id))
						{
							this._ResourceModificationInfoAll.Add(modificationInfo);

							this._ResourceModificationToHandle.Add(newResource.Id, modificationInfo);
						}
					}

					return newResource;
				}
				else
				{
					var newResource = createResourceFunc != null ? createResourceFunc() : default(IScriptEstResource);

					var modificationInfo = new EstResourceModificationInfo()
					{
						Entity = newResource,
						Parent = parent,
						LineItem = lineItem,
						ModificationType = ModificationType.Add
					};

					if (newResource != null)
					{
						this._SingleResourceCache.Add(code, new List<EstResourceModificationInfo>() { modificationInfo });

						if (!this._ResourceModificationToHandle.ContainsKey(newResource.Id))
						{
							this._ResourceModificationInfoAll.Add(modificationInfo);

							this._ResourceModificationToHandle.Add(newResource.Id, modificationInfo);
						}
					}

					return newResource;
				}


			}
		}

		/// <summary>
		/// 
		/// </summary>
		public void HandleModification()
		{
			lock (this._Lock)
			{
				if (this._ResourceModificationToHandle != null && this._ResourceModificationToHandle.Any())
				{
					foreach (var item in this._ResourceModificationToHandle)
					{
						if (item.Value == null && item.Value.Entity == null)
						{
							continue;
						}

						if (item.Value.Parent != null)
						{
							if (item.Value.Parent.Resources == null)
							{
								item.Value.Parent.Resources = new List<IScriptEstResource>();
							}
							else
							{
								if (item.Value.Parent.Resources.Any(e => e.Id == item.Key))
								{
									continue;
								}
							}

							item.Value.Parent.Resources.Add(item.Value.Entity);
						}
						else
						{
							if (item.Value.LineItem == null)
							{
								continue;
							}

							if (item.Value.LineItem.Resources == null)
							{
								item.Value.LineItem.Resources = new List<IScriptEstResource>();
							}
							else
							{
								if (item.Value.LineItem.Resources.Any(e => e.Id == item.Key))
								{
									continue;
								}
							}

							item.Value.LineItem.Resources.Add(item.Value.Entity);
						}
					}
				}

				this._ResourceModificationToHandle = new Dictionary<int, EstResourceModificationInfo>();
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public IScriptEstResource GetResourceFromModification(int id)
		{
			lock (this._Lock)
			{
				var modificationInfo = this._ResourceModificationInfoAll.FirstOrDefault(e => e.Entity.Id == id);

				return modificationInfo != null ? modificationInfo.Entity : null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="lineItemId"></param>
		/// <returns></returns>
		public IEnumerable<IScriptEstResource> GetResourcesByLineItemId(int lineItemId)
		{
			lock (this._Lock)
			{
				if (this._ResourceModificationToHandle == null || !this._ResourceModificationToHandle.Any())
				{
					return new List<IScriptEstResource>();
				}

				return this._ResourceModificationToHandle.Select(e => e.Value).Where(e => e.LineItem != null && e.LineItem.Id == lineItemId).Select(e => e.Entity).ToList();
			}
		}
	}

	/// <summary>
	/// 
	/// </summary>
	public class EstResourceModificationInfo
	{
		/// <summary>
		/// 
		/// </summary>
		public ModificationType ModificationType { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IScriptEstResource Entity { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IScriptEstResource Parent { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IScriptEstLineItem LineItem { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public enum ModificationType
	{
		/// <summary>
		/// 
		/// </summary>
		Add = 1,

		/// <summary>
		/// 
		/// </summary>
		Update = 2,

		/// <summary>
		/// 
		/// </summary>
		Delete = 3
	}
}
