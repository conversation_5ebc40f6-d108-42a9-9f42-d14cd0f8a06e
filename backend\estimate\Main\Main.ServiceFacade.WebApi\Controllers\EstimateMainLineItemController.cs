/*
 * $Id$
 * Copyright (c) RIB Software AG
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using Newtonsoft.Json;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostCodes.ServiceFacade.WebApi;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Basics.CostGroups.ServiceFacade.WebApi;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.LookupData.ServiceFacade.WebApi;
using RIB.Visual.Basics.Material.ServiceFacade.WebApi;
using RIB.Visual.Estimate.Assemblies.BusinessComponents;
using RIB.Visual.Estimate.Common.BusinessComponents;
using RIB.Visual.Estimate.Main.BusinessComponents;
using RIB.Visual.Estimate.Main.BusinessComponents.Entities;
using RIB.Visual.Estimate.Parameter.BusinessComponents;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using RIB.Visual.Estimate.Rule.ServiceFacade.WebApi;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using EstAssembly = RIB.Visual.Estimate.Assemblies.ServiceFacade.WebApi;
using EstLineItemEntity = RIB.Visual.Estimate.Main.BusinessComponents.EstLineItemEntity;
using Permission = RIB.Visual.Platform.BusinessComponents.Permission;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using RIB.Visual.Estimate.Main.Localization.Properties;
using System.Collections.Concurrent;
using Microsoft.AspNetCore.Mvc.WebApiCompatShim;
using static System.Net.Mime.MediaTypeNames;
using System.Collections.Specialized;
using PlatformServerCommon = RIB.Visual.Platform.Server.Common;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Estimate.Main.BusinessComponents.import;
using RIB.Visual.Basics.Core.Common.Enum;
using RIB.Visual.Boq.Main.BusinessComponents;
using static RIB.Visual.Estimate.Rule.ServiceFacade.WebApi.EstimateRulePrjEstRuleParamController;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Controllers.Dependent;

namespace RIB.Visual.Estimate.Main.ServiceFacade.WebApi
{
	/// <summary>
	/// estimate main module controller
	/// </summary>
	[RoutePrefix("estimate/main/lineitem")]
	[Export("estimate.main.estlineitems", typeof(IBulkChangeSupport))]
	public class EstimateMainLineItemController : ApiControllerBase<EstimateMainLineItemLogic>, IBulkChangeSupport
	{
		/// <summary>
		/// </summary>
		private const string AIBoqMappingPermissionDescriptor = "af7aee3e27bd4242ad7d2f03ae9b1921";
		/// <summary>
		/// </summary>
		private const string AIActivityMappingPermissionDescriptor = "17fcdd3770ab4ab594e05be50ab5db22";
		/// <summary>
		/// </summary>
		private const string AICostGroupMappingPermissionDescriptor = "f7faf05bcfae44499118cea5aa112c9e";

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		[Route("getinfo")]
		public string GetInfo()
		{
			return GetDefaultInfo();
		}
		/// <summary>
		/// Gets the estimate line item dto scheme
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("scheme")]
		public HttpResponseMessage GetScheme()
		{
			return GetJsonSchema(typeof(EstLineItemDto));
		}

		/// <summary>
		///
		/// </summary>
		public class ChangeStatusFilterInfo
		{
			/// <summary>
			///
			/// </summary>
			public int ProjectFk { get; set; }

			/// <summary>
			///
			/// </summary>
			public int EstHeaderFk { get; set; }

			/// <summary>
			/// whether to filter the lineitem by changeStatusIds
			/// </summary>
			public bool FilterByChangeStatus { get; set; }

			/// <summary>
			/// changeStatus Ids which need to filter the lineItems
			/// </summary>
			public IEnumerable<int> ChangeStatusIds { get; set; }
		}

		/// <summary>
		/// Gets the list of line items
		/// </summary>
		/// <returns></returns>
		/// TODO: merge with listfiltered
		[HttpPost]
		[Route("list")]
		public IEnumerable<EstLineItemDto> GetList(EstLineItemReadData data)
		{
			return Logic.GetList(data.estHeaderFk).Select(e => new EstLineItemDto(e)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("projectcontrolslineitemlist")]
		public Dictionary<string, object> GetList(ProjectControlsLineItemParam param)
		{
			var jsData = new Dictionary<string, object>();

			if (param != null && param.prjProjectFk != null)
			{
				var estCompositeLogic = Injector.Get<IEstimateCompositeLogic>();
				var estHeaders = estCompositeLogic.GetControllingEstHeaders(param.prjProjectFk.Value);
				var estHeaderIs = estHeaders.Select(e => e.EstHeaderFk).ToList();
				var entities = Logic.GetPrjControlsLineitemList(param, estHeaderIs);

				var dtos = entities.Select(e => new EstLineItemDto(e)).ToList();
				jsData["Main"] = dtos;

				var estAssemblyIds = dtos.CollectIds(e => e.EstAssemblyFk);
				if (estAssemblyIds.Any())
				{
					var estassemblies = new EstimateAssembliesLogic().GetListByIds(estAssemblyIds.ToArray());
					if (estassemblies.Any())
					{
						jsData["estassemblies"] = estassemblies.Select(e => new KeyValuePair<int, string>(e.Id, e.Code)).ToList();
					}
				}
			}
			return jsData;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("controllingunitlineitemlist")]
		public Dictionary<string, object> GetList(ControllinUnitLineItemParam param)
		{
			var jsData = new Dictionary<string, object>();

			var main = Logic.GetList(param).Select(e => new EstLineItemDto(e)).ToList();

			jsData["Main"] = main;

			var estLineItemIds = main.CollectIds(e => e.EstLineItemFk);
			if (estLineItemIds != null && estLineItemIds.Any())
			{
				var lineItems = new EstimateMainLineItemLogic().GetSearchList(e => estLineItemIds.Contains(e.Id));
				if (lineItems != null && lineItems.Any())
				{
					jsData["estlineitems"] = lineItems.Select(e => new EstLineItemDto(e)).ToList();
				}
			}

			return jsData;
		}

		/// <summary>
		/// Gets the list of line items
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("getList")]
		public IEnumerable<EstLineItemDto> GetList(int mainItemId)
		{
			return Logic.Get(rel => rel.EstLineItemFk == mainItemId && rel.IsTemp == false).Select(i => new EstLineItemDto(i)).ToList();
		}

		/// <summary>
		/// Get line item by Id
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("getitembyid")]
		public EstLineItemDto GetItemById(int id)
		{
			return Logic.Get(rel => rel.Id == id).Select(i => new EstLineItemDto(i)).FirstOrDefault();
		}

		/// <summary>
		/// Get line item list by Ids
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("getlineitemlistbyids")]
		public IEnumerable<EstLineItemDto> GetLineItemListbyids(IEnumerable<EstLineItemData> readData)
		{
			List<EstLineItemDto> result = new List<EstLineItemDto>();

			if (readData != null && readData.Any())
			{
				var header2Ids = readData.GroupBy(e => e.EstHeaderFk).ToDictionary(i => i.Key, i => i.Select(e => e.Id).ToList());

				foreach (var header2Id in header2Ids)
				{
					var estHeaderId = header2Id.Key;
					var estLineItemIds = header2Id.Value.Distinct();

					result.AddRange(Logic.GetLineItemByIds(estLineItemIds, estHeaderId).Select(x => x as BusinessComponents.EstLineItemEntity).Select(i => new EstLineItemDto(i)));
				}
			}

			return result;
		}

		/// <summary>
		/// Get assembly item by Id
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("getassemblybyid")]
		public EstLineItemDto GetAssemblyById(int id)
		{
			return Logic.Get(rel => rel.Id == id, true).Select(i => new EstLineItemDto(i)).FirstOrDefault();
		}

		/// <summary>
		/// Get assemblies item by Id
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("getassembliesbyid")]
		public IEnumerable<EstLineItemDto> GetAssembliesById(IEnumerable<int> ids)
		{
			return Logic.Get(rel => ids.Contains(rel.Id), true).Select(i => new EstLineItemDto(i)).ToList();
		}

		/// <summary>
		/// Returns list of line items.
		/// </summary>
		/// <param name="filterRequest">filter to specify line items</param>
		/// <returns>list of line items</returns>
		// TODO: merge with GetFilteredList
		[Route("listfiltered_new")]
		[HttpPost]
		public IDictionary<string, object> GetFilteredListNew([FromBody] FilterRequest<Int32> filterRequest)
		{
			if (filterRequest == null)
			{
				return new Dictionary<string, object>();
			}
			Stopwatch swAll = new Stopwatch();
			Stopwatch w1 = new Stopwatch();
			StringBuilder timeStr = new StringBuilder();
			var filterInfo = new FilterResponse<int>();

			swAll.Start();
			var execInfo = new FilterExecutionInfo<int>(filterRequest, filterInfo);
			execInfo.CreateHint("Start fetching of data by '" + filterRequest.Pattern + "'");
			Logic.EvaluateEnhancedFilter(filterRequest);

			// returns all entities and converts it to a dto list
			int projectId = -1, estHeaderId = -1;

			w1.Start();
			if (filterRequest.GroupingFilter != null)
			{
				filterRequest.GroupingFilter.Module = "estimate.main";
			}
			var entities = Logic.GetList(filterRequest, ref filterInfo, ref projectId, ref estHeaderId);

			//Verify that the project belongs to the current company
			var prjLogic = RVPARB.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
			var prjEntity = projectId != -1 ? prjLogic.GetProjectById(projectId) : null;
			if (prjEntity != null && prjEntity.CompanyFk != BusinessApplication.BusinessEnvironment.CurrentContext.ClientId && !prjEntity.IsInterCompany)
			{
				return new Dictionary<string, object>();
			}

			w1.Stop();
			timeStr.AppendLine("function to get the LineItemEntities:--------->" + w1.ElapsedMilliseconds + " ms");


			w1.Restart();

			Logic.GetPrcItemAssignmentsByEstimateInfo(estHeaderId, entities);

			w1.Stop();
			timeStr.AppendLine("function GetPrcItemAssignmentsByEstimateInfo:--------->" + w1.ElapsedMilliseconds + " ms");

			var dtos = entities != null ? entities.Select(e => new EstLineItemDto(e)).ToList() : null;
			execInfo.CreateHint("completed data fetching ...");

			var advancedAllowanceCc = EstimateAdvancedAllowanceHelper.GetAllowanceCostCodeId();

			var systemoptions = new BasicsCustomizeSystemOptionLogic().GetListByFilter(e => e.Id == SystemOption.EnableInputLineitemTotalQuantity || e.Id == SystemOption.ActivateEstimateComparisonIndicator).ToList();
			foreach (var optionEntity in systemoptions)
			{
				optionEntity.ParameterValue = ConverterHelper.GetBooleanFromString(optionEntity.ParameterValue).ToString();
			}

			var doConsiderDisabledDirect = new SystemOptionLogic().GetValueAsBool(SystemOption.ConsiderDisabledDirect);

			var lazyLoadCostCode = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ActivateLazyLoadInCostCodeLookup);

			var lineItemStatusList = Logic.GetEstLineItemStatus();

			/* use for standard allowance calculation in client side */
			var estimateScopeObject = new EstimateScopeObject(estHeaderId, projectId, SearchCacheType.GlobalCache);

			var advancedAll = estimateScopeObject.GetAdvancedAll();

			var lineItemsWithAdvancedAll = estimateScopeObject.GetLineItemsWithAdvancedAll();

			var fixedPriceLineItems = new EstimateMainLineItemLogic().GetListByFilter(e => e.EstHeaderFk == estHeaderId && !e.IsOptional && !e.IsDaywork && e.IsFixedPrice && !e.IsTemp && !e.IsDisabled);

			var mdcCostCodes = estimateScopeObject.GetCostCodeSearchService().GetCostCodeStructures();

			var mdcCostCodeStructure = mdcCostCodes == null ? null : mdcCostCodes.Select(e => new { Id = e.Id, ParentFk = e.CostCodeParentFk }).ToList();

			var prjCostCodes = estimateScopeObject.GetProjectCostCodeSearchService().GetProjectCostCodes();

			var prjCostCodeStructure = prjCostCodes == null ? null : prjCostCodes.Select(e => new { Id = e.Id, ParentFk = e.CostCodeParentFk, MdcCostCodeFk = e.MdcCostCodeFk }).ToList();

			var isFixedBudgetTotal = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.FixedBudgetTotal);

			var isAllowAssemblyTemplateNavigation = new SystemOptionLogic().GetValueAsBool(SystemOption.AllowAssemblyTemplateNavigation);
			bool isShowPlantAsOneRecord = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ShowEquipmentAssembliesAsOneRecord);
			if (dtos != null)
			{
				var estProjectLogic = RVPARB.BusinessEnvironment.GetExportedValue<IEstimateCompositeLogic>();
				var estPrjFactory = RVPARB.BusinessEnvironment.GetExportedValue<IDtoEntityMapper>("Estimate.Project.EstimateCompositeDtoMapper");


				var factory = RVPARB.BusinessEnvironment.GetExportedValue<IProjectTypeFactory>();

				w1.Restart();
				// if estHeaderId is still -1, then request was not definite (-> more than one estimate headers and/or projects available)
				var prjEstComposites = (estHeaderId >= 0) ? new List<IIdentifyable> { estProjectLogic.GetCompositeByEstHeader(estHeaderId) } : estProjectLogic.GetEstPrjList(new[] { projectId });



				w1.Stop();
				timeStr.AppendLine("function GetProjectById:--------->" + w1.ElapsedMilliseconds + " ms");

				SetSplitQuantitiesValue(estHeaderId, dtos);

				//get assembly templates info
				List<int> assemblyIds = dtos.Where(e => e.EstAssemblyFk.HasValue).Select(e => e.EstAssemblyFk.Value).Distinct().ToList();
				var assemblies = Logic.GetAssembliesByIds(assemblyIds).Select(e => new EstLineItemDto(e)).ToList();
				var prjAssemblies = Logic.GetPrjAssembliesByIds(assemblyIds, projectId).Select(e => new EstLineItemDto(e)).ToList();
				assemblies.AddRange(prjAssemblies);

				w1.Restart();
				//get characteristics data
				var objectIds = dtos.Select(e => new IdentificationData() { Id = e.Id, PKey1 = e.EstHeaderFk}).Distinct().ToList();
				var characterLogic = RVPARB.BusinessEnvironment.GetExportedValue<ICharacteristicDataLogic>();
				var groupIds = RVPARB.BusinessEnvironment.GetExportedValue<ICharacteristicGroupLogic>().GetTreeBySectionAndCompany(28, RVPARB.BusinessEnvironment.CurrentContext.SignedInClientId).Flatten(e => e.CharacteristicGroups).Select(e => e.Id);
				var defaultCharacteristics = characterLogic.GetDefaultCharacterisitcs(28);
				var characteristics = characterLogic.GetListBySectionIdAndObjectIds(28, objectIds, true);

				w1.Stop();
				timeStr.AppendLine("function to get the characteristics data:--------->" + w1.ElapsedMilliseconds + " ms");

				w1.Restart();

				IEnumerable<UserDefinedcolValEntity> LineItemUDPs;
				var udpReadData = dtos.Select(e => new Tuple<int, int, int>((int)userDefinedColumnTableIds.EstimateLineItem, estHeaderId, e.Id));
				LineItemUDPs = new UserDefinedColumnValueLogic().GetListByKeys(udpReadData);

				w1.Stop();
				timeStr.AppendLine("function to get the LineItemUserDefinedColumnValue data:--------->" + w1.ElapsedMilliseconds + " ms");

				w1.Restart();
				//estimate column configuration data
				var estHeader = new EstimateMainHeaderLogic().GetItemById(estHeaderId);
				var jobFk = estHeader != null ? estHeader.LgmJobFk : 0;

				var columnConfig = estHeader != null ? new EstConfigLogic().GetById(new Platform.Core.IdentificationData() { Id = estHeader.EstConfigFk }) : null;
				var isEstDynamicColumnActive = columnConfig != null && columnConfig.IsColumnConfig;

				var statusEntity = estHeader != null ? (BasicsCustomizeEstStatusEntity)new BasicsCustomizeEstStatusLogic().GetById(estHeader.EstStatusFk) : null;
				var isHeaderStatusReadOnly = statusEntity != null ? statusEntity.IsReadOnly : false;

				var typeEntity = estHeader != null && estHeader.EstTypeFk.HasValue ? (BasicsCustomizeEstimationTypeEntity)new BasicsCustomizeEstimationTypeLogic().GetById((int)estHeader.EstTypeFk) : null;
				var isCalcTotalWithWq = typeEntity != null ? typeEntity.IsTotalWq : false;
				var isWqReadOnly = typeEntity != null ? typeEntity.IsWqReadOnly : false;
				var isTotalAqBudget = typeEntity != null ? typeEntity.IsTotalAqBudget : false;
				var isBudgetEditable = typeEntity != null ? typeEntity.IsBudgetEditable : true;
				var quantityTypes = new BasicsCustomizeQuantityTypeLogic().GetListByFilter(e => e.Code != null);

				var columnStructDetails = columnConfig != null && columnConfig.EstStructureConfigFk.HasValue ? new EstimateMainStructureDetailLogic().GetStructureDetailOrdered(columnConfig.EstStructureConfigFk.Value) : new List<EstStructureDetailEntity>();

				var estHeaderRoundingConfigDetails = columnConfig != null ? new EstimateRoundingHelper().GetRoundingConfigDetails(estHeaderId) : null;
				var estRoundingConfigDetails = new
				{
					RoundingConfigDetails = estHeaderRoundingConfigDetails,
					EstRoundingColumnIds = columnConfig != null ? new EstimateRoundingHelper().GetRoundingColumnIds() : null,
				};

				var estCopyOptions = columnConfig != null ? new EstimateCopyOptionLogic().GetCopyOptionByEstHeaderId(estHeaderId) : null;

				//Estimate column config and line item characteristic data
				//1. Est Dynamic columns only if EstConfig is activated
				var columnConfigDetails = new List<EstColumnConfigDetailEntity>();
				var extColumnConfigDetails = new Dictionary<int, Dictionary<string, ExtendColumnValue>>();

				if (isEstDynamicColumnActive)
				{
					//Get Estimate config details
					var estLineItemColumnConfigLogic = new EstLineItemColumnConfigLogic(estHeader, prjEntity);
					extColumnConfigDetails = estLineItemColumnConfigLogic.GetExtendColumns(entities);
					columnConfigDetails = estLineItemColumnConfigLogic.GetEstColumnConfigDetails().ToList();
				}

				var dynamicColumns = new
				{
					DynamicColumns = new ColumnIdLogic().getAllColumnItems(),
					ColumnConfigDetails = columnConfigDetails.Any() ? columnConfigDetails.Select(e => new EstColumnConfigDetailDto(e)).ToList() : null,
					Characteristics = characteristics,
					DefaultCharacteristics = defaultCharacteristics,
					CharacteristicsGroupIds = groupIds,
					liUDPs = LineItemUDPs
				};

				w1.Stop();
				timeStr.AppendLine("function to get the dynamicColumns:--------->" + w1.ElapsedMilliseconds + " ms");

				w1.Restart();
				/* Get the Cost Group Of LineItem */
				var lineItem2CostGroups = new List<MainItem2CostGroupDto>();
				var mainItem2CostGroupLogic = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP");
				// LineItem Chunks
				int chunkSizeLineItems = 300;
				var lineItemChunksIds = entities.Select(x => x.Id).ToSizedChunks(chunkSizeLineItems).ToList();
				object _lock = new object();
				ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };

				Parallel.ForEach(lineItemChunksIds, parallelOptions, lineItemIds =>
				{
					var lineItem2CostGroupChunkResult = mainItem2CostGroupLogic.GetByFilter(e => e.RootItemId == estHeaderId && lineItemIds.Contains(e.MainItemId.Value)).Select(e => new MainItem2CostGroupDto(e)).ToList();
					lock (_lock)
					{
						lineItem2CostGroups.AddRange(lineItem2CostGroupChunkResult);
					}
				});

				w1.Stop();
				timeStr.AppendLine("function to get the Cost Group Of LineItem:--------->" + w1.ElapsedMilliseconds + " ms");

				w1.Restart();
				var costGroupCats = new CostGroupCompleteEntityDto(new CostGroupCatLogic().GetCostGroupCatsByModuleBase(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Estimate, prjEntity));
				w1.Stop();
				timeStr.AppendLine("function to get the costGroupCats:--------->" + w1.ElapsedMilliseconds + " ms");

				var basPackageRefColumnConfig = new BasicsCustomizeStringColumnConfigLogic().GetByFilter(e => e.TableName == "PRC_PACKAGE" && e.ColumnName == "REFERENCE").FirstOrDefault();
				decimal packageReferenceLen = 252;
				if (basPackageRefColumnConfig != null)
				{
					packageReferenceLen = basPackageRefColumnConfig.ColumnSize.HasValue ? basPackageRefColumnConfig.ColumnSize.Value : packageReferenceLen;
				}

				swAll.Stop();
				timeStr.AppendLine("the total time of the function listfiltered_new  is :--------->" + swAll.ElapsedMilliseconds + " ms");
				var boqItemIds = dtos.Where(e => e.BoqItemFk.HasValue).Select(e => e.BoqItemFk.Value).Distinct().ToList();
				var boq2CalcQtySplitMap = boqItemIds.Any() ? Injector.Get<IBoqItemLogic>().GetCalculateQuantitySplittings(boqItemIds).ToList() : new List<Tuple<Int32, Int32, Boolean>>();

				var ordHeaderFks = dtos.Where(e => e.OrdHeaderFk.HasValue).Select(e => e.OrdHeaderFk.Value);
				if (ordHeaderFks != null && ordHeaderFks.Any())
				{
					var orderChangeFkDics = RVPARB.BusinessEnvironment.GetExportedValue<ISalesContractLogic>("Contract").GetContractsByIds(ordHeaderFks, true).Where(e => e.PrjChangeFk.HasValue).ToDictionary(e => e.Id, e => e.PrjChangeFk);
					foreach (var dto in dtos)
					{
						if (dto.OrdHeaderFk.HasValue && orderChangeFkDics.ContainsKey(dto.OrdHeaderFk.Value))
						{
							dto.OrderChangeFk = orderChangeFkDics[dto.OrdHeaderFk.Value];
						}
					}
				}

				return new Dictionary<string, object>()
				{
					{ "FilterResult", filterInfo },
					{ "dtos", dtos },
					{ "prjEstComposites", prjEstComposites != null ? prjEstComposites.Select(estPrjFactory.CreateFrom).ToList() : null },
					{ "selectedPrj", prjEntity != null ? factory.CreateFrom(prjEntity) : null },
					{ "isEstDynamicColumnActive", isEstDynamicColumnActive },
					{ "dynamicColumns", dynamicColumns },
					{ "EstStructureDetails", columnStructDetails},
					{ "companyMdcContextFk", EstimateContext.MasterDataContextId },
					{ "LookupAssemblies", assemblies },
					{ "IsHeaderStatusReadOnly" , isHeaderStatusReadOnly },
					{ "advancedAllowanceCc", advancedAllowanceCc },
					{ "SystemOptions", systemoptions },
					{ "jobFk", jobFk},
					{ "ExtendColumns", extColumnConfigDetails },
					{ "LineItem2CostGroups", lineItem2CostGroups },
					{ "CostGroupCats", costGroupCats },
					{ "IsWQReadOnly", isWqReadOnly},
					{ "IsTotalAQBudget", isTotalAqBudget},
					{ "DoConsiderDisabledDirect", doConsiderDisabledDirect},
					{ "LazyLoadCostCode", lazyLoadCostCode},
					{ "EstLineItemStatusList", lineItemStatusList},
					{ "packageReferenceLen",packageReferenceLen},
					{ "IsCalcTotalWithWQ",  isCalcTotalWithWq },
					{ "EstRoundingConfigDetails", estRoundingConfigDetails },
					{ "allowanceEntity", estimateScopeObject.GetActiveAllowanceEntity() },
					{ "estMarkup2CostCodes", estimateScopeObject.GetEstAllMarkup2CostCodes() },
					{ "mdcCostCodeStructure", mdcCostCodeStructure },
					{ "prjCostCodeStructure", prjCostCodeStructure},
					{ "IsBudgetEditable", isBudgetEditable},
					{ "advancedAll", advancedAll},
					{ "lineItemsWithAdvancedAll", lineItemsWithAdvancedAll },
					{ "fixedPriceLineItems",fixedPriceLineItems },
					{ "QuantityTypes",quantityTypes },
					{ "IsFixedBudgetTotal", isFixedBudgetTotal},
					{ "IsAllowAssemblyTemplateNavigation",isAllowAssemblyTemplateNavigation},
					{ "EstCopyOptions",estCopyOptions},
					{ "Boq2CalcQtySplitMap",boq2CalcQtySplitMap },
					{ "EquipmentAssemblyCostUnitAlwaysEditable",Logic.GetEquipmentAssemblyCostUnitAlwaysEditable()},
					{ "LineItemContextEstHeaderId",Logic.GetEstHeaderByCurLineItemContextId()},
					{ "isShowPlantAsOneRecord", isShowPlantAsOneRecord}
				};
			}

			// result for search by sidebar without parameter (no pattern)
			return new Dictionary<string, object>()
			{
				{ "FilterResult", filterInfo },
				{ "dtos", (IEnumerable<IEstLineItemEntity>)null },
				{ "prjEstComposites", (IEnumerable<IIdentifyable>)null },
				{ "selectedPrj", (IProjectEntity)null },
				{ "companyMdcContextFk", EstimateContext.MasterDataContextId },
				{ "IsHeaderStatusReadOnly" , false},
				{ "advancedAllowanceCc", advancedAllowanceCc },
				{ "SystemOptions", systemoptions },
				{ "DoConsiderDisabledDirect", doConsiderDisabledDirect},
				{ "EstLineItemStatusList", lineItemStatusList},
				{ "IsCalcTotalWithWQ", estimateScopeObject.IsCalcTotalWithWq() },
				{ "allowanceEntity", estimateScopeObject.GetActiveAllowanceEntity() },
				{ "estMarkup2CostCodes", estimateScopeObject.GetEstAllMarkup2CostCodes() },
				{ "mdcCostCodeStructure", mdcCostCodeStructure },
				{ "prjCostCodeStructure", prjCostCodeStructure},
				{ "advancedAll", advancedAll},
				{ "lineItemsWithAdvancedAll", lineItemsWithAdvancedAll },
				{ "fixedPriceLineItems",fixedPriceLineItems },
				{ "IsFixedBudgetTotal",isFixedBudgetTotal},
				{ "EquipmentAssemblyCostUnitAlwaysEditable",Logic.GetEquipmentAssemblyCostUnitAlwaysEditable()},
				{ "LineItemContextEstHeaderId",Logic.GetEstHeaderByCurLineItemContextId()},
				{ "isShowPlantAsOneRecord", isShowPlantAsOneRecord}
			};
		}

		/// <summary>
		/// Returns list of combined line items.
		/// </summary>
		/// <param name="filterRequest">filter to specify line items</param>
		/// <returns>list of line items</returns>
		// TODO: merge with GetFilteredList
		[Route("listcombined_new")]
		[HttpPost]
		public IDictionary<string, object> GetCombinedListNew([FromBody] FilterRequest<Int32> filterRequest)
		{
			var filterInfo = new FilterResponse<int>();
			var execInfo = new FilterExecutionInfo<int>(filterRequest, filterInfo);
			execInfo.CreateHint("Start fetching of data by '" + filterRequest.Pattern + "'");

			var prjLocationLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectLocationLogic>();
			// returns all entities and converts it to a dto list
			int projectId = -1, estHeaderId = -1;
			// pinning context
			if (filterRequest != null && filterRequest.PinningContext != null && filterRequest.PinningContext.Any())
			{
				var pinningContext = filterRequest.PinningContext.Where(e => e != null).ToList();
				int? pinnedProjectId = pinningContext.Any() ? filterRequest.GetPinningItem("project.main") : null;
				if (pinnedProjectId == null)
				{
					throw new BusinessLayerException("Please pin the Project!");
				}
			}

			var entities = Logic.GetList(filterRequest, ref filterInfo, ref projectId, ref estHeaderId);

			var originalIds = entities.Select(e => e.Id).ToList();
			var projectEntity = RVPARB.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>().GetProjectById(projectId);

			// default is the standard view
			List<string> lst = new List<string>();
			foreach (var filter in filterRequest.FurtherFilters)
			{
				if (!lst.Contains(filter.Token))
				{
					lst.Add(filter.Token);
				}
			}

			var lineItems2CostGroup = new EstLineItem2CostGroupLogic().GetListByLineItemIds(entities.Select(x => x.Id), estHeaderId).ToList();

			if (lst.Any())
			{
				entities = new EstimateCombinedLineItemLogic().CreateCombinedEntities(entities, lst, lineItems2CostGroup);
			}

			var cgCats = Injector.Get<ICostGroupCatalogLogic>().GetCostGroupCatByProject(projectEntity);

			foreach (var cgCat in cgCats)
			{
				var prjCg1List = lineItems2CostGroup.Where(x => x.CostGroupCatFk == cgCat.Id).ToList();

				foreach (var entity in prjCg1List)
				{
					var lineItem = entities.FirstOrDefault(x => x.EstHeaderFk == entity.EstHeaderFk && x.Id == entity.EstLineItemFk);
					if (lineItem != null)
					{
						switch (cgCat.Code)
						{
							case "PRJCG1":
								lineItem.PrjCostGroup1Fk = entity.CostGroupFk;
								break;
							case "PRJCG2":
								lineItem.PrjCostGroup2Fk = entity.CostGroupFk;
								break;
							case "PRJCG3":
								lineItem.PrjCostGroup3Fk = entity.CostGroupFk;
								break;
							case "PRJCG4":
								lineItem.PrjCostGroup4Fk = entity.CostGroupFk;
								break;
							case "PRJCG5":
								lineItem.PrjCostGroup5Fk = entity.CostGroupFk;
								break;
							case "LICCG1":
								lineItem.LicCostGroup1Fk = entity.CostGroupFk;
								break;
							case "LICCG2":
								lineItem.LicCostGroup2Fk = entity.CostGroupFk;
								break;
							case "LICCG3":
								lineItem.LicCostGroup3Fk = entity.CostGroupFk;
								break;
							case "LICCG4":
								lineItem.LicCostGroup4Fk = entity.CostGroupFk;
								break;
							case "LICCG5":
								lineItem.LicCostGroup5Fk = entity.CostGroupFk;
								break;
						}
					}
				}
			}

			var dtos = entities != null ? entities.Select(e => new EstLineItemDto(e)).ToList() : null;
			execInfo.CreateHint("completed data fetching ...");

			var advancedAllowanceCc = EstimateAdvancedAllowanceHelper.GetAllowanceCostCodeId();

			var systemoptions = new BasicsCustomizeSystemOptionLogic().GetListByFilter(e => e.Id == SystemOption.EnableInputLineitemTotalQuantity || e.Id == SystemOption.ActivateEstimateComparisonIndicator);

			if (dtos != null)
			{
				var estProjectLogic = RVPARB.BusinessEnvironment.GetExportedValue<IEstimateCompositeLogic>();
				var estPrjFactory = RVPARB.BusinessEnvironment.GetExportedValue<IDtoEntityMapper>("Estimate.Project.EstimateCompositeDtoMapper");

				var prjLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();
				var factory = RVPARB.BusinessEnvironment.GetExportedValue<IProjectTypeFactory>();

				// if estHeaderId is still -1, then request was not definite (-> more than one estimate headers and/or projects available)
				var prjEstComposites = (estHeaderId >= 0) ? new List<IIdentifyable> { estProjectLogic.GetCompositeByEstHeader(estHeaderId) } : estProjectLogic.GetEstPrjList(new[] { projectId });
				var prjEntity = projectId != -1 ? prjLogic.GetProjectById(projectId) : null;

				SetSplitQuantitiesValue(estHeaderId, dtos);

				//get assembly templates info
				var assemblyIds = dtos.Where(e => e.EstAssemblyFk.HasValue).Select(e => e.EstAssemblyFk.Value).Distinct().ToList();
				var assemblies = Logic.GetAssembliesByIds(assemblyIds).Select(e => new EstLineItemDto(e));

				//get characteristics data
				var objectIds = dtos.Select(e => e.Id).Distinct().ToList();
				var characterLogic = RVPARB.BusinessEnvironment.GetExportedValue<ICharacteristicDataLogic>();
				var characterGroupLogic = RVPARB.BusinessEnvironment.GetExportedValue<ICharacteristicGroupLogic>();
				int clientId = RVPARB.BusinessEnvironment.CurrentContext.SignedInClientId;
				var groups = characterGroupLogic.GetTreeBySectionAndCompany(28, clientId);
				var groupList = groups.Flatten(e => e.CharacteristicGroups);
				var groupIds = groupList.Select(e => e.Id);
				var defaultCharacteristics = characterLogic.GetDefaultCharacterisitcs(28, 0);

				var characteristics = characterLogic.GetListBySectionIdAndObjectIds(28, originalIds).ToList();

				//estimate column configuration data
				var estHeader = new EstimateMainHeaderLogic().GetItemById(estHeaderId);
				var jobFk = estHeader != null ? estHeader.LgmJobFk : 0;

				var statusEntity = estHeader != null ? (BasicsCustomizeEstStatusEntity)new BasicsCustomizeEstStatusLogic().GetById(estHeader.EstStatusFk) : null;
				var isHeaderStatusReadOnly = statusEntity != null ? statusEntity.IsReadOnly : false;

				var typeEntity = estHeader != null && estHeader.EstTypeFk.HasValue ? (BasicsCustomizeEstimationTypeEntity)new BasicsCustomizeEstimationTypeLogic().GetById(estHeader.EstTypeFk.Value) : null;
				var isCalcTotalWithWq = typeEntity != null ? typeEntity.IsTotalWq : false;
				var isWqReadOnly = typeEntity != null ? typeEntity.IsWqReadOnly : false;

				var columnStructDetails = estHeader != null ? new EstimateMainStructureDetailLogic().GetEstStructureDetails(estHeader.Id) : new List<EstStructureDetailEntity>();

				var columnConfig = estHeader != null ? new EstConfigLogic().GetById(new Platform.Core.IdentificationData() { Id = estHeader.EstConfigFk }) : null;
				var isEstDynamicColumnActive = columnConfig != null && columnConfig.IsColumnConfig == true;

				//Estimate column config and line item characteristic data
				//1. Est Dynamic columns only if EstConfig is activated
				var columnConfigDetails = new List<EstColumnConfigDetailEntity>();
				var extColumnConfigDetails = new Dictionary<int, Dictionary<string, ExtendColumnValue>>();

				if (isEstDynamicColumnActive)
				{
					//Get Estimate config details
					var estLineItemColumnConfigLogic = new EstLineItemColumnConfigLogic(estHeader, prjEntity);
					extColumnConfigDetails = estLineItemColumnConfigLogic.GetExtendColumns(entities);
					columnConfigDetails = estLineItemColumnConfigLogic.GetEstColumnConfigDetails().ToList();
				}

				/* Get the Cost Group Of LineItem */
				var lineItemIds = entities.Select(x => x.Id).ToList();
				List<int> ids = new List<int>();
				foreach (var item in dtos)
				{
					if (item.CombinedLineItems != null)
					{
						var lineItemId = item.CombinedLineItems.Select(e => e.Id).ToList();

						var chart = characteristics.Where(e => lineItemId.Contains(e.ObjectFk)).ToList();

						for (int i = 0; i < chart.Count; i++)
						{
							{
								for (int j = i + 1; j < chart.Count; j++)
								{
									if (chart[i].CharacteristicFk == chart[j].CharacteristicFk)
									{
										if (chart[i].ValueText != chart[j].ValueText)
										{
											characteristics.Remove(chart[i]);
											characteristics.Remove(chart[j]);
										}
									}
								}
							}
						}
						if (extColumnConfigDetails.ContainsKey(item.Id))
						{
							var value = extColumnConfigDetails[item.Id];
							extColumnConfigDetails.Remove(item.Id);
							extColumnConfigDetails[item.CombinedLineItems.FirstOrDefault().Id] = value;
						}
						item.Id = item.CombinedLineItems.FirstOrDefault().Id;
						item.EstHeaderFk = item.CombinedLineItems.FirstOrDefault().EstHeaderFk;
						var combinedIds = item.CombinedLineItems.Select(e => e.Id).ToList();
						foreach (var id in combinedIds)
						{
							ids.Add(id);
						}
					}
					else
					{
						ids.Add(item.Id);
					}
				}

				var dynamicColumns = new
				{
					DynamicColumns = new ColumnIdLogic().getAllColumnItems(),
					ColumnConfigDetails = columnConfigDetails.Any() ? columnConfigDetails.Select(e => new EstColumnConfigDetailDto(e)).ToList() : null,
					Characteristics = characteristics,
					DefaultCharacteristics = defaultCharacteristics,
					CharacteristicsGroupIds = groupIds
				};

				var lineItem2CostGroups = new EstLineItem2CostGroupLogic().GetListByLineItemIds(ids, estHeaderId).ToList();

				var templineItem2CostGroups = new List<MainItem2CostGroupDto>();
				var mainItem2CostGroupLogic = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP");
				templineItem2CostGroups.AddRange(mainItem2CostGroupLogic.GetByFilter(e => e.RootItemId == estHeaderId && ids.Contains(e.MainItemId.Value)).Select(e => new MainItem2CostGroupDto(e)).ToList());

				if (filterRequest.FurtherFilters.Count() < 10)
				{
					this.SetSortCodeValue(dtos);
					var costItemGroup = lineItem2CostGroups.Select(e => e.CostGroupCatFk).Distinct().ToList();
					if (costItemGroup.Count > 0)
					{
						foreach (var item in costItemGroup)
						{
							var list = lineItem2CostGroups.Where(e => e.CostGroupCatFk == item).Select(e => new { e.CostGroupFk }).Distinct().ToList();
							if (list.Count() != 1)
							{
								lineItem2CostGroups.RemoveAll(e => e.CostGroupCatFk == item);
							}
						}
					}
				}
				var costGroupCats = new CostGroupCompleteEntityDto(new CostGroupCatLogic().GetCostGroupCatsByModuleBase(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Estimate, prjEntity));

				// fix huge data to client: change attach CombinedLineitems as id list
				foreach (var dto in dtos)
				{
					if (dto.CombinedLineItems.Any())
					{
						dto.CombinedLineItemsSimple = dto.CombinedLineItems.Select(e => new { e.Id, e.EstHeaderFk, e.EstRuleSourceFk, e.EstLineItemFk });
						dto.CombinedLineItems = null;
					}
				}

				return new Dictionary<string, object>()
				 {
					{ "FilterResult", filterInfo },
					{ "dtos", dtos },
					{ "prjEstComposites", prjEstComposites != null ? prjEstComposites.Select(estPrjFactory.CreateFrom).ToList() : null },
					{ "selectedPrj", prjEntity != null ? factory.CreateFrom(prjEntity) : null },
					{ "isEstDynamicColumnActive", isEstDynamicColumnActive },
					{ "dynamicColumns", dynamicColumns },
					{ "EstStructureDetails", columnStructDetails},
					{ "companyMdcContextFk", EstimateContext.MasterDataContextId },
					{ "LookupAssemblies", assemblies },
					{ "IsHeaderStatusReadOnly" , isHeaderStatusReadOnly },
					{ "advancedAllowanceCc", advancedAllowanceCc },
					{ "SystemOptions", systemoptions },
					{ "jobFk", jobFk},
					{ "ExtendColumns", extColumnConfigDetails },
					{ "LineItem2CostGroups", templineItem2CostGroups },
					{ "CostGroupCats", costGroupCats },
					{ "IsWQReadOnly", isWqReadOnly},
					{ "IsCalcTotalWithWQ",  isCalcTotalWithWq }
				};
			}

			var showPlantAsOneRecordOption = new BasicsCustomizeSystemOptionLogic().GetListByFilter(e => e.Id == SystemOption.ShowEquipmentAssembliesAsOneRecord).FirstOrDefault();
			// result for search by sidebar without parameter (no pattern)
			return new Dictionary<string, object>()
			{
				{ "FilterResult", filterInfo },
				{ "dtos", (IEnumerable<IEstLineItemEntity>)null },
				{ "prjEstComposites", (IEnumerable<IIdentifyable>)null },
				{ "selectedPrj", (IProjectEntity)null },
				{ "companyMdcContextFk", EstimateContext.MasterDataContextId },
				{ "IsHeaderStatusReadOnly" , false},
				{ "advancedAllowanceCc", advancedAllowanceCc },
				{ "SystemOptions", systemoptions },
				{ "showPlantAsOneRecordOption ", showPlantAsOneRecordOption  }
			};
		}



		private void SetSortCodeValue(List<EstLineItemDto> dtos)
		{
			foreach (var item in dtos)
			{
				if (item.CombinedLineItems != null)
				{
					if (item.CombinedLineItems.Select(e => e.SortCode01Fk).Distinct().Count() == 1)
					{
						item.SortCode01Fk = item.CombinedLineItems.First().SortCode01Fk;
					}
					if (item.CombinedLineItems.Select(e => e.SortCode02Fk).Distinct().Count() == 1)
					{
						item.SortCode02Fk = item.CombinedLineItems.First().SortCode02Fk;
					}
					if (item.CombinedLineItems.Select(e => e.SortCode03Fk).Distinct().Count() == 1)
					{
						item.SortCode03Fk = item.CombinedLineItems.First().SortCode03Fk;
					}
					if (item.CombinedLineItems.Select(e => e.SortCode04Fk).Distinct().Count() == 1)
					{
						item.SortCode04Fk = item.CombinedLineItems.First().SortCode04Fk;
					}
					if (item.CombinedLineItems.Select(e => e.SortCode05Fk).Distinct().Count() == 1)
					{
						item.SortCode05Fk = item.CombinedLineItems.First().SortCode05Fk;
					}
					if (item.CombinedLineItems.Select(e => e.SortCode06Fk).Distinct().Count() == 1)
					{
						item.SortCode06Fk = item.CombinedLineItems.First().SortCode06Fk;
					}
					if (item.CombinedLineItems.Select(e => e.SortCode07Fk).Distinct().Count() == 1)
					{
						item.SortCode07Fk = item.CombinedLineItems.First().SortCode07Fk;
					}
					if (item.CombinedLineItems.Select(e => e.SortCode08Fk).Distinct().Count() == 1)
					{
						item.SortCode08Fk = item.CombinedLineItems.First().SortCode08Fk;
					}
					if (item.CombinedLineItems.Select(e => e.SortCode09Fk).Distinct().Count() == 1)
					{
						item.SortCode09Fk = item.CombinedLineItems.First().SortCode09Fk;
					}
					if (item.CombinedLineItems.Select(e => e.SortCode10Fk).Distinct().Count() == 1)
					{
						item.SortCode10Fk = item.CombinedLineItems.First().SortCode10Fk;
					}
				}
			}
		}
		private void SetSplitQuantitiesValue(int estHeaderId, IEnumerable<EstLineItemDto> dtos)
		{
			if (dtos == null || !dtos.Any())
			{
				return;
			}

			var lineItem2MdlObjectQtys = new EstLineItem2MdlObjectQtyLogic().GetListByLineItemIds(estHeaderId, dtos.CollectIds(e => e.Id));

			if (lineItem2MdlObjectQtys == null || !lineItem2MdlObjectQtys.Any())
			{
				return;
			}

			var lineItemId2MdlObjectQtyMap = lineItem2MdlObjectQtys.GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var dto in dtos)
			{
				if (!lineItemId2MdlObjectQtyMap.ContainsKey(dto.Id))
				{
					continue;
				}

				var currentLineItem2MdlObjectQty = lineItemId2MdlObjectQtyMap[dto.Id];

				dto.HasSplitQuantities = currentLineItem2MdlObjectQty.Any(e => e.QuantityTarget != 0m || e.WqQuantityTarget != 0m);
			}
		}

		/// <summary>
		/// Returns list of line items.
		/// </summary>
		/// <param name="filterRequest">filter to specify line items</param>
		/// <returns>list of line items</returns>
		// [Route("listfiltered")] // disabled because listfiltered_new will be used at the moment until both are merged
		// [HttpPost]
		public IDictionary<string, object> GetFilteredList([FromBody] FilterRequest<Int32> filterRequest)
		{
			try
			{
				var filterInfo = new FilterResponse<int>();
				var execInfo = new FilterExecutionInfo<int>(filterRequest, filterInfo);
				execInfo.CreateHint("Start fetching of data by '" + filterRequest.Pattern + "'");
				IEnumerable<EstHeaderDto> estHeaders = null;
				var projectId = 0;
				var projectIds = new int[] { projectId };
				List<IProjectEntity> projectList = null;
				var selectedPrj = new List<IProjectEntity>();
				IDictionary<string, object> result = new Dictionary<string, object>();

				var estProjectLogic = RVPARB.BusinessEnvironment.GetExportedValue<IEstimateCompositeLogic>();
				var estPrjFactory = RVPARB.BusinessEnvironment.GetExportedValue<IDtoEntityMapper>("Estimate.Project.EstimateCompositeDtoMapper");

				var prjLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();
				var factory = RVPARB.BusinessEnvironment.GetExportedValue<IProjectTypeFactory>();

				// returns all entities and converts it to a dto list
				//var lineItems = Logic.GetList(filterRequest, ref filterInfo).Select(e => new EstLineItemDto(e)).ToList();

				if (filterRequest.FurtherFilters != null)
				{
					// used for favorites filter, with furtherFilter parameter
					var furtherFilter = filterRequest.FurtherFilters.FirstOrDefault();

					// we're expecting a primary key for estimate
					if (furtherFilter != null && furtherFilter.Token == "EST_HEADER")
					{
						var estHeaderId = Platform.Common.Convert.ToInt32(furtherFilter.Value);
						estHeaders = new EstimateMainHeaderLogic().GetList(estHeaderId).Select(e => new EstHeaderDto(e)).ToList();
					}

					projectId = (int)filterRequest.ProjectContextId;
					projectIds[0] = projectId;

					//get selected project
					var ent = prjLogic.GetProjectById(projectId);

					if (ent != null)
					{
						selectedPrj.Add(ent);
					}
				}
				else
				{

					projectList = prjLogic.GetListFiltered(filterRequest).ToList();
					projectIds = projectList.Select(e => e.Id).ToArray();
				}

				execInfo.CreateHint(string.Format("completed data fetching ..."));
				var prjEstComposites = estProjectLogic.GetEstPrjList(projectIds);


				result["FilterRequest"] = filterRequest;
				result["FilterResult"] = filterInfo;
				result["SelectedEstHeader"] = estHeaders != null ? estHeaders.First() : null;
				result["PrjEstComposites"] = prjEstComposites != null ? prjEstComposites.Select(e => estPrjFactory.CreateFrom(e)).ToList() : null; ;
				result["SelectedPrj"] = selectedPrj != null ? selectedPrj.Select(e => factory.CreateFrom(e)).FirstOrDefault() : null;
				result["ProjectList"] = projectList != null ? projectList.Select(e => factory.CreateFrom(e)).ToList() : null;

				return result;
			}
			catch (Exception e)
			{
				throw new BusinessLayerException(e.Message, e);
			}
		}

		/// <summary>
		/// Gets the list of line items
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("getlineitemslist")]
		public IEnumerable<EstLineItemDto> GetLineItemList(int estHeaderFk, int id)
		{
			var dtos = Logic.getLineItemLookupList(estHeaderFk, id, string.Empty).Select(e => new EstLineItemDto(e)).ToList();
			return dtos;
		}

		/// <summary>
		/// Map LineItem to BoQ by AI
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("mtwoai/lineitem2boqmapping")]
		[Permission(Permissions.Execute, AIBoqMappingPermissionDescriptor)]
		public Dictionary<string, object> lineitem2BoqMapping(EstLineItem2BoqDto data)
		{
			IEnumerable<BusinessComponents.EstLineItemEntity> mainEntities = Logic.AutoMapLineitemBoq(data.EstHeaderFk, data.ProjectFk, data.BoqLineTypeFks);
			//No boqitems found in current project
			if (mainEntities == null)
			{
				Dictionary<string, object> exceptionResult = new Dictionary<string, object>();
				exceptionResult["BoqItem"] = null;
				exceptionResult["HasBoqItem"] = false;
				exceptionResult["Main"] = null;
				exceptionResult["IsAllLineitemDescriptionEmpty"] = false;
				return exceptionResult;
			}

			//No lineitems found in current project
			if (mainEntities.Count() == 0)
			{
				Dictionary<string, object> exceptionResult = new Dictionary<string, object>();
				exceptionResult["BoqItem"] = null;
				exceptionResult["HasBoqItem"] = true;
				exceptionResult["Main"] = null;
				exceptionResult["IsAllLineitemDescriptionEmpty"] = true;
				return exceptionResult;
			}

			var dtos = mainEntities.ToDtos(e => new EstLineItem2BoqMappingDto(e));

			foreach (EstLineItem2BoqMappingDto dto in dtos)
			{
				dto.IsCheckAi = false;
				dto.OrigBoqItemFk = dto.BoqItemFk;
				if (dto.MatchedBoqFks != null && dto.MatchedBoqFks.Count > 0 && dto.MatchedBoqFks[0] != dto.OrigBoqItemFk)
				{
					// AI recommend another different Boqitem
					dto.BoqItemFk = dto.MatchedBoqFks[0];
					dto.IsCheckAi = true;
				}
				else
				{
					dto.IsCheckAi = false;
				}
			}

			var result = CollectBoqItemLookups(dtos);
			result["IsAllLineitemDescriptionEmpty"] = false;
			result["HasBoqItem"] = true;

			result["Main"] = dtos;
			return result;
		}

		/// <summary>
		/// Collect user feedback AI suggestion for LineItem to BoQ mapping
		/// </summary>
		/// <param name="dtos"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("mtwoai/aimapboqfeedback")]
		public void lineitem2BoqFeedback(List<EstLineItem2BoqMappingDto> dtos)
		{
			try
			{
				if (!AIConfigHelper.AICollectFeedback())
				{
					return;
				}
				if (!Permission.Has(AIBoqMappingPermissionDescriptor, Permissions.Execute))
				{
					// No permission
					return;
				}
				if (dtos.Count > 0)
				{
					var feedbacks = dtos.ConvertAll(x => x.ToFeedbackEntity());
					Logic.AutoMapLineitemBoqFeedback(dtos[0].EstHeaderFk, feedbacks);
				}
			}
			catch (Exception /*e*/)
			{
				// do nothing. Feedback collection is "best-effort".
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		/// <returns></returns>
		private Dictionary<string, object> CollectBoqItemLookups(IEnumerable<EstLineItem2BoqMappingDto> lineItems)
		{
			var lookupBoqFks = new HashSet<object>();
			foreach (EstLineItem2BoqMappingDto dto in lineItems)
			{
				lookupBoqFks.Add(dto.BoqItemFk);
				lookupBoqFks.Add(dto.OrigBoqItemFk);
				if (dto.MatchedBoqFks != null)
				{
					foreach (var boqFk in dto.MatchedBoqFks)
					{
						lookupBoqFks.Add(boqFk);
					}
				}
			}

			return lookupBoqFks.CollectLookups(collector => collector.Add("BoqItem", e => (int?)e));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		/// <returns></returns>
		private Dictionary<string, object> CollectActivityItemLookups(IEnumerable<EstLineItem2ActivityMappingDto> lineItems)
		{
			var lookupActivityFks = new HashSet<object>();
			foreach (EstLineItem2ActivityMappingDto dto in lineItems)
			{
				lookupActivityFks.Add(dto.PsdActivityFk);
				lookupActivityFks.Add(dto.OrigPsdActivityFk);
				if (dto.MatchedActivityFks != null)
				{
					foreach (var activityFk in dto.MatchedActivityFks)
					{
						lookupActivityFks.Add(activityFk);
					}
				}
			}

			return lookupActivityFks.CollectLookups(collector => collector.Add("SchedulingActivity", e => (int?)e));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="mainItem2CostGroupDto"></param>
		/// <returns></returns>
		private Dictionary<string, object> CollectCostGroupItemLookups(IEnumerable<MainItem2CostGroupDto> mainItem2CostGroupDto)
		{
			var lookupCostGroupFks = new HashSet<object>();
			foreach (MainItem2CostGroupDto dto in mainItem2CostGroupDto)
			{
				lookupCostGroupFks.Add(dto.CostGroupFk);
				if (dto.MatchedCostGroupFks != null)
				{
					foreach (var costGroupFk in dto.MatchedCostGroupFks)
					{
						lookupCostGroupFks.Add(costGroupFk);
					}
				}
			}

			return lookupCostGroupFks.CollectLookups(collector => collector.Add("CostGroup", e => (int?)e));
		}

		/// <summary>
		/// Gets the list of line items
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("getsearchlookuplist")]
		public IEnumerable<EstLineItemDto> GetSearchLookupList(EstLineItemReadData filterData)
		{
			return Logic.getLineItemLookupList(filterData.estHeaderFk, filterData.id, filterData.filterValue).Select(e => new EstLineItemDto(e)).ToList();
		}

		/// <summary>
		/// Gets the list of line items
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("filterlineitems4package")]
		public Dictionary<string, object> GetLineItems(EstLineItemReadData data)
		{
			if (data == null || data.packageFk < 0)
			{
				return null;
			}

			IEnumerable<EstLineItemDto> main = null;
			IDictionary<int, int> lineItemId2AssignStatus = null;
			//select the estimate header
			if (data.estHeaderFk > 0)
			{
				var entities = Logic.FilterLineItems4Package(data.projectFk, data.packageFk, out lineItemId2AssignStatus, data.estHeaderFk).ToList();
				Logic.GetPrcItemAssignmentsByEstimateInfo(data.estHeaderFk, entities);
				main = entities.Select(e => new EstLineItemDto(e)
				{
					StatusOfLineItemAssignedToPackage = lineItemId2AssignStatus[e.Id]
				}).ToList();
			}
			else
			{
				var entities = Logic.FilterLineItems4Package(data.projectFk, data.packageFk, out lineItemId2AssignStatus);
				var estHeaderIds = entities.Select(e => e.EstHeaderFk).Distinct().ToList();
				foreach (var h in estHeaderIds)
				{
					var relatedLineItems = entities.Where(e => e.EstHeaderFk == h).ToList();
					Logic.GetPrcItemAssignmentsByEstimateInfo(h, relatedLineItems);
				}
				main = entities.Select(e => new EstLineItemDto(e)
				{
					StatusOfLineItemAssignedToPackage = lineItemId2AssignStatus[e.Id]
				}).ToList();
			}


			var jsData = new Dictionary<string, object>();
			jsData["Main"] = main;

			var lineItemIds = main.Select(x => x.Id).ToList();
			var estLineItemIds = main.CollectIds(e => e.EstLineItemFk);
			if (estLineItemIds != null && estLineItemIds.Any())
			{
				var baseLineItems = main.Where(e => estLineItemIds.Contains(e.Id)).ToList();
				var notFoundbaseLineItemIds = estLineItemIds.Where(e => !lineItemIds.Contains(e)).ToList();
				if (notFoundbaseLineItemIds != null && notFoundbaseLineItemIds.Any())
				{
					var lineItems = new EstimateMainLineItemLogic().GetSearchList(e => notFoundbaseLineItemIds.Contains(e.Id)).ToList();
					if (lineItems != null && lineItems.Any())
					{
						baseLineItems.AddRange(lineItems.Select(e => new EstLineItemDto(e)).ToList());
					}
				}
				if (baseLineItems != null && baseLineItems.Any())
				{
					jsData["estlineitems"] = baseLineItems;
				}
			}
			//get costGroupCat.
			var projectId = data.projectFk;
			var prjLogic = RVPARB.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
			var prjEntity = projectId != -1 ? prjLogic.GetProjectById(projectId) : null;

			var estHeaderIdsFromLI = main.Select(x => x.EstHeaderFk).Distinct().ToList();
			var lineItem2CostGroupEntities = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => estHeaderIdsFromLI.Contains(e.RootItemId.Value)).ToList();
			lineItem2CostGroupEntities = lineItem2CostGroupEntities.Where(e => lineItemIds.Contains(e.MainItemId.Value)).ToList();
			var lineItem2CostGroups = lineItem2CostGroupEntities.Select(e => new MainItem2CostGroupDto(e)).ToList();
			var costGroupCats = new CostGroupCompleteEntityDto(new CostGroupCatLogic().GetCostGroupCatsByModuleBase(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Estimate, prjEntity));
			jsData["LineItem2CostGroups"] = lineItem2CostGroups;
			jsData["CostGroupCats"] = costGroupCats;
			var estAssemblyIds = main.CollectIds(e => e.EstAssemblyFk);
			if (estAssemblyIds.Any())
			{
				var estassemblies = new EstimateAssembliesLogic().GetListByIds(estAssemblyIds.ToArray());
				if (estassemblies.Any())
				{
					jsData["estassemblyfk"] = estassemblies.ToDtos(e => new EstAssembly.EstLineItemDto(e));
				}
			}
			return jsData;
		}

		/// <summary>
		/// Gets the list of line items by wic boq items
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("filterlineitemsbywicboq")]
		public IEnumerable<EstLineItemDto> GetLineItemsByWicBoq(LineItemsByWicBoqInfo lineItemsByWicBoqInfo)
		{
			var wicBoqItemIds = lineItemsByWicBoqInfo.WicBoqItemIds.Where(x => x.HasValue).Select(s => s.Value).ToList();
			if (wicBoqItemIds.Any() && lineItemsByWicBoqInfo.WicBoqHeaderId.HasValue)
			{
				var lineItems = Logic.GetLineItemsByWicBoq(lineItemsByWicBoqInfo.WicBoqHeaderId.Value, wicBoqItemIds);
				var result = lineItems.Select(e => new EstLineItemDto(e)).ToList();
				return result;
			}
			return new List<EstLineItemDto>();
		}

		/// <summary>
		/// 
		/// </summary>
		public class LineItemsByWicBoqInfo
		{
			/// <summary>
			///
			/// </summary>
			public int? WicBoqHeaderId { get; set; }

			/// <summary>
			///
			/// </summary>
			public List<int?> WicBoqItemIds { get; set; }
		}

		/// <summary>
		/// Gets the list of line items
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("filterlineitems4sales")]
		public Dictionary<string, object> GetLineItemsForWip(EstLineItemReadData data)
		{
			var filterData = Logic.FilterLineItemsForSales(data);
			var jsData = filterData.jsData;
			if (jsData["Main"] != null)
			{
				var main = jsData["Main"];
				var entities = filterData.entities;
				//get costGroupCat.
				var projectId = data.projectFk;
				var prjLogic = RVPARB.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
				var prjEntity = projectId != -1 ? prjLogic.GetProjectById(projectId) : null;
				var lineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => filterData.lineItemIds.Contains(e.MainItemId.Value)).Select(e => new MainItem2CostGroupDto(e)).ToList();
				var costGroupCats = new CostGroupCompleteEntityDto(new CostGroupCatLogic().GetCostGroupCatsByModuleBase(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Estimate, prjEntity));
				jsData["EstimateLineItems"] = entities.Select(e => new EstLineItemDto(e)).ToList();
				jsData["LineItem2CostGroups"] = lineItem2CostGroups;
				jsData["CostGroupCats"] = costGroupCats;
			}
			return jsData;
		}

		/// <summary>
		/// Get line item by changeID
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("tochange")]
		public IEnumerable<RichEstLineItemDto> GetItemsToChange(int changeId, int projectId)
		{
			return Logic.GetRichLineItem(changeId, projectId).ToList().Select(i => new RichEstLineItemDto(i.LineItem, i.DedicatedAssembly)).ToList();
		}

		/// <summary>
		/// Get line item by Id
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("toactivity")]
		public IDictionary<string, object> GetItemsToActivityAndType(int activityId, int? estType, bool isFilterActive)
		{
			var lineitems = Logic.ByActivityAndEstTypeCompleteRich(activityId, estType, isFilterActive).ToList();
			List<RichEstLineItemDto> dtos = null;
			EstConfigEntity columnConfig = null;
			IEnumerable<EstRoundingConfigDetailEntity> mainConfig = null;
			if (lineitems != null && lineitems.Any())
			{
				dtos = lineitems.Select(i => new RichEstLineItemDto(i.LineItem, i.DedicatedAssembly)).ToList();
				var lineItem = lineitems != null ? lineitems.FirstOrDefault().LineItem : null;
				var estHeader = lineItem != null ? lineItem.EstHeaderEntity : null;
				if (estHeader == null && lineItem != null)
				{
					estHeader = new EstimateMainHeaderLogic().GetListByFilter(e => e.Id == lineItem.EstHeaderFk).FirstOrDefault();
				}
				columnConfig = estHeader != null ? new EstConfigLogic().GetById(new Platform.Core.IdentificationData() { Id = estHeader.EstConfigFk }) : null;
				mainConfig = estHeader != null ? new EstimateRoundingHelper().GetRoundingConfigDetails(estHeader.Id) : null;
			}
			var estRoundingConfigDetails = new
			{
				RoundingConfigDetails = mainConfig,
				EstRoundingColumnIds = columnConfig != null ? new EstimateRoundingHelper().GetRoundingColumnIds() : null
			};

			return new Dictionary<string, object>()
			{
				{ "dtos", dtos },
				{ "EstRoundingConfigDetails", estRoundingConfigDetails }
			};
		}

		/// <summary>
		/// Create an empty line item detail
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("create")]
		public EstLineItemDto Create(EstLineItemCreationData data)
		{
			return new EstLineItemDto(Logic.Create(data, false));
		}

		#region delete lineitems

		/// <summary>
		/// Delete an existing line item detail
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("delete")]
		public IEnumerable<EstLineItemDto> Delete(IEnumerable<EstLineItemDto> dto)
		{
			var entities = dto.Select(e => e.Copy()).ToList();

			Logic.DeleteLineItems(entities);

			return dto;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="dto"></param>
		/// <returns>0, no depend; 1, package item assignment; 2, lineitem reference; 3, qto line; 4, lineitem qty</returns>
		[Route("hasdependentdata")]
		[HttpPost]
		public int HasDependentData(IEnumerable<EstLineItemDto> dto)
		{
			var entities = dto.Select(e => e.Copy()).ToList();
			return Logic.HasDependentData(entities);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[Route("getdependentdata")]
		[HttpPost]
		public IEnumerable<DependDataDto> GetDependentData(IEnumerable<EstLineItemDto> dto)
		{
			var entities = dto.Select(e => e.Copy()).ToList();
			return Logic.GetDependentDatas(entities).Select(x => new DependDataDto(x)).ToList();
		}

		#endregion

		/// <summary>
		/// Save an empty line item detail
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("save")]
		public EstLineItemDto Save(EstLineItemDto dto)
		{
			try
			{
				return new EstLineItemDto((BusinessComponents.EstLineItemEntity)Logic.Save(dto.Copy()));
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, dto);
			}
		}

		/// <summary>
		/// Update estimate line items
		/// </summary>
		/// <returns>EstMainCompleteDto</returns>
		[HttpPost]
		[Route("update")]
		public EstMainCompleteDto Update(EstMainCompleteDto dto)
		{
			try
			{
				var enti = dto.Copy();

				Logic.Update(enti);

				var ruleExecResult = new EstimateQNARuleExecutor().Run(enti);

				var result = new EstMainCompleteDto(enti);

				result.UserDefinedcolsOfLineItemModified = result.UserDefinedcolsOfLineItemModified == null ? new List<UserDefinedcolValEntity>() : result.UserDefinedcolsOfLineItemModified;
				result.UserDefinedcolsOfResourceModified = result.UserDefinedcolsOfResourceModified == null ? new List<UserDefinedcolValEntity>() : result.UserDefinedcolsOfResourceModified;

				if (ruleExecResult.IsAnyRuleExecuted && dto.EstHeaderId > 0)
				{
					var existIds = result.EstLineItems != null ? result.EstLineItems.Select(x => x.Id).ToList() : new List<int>();

					result.EstLineItemsUpdatedByRule = dto.ShowedLineItemIds != null && dto.ShowedLineItemIds.Any()
						? Logic.GetLineItemByIds(dto.ShowedLineItemIds ?? new List<int>(), dto.EstHeaderId).Select(x => new EstLineItemDto(x as BusinessComponents.EstLineItemEntity))
						: Logic.GetList(dto.EstHeaderId).Select(x => new EstLineItemDto(x));
					if (result.EstLineItemsUpdatedByRule != null && result.EstLineItemsUpdatedByRule.Any() && dto.ProjectId.HasValue)
					{
						// set project id for all line items
						result.EstLineItemsUpdatedByRule.SetPropertyValue((a, b) => { a.ProjectFk = b; }, dto.ProjectId.Value);
					}

					/* search the userDefinedcolVal again */
					var userDefinedColumnValueLogic = new UserDefinedColumnValueLogic();
					var pk1s = new List<int>() { dto.EstHeaderId };
					var pk2s = existIds.Select(e => new int?(e));
					result.UserDefinedcolsOfLineItemModified = result.UserDefinedcolsOfLineItemModified.Concat(userDefinedColumnValueLogic.GetListByKeys((int)userDefinedColumnTableIds.EstimateLineItem, dto.EstHeaderId, existIds));
					if (result.IsQNARuleExecuteSuccess && result.EstResourcesAfterQNARuleExecuted != null && result.EstResourcesAfterQNARuleExecuted.Any())
					{
						var readData = result.EstResourcesAfterQNARuleExecuted.Where(e => e.EstRuleSourceFk.HasValue).Select(e => new Tuple<int, int, int, int>((int)userDefinedColumnTableIds.EstimateResource, e.EstHeaderFk, e.EstLineItemFk, e.Id));
						result.UserDefinedcolsOfResourceModified = result.UserDefinedcolsOfResourceModified.Concat(userDefinedColumnValueLogic.GetListByKeys(readData));
					}
				}

				// generate rest of advanced allowance of reference line item
				if (result.EstLineItems != null && result.EstLineItems.Any())
				{
					var restRefLineItem = result.EstLineItems.Where(x => x.EstLineItemFk.HasValue).ToList();

					foreach (var item in restRefLineItem)
					{
						var baseItem = result.EstLineItems.FirstOrDefault(x => item.EstLineItemFk == x.Id);

						item.ProjectFk = baseItem != null ? baseItem.ProjectFk : 0;
					}
				}

				if (enti.EstLineItems != null && enti.EstLineItems.Any())
				{
					result.UserDefinedcolsOfLineItemModified = result.UserDefinedcolsOfLineItemModified.Concat(enti.EstLineItems.Where(e => e.UserDefinedcolValEntity != null).Select(e => new UserDefinedcolValEntity(e.UserDefinedcolValEntity)));
				}

				SetSplitQuantitiesValue(result.EstHeaderId, result.EstLineItems);

				result.EstLineItemStatusList = Logic.GetEstLineItemStatus();

				var showProtectedAssembliesAsOneRecordSystemOption = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ShowProtectedAssembliesAsOneRecord);

				var showEquipmentAssembliesAsOneRecordSystemOption = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ShowEquipmentAssembliesAsOneRecord);

				var estLineItemUpdateFrmPrjLogic = new EstLineItemUpdateFrmPrjLogic(dto.ProjectId, new EstResourceUpdateOption());

				if (showProtectedAssembliesAsOneRecordSystemOption && dto.fromModule == "Estimate.Main")
				{
					if (result.EstLineItems != null && result.EstLineItems.Any())
					{
						foreach (var lineItem in result.EstLineItems)
						{
							if (lineItem.EstResources != null && lineItem.EstResources.Any()) { ShowProtectedAssemblyAsOneRecord(lineItem.EstResources); }
						}
					}

					if (result.EstResourceToSave != null && result.EstResourceToSave.Any())
					{
						foreach (var estResourceToSave in result.EstResourceToSave)
						{
							ShowProtectedAssemblyAsOneRecord(new List<EstResourceDto> { estResourceToSave.EstResource });
						}
					}

				}
				void ShowProtectedAssemblyAsOneRecord(IEnumerable<EstResourceDto> resources)
				{
					foreach (var resource in resources)
					{
						if (resource != null && resource.EstResources != null && resource.EstResources.Any())
						{
							if (resource.EstResourceTypeFk == (int)EstResourceType.Assembly)
							{
								if (estLineItemUpdateFrmPrjLogic.IsProtectedAssemblyUpdated(resource.Copy()))
								{
									resource.EstResources.Clear();
									resource.HasChildren = false;
								}
							}
							ShowProtectedAssemblyAsOneRecord(resource.EstResources);
						}
					}
				}

				if (showEquipmentAssembliesAsOneRecordSystemOption && dto.fromModule == "Estimate.Main")
				{
					if (result.EstLineItems != null && result.EstLineItems.Any())
					{
						foreach (var lineItem in result.EstLineItems)
						{
							if (lineItem.EstResources != null && lineItem.EstResources.Any()) { ShowEquipmentAssemblyAsOneRecord(lineItem.EstResources); }
						}


						if (result.EstResourceToSave != null && result.EstResourceToSave.Any())
						{
							foreach (var estResourceToSave in result.EstResourceToSave)
							{
								ShowEquipmentAssemblyAsOneRecord(new List<EstResourceDto> { estResourceToSave.EstResource });
							}
						}
					}
				}

				void ShowEquipmentAssemblyAsOneRecord(IEnumerable<EstResourceDto> resources)
				{
					foreach (var resource in resources)
					{
						// Check if resource is a Plant and doesn't have a related resource
						if (resource != null && resource.EstResourceTypeFk == (int)EstResourceType.Plant && !resource.EstResourceFk.HasValue)
						{
							resource.EstResources = null; // Remove any child resources
							resource.HasChildren = false; // Mark as no children
						}
					}
				}

				return result;
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, dto);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="lgmjobfk"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("getdefaultwot")]
		public int? GetDefaultWot(int lgmjobfk)
		{
			var plantEstimateSettingsLogic = Injector.Get<ILogisticPlantEstimateSettings>();
			if (lgmjobfk > 0)
			{
				return plantEstimateSettingsLogic.DefaultPlantEstimateWorkOperationType(lgmjobfk);
			}
			else {
				return plantEstimateSettingsLogic.DefaultPlantEstimateWorkOperationType();
			}

		}

		/// <summary>
		/// Update estimate combined line items
		/// </summary>
		/// <returns>EstMainCompleteDto</returns>
		[HttpPost]
		[Route("updateCombinedLineItems")]
		public EstMainCompleteDto UpdateCombinedLineItems(EstMainCompleteDto dto)
		{
			try
			{
				var enti = dto.Copy();

				Logic.UpdateCombinedLineItems(enti);

				new EstimateQNARuleExecutor().Run(enti);

				var result = new EstMainCompleteDto(enti);

				// generate rest of advanced allowance of reference line item
				if (result.EstLineItems != null && result.EstLineItems.Any())
				{
					var restRefLineItem = result.EstLineItems.Where(x => x.EstLineItemFk.HasValue).ToList();

					foreach (var item in restRefLineItem)
					{
						var baseItem = result.EstLineItems.FirstOrDefault(x => item.EstLineItemFk == x.Id);

						item.ProjectFk = baseItem != null ? baseItem.ProjectFk : 0;
					}
				}

				SetSplitQuantitiesValue(result.EstHeaderId, result.EstLineItems);

				return result;
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, dto);
			}
		}

		/// <summary>
		/// Returns list of Project costcode items for given project id
		/// </summary>
		/// <returns>List of Project costcode items</returns>
		[HttpGet]
		[Route("prjcostcodes")]
		public IEnumerable<IIdentifyable> GetPrjCostCodes(int projectId)
		{
			var factory = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesTypeFactory>();

			return new EstProjectCostCodeHelper(projectId).GetPrjCostCodes(projectId).Select(e => factory.CreateFrom(e)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="estHeaderFk"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("prjestcostcodes")]
		public IEnumerable<IIdentifyable> GetPrjEstCostCodes(int projectId, int? estHeaderFk)
		{
			var factory = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesTypeFactory>();

			return new EstProjectCostCodeHelper(projectId).GetPrjEstCostCodes(estHeaderFk).Select(e => factory.CreateFrom(e)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="estHeaderFk"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getestmajorcostcode")]
		public IEnumerable<IIdentifyable> GetEstMajorCostCodes(int projectId, int estHeaderFk)
		{
			var factory = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesTypeFactory>();

			return new EstProjectCostCodeHelper(projectId).GetEstMajorCostCodes(estHeaderFk).Select(e => factory.CreateFrom(e)).ToList();
		}

		/// <summary>
		/// Returns Estimate(Project/Master) costcode item for given project id and costcode id
		/// </summary>
		/// <returns>Costcode item</returns>
		[HttpGet]
		[Route("estcostcodebyid")]
		public CostCodeDto GetEstCostCodeById(int projectId, int costcodeId, int? jobId)
		{
			return new CostCodeDto(new EstProjectCostCodeHelper(projectId).GetEstCostCode(projectId, costcodeId, jobId));
		}

		/// <summary>
		/// Returns list of basics/mdc costcode items with corresponding project costcodes
		/// </summary>
		/// <returns>List of Project costcode items</returns>
		[HttpGet]
		[Route("mdccostcodes")]
		public IEnumerable<CostCodeDto> GetMdcCostCodes(int projectId, int startId = 0, int depth = 999)
		{
			return new CostCodeSearchService().GetMdcCostCodes(startId, depth, projectId).Select(e => new CostCodeDto(e)).ToList();
		}

		/// <summary>
		/// GetResourcesByLineItem Get list of line items with corresponding resources
		/// </summary>
		/// <returns>List of line items with corresponding resources</returns>
		[HttpPost]
		[Route("getresourcesbyestheaderfk")]
		public IEnumerable<EstLineItemDto> GetResourcesByEstHeader(TotalCaluationParam param)
		{
			var lineItemAndResources = new EstimateMainResourceTotalLogic().GetResourcesByEstHeader(param);

			return lineItemAndResources != null ? lineItemAndResources.Select(e => new EstLineItemDto(e)).ToList() : null;
		}

		/// <summary>
		/// GetResourcesByLineItem Get list of line items with corresponding resources
		/// </summary>
		/// <returns>List of line items with corresponding resources</returns>
		[HttpPost]
		[Route("getresourcesbylineitem")]
		public IEnumerable<EstLineItemDto> GetResourcesByLineItem(TotalCaluationParam param)
		{
			var lineItemAndResources = new EstimateMainResourceTotalLogic().GetResourcesByLineItem(param);

			return lineItemAndResources != null ? lineItemAndResources.Select(e => new EstLineItemDto(e)).ToList() : null;
		}

		/// <summary>
		/// IsUniqCode validate code
		/// </summary>
		/// <returns>true/false based on validation</returns>
		[HttpPost]
		[Route("isuniquecode")]
		public bool IsUniqueCode(EstLineItemReadData data)
		{
			return new EstLineItemCodeGenerator(data.estHeaderFk).IsUnique(e => e.EstHeaderFk == data.estHeaderFk && e.Id != data.id && e.Code == data.code);
		}

		/// <summary>
		///Update Line Item and Resources from Project, update Rules and Parameters as per conditions given in data
		/// </summary>
		/// <returns>updated line items</returns>
		[HttpPost]
		[Route("updateestimate")]
		public EstUpdateEstimateResultDto UpdateEstimate(EstLineItemUpdateFrmPrjData data)
		{
			try
			{
				var result = new EstimateMainCalculator().UpdateEstimate(data);

				return new EstUpdateEstimateResultDto(result);
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		///Update Line Item and Resources from Project, update Rules and Parameters as per conditions given in data
		/// </summary>
		/// <returns>updated line items</returns>
		[HttpPost]
		[Route("generateBoqUnitRate")]
		public void generateBoqUnitRate(EstLineItemUpdateFrmPrjData data)
		{
			try
			{
				new EstimateMainCalculator().GenerateBoqUnitRate(data);
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		/// Update the composite assembly of estHeader
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("updatecompositeassembly")]
		public IEnumerable<EstLineItemDto> UpdateCompositeAssembly(UpdateCompositeAssemblyInfo data)
		{
			try
			{
				var result = new EstCompositeAssemblyUpdateLogic().Execute(data.EstHeaderId, data.ProjectId);

				return result.Select(e => new EstLineItemDto(e)).ToList();
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		/// Copy a line item
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("copy")]
		public EstMainCompleteDto Copy(CopyOrMoveData data)
		{
			try
			{
				var listFrom = data.FromItems.Select(e => e.Copy()).ToList();
				IEnumerable<EstLineItemDto> result;

				if (data.IsLookAtCopyOptions)
				{
					result = Logic.Copy(listFrom, data.ToItem, null, data.IsLookAtCopyOptions).Select(e => new EstLineItemDto(e)).ToList();
				}
				else
				{
					result = Logic.Copy(listFrom, data.ToItem, null).Select(e => new EstLineItemDto(e)).ToList();
				}

				var dto = new EstMainCompleteDto();

				dto.CopiedLineItems = result;

				var estHeaderFk = result.FirstOrDefault().EstHeaderFk;

				/* Get the Cost Group Of LineItem */
				var lineItemIds = result.Select(x => x.Id).ToList();
				var lineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => e.RootItemId == estHeaderFk && lineItemIds.Contains(e.MainItemId.Value)).Select(e => new MainItem2CostGroupDto(e)).ToList();

				dto.EstLineItem2CostGroups = lineItem2CostGroups;

				return dto;
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data.FromItems);
			}
		}

		/// <summary>
		/// Copy a line item
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("copyfromleadingstructure")]
		public EstMainCompleteDto CopyFromLeadingStructure(LeadingStructureParaData para)
		{
			try
			{
				var newLineItems = Logic.CopyLineItemUsingLeadingStructure(para);

				var dto = new EstMainCompleteDto();

				dto.CopiedLineItems = newLineItems.Select(e => new EstLineItemDto((BusinessComponents.EstLineItemEntity)e)).ToList();

				var estHeaderFk = newLineItems.FirstOrDefault().EstHeaderFk;

				/* Get the Cost Group Of LineItem */
				var lineItemIds = newLineItems.Select(x => x.Id).ToList();
				var lineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => e.RootItemId == estHeaderFk && lineItemIds.Contains(e.MainItemId.Value)).Select(e => new MainItem2CostGroupDto(e)).ToList();

				dto.EstLineItem2CostGroups = lineItem2CostGroups;

				return dto;
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, para);
			}
		}


		/// <summary>
		/// Move a line item
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("move")]
		public IEnumerable<EstLineItemDto> Move(int toItem, IEnumerable<EstLineItemDto> fromItems)
		{
			var listFrom = fromItems.Select(e => e.Copy()).ToList();

			Logic.Move(listFrom, toItem);

			return fromItems;
		}

		/// <summary>
		/// LineItem Copy or Move input data
		/// </summary>
		public class CopyOrMoveData
		{
			/// <summary>
			/// copied to lineitem
			/// </summary>
			public int ToItem { get; set; }

			/// <summary>
			/// pasted lineitem data
			/// </summary>
			public IEnumerable<EstLineItemDto> FromItems { get; set; }

			/// <summary>
			/// Is LookAt Copy Options
			/// </summary>
			public bool IsLookAtCopyOptions { get; set; }

		}

		/// <summary/>
		public class DynamicLineItemsData
		{
			/// <summary/>
			public IEnumerable<int> LineItemIds { get; set; }

			/// <summary/>
			public int EstHeaderFk { get; set; }

			/// <summary/>
			public int EstColumnConfigFk { get; set; }

			/// <summary/>
			public int PrjProjectFk { get; set; }
		}

		/// <summary>
		///
		/// </summary>
		public class DynamicLineItemCellData
		{
			/// <summary>
			///
			/// </summary>
			public int LineItemId { get; set; }

			/// <summary>
			///
			/// </summary>
			public int EstHeaderFk { get; set; }

			/// <summary>
			///
			/// </summary>
			public int ColumnConfigTypeDetailId { get; set; }

		}

		/// <summary>
		/// get the total value for the dynamic column in the lineitem container
		/// </summary>
		/// <param name="dynamicData"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("getdynamiccellvalue")]
		public object GetDynamicCellValue(DynamicLineItemCellData dynamicData)
		{
			if (dynamicData.EstHeaderFk > 0 && dynamicData.LineItemId > 0 && dynamicData.ColumnConfigTypeDetailId > 0)
			{
				// return new EstimateMainLineItemLogic().GetCellTotal(dynamicData.EstHeaderFk, dynamicData.LineItemId, dynamicData.ColumnConfigTypeDetailId);
			}

			return null;
		}

		/// <summary>
		/// get the total value for the dynamic column in the lineitem container
		/// </summary>
		/// <param name="para"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("generatefromleadingstructure")]
		public object GenerateFromLeadingStructure(LeadingStructureParaData para)
		{
			LookResourceManagerWrapper resourceWrapper = null;
			try
			{
				if (para != null && (para.UpdateExistedItem || para.CreateOnlyNewLineItem) && para.EstHeaderFk > 0)
				{
					resourceWrapper = para.CreateOnlyNewLineItem ? new LookResourceManagerWrapper(LockResourceType.GenerateLineItemFromLs) : null;
					var key = para.StructureName == "ControllingUnit" || para.StructureName == "Location" ? para.ProjectFk : para.RootItemId;
					var resourceItem = resourceWrapper != null ? resourceWrapper.GetResource(para.StructureName + "_" + para.EstHeaderFk + "_" + key, new EstimateMainHeaderLogic().Context.UserName) : new ResourceItem("");

					// if create new lineItem option is check, then get lock resource, Prevent multiple users from creating lineItem by same leading sturcture in same Estimate Header
					if (resourceItem.UserThreadId != Environment.CurrentManagedThreadId.ToString() && para.CreateOnlyNewLineItem)
					{
						return new
						{
							result = false,
							isConcurrencyErr = true,
							errorLog = string.Format(Resources.ERR_CurrentDataIsUsing, resourceItem.OwnerUser)
						};
					}

					object res;
					lock (resourceItem)
					{
						IEnumerable<EstLineItemEntity> lineItemEntities = null;
						var result = new EstGenarateLineItemsLogic().UpdateItemFromLeadingStructure(para, ref lineItemEntities);
						res = new
						{
							result = result,
							errorLog = result ? "" : Resources.Err_No_data_to_update
						};
					}
					return res;
				}

				return new
				{
					result = false,
					errorLog = Resources.Err_No_data_to_update
				};
			}
			catch (Exception ex)
			{
				if (ex is DataUpdateVersionConflictException || (ex.InnerException != null && ex.InnerException is DataUpdateVersionConflictException))
				{
					return new
					{
						result = false,
						isConcurrencyErr = true,
						errorLog = Resources.ERR_Current_estimate_updating
					};
				}

				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, para);
			}
			finally
			{
				if (resourceWrapper != null) { resourceWrapper.Dispose(); }
			}
		}

		/// <summary>
		/// calculate the sum of all the resources for  line items.
		/// </summary>
		/// <param name="sumReqData"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("getcolumnestimatesumvalue")]
		public IEnumerable<Dictionary<string, object>> GetColumnEstimateSumValue(DynamicLineItemsData sumReqData)
		{
			return new EstimateMainResourceLogic().ColumnEstimateSumValue(sumReqData.LineItemIds, sumReqData.EstHeaderFk, sumReqData.EstColumnConfigFk, sumReqData.PrjProjectFk);
		}

		/// <summary>
		/// get all lineitems resources
		/// </summary>
		/// <param name="sumReqData"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("columnestimatelineitem2resources")]
		public object ColumnEstimateLineitem2Resources(DynamicLineItemsData sumReqData)
		{
			Dictionary<int, IEnumerable<EstResourceDto>> lineItemsResources = new Dictionary<int, IEnumerable<EstResourceDto>>();

			var dic = new EstimateMainResourceLogic().ColumnEstimateLineitem2Resources(sumReqData.LineItemIds, sumReqData.EstHeaderFk, sumReqData.PrjProjectFk);

			foreach (var item in dic)
			{
				// no rule type needed to improve performance
				var resourceDto = item.Value.Where(e => e.EstResourceFk != e.Id).Select(e => { return new EstResourceDto(e); }).ToList();//e.EstRuleSourceFk = null;
				lineItemsResources.Add(item.Key, resourceDto);
			}

			var configInfo = new EstColumnConfigDetailLogic().GetColumnConfigDetaisInfoByEstHeaderId(sumReqData.EstHeaderFk);

			var projectCostCodesLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();

			var prjCostCodes = projectCostCodesLogic.GetAllCostCodesInProject(sumReqData.PrjProjectFk);

			return new
			{
				Main = lineItemsResources,
				columnConfigInfo = new
				{
					Main = configInfo.Main.Select(e => new EstColumnConfigDetailDto(e)).ToList(),
					CostCode = configInfo.CostCode.Select(item => new CostCodeDto(item)).ToList(),
					PrjCostCode = prjCostCodes.Select(e => new { Id = e.Id, MdcCostCodeFk = e.MdcCostCodeFk, IsRate = e.IsRate }).ToList(),
					MaterialV = configInfo.MaterialV.Select(item => new MaterialLookupVDto(item)).ToList(),
					DynamicColumns = configInfo.DynamicColumns
				}
			};

		}

		/// <summary>
		/// copy boqItem reference from assembly with default WIC-Group
		/// </summary>
		/// <param name="assemblyId">assemblyId</param>
		/// <param name="assemblyHeaderId">assemblyHeaderId</param>
		/// <param name="lineItemHeaderFk">lineItemHeaderFk</param>
		/// <param name="projectId">projectId</param>
		/// <returns>Project Boq Item</returns>
		[HttpGet]
		[Route("linkboqitemtolineitem")]
		public object LinkBoqItemByAssembly(int assemblyId, int assemblyHeaderId, int lineItemHeaderFk, int projectId)
		{
			return Logic.LinkBoqItemByAssembly(assemblyId, assemblyHeaderId, lineItemHeaderFk, projectId, out _);
		}

		/// <summary>
		/// Create a deep copy of given line Items(copy resources, model objects, rules and parameters for the same)
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("deepcopy")]
		public EstMainCompleteDto CreateDeepCopy(EstMainCompleteDto data)
		{
			try
			{
				var ent = data.Copy();

				Logic.CreateDeepCopy(ent);

				/* Get the Cost Group Of LineItem */
				var lineItemIds = ent.CopiedLineItems.Select(x => x.Id).ToList();
				var lineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => e.RootItemId == data.EstHeaderId && lineItemIds.Contains(e.MainItemId.Value)).Select(e => new MainItem2CostGroupDto(e)).ToList();

				Logic.GetPrcItemAssignmentsByEstimateInfo(data.EstHeaderId, ent.CopiedLineItems);

				var dto = new EstMainCompleteDto(ent);

				dto.EstLineItem2CostGroups = lineItem2CostGroups;

				Logic.CalculatePackageByLineItems(ent.CopiedLineItems);

				// collect and return copied user defined values.
				if (ent.CopiedLineItems != null && ent.CopiedLineItems.Any())
				{
					dto.UserDefinedcolsOfLineItemModified = ent.CopiedLineItems.Where(e => e.UserDefinedcolValEntity != null).Select(e => new UserDefinedcolValEntity(e.UserDefinedcolValEntity)).ToList();
				}

				return dto;
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("resolveassembliestolineitem")]
		public AssemblyLineItemCharacteristcData ResolveAssembliesToLineItem(EstLineItemResolveAssembliesData data)
		{
			try
			{
				var result = new AssemblyLineItemCharacteristcData();

				var entities = Logic.ResolveAssembliesToLineItem(data);

				if (result.LineItemsTotal == null)
				{
					result.LineItemsTotal = new List<EstLineItemDto>();
				}

				if (data.lineItemsNoNeedToUpdate != null && data.lineItemsNoNeedToUpdate.Any())
				{
					result.lineItemsNoNeedToUpdate = data.lineItemsNoNeedToUpdate.Select(e => new EstLineItemDto(e)).ToList();

					result.LineItemsTotal.AddRange(result.lineItemsNoNeedToUpdate);
				}

				result.copyBoqItem = data.copyBoqItem;

				result.lineItemsUpdated = entities.Select(e => new EstLineItemDto(e)).ToList();

				result.LineItemsTotal.AddRange(result.lineItemsUpdated);

				result.EstLineItems2EstRules = new EstimateRuleLineItemLogic().GetList(data.LineItemCreationData.EstHeaderFk).Select(e => new EstLineItem2EstRuleDto(e)).ToList();

				result.EstPrjRules = new EstimateRulePrjEstRuleLogic().GetList(data.LineItemCreationData.ProjectId).Select(e => new PrjEstRuleDto(e)).ToList();

				result.EstLineItemsParams = new EstimateParameterLineItemLogic().GetByFilter(i => i.EstHeaderFk == data.LineItemCreationData.EstHeaderFk);

				var lineItemIds = entities.Select(e => e.Id).ToList();

				result.LineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP")
						  .GetByFilter(e => e.RootItemId == data.LineItemCreationData.EstHeaderFk && e.MainItemId.HasValue && lineItemIds.Contains(e.MainItemId.Value))
						  .Select(e => new MainItem2CostGroupDto(e)).ToList();

				//User defined price
				result.UserDefinedcolsOfLineItemModified = entities.Where(e => e.UserDefinedcolValEntity != null).Select(e => new UserDefinedcolValEntity(e.UserDefinedcolValEntity)).ToList();

				//TODO: Add characteristics to each lineitem created
				return result;
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("resolveplantassembliestolineitem")]
		public AssemblyLineItemCharacteristcData ResolvePlantAssembliesToLineItem(EstLineItemResolvePlantAssembliesData data)
		{
			try
			{
				var result = new AssemblyLineItemCharacteristcData();
				var entities = Logic.ResolvePlantAssembliesToLineItem(data);

				if (result.LineItemsTotal == null)
				{
					result.LineItemsTotal = new List<EstLineItemDto>();
				}

				if (data.lineItemsNoNeedToUpdate != null && data.lineItemsNoNeedToUpdate.Any())
				{
					result.lineItemsNoNeedToUpdate = data.lineItemsNoNeedToUpdate.Select(e => new EstLineItemDto(e));

					result.LineItemsTotal.AddRange(result.lineItemsNoNeedToUpdate);
				}

				//Todo1 Commented boqItem
				//result.copyBoqItem = data.copyBoqItem;

				result.lineItemsUpdated = entities.Select(e => new EstLineItemDto(e)).ToList();

				result.LineItemsTotal.AddRange(result.lineItemsUpdated);

				result.EstLineItems2EstRules = new EstimateRuleLineItemLogic().GetList(data.LineItemCreationData.EstHeaderFk).Select(e => new EstLineItem2EstRuleDto(e));

				result.EstPrjRules = new EstimateRulePrjEstRuleLogic().GetList(data.LineItemCreationData.ProjectId).Select(e => new PrjEstRuleDto(e));

				result.EstLineItemsParams = new EstimateParameterLineItemLogic().GetByFilter(i => i.EstHeaderFk == data.LineItemCreationData.EstHeaderFk);

				var lineItemIds = entities.Select(e => e.Id).ToList();

				result.LineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP")
						  .GetByFilter(e => e.RootItemId == data.LineItemCreationData.EstHeaderFk && e.MainItemId.HasValue && lineItemIds.Contains(e.MainItemId.Value))
						  .Select(e => new MainItem2CostGroupDto(e)).ToList();

				//User defined price
				result.UserDefinedcolsOfLineItemModified = entities.Where(e => e.UserDefinedcolValEntity != null).Select(e => new UserDefinedcolValEntity(e.UserDefinedcolValEntity)).ToList();

				//TODO: Add characteristics to each lineitem created
				return result;
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("resolvecalculationlineitem")]
		public AssemblyLineItemCharacteristcData ResolveCalculationLineItem(EstLineItemResolveAssembliesData data)
		{
			try
			{
				var result = new AssemblyLineItemCharacteristcData();

				var resolveResult = Logic.ResolveCalculationLineItem(data);

				if (resolveResult != null)
				{
					result.lineItemsUpdated = resolveResult.lineItemsUpdated.Select(e => new EstLineItemDto(e));
					result.UserDefinedcolsOfLineItemModified = resolveResult.UserDefinedcolsOfLineItemModified;
					result.UserDefinedcolsOfResourceModified = resolveResult.UserDefinedcolsOfResourceModified;
				}

				return result;
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		///
		/// </summary>
		public class ResolveEstLineItem2MdlObjectData
		{
			/// <summary/>
			public IEnumerable<EstLineItemDto> lineItems { get; set; }

			/// <summary>
			///
			/// </summary>
			public IEnumerable<IEstLineItem2MdlObjectEntity> EstLineItem2MdlObjects { get; set; }
		}

		/// <summary>
		///
		/// </summary>
		public class AssemblyLineItemCharacteristcData
		{
			/// <summary/>
			public IEnumerable<EstLineItemDto> lineItemsUpdated { get; set; }

			/// <summary>
			///
			/// </summary>
			public List<EstLineItemDto> LineItemsTotal { get; set; }

			/// <summary/>
			public IEnumerable<ICharacteristicDataEntity> resourcesCharacteristics { get; set; }

			/// <summary>
			///
			/// </summary>
			public IEnumerable<EstLineItem2EstRuleDto> EstLineItems2EstRules { get; set; }

			/// <summary/>
			public IEnumerable<EstLineItemDto> lineItemsNoNeedToUpdate { get; set; }

			/// <summary>
			///
			/// </summary>
			public IEnumerable<PrjEstRuleDto> EstPrjRules { get; set; }

			/// <summary>
			///
			/// </summary>
			public IEnumerable<IEstimateRuleCommonParamEntity> EstLineItemsParams { get; set; }

			/// <summary>
			///
			/// </summary>
			public IBoqItemEntity copyBoqItem { get; set; }

			/// <summary>
			///
			/// </summary>
			public IEnumerable<MainItem2CostGroupDto> LineItem2CostGroups { get; set; }

			/// <summary>
			///
			/// </summary>
			public IEnumerable<UserDefinedcolValEntity> UserDefinedcolsOfLineItemModified { get; set; }

			/// <summary>
			///
			/// </summary>
			public IEnumerable<UserDefinedcolValEntity> UserDefinedcolsOfResourceModified { get; set; }
		}

		/// <summary>
		/// Create a deep copy of given assemblies(copy resources, rules and parameters for the same)
		/// </summary>
		[HttpPost]
		[Route("deepcopyassembly")]
		public EstMainCompleteDto DeepCopyAssembly(EstMainCompleteDto data)
		{
			try
			{
				var updateEntity = data.Copy();

				Logic.DeepCopyAssembly(updateEntity);

				var estMainComplete = new EstMainCompleteDto(updateEntity);

				var dtos = estMainComplete.EstLineItems.ToList();
				var ids = dtos.Select(e => e.Id);
				var estHeaderId = dtos.FirstOrDefault().EstHeaderFk;

				var lineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => e.RootItemId == estHeaderId && ids.Contains(e.MainItemId.Value)).Select(e => new MainItem2CostGroupDto(e)).ToList();
				//if (filterRequest.FurtherFilters.Count() < 10)
				//{
				this.SetSortCodeValue(dtos);
				var costItemGroup = lineItem2CostGroups.Select(e => e.CostGroupCatFk).Distinct().ToList();
				if (costItemGroup.Count > 0)
				{
					foreach (var item in costItemGroup)
					{
						var list = lineItem2CostGroups.Where(e => e.CostGroupCatFk == item).Select(e => new { e.CostGroupFk }).Distinct().ToList();
						if (list.Count() != 1)
						{
							lineItem2CostGroups.RemoveAll(e => e.CostGroupCatFk == item);
						}
					}
				}
				//}

				estMainComplete.EstLineItem2CostGroups = lineItem2CostGroups;

				if (updateEntity.EstLineItems != null && updateEntity.EstLineItems.Any())
				{
					estMainComplete.UserDefinedcolsOfLineItemModified =
						updateEntity.EstLineItems.Where(e => e.UserDefinedcolValEntity != null).Select(e => new UserDefinedcolValEntity(e.UserDefinedcolValEntity)).ToList();
				}

				return estMainComplete;
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		/// Return all line items that have a valid project change assignment
		/// </summary>
		/// <param name="changeStatusFilterInfo"></param>
		/// <returns>list of line items with valid project change assignment</returns>
		[HttpPost]
		[Route("getprojectchangelineitems")]
		public IEnumerable<EstLineItemDto> GetProjectChangeLineItems(ChangeStatusFilterInfo changeStatusFilterInfo)
		{
			return Logic.GetProjectChangeLineItems(changeStatusFilterInfo.ProjectFk, changeStatusFilterInfo.EstHeaderFk, changeStatusFilterInfo.FilterByChangeStatus, changeStatusFilterInfo.ChangeStatusIds).Select(e => new EstLineItemDto(e)).ToList();
		}

		/// <summary>
		/// find all line items which are not yet assigned to an activity; create an schedule and activities assign activity to line item
		/// </summary>
		/// <param name="createScheduleDto">createScheduleDto</param>
		/// <returns>list of changed line items (activties are assigned to li now)</returns>
		[HttpPost]
		[Route("generateschedulefromestheader")]
		public int GenerateScheduleFromEstHeader(CreateScheduleDto createScheduleDto)
		{
			IEnumerable<IActivityEntity> activityEntities = Logic.FindLineItemsToGenerateSchedule(createScheduleDto.Copy());
			if (activityEntities.Any())
			{
				return activityEntities.Count();
			}
			else
			{
				return 0;
			}
		}

		/// <summary>
		/// is that any assignment lineitems by leading structure type
		/// </summary>
		/// <param name="estimateFilterData">Estimate Filter Data</param>
		/// <returns></returns>
		[HttpPost]
		[Route("isanyassignmentbystructure")]
		public EstimateMainAssignmentData IsAnyAssignmentByStructure(EstimateFilterData estimateFilterData)
		{
			return Logic.GetAssignmentByStructure(estimateFilterData);
		}

		/// <summary>
		/// Return all line items that belongs to a given project and a given estimate type
		/// </summary>
		/// <param name="projectFk">project id</param>
		/// <param name="estTypeFk">estimate type id</param>
		/// <returns>list of line items with valid project and estimate type</returns>
		[HttpGet]
		[Route("getitemsbyprojectandesttype")]
		public IEnumerable<RichEstLineItemDto> GetItemsByProjectAndEstType(int projectFk, int? estTypeFk)
		{
			return Logic.ByProjectAndEstTypeRich(projectFk, estTypeFk).Select(e => new RichEstLineItemDto(e.LineItem, e.DedicatedAssembly)).ToList();
		}

		/// <summary>
		/// Return all line items that belongs to a given project and a given estimate type
		/// </summary>
		/// <param name="projectFk">project id</param>
		/// <param name="estTypeFk">estimate type id</param>
		/// <returns>list of line items with valid project and estimate type</returns>
		[HttpGet]
		[Route("getitemsbyprojectandesttype_new")]
		public object GetItemsByProjectAndEstTypeNew(int projectFk, int? estTypeFk)
		{
			var dtos = Logic.ByProjectAndEstTypeRich(projectFk, estTypeFk).Select(e => new RichEstLineItemDto(e.LineItem, e.DedicatedAssembly)).ToList();

			var estHeaderIds = dtos.Select(e => e.LineItem.EstHeaderFk).ToArray().Distinct();
			var estHeaders = new EstimateMainHeaderLogic().GetListByFilter(e => estHeaderIds.Contains(e.Id)).ToList();
			Dictionary<int, object> roundingConfigs = new Dictionary<int, object>();
			Dictionary<int, bool> calcTotalWithWqs = new Dictionary<int, bool>();

			foreach (var item in estHeaders)
			{
				var columnConfig = new EstConfigLogic().GetById(new Platform.Core.IdentificationData() { Id = item.EstConfigFk });

				var estHeaderRoundingConfigDetails = columnConfig != null ? new EstimateRoundingHelper().GetRoundingConfigDetails(item.Id) : null;
				var estRoundingConfigDetails = new
				{
					RoundingConfigDetails = estHeaderRoundingConfigDetails,
					EstRoundingColumnIds = columnConfig != null ? new EstimateRoundingHelper().GetRoundingColumnIds() : null,
				};
				roundingConfigs.Add(item.Id, estRoundingConfigDetails);
				var typeEntity = item != null && item.EstTypeFk.HasValue ? (BasicsCustomizeEstimationTypeEntity)new BasicsCustomizeEstimationTypeLogic().GetById((int)item.EstTypeFk) : null;
				var isCalcTotalWithWq = typeEntity != null ? typeEntity.IsTotalWq : false;
				calcTotalWithWqs.Add(item.Id, isCalcTotalWithWq);

			}
			return new Dictionary<string, object>()
				 {
					{ "dtos", dtos },
					{ "EstRoundingConfigDetails", roundingConfigs },
					{ "CalcTotalWithWqs", calcTotalWithWqs }
				 };
		}


		/// <summary>
		/// Copy Resources to lineItem
		/// </summary>
		/// <param name="dto">estimate main complete dto</param>
		/// <returns>EstMainCompleteDto</returns>
		[HttpPost]
		[Route("copyresourcestolineitem")]
		public EstMainCompleteDto CopyResourcesToLineItem(EstMainCompleteDto dto)
		{
			try
			{
				var entity = dto.Copy();

				if (!entity.IsCopyResourcesTo)
				{
					if (entity.EstResourcesToCopy != null && entity.EstResourcesToCopy.Any() && entity.ToLineItemId > 0)
					{
						var toLineItem = entity.EstLineItems.FirstOrDefault(e => e.Id == entity.ToLineItemId);

						var fromLineItem = Logic.GetLineItemByFk(entity.EstResourcesToCopy.First().EstLineItemFk, entity.EstResourcesToCopy.First().EstHeaderFk);

						entity.EstLineItems = Logic.CopyorMoveResources(entity, fromLineItem, toLineItem);

						entity.UserDefinedcolsOfLineItemModified = entity.EstLineItems.Select(x => new UserDefinedcolValEntity(x.UserDefinedcolValEntity)).ToList();
					}
				}
				else
				{
					if (entity.EstResourcesToCopy != null && entity.EstResourcesToCopy.Any() && entity.ToLineItemId > 0)
					{
						var lineitem = entity.EstLineItems.FirstOrDefault(e => e.Id == entity.ToLineItemId);

						var estCopyOptionLogic = new EstimateCopyOptionLogic(lineitem.EstHeaderFk);
						var copyOptionEntity = estCopyOptionLogic.GetCopyOptionByEstHeaderId(lineitem.EstHeaderFk);

						IEnumerable<EstLineItemEntity> lineItems = copyOptionEntity.CopyResourcesTo == 0 ? Logic.GetList(lineitem.EstHeaderFk) :
							(IEnumerable<EstLineItemEntity>)Logic.GetLineItemByIds(entity.CopyResourcesToLineItemIds, lineitem.EstHeaderFk);

						var fromLineItem = Logic.GetLineItemByFk(entity.EstResourcesToCopy.First().EstLineItemFk, entity.EstResourcesToCopy.First().EstHeaderFk);

						if (entity.IsMoveOrCopyResource == "move")
						{
							lineItems = lineItems.Where(lineitem => lineitem.Id != fromLineItem.Id);
						}

						// TODO - DEV-28486, DEV-5575
						//if (dto.IsCopyFromLineItemResource){
						//	entity.EstLineItems = Logic.CopyorMoveResources(entity, lineItems.ToList(), fromLineItem, lineitem);
						//} else {

						ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };

						bool saveFromLineItem = false;
						ConcurrentBag<EstResourceEntity> resourceToDelete = new ConcurrentBag<EstResourceEntity>();
						var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

						Parallel.ForEach(lineItems, parallelOptions, toLineItem =>
						{
							Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;

							new EstimateMainResourceLogic().CopyResourcesToLineItem(entity.EstResourcesToCopy, fromLineItem, toLineItem, entity.EstParentResource, dto.ProjectId, entity.IsMoveOrCopyResource, entity.IsSubitemResource, entity.IsLookAtCopyOptions, true, resourceToDelete);

							new EstimateMainCalculator().CalculateLineItem(toLineItem, dto.ProjectId, true, true, true);

							if ((toLineItem.Id != fromLineItem.Id || toLineItem.EstHeaderFk != fromLineItem.EstHeaderFk) && dto.DoUpdate)
							{
								saveFromLineItem = true;
							}
						});

						if (resourceToDelete != null)
						{
							new EstimateMainResourceLogic().Delete(resourceToDelete);
						}

						if (saveFromLineItem)
						{
							new EstimateMainCalculator().CalculateLineItem(fromLineItem, dto.ProjectId, false, true, true);
						}

						entity.EstLineItems = lineItems;
						entity.EstLineItems = entity.EstLineItems.Concat(new[] { fromLineItem });

						//}
					}
				}

				return new EstMainCompleteDto(entity);
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, dto);
			}
		}

		/// <summary>
		/// export estimate and project boq data to bc3
		/// </summary>
		/// <param name="dto">input data</param>
		/// <returns></returns>
		[HttpPost]
		[Route("exportbc3")]
		public HttpResponseMessage ExportBc3(EstMainCompleteDto dto)
		{
			HttpResponseMessage response = null;
			var completeEntity = dto.Copy();
			var prestoBc3Logic = new PrestoBc3Logic();
			Bc3FileData bc3Data = prestoBc3Logic.Export(completeEntity.EstHeaderId, completeEntity.ProjectId.Value);

			var bc3DirName = "bc3";
			var physicalDirectoryPath = Path.Combine(Platform.Server.Common.AppSettingsReader.ReadString("fileserver:downloadsPath"), bc3DirName);

			if (!Directory.Exists(physicalDirectoryPath))
			{
				Directory.CreateDirectory(physicalDirectoryPath);
			}

			var url = RVPARB.BusinessEnvironment.ServerBaseUri.AbsoluteUri.TrimEnd('/') + "/downloads/" + bc3DirName;

			if (System.IO.File.Exists(bc3Data.Path))
			{
				string bc3FilePath = Path.Combine(physicalDirectoryPath, bc3Data.FileName + bc3Data.FileExtension);

				System.IO.File.Copy(bc3Data.Path, bc3FilePath, overwrite: true);

				response = Request.CreateResponse(HttpStatusCode.OK);

				response.Content = new StringContent(url + "/" + bc3Data.FileName + bc3Data.FileExtension, Encoding.UTF8);

				response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment")
				{
					FileName = Uri.EscapeDataString(bc3Data.FileName + bc3Data.FileExtension)
				};

			}
			else
			{
				response = Request.CreateResponse(HttpStatusCode.NotFound, bc3Data.Path);
			}

			return response;
		}

		/// <summary>
		/// GenerateBudget For given LineItems
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("generatebudget")]
		public IEnumerable<EstLineItemDto> GenerateBudget(EstGenerateBudgetData data)
		{
			try
			{
				return Logic.GenerateBudget(data).Select(e => new EstLineItemDto(e)).ToList();
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		/// Delete Procurement Package assignmnets for the given LineItems or Resources
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("deleteprcpackageassignment")]
		public void DeletePrcPackageAsignments(EstMainPrcPackageDeleteData data)
		{
			try
			{
				Logic.DeletePrcPackageAssignments(data);
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}


		/// <summary>
		/// Copy Resources to Assemblies
		/// </summary>
		/// <param name="dto">estimate main complete dto</param>
		/// <returns>EstMainCompleteDto</returns>
		[HttpPost]
		[Route("copyresourcestoassemblies")]
		public EstMainCompleteDto CopyResourcesToAssemblies(EstMainCompleteDto dto)
		{
			try
			{
				var entity = dto.Copy();

				if (entity.EstResourcesToCopy != null && entity.EstResourcesToCopy.Any() && entity.ToLineItemId > 0)
				{
					
					var fromLineItem = Logic.GetLineItemByFk(entity.EstResourcesToCopy.First().EstLineItemFk, entity.EstResourcesToCopy.First().EstHeaderFk);

					var toLineItem = entity.EstLineItems.FirstOrDefault(e => e.Id == entity.ToLineItemId);
					ConcurrentBag<EstResourceEntity> resourceToDelete = new ConcurrentBag<EstResourceEntity>();
					var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
					if (toLineItem != null)
					{
						Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;

						new EstimateMainResourceLogic().CopyResourcesToLineItem(entity.EstResourcesToCopy, fromLineItem, toLineItem, entity.EstParentResource, dto.ProjectId, entity.IsMoveOrCopyResource, entity.IsSubitemResource, entity.IsLookAtCopyOptions, true, resourceToDelete);

						new EstimateMainCalculator().CalculateLineItem(toLineItem, dto.ProjectId, true, true, true);

					}

					entity.EstLineItems = entity.EstLineItems.Concat(new[] { fromLineItem });
				}

				return new EstMainCompleteDto(entity);
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, dto);
			}
		}

		/// <summary>
		/// Remove Procurement Package from the given LineItems or Resources
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("removeprcpackage")]
		public Dictionary<string, object> RemovePrcPackage(EstMainPrcPackageDeleteData data)
		{
			try
			{
				Logic.RemovePrcPackage(data);
				return new Dictionary<string, object>()
						 {
								{ "EstLineItems", data.EstLineItems }
						 };
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}
		/// <summary>
		/// Create a deep copy of given line Items(copy resources, model objects, rules and parameters for the same)
		/// </summary>
		/// <param name="itemData"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("splititemforactivity")]
		public IEnumerable<EstLineItemDto> SplitItemForActivity(GetEstLineItemSplitDataDto itemData)
		{
			try
			{
				Logic.SplitLineItemsForActivity(itemData.EstHeaderFk, itemData.PrjProjectFk, itemData.LineItems);
				List<BusinessComponents.EstLineItemEntity> splitAll = new List<BusinessComponents.EstLineItemEntity>();

				foreach (var lineItem in itemData.LineItems)
				{
					List<BusinessComponents.EstLineItemEntity> lineItems = new List<BusinessComponents.EstLineItemEntity>();
					List<BusinessComponents.EstLineItemEntity> splitLineItems = new List<BusinessComponents.EstLineItemEntity>();
					lineItems.Add(lineItem);
					splitLineItems.Add(itemData.SplitLineItems.Where(e => e.Id == lineItem.Id).FirstOrDefault());
					new EstimateMainSplitter(itemData.PrjProjectFk, itemData.EstHeaderFk, lineItems).ExecuteSplitByPercentAndQuantity(splitLineItems, itemData.SplitMethod);
				}
				return itemData.LineItems.Select(e => new EstLineItemDto(e));
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, itemData);
			}
		}
		/// <summary>
		/// ProcessEntities called from bulkEditor in basics.common
		/// </summary>
		/// <param name="entitiesToProcess"></param>
		/// <param name="changeRequestList"></param>
		/// <returns></returns>
		IEnumerable<IEnumerable<IBulkInformationData>> IBulkChangeSupport.ProcessEntities(IEnumerable<dynamic> entitiesToProcess, IEnumerable<IEnumerable<IBulkInformationData>> changeRequestList)
		{
			var dto = (from entity in entitiesToProcess select entity.ToString() into s select JsonConvert.DeserializeObject<EstLineItemDto>(s) into a select (EstLineItemDto)a).ToList();
			var entities = dto.Select(e => e.Copy()).ToList();
			try
			{
				new LineItemBulkChangeSupport(false, false, entities.First().ProjectFk).ProcessBulkEntities(entities, changeRequestList);
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, dto);
			}
			return changeRequestList;
		}

		/// <summary>
		/// Delete Rule Assignments from the LineItems or Entire Estimates
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("deleteruleassignments")]
		public void DeleteRuleAssignments(EstMainRuleAssignDeleteData data)
		{
			new RemoveRulesLogic().DeleteRuleAssignments(data);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("deleteParamByPrjRule")]
		public object DeleteParamByPrjRule(EstMainRuleAssignDeleteData data)
		{
			return Logic.DeleteParamByPrjRule(data);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItemId"></param>
		/// <param name="estHeaderId"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getcostgroupassigment")]
		public IEnumerable<MainItem2CostGroupDto> GetCostGroupAssigment(int lineItemId, int estHeaderId)
		{
			return new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP")
					  .GetByFilter(e => e.RootItemId == estHeaderId && e.MainItemId == lineItemId)
					  .Select(e => new MainItem2CostGroupDto(e)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("listcostgroup")]
		public object GetCostGroup(RIB.Visual.Platform.Core.IdentificationData data)
		{
			var projectId = data.PKey1.GetValueOrDefault();
			var mainItemId = data.PKey2.GetValueOrDefault();// line item id
			var rootItemId = data.PKey3.GetValueOrDefault(); //estimate header id
			var object2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => mainItemId == e.MainItemId).Select(e => new MainItem2CostGroupDto(e)).ToList();
			var costGroupCats = new CostGroupCompleteEntityDto(new CostGroupCatLogic().GetCostGroupCatsByModule(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Estimate, projectId));
			return new
			{
				LineItem2CostGroups = object2CostGroups,
				CostGroupCats = costGroupCats,
				MainItemId = mainItemId,
				RootItemId = rootItemId
			};
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="info"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("getlineitemparameter")]
		public object GetLineItemParameters(GetLineItemParameterInfo info)
		{
			if (info != null && info.Dto != null)
			{
				if (!info.IsVistorResult.HasValue || !info.IsVistorResult.Value)
				{
					var list = Logic.GetLineItemParameters(info.Dto.Copy(), info.ParamLevel);
					return new
					{
						parameters = list,
						structureDetails = list.Any(x => x.AssignedStructureId != 1001 && x.AssignedStructureId != 1011 && x.AssignedStructureId != 1010) ? EstMainConfigLogic.GetStructureDetailByHeaderId(info.Dto.EstHeaderFk) : null
					};
				}

				var estParameterVistor = new EstParameterVisitor(info.Dto.EstHeaderFk, info.Dto.ProjectFk, false).RegisterParameterProviderByConfig();

				return estParameterVistor.GetParametersByLineItem(info.Dto.Copy());
			}

			return null;
		}

		/// <summary>
		/// Get the Est Line Item Status
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("getestlineitemstatus")]
		public Dictionary<int, bool> GetEstLineItemStatus()
		{
			return Logic.GetEstLineItemStatus();
		}

		/// <summary>
		/// Get Line Item List by Estimate Headers
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("getlistbyestheaders")]
		public IEnumerable<EstLineItemDto> GetListByEstHeaders(IEnumerable<int> headerIds)
		{
			var result = new List<EstLineItemDto>();
			if (headerIds == null || headerIds.Count() == 0)
			{
				return result;
			}

			foreach (int headerId in headerIds)
			{
				result.AddRange(Logic.GetList(headerId).Select(e => new EstLineItemDto(e)).ToList());
			}
			return result;
		}

		/// <summary>
		/// Get Estimate Info For Publishint Line Item Budget To iTWOCx
		/// </summary>
		/// <returns></returns>
		[HttpPost]
		[Route("getestimateinfoforpublishbudgettocx")]
		public object GetEstimateInfoForPublishBudgetToCx(EstHeadersLineItemsData data)
		{
			var list = new List<EstLineItemDto>();

			if (data.EstHeaderFk > 0 && data.ReturnLineItemList)
			{
				var filterInfo = new FilterResponse<int>();
				int projectId = -1, estHeaderId = -1;
				list.AddRange(Logic.GetList(data.filterRequest, ref filterInfo, ref projectId, ref estHeaderId).Where(x => x.MdcControllingUnitFk.HasValue).Select(e => new EstLineItemDto(e)).ToList());
			}

			var estHeader = data.EstHeaderFk > 0 ? new EstimateMainHeaderLogic().Get(data.EstHeaderFk) : null;


			var controllingUnits = RVPARB.BusinessEnvironment.GetExportedValue<IControllingUnitLogic>().GetListByProjectId(data.ProjectId).ToList();
			foreach (var controllingUnitEntity in controllingUnits)
			{
				controllingUnitEntity.CtuChildren = null;
			}

			return new
			{
				LineItems = list,
				EstHeaderCode = estHeader != null ? estHeader.Code : "",
				ControllingUnits = controllingUnits.Select(x => new SimpleControllingUnit
				{
					Code = x.Code,
					DescriptionInfo = x.DescriptionInfo,
					ControllingUnitFk = x.ControllingUnitFk,
					Id = x.Id
				})
			};
		}

		/// <summary>
		///
		/// </summary>
		public class SimpleControllingUnit
		{
			/// <summary/>
			public string Code { get; set; }

			/// <summary/>
			public DescriptionTranslateType DescriptionInfo { get; set; }

			/// <summary/>
			public int? ControllingUnitFk { get; set; }

			/// <summary/>
			public int Id { get; set; }
		}

		/// <summary/>
		public class EstHeadersLineItemsData
		{
			/// <summary/>
			public int EstHeaderFk { get; set; }

			/// <summary>
			///
			/// </summary>
			public int ProjectId { get; set; }

			/// <summary>
			///
			/// </summary>
			public bool ReturnLineItemList { get; set; }

			/// <summary>
			///
			/// </summary>
			public FilterRequest<Int32> filterRequest { get; set; }

		}

		/// <summary>
		/// Returns list of line items.
		/// </summary>
		/// <param name="filterRequest">filter to specify line items</param>
		/// <returns>list of line items</returns>
		[Route("getsourcelineitems")]
		[HttpPost]
		public IDictionary<string, object> GetSourceLineItems([FromBody] FilterRequest<Int32> filterRequest)
		{
			Stopwatch swAll = new Stopwatch();
			Stopwatch w1 = new Stopwatch();
			StringBuilder timeStr = new StringBuilder();
			var filterInfo = new FilterResponse<int>();

			swAll.Start();
			var execInfo = new FilterExecutionInfo<int>(filterRequest, filterInfo);
			execInfo.CreateHint("Start fetching of data by '" + filterRequest.Pattern + "'");

			// returns all entities and converts it to a dto list
			int projectId = -1, estHeaderId = -1;

			w1.Start();
			var entities = Logic.GetList(filterRequest, ref filterInfo, ref projectId, ref estHeaderId);
			w1.Stop();
			timeStr.AppendLine("function to get the LineItemEntities:--------->" + w1.ElapsedMilliseconds + " ms");


			var dtos = entities != null ? entities.Select(e => new EstLineItemDto(e)).ToList() : null;
			execInfo.CreateHint("completed data fetching ...");

			if (dtos != null)
			{
				var estProjectLogic = RVPARB.BusinessEnvironment.GetExportedValue<IEstimateCompositeLogic>();
				var estPrjFactory = RVPARB.BusinessEnvironment.GetExportedValue<IDtoEntityMapper>("Estimate.Project.EstimateCompositeDtoMapper");

				var prjLogic = RVPARB.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
				var factory = RVPARB.BusinessEnvironment.GetExportedValue<IProjectTypeFactory>();

				w1.Restart();
				// if estHeaderId is still -1, then request was not definite (-> more than one estimate headers and/or projects available)
				var prjEstComposites = (estHeaderId >= 0) ? new List<IIdentifyable> { estProjectLogic.GetCompositeByEstHeader(estHeaderId) } : estProjectLogic.GetEstPrjList(new[] { projectId });

				var prjEntity = projectId != -1 ? prjLogic.GetProjectById(projectId) : null;

				w1.Stop();
				timeStr.AppendLine("function GetProjectById:--------->" + w1.ElapsedMilliseconds + " ms");

				SetSplitQuantitiesValue(estHeaderId, dtos);

				//get assembly templates info
				var assemblyIds = dtos.Where(e => e.EstAssemblyFk.HasValue).Select(e => e.EstAssemblyFk.Value).Distinct().ToList();
				var assemblies = Logic.GetAssembliesByIds(assemblyIds).Select(e => new EstLineItemDto(e));


				w1.Restart();
				/* Get the Cost Group Of LineItem */
				var lineItemIds = entities.Select(x => x.Id).ToList();
				var lineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => e.RootItemId == estHeaderId && lineItemIds.Contains(e.MainItemId.Value)).Select(e => new MainItem2CostGroupDto(e)).ToList();
				w1.Stop();
				timeStr.AppendLine("function to get the Cost Group Of LineItem:--------->" + w1.ElapsedMilliseconds + " ms");


				w1.Restart();
				var costGroupCats = new CostGroupCompleteEntityDto(new CostGroupCatLogic().GetCostGroupCatsByModuleBase(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Estimate, prjEntity));
				w1.Stop();
				timeStr.AppendLine("function to get the costGroupCats:--------->" + w1.ElapsedMilliseconds + " ms");

				swAll.Stop();
				timeStr.AppendLine("the total time of the function listfiltered_new  is :--------->" + swAll.ElapsedMilliseconds + " ms");

				IEnumerable<UserDefinedcolValEntity> LineItemUDPs;
				var udpReadData = dtos.Select(e => new Tuple<int, int, int>((int)userDefinedColumnTableIds.EstimateLineItem, estHeaderId, e.Id));
				LineItemUDPs = new UserDefinedColumnValueLogic().GetListByKeys(udpReadData);

				var estHeader = new EstimateMainHeaderLogic().GetItemById(estHeaderId);
				var columnConfig = estHeader != null ? new EstConfigLogic().GetById(new Platform.Core.IdentificationData() { Id = estHeader.EstConfigFk }) : null;
				var isEstDynamicColumnActive = columnConfig != null && columnConfig.IsColumnConfig;

				//Estimate column config and line item characteristic data
				//1. Est Dynamic columns only if EstConfig is activated
				var columnConfigDetails = new List<EstColumnConfigDetailEntity>();
				var extColumnConfigDetails = new Dictionary<int, Dictionary<string, ExtendColumnValue>>();

				if (isEstDynamicColumnActive)
				{
					//Get Estimate config details
					var estLineItemColumnConfigLogic = new EstLineItemColumnConfigLogic(estHeader, prjEntity);
					extColumnConfigDetails = estLineItemColumnConfigLogic.GetExtendColumns(entities);
					columnConfigDetails = estLineItemColumnConfigLogic.GetEstColumnConfigDetails().ToList();
				}

				var dynamicColumns = new
				{
					DynamicColumns = new ColumnIdLogic().getAllColumnItems(),
					ColumnConfigDetails = columnConfigDetails.Any() ? columnConfigDetails.Select(e => new EstColumnConfigDetailDto(e)).ToList() : null,
					liUDPs = LineItemUDPs
				};

				return new Dictionary<string, object>()
				 {
					{ "FilterResult", filterInfo },
					{ "dtos", dtos },
					{ "prjEstComposites", prjEstComposites != null ? prjEstComposites.Select(estPrjFactory.CreateFrom).ToList() : null },
					{ "selectedPrj", prjEntity != null ? factory.CreateFrom(prjEntity) : null },
					{ "companyMdcContextFk", EstimateContext.MasterDataContextId },
					{ "LookupAssemblies", assemblies },
					{ "LineItem2CostGroups", lineItem2CostGroups },
					{ "CostGroupCats", costGroupCats },
					{ "dynamicColumns", dynamicColumns },
				 };
			}

			return new Dictionary<string, object>()
			  {
				  { "FilterResult", filterInfo },
				  { "dtos", dtos }
			  };
		}

		#region CostX Benchamark

		/// <summary>
		///
		/// </summary>
		[HttpGet]
		[Route("getestimatesfromcxbm")]
		public IEnumerable<EstCostXBenchmarkDto> GetEstimatesFromCXBM(int externalConfigId)
		{
			var estimateDtos = new EstGenerateBudgetFromCXBMLogic().GetEstimatesFromCXBM(externalConfigId).Select(e => new EstCostXBenchmarkDto(e)).ToList();
			return estimateDtos;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("getextersource2costxbm")]
		public object IsConfiguser2CostXBm()
		{
			return new EstGenerateBudgetFromCXBMLogic().GetExternalSource2CostXBm();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("uploadfiletobenchmark")]
		public bool UploadFileToBenchmark(UploadData data)
		{
			var isSuccess = new EstCostXBenchmarkUploadLogic(data.ExternalConfigId).UploadFileToBenchmark(data.ProjectFk, data.EstimateId, data.SourceDescData);
			return isSuccess;
		}

		/// <summary>
		///
		/// </summary>
		public class UploadData
		{
			/// <summary>
			///
			/// </summary>
			public int ProjectFk { get; set; }

			/// <summary>
			///
			/// </summary>
			public int EstimateId { get; set; }

			/// <summary>
			///
			/// </summary>
			public int ExternalConfigId { get; set; }

			/// <summary>
			///
			/// </summary>
			public IEnumerable<SourceDescEntity> SourceDescData { get; set; }
		}

		#endregion

		/// <summary>
		/// Map LineItem to Activity by AI
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("mtwoai/lineitem2activitymapping")]
		[Permission(Permissions.Execute, AIActivityMappingPermissionDescriptor)]
		public Dictionary<string, object> lineitem2ActivityMapping(EstLineItemInputDto data)
		{
			bool hasLocation = false, hasBoq = false;

			if (data.ModelFeature != null)
			{
				hasLocation = data.ModelFeature.HasLocation;
				hasBoq = data.ModelFeature.HasBoq;
			}

			IEnumerable<BusinessComponents.EstLineItemEntity> mainEntities = Logic.AutoMapLineitemActivity(data.EstHeaderFk, data.ProjectFk, hasLocation, hasBoq);
			//No SchedulingActivity found in current project
			if (mainEntities == null)
			{
				Dictionary<string, object> exceptionResult = new Dictionary<string, object>();
				exceptionResult["SchedulingActivity"] = null;
				exceptionResult["HasSchedulingActivity"] = false;
				exceptionResult["Main"] = null;
				exceptionResult["IsAllLineitemDescriptionEmpty"] = false;
				return exceptionResult;
			}

			//No lineitems found in current project
			if (mainEntities.Count() == 0)
			{
				Dictionary<string, object> exceptionResult = new Dictionary<string, object>();
				exceptionResult["SchedulingActivity"] = null;
				exceptionResult["HasSchedulingActivity"] = true;
				exceptionResult["Main"] = null;
				exceptionResult["IsAllLineitemDescriptionEmpty"] = true;
				return exceptionResult;
			}

			var dtos = mainEntities.ToDtos(e => new EstLineItem2ActivityMappingDto(e));

			foreach (EstLineItem2ActivityMappingDto dto in dtos)
			{
				dto.IsCheckAi = false;
				dto.OrigPsdActivityFk = dto.PsdActivityFk;
				if (dto.MatchedActivityFks != null && dto.MatchedActivityFks.Count > 0 && dto.MatchedActivityFks[0] != dto.OrigPsdActivityFk)
				{
					// AI recommend another different Boqitem
					dto.PsdActivityFk = dto.MatchedActivityFks[0];
					dto.IsCheckAi = true;
				}
				else
				{
					dto.IsCheckAi = false;
				}
			}

			dtos = dtos.OrderByDescending(e => e.IsCheckAi).ThenBy(e => e.Code);
			var result = CollectActivityItemLookups(dtos);
			result["IsAllLineitemDescriptionEmpty"] = false;
			result["HasSchedulingActivity"] = true;

			result["Main"] = dtos;
			return result;
		}

		/// <summary>
		/// Collect user feedback AI suggestion for LineItem to Activity mapping
		/// </summary>
		/// <param name="dtos"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("mtwoai/aimapactivityfeedback")]
		public void lineitem2BoqFeedback(List<EstLineItem2ActivityMappingDto> dtos)
		{
			try
			{
				if (!AIConfigHelper.AICollectFeedback())
				{
					return;
				}
				if (!Permission.Has(AIActivityMappingPermissionDescriptor, Permissions.Execute))
				{
					// No permission
					return;
				}
				if (dtos.Count > 0)
				{
					var feedbacks = dtos.ConvertAll(x => x.ToFeedbackEntity());
					Logic.AutoMapLineitemActivityFeedback(dtos[0].EstHeaderFk, feedbacks);
				}
			}
			catch (Exception /*e*/)
			{
				// do nothing. Feedback collection is "best-effort".
			}
		}

		/// <summary>
		/// Check for Reference LineItems for the given LineItems
		/// </summary>
		/// <param name="dtos"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("hasreferencelineitems")]
		public bool HasReferenceLineItems(IEnumerable<EstLineItemDto> dtos)
		{
			var lineItems = dtos.Select(e => e.Copy());
			return Logic.HasReferenceLineItems(lineItems);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="estHeaderId"></param>
		/// <param name="filterByType"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("masterCCandProjectCCChildOnly")]
		public IEnumerable<CostCodeDto> GetMasterAndProjectCostCodeChildOnly(int projectId, int estHeaderId, bool filterByType = true)
		{
			CostCodeConfigRequest costCodeConfigRequest = new CostCodeConfigRequest()
			{
				ProjectId = projectId,
				EstHeaderId = estHeaderId,
				FilterByType = filterByType

			};
			var costCodes = this.Logic.GetMasterCostCodeAndProjectCostCodeChildOnly(costCodeConfigRequest);
			return costCodes.Select(e => new CostCodeDto(e)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("masterCCandProjectCCChildOnlyForConfig")]
		public IEnumerable<CostCodeDto> GetMasterAndProjectCostCodeChildOnlyForConfig(CostCodeConfigRequest data)
		{
			var costCodes = this.Logic.GetMasterCostCodeAndProjectCostCodeChildOnly(data);
			return costCodes.Select(e => new CostCodeDto(e)).ToList();
		}

		/// <summary>
		/// Map LineItem to Cost Group by AI
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("mtwoai/lineitem2costgroupmapping")]
		[Permission(Permissions.Execute, AICostGroupMappingPermissionDescriptor)]
		public IDictionary<string, object> lineitem2CostGroupMapping(EstLineItemInputDto data)
		{
			int projectId = data.ProjectFk;
			int estHeaderFk = data.EstHeaderFk;

			//Get line-items by estHeaderId
			var entities = this.Logic.getLineItemLookupList(estHeaderFk, -1, string.Empty).ToList();

			var dtos = entities != null ? entities.Select(e => new EstLineItem2CostGroupMappingDto(e)).ToList() : null;
			EstimateMainLineItemLogic estimateMainLineItemLogic = new EstimateMainLineItemLogic();


			if (dtos != null)
			{
				var prjLogic = RVPARB.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
				var prjEntity = projectId != -1 ? prjLogic.GetProjectById(projectId) : null;

				var costGroupCats = new CostGroupCompleteEntityDto(new CostGroupCatLogic().GetCostGroupCatsByModuleBase(CostGroupCatalogConfigOpt.Project, CostGroupCatalogAssignConfigOpt.Estimate, prjEntity));
				List<CostGroupCatDto> allCostGroupCats = new List<CostGroupCatDto>();
				if (costGroupCats != null)
				{
					if (data.SelectLevel == "1")
					{
						costGroupCats.LicCostGroupCats = null;
					}
					else
					{
						costGroupCats.PrjCostGroupCats = null;
					}

					if (costGroupCats.LicCostGroupCats != null && costGroupCats.LicCostGroupCats.Count() >= 1 && data.SelectLevel != "1")
					{
						allCostGroupCats.AddRange(costGroupCats.LicCostGroupCats);
					}

					if (costGroupCats.PrjCostGroupCats != null && costGroupCats.PrjCostGroupCats.Count() >= 1 && data.SelectLevel == "1")
					{
						allCostGroupCats.AddRange(costGroupCats.PrjCostGroupCats);
					}

					if (allCostGroupCats.Count() < 1) // Haven't cost group catalog after filter by select level
					{
						Dictionary<string, object> result1 = new Dictionary<string, object>();
						result1["CostGroup"] = null;
						result1["dtos"] = dtos;
						result1["LineItem2CostGroups"] = null;
						result1["CostGroupCats"] = null;
						return result1;
					}
				}

				var lineItemIds = entities.Select(x => x.Id).ToList();
				var costGroupCatIds = allCostGroupCats.Select(e => e.Id);
				var lineItem2CostGroups = new MainItem2CostGroupLogic("EST_LINE_ITEM2COSTGRP").GetByFilter(e => e.RootItemId == estHeaderFk && lineItemIds.Contains(e.MainItemId.Value) && costGroupCatIds.Contains(e.CostGroupCatFk)).Select(e => new MainItem2CostGroupDto(e)).ToList();

				List<MainItem2CostGroupDto> mainItem2CostGroupDtos = new List<MainItem2CostGroupDto>();

				List<EstLineItemSuggestedCostGroupEntity> estLineItemSuggestedCostGroupEntities = Logic.AutoMapLineitemCostGroup(estHeaderFk, projectId, data.SelectLevel);
				if (estLineItemSuggestedCostGroupEntities != null && estLineItemSuggestedCostGroupEntities.Count() >= 1)
				{
					foreach (var suggestedEntity in estLineItemSuggestedCostGroupEntities)
					{
						var estimateMainLineItem = estimateMainLineItemLogic.GetItemById(suggestedEntity.EstLineItemFk);
						if (estimateMainLineItem == null)
						{
							continue;
						}

						var filterLineItem2CostGroups = lineItem2CostGroups.Where(e => e.CostGroupCatFk == suggestedEntity.CostGroupCatFk && e.MainItemId == suggestedEntity.EstLineItemFk).ToList();
						if (filterLineItem2CostGroups != null && filterLineItem2CostGroups.Count() >= 1)
						{
							var filterLineItem2CostGroup = filterLineItem2CostGroups.First();

							if (filterLineItem2CostGroup != null)
							{
								//filterLineItem2CostGroup.SuggestedCostGroupFk = filterLineItem2CostGroup.CostGroupFk;

								if (suggestedEntity.MatchedCostGroupFks != null && suggestedEntity.MatchedCostGroupFks.Count > 0 && suggestedEntity.MatchedCostGroupFks[0] != filterLineItem2CostGroup.CostGroupFk)
								{
									// AI recommend another different Cost Group
									filterLineItem2CostGroup.SuggestedCostGroupFk = suggestedEntity.MatchedCostGroupFks[0];
									filterLineItem2CostGroup.MatchedCostGroupFks = suggestedEntity.MatchedCostGroupFks;

									var lineItemDtos = dtos.Where(e => e.Id == filterLineItem2CostGroup.MainItemId);
									if (lineItemDtos != null && lineItemDtos.Count() >= 1)
									{
										var lineItemDto = lineItemDtos.First();
										lineItemDto.IsCheckAi = true;
									}

								}
							}
						}
						else
						{
							MainItem2CostGroupDto dto = new MainItem2CostGroupDto();
							dto.Code = estimateMainLineItem.Code;
							dto.RootItemId = estHeaderFk;
							dto.MainItemId = estimateMainLineItem.Id;
							dto.CostGroupCatFk = suggestedEntity.CostGroupCatFk;


							if (suggestedEntity.MatchedCostGroupFks != null && suggestedEntity.MatchedCostGroupFks.Count() >= 1)
							{
								dto.SuggestedCostGroupFk = suggestedEntity.MatchedCostGroupFks[0];
								dto.MatchedCostGroupFks = suggestedEntity.MatchedCostGroupFks;
							}
							else
							{
								dto.SuggestedCostGroupFk = null;
								dto.MatchedCostGroupFks = null;
							}

							var lineItemDtos = dtos.Where(e => e.Id == suggestedEntity.EstLineItemFk);
							if (lineItemDtos != null && lineItemDtos.Count() >= 1)
							{
								var lineItemDto = lineItemDtos.First();
								lineItemDto.IsCheckAi = true;
							}

							mainItem2CostGroupDtos.Add(dto);
						}
					}
				}

				if (mainItem2CostGroupDtos != null && mainItem2CostGroupDtos.Count() >= 1)
				{
					lineItem2CostGroups.AddRange(mainItem2CostGroupDtos);
				}

				Dictionary<string, object> result = new Dictionary<string, object>();

				var lookupResult = CollectCostGroupItemLookups(lineItem2CostGroups);
				if (lookupResult.ContainsKey("CostGroup"))
				{
					result["CostGroup"] = lookupResult["CostGroup"];
				}

				result["dtos"] = dtos;
				result["LineItem2CostGroups"] = lineItem2CostGroups;
				result["CostGroupCats"] = costGroupCats;

				return result;
			}

			return null;
		}

		/// <summary>
		/// Update AI suggestion for LineItem to Cost Group mapping
		/// </summary>
		/// <param name="items"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("mtwoai/aimapcostgroupfeedback")]
		public void lineitem2costgroupFeedback(List<EstLineItem2CostGroupMappingFeedbackDto> items)
		{
			try
			{
				if (items.Count > 0)
				{
					var feedbacks = items.ConvertAll(x => x.ToFeedbackEntity());
					Logic.UpdateLineitemCostGroupFeedBackByAI(feedbacks);
				}
			}
			catch (Exception e)
			{
				throw new BusinessLayerException(e.Message, e);
			}
		}

		/// <summary>
		/// Get Assembly Resources detail by given filter
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("getassemblywithresourcesdetail")]
		public IEnumerable<EstLineItemDto> GetAssemblyWithResourcesDetail(AssemblyPostData data)
		{
			var result = Logic.GetAssemblyWithResourcesDetail(data.AssemblyIds, data.EstHeaderId);

			if (result == null)
			{
				return new List<EstLineItemDto>();
			}

			return result.Select(e => new EstLineItemDto(e)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		/// <exception cref="HttpResponseException"></exception>
		[Route("importassemblies")]
		[HttpPost]
		public async Task<HttpResponseMessage> ImportAssemblies()
		{
			if (!HttpContext.GetHttpRequestMessage().Content.IsMimeMultipartContent())
			{
				throw new HttpResponseException(HttpStatusCode.UnsupportedMediaType);
			}
			var uploadFolder = PlatformServerCommon.AppSettingsReader.ReadString("fileserver:uploadsPath");

			var context = BusinessApplication.BusinessEnvironment.CurrentContext;

			var provider = new MultipartFormDataStreamProvider(uploadFolder);

			var result = await HttpContext.GetHttpRequestMessage().Content.ReadAsMultipartAsync(provider);

			var formData = this.ConvertToCustomFormData(result.FormData);

			object reulstObj = null;

			if (provider.FileData.Count > 0)
			{
				var fileData = provider.FileData[0];

				// start import the file here
				if (fileData.Headers.ContentDisposition.FileName.EndsWith(".xml\""))
				{
					reulstObj = new EstimateAssembliesXmlExchangeLogic(fileData.LocalFileName, formData).Run();
				}

				System.IO.File.Delete(fileData.LocalFileName);
			}

			return Request.CreateResponse(HttpStatusCode.OK, reulstObj);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="formData"></param>
		/// <returns></returns>
		private ImportAssembliesRequestFormData ConvertToCustomFormData(NameValueCollection formData)
		{
			ImportAssembliesRequestFormData newFromData = new ImportAssembliesRequestFormData();

			if (formData["ProjectId"] != null && Int32.TryParse(formData["ProjectId"], out Int32 ProjectId))
			{
				newFromData.ProjectId = ProjectId;
			}
			if (formData["IsSelectCategoryAsRoot"] != null && bool.TryParse(formData["IsSelectCategoryAsRoot"], out bool isSelectCategoryAsRoot))
			{
				newFromData.IsSelectCategoryAsRoot = isSelectCategoryAsRoot;
			}
			if (formData["IsOverride"] != null && bool.TryParse(formData["IsOverride"], out bool isOverride))
			{
				newFromData.IsOverride = isOverride;
			}
			if (formData["CategoryId"] != null && Int32.TryParse(formData["CategoryId"], out Int32 categoryId))
			{
				newFromData.CategoryId = categoryId;
			}
			if (formData["MaterialId"] != null && Int32.TryParse(formData["MaterialId"], out Int32 materialId))
			{
				newFromData.MaterialId = materialId;
			}
			return newFromData;
		}

		/// <summary>
		///
		/// </summary>
		public class AssemblyPostData
		{
			/// <summary>
			///
			/// </summary>
			public int? EstHeaderId { get; set; }

			/// <summary>
			///
			/// </summary>
			public List<int?> AssemblyIds { get; set; }
		}

		/// <summary>
		/// Update or Generate BaseCostUnit and BaseCostTotal
		/// </summary>
		/// <param name="data"></param>
		/// <returns>Estimate LineItems</returns>
		[HttpPost]
		[Route("updatelineitemsbasecost")]
		public IEnumerable<EstLineItemDto> UpdateLineItemsBaseCost(EstUpdateBaseCostData data)
		{
			try
			{
				return Logic.UpdateLineItemsBaseCost(data).Select(e => new EstLineItemDto(e)).ToList();
			}
			catch (Exception ex)
			{
				throw ConcurrencyExceptionHandle.ConverToHttpException(ex, data);
			}
		}

		/// <summary>
		/// Calculates Lead Quantities of LineItems by Leading Structure Filter
		/// </summary>
		/// <param name="filterRequest">filter to specify line items</param>
		/// <returns>calculated lead quantities</returns>
		[Route("calculateaggregatequantities")]
		[HttpPost]
		public IEnumerable<EstLineItemLeadQuantiyEntity> GetCalculatedAggregatedQuantities([FromBody] FilterRequest<Int32> filterRequest)
		{
			var filterInfo = new FilterResponse<int>();

			var execInfo = new FilterExecutionInfo<int>(filterRequest, filterInfo);
			execInfo.CreateHint("Start fetching of data by '" + filterRequest.Pattern + "'");

			// returns all entities and converts it to a dto list
			int projectId = -1, estHeaderId = -1;

			if (filterRequest.GroupingFilter != null)
			{
				filterRequest.GroupingFilter.Module = "estimate.main";
			}

			var entities = Logic.GetList(filterRequest, ref filterInfo, ref projectId, ref estHeaderId);

			var leadQuantityItems = new EstLineItemLeadQuantitiesHelper().CalculateLeadQuantities(entities, filterRequest, estHeaderId);

			return leadQuantityItems;
		}

		/// <summary>
		/// Dto used for GetCharacteristicResourceTypeByCCMaterialId
		/// </summary>
		public class CharacteristicEstResourceTypeDto
		{
			/// <summary>
			///
			/// </summary>
			public int CharacteristicSectionId { get; set; }

			/// <summary>
			///
			/// </summary>
			public int EstResourceTypeFk { get; set; }

			/// <summary>
			///
			/// </summary>
			public int Id { get; set; }

			/// <summary>
			///
			/// </summary>
			public int EstHeaderFk { get; set; }

			/// <summary>
			///
			/// </summary>
			public int EstLineItemFk { get; set; }

			/// <summary>
			///
			/// </summary>
			public int? MdcCostCodeFk { get; set; }

			/// <summary>
			///
			/// </summary>
			public int? MdcMaterialFk { get; set; }
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="resourceDto"></param>
		/// <returns></returns>
		[Route("getCharacteristicResourceTypeByCCMaterialId")]
		[HttpPost]
		public object GetCharacteristicResourceTypeByCCMaterialId(CharacteristicEstResourceTypeDto resourceDto)
		{
			return new EstCharacteristicLogic().GetCharacteristicResourceTypeByCCMaterialId(resourceDto.CharacteristicSectionId, resourceDto.EstResourceTypeFk, new IdentificationData() { Id = resourceDto.Id, PKey1 = resourceDto.EstHeaderFk, PKey2 = resourceDto.EstLineItemFk }, resourceDto.MdcCostCodeFk, resourceDto.MdcMaterialFk);
		}

		/// <summary>
		/// GetList
		/// </summary>
		/// <returns></returns>
		[Route("getLineItemByCondition"), HttpPost]
		public Dictionary<string, object> GetLineItemByCondition(ReadDataHelper data)
		{
			var result = new Dictionary<string, object>();
			var filterResult = new LineItemSearchResult();

			if (data.EstLineItems != null && data.EstLineItems.Any())
			{
				var headerIds = data.EstLineItems.Select(e => e.EstHeaderFk).ToList();
				var lineItemIds = data.EstLineItems.Select(e => e.Id).ToList();
				var dtos = Logic.Get(e => e.LineItemType == 0 && headerIds.Contains(e.EstHeaderFk) && lineItemIds.Contains(e.Id)).Select(e => new EstLineItemDto(e)).ToList();
				result["dtos"] = dtos;
			}
			else
			{
				var currentContext = BusinessApplication.BusinessEnvironment.CurrentContext;
				var clientId = currentContext != null ? currentContext.ClientId : -1;

				var companyInfoProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<ICompanyInfoProvider>();

				var mdcContextFk = clientId != -1 ? companyInfoProvider.GetMasterDataContext(clientId) : -1;

				var dtos = Logic.GetEstLineItems(data, mdcContextFk, ref filterResult).Select(e => new EstLineItemDto(e)).ToList();
				result["dtos"] = dtos;
			}

			result["filterResponse"] = filterResult;
			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		[Route("excutebackwardcalculation")]
		public object BackwardCalculationLineItemByWizard(BackwardCalcultionParam paramData)
		{
			BackwardCalculationLogic backwardCalculationLogic = new BackwardCalculationLogic();
			return backwardCalculationLogic.Execute(paramData);
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		[HttpGet]
		[Route("GetEquipmentAssemblyCostUnitAlwaysEditableFlag")]
		public bool GetEquipmentAssemblyCostUnitAlwaysEditableFlag()
		{
			return Logic.GetEquipmentAssemblyCostUnitAlwaysEditable();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("alllineitemassignsamechange")]
		public bool AllLineItemAssignedSameChange(LineItemChangeCheckDto dto)
		{
			IEnumerable<IEstLineItemEntity> allLineItems = null;
			if (dto.EstimateScope == 0)
			{
				allLineItems = Logic.GetList(dto.EstimateHeaderId, false);
			}
			else if (dto.EstimateScope == 1)
			{
				int projectId = -1;
				int estHeaderId = -1;

				allLineItems = Logic.GetList(dto.EstimateScope, dto.FilterRequest, ref projectId, ref estHeaderId);
			}

			if (allLineItems == null || !allLineItems.Any()) { return true; }
			allLineItems = allLineItems.Where(x => !x.IsTemp).ToList();

			if (dto.StructureType == (int)NewAssignedStructureType.Boq)
			{
				var boqHeaderIdsWithBoqFk = allLineItems
					 .Where(x => x.BoqHeaderFk.HasValue)
					 .Select(x => x.BoqHeaderFk.Value)
					 .Distinct()
					 .ToList();

				if (boqHeaderIdsWithBoqFk.Any())
				{
					var validBoqHeaderIds = new BoqHeaderLogic()
						 .getBoqHeadersByIds(boqHeaderIdsWithBoqFk)
						 .Where(x => !x.IsGCBoq)
						 .Select(x => x.Id)
						 .ToHashSet(); // Using HashSet for lookups

					if (validBoqHeaderIds.Any())
					{
						allLineItems = allLineItems
							 .Where(x => x.BoqHeaderFk.HasValue && validBoqHeaderIds.Contains(x.BoqHeaderFk.Value));
					}
				}
			}

			if (!allLineItems.Any()) { return true; }

			if (!dto.MajorLineItems)
			{
				allLineItems = allLineItems.Where(x => x.PrjChangeFk.HasValue).ToList();
			}
			if (dto.ProjectChangeOrders != null && dto.ProjectChangeOrders.Any())
			{
				allLineItems = allLineItems.Where(x => x.PrjChangeFk.HasValue && dto.ProjectChangeOrders.Contains(x.PrjChangeFk.Value)).ToList();
			}

			var changeIds = allLineItems.Select(x => x.PrjChangeFk).Distinct().ToList();

			return changeIds.Count == 1;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="estHeaderFk"></param>
		/// <param name="estLineItemFk"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("FilterOutMaterIdsByAssembly")]
		public IEnumerable<int> FilterOutMaterIdsByAssembly(int estHeaderFk, int estLineItemFk)
		{
			return new MasterAssemblyMaterialFilter().FilterOutMaterialIdsByAssembly(estHeaderFk, estLineItemFk);
		}
	}
}