import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _package, _procurementContractPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const COMMENT_TEXT = _common.generateRandomString(4);
let PROCUREMENT_CONTRACT_PARAMETER: DataCells

let CONTAINERS_CONTRACT,CONTAINERS_MANDATORY_DEADLINES;

let CONTAINER_COLUMNS_CONTRACT;

describe("PCM- 4.215 | Mandatory deadlines container in contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
	before(function () {
		cy.fixture("pcm/con-4.215-mandatory-deadlines -container-in-contract-module.json").then((data) => {
			this.data = data;
			CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
			CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
			CONTAINERS_MANDATORY_DEADLINES = this.data.CONTAINERS.MANDATORY_DEADLINES
			PROCUREMENT_CONTRACT_PARAMETER = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
			}
		}).then(() => {
			cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
		});
	})
	after(() => {
		cy.LOGOUT();
	});

	it("TC - Create new contract record", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
		//	_common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.projectfk, CONTAINER_COLUMNS_CONTRACT.ProjectFkProjectName, CONTAINER_COLUMNS_CONTRACT.code, CONTAINER_COLUMNS_CONTRACT.businesspartnerfk], cnt.uuid.PROCUREMENTCONTRACT)
		});
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	});

	it("TC - Verify create mandatory deadlines record & verify each field is editable", function () {
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
		});
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env("CONTRACT_CODE")).pinnedItem();
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CONTRACT_CODE"))
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.MANDATORY_DEADLINES, app.FooterTab.MANDATORY_DEADLINES, 2);
		});
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.MANDATORY_DEADLINES)
		_common.create_newRecord(cnt.uuid.MANDATORY_DEADLINES)
		_common.waitForLoaderToDisappear()
		_common.edit_containerCell(cnt.uuid.MANDATORY_DEADLINES, app.GridCells.INDIVIDUAL_PERFORMANCE, app.InputFields.DOMAIN_TYPE_COMMENT, COMMENT_TEXT)
		_common.edit_containerCell(cnt.uuid.MANDATORY_DEADLINES, app.GridCells.START, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
		_common.enterRecord_inNewRow(cnt.uuid.MANDATORY_DEADLINES, app.GridCells.END, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(commonLocators.CommonKeys.FETCHED_DATE_DECREMENT, CONTAINERS_MANDATORY_DEADLINES.DATE[0], _common.getDate(commonLocators.CommonKeys.CURRENT_SMALL)))
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_validate.validate_Text_message_In_PopUp(CONTAINERS_MANDATORY_DEADLINES.VALIDATION_MSG)
		_common.clickOn_modalFooterButton(btn.ButtonText.CANCEL)
		_common.waitForLoaderToDisappear()
		_common.edit_containerCell(cnt.uuid.MANDATORY_DEADLINES, app.GridCells.END, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.INCREMENTED, CONTAINERS_MANDATORY_DEADLINES.DATE[0]))
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_validate.verify_isRecordPresent(cnt.uuid.MANDATORY_DEADLINES, COMMENT_TEXT)
	});

	it("TC - Verify delete mandatory deadlines record & verify record is not present", function () {
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.MANDATORY_DEADLINES, app.FooterTab.MANDATORY_DEADLINES, 2);

		});
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.MANDATORY_DEADLINES)
		_common.select_rowHasValue(cnt.uuid.MANDATORY_DEADLINES,COMMENT_TEXT)
		_common.delete_recordFromContainer(cnt.uuid.MANDATORY_DEADLINES)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
			_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		});
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.MANDATORY_DEADLINES, app.FooterTab.MANDATORY_DEADLINES, 2);
			_common.clear_subContainerFilter(cnt.uuid.MANDATORY_DEADLINES)
		});
		_common.waitForLoaderToDisappear()
		_validate.verify_recordNotPresentInContainer(cnt.uuid.MANDATORY_DEADLINES, COMMENT_TEXT)
		_common.waitForLoaderToDisappear()

	});

});