using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Documents.Project.Localization.Properties;
using RIB.Visual.Platform.BusinessComponents;
using RVPC = RIB.Visual.Platform.Core;
using System.Linq;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;
using System;
using System.Collections.Generic;
using RIB.Visual.Platform.AppServer.Runtime;
using System.Threading;
using Microsoft.Graph;
using RIB.Visual.Services.Scheduler.BusinessComponents;
using RIB.Visual.Services.Scheduler.Core;
using System.Linq.Expressions;

namespace RIB.Visual.Documents.Project.BusinessComponents
{
	/// <summary>
	/// SharePoint logic
	/// </summary>
	/// 2025-05-13 , by hzh
	public partial class DocumentSharePointLogic : LogicBase
	{
		private SharePointApiFacade sharePointApiFacade;

		private DocMetadata2extEntityLogic docMetadata2ExtEntityLogic = new DocMetadata2extEntityLogic();
		private DocSpProjectConfigLogic docSpProjectConfigLogic = new DocSpProjectConfigLogic();
		private DocSpprjDetailStructLogic docSpprjDetailStructLogic = new DocSpprjDetailStructLogic();
		private DocSpprjFolderStructLogic docSpprjFolderStructLogic = new DocSpprjFolderStructLogic();

		private int externalSourceFk = DocSpCommonLogic<DocSpProjectConfigEntity>.ExternalSourceFk;

		/// <summary>
		/// 
		/// </summary>
		public DocumentSharePointLogic()
		{
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="authParams"></param>
		public DocumentSharePointLogic(OfficeAuthParams authParams)
		{
			sharePointApiFacade = new SharePointApiFacade(authParams);
			docSpprjDetailStructLogic.SharePointApi = sharePointApiFacade;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <param name="aadUserId"></param>
		/// <returns></returns>
		public MsOfficeApiResult<string> GetShareLink(SharePointGetShareLinkRequestEntity request, string aadUserId)
		{
			MsOfficeApiResult<string> officeApiResult = new MsOfficeApiResult<string>();
			officeApiResult.Success = false;

			DocumentLogic documentLogic = new DocumentLogic();
			var identificationData = new RVPC.IdentificationData()
			{
				Id = request.PrjDocId
			};

			var prjDocEntity = documentLogic.GetById(identificationData);
			if (null == prjDocEntity)
			{
				officeApiResult.Message = Resources.ERR_DocumentNotFound;
				return officeApiResult;
			}

			if (null == prjDocEntity.PrjProjectFk)
			{
				officeApiResult.Message = Resources.ERR_ProjectIsEmpty;
				return officeApiResult;
			}


			string msg = string.Empty;
			var metaData2Ext = docMetadata2ExtEntityLogic.GetByFilter(e => e.Objectid == prjDocEntity.PrjProjectFk.Value
								&& e.Type == (int)MetaDataType.Project && e.ExternalId != "").FirstOrDefault();

			if (null == metaData2Ext)
			{
				officeApiResult.Message = Resources.ERR_PrjDocSpPrjNotSynced;
				return officeApiResult;
			}
			var siteId = metaData2Ext.ExternalId;
			msg = "";
			string driveItemId;
			driveItemId = GetDriveItemIdForFile(prjDocEntity.Id, ref msg);
			switch (request.ItemType)
			{
				case SharePointDriveItemType.file:
					if (!string.IsNullOrWhiteSpace(msg))
					{
						officeApiResult.Message = msg;
						return officeApiResult;
					}
					break;
				default:

					//var driveItemPath = GetDriveItemPathForFolder(prjDocEntity, ref msg);
					if (!string.IsNullOrWhiteSpace(msg))
					{
						officeApiResult.Message = msg;
						return officeApiResult;
					}
					MsOfficeApiResult<DriveItem> driveItemApiResult = sharePointApiFacade.GetDriveItemById(siteId, driveItemId);
					if (!driveItemApiResult.Success)
					{
						officeApiResult.Message = driveItemApiResult.Message;
						return officeApiResult;
					}
					driveItemId = driveItemApiResult.Item.ParentReference.Id;
					break;
			}

			//var permissionResponse = sharePointApiFacade.GetPermissionByUserId(siteId, driveItemId, aadUserId);
			//if (!permissionResponse.Success)
			//{
			//	officeApiResult.Message = permissionResponse.Message;
			//	return officeApiResult;
			//}

			//var permission = permissionResponse.Items.FirstOrDefault();
			//if (null == permission || !permission.Roles.Any())
			//{
			//	officeApiResult.Message = Resources.ERR_NoPermission;
			//	return officeApiResult;
			//}

			//var role = permission.Roles.First();

			//SharePointCreateLinkType linkType = role == "write" ? SharePointCreateLinkType.edit : SharePointCreateLinkType.view;

			var createLinkResponse = sharePointApiFacade.CreateLink(siteId, driveItemId, SharePointCreateLinkType.edit);
			if (!createLinkResponse.Success)
			{
				if (!createLinkResponse.Success)
				{
					officeApiResult.Message = createLinkResponse.Message;
					return officeApiResult;
				}	
			}

			officeApiResult.Item = createLinkResponse.Item;
			officeApiResult.Success = true;

			return officeApiResult;
		}

		/// <summary>
		/// get drive item path for folder
		/// </summary>
		/// <param name="prjDoc"></param>
		/// <param name="msg"></param>
		/// <returns></returns>
		public string GetDriveItemPathForFolder(DocumentEntity prjDoc, ref string msg)
		{
			var result = "";
			var prjId = prjDoc.PrjProjectFk.Value;
			var prjConfigEntity = docSpProjectConfigLogic.GetByFilter(e => e.PrjProjectFk == prjId).FirstOrDefault();

			if (null == prjConfigEntity)
			{
				msg = Resources.ERR_PrjDocSpPrjNotSynced;
				return result;
			}

			if (!prjConfigEntity.PrjDocSpprjFolderSettingFk.HasValue)
			{
				msg = Resources.ERR_PrjDocSpNoFolderSetting;
				return result;
			}

			var levelMetaDataDic = docSpprjFolderStructLogic.GetLevelMetaDataDicForDocPrj(prjConfigEntity.PrjDocSpprjFolderSettingFk.Value, prjDoc, false);

			if (!levelMetaDataDic.Any())
			{
				msg = Resources.ERR_PrjDocSpPrjNotSynced;
				return result;
			}

			result = "/" + string.Join('/', levelMetaDataDic.Values.Select(e => e.Value));
			return result;
		}

		private string GetDriveItemIdForFile(int prjDocId, ref string msg)
		{
			msg = "";
			var docRevisionLogic = new DocumentRevisionLogic();
			var revisions = docRevisionLogic.GetByFilter(e => e.PrjDocumentFk == prjDocId && e.FileArchiveDocFk.HasValue).ToList();
			if (!revisions.Any())
			{
				msg = Resources.ERR_DocumentNoRevision;
				return "";
			}

			var filearchiveIds = revisions.CollectIds(e => e.FileArchiveDocFk).ToList();
			FileArchiveDoc2ExternalLogic fileArchiveDoc2ExternalLogic = new FileArchiveDoc2ExternalLogic();

			var fileExt = fileArchiveDoc2ExternalLogic.GetSearchList(e => e.BasExternalSourceFk == externalSourceFk && e.FileArchiveDocFk.HasValue &&
						null != e.ExtGuid && e.ExtGuid != "" && filearchiveIds.Contains(e.FileArchiveDocFk.Value)).OrderByDescending(ex => ex.Id).FirstOrDefault();
			if (null == fileExt || string.IsNullOrWhiteSpace(fileExt.ExtGuid))
			{
				msg = Resources.ERR_DocNoSyncSharePoint;
				return "";
			}
			return fileExt.ExtGuid;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public IEnumerable<DocSpProjectConfigEntity> GetSharePointConfigurationList()
		{
			var companyInfo = GetCurrentCompany();
			var projectLogic = Injector.Get<IGetProjectLogic>();
			var projects = projectLogic.GetProjectsByCompany(companyInfo.Id);
			var prjIds = projects.CollectIds(e => e.Id);

			var spPrjConfigDic = docSpProjectConfigLogic.GetList().Where(e => prjIds.Contains(e.PrjProjectFk)).ToDictionary(e => e.PrjProjectFk, e => e);
			var spAllFsIds = spPrjConfigDic.Values.Where(e => e.PrjDocSpprjFolderSettingFk.HasValue).Select(e => e.PrjDocSpprjFolderSettingFk.Value).ToList();
			var userAssignsByFsDic = docSpprjDetailStructLogic.GetAllAadUserDisplaysOrIdsForFs(spAllFsIds);
			var spPrjConfigSettingIds = spPrjConfigDic.Values.Where(e => null != e.PrjDocSpprjFolderSettingFk).CollectIds(e => e.PrjDocSpprjFolderSettingFk);

			List<DocSpProjectConfigEntity> result = new List<DocSpProjectConfigEntity>();
			foreach (var project in projects)
			{
				var prjId = project.Id;
				var docSpProjectConfigEntity = new DocSpProjectConfigEntity();
				docSpProjectConfigEntity.PrjProjectFk = prjId;
				docSpProjectConfigEntity.ProjectNo = project.ProjectNo;
				docSpProjectConfigEntity.ProjectName = project.ProjectName;

				//here, id uses project id, because docSpProjectConfigEntity.id may not exist
				docSpProjectConfigEntity.Id = prjId;
				if (spPrjConfigDic.ContainsKey(prjId))
				{
					var spPrjConfig = spPrjConfigDic[project.Id];
					var fsId = spPrjConfig.PrjDocSpprjFolderSettingFk;
					docSpProjectConfigEntity.PrjDocSpprjFolderSettingFk = fsId;
					if (null != fsId)
					{
						if (userAssignsByFsDic.ContainsKey(fsId.Value))
						{
							docSpProjectConfigEntity.UserAssignment = userAssignsByFsDic[fsId.Value];
							docSpProjectConfigEntity.HasDrillDown = true;
						}
					}

					docSpProjectConfigEntity.IsAutoSync = spPrjConfig.IsAutoSync;
					docSpProjectConfigEntity.IsProjectSynced = spPrjConfig.IsProjectSynced;
				}

				result.Add(docSpProjectConfigEntity);
			}

			return result;
		}

		/// <summary>
		/// get folder count by projects
		/// </summary>
		/// <param name="prjIds"></param>
		/// <returns></returns>
		public List<KeyValuePair<int, int>> GetFolderCountByPrjs(IEnumerable<int> prjIds)
		{
			List<KeyValuePair<int, int>> result = new List<KeyValuePair<int, int>>();

			// key is project ids, value: PrjDocSpprjFolderSettingFk
			var prj2fsetting = docSpProjectConfigLogic.GetByFilter(e => prjIds.Contains(e.PrjProjectFk) && null != e.PrjDocSpprjFolderSettingFk)
							.ToDictionary(e => e.PrjProjectFk, e => e.PrjDocSpprjFolderSettingFk.Value);

			var fsettingIds = prj2fsetting.Values;
			var fsetting2Detail = docSpprjDetailStructLogic.GetByFsIds(fsettingIds).GroupBy(e => e.PrjDocSpprjFolderSettingFk);
			var fsetting2DetailCount = fsetting2Detail.ToDictionary(e => e.Key, e => e.Count());
			foreach (var item in prj2fsetting)
			{
				if (fsetting2DetailCount.ContainsKey(item.Value))
				{
					result.Add(new KeyValuePair<int, int>(item.Key, fsetting2DetailCount[item.Value]));
				}
			}

			return result;
		}

		/// <summary>
		/// if site name includes special chars, create site faile
		/// </summary>
		/// <param name="projectNo"></param>
		/// <returns></returns>
		public string GetSpValidProject(string projectNo)
		{
			string[] specialChars = [" ", "{", "}", "\\"];
			foreach (var item in specialChars)
			{
				projectNo = projectNo.Replace(item, "");
			}
			return projectNo;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="projectNo"></param>
		/// <param name="projectName"></param>
		/// <param name="companyCode"></param>
		/// <param name="entity"></param>
		/// <param name="accessToken"></param>
		/// <param name="refreshToken"></param>
		/// <param name="aadUserId"></param>
		/// <returns></returns>
		/// <exception cref="BusinessLayerException"></exception>
		public bool SyncProject(int projectId, string projectNo, string projectName, string companyCode, DocSpProjectConfigEntity entity,
						string accessToken, string refreshToken, string aadUserId)
		{
			projectNo = GetSpValidProject(projectNo);

			var siteName = $"{companyCode}_{projectNo}";
			//before create team, check if the sitename exists. Because same sitename can be existed.
			var getSiteResponse = sharePointApiFacade.GetSite(siteName);
			if (getSiteResponse.Success)
			{
				SavePrjSyncedInfo(projectId, entity, getSiteResponse.Item.Id);
				return true;
			}

			var createTeamResponse = sharePointApiFacade.CreateTeam(siteName, aadUserId, accessToken, refreshToken, projectName);
			if (createTeamResponse.Success)
			{
				//save team id, user for add members
				Team team = createTeamResponse.Item;
				SaveTeamInfo(projectId, team.Id);

				//Save driveitem id for channel
				SaveDriveItemIdForChannel(projectId, team.Id);				

				//due to createteam's response can't return site id. 
				//After creating success, the api getsite can't get this site immediately. so here delay 2s and loop 3 times.
				Thread.Sleep(2000);

				var loopCount = 3;
				while (loopCount > 0)
				{
					getSiteResponse = sharePointApiFacade.GetSite(siteName);
					if (getSiteResponse.Success)
					{
						SavePrjSyncedInfo(projectId, entity, getSiteResponse.Item.Id);
						break;
					}
					else
					{
						if (loopCount > 0)
						{
							loopCount--;
							Thread.Sleep((3 - loopCount) * 1000);
							continue;
						}

						throw new BusinessLayerException(getSiteResponse.Message);
					}
				}

				return true;
			}
			else
			{
				throw new BusinessLayerException(createTeamResponse.Message);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="teamId"></param>
		public void SaveTeamInfo(int projectId, string teamId)
		{
			if (string.IsNullOrWhiteSpace(teamId))
			{
				return;
			}

			var pubSpUserResponse = sharePointApiFacade.GetPublicUser();
			var pubSpUserId = pubSpUserResponse.Success ? pubSpUserResponse.Item.Id : "";

			if (!string.IsNullOrWhiteSpace(pubSpUserId))
			{
				sharePointApiFacade.AddMembers(teamId, new Dictionary<string, MemberRoleType>() { { pubSpUserId, MemberRoleType.owner } });
				sharePointApiFacade.AddMembers(teamId, new Dictionary<string, MemberRoleType>() { { pubSpUserId, MemberRoleType.owner } });
			}			

			var teamMetadata = docMetadata2ExtEntityLogic.GetTeamByPrjIds([projectId]).FirstOrDefault();

			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				if (null != teamMetadata)
				{
					docMetadata2ExtEntityLogic.Delete(teamMetadata);
				}

				DocMetadata2extEntity docMetadata2ExtEntity = docMetadata2ExtEntityLogic.Create();
				docMetadata2ExtEntity.Objectid = projectId;
				docMetadata2ExtEntity.ExternalId = teamId;
				docMetadata2ExtEntity.BasExternalsourceFk = externalSourceFk;
				docMetadata2ExtEntity.Type = (int)MetaDataType.Team;
				docMetadata2ExtEntityLogic.Save(docMetadata2ExtEntity);

				transaction.Complete();
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="teamId"></param>
		public void SaveDriveItemIdForChannel(int projectId, string teamId)
		{
			if (string.IsNullOrWhiteSpace(teamId))
			{
				return;
			}

			var getDriveItemForChannelResponse = sharePointApiFacade.GetDriveItemForChannel(teamId);
			if (!getDriveItemForChannelResponse.Success)
			{
				throw new BusinessLayerException(getDriveItemForChannelResponse.Message);
			}

			List<DocMetadata2extEntity> docMetadata2ExtEntities = new List<DocMetadata2extEntity>();
			//key: channel id, value: channel folder id
			var kv = getDriveItemForChannelResponse.Item.First();
			if (!string.IsNullOrWhiteSpace(kv.Key))
			{
				DocMetadata2extEntity channelExtEntity = docMetadata2ExtEntityLogic.Create();
				channelExtEntity.Objectid = projectId;
				channelExtEntity.ExternalId = kv.Key;
				channelExtEntity.BasExternalsourceFk = externalSourceFk;
				channelExtEntity.Type = (int)MetaDataType.Channel;
				docMetadata2ExtEntities.Add(channelExtEntity);
			}

			if (!string.IsNullOrWhiteSpace(kv.Value))
			{
				DocMetadata2extEntity channelFolderExtEntity = docMetadata2ExtEntityLogic.Create();
				channelFolderExtEntity.Objectid = projectId;
				channelFolderExtEntity.ExternalId = kv.Value;
				channelFolderExtEntity.BasExternalsourceFk = externalSourceFk;
				channelFolderExtEntity.Type = (int)MetaDataType.ChannelFolder;
				docMetadata2ExtEntities.Add(channelFolderExtEntity);
			}

			if (!docMetadata2ExtEntities.Any())
			{
				return;
			}

			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				docMetadata2ExtEntityLogic.Save(docMetadata2ExtEntities);

				transaction.Complete();
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="entity"></param>
		/// <param name="extId"></param>
		public void SavePrjSyncedInfo(int projectId, DocSpProjectConfigEntity entity, string extId = "")
		{
			if (null == entity && string.IsNullOrWhiteSpace(extId))
			{
				return;
			}

			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				if (null != entity)
				{
					docSpProjectConfigLogic.Save(entity);
				}

				if (!string.IsNullOrWhiteSpace(extId))
				{
					DocMetadata2extEntity docMetadata2ExtEntity = docMetadata2ExtEntityLogic.Create();
					docMetadata2ExtEntity.Objectid = projectId;
					docMetadata2ExtEntity.ExternalId = extId;
					docMetadata2ExtEntity.BasExternalsourceFk = externalSourceFk;
					docMetadata2ExtEntity.Type = (int)MetaDataType.Project;
					docMetadata2ExtEntityLogic.Save(docMetadata2ExtEntity);
				}

				transaction.Complete();
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entities"></param>
		/// <param name="refreshToken"></param>
		/// <param name="accessToken"></param>
		/// <param name="aadUserId"></param>
		/// <returns></returns>
		public bool SyncProjects(IEnumerable<DocSpProjectConfigEntity> entities, string accessToken, string refreshToken, string aadUserId)
		{
			var prjIds = entities.CollectIds(e => e.PrjProjectFk);

			int projectTypeFk = (int)MetaDataType.Project;
			var prjMetadata2Exts = docMetadata2ExtEntityLogic.GetByFilter(e => prjIds.Contains(e.Objectid) && e.ExternalId != ""
						&& e.BasExternalsourceFk == externalSourceFk && e.Type == projectTypeFk);
			if (prjMetadata2Exts.Any())
			{
				var prjMetadata2ExtsIds = prjMetadata2Exts.CollectIds(e => e.Objectid);
				var syncedProjects = docSpProjectConfigLogic.GetByFilter(e => prjMetadata2ExtsIds.Contains(e.PrjProjectFk) && !e.IsProjectSynced)
					.Select(e =>
					{
						e.IsProjectSynced = true;
						return e;
					});
				if (syncedProjects.Any())
				{
					docSpProjectConfigLogic.Save(syncedProjects);
				}

				entities = entities.Where(e => !prjMetadata2ExtsIds.Contains(e.PrjProjectFk));
				prjIds = entities.CollectIds(e => e.PrjProjectFk);
			}

			var needSyncedProjects = entities.ToDictionary(e => e.PrjProjectFk, e => (e.ProjectNo, e.ProjectName));

			var prjConfigs = docSpProjectConfigLogic.GetByFilter(e => prjIds.Contains(e.PrjProjectFk)).CollectIds(e => e.PrjProjectFk);
			if (prjConfigs.Any())
			{
				entities = entities.Where(e => !prjConfigs.Contains(e.PrjProjectFk));
			}

			var companyInfo = GetCurrentCompany();
			var companyCode = companyInfo.GetCompanyCode();

			List<int> ids = new List<int>();
			if (entities.Any())
			{
				ids = docSpProjectConfigLogic.GetNextSequences(entities.Count());
			}

			var index = 0;
			foreach (var item in needSyncedProjects)
			{
				var entity = entities.FirstOrDefault(e => e.PrjProjectFk == item.Key);
				if (entity != null)
				{
					entity.Id = ids[index++];
				}

				SyncProject(item.Key, item.Value.ProjectNo, item.Value.ProjectName, companyCode, entity, accessToken, refreshToken, aadUserId);
			}

			return true;
		}

		/// <summary>
		/// recheck whether the project is synced.
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public IEnumerable<SyncedProjectResponse> CheckProjectsIsSynced(IEnumerable<DocSpProjectConfigEntity> request)
		{
			List <SyncedProjectResponse> result = new List<SyncedProjectResponse>();
			var prjIds = request.CollectIds(e => e.PrjProjectFk);

			var pubSpUserResponse = sharePointApiFacade.GetPublicUser();
			var pubSpUserId = pubSpUserResponse.Success ? pubSpUserResponse.Item.Id : "";
			
			var prj2Sites = docMetadata2ExtEntityLogic.GetSiteIdByPrjIds(prjIds);
			var prj2Teams = docMetadata2ExtEntityLogic.GetTeamIdByPrjIds(prjIds);
			var prj2Configs = docSpProjectConfigLogic.GetByPrjIds(prjIds);
			var fSettingIds = prj2Configs.Values.Where(e => null != e.PrjDocSpprjFolderSettingFk).CollectIds(e => e.PrjDocSpprjFolderSettingFk);
			var existDetailFSettingIds = docSpprjDetailStructLogic.GetByFilter(e => fSettingIds.Contains(e.PrjDocSpprjFolderSettingFk)).CollectIds(e => e.PrjDocSpprjFolderSettingFk).ToDictionary(e => e, e => true);
			var folderSetting2AadUserDic = docSpprjDetailStructLogic.GetAllAadUseIdsWithGuestForFs(fSettingIds, pubSpUserId);

			List<DocSpProjectConfigEntity> toSavePrjConfig = new List<DocSpProjectConfigEntity>();
			List<DocMetadata2extEntity> toSaveMetadata2Ext = new List<DocMetadata2extEntity>();

			var companyInfo = GetCurrentCompany();
			var companyCode = companyInfo.GetCompanyCode();

			foreach (var project in request)
			{
				bool isSynced = false;
				int prjId = project.PrjProjectFk;
				if (prj2Configs.ContainsKey(prjId) && prj2Teams.ContainsKey(prjId))
				{
					try
					{
						var folderSettingId = prj2Configs[prjId].PrjDocSpprjFolderSettingFk.Value;
						if (folderSetting2AadUserDic.ContainsKey(folderSettingId) && folderSetting2AadUserDic[folderSettingId].Any())
						{
							sharePointApiFacade.AddMembers(prj2Teams[prjId], folderSetting2AadUserDic[folderSettingId]);
						}
					}
					catch { }
				}

				if (prj2Configs.ContainsKey(prjId) && prj2Sites.ContainsKey(prjId) && !string.IsNullOrWhiteSpace(prj2Sites[prjId]))
				{
					isSynced = true;
				}
				else
				{
					if (!prj2Configs.ContainsKey(prjId))
					{
						toSavePrjConfig.Add(project);
					}

					if (!prj2Sites.ContainsKey(prjId))
					{
						var projectNo = GetSpValidProject(project.ProjectNo);

						var siteName = $"{companyCode}_{projectNo}";
						//check if the sitename exists.
						var getSiteResponse = sharePointApiFacade.GetSite(siteName);
						if (getSiteResponse.Success)
						{
							var siteId = getSiteResponse.Item.Id;
							DocMetadata2extEntity docMetadata2ExtEntity = new DocMetadata2extEntity();
							docMetadata2ExtEntity.Objectid = prjId;
							docMetadata2ExtEntity.ExternalId = siteId;
							docMetadata2ExtEntity.BasExternalsourceFk = externalSourceFk;
							docMetadata2ExtEntity.Type = (int)MetaDataType.Project;
							toSaveMetadata2Ext.Add(docMetadata2ExtEntity);

							isSynced = true;
						}
					}
				}

				result.Add(new SyncedProjectResponse()
				{
					Id = project.Id,
					IsSynced = isSynced,
					ProjectId = prjId
				});
			}

			if (!toSaveMetadata2Ext.Any() && !toSavePrjConfig.Any())
			{
				return result;
			}

			var prjdocCommon = new DocSpCommonLogic<DocSpProjectConfigEntity>();

			if (toSaveMetadata2Ext.Any())
			{
				var prj2ExtIds = docMetadata2ExtEntityLogic.GetNextSequences(toSaveMetadata2Ext.Count);
				foreach (var item in toSaveMetadata2Ext)
				{
					item.Id = prjdocCommon.SeqPop(prj2ExtIds);
				}
			}

			if (toSavePrjConfig.Any())
			{
				var prjConfigIds = docSpProjectConfigLogic.GetNextSequences(toSavePrjConfig.Count);
				foreach (var item in toSavePrjConfig)
				{
					item.Id = prjdocCommon.SeqPop(prjConfigIds);
					var prjId = item.PrjProjectFk;
					var syncedPrj = result.FirstOrDefault(e => e.ProjectId == prjId);
					if (syncedPrj != null)
					{
						syncedPrj.Id = item.Id;
					}
				}
			}

			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				if (toSavePrjConfig.Any())
				{
					docSpProjectConfigLogic.Save(toSavePrjConfig);
				}

				if (toSaveMetadata2Ext.Any())
				{
					docMetadata2ExtEntityLogic.Save(toSaveMetadata2Ext);
				}

				transaction.Complete();
			}

			return result;
		}

		private ICompanyInfo GetCurrentCompany()
		{
			var companyLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ILoginCompanyInfoProvider>();
			return companyLogic.GetInstanceFromCurrentContext();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		/// <exception cref="BusinessLayerException"></exception>
		public object GetAadUsers()
		{
			var response = sharePointApiFacade.GetUsers();
			if (!response.Success)
			{
				throw new BusinessLayerException(response.Message);
			}

			return response.Items.Select(e =>
			{
				var aadUser = new AadUser()
				{
					Id = e.Id,
					DisplayName = e.DisplayName,
					Mail = e.Mail,
					UserPrincipalName = e.UserPrincipalName
				};
				if (e.UserPrincipalName.IndexOf("#EXT#") >= 0)
				{
					aadUser.IsGuest = true;
				}

				return aadUser;
			});
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public bool ShareViaSharePoint(SendManualSyncInfoEntity request)
		{
			BasicsCustomizeProjectDocumentTypeLogic projectDocumentTypeLogic = new BasicsCustomizeProjectDocumentTypeLogic();
			var sharepointType = projectDocumentTypeLogic.GetByFilter(e => e.IsSharepoint).FirstOrDefault();
			if (null == sharepointType)
			{
				throw new BusinessLayerException(Resources.ERR_PrjDocSpPrjDocTypeNotExistsSharePoint);
			}

			DocumentLogic documentLogic = new DocumentLogic();
			var docEntities = documentLogic.GetByFilter(e => request.DocIds.Contains(e.Id) && e.PrjDocumentTypeFk != sharepointType.Id);
			if (!docEntities.Any())
			{
				throw new BusinessLayerException(Resources.ERR_PrjDocSpDocSelectedIsSynced);
			}

			var docIds = docEntities.CollectIds(e => e.Id);
			var prjIds = docEntities.CollectIds(e => e.PrjProjectFk);
			var prjIdsStr = string.Join(',', prjIds);
			var docIdsStr = string.Join(',', docIds);

			var jobIds = this.Sync(null, prjIdsStr, docIdsStr, request.Profile, request.AccessToken, false);
			if (jobIds.Any())
			{
				int jobId = jobIds.First();
				JobLogic jobLogic = new JobLogic();

				while (true)
				{
					var job = jobLogic.GetJobById(jobId);
					var jobState = (JobState)job.JobState;
					if (jobState == JobState.Finished)
					{
						return true;
					}
					else if (jobState == JobState.Aborted)
					{
						throw new BusinessLayerException("Import Aborted!");
					}
					else if (jobState == JobState.Stopped)
					{
						string msg = job.ErrorMessage;
						throw new BusinessLayerException(string.IsNullOrWhiteSpace(msg) ? "Synced Stopped" : msg);
					}

					Thread.Sleep(1000);
				}
			}

			return true;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="docRevisionId"></param>
		/// <returns></returns>
		public SpResponseEntity ReplaceFileSpDoc(int docRevisionId)
		{
			SpResponseEntity result = new SpResponseEntity()
			{
				Success = false
			};

			BasicsCustomizeProjectDocumentTypeLogic projectDocumentTypeLogic = new BasicsCustomizeProjectDocumentTypeLogic();
			var sharepointType = projectDocumentTypeLogic.GetByFilter(e => e.IsSharepoint).FirstOrDefault();
			if (null == sharepointType)
			{
				result.Message = Resources.ERR_PrjDocSpPrjDocTypeNotExistsSharePoint;
				return result;
			}

			var docRevisionLogic = new DocumentRevisionLogic();
			Expression<Func<DocumentRevisionEntity, int>> predicate = e => e.Id;
			var docRevision = docRevisionLogic.GetItemByKey(predicate, docRevisionId);
			var prjDocId = docRevision.PrjDocumentFk;
			DocumentLogic docLogic = new DocumentLogic();
			var prjDocEntity = docLogic.GetItemByKey(prjDocId);
			if (prjDocEntity.PrjDocumentTypeFk != sharepointType.Id)
			{
				result.Message = Resources.ERR_DocNoSyncSharePoint;
				return result;
			}

			var docRevisions = docRevisionLogic.GetByFilter(e => e.PrjDocumentFk == prjDocId && e.FileArchiveDocFk.HasValue);
			if (docRevisions.Any())
			{
				result.Message = Resources.ERR_DocNoSyncSharePoint;
				return result;
			}

			var fileIds = docRevisions.CollectIds(e => e.FileArchiveDocFk.Value);
			FileArchiveDoc2ExternalLogic fileArchiveDoc2ExternalLogic = new FileArchiveDoc2ExternalLogic();
			var doc2exts = fileArchiveDoc2ExternalLogic.GetSearchList(e => e.BasExternalSourceFk == externalSourceFk && e.FileArchiveDocFk.HasValue
						 && fileIds.Contains(e.FileArchiveDocFk.Value) && null != e.ExtGuid);

			//rename and then replace
			if (!doc2exts.Any())
			{
				result.Message = Resources.ERR_PrjDocSpRevisionSyncedNotFound;
			}
			else
			{
				var prjId = prjDocEntity.Id;
				var prj2SiteDic = docMetadata2ExtEntityLogic.GetSiteIdByPrjIds([prjId]);
				if (null == prj2SiteDic)
				{
					result.Message = Resources.ERR_PrjDocSpPrjNotSynced;
					return result;
				}

				var extGuid = doc2exts.First().ExtGuid;
				var siteId = prj2SiteDic[prjId];
				var driveItemResponse = sharePointApiFacade.GetDriveItemById(siteId, extGuid);
				if (!driveItemResponse.Success)
				{
					result.Message = driveItemResponse.Message;
					return result;
				}
				var driveItem = driveItemResponse.Item;
				var fileId = docRevision.FileArchiveDocFk.Value;
				var fileArchive = new FileArchiveDocLogic().GetById(new RVPC.IdentificationData()
				{
					Id = fileId
				});

				var renameResponse = sharePointApiFacade.Rename(siteId, driveItem, fileArchive.OriginFileName);
				if (!renameResponse.Success)
				{
					result.Message = renameResponse.Message;
					return result;
				}

				var uploadResponse = sharePointApiFacade.UploadFile(fileId, siteId, driveItem.Id, false);
				if (!uploadResponse.Success)
				{
					result.Message = uploadResponse.Message;
					return result;
				}

				//clear old doc revision's ext id
				foreach (var item in doc2exts)
				{
					item.ExtGuid = null;
				}

				var newFile2Ext = fileArchiveDoc2ExternalLogic.GetSearchList(e => e.BasExternalSourceFk == externalSourceFk).FirstOrDefault();
				bool isCreate = false;
				if (null == newFile2Ext)
				{
					isCreate = true;
				}
				else
				{
					if (newFile2Ext.FileArchiveDocFk.HasValue && newFile2Ext.FileArchiveDocFk.Value != docRevision.FileArchiveDocFk)
					{
						isCreate = true;
					}
				}

				if (isCreate)
				{
					newFile2Ext = fileArchiveDoc2ExternalLogic.Create();
					newFile2Ext.BasExternalSourceFk = externalSourceFk;
				}
				newFile2Ext.FileArchiveDocFk = docRevision.FileArchiveDocFk;
				newFile2Ext.ExtGuid = driveItem.Id;
				newFile2Ext.ExtLastUpdate = DateTime.UtcNow;

				doc2exts.Append(newFile2Ext);

				using (var transaction = TransactionScopeFactory.CreateRequiresNew())
				{
					fileArchiveDoc2ExternalLogic.Save(doc2exts);

					transaction.Complete();
				}

				result.Success = true;
			}

			return result;
		}

		/// <summary>
		/// get sp extid by docs, key: prj doc id, value: ext id
		/// </summary>
		/// <param name="docs"></param>
		/// <returns></returns>
		public void GetExtIdByDocs(IEnumerable<DocumentEntity> docs)
		{
			if (!docs.Any())
			{
				return;
			}

			//Judage whether project has been synced to sharepoint
			var tempDocs = docs.Where(e => null != e.PrjProjectFk);
			if (null == tempDocs || !tempDocs.Any())
			{
				return;
			}

			var prjIds = tempDocs.CollectIds(e => e.PrjProjectFk.Value);
			var prj2ext = new DocMetadata2extEntityLogic().GetSiteIdByPrjIds(prjIds);
			if (!prj2ext.Any())
			{
				return;
			}

			foreach (var item in tempDocs)
			{
				var prjId = item.PrjProjectFk.Value;
				if (prj2ext.ContainsKey(prjId))
				{
					item.Prj2SpExtId = prj2ext[prjId];
				}
			}

			tempDocs = tempDocs.Where(e => null != e.FileArchiveDocFk);

			var prj2extKeys = prj2ext.Keys;
			tempDocs = tempDocs.Where(e => prj2extKeys.Contains(e.PrjProjectFk.Value));
			var docIds = tempDocs.CollectIds(e => e.Id);
			var docRevisionLogic = new DocumentRevisionLogic();
			var docRevs = docRevisionLogic.GetCoresByFilter(e => docIds.Contains(e.PrjDocumentFk) && null != e.FileArchiveDocFk);
			if (!docRevs.Any())
			{
				return;
			}


			var docRevfileIds = docRevs.CollectIds(e => e.FileArchiveDocFk.Value);
			FileArchiveDoc2ExternalLogic fileArchiveDoc2ExternalLogic = new FileArchiveDoc2ExternalLogic();
			var file2SpExtDic = fileArchiveDoc2ExternalLogic.GetSearchList(e => null != e.FileArchiveDocFk && docRevfileIds.Contains(e.FileArchiveDocFk.Value)
								&& e.BasExternalSourceFk == externalSourceFk && e.ExtGuid != "").ToDictionary(e => e.FileArchiveDocFk.Value, e => e.ExtGuid);
			if (!file2SpExtDic.Any())
			{
				return;
			}

			var rev2SpExtDic = (from a in docRevs
									  from f in file2SpExtDic
									  where a.FileArchiveDocFk.Value == f.Key
									  select new { a.Id, a.PrjDocumentFk, f.Value }).ToDictionary(e => e.Id, e => (e.PrjDocumentFk, e.Value));

			var doc2SpExtDic = rev2SpExtDic.Values.GroupBy(e => e.PrjDocumentFk).ToDictionary(e => e.Key, e => e.First().Value);
			foreach (var item in tempDocs)
			{
				var docId = item.Id;
				if (doc2SpExtDic.ContainsKey(docId))
				{
					item.File2SpExtId = doc2SpExtDic[docId];
				}
			}

			return;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="prjDocs"></param>
		/// <returns></returns>
		public Dictionary<int, int> DownloadSpFiles(IEnumerable<DocumentEntity> prjDocs)
		{
			Dictionary<int, int> result = new Dictionary<int, int>();

			var validPrjDocs = prjDocs.Where(e => null != e.PrjProjectFk && e.Prj2SpExtId != "" && null != e.FileArchiveDocFk);
			if (!validPrjDocs.Any())
			{
				return result;
			}

			var prjIds = validPrjDocs.CollectIds(e => e.PrjProjectFk.Value);

			GetExtIdByDocs(prjDocs);

			var tempPrjDocsDic = prjDocs.Where(e => !string.IsNullOrWhiteSpace(e.Prj2SpExtId) && !string.IsNullOrWhiteSpace(e.File2SpExtId));
			foreach (var item in tempPrjDocsDic)
			{
				var siteId = item.Prj2SpExtId;
				var driveItemId = item.File2SpExtId;

				var fileIdFromSpFile = GetDownloadSpFileId(siteId, driveItemId);

				result.Add(item.Id, fileIdFromSpFile);
			}

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="docRevs"></param>
		/// <returns></returns>
		public Dictionary<int, int> DownloadSpFilesByDocRevs(IEnumerable<DocumentRevisionEntity> docRevs)
		{
			Dictionary<int, int> result = new Dictionary<int, int>();

			var validDocRevs = docRevs.Where(e => !string.IsNullOrWhiteSpace(e.Prj2SpExtId) && null != e.FileArchiveDocFk);
			if (!validDocRevs.Any())
			{
				return result;
			}

			var siteId = validDocRevs.First().Prj2SpExtId;

			var fileIds = validDocRevs.CollectIds(e => e.FileArchiveDocFk.Value);

			FileArchiveDoc2ExternalLogic fileArchiveDoc2ExternalLogic = new FileArchiveDoc2ExternalLogic();

			var file2Ext = fileArchiveDoc2ExternalLogic.GetSearchList(e => null != e.FileArchiveDocFk && null != e.ExtGuid && e.BasExternalSourceFk == externalSourceFk
									&& fileIds.Contains(e.FileArchiveDocFk.Value)).ToDictionary(e => e.FileArchiveDocFk.Value, e => e.ExtGuid);
			if (!file2Ext.Any())
			{
				return result;
			}

			foreach (var item in docRevs)
			{
				if (file2Ext.ContainsKey(item.FileArchiveDocFk.Value))
				{
					var driveItemId = file2Ext[item.FileArchiveDocFk.Value];

					var fileIdFromSpFile = GetDownloadSpFileId(siteId, driveItemId);

					result.Add(item.Id, fileIdFromSpFile);
				}
			}

			return result;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="siteId"></param>
		/// <param name="driveItemId"></param>
		/// <returns></returns>
		private int GetDownloadSpFileId(string siteId, string driveItemId)
		{
			var driveItemResponse = sharePointApiFacade.GetDriveItemById(siteId, driveItemId);
			if (null == driveItemResponse)
			{
				throw new BusinessLayerException(Resources.ERR_DocNoSyncSharePoint);
			}
			if (!driveItemResponse.Success)
			{
				throw new BusinessLayerException(driveItemResponse.Message);
			}

			var downloadFileResponse = sharePointApiFacade.Download(driveItemResponse.Item, "", true, "temp");
			if (!downloadFileResponse.Success)
			{
				throw new BusinessLayerException(downloadFileResponse.Message);
			}

			if (!downloadFileResponse.Item.FileArchiveDocId.HasValue)
			{
				throw new BusinessLayerException(Resources.ERR_DownFileFail);
			}

			return downloadFileResponse.Item.FileArchiveDocId.Value;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="prjId"></param>
		/// <param name="isAutoSync"></param>
		/// <returns></returns>
		public bool UpdateIsAutoSync(int prjId, bool isAutoSync)
		{
			var prjDocConfigEntity = docSpProjectConfigLogic.GetByFilter(e => e.PrjProjectFk == prjId).FirstOrDefault();
			if (null != prjDocConfigEntity)
			{
				prjDocConfigEntity.IsAutoSync = isAutoSync;

				try
				{
					using (var transaction = TransactionScopeFactory.CreateRequiresNew())
					{
						docSpProjectConfigLogic.Save(prjDocConfigEntity);
						transaction.Complete();
					}
					return true;
				}
				catch (Exception ex)
				{
					throw new BusinessLayerException(ex.Message);
				}
			}
			else
			{
				throw new BusinessLayerException(Resources.ERR_PrjDocSpPrjNotSynced);
			}
		}

		/// <summary>
		/// key: project id, value: isSynced
		/// </summary>
		/// <param name="prjIds"></param>
		/// <returns></returns>
		public IEnumerable<SyncedProjectResponse> SyncDetailStruct(IEnumerable<int> prjIds)
		{
			var result = new List<SyncedProjectResponse>();
			var prjdocCommon = new DocSpCommonLogic<DocSpProjectConfigEntity>();
			var prj2ChannelFolderDic = docMetadata2ExtEntityLogic.GetChannelFolderDicByPrjIds(prjIds);

			foreach (var prjId in prjIds)
			{
				SyncedProjectResponse syncedProjectResponse = new SyncedProjectResponse();
				syncedProjectResponse.ProjectId = prjId;
				syncedProjectResponse.IsSynced = false;
				//here use one by one filter, avoid multi-people sync at same time.	
				var docPrjConfig = docSpProjectConfigLogic.GetByFilter(e => prjId == e.PrjProjectFk).FirstOrDefault();
				if (null == docPrjConfig || null == docPrjConfig.PrjDocSpprjFolderSettingFk)
				{
					result.Add(syncedProjectResponse);
					continue;
				}

				syncedProjectResponse.Id = docPrjConfig.Id;

				if (docPrjConfig.IsProjectSynced)
				{
					syncedProjectResponse.IsSynced = true;
					result.Add(syncedProjectResponse);					
					continue;
				}

				var prj2Ext = docMetadata2ExtEntityLogic.GetSiteIdByPrjIds([prjId]);
				if ((null == prj2Ext && !prj2Ext.Any()) || (string.IsNullOrWhiteSpace(prj2Ext[prjId])))
				{
					result.Add(syncedProjectResponse);
					continue;
				}

				var siteId = prj2Ext[prjId];
				var foldersettingId = docPrjConfig.PrjDocSpprjFolderSettingFk.Value;

				var detailStrucs = docSpprjDetailStructLogic.GetByFsIds([foldersettingId]);
				if (!detailStrucs.Any())
				{
					result.Add(syncedProjectResponse);
					continue;
				}

				var detailStructIds = detailStrucs.CollectIds(e => e.Id);
				var folder2Ext = docMetadata2ExtEntityLogic.GetSyncedFolderDic(detailStructIds);
				if (detailStrucs.Count() == folder2Ext.Count)
				{
					docPrjConfig.IsProjectSynced = true;
					docSpProjectConfigLogic.Save(docPrjConfig);

					syncedProjectResponse.IsSynced = true;
					result.Add(syncedProjectResponse);
					continue;
				}

				List<int> ids = docMetadata2ExtEntityLogic.GetNextSequences(detailStrucs.Count() - folder2Ext.Count);

				var level2DetailStruct = detailStrucs.GroupBy(e => e.Level).OrderBy(e => e.Key).ToDictionary(e => e.Key, e => e.ToList());

				Dictionary<int, bool> SyncResult = new Dictionary<int, bool>();

				var channelFolderDriveItemId = string.Empty;
				if (prj2ChannelFolderDic.ContainsKey(prjId))
				{
					channelFolderDriveItemId = prj2ChannelFolderDic[prjId];
				}

				docSpprjDetailStructLogic.SyncDetailStructToSharePointFolder(level2DetailStruct, ids, siteId, 0, channelFolderDriveItemId);

				folder2Ext = docMetadata2ExtEntityLogic.GetSyncedFolderDic(detailStructIds);
				var flag = false;
				if (detailStrucs.Count() == folder2Ext.Count)
				{
					flag = true;
					docPrjConfig.IsProjectSynced = true;
					docSpProjectConfigLogic.Save(docPrjConfig);
				}
				syncedProjectResponse.IsSynced = flag;
				result.Add(syncedProjectResponse);
			}

			return result;
		}

		/// <summary>
		/// add permission for prjids
		/// </summary>
		/// <param name="prjIds"></param>
		public void AddFolderPermission(IEnumerable<int> prjIds)
		{
			var prj2SiteDic = docMetadata2ExtEntityLogic.GetSiteIdByPrjIds(prjIds);
			var prj2ConfigDic = docSpProjectConfigLogic.GetByFilter(e => prjIds.Contains(e.PrjProjectFk) && null != e.PrjDocSpprjFolderSettingFk).ToDictionary(e => e.PrjProjectFk, e => e.PrjDocSpprjFolderSettingFk.Value);
			var fSetting2SiteDic = new Dictionary<int, string>();
			foreach(var item in prj2ConfigDic)
			{
				var prjId = item.Key;
				if (prj2SiteDic.ContainsKey(prjId) && !string.IsNullOrWhiteSpace(prj2SiteDic[prjId]))
				{
					fSetting2SiteDic.Add(prj2ConfigDic[prjId], prj2SiteDic[prjId]);
				}
			}

			var fsIds = fSetting2SiteDic.Keys;

			var fsDetailStrucs = docSpprjDetailStructLogic.GetByFsIds(fsIds);
			var fsDetailStrucIds = fsDetailStrucs.CollectIds(e => e.Id);
			var detailStruc2FolderId = docMetadata2ExtEntityLogic.GetSyncedFolderDic(fsDetailStrucIds);
			var fsId2DetailStrucDic = fsDetailStrucs.GroupBy(e => e.PrjDocSpprjFolderSettingFk).ToDictionary(e => e.Key, e => e.ToList());		
			foreach(var item in fsId2DetailStrucDic)
			{
				var fsId = item.Key;
				var detailStructs = item.Value;
				var siteId = "";
				if (fSetting2SiteDic.ContainsKey(fsId) && !string.IsNullOrWhiteSpace(fSetting2SiteDic[fsId]))
				{
					siteId = fSetting2SiteDic[fsId];

					foreach (var detailStruc in detailStructs)
					{
						var detailStrucId = detailStruc.Id;
						if (detailStruc2FolderId.ContainsKey(detailStrucId))
						{
							var driveItemId = detailStruc2FolderId[detailStrucId];
							Dictionary<string, string> aadUsers = new Dictionary<string, string>();
							if (detailStruc.ObjectParentFk.HasValue)
							{
								docSpprjDetailStructLogic.GetAllParentAadUsers(detailStruc.ObjectParentFk.Value, detailStructs, ref aadUsers);
							}

							docSpprjDetailStructLogic.AddPermissionForDetail(siteId, detailStruc, driveItemId);
						}
					}
				}			
			}
		}
	}
}
