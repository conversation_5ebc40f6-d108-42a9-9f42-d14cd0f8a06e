<style>
	.groupHeader {
		font-size: 14px;
		font-weight: bold;
		border-bottom: 1px solid #e5e5e5;
	}

	.sharepoint-drill-down-config .border-all {
		border-color: #ccc;
		padding: 8px;
	}

	.sharepoint-drill-down-config .small-icon {
		height: 20px;
		width: 20px;
	}

	.sharepoint-drill-down-config .ico-input-delete {
		margin-top: 4px;
	}


	.sharepoint-drill-down-config .border-top {
		border-color: #ccc;
		padding: 8px;
	}

	.sharepoint-drill-down-config input[readonly] {
		background-color: transparent !important;
	}

	.sharepoint-drill-down-config .fm-ctrl {
		height: 28px !important;
		line-height: 28px !important;
		padding-left: 4px;
	}

	.sharepoint-drill-down-config input[type="text"] {
		padding-left: 4px;
	}
</style>

<div class="sharepoint-drill-down-config flex-box flex-column fullwidth">
	<section class="modal-body">
		<div>
			<div class="platform-form-group">
				<div class="platform-form-row">
					<label class="platform-form-label ng-binding fontSize12px">{{ 'documents.centralquery.sharepoint.folderTemplate.templateName' | translate}}
						<span class="required-cell"></span>
					</label>
				</div>
			</div>
			<div class="flex-box flex-row">
				<div class="margin-right-ld flex-element">
					<input class="form-control fm-ctrl" type="text" data-ng-model="displayText" disabled/>
				</div>
				<div class="margin-left-ld">
					<button type="button" class="btn btn-default" title="{{ 'documents.centralquery.sharepoint.resetTitle' | translate}}" data-ng-click="onReset()">
						{{ 'documents.centralquery.sharepoint.reset' | translate}}
					</button>
					<button type="button" class="btn btn-default" title="{{ 'documents.centralquery.sharepoint.refreshTitle' | translate}}"  data-ng-click="onRefresh()">
						{{ 'documents.centralquery.sharepoint.refresh' | translate}}
					</button>
				</div>
			</div>
			<div class="platform-form-group groupHeader">
				<div class="platform-form-row ">
					<label class="platform-form-label ng-binding groupFont">{{ 'documents.centralquery.sharepoint.folderTemplate.structure' | translate}}
					</label>
				</div>
			</div>
			<div class="flex-element flex-box flex-column border-all overflow-hidden">
				<div class="platform-lg margin-bottom-ld">
					<div class="lg-4">
						<label class="platform-form-label">{{'documents.centralquery.sharepoint.folderTemplate.folderStructure' | translate}}
							<span class="required-cell"></span>
						</label>
					</div>
					<div class="lg-2">
						<label class="platform-form-label">{{'documents.centralquery.sharepoint.folderTemplate.shareOptions' | translate}}</label>
					</div>
					<div class="lg-5">
						<label class="platform-form-label">{{'documents.centralquery.sharepoint.folderTemplate.userAssignment' | translate}}</label>
					</div>
					<div class="lg-1">&nbsp;</div>
				</div>
				<div class="overflow-auto">
					<div class="flex-box flex-column" data-ng-repeat="item in detailStructrues"
					     data-ng-include="'documents.centralquery/templates/sharepoint/project-structure.html'">
					</div>
				</div>
			</div>
		</div>
	</section>

	<footer class="modal-footer">
		<!--<button type="button" class="btn btn-default" data-ng-click="onUpdateForAllProjcts()" data-ng-disabled="dlgOptions.OKBtnDisabled">{{'documents.centralquery.sharepoint.drillDown.updateForAllProjects' | translate}}</button>-->
		<button type="button" class="btn btn-default" data-ng-click="onUpdate()">{{'basics.common.ok' | translate}}</button>
		<button type="button" class="btn btn-default" data-ng-click="onClose($event)" data-ng-disabled="dlgOptions.OKBtnDisabled">{{'basics.common.cancel' | translate}}</button>
	</footer>
</div>