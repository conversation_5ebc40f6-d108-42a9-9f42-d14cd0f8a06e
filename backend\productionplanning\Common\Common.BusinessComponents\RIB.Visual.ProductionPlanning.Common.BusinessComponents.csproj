﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{D42ABB5C-36D0-4B20-AFA1-8F3AC5CCA7F8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.ProductionPlanning.Common.BusinessComponents</RootNamespace>
    <AssemblyName>RIB.Visual.ProductionPlanning.Common.BusinessComponents</AssemblyName>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFramework>net8.0</TargetFramework>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <TargetFrameworkProfile />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.ProductionPlanning.Common.BusinessComponents.xml</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="Resources\dateshift-invocation.js" />
    <None Remove="Resources\lodash.js" />
    <None Remove="Resources\moment.js" />
    <None Remove="Resources\platform-dateshift-service.js" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CodeGenerator\CreationDataWithExistedCodes.cs" />
    <Compile Include="CodeGenerator\LocationInfoGenerator.cs" />
    <Compile Include="CodeGenerator\NumberSequenceCodeBasedOnExistedCountGenerator.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Configuration\PpsHeaderCostGroupConfiguration.cs" />
    <Compile Include="Configuration\PpsProduct2CostGroupConfiguration.cs" />
    <Compile Include="Connected Services\TimServiceReference\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Constants\CommonBizPartnerConstants.cs" />
    <Compile Include="Constants\EPpsDateshiftModes.cs" />
    <Compile Include="Constants\EPpsDocumentType.cs" />
    <Compile Include="Constants\PpsCalendarModes.cs" />
    <Compile Include="Entities\EntitiesForDateShift.cs" />
    <Compile Include="Entities\CommonBizPartnerComplete.cs" />
    <Compile Include="Entities\CommonBizPartnerContactEntity.cs" />
    <Compile Include="Entities\CommonBizPartnerEntity.cs" />
    <Compile Include="Entities\DdTempIdsEntity.Generated.cs">
      <DependentUpon>DdTempIdsEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\EventProjectFkSpEntity.cs" />
    <Compile Include="Entities\PpsBillingDataProductVEntity.cs" />
    <Compile Include="Entities\PpsBillingDataProductVEntity.Generated.cs" />
    <Compile Include="Entities\PpsCalendarForSiteEntity.cs" />
    <Compile Include="Entities\PpsCalendarForSiteEntity.Generated.cs" />
    <Compile Include="Entities\PpsCalendarIdEntity.cs" />
    <Compile Include="Entities\PpsDocAnnotextConfigEntity.cs" />
    <Compile Include="Entities\PpsDocAnnotextConfigEntity.Generated.cs" />
    <Compile Include="Entities\PpsGetEventCodeEntity.cs" />
    <Compile Include="Entities\PpsGetEventCodeEntity.Generated.cs" />
    <Compile Include="Entities\PpsGetEventProjectEntity.cs" />
    <Compile Include="Entities\PpsGetEventProjectEntity.Generated.cs" />
    <Compile Include="Entities\PpsEvent2resrequisitionVEntity.cs" />
    <Compile Include="Entities\PpsEvent2resrequisitionVEntity.Generated.cs" />
    <Compile Include="Entities\PpsItemVEntity.cs" />
    <Compile Include="Entities\PpsItemVEntity.Generated.cs" />
    <Compile Include="Entities\PrjLocationInfoSPEntity.cs" />
    <Compile Include="Entities\EstLineitem2PpsProductEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\EstLineitem2PpsProductEntity.Generated.cs">
      <DependentUpon>EstLineitem2PpsProductEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\EventEntity.Generated.cs">
      <DependentUpon>EventEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\GenericDocumentComplete.cs" />
    <Compile Include="Entities\GenericDocumentEntity.cs" />
    <Compile Include="Entities\GenericDocumentRevisionEntity.cs" />
    <Compile Include="Entities\GenericEventEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\GenericEventEntity.Generated.cs">
      <DependentUpon>GenericEventEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\Header2ClerkEntity.Generated.cs">
      <DependentUpon>Header2ClerkEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\HeaderEntity.Generated.cs">
      <DependentUpon>HeaderEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\HeaderGroupEntity.Generated.cs">
      <DependentUpon>HeaderGroupEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\HeaderStructureVEntity.Generated.cs">
      <DependentUpon>HeaderStructureVEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\Item2EventEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\Item2EventEntity.Generated.cs">
      <DependentUpon>Item2EventEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PesItemPpsUpstrmIdVEntity.cs" />
    <Compile Include="Entities\PesItemPpsUpstrmIdVEntity.Generated.cs" />
    <Compile Include="Entities\PpsDatevDocumentVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsDatevDocumentVEntity.Generated.cs">
      <DependentUpon>PpsDatevDocumentVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsDatevInvoiceInVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsDatevInvoiceInVEntity.Generated.cs">
      <DependentUpon>PpsDatevInvoiceInVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsDatevInvoiceOutVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsDatevInvoiceOutVEntity.Generated.cs">
      <DependentUpon>PpsDatevInvoiceOutVEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsDocumentEntity.Generated.cs">
      <DependentUpon>PpsDocumentEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsDocumentRelationEntity.Generated.cs">
      <DependentUpon>PpsDocumentRelationEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsDocumentrevisionEntity.Generated.cs">
      <DependentUpon>PpsDocumentrevisionEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsDocumentTypeEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsDocumentTypeEntity.Generated.cs">
      <DependentUpon>PpsDocumentTypeEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsJobProductInfoVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsJobProductInfoVEntity.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>PpsJobProductInfoVEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsLogEntryEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsLogEntryEntity.Generated.cs">
      <DependentUpon>PpsLogEntryEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsLogReasonEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsLogReasonEntity.Generated.cs">
      <DependentUpon>PpsLogReasonEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsLogReasonGroupEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsLogReasonGroupEntity.Generated.cs">
      <DependentUpon>PpsLogReasonGroupEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsLogReasonLookupVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsLogReasonLookupVEntity.Generated.cs">
      <DependentUpon>PpsLogReasonLookupVEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsLogReportTreeFilterVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsLogReportTreeFilterVEntity.Generated.cs">
      <DependentUpon>PpsLogReportTreeFilterVEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsLogReportVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsLogReportVEntity.Generated.cs">
      <DependentUpon>PpsLogReportVEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\PpsOutgoingInvoiceDocumentsExportVEntity.cs" />
    <Compile Include="Entities\PpsOutgoingInvoiceExportVEntity.cs" />
    <Compile Include="Entities\PpsPhaseEventRelationVEntity.cs" />
    <Compile Include="Entities\PpsPhaseEventRelationVEntity.Generated.cs" />
    <Compile Include="Entities\PpsProductFormDataEntity.cs" />
    <Compile Include="Entities\PpsProductFormDataEntity.Generated.cs" />
    <Compile Include="Entities\PpsProductForRouteVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsProductForRouteVEntity.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>PpsProductForRouteVEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\ProductCalculationEntity.cs" />
    <Compile Include="Entities\PpsProductOrientationEntity.cs" />
    <Compile Include="Entities\PpsProductOrientationEntity.Generated.cs" />
    <Compile Include="Entities\PpsProductionSetEventVEntity.cs" />
    <Compile Include="Entities\PpsProductionSetEventVEntity.Generated.cs" />
    <Compile Include="Entities\PpsProductPlannedDateVEntity.cs" />
    <Compile Include="Entities\PpsProductPlannedDateVEntity.Generated.cs" />
    <Compile Include="Entities\PpsProjectBaseVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsProjectBaseVEntity.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>PpsProjectBaseVEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsTriggerHistoryEntity.cs" />
    <Compile Include="Entities\PpsTriggerHistoryEntity.Generated.cs" />
    <Compile Include="Entities\PpsEmployeeAssignmentEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsEmployeeAssignmentEntity.Generated.cs">
      <DependentUpon>PpsEmployeeAssignmentEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\Product2AnnotationEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\Product2AnnotationEntity.Generated.cs">
      <DependentUpon>Product2AnnotationEntity.cs</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\SiteJobVEntity.cs" />
    <Compile Include="Entities\SiteJobVEntity.Generated.cs" />
    <Compile Include="Entities\TrsBundlePpsDocumentVEntity.cs" />
    <Compile Include="Entities\TrsBundlePpsDocumentVEntity.Generated.cs" />
    <Compile Include="Entities\TrsRequisitionEventVEntity.cs" />
    <Compile Include="Entities\TrsRequisitionEventVEntity.Generated.cs" />
    <Compile Include="Entities\TrsWaypointVEntity.cs" />
    <Compile Include="Entities\TrsWaypointVEntity.Generated.cs" />
    <Compile Include="Facade\WebServiceJobFacade.cs" />
    <Compile Include="Handler\PpsFormulaScriptHandler.cs" />
    <Compile Include="InheritStatus\InheritStatusCollectorHelper.cs" />
    <Compile Include="InheritStatus\PPSStatusTriggerRuleExecutor.cs" />
    <Compile Include="Interface\IMaterialReservationSystemOption.cs" />
    <Compile Include="Logic\Boq\PpsBoqLogic.cs" />
    <Compile Include="Logic\EmployeeAssign\PpsEmploeddAssignmentLogic.IDataBaseLogic.cs" />
    <Compile Include="Logic\EmployeeAssign\PpsEmployeeAssignmentLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Dtos\PatchMesProductDto.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\EntityLogic\MesPatchProductController.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\EntityLogic\MesPatchProductLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Enums\PpsProductOrientation.cs" />
    <Compile Include="Logic\ExternalSystem\Tim\Config\PrecastTimConfigDto.cs" />
    <Compile Include="Entities\ProductEntity.Generated.cs">
      <DependentUpon>ProductEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\ProductHistoryEntity.cs" />
    <Compile Include="Entities\ProductParamEntity.Generated.cs">
      <DependentUpon>ProductParamEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\ProductSPEntity.cs" />
    <Compile Include="Entities\ProductTransportHistoryVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ProductTransportHistoryVEntity.Generated.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>ProductTransportHistoryVEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\TranslationEntity.Generated.cs">
      <DependentUpon>TranslationEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\WebServiceJobEntity.Generated.cs">
      <DependentUpon>WebServiceJobEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="Entities\WebserviceJobs2ProductVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsEventRelationEntity.cs" />
    <Compile Include="Entities\WebserviceJobs2ProductVEntity.Generated.cs">
      <DependentUpon>WebserviceJobs2ProductVEntity.cs</DependentUpon>
      <SubType>Code</SubType>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="EntityModel\EntityModel.ModelBuilder.cs">
      <SubType>Code</SubType>
      <DependentUpon>EntityModel.edml</DependentUpon>
      <AutoGen>True</AutoGen>
    </Compile>
    <Compile Include="EntityModel\ModelBuilder.cs" />
    <Compile Include="Handler\DimensionFormulaHandler.cs" />
    <Compile Include="Handler\QuantityFormulaHandler.cs" />
    <Compile Include="Handler\DimensionFormulaJavaScriptHandler.cs" />
    <Compile Include="Handler\QuantityFormulaJavaScriptHandler.cs" />
    <Compile Include="Logic\BizPartner\CommonBizPartnerContactLogic.cs" />
    <Compile Include="Logic\BizPartner\CommonBizPartnerLogic.cs" />
    <Compile Include="Logic\BizPartner\ContactEntityExtension.cs" />
    <Compile Include="Logic\BizPartner\ContactHandlerBase.cs" />
    <Compile Include="Logic\BizPartner\PpsHeader2ContactHandler.cs" />
    <Compile Include="Logic\BizPartner\MntReq2ContactHandler.cs" />
    <Compile Include="Logic\BizPartner\Prj2ContactHandler.cs" />
    <Compile Include="Logic\CreateBillHelper.cs" />
    <Compile Include="Logic\Document\PpsProductDocLinkModelObjHandler.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Config\ConfigBase.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Config\IConfig.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Controller\LoginControllerBase.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Controller\ControllerBase.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Controller\EntityUpdateControllerBase.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Dtos\ServiceStatusDto.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Logic\LoginLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Logic\EntityUpdateLogicBase.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Logic\LogicBase.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Session\SessionBase.cs" />
    <Compile Include="Logic\ExternalSystem\Base\Session\SessionProviderBase.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Dtos\GetMesProductDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Config\SceConfig.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Config\SceEndpoints.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Controllers\SceDeliveryUnitController.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Controllers\SceDeliveryNoteController.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Controllers\SceCarrierController.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Controllers\SceArticleController.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Dtos\SceDeliveryResourceDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Dtos\SceDeliveryUnitArticlesDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Dtos\SceDeliveryUnitItemDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Dtos\SceDeliveryUnitItemImportDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Dtos\SceDimensionDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Dtos\SceDeliveryNoteDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Dtos\SceCarrierDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Dtos\SceArticleDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Dtos\SceDeliveryUnitDto.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Enums\ArticleType.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Enums\DeliveryRelease.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Logic\SceDeliveryUnitLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Logic\SceDeliveryNoteLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Logic\SceCarrierLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Logic\SceArticleLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Session\SceSession.cs" />
    <Compile Include="Logic\ExternalSystem\Sce\Session\SceSessionProvider.cs" />
    <Compile Include="Logic\ExternalSystem\Tim\Config\TimConfig.cs" />
    <Compile Include="Logic\ExternalSystem\Tim\Logic\TimLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Tim\Session\TimSession.cs" />
    <Compile Include="Logic\ExternalSystem\Tim\Session\TimSessionProvider.cs" />
    <Compile Include="Logic\ExternalSystem\Tim\Logic\TimController.cs" />
    <Compile Include="Logic\GenericDocumentHelper.cs" />
    <Compile Include="Logic\GenericDocumentRevisionHelper.cs" />
    <Compile Include="Logic\Header\ProductionPlanningCommonHeaderLookupLogicV3.cs" />
    <Compile Include="Logic\HintInterceptor\HintInterceptors.cs" />
    <Compile Include="Logic\HintInterceptor\HintTypes\HintBase.cs" />
    <Compile Include="Logic\HintInterceptor\HintTypes\HintForceOrder.cs" />
    <Compile Include="Logic\HintInterceptor\HintTypes\HintHoldLock.cs" />
    <Compile Include="Logic\HintInterceptor\HintTypes\HintUpdLock.cs" />
    <Compile Include="Logic\Log\PpsCommonLogReportLogic.LogsPinboard.cs" />
    <Compile Include="Logic\PpsCalendarForSiteLogic.cs" />
    <Compile Include="Logic\PpsCalendarForSiteLogic.IDataBaseLogic.cs" />
    <Compile Include="Logic\PpsDocAnnotextConfigLogic.cs" />
    <Compile Include="Logic\PpsEventEntityDateShifter.cs" />
    <Compile Include="Logic\PpsEventSequenceHelper.cs" />
    <Compile Include="Logic\MdlModelPropertyValueHelper.cs" />
    <Compile Include="Logic\PpsProduct2AnnotationLogic.cs" />
    <Compile Include="Logic\PpsProductFormDataLogic.cs" />
    <Compile Include="Logic\PpsEventEntitiesCodeProcessor.cs" />
    <Compile Include="Logic\PpsEventEntitiesProjectFkProcessor.cs" />
    <Compile Include="Logic\PpsJobProductInfoVLogic.cs" />
    <Compile Include="Logic\PesConAcquisitionLogic\PesConAcquisitionLogic.cs" />
    <Compile Include="Logic\PpsCommonEstLineitem2PpsProductLogic.cs" />
    <Compile Include="Logic\Log\PpsLogReportVFilterRequest.cs" />
    <Compile Include="Logic\GroupingFilterExtensionHelper.cs" />
    <Compile Include="Logic\Log\PpsCommonLogReportLogic.cs" />
    <Compile Include="Logic\PpsGenericEventLogic.cs" />
    <Compile Include="Logic\PpsModelFilterLogic.cs" />
    <Compile Include="Logic\PpsPhaseEventRelationVLogic.cs" />
    <Compile Include="Logic\PpsProductFilter.cs" />
    <Compile Include="Logic\Log\PpsLogEntryLogic.cs" />
    <Compile Include="Logic\Log\PpsLogReasonLookupVLogic.cs" />
    <Compile Include="Logic\PesItemPpsUpStreamIDVLogic.cs" />
    <Compile Include="Logic\PpsProductOrientationLogic.cs" />
    <Compile Include="Logic\PpsProjectBaseVLogic.cs" />
    <Compile Include="Logic\PpsTriggerHistoryLogic.cs" />
    <Compile Include="Logic\DateshiftLogic.cs" />
    <Compile Include="Logic\ProductionPlanningCommonEventLogic.Dateshift.cs" />
    <Compile Include="Logic\ProductionPlanningCommonEventLogic.IEntityFacade.cs" />
    <Compile Include="Logic\ProductionPlanningCommonEventLogic.IPpsEventLogic.cs" />
    <Compile Include="Logic\ProductionPlanningCommonEventLogic.IDataBaseLogic.cs" />
    <Compile Include="Logic\ProductionPlanningCommonEventLogic.IPpsManualLogHandler.cs" />
    <Compile Include="Logic\ProductionPlanningCommonEventLogic.SyncRootProdSets.cs" />
    <Compile Include="Logic\ProductionPlanningCommonItem2EventLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Dtos\MesAttachmentDto.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\EntityLogic\MesAttachmentController.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\EntityLogic\MesAttachmentLogic.cs" />
    <Compile Include="Entities\ProductWithStockInfoEntity.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.Lookup.BillingData.cs" />
    <Compile Include="Logic\Product\PpsCommonProductHistoryLogic.cs" />
    <Compile Include="Logic\Product\PpsCommonProductLogic.RelocateStock.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.PhaseDateSlot.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.PhaseReqSlot.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.Recalculate.cs" />
    <Compile Include="Entities\StockProductInfo.cs" />
    <Compile Include="Logic\Product\Stock\ProductWithStockInfoLogic.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.IGroupingEntityProvider.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.IPpsProductLogic.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.Filter.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.EventTypeSlot.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.EntitiesProcess.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductStatusLogic.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.IEntityFacade.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.ITransportGoodsLogic.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.Lookup.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.Reproduction.cs" />
    <Compile Include="CodeGenerator\NumberSequenceCodeGenerator.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Logic\Product\Stock\StockProductLogic.cs" />
    <Compile Include="Logic\Product\Stock\StockTransactionCreationData.cs" />
    <Compile Include="Logic\RelationInfo\PpsDocumentRelationRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PpsLogEntryRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PpsProductRelationInfo.cs" />
    <Compile Include="Logic\ScheduleTask\Base\ConfigurationBase.cs" />
    <Compile Include="Logic\ScheduleTask\Base\JobProcessorBase.cs" />
    <Compile Include="Logic\ScheduleTask\ImportProjectDocumentsTask.cs" />
    <Compile Include="Logic\ScheduleTask\MesScheduleTasks\MesConfiguration.cs" />
    <Compile Include="Logic\ScheduleTask\MesScheduleTasks\iTWOMES\MesJobProcessor.cs" />
    <Compile Include="Logic\ScheduleTask\MesScheduleTasks\MesScheduleTaskBase.cs" />
    <Compile Include="Logic\ScheduleTask\MesScheduleTasks\MesUserDefinedField.cs" />
    <Compile Include="Logic\ScheduleTask\MesScheduleTasks\Unitechnik\UnitechnikTransferTask.cs" />
    <Compile Include="Logic\ScheduleTask\SceScheduleTasks\iTWOSCE\SceJobProcessor.cs" />
    <Compile Include="Logic\ScheduleTask\SceScheduleTasks\iTWOSCE\TransferToSceTask.cs" />
    <Compile Include="Logic\ScheduleTask\SceScheduleTasks\SceConfiguration.cs" />
    <Compile Include="Logic\ScheduleTask\SceScheduleTasks\SceScheduleTaskBase.cs" />
    <Compile Include="Logic\ScheduleTask\StatusTriggerConsumerTask.cs" />
    <Compile Include="Logic\ScheduleTask\TimScheduleTasks\TransferToTimTask.cs" />
    <Compile Include="Logic\ScheduleTask\TriggerWorkflowsTask.cs" />
    <Compile Include="Logic\SiteJobVLogic.cs" />
    <Compile Include="Logic\Slot\DateSlotHelpLogicBase.cs" />
    <Compile Include="Logic\Slot\PhaseDateSlotHelpLogic.cs" />
    <Compile Include="Logic\Slot\PhaseReqSlotHelpLogic.cs" />
    <Compile Include="Logic\Slot\PlannedQuantitySlotHelpLogic.cs" />
    <Compile Include="Logic\Slot\SlotHelpLogicBase.cs" />
    <Compile Include="Logic\SplittingSaveHelper.cs" />
    <Compile Include="Logic\SpecificEntityHelper.cs" />
    <Compile Include="Logic\TransportInfoHelper.cs" />
    <Compile Include="Util\BingMapHelper.cs" />
    <Compile Include="CodeGenerator\PpsDefaultCodeGenerator.cs" />
    <Compile Include="CodeGenerator\PpsDerivedEventCodeGenerator.cs" />
    <Compile Include="CodeGenerator\RubricCodeGenerator.cs" />
    <Compile Include="Configuration\StatusWorkflowConfigurationProvider.cs" />
    <Compile Include="Constants\PermissionDescriptors.cs" />
    <Compile Include="Entities\DdTempIdsEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\EventComplete.cs" />
    <Compile Include="Entities\EventEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\Header2ClerkEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\HeaderEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\HeaderGroupEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\HeaderStructureVEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PlanningboardConfig.cs" />
    <Compile Include="Entities\PpsDocumentCompleteEntity.cs" />
    <Compile Include="Entities\PpsDocumentEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsDocumentRelationEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\PpsDocumentrevisionEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ProductCollectionInfo.cs" />
    <Compile Include="Entities\ProductComplete.cs" />
    <Compile Include="Entities\ProductEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\ProductParamEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\TranslationEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Entities\WebServiceJobEntity.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Entities</DependentUpon>
    </Compile>
    <Compile Include="Interface\IExternalSession.cs" />
    <Compile Include="Logic\Slot\ClerkRoleSlotHelpLogic.cs" />
    <Compile Include="Logic\Slot\EventTypeSlotHelpLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Base\ExternalCommunicationBase.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\MesLogicBase.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Authentication\BasicsController.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Authentication\BasicsLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Dtos\PutMesProductDto.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Entities\MesEndpoints.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\EntityLogic\MesProductLogic.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\MesEntityUpdateControllerBase.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\MesControllerBase.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\EntityLogic\MesProductController.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\MesEntityUpdateLogicBase.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Session\MesSession.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Session\MesSessionProvider.cs" />
    <Compile Include="Logic\Header\HeaderGroupLogic.cs" />
    <Compile Include="Util\CharacteristicDataHepler.cs" />
    <Compile Include="Util\FiveDTrackingFlags.cs" />
    <Compile Include="Util\FormulaScriptContextHelperBase.cs" />
    <Compile Include="Util\MaterialReservationSystemOption.cs" />
    <Compile Include="Util\MaterialReservationSystemOptionHelper.cs" />
    <Compile Include="Util\PpsDateshiftRelationsUpdateEntity.cs" />
    <Compile Include="Util\PpsPermissionCheckControl.cs" />
    <Compile Include="Util\RouteInfoJsonConverter.cs" />
    <Compile Include="Util\PpsCalendarUtilities.cs" />
    <Compile Include="Util\PpsDateshiftDataEntity.cs" />
    <Compile Include="Util\SerialProductionDateCalculator.cs" />
    <Compile Include="Workflow\CreateConContractWorkflowAction.cs" />
    <Compile Include="Workflow\CreateDocumentWorkflowAction.cs" />
    <Compile Include="Workflow\CreatePesHeaderItemWorkflowAction.cs" />
    <Compile Include="Workflow\CreateReturnDispatchingOnRouteEvent.cs" />
    <Compile Include="Workflow\DateChangeOnWaypointEventHandler.cs" />
    <Compile Include="Workflow\DateChangeOnWaypointEvent.cs" />
    <Compile Include="Workflow\DateChangeOnRouteEvent.cs" />
    <Compile Include="Workflow\DateChangeOnRouteEventHandler.cs" />
    <Compile Include="Workflow\Datev\CreateDatevXmlInvoiceInAction.cs" />
    <Compile Include="Workflow\Datev\CreateDatevXmlInvoiceOutAction.cs" />
    <Compile Include="Workflow\Datev\DatevHelper.cs" />
    <Compile Include="Workflow\Datev\Entities\LedgerImport.cs" />
    <Compile Include="Workflow\Datev\Entities\MainDocument.cs" />
    <Compile Include="Workflow\ExecutePublicApiAction.cs" />
    <Compile Include="Workflow\GetJobForProductionPlanningAction.cs" />
    <Compile Include="Workflow\OnMesTransferFailedEvent.cs" />
    <Compile Include="Workflow\ProductionDateChangeEvent.cs" />
    <Compile Include="Workflow\RunDateShiftAction.cs" />
    <Compile Include="Workflow\SetProductsStockWorkflowAction.cs" />
    <Compile Include="Workflow\SyncDispatchingOnRouteEvent.cs" />
    <Compile Include="Workflow\UpdateBoqsOfWipByDispatchRecordsAction.cs" />
    <Compile Include="Workflow\WebServiceJobWorkflowAction.cs" />
    <Compile Include="Logic\Document\PpsDocumentLogic.cs" />
    <Compile Include="Logic\Document\PpsDocumentRelationLogic.cs" />
    <Compile Include="Logic\Document\PpsDocumentRevisionLogic.cs" />
    <Compile Include="Logic\ProductionPlanningCommonEventLogic.cs" />
    <Compile Include="Logic\Header\ProductionPlanningCommonHeaderLookupLogic.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.IDataBaseLogic.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.cs" />
    <Compile Include="Logic\Header\ProductionPlanningCommonHeaderStructureLogic.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductParamLogic.cs" />
    <Compile Include="Logic\RelationInfo\PpsDocumentRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\PpsDocumentRevisionRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\ProductionPlanningCommonEventRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\ProductionPlanningCommonProductRelationInfo.cs" />
    <Compile Include="Logic\RelationInfo\ProductionPlanningCommonHeaderRelation.cs" />
    <Compile Include="Logic\ExternalSystem\Mes\Entities\MesConfig.cs" />
    <Compile Include="Logic\ScheduleTask\MesScheduleTasks\iTWOMES\TransferToMesTask.cs" />
    <Compile Include="Logic\WebServiceJobLogic.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Logic\LogisticJobHelper.cs" />
    <Compile Include="Util\PpsLoggableHelper.cs" />
    <Compile Include="Util\StockTransactionHelper.cs" />
    <Compile Include="Workflow\AutoSiteSelectionAction.cs" />
    <Compile Include="Workflow\CreateJobForOrderAction.cs" />
    <Compile Include="Workflow\WorkflowContextHelper.cs" />
    <Compile Include="Workflow\WorkflowHandler.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Basics.Common.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.ServiceFacade.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessEnvironment">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessEnvironment.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Characteristic.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Company.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Company.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Common.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Services.Infrastructure.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Infrastructure.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Services.Infrastructure.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Infrastructure.Common.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Customize.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Customize.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Workflow.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Workflow.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Workflow.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Workflow.Core.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.ProductionPlanning.Configuration.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.ProductionPlanning.Configuration.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Productionplanning.StrandPattern.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Productionplanning.StrandPattern.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Unit.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Unit.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Common.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Services.Scheduler.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Scheduler.Core.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.CostGroups.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.CostGroups.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.ProductionPlanning.Configuration.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.ProductionPlanning.Configuration.Core.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Clerk.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Clerk.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Reporting.Platform.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Reporting.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Reporting.Platform.ServiceDomain">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Reporting.Platform.ServiceDomain.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Reporting.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Reporting.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Server.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Server.Common.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Syndication">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ServiceModel.Syndication.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Common, Version=4.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Drawing.Common.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents, Version=5.1.229.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.Functions, Version=1.5.0.0, Culture=neutral, PublicKeyToken=dbe58f97a0872a64, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.Functions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.MaterialCatalog.BusinessComponents, Version=6.2.93.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.MaterialCatalog.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Site.BusinessComponents, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Site.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="ClearScript.Core, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\ClearScript.Core.dll</HintPath>
    </Reference>
    <Reference Include="ClearScript.V8, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\ClearScript.V8.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Services.Scheduler.BusinessComponents, Version=**********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Scheduler.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Services.Scheduler.Common, Version=**********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Scheduler.Common.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Services.Scheduler.Core, Version=**********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Scheduler.Core.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Common.Common\RIB.Visual.ProductionPlanning.Common.Common.csproj">
      <Project>{B4288A3B-3490-4108-A395-E0273EBC1278}</Project>
      <Name>RIB.Visual.ProductionPlanning.Common.Common</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Common.Core\RIB.Visual.ProductionPlanning.Common.Core.csproj">
      <Project>{61DA7E05-5C48-478E-BEF1-85721AB57C2F}</Project>
      <Name>RIB.Visual.ProductionPlanning.Common.Core</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Common.Localization\RIB.Visual.ProductionPlanning.Common.Localization.csproj">
      <Project>{D3A2D635-F845-4518-8BF7-A9BE67A68F5A}</Project>
      <Name>RIB.Visual.ProductionPlanning.Common.Localization</Name>
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <DevartEntityDeploy Include="EntityModel\EntityModel.edml">
      <Generator>DevartEfGenerator</Generator>
      <LastGenOutput>EntityModel.info</LastGenOutput>
      <SubType>Designer</SubType>
    </DevartEntityDeploy>
    <None Include="App.config" />
    <None Include="Connected Services\TimServiceReference\NmWsGeneral.wsdl" />
    <None Include="EntityModel\CreateT4.Links.Cmd" />
    <None Include="EntityModel\DataTransferObject.T4" />
    <None Include="EntityModel\DbContext.T4" />
    <None Include="EntityModel\EntityModel.edps">
      <DependentUpon>EntityModel.edml</DependentUpon>
      <SubType>Designer</SubType>
    </None>
    <None Include="EntityModel\EntityModel.MainDiagram.view">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\Validation.T4" />
    <Compile Include="Logic\ProductionPlanningCommonEventLogic.Lookup.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.Validation.cs" />
    <Compile Include="Logic\Product\ProductionPlanningCommonProductLogic.SerialProduction.cs" />
    <None Include="RIBvisual.snk">
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Common.MetaModel.xml" />
    <None Include="Connected Services\TimServiceReference\configuration91.svcinfo" />
    <None Include="Connected Services\TimServiceReference\configuration.svcinfo" />
    <None Include="Connected Services\TimServiceReference\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <Content Include="EntityModel\.MetaModel.xml">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\dateshift-invocation.js" />
    <EmbeddedResource Include="Resources\lodash.js" />
    <EmbeddedResource Include="Resources\moment.js" />
    <EmbeddedResource Include="Resources\platform-dateshift-service.js" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Connected Services\TimServiceReference\" />
  </ItemGroup>
  <ItemGroup>
    <None Update="EntityModel\EntityModel.info">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="System.Runtime.Caching" />
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
  <Target Name="PreBuild" BeforeTargets="PreBuildEvent">
    <Exec Command="call &quot;$(ProjectDir)\_make.dateshift.angular.links.cmd&quot;" />
  </Target>
</Project>