/**
 * Created by hzh on 6/25/2025.
 */

(function (angular) {
	'use strict';

	const moduleName = 'documents.centralquery';

	angular.module(moduleName).controller('documentsCentralqueryShareViaSharepointDialogController',
		['_', 'globals', '$scope', '$translate', '$http', 'platformGridAPI',
			'platformGridControllerService', '$timeout', 'documentsCentralqueryShareViaSharepointDialogService',
			'documentsCentralquerySpConfigurationService', 'documentsCentralquerySpFolderTemplateContantService', 'platformDialogService',
			'cloudDesktopSharePointMainService', 'documentCentralQueryDataService',
			function (_, globals, $scope, $translate, $http, platformGridAPI,
			          gridControllerService, $timeout, shareViaSharepointDialogService, spConfigurationService,
			          spFolderTemplateContantService, platformDialogService,
					  sharepointMainService, documentCentralQueryDataService) {
				let datas = $scope.options.selections;
				let isShared = false;

				$scope.data = [];
				$scope.gridId = '19550b4881584bceb834727bbff313d1';

				const gridColumns = {
					getStandardConfigForListView: function () {
						return {
							columns: [
								{
									id: 'FileType',
									field: 'FileType',
									name: 'Document Type',
									name$tr$: 'cloud.common.entityDocumentType',
									width: 120,
									readonly: true
								},
								{
									id: 'OriginFileName',
									field: 'OriginFileName',
									name: 'Document Name',
									name$tr$: 'cloud.common.documentDescription',
									width: 240,
									readonly: true
								},
								{
									id: 'ProjectName',
									field: 'ProjectName',
									name: 'ProjectName',
									name$tr$: 'documents.centralquery.sharepoint.columns.projectName',
									width: 240,
									readonly: true,
								},
								{
									id: 'Description',
									field: 'Description',
									name: 'FolderTemplate',
									name$tr$: 'cloud.common.entityDescription',
									width: 390
								}
							]
						};
					}
				};

				$scope.sendInfo = {
					users: [],
					shareOption: spFolderTemplateContantService.shareOptionsEnum.canView,
					message: ''
				};

				$scope.modalOptions = {
					header: {
						title: $translate.instant('documents.centralquery.sharepoint.shareViaSharePointTitle')
					},
					body: {
						selectedDocument: $translate.instant('documents.centralquery.sharepoint.shareViaSharepoint.selectedDocument'),
						addAssignment: $translate.instant('documents.centralquery.sharepoint.shareViaSharepoint.addAssignment'),
						userAssignment: $translate.instant('documents.centralquery.sharepoint.folderTemplate.userAssignment'),
						shareOptions: $translate.instant('documents.centralquery.sharepoint.folderTemplate.shareOptions'),
						message: $translate.instant('documents.centralquery.sharepoint.shareViaSharepoint.message')
					},
					footer: {
						send: $translate.instant('documents.centralquery.sharepoint.shareViaSharepoint.send'),
						btnCancel: $translate.instant('basics.common.button.cancel')
					},
					ok: function () {
						$scope.isLoading = true;

						const prjIds = Array.from(new Set(_.map(datas, item => {
							return item.PrjProjectFk;
						})));
						const docIds = _.map(datas, item => {
							return item.Id;
						});

						let aadUsers = spConfigurationService.aadUsers;
						let tempUsers = [];
						let spUsers = [];
						const usersAssigned = $scope.sendInfo.users;
						_.forEach(usersAssigned, function (userAssigned) {
							let users = _.filter(tempUsers, {Id: userAssigned.Id});
							if (users.length === 0) {
								users = _.filter(aadUsers, {Id: userAssigned});
								tempUsers.push(users[0]);
							}
							spUsers.push(users[0]);
						});

						let profile = {
							ShareOption: $scope.sendInfo.shareOption,
							AadUsers: spUsers,
							Message: $scope.sendInfo.message
						};

						let data = {
							ProjectIds: prjIds,
							DocIds: docIds,
							Profile: profile
						};
						sharepointMainService.msalClient.acquireTokenAsync().then(function (r) {
							data.AccessToken = r;
							$http.post(globals.webApiBaseUrl + 'documents/centralquery/sharepoint/shareviasharepoint', data)
								.then(function (response) {
									let msg = $translate.instant('documents.centralquery.sharepoint.shareViaSharePointCompleted');
									platformDialogService.showDialog({
										headerText$tr$: '',
										iconClass: 'info',
										bodyText: msg,
										showOkButton: true
									}).then(function () {
										if (response.data) {
											$scope.$close(false);
										}
									});
								})
								.finally(function () {
									$scope.isLoading = false;
									isShared = true;
								});
						});
					},
					cancel: function () {
						$scope.$close(false);
					}
				};

				$scope.userAssignment = {
					options: {
						multipleSelection: true,
						showFilterRow: false
					}
				};

				let page = {
					enabled: true,
					size: 10,
					number: 0,
					count: 0,
					currentLength: 0,
					totalLength: 0
				};

				page.totalLength = datas.length;
				page.count = Math.ceil(page.totalLength / page.size);

				$scope.getPageText = function () {
					let startIndex = page.number * page.size,
						endIndex = ((page.count - (page.number + 1) > 0 ? startIndex + page.size : page.totalLength));
					if (page.totalLength > 0) {
						return (startIndex + 1) + ' - ' + endIndex + ' / ' + (page.hasMore ? page.totalLength + '+' : page.totalLength);
					}
					return (startIndex + 1) + ' - ' + endIndex + ' / ' + (page.hasMore ? page.totalLength + '+' : page.totalLength);
				};
				$scope.getFirstPage = function () {
					page.number = 0;
					getSearchList();
				};
				$scope.getLastPage = function () {
					page.number = page.count - 1;
					getSearchList();
				};
				$scope.getPrevPage = function () {
					if (page.number <= 0) {
						return;
					}
					page.number--;
					getSearchList();
				};
				$scope.getNextPage = function () {
					if (page.count <= page.number) {
						return;
					}
					page.number++;
					getSearchList();
				};

				$scope.canFirstPage = $scope.canPrevPage = function () {
					return page.number > 0;
				};
				$scope.canLastPage = $scope.canNextPage = function () {
					return page.count > (page.number + 1);
				};

				function getSearchList() {
					let startIndex = page.number * page.size,
						endIndex = ((page.count - (page.number + 1) > 0 ? startIndex + page.size : page.totalLength));

					let data = datas.slice(startIndex, endIndex);

					shareViaSharepointDialogService.setDataList(data);
					getDataSource(data);
				}

				// set data to grid
				function setDataSource() {
					$scope.isLoading = true;

					getSearchList();

					$scope.isLoading = false;
				}

				function getDataSource() {
					shareViaSharepointDialogService.refreshGrid();
					$scope.onContentResized();
				}

				const gridConfig = {
					initCalled: false,
					columns: []
				};

				$scope.setTools = function () {
				};

				$scope.tools = {
					update: () => {
					}
				};

				$scope.onContentResized = function () {
					resizeGrid();
				};

				function resizeGrid() {
					$timeout(function () {
						platformGridAPI.grids.resize($scope.gridId);
					});
				}

				if (platformGridAPI.grids.exist($scope.gridId)) {
					platformGridAPI.grids.unregister($scope.gridId);
				}

				shareViaSharepointDialogService.refreshData.register(setDataSource);

				gridControllerService.initListController($scope, gridColumns, shareViaSharepointDialogService, null, gridConfig);

				// resize the grid
				$timeout(function () { // use timeout to do after the grid instance is finished
					platformGridAPI.grids.resize($scope.gridId);
				});

				$scope.$on('$destroy', function () {
					platformGridAPI.grids.unregister($scope.gridId);

					shareViaSharepointDialogService.refreshData.unregister(setDataSource);
					if (isShared) {
						documentCentralQueryDataService.refresh();
					}
				});

				init();

				function init() {
					setDataSource();
					spConfigurationService.getAadUsers();
				}
			}]);
})(angular);
