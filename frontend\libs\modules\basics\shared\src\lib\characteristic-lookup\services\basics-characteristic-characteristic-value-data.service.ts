import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { PlatformConfigurationService, PlatformLazyInjectorService, ServiceLocator } from '@libs/platform/common';
import { BASICS_CHARACTERISTIC_DISCRETE_VALUE_DATA_PROVIDER, ICharacteristicValueEntity } from '@libs/basics/interfaces';
import { BasicsSharedCharacteristicDataDiscreteValueLookupService } from './basics-characteristic-data-discrete-value-lookup.service';

@Injectable({
	providedIn: 'root',
})
export class BasicsSharedCharacteristicCharacteristicValueDataService {
	protected http = inject(HttpClient);
	protected configurationService = inject(PlatformConfigurationService);
	protected queryPath = this.configurationService.webApiBaseUrl + 'basics/characteristic/discretevalue/list?mainItemId=';
	private discreteValueLookupService = ServiceLocator.injector.get(BasicsSharedCharacteristicDataDiscreteValueLookupService);
	private readonly lazyInjector = ServiceLocator.injector.get(PlatformLazyInjectorService);

	public async getListAsync(characteristicId: number): Promise<ICharacteristicValueEntity[]> {
		const discreteValueService = await this.lazyInjector.inject(BASICS_CHARACTERISTIC_DISCRETE_VALUE_DATA_PROVIDER);
		if (discreteValueService.getSelectedCharacteristicId() !== characteristicId || !discreteValueService.getList()?.length) {
			await discreteValueService.loadSubEntities({ id: 0, pKey1: characteristicId });
		}

		return discreteValueService.getList() ?? [];
	}

	public getDefaultItem(characteristicFk: number) {
		const list = this.discreteValueLookupService.syncService?.getListSync();
		return list?.find((item) => item.CharacteristicFk === characteristicFk && item.IsDefault);
	}

	public getDefaultItemAsync(characteristicFk: number): Observable<ICharacteristicValueEntity> {
		return new Observable((observer) => {
			let item = this.getDefaultItem(characteristicFk);
			if (item) {
				observer.next(item);
				observer.complete();
			} else {
				this.discreteValueLookupService.getList().subscribe((items) => {
					item = items?.find((e) => e.CharacteristicFk === characteristicFk && e.IsDefault);
					observer.next(item);
					observer.complete();
				});
			}
		});
	}
}
