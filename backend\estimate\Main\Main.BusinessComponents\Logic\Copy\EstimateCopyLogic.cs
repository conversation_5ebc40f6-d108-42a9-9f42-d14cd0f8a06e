using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RIB.Visual.Basics.Characteristic.BusinessComponents;
using RIB.Visual.Basics.Characteristic.Common;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Estimate.Assemblies.BusinessComponents;
using RIB.Visual.Estimate.Common.BusinessComponents;
using RIB.Visual.Estimate.Parameter.BusinessComponents;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RVPO = RIB.Visual.Platform.OperationalManagement;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	///
	/// </summary>
	public class EstimateCopyLogic : EstLineItemCopyLogicBase
	{
		private bool _IsCopyPackage = false;

		private bool _IsDeepCopyFromProject = false;

		private bool _IsResolveAssembly = true;

		private bool _IsSetProjectCurrency = true;

		private int? _ProjectId;

		private int? _TargetProjectJobId;

		private int? _SourceProjectJobId;

		private int _TargetHeaderId;

		private bool _IsSameProject = true;

		private bool _IsCopyBudget = false;

		private bool _IsDeleteItemAssignment = false;

		private bool _SystemOptionCopyBudget = false;

		private bool _ConsiderSystemOptionCopyBudget = true;

		private bool _IsCopyCostTotalToBudget = false;

		private bool _IsCopyBaseCost = false;

		private EstHeaderEntity _TargetHeader;

		private EstimateMainCalculatorData _EstimateCopyOption;

		private bool _IsDeepCopyFromEstimate = false;

		private bool _isCopyLineItemQuantity = true;

		private int? _lSumUomId;

		private bool _EstimateFixedActivatedAllowance = false;

		private bool _IsCopyLineItems = false;

		private Dictionary<int, int> _Old2NewJobIdMapping = new Dictionary<int, int>();

		private Dictionary<int, int> _Old2NewPlantJobIdMapping = new Dictionary<int, int>();

		private ConcurrentDictionary<int, int> _Old2NewProjectCostCodeIdMapping = new ConcurrentDictionary<int, int>();

		private EstimateMainLineItemLogic _LineItemLogic = new EstimateMainLineItemLogic();

		private EstimateMainResourceLogic _ResourceLogic = new EstimateMainResourceLogic();

		private EstLineItemUpdateFrmPrjLogic _EstLineItemUpdateFrmPrjLogic;

		private ConcurrentDictionary<IdentificationData, IdentificationData> _Old2NewLineItemIdMapping = new ConcurrentDictionary<IdentificationData, IdentificationData>();

		private ConcurrentDictionary<IdentificationData, IdentificationData> _Old2NewModelObjectIdMapping = new ConcurrentDictionary<IdentificationData, IdentificationData>();

		private ConcurrentDictionary<IdentificationData, IdentificationData> _Old2NewResourceIdMapping = new ConcurrentDictionary<IdentificationData, IdentificationData>();

		private ConcurrentDictionary<int, int> _LineItemId2JobIdMapping = new ConcurrentDictionary<int, int>();

		private Dictionary<int, int> _ResourceId2JobIdMapping = new Dictionary<int, int>();

		private ConcurrentDictionary<int, int> _Old2NewJobIdMappingPrjDeepCopy = new ConcurrentDictionary<int, int>();

		private ConcurrentDictionary<Tuple<int, int>, Tuple<int, int>> _oldResLiId2NewResLiIdMapping = new ConcurrentDictionary<Tuple<int, int>, Tuple<int, int>>();

		private IDictionary<int, List<int>> _oldPrjCostCodeId2NewJobId = new Dictionary<int, List<int>>();

		private ProjectScopeObject _ProjectScopeObject;

		private Dictionary<int, int> _AssemblyCatalogIdMapping = new Dictionary<int, int>();

		private bool _isBackup = false;

		private bool _isRestore = false;

		private EstimateCopyOptionLogic _estCopyOptionLogic;

		private EstCopyOptionEntity _estCopyOptionEntity;

		private ConcurrentDictionary<int, int> _old2NewRuleSourceIdMap;

		/// <summary>
		///
		/// </summary>
		/// <param name="estimateCopyOption"></param>
		/// <param name="isDeepCopyProject"></param>
		/// <param name="estHeaderEntity"></param>
		/// <param name="isCopyPackage"></param>
		public EstimateCopyLogic(EstimateMainCalculatorData estimateCopyOption, bool isDeepCopyProject = false, EstHeaderEntity estHeaderEntity = null, bool isCopyPackage = false) : base(estimateCopyOption.EstHeaderFk, estimateCopyOption.ProjectId)
		{
			this._IsDeepCopyFromProject = isDeepCopyProject;

			this._IsCopyPackage = isCopyPackage;

			this._EstimateCopyOption = estimateCopyOption;

			this._IsDeepCopyFromEstimate = estimateCopyOption.IsDeepCopyEstimate;

			this._ProjectId = estimateCopyOption.ProjectId;

			this._TargetHeader = estHeaderEntity ?? new EstimateMainHeaderLogic().GetItemById(estimateCopyOption.EstHeaderFk);

			this._EstimateCopyOption.TargetEstHeaderJobId = this._TargetHeader != null ? this._TargetHeader.LgmJobFk : null;

			this._TargetHeaderId = estimateCopyOption.EstHeaderFk;

			this._ConsiderSystemOptionCopyBudget = this._IsDeepCopyFromEstimate ? false : this._ConsiderSystemOptionCopyBudget;

			this._SystemOptionCopyBudget = new SystemOptionLogic().GetValueAsBool(SystemOption.CopyBudget);

			this._IsCopyBudget = this._IsDeepCopyFromEstimate ? estimateCopyOption.IsCopyBudget : !this._ConsiderSystemOptionCopyBudget ? this._IsCopyBudget : this._SystemOptionCopyBudget;

			this._IsCopyCostTotalToBudget = this._IsDeepCopyFromEstimate ? estimateCopyOption.IsCopyCostTotalToBudget : !this._ConsiderSystemOptionCopyBudget && this._IsCopyCostTotalToBudget;

			this._IsCopyBaseCost = this._IsDeepCopyFromEstimate ? estimateCopyOption.IsCopyBaseCost : this._IsCopyBaseCost;

			this._IsDeleteItemAssignment = this._IsDeepCopyFromEstimate ? estimateCopyOption.IsDeleteItemAssignment : this._IsDeleteItemAssignment;

			this._IsSameProject = !estimateCopyOption.SourceProjectId.HasValue || (estimateCopyOption.SourceProjectId.Value == estimateCopyOption.ProjectId);

			this._ProjectScopeObject = new ProjectScopeObject(this._EstimateCopyOption.ProjectId ?? 0);

			this._EstLineItemUpdateFrmPrjLogic = new EstLineItemUpdateFrmPrjLogic(estimateCopyOption.EstHeaderFk, this._ProjectId, new EstResourceUpdateOption()
			{
				ConsiderIsRate = true,
				IsUpdateCostType = false,
				IsUpdateOriginalValue = true
			});

			this._TargetProjectJobId = this._EstimateCopyOption.TargetEstHeaderJobId.HasValue ? this._EstimateCopyOption.TargetEstHeaderJobId : EstJobHelper.GetProjectJobId(this._ProjectId);

			this._SourceProjectJobId = this._EstimateCopyOption.SourceEstHeaderJobId.HasValue ? this._EstimateCopyOption.SourceEstHeaderJobId : EstJobHelper.GetProjectJobId(estimateCopyOption.SourceProjectId ?? 0);

			if (this._TargetHeader != null && this._TargetHeader.EstTypeFk.HasValue && this._IsDeepCopyFromEstimate)
			{
				var targetHeaderTypeEntity = (BasicsCustomizeEstimationTypeEntity)new BasicsCustomizeEstimationTypeLogic()
					.GetById(this._TargetHeader.EstTypeFk.Value);

				this._EstimateCopyOption.DoCalculateWithWqQuantity = targetHeaderTypeEntity != null && targetHeaderTypeEntity.IsTotalWq;
			}

			this._isCopyLineItemQuantity = new SystemOptionLogic().GetValueAsBool(SystemOption.CopyLineItemQuantity);

			this._lSumUomId = EstimateMainBaseCalculator.GetLSumUomId();

			this._isBackup = estimateCopyOption.IsBackup;

			this._isRestore = estimateCopyOption.IsRestore;

			if (estimateCopyOption.JobSourceTargetMapping != null)
			{
				this._Old2NewJobIdMapping = estimateCopyOption.JobSourceTargetMapping.Where(e => e.Key.HasValue && e.Value.HasValue).ToDictionary(e => e.Key.Value, e => e.Value.Value);
			}

			if (estimateCopyOption.PlantJobSourceTargetMapping != null)
			{
				this._Old2NewPlantJobIdMapping = estimateCopyOption.PlantJobSourceTargetMapping.Where(e => e.Key.HasValue && e.Value.HasValue).ToDictionary(e => e.Key.Value, e => e.Value.Value);
			}

			this._Old2NewJobIdMappingPrjDeepCopy = new ConcurrentDictionary<int, int>(this._Old2NewJobIdMapping);

			_estCopyOptionLogic = new EstimateCopyOptionLogic(this._TargetHeaderId);

			_estCopyOptionEntity = _estCopyOptionLogic.GetCopyOptionByEstHeaderId(this._TargetHeaderId);
			this._EstimateCopyOption.EstimateSchedulingPerformanceTransfer = estimateCopyOption.EstimateSchedulingPerformanceTransfer;

			this._EstimateFixedActivatedAllowance = this._IsDeepCopyFromEstimate ? estimateCopyOption.EstimateFixedActivatedAllowance : false;

			this._IsCopyLineItems = estimateCopyOption.IsCopyLineItems;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="autoSave"></param>
		/// <param name="isCopyByDragDropSearchWizard"></param>
		/// <returns></returns>
		public EstimateDeepCopyResult DeepCopyByHeader(IEnumerable<EstLineItemEntity> lineItems, bool autoSave, bool isCopyByDragDropSearchWizard = false)
		{
			var retValue = DeepCopyEstimate(lineItems, isCopyByDragDropSearchWizard);

			if (autoSave && retValue != null)
			{
				retValue.Save();
			}

			return retValue;
		}

		private Stack<int> CreateNewLineItemIds(int count)
		{
			return new Stack<int>(this._LineItemLogic.SequenceManager.GetNextList("EST_LINE_ITEM", count));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="resToCopy"></param>
		/// <param name="fromLineItem"></param>
		/// <param name="toLineItem"></param>
		/// <param name="parentResource"></param>
		/// <param name="projectId"></param>
		/// <param name="isMoveOrCopyResource"></param>
		/// <param name="isSubitemResource"></param>
		/// <param name="isLookAtCopyOptions"></param>
		/// <param name="isCopyByDragDropSearchWizard"></param>
		public EstimateDeepCopyResult CopyResourcesToLineItem(IEnumerable<EstResourceEntity> resToCopy, EstLineItemEntity fromLineItem, EstLineItemEntity toLineItem, EstResourceEntity parentResource, int? projectId, string isMoveOrCopyResource, bool isSubitemResource = false, bool isLookAtCopyOptions = false, bool isCopyByDragDropSearchWizard = false)
		{
			var estimateMainResourceLogic = new EstimateMainResourceLogic();

			if (toLineItem == null)
			{
				return null;
			}
			var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

			var getTargetResourcesTask = Task.Run(() =>
			{
				Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
				return estimateMainResourceLogic.GetListByLineItemId(toLineItem.Id, toLineItem.EstHeaderFk);
			});

			var retValue = new EstimateDeepCopyResult(this._EstimateCopyOption, this._IsDeepCopyFromProject, this._IsDeepCopyFromEstimate, false);

			retValue.SaveRulesInBackground = true;

			if (!this._Old2NewLineItemIdMapping.ContainsKey(fromLineItem.IdentificationData))
			{
				_Old2NewLineItemIdMapping.AddOrUpdate(fromLineItem.IdentificationData, toLineItem.IdentificationData, (Key, Value) => toLineItem.IdentificationData);
			}

			estimateMainResourceLogic.AttachUserDefinedColumnVals(resToCopy, fromLineItem.EstHeaderFk, fromLineItem.Id);

			var estimateCopyOption = new EstimateCopyOption()
			{
				TargetEstHeaderId = toLineItem.EstHeaderFk,
				TargetProjectId = projectId,
				ResolveAssembly = true,
				ResolvePlantAssembly = true,
				IsLookAtCopyOptions = isLookAtCopyOptions,
				SourceJobId = EstJobHelper.GetJobId(fromLineItem, fromLineItem.EstHeaderFk)
			};

			var targetResources = estimateMainResourceLogic.CreateAndSaveResourcesCopy(resToCopy, toLineItem, estimateCopyOption, false, true, isSubitemResource, parentResource, false, false, isCopyByDragDropSearchWizard: isCopyByDragDropSearchWizard);

			if (targetResources == null)
			{
				return retValue;
			}

			//get and save parameters from base lineitem,
			Task.Run(() =>
			{
				Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
				new EstimateParameterLineItemLogic().CopyNSaveParamFrmLineItem(fromLineItem.EstHeaderFk, fromLineItem.Id, toLineItem.Id, toLineItem.EstHeaderFk);
			});

			var lineItem2estrulemapping = new EstimateRuleLineItemLogic().CopyNSaveRuleFrmLineItem(fromLineItem.EstHeaderFk, fromLineItem.Id, toLineItem.Id, toLineItem.EstHeaderFk);

			var lineItem2EstRuleList = lineItem2estrulemapping.Values.ToList();

			new EstimateRuleCompleteLookupLogic().FillWithSorting(lineItem2EstRuleList);

			var parentResId = parentResource != null && parentResource.EstLineItemFk == toLineItem.Id && parentResource.EstHeaderFk == toLineItem.EstHeaderFk
					? parentResource.Id
					: (int?)null;

			var ruleSourceMapping = new EstRuleResourceLogic().CopyNSaveRuleSource(lineItem2estrulemapping);

			foreach (var resourceEntity in targetResources)
			{
				resourceEntity.EstRuleSourceFk = resourceEntity.EstRuleSourceFk > 0 ? ruleSourceMapping[resourceEntity.EstRuleSourceFk.Value] : resourceEntity.EstRuleSourceFk;
				resourceEntity.EstResourceFk = resourceEntity.EstResourceFk == null && parentResId != null ? parentResId : null;
			}
			if (isLookAtCopyOptions)
			{
				Task.Run(() =>
				{
					Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
					var copyOptionEntity = new EstimateCopyOptionLogic(toLineItem.EstHeaderFk).GetCopyOptionByEstHeaderId(toLineItem.EstHeaderFk);

					if (copyOptionEntity.ResCharacteristics.Value)
					{
						var characteristic1List = targetResources.Flatten(e => e.ResourceChildren).Where(e => e.Characteristic1List != null).SelectMany(e => e.Characteristic1List).ToList();
						new CharacteristicDataLogic().Save(characteristic1List);
					}
				});
			}

			retValue.UserDefinedPriceToSave = targetResources.FlattenResources().Where(e => e.UserDefinedcolValEntity != null).Select(e => new UserDefinedcolValEntity(e.UserDefinedcolValEntity)).ToList();

			var estResources = getTargetResourcesTask.Result.ToList();

			var resourceIdsToSave = new List<int>();

			if (targetResources.Any(x => x.Resources.Any()))
			{
				resourceIdsToSave.AddRange(targetResources.SelectMany(x => x.FlattenResources()).Select(e => e.Id).Distinct());
			}

			var targetResourcesGrouped = targetResources.GroupBy(tr => tr.EstResourceFk ?? -1).ToDictionary(g => g.Key, g => g.ToList()).ToDictionary();

			var rootResources = targetResourcesGrouped.Where(group => group.Key == -1).SelectMany(group => group.Value).ToList();
			List<KeyValuePair<int, List<EstResourceEntity>>> childResources = targetResourcesGrouped.Where(group => group.Key != -1).ToList();
			var existingIds = new HashSet<int>(estResources.Select(r => r.Id));
			var newRootResources = rootResources.Where(r => !existingIds.Contains(r.Id));
			estResources.AddRange(newRootResources);
			resourceIdsToSave.AddRange(newRootResources.Select(r => r.Id));

			foreach (var group in childResources)
			{
				GetParentPathWithAttachedChildren(estResources, group, resourceIdsToSave);
			}
			toLineItem.EstResourceEntities = estResources;
			var lineItemsToSave = new EstLineItemUpdateHelper(toLineItem.EstHeaderFk, projectId, new EstLineItemUpdateOption()
			{
				IsCalculateDetail = true,
				IsUpdateRate = true,
				IsUpdateAssembly = false,
				ToSave = false,
				ConsiderIsRate = true,
				DoSaveModifiedUDP = false,
			}).CalculateLineItemsOfEstimate(new List<EstLineItemEntity> { toLineItem });

			var allResources = lineItemsToSave.SelectMany(x => x.EstResourceEntities).Where(s => s.EstResourceFk == null).ToList();
			//CopyAndSaveProjectDataForResources(lineItemsToSave, allResources);

			retValue.EstLineItemsToSave = lineItemsToSave;
			var flatResources = allResources.FlattenResources().Select(e => e as EstResourceEntity);
			var resourcesToSave = flatResources.Where(x => resourceIdsToSave.Contains(x.Id)).ToList();
			retValue.EstResourcesToSave = resourcesToSave.Distinct().ToList();

			return retValue;
		}

		private void GetParentPathWithAttachedChildren(IEnumerable<EstResourceEntity> resources, KeyValuePair<int, List<EstResourceEntity>> targetResources, List<int> resourceIdsToSave)
		{
			foreach (var resource in resources)
			{
				if (!resourceIdsToSave.Contains(resource.Id))
				{
					resourceIdsToSave.Add(resource.Id);
				}
				if (resource.Id == targetResources.Key)
				{
					foreach (var targetResource in targetResources.Value)
					{
						resource.ResourceChildren.Add(targetResource);
						resourceIdsToSave.Add(targetResource.Id);
					}
					return;
				}
				else if (resource.ResourceChildren != null && resource.ResourceChildren.Count > 0)
				{
					GetParentPathWithAttachedChildren(resource.ResourceChildren, targetResources, resourceIdsToSave);
					if (resourceIdsToSave.Contains(targetResources.Key))
					{
						return;
					}
				}
				resourceIdsToSave.RemoveAt(resourceIdsToSave.Count - 1);
			}
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="resToCopy"></param>
		/// <param name="fromLineItem"></param>
		/// <param name="toLineItem"></param>
		/// <param name="parentResource"></param>
		/// <param name="projectId"></param>
		/// <param name="isMoveOrCopyResource"></param>
		/// <param name="isSubitemResource"></param>
		/// <param name="isLookAtCopyOptions"></param>
		/// <param name="autoSave"></param>
		/// <param name="isCopyByDragDropSearchWizard"></param>
		/// <returns></returns>
		public EstimateDeepCopyResult CopyResourcesToLineItem(IEnumerable<EstResourceEntity> resToCopy, EstLineItemEntity fromLineItem, EstLineItemEntity toLineItem, EstResourceEntity parentResource, int? projectId, string isMoveOrCopyResource, bool isSubitemResource = false, bool isLookAtCopyOptions = false, bool autoSave = false, bool isCopyByDragDropSearchWizard = false)
		{
			var retValue = CopyResourcesToLineItem(resToCopy, fromLineItem, toLineItem, parentResource, projectId, isMoveOrCopyResource, isSubitemResource, isLookAtCopyOptions, isCopyByDragDropSearchWizard: isCopyByDragDropSearchWizard);
			if (autoSave && retValue != null)
			{
				retValue.Save();
			}
			return retValue;
		}

		private bool IncludeDataGeneratedByRule()
		{
			return this._isBackup || this._isRestore || this._IsDeepCopyFromProject || _EstimateFixedActivatedAllowance;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="isCopyByDragDropSearchWizard"></param>
		/// <returns></returns>
		/// <exception cref="RVPO.BusinessLayerException"></exception>
		public EstimateDeepCopyResult DeepCopyEstimate(IEnumerable<EstLineItemEntity> lineItems, bool isCopyByDragDropSearchWizard = false)
		{
			var retValue = new EstimateDeepCopyResult(this._EstimateCopyOption, this._IsDeepCopyFromProject, this._IsDeepCopyFromEstimate, !this._isBackup && !this._isRestore);

			if (!this._ProjectId.HasValue || lineItems == null || !lineItems.Any())
			{
				if (!this._EstimateCopyOption.IsCopyFromSourceBoq)
				{
					/* deep copy allowance */
					retValue.EstAllowanceDeepCopyResult = DeepCopyEstAllowance(false, this._IsDeepCopyFromProject, this._EstimateFixedActivatedAllowance, this._IsDeepCopyFromEstimate);
				}
				return retValue;
			}

			DeepCopyEstRuleSource(retValue, out _old2NewRuleSourceIdMap);

			var sourceLineItems = FilterLineItemsByRuleSource(lineItems);

			/* translate the source lineItem */
			sourceLineItems.Translate(this._LineItemLogic.UserLanguageId, new Func<EstLineItemEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

			if (!this._isBackup && !this._isRestore)
			{
				/* generated hint todo check for project deep copy? */
				GenerateLineItemHint(sourceLineItems, this._IsDeepCopyFromProject);
			}

			/* copy the job from source project, used for jobFk replacement */
			retValue.EstLineItemsToSave = CopyLineItemsForPrjDeepCopy(sourceLineItems);

			/* replace the EstLineItemFk with new linItemId */
			if (this._EstimateCopyOption.SplitQuantityLineItems == null)
			{
				ReplaceEstLineItemFk(retValue.EstLineItemsToSave);
			}

			/* generated lineitem code */
			if (!this._isBackup && !this._isRestore && !this._IsDeepCopyFromEstimate)
			{
				var lineItemCodeGenerator = this._TargetHeader != null ? new EstLineItemCodeGenerator(this._TargetHeader) : new EstLineItemCodeGenerator(this._TargetHeaderId);

				lineItemCodeGenerator.GenerateCode(retValue.EstLineItemsToSave.OrderBy(e => e.Code), null, this._ProjectId);
			}

			if (this._EstimateCopyOption.IsLookAtCopyOptions)
			{
				if (_estCopyOptionEntity.LiResources.Value)
				{
					retValue.EstResourcesToSave = CopyResourcesForPrjDeepCopy(sourceLineItems, isCopyByDragDropSearchWizard);
				}
			}
			else
			{
				retValue.EstResourcesToSave = CopyResourcesForPrjDeepCopy(sourceLineItems, isCopyByDragDropSearchWizard);
			}

			// if backup or restore, will copy the job rate, job materail and job assembly
			if (!this._isBackup && !this._isRestore)
			{
				/* create project costcode, material and assembly */
				CopyAndSaveProjectDataForResources(retValue.EstLineItemsToSave, retValue.EstResourcesToSave);
			}
			else
			{
				/* backup the project jobrate, material and assembly */
				BackupPrjData(retValue.EstLineItemsToSave, retValue.EstResourcesToSave);
			}

			if (IncludeDataGeneratedByRule())
			{
				/* deep copy rule result */
				retValue.EstRuleResultComplete = new EstRuleResultHeaderLogic().DeepCopyEstRuleResult(this._EstimateCopyOption.SourceEstHeaderFk, this._TargetHeaderId);
			}

			/* update estConfigFk of estHeader */
			retValue.EstConfigComplete = DeepCopyEstConfig(false);

			if (!this._EstimateCopyOption.IsCopyFromSourceBoq)
			{
				/* deep copy allowance */
				retValue.EstAllowanceDeepCopyResult = DeepCopyEstAllowance(false, this._IsDeepCopyFromProject, this._EstimateFixedActivatedAllowance, this._IsDeepCopyFromEstimate);
			}

			/* deepcopy estprice adjustment */
			retValue.EstPriceAdjustmentToSave = DeepCopyEstPriceAdjustment(false);

			/* deepcopy udp */
			retValue.UserDefinedPriceToSave = CopyUserDefinedPrice();

			retValue.LineItemUserDefinedPriceToSave = CopyLineItemUDP(false);

			retValue.ResourceUserDefinedPriceToSave = CopyResourceUDP(false);

			if (this._EstimateCopyOption.IsLookAtCopyOptions)
			{
				if (_estCopyOptionEntity.LiPrjCostGroup.Value || _estCopyOptionEntity.LiCostGroup.Value)
				{
					/* Copy LineItem cost Groups */
					retValue.EstLineItem2CostGroupToSave = CopyLineItemCostGroupOfEstimate(sourceLineItems, false);
				}
			}
			else
			{
				/* Copy LineItem cost Groups */
				retValue.EstLineItem2CostGroupToSave = CopyLineItemCostGroupOfEstimate(sourceLineItems, false);
			}

			if (string.IsNullOrEmpty(this._EstimateCopyOption.FromAssembly))
			{
				/* Copy MdlObject And Quantities */
				if (!this._IsDeepCopyFromProject || this._IsSameProject)
				{
					retValue.EstLineItem2MdlObjectDeepCopyResult = new List<EstLineItem2MdlObjectDeepCopyResult> { new EstLineItem2MdlObjectLogic()
						.CopyLineItem2MdlObjects(this._Old2NewLineItemIdMapping
						.ToDictionary(e => e.Key.Id, e => e.Value.Id), _EstimateCopyOption.SourceEstHeaderFk, this._TargetHeaderId, true, false) };

					this._Old2NewModelObjectIdMapping = new ConcurrentDictionary<IdentificationData, IdentificationData>(retValue.EstLineItem2MdlObjectDeepCopyResult
						.FirstOrDefault()?.OldNewModelObjectIdMapping?.ToDictionary(e => new IdentificationData { Id = e.Key }, e => new IdentificationData { Id = e.Value })
						?? new Dictionary<IdentificationData, IdentificationData>());
				}
			}

			if (!this._IsDeepCopyFromProject || this._IsSameProject)
			{
				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (_estCopyOptionEntity.LiPackageItemAssignment.Value)
					{
						/* Copy PrcItemAssignments from Source Estimate to Target Estimate */
						retValue.PrcItemAssignmentToSave = CopyPrcItemAssignments(lineItems, false);

						retValue.PrcItemAssignmentToDelete = DeletePrcItemAssignments(lineItems, false);
					}
				}
				else
				{
					/* Copy PrcItemAssignments from Source Estimate to Target Estimate */
					retValue.PrcItemAssignmentToSave = CopyPrcItemAssignments(lineItems, false);

					retValue.PrcItemAssignmentToDelete = DeletePrcItemAssignments(lineItems, false);
				}
			}

			if (this._IsCopyPackage)
			{
				retValue.PrcItemAssignmentToSaveForCopyProject = CopyPrcItemAssignments(lineItems, false);
			}

			retValue.RiskRegisterEntityDeepCopyResult = new EstLineItemCopyLogic().DeepCopyRisk(this._EstimateCopyOption.SourceEstHeaderFk, this._TargetHeaderId, false);

			retValue.Old2NewLineItemIdMapping = this._Old2NewLineItemIdMapping;

			retValue.Old2NewResourceIdMapping = this._Old2NewResourceIdMapping;

			retValue.AssemblyCatalogIdMapping = this._AssemblyCatalogIdMapping;

			if (this._EstimateCopyOption.EstimateSchedulingPerformanceTransfer)
			{
				CopySchedulingPerformanceData(retValue);
			}

			SetIsFromBidForLineItem(retValue.EstLineItemsToSave);

			return retValue;
		}

		private IEnumerable<EstRuleSourceEntity> GetValidRuleSourcesToCopy(out IEnumerable<EstRuleSrc2CostGroupRuleEntity> estRuleSrc2CostGroupRules)
		{
			estRuleSrc2CostGroupRules = new List<EstRuleSrc2CostGroupRuleEntity>();

			var ruleSourceIds = this._LineItemLogic.GetRuleSourceIdsOfLineItem(this._EstimateCopyOption.SourceEstHeaderFk);

			ruleSourceIds = ruleSourceIds.Concat(this._ResourceLogic.GetRuleSourceIdsOfResource(this._EstimateCopyOption.SourceEstHeaderFk));

			var ruleSourceInSourceEstimate = new EstRuleResourceLogic().GetCoresByIds(ruleSourceIds.Select(e => new IdentificationData()
			{
				Id = e
			})).ToList();

			if (this._isBackup || this._isRestore || this._EstimateFixedActivatedAllowance)
			{
				return ruleSourceInSourceEstimate;
			}

			if (!this._IsDeepCopyFromProject)
			{
				return Enumerable.Empty<EstRuleSourceEntity>();
			}

			var ruleSourceOfLineItem = ruleSourceInSourceEstimate.Where(e => e.EstLineitem2estRuleFk.HasValue).ToList();

			var ruleSourceOfLeadingStructure = ruleSourceInSourceEstimate.Where(e => !e.EstLineitem2estRuleFk.HasValue).ToList();

			var validRuleSources = new List<EstRuleSourceEntity>();

			if (this._EstimateCopyOption.RuleAssignmentCopyResult != null)
			{
				foreach (var ruleSourceEntity in ruleSourceOfLeadingStructure)
				{
					if (ruleSourceEntity.EstActivity2estRuleFk.HasValue && this._EstimateCopyOption.RuleAssignmentCopyResult.ActivityRuleAssignmentIdMap != null && this._EstimateCopyOption.RuleAssignmentCopyResult.ActivityRuleAssignmentIdMap.ContainsKey(ruleSourceEntity.EstActivity2estRuleFk.Value))
					{
						ruleSourceEntity.EstActivity2estRuleFk = this._EstimateCopyOption.RuleAssignmentCopyResult.ActivityRuleAssignmentIdMap[ruleSourceEntity.EstActivity2estRuleFk.Value];
						validRuleSources.Add(ruleSourceEntity);
					}

					if (ruleSourceEntity.EstBoq2estRuleFk.HasValue && this._EstimateCopyOption.RuleAssignmentCopyResult.EstBoqRuleAssignmentIdMap != null && this._EstimateCopyOption.RuleAssignmentCopyResult.EstBoqRuleAssignmentIdMap.ContainsKey(ruleSourceEntity.EstBoq2estRuleFk.Value))
					{
						ruleSourceEntity.EstBoq2estRuleFk = this._EstimateCopyOption.RuleAssignmentCopyResult.EstBoqRuleAssignmentIdMap[ruleSourceEntity.EstBoq2estRuleFk.Value];
						validRuleSources.Add(ruleSourceEntity);
					}

					if (ruleSourceEntity.EstCtu2estRuleFk.HasValue && this._EstimateCopyOption.RuleAssignmentCopyResult.ControllingUnitRuleAssignmentIdMap != null && this._EstimateCopyOption.RuleAssignmentCopyResult.ControllingUnitRuleAssignmentIdMap.ContainsKey(ruleSourceEntity.EstCtu2estRuleFk.Value))
					{
						ruleSourceEntity.EstCtu2estRuleFk = this._EstimateCopyOption.RuleAssignmentCopyResult.ControllingUnitRuleAssignmentIdMap[ruleSourceEntity.EstCtu2estRuleFk.Value];
						validRuleSources.Add(ruleSourceEntity);
					}

					if (ruleSourceEntity.EstPrcStruc2estRuleFk.HasValue && this._EstimateCopyOption.RuleAssignmentCopyResult.PrcStructureRuleAssignmentIdMap != null && this._EstimateCopyOption.RuleAssignmentCopyResult.PrcStructureRuleAssignmentIdMap.ContainsKey(ruleSourceEntity.EstPrcStruc2estRuleFk.Value))
					{
						ruleSourceEntity.EstPrcStruc2estRuleFk = this._EstimateCopyOption.RuleAssignmentCopyResult.PrcStructureRuleAssignmentIdMap[ruleSourceEntity.EstPrcStruc2estRuleFk.Value];
						validRuleSources.Add(ruleSourceEntity);
					}

					if (ruleSourceEntity.EstHeader2estRuleFk.HasValue && this._EstimateCopyOption.RuleAssignmentCopyResult.EstHeaderRuleAssignmentIdMap != null && this._EstimateCopyOption.RuleAssignmentCopyResult.EstHeaderRuleAssignmentIdMap.ContainsKey(ruleSourceEntity.EstHeader2estRuleFk.Value))
					{
						ruleSourceEntity.EstHeader2estRuleFk = this._EstimateCopyOption.RuleAssignmentCopyResult.EstHeaderRuleAssignmentIdMap[ruleSourceEntity.EstHeader2estRuleFk.Value];
						validRuleSources.Add(ruleSourceEntity);
					}

					if (ruleSourceEntity.EstPrjLoc2estRuleFk.HasValue && this._EstimateCopyOption.RuleAssignmentCopyResult.PrjLocationRuleAssignmentIdMap != null && this._EstimateCopyOption.RuleAssignmentCopyResult.PrjLocationRuleAssignmentIdMap.ContainsKey(ruleSourceEntity.EstPrjLoc2estRuleFk.Value))
					{
						ruleSourceEntity.EstPrjLoc2estRuleFk = this._EstimateCopyOption.RuleAssignmentCopyResult.PrjLocationRuleAssignmentIdMap[ruleSourceEntity.EstPrjLoc2estRuleFk.Value];
						validRuleSources.Add(ruleSourceEntity);
					}
				}
			}


			// include the rule source of line item, and it will replace the estLineItemFk after lineitem copied
			var result = validRuleSources.Concat(ruleSourceOfLineItem);

			// include the rule source of assembly catalog, and it will replace the assembly catalog id after resource copied
			if (ruleSourceInSourceEstimate.Any(e => e.EstAssembly2estRuleFk.HasValue))
			{
				result = result.Concat(ruleSourceInSourceEstimate.Where(e => e.EstAssembly2estRuleFk.HasValue).ToList());
			}

			if (this._EstimateCopyOption.RuleAssignmentCopyResult != null && this._EstimateCopyOption.RuleAssignmentCopyResult.CostGroupRuleAssignmentIdMap != null && this._EstimateCopyOption.RuleAssignmentCopyResult.CostGroupRuleAssignmentIdMap.Any())
			{
				var costGroupRuleAssignmentIds = this._EstimateCopyOption.RuleAssignmentCopyResult.CostGroupRuleAssignmentIdMap.Keys;
				estRuleSrc2CostGroupRules = new EstRuleSrc2CostGroupRuleLogic().GetListByFilter(e => costGroupRuleAssignmentIds.Contains(e.EstCostGroupRuleFk)).ToList();
				if (estRuleSrc2CostGroupRules.Any())
				{
					var ruleSourceIdsOfCostGroup = estRuleSrc2CostGroupRules.Select(e => e.EstRuleSourceFk).Distinct().ToList();
					var ruleSourcesOfCostGroup = ruleSourceInSourceEstimate.Where(e => ruleSourceIdsOfCostGroup.Contains(e.Id)).ToList();
					result = result.Concat(ruleSourcesOfCostGroup);
				}
			}

			return result.ToList();
		}

		private void DeepCopyEstRuleSource(EstimateDeepCopyResult deepCopyResult, out ConcurrentDictionary<int, int> old2NewRuleSourceIdMap)
		{
			IEnumerable<EstRuleSrc2CostGroupRuleEntity> estRuleSrc2CostGroupRules;

			var estRuleSources = GetValidRuleSourcesToCopy(out estRuleSrc2CostGroupRules);

			old2NewRuleSourceIdMap = new ConcurrentDictionary<int, int>();

			if (estRuleSources == null || !estRuleSources.Any())
			{
				return;
			}

			var cloneRuleSources = estRuleSources.Select(e => e.Clone() as EstRuleSourceEntity).ToList();

			if (cloneRuleSources.Any())
			{
				var ids = new Stack<int>(this.SequenceManager.GetNextList("EST_RULE_SOURCE", cloneRuleSources.Count));

				foreach (var ruleSource in cloneRuleSources)
				{
					var newId = ids.Pop();

					old2NewRuleSourceIdMap.TryAdd(ruleSource.Id, newId);

					ruleSource.Id = newId;
					ruleSource.Version = 0;
				}
			}

			if (estRuleSrc2CostGroupRules != null && estRuleSrc2CostGroupRules.Any() && this._EstimateCopyOption.RuleAssignmentCopyResult != null && this._EstimateCopyOption.RuleAssignmentCopyResult.CostGroupRuleAssignmentIdMap != null && this._EstimateCopyOption.RuleAssignmentCopyResult.CostGroupRuleAssignmentIdMap.Any())
			{
				var estRuleSrc2CostGroupRuleToCopy = new List<EstRuleSrc2CostGroupRuleEntity>();

				foreach (var ruleSrc2CostGroup in estRuleSrc2CostGroupRules)
				{
					if (old2NewRuleSourceIdMap.TryGetValue(ruleSrc2CostGroup.EstRuleSourceFk, out int newRuleSourceId))
					{
						ruleSrc2CostGroup.EstRuleSourceFk = newRuleSourceId;

						if (this._EstimateCopyOption.RuleAssignmentCopyResult.CostGroupRuleAssignmentIdMap.TryGetValue(ruleSrc2CostGroup.EstCostGroupRuleFk, out int newCostGroupRuleId))
						{
							ruleSrc2CostGroup.EstCostGroupRuleFk = newCostGroupRuleId;

							var cloneEntity = ruleSrc2CostGroup.Clone() as EstRuleSrc2CostGroupRuleEntity;

							estRuleSrc2CostGroupRuleToCopy.Add(cloneEntity);
						}
					}
				}

				if (estRuleSrc2CostGroupRuleToCopy.Any())
				{
					var costGroupSrcIds = new Stack<int>(this.SequenceManager.GetNextList("EST_RULE_SRC2COSTGRP_RUL", estRuleSrc2CostGroupRuleToCopy.Count));

					foreach (var item in estRuleSrc2CostGroupRuleToCopy)
					{
						item.Id = costGroupSrcIds.Pop();
						item.Version = 0;
					}

					deepCopyResult.EstRuleSrc2CostGroupRuleToSave = estRuleSrc2CostGroupRuleToCopy;
				}
			}

			deepCopyResult.EstRuleSourceToSave = cloneRuleSources;
		}

		private IEnumerable<EstLineItemEntity> FilterLineItemsByRuleSource(IEnumerable<EstLineItemEntity> lineItems)
		{
			if (lineItems == null || !lineItems.Any())
			{
				return lineItems;
			}

			if (this._IsDeepCopyFromProject && this._old2NewRuleSourceIdMap != null && _old2NewRuleSourceIdMap.Any())
			{
				var validRuleSources = _old2NewRuleSourceIdMap.Select(e => e.Key).ToHashSet();

				return lineItems.Where(e => !e.EstRuleSourceFk.HasValue || validRuleSources.Contains(e.EstRuleSourceFk.Value)).ToList();
			}
			else if (this._isBackup || this._isRestore || this._EstimateFixedActivatedAllowance)
			{
				// !this._isBackup && !this._isRestore: not bakcup/restore estimate.
				// if backup or restore, will copy include the rule generated.
				return lineItems;
			}
			else
			{
				return lineItems.Where(e => e.EstRuleSourceFk == null).ToList();
			}
		}

		private void SetIsFromBidForLineItem(IEnumerable<EstLineItemEntity> estLineItems)
		{
			foreach (var lineItem in estLineItems)
			{
				if (this._IsDeepCopyFromEstimate && this._EstimateFixedActivatedAllowance)
				{
					lineItem.IsFromBid = true;
				}
				else if (this._IsCopyLineItems)
				{
					lineItem.IsFromBid = false;
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		private void CopyAndSaveProjectDataForResources(IEnumerable<EstLineItemEntity> lineItems, IEnumerable<EstResourceEntity> targetResources)
		{
			if (this._IsDeepCopyFromProject || !this._IsSameProject)
			{
				if (this._ProjectId.HasValue && this._EstimateCopyOption.SourceProjectId.HasValue)
				{

					var estJobHelper = new EstJobHelper(this._ProjectId.Value)
						 .SetSourceProjectId(this._EstimateCopyOption.SourceProjectId.Value)
						 .SetLineItemIdToJobIdMap(this._LineItemId2JobIdMapping)
						 .SetResourceIdToJobIdMap(this._ResourceId2JobIdMapping)
						 .SetOld2NewJobIdMapiing(this._Old2NewJobIdMappingPrjDeepCopy)
						 .SetIsCopyFromSameProject(false);

					var parentJobId = this._EstimateCopyOption.TargetEstHeaderJobId.HasValue
									? this._EstimateCopyOption.TargetEstHeaderJobId.Value : this._TargetHeader != null && this._TargetHeader.LgmJobFk.HasValue
									? this._TargetHeader.LgmJobFk.Value : this._TargetProjectJobId;

					estJobHelper.CollectPrjCostCodesAndPrjMaterialToSave(targetResources, parentJobId);

					// collect project assemblies and project plant assemblies
					estJobHelper.CollectAllPrjAssembliesToSave(lineItems, parentJobId);

					estJobHelper.Save(true, this._IsDeepCopyFromProject, this._Old2NewJobIdMapping, false, _EstimateCopyOption.SourceProjectId ?? 0);

					this._AssemblyCatalogIdMapping = estJobHelper._AssemblyCatalogIdMapping;

					this._Old2NewProjectCostCodeIdMapping = estJobHelper.GetOldNewPrjCostCodeIdMap();

					this._oldPrjCostCodeId2NewJobId = estJobHelper.getOldPrjCostCodeIds2NewJobIds();

					foreach (var item in targetResources)
					{
						if (!item.ProjectCostCodeFk.HasValue)
						{
							continue;
						}

						var old2NewProjectCostCodeIdMap = _Old2NewProjectCostCodeIdMapping.FirstOrDefault(x => x.Key == item.ProjectCostCodeFk);
						if (old2NewProjectCostCodeIdMap.Key != 0)
						{
							item.ProjectCostCodeFk = _Old2NewProjectCostCodeIdMapping[item.ProjectCostCodeFk.Value];
						}
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		private void BackupPrjData(IEnumerable<EstLineItemEntity> lineItems, IEnumerable<EstResourceEntity> targetResources)
		{
			if (this._ProjectId.HasValue)
			{
				var masterAssemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();
				var projectAssemblyEstHeader = Injector.Get<IProjectAssemblyLogic>().GetByProject(this._ProjectId ?? 0);
				int prjAssemblyHeaderId = projectAssemblyEstHeader != null ? projectAssemblyEstHeader.EstHeaderFk : 0;

				var estBackupHelper = new EstBackupHelper(this._ProjectId.Value)
					 .SetLineItemIdToJobIdMap(this._LineItemId2JobIdMapping)
					 .SetResourceIdToJobIdMap(this._ResourceId2JobIdMapping)
					 .SetTargetEstHeaderJobId(this._EstimateCopyOption.TargetEstHeaderJobId)
					 .SetTargetEstHeaderJobCode(this._EstimateCopyOption.TargetEstHeaderJobCode)
					 .SetSourceEstHeaderJobId(this._SourceProjectJobId)
					 .SetMasterAssemblyHeaderId(masterAssemblyHeaderId)
					 .SetPrjAssemblyHeaderId(prjAssemblyHeaderId);

				var parentJobId = this._SourceProjectJobId;


				estBackupHelper.CollectPrjDataFromResourcesToSave(targetResources, parentJobId);

				// collect project assemblies
				estBackupHelper.CollectAllPrjAssembliesToSave(lineItems, parentJobId);

				estBackupHelper.BackupSave(lineItems, targetResources);
			}
		}

		/// <summary>
		/// restore the version estimate, the actual estimate was replaced as version estimate
		/// will replace the jobs and backup project costcode, materail and assembly
		/// </summary>
		/// <param name="actualEstimate"></param>
		/// <param name="sourceJobId"></param>
		/// <param name="prjId"></param>
		/// <param name="versionJobCode"></param>
		public static void BackupActulaEstimatePrjSources(EstHeaderEntity actualEstimate, int sourceJobId, int prjId, string versionJobCode)
		{
			var lineItemLogic = new EstimateMainLineItemLogic();
			var lineItems = lineItemLogic.GetListForDeepCopy(e => e.EstHeaderFk == actualEstimate.Id && e.LineItemType == 0 && e.IsTemp == false);

			if (lineItems.Any())
			{
				ConcurrentDictionary<int, int> lineItemId2JobIdMapping = new ConcurrentDictionary<int, int>();
				foreach (var item in lineItems)
				{
					if (item.LgmJobFk.HasValue)
					{
						lineItemId2JobIdMapping.AddOrUpdate(item.Id, item.LgmJobFk.Value, (Key, Value) => item.LgmJobFk.Value);
					}
				}

				var lineItemIds = lineItems.Where(e => !e.EstLineItemFk.HasValue).Select(e => e.Id).ToList();

				var estResourceLogic = new EstimateMainResourceLogic();
				/* get resources list */
				var sourceResources = estResourceLogic.GetResourcesByLineItemIdsCore(lineItemIds, actualEstimate.Id, new EstResourceSearchOption()
				{
					IncludeChildren = true,
					IgnoreRuleGenerated = true
				});

				Dictionary<int, int> resourceId2JobIdMapping = new Dictionary<int, int>();
				foreach (var sourceResource in sourceResources)
				{
					if (!resourceId2JobIdMapping.ContainsKey(sourceResource.Id))
					{
						if (sourceResource.LgmJobFk.HasValue)
						{
							resourceId2JobIdMapping.Add(sourceResource.Id, sourceResource.LgmJobFk.Value);
						}
						else if (sourceResource.EstResourceFk.HasValue && resourceId2JobIdMapping.ContainsKey(sourceResource.EstResourceFk.Value))
						{
							resourceId2JobIdMapping.Add(sourceResource.Id, resourceId2JobIdMapping[sourceResource.EstResourceFk.Value]);
						}
					}
				}

				var masterAssemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();
				var projectAssemblyEstHeader = Injector.Get<IProjectAssemblyLogic>().GetByProject(prjId);
				int prjAssemblyHeaderId = projectAssemblyEstHeader != null ? projectAssemblyEstHeader.EstHeaderFk : 0;

				var estBackupHelper = new EstBackupHelper(prjId)
					 .SetLineItemIdToJobIdMap(lineItemId2JobIdMapping)
					 .SetResourceIdToJobIdMap(resourceId2JobIdMapping)
					 .SetTargetEstHeaderJobId(actualEstimate.LgmJobFk)
					 .SetTargetEstHeaderJobCode(versionJobCode)
					 .SetSourceEstHeaderJobId(sourceJobId)
					  .SetMasterAssemblyHeaderId(masterAssemblyHeaderId)
					 .SetPrjAssemblyHeaderId(prjAssemblyHeaderId);

				var parentJobId = sourceJobId;

				estBackupHelper.CollectPrjDataFromResourcesToSave(sourceResources, parentJobId);

				// collect project assemblies and project plant assemblies
				estBackupHelper.CollectAllPrjAssembliesToSave(lineItems, parentJobId);

				estBackupHelper.BackupSave(lineItems, sourceResources);

				// save the modified lineitems and resources
				var lineItemsToSave = lineItems.Where(e => e.LgmJobFk.HasValue);
				if (lineItemsToSave.Any())
				{
					/* save lineitems */
					lineItemLogic.BulkSave(lineItemsToSave);
				}

				var resourcesToSave = sourceResources.Where(e => e.LgmJobFk.HasValue || e.EstAssemblyFk.HasValue);
				if (resourcesToSave.Any())
				{
					/* save resources */
					estResourceLogic.SaveInBatch(resourcesToSave);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		private IEnumerable<EstRuleSourceEntity> DeepCopyEstRuleSource(IEnumerable<EstLineItemEntity> lineItemsToSave, IEnumerable<EstResourceEntity> resourcesToSave, bool skipLineItem = false)
		{
			var retVal = new List<EstRuleSourceEntity>();

			var ruleSources2LineItem = skipLineItem ? new List<EstLineItemEntity>() : lineItemsToSave.Where(e => e.EstRuleSourceFk.HasValue).ToList();
			var ruleSourceIds2LineItem = ruleSources2LineItem.Select(i => i.EstRuleSourceFk.Value);
			var ruleSource2EstResource = resourcesToSave.Where(e => e.EstRuleSourceFk.HasValue).ToList();
			var ruleSourceId2EstResource = ruleSource2EstResource.Select(i => i.EstRuleSourceFk.Value);
			var ruleSourceIds = ruleSourceIds2LineItem.Concat(ruleSourceId2EstResource).Distinct();

			if (ruleSourceIds.Any())
			{
				var ruleSources = new EstRuleResourceLogic().GetItemsByKey(ruleSourceIds);
				if (ruleSources != null && ruleSources.Any())
				{
					var cloneRuleSources = ruleSources.Select(e => e.Clone() as EstRuleSourceEntity).ToList();
					var ids = this.SequenceManager.GetNextList("EST_RULE_SOURCE", cloneRuleSources.Count);
					for (int i = 0; i < ids.Count; i++)
					{
						int sourceId = cloneRuleSources[i].Id;
						cloneRuleSources[i].Id = ids[i];
						cloneRuleSources[i].Version = 0;

						retVal.Add(cloneRuleSources[i]);

						if (!skipLineItem)
						{
							var lineItems = ruleSources2LineItem.Where(e => e.EstRuleSourceFk == sourceId);
							foreach (var item in lineItems)
							{
								item.EstRuleSourceFk = ids[i];
							}
						}

						var resources = ruleSource2EstResource.Where(e => e.EstRuleSourceFk == sourceId);
						foreach (var res in resources)
						{
							res.EstRuleSourceFk = ids[i];
						}
					}
				}
			}

			return retVal;
		}

		/// <summary>
		/// Copy Procurement Item Assignments from Source Estimate to Target Estimate
		/// </summary>
		/// <param name="sourceLineItems"></param>
		/// <param name="autoSave"></param>
		/// <returns></returns>
		private IEnumerable<IPrcItemAssignmentEntity> CopyPrcItemAssignments(IEnumerable<EstLineItemEntity> sourceLineItems, bool autoSave = true)
		{
			var sourceLineItemIds = sourceLineItems.Select(e => e.Id).ToList();

			// get procurement item assignments by EstimateHeader ID.
			var prcItemAssignmentLogic = Injector.Get<IPrcItemAssignmentLogic>();

			var sourcePrcItemAssignments = prcItemAssignmentLogic.GetEntitiesByEstimateInfo(this._EstimateCopyOption.SourceEstHeaderFk, sourceLineItemIds);

			if (sourcePrcItemAssignments == null || !sourcePrcItemAssignments.Any())
			{
				return Enumerable.Empty<IPrcItemAssignmentEntity>();
			}

			var prcItemsToSave = new ConcurrentBag<IPrcItemAssignmentEntity>();

			ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount / 2 };
			var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

			Parallel.ForEach(sourcePrcItemAssignments, parallelOptions, sourcePrcItemAssignment =>
			{
				Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;

				if (sourcePrcItemAssignment != null)
				{
					/* clone PrcItemAssignments */
					var lineItemKey = new IdentificationData()
					{
						Id = sourcePrcItemAssignment.EstLineItemFk,
						PKey1 = sourcePrcItemAssignment.EstHeaderFk
					};

					if (this._Old2NewLineItemIdMapping.ContainsKey(lineItemKey))
					{
						if (sourcePrcItemAssignment.EstResourceFk != null)
						{
							var resourceKey = new IdentificationData()
							{
								Id = sourcePrcItemAssignment.EstResourceFk.Value,
								PKey1 = sourcePrcItemAssignment.EstHeaderFk,
								PKey2 = sourcePrcItemAssignment.EstLineItemFk
							};

							if (this._Old2NewResourceIdMapping.ContainsKey(resourceKey))
							{
								sourcePrcItemAssignment.EstResourceFk = _Old2NewResourceIdMapping[resourceKey].Id;

								sourcePrcItemAssignment.EstLineItemFk = _Old2NewLineItemIdMapping[lineItemKey].Id;

								sourcePrcItemAssignment.EstHeaderFk = this._TargetHeaderId;
							}
						}
						else
						{
							sourcePrcItemAssignment.EstLineItemFk = _Old2NewLineItemIdMapping[lineItemKey].Id;

							sourcePrcItemAssignment.EstHeaderFk = this._TargetHeaderId;
						}

						prcItemsToSave.Add(sourcePrcItemAssignment);
					}
				}
			});

			if (autoSave)
			{
				prcItemAssignmentLogic.CreateNSaveEntities(prcItemsToSave);
			}

			return prcItemsToSave;
		}

		private IEnumerable<IPrcItemAssignmentEntity> DeletePrcItemAssignments(IEnumerable<EstLineItemEntity> sourceLineItems, bool autoDelete = true)
		{
			//Delete PrcItemAssignments in Source Estimate Header
			if (this._IsDeleteItemAssignment)
			{
				var prcItemAssignmentLogic = Injector.Get<IPrcItemAssignmentLogic>();

				var sourceLineItemIds = sourceLineItems.Select(e => e.Id).ToList();

				var deleteSourceItemAssignments = prcItemAssignmentLogic.GetEntitiesByEstimateInfo(this._EstimateCopyOption.SourceEstHeaderFk, sourceLineItemIds);

				if (autoDelete)
				{
					DeletePrcItemAssignmentsCore(deleteSourceItemAssignments);
				}

				return deleteSourceItemAssignments;
			}

			return Enumerable.Empty<IPrcItemAssignmentEntity>();
		}

		private void DeletePrcItemAssignmentsCore(IEnumerable<IPrcItemAssignmentEntity> deleteSourceItemAssignments)
		{
			if (deleteSourceItemAssignments == null && !deleteSourceItemAssignments.Any())
			{
				return;
			}

			var prcItemAssignmentLogic = Injector.Get<IPrcItemAssignmentLogic>();

			foreach (var sourcePrcItemAssignment in deleteSourceItemAssignments)
			{
				if (sourcePrcItemAssignment.EstResourceFk.HasValue)
				{
					var resources = this._ResourceLogic.GetListByLineItemId(sourcePrcItemAssignment.EstLineItemFk, this._EstimateCopyOption.SourceEstHeaderFk);

					var resIds = resources != null && resources.Any() ? resources.Select(i => i.Id) : new List<int>();

					prcItemAssignmentLogic.DeleteByEstimateInfo(this._EstimateCopyOption.SourceEstHeaderFk, sourcePrcItemAssignment.EstLineItemFk, resIds.ToList());
				}
				else
				{
					prcItemAssignmentLogic.DeleteByEstimateInfo(this._EstimateCopyOption.SourceEstHeaderFk, sourcePrcItemAssignment.EstLineItemFk, null);
				}
			}
		}

		private EstConfigComplete DeepCopyEstConfig(bool autoSave = true)
		{
			var sourceHeader = new EstimateMainHeaderLogic().GetById(new Platform.Core.IdentificationData(_EstimateCopyOption.SourceEstHeaderFk));

			if (sourceHeader == null || sourceHeader.EstConfigtypeFk.HasValue)
			{
				return null;
			}

			var sourceEstConfigEntity = new EstConfigLogic().GetById(new Platform.Core.IdentificationData() { Id = (int)sourceHeader.EstConfigFk });

			if (sourceEstConfigEntity == null)
			{
				return null;
			}

			if (this._Old2NewProjectCostCodeIdMapping == null)
			{
				this._Old2NewProjectCostCodeIdMapping = new ConcurrentDictionary<int, int>();
			}

			EstConfigDeepCopyOption deepCopyOption = new EstConfigDeepCopyOption()
			{
				DeepCopyProjectCostCode = this._IsDeepCopyFromProject || !this._IsSameProject,
				SourceProjectId = this._EstimateCopyOption.SourceProjectId,
				TargetProjectId = this._ProjectId,
				ExistSource2TargetProjectCostCodeIdMap = this._Old2NewProjectCostCodeIdMapping,
				AutoSave = autoSave
			};

			return new EstConfigLogic().DeepCopy(sourceHeader.EstConfigFk, deepCopyOption);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="autoSave"></param>
		/// <returns></returns>
		public EstBoq2uppConfigDeepCopyResult DeepCopyEstBoq2UppConfig(bool autoSave = true)
		{
			var deepCopyOption = new EstDeepCopyOption()
			{
				SourceEstHeaderId = _EstimateCopyOption.SourceEstHeaderFk,
				TargetEstHeaderId = _EstimateCopyOption.EstHeaderFk,
				IsDeepCopyFromProject = this._IsDeepCopyFromProject || !this._IsSameProject,
				SourceProjectId = this._EstimateCopyOption.SourceProjectId,
				TargetProjectId = this._ProjectId,
				Source2TargetPrjCostCodeIdMap = this._Old2NewProjectCostCodeIdMapping,
				BoqSourceTargetMapping = this._EstimateCopyOption.BoqSourceTargetMapping,
				AutoSave = autoSave
			};

			return new EstimateMainEstBoq2UppConfigLogic().DeepCopy(deepCopyOption);
		}

		private IEnumerable<EstPriceAdjustmentEntity> DeepCopyEstPriceAdjustment(bool autoSave = true)
		{
			var deepCopyOption = new EstDeepCopyOption()
			{
				SourceEstHeaderId = _EstimateCopyOption.SourceEstHeaderFk,
				TargetEstHeaderId = _EstimateCopyOption.EstHeaderFk,
				IsDeepCopyFromProject = this._IsDeepCopyFromProject || !this._IsSameProject,
				SourceProjectId = this._EstimateCopyOption.SourceProjectId,
				TargetProjectId = this._ProjectId,
				Source2TargetPrjCostCodeIdMap = this._Old2NewProjectCostCodeIdMapping,
				BoqSourceTargetMapping = this._EstimateCopyOption.BoqSourceTargetMapping,
				AutoSave = autoSave
			};

			return new EstPriceAdjustmentLogic().DeepCopy(deepCopyOption);
		}

		private EstAllowanceDeepCopyResult DeepCopyEstAllowance(bool autoSave = true, bool isDeepCopyFromProject = false, bool estimateFixedActivatedAllowance = false, bool isDeepCopyFromEstimate = false)
		{
			var estHeaderIdMap = new Dictionary<int, int>();

			if (_EstimateCopyOption.SourceEstHeaderFk != _EstimateCopyOption.EstHeaderFk)
			{
				estHeaderIdMap.Add(_EstimateCopyOption.SourceEstHeaderFk, _EstimateCopyOption.EstHeaderFk);
			}

			estimateFixedActivatedAllowance = isDeepCopyFromEstimate && estimateFixedActivatedAllowance;
			return new EstAllowanceLogic().DeepCopyEstAllowanceAndMarkupCostCode(estHeaderIdMap, this._Old2NewProjectCostCodeIdMapping, this._IsDeepCopyFromProject || !this._IsSameProject, autoSave, this._EstimateCopyOption.BoqSourceTargetMapping, isDeepCopyFromProject, estimateFixedActivatedAllowance);
		}

		private IEnumerable<UserDefinedcolValEntity> CopyUserDefinedPrice()
		{
			var copyUserDefinedPrices = new List<CopyRequest>();

			if (_EstimateCopyOption.BoqSourceTargetMapping != null && _EstimateCopyOption.BoqSourceTargetMapping.Any())
			{
				foreach (var item in _EstimateCopyOption.BoqSourceTargetMapping)
				{
					copyUserDefinedPrices.Add(new CopyRequest
					{
						SourceTableId = (int)userDefinedColumnTableIds.Boq,
						SourcePk1 = item.Key.Item2.Value,
						SourcePk2 = item.Key.Item1,
						Pk1 = item.Value.Item2.Value,
						Pk2 = item.Value.Item1,
						TableId = (int)userDefinedColumnTableIds.Boq
					});
				}
			}

			if (_Old2NewProjectCostCodeIdMapping != null && _Old2NewProjectCostCodeIdMapping.Any())
			{
				foreach (var item in _Old2NewProjectCostCodeIdMapping)
				{
					copyUserDefinedPrices.Add(new CopyRequest
					{
						SourceTableId = (int)userDefinedColumnTableIds.ProjectCostCode,
						SourcePk1 = item.Key,
						Pk1 = item.Value,
						TableId = (int)userDefinedColumnTableIds.ProjectCostCode
					});
				}

				if (_oldPrjCostCodeId2NewJobId != null && _oldPrjCostCodeId2NewJobId.Any())
				{
					foreach (var item in _oldPrjCostCodeId2NewJobId.Keys)
					{
						foreach (var newJobId in _oldPrjCostCodeId2NewJobId[item])
						{
							var newPrjCostCodeId = _Old2NewProjectCostCodeIdMapping.ContainsKey(item) ? _Old2NewProjectCostCodeIdMapping[item] : 0;
							if (newPrjCostCodeId <= 0)
							{
								continue;
							}

							var jobItem = _Old2NewJobIdMappingPrjDeepCopy.FirstOrDefault(x => x.Value == newJobId);
							if (jobItem.Key == 0 || jobItem.Value == 0)
							{
								continue;
							}

							copyUserDefinedPrices.Add(new CopyRequest
							{
								SourceTableId = (int)userDefinedColumnTableIds.ProjectCostCodeJobRate,
								TableId = (int)userDefinedColumnTableIds.ProjectCostCodeJobRate,
								SourcePk1 = _EstimateCopyOption.SourceProjectId.Value,
								Pk1 = _EstimateCopyOption.ProjectId.Value,
								SourcePk2 = item,
								Pk2 = newPrjCostCodeId,
								SourcePk3 = jobItem.Key,
								Pk3 = newJobId
							});
						}
					}
				}
			}

			if (copyUserDefinedPrices.Any())
			{
				return new UserDefinedColumnValueLogic().Copy(copyUserDefinedPrices, true, false).ToList();
			}

			return new List<UserDefinedcolValEntity>();
		}

		private IEnumerable<UserDefinedcolValEntity> CopyLineItemUDP(bool autoSave = true)
		{
			var newLineItemUpds = new List<UserDefinedcolValEntity>();

			var oldLineItemUdps = new UserDefinedColumnValueLogic().GetSearchList(e => e.TableId == (int)userDefinedColumnTableIds.EstimateLineItem && e.Pk1 == _EstimateCopyOption.SourceEstHeaderFk);

			if (oldLineItemUdps.Any())
			{
				var oldLineItemId2UdpMap = oldLineItemUdps.GroupBy(e => e.Pk2).ToDictionary(e => e.Key, e => e.FirstOrDefault());

				foreach (var item in _Old2NewLineItemIdMapping)
				{
					if (oldLineItemId2UdpMap.ContainsKey(item.Key.Id))
					{
						var newLineItemUdp = oldLineItemId2UdpMap[item.Key.Id].Clone() as UserDefinedcolValEntity;
						newLineItemUdp.Pk1 = _TargetHeaderId;
						newLineItemUdp.Pk2 = item.Value.Id;
						newLineItemUdp.Version = 0;
						newLineItemUdp.InsertedAt = default(DateTime);
						newLineItemUdp.InsertedBy = this.CurrentUserId;
						newLineItemUdp.UpdatedAt = null;
						newLineItemUdp.UpdatedBy = null;

						newLineItemUpds.Add(newLineItemUdp);
					}
				}

				if (newLineItemUpds.Any())
				{
					var ids = new Queue<int>(new UserDefinedColumnValueLogic().GetNextIds(newLineItemUpds.Count));

					foreach (var item in newLineItemUpds)
					{
						item.Id = ids.Dequeue();
					}

					if (autoSave)
					{
						new UserDefinedColumnValueLogic().BulkSave(newLineItemUpds);
					}
				}
			}

			return newLineItemUpds;
		}

		private IEnumerable<UserDefinedcolValEntity> CopyResourceUDP(bool autoSave = true)
		{
			var newResourceUpds = new List<UserDefinedcolValEntity>();

			var oldLineItemUdps = new UserDefinedColumnValueLogic().GetSearchList(e => e.TableId == (int)userDefinedColumnTableIds.EstimateResource && e.Pk1 == _EstimateCopyOption.SourceEstHeaderFk);

			if (oldLineItemUdps.Any())
			{
				var oldResourceId2UdpMap = oldLineItemUdps.GroupBy(e => new { e.Pk2, e.Pk3 }).ToDictionary(e => e.Key.Pk2 + "|" + e.Key.Pk3, e => e.FirstOrDefault());

				foreach (var item in _oldResLiId2NewResLiIdMapping)
				{
					var key = item.Key.Item2 + "|" + item.Key.Item1;

					if (oldResourceId2UdpMap.ContainsKey(key))
					{
						var newResourceUdp = oldResourceId2UdpMap[key].Clone() as UserDefinedcolValEntity;
						newResourceUdp.Pk1 = _TargetHeaderId;
						newResourceUdp.Pk2 = item.Value.Item2;
						newResourceUdp.Pk3 = item.Value.Item1;
						newResourceUdp.Version = 0;
						newResourceUdp.InsertedAt = default(DateTime);
						newResourceUdp.InsertedBy = this.CurrentUserId;
						newResourceUdp.UpdatedAt = null;
						newResourceUdp.UpdatedBy = null;

						newResourceUpds.Add(newResourceUdp);
					}
				}

				if (newResourceUpds.Any())
				{
					var ids = new Queue<int>(new UserDefinedColumnValueLogic().GetNextIds(newResourceUpds.Count));

					foreach (var item in newResourceUpds)
					{
						item.Id = ids.Dequeue();
					}

					if (autoSave)
					{
						new UserDefinedColumnValueLogic().BulkSave(newResourceUpds);
					}
				}
			}

			return newResourceUpds;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="isCopyByDragDropSearchWizard"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> CopyLineItemsForAssembly(IEnumerable<EstLineItemEntity> lineItems, bool isCopyByDragDropSearchWizard = false)
		{
			if (!this._ProjectId.HasValue || lineItems == null || !lineItems.Any())
			{
				return lineItems;
			}

			SetIsFromBidForLineItem(lineItems);

			// filter out the lineitems generate by rule
			List<EstLineItemEntity> lineItemsToCopy = lineItems.Where(e => e.EstRuleSourceFk == null).ToList();

			if (!lineItemsToCopy.Any())
			{
				return lineItemsToCopy;
			}

			//Project Assemblies
			List<EstLineItemEntity> projectAssemblies = new List<EstLineItemEntity>();

			if (_EstimateCopyOption.FromAssembly == "AssemblyMaster")
			{
				var prjAsssemblyHeader = RVPARB.BusinessEnvironment.GetExportedValue<IProjectAssemblyLogic>().GetByProject(this._ProjectId.Value);

				var prjHeaderFk = prjAsssemblyHeader != null ? prjAsssemblyHeader.EstHeaderFk : -1;

				projectAssemblies = new EstimateMainLineItemLogic().GetPrjAssemblies(e => e.IsTemp == false && e.LineItemType == 1 && e.EstHeaderFk == prjHeaderFk).ToList();

				// repace master assembly by prject assembly
				this.RepaceLineItemByPrjAssembly(lineItemsToCopy, projectAssemblies);
			}

			new EstimateMainLineItemLogic().AssignQtyRel(lineItemsToCopy, this._EstimateCopyOption.EstHeaderFk, lineItemsToCopy[0].EstQtyTelAotFk);

			/* generated hint todo check for project deep copy? */
			GenerateLineItemHint(lineItemsToCopy, false, true);

			/* prepare data */
			var masterAssemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();

			var lineItemCopyEntityList = new List<LineItemCopyEntity>();

			var headerId2LineItemsMap = lineItemsToCopy.GroupBy(e => e.EstHeaderFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var item in headerId2LineItemsMap)
			{
				var estLineItemCopyOption = new EstLineItemCopyOption()
				{
					SourceEstHeaderId = item.Key,
					TargetEstHeaderId = this._EstimateCopyOption.EstHeaderFk
				};

				var lineItemIds = item.Value.Select(e => e.Id).ToList();

				/* get resources tree */
				List<EstResourceEntity> resourcesOfCurrentHeader = new EstimateMainResourceLogic().GetTreeOfHeaderSimple(item.Key, lineItemIds).ToList();

				if (_EstimateCopyOption.FromAssembly == "AssemblyMaster")
				{
					// repace resource of master assembly by project assembly
					this.RepaceResourceByPrjAssembly(resourcesOfCurrentHeader, projectAssemblies, masterAssemblyHeaderId);
				}

				foreach (var lineItem in item.Value)
				{
					/* attach resources tree to lineItem */
					var resouces = resourcesOfCurrentHeader.Where(e => e.EstLineItemFk == lineItem.Id).ToList();

					if (lineItem.DescriptionInfo != null)
					{
						var description = lineItem.DescriptionInfo.Description;

						var translated = lineItem.DescriptionInfo.Translated;

						lineItem.EstAssemblyDescriptionInfo = new DescriptionTranslateType(translated, translated);

						lineItem.CopyTranslate<EstLineItemEntity>(this._LineItemLogic.UserLanguageId, new Func<EstLineItemEntity, DescriptionTranslateType>[] { e => e.EstAssemblyDescriptionInfo });

					}

					lineItemCopyEntityList.Add(new LineItemCopyEntity(lineItem, resouces, estLineItemCopyOption));
				}

			}

			/* do lineItem and resource copy */
			this.CopyLineItemsForRequest(lineItemCopyEntityList, isCopyByDragDropSearchWizard);

			/* load project information to attach rule and Param for different projects */
			var targetLineItem = lineItemCopyEntityList.Select(e => e.TargetLineItem).ToList();

			var canCopyPrcPackageItemAssingment = new SystemOptionLogic().GetValueAsBool(SystemOption.CanCopyPrcPackageItemAssingment);

			if (this._EstimateCopyOption.ProjectId == this._EstimateCopyOption.SourceProjectId && canCopyPrcPackageItemAssingment)
			{
				var oldLineItems = lineItemCopyEntityList.Select(e => e.SourceLineItem).ToList();

				this._EstimateCopyOption.SourceEstHeaderFk = oldLineItems.Select(e => e.EstHeaderFk).FirstOrDefault();

				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (_estCopyOptionEntity.LiPackageItemAssignment.Value)
					{
						CopyPrcItemAssignments(oldLineItems);
						DeletePrcItemAssignments(oldLineItems);
					}
				}
				else
				{
					CopyPrcItemAssignments(oldLineItems);
					DeletePrcItemAssignments(oldLineItems);
				}
			}

			this._LineItemLogic.CalculatePackageByLineItems(targetLineItem);

			var lineItemProjectId = targetLineItem.Select(e => e.ProjectFk).First();

			if (this._ProjectId.Value != lineItemProjectId)
			{

				var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel);
				var prjEntity = dbContext.Entities<EstPrjHeaderVEntity>().FirstOrDefault(e => e.PrjProjectFk == this._ProjectId.Value);
				this._LineItemLogic.CollectAdditionalInfos4LineItems(dbContext, targetLineItem, prjEntity);
			}

			return targetLineItem;
		}

		/// <summary>
		///
		/// </summary>
		private void RepaceLineItemByPrjAssembly(List<EstLineItemEntity> lineItemsToCopy, List<EstLineItemEntity> projectAssemblies)
		{
			List<EstLineItemEntity> prjAssemblies = new List<EstLineItemEntity>();

			List<EstLineItemEntity> removeLineItems = new List<EstLineItemEntity>();

			foreach (var lineItemToCopy in lineItemsToCopy)
			{
				var prjAssembly = projectAssemblies.FirstOrDefault(e => e.EstHeaderAssemblyFk == lineItemToCopy.EstHeaderFk && e.EstAssemblyFk == lineItemToCopy.Id && e.LgmJobFk == this._TargetHeader.LgmJobFk);

				prjAssembly ??= projectAssemblies.FirstOrDefault(e => e.EstHeaderAssemblyFk == lineItemToCopy.EstHeaderFk && e.EstAssemblyFk == lineItemToCopy.Id && !e.LgmJobFk.HasValue);

				if (prjAssembly != null)
				{
					prjAssemblies.Add(prjAssembly);

					removeLineItems.Add(lineItemToCopy);
				}
			}

			foreach (var removeLineItem in removeLineItems)
			{
				lineItemsToCopy.Remove(removeLineItem);
			}

			lineItemsToCopy.AddRange(prjAssemblies);
		}

		/// <summary>
		///
		/// </summary>
		private void RepaceResourceByPrjAssembly(List<EstResourceEntity> resourcesOfCurrentHeader, List<EstLineItemEntity> projectAssemblies, int masterAssemblyHeaderId)
		{
			foreach (var resource in resourcesOfCurrentHeader)
			{
				if (resource.EstHeaderAssemblyFk.HasValue && resource.EstAssemblyFk.HasValue)
				{
					var prjAssembly = masterAssemblyHeaderId == resource.EstHeaderAssemblyFk ?
						projectAssemblies.FirstOrDefault(e => e.EstHeaderAssemblyFk == resource.EstHeaderAssemblyFk && e.EstAssemblyFk == resource.EstAssemblyFk && e.LgmJobFk == this._TargetHeader.LgmJobFk) :
						projectAssemblies.FirstOrDefault(e => e.EstHeaderFk == resource.EstHeaderAssemblyFk && e.Id == resource.EstAssemblyFk && e.LgmJobFk == this._TargetHeader.LgmJobFk);

					prjAssembly ??= masterAssemblyHeaderId == resource.EstHeaderAssemblyFk ?
						projectAssemblies.FirstOrDefault(e => e.EstHeaderAssemblyFk == resource.EstHeaderAssemblyFk && e.EstAssemblyFk == resource.EstAssemblyFk && !e.LgmJobFk.HasValue) :
						projectAssemblies.FirstOrDefault(e => e.EstHeaderFk == resource.EstHeaderAssemblyFk && e.Id == resource.EstAssemblyFk && !e.LgmJobFk.HasValue);

					if (prjAssembly != null)
					{
						resource.EstHeaderAssemblyFk = prjAssembly.EstHeaderFk;
						resource.EstAssemblyFk = prjAssembly.Id;
					}
				}
			}
		}

		private IEnumerable<LineItemCopyEntity> CopyLineItemsForRequest(IEnumerable<LineItemCopyEntity> lineItemCopyEntities, bool isCopyByDragDropSearchWizard = false)
		{
			/* translate the source lineItem */
			SourceLineItemTranslate(lineItemCopyEntities);

			/* clone line item */
			var newLineItemIds = CreateNewLineItemIds(lineItemCopyEntities.Count());

			foreach (var lineItemToCopy in lineItemCopyEntities)
			{
				if (lineItemToCopy == null || lineItemToCopy.SourceLineItem == null)
				{
					continue;
				}

				lineItemToCopy.TargetLineItem = CreateLineItemCopy(lineItemToCopy, newLineItemIds.Pop(), lineItemToCopy.LineItemCopyOption);

				if (!this._Old2NewLineItemIdMapping.ContainsKey(lineItemToCopy.SourceLineItem.IdentificationData))
				{
					_Old2NewLineItemIdMapping.AddOrUpdate(lineItemToCopy.SourceLineItem.IdentificationData, lineItemToCopy.TargetLineItem.IdentificationData, (Key, Value) => lineItemToCopy.TargetLineItem.IdentificationData);
				}

				GetLgmJobForLineItem(lineItemToCopy);

				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (this._isCopyLineItemQuantity)
					{
						_estCopyOptionLogic.CheckLineItemQuantity(lineItemToCopy.TargetLineItem);
					}

					_estCopyOptionLogic.CheckLineItemQuantityAndCostFactors(lineItemToCopy.TargetLineItem);
				}

				CopyBudget(lineItemToCopy.TargetLineItem);
			}

			GenerateLineItemCodeForSplit(lineItemCopyEntities);

			/* replace the EstLineItemFk with new linItemId */
			ReplaceEstLineItemFk(lineItemCopyEntities);

			var resource = lineItemCopyEntities.Where(e => e.SourceResources != null).SelectMany(e => e.SourceResources).Flatten(e => e.ResourceChildren).ToList();

			this._ProjectScopeObject.LoadCacheDataInBulk(resource);

			if (this._EstimateCopyOption.IsLookAtCopyOptions)
			{
				if (_estCopyOptionEntity.LiResources.Value)
				{
					/* clone resources */
					CopyResourcesOfLineItems(lineItemCopyEntities, isCopyByDragDropSearchWizard);
				}
			}
			else
			{
				/* clone resources */
				CopyResourcesOfLineItems(lineItemCopyEntities, isCopyByDragDropSearchWizard);
			}

			// Get All project BoQs from source line items, BoQItemFk
			List<EstLineItemEntity> allSourceLineItems = new List<EstLineItemEntity>();
			List<IBoqItemEntity> allSourceBoqItems = new List<IBoqItemEntity>();
			if (this._EstimateCopyOption.FromAssembly != "AssemblyMaster")
			{
				var sourceLineItemIds = lineItemCopyEntities?.Select(e => e?.SourceLineItem?.Id);

				allSourceLineItems = new EstimateMainLineItemLogic().GetListByFilter(e => sourceLineItemIds.Contains(e.Id))?.ToList();

				var sourceLineItemBoqItemIds = allSourceLineItems?.Select(e => e.BoqItemFk)
																					 .Where(boqItemFk => boqItemFk.HasValue)
																					 .Select(boqItemFk => boqItemFk.Value)
																					 .ToList();

				if (sourceLineItemBoqItemIds?.Count > 0)
				{
					allSourceBoqItems = BoqContext.BoqItemLogic.GetBoqItemsByBoqItemIds(sourceLineItemBoqItemIds)?.ToList();
				}
			}

			foreach (var lineItemToCopy in lineItemCopyEntities)
			{
				/*replace Leading Structure assignment when Source and Target Project are not same when using Search and Copy LineItem Wizard*/
				if (this._EstimateCopyOption.FromAssembly == "AssemblyMaster")
				{
					lineItemToCopy.TargetLineItem.BoqItemFk = this._EstimateCopyOption.SelectedTargetBoqItemId.HasValue && this._EstimateCopyOption.SelectedTargetBoqItemId > 0 ? this._EstimateCopyOption.SelectedTargetBoqItemId : null;
					lineItemToCopy.TargetLineItem.BoqHeaderFk = this._EstimateCopyOption.SelectedTargetBoqHeaderId.HasValue && this._EstimateCopyOption.SelectedTargetBoqHeaderId > 0 ? this._EstimateCopyOption.SelectedTargetBoqHeaderId : null;
				}
				else
				{
					if (!this._IsSameProject)
					{
						ReplaceBoqItemFk(lineItemToCopy.TargetLineItem);

						ReplaceCosInstanceFk(lineItemToCopy.TargetLineItem);

						ReplaceActivityFk(lineItemToCopy.TargetLineItem);

						ReplaceLocationFk(lineItemToCopy.TargetLineItem);

						ReplaceControllingUnitFk(lineItemToCopy.TargetLineItem);
					}
					else
					{
						if (this._EstimateCopyOption.IsLookAtCopyOptions)
						{
							_estCopyOptionLogic.CheckLineItemLeadingStructures(lineItemToCopy.TargetLineItem);

							if (!_estCopyOptionEntity.LiBoq.Value)
							{
								lineItemToCopy.TargetLineItem.BoqItemFk = this._EstimateCopyOption.SelectedTargetBoqItemId.HasValue && this._EstimateCopyOption.SelectedTargetBoqItemId > 0 ? this._EstimateCopyOption.SelectedTargetBoqItemId : null;
								lineItemToCopy.TargetLineItem.BoqHeaderFk = this._EstimateCopyOption.SelectedTargetBoqHeaderId.HasValue && this._EstimateCopyOption.SelectedTargetBoqHeaderId > 0 ? this._EstimateCopyOption.SelectedTargetBoqHeaderId : null;
							}
							else
							{
								//Handle only scenario 6.
								if (lineItemToCopy.SourceLineItem.BoqItemFk.HasValue &&
									lineItemToCopy.SourceLineItem.BoqItemFk > 0 &&
									lineItemToCopy.SourceLineItem.BoqHeaderFk.HasValue &&
									lineItemToCopy.SourceLineItem.BoqHeaderFk > 0)
								{
									var sourceLineItem = allSourceLineItems?.FirstOrDefault(e => e.Id == lineItemToCopy.SourceLineItem.Id && e.EstHeaderFk == lineItemToCopy.SourceLineItem.EstHeaderFk);

									if (sourceLineItem != null)
									{
										lineItemToCopy.TargetLineItem.BoqItemFk = sourceLineItem.BoqItemFk;
										lineItemToCopy.TargetLineItem.BoqHeaderFk = sourceLineItem.BoqHeaderFk;

										var sourceBoqItem = allSourceBoqItems?.FirstOrDefault(e => e.Id == sourceLineItem.BoqItemFk && e.BoqHeaderFk == sourceLineItem.BoqHeaderFk);

										if (sourceBoqItem != null)
										{
											lineItemToCopy.TargetLineItem.Quantity = sourceLineItem.Quantity;
											lineItemToCopy.TargetLineItem.QuantityTarget = sourceBoqItem.QuantityAdj;
											lineItemToCopy.TargetLineItem.QuantityTargetDetail = sourceBoqItem.QuantityAdjDetail;
											lineItemToCopy.TargetLineItem.WqQuantityTarget = sourceBoqItem.Quantity;
											lineItemToCopy.TargetLineItem.WqQuantityTargetDetail = sourceBoqItem.QuantityDetail;
										}
									}
								}
								else
								{
									lineItemToCopy.TargetLineItem.BoqItemFk = null;
									lineItemToCopy.TargetLineItem.BoqHeaderFk = null;
								}
							}
						}
					}
				}
			}

			/* copy project cost code and replace prjCostCodeFk; no include project assembly */
			CopyAndReplaceProjectMaterial(lineItemCopyEntities, true);

			/* copy userDefinedColVal */
			CopyUserDefinedPriceOfResource(lineItemCopyEntities);
			CopyUserDefinedPriceOfLineitem(lineItemCopyEntities);

			/* update resource information from project costcode and project material */
			UpdateRateFromProject(lineItemCopyEntities);

			/* Create Project Exchange Rate for Resource Currency */
			CreateProjectExchangeRate(lineItemCopyEntities);

			/* attach exchangeRate to resources */
			AttachExchangeRateToResources(lineItemCopyEntities);

			/* recalculate system paramter detail */
			CalculteSystemParamDetail(lineItemCopyEntities);

			/* recalculate lineitem and resources */
			Recalculate(lineItemCopyEntities);
			RecalculateUserDefinedPriceOfLineitem(lineItemCopyEntities);

			/**/
			using (var transaction = TransactionScopeFactory.Create())
			{
				var assemblyTemplate2LineItemLogic = new AssemblyTemplate2LineItemLogic(this._ProjectId ?? 0);
				/* save Project Assembly*/
				if (this._ProjectId.HasValue)
				{
					CopyProjectAssemblies(lineItemCopyEntities, this._ProjectId.Value, assemblyTemplate2LineItemLogic, this._EstimateCopyOption.SourceProjectId);

					CopyProjectPlantAssemblies(lineItemCopyEntities, this._ProjectId.Value);
				}

				Save(lineItemCopyEntities);

				// select lineitems to copy, only copy rule and parameter to assembly cat at estimate module
				assemblyTemplate2LineItemLogic.DoCopyRulesAndParams(true);
				assemblyTemplate2LineItemLogic.Save();

				/* auto assign default characteristics to resource */
				AutoAssignDefaultCharacteristics(lineItemCopyEntities);

				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (_estCopyOptionEntity.ResCharacteristics.Value)
					{
						/* copy resource characteristics */
						CopyCharacteristicsOfResource(lineItemCopyEntities);
					}
				}
				else
				{
					/* copy resource characteristics */
					CopyCharacteristicsOfResource(lineItemCopyEntities);
				}

				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (_estCopyOptionEntity.LiCharacteristics.Value)
					{
						/* copy lineitems characteristics */
						CopyCharacteristicsOfLineItem(lineItemCopyEntities);
					}
				}
				else
				{
					/* copy lineitems characteristics */
					CopyCharacteristicsOfLineItem(lineItemCopyEntities);
				}

				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (_estCopyOptionEntity.LiPrjCostGroup.Value)
					{
						//Copy only Project cost groups
						CopyLineItemProjectOrEnterpriseCostGroups(lineItemCopyEntities, true);
					}

					if (_estCopyOptionEntity.LiCostGroup.Value)
					{
						//Copy only Enterprise cost groups
						CopyLineItemProjectOrEnterpriseCostGroups(lineItemCopyEntities, false);
					}
				}
				else
				{
					/* Copy LineItem cost Groups */
					CopyLineItemCostGroup(lineItemCopyEntities);
				}

				/* copy rule and parameter of lineitem */
				CopyRuleAndParamsOfLineItem(lineItemCopyEntities);

				CopyMdlObjectAndQuantities(lineItemCopyEntities);

				transaction.Complete();
			}

			return lineItemCopyEntities;
		}


		/// <summary>
		///
		/// </summary>
		public IEnumerable<LineItemCopyEntity> CopyResourcesToLineItemByDragDrop(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{

			var resource = lineItemCopyEntities.Where(e => e.SourceResources != null).SelectMany(e => e.SourceResources).Flatten(e => e.ResourceChildren).ToList();

			this._ProjectScopeObject.LoadCacheDataInBulk(resource);

			foreach (var lineItemToCopy in lineItemCopyEntities)
			{
				new EstimateMainResourceLogic().AttachUserDefinedColumnVals(lineItemToCopy.SourceResources, lineItemToCopy.SourceLineItem.EstHeaderFk, lineItemToCopy.SourceLineItem.Id);
			}

			if (this._EstimateCopyOption.IsLookAtCopyOptions)
			{
				if (_estCopyOptionEntity.LiResources.Value)
				{
					/* clone resources */
					CopyResourcesOfLineItemsDragDrop(lineItemCopyEntities);
				}
			}
			else
			{
				/* clone resources */
				CopyResourcesOfLineItemsDragDrop(lineItemCopyEntities);
			}

			foreach (var lineItemToCopy in lineItemCopyEntities)
			{
				/*replace Leading Structure assignment when Source and Target Project are not same when using Search and Copy LineItem Wizard*/
				if (this._EstimateCopyOption.FromAssembly == "AssemblyMaster")
				{
					lineItemToCopy.TargetLineItem.BoqItemFk = this._EstimateCopyOption.SelectedTargetBoqItemId.HasValue && this._EstimateCopyOption.SelectedTargetBoqItemId > 0 ? this._EstimateCopyOption.SelectedTargetBoqItemId : null;
					lineItemToCopy.TargetLineItem.BoqHeaderFk = this._EstimateCopyOption.SelectedTargetBoqHeaderId.HasValue && this._EstimateCopyOption.SelectedTargetBoqHeaderId > 0 ? this._EstimateCopyOption.SelectedTargetBoqHeaderId : null;
				}
				else
				{
					if (!this._IsSameProject)
					{
						ReplaceBoqItemFk(lineItemToCopy.TargetLineItem);

						ReplaceCosInstanceFk(lineItemToCopy.TargetLineItem);

						ReplaceActivityFk(lineItemToCopy.TargetLineItem);

						ReplaceLocationFk(lineItemToCopy.TargetLineItem);

						ReplaceControllingUnitFk(lineItemToCopy.TargetLineItem);
					}
					else
					{
						if (this._EstimateCopyOption.IsLookAtCopyOptions)
						{
							_estCopyOptionLogic.CheckLineItemLeadingStructures(lineItemToCopy.TargetLineItem);

							if (!_estCopyOptionEntity.LiBoq.Value)
							{
								lineItemToCopy.TargetLineItem.BoqItemFk = this._EstimateCopyOption.SelectedTargetBoqItemId.HasValue && this._EstimateCopyOption.SelectedTargetBoqItemId > 0 ? this._EstimateCopyOption.SelectedTargetBoqItemId : null;
								lineItemToCopy.TargetLineItem.BoqHeaderFk = this._EstimateCopyOption.SelectedTargetBoqHeaderId.HasValue && this._EstimateCopyOption.SelectedTargetBoqHeaderId > 0 ? this._EstimateCopyOption.SelectedTargetBoqHeaderId : null;
							}
							else
							{
								var sourceLineItem = new EstimateMainLineItemLogic().GetLineItemById(lineItemToCopy.SourceLineItem.Id, lineItemToCopy.SourceLineItem.EstHeaderFk).FirstOrDefault();
								if (sourceLineItem != null && sourceLineItem.BoqItemFk.HasValue && sourceLineItem.BoqItemFk > 0 && sourceLineItem.BoqHeaderFk.HasValue && sourceLineItem.BoqHeaderFk > 0)
								{
									lineItemToCopy.TargetLineItem.BoqItemFk = sourceLineItem.BoqItemFk;
									lineItemToCopy.TargetLineItem.BoqHeaderFk = sourceLineItem.BoqHeaderFk;
								}
								else
								{
									lineItemToCopy.TargetLineItem.BoqItemFk = null;
									lineItemToCopy.TargetLineItem.BoqHeaderFk = null;
								}
							}
						}
					}
				}
			}


			/* copy project cost code and replace prjCostCodeFk; no include project assembly */
			CopyAndReplaceProjectMaterial(lineItemCopyEntities, true);

			/* update resource information from project costcode and project material */
			UpdateRateFromProject(lineItemCopyEntities);

			/* Create Project Exchange Rate for Resource Currency */
			CreateProjectExchangeRate(lineItemCopyEntities);

			/* attach exchangeRate to resources */
			AttachExchangeRateToResources(lineItemCopyEntities);

			/* recalculate lineitem and resources */
			Recalculate(lineItemCopyEntities);

			UpdateSubItemCode(lineItemCopyEntities);

			/**/
			using (var transaction = TransactionScopeFactory.Create())
			{
				var assemblyTemplate2LineItemLogic = new AssemblyTemplate2LineItemLogic(this._ProjectId ?? 0);
				/* save Project Assembly*/
				if (this._ProjectId.HasValue)
				{
					CopyProjectAssemblies(lineItemCopyEntities, this._ProjectId.Value, assemblyTemplate2LineItemLogic, this._EstimateCopyOption.SourceProjectId);

					CopyProjectPlantAssemblies(lineItemCopyEntities, this._ProjectId.Value);
				}

				/* copy rule and parameter of lineitem */
				CopyRuleAndParamsOfLineItem(lineItemCopyEntities);

				/* modify the ruleSourceFk of resources which generated by rule */
				var allTargetLineItems = lineItemCopyEntities.Select(li => li.TargetLineItem).ToList();
				var allRuleSourceEntities = new List<EstRuleSourceEntity>();

				foreach (var lineItemToCopy in lineItemCopyEntities)
				{
					var targetResources = DeepCopyEstRuleSource(allTargetLineItems, lineItemToCopy.TargetResources, true);
					allRuleSourceEntities.AddRange(targetResources);
				}

				new EstRuleResourceLogic().Save(allRuleSourceEntities);

				Save(lineItemCopyEntities, false);

				// select lineitems to copy, only copy rule and parameter to assembly cat at estimate module
				assemblyTemplate2LineItemLogic.DoCopyRulesAndParams(true);
				assemblyTemplate2LineItemLogic.Save();

				/* auto assign default characteristics to resource */
				AutoAssignDefaultCharacteristics(lineItemCopyEntities);

				_estCopyOptionEntity = new EstimateCopyOptionLogic(this._TargetHeaderId).GetCopyOptionByEstHeaderId(this._TargetHeaderId);

				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (_estCopyOptionEntity.ResCharacteristics.Value)
					{
						/* copy resource characteristics */
						CopyCharacteristicsOfResource(lineItemCopyEntities);
					}
				}
				else
				{
					/* copy resource characteristics */
					CopyCharacteristicsOfResource(lineItemCopyEntities);
				}

				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (_estCopyOptionEntity.LiCharacteristics.Value)
					{
						/* copy lineitems characteristics */
						CopyCharacteristicsOfLineItem(lineItemCopyEntities);
					}
				}
				else
				{
					/* copy lineitems characteristics */
					CopyCharacteristicsOfLineItem(lineItemCopyEntities);
				}

				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (_estCopyOptionEntity.LiPrjCostGroup.Value)
					{
						//Copy only Project cost groups
						CopyLineItemProjectOrEnterpriseCostGroups(lineItemCopyEntities, true);
					}

					if (_estCopyOptionEntity.LiCostGroup.Value)
					{
						//Copy only Enterprise cost groups
						CopyLineItemProjectOrEnterpriseCostGroups(lineItemCopyEntities, false);
					}
				}
				else
				{
					/* Copy LineItem cost Groups */
					CopyLineItemCostGroup(lineItemCopyEntities);
				}

				SaveUserDefinedPrice(lineItemCopyEntities);

				CopyMdlObjectAndQuantities(lineItemCopyEntities);

				transaction.Complete();
			}

			return lineItemCopyEntities;
		}

		private void SaveUserDefinedPrice(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			var copyEntities = lineItemCopyEntities.Where(e => e.SourceLineItem != null && e.TargetLineItem != null && e.SourceResources != null).ToList();
			foreach (var lineItemCopyEntity in copyEntities)
			{
				if (lineItemCopyEntity.TargetResources != null && lineItemCopyEntity.TargetResources.Any())
				{
					new UserDefinedColumnValueLogic().BulkUpdate(new UserDefinedColumnValueComplete()
					{
						UserDefinedColumnValueToUpdate = lineItemCopyEntity.TargetResources.FlattenResources().Where(e => e.UserDefinedcolValEntity != null && e.UserDefinedcolValEntity.Id != 0).Select(e => new UserDefinedcolValEntity(e.UserDefinedcolValEntity))
					});
				}
			}
		}

		private void UpdateSubItemCode(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{

			var copyEntities = lineItemCopyEntities.Where(e => e.SourceLineItem != null && e.TargetLineItem != null && e.SourceResources != null).ToList();
			foreach (var lineItemCopyEntity in copyEntities)
			{

				var allResources = new EstimateMainResourceLogic().GetResourcesByLineItemId(lineItemCopyEntity.TargetLineItem.Id);
				var subItemResources = lineItemCopyEntity.TargetResources.Where(e => e.EstResourceTypeFk == (int)EstResourceType.SubItem);

				foreach (var subitem in subItemResources)
				{

					string maxCode = "0";
					string maxSort = "0";

					var parentResource = subitem.EstResourceFk == null ? null : new EstimateMainResourceLogic().GetItemById(subitem.EstResourceFk.Value);

					if (parentResource == null)
					{
						if (allResources.Any(obj => obj.EstResourceTypeFk == (int)EstResourceType.SubItem && obj.EstResourceFk == null))
						{
							int maxLength = allResources.Where(obj => obj.EstResourceTypeFk == (int)EstResourceType.SubItem && obj.EstResourceFk == null).Max(s => s.Code.Length);
							maxCode = allResources.Where(obj => obj.EstResourceTypeFk == (int)EstResourceType.SubItem && obj.EstResourceFk == null && obj.Code.Length == maxLength).Select(obj => obj.Code).DefaultIfEmpty("0").Max().ToString();
						}
						maxSort = allResources.Where(obj => obj.EstResourceFk == null).Select(obj => int.TryParse(obj.Sorting.ToString(), out int sortingValue) ? sortingValue : 0).DefaultIfEmpty(0).Max().ToString();
					}
					else
					{
						var childResources = allResources.Where(p => p.EstResourceFk == parentResource?.Id);
						if (childResources.Any(obj => obj.EstResourceTypeFk == (int)EstResourceType.SubItem))
						{
							int maxLength = childResources.Where(obj => obj.EstResourceTypeFk == (int)EstResourceType.SubItem).Max(s => s.Code.Length);
							maxCode = maxLength == 0 ? "0" : childResources.Where(obj => obj.EstResourceTypeFk == (int)EstResourceType.SubItem && obj.Code.Length == maxLength).Select(obj => obj.Code).DefaultIfEmpty("0").Max().ToString();
						}
						else
						{
							maxCode = parentResource.Code + "0";
						}
						maxSort = childResources.Select(obj => obj.Sorting.ToString()).DefaultIfEmpty(parentResource.Sorting + "0").Max().ToString();
					}

					var parent = allResources.Where(resource => resource.EstResourceFk == null).ToList();
					var pasetedResource = allResources.Where(p => p.EstResourceFk == parentResource.Id && p.EstResourceTypeFk == (int)EstResourceType.SubItem);

					new EstimateMainResourceLogic().GenarteCodeAndSortingDragDrop(maxCode, maxSort, lineItemCopyEntity.TargetResources, true, false, pasetedResource);
				}

			}

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItemCopyEntities"></param>
		/// <param name="projectId"></param>
		/// <param name="assemblyTemplate2LineItemLogic"></param>
		/// <param name="sourceProjectId"></param>
		public void CopyProjectAssemblies(IEnumerable<LineItemCopyEntity> lineItemCopyEntities, int projectId, AssemblyTemplate2LineItemLogic assemblyTemplate2LineItemLogic = null, int? sourceProjectId = null)
		{
			var header = new EstimateMainHeaderLogic().GetItemById(lineItemCopyEntities.First().TargetLineItem.EstHeaderFk);

			List<EstLineItemEntity> lineItems = new List<EstLineItemEntity>();

			List<EstResourceEntity> resourcesToSave = new List<EstResourceEntity>();

			//Assembly Header Id
			var assemblyHeaderId = new Assemblies.BusinessComponents.EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();

			bool isSource2Assembly = lineItemCopyEntities.First().SourceLineItem.EstHeaderFk == assemblyHeaderId;

			List<Tuple<int, int, int>> projectAssembliesToSave = new List<Tuple<int, int, int>>();

			foreach (var lineItemCopyEntity in lineItemCopyEntities)
			{
				//LineItem with EstAssemblyFk
				var lineItem = lineItemCopyEntity.TargetLineItem;
				var targetResources = lineItemCopyEntity.TargetResources;

				var jobId = EstJobHelper.GetJobId(lineItem, header, projectId);

				lineItems.Add(lineItem);
				if (targetResources != null && targetResources.Any())
				{
					resourcesToSave.AddRange(targetResources);
				}

				/* collect project assembly */
				projectAssembliesToSave.AddRange(new EstProjectRelationEntityCollector().CollectProjectAssemblyToSave(
					lineItem,
					targetResources,
					lineItemCopyEntity.SourceResources,
					jobId,
					e => e.EstAssemblyFk,
					e => e.EstHeaderAssemblyFk));
			}

			if (projectAssembliesToSave != null && projectAssembliesToSave.Any())
			{
				projectAssembliesToSave = projectAssembliesToSave.Distinct().ToList();
				Dictionary<int, int> mapAssemblyIdDic = new Dictionary<int, int>();

				var prjAssemblyLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectAssemblyLogic>();
				int? projectHeaderId = null;

				List<int> sourceAssemblyHeaderIds = new List<int>();

				if (this._IsSameProject)
				{
					//Assembly Ids
					var assemblyIds = projectAssembliesToSave.Select(e => e.Item1).ToList();

					//1. Save assemblies to Project
					// TODO: create project assembly from master, changed sp as the c# code
					var prjAssemblyDatas = new EstProjectAssemblyLogic().CreateProjectAssemblyFromMaster(assemblyIds, projectId, assemblyHeaderId);

					var prjAsssemblyHeader = prjAssemblyLogic.GetByProject(projectId);
					if (prjAsssemblyHeader != null)
					{
						projectHeaderId = prjAsssemblyHeader.EstHeaderFk;
					}

					// copy project assembly rule
					new ProjectAssemblyRuleParameterHelper(assemblyHeaderId, this._ProjectId ?? 0, projectHeaderId ?? 0).CopyRuleNParameterForAssemblyNCatalog(prjAssemblyDatas);

					mapAssemblyIdDic = prjAssemblyDatas.Where(e => e.AssemblyOldId.HasValue && e.AssemblyNewId.HasValue).ToDictionary(i => i.AssemblyOldId.Value, i => i.AssemblyNewId.Value);

					// set the project assembly to lineitem
					new EstimateMainLineItemLogic().SetPrjAssemblyToLineItem((int)projectId, lineItems, false, mapAssemblyIdDic);

					// set the project assembly to resource,
					new EstimateMainResourceLogic().SetPrjAssemblyToResource((int)projectId, resourcesToSave, false, mapAssemblyIdDic);
				}
				else
				{
					var prjAssemblies = new EstimateAssembliesLogic().GetProjectAssemblies(this._ProjectId.Value);

					if (prjAssemblies.Count > 0)
					{
						var assemblyIds = projectAssembliesToSave.Select(e => e.Item1).ToArray();
						var assemblyHeaderIds = projectAssembliesToSave.Select(e => e.Item2).ToArray();
						var copyAssemblies = new EstimateAssembliesLogic().GetListByIds(assemblyIds).Where(e => assemblyHeaderIds.Contains(e.EstHeaderFk)).ToList();

						// if the project assemblies => source assemblies, include copy assemblies,
						foreach (var prjAssembly in prjAssemblies)
						{
							var item = copyAssemblies.FirstOrDefault(e => e.Code == prjAssembly.Code && e.EstHeaderAssemblyFk == prjAssembly.EstHeaderAssemblyFk && e.EstAssemblyFk == prjAssembly.EstAssemblyFk);
							if (item != null)
							{
								var toRemoveItem = projectAssembliesToSave.FirstOrDefault(e => e.Item1 == item.Id && e.Item2 == item.EstHeaderFk);
								if (toRemoveItem != null)
								{
									if (!mapAssemblyIdDic.ContainsKey(item.Id))
									{
										mapAssemblyIdDic.Add(item.Id, prjAssembly.Id);
									}

									projectAssembliesToSave.Remove(toRemoveItem);
								}
							}
						}
					}

					if (projectAssembliesToSave.Count > 0)
					{
						sourceAssemblyHeaderIds = projectAssembliesToSave.Where(e => e.Item2 != assemblyHeaderId).Select(i => i.Item2).Distinct().ToList();

						mapAssemblyIdDic = SaveDifferentPrjAssembly(projectAssembliesToSave, this._Old2NewJobIdMappingPrjDeepCopy, this._IsDeepCopyFromProject, (int)projectId, sourceProjectId ?? 0);

						var prjAsssemblyHeader = prjAssemblyLogic.GetByProject(projectId);
						if (prjAsssemblyHeader != null)
						{
							projectHeaderId = prjAsssemblyHeader.EstHeaderFk;
						}
					}

					// set the project assembly to lineitem
					new EstimateMainLineItemLogic().SetPrjAssemblyToLineItem((int)projectId, lineItems, false, mapAssemblyIdDic);

					// set the project assembly to resource,
					new EstimateMainResourceLogic().SetPrjAssemblyToResource((int)projectId, resourcesToSave, false, mapAssemblyIdDic);
				}

				// Copy CostGroups: Copy Cost Groups from master assembly to project assembly
				var estLineItemLogic = new EstimateMainLineItemLogic();

				var sourceAssemblyItemIds = mapAssemblyIdDic.Select(e => e.Key).ToList();
				List<IEstLineItemEntity> sourceAssemblyItems = new List<IEstLineItemEntity>();
				if (this._IsSameProject)
				{
					sourceAssemblyItems = estLineItemLogic.GetLineItemByIdsAndHeader(sourceAssemblyItemIds, assemblyHeaderId, (int)CommonLogic.LineItemTypes.Assembly).ToList();
				}
				else
				{
					foreach (var sourceAssemblyHeaderId in sourceAssemblyHeaderIds)
					{
						sourceAssemblyItems.AddRange(estLineItemLogic.GetLineItemByIdsAndHeader(sourceAssemblyItemIds, sourceAssemblyHeaderId, (int)CommonLogic.LineItemTypes.Assembly).ToList());
					}
				}

				var targetAssemblyItemIds = mapAssemblyIdDic.Select(e => e.Value).ToList();
				var targetAssemblyItems = estLineItemLogic.GetLineItemByIdsAndHeader(targetAssemblyItemIds, projectHeaderId ?? 0, (int)CommonLogic.LineItemTypes.Assembly).ToList();

				if (projectAssembliesToSave.Count > 0)
				{
					if (isSource2Assembly)
					{
						// reset the lineItemCopyEntities TargetResources
						foreach (var item in targetAssemblyItems)
						{
							var existCopyItem = lineItemCopyEntities.FirstOrDefault(e => e.SourceLineItem.Id == item.EstAssemblyFk && e.SourceLineItem.LineItemType == 1);
							if (existCopyItem != null)
							{
								existCopyItem.SourceLineItem = item as EstLineItemEntity;
							}
						}
					}
					else
					{
						// only to copy assembly structure rule and parameters at estimate module
						var itemsToCopy = lineItemCopyEntities.Where(e => e.SourceLineItem.EstAssemblyCatFk.HasValue).DistinctBy(e => e.SourceLineItem.EstAssemblyCatFk.Value);
						foreach (var itemToCopy in itemsToCopy)
						{
							if (itemToCopy.SourceLineItem.EstAssemblyFk.HasValue && mapAssemblyIdDic.ContainsKey(itemToCopy.SourceLineItem.EstAssemblyFk.Value))
							{
								/* copy rule and parameter of assembly to lineItem */
								assemblyTemplate2LineItemLogic.AddLineItemForCopyWithAssemblyKey(itemToCopy.TargetLineItem, mapAssemblyIdDic[itemToCopy.SourceLineItem.EstAssemblyFk.Value], projectHeaderId);
							}
						}
					}

					var estLineItemCopyOption = new EstLineItemCopyOption()
					{
						SourceEstHeaderId = assemblyHeaderId,
						TargetEstHeaderId = projectHeaderId
					};

					var assemblyItemCopies = new List<LineItemCopyEntity>();
					foreach (var item in mapAssemblyIdDic)
					{
						var sourceLineItem = sourceAssemblyItems.FirstOrDefault(e => e.Id == item.Key);
						var targetLineItem = targetAssemblyItems.FirstOrDefault(e => e.Id == item.Value);
						if (sourceLineItem != null && targetLineItem != null)
						{
							var assemblyItemCopy = new LineItemCopyEntity()
							{
								LineItemCopyOption = estLineItemCopyOption,
								SourceLineItem = (EstLineItemEntity)sourceLineItem,
								TargetLineItem = (EstLineItemEntity)targetLineItem
							};

							assemblyItemCopies.Add(assemblyItemCopy);
						}
					}

					new EstLineItem2CostGroupLogic().CopyLineItemCostGroupInSameProject(assemblyItemCopies);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItemCopyEntities"></param>
		/// <param name="projectId"></param>
		public void CopyProjectPlantAssemblies(IEnumerable<LineItemCopyEntity> lineItemCopyEntities, int projectId)
		{
			var parentJobId = this._EstimateCopyOption.TargetEstHeaderJobId.HasValue
				? this._EstimateCopyOption.TargetEstHeaderJobId.Value : this._TargetHeader != null && this._TargetHeader.LgmJobFk.HasValue
					? this._TargetHeader.LgmJobFk.Value : this._TargetProjectJobId;

			var lineItems = lineItemCopyEntities.Select(e => e.TargetLineItem).ToList();

			var targetResources = lineItemCopyEntities.Where(e => e.TargetResources != null && e.TargetResources.Any()).SelectMany(e => e.TargetResources).ToList();

			var jobHelper = new EstJobHelper(projectId);

			// collect project plant assemblies
			jobHelper.CollectAllPrjPlantAssembliesToSave(lineItems, targetResources, parentJobId);

			// save project plant assemblies
			jobHelper.Save();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="old2CosLineItemMap"></param>
		/// <param name="copyResOnly"></param>
		/// <returns></returns>
		public IEnumerable<LineItemCopyEntity> CopyLineItemsForCos(IEnumerable<KeyValuePair<EstLineItemEntity, EstLineItemEntity>> old2CosLineItemMap, bool copyResOnly = false)
		{
			if (old2CosLineItemMap == null || !old2CosLineItemMap.Any())
			{
				return new List<LineItemCopyEntity>();
			}

			if (!this._isCopyLineItemQuantity)
			{
				this._isCopyLineItemQuantity = true;
			}

			var lineItemIds = old2CosLineItemMap.Where(e => e.Value != null).Select(e => e.Value.Id).Distinct().ToList();

			/* prepare data */
			var resources = new EstimateMainResourceLogic().GetTreeOfHeaderSimple(this._TargetHeaderId, lineItemIds);

			var lineItemCopyEntities = new List<LineItemCopyEntity>();

			foreach (var item in old2CosLineItemMap)
			{
				if (item.Key == null || item.Value == null)
				{
					continue;
				}

				var cosLineItem = item.Value;

				var estLineItemCopyOption = new EstLineItemCopyOption()
				{
					SourceEstHeaderId = cosLineItem.EstHeaderFk,
					TargetEstHeaderId = cosLineItem.EstHeaderFk,
					IgnoreResourceGeneratedByRule = false
				};

				var resourcesOfCosLineItem = resources.Where(e => e.EstLineItemFk == cosLineItem.Id).ToList();

				var lineItemCopyEntity = new LineItemCopyEntity(cosLineItem, resourcesOfCosLineItem, estLineItemCopyOption);

				lineItemCopyEntity.TargetLineItem = item.Key;

				// sync the disable value
				lineItemCopyEntity.TargetLineItem.IsDisabled = cosLineItem.IsDisabled;

				lineItemCopyEntity.DoAfterResourceClone = this.DoAfterResourceCloneForCos;

				lineItemCopyEntities.Add(lineItemCopyEntity);
			}

			var resource = lineItemCopyEntities.Where(e => e.SourceResources != null).SelectMany(e => e.SourceResources).Flatten(e => e.ResourceChildren).ToList();

			this._ProjectScopeObject.LoadCacheDataInBulk(resource);

			/* clone resources */
			CopyResourcesOfLineItems(lineItemCopyEntities);

			/* attach exchangeRate to resources */
			AttachExchangeRateToResources(lineItemCopyEntities);

			/* copy userDefinedColVal */
			CopyUserDefinedPriceOfResource(lineItemCopyEntities);

			/* recalculate lineitem and resources */
			Recalculate(lineItemCopyEntities, true);

			if (copyResOnly)
			{
				return lineItemCopyEntities;
			}

			/* copy characteristics from assemblies or lineitems */
			CopyCharacteristicsOfLineItemCore(lineItemCopyEntities);

			/* copy the rule and parameter of assembly to lineItem, when it copy from assembly and Override option is true */
			CopyRuleAndParamsFromAssembly(lineItemCopyEntities);

			if (string.IsNullOrEmpty(this._EstimateCopyOption.FromAssembly))
			{
				/* Copy MdlObject And Quantities */
				CopyMdlObjectAndQuantitiesCore(lineItemCopyEntities);

				/* Copy the rule and parameter by project */
				CopyRuleAndParamsByProject(lineItemCopyEntities);
			}

			return lineItemCopyEntities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> CopyLineItemsForCos(IEnumerable<EstLineItemEntity> lineItems)
		{
			if (!this._ProjectId.HasValue || lineItems == null || !lineItems.Any())
			{
				return lineItems;
			}

			if (!this._isCopyLineItemQuantity)
			{
				this._isCopyLineItemQuantity = true;
			}

			var lineItemsToCopy = lineItems.Where(e => e.EstRuleSourceFk == null).ToList();

			/* prepare data */
			var lineItemCopyEntityList = new List<LineItemCopyEntity>();

			var headerId2LineItemsMap = lineItemsToCopy.GroupBy(e => e.EstHeaderFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var item in headerId2LineItemsMap)
			{
				var estLineItemCopyOption = new EstLineItemCopyOption()
				{
					SourceEstHeaderId = item.Key,
					TargetEstHeaderId = this._TargetHeaderId
				};

				var lineItemIds = item.Value.Select(e => e.Id).ToList();

				/* get resources tree */
				var resourcesOfCurrentHeader = new EstimateMainResourceLogic().GetTreeOfHeaderSimple(item.Key, lineItemIds);

				foreach (var lineItem in item.Value)
				{
					/* attach resources tree to lineItem */
					var resouces = resourcesOfCurrentHeader.Where(e => e.EstLineItemFk == lineItem.Id).ToList();

					lineItemCopyEntityList.Add(new LineItemCopyEntity(lineItem, resouces, estLineItemCopyOption));
				}
			}

			//* translate the source lineItem */
			SourceLineItemTranslate(lineItemCopyEntityList);

			/* clone line item */
			var newLineItemIds = CreateNewLineItemIds(lineItemCopyEntityList.Count);

			foreach (var lineItemToCopy in lineItemCopyEntityList)
			{
				if (lineItemToCopy == null || lineItemToCopy.SourceLineItem == null)
				{
					continue;
				}

				lineItemToCopy.TargetLineItem = CreateLineItemCopy(lineItemToCopy, newLineItemIds.Pop(), lineItemToCopy.LineItemCopyOption);
			}

			/* generated lineitem code */
			GenerateLineItemCode(lineItemCopyEntityList);

			/* replace the EstLineItemFk with new linItemId */
			if (this._EstimateCopyOption.SplitQuantityLineItems == null)
			{
				ReplaceEstLineItemFk(lineItemCopyEntityList);
			}

			var resource = lineItemCopyEntityList.Where(e => e.SourceResources != null).SelectMany(e => e.SourceResources).Flatten(e => e.ResourceChildren).ToList();

			this._ProjectScopeObject.LoadCacheDataInBulk(resource);

			/* clone resources */
			CopyResourcesOfLineItems(lineItemCopyEntityList);

			/* copy userDefinedColVal */
			CopyUserDefinedPriceOfResource(lineItemCopyEntityList);

			/* update resource information from project costcode and project material */
			UpdateRateFromProject(lineItemCopyEntityList);

			/* Create Project Exchange Rate for Resource Currency */
			CreateProjectExchangeRate(lineItemCopyEntityList);

			/* attach exchangeRate to resources */
			AttachExchangeRateToResources(lineItemCopyEntityList);

			/* recalculate lineitem and resources */
			Recalculate(lineItemCopyEntityList, true);

			using (var transaction = TransactionScopeFactory.Create())
			{
				/* save lineitem and resource */
				Save(lineItemCopyEntityList);

				/* auto assign default characteristics to resource */
				AutoAssignDefaultCharacteristics(lineItemCopyEntityList);

				/* copy characteristics of resource */
				CopyCharacteristicsOfResource(lineItemCopyEntityList);

				/* Collect And Save LineItem Project Assemblies */
				CollectAndSaveLineItemProjectAssembly(lineItemCopyEntityList);

				/* Copy LineItem cost Groups */
				CopyLineItemCostGroup(lineItemCopyEntityList);

				/* copy characteristics from assemblies or lineitems */
				CopyCharacteristicsOfLineItemCore(lineItemCopyEntityList);

				/* copy the rule and parameter of assembly to lineItem, when it copy from assembly and Override option is true */
				CopyRuleAndParamsFromAssembly(lineItemCopyEntityList);

				if (string.IsNullOrEmpty(this._EstimateCopyOption.FromAssembly))
				{
					/* Copy MdlObject And Quantities */
					CopyMdlObjectAndQuantitiesCore(lineItemCopyEntityList);

					/* Copy the rule and parameter by project */
					CopyRuleAndParamsByProject(lineItemCopyEntityList);
				}

				transaction.Complete();
			}

			return lineItemCopyEntityList.Select(e => e.TargetLineItem).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItemToCopy"></param>
		/// <param name="newId"></param>
		/// <param name="lineItemCopyOption"></param>
		/// <returns></returns>
		protected override EstLineItemEntity CreateLineItemCopy(LineItemCopyEntity lineItemToCopy, int newId, EstLineItemCopyOption lineItemCopyOption)
		{
			var lineItem = base.CreateLineItemCopy(lineItemToCopy, newId, lineItemCopyOption);
	
			lineItem.WqQuantityTarget = lineItem.WqQuantityTarget == 0 ?  1 : lineItem.WqQuantityTarget;
			lineItem.WqQuantityTargetDetail = string.IsNullOrEmpty(lineItem.WqQuantityTargetDetail) ? lineItem.WqQuantityTarget.ToString() : lineItem.WqQuantityTargetDetail;

			if (!this._IsSameProject)
			{
				/*lineItem.PrcPackage2HeaderFk = null;
				lineItem.PrcPackageFk = null;*/
				lineItem.QuantityTargetDetail = lineItem.QuantityTarget.ToString();
				lineItem.PrjChangeFk = null;
				lineItem.PrjCostGroup1Fk = null;
				lineItem.PrjCostGroup2Fk = null;
				lineItem.PrjCostGroup3Fk = null;
				lineItem.PrjCostGroup4Fk = null;
				lineItem.PrjCostGroup5Fk = null;
				lineItem.CostFactorDetail1 = this._EstimateCopyOption.IsLookAtCopyOptions ? lineItem.CostFactorDetail1 : null;
				lineItem.CostFactorDetail2 = this._EstimateCopyOption.IsLookAtCopyOptions ? lineItem.CostFactorDetail1 : null;
				lineItem.ProductivityFactorDetail = this._EstimateCopyOption.IsLookAtCopyOptions ? lineItem.CostFactorDetail1 : null;
				//lineItem.ProjectFk = (Int32)this._ProjectId.Value;
			}

			// Set default value for EstLineItemStatusFk
			var defaultStatus = (BasicsCustomizeEstLineItemStatusEntity)new BasicsCustomizeEstLineItemStatusLogic().GetDefault();

			if (defaultStatus != null)
			{
				lineItem.EstLineItemStatusFk = defaultStatus.Id;
			}

			/* reset assembly relate properties */
			if (!string.IsNullOrEmpty(this._EstimateCopyOption.FromAssembly))
			{
				lineItem.EstAssemblyFk = lineItemToCopy.SourceLineItem.Id;
				lineItem.EstAssemblyCatFk = lineItemToCopy.SourceLineItem.EstAssemblyCatFk;
				lineItem.EstHeaderAssemblyFk = lineItemToCopy.SourceLineItem.EstHeaderFk;
			}

			SetQuantityByLSumUom(lineItem);

			CheckCopyLineItemQuantity(lineItem);

			SetDetailsAfterCopy(lineItem);

			return lineItem;
		}

		private EstLineItemEntity CreateLineItemCopy(EstLineItemEntity sourceLineItem)
		{
			var lineItem = (EstLineItemEntity)(sourceLineItem.Clone());

			lineItem.EstHeaderFk = this._TargetHeaderId;

			/* reset basics properties */
			ResetLineItemProperties(lineItem);

			if (!this._IsSameProject)
			{
				//lineItem.PrcPackage2HeaderFk = null;
				//lineItem.PrcPackageFk = null;
				//lineItem.PrcStructureFk = null;
				lineItem.QuantityTargetDetail = lineItem.QuantityTarget.ToString();
				lineItem.PrjChangeFk = null;
				lineItem.PrjCostGroup1Fk = null;
				lineItem.PrjCostGroup2Fk = null;
				lineItem.PrjCostGroup3Fk = null;
				lineItem.PrjCostGroup4Fk = null;
				lineItem.PrjCostGroup5Fk = null;
				lineItem.CostFactorDetail1 = this._EstimateCopyOption.IsLookAtCopyOptions ? lineItem.CostFactorDetail1 : null;
				lineItem.CostFactorDetail2 = this._EstimateCopyOption.IsLookAtCopyOptions ? lineItem.CostFactorDetail1 : null;
				lineItem.ProductivityFactorDetail = this._EstimateCopyOption.IsLookAtCopyOptions ? lineItem.CostFactorDetail1 : null;
			}

			/* reset assembly relate properties */
			if (!string.IsNullOrEmpty(this._EstimateCopyOption.FromAssembly))
			{
				lineItem.EstAssemblyFk = sourceLineItem.Id;
				lineItem.EstAssemblyCatFk = sourceLineItem.EstAssemblyCatFk;
				lineItem.EstHeaderAssemblyFk = sourceLineItem.EstHeaderFk;
			}

			if (this._EstimateCopyOption.IsCopyBaseCost)
			{
				lineItem.BaseCostUnit = lineItem.CostUnit;
				lineItem.BaseCostTotal = lineItem.CostTotal;
			}

			/* reset description */
			if (sourceLineItem.DescriptionInfo != null)
			{
				lineItem.DescriptionInfo = sourceLineItem.DescriptionInfo;
				lineItem.CopyTranslate(this.UserLanguageId, [e => e.DescriptionInfo]);
			}

			SetQuantityByLSumUom(lineItem);

			CheckCopyLineItemQuantity(lineItem);

			SetDetailsAfterCopy(lineItem);

			return lineItem;
		}

		/// <summary>
		/// Set LineItem Factor Details if it is empty
		/// </summary>
		/// <param name="lineItem"></param>
		/// <returns></returns>
		public EstLineItemEntity SetDetailsAfterCopy(EstLineItemEntity lineItem)
		{
			if (lineItem == null)
			{
				return lineItem;
			}

			lineItem.QuantityDetail = string.IsNullOrEmpty(lineItem.QuantityDetail) ? lineItem.Quantity.ToString() : lineItem.QuantityDetail;
			lineItem.QuantityTargetDetail = string.IsNullOrEmpty(lineItem.QuantityTargetDetail) ? lineItem.QuantityTarget.ToString() : lineItem.QuantityTargetDetail;
			lineItem.WqQuantityTargetDetail = string.IsNullOrEmpty(lineItem.WqQuantityTargetDetail) ? lineItem.WqQuantityTarget.ToString() : lineItem.WqQuantityTargetDetail;

			lineItem.QuantityFactorDetail1 = string.IsNullOrEmpty(lineItem.QuantityFactorDetail1) ? lineItem.QuantityFactor1.ToString() : lineItem.QuantityFactorDetail1;
			lineItem.QuantityFactorDetail2 = string.IsNullOrEmpty(lineItem.QuantityFactorDetail2) ? lineItem.QuantityFactor2.ToString() : lineItem.QuantityFactorDetail2;
			lineItem.ProductivityFactorDetail = string.IsNullOrEmpty(lineItem.ProductivityFactorDetail) ? lineItem.ProductivityFactor.ToString() : lineItem.ProductivityFactorDetail;

			lineItem.CostFactorDetail1 = string.IsNullOrEmpty(lineItem.CostFactorDetail1) ? lineItem.CostFactor1.ToString() : lineItem.CostFactorDetail1;
			lineItem.CostFactorDetail2 = string.IsNullOrEmpty(lineItem.CostFactorDetail2) ? lineItem.CostFactor2.ToString() : lineItem.CostFactorDetail2;

			return lineItem;
		}

		private ConcurrentBag<EstLineItemEntity> CopyLineItemsForPrjDeepCopy(IEnumerable<EstLineItemEntity> sourceLineItems)
		{
			var lineItemsToSave = new ConcurrentBag<EstLineItemEntity>();

			if (sourceLineItems == null || !sourceLineItems.Any())
			{
				return lineItemsToSave;
			}

			/* prepare source to target project sortcodes map */
			var estSortCodeHelper = new EstSortCodeHelper((int)this._ProjectId);

			if (!this._IsSameProject)
			{
				estSortCodeHelper.PrepareSortCodesMap(sourceLineItems);
			}

			/* clone line item */
			var newLineItemIds = new ConcurrentStack<int>(this._LineItemLogic.SequenceManager.GetNextList("EST_LINE_ITEM", sourceLineItems.Count()));

			ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };
			var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

			Parallel.ForEach(sourceLineItems, parallelOptions, sourceLineItem =>
			{
				Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
				if (sourceLineItem != null)
				{
					var targetLineItem = CreateLineItemCopy(sourceLineItem);

					if (newLineItemIds.TryPop(out int newId))
					{
						targetLineItem.Id = newId;
					}

					if (this._IsDeepCopyFromProject)
					{
						targetLineItem.PrjChangeFk = null;
					}

					targetLineItem.IsFixedPrice = !targetLineItem.IsGc && (_EstimateCopyOption.SetFixUnitPrice || targetLineItem.IsFixedPrice);

					if (this._EstimateCopyOption.IsLookAtCopyOptions)
					{
						if (this._isCopyLineItemQuantity)
						{
							_estCopyOptionLogic.CheckLineItemQuantity(targetLineItem);
						}

						_estCopyOptionLogic.CheckLineItemQuantityAndCostFactors(targetLineItem);
					}

					CopyBudget(targetLineItem);

					// deep project
					if (!this._IsSameProject)
					{
						ReplaceBoqItemFk(targetLineItem);

						ReplaceCosInstanceFk(targetLineItem);

						ReplaceActivityFk(targetLineItem);

						ReplaceLocationFk(targetLineItem);

						ReplaceControllingUnitFk(targetLineItem);

						estSortCodeHelper.ReplaceSortCode(sourceLineItem, targetLineItem);

						targetLineItem.LgmJobFk = targetLineItem.LgmJobFk.HasValue ? this.GetMappingJobIdPrjDeepCopy(targetLineItem.LgmJobFk.Value) : targetLineItem.LgmJobFk;
					}
					else
					{
						if (this._EstimateCopyOption.IsCopyFromSourceBoq)
						{
							ReplaceBoqItemFk(targetLineItem);
						}

						if (this._EstimateCopyOption.IsLookAtCopyOptions)
						{
							_estCopyOptionLogic.CheckLineItemLeadingStructures(targetLineItem);
						}
					}

					ReplaceRuleSourceFk(targetLineItem);

					if (targetLineItem.LgmJobFk.HasValue)
					{
						this._LineItemId2JobIdMapping.AddOrUpdate(targetLineItem.Id, targetLineItem.LgmJobFk.Value, (Key, Value) => targetLineItem.LgmJobFk.Value);
					}

					lineItemsToSave.Add(targetLineItem);

					this._Old2NewLineItemIdMapping.AddOrUpdate(sourceLineItem.IdentificationData, targetLineItem.IdentificationData, (Key, Value) => targetLineItem.IdentificationData);
				}
			});

			SetAqQuantityOfOptionalLineItem(lineItemsToSave);

			return lineItemsToSave;
		}

		/// <summary>
		/// AQ Quantity of Optional Line Items will be set to 0.00 as long as related BoQ Positions are not 'Awarded Alternative Item'
		/// </summary>
		/// <param name="lineItemsToSave"></param>
		private void SetAqQuantityOfOptionalLineItem(IEnumerable<EstLineItemEntity> lineItemsToSave)
		{
			if (this._EstimateCopyOption.ClearAqQuantityOfOptionalWithIT || this._EstimateCopyOption.ClearAqQuantityOfOptionalWithoutIT)
			{
				var boqHeaderIds = lineItemsToSave.Where(e => e.BoqHeaderFk.HasValue).Select(e => e.BoqHeaderFk.Value).Distinct().ToList();

				var boqItems = boqHeaderIds.Any() ? BoqContext.BoqItemLogic.GetBoqItemsByBoqHeaderIds(boqHeaderIds) : new List<IBoqItemEntity>();

				var boqItemFk2EntityMap = boqItems.GroupBy(e => new { BoqHeaderFk = e.BoqHeaderFk, Id = e.Id }).ToDictionary(e => new IdentificationData(e.Key.Id, e.Key.BoqHeaderFk), e => e.FirstOrDefault());

				foreach (var lineItem in lineItemsToSave)
				{
					if (lineItem.BoqHeaderFk.HasValue && lineItem.BoqItemFk.HasValue)
					{
						var boqKey = new IdentificationData(lineItem.BoqItemFk.Value, lineItem.BoqHeaderFk.Value);

						if (!boqItemFk2EntityMap.ContainsKey(boqKey) || boqItemFk2EntityMap[boqKey].BasItemType2Fk == (int)EBoqItemType2.AlternativeAwarded)
						{
							continue;
						}

						if (this._EstimateCopyOption.ClearAqQuantityOfOptionalWithIT && boqItemFk2EntityMap[boqKey].BasItemTypeFk == (int)EBoqItemType1.OptionalWithIT)
						{
							lineItem.QuantityTarget = 0;
						}

						if (this._EstimateCopyOption.ClearAqQuantityOfOptionalWithoutIT && boqItemFk2EntityMap[boqKey].BasItemTypeFk == (int)EBoqItemType1.OptionalWithoutIT)
						{
							lineItem.QuantityTarget = 0;
						}
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItem"></param>
		protected override void ResetLineItemProperties(EstLineItemEntity lineItem)
		{
			base.ResetLineItemProperties(lineItem);

			if (!this.IncludeDataGeneratedByRule())
			{
				lineItem.EstRuleSourceFk = null;
			}

			lineItem.WqQuantityTargetDetail = lineItem.WqQuantityTarget.ToString();
			lineItem.DescriptionInfo.Description = null;
			lineItem.DescriptionInfo.DescriptionTr = null;
			lineItem.DescriptionInfo.Modified = true;
			lineItem.UpdatedAt = null;
			lineItem.UpdatedBy = null;
			lineItem.InsertedAt = DateTime.UtcNow;
			lineItem.InsertedBy = this.CurrentUserId;
			lineItem.Version = 0;
		}

		private void GenerateLineItemHint(IEnumerable<EstLineItemEntity> entities, bool isDeepCopyProject, bool isGenerateHintFromAssemblyCat = false)
		{
			var sourceProjectId = this._EstimateCopyOption.SourceProjectId.HasValue ? this._EstimateCopyOption.SourceProjectId.Value : (int)this._EstimateCopyOption.ProjectId;

			var sourceHeaderLookup = this._EstimateCopyOption.EstHeaderSourceTargetMapping != null && this._EstimateCopyOption.EstHeaderSourceTargetMapping.Any() ?
				 this._EstimateCopyOption.EstHeaderSourceTargetMapping.Keys.ToDictionary(e => e.Id, e => e) :
				 new Dictionary<int, EstHeaderEntity>();

			var estLineItemHintGenerator = isDeepCopyProject
						? new EstLineItemHintGenerator(this._EstimateCopyOption.SourceProjectId).SetSourceProjectNumber(this._EstimateCopyOption.SourceProjectNo)
						: this._EstimateCopyOption.IsCopyLineItems
						? new EstLineItemHintGenerator(sourceProjectId).GetSourceProjectNumber(sourceProjectId)
						: new EstLineItemHintGenerator(sourceProjectId);

			var headerIds = isDeepCopyProject ? new List<int>(this._EstimateCopyOption.SourceEstHeaderFk) : entities.Select(e => e.EstHeaderFk).Distinct().ToList();

			var headerEntities = isDeepCopyProject ? sourceHeaderLookup : new EstimateMainHeaderLogic().GetList(headerIds).ToDictionary(e => e.Id, e => e);

			var assemblyProjectNo = this._EstimateCopyOption.IsCopyLineItems ? string.Empty : this._EstimateCopyOption.FromAssembly;

			foreach (var entity in entities)
			{
				if (entity == null || !headerEntities.ContainsKey(entity.EstHeaderFk))
				{
					continue;
				}

				var headerEntity = headerEntities[entity.EstHeaderFk];

				if (isDeepCopyProject || this._EstimateCopyOption.IsCopyLineItems)
				{
					entity.Hint = estLineItemHintGenerator.GenerateLineItemHintForDeepCopy(headerEntity.Code, entity.Code, assemblyProjectNo);
				}
				else if (isGenerateHintFromAssemblyCat)
				{
					IEstimateAssemblyCatalogLogic getAssemblyCategory = RVPARB.BusinessEnvironment.GetExportedValue<IEstimateAssemblyCatalogLogic>();
					List<int> list = new List<int>();
					if (entity.EstAssemblyCatFk.HasValue)
					{
						list.Add((Int32)entity.EstAssemblyCatFk);
					}
					var estCatCode = list.Any() ? getAssemblyCategory.GetAssemblyCatsByIds(list).Select(x => x.Code).FirstOrDefault() : "";
					entity.Hint = estLineItemHintGenerator.GenerateLineItemHint(headerEntity, entity.Code, this._EstimateCopyOption.FromAssembly, estCatCode);
				}
			}
		}

		private void GenerateLineItemCodeForSplit(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			var newLineItems = lineItemCopyEntities.Select(e => e.TargetLineItem).ToList();

			var lineItemCodeGenerator = this._TargetHeader != null ? new EstLineItemCodeGenerator(this._TargetHeader) : new EstLineItemCodeGenerator(this._TargetHeaderId);

			lineItemCodeGenerator.GenerateCode(newLineItems.OrderBy(e => e.Code).ToList());
		}

		private void GenerateLineItemCode(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			var newCodes = new List<string>();

			var codeItem = new EstLineItemEntity();

			var targetHeaderLookup = new Dictionary<int, EstHeaderEntity>();

			if (this._EstimateCopyOption.EstHeaderSourceTargetMapping != null && this._EstimateCopyOption.EstHeaderSourceTargetMapping.Any())
			{
				targetHeaderLookup = this._EstimateCopyOption.EstHeaderSourceTargetMapping.Values.ToDictionary(e => e.Id, e => e);
			}

			var targetHeader = !this._IsDeepCopyFromProject ? this._TargetHeader : (targetHeaderLookup.ContainsKey(this._TargetHeaderId) ? targetHeaderLookup[this._TargetHeaderId] : null);

			var newLineItems = lineItemCopyEntities.Select(e => e.TargetLineItem).ToList();

			foreach (var newLineItem in newLineItems)
			{
				newLineItem.Code = new EstLineItemCodeGenerator(newLineItem.EstHeaderFk).GenerateCode(newLineItem, codeItem, this._EstimateCopyOption.TargetEstHeaderRubricCatId);

				codeItem.Code = newLineItem.Code;

				newCodes.Add(newLineItem.Code);
			}

			var oldCodeNewIdMap = lineItemCopyEntities.Select(e => new KeyValuePair<string, int>(e.SourceLineItem.Code, e.TargetLineItem.Id)).ToList();

			var oldCodeNewIdMapOrderByCode = oldCodeNewIdMap.OrderBy(e => e.Key);

			newCodes = newCodes.OrderBy(e => e).ToList();

			var index = 0;

			foreach (var item in oldCodeNewIdMapOrderByCode)
			{
				string newCode = newCodes[index];

				var ent = newLineItems.FirstOrDefault(e => e.Id == item.Value);

				if (ent != null)
				{
					ent.Code = newCode;
				}

				index++;
			}
		}

		private void GetLgmJobForLineItem(LineItemCopyEntity lineItemToCopy)
		{
			if (lineItemToCopy.TargetLineItem is not null && lineItemToCopy.TargetLineItem.LgmJobFk.HasValue && _Old2NewJobIdMapping.ContainsKey(lineItemToCopy.TargetLineItem.LgmJobFk.Value))
			{
				lineItemToCopy.TargetLineItem.LgmJobFk = _Old2NewJobIdMapping[lineItemToCopy.TargetLineItem.LgmJobFk.Value];
			}
		}

		private void CopyBudget(EstLineItemEntity newLineItem)
		{
			if (this._IsDeepCopyFromEstimate)
			{
				newLineItem.Budget = this._IsCopyCostTotalToBudget ? newLineItem.CostTotal : this._IsCopyBudget ? newLineItem.Budget : 0m;
				newLineItem.BudgetUnit = this._IsCopyCostTotalToBudget ? newLineItem.CostUnit : this._IsCopyBudget ? newLineItem.BudgetUnit : 0m;
				newLineItem.BudgetDifference = this._IsCopyBudget ? newLineItem.BudgetDifference : 0m;
			}
			else
			{
				if (this._EstimateCopyOption.IsLookAtCopyOptions)
				{
					if (!this._IsCopyBudget || !_estCopyOptionEntity.LiBudget.Value)
					{
						newLineItem.BudgetUnit = 0m;
						newLineItem.Budget = 0m;
						newLineItem.BudgetDifference = 0m;
						newLineItem.IsFixedBudget = false;
						newLineItem.IsFixedBudgetUnit = false;
					}
				}
				else
				{
					newLineItem.BudgetUnit = this._IsCopyBudget ? newLineItem.BudgetUnit : 0m;
					newLineItem.Budget = this._IsCopyBudget ? newLineItem.Budget : 0m;
					newLineItem.BudgetDifference = this._IsCopyBudget ? newLineItem.BudgetDifference : 0m;
				}
			}

			if (!this._IsCopyBaseCost)
			{
				newLineItem.BaseCostUnit = 0m;
				newLineItem.BaseCostTotal = 0m;
			}
		}

		private void ReplaceBoqItemFk(EstLineItemEntity newLineItem)
		{
			if (this._EstimateCopyOption.IsLookAtCopyOptions)
			{
				if (_estCopyOptionEntity.LiBoq.Value)
				{
					ReplaceBoq(newLineItem);
				}
				else
				{
					newLineItem.BoqItemFk = this._EstimateCopyOption.SelectedTargetBoqItemId.HasValue && this._EstimateCopyOption.SelectedTargetBoqItemId > 0 ? this._EstimateCopyOption.SelectedTargetBoqItemId : null;
					newLineItem.BoqHeaderFk = this._EstimateCopyOption.SelectedTargetBoqHeaderId.HasValue && this._EstimateCopyOption.SelectedTargetBoqHeaderId > 0 ? this._EstimateCopyOption.SelectedTargetBoqHeaderId : null;
					newLineItem.BoqSplitQuantityFk = null;
				}
			}
			else
			{
				ReplaceBoq(newLineItem);
			}
		}

		private void ReplaceBoq(EstLineItemEntity newLineItem)
		{
			if (this._EstimateCopyOption.BoqSourceTargetMapping != null && this._EstimateCopyOption.BoqSourceTargetMapping.Any() && newLineItem.BoqHeaderFk != null && newLineItem.BoqItemFk != null)
			{
				var sourceBoq = new Tuple<int?, int?>(newLineItem.BoqItemFk.Value, newLineItem.BoqHeaderFk.Value);

				Tuple<int?, int?> targetBoq = null;

				if (this._EstimateCopyOption.BoqSourceTargetMapping.TryGetValue(sourceBoq, out targetBoq))
				{
					newLineItem.BoqItemFk = targetBoq != null ? targetBoq.Item1 : null;

					newLineItem.BoqHeaderFk = targetBoq != null ? targetBoq.Item2 : null;

					if (this._EstimateCopyOption.BoqSplitQuantitySourceTargetMapping != null && this._EstimateCopyOption.BoqSplitQuantitySourceTargetMapping.Any() && newLineItem.BoqSplitQuantityFk.HasValue)
					{
						var sourceBoqSplitQty = new Tuple<int?, int?, int?>(sourceBoq.Item2, sourceBoq.Item1, newLineItem.BoqSplitQuantityFk.Value);

						Tuple<int?, int?, int?> targetBoqSplitQty = null;

						if (this._EstimateCopyOption.BoqSplitQuantitySourceTargetMapping.TryGetValue(sourceBoqSplitQty, out targetBoqSplitQty))
						{
							if (newLineItem.BoqHeaderFk == targetBoqSplitQty.Item1 &&
										 newLineItem.BoqItemFk == targetBoqSplitQty.Item2)
							{
								newLineItem.BoqSplitQuantityFk = targetBoqSplitQty.Item3;
							}
						}
					}
				}
				else
				{
					newLineItem.BoqItemFk = null;

					newLineItem.BoqHeaderFk = null;

					newLineItem.BoqSplitQuantityFk = null;
				}
			}
			else
			{
				newLineItem.BoqItemFk = null;

				newLineItem.BoqHeaderFk = null;

				newLineItem.BoqSplitQuantityFk = null;
			}
		}

		private void ReplaceCosInstanceFk(EstLineItemEntity newLineItem)
		{
			if (this._EstimateCopyOption.CosInstanceSourceTargetMapping != null && this._EstimateCopyOption.CosInstanceSourceTargetMapping.Any() && newLineItem.CosInsHeaderFk != null && newLineItem.CosInstanceFk != null)
			{
				var sourceCosInstance = new Tuple<int, int>(newLineItem.CosInstanceFk.Value, newLineItem.CosInsHeaderFk.Value);

				Tuple<int, int> targetCosInstance = null;

				if (this._EstimateCopyOption.CosInstanceSourceTargetMapping.TryGetValue(sourceCosInstance, out targetCosInstance))
				{
					newLineItem.CosInstanceFk = targetCosInstance != null ? targetCosInstance.Item1 : null;

					newLineItem.CosInsHeaderFk = targetCosInstance != null ? targetCosInstance.Item2 : null;
				}
				else
				{
					newLineItem.CosInstanceFk = null;

					newLineItem.CosInsHeaderFk = null;
				}
			}
			else
			{
				newLineItem.CosInstanceFk = null;

				newLineItem.CosInsHeaderFk = null;
			}
		}

		private void ReplaceActivityFk(EstLineItemEntity newLineItem)
		{
			if (this._EstimateCopyOption.IsLookAtCopyOptions)
			{
				if (_estCopyOptionEntity.LiActivity.Value)
				{
					ReplaceActivity(newLineItem);
				}
				else
				{
					newLineItem.PsdActivityFk = null;
				}
			}
			else
			{
				ReplaceActivity(newLineItem);
			}
		}

		private void ReplaceActivity(EstLineItemEntity newLineItem)
		{
			if (this._EstimateCopyOption.ActivitySourceTargetMapping != null && this._EstimateCopyOption.ActivitySourceTargetMapping.Any() && newLineItem.PsdActivityFk.HasValue)
			{
				int? targetActivityFk = null;

				if (this._EstimateCopyOption.ActivitySourceTargetMapping.TryGetValue(newLineItem.PsdActivityFk.Value, out targetActivityFk))
				{
					newLineItem.PsdActivityFk = targetActivityFk;
				}
				else
				{
					newLineItem.PsdActivityFk = null;
				}
			}
			else
			{
				newLineItem.PsdActivityFk = null;
			}
		}

		private void ReplaceLocationFk(EstLineItemEntity newLineItem)
		{
			if (this._EstimateCopyOption.IsLookAtCopyOptions)
			{
				if (_estCopyOptionEntity.LiLocation.Value)
				{
					ReplaceLocation(newLineItem);
				}
				else
				{
					newLineItem.PrjLocationFk = null;
				}
			}
			else
			{
				ReplaceLocation(newLineItem);
			}
		}

		private void ReplaceLocation(EstLineItemEntity newLineItem)
		{
			if (this._EstimateCopyOption.LocationSourceTargetMapping != null && this._EstimateCopyOption.LocationSourceTargetMapping.Any() && newLineItem.PrjLocationFk.HasValue)
			{
				int? targetPrjLocationFk = null;

				if (this._EstimateCopyOption.LocationSourceTargetMapping.TryGetValue(newLineItem.PrjLocationFk.Value, out targetPrjLocationFk))
				{
					newLineItem.PrjLocationFk = targetPrjLocationFk;
				}
				else
				{
					newLineItem.PrjLocationFk = null;
				}
			}
			else
			{
				newLineItem.PrjLocationFk = null;
			}
		}

		private void ReplaceControllingUnitFk(EstLineItemEntity newLineItem)
		{
			if (this._EstimateCopyOption.IsLookAtCopyOptions)
			{
				if (_estCopyOptionEntity.LiControllingUnit.Value)
				{
					ReplaceControllingUnit(newLineItem);
				}
				else
				{
					newLineItem.MdcControllingUnitFk = null;
				}
			}
			else
			{
				ReplaceControllingUnit(newLineItem);
			}
		}

		private void ReplaceRuleSourceFk(EstLineItemEntity newLineItem)
		{
			if (newLineItem.EstRuleSourceFk.HasValue)
			{
				if (this._old2NewRuleSourceIdMap != null && this._old2NewRuleSourceIdMap.TryGetValue(newLineItem.EstRuleSourceFk.Value, out var targetRuleSourceFk))
				{
					newLineItem.EstRuleSourceFk = targetRuleSourceFk;
				}
				else
				{
					newLineItem.EstRuleSourceFk = null;
				}
			}
		}

		private void ReplaceRuleSourceFk(EstResourceEntity newResource)
		{
			if (newResource.EstRuleSourceFk.HasValue)
			{
				if (this._old2NewRuleSourceIdMap != null && this._old2NewRuleSourceIdMap.TryGetValue(newResource.EstRuleSourceFk.Value, out var targetRuleSourceFk))
				{
					newResource.EstRuleSourceFk = targetRuleSourceFk;
				}
				else
				{
					newResource.EstRuleSourceFk = null;
				}
			}
		}

		private void ReplaceControllingUnit(EstLineItemEntity newLineItem)
		{
			if (this._EstimateCopyOption.ControllingUnitSourceTargetMapping != null && this._EstimateCopyOption.ControllingUnitSourceTargetMapping.Any() && newLineItem.MdcControllingUnitFk.HasValue)
			{
				int? targetMdcControllingUnitFk = null;

				if (this._EstimateCopyOption.ControllingUnitSourceTargetMapping.TryGetValue(newLineItem.MdcControllingUnitFk.Value, out targetMdcControllingUnitFk))
				{
					newLineItem.MdcControllingUnitFk = targetMdcControllingUnitFk;
				}
				else
				{
					newLineItem.MdcControllingUnitFk = null;
				}
			}
			else
			{
				newLineItem.MdcControllingUnitFk = null;
			}
		}

		private void ReplaceEstLineItemFk(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			if (lineItemCopyEntities == null || !lineItemCopyEntities.Any())
			{
				return;
			}

			//here may use a dictionary(oldId2newId) to enhance the performance
			foreach (var item in lineItemCopyEntities)
			{
				if (item.SplitLineItem != null)
				{
					continue;
				}

				if (item == null || item.TargetLineItem == null || !item.TargetLineItem.EstLineItemFk.HasValue)
				{
					continue;
				}

				var referenceLineItem = lineItemCopyEntities.FirstOrDefault(e => e.SourceLineItem.Id == item.TargetLineItem.EstLineItemFk.Value);

				if (referenceLineItem == null || referenceLineItem.TargetLineItem == null)
				{
					item.TargetLineItem.EstLineItemFk = null;
				}
				else
				{
					item.TargetLineItem.EstLineItemFk = referenceLineItem.TargetLineItem.Id;
				}
			}
		}

		private void ReplaceEstLineItemFk(IEnumerable<EstLineItemEntity> lineItemEntities)
		{
			var old2NewIdMap = this._Old2NewLineItemIdMapping.ToDictionary(e => e.Key.Id, e => e.Value.Id);

			foreach (var newLineItem in lineItemEntities)
			{
				if (newLineItem.EstLineItemFk.HasValue && this._EstimateCopyOption.SplitQuantityLineItems == null)
				{
					var refEstLineItemFk = old2NewIdMap.ContainsKey(newLineItem.EstLineItemFk.Value) ? (int?)old2NewIdMap[newLineItem.EstLineItemFk.Value] : null;

					newLineItem.EstLineItemFk = refEstLineItemFk ?? (this._IsSameProject && this._TargetHeaderId == this._EstimateCopyOption.SourceEstHeaderFk ? newLineItem.EstLineItemFk : null);
				}
			}
		}

		private IEnumerable<EstResourceEntity> CopyResourcesForPrjDeepCopy(IEnumerable<EstLineItemEntity> sourceLineItems, bool isCopyByDragDropSearchWizard = false)
		{
			if (sourceLineItems == null || !sourceLineItems.Any())
			{
				return new List<EstResourceEntity>();
			}

			var lineItemIds = sourceLineItems.Where(e => !e.EstLineItemFk.HasValue).Select(e => e.Id).ToList();

			/* get resources list */
			// if backup or restore, will copy include the rule generated.
			var ignoreRuleGenerated = !this.IncludeDataGeneratedByRule();

			var sourceResources = new EstimateMainResourceLogic().GetResourcesByLineItemIdsCore(lineItemIds, this._EstimateCopyOption.SourceEstHeaderFk, new EstResourceSearchOption()
			{
				IncludeChildren = false,
				IgnoreRuleGenerated = ignoreRuleGenerated,
				IncludeIsInformation = true
			});

			if (sourceResources == null || !sourceResources.Any())
			{
				return sourceResources;
			}

			// if EstimateFixedActivatedAllowance will calculate the allowance include the rule generated.
			if (_IsDeepCopyFromEstimate && _EstimateFixedActivatedAllowance)
			{
				CalEstimateFixedActivatedAllowance(sourceLineItems, sourceResources);
			}

			var resourcesToCopy = FilterResourcesByRuleSource(sourceResources);

			/*Copy resources*/
			var targetResources = CopyResourcesForPrjDeepCopy(resourcesToCopy);

			targetResources = targetResources.OrderBy(e => e.Code).OrderBy(e => e.Sorting).OrderBy(e => e.Code);

			if (isCopyByDragDropSearchWizard)
			{
				var estResourceLogic = new EstimateMainResourceLogic();
				targetResources = estResourceLogic.SortResourcesRecursively(targetResources);
			}

			var targetResourceParents = targetResources.Where(e => !e.EstResourceFk.HasValue);

			foreach (var targetRes in targetResourceParents)
			{
				SortingGenerator.SetSortingProperty(targetRes, targetResources, isCopyByDragDropSearchWizard);
			}

			/* Create Project Exchange Rate for Resource Currency */
			CreateProjectExchangeRate(targetResources);

			/* attach exchangeRate to resources */
			AttachExchangeRateToResources(targetResources);

			return targetResources;
		}

		private void CalEstimateFixedActivatedAllowance(IEnumerable<EstLineItemEntity> sourceLineItems, IEnumerable<EstResourceEntity> sourceResources)
		{
			if (sourceLineItems == null || !sourceLineItems.Any() || sourceResources == null || !sourceResources.Any())
			{
				return;
			}

			var resourcesByLineItem = sourceResources.GroupBy(e => e.EstLineItemFk).ToDictionary(g => g.Key, g => g.Select(i => i));
			var calculateItems = new List<EstLineItemCalculationItem>();
			var estResourceAllowanceCalculator = new EstResourceAllowanceCalculator(new EstimateCoreCalculationOption(sourceLineItems.First().EstHeaderFk, this._ProjectId));
			foreach (var item in sourceLineItems)
			{
				if (item.EstLineItemFk.HasValue)
				{
					continue;
				}
				else
				{
					calculateItems.Add(new EstLineItemCalculationItem()
					{
						CurrentLineItem = item,
						CurrentResources = resourcesByLineItem.ContainsKey(item.Id) ? resourcesByLineItem[item.Id] : null
					});
				}
			}
			foreach (var calculationItem in calculateItems)
			{
				var resources = calculationItem.CurrentResources;
				estResourceAllowanceCalculator.CalculateAllowance(calculationItem.CurrentLineItem, resources, e => e.Resources);
			}
		}

		private IEnumerable<EstResourceEntity> FilterResourcesByRuleSource(IEnumerable<EstResourceEntity> resources)
		{
			if (!this._EstimateFixedActivatedAllowance && (resources == null || !resources.Any() || this._old2NewRuleSourceIdMap == null || !this._old2NewRuleSourceIdMap.Any()))
			{
				return resources;
			}

			var ruleSourceIds = this._old2NewRuleSourceIdMap.Keys.ToHashSet();

			return resources.Where(e => !e.EstRuleSourceFk.HasValue || ruleSourceIds.Contains(e.EstRuleSourceFk.Value));
		}

		private void CopyResourceProperties(EstResourceEntity targetResource, EstResourceEntity sourceResource, IEnumerable<EstResourceEntity> resources)
		{
			// set original price and quantity
			targetResource.CostUnitOriginal = sourceResource.CostUnit;
			targetResource.QuantityOriginal = sourceResource.Quantity;

			SetBudgetProperties(targetResource, sourceResource, resources);

			targetResource.EstHeaderFk = this._TargetHeaderId;

			// deepcopy project
			if (!this._IsSameProject)
			{
				/*targetResource.PrcPackage2HeaderFk = null;
				targetResource.PrcPackageFk = null;*/
				targetResource.PrcStructureFk = null;

				targetResource.LgmJobFk = targetResource.LgmJobFk.HasValue ? this.GetMappingJobIdPrjDeepCopy(targetResource.LgmJobFk.Value) : null;
			}

			if (!this._ResourceId2JobIdMapping.ContainsKey(targetResource.Id))
			{
				if (targetResource.LgmJobFk.HasValue)
				{
					this._ResourceId2JobIdMapping.Add(targetResource.Id, targetResource.LgmJobFk.Value);
				}
				else if (targetResource.EstResourceFk.HasValue && this._ResourceId2JobIdMapping.ContainsKey(targetResource.EstResourceFk.Value))
				{
					this._ResourceId2JobIdMapping.Add(targetResource.Id, this._ResourceId2JobIdMapping[targetResource.EstResourceFk.Value]);
				}
			}

			/* reset description todo check translate logic while saving*/
			if (sourceResource.DescriptionInfo != null)
			{
				targetResource.DescriptionInfo = sourceResource.DescriptionInfo;
			}

			/* reset further description */
			if (sourceResource.DescriptionInfo1 != null)
			{
				targetResource.DescriptionInfo1 = sourceResource.DescriptionInfo1;
			}

			ReplaceRuleSourceFk(targetResource);

			targetResource.UpdatedAt = null;
			targetResource.UpdatedBy = null;
			targetResource.InsertedAt = DateTime.UtcNow;
			targetResource.InsertedBy = this.CurrentUserId;
			targetResource.Version = 0;
		}

		private IList<EstResourceEntity> CopyResourceTreeOfLineItem(IEnumerable<EstResourceEntity> resources, Func<int> getNewResourceId)
		{
			var result = new List<EstResourceEntity>();

			if (resources == null || !resources.Any())
			{
				return result;
			}

			var parentId2ChildrenMapping = resources.Where(e => e.EstResourceFk.HasValue).GroupBy(e => e.EstResourceFk.Value).ToDictionary(e => e.Key, e => e.ToList());

			var firstLevelResources = resources.Where(e => !e.EstResourceFk.HasValue).ToList();

			var resourceQueue = new Queue<EstResourceEntity>(firstLevelResources);

			/* use to replace the estResourceFk with new parent id */
			var old2NewResourceIdMapping = new Dictionary<int, int>();

			/* resource id which has been copy, use to check reference loop */
			var oldResourceIdsCopied = new Stack<int>();

			var resourceWaitingTimes = new Dictionary<int, int>();

			while (resourceQueue.Any())
			{
				var currentResource = resourceQueue.Dequeue();

				if (currentResource.Id == currentResource.EstResourceFk || oldResourceIdsCopied.Contains(currentResource.Id))
				{
					throw new Exception(string.Format("self reference in resource which id is {0}", currentResource.Id));
				}

				EstResourceEntity targetResource = null;

				if (currentResource.EstResourceFk.HasValue)
				{
					if (old2NewResourceIdMapping.ContainsKey(currentResource.EstResourceFk.Value))
					{
						targetResource = (EstResourceEntity)currentResource.Clone();

						targetResource.Id = getNewResourceId();

						targetResource.EstResourceFk = old2NewResourceIdMapping[currentResource.EstResourceFk.Value];
					}
					else
					{
						if (resourceWaitingTimes.ContainsKey(currentResource.Id))
						{
							if (resourceWaitingTimes[currentResource.Id] > 100)
							{
								throw new Exception(string.Format("TTL in resource which id is {0}", currentResource.Id));
							}
							else
							{
								resourceWaitingTimes[currentResource.Id] += 1;
							}
						}
						else
						{
							resourceWaitingTimes.Add(currentResource.Id, 1);
						}

						resourceQueue.Enqueue(currentResource);

						continue;
					}
				}
				else
				{
					targetResource = (EstResourceEntity)currentResource.Clone();

					targetResource.Id = getNewResourceId();

					targetResource.EstResourceFk = null;
				}

				if (targetResource != null)
				{
					CopyResourceProperties(targetResource, currentResource, resources);

					if (this._EstimateCopyOption.IsLookAtCopyOptions)
					{
						this._estCopyOptionLogic.CheckResourceQuantity(targetResource);

						this._estCopyOptionLogic.CheckResourceQuantityAndCostFactors(targetResource);

						this._estCopyOptionLogic.CheckResourceCostUnit(targetResource);

						this._estCopyOptionLogic.CheckResourceBudget(targetResource, this._IsCopyBudget);
					}

					var oldLineItemKey = new IdentificationData() { Id = currentResource.EstLineItemFk, PKey1 = currentResource.EstHeaderFk };

					if (this._Old2NewLineItemIdMapping.ContainsKey(oldLineItemKey))
					{
						targetResource.EstLineItemFk = _Old2NewLineItemIdMapping[oldLineItemKey].Id;
					}
					else
					{
						throw new Exception(string.Format("current lineItem id is not found {0}", currentResource.EstLineItemFk));
					}

					result.Add(targetResource);

					if (parentId2ChildrenMapping.ContainsKey(currentResource.Id))
					{
						foreach (var item in parentId2ChildrenMapping[currentResource.Id])
						{
							resourceQueue.Enqueue(item);
						}
					}

					oldResourceIdsCopied.Push(currentResource.Id);

					if (!old2NewResourceIdMapping.ContainsKey(currentResource.Id))
					{
						old2NewResourceIdMapping.Add(currentResource.Id, targetResource.Id);
					}

					//Mapping old resource Id, new resource Id
					if (!_Old2NewResourceIdMapping.ContainsKey(currentResource.IdentificationData))
					{
						_Old2NewResourceIdMapping.AddOrUpdate(currentResource.IdentificationData, targetResource.IdentificationData, (key, value) => targetResource.IdentificationData);
					}

					//copy issue for resources with same id entries
					var oldResLiIdPair = new Tuple<int, int>(currentResource.Id, currentResource.EstLineItemFk);

					if (!_oldResLiId2NewResLiIdMapping.ContainsKey(oldResLiIdPair))
					{
						var newResLiIdPair = new Tuple<int, int>(targetResource.Id, _Old2NewLineItemIdMapping[oldLineItemKey].Id);

						_oldResLiId2NewResLiIdMapping.AddOrUpdate(oldResLiIdPair, newResLiIdPair, (key, val) => newResLiIdPair);
					}
				}
			}

			return result;
		}

		private IEnumerable<EstResourceEntity> CopyResourcesForPrjDeepCopy(IEnumerable<EstResourceEntity> resources2Copy)
		{
			try
			{
				var result = new List<EstResourceEntity>();

				var newResourceIds = new ConcurrentStack<int>(this._ResourceLogic.SequenceManager.GetNextList("EST_RESOURCE", resources2Copy.Count()));

				Func<int> getNewIds = () =>
				{
					int newId;

					if (newResourceIds.TryPop(out newId))
					{
						return newId;
					}
					else
					{
						return this._ResourceLogic.SequenceManager.GetNext("EST_RESOURCE");
					}
				};

				var estHeaderId2ResourceMap = resources2Copy.GroupBy(e => e.EstHeaderFk).ToDictionary(e => e.Key, e => e.ToList());

				foreach (var resourceOfHeader in estHeaderId2ResourceMap)
				{
					if (resourceOfHeader.Value == null || !resourceOfHeader.Value.Any())
					{
						continue;
					}

					var lineItemId2ResourceMap = resourceOfHeader.Value.GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.ToList());

					foreach (var resourceOfLineItem in lineItemId2ResourceMap)
					{
						result.AddRange(CopyResourceTreeOfLineItem(resourceOfLineItem.Value, getNewIds));
					}
				}

				return result;
			}
			catch (Exception ex)
			{
				throw new RVPO.BusinessLayerException(ex.Message, ex)
				{
					ErrorCode = (Int32)RVPO.ExceptionErrorCodes.BusinessFatalError,
					ErrorMessage = ex.Message,
					ErrorDetail = ex.StackTrace
				};
			}
		}

		private void CopyResourcesOfLineItems(IEnumerable<LineItemCopyEntity> lineItemCopyEntities, bool isCopyByDragDropSearchWizard = false)
		{
			if (lineItemCopyEntities == null || !lineItemCopyEntities.Any())
			{
				return;
			}

			var copyEntities = lineItemCopyEntities.Where(e => e.SourceLineItem != null && e.TargetLineItem != null && e.SourceResources != null).ToList();

			var resourceCount = copyEntities.SelectMany(e => e.SourceResources).Flatten(e => e.ResourceChildren).Count();

			var idGenarator = new IdGenerator(this.SequenceManager, "EST_RESOURCE", new IdGenerateOption() { IdCount = resourceCount });

			var estResourceCopyLogic = new EstResourceCopyLogic(this._EstimateCopyOption.ProjectId, this._ProjectScopeObject, idGenarator);

			foreach (var lineItemCopyEntity in copyEntities)
			{
				var resourceFiltered = lineItemCopyEntity.LineItemCopyOption != null && lineItemCopyEntity.LineItemCopyOption.IgnoreResourceGeneratedByRule ? this.FilterResource(lineItemCopyEntity.SourceResources, ValidateResourceByRule) : lineItemCopyEntity.SourceResources;

				if (resourceFiltered == null || !resourceFiltered.Any())
				{
					continue;
				}

				var resourceCopyOption = new ResourceCopyOption()
				{
					LineItemId = lineItemCopyEntity.TargetLineItem.Id,
					HeaderId = lineItemCopyEntity.TargetLineItem.EstHeaderFk,
					JobId = lineItemCopyEntity.TargetLineItem.LgmJobFk.HasValue ? lineItemCopyEntity.TargetLineItem.LgmJobFk : this._TargetHeader.LgmJobFk,
					IsResolveAssembly = this._IsResolveAssembly,
					IsSetProjectCurrency = this._IsSetProjectCurrency,
					LineItemCopyEntity = lineItemCopyEntity,
					ResourceCopyFrom = lineItemCopyEntity.SourceLineItem.LineItemType == 1 ? ResourceModuleType.MasterAssembly : ResourceModuleType.Estimate,
					ResourceCopyTo = lineItemCopyEntity.TargetLineItem.LineItemType == 1 ? ResourceModuleType.MasterAssembly : ResourceModuleType.Estimate,
					UserDefinedColumnValCopyOption = new UserDefinedColumnValCopyOption()
					{
						IsCopyUserDefinedColumnVal = false
					},
					CharacteristicCopyOption = new CharacteristicCopyOption()
					{
						IsCopyCharacteristic = false,
						IsAutoAssignCharacteristic = false
					}
				};

				estResourceCopyLogic.DoAfterResourceClone = lineItemCopyEntity.DoAfterResourceClone ?? this.DoAfterResourceClone;

				lineItemCopyEntity.TargetResources = estResourceCopyLogic.CopyResources(resourceFiltered, resourceCopyOption);
				lineItemCopyEntity.TargetResources = lineItemCopyEntity.TargetResources.OrderBy(e => e.Sorting).OrderBy(e => e.Code);

				var allTargetResources = lineItemCopyEntity.TargetResources.Flatten(e => e.ResourceChildren).OrderBy(e => e.Sorting).OrderBy(e => e.Code);

				if (isCopyByDragDropSearchWizard)
				{
					var estResourceLogic = new EstimateMainResourceLogic();
					lineItemCopyEntity.TargetResources = estResourceLogic.SortResourcesRecursively(lineItemCopyEntity.TargetResources);
					foreach (var estResourceEntity in lineItemCopyEntity.TargetResources)
					{
						UpdateMultipliersFrmPlantEstimate(estResourceEntity);
					}
					allTargetResources = lineItemCopyEntity.TargetResources.Flatten(e => e.ResourceChildren).OrderBy(e => e.Sorting);
				}

				ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };
				var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

				Parallel.ForEach(allTargetResources, parallelOptions, targetResource =>
				{
					Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
					if (targetResource != null)
					{
						if (this._EstimateCopyOption.IsLookAtCopyOptions)
						{
							this._estCopyOptionLogic.CheckResourceQuantity(targetResource);

							this._estCopyOptionLogic.CheckResourceQuantityAndCostFactors(targetResource);

							this._estCopyOptionLogic.CheckResourceCostUnit(targetResource);

							this._estCopyOptionLogic.CheckResourceBudget(targetResource, this._IsCopyBudget);
						}
					}

				});

				foreach (var targetRes in lineItemCopyEntity.TargetResources)
				{
					SortingGenerator.SetSortingProperty(targetRes, allTargetResources, isCopyByDragDropSearchWizard);
				}

				//update IsDisabled from Parent Resource (drag drop Source Assembly to Lineitem or Search and Copy Assembly to LineItem)
				if (_EstimateCopyOption.FromAssembly == "AssemblyMaster")
				{
					// update IsDisabled from Parent
					new EstResourceUpdateHelper().UpdateIsDisabledFromParent(lineItemCopyEntity.TargetResources);
				}
			}
		}

		private void UpdateMultipliersFrmPlantEstimate(EstResourceEntity resource)
		{
			if (resource is null || (resource.EstResourceTypeFk != (int)EstResourceType.Plant && resource.EstResourceTypeFk != (int)EstResourceType.PlantDissolved))
			{
				return;
			}

			if (resource.ResourceChildren != null)
			{
				foreach (var resChild in resource.ResourceChildren)
				{
					if (resChild.EstResourceTypeFk == (int)EstResourceType.Plant || resChild.EstResourceTypeFk == (int)EstResourceType.PlantDissolved)
					{
						UpdateMultipliersFrmPlantEstimate(resChild);
					}
					else if (resChild.EstResourceTypeFk == (int)EstResourceType.EquipmentAssembly)
					{
						var currentJobId = resource.LgmJobFk ?? new EstJobHelper(this._ProjectId ?? 0).GetProjectJobId();

						/* QuantityFactor1 by Logistic Job and Work Operation Type */
						new PlantAssemblyUpdateManager(this._ProjectId, resource.EstHeaderFk, null).UpdateMultipliersFrmPlantEstimate(resChild, currentJobId, resource.WorkOperationTypeFk, false);
					}
				}
			}
		}

		private void CopyResourcesOfLineItemsDragDrop(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			if (lineItemCopyEntities == null || !lineItemCopyEntities.Any())
			{
				return;
			}

			var copyEntities = lineItemCopyEntities.Where(e => e.SourceLineItem != null && e.TargetLineItem != null && e.SourceResources != null).ToList();

			var resourceCount = copyEntities.SelectMany(e => e.SourceResources).Flatten(e => e.ResourceChildren).Count();

			var idGenarator = new IdGenerator(this.SequenceManager, "EST_RESOURCE", new IdGenerateOption() { IdCount = resourceCount });

			var estResourceCopyLogic = new EstResourceCopyLogic(this._EstimateCopyOption.ProjectId, this._ProjectScopeObject, idGenarator);

			var estCopyOptionLogic = new EstimateCopyOptionLogic(this._TargetHeaderId);
			var copyOptionEntity = estCopyOptionLogic.GetCopyOptionByEstHeaderId(this._TargetHeaderId);
			var IsCopyOptionsCopyCharacteristics = copyOptionEntity.ResCharacteristics.Value;

			foreach (var lineItemCopyEntity in copyEntities)
			{
				var resourceFiltered = lineItemCopyEntity.LineItemCopyOption != null && lineItemCopyEntity.LineItemCopyOption.IgnoreResourceGeneratedByRule ? this.FilterResource(lineItemCopyEntity.SourceResources, ValidateResourceByRule) : lineItemCopyEntity.SourceResources;

				if (resourceFiltered == null || !resourceFiltered.Any())
				{
					continue;
				}

				var resourceCopyOption = new ResourceCopyOption()
				{
					LineItemId = lineItemCopyEntity.TargetLineItem.Id,
					HeaderId = lineItemCopyEntity.TargetLineItem.EstHeaderFk,
					JobId = lineItemCopyEntity.TargetLineItem.LgmJobFk.HasValue ? lineItemCopyEntity.TargetLineItem.LgmJobFk : this._TargetHeader.LgmJobFk,
					IsResolveAssembly = true,
					SetResourceJobFk = false,
					IncludeMarkupAsCostUnit = false,
					IsResolvePlantAssembly = true,
					UseParentIsCost = false,
					IsSetProjectCurrency = this._IsSetProjectCurrency,
					LineItemCopyEntity = lineItemCopyEntity,
					ResourceCopyFrom = lineItemCopyEntity.SourceLineItem.LineItemType == 1 ? ResourceModuleType.MasterAssembly : ResourceModuleType.Estimate,
					ResourceCopyTo = lineItemCopyEntity.TargetLineItem.LineItemType == 1 ? ResourceModuleType.MasterAssembly : ResourceModuleType.Estimate,
					UserDefinedColumnValCopyOption = new UserDefinedColumnValCopyOption()
					{
						IsCopyUserDefinedColumnVal = true
					},
					CharacteristicCopyOption = new CharacteristicCopyOption()
					{
						IsCopyCharacteristic = IsCopyOptionsCopyCharacteristics,
						IsAutoAssignCharacteristic = false
					}
				};

				estResourceCopyLogic.DoAfterResourceClone = lineItemCopyEntity.DoAfterResourceClone ?? this.DoAfterResourceClone;

				lineItemCopyEntity.TargetResources = estResourceCopyLogic.CopyResources(resourceFiltered, resourceCopyOption);
				lineItemCopyEntity.TargetResources = lineItemCopyEntity.TargetResources.OrderBy(e => e.Sorting).OrderBy(e => e.Code);

				var allTargetResources = lineItemCopyEntity.TargetResources.Flatten(e => e.ResourceChildren).OrderBy(e => e.Sorting).OrderBy(e => e.Code);

				ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };
				var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

				Parallel.ForEach(allTargetResources, parallelOptions, targetResource =>
				{
					Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext = context;
					if (targetResource != null)
					{
						if (this._EstimateCopyOption.IsLookAtCopyOptions)
						{
							this._estCopyOptionLogic.CheckResourceQuantity(targetResource);

							this._estCopyOptionLogic.CheckResourceQuantityAndCostFactors(targetResource);

							this._estCopyOptionLogic.CheckResourceCostUnit(targetResource);

							this._estCopyOptionLogic.CheckResourceBudget(targetResource, this._IsCopyBudget);
						}
					}

				});

				foreach (var targetRes in lineItemCopyEntity.TargetResources)
				{
					SortingGenerator.SetSortingProperty(targetRes, allTargetResources);
				}

				//update IsDisabled from Parent Resource (drag drop Source Assembly to Lineitem or Search and Copy Assembly to LineItem)
				if (_EstimateCopyOption.FromAssembly == "AssemblyMaster")
				{
					// update IsDisabled from Parent
					new EstResourceUpdateHelper().UpdateIsDisabledFromParent(lineItemCopyEntity.TargetResources);
				}
			}
		}

		private IEnumerable<EstResourceEntity> GetFlatResourceList(IEnumerable<EstResourceEntity> resources)
		{
			List<EstResourceEntity> list = new List<EstResourceEntity>();

			if (resources != null && resources.Count() > 0)
			{
				Queue<EstResourceEntity> queue = new Queue<EstResourceEntity>();

				foreach (EstResourceEntity item in resources)
				{
					queue.Enqueue(item);
				}

				while (queue.Count > 0)
				{
					EstResourceEntity res = queue.Dequeue();

					list.Add(res as EstResourceEntity);

					if (res.ResourceChildren != null && res.ResourceChildren.Count > 0)
					{
						foreach (var item in res.Resources)
						{
							queue.Enqueue(item as EstResourceEntity);
						}
					}
				}
			}

			return list;
		}

		private void CopyAndReplaceProjectMaterial(IEnumerable<LineItemCopyEntity> lineItemCopyEntities, bool noPrjAssembly = false)
		{
			if (this._IsSameProject)
			{
				return;
			}

			if (this._IsResolveAssembly)
			{
				if (this._ProjectId.HasValue)
				{
					var projectJobId = this._TargetProjectJobId;

					this._EstimateCopyOption.SourceProjectId = this._EstimateCopyOption.SourceProjectId.HasValue ? this._EstimateCopyOption.SourceProjectId : this._ProjectId;

					var estJobHelper = new EstJobHelper(this._ProjectId.Value)
						 .SetSourceProjectId(this._EstimateCopyOption.SourceProjectId.Value)
						 .SetIsCopyFromSameProject(false);

					foreach (var item in lineItemCopyEntities)
					{
						var currentJobId = EstJobHelper.GetLineItemAndHeaderJobId(item.TargetLineItem, this._TargetHeader);

						estJobHelper.CollectPrjCostCodesAndPrjMaterialToSave(item.TargetResources, currentJobId ?? projectJobId);
					}

					estJobHelper.Save(false, this._IsDeepCopyFromProject, this._Old2NewJobIdMapping, noPrjAssembly);

					this._Old2NewProjectCostCodeIdMapping = estJobHelper.GetOldNewPrjCostCodeIdMap();

					var targetResourcesList = lineItemCopyEntities.Where(e => e.TargetResources != null).SelectMany(e => e.TargetResources).ToList();
					var resourcesInList = GetFlatResourceList(targetResourcesList);

					List<IProjectCostCodesEntity> newProjectCostCodes = new List<IProjectCostCodesEntity>();

					if (resourcesInList.Any(x => !x.MdcCostCodeFk.HasValue))
					{
						var oldProjectCostCodeIds = resourcesInList.Select(x => x.ProjectCostCodeFk);
						var newProjectCostCodeIds = _Old2NewProjectCostCodeIdMapping.Where(x => oldProjectCostCodeIds.Contains(x.Key)).Select(x => x.Value);
						var prjCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();
						newProjectCostCodes.AddRange(prjCostCodeLogic.GetProjectCostCodesByIds(newProjectCostCodeIds));
					}
					foreach (var item in resourcesInList)
					{
						if (item == null || !item.ProjectCostCodeFk.HasValue)
						{
							continue;
						}

						if (this._Old2NewProjectCostCodeIdMapping.ContainsKey(item.ProjectCostCodeFk.Value))
						{
							item.ProjectCostCodeFk = this._Old2NewProjectCostCodeIdMapping[item.ProjectCostCodeFk.Value];
							if (!item.MdcCostCodeFk.HasValue && newProjectCostCodes.Any(x => x.Id == item.ProjectCostCodeFk))
							{
								item.Code = newProjectCostCodes.Where(x => x.Id == item.ProjectCostCodeFk).Select(s => s.Code).FirstOrDefault();
							}
						}
						else
						{
							throw new Exception("Project Cost Code Missing");
						}
					}
				}
			}
		}

		private void UpdateRateFromProject(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			var projectJobId = EstJobHelper.GetProjectJobId(this._ProjectId);

			foreach (var item in lineItemCopyEntities)
			{
				var currentJobId = EstJobHelper.GetLineItemAndHeaderJobId(item.TargetLineItem, this._TargetHeader);

				this._EstLineItemUpdateFrmPrjLogic.UpdateRateFromProjectInTree(item.TargetResources, currentJobId ?? projectJobId);
			}
		}

		private void CreateProjectExchangeRate(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			var resourceTree = lineItemCopyEntities.Where(e => e.TargetResources != null && e.TargetResources.Any()).SelectMany(e => e.TargetResources).ToList();

			var projectExchangeRateToSave = resourceTree.Flatten(e => e.ResourceChildren).Where(e => e.BasCurrencyFk.HasValue).Select(e => e.BasCurrencyFk.Value).ToList();

			//Project Currency Exchange Rate per Project
			if (projectExchangeRateToSave.Any() && this._ProjectId.HasValue && this._ProjectId.Value > 0)
			{
				RVPARB.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>().SaveCurrencyRateItems(projectExchangeRateToSave, this._ProjectId.Value);
			}
		}

		private void CreateProjectExchangeRate(IEnumerable<EstResourceEntity> targetResources)
		{
			var projectExchangeRateToSave = targetResources.Where(e => e.BasCurrencyFk.HasValue).Select(e => e.BasCurrencyFk.Value).ToList();

			//Project Currency Exchange Rate per Project
			if (projectExchangeRateToSave.Any() && this._ProjectId.HasValue && this._ProjectId.Value > 0)
			{
				RVPARB.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>().SaveCurrencyRateItems(projectExchangeRateToSave, this._ProjectId.Value);
			}
		}

		private void AttachExchangeRateToResources(IEnumerable<EstResourceEntity> targetResources)
		{
			if (this._ProjectId.HasValue && this._ProjectId.Value > 0)
			{
				new ExchangeRateHelper(this._ProjectId).ExchangeRate(targetResources, false);
			}
		}

		private void CollectAndSaveLineItemProjectAssembly(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			if (this._ProjectId.HasValue && this._ProjectId.Value > 0)
			{
				var parentJobId = this._EstimateCopyOption.TargetEstHeaderJobId.HasValue
						? this._EstimateCopyOption.TargetEstHeaderJobId.Value : this._TargetHeader != null && this._TargetHeader.LgmJobFk.HasValue
						? this._TargetHeader.LgmJobFk.Value : this._TargetProjectJobId;

				var lineItems = lineItemCopyEntities.Select(e => e.TargetLineItem).ToList();

				var jobHelper = new EstJobHelper(this._ProjectId.Value);

				// collect project assemblies and project plant assemblies
				jobHelper.CollectAllPrjAssembliesToSave(lineItems, parentJobId);

				jobHelper.Save();
			}
		}

		private void CopyLineItemProjectOrEnterpriseCostGroups(IEnumerable<LineItemCopyEntity> lineItemCopyEntities, bool isCopyProjectCostGroups)
		{
			var oldLineItems = lineItemCopyEntities.Select(e => e.SourceLineItem).ToList();

			var oldNewLineItemRefMap = lineItemCopyEntities.GroupBy(e => e.SourceLineItem.Id).ToDictionary(e => e.Key, e => e.First().TargetLineItem.Id);

			new EstLineItem2CostGroupLogic().CopyLineItemCostGroups(oldLineItems, this._EstimateCopyOption, new ConcurrentDictionary<int, int>(oldNewLineItemRefMap), isCopyProjectCostGroups);
		}

		private void CopyLineItemCostGroup(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			var oldLineItems = lineItemCopyEntities.Select(e => e.SourceLineItem).ToList();

			var oldNewLineItemRefMap = lineItemCopyEntities.GroupBy(e => e.SourceLineItem.Id).ToDictionary(e => e.Key, e => e.First().TargetLineItem.Id);

			new EstLineItem2CostGroupLogic().CopyCostGroupsByProject(oldLineItems, this._EstimateCopyOption, new ConcurrentDictionary<int, int>(oldNewLineItemRefMap));
		}

		private IEnumerable<EstLineItem2CostGroupEntity> CopyLineItemCostGroupOfEstimate(IEnumerable<EstLineItemEntity> oldLineItems, bool autoSave)
		{
			return new EstLineItem2CostGroupLogic().CopyCostGroupsByProject(oldLineItems, this._EstimateCopyOption, new ConcurrentDictionary<int, int>(this._Old2NewLineItemIdMapping.ToDictionary(e => e.Key.Id, e => e.Value.Id)), true, autoSave);
		}

		private void CalculteSystemParamDetail(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			if(this._EstimateCopyOption.FromAssembly == "AssemblyMaster" || lineItemCopyEntities == null || !lineItemCopyEntities.Any())
			{
				return;
			}

			foreach (var item in lineItemCopyEntities)
			{
				item.TargetLineItem.EstResourceEntities = item.TargetResources == null ? null : item.TargetResources.ToList();
			}

			var group = lineItemCopyEntities.GroupBy(x => x.TargetLineItem.EstHeaderFk);

			foreach (var item in group)
			{
				var lineItemUpdateHelper = new EstLineItemUpdateHelper(item.Key, this._ProjectId, new EstLineItemUpdateOption { IsCalculateDetail = false});
				lineItemUpdateHelper.CalculateDetails(item.Select(x => x.TargetLineItem).ToList());
			}
		}

		/// <summary>
		/// CopyCharacteristicsOfLineItem
		/// </summary>
		/// <param name="lineItemCopyEntities"></param>
		/// <returns></returns>
		public void CopyCharacteristicsOfLineItemCore(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			if (!string.IsNullOrEmpty(this._EstimateCopyOption.FromAssembly))
			{
				if (!lineItemCopyEntities.Any(e => e.TargetLineItem.EstAssemblyFk.HasValue))
				{
					return;
				}

				var oldNewAssemblyRefMap = lineItemCopyEntities.Where(e => e.TargetLineItem.EstAssemblyFk.HasValue && e.TargetLineItem.EstHeaderAssemblyFk.HasValue).GroupBy(e => new { EstHeaderAssemblyFk = e.TargetLineItem.EstHeaderAssemblyFk.Value, EstAssemblyFk = e.TargetLineItem.EstAssemblyFk.Value }).ToDictionary(e => new IdentificationData() { Id = e.Key.EstAssemblyFk, PKey1 = e.Key.EstHeaderAssemblyFk }, e => new IdentificationData() { Id = e.First().TargetLineItem.Id, PKey1 = e.First().TargetLineItem.EstHeaderFk });

				var sourceDestSectionIds = new Dictionary<int, int>();

				sourceDestSectionIds.Add(CharacteristicSectionConstants.Assembly1, CharacteristicSectionConstants.Estimate1);

				sourceDestSectionIds.Add(CharacteristicSectionConstants.Assembly2, CharacteristicSectionConstants.Estimate2);

				new CharacteristicDataLogic().CopyBulkData(new ConcurrentDictionary<int, int>(sourceDestSectionIds), new ConcurrentDictionary<IdentificationData, IdentificationData>(oldNewAssemblyRefMap), this._IsDeepCopyFromProject);
			}
			else
			{
				var sourceDestSectionIds = new Dictionary<int, int>();

				sourceDestSectionIds.Add(CharacteristicSectionConstants.Estimate1, CharacteristicSectionConstants.Estimate1);

				sourceDestSectionIds.Add(CharacteristicSectionConstants.Estimate2, CharacteristicSectionConstants.Estimate2);

				var oldNewLineItemRefMap = lineItemCopyEntities.GroupBy(e => new { Id = e.SourceLineItem.Id, EstHeaderFk = e.SourceLineItem.EstHeaderFk }).ToDictionary(e => new IdentificationData() { Id = e.Key.Id, PKey1 = e.Key.EstHeaderFk }, e => new IdentificationData() { Id = e.First().TargetLineItem.Id, PKey1 = e.First().TargetLineItem.EstHeaderFk });

				new CharacteristicDataLogic().CopyBulkData(new ConcurrentDictionary<int, int>(sourceDestSectionIds), new ConcurrentDictionary<IdentificationData, IdentificationData>(oldNewLineItemRefMap), this._IsDeepCopyFromProject);
			}
		}

		private void CopyRuleAndParamsFromAssembly(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			if (!this._ProjectId.HasValue)
			{
				return;
			}

			var assemblyTemplate2LineItemLogic = new AssemblyTemplate2LineItemLogic(this._ProjectId.Value);

			foreach (var item in lineItemCopyEntities)
			{
				if (!item.TargetLineItem.EstHeaderAssemblyFk.HasValue || !item.TargetLineItem.EstAssemblyFk.HasValue || string.IsNullOrEmpty(this._EstimateCopyOption.FromAssembly))
				{
					continue;
				}

				assemblyTemplate2LineItemLogic.AddLineItemForCopyRulesAndParams(item.TargetLineItem);

				assemblyTemplate2LineItemLogic.CopyCostGroupAssigments(item.TargetLineItem.EstAssemblyFk.Value, item.TargetLineItem.EstHeaderAssemblyFk.Value, item.TargetLineItem);
			}

			assemblyTemplate2LineItemLogic.DoCopyRulesAndParams();

			assemblyTemplate2LineItemLogic.Save();
		}

		private void CopyMdlObjectAndQuantitiesCore(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			var oldNewLineItemRefMap = lineItemCopyEntities.GroupBy(e => e.SourceLineItem.Id).ToDictionary(e => e.Key, e => e.First().TargetLineItem.Id);

			var headerId2LineItemsMap = lineItemCopyEntities.Select(e => e.TargetLineItem).GroupBy(e => e.EstHeaderFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var item in headerId2LineItemsMap)
			{
				var sourceHeaderFk = item.Key;

				new EstLineItem2MdlObjectLogic().CopyLineItem2MdlObjects(oldNewLineItemRefMap, sourceHeaderFk, this._TargetHeaderId);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItemCopyEntities"></param>
		protected override void CopyRuleAndParamsOfLineItem(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			if (lineItemCopyEntities == null || !lineItemCopyEntities.Any())
			{
				return;
			}

			/* if source lineItem is assembly, copy from assembly */
			var assemblyCopyToLineItems = lineItemCopyEntities.Where(e => e.SourceLineItem.LineItemType == 1).ToList();

			if (assemblyCopyToLineItems.Any())
			{
				CopyRuleAndParamsOfAssembly(assemblyCopyToLineItems);
			}

			/* if source lineItem is lineItem, copy from lineItem */
			var lineItemCopyToLineItems = lineItemCopyEntities.Where(e => e.SourceLineItem.LineItemType == 0).ToList();

			if (lineItemCopyToLineItems.Any())
			{
				var lineItemCopyGrouping = lineItemCopyToLineItems.GroupBy(e => e.SourceLineItem.EstHeaderFk).ToDictionary(e => e.Key, e => e.ToList());

				foreach (var item in lineItemCopyGrouping)
				{
					var oldNewLineItemIdMap = item.Value.Select(e => new KeyValuePair<int, int>(e.SourceLineItem.Id, e.TargetLineItem.Id)).ToList();
					if (this._EstimateCopyOption.SourceProjectId != null && this._EstimateCopyOption.SourceProjectId != this._EstimateCopyOption.ProjectId)
					{
						var oldNewLineItemIds = item.Value.GroupBy(e => e.SourceLineItem.Id).ToDictionary(e => e.Key, e => e.First().TargetLineItem.Id);
						new EstimateRuleLineItemLogic().CopyNSaveRuleByProject(this._EstimateCopyOption.SourceEstHeaderFk, this._EstimateCopyOption.EstHeaderFk, this._EstimateCopyOption.SourceProjectId ?? 0, this._EstimateCopyOption.ProjectId ?? 0, oldNewLineItemIds);
					}
					else
					{
						new EstimateRuleLineItemLogic().CopyAndSaveRule(oldNewLineItemIdMap, item.Key, this._EstimateCopyOption.EstHeaderFk);
					}


					new EstimateParameterLineItemLogic().CopyAndSaveParameter(oldNewLineItemIdMap, item.Key, this._EstimateCopyOption.EstHeaderFk);
				}
			}
		}

		private void CopyRuleAndParamsOfAssembly(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			if (!this._ProjectId.HasValue)
			{
				return;
			}

			var assemblyTemplate2LineItemLogic = new AssemblyTemplate2LineItemLogic(this._ProjectId.Value);

			foreach (var item in lineItemCopyEntities)
			{
				assemblyTemplate2LineItemLogic.AddLineItemForCopyWithAssemblyKey(item.TargetLineItem, item.SourceLineItem.Id, item.SourceLineItem.EstHeaderFk);
			}

			assemblyTemplate2LineItemLogic.DoCopyRulesAndParams();

			assemblyTemplate2LineItemLogic.Save();
		}

		private void CopyRuleAndParamsByProject(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			var sourceProjectFk = this._EstimateCopyOption.SourceProjectId ?? 0;

			var targetProjectFk = this._EstimateCopyOption.ProjectId ?? 0;

			var targetHeaderFk = this._TargetHeaderId;

			var oldNewLineItemRefMap = lineItemCopyEntities.GroupBy(e => e.SourceLineItem.Id).ToDictionary(e => e.Key, e => e.First().TargetLineItem.Id);

			var headerId2LineItemsMap = lineItemCopyEntities.Select(e => e.TargetLineItem).GroupBy(e => e.EstHeaderFk).ToDictionary(e => e.Key, e => e.ToList());

			if (!this._IsSameProject)
			{
				if (this._EstimateCopyOption.DoCopyPrjRuleParam)
				{
					new EstimateRulePrjEstRuleLogic().CopyRuleByProject((int)this._EstimateCopyOption.SourceProjectId, (int)this._ProjectId);

					new EstimateParameterPrjParamLogic().CopyParamByProject((int)this._EstimateCopyOption.SourceProjectId, (int)this._ProjectId);
				}
			}

			foreach (var item in headerId2LineItemsMap)
			{
				var sourceHeaderFk = item.Key;

				if (!this._IsSameProject)
				{
					new EstimateRuleHeaderLogic().CopyNSaveRuleByProject(this._EstimateCopyOption.SourceEstHeaderFk, this._TargetHeaderId, (int)this._EstimateCopyOption.SourceProjectId, (int)this._ProjectId);

					new EstimateParameterHeaderLogic().CopyParamByProject(this._EstimateCopyOption.SourceEstHeaderFk, this._TargetHeaderId, (int)this._EstimateCopyOption.SourceProjectId, (int)this._ProjectId);

					new EstimateRuleLineItemLogic().CopyNSaveRuleByProject(sourceHeaderFk, targetHeaderFk, sourceProjectFk, targetProjectFk, new Dictionary<int, int>(oldNewLineItemRefMap));

					new EstimateParameterLineItemLogic().CopyParamByProject(sourceHeaderFk, new Dictionary<int, int>(oldNewLineItemRefMap), targetHeaderFk, sourceProjectFk, targetProjectFk);

				}
				else
				{
					new EstimateRuleLineItemLogic().CopyNSaveRuleFrmLineItems(sourceHeaderFk, new Dictionary<int, int>(oldNewLineItemRefMap), targetHeaderFk);

					new EstimateParameterLineItemLogic().CopyParamByProject(sourceHeaderFk, new Dictionary<int, int>(oldNewLineItemRefMap), targetHeaderFk, sourceProjectFk, targetProjectFk);

					CopyLsRulesAndParamsByHeader(sourceHeaderFk, targetHeaderFk);
				}
			}
		}

		private void AutoAssignDefaultCharacteristics(IEnumerable<LineItemCopyEntity> lineItemCopyEntities)
		{
			var newResources = lineItemCopyEntities.Where(e => e.TargetResources != null && e.TargetResources.Any()).SelectMany(e => e.TargetResources).ToList();

			new EstCharacteristicLogic().AssignAndSaveResourceCharacteristics(newResources);
		}

		private void CopyLsRulesAndParamsByHeader(int sourceHeaderId, int targetHeaderId)
		{
			if (sourceHeaderId == targetHeaderId)
			{
				return;
			}

			#region copy and save rules from estheader, leading structures
			new EstimateRuleHeaderLogic().CopyNSaveRuleByHeader(sourceHeaderId, targetHeaderId);
			new EstimateRuleBoqLogic().CopyNSaveRuleByHeader(sourceHeaderId, targetHeaderId);
			new EstimateRuleActivityLogic().CopyNSaveRuleByHeader(sourceHeaderId, targetHeaderId);
			new EstimateRuleAssemblyCatLogic().CopyNSaveRuleByHeader(sourceHeaderId, targetHeaderId);
			new EstimateRuleControllingUnitLogic().CopyNSaveRuleByHeader(sourceHeaderId, targetHeaderId);
			new EstimateRulePrcStructureLogic().CopyNSaveRuleByHeader(sourceHeaderId, targetHeaderId);
			new EstimateRulePrjLocationLogic().CopyNSaveRuleByHeader(sourceHeaderId, targetHeaderId);
			new EstimateCostGrpRuleLogic().CopyNSaveRuleByHeader(sourceHeaderId, targetHeaderId);

			#endregion

			#region copy and save parameters from estheader, leading structure
			new EstimateParameterHeaderLogic().CopyNSaveParamByHeader(sourceHeaderId, targetHeaderId);
			new EstimateParameterBoqLogic().CopyNSaveParamByHeader(sourceHeaderId, targetHeaderId);
			new EstimateParameterActivityLogic().CopyNSaveParamByHeader(sourceHeaderId, targetHeaderId);
			new EstimateParameterAssemblyCatLogic().CopyNSaveParamByHeader(sourceHeaderId, targetHeaderId);
			new EstimateParameterControllingUnitLogic().CopyNSaveParamByHeader(sourceHeaderId, targetHeaderId);
			new EstimateParameterPrcStructureLogic().CopyNSaveParamByHeader(sourceHeaderId, targetHeaderId);
			new EstimateParameterPrjLocationLogic().CopyNSaveParamByHeader(sourceHeaderId, targetHeaderId);
			new EstimateCostGrpParameterLogic().CopyNSaveParamByHeader(sourceHeaderId, targetHeaderId);
			#endregion
		}

		private void DoAfterResourceCloneForCos(EstResourceEntity targetResource, EstResourceEntity sourceResource, ResourceCopyOption resourceCopyOption, EstParentResourceInfo parentResourceInfo)
		{
			if (targetResource == null || sourceResource == null)
			{
				return;
			}

			//targetResource.PrcPackageFk = null;

			CollectResourceMap(targetResource, sourceResource, resourceCopyOption);
		}

		private void DoAfterResourceClone(EstResourceEntity newResource, EstResourceEntity oldResource, ResourceCopyOption resourceCopyOption, EstParentResourceInfo parentResourceInfo)
		{
			if (newResource == null || oldResource == null)
			{
				return;
			}

			if (this._Old2NewResourceIdMapping != null && !this._Old2NewResourceIdMapping.ContainsKey(oldResource.IdentificationData))
			{
				this._Old2NewResourceIdMapping.AddOrUpdate(oldResource.IdentificationData, newResource.IdentificationData, (Key, Value) => newResource.IdentificationData);
			}

			SetBudgetProperties(newResource, oldResource);

			ChangeResourceJobFk(newResource, oldResource);

			CollectResourceMap(newResource, oldResource, resourceCopyOption);
		}

		private void SetBudgetProperties(EstResourceEntity targetResource, EstResourceEntity sourceResource, IEnumerable<EstResourceEntity> resources = null)
		{
			targetResource.BudgetUnit = GetBudgetPerUnit(targetResource, sourceResource, resources);
			targetResource.Budget = targetResource.IsBudget ? this._IsCopyCostTotalToBudget ? targetResource.CostTotal : this._IsCopyBudget ? targetResource.Budget : 0m : 0m;
			targetResource.BudgetDifference = targetResource.IsBudget ? this._IsCopyBudget ? targetResource.BudgetDifference : 0m : 0m;
			targetResource.BaseCostUnit = this._IsCopyBaseCost ? targetResource.CostUnit : 0m;
			targetResource.BaseCostTotal = this._IsCopyBaseCost ? targetResource.CostTotal : 0m;
		}

		private decimal GetBudgetPerUnit(EstResourceEntity targetResource, EstResourceEntity sourceResource, IEnumerable<EstResourceEntity> resources)
		{
			Func<IScriptEstResource, IScriptEstResource> getParentFunc = e =>
			{
				if (resources == null)
				{
					return null;
				}

				return resources.FirstOrDefault(i => e.EstResourceFk.HasValue && i.Id == e.EstResourceFk.Value);
			};

			var parent = getParentFunc(sourceResource);
			var budgetUnit = 0m;

			if (targetResource.IsBudget)
			{
				if (this._IsCopyCostTotalToBudget)
				{
					budgetUnit = targetResource.CostUnit * targetResource.CostFactor1 * targetResource.CostFactor2 * targetResource.CostFactorCc;

					if (parent != null)
					{
						budgetUnit *= parent.CostFactor1 * parent.CostFactor2;
					}

				}
				else if (this._IsCopyBudget)
				{
					budgetUnit = targetResource.BudgetUnit;
				}
				else
				{
					budgetUnit = 0m;
				}
			}
			else
			{
				budgetUnit = 0m;
			}

			return budgetUnit;
		}

		private void ChangeResourceJobFk(EstResourceEntity targetResource, EstResourceEntity sourceResource)
		{
			if (!IsCopyProjectJob() || !sourceResource.LgmJobFk.HasValue)
			{
				return;
			}

			var isPlant = ((EstResourceTypeHelper.IsPlantAssembly(sourceResource) || EstResourceTypeHelper.IsEquipmentAssembly(sourceResource))
			               && (EstResourceTypeHelper.IsPlantAssembly(targetResource) || EstResourceTypeHelper.IsEquipmentAssembly(targetResource)));
			targetResource.LgmJobFk = isPlant ? GetMappingPlantJobId(sourceResource.LgmJobFk.Value) ?? GetMappingJobId(sourceResource.LgmJobFk.Value) : GetMappingJobId(sourceResource.LgmJobFk.Value);
		}

		private int? GetMappingJobId(int srcJobId)
		{
			if (this._Old2NewJobIdMapping == null || !this._Old2NewJobIdMapping.ContainsKey(srcJobId))
			{
				return null;
			}

			return this._Old2NewJobIdMapping[srcJobId];
		}

		private int? GetMappingPlantJobId(int srcJobId)
		{
			if (this._Old2NewPlantJobIdMapping == null || !this._Old2NewPlantJobIdMapping.ContainsKey(srcJobId))
			{
				return null;
			}

			return this._Old2NewPlantJobIdMapping[srcJobId];
		}

		private int? GetMappingJobIdPrjDeepCopy(int srcJobId)
		{
			if (this._Old2NewJobIdMappingPrjDeepCopy == null || !this._Old2NewJobIdMappingPrjDeepCopy.ContainsKey(srcJobId))
			{
				return null;
			}

			return this._Old2NewJobIdMappingPrjDeepCopy[srcJobId];
		}

		private bool IsCopyProjectJob()
		{
			return (this._IsSameProject || !this._EstimateCopyOption.SourceProjectId.HasValue) ? false : true;
		}

		private EstLineItemEntity CheckCopyLineItemQuantity(EstLineItemEntity lineItem)
		{
			if (lineItem != null && !this._isCopyLineItemQuantity)
			{
				lineItem.Quantity = 0m;
				lineItem.QuantityTarget = 0m;
				lineItem.WqQuantityTarget = 0m;
				lineItem.QuantityDetail = string.Empty;
				lineItem.QuantityTargetDetail = string.Empty;
				lineItem.WqQuantityTargetDetail = string.Empty;
			}

			return lineItem;
		}

		private EstLineItemEntity SetQuantityByLSumUom(EstLineItemEntity lineItem)
		{
			if (lineItem != null)
			{
				EstimateMainBaseCalculator.UpdateQuantityByLSumUom(lineItem, this._lSumUomId);
			}

			return lineItem;
		}

		/// <summary>
		///
		/// </summary>
		public static Dictionary<int, int> SaveDifferentPrjAssembly(List<Tuple<int, int, int>> projectAssembliesToSave, ConcurrentDictionary<int, int> old2NewJobIdMappingPrjDeepCopy, bool isDeepCopyProject, int projectId, int sourceProjectId = 0, Dictionary<int, int> assemblyCatalogIdMapping = null)
		{
			Dictionary<int, int> mapAssemblyIdDic = new Dictionary<int, int>();

			if (projectAssembliesToSave.Count <= 0)
			{
				return mapAssemblyIdDic;
			}

			Dictionary<int, List<int>> header2Items = new Dictionary<int, List<int>>();

			var prjAssemblyLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectAssemblyLogic>();

			foreach (var item in projectAssembliesToSave)
			{
				if (!header2Items.ContainsKey(item.Item2))
				{
					List<int> assemblyIds = new List<int>();
					assemblyIds.Add(item.Item1);
					header2Items.Add(item.Item2, assemblyIds);
				}
				else
				{
					header2Items[item.Item2].Add(item.Item1);
				}
			}

			var assemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();
			foreach (var header2Item in header2Items)
			{
				// TODO: create project assembly from master, changed sp as the c# code
				// var projAssemblyDatas = prjAssemblyLogic.CreateProjectAssemblyFromMaster(header2Item.Value.Distinct().ToList(), projectId, header2Item.Key);
				var projAssemblyDatas = new EstProjectAssemblyLogic(old2NewJobIdMappingPrjDeepCopy).CreateProjectAssemblyFromMaster(header2Item.Value.Distinct().ToList(), projectId, header2Item.Key, isDeepCopyProject);

				if (header2Item.Key != assemblyHeaderId)
				{
					var projectAssembly = prjAssemblyLogic.GetByProject(projectId);
					int prjAssemblyHeaderId = projectAssembly != null ? projectAssembly.EstHeaderFk : 0;

					var catOldIds = projAssemblyDatas.Where(i => i.CatalogOldId.HasValue).Select(e => e.CatalogOldId.Value).ToList();
					var catNewIds = projAssemblyDatas.Where(i => i.CatalogNewId.HasValue).Select(e => e.CatalogNewId.Value).ToList();
					List<int> catIds = new List<int>();
					catIds.AddRange(catOldIds);
					catIds.AddRange(catNewIds);

					var catItems = new EstimateAssembliesStructureLogic().GetAssemblyCatsByIds(catIds);
					var sourceCatIds = catItems != null && catItems.Any() ? catItems.Where(e => e.EstAssemblyCatSourceFk.HasValue).Select(i => i.EstAssemblyCatSourceFk.Value).ToList() : new List<int>();
					var sourceCatItems = new EstimateAssembliesStructureLogic().GetAssemblyCatsByIds(sourceCatIds);
					var prjCatItems = new EstimateAssembliesStructureLogic().GetSearchList(e => e.PrjProjectFk == projectId).ToList();

					var assemblyOldIds = projAssemblyDatas.Where(i => i.AssemblyOldId.HasValue).Select(e => e.AssemblyOldId.Value).ToList();
					var assemblyNewIds = projAssemblyDatas.Where(i => i.AssemblyNewId.HasValue).Select(e => e.AssemblyNewId.Value).ToList();
					List<int> assemblyIds = new List<int>();
					assemblyIds.AddRange(assemblyOldIds);
					assemblyIds.AddRange(assemblyNewIds);

					// copy project assembly rule and parameter
					new ProjectAssemblyRuleParameterHelper(header2Item.Key, projectId, prjAssemblyHeaderId, true, sourceProjectId).CopyRuleNParameterForAssemblyNCatalog(projAssemblyDatas);

					mapAssemblyIdDic.AddRange(projAssemblyDatas.Where(e => e.AssemblyOldId.HasValue && e.AssemblyNewId.HasValue).ToDictionary(i => i.AssemblyOldId.Value, i => i.AssemblyNewId.Value));
					if (assemblyCatalogIdMapping != null)
					{
						assemblyCatalogIdMapping.AddRange(projAssemblyDatas.Where(e => e.CatalogOldId.HasValue && e.CatalogNewId.HasValue).ToDictionary(i => i.CatalogOldId.Value, i => i.CatalogNewId.Value));
					}

					var assemblies = new EstimateAssembliesLogic().GetListByIds(assemblyIds.ToArray()).Distinct().ToList();
					var sourceIds = assemblies.Where(e => e.EstAssemblyFk.HasValue).Select(i => i.EstAssemblyFk.Value).Distinct().ToList();
					var sourceAssemblies = new EstimateAssembliesLogic().GetSearchList(e => e.EstHeaderFk == assemblyHeaderId && sourceIds.Contains(e.Id)).Distinct().ToList();

					List<EstAssemblyCatEntity> catsToSave = new List<EstAssemblyCatEntity>();
					List<Assemblies.BusinessComponents.EstLineItemEntity> itemsToSave = new List<Assemblies.BusinessComponents.EstLineItemEntity>();
					foreach (var item in projAssemblyDatas)
					{
						if (item.CatalogOldId.HasValue && item.CatalogNewId.HasValue)
						{
							var catOld = catItems.FirstOrDefault(e => e.Id == item.CatalogOldId.Value);
							var catNew = catItems.FirstOrDefault(e => e.Id == item.CatalogNewId.Value);
							if (catNew != null && catOld != null)
							{
								catNew.EstAssemblyCatSourceFk = catOld.EstAssemblyCatSourceFk;
								var sourceCatItem = sourceCatItems.FirstOrDefault(e => e.Id == catOld.EstAssemblyCatSourceFk);
								if (!catNew.EstAssemblyCatFk.HasValue && sourceCatItem != null && sourceCatItem.EstAssemblyCatFk.HasValue)
								{
									var prjCatItem = prjCatItems.FirstOrDefault(e => e.EstAssemblyCatSourceFk == sourceCatItem.EstAssemblyCatFk);
									if (prjCatItem != null)
									{
										catNew.EstAssemblyCatFk = prjCatItem.Id;
									}
								}

								catsToSave.Add(catNew as EstAssemblyCatEntity);
							}
						}

						if (item.AssemblyOldId.HasValue && item.AssemblyNewId.HasValue)
						{
							var itemOld = assemblies.FirstOrDefault(e => e.Id == item.AssemblyOldId.Value);
							var itemNew = assemblies.FirstOrDefault(e => e.Id == item.AssemblyNewId.Value);
							if (itemNew != null && itemOld != null)
							{
								itemNew.EstHeaderAssemblyFk = itemOld.EstHeaderAssemblyFk;
								itemNew.EstAssemblyFk = itemOld.EstAssemblyFk;
								if (!itemNew.EstAssemblyCatFk.HasValue && itemOld.EstAssemblyCatFk.HasValue)
								{
									var sourceAssembly = sourceAssemblies.FirstOrDefault(e => e.Id == itemOld.EstAssemblyFk && e.EstHeaderFk == itemOld.EstHeaderAssemblyFk);
									if (sourceAssembly != null)
									{
										var mapCatalog = prjCatItems.FirstOrDefault(e => e.EstAssemblyCatSourceFk == sourceAssembly.EstAssemblyCatFk);
										if (mapCatalog != null)
										{
											itemNew.EstAssemblyCatFk = mapCatalog.Id;
										}
									}
								}

								itemsToSave.Add(itemNew);
							}
						}
					}

					if (catsToSave.Count > 0)
					{
						new EstimateAssembliesStructureLogic().SaveEstAssemblyCatalogs(catsToSave);
					}

					if (itemsToSave.Count > 0)
					{
						new EstimateAssembliesLogic().SaveEstAssemblies(itemsToSave);
					}
				}
			}

			return mapAssemblyIdDic;
		}

		/// <summary>
		/// copy project plant assembly to oter project plant assembly
		/// </summary>
		/// <param name="prjPlantAssembliesToSave"></param>
		/// <param name="old2NewJobIdMappingPrjDeepCopy"></param>
		/// <param name="isDeepCopyProject"></param>
		/// <param name="projectId"></param>
		/// <param name="prjPlantAssemblyHeaderId"></param>
		/// <param name="sourceProjectId"></param>
		/// <param name="masterPlantAssemblyHeaderId"></param>
		/// <returns></returns>
		public static Dictionary<int, int> SaveDifferentPrjPlantAssembly(List<Tuple<int, int, int>> prjPlantAssembliesToSave, ConcurrentDictionary<int, int> old2NewJobIdMappingPrjDeepCopy, bool isDeepCopyProject, int projectId, out int prjPlantAssemblyHeaderId, int sourceProjectId = 0, int masterPlantAssemblyHeaderId = 0)
		{
			Dictionary<int, int> mapAssemblyIdDic = new Dictionary<int, int>();

			prjPlantAssemblyHeaderId = 0;

			if (prjPlantAssembliesToSave.Count <= 0)
			{
				return mapAssemblyIdDic;
			}

			Dictionary<int, List<int>> header2Items = new Dictionary<int, List<int>>();

			Dictionary<int, int> oldAssemblyId2JobIdMapping = new Dictionary<int, int>();

			foreach (var item in prjPlantAssembliesToSave)
			{
				if (!header2Items.ContainsKey(item.Item2))
				{
					List<int> assemblyIds = new List<int>
					{
						item.Item1
					};

					header2Items.Add(item.Item2, assemblyIds);
				}
				else
				{
					header2Items[item.Item2].Add(item.Item1);
				}

				if (!oldAssemblyId2JobIdMapping.ContainsKey(item.Item1))
				{
					oldAssemblyId2JobIdMapping.Add(item.Item1, item.Item3);
				}
			}

			foreach (var header2Item in header2Items)
			{
				var prjPlantAssemblyDatas = new EstProjectPlantAssemblyLogic(old2NewJobIdMappingPrjDeepCopy, oldAssemblyId2JobIdMapping, true).CreateProjectPlantAssemblyFromMaster(header2Item.Value.Distinct().ToList(), projectId, header2Item.Key, false);

				if (header2Item.Key != masterPlantAssemblyHeaderId && prjPlantAssemblyDatas.Any())
				{
					prjPlantAssemblyHeaderId = prjPlantAssemblyDatas.First().PrjPlantAssemblyHeaderId;

					mapAssemblyIdDic.AddRange(prjPlantAssemblyDatas.Where(e => e.PlantAssemblyOldId.HasValue && e.PlantAssemblyNewId.HasValue).ToDictionary(i => i.PlantAssemblyOldId.Value, i => i.PlantAssemblyNewId.Value));

					// copy project plant assembly rule and parameter
					new ProjectPlantAssemblyRuleParameterHelper(header2Item.Key, projectId, masterPlantAssemblyHeaderId, true, sourceProjectId).CopyRuleNParameterForPlantAssemblyNCatalog(prjPlantAssemblyDatas);

					var plantAssemblyOldIds = prjPlantAssemblyDatas.Where(i => i.PlantAssemblyOldId.HasValue).Select(e => e.PlantAssemblyOldId.Value).ToList();
					var plantAssemblyNewIds = prjPlantAssemblyDatas.Where(i => i.PlantAssemblyNewId.HasValue).Select(e => e.PlantAssemblyNewId.Value).ToList();
					List<int> assemblyIds = new List<int>();
					assemblyIds.AddRange(plantAssemblyOldIds);
					assemblyIds.AddRange(plantAssemblyNewIds);
					List<int> headerIds = new List<int>() { header2Item.Key, prjPlantAssemblyHeaderId };
					var plantAssemblies = new EstimateAssembliesLogic().GetSearchList(e => e.LineItemType == 2 && headerIds.Contains(e.EstHeaderFk) && assemblyIds.Contains(e.Id)).ToList();

					List<Assemblies.BusinessComponents.EstLineItemEntity> itemsToSave = new List<Assemblies.BusinessComponents.EstLineItemEntity>();
					foreach (var item in prjPlantAssemblyDatas)
					{
						if (item.PlantAssemblyOldId.HasValue && item.PlantAssemblyNewId.HasValue)
						{
							var itemOld = plantAssemblies.FirstOrDefault(e => e.Id == item.PlantAssemblyOldId.Value);
							var itemNew = plantAssemblies.FirstOrDefault(e => e.Id == item.PlantAssemblyNewId.Value);
							if (itemNew != null && itemOld != null)
							{
								itemNew.EstHeaderAssemblyFk = itemOld.EstHeaderAssemblyFk;
								itemNew.EstAssemblyFk = itemOld.EstAssemblyFk;

								itemsToSave.Add(itemNew);
							}
						}
					}

					if (itemsToSave.Count > 0)
					{
						new EstimateAssembliesLogic().SaveEstAssemblies(itemsToSave);
					}
				}
			}

			return mapAssemblyIdDic;
		}

		/// <summary>
		/// CopySchedulingPerformanceData method is used to copy scheduling performance data from the source estimate to the target estimate
		/// </summary>
		public void CopySchedulingPerformanceData(EstimateDeepCopyResult retValue)
		{
			if (!this._EstimateCopyOption.EstimateSchedulingPerformanceTransfer)
			{
				return;
			}

			IActivityProgressReportLogic activityProgressReportLogic = Injector.Get<IActivityProgressReportLogic>("Scheduling.Main.ActivityProgressReportLogic");
			ISchedulingOjectModelSimulationLogic schedulingOjectModelSimulationLogic = Injector.Get<ISchedulingOjectModelSimulationLogic>();

			Dictionary<int, int> oldNewLineItemMap = this._Old2NewLineItemIdMapping
				 .ToDictionary(kvp => kvp.Key.Id, kvp => kvp.Value.Id);

			retValue.UpdatedLineItemProgressReports = activityProgressReportLogic.UpdateLineItemProgress(
				 oldNewLineItemRefMap: oldNewLineItemMap,
				 sourceHeaderId: this._EstimateCopyOption.SourceEstHeaderFk,
				 targetHeaderId: this._TargetHeaderId,
				 copyWholeEstimate: true,
				 autoSave: false
			);

			retValue.UpdatedActivityModelObjects = schedulingOjectModelSimulationLogic.UpdateActivity2ModelObject
				(oldNewLineItemRefMap: oldNewLineItemMap,
				sourceHeaderId: this._EstimateCopyOption.SourceEstHeaderFk,
				targetHeaderId: this._TargetHeaderId,
				oldNewModelObjectRefMap: this._Old2NewModelObjectIdMapping
				.ToDictionary(x => x.Key.Id, x => x.Value.Id),
				copyWholeEstimate: true,
				autoSave: false
			);
		}
	}
}
