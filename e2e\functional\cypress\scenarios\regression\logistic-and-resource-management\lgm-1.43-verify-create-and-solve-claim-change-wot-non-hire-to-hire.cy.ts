import apiConstantData from "cypress/constantData/apiConstantData";
import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _projectPage, _controllingUnit, _logesticPage, _modalView, _validate, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import _ from "cypress/types/lodash";

const CU_MAIN_1 = "CU-A-" + Cypress._.random(0, 999)
const PLANT_LIST_TYPE = "PLT-DESC-" + Cypress._.random(0, 999);
const PLANT_PRICE_LIST_R = "RENTAL-PPL-" + Cypress._.random(0, 999);
const PLANT_PRICE_LIST_P = "PERFORMANCE-PPL-" + Cypress._.random(0, 999);
const PLANT_TYPE_RENTAL = "RENTAL-PT-" + Cypress._.random(0, 999);
const PLANT_TYPE_PERFORMANCE = "PERFOR-PT-" + Cypress._.random(0, 999);
const PLANT_TYPE_BULK = "BULK-PT-" + Cypress._.random(0, 999);
const CODE_REPAIR = "REAPIR-OT-" + Cypress._.random(0, 999);
const DESC_REPAIR = "REAPIR-OT-DESC-" + Cypress._.random(0, 999);
const CODE_WORK = "WORK-OT-" + Cypress._.random(0, 999);
const DESC_WORK = "WORK-OT-DESC-" + Cypress._.random(0, 999);
const DESC_PERFORMANCE = "PERFOR-DESC-" + Cypress._.random(0, 999);
const PLANT_GROUP = "PG-" + Cypress._.random(0, 999);
const PLANT_GROUP_DESC = "PG_DESC-" + Cypress._.random(0, 999);
const SUB_PLANT_GROUP = "SUB-PG-" + Cypress._.random(0, 999);
const SUB_PLANT_GROUP_DESC = "SUB-PG-DESC-" + Cypress._.random(0, 999);
const PLANT_DESCRIPTION = "EXCAVATOR-" + Cypress._.random(0, 999);
const PLANT_CODE = "PLANT-CODE-" + Cypress._.random(0, 999);
const PROJECT_B_DESC = "PRJ_B_DESC-" + Cypress._.random(0, 999);
const CONDITION_CODE = "CONDITION-" + Cypress._.random(0, 999);
const CONDITION_DESC = "CONDITION-D-" + Cypress._.random(0, 999);

let MODAL_PLANT_ALLOCATION, MODAL_SETTLEMENT_CLAIM;
let CONTAINER_PROJECT, PROJECT_A_PARAMETERS: DataCells, PROJECT_B_PARAMETERS: DataCells;
let CONTAINER_COLUMNS_CLERK;
let CONTAINERS_CONTROLLING_UNITS, CONTAINER_COLUMNS_CONTROLLING_UNITS_1, CONTROLLING_UNIT_MAIN_PARAMETERS_1: DataCells, CONTROLLING_UNIT_SUB_PARAMETERS_1: DataCells, CONTAINER_COLUMNS_CONTROLLING_UNIT2, CONTROLLING_UNIT_MAIN_PARAMETERS_2: DataCells, CONTROLLING_UNIT_SUB_PARAMETERS_2: DataCells;
let CONTAINER_COLUMNS_DATA_TYPES, CONTAINER_DATA_RECORD, DATA_RECORD_PARAMETER_RENRAL, DATA_RECORD_PARAMETER_PERFORMANCE;
let CONTAINER_OPERATION_TYPES, CONTAINER_COLUMNS_OPERATION_TYPES, CONTAINER_COLUMNS_PLANT_TYPES, OPERATION_TYPE_PARAMETER_REPAIR: DataCells, OPERATION_TYPE_PARAMETER_WORK: DataCells, OPERATION_TYPE_PARAMETER_PERFORMANCE: DataCells;
let CONTAINERS_PLANT_GROUP, CONTAINER_COLUMNS_PLANT_GROUP;
let CONTAINERS_PLANT, CONTAINER_COLUMNS_PLANT, PLANT_PARAMETERS: DataCells, CONTAINER_COLUMNS_PLANT_CONTROLLING, CONTAINERS_PLANT_PRICE_LISTS, PLANTS_ALLOCATION_PARAMETER: DataCells;
let CONTAINER_COLUMNS_JOBS, CONTAINER_JOBS;
let CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS, CONTAINER_COLUMNS_CONDITIONS;
let CONTAINERS_DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER, CONTAINER_COLUMNS_SETTLEMENT_ITEMS, SETTLEMENT_CLAIM_PARAMETERS: DataCells, CONTAINER_COLUMNS_DISPATCHING_RECORD;


describe("LRM- 1.43 | Verify Create and solve Claim - Change WOT non-hire to hire", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture("LRM/lgm-1.43-verify-create-and-solve-claim-change-wot-non-hire-to-hire.json").then((data) => {
            this.data = data;
            CONTAINER_PROJECT = this.data.CONTAINERS.PROJECT

            CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNITS
            CONTAINER_COLUMNS_CONTROLLING_UNITS_1 = this.data.CONTAINER_COLUMNS.CONTROLLING_UNITS;
            CONTROLLING_UNIT_MAIN_PARAMETERS_1 = {
                [app.GridCells.DESCRIPTION_INFO]: CU_MAIN_1,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_CONTROLLING_UNITS.QUANTITY,
                [app.GridCells.UOM_FK]: CONTAINERS_CONTROLLING_UNITS.UOM
            }

            CONTAINER_COLUMNS_CLERK = this.data.CONTAINER_COLUMNS.CLERK
            CONTAINER_COLUMNS_DATA_TYPES = this.data.CONTAINER_COLUMNS.DATA_TYPES;
            CONTAINER_DATA_RECORD = this.data.CONTAINERS.DATA_RECORD;

            CONTAINER_COLUMNS_PLANT_TYPES = this.data.CONTAINER_COLUMNS.PLANT_TYPES;
            CONTAINER_OPERATION_TYPES = this.data.CONTAINERS.OPERATION_TYPES;
            CONTAINER_COLUMNS_OPERATION_TYPES = this.data.CONTAINER_COLUMNS.OPERATION_TYPES;

            CONTAINERS_PLANT_GROUP = this.data.CONTAINERS.PLANT_GROUP;
            CONTAINER_COLUMNS_PLANT_GROUP = this.data.CONTAINER_COLUMNS.PLANT_GROUP;
            CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
            CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;

            CONTAINER_COLUMNS_PLANT_CONTROLLING = this.data.CONTAINER_COLUMNS.PLANT_CONTROLLING;
            CONTAINERS_PLANT_PRICE_LISTS = this.data.CONTAINERS.PLANT_PRICE_LISTS;
            MODAL_PLANT_ALLOCATION = this.data.MODAL.PLANT_ALLOCATION;

            CONTAINER_COLUMNS_CONTROLLING_UNIT2 = this.data.CONTAINER_COLUMNS.CONTROLLING_UNIT2;

            CONTAINER_COLUMNS_JOBS = this.data.CONTAINER_COLUMNS.JOBS
            CONTAINER_JOBS = this.data.CONTAINERS.JOBS
            CONTAINER_COLUMNS_CONDITIONS = this.data.CONTAINER_COLUMNS.CONDITIONS
            CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS = this.data.CONTAINERS.WORK_OPERATION_TYPE_CONDITIONS
            CONTAINERS_DISPATCHING_HEADER = this.data.CONTAINERS.DISPATCHING_HEADER
            CONTAINER_COLUMNS_DISPATCHING_HEADER = this.data.CONTAINER_COLUMNS.DISPATCHING_HEADER
            CONTAINER_COLUMNS_SETTLEMENT_ITEMS = this.data.CONTAINER_COLUMNS.SETTLEMENT_ITEMS
            MODAL_SETTLEMENT_CLAIM = this.data.MODAL.SETTLEMENT_CLAIM
            CONTAINER_COLUMNS_DISPATCHING_RECORD = this.data.CONTAINER_COLUMNS.DISPATCHING_RECORD
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT)
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it("TC - API call to assign logged-in user a clerk", function () {
        _commonAPI.getULoggedInUserId(Cypress.env("USER_NAME"))
            .then(() => {
                _commonAPI.assignUserToClerk(Cypress.env("API_LOGGED_IN_USER_ID"), Cypress.env("USER_NAME"), apiConstantData.CONSTANT.SMIJ)
            })
    });

    it('TC - API: Create project', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)

        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

    it("TC - Create Controlling Units", function () {
        CONTROLLING_UNIT_SUB_PARAMETERS_1 = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true"]
        }


        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_SUB_PARAMETERS_1)

    })

    it("TC - Add plant list type record", function () {
        const DISPATCH_HEADER_PARAMETERS = {
            [app.GridCells.IS_READY_FOR_SETTLEMENT]: "true",
        }
        const PLANT_LIST_TYPE_PARAMETERS = {
            [app.GridCells.DESCRIPTION_INFO]: PLANT_LIST_TYPE
        }

        _commonAPI.updateDispatchHeaderStatus_underCustomizing(DISPATCH_HEADER_PARAMETERS, commonLocators.CommonKeys.DELIVERED, commonLocators.CommonKeys.MATERIAL)
        _commonAPI.createPlantListType(PLANT_LIST_TYPE_PARAMETERS)
    })

    it("TC - Create new plant price list record", function () {

        let DATA_RECORD_PARAMETER_RENRAL: DataCells = {
            [app.GridCells.CONTEXT_FK]: apiConstantData.ID.PLANT_CONTEXT_RIB_DEMO,
            [app.GridCells.PRICE_LIST_TYPE_FK]: Cypress.env('API_PLANT_LIST_TYPE_ID_1'),
            [app.GridCells.CURRENCY]: apiConstantData.ID.CURRENCY_EUR,
            [app.GridCells.UOM_FK]: apiConstantData.ID.UOM_DAY,
            [app.GridCells.DESCRIPTION_INFO]: PLANT_PRICE_LIST_R,
            [app.GridCells.CALCULATION_TYPE_FK]: apiConstantData.ID.CALCULATION_TYPE_AVERAGE_CATALOG_VALUE,
            [app.GridCells.PERCENT]: CONTAINER_DATA_RECORD.PERCENT,
            [app.GridCells.IS_MANUAL_EDIT_PLANT_MASTER]: "true"
        }
        _commonAPI.createPlantPriceList(DATA_RECORD_PARAMETER_RENRAL)

        let DATA_RECORD_PARAMETER_PERFORMANCE: DataCells = {
            [app.GridCells.CONTEXT_FK]: apiConstantData.ID.PLANT_CONTEXT_RIB_DEMO,
            [app.GridCells.PRICE_LIST_TYPE_FK]: Cypress.env('API_PLANT_LIST_TYPE_ID_1'),
            [app.GridCells.CURRENCY]: apiConstantData.ID.CURRENCY_EUR,
            [app.GridCells.UOM_FK]: apiConstantData.ID.UOM_HOUR,
            [app.GridCells.DESCRIPTION_INFO]: PLANT_PRICE_LIST_P,
            [app.GridCells.CALCULATION_TYPE_FK]: apiConstantData.ID.CALCULATION_TYPE_AVERAGE_CATALOG_VALUE,
            [app.GridCells.PERCENT]: CONTAINER_DATA_RECORD.PERCENT,
            [app.GridCells.IS_MANUAL_EDIT_PLANT_MASTER]: "true"
        }
        _commonAPI.createPlantPriceList(DATA_RECORD_PARAMETER_PERFORMANCE)

    })

    it("TC - Create plant type", function () {

        let PLANT_TYPE_RENTAL_PARAMETER: DataCells = {
            [app.GridCells.IS_CLUSTER]: "true",
            [app.GridCells.DESCRIPTION_INFO]: PLANT_TYPE_RENTAL
        }
        _commonAPI.createPlantType(PLANT_TYPE_RENTAL_PARAMETER)

        let PLANT_TYPE_PERFORMANCE_PARAMETER: DataCells = {
            [app.GridCells.IS_TIMEKEEPING]: "true",
            [app.GridCells.IS_CLUSTER]: "true",
            [app.GridCells.DESCRIPTION_INFO]: PLANT_TYPE_PERFORMANCE
        }
        _commonAPI.createPlantType(PLANT_TYPE_PERFORMANCE_PARAMETER)

        let PLANT_TYPE_BULK_PARAMETER: DataCells = {
            [app.GridCells.IS_BULK]: "true",
            [app.GridCells.DESCRIPTION_INFO]: PLANT_TYPE_BULK
        }
        _commonAPI.createPlantType(PLANT_TYPE_BULK_PARAMETER)
    })

    it("TC - Create work operation types and assign plant type to work operation ", function () {

        let OPERATION_TYPE_PARAMETER_REPAIR: DataCells = {
            [app.GridCells.IS_HIRE]: "false",
            [app.GridCells.UOM]: apiConstantData.ID.UOM_DAY,
            [app.GridCells.IS_LIVE]: "true",
            [app.GridCells.DESCRIPTION_INFO]: DESC_REPAIR

        }
        _commonAPI.createWorkOperationType(OPERATION_TYPE_PARAMETER_REPAIR).then(() => {
            let PLANT_TYPE_RENTAL: DataCells = {
                [app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_ID_1`),
                [app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_ID_1`)
            }
            _commonAPI.createWorkOperationPlantType(PLANT_TYPE_RENTAL)

            let PLANT_TYPE_PERFORMANCE: DataCells = {
                [app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_ID_1`),
                [app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_ID_2`)
            }
            _commonAPI.createWorkOperationPlantType(PLANT_TYPE_PERFORMANCE)

            let PLANT_TYPE_BULK: DataCells = {
                [app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_ID_1`),
                [app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_ID_3`)
            }
            _commonAPI.createWorkOperationPlantType(PLANT_TYPE_BULK)
        })



        let OPERATION_TYPE_PARAMETER_WORK: DataCells = {
            [app.GridCells.IS_HIRE]: "true",
            [app.GridCells.UOM]: apiConstantData.ID.UOM_DAY,
            [app.GridCells.IS_LIVE]: "true",
            [app.GridCells.DESCRIPTION_INFO]: DESC_WORK

        }
        _commonAPI.createWorkOperationType(OPERATION_TYPE_PARAMETER_WORK).then(() => {
            let PLANT_TYPE_RENTAL: DataCells = {
                [app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_ID_2`),
                [app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_ID_1`)
            }
            _commonAPI.createWorkOperationPlantType(PLANT_TYPE_RENTAL)

            let PLANT_TYPE_BULK: DataCells = {
                [app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_ID_2`),
                [app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_ID_3`)
            }
            _commonAPI.createWorkOperationPlantType(PLANT_TYPE_BULK)
        })

        let OPERATION_TYPE_PARAMETER_PERFORMANCE: DataCells = {
            [app.GridCells.UOM]: apiConstantData.ID.UOM_HOUR,
            [app.GridCells.IS_LIVE]: "true",
            [app.GridCells.DESCRIPTION_INFO]: DESC_PERFORMANCE

        }
        _commonAPI.createWorkOperationType(OPERATION_TYPE_PARAMETER_PERFORMANCE).then(() => {
            let PLANT_TYPE_PERFORMANCE: DataCells = {
                [app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_ID_3`),
                [app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_ID_2`),
                [app.GridCells.IS_TIMEKEEPING_DEFAULT]: "true"
            }
            _commonAPI.createWorkOperationPlantType(PLANT_TYPE_PERFORMANCE)
        })


    })

    it("TC - Create new Plant Group and Sub record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_GROUP)
        _common.openTab(app.TabBar.PLANT_GROUP_AND_LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_GROUP, app.FooterTab.PLANT_GROUPS, 0)
            _common.setup_gridLayout(cnt.uuid.PLANT_GROUP, CONTAINER_COLUMNS_PLANT_GROUP)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_GROUP)
        })
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.PLANT_GROUP)
        _common.clickOn_expandCollapseButton(cnt.uuid.PLANT_GROUP, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.COLLAPSE_ALL)
        _common.clear_subContainerFilter(cnt.uuid.PLANT_GROUP)
        _common.create_newRecord(cnt.uuid.PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, PLANT_GROUP_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT_GROUP, PLANT_GROUP_DESC)
        _common.create_newSubRecord(cnt.uuid.PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, SUB_PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, SUB_PLANT_GROUP_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(2000)

    });

    it("TC - Create new plant in plant master", function () {
        PLANT_PARAMETERS = {
            [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION,
            [app.GridCells.PLANT_GROUP_FK]: SUB_PLANT_GROUP,
            [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
            [app.GridCells.PLANT_TYPE_FK]: PLANT_TYPE_PERFORMANCE,
            [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
            [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
        }

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        cy.wait(4000)
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
            _common.setup_gridLayout(cnt.uuid.PLANT, CONTAINER_COLUMNS_PLANT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PLANT)
        _common.clear_subContainerFilter(cnt.uuid.PLANT)
        _common.create_newRecord(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS)
        _common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE)
        cy.wait(1000)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
    });

    it('TC - Add Controlling Unit to Plant from Plant Master Module', function () {
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            cy.REFRESH_CONTAINER()
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PLANT_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.PLANT)
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_CONTROLLING, app.FooterTab.CONTROLLING_UNIT)
            _common.setup_gridLayout(cnt.uuid.PLANT_CONTROLLING, CONTAINER_COLUMNS_PLANT_CONTROLLING)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_CONTROLLING)
        });
        _common.create_newRecord(cnt.uuid.PLANT_CONTROLLING)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to appear dropdown
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_CONTROLLING, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it('TC - Assign Price List to Plant', function () {
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICE_LISTS, app.FooterTab.PRICE_LISTS_SMALL, 1)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICE_LISTS)
        });
        _common.create_newRecord(cnt.uuid.PLANT_PRICE_LISTS)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_PRICE_LIST_P)
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it('TC - Create Initial Allocation for plants from wizard', function () {
        PLANTS_ALLOCATION_PARAMETER = {
            [commonLocators.CommonLabels.JOB]: Cypress.env('API_PROJECT_NUMBER_1'),
            [commonLocators.CommonLabels.ALLOCATED_FROM]: MODAL_PLANT_ALLOCATION.ALLOCATED_FROM,
            [app.GridCells.WORK_OPERATION_TYPE_FK]: DESC_PERFORMANCE
        }

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PLANT_DESCRIPTION)


        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_INITIAL_ALLOCATION_FOR_PLANTS)
        _common.waitForLoaderToDisappear()
        _logesticPage.create_initialAllocationForPlants_fromWizard_byClass(PLANTS_ALLOCATION_PARAMETER, PLANT_TYPE_PERFORMANCE)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
        });
        _common.select_allContainerData(cnt.uuid.PLANT_ALLOCATIONS)
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
        });
        _common.select_allContainerData(cnt.uuid.PLANT_LOCATION)
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
    });

    it('TC - API: Create project - B', function () {

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2')).pinnedItem();
        });
    })

    it("TC - Add Controlling Unit", function () {

        const CONTROLLING_UNIT2_SUB_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_BILLING_ELEMENT]: ["true", "true"],
            [app.GridCells.ISA_ACCOUNTING_ELEMENT]: ["true", "true"],
            [app.GridCells.IS_PLANNING_ELEMENT]: ["true", "true"],
            [app.GridCells.IS_TIMEKEEPING_ELEMENT]: ["true", "true"]
        }
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_2'), 2, CONTROLLING_UNIT2_SUB_PARAMETERS)
    })

    it("TC - Create Job record in Logistic Job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(2000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        cy.wait(1000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS, 0);
            _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_JOBS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_JOBS.controllingunitfk], cnt.uuid.JOBS)
        });
        _common.clear_subContainerFilter(cnt.uuid.JOBS)
        _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.maximizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
        cy.wait(1000)//required wait to enable data input fields
        _common.clickOn_activeRowCell(cnt.uuid.JOBS, app.GridCells.CONTROLLING_UNIT_FK)
        cy.wait(1000)//required wait to save data
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.CONTROLLING_UNIT_FK, Cypress.env(`API_CNT_CODE_1`), commonLocators.CommonKeys.GRID)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create Job record in Logistic Price Condition module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_PRICE_CONDITION)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONDITIONS, app.FooterTab.CONDITIONS);
            _common.setup_gridLayout(cnt.uuid.CONDITIONS, CONTAINER_COLUMNS_CONDITIONS)
            _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        });
        _common.maximizeContainer(cnt.uuid.CONDITIONS)
        _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        _common.create_newRecord(cnt.uuid.CONDITIONS)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, CONDITION_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, CONDITION_DESC)
        _common.set_cellCheckboxValue(cnt.uuid.CONDITIONS, app.GridCells.IS_HANDLING_CHARGE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONDITIONS)
        _common.select_rowHasValue(cnt.uuid.CONDITIONS, CONDITION_DESC)
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.FooterTab.PLANT_CATALOG_PRICELISTS);
        });
        _common.maximizeContainer(cnt.uuid.EQUIPMENT_CATALOG_PRICES)

        _common.clear_subContainerFilter(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.edit_dropdownCellWithCaret_scroll(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_PRICE_LIST_R)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_PRICE_LIST_P)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.minimizeContainer(cnt.uuid.EQUIPMENT_CATALOG_PRICES)

        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_ITEM, app.FooterTab.WORK_OPERATION_TYPE_CONDITIONS);
            _common.maximizeContainer(cnt.uuid.PRICE_CONDITION_ITEM);
            _common.clear_subContainerFilter(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.minimizeContainer(cnt.uuid.PRICE_CONDITION_ITEM);
            _common.create_newRecord(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.waitForLoaderToDisappear()
            _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, DESC_WORK)
        });
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        _common.edit_caretDropdown_fromModal_byClass(app.ModalInputFields.PRICING_GROUP_FK)
        _common.select_ItemFromPopUpList(commonLocators.CommonKeys.LIST, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PLANT_PRICING_GROUP)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_ITEM, app.FooterTab.WORK_OPERATION_TYPE_CONDITIONS);
            _common.maximizeContainer(cnt.uuid.PRICE_CONDITION_ITEM);
            _common.clear_subContainerFilter(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.minimizeContainer(cnt.uuid.PRICE_CONDITION_ITEM);
            _common.create_newRecord(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, DESC_REPAIR)
            _common.edit_containerCell(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_01, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PERCENTAGE)
            _common.edit_containerCell(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_02, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PERCENTAGE)
            _common.edit_containerCell(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_03, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PERCENTAGE)
            _common.edit_containerCell(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_04, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PERCENTAGE)
            _common.edit_containerCell(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_05, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PERCENTAGE)
            _common.edit_containerCell(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_06, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PERCENTAGE)
        })
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        _common.edit_caretDropdown_fromModal_byClass(app.ModalInputFields.PRICING_GROUP_FK)
        _common.select_ItemFromPopUpList(commonLocators.CommonKeys.LIST, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PLANT_PRICING_GROUP)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Assign Price Condition in Logistic Job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS);
            _common.search_inSubContainer(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
            _common.waitForLoaderToDisappear()
            _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        });
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PRICE_CONDITION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONDITION_CODE)
        // _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_JOBS.BUSINESS_PARTNER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create record in Dispatching Notes", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER)
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.create_newRecord(cnt.uuid.DISPATCHING_HEADER)
        cy.wait(1000)
        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, CONTAINERS_DISPATCHING_HEADER.EFFECTIVE_DATE)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB1_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB2_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.DISPATCHING_HEADER)

        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()

        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE, "DISPATCH_CODE")
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue, (cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        cy.REFRESH_SELECTED_ENTITIES()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, app.FooterTab.PERFORMING_JOB_PLANT_LOCATIONS)
        })
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS)
        })
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.create_newRecord(cnt.uuid.DISPATCHING_RECORD)
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.RECORD_TYPE_FK, commonLocators.CommonKeys.GRID, CONTAINERS_DISPATCHING_HEADER.RECORD_TYPE)
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_RECORD, app.GridCells.ARTICLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE)
        cy.wait(1000) //required wait to enable work operations type cell
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.WORK_OPERATION_TYPE_FK, DESC_REPAIR, commonLocators.CommonKeys.GRID)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER)
        })
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.TRANSPORT_PLANNING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.PICKING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.LOADED)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.DELIVERED)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Validation of Dispatch Notes in Plant Master Module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
        });
        _validate.verify_isRecordPresent(cnt.uuid.PLANT_LOCATION, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
        });
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.PLANT_ALLOCATIONS, Cypress.env("DISPATCH_CODE"))
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.PLANT_ALLOCATIONS, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()

    });

    it("TC - Create Settlement in settlement module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.SETTLEMENT)

        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.clear_searchInSidebar();
        _common.waitForLoaderToDisappear()
        cy.wait(2000)

        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.START_SETTLEMENT_BATCH)
        _common.inputField_fromModal(commonLocators.CommonElements.ROW, commonLocators.CommonLabels.DUE_DATE, 0, app.InputFields.INPUT_GROUP_CONTENT).clear({ force: true }).type(_common.getDate(CommonLocators.CommonKeys.INCREMENTED_SMALL, 10), { force: true })
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.wait(3000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        cy.wait(3000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        cy.wait(3000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to search project and select row
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.SETTLEMENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.saveCellDataToEnv(cnt.uuid.SETTLEMENT, app.GridCells.SETTLEMENT_NO, "SettlementNo")
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_ITEMS, app.FooterTab.SETTLEMENT_ITEMS)
            _common.setup_gridLayout(cnt.uuid.SETTLEMENT_ITEMS, CONTAINER_COLUMNS_SETTLEMENT_ITEMS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_SETTLEMENT_ITEMS.dispatchheaderfk, CONTAINER_COLUMNS_SETTLEMENT_ITEMS.dispatchrecordfkdescription], cnt.uuid.SETTLEMENT_ITEMS)
        });
        _common.select_rowInContainer(cnt.uuid.SETTLEMENT_ITEMS)
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT_ITEMS, app.GridCells.DISPATCH_HEADER_FK, Cypress.env("DISPATCH_CODE"))
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT_ITEMS, app.GridCells.DISPATCH_RECORD_FK_DESCRIPTION, PLANT_DESCRIPTION)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create Settlement Claims from wizard", function () {

        SETTLEMENT_CLAIM_PARAMETERS = {
            [commonLocators.CommonLabels.CLAIM_REASON]: MODAL_SETTLEMENT_CLAIM.CLAIM_REASON,
            [commonLocators.CommonLabels.COMMENTS]: MODAL_SETTLEMENT_CLAIM.COMMENTS,
            [commonLocators.CommonLabels.EXPECTED_WOT]: DESC_REPAIR
        }

        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT, Cypress.env("SettlementNo"))
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT, Cypress.env("SettlementNo"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_ITEMS, app.FooterTab.SETTLEMENT_ITEMS)
        });
        _common.select_rowInContainer(cnt.uuid.SETTLEMENT_ITEMS)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_SETTLEMENT_CLAIMS)
        _common.waitForLoaderToDisappear()
        _logesticPage.create_settlementClaim_fromWizard(SETTLEMENT_CLAIM_PARAMETERS)

        _common.openTab(app.TabBar.SETTLEMENT_ITEMS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS)
        });
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT, Cypress.env("SettlementNo"))
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT, Cypress.env("SettlementNo"))

        _common.openTab(app.TabBar.SETTLEMENT_ITEMS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_CLAIMS, app.FooterTab.SETTLEMENT_CLAIMS)
        });//container refresh
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)
        _common.clickOn_toolbarButton(cnt.uuid.SETTLEMENT_CLAIMS, btn.ToolBar.ICO_REFRESH)
        cy.wait(1000)
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT_CLAIMS, Cypress.env("SettlementNo"))
        _validate.verify_isRecordPresent(cnt.uuid.SETTLEMENT_CLAIMS, Cypress.env("SettlementNo"))
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_SETTLEMENT_CLAIM_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.ACCEPTED)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_SETTLEMENT_CLAIM_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.DONE)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_DISPATCH_NOTE_INFORMATION_TO_CORRECT_DATA_FOR_SETTLEMENT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.FINISH)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)


         _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_RECORD, CONTAINER_COLUMNS_DISPATCHING_RECORD)
            _common.set_columnAtTop([CONTAINER_COLUMNS_DISPATCHING_RECORD.pricetotaloc, CONTAINER_COLUMNS_DISPATCHING_RECORD.workoperationtypefkdescription], cnt.uuid.DISPATCHING_RECORD)
        })

        cy.wait(2000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.clear_searchInSidebar();
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER)
        })
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))

        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
        })
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _validate.verify_isRecordPresent(cnt.uuid.DISPATCHING_RECORD, DESC_REPAIR)
        _common.waitForLoaderToDisappear()
        _common.assert_cellData(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_TOTAL_OC, CONTAINERS_DISPATCHING_HEADER.TOTAL_PRICE)
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
           
        })
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(2000)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PLANT_DESCRIPTION)

        _common.clear_subContainerFilter(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)

        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ALLOCATIONS, app.FooterTab.PLANT_ALLOCATIONS)
        })
        _common.maximizeContainer(cnt.uuid.PLANT_ALLOCATIONS)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.PLANT_ALLOCATIONS, DESC_REPAIR)
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PLANT_ALLOCATIONS)

    })
});