/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { IConHeaderEntity } from '../model/entities';
import { ContractComplete } from '../model/contract-complete.class';
import { ProcurementContractHeaderDataService } from '../services/procurement-contract-header-data.service';
import { ProcurementCommonWizardBaseService } from '@libs/procurement/common';
import { IEditorDialogResult, StandardDialogButtonId } from '@libs/ui/common';
import { DialogType, IPesCreationParam, PesEntity, PRC_CONTRACT_CREATE_PES_PARAM, PRC_CONTRACT_CREATE_PES_PARAMS, ValidResult, ValidResultKeys } from '../model/interfaces/wizards/prc-contract-create-pes-wizard.interface';
import { PrcConCreatePesCoverConfirmComponent } from '../components/prc-con-create-pes-dialog/cover-confirm-page/cover-confirm-page.component';
import { PrcConCreatePartialPesComponent } from '../components/prc-con-create-pes-dialog/create-partial-pes/create-partial-pes.component';
import { ProcurementInternalModule } from '@libs/procurement/shared';

@Injectable({
	providedIn: 'root',
})
export class ProcurementContractCreatePesWizardService extends ProcurementCommonWizardBaseService<IConHeaderEntity, ContractComplete, IPesCreationParam[]> {
	private validContracts: IConHeaderEntity[] = [];

	public constructor(private readonly dataService: ProcurementContractHeaderDataService) {
		super({ rootDataService: dataService });
	}

	protected override async startWizardValidate(): Promise<boolean> {
		await super.startWizardValidate(false);
		const selected = this.dataService.getSelection();
		const headerText = 'procurement.contract.wizard.isActivateCaption';

		if (selected.some((e) => e.IsFramework)) {
			await this.messageBoxService.showMsgBox('procurement.contract.frameworkContractNotSupported', headerText, 'ico-error');
			return false;
		}

		this.wizardUtilService.showLoadingDialog('procurement.contract.wizard.createPesTitle');
		const result = await this.validateContractsStatus(selected);
		this.validContracts = result.ValidContracts;

		if (!result.Messages) {
			return true;
		}
		return await this.handleValidationMessages(result, headerText);
	}

	protected override async showWizardDialog(): Promise<IEditorDialogResult<IPesCreationParam[]> | undefined> {
		const results = await this.createPes4ContractPrepareCreation(this.validContracts);
		this.wizardUtilService.closeLoadingDialog();
		if (!results.length) {
			return undefined;
		}

		const grouped = this.groupByDialogType(results);
		const collectedOptions: IPesCreationParam[] = [];

		for (const [type, items] of grouped) {
			if (type === DialogType.CreateCompletely) {
				collectedOptions.push(...items);
			} else {
				const dialogResult = await this.handleDialogType(type, items);
				if (dialogResult?.closingButtonId === StandardDialogButtonId.Ok && dialogResult.value) {
					collectedOptions.push(...dialogResult.value);
				}
			}
		}

		return collectedOptions.length
			? {
					closingButtonId: StandardDialogButtonId.Ok,
					value: collectedOptions,
				}
			: undefined;
	}

	protected override async onFinishWizard(params: IPesCreationParam[]): Promise<void> {
		this.wizardUtilService.showLoadingDialog('procurement.contract.wizard.createPesTitle', { key: 'procurement.contract.wizard.processingCreatePes' });
		let results: PesEntity[];
		try {
			results = await this.createPes4Contract(params);
		} catch (error) {
			this.wizardUtilService.closeLoadingDialog();
			throw error;
		}
		this.wizardUtilService.closeLoadingDialog();
		const successMessage = this.translateService.instant('procurement.contract.wizard.createPesSuccessfully').text;
		const codesMessage = this.translateService.instant('procurement.contract.wizard.newCode', { newCode: results.map((p) => p.Code).join(', ') }).text;

		await this.wizardUtilService.showGoToMsgBox(
			`${successMessage}\n${codesMessage}`,
			'procurement.contract.wizard.createPesTitle',
			results.map((entity) => ({ id: entity.Id })),
			ProcurementInternalModule.Pes
		);
	}

	private async handleDialogType(type: DialogType, items: IPesCreationParam[]): Promise<IEditorDialogResult<IPesCreationParam[]> | undefined> {
		if (type === DialogType.ShowCoverConfirmDialog || type === DialogType.ShowNonContractedItems) {
			return this.showCoverConfirmDialog(items, type === DialogType.ShowCoverConfirmDialog);
		}
		if (type === DialogType.ShowOptionDialog) {
			const results = await Promise.all(items.map((item) => this.showOptionDialog(item)));
			return { closingButtonId: StandardDialogButtonId.Ok, value: results.flatMap((res) => res?.value || []) };
		}
		return undefined;
	}

	private async handleValidationMessages(result: ValidResult, headerText: string): Promise<boolean> {
		const messages = {
			[ValidResultKeys.IsValid]: 'procurement.contract.wizard.isActiveMessage',
			[ValidResultKeys.CreatePesSkipContracts]: 'procurement.contract.wizard.createPesSkipContracts',
			[ValidResultKeys.CreatePesSkipContractsByInvalidBoq]: 'procurement.contract.wizard.createPesSkipContractsByInvalidBoq',
			[ValidResultKeys.CreatePesDefaultPrcClerkRequired]: 'procurement.contract.wizard.createPesDefaultPrcClerkRequired',
			[ValidResultKeys.MultipleBaseContractAbortCreatePes]: 'procurement.contract.wizard.multipleBaseContractAbortCreatePes',
		};

		let continueFlag = true;

		for (const [key, msgKey] of Object.entries(messages)) {
			const keyNum = +key;
			const isError = keyNum === ValidResultKeys.IsValid || keyNum === ValidResultKeys.MultipleBaseContractAbortCreatePes;
			const msgDict = result.Messages.find((m) => keyNum in m);
			if (!msgDict) {
				continue;
			}

			this.wizardUtilService.closeLoadingDialog();
			const bodyText = this.translateService.instant(msgKey, { contracts: msgDict[keyNum] }).text;
			await this.messageBoxService.showMsgBox(bodyText, headerText, isError ? 'ico-error' : 'ico-info');
			if (isError) {
				continueFlag = false;
			}
		}
		return continueFlag;
	}

	private groupByDialogType(results: IPesCreationParam[]): Map<DialogType, IPesCreationParam[]> {
		return results.reduce((map, item) => {
			const list = map.get(item.DialogType) ?? [];
			list.push(item);
			map.set(item.DialogType, list);
			return map;
		}, new Map<DialogType, IPesCreationParam[]>());
	}

	private async showCoverConfirmDialog(creationParams: IPesCreationParam[], showIncluded: boolean) {
		const contractCodes = creationParams.map((p) => p.ContractCode).join(', ');
		const headerText = this.translateService.instant('procurement.contract.wizard.createPesByCurrentContract', { contracts: contractCodes }).text;

		const result = await this.dialogService.show({
			width: '400px',
			headerText,
			resizeable: true,
			showCloseButton: true,
			bodyComponent: PrcConCreatePesCoverConfirmComponent,
			bodyProviders: [
				{
					provide: PRC_CONTRACT_CREATE_PES_PARAMS,
					useValue: creationParams.map((creationParam) => ({
						...creationParam,
						ShowIncluded: showIncluded,
						IsIncluded: showIncluded,
					})),
				},
			],
			buttons: [
				{
					id: StandardDialogButtonId.Yes,
					caption: { key: 'ui.common.dialog.yesBtn' },
					fn: (_, info) => info.dialog.body.onConfirm(),
					isVisible: showIncluded,
				},
				{
					id: StandardDialogButtonId.No,
					caption: { key: 'ui.common.dialog.noBtn' },
					isVisible: showIncluded,
				},
				{
					id: 'include',
					caption: { key: 'procurement.contract.wizard.include' },
					fn: (_, info) => info.dialog.body.updateDialogWrapper(true),
					isVisible: !showIncluded,
				},
				{
					id: StandardDialogButtonId.Ignore,
					caption: { key: 'ui.common.dialog.ignoreBtn' },
					fn: (_, info) => info.dialog.body.updateDialogWrapper(false),
					isVisible: !showIncluded,
				},
			],
		});

		return {
			closingButtonId: result.closingButtonId,
			value: result.value,
		} as IEditorDialogResult<IPesCreationParam[]>;
	}

	private async showOptionDialog(creationParam: IPesCreationParam) {
		const headerText = this.translateService.instant('procurement.contract.wizard.createPesByCurrentContract', {
			contracts: creationParam.ContractCode,
		}).text;

		const result = await this.dialogService.show({
			width: '800px',
			headerText,
			resizeable: true,
			showCloseButton: true,
			bodyComponent: PrcConCreatePartialPesComponent,
			bodyProviders: [
				{
					provide: PRC_CONTRACT_CREATE_PES_PARAM,
					useValue: {
						...creationParam,
					},
				},
			],
			buttons: [
				{
					id: StandardDialogButtonId.Ok,
					caption: { key: 'ui.common.dialog.okBtn' },
					fn: (_, info) => info.dialog.body.onConfirm(),
				},
				{
					id: StandardDialogButtonId.Cancel,
					caption: { key: 'ui.common.dialog.cancelBtn' },
				},
			],
		});
		return {
			closingButtonId: result.closingButtonId,
			value: result.value,
		} as IEditorDialogResult<IPesCreationParam>;
	}

	private validateContractsStatus(entities: IConHeaderEntity[]) {
		return this.http.post<ValidResult>('procurement/contract/header/validatecontracts2createpes', entities);
	}

	private createPes4ContractPrepareCreation(entities: IConHeaderEntity[]) {
		return this.http.post<IPesCreationParam[]>('procurement/pes/wizard/createpes4contractpreparecreation', entities);
	}

	private createPes4Contract(params: IPesCreationParam[]) {
		// Remove large data objects from each parameter to optimize network payload
		// Using destructuring to exclude unnecessary properties
		const cleanParams = params.map(({ BoqPrcItems, PesHeaders, PesContract, ...rest }) => {
			// These properties are intentionally excluded to reduce network traffic
			return rest;
		});
		return this.http.post<PesEntity[]>('procurement/pes/wizard/createpes4contract', cleanParams );
	}
}
