{
	"compileOnSave": false,
	"compilerOptions": {
		"rootDir": ".",
		"sourceMap": true,
		"declaration": false,
		"moduleResolution": "node",
		"emitDecoratorMetadata": true,
		"experimentalDecorators": true,
		"importHelpers": true,
		"target": "es2020",
		"module": "esnext",
		"lib": ["es2020", "dom"],
		"skipLibCheck": true,
		"skipDefaultLibCheck": true,
		"allowSyntheticDefaultImports": true,
		"baseUrl": ".",
		"paths": {
			"@libs/basics/accountingjournals": ["libs/modules/basics/accountingjournals/src/index.ts"],
			"@libs/basics/assetmaster": ["libs/modules/basics/assetmaster/src/index.ts"],
			"@libs/basics/bank": ["libs/modules/basics/bank/src/index.ts"],
			"@libs/basics/billingschema": ["libs/modules/basics/billingschema/src/index.ts"],
			"@libs/basics/biplusdesigner": ["libs/modules/basics/biplusdesigner/src/index.ts"],
			"@libs/basics/characteristic": ["libs/modules/basics/characteristic/src/index.ts"],
			"@libs/basics/clerk": ["libs/modules/basics/clerk/src/index.ts"],
			"@libs/basics/common": ["libs/modules/basics/common/src/index.ts"],
			"@libs/basics/company": ["libs/modules/basics/company/src/index.ts"],
			"@libs/basics/config": ["libs/modules/basics/config/src/index.ts"],
			"@libs/basics/controllingcostcodes": ["libs/modules/basics/controllingcostcodes/src/index.ts"],
			"@libs/basics/costcodes": ["libs/modules/basics/costcodes/src/index.ts"],
			"@libs/basics/costgroups": ["libs/modules/basics/costgroups/src/index.ts"],
			"@libs/basics/country": ["libs/modules/basics/country/src/index.ts"],
			"@libs/basics/currency": ["libs/modules/basics/currency/src/index.ts"],
			"@libs/basics/customize": ["libs/modules/basics/customize/src/index.ts"],
			"@libs/basics/dependentdata": ["libs/modules/basics/dependentdata/src/index.ts"],
			"@libs/basics/efbsheets": ["libs/modules/basics/efbsheets/src/index.ts"],
			"@libs/basics/indextable": ["libs/modules/basics/indextable/src/index.ts"],
			"@libs/basics/interfaces": ["libs/modules/basics/interfaces/src/index.ts"],
			"@libs/basics/material": ["libs/modules/basics/material/src/index.ts"],
			"@libs/basics/materialcatalog": ["libs/modules/basics/materialcatalog/src/index.ts"],
			"@libs/basics/meeting": ["libs/modules/basics/meeting/src/index.ts"],
			"@libs/basics/payment": ["libs/modules/basics/payment/src/index.ts"],
			"@libs/basics/preload": ["libs/modules/basics/preload/src/index.ts"],
			"@libs/basics/pricecondition": ["libs/modules/basics/pricecondition/src/index.ts"],
			"@libs/basics/procurementconfiguration": ["libs/modules/basics/procurementconfiguration/src/index.ts"],
			"@libs/basics/procurementstructure": ["libs/modules/basics/procurementstructure/src/index.ts"],
			"@libs/basics/regioncatalog": ["libs/modules/basics/regioncatalog/src/index.ts"],
			"@libs/basics/reporting": ["libs/modules/basics/reporting/src/index.ts"],
			"@libs/basics/salestaxcode": ["libs/modules/basics/salestaxcode/src/index.ts"],
			"@libs/basics/shared": ["libs/modules/basics/shared/src/index.ts"],
			"@libs/basics/site": ["libs/modules/basics/site/src/index.ts"],
			"@libs/basics/taxcode": ["libs/modules/basics/taxcode/src/index.ts"],
			"@libs/basics/textmodules": ["libs/modules/basics/textmodules/src/index.ts"],
			"@libs/basics/unit": ["libs/modules/basics/unit/src/index.ts"],
			"@libs/basics/userform": ["libs/modules/basics/userform/src/index.ts"],
			"@libs/basics/workflow": ["libs/modules/basics/workflow/src/index.ts"],
			"@libs/boq/interfaces": ["libs/modules/boq/interfaces/src/index.ts"],
			"@libs/boq/main": ["libs/modules/boq/main/src/index.ts"],
			"@libs/boq/preload": ["libs/modules/boq/preload/src/index.ts"],
			"@libs/boq/project": ["libs/modules/boq/project/src/index.ts"],
			"@libs/boq/shared": ["libs/modules/boq/shared/src/index.ts"],
			"@libs/boq/wic": ["libs/modules/boq/wic/src/index.ts"],
			"@libs/businesspartner/certificate": ["libs/modules/businesspartner/certificate/src/index.ts"],
			"@libs/businesspartner/common": ["libs/modules/businesspartner/common/src/index.ts"],
			"@libs/businesspartner/contact": ["libs/modules/businesspartner/contact/src/index.ts"],
			"@libs/businesspartner/evaluationschema": ["libs/modules/businesspartner/evaluationschema/src/index.ts"],
			"@libs/businesspartner/interfaces": ["libs/modules/businesspartner/interfaces/src/index.ts"],
			"@libs/businesspartner/main": ["libs/modules/businesspartner/main/src/index.ts"],
			"@libs/businesspartner/preload": ["libs/modules/businesspartner/preload/src/index.ts"],
			"@libs/businesspartner/shared": ["libs/modules/businesspartner/shared/src/index.ts"],
			"@libs/cloud/interfaces": ["libs/modules/cloud/interfaces/src/index.ts"],
			"@libs/cloud/preload": ["libs/modules/cloud/preload/src/index.ts"],
			"@libs/cloud/translation": ["libs/modules/cloud/translation/src/index.ts"],
			"@libs/constructionsystem/common": ["libs/modules/constructionsystem/common/src/index.ts"],
			"@libs/constructionsystem/interfaces": ["libs/modules/constructionsystem/interfaces/src/index.ts"],
			"@libs/constructionsystem/main": ["libs/modules/constructionsystem/main/src/index.ts"],
			"@libs/constructionsystem/master": ["libs/modules/constructionsystem/master/src/index.ts"],
			"@libs/constructionsystem/preload": ["libs/modules/constructionsystem/preload/src/index.ts"],
			"@libs/constructionsystem/project": ["libs/modules/constructionsystem/project/src/index.ts"],
			"@libs/constructionsystem/shared": ["libs/modules/constructionsystem/shared/src/index.ts"],
			"@libs/controlling/actuals": ["libs/modules/controlling/actuals/src/index.ts"],
			"@libs/controlling/common": ["libs/modules/controlling/common/src/index.ts"],
			"@libs/controlling/configuration": ["libs/modules/controlling/configuration/src/index.ts"],
			"@libs/controlling/controllingunittemplate": ["libs/modules/controlling/controllingunittemplate/src/index.ts"],
			"@libs/controlling/enterprise": ["libs/modules/controlling/enterprise/src/index.ts"],
			"@libs/controlling/generalcontractor": ["libs/modules/controlling/generalcontractor/src/index.ts"],
			"@libs/controlling/interfaces": ["libs/modules/controlling/interfaces/src/index.ts"],
			"@libs/controlling/preload": ["libs/modules/controlling/preload/src/index.ts"],
			"@libs/controlling/projectcontrols": ["libs/modules/controlling/projectcontrols/src/index.ts"],
			"@libs/controlling/revrecognition": ["libs/modules/controlling/revrecognition/src/index.ts"],
			"@libs/controlling/shared": ["libs/modules/controlling/shared/src/index.ts"],
			"@libs/controlling/structure": ["libs/modules/controlling/structure/src/index.ts"],
			"@libs/defect/interfaces": ["libs/modules/defect/interfaces/src/index.ts"],
			"@libs/defect/main": ["libs/modules/defect/main/src/index.ts"],
			"@libs/defect/preload": ["libs/modules/defect/preload/src/index.ts"],
			"@libs/defect/shared": ["libs/modules/defect/shared/src/index.ts"],
			"@libs/desktop/common": ["libs/modules/desktop/common/src/index.ts"],
			"@libs/desktop/main": ["libs/modules/desktop/main/src/index.ts"],
			"@libs/desktop/preload": ["libs/modules/desktop/preload/src/index.ts"],
			"@libs/desktop/shared": ["libs/modules/desktop/shared/src/index.ts"],
			"@libs/documents/centralquery": ["libs/modules/documents/centralquery/src/index.ts"],
			"@libs/documents/common": ["libs/modules/documents/common/src/index.ts"],
			"@libs/documents/import": ["libs/modules/documents/import/src/index.ts"],
			"@libs/documents/interfaces": ["libs/modules/documents/interfaces/src/index.ts"],
			"@libs/documents/main": ["libs/modules/documents/main/src/index.ts"],
			"@libs/documents/preload": ["libs/modules/documents/preload/src/index.ts"],
			"@libs/documents/project": ["libs/modules/documents/project/src/index.ts"],
			"@libs/documents/shared": ["libs/modules/documents/shared/src/index.ts"],
			"@libs/estimate/assemblies": ["libs/modules/estimate/assemblies/src/index.ts"],
			"@libs/estimate/common": ["libs/modules/estimate/common/src/index.ts"],
			"@libs/estimate/interfaces": ["libs/modules/estimate/interfaces/src/index.ts"],
			"@libs/estimate/main": ["libs/modules/estimate/main/src/index.ts"],
			"@libs/estimate/parameter": ["libs/modules/estimate/parameter/src/index.ts"],
			"@libs/estimate/preload": ["libs/modules/estimate/preload/src/index.ts"],
			"@libs/estimate/project": ["libs/modules/estimate/project/src/index.ts"],
			"@libs/estimate/rule": ["libs/modules/estimate/rule/src/index.ts"],
			"@libs/estimate/shared": ["libs/modules/estimate/shared/src/index.ts"],
			"@libs/example/common": ["libs/modules/example/common/src/index.ts"],
			"@libs/example/container-layout-demo": ["libs/modules/example/container-layout-demo/src/index.ts"],
			"@libs/example/interfaces": ["libs/modules/example/interfaces/src/index.ts"],
			"@libs/example/preload": ["libs/modules/example/preload/src/index.ts"],
			"@libs/example/shared": ["libs/modules/example/shared/src/index.ts"],
			"@libs/example/topic-one": ["libs/modules/example/topic-one/src/index.ts"],
			"@libs/example/topic-two": ["libs/modules/example/topic-two/src/index.ts"],
			"@libs/hsqe/checklist": ["libs/modules/hsqe/checklist/src/index.ts"],
			"@libs/hsqe/checklisttemplate": ["libs/modules/hsqe/checklisttemplate/src/index.ts"],
			"@libs/hsqe/interfaces": ["libs/modules/hsqe/interfaces/src/index.ts"],
			"@libs/hsqe/preload": ["libs/modules/hsqe/preload/src/index.ts"],
			"@libs/hsqe/shared": ["libs/modules/hsqe/shared/src/index.ts"],
			"@libs/logistic/action": ["libs/modules/logistic/action/src/index.ts"],
			"@libs/logistic/card": ["libs/modules/logistic/card/src/index.ts"],
			"@libs/logistic/cardtemplate": ["libs/modules/logistic/cardtemplate/src/index.ts"],
			"@libs/logistic/dispatching": ["libs/modules/logistic/dispatching/src/index.ts"],
			"@libs/logistic/interfaces": ["libs/modules/logistic/interfaces/src/index.ts"],
			"@libs/logistic/job": ["libs/modules/logistic/job/src/index.ts"],
			"@libs/logistic/plantsupplier": ["libs/modules/logistic/plantsupplier/src/index.ts"],
			"@libs/logistic/preload": ["libs/modules/logistic/preload/src/index.ts"],
			"@libs/logistic/pricecondition": ["libs/modules/logistic/pricecondition/src/index.ts"],
			"@libs/logistic/settlement": ["libs/modules/logistic/settlement/src/index.ts"],
			"@libs/logistic/shared": ["libs/modules/logistic/shared/src/index.ts"],
			"@libs/logistic/sundrygroup": ["libs/modules/logistic/sundrygroup/src/index.ts"],
			"@libs/logistic/sundryservice": ["libs/modules/logistic/sundryservice/src/index.ts"],
			"@libs/model/administration": ["libs/modules/model/administration/src/index.ts"],
			"@libs/model/annotation": ["libs/modules/model/annotation/src/index.ts"],
			"@libs/model/change": ["libs/modules/model/change/src/index.ts"],
			"@libs/model/changeset": ["libs/modules/model/changeset/src/index.ts"],
			"@libs/model/common": ["libs/modules/model/common/src/index.ts"],
			"@libs/model/interfaces": ["libs/modules/model/interfaces/src/index.ts"],
			"@libs/model/main": ["libs/modules/model/main/src/index.ts"],
			"@libs/model/map": ["libs/modules/model/map/src/index.ts"],
			"@libs/model/measurements": ["libs/modules/model/measurements/src/index.ts"],
			"@libs/model/preload": ["libs/modules/model/preload/src/index.ts"],
			"@libs/model/project": ["libs/modules/model/project/src/index.ts"],
			"@libs/model/shared": ["libs/modules/model/shared/src/index.ts"],
			"@libs/mtwo/controltower": ["libs/modules/mtwo/controltower/src/index.ts"],
			"@libs/mtwo/controltowerconfiguration": ["libs/modules/mtwo/controltowerconfiguration/src/index.ts"],
			"@libs/mtwo/interfaces": ["libs/modules/mtwo/interfaces/src/index.ts"],
			"@libs/mtwo/preload": ["libs/modules/mtwo/preload/src/index.ts"],
			"@libs/platform/authentication": ["libs/platform/authentication/src/index.ts"],
			"@libs/platform/common": ["libs/platform/common/src/index.ts"],
			"@libs/platform/data-access": ["libs/platform/data-access/src/index.ts"],
			"@libs/procurement/common": ["libs/modules/procurement/common/src/index.ts"],
			"@libs/procurement/contract": ["libs/modules/procurement/contract/src/index.ts"],
			"@libs/procurement/interfaces": ["libs/modules/procurement/interfaces/src/index.ts"],
			"@libs/procurement/inventory": ["libs/modules/procurement/inventory/src/index.ts"],
			"@libs/procurement/invoice": ["libs/modules/procurement/invoice/src/index.ts"],
			"@libs/procurement/orderproposals": ["libs/modules/procurement/orderproposals/src/index.ts"],
			"@libs/procurement/package": ["libs/modules/procurement/package/src/index.ts"],
			"@libs/procurement/pes": ["libs/modules/procurement/pes/src/index.ts"],
			"@libs/procurement/preload": ["libs/modules/procurement/preload/src/index.ts"],
			"@libs/procurement/pricecomparison": ["libs/modules/procurement/pricecomparison/src/index.ts"],
			"@libs/procurement/quote": ["libs/modules/procurement/quote/src/index.ts"],
			"@libs/procurement/requisition": ["libs/modules/procurement/requisition/src/index.ts"],
			"@libs/procurement/rfq": ["libs/modules/procurement/rfq/src/index.ts"],
			"@libs/procurement/shared": ["libs/modules/procurement/shared/src/index.ts"],
			"@libs/procurement/stock": ["libs/modules/procurement/stock/src/index.ts"],
			"@libs/procurement/ticketsystem": ["libs/modules/procurement/ticketsystem/src/index.ts"],
			"@libs/productionplanning/accounting": ["libs/modules/productionplanning/accounting/src/index.ts"],
			"@libs/productionplanning/cadimport": ["libs/modules/productionplanning/cadimport/src/index.ts"],
			"@libs/productionplanning/cadimportconfig": ["libs/modules/productionplanning/cadimportconfig/src/index.ts"],
			"@libs/productionplanning/common": ["libs/modules/productionplanning/common/src/index.ts"],
			"@libs/productionplanning/configuration": ["libs/modules/productionplanning/configuration/src/index.ts"],
			"@libs/productionplanning/drawing": ["libs/modules/productionplanning/drawing/src/index.ts"],
			"@libs/productionplanning/drawingtype": ["libs/modules/productionplanning/drawingtype/src/index.ts"],
			"@libs/productionplanning/engineering": ["libs/modules/productionplanning/engineering/src/index.ts"],
			"@libs/productionplanning/eventconfiguration": ["libs/modules/productionplanning/eventconfiguration/src/index.ts"],
			"@libs/productionplanning/fabricationunit": ["libs/modules/productionplanning/fabricationunit/src/index.ts"],
			"@libs/productionplanning/formulaconfiguration": ["libs/modules/productionplanning/formulaconfiguration/src/index.ts"],
			"@libs/productionplanning/formworktype": ["libs/modules/productionplanning/formworktype/src/index.ts"],
			"@libs/productionplanning/header": ["libs/modules/productionplanning/header/src/index.ts"],
			"@libs/productionplanning/interfaces": ["libs/modules/productionplanning/interfaces/src/index.ts"],
			"@libs/productionplanning/item": ["libs/modules/productionplanning/item/src/index.ts"],
			"@libs/productionplanning/mounting": ["libs/modules/productionplanning/mounting/src/index.ts"],
			"@libs/productionplanning/ppscostcodes": ["libs/modules/productionplanning/ppscostcodes/src/index.ts"],
			"@libs/productionplanning/ppsmaterial": ["libs/modules/productionplanning/ppsmaterial/src/index.ts"],
			"@libs/productionplanning/preload": ["libs/modules/productionplanning/preload/src/index.ts"],
			"@libs/productionplanning/processconfiguration": ["libs/modules/productionplanning/processconfiguration/src/index.ts"],
			"@libs/productionplanning/product": ["libs/modules/productionplanning/product/src/index.ts"],
			"@libs/productionplanning/product-template": ["libs/modules/productionplanning/product-template/src/index.ts"],
			"@libs/productionplanning/productionplace": ["libs/modules/productionplanning/productionplace/src/index.ts"],
			"@libs/productionplanning/productionset": ["libs/modules/productionplanning/productionset/src/index.ts"],
			"@libs/productionplanning/report": ["libs/modules/productionplanning/report/src/index.ts"],
			"@libs/productionplanning/shared": ["libs/modules/productionplanning/shared/src/index.ts"],
			"@libs/productionplanning/strandpattern": ["libs/modules/productionplanning/strandpattern/src/index.ts"],
			"@libs/project/calendar": ["libs/modules/project/calendar/src/index.ts"],
			"@libs/project/common": ["libs/modules/project/common/src/index.ts"],
			"@libs/project/costcodes": ["libs/modules/project/costcodes/src/index.ts"],
			"@libs/project/droppoints": ["libs/modules/project/droppoints/src/index.ts"],
			"@libs/project/efbsheets": ["libs/modules/project/efbsheets/src/index.ts"],
			"@libs/project/group": ["libs/modules/project/group/src/index.ts"],
			"@libs/project/inforequest": ["libs/modules/project/inforequest/src/index.ts"],
			"@libs/project/interfaces": ["libs/modules/project/interfaces/src/index.ts"],
			"@libs/project/location": ["libs/modules/project/location/src/index.ts"],
			"@libs/project/main": ["libs/modules/project/main/src/index.ts"],
			"@libs/project/material": ["libs/modules/project/material/src/index.ts"],
			"@libs/project/plantassembly": ["libs/modules/project/plantassembly/src/index.ts"],
			"@libs/project/preload": ["libs/modules/project/preload/src/index.ts"],
			"@libs/project/rule": ["libs/modules/project/rule/src/index.ts"],
			"@libs/project/shared": ["libs/modules/project/shared/src/index.ts"],
			"@libs/project/stock": ["libs/modules/project/stock/src/index.ts"],
			"@libs/project/structures": ["libs/modules/project/structures/src/index.ts"],
			"@libs/qto/common": ["libs/modules/qto/common/src/index.ts"],
			"@libs/qto/formula": ["libs/modules/qto/formula/src/index.ts"],
			"@libs/qto/interfaces": ["libs/modules/qto/interfaces/src/index.ts"],
			"@libs/qto/main": ["libs/modules/qto/main/src/index.ts"],
			"@libs/qto/preload": ["libs/modules/qto/preload/src/index.ts"],
			"@libs/qto/shared": ["libs/modules/qto/shared/src/index.ts"],
			"@libs/resource/catalog": ["libs/modules/resource/catalog/src/index.ts"],
			"@libs/resource/certificate": ["libs/modules/resource/certificate/src/index.ts"],
			"@libs/resource/common": ["libs/modules/resource/common/src/index.ts"],
			"@libs/resource/componenttype": ["libs/modules/resource/componenttype/src/index.ts"],
			"@libs/resource/equipment": ["libs/modules/resource/equipment/src/index.ts"],
			"@libs/resource/equipmentgroup": ["libs/modules/resource/equipmentgroup/src/index.ts"],
			"@libs/resource/interfaces": ["libs/modules/resource/interfaces/src/index.ts"],
			"@libs/resource/maintenance": ["libs/modules/resource/maintenance/src/index.ts"],
			"@libs/resource/master": ["libs/modules/resource/master/src/index.ts"],
			"@libs/resource/plantpricing": ["libs/modules/resource/plantpricing/src/index.ts"],
			"@libs/resource/preload": ["libs/modules/resource/preload/src/index.ts"],
			"@libs/resource/project": ["libs/modules/resource/project/src/index.ts"],
			"@libs/resource/requisition": ["libs/modules/resource/requisition/src/index.ts"],
			"@libs/resource/shared": ["libs/modules/resource/shared/src/index.ts"],
			"@libs/resource/skill": ["libs/modules/resource/skill/src/index.ts"],
			"@libs/resource/type": ["libs/modules/resource/type/src/index.ts"],
			"@libs/resource/wot": ["libs/modules/resource/wot/src/index.ts"],
			"@libs/sales/bid": ["libs/modules/sales/bid/src/index.ts"],
			"@libs/sales/billing": ["libs/modules/sales/billing/src/index.ts"],
			"@libs/sales/common": ["libs/modules/sales/common/src/index.ts"],
			"@libs/sales/contract": ["libs/modules/sales/contract/src/index.ts"],
			"@libs/sales/interfaces": ["libs/modules/sales/interfaces/src/index.ts"],
			"@libs/sales/preload": ["libs/modules/sales/preload/src/index.ts"],
			"@libs/sales/shared": ["libs/modules/sales/shared/src/index.ts"],
			"@libs/sales/wip": ["libs/modules/sales/wip/src/index.ts"],
			"@libs/scheduling/calendar": ["libs/modules/scheduling/calendar/src/index.ts"],
			"@libs/scheduling/interfaces": ["libs/modules/scheduling/interfaces/src/index.ts"],
			"@libs/scheduling/main": ["libs/modules/scheduling/main/src/index.ts"],
			"@libs/scheduling/preload": ["libs/modules/scheduling/preload/src/index.ts"],
			"@libs/scheduling/schedule": ["libs/modules/scheduling/schedule/src/index.ts"],
			"@libs/scheduling/shared": ["libs/modules/scheduling/shared/src/index.ts"],
			"@libs/scheduling/template": ["libs/modules/scheduling/template/src/index.ts"],
			"@libs/scheduling/templategroup": ["libs/modules/scheduling/templategroup/src/index.ts"],
			"@libs/services/interfaces": ["libs/modules/services/interfaces/src/index.ts"],
			"@libs/services/preload": ["libs/modules/services/preload/src/index.ts"],
			"@libs/services/schedulerui": ["libs/modules/services/schedulerui/src/index.ts"],
			"@libs/timekeeping/employee": ["libs/modules/timekeeping/employee/src/index.ts"],
			"@libs/timekeeping/interfaces": ["libs/modules/timekeeping/interfaces/src/index.ts"],
			"@libs/timekeeping/paymentgroup": ["libs/modules/timekeeping/paymentgroup/src/index.ts"],
			"@libs/timekeeping/period": ["libs/modules/timekeeping/period/src/index.ts"],
			"@libs/timekeeping/preload": ["libs/modules/timekeeping/preload/src/index.ts"],
			"@libs/timekeeping/recording": ["libs/modules/timekeeping/recording/src/index.ts"],
			"@libs/timekeeping/settlement": ["libs/modules/timekeeping/settlement/src/index.ts"],
			"@libs/timekeeping/shared": ["libs/modules/timekeeping/shared/src/index.ts"],
			"@libs/timekeeping/shiftmodel": ["libs/modules/timekeeping/shiftmodel/src/index.ts"],
			"@libs/timekeeping/timeallocation": ["libs/modules/timekeeping/timeallocation/src/index.ts"],
			"@libs/timekeeping/timecontrolling": ["libs/modules/timekeeping/timecontrolling/src/index.ts"],
			"@libs/timekeeping/timesymbols": ["libs/modules/timekeeping/timesymbols/src/index.ts"],
			"@libs/timekeeping/certificate": ["libs/modules/timekeeping/certificate/src/index.ts"],
			"@libs/timekeeping/worktimemodel": ["libs/modules/timekeeping/worktimemodel/src/index.ts"],
			"@libs/transportplanning/bundle": ["libs/modules/transportplanning/bundle/src/index.ts"],
			"@libs/transportplanning/package": ["libs/modules/transportplanning/package/src/index.ts"],
			"@libs/transportplanning/preload": ["libs/modules/transportplanning/preload/src/index.ts"],
			"@libs/transportplanning/requisition": ["libs/modules/transportplanning/requisition/src/index.ts"],
			"@libs/transportplanning/transport": ["libs/modules/transportplanning/transport/src/index.ts"],
			"@libs/ui/business-base": ["libs/ui/business-base/src/index.ts"],
			"@libs/ui/common": ["libs/ui/common/src/index.ts"],
			"@libs/ui/container-system": ["libs/ui/container-system/src/index.ts"],
			"@libs/ui/desktop": ["libs/ui/desktop/src/index.ts"],
			"@libs/ui/external": ["libs/ui/external/src/index.ts"],
			"@libs/ui/grid": ["libs/ui/grid/src/index.ts"],
			"@libs/ui/main-frame": ["libs/ui/main-frame/src/index.ts"],
			"@libs/ui/map": ["libs/ui/map/src/index.ts"],
			"@libs/ui/platform": ["libs/ui/platform/src/index.ts"],
			"@libs/ui/sidebar": ["libs/ui/sidebar/src/index.ts"],
			"@libs/usermanagement/group": ["libs/modules/usermanagement/group/src/index.ts"],
			"@libs/usermanagement/interfaces": ["libs/modules/usermanagement/interfaces/src/index.ts"],
			"@libs/usermanagement/preload": ["libs/modules/usermanagement/preload/src/index.ts"],
			"@libs/usermanagement/right": ["libs/modules/usermanagement/right/src/index.ts"],
			"@libs/usermanagement/user": ["libs/modules/usermanagement/user/src/index.ts"],
			"@libs/webapihelp/main": ["libs/modules/webapihelp/main/src/index.ts"],
			"@libs/webapihelp/preload": ["libs/modules/webapihelp/preload/src/index.ts"],
			"@libs/workflow/admin": ["libs/modules/workflow/admin/src/index.ts"],
			"@libs/workflow/common": ["libs/modules/workflow/common/src/index.ts"],
			"@libs/workflow/interfaces": ["libs/modules/workflow/interfaces/src/index.ts"],
			"@libs/workflow/main": ["libs/modules/workflow/main/src/index.ts"],
			"@libs/workflow/preload": ["libs/modules/workflow/preload/src/index.ts"],
			"@libs/workflow/shared": ["libs/modules/workflow/shared/src/index.ts"],
			"@libs/workflow/task": ["libs/modules/workflow/task/src/index.ts"],
			"@libs/workspace-plugin": ["tools/workspace-plugin/src/index.ts"],
			"@test/jest-shared": ["jest-shared/index.ts"],

		}
	},
	"exclude": ["node_modules", "tmp"]
}
