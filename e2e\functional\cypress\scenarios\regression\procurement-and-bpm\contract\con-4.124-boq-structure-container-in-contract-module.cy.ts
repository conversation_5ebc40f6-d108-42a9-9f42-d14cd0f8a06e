import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _procurementContractPage, _boqPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const allure = Cypress.Allure.reporter.getInterface();
const CODE_SALES_TAX_CODE = _common.generateRandomString(4);
const OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const BOQ_OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const BOQ_STRCU_DESC2 = _common.generateRandomString(4);
const BOQ_STRCU_DESC3 = _common.generateRandomString(4);
const BOQ_STRCU_DESC4 = _common.generateRandomString(4);
const BOQ_STRCU_DESC5 = _common.generateRandomString(4);
const BOQ_STRCU_DESC6 = _common.generateRandomString(4);
const BOQ_STRCU_DESC7 = _common.generateRandomString(4);

let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_PROCUREMENT_BOQ, CONTAINER_COLUMNS_BOQ_STRUCTURE;
let PROCUREMENT_CONTRACT_PARAMETER: DataCells, BOQ_STRUCTURE_PARAMETERS: DataCells;
let CONTAINERS_DATA_RECORDS, CONTAINERS_PROCUREMENT_STRUCTURE, CONTAINERS_CONFIGURATION, CONTAINERS_BOQ_STRUCTURE;

allure.epic('PROCUREMENT AND BPM');
allure.feature('Contract');
allure.story('PCM- 4.124 | Boq Structure container in Contract module');
describe('PCM- 4.124 | Boq Structure container in Contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
	before(function () {
		cy.fixture('pcm/con-4.124-boq-structure-container-in-contract-module.json').then((data) => {
			this.data = data;
			CONTAINERS_DATA_RECORDS = this.data.CONTAINERS.DATA_RECORDS
			CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
			CONTAINERS_CONFIGURATION = this.data.CONTAINERS.CONFIGURATION
			PROCUREMENT_CONTRACT_PARAMETER = {
				[commonLocators.CommonLabels.CONFIGURATION]: commonLocators.CommonKeys.SERVICE,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONFIGURATION.BUSINESS_PARTNER
			}
			CONTAINERS_PROCUREMENT_STRUCTURE = this.data.CONTAINERS.PROCUREMENT_STRUCTURE
			CONTAINER_COLUMNS_PROCUREMENT_BOQ = this.data.CONTAINER_COLUMNS.PROCUREMENT_BOQ;
			CONTAINER_COLUMNS_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.BOQ_STRUCTURE
			CONTAINERS_BOQ_STRUCTURE = this.data.CONTAINERS.BOQ_STRUCTURE
			BOQ_STRUCTURE_PARAMETERS = {
				[commonLocators.CommonLabels.TYPE]: commonLocators.CommonLabels.NEW_RECORD,
				[app.GridCells.BRIEF_INFO_SMALL]: BOQ_OUTLINE_SPECIFICATION,
			}
		}).then(() => {
			cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
			_common.openDesktopTile(tile.DesktopTiles.PROJECT);
			_common.openTab(app.TabBar.PROJECT).then(() => {
				_common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
				_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
				_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("PROJECT_NUMBER")).pinnedItem();
			});
		});
	})
	after(() => {
		cy.LOGOUT();
	});

	it('TC - Create record in Sales Tax Group from customizing', function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING)
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.MASTER_DATA).then(() => {
			_common.select_tabFromFooter(cnt.uuid.ENTITY_TYPES, app.FooterTab.DATA_TYPES, 0);
		});
		_common.clear_subContainerFilter(cnt.uuid.ENTITY_TYPES)
		_common.clear_subContainerFilter(cnt.uuid.ENTITY_TYPES)
		_common.search_inSubContainer(cnt.uuid.ENTITY_TYPES, "Sales Tax Group")
		_common.clickOn_cellHasUniqueValue(cnt.uuid.ENTITY_TYPES, app.GridCells.NAME, "Sales Tax Group")
		cy.wait(1000)//required wait
		_common.openTab(app.TabBar.MASTER_DATA).then(() => {
			_common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORD, 0);
		});
		_common.create_newRecord(cnt.uuid.DATA_RECORDS)
		_common.edit_dropdownCellWithCaret(cnt.uuid.DATA_RECORDS, app.GridCells.LEDGER_CONTEXT_FK, commonLocators.CommonKeys.LIST, CONTAINERS_DATA_RECORDS.LEDGER_CONTEXT)
		_common.edit_containerCell(cnt.uuid.DATA_RECORDS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, CODE_SALES_TAX_CODE)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.saveCellDataToEnv(cnt.uuid.DATA_RECORDS, app.GridCells.CODE, "CODE_SALES_TAX_CODE")
	});

	it('TC - Create Contract for respective project and with structure', function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.setDefaultView(app.TabBar.CONTRACT);
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT);
		});
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.STRUCTURE_CODE, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PROCUREMENT_STRUCTURE.PROCUREMENT_STRUCTURE);
		cy.SAVE();
		cy.wait(3000)
		_common.clickOn_modalFooterButton(btn.ButtonText.YES)
		cy.SAVE();
		_common.waitForLoaderToDisappear()
	});

	it('TC - Create Procurment BOQ for the respective contract', function () {
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 1);
		});
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
		_common.waitForLoaderToDisappear()
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS);
		_boqPage.enterRecord_ToCreate_procurementBoQs(CommonLocators.CommonKeys.SERVICE, OUTLINE_SPECIFICATION, commonLocators.CommonLabels.CREATE_NEW_BOQ);
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.GridCells.PACKAGE_CODE, "Package_Code");
	});

	it('TC - Create BOQ structure & verify the root item cannot be deleted', function () {
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQS, CONTAINER_COLUMNS_PROCUREMENT_BOQ);
		});
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQS, Cypress.env("Package_Code"))
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQS, Cypress.env("Package_Code"))
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINER_COLUMNS_BOQ_STRUCTURE);
		});
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
		_validate.verify_ToolbarButtonsDisabledEnabled(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, btn.IconButtons.ICO_REC_DELETE, commonLocators.CommonKeys.DISABLED)
	});

	it('TC - Verify each field and lookup is working', function () {
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINER_COLUMNS_BOQ_STRUCTURE);
		});
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQS, Cypress.env("Package_Code"))
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQS, Cypress.env("Package_Code"))
		_common.waitForLoaderToDisappear()

		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINER_COLUMNS_BOQ_STRUCTURE);
			_common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.briefinfo, CONTAINER_COLUMNS_BOQ_STRUCTURE.prcitemevaluationfk, CONTAINER_COLUMNS_BOQ_STRUCTURE.prcstructurefk, CONTAINER_COLUMNS_BOQ_STRUCTURE.mdctaxcodefk, CONTAINER_COLUMNS_BOQ_STRUCTURE.mdccontrollingunitfk, CONTAINER_COLUMNS_BOQ_STRUCTURE.exsalestaxgroupfk], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		});
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
		_common.waitForLoaderToDisappear()
		_common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		//record_1
		_boqPage.enterRecord_toCreateBoQStructure(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRUCTURE_PARAMETERS);
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		//record_2
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE);
		_common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC2);
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.QUANTITY);
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UNIT_RATE);
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BAS_UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UOM)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		//record_3
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE);
		_common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC3);
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.QUANTITY);
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UNIT_RATE);
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BAS_UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UOM)
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.PRC_ITEM_EVALUATION_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.PRC_ITEM_EVALUATION)
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.PRC_STRUCTURE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.PROCUREMENT_STRUCTURE_BOQ);
		_common.waitForLoaderToDisappear()
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.MDC_TAX_CODE_FK_SMALL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.TAX_CODE);
		_common.waitForLoaderToDisappear()
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.MDC_CONTROLLING_UNIT_FK_SMALL, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.CONTROLLING_UNIT);
		_common.waitForLoaderToDisappear()
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.EX_SALES_TAX_GROUP_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env("CODE_SALES_TAX_CODE"));
		//record_4
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE);
		_common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC7);
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
	});

	it('TC - Check item type and ANN and AGN', function () {
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
			_common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.aan, CONTAINER_COLUMNS_BOQ_STRUCTURE.agn, CONTAINER_COLUMNS_BOQ_STRUCTURE.basitemtype2fk], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		});
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		//record_1
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_OUTLINE_SPECIFICATION)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_OUTLINE_SPECIFICATION)
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.AGN, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_BOQ_STRUCTURE.AGN);
		_common.edit_dropdownCellWithCaret(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BAS_ITEM_TYPE_2_FK, commonLocators.CommonKeys.LIST, CONTAINERS_BOQ_STRUCTURE.ITEM_TYPE_BASE_ALT[0])
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		//record_2
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC2)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC2)
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.AGN, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_BOQ_STRUCTURE.AGN);
		_common.edit_dropdownCellWithCaret(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BAS_ITEM_TYPE_2_FK, commonLocators.CommonKeys.LIST, CONTAINERS_BOQ_STRUCTURE.ITEM_TYPE_BASE_ALT[1])
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		//record_3
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC3)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC3)
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.AGN, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_BOQ_STRUCTURE.AGN);
		_common.edit_dropdownCellWithCaret(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BAS_ITEM_TYPE_2_FK, commonLocators.CommonKeys.LIST, CONTAINERS_BOQ_STRUCTURE.ITEM_TYPE_BASE_ALT[1])
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_OUTLINE_SPECIFICATION)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_OUTLINE_SPECIFICATION)
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.AAN, CONTAINERS_BOQ_STRUCTURE.AAN[0]);
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC2)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC2)
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.AAN, CONTAINERS_BOQ_STRUCTURE.AAN[1]);
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC3)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC3)
		_common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.AAN, CONTAINERS_BOQ_STRUCTURE.AAN[2]);
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		_common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
	});

	it('TC - Modify quantity or unit rate or correction & assert final price as a multyplication of quantity or unit rate or correction', function () {
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
			_common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.quantity, CONTAINER_COLUMNS_BOQ_STRUCTURE.correction, CONTAINER_COLUMNS_BOQ_STRUCTURE.finalprice, CONTAINER_COLUMNS_BOQ_STRUCTURE.price], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		});
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC7)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC7)
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.QUANTITY_1);
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.CORRECTION, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.CORRECTION);
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ_STRUCTURE.QUANTITY_1, CONTAINERS_BOQ_STRUCTURE.CORRECTION, app.GridCells.FINAL_PRICE_SMALL)
		_common.waitForLoaderToDisappear()
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC7)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC7)
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.UNIT_RATE_1);
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ_STRUCTURE.QUANTITY_1, CONTAINERS_BOQ_STRUCTURE.UNIT_RATE_1, app.GridCells.FINAL_PRICE_SMALL)
		_common.waitForLoaderToDisappear()
		_common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
	});

	it('TC - Check lumpsum checkbox & assert final price=lumpsum price', function () {
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
			_common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.lumpsumprice, CONTAINER_COLUMNS_BOQ_STRUCTURE.finalprice, CONTAINER_COLUMNS_BOQ_STRUCTURE.islumpsum], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		});
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
		_common.set_cellCheckboxValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.IS_LUMP_SUM, commonLocators.CommonKeys.CHECK)
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.LUMP_SUM_PRICE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.LUMP_SUM_PRICE);
		cy.SAVE()
		_common.assert_activeRow_cellDataByContent_inContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.FINAL_PRICE_SMALL, CONTAINERS_BOQ_STRUCTURE.LUMP_SUM_PRICE)
	});

	it('TC - Verify if boq line type =note, then this record will be read only', function () {
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
			_common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.boqlinetypefk, CONTAINER_COLUMNS_BOQ_STRUCTURE.quantity, CONTAINER_COLUMNS_BOQ_STRUCTURE.finalprice], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		});
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		_common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_OUTLINE_SPECIFICATION)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_OUTLINE_SPECIFICATION)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BOQ_LINE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.ITEM_TYPE_STD_OPT)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_validate.verify_inputFieldVisibility(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.QUANTITY_SMALL, commonLocators.CommonKeys.NOT_VISIBLE, app.InputFields.INPUT_GROUP_CONTENT)
	});

	it('TC - Verify each button is working', function () {
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
			_common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.briefinfo], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		});
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
		_common.clickOn_toolbarButton(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, btn.ToolBar.ICO_REC_NEW)
		_common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC4)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
		_common.clickOn_toolbarButton(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, btn.ToolBar.ICO_FLD_INS_BELOW)
		_common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC5)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ_STRUCTURE.ROOT)
		_common.clickOn_toolbarButton(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, btn.IconButtons.ICO_SUB_FLD_NEW)
		_common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, BOQ_STRCU_DESC6)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC4)
		_validate.verify_isRecordPresent(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC4)
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC5)
		_validate.verify_isRecordPresent(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC5)
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC6)
		_validate.verify_isRecordPresent(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC6)
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC4)
		_common.delete_recordFromContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
		_common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE);
		});
		_common.waitForLoaderToDisappear()
		_validate.verify_recordNotPresentInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, BOQ_STRCU_DESC4)
		_common.waitForLoaderToDisappear()
	});
});