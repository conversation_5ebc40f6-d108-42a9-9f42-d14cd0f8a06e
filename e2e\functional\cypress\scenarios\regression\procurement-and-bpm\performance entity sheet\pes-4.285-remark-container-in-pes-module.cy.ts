import { sidebar, commonLocators, app, cnt, btn, tile } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _logesticPage, _procurementContractPage, _projectPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
const CONTRACT_DESC = _common.generateRandomString(5)
const PES_DESCRIPTION = _common.generateRandomString(5)
const REMARK = _common.generateRandomString(5)
const ITEM1_DESC = _common.generateRandomString(5)

let PROJECTS_PARAMETERS
let CONTAINERS_CONTRACT
let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let ITEM_PARAMETER1: DataCells
let CONTAINERS_ITEM

describe("PCM- 4.285 | Remark container in pes module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  before(function () {
    cy.fixture('pcm/pes-4.285-remark-container-in-pes-module.json')
      .then((data) => {
        this.data = data;
        CONTAINERS_ITEM = this.data.CONTAINERS.ITEM
        CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
        PROCUREMENT_CONTRACT_PARAMETER = {
          [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
          [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER
        }
        ITEM_PARAMETER1 = {
          [app.GridCells.DESCRIPTION_1]: ITEM1_DESC,
          [app.GridCells.PRC_STRUCTURE_FK]: CONTAINERS_ITEM.PROCUREMENT_STRUCTURE,
          [app.GridCells.QUANTITY_SMALL]: CONTAINERS_ITEM.QUANTITY,
          [app.GridCells.BAS_UOM_FK]: CONTAINERS_ITEM.UOM,
        };
      });
    cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
  });

  after(() => {
    cy.LOGOUT();
  });

  it('TC - Add new contract and Items', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT)
    _common.openTab(app.TabBar.CONTRACT).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTACTS, 0);
          _common.waitForLoaderToDisappear()
      _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
    });
    _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
    cy.wait(1000) //required wait to create contract to clear configuration type
    _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
    cy.SAVE();
    _common.waitForLoaderToDisappear()
    _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.DESCRIPTION,app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTRACT_DESC)
    _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_REQ_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.CLERK);
    _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.CLERK);
    _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.PRJ_NO);
    _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_CONTRACT.CONTROLLING_UNIT);
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.CODE,"CONTRACT_CODE")
    _common.openTab(app.TabBar.CONTRACT).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS)
       })
    _common.maximizeContainer(cnt.uuid.ITEMSCONTRACT)
    _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
    _procurementContractPage.enterRecord_toCreateContractItems(cnt.uuid.ITEMSCONTRACT, ITEM_PARAMETER1)
    cy.SAVE()
    cy.wait(1000) //wait required to save contract items parameter
    _common.minimizeContainer(cnt.uuid.ITEMSCONTRACT)
   
  });

  it("TC- Create pes from contract", function () {
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,CONTRACT_DESC)

    _common.openTab(app.TabBar.CONTRACT).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
    })
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS)
    _common.changeStatus_fromModal(commonLocators.CommonKeys.PART_DELIVERED)
    cy.SAVE()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES)
    _common.waitForLoaderToDisappear()
    _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_PES)
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.setDefaultView(app.TabBar.PERFORMANCEENTRYSHEET)
      _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_6)
      _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
    });
    _common.clear_subContainerFilter(cnt.uuid.HEADERS);
    _common.select_rowInContainer(cnt.uuid.HEADERS)
    _common.saveCellDataToEnv(cnt.uuid.HEADERS,app.GridCells.CODE,"PES_CODE")

  })
  it("TC - Create remark in pes module", function () {
    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PES_REMARK, app.FooterTab.REMARK, 1);
    });
    _common.waitForLoaderToDisappear()
    _common.clearOrType_inPlainTextArea_inContainer(cnt.uuid.PES_REMARK,CommonLocators.CommonElements.TEXTAREA_REMARK,REMARK)
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env("PES_CODE"))
    _common.waitForLoaderToDisappear()
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PES_REMARK, app.FooterTab.REMARK, 1);
    });
    _validate.validate_Text_In_Container_TextareaRemark(cnt.uuid.PES_REMARK,CommonLocators.CommonElements.TEXTAREA_REMARK,REMARK)

  })

})