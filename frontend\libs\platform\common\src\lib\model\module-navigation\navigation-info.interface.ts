/*
 * Copyright(c) RIB Software GmbH
 */
import { IPinningContext } from '../../interfaces/pinning-context.interface';
import { IIdentificationData } from '../identification-data.interface';
import { IInitializationContext } from '../initialization';
import { ModuleName } from '../../constant/modules';

/**
 * Interface for moduleNavigation payload
 */
export interface INavigationInfo {

	/**
	 * The module in which to navigate
	 */
	internalModuleName: ModuleName;

	/**
	 * Entity identification for the target module
	 */
	entityIdentifications: IIdentificationData[] | ((initializationContext: IInitializationContext) => IIdentificationData[]);

	/**
	 *  event<PERSON><PERSON><PERSON> called when navigation happened
	 */
	onNavigationDone?: (navigationInfo: INavigationInfo) => void;

	/**
	 * Pinning context to be set after navigation is done.
	 */
	getPinningContext?: () => IPinningContext[] | undefined;
}