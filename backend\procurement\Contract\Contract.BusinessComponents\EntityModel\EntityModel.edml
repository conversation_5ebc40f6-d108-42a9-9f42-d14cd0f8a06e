﻿<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="BusinessComponents.Store" Alias="Self" Provider="System.Data.SqlClient" ProviderManifestToken="2012" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:devart="http://devart.com/schemas/edml/StorageSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityContainer Name="DbContextStoreContainer">
          <EntitySet Name="CON_HEADERs" EntityType="BusinessComponents.Store.CON_HEADER" store:Type="Tables" Table="CON_HEADER" />
          <EntitySet Name="CON_STATUS" EntityType="BusinessComponents.Store.CON_STATUS" store:Type="Tables" Table="CON_STATUS" />
          <EntitySet Name="CON_TYPEs" EntityType="BusinessComponents.Store.CON_TYPE" store:Type="Tables" Table="CON_TYPE" />
          <EntitySet Name="CON_TOTALs" EntityType="BusinessComponents.Store.CON_TOTAL" store:Type="Tables" Table="CON_TOTAL" />
          <EntitySet Name="CON_HEADER_LOOKUP_Vs" EntityType="BusinessComponents.Store.CON_HEADER_LOOKUP_V" store:Type="Views" Table="CON_HEADER_LOOKUP_V" />
          <EntitySet Name="BAS_TRANSLATIONs" EntityType="BusinessComponents.Store.BAS_TRANSLATION" store:Type="Tables" Table="BAS_TRANSLATION" />
          <EntitySet Name="CON_HEADER2PRJ_MATERIAL_Vs" EntityType="BusinessComponents.Store.CON_HEADER2PRJ_MATERIAL_V" store:Type="Views" Table="CON_HEADER2PRJ_MATERIAL_V" />
          <EntitySet Name="CON_STATUS2EXTERNALs" EntityType="BusinessComponents.Store.CON_STATUS2EXTERNAL" store:Type="Tables" Table="CON_STATUS2EXTERNAL" />
          <EntitySet Name="CON_HEADERUSERACCESS_Vs" EntityType="BusinessComponents.Store.CON_HEADERUSERACCESS_V" store:Type="Views" Table="CON_HEADERUSERACCESS_V" />
          <EntitySet Name="CON_HEADER2CUSTOMERs" EntityType="BusinessComponents.Store.CON_HEADER2CUSTOMER" store:Type="Tables" Table="CON_HEADER2CUSTOMER" />
          <EntitySet Name="BAS_DDTEMPIDS" EntityType="BusinessComponents.Store.BAS_DDTEMPIDS" store:Type="Tables" Table="BAS_DDTEMPIDS" />
          <EntitySet Name="CON_ACCOUNT_ASSIGNMENTs" EntityType="BusinessComponents.Store.CON_ACCOUNT_ASSIGNMENT" store:Type="Tables" Table="CON_ACCOUNT_ASSIGNMENT" />
          <EntitySet Name="CON_HEADER2MDL_OBJECT_Vs" EntityType="BusinessComponents.Store.CON_HEADER2MDL_OBJECT_V" store:Type="Views" Table="CON_HEADER2MDL_OBJECT_V" />
          <EntitySet Name="CON_CREWs" EntityType="BusinessComponents.Store.CON_CREW" store:Type="Tables" Table="CON_CREW" />
          <EntitySet Name="CON_MASTERRESTRICTIONs" EntityType="BusinessComponents.Store.CON_MASTERRESTRICTION" store:Type="Tables" Table="CON_MASTERRESTRICTION" />
          <EntitySet Name="PRC_COPYMODEs" EntityType="BusinessComponents.Store.PRC_COPYMODE" store:Type="Tables" Table="PRC_COPYMODE" />
          <EntitySet Name="CON_ADVANCEs" EntityType="BusinessComponents.Store.CON_ADVANCE" store:Type="Tables" Table="CON_ADVANCE" />
          <EntitySet Name="CON_TRANSACTIONs" EntityType="BusinessComponents.Store.CON_TRANSACTION" store:Type="Tables" Table="CON_TRANSACTION" />
          <EntitySet Name="CON_HEADERAPPROVALs" EntityType="BusinessComponents.Store.CON_HEADERAPPROVAL" store:Type="Tables" Table="CON_HEADERAPPROVAL" />
          <EntitySet Name="CON_HEADER_ITWOFINANCE_Vs" EntityType="BusinessComponents.Store.CON_HEADER_ITWOFINANCE_V" store:Type="Views" Table="CON_HEADER_ITWOFINANCE_V" />
          <EntitySet Name="CON_HEADER2BOQ_WIC_CAT_BOQs" EntityType="BusinessComponents.Store.CON_HEADER2BOQ_WIC_CAT_BOQ" store:Type="Tables" Table="CON_HEADER2BOQ_WIC_CAT_BOQ" />
          <EntitySet Name="CON_HEADEREXT_Vs" EntityType="BusinessComponents.Store.CON_HEADEREXT_V" store:Type="Views" Table="CON_HEADEREXT_V" />
          <EntitySet Name="PRC_CONTRACTUSERACCESSCACHEs" EntityType="BusinessComponents.Store.PRC_CONTRACTUSERACCESSCACHE" store:Type="Tables" Table="PRC_CONTRACTUSERACCESSCACHE" />
          <AssociationSet Name="CON_STATUS_FK05" Association="BusinessComponents.Store.CON_STATUS_FK05">
            <End Role="CON_STATUS" EntitySet="CON_STATUS" />
            <End Role="CON_STATUS2EXTERNAL" EntitySet="CON_STATUS2EXTERNALs" />
          </AssociationSet>
          <AssociationSet Name="CON_HEADER_FK17" Association="BusinessComponents.Store.CON_HEADER_FK17">
            <End Role="CON_HEADER" EntitySet="CON_HEADERs" />
            <End Role="CON_HEADER2CUSTOMER" EntitySet="CON_HEADER2CUSTOMERs" />
          </AssociationSet>
          <AssociationSet Name="CON_HEADER_FK30" Association="BusinessComponents.Store.CON_HEADER_FK30">
            <End Role="CON_HEADER" EntitySet="CON_HEADERs" />
            <End Role="CON_HEADERAPPROVAL" EntitySet="CON_HEADERAPPROVALs" />
          </AssociationSet>
          <AssociationSet Name="CON_HEADER_FK35" Association="BusinessComponents.Store.CON_HEADER_FK35">
            <End Role="CON_HEADER" EntitySet="CON_HEADERs" />
            <End Role="CON_MASTERRESTRICTION" EntitySet="CON_MASTERRESTRICTIONs" />
          </AssociationSet>
          <AssociationSet Name="CON_HEADER_FK21" Association="BusinessComponents.Store.CON_HEADER_FK21">
            <End Role="CON_HEADER" EntitySet="CON_HEADERs" />
            <End Role="CON_MASTERRESTRICTION" EntitySet="CON_MASTERRESTRICTIONs" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="CON_HEADER">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CON_STATUS_FK" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_FK" Type="int" Nullable="false" />
          <Property Name="PRJ_PROJECT_FK" Type="int" />
          <Property Name="PRC_PACKAGE_FK" Type="int" />
          <Property Name="MDC_TAX_CODE_FK" Type="int" Nullable="false" />
          <Property Name="BAS_CLERK_PRC_FK" Type="int" />
          <Property Name="BAS_CLERK_REQ_FK" Type="int" />
          <Property Name="BAS_CURRENCY_FK" Type="int" Nullable="false" />
          <Property Name="EXCHANGERATE" Type="numeric" Nullable="false" Precision="10" Scale="5" />
          <Property Name="PRJ_CHANGE_FK" Type="int" />
          <Property Name="CON_HEADER_FK" Type="int" />
          <Property Name="HASCHANGES" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="MDC_MATERIAL_CATALOG_FK" Type="int" />
          <Property Name="PRC_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="BAS_PAYMENT_TERM_FI_FK" Type="int" />
          <Property Name="BAS_PAYMENT_TERM_PA_FK" Type="int" />
          <Property Name="CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="SEARCH_PATTERN" Type="nvarchar" MaxLength="450" />
          <Property Name="DATE_ORDERED" Type="date" Nullable="false" />
          <Property Name="DATE_REPORTED" Type="date" />
          <Property Name="DATE_CANCELED" Type="date" />
          <Property Name="DATE_DELIVERY" Type="date" />
          <Property Name="DATE_CALLOFFFROM" Type="date" />
          <Property Name="DATE_CALLOFFTO" Type="date" />
          <Property Name="CON_TYPE_FK" Type="int" Nullable="false" />
          <Property Name="PRC_AWARDMETHOD_FK" Type="int" Nullable="false" />
          <Property Name="PRC_CONTRACTTYPE_FK" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGUNIT_FK" Type="int" />
          <Property Name="BPD_BUSINESSPARTNER_FK" Type="int" Nullable="false" />
          <Property Name="BPD_SUBSIDIARY_FK" Type="int" />
          <Property Name="BPD_SUPPLIER_FK" Type="int" />
          <Property Name="BPD_CONTACT_FK" Type="int" />
          <Property Name="BPD_BUSINESSPARTNER2_FK" Type="int" />
          <Property Name="BPD_SUBSIDIARY2_FK" Type="int" />
          <Property Name="BPD_SUPPLIER2_FK" Type="int" />
          <Property Name="BPD_CONTACT2_FK" Type="int" />
          <Property Name="PRC_INCOTERM_FK" Type="int" />
          <Property Name="BAS_COMPANY_INVOICE_FK" Type="int" />
          <Property Name="BAS_ADDRESS_FK" Type="int" />
          <Property Name="CODE_QUOTATION" Type="nvarchar" MaxLength="20" />
          <Property Name="BPD_BUSINESSPARTNER_AGENT_FK" Type="int" />
          <Property Name="PRC_PACKAGE2HEADER_FK" Type="int" />
          <Property Name="DATE_QUOTATION" Type="date" />
          <Property Name="REMARK" Type="nvarchar" MaxLength="2000" />
          <Property Name="USERDEFINED1" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED2" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED3" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED4" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED5" Type="nvarchar" MaxLength="252" />
          <Property Name="MDC_BILLING_SCHEMA_FK" Type="int" Nullable="false" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="CONFIRMATION_CODE" Type="nvarchar" MaxLength="252" />
          <Property Name="CONFIRMATION_DATE" Type="date" />
          <Property Name="EXTERNAL_CODE" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_PAYMENT_TERM_AD_FK" Type="int" />
          <Property Name="PRC_COPYMODE_FK" Type="int" Nullable="false" DefaultValue="1" />
          <Property Name="DATE_PENALTY" Type="date" />
          <Property Name="PENALTY_PERCENTPERDAY" Type="numeric" Nullable="false" Precision="9" Scale="3" />
          <Property Name="PENALTY_PERCENTMAX" Type="numeric" Nullable="false" Precision="9" Scale="3" />
          <Property Name="PENALTY_COMMENT" Type="nvarchar" MaxLength="255" />
          <Property Name="DATE_EFFECTIVE" Type="date" Nullable="false" />
          <Property Name="BPD_VATGROUP_FK" Type="int" />
          <Property Name="PROVING_PERIOD" Type="int" />
          <Property Name="PROVING_DEALDLINE" Type="int" />
          <Property Name="APPROVAL_PERIOD" Type="int" />
          <Property Name="APPROVAL_DEALDLINE" Type="int" />
          <Property Name="ISFREEITEMSALLOWED" Type="bit" Nullable="false" devart:DefaultValue="1" />
          <Property Name="MDC_PRICE_LIST_FK" Type="int" />
          <Property Name="BPD_BANK_FK" Type="int" />
          <Property Name="QTN_HEADER_FK" Type="int" />
          <Property Name="REQ_HEADER_FK" Type="int" />
          <Property Name="EXECUTION_START" Type="date" />
          <Property Name="EXECUTION_END" Type="date" />
          <Property Name="BAS_ACCASSIGN_BUSINESS_FK" Type="int" />
          <Property Name="BAS_ACCASSIGN_CONTROL_FK" Type="int" />
          <Property Name="BAS_ACCASSIGN_ACCOUNT_FK" Type="int" />
          <Property Name="BAS_ACCASSIGN_CON_TYPE_FK" Type="int" />
          <Property Name="ORD_HEADER_FK" Type="int" />
          <Property Name="OVERALL_DISCOUNT" Type="numeric" Nullable="false" Precision="19" Scale="7" DefaultValue="0" />
          <Property Name="OVERALL_DISCOUNT_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" DefaultValue="0" />
          <Property Name="OVERALL_DISCOUNT_PERCENT" Type="numeric" Nullable="false" Precision="9" Scale="3" DefaultValue="0" />
          <Property Name="BAS_SALES_TAX_METHOD_FK" Type="int" Nullable="false" DefaultValue="1" />
          <Property Name="VALIDFROM" Type="datetime" />
          <Property Name="VALIDTO" Type="datetime" />
          <Property Name="BOQ_WIC_CAT_FK" Type="int" />
          <Property Name="BOQ_WIC_CAT_BOQ_FK" Type="int" />
          <Property Name="BASELINE_UPDATE" Type="datetime" />
          <Property Name="ISFRAMEWORK" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISNOTACCRUAL_PRR" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="BAS_LANGUAGE_FK" Type="int" />
        </EntityType>
        <EntityType Name="CON_STATUS">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="DESCRIPTION_TR" Type="int" />
          <Property Name="SORTING" Type="int" Nullable="false" />
          <Property Name="ISDEFAULT" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ICON" Type="int" Nullable="false" />
          <Property Name="ISREADONLY" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISCANCELED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISREPORTED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISVIRTUAL" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISDELIVERED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISPARTDELIVERED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISINVOICED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISORDERED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="ISCHANGSENT" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISCHANGEACCEPTED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISCHANGEREJECTED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISOPTIONAL_UPWARDS" Type="bit" Nullable="false" />
          <Property Name="ISOPTIONAL_DOWNWARDS" Type="bit" Nullable="false" />
          <Property Name="ISACCEPTED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISPARTACCEPTED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISREJECTED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISLIVE" Type="bit" Nullable="false" devart:DefaultValue="1" />
          <Property Name="FRM_ACCESSRIGHTDESCRIPTOR_FK" Type="int" />
          <Property Name="CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="ISUPDATEIMPORT" Type="bit" Nullable="false" />
          <Property Name="ISPESCO" Type="bit" Nullable="false" devart:DefaultValue="0" />
        </EntityType>
        <EntityType Name="CON_TYPE">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="DESCRIPTION_TR" Type="int" />
          <Property Name="SORTING" Type="int" Nullable="false" />
          <Property Name="ISDEFAULT" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="ISLIVE" Type="bit" Nullable="false" devart:DefaultValue="1" />
        </EntityType>
        <EntityType Name="CON_TOTAL">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="PRC_TOTALTYPE_FK" Type="int" Nullable="false" />
          <Property Name="VALUE_NET" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="VALUE_NET_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="VALUE_TAX" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="VALUE_TAX_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="COMMENT_TEXT" Type="nvarchar" MaxLength="255" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CON_HEADER_LOOKUP_V">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="MDC_CONTROLLINGUNIT_FK" Type="int" />
          <Property Name="PRC_PACKAGE_FK" Type="int" />
          <Property Name="PRC_PACKAGE2HEADER_FK" Type="int" />
          <Property Name="PRC_STRUCTURE_FK" Type="int" />
          <Property Name="DATE_ORDERED" Type="date" Nullable="false" />
          <Property Name="BPD_BUSINESSPARTNER2_FK" Type="int" />
          <Property Name="CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="PRC_CONFIGURATION_FK" Type="int" Nullable="false" />
          <Property Name="BAS_PAYMENT_TERM_FI_FK" Type="int" />
          <Property Name="EXTERNAL_CODE" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_PAYMENT_TERM_PA_FK" Type="int" />
          <Property Name="MDC_TAX_CODE_FK" Type="int" Nullable="false" />
          <Property Name="BAS_CLERK_PRC_FK" Type="int" />
          <Property Name="BAS_CLERK_REQ_FK" Type="int" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="BP_NAME1" Type="nvarchar" MaxLength="252" />
          <Property Name="PRC_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="ISVIRTUAL" Type="bit" />
          <Property Name="ISREPORTED" Type="bit" />
          <Property Name="SEARCH_PATTERN" Type="nvarchar" MaxLength="450" />
          <Property Name="BAS_CURRENCY_FK" Type="int" Nullable="false" />
          <Property Name="MDC_BILLING_SCHEMA_FK" Type="int" Nullable="false" />
          <Property Name="PRJ_CHANGE_FK" Type="int" />
          <Property Name="EXCHANGERATE" Type="numeric" Nullable="false" Precision="10" Scale="5" />
          <Property Name="BPD_CONTACT_FK" Type="int" />
          <Property Name="BPD_SUBSIDIARY_FK" Type="int" />
          <Property Name="BPD_SUPPLIER_FK" Type="int" />
          <Property Name="BPD_VATGROUP_FK" Type="int" />
          <Property Name="ISLIVE" Type="bit" />
          <Property Name="ISCANCELED" Type="bit" />
          <Property Name="ISDELIVERED" Type="bit" />
          <Property Name="ISREADONLY" Type="bit" />
          <Property Name="ISINVOICED" Type="bit" />
          <Property Name="ISORDERED" Type="bit" />
          <Property Name="STATUSDESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="CONSTATUSFK" Type="int" />
          <Property Name="ISREJECTED" Type="bit" />
          <Property Name="PRC_CONFIGHEADER_FK" Type="int" Nullable="false" />
          <Property Name="STATUSDESCRIPTIONTR" Type="int" />
          <Property Name="ICON" Type="int" />
          <Property Name="BP_NAME2" Type="nvarchar" MaxLength="252" />
          <Property Name="SUPPLIER_CODE" Type="nvarchar" MaxLength="252" />
          <Property Name="PROJECTNO" Type="nvarchar" MaxLength="16" />
          <Property Name="PROJECT_NAME" Type="nvarchar" MaxLength="252" />
          <Property Name="BP2_NAME1" Type="nvarchar" MaxLength="252" />
          <Property Name="BP2_NAME2" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_COMPANY_FK" Type="int" Nullable="false" />
          <Property Name="SUPPLIER2_CODE" Type="nvarchar" MaxLength="252" />
          <Property Name="BPD_BUSINESSPARTNER_FK" Type="int" Nullable="false" />
          <Property Name="PRJ_PROJECT_FK" Type="int" />
          <Property Name="CODE_QUOTATION" Type="nvarchar" MaxLength="20" />
          <Property Name="CON_HEADER_FK" Type="int" />
          <Property Name="PRC_COPYMODE_FK" Type="int" Nullable="false" />
          <Property Name="ISFREEITEMSALLOWED" Type="bit" Nullable="false" />
          <Property Name="BAS_SALES_TAX_METHOD_FK" Type="int" Nullable="false" />
          <Property Name="ISFRAMEWORK" Type="bit" Nullable="false" />
          <Property Name="BPD_BANK_FK" Type="int" />
          <Property Name="MDC_MATERIAL_CATALOG_FK" Type="int" />
          <Property Name="BOQ_WIC_CAT_FK" Type="int" />
          <Property Name="BAS_LANGUAGE_FK" Type="int" />
        </EntityType>
        <EntityType Name="BAS_TRANSLATION">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="BAS_LANGUAGE_FK" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="BAS_LANGUAGE_FK" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="2000" />
        </EntityType>
        <EntityType Name="CON_HEADER2PRJ_MATERIAL_V">
          <Key>
            <PropertyRef Name="CON_HEADER_FK" />
            <PropertyRef Name="PRJ_PROJECT_FK" />
            <PropertyRef Name="BAS_COMPANY_FK" />
          </Key>
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="PRJ_PROJECT_FK" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_FK" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CON_STATUS2EXTERNAL">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CON_STATUS_FK" Type="int" Nullable="false" />
          <Property Name="BAS_EXTERNALSOURCE_FK" Type="int" Nullable="false" />
          <Property Name="EXT_CODE" Type="nvarchar" Nullable="false" MaxLength="252" />
          <Property Name="EXT_DESCRIPTION" Type="nvarchar" MaxLength="255" />
          <Property Name="COMMENT_TEXT" Type="nvarchar" MaxLength="255" />
          <Property Name="SORTING" Type="int" Nullable="false" />
          <Property Name="ISDEFAULT" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="ISLIVE" Type="bit" Nullable="false" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CON_HEADERUSERACCESS_V">
          <Key>
            <PropertyRef Name="USERID" />
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="USERID" Type="int" Nullable="false" />
          <Property Name="ID" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CON_HEADER2CUSTOMER">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="BPD_BUSINESSPARTNER_FK" Type="int" Nullable="false" />
          <Property Name="BPD_SUBSIDIARY_FK" Type="int" />
          <Property Name="BPD_CUSTOMER_FK" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="BAS_DDTEMPIDS">
          <Key>
            <PropertyRef Name="REQUESTID" />
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="REQUESTID" Type="char" Nullable="false" MaxLength="32" />
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="KEY1" Type="int" />
          <Property Name="KEY2" Type="int" />
          <Property Name="KEY3" Type="int" />
        </EntityType>
        <EntityType Name="CON_ACCOUNT_ASSIGNMENT">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="ITEMNO" Type="int" Nullable="false" />
          <Property Name="BREAKDOWN_PERCENT" Type="numeric" Nullable="false" Precision="10" Scale="2" />
          <Property Name="BREAKDOWN_AMOUNT" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="BAS_COMPANY_YEAR_FK" Type="int" />
          <Property Name="MDC_CONTROLLINGUNIT_FK" Type="int" />
          <Property Name="PSD_SCHEDULE_FK" Type="int" />
          <Property Name="PSD_ACTIVITY_FK" Type="int" />
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="REMARK" Type="nvarchar" MaxLength="2000" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="CON_CREW_FK" Type="int" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="QUANTITY" Type="numeric" Precision="19" Scale="6" />
          <Property Name="BAS_ACCOUNT_FK" Type="int" />
          <Property Name="ISDELETE" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="BREAKDOWN_AMOUNT_OC" Type="numeric" Nullable="false" Precision="19" Scale="6" DefaultValue="0" />
          <Property Name="BAS_UOM_FK" Type="int" />
          <Property Name="DATE_DELIVERY" Type="datetime" />
          <Property Name="ACCOUNT_ASSIGNMENT01" Type="nvarchar" MaxLength="252" />
          <Property Name="ACCOUNT_ASSIGNMENT02" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_ACCASSIGN_ITEMTYPE_FK" Type="int" />
          <Property Name="BAS_ACCASSIGN_MAT_GROUP_FK" Type="int" />
          <Property Name="ISFINALINVOICE" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="BAS_ACCASSIGN_ACC_TYPE_FK" Type="int" />
          <Property Name="ACCOUNT_ASSIGNMENT03" Type="nvarchar" MaxLength="252" />
          <Property Name="BAS_ACCASSIGN_FACTORY_FK" Type="int" />
        </EntityType>
        <EntityType Name="CON_HEADER2MDL_OBJECT_V">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="MDL_MODEL_FK" />
            <PropertyRef Name="MDL_OBJECT_FK" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="MDL_MODEL_FK" Type="int" Nullable="false" />
          <Property Name="MDL_OBJECT_FK" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CON_CREW">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="DESCRIPTION_TR" Type="int" />
          <Property Name="BPD_CONTACT_FK" Type="int" />
          <Property Name="SORTING" Type="int" Nullable="false" />
          <Property Name="ISDEFAULT" Type="bit" Nullable="false" />
          <Property Name="ISLIVE" Type="bit" Nullable="false" />
          <Property Name="USERDEFINED1" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED2" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED3" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED4" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED5" Type="nvarchar" MaxLength="252" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CON_MASTERRESTRICTION">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="MDC_MATERIAL_CATALOG_FK" Type="int" />
          <Property Name="BOQ_WIC_CAT_FK" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="BOQ_HEADER_FK" Type="int" />
          <Property Name="CON_HEADER_BOQ_FK" Type="int" />
          <Property Name="VISIBILITY" Type="int" Nullable="false" DefaultValue="1" />
          <Property Name="PRC_PACKAGE_FK" Type="int" />
          <Property Name="PRJ_PROJECT_FK" Type="int" />
          <Property Name="PRJ_BOQ_FK" Type="int" />
          <Property Name="COPY_TYPE" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="PRC_COPYMODE">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="DESCRIPTION_TR" Type="int" />
          <Property Name="SORTING" Type="int" Nullable="false" />
          <Property Name="ISDEFAULT" Type="bit" Nullable="false" />
          <Property Name="ISLIVE" Type="bit" Nullable="false" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CON_ADVANCE">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="PRC_ADVANCETYPE_FK" Type="int" Nullable="false" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="DATE_DUE" Type="date" Nullable="false" />
          <Property Name="AMOUNT_DUE" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="PERCENT_PRORATA" Type="numeric" Nullable="false" Precision="9" Scale="3" />
          <Property Name="DATE_DONE" Type="date" />
          <Property Name="AMOUNT_DONE" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="COMMENT_TEXT" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED1" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED2" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED3" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED4" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED5" Type="nvarchar" MaxLength="252" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="AMOUNT_DUE_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="AMOUNT_DONE_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="BAS_PAYMENT_TERM_FK" Type="int" />
        </EntityType>
        <EntityType Name="CON_TRANSACTION">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="TRANSACTION_ID" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_FK" Type="int" Nullable="false" />
          <Property Name="BAS_COMPANY_INVOICE_FK" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="PRC_CONFIGURATION_FK" Type="int" Nullable="false" />
          <Property Name="ISCONSOLIDATED" Type="bit" Nullable="false" />
          <Property Name="ISCHANGE" Type="bit" Nullable="false" />
          <Property Name="CURRENCY" Type="nvarchar" Nullable="false" MaxLength="3" />
          <Property Name="CON_STATUS_FK" Type="int" Nullable="false" />
          <Property Name="CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="DATE_ORDERED" Type="date" Nullable="false" />
          <Property Name="DATE_EFFECTIVE" Type="date" Nullable="false" />
          <Property Name="DATE_REPORTED" Type="date" />
          <Property Name="BPD_BUSINESSPARTNER_FK" Type="int" Nullable="false" />
          <Property Name="BPD_SUBSIDIARY_FK" Type="int" />
          <Property Name="BPD_SUPPLIER_FK" Type="int" />
          <Property Name="BPD_CONTACT_FK" Type="int" />
          <Property Name="BPD_BANK_FK" Type="int" />
          <Property Name="BPD_VATGROUP_FK" Type="int" Nullable="false" />
          <Property Name="BAS_PAYMENT_TERM_FI_FK" Type="int" />
          <Property Name="BAS_PAYMENT_TERM_PA_FK" Type="int" />
          <Property Name="BAS_PAYMENT_TERM_AD_FK" Type="int" />
          <Property Name="INCOTERM" Type="nvarchar" MaxLength="252" />
          <Property Name="DATE_DELIVERY" Type="date" />
          <Property Name="BAS_ADDRESS_FK" Type="int" />
          <Property Name="ITEM_REFERENCE" Type="int" Nullable="false" />
          <Property Name="AMOUNT" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="VAT_AMOUNT" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="AMOUNT_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="VAT_AMOUNT_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="INC_AMOUNT" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="INC_VAT_AMOUNT" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="INC_AMOUNT_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="INC_VAT_AMOUNT_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="INC_QUANTITY" Type="numeric" Nullable="false" Precision="19" Scale="6" />
          <Property Name="QUANTITY" Type="numeric" Nullable="false" Precision="19" Scale="6" />
          <Property Name="PRICE" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="VAT_PRICE" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="PRICE_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="VAT_PRICE_OC" Type="numeric" Nullable="false" Precision="19" Scale="7" />
          <Property Name="NOMINAL_ACCOUNT" Type="nvarchar" MaxLength="16" />
          <Property Name="NOMINAL_DIMENSION1" Type="nvarchar" MaxLength="32" />
          <Property Name="NOMINAL_DIMENSION2" Type="nvarchar" MaxLength="32" />
          <Property Name="NOMINAL_DIMENSION3" Type="nvarchar" MaxLength="32" />
          <Property Name="MDC_TAX_CODE_FK" Type="int" Nullable="false" />
          <Property Name="MDC_TAX_CODE_MATRIX_FK" Type="int" />
          <Property Name="VATPERCENT" Type="numeric" Nullable="false" Precision="9" Scale="3" />
          <Property Name="VAT_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="MDC_CONTROLLINGUNIT_FK" Type="int" />
          <Property Name="CONTROLLINGUNIT_CODE" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN01" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN01DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="CONTROLLINGUNIT_ASSIGN02" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN02DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="CONTROLLINGUNIT_ASSIGN03" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN03DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="CONTROLLINGUNIT_ASSIGN04" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN04DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="CONTROLLINGUNIT_ASSIGN05" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN05DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="CONTROLLINGUNIT_ASSIGN06" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN06DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="CONTROLLINGUNIT_ASSIGN07" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN07DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="CONTROLLINGUNIT_ASSIGN08" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN08DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="CONTROLLINGUNIT_ASSIGN09" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN09DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="CONTROLLINGUNIT_ASSIGN10" Type="nvarchar" MaxLength="32" />
          <Property Name="CONTROLLINGUNIT_ASSIGN10DESC" Type="nvarchar" MaxLength="252" />
          <Property Name="PRC_ITEM_FK" Type="bigint" />
          <Property Name="MDC_MATERIAL_FK" Type="int" />
          <Property Name="ITEM_DESCRIPTION1" Type="nvarchar" MaxLength="252" />
          <Property Name="ITEM_DESCRIPTION2" Type="nvarchar" MaxLength="252" />
          <Property Name="ITEM_SPECIFICATION" Type="nvarchar" MaxLength="2000" />
          <Property Name="ITEM_UOMQUANTITY" Type="nvarchar" MaxLength="16" />
          <Property Name="ISSUCCESS" Type="bit" Nullable="false" />
          <Property Name="HANDOVER_ID" Type="int" />
          <Property Name="ORDERNO" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED1" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED2" Type="nvarchar" MaxLength="252" />
          <Property Name="USERDEFINED3" Type="nvarchar" MaxLength="252" />
          <Property Name="RETURN_VALUE" Type="nvarchar" MaxLength="2000" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="INCOTERM_CODE" Type="nvarchar" MaxLength="16" />
        </EntityType>
        <EntityType Name="CON_HEADERAPPROVAL">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="ISAPPROVED" Type="bit" Nullable="false" devart:DefaultValue="0" />
          <Property Name="COMMENT" Type="nvarchar" MaxLength="255" />
          <Property Name="BAS_CLERK_FK" Type="int" Nullable="false" />
          <Property Name="BAS_CLERK_ROLE_FK" Type="int" />
          <Property Name="DUEDATE" Type="date" />
          <Property Name="EVALUATEDON" Type="date" />
          <Property Name="EVALUATIONLEVEL" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CON_HEADER_ITWOFINANCE_V">
          <Key>
            <PropertyRef Name="CON_HEADER_ID" />
            <PropertyRef Name="CON_HEADER_CODE" />
            <PropertyRef Name="CON_HEADER_DATE_ORDERED" />
          </Key>
          <Property Name="CON_HEADER_ID" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_CON_HEADER_FK" Type="int" />
          <Property Name="CON_HEADER_CODE" Type="nvarchar" Nullable="false" MaxLength="16" />
          <Property Name="CON_HEADER_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="CON_HEADER_DATE_ORDERED" Type="date" Nullable="false" />
          <Property Name="CON_HEADER_MDC_TAX_CODE_FK" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_BPD_VATGROUP_FK" Type="int" />
          <Property Name="CON_TOTAL_VALUE_NET" Type="numeric" Precision="19" Scale="7" />
          <Property Name="CON_TOTAL_VALUE_GROSS" Type="numeric" Precision="20" Scale="7" />
          <Property Name="CON_STATUS_CODE" Type="nvarchar" MaxLength="16" />
          <Property Name="CON_STATUS_DESCRIPTION" Type="nvarchar" MaxLength="252" />
          <Property Name="PRJ_PROJECT_PROJECTNO" Type="nvarchar" MaxLength="16" />
          <Property Name="BPD_SUPPLIER_CODE" Type="nvarchar" MaxLength="252" />
          <Property Name="BPD_VATGROUP_REFERENCE" Type="nvarchar" MaxLength="20" />
        </EntityType>
        <EntityType Name="CON_HEADER2BOQ_WIC_CAT_BOQ">
          <Key>
            <PropertyRef Name="ID" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="CON_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="BOQ_HEADER_FK" Type="int" Nullable="false" />
          <Property Name="BOQ_WIC_CAT_BOQ_FK" Type="int" Nullable="false" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="CON_HEADEREXT_V">
          <Key>
            <PropertyRef Name="ID" />
            <PropertyRef Name="BAS_RUBRIC_CATEGORY_FK" />
            <PropertyRef Name="VATPERCENT" />
            <PropertyRef Name="INSERTED" />
            <PropertyRef Name="WHOISR" />
            <PropertyRef Name="VERSION" />
          </Key>
          <Property Name="ID" Type="int" Nullable="false" />
          <Property Name="BAS_RUBRIC_CATEGORY_FK" Type="int" Nullable="false" />
          <Property Name="VATPERCENT" Type="numeric" Nullable="false" Precision="9" Scale="3" />
          <Property Name="BASELINE_PATH" Type="nvarchar" MaxLength="255" />
          <Property Name="PRJ_STATUS_FK" Type="int" />
          <Property Name="IS_STATUS_READONLY" Type="bit" Nullable="false" />
          <Property Name="CON_HEADERMAT_FK" Type="int" />
          <Property Name="CON_HEADERBOQCAT_FK" Type="int" />
          <Property Name="PRC_PACKAGEMAXBOQ_FK" Type="int" />
          <Property Name="POSTED_CON_HEADER_FK" Type="int" />
          <Property Name="ACCASSIGN_CON_TYPE_FK" Type="int" />
          <Property Name="INSERTED" Type="datetime" Nullable="false" />
          <Property Name="WHOISR" Type="int" Nullable="false" />
          <Property Name="UPDATED" Type="datetime" />
          <Property Name="WHOUPD" Type="int" />
          <Property Name="VERSION" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="PRC_CONTRACTUSERACCESSCACHE">
          <Key>
            <PropertyRef Name="USERID" />
            <PropertyRef Name="COMPANYSIGNEDINID" />
            <PropertyRef Name="ACCESSROLEID" />
            <PropertyRef Name="FROMID" />
            <PropertyRef Name="TOID" />
          </Key>
          <Property Name="USERID" Type="int" Nullable="false" />
          <Property Name="COMPANYSIGNEDINID" Type="int" Nullable="false" />
          <Property Name="ACCESSROLEID" Type="int" Nullable="false" />
          <Property Name="FROMID" Type="int" Nullable="false" />
          <Property Name="TOID" Type="int" Nullable="false" />
        </EntityType>
        <Association Name="CON_STATUS_FK05">
          <End Role="CON_STATUS" Type="BusinessComponents.Store.CON_STATUS" Multiplicity="1" />
          <End Role="CON_STATUS2EXTERNAL" Type="BusinessComponents.Store.CON_STATUS2EXTERNAL" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CON_STATUS">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="CON_STATUS2EXTERNAL">
              <PropertyRef Name="CON_STATUS_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="CON_HEADER_FK17">
          <End Role="CON_HEADER" Type="BusinessComponents.Store.CON_HEADER" Multiplicity="1" />
          <End Role="CON_HEADER2CUSTOMER" Type="BusinessComponents.Store.CON_HEADER2CUSTOMER" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CON_HEADER">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="CON_HEADER2CUSTOMER">
              <PropertyRef Name="CON_HEADER_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="CON_HEADER_FK30">
          <End Role="CON_HEADER" Type="BusinessComponents.Store.CON_HEADER" Multiplicity="1" />
          <End Role="CON_HEADERAPPROVAL" Type="BusinessComponents.Store.CON_HEADERAPPROVAL" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CON_HEADER">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="CON_HEADERAPPROVAL">
              <PropertyRef Name="CON_HEADER_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="CON_HEADER_FK35">
          <End Role="CON_HEADER" Type="BusinessComponents.Store.CON_HEADER" Multiplicity="0..1" />
          <End Role="CON_MASTERRESTRICTION" Type="BusinessComponents.Store.CON_MASTERRESTRICTION" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CON_HEADER">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="CON_MASTERRESTRICTION">
              <PropertyRef Name="CON_HEADER_BOQ_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="CON_HEADER_FK21">
          <End Role="CON_HEADER" Type="BusinessComponents.Store.CON_HEADER" Multiplicity="1" />
          <End Role="CON_MASTERRESTRICTION" Type="BusinessComponents.Store.CON_MASTERRESTRICTION" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="CON_HEADER">
              <PropertyRef Name="ID" />
            </Principal>
            <Dependent Role="CON_MASTERRESTRICTION">
              <PropertyRef Name="CON_HEADER_FK" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="RIB.Visual.Procurement.Contract.BusinessComponents" Alias="Self" d4p1:ViewGeneration="true" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:devart="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns:ed="http://devart.com/schemas/EntityDeveloper/1.0" annotation:UseStrongSpatialTypes="false" xmlns:d4p1="http://devart.com/schemas/edml/ConceptualSchemaExtensions/1.0" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityContainer Name="ModelBuilder" ed:Namespace="RIB.Visual.Procurement.Contract.BusinessComponents" annotation:LazyLoadingEnabled="false" ed:Guid="2c8b7c01-1421-4d2d-b86b-3d5c5b084643">
          <EntitySet Name="ConHeaderEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderEntity" />
          <EntitySet Name="ConStatusEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConStatusEntity" />
          <EntitySet Name="ConTypeEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConTypeEntity" />
          <EntitySet Name="ConTotalEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConTotalEntity" />
          <EntitySet Name="ConHeaderLookupVEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderLookupVEntity" />
          <EntitySet Name="TranslationEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.TranslationEntity" />
          <EntitySet Name="ConHeader2prjMaterialVEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeader2prjMaterialVEntity" />
          <EntitySet Name="ConStatus2externalEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConStatus2externalEntity" />
          <EntitySet Name="ConHeaderUserAccessVEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderUserAccessVEntity" />
          <EntitySet Name="ConHeader2customerEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeader2customerEntity" />
          <EntitySet Name="DdTempIdsEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.DdTempIdsEntity" />
          <EntitySet Name="ConAccountAssignmentEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConAccountAssignmentEntity" />
          <EntitySet Name="ConHeader2MdlObjectVEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeader2MdlObjectVEntity" />
          <EntitySet Name="ConCrewEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConCrewEntity" />
          <EntitySet Name="ConMasterRestrictionEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConMasterRestrictionEntity" />
          <EntitySet Name="PrcCopyModeEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.PrcCopyModeEntity" />
          <EntitySet Name="ConAdvanceEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConAdvanceEntity" />
          <EntitySet Name="ConTransactionEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConTransactionEntity" />
          <EntitySet Name="ConHeaderApprovalEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderApprovalEntity" />
          <EntitySet Name="ConHeaderItwoFinanceVEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderItwoFinanceVEntity" />
          <EntitySet Name="ConHeader2BoqWicCatBoqEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeader2BoqWicCatBoqEntity" />
          <EntitySet Name="ConHeaderExtendedVEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderExtendedVEntity" />
          <EntitySet Name="ContractUserAccessCacheEntities" EntityType="RIB.Visual.Procurement.Contract.BusinessComponents.ContractUserAccessCacheEntity" />
          <AssociationSet Name="CON_STATUS_FK05Set" Association="RIB.Visual.Procurement.Contract.BusinessComponents.CON_STATUS_FK05">
            <End Role="ConStatusEntity" EntitySet="ConStatusEntities" />
            <End Role="ConStatus2externalEntities" EntitySet="ConStatus2externalEntities" />
          </AssociationSet>
          <AssociationSet Name="CON_HEADER_FK17Set" Association="RIB.Visual.Procurement.Contract.BusinessComponents.CON_HEADER_FK17">
            <End Role="ConHeaderEntity" EntitySet="ConHeaderEntities" />
            <End Role="ConHeader2customerEntities" EntitySet="ConHeader2customerEntities" />
          </AssociationSet>
          <AssociationSet Name="ConHeaderEntity_DdTempIdsEntitySet" Association="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderEntity_DdTempIdsEntity">
            <End Role="ConHeaderEntity" EntitySet="ConHeaderEntities" />
            <End Role="DdTempIdsEntities" EntitySet="DdTempIdsEntities" />
          </AssociationSet>
          <AssociationSet Name="CON_HEADER_FK30Set" Association="RIB.Visual.Procurement.Contract.BusinessComponents.CON_HEADER_FK30">
            <End Role="HeaderEntity" EntitySet="ConHeaderEntities" />
            <End Role="ConHeaderapprovalEntities" EntitySet="ConHeaderApprovalEntities" />
          </AssociationSet>
          <AssociationSet Name="CON_HEADER_FK35Set" Association="RIB.Visual.Procurement.Contract.BusinessComponents.CON_HEADER_FK35">
            <End Role="ConHeaderEntity" EntitySet="ConHeaderEntities" />
            <End Role="ConMasterRestrictionEntities" EntitySet="ConMasterRestrictionEntities" />
          </AssociationSet>
          <AssociationSet Name="CON_HEADER_FK21Set" Association="RIB.Visual.Procurement.Contract.BusinessComponents.CON_HEADER_FK21">
            <End Role="ConHeaderEntity1" EntitySet="ConHeaderEntities" />
            <End Role="ConMasterRestrictionEntities1" EntitySet="ConMasterRestrictionEntities" />
          </AssociationSet>
        </EntityContainer>
        <EntityType Name="ConHeaderEntity" ed:Guid="8b1a4bbd-1602-4b0c-8ea8-c2f9468c6118" ed:SupportsGrouping="True">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="556db4e3-f2df-4558-8f56-829bbeb4ac79">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ConStatusFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b94ba73f-1e6e-41b5-927b-d6ad6d23bc21">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CompanyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="5374c49a-0a79-409f-9429-7ecdbf4576c2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProjectFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="c3686c84-b26f-4a5b-87b2-918f119e2409">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PackageFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4c8e1123-3dd3-43cd-b1e3-d5b7dde3ded4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="TaxCodeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="046ca37c-5382-4ee0-9eed-10e68cd9a72b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ClerkPrcFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e9549f75-18c3-41e3-ac69-20c6d7630b18">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ClerkReqFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="9c9b805e-48d9-401a-af80-49ba92f5b32c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasCurrencyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4bce131f-93c2-48b7-8a5b-12c29ff55425">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ExchangeRate" Type="Decimal" Nullable="false" Precision="10" Scale="5" ed:ValidateRequired="true" ed:Guid="928a8e0d-0159-4d08-ad36-0d6f9f2f99e0">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">exchangerate</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProjectChangeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="fe0f26be-a54f-4176-871e-75f2df9cccf4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ConHeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b464fefa-9148-440b-9fe2-94d66ea4812d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="HasChanges" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="86a98ffa-d75a-4f25-8fcf-9b5a33ffb3f2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MaterialCatalogFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="d416220f-ac30-429d-ac0b-c3c251839abc">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PrcHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="620331b4-fb7f-41bb-b285-af9a7ce45caf">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PaymentTermFiFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b48e79d1-baea-4f11-aa61-a5e40b00e259">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PaymentTermPaFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="0b10aac6-e739-431b-9af4-0cf7a2388c81">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Code" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="9890f971-88d0-4838-b540-7743f05464cf">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">code</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="d2907d8e-d5ed-4722-a806-96220f15b2ee">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SearchPattern" Type="String" MaxLength="450" Unicode="true" ed:ValidateMaxLength="450" ed:ValidateRequired="false" ed:Guid="1e3cfeff-477d-432c-891a-0b5f2eae09b0" />
          <Property Name="DateOrdered" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="43567ad1-02e9-47e6-a3ca-2c28e17a02a3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateReported" Type="DateTime" ed:ValidateRequired="false" ed:Guid="71133388-eed2-46d2-bbbc-be7ba7b75085">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateCanceled" Type="DateTime" ed:ValidateRequired="false" ed:Guid="a3a593c5-0ac9-447d-ae33-441477e71f5b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateDelivery" Type="DateTime" ed:ValidateRequired="false" ed:Guid="65bb5a28-193f-4593-be20-76adcc5f1598">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateCallofffrom" Type="DateTime" ed:ValidateRequired="false" ed:Guid="52d98ad0-4ffb-4832-bd39-5df6daa3c470">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateCalloffto" Type="DateTime" ed:ValidateRequired="false" ed:Guid="b1a89d35-0dc3-4b06-8138-4b0f22c36806">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ConTypeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="1f1bccd7-3877-411b-965e-5ed5bad48201">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AwardmethodFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="54f5b666-5716-4baf-b2f9-8a439c77c300">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ContracttypeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="16786b8f-2c15-4eb8-9c76-3f73388e610a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="528f414b-a066-4aca-ab14-e677bc32819f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BusinessPartnerFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9fc3b32b-77f4-4044-9f64-8381ea7ea7a2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SubsidiaryFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e129980a-2c56-4543-9847-f409f013b8c8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SupplierFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="431856a7-0c13-4674-832a-765b19a7ab84">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ContactFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="c8965524-0b7e-4f6b-8411-64b6a61cbfd0">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BusinessPartner2Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="a380a048-f5ad-422f-b25b-773e20078947">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Subsidiary2Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="eeb29194-5227-4f0e-ba0c-c142614f5c28">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Supplier2Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="634d970e-017c-4e6c-9e82-dd9c22dcdf6f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Contact2Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="7419b107-1c68-4d1b-9051-135f5420bce4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IncotermFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="a1521bd3-da2c-45d8-9b3c-0cfd14ce1aa8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CompanyInvoiceFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="36ea99a4-8439-4209-a6f5-adbed89c13fb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AddressFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="459bca63-07a6-420b-a48b-e12ba35b9450">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CodeQuotation" Type="String" MaxLength="20" Unicode="true" ed:ValidateMaxLength="20" ed:ValidateRequired="false" ed:Guid="771f0351-186d-4b3e-a1ee-3c7253d72226">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BusinessPartnerAgentFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="26c1e85b-cf5c-460f-8f2a-04661f9fc579">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Package2HeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4d7f554f-ee9a-4150-ae02-9afd20a2eaea" />
          <Property Name="DateQuotation" Type="DateTime" ed:ValidateRequired="false" ed:Guid="f9f48237-2404-4016-881c-a74e3a3c2aaf">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Remark" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="1b116217-1597-41a8-9bfb-d3bbd1407f35">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">remark</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="a55f10c6-03d7-49e8-a151-8bd33e670506">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="d3fa0577-fb99-4aea-b2e0-9e6a1c3521a6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined3" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="10a9f895-8704-49dd-b325-b9fa9573683a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined4" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="5a018112-b9d5-4e9e-8b64-9dd60d8490c6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined5" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="6703ad6e-7dc4-4833-8370-536ba681c566">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BillingSchemaFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="8ead4112-e01c-4cba-9f1e-b9b64666ed0e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="4940b3d7-e835-419c-8b9f-67fb6b72398f" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="f28e5b31-b995-4e11-9def-46c7900bb0e2" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="2347f2d6-9175-4026-8edb-c39a8066c7a2" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="9982225c-3cce-4973-9b83-818c25b71a73" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="88de54f4-0f45-4ff0-8ef6-ab64c556af94" />
          <Property Name="ConfirmationCode" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="c14a05df-8077-450d-874d-5d0329619911">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ConfirmationDate" Type="DateTime" ed:ValidateRequired="false" ed:Guid="6a2e0bcf-e668-44e3-8fb1-cb03b577ee31">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ExternalCode" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="08a08c05-221b-46a2-9822-b62a81a997f8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PaymentTermAdFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="dc437490-4165-4c39-99d0-22bc853107a8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PrcCopyModeFk" Type="Int32" Nullable="false" DefaultValue="1" ed:ValidateRequired="true" ed:Guid="15b20b00-189f-4eb6-b184-a2ab25e8f3e5">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DatePenalty" Type="DateTime" ed:ValidateRequired="false" ed:Guid="9a527c30-00ff-4798-8170-d3f6bb4ab9d4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PenaltyPercentPerDay" Type="Decimal" Nullable="false" Precision="9" Scale="3" ed:ValidateRequired="true" ed:Guid="a81d510d-888c-4f1e-8bc5-463dda04f817">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PenaltyPercentMax" Type="Decimal" Nullable="false" Precision="9" Scale="3" ed:ValidateRequired="true" ed:Guid="dbe55cc3-76c0-4748-b1e6-3bb9d7b9df6e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PenaltyComment" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="931f8b34-a36a-4894-8e92-3653dac73a15">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateEffective" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="3c42fa17-611c-4586-a47a-9b61df9ad856">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BpdVatGroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="1bf068b2-1d38-4b99-9736-48d7946dd60a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProvingPeriod" Type="Int32" ed:ValidateRequired="false" ed:Guid="8a6aaeb0-e7ac-412a-82cf-1aeb7c3ceadd">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProvingDealdline" Type="Int32" ed:ValidateRequired="false" ed:Guid="76929f78-360b-4c6f-8711-70ceb173167b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ApprovalPeriod" Type="Int32" ed:ValidateRequired="false" ed:Guid="1372b60f-f50b-4a1d-8891-d65578308b65">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ApprovalDealdline" Type="Int32" ed:ValidateRequired="false" ed:Guid="f8126ea3-0ded-488e-b63a-9a5f78670847">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsFreeItemsAllowed" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="62d55d49-3339-4554-954d-7ce8c50c8699">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MdcPriceListFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="d89eb724-a5f4-414a-8790-97c8b2b77990">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BankFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="77f3e502-0e73-4264-8c13-918834ca3263">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="QtnHeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b2f64f8d-52f5-4a6b-9554-a2c6fdd61010">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ReqHeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="50f41f8a-c836-4322-8ea5-df1e010d409b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ExecutionStart" Type="DateTime" ed:ValidateRequired="false" ed:Guid="aaef38ea-cf56-416b-8b8f-3c8f12f7466d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ExecutionEnd" Type="DateTime" ed:ValidateRequired="false" ed:Guid="22efb60b-79b4-4bd0-b2ee-c935938dfd46">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasAccassignBusinessFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="eae7af3d-a3f2-44d0-8fcb-81d8fd6133e0">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasAccassignControlFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="c613697c-cb25-494c-9871-39baa8777cc0">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasAccassignAccountFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="fe4c8ad3-d739-4042-9369-0c8c3bcd43fc">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasAccassignConTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b52a3241-98c5-49e2-9a67-598cb27a29d3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="OrdHeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="a3a6a7c9-eec9-41b2-bd71-7ecc891385d4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="OverallDiscount" Type="Decimal" Nullable="false" DefaultValue="0" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="f8f53634-8c60-444f-9d76-f201abc8c87d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">percent</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="OverallDiscountOc" Type="Decimal" Nullable="false" DefaultValue="0" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="4a3261dc-7182-42d2-b016-be19ab0d56be">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">percent</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="OverallDiscountPercent" Type="Decimal" Nullable="false" DefaultValue="0" Precision="9" Scale="3" ed:ValidateRequired="true" ed:Guid="223ec7b7-7ea1-4449-a769-a0f9dc0e1545">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">percent</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SalesTaxMethodFk" Type="Int32" Nullable="false" DefaultValue="1" ed:ValidateRequired="true" ed:Guid="c60bc0be-1169-444e-8e0b-d1d3c59312e5">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ValidFrom" Type="DateTime" ed:ValidateRequired="false" ed:Guid="fa677089-dd5b-4218-aa03-70280b19d4db">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ValidTo" Type="DateTime" ed:ValidateRequired="false" ed:Guid="1a8ffbdf-7dfc-4775-93a6-759ed97992c5">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BoqWicCatFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="cca98f1e-15a5-4588-94c8-f6101fcbd15d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BoqWicCatBoqFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="eff8e285-7724-45e3-bc95-9889c7c86aad">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BaselineUpdate" Type="DateTime" ed:ValidateRequired="false" ed:Guid="6478cdfb-038c-4ae4-a323-c3f824bb258a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">datetime</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsFramework" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="5c96434a-764e-4a1d-b5b7-94386cd284e1">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsNotAccrualPrr" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="458b364e-a28a-41ac-80fb-88088ee77183">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasLanguageFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="7b2286d5-4e84-4f9f-9a1d-453cafcd7388">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <NavigationProperty Name="ConHeader2customerEntities" Relationship="RIB.Visual.Procurement.Contract.BusinessComponents.CON_HEADER_FK17" FromRole="ConHeaderEntity" ToRole="ConHeader2customerEntities" ed:Guid="8d2fadc8-a59e-49a3-ba18-7dddefe955fc" />
          <NavigationProperty Name="DdTempIdsEntities" Relationship="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderEntity_DdTempIdsEntity" FromRole="ConHeaderEntity" ToRole="DdTempIdsEntities" ed:Guid="313bfd36-6a52-4ba6-ad78-7b7bcc45ba0f" />
          <NavigationProperty Name="ConHeaderapprovalEntities" Relationship="RIB.Visual.Procurement.Contract.BusinessComponents.CON_HEADER_FK30" FromRole="HeaderEntity" ToRole="ConHeaderapprovalEntities" ed:Guid="a2ae3a19-55b1-42d8-85d2-aacbe9dc3d3f" />
        </EntityType>
        <EntityType Name="ConStatusEntity" ed:Guid="c6d703e0-cf1c-432a-b3f2-fa0affdb5221">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="1de47b2a-4ab7-4914-91ab-3441a13667b5" />
          <Property Name="DescriptionInfo" Type="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType" Nullable="false" ed:ValidateRequired="false" ed:Guid="38847619-3b8a-46fa-88f9-48d49ae64ded" />
          <Property Name="Sorting" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="99849b52-9d4f-4ffd-ba7d-da95aafcaac8" />
          <Property Name="IsDefault" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="cb905d39-c927-462a-81d8-688f6e23b9a4" />
          <Property Name="Icon" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="a719a1ce-5c11-4421-b5e9-9e600ac2ab4b" />
          <Property Name="IsReadonly" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="c08d209d-1ee4-4e6f-a1b5-eca0f0e32dec" />
          <Property Name="Iscanceled" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="49f73257-af70-43e6-8f06-ed2c315be334" />
          <Property Name="IsReported" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="78fd0a52-5edf-418f-bd3f-b3f718bbf40e" />
          <Property Name="IsVirtual" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="ba73d7a6-2410-4716-a945-2f21f97fa42e" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="4732391e-2eaa-4ae3-8a82-6d11286441ab" />
          <Property Name="IsDelivered" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="f97fc57a-c6cd-463e-8e95-84c225aeb16e" />
          <Property Name="IsPartDelivered" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="a7714266-0d82-4ab1-b85d-ae6646a6ad36" />
          <Property Name="IsInvoiced" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="a6f430f2-7f9b-4114-a3e6-e5f726f985bd" />
          <Property Name="IsOrdered" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="17ed13d1-71c0-4072-bd13-8bb68adad59e" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4e67e0fc-7ffb-4eb1-b34b-2fd9b9ea37cc" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="6541a5de-1836-4618-91e1-b814f8499e6e" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="aab5d945-1405-4b2c-a926-4b4c865e404e" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0a615b55-7d15-4527-8269-0a39fcc45c71" />
          <Property Name="IsOptionalUpwards" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="d7cff9c6-43de-430e-a6a1-cbf490dd0eb1" />
          <Property Name="IsChangSent" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="25ac5520-0baa-498e-b206-ebd896568896" />
          <Property Name="IsChangeAccepted" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="472f228c-5754-4cc3-8ff2-1b27293ea4f9" />
          <Property Name="IsChangeRejected" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="b003b39d-da36-48b7-92be-ef2241b5afa8" />
          <Property Name="IsOptionalDownwards" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="4d2cf7cd-86f3-424d-99b4-fba9dec7b878" />
          <Property Name="IsAccepted" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="980e1d13-03ac-4f6a-ab89-200bde9fea6d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsPartAccepted" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="6f251693-c4dc-4b0f-92c1-3d4da02bf4ea">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsRejected" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="55d33e5a-d73d-4819-a030-8cfe15432df9">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsLive" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="89e634fd-c3b0-4365-a7dd-e9eca07c5144" />
          <Property Name="AccessRightDescriptorFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="9eda2645-c18f-49b2-95eb-4626f9e245b4" />
          <Property Name="Code" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="f071f84b-5e02-43ef-aae5-01177b35271c" />
          <Property Name="IsUpdateImport" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="0aabb935-b93d-474b-b449-6a88f328a018" />
          <Property Name="IsPesCo" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="c0384ac9-51c2-4736-8b23-282a8b8dba6e" />
          <NavigationProperty Name="ConStatus2externalEntities" Relationship="RIB.Visual.Procurement.Contract.BusinessComponents.CON_STATUS_FK05" FromRole="ConStatusEntity" ToRole="ConStatus2externalEntities" ed:Guid="ea3a1b51-a964-45f8-8b7c-20c2d4d9f622" />
        </EntityType>
        <EntityType Name="ConTypeEntity" ed:Guid="561c8994-fe67-455f-b912-68e44737ceee">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="2fe08e15-2d09-492d-9cf6-787f466f3218" />
          <Property Name="DescriptionInfo" Type="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType" Nullable="false" ed:ValidateRequired="false" ed:Guid="650efcc5-6ab9-4169-84f8-efb8d3457679" />
          <Property Name="Sorting" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="2645091a-fdc0-4d83-bcaf-5ae994e7f251" />
          <Property Name="IsDefault" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="b56dad24-238d-49cd-a0de-6e1279199ef6" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="a3e87356-7be9-4665-a6da-fafeafb1c3a8" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4555eeb5-d820-4146-94e2-a0f737f4323d" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="185be3ff-b0c2-49e0-bb30-8ee968558423" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="0381d6d5-b18e-4078-a29f-2d6bd39d254a" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="3d9f1322-e00c-485f-b90b-88c65c3b541b" />
          <Property Name="IsLive" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="c4572063-8168-43d3-b3a7-b8b04ee8296c" />
        </EntityType>
        <EntityType Name="ConTotalEntity" ed:Guid="9336e779-b861-4d2d-8d94-11543dbbc726">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="5097cf20-6677-4a41-ba0c-8584920b206b" />
          <Property Name="HeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0267f8df-d118-4dc5-a667-7569d234fca0" />
          <Property Name="TotalTypeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="dc1c9888-4b23-4988-a77c-c2828f18f7eb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ValueNet" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="1c46d87a-f0d3-4ffd-9255-0c65bf3f54b3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ValueNetOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="f81477ab-4f6d-480e-8544-3686ac2570cf">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ValueTax" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="ac793c96-8b64-4a3a-aadb-aa14c0e1fad1">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ValueTaxOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="b47c734d-dbf7-4dae-87c1-2df8dedcd10e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CommentText" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="2f662c3b-4bdb-4738-9fba-01260f471d27">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">comment</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="19830d22-f0ac-442f-9d0d-c8c6b950b41f" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c7a5499d-ed80-41a3-9317-926511400e1e" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="ab339996-0284-419f-bad6-beb9270b5a10" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="f4b2c3d6-1977-4ca3-9363-c73562433a76" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b4c3bcf7-1fc9-42de-a4d9-327b328e067d" />
        </EntityType>
        <EntityType Name="ConHeaderLookupVEntity" ed:Guid="60be70ea-9a12-45cc-bd1e-83c3e39811a5">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="94672483-d74b-46a4-9786-5886ca988456" />
          <Property Name="ControllingUnitFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="a5ce3e34-23c5-470f-9787-e8b210b31140" />
          <Property Name="PrcPackageFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="2f9c5fda-a69a-4f60-afde-97ee3d4e132f" />
          <Property Name="PrcPackage2HeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="ad7a632c-e19d-4fa1-8059-3476f1421abf" />
          <Property Name="PrcStructureFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="44d46f0d-37ad-433f-af78-7f3218817711" />
          <Property Name="DateOrdered" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="8d9b2fd7-dec6-40d2-a02f-f717cc6822ec" />
          <Property Name="BusinessPartner2Fk" Type="Int32" ed:ValidateRequired="false" ed:Guid="2cdf2449-9beb-4cc6-93a9-a660311b001e" />
          <Property Name="Code" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="6fbe183a-cc38-4e43-a3b1-dcd7f92d53db" />
          <Property Name="PrcConfigurationFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0589fd9b-3849-48e3-94a5-5633919891b2" />
          <Property Name="PaymentTermFiFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="2f52bb5d-0be7-4b1d-93a0-03ee1a57ebfa" />
          <Property Name="ExternalCode" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="ab825ea3-c87d-485a-85ab-59eaf0223172" />
          <Property Name="PaymentTermPaFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="6b2e1797-fda9-428b-bc54-79a13b63def7" />
          <Property Name="TaxCodeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="080c9718-61a6-4421-a328-abeb9c46a04f" />
          <Property Name="ClerkPrcFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b29b44c8-0725-4131-99b6-3db7b8f8fda8" />
          <Property Name="ClerkReqFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="7dcdb8e0-04c7-4711-a366-8dbf3c7eb68a" />
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="d605627c-1cde-41ac-beb6-819e1e27b374" />
          <Property Name="BpName1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="c7b342c4-03dc-4d65-b1cb-5255e7c9d686" />
          <Property Name="PrcHeaderId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="41946a55-f5d2-4e1c-b387-47277be35418" />
          <Property Name="StatusIsVirtual" Type="Boolean" ed:ValidateRequired="false" ed:Guid="7f2268a9-4741-490a-ae56-83db2f735292" />
          <Property Name="StatusIsReported" Type="Boolean" ed:ValidateRequired="false" ed:Guid="c31d35e2-a468-4e30-b825-2e076cd920b3" />
          <Property Name="SearchPattern" Type="String" MaxLength="450" Unicode="true" ed:ValidateMaxLength="450" ed:ValidateRequired="false" ed:Guid="daec2f2d-184f-48f6-bb44-0cbe492729b0" />
          <Property Name="CurrencyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="441425a8-a16c-4214-aa34-058082840ea5" />
          <Property Name="MdcBillingSchemaFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0a0bf568-9258-408b-9531-909425842b72" />
          <Property Name="ProjectChangeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="6645ea7b-a912-4855-8513-aee3a29365d5" />
          <Property Name="Exchangerate" Type="Decimal" Nullable="false" Precision="10" Scale="5" ed:ValidateRequired="true" ed:Guid="54afe758-c044-42f0-b403-d349bba77ba0" />
          <Property Name="BpdContactFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="28f10180-9574-4315-8f3b-a10e0cecde49" />
          <Property Name="BpdSubsidiaryFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="5ac8ef17-ba91-436f-9394-26425172f289" />
          <Property Name="BpdSupplierFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="ccda2331-9d5b-4f83-af53-d89c09df3fd7" />
          <Property Name="BpdVatGroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e9052f0d-719a-412f-aa99-d1768376b774" />
          <Property Name="StatusIsLive" Type="Boolean" ed:ValidateRequired="false" ed:Guid="b33b3dc0-e26a-488d-8ad5-4d35cf10650f" />
          <Property Name="StatusIsCanceled" Type="Boolean" ed:ValidateRequired="false" ed:Guid="1d8282c0-e7f3-4df9-b271-386094add823" />
          <Property Name="StatusIsDelivered" Type="Boolean" ed:ValidateRequired="false" ed:Guid="84f89af3-cd5c-4afa-abae-0fcd2b0efa2c" />
          <Property Name="StatusIsReadonly" Type="Boolean" ed:ValidateRequired="false" ed:Guid="7c5a5065-257c-443d-95d2-81302c9665c8" />
          <Property Name="StatusIsInvoiced" Type="Boolean" ed:ValidateRequired="false" ed:Guid="9b0b8412-95eb-4c19-b172-1d687ae9b0db" />
          <Property Name="StatusIsOrdered" Type="Boolean" ed:ValidateRequired="false" ed:Guid="e16103ea-15c0-41a6-b795-dd9a731bea3a" />
          <Property Name="ConStatusFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="bc3bf31f-e034-4a4f-bc89-ef177c1f3d1d" />
          <Property Name="PrcConfigHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="05413ab8-41e6-43ac-99d5-32fc8f907e2f" />
          <Property Name="StatusIsRejected" Type="Boolean" ed:ValidateRequired="false" ed:Guid="0b911141-0f70-4d63-9095-24858fc52adb" />
          <Property Name="StatusDescriptionInfo" Type="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType" Nullable="false" ed:ValidateRequired="false" ed:Guid="dff58b6f-aba6-44c5-8518-4f7fdd678128" />
          <Property Name="Icon" Type="Int32" ed:ValidateRequired="false" ed:Guid="df084385-d48d-47a9-8720-785e9196a452" />
          <Property Name="BpName2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="c5689214-9624-4383-9fd3-40d642c1c0fe" />
          <Property Name="SupplierCode" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="a1162c1f-d4a9-4d40-96dd-f397e8026d6c" />
          <Property Name="ProjectNo" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="241f216c-f766-48e7-804d-dba297f46e34" />
          <Property Name="ProjectName" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="e12c2779-54da-4436-83ae-425ca660d529" />
          <Property Name="CompanyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="78c470d2-05d3-4e6b-9d50-73a129a719e5" />
          <Property Name="Bp2Name1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="f95cea41-6c8c-4274-ba62-a8b3144c015d" />
          <Property Name="Bp2Name2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="f874e734-8b38-451e-a314-80bd4e6e6a93" />
          <Property Name="BusinessPartnerFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="bdda0eda-9fef-4752-af14-dd0d0b5a1cba" />
          <Property Name="Supplier2Code" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="8f995a36-5f8a-47c6-bee3-c97dcd6cdd1a" />
          <Property Name="ProjectFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="42df21ad-b09e-4ff5-bdb4-4e9db3ad9fea" />
          <Property Name="CodeQuotation" Type="String" MaxLength="20" Unicode="true" ed:ValidateMaxLength="20" ed:ValidateRequired="false" ed:Guid="ffdf4ecf-6167-4433-acbb-198492419835" />
          <Property Name="ConHeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="795c31cc-1b23-433b-ae11-3300687479ea" />
          <Property Name="PrcCopyModeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="8a3c30b2-34b1-47b5-b2a7-1946440e4dfe" />
          <Property Name="IsFreeItemsAllowed" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="ce635f42-3f88-48e8-86e7-ca00107fa6e9" />
          <Property Name="SalesTaxMethodFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="6d405c6d-227d-47f1-b9b8-645d5bf1958e" />
          <Property Name="IsFramework" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="dd651c7f-8589-4a93-9377-2e6c7e476ac0" />
          <Property Name="BankFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="5fbd9c46-5df7-4eb6-937d-053d65c7109f" />
          <Property Name="MdcMaterialCatalogFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4bbaf7cd-1cb4-43d6-83d1-132ad135dafb" />
          <Property Name="BoqWicCatFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="13e4b290-37d5-411a-b5d1-9234407d083a" />
          <Property Name="BasLanguageFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="e0326ea5-6eba-417a-8092-00af5b747abb" />
        </EntityType>
        <EntityType Name="TranslationEntity" ed:Guid="d9272c42-4858-489e-992e-99b1c63ff1cf" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="BasLanguageFk" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="eb244c6a-bdc7-42e8-8b94-874f7295d217" />
          <Property Name="BasLanguageFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="36fa2e13-00e2-4e45-914b-e5b60ec4cbe5" />
          <Property Name="Description" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="b496e1b3-a485-4404-aa51-40ed4fa7e87c" />
        </EntityType>
        <EntityType Name="ConHeader2prjMaterialVEntity" ed:Guid="84466227-7ae3-4c76-8b2e-c57df606b26f">
          <Key>
            <PropertyRef Name="ConHeaderFk" />
            <PropertyRef Name="PrjProjectFk" />
            <PropertyRef Name="BasCompanyFk" />
          </Key>
          <Property Name="ConHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="480b6ed4-66d2-41d8-bb0a-0bab8ee3216e" />
          <Property Name="PrjProjectFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="2914e501-525f-4bd8-ad6e-ffd87399e762" />
          <Property Name="BasCompanyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="a8cae38e-43b9-4231-b487-dd21d0af5c4e" />
        </EntityType>
        <EntityType Name="ConStatus2externalEntity" ed:Guid="9195d60c-a97f-478f-be5b-b4a81b8a2ed9">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9cecba45-3970-4817-aaf8-ef4fa8e25502" />
          <Property Name="ConStatusFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="5d2557ee-43bc-4066-b84f-29bb0b98eeb8" />
          <Property Name="BasExternalsourceFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="8f5323dd-dbdd-440b-9585-67c835b7a903" />
          <Property Name="ExtCode" Type="String" Nullable="false" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="true" ed:Guid="087c9da9-d26d-4a14-80be-8ae6a7d5edea" />
          <Property Name="ExtDescription" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="54cb8749-79b8-4904-96d2-49b65c326c66" />
          <Property Name="CommentText" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="990827ff-e949-4c84-a5e8-7525959f8ae5" />
          <Property Name="Sorting" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="94883773-65b7-4953-acf4-faae1fbde80f" />
          <Property Name="Isdefault" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="1a19c004-1ffd-406d-8526-5728fad6a429" />
          <Property Name="Islive" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="2520ada9-c15a-4c7d-b58f-e67996fe3883" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="61b485ca-5102-4103-96f0-0098d233bddf" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c8e05782-7695-40d3-91e4-65bc9ad7d4de" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="b645111d-8843-41f6-b325-b76971c594bf" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="e969729a-4e9b-4bad-ad87-be0755405814" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="09c56f84-2362-42a2-a95a-12697f3832aa" />
          <NavigationProperty Name="ConStatusEntity" Relationship="RIB.Visual.Procurement.Contract.BusinessComponents.CON_STATUS_FK05" FromRole="ConStatus2externalEntities" ToRole="ConStatusEntity" ed:Guid="18a038e2-5f55-4f72-ae4c-0757fcef77d8" />
        </EntityType>
        <EntityType Name="ConHeaderUserAccessVEntity" ed:Guid="3f47d77f-0a8a-4627-b8bb-7f606dc495f1" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="UserId" />
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="UserId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="258dafb5-7f24-485c-9a77-33811711efe5" />
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="72af618f-c722-47ef-9479-dc7dbecd2819" />
        </EntityType>
        <EntityType Name="ConHeader2customerEntity" ed:Guid="9db7de35-9007-421f-9cf0-5f07b5620333">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c894ad8f-2988-453f-8496-bd9982f2cabd" />
          <Property Name="ConHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="2a45c78d-2f4e-45d4-ba77-b7f97c0f05b0" />
          <Property Name="BpdBusinesspartnerFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="276864a2-6a05-45b9-bd2b-66065e9e87f2" />
          <Property Name="BpdSubsidiaryFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="49695cb6-ec18-42af-8485-d7e0738819e3" />
          <Property Name="BpdCustomerFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="2bb9fea7-70c2-41fc-bb1d-be4bac25b52e" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="6fe4862a-54f4-4a7d-bc95-3a3dda922ff8" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="423307af-43af-41a5-bede-59cac57db243" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="bb5b6a6a-ba0c-4435-a7de-82cf1d2c47a0" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="f435ca9a-56ea-410c-954c-8ab6c9f5932f" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="e79f3e0a-cebc-4b6c-8231-07204a5062f8" />
          <NavigationProperty Name="ConHeaderEntity" Relationship="RIB.Visual.Procurement.Contract.BusinessComponents.CON_HEADER_FK17" FromRole="ConHeader2customerEntities" ToRole="ConHeaderEntity" ed:Guid="2a1abe6b-5739-4c7c-9291-22aeb4187111" />
        </EntityType>
        <EntityType Name="DdTempIdsEntity" ed:Guid="d909209d-d112-4be0-8338-f046fc1c5fe2" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="RequestId" />
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="RequestId" Type="String" Nullable="false" MaxLength="32" FixedLength="true" ed:ValidateMaxLength="32" ed:ValidateRequired="true" ed:Guid="a53b7126-5bdb-4d4b-8dbc-65cbf45ef22f" />
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="afa879aa-43d9-4fc1-8e9b-24e48e2f4a58" />
          <Property Name="Key1" Type="Int32" ed:ValidateRequired="false" ed:Guid="e8d6df16-87c5-479b-9bcb-8dc187bfb713" />
          <Property Name="Key2" Type="Int32" ed:ValidateRequired="false" ed:Guid="02adfc4a-8ba6-4408-b957-c6b68ed531c0" />
          <Property Name="Key3" Type="Int32" ed:ValidateRequired="false" ed:Guid="c6c0dcbf-2b9b-45fc-b02a-392c06558ac5" />
        </EntityType>
        <EntityType Name="ConAccountAssignmentEntity" ed:Guid="06cd48a2-9c9a-4e2f-a1a6-09dc061c24dd">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0094c387-6638-4d06-acbd-b157b3c50343" />
          <Property Name="ItemNO" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c5e794ac-79be-4a38-bc16-0128392e0320">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BreakdownPercent" Type="Decimal" Nullable="false" Precision="10" Scale="2" ed:ValidateRequired="true" ed:Guid="e330cd69-5ecc-4345-87a4-21841c19ea5d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">percent</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BreakdownAmount" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="ec633716-8af9-4188-870e-9bf119fc2b52">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasCompanyYearFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="160e14cd-738c-4e5f-a5ca-1411de3973e0">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MdcControllingUnitFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="94e776fd-d91c-42f1-91d7-56e4e6bbbf15">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PsdScheduleFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="5bb18cdb-a08e-4f99-9634-19e3682e44a2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PsdActivityFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="3d95cd46-5b0b-47e7-a97d-3534bec4c6bc">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ConHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="9a7f11c4-c9c2-4135-8838-1acf711dfb9d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Remark" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="9ff4bc2b-30a8-4dd3-92df-0699ca5ffdf7">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">remark</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="fb63fe07-19a0-4eb7-96bc-a149b5bdb0d4" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="f732f2ea-4ffd-489c-b0af-335e006d980d" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="099c5845-efd8-4591-b365-edece84d6e84" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="68b57c04-5678-42f1-a7a1-7a36c4181492" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="11ad1b19-7a8e-4dae-b0c9-80f6de7a9d47" />
          <Property Name="ConCrewFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="0b508079-9363-45a6-a22f-fac69c1538d6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="ae5f3de4-632f-4050-8711-bab20bd1e810">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Quantity" Type="Decimal" Precision="19" Scale="6" ed:ValidateRequired="false" ed:Guid="1d351f1b-c7b4-40bd-a03f-bb2a0a13091a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasAccountFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="42d1df31-4667-45dc-9cf3-c444f6165913">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsDelete" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="5bf3429c-0f5d-46b7-880b-5833e5f2e622">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsFinalInvoice" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="7cc99359-e133-4456-ae98-3581e8e6562a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BreakdownAmountOc" Type="Decimal" Nullable="false" DefaultValue="0" Precision="19" Scale="6" ed:ValidateRequired="true" ed:Guid="d5c6acc8-eef1-4354-a1ea-4a0a5ca0d119">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasUomFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="0db40ffd-c67a-4874-90a4-0c9985127b15">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateDelivery" Type="DateTime" ed:ValidateRequired="false" ed:Guid="114f2365-72a4-4e1c-a9b2-6b01e108a3c4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AccountAssignment01" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="96d0e5b9-e774-4c77-91c9-463363b5d6d3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AccountAssignment02" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="13aaf202-5424-41b3-a406-d94ee2a607ab">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasAccAssignItemTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b0f9526d-8aa1-4b73-98a7-29e88661800e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasAccAssignMatGroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="1ea9d680-f9e8-45ee-a007-c63cda30015c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasAccAssignAccTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="14bbb653-34bf-4ab5-a9b1-f2db8772b6d8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AccountAssignment03" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="*************-4386-ac0b-2af67a64e8ce">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BasAccAssignFactoryFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="9479d8a2-0521-4d68-8a6a-d9ef432349ba">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
        </EntityType>
        <EntityType Name="ConHeader2MdlObjectVEntity" ed:Guid="5dbf6160-7801-4e3d-ab5c-2d6be3aaa723">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="ModelFk" />
            <PropertyRef Name="ObjectFk" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="605eb307-7609-4e60-82b2-65e2c1a7da55" />
          <Property Name="ModelFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b71e71b7-5a2c-4b46-beaa-91db2056358e" />
          <Property Name="ObjectFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b067443a-4c51-41b8-840f-f23ed6b9199f" />
        </EntityType>
        <EntityType Name="ConCrewEntity" ed:Guid="bee023fa-611a-4152-8492-997ea62f9581">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="3132ba89-c2f7-405b-80de-caecafced14e" />
          <Property Name="ConHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="588c3508-4d5a-4342-b313-fc515233db33" />
          <Property Name="DescriptionInfo" Type="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType" Nullable="false" ed:ValidateRequired="false" ed:Guid="84fbd50b-0b00-4d1e-9619-3362a6c06c03">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">translation</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BpdContactFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="69e91aab-29a2-49ce-8c97-3cad5b341b90">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Sorting" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="1c2e0c42-0f50-47b1-9c8d-0f2560009255">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsDefault" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="bcc21966-17d1-4d9b-bf90-7119b8415577">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsLive" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="74a95ac6-ceb8-4dc9-9918-ce89a138a65c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefined1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="84cd23b2-874f-4df9-b7a2-1453aa74b310">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefined2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="5f4f81b8-c686-4f4e-9ebb-a2112bc9b089">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefined3" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="4eb8c185-0381-4ce1-bc95-2862edea06e1">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefined4" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="0f268263-cb02-484e-8b7a-74c49b4a2663">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="UserDefined5" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="431a1a9f-c2cb-4cd1-9ca0-617c214b8db1">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="7163706c-fad8-4d57-91cb-5b590b1e536e" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4bee27da-2d4d-45ff-8592-22c326b542e7" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="4fc15d49-3022-4f3c-b9b1-2942d2c0b079" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="338e589a-f7ef-4460-8ba7-25fa81b7165b" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="acc04432-9db9-458a-b421-f758e287968b" />
        </EntityType>
        <EntityType Name="ConMasterRestrictionEntity" ed:Guid="5a9d0644-8d3f-44b1-9255-ccb2c7ea86af">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="5a9d7b95-a5ad-4dbe-8fba-1efef70abd79" />
          <Property Name="ConHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="f4134233-7888-45eb-8ef7-a9028d1399f2" />
          <Property Name="MdcMaterialCatalogFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="69da6416-9fc1-47b0-94ef-111f9e82e9ce">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BoqWicCatFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="6301f5fe-7136-47af-9e14-8ff569d14b5e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="92dfc4b0-7359-4050-978c-76ace29309b8" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="405448e4-33e0-427b-9273-5ac6767d7922" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="4663d000-3074-42c6-b7c8-3cf4f406b6ed" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="58009a85-54e9-42c1-826b-255703c48063" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c2d7c1c8-e36b-4588-b7da-e2353ca22c68" />
          <Property Name="BoqHeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="378bd664-50f7-4a09-8b0e-0837864fd02f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ConHeaderBoqFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b5f1cad3-ad7b-42fe-ab25-ade7b5a6818a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Visibility" Type="Int32" Nullable="false" DefaultValue="1" ed:ValidateRequired="true" ed:Guid="28b0ad37-e8ef-4ef4-93a3-fb1d181e8b0b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PackageFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="edb62879-9187-46b2-93b4-fc8bf4f54f2e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ProjectFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="dcd82376-493b-474d-8c12-5a52207b76b5">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PrjBoqFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="3f7d0faa-18b5-4d61-924e-51f3907da172">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CopyType" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="e3d63f87-cb0b-442d-be35-1189653f8299">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
        </EntityType>
        <EntityType Name="PrcCopyModeEntity" ed:Guid="0a6d60ec-4c39-43a1-94ff-56366fe685ca">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="e0167a2a-94b9-461b-9203-8a8ac13314a0" />
          <Property Name="Sorting" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="5f0c0d10-25ca-4745-bb24-408df6373acb" />
          <Property Name="IsDefault" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="c6237416-93f4-4db9-8a7e-1e7dad6abc61" />
          <Property Name="IsLive" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="c6a54d3a-21c4-4704-89c3-6ccd3be7a938" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="453509e2-84e9-497f-8a63-d4444bcf4b3b" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="ffa02fe5-6a5b-4d1e-85d9-228e06eba142" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="6f1f94a5-a6dd-42c8-a990-734adc482793" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="96772156-6f5d-4baa-825f-27a63154ef48" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="3e914b9c-9dac-4c1e-818e-047d7b31e9f8" />
          <Property Name="DescriptionInfo" Type="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType" Nullable="false" ed:ValidateRequired="false" ed:Guid="882c51c6-5e41-4375-bda0-f2a426e4976f" />
        </EntityType>
        <EntityType Name="ConAdvanceEntity" ed:Guid="e0666031-d293-4c4f-8f78-f1f2547ca9e2">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="947385c2-ec18-48f0-9237-589f7bc37802" />
          <Property Name="ConHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="b1b0084f-7d27-4b68-b9be-080d913a230c" />
          <Property Name="PrcAdvanceTypeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="87c41203-6ff7-4ee7-8fc0-a51746f48237">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="611279b5-4059-42ce-9930-1ec3b921ec21">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateDue" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="79904767-d9e1-408a-b3ed-bfc2f0a49fbb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AmountDue" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="76e83d3a-6ff3-4ab9-8752-0242519c5592">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PercentProrata" Type="Decimal" Nullable="false" Precision="9" Scale="3" ed:ValidateRequired="true" ed:Guid="fc7bc289-e210-4ae9-9158-072ae26478b1">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateDone" Type="DateTime" ed:ValidateRequired="false" ed:Guid="1af48bc4-a0b2-4d2b-b488-49967befc079">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AmountDone" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="f0e7fc48-30af-4146-b16a-17a43be331de">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CommentText" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="741a615a-bfcb-4834-a651-32f4d8fd668d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">comment</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="9d71de5e-76c8-4d91-a49f-a2bc91d8a902">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="a9b58a36-8e82-463b-acd6-a8c7e8557723">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined3" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="5b0aedf5-705d-44a2-9bc3-938c8db2bd9c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined4" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="b38c8efe-7fd5-40b6-ad90-f9cf23018700">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined5" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="1cd956ac-9b63-4287-a966-ae310f0b2c53">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="4ef7cda8-e0a6-4c4d-b308-c68c7129cc29" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="6e17ce13-8721-49ff-9dce-2946636d959b" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="048022c7-ab2d-4ce1-98f7-f1de0232396e" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="d2357e32-0884-4e9a-a7cd-009d49379bfa" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="efc7e5c8-8f2f-44e4-b970-3f8721887e35" />
          <Property Name="AmountDueOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="33fa320a-b7e1-4ff5-9611-34717578a3cb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AmountDoneOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="599b0ecb-92ea-4807-8bba-ec2c79930bf3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PaymentTermFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="7bdfd892-7d77-4fb6-9ffe-51bb761c267e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
        </EntityType>
        <EntityType Name="ConTransactionEntity" ed:Guid="0e49ef7d-6c9e-480e-a3d6-5d9826095443">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="eb7dfb90-c463-4a59-b624-8ac15cbfabb4" />
          <Property Name="TransactionId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="7b5d2654-ac62-4fe7-9f94-70f8f2e23a8a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CompanyFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="30f01bb1-13c2-4327-864c-04cd0f8b1dca">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="CompanyInvoiceFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="46eec078-cf1a-42b7-ba7d-9fe49ef73264">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ConHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="dcfd3c11-cc7a-49c6-ad74-2b9fb2bfd721">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PrcConfigurationFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="750aa9e9-d605-43ea-b1af-be23b979f07a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Isconsolidated" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="b43bde71-1a74-4825-9ffa-6f307471137a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Ischange" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="dffac4de-6af0-4b86-902e-ae12deecb9be">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Currency" Type="String" Nullable="false" MaxLength="3" Unicode="true" ed:ValidateMaxLength="3" ed:ValidateRequired="true" ed:Guid="742898f0-d6fd-410a-8580-adfd1bbe384f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ConStatusFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4cb2063d-b7fc-4887-8f6f-530bceb52168">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Code" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="92e779d5-**************-2cdd38e1a15e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="d88101f0-1c94-44a0-a4b8-15c107e5efda">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateOrdered" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="abbb2570-1601-43ad-b9cc-ed9d968e7ef9">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">date</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateEffective" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="773875e7-0673-47ab-a2fc-26d9db771b71">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">date</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateReported" Type="DateTime" ed:ValidateRequired="false" ed:Guid="7f44ebc1-9e3b-4ba6-9fb4-920aaeee58b9">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">date</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BusinessPartnerFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="867ed099-21c9-48c3-943d-8f1a0f8b2923">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SubsidiaryFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="c093d6e8-96d6-43a5-be86-697e1919d861">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="SupplierFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="10d88e5f-d67f-4f63-909f-535768ca8e1f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ContactFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="cd10fbb5-13d0-4a80-bb1a-47dd2393b279">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BankFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="0120d2a6-9474-47ea-8bbb-9665a5c11098">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="VatGroupFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="********-d94a-4d34-ab82-bc9725b3beb3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PaymentTermFiFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="3769f093-7fe9-4fe3-abd5-c0fd40da9e30">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PaymentTermPaFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="c242be6b-a9dc-4c86-ad9a-107935af8d0a">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PaymentTermAdFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="fdc38619-1ff5-4b2e-b09e-92991375e5d1">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Incoterm" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="576cbd4d-f756-45b2-84d4-a6e43e61b5f0">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DateDelivery" Type="DateTime" ed:ValidateRequired="false" ed:Guid="0dbb1664-8d01-4f80-940c-12bf9bc9eef2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">date</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AddressFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="6c0713b0-3974-4497-b1d9-d7014d1d4aaa">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ItemReference" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="eb146f25-6baf-4c9a-8dee-3b72fa61d85f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Amount" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="a5e3cc64-0686-4f6c-a6dd-88b12140a926">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="VatAmount" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="f36f31aa-bd53-425e-ab10-ec5a45efff0b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="AmountOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="*************-4ed4-94e0-a749bd3fde00">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="VatAmountOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="eccf4e17-8d04-4190-9682-01812ecb64d0">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IncAmount" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="9335a49a-3a30-461d-b8c7-9f8c64d7c0f6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IncVatAmount" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="0836bffb-33d1-4fdd-a06e-93498669fe81">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IncAmountOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="70eea665-a8ec-4541-b612-91b576c6c448">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IncVatAmountOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="92d75a70-d1dd-4f08-8ec1-eb9677e03eae">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IncQuantity" Type="Decimal" Nullable="false" Precision="19" Scale="6" ed:ValidateRequired="true" ed:Guid="3ad172d5-ba5a-4725-8900-802218ec195f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Quantity" Type="Decimal" Nullable="false" Precision="19" Scale="6" ed:ValidateRequired="true" ed:Guid="36bccedc-5fe6-41b2-9dca-438f3693a180">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">quantity</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Price" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="386fd4ff-**************-bc7b4d737d7b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="VatPrice" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="d3119c4a-1c47-4e8b-8882-bd59eff27379">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PriceOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="b9c178a2-f4f2-46e2-8a31-6be2ba4f5c50">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="VatPriceOc" Type="Decimal" Nullable="false" Precision="19" Scale="7" ed:ValidateRequired="true" ed:Guid="6beab1e6-3576-4020-a12e-d13ec6d6a824">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="NominalAccount" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="f4d79c45-f685-404a-a6c7-22d1aa35879b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="NominalDimension1" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="46b3d370-733d-485b-b55e-59d06c868af2">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="NominalDimension2" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="280125f8-e3a5-4b8d-ad29-192431430a10">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="NominalDimension3" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="43c0626c-c4d3-4bb1-9844-63e0a45887f6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="TaxCodeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="4e81eda9-deb3-4248-bf6b-3aea1d674d04">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="TaxCodeMatrixFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="a39243dc-1af9-4354-a357-03427ceffde1">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="VatPercent" Type="Decimal" Nullable="false" Precision="9" Scale="3" ed:ValidateRequired="true" ed:Guid="1a491ba4-32d7-4542-9c1b-34131cb07fbc">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">money</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="VatCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="f27fbae7-43e1-4307-99fa-e3ceab3db2c3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="d5f30503-094b-4b38-a857-fae263e4338f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitCode" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="3e09cd44-4ecc-4111-9e0a-491ad3ce06eb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign01" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="99ed9ebf-79ce-4779-8e20-32f95073dcfa">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign01desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="b5c4db17-c1b2-4af9-bcb1-0b7a32ab11cb">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign02" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="72946b51-16b1-497a-9061-837f9c93d84c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign02desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="e8c1264a-0b32-439d-88ec-612c546be151">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign03" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="d457a8d5-5692-4a42-808d-684d6059bfb0">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign03desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="e2b3abf0-67e8-4b93-a6bc-982649bb0b0c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign04" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="2d43fe37-c366-4272-b171-3b382a8d7fc7">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign04desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="ed801e36-2e94-41cc-91e8-d3768c9dba02">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign05" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="a173c50c-a6d8-4e16-9bff-2289da4e21a5">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign05desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="ea3cb317-3fe6-4902-b6d9-afb54c4fcc9d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign06" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="93b60c9e-67db-4472-a69c-ab8b62a00cdc">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign06desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="ca7a1291-2c9e-4b97-9fad-6c6023f820e6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign07" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="47017516-10a4-4e77-a91c-321b112710ab">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign07desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="6d99ff8e-ac39-4c08-b233-7fee76e8caec">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign08" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="6e896885-5a6d-4058-9e76-cb1131b4ea74">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign08desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="5333678a-f088-4fff-b8b9-3e86b6d92d1b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign09" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="1a8700e0-7c9a-405d-bd49-f3d5e8ff809d">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign09desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="ebe2dd7d-67e0-4e16-8afe-9952440fb81f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingUnitAssign10" Type="String" MaxLength="32" Unicode="true" ed:ValidateMaxLength="32" ed:ValidateRequired="false" ed:Guid="227a6fee-9fdd-4468-8ba9-ff198ab1d133">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ControllingunitAssign10desc" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="918e2ba2-8739-44b1-a0a6-9401ca691c00">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="PrcItemFk" Type="Int64" ed:ValidateRequired="false" ed:Guid="917dbda2-d99c-4c23-860f-82030bba5d3e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="MaterialFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="fa38ebfa-94d7-4557-84c0-28fef9054907">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ItemDescription1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="c7a22a9d-e160-4d8f-9bec-b0b4b16954ce">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ItemDescription2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="a3c59b63-d575-4138-9da5-86aa389a3a5c">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ItemSpecification" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="59991d15-cc76-48e5-b7f0-7ac9f5ba1232">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">remark</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ItemUomQuantity" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="c90e7a02-2f69-47eb-967e-8da62229f10b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsSuccess" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="8213b458-4c1a-45b5-b831-e5add5f75e0e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="HandoverId" Type="Int32" ed:ValidateRequired="false" ed:Guid="65e8bd0c-2afd-4895-b90c-2716e437b9ba">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Orderno" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="7d8fde7a-5d58-4ee2-bb05-c11b13e735e5">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined1" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="e90fa83f-01fc-433a-81ba-2f19b83dbdcc">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined2" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="16145fbc-5f18-4dff-838f-fa6f5e97cb8f">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Userdefined3" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="394a1475-4e94-40b9-add5-3e037626a5e4">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">description</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ReturnValue" Type="String" MaxLength="2000" Unicode="true" ed:ValidateMaxLength="2000" ed:ValidateRequired="false" ed:Guid="8cb553f4-62ee-4e03-b9e5-267a7837a36b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">remark</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="ebbcef1b-8130-4b2b-a8ef-4ae571a6538f" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="bdafd924-e057-4766-9541-2e729805e59b" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="af8c3e82-a464-4f80-950a-0d37873ae8c0" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="42aaa8e3-10d0-4b68-9820-45551dc47cfd" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c31437ce-5d33-4b86-af4c-5df2f3e44388" />
          <Property Name="IncotermCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="a9b4ec48-a860-4d99-bfc2-ea9f841ae9a3">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">code</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
        </EntityType>
        <EntityType Name="ConHeaderApprovalEntity" ed:Guid="6bd57b04-d9be-4b87-ba8b-411d288a7713">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c339919e-dac0-4395-9e14-f09f99fde00e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="IsApproved" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="05f93146-b496-4c1e-b29d-cd30e1f4f7b8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">boolean</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="Comment" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="5593e627-5018-4c7f-aaea-3c4432d56ee8">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">comment</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ClerkFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c5e18cb6-67a1-465d-8aec-d810614b31d6">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ClerkRoleFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="3e12163f-1063-40ac-9653-d61012f6ba3b">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="DueDate" Type="DateTime" ed:ValidateRequired="false" ed:Guid="692bbd9f-1608-4940-83d7-2baec4087c16">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="EvaluatedOn" Type="DateTime" ed:ValidateRequired="false" ed:Guid="684a470c-e91a-4a69-afe9-f3f937f70306">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">dateutc</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="EvaluationLevel" Type="Int32" ed:ValidateRequired="false" ed:Guid="0e49a5be-78e0-40c4-831c-06848fccc7fc">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="d9b3bd1b-f9d0-4a77-bcde-1b46eea2bf55" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="a720a4ed-6a7f-45b8-837f-0dbbf7add3e1" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="299df1be-5308-4151-830f-63192a5965d4" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="3e143191-d65c-4a33-962a-dea0ec9013aa" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="673a0ac5-aa13-4a5d-8550-b40fb724048a" />
          <Property Name="HeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="a234a030-db14-4c78-bbe4-86f2ea2fcf29">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <NavigationProperty Name="HeaderEntity" Relationship="RIB.Visual.Procurement.Contract.BusinessComponents.CON_HEADER_FK30" FromRole="ConHeaderapprovalEntities" ToRole="HeaderEntity" ed:Guid="db778240-6292-4198-be04-36d1d374bbd1" />
        </EntityType>
        <EntityType Name="ConHeaderItwoFinanceVEntity" ed:Guid="78a5995f-af73-4f9b-a639-6c78e71f6076" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="ConHeaderId" />
            <PropertyRef Name="ConHeaderCode" />
            <PropertyRef Name="ConHeaderDateOrdered" />
          </Key>
          <Property Name="ConHeaderId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="bebc7159-1b76-440a-9f26-f8388bfc8bfb" />
          <Property Name="ConHeaderConHeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="b61e9064-4d10-40ae-aaed-f66ac1ff2add" />
          <Property Name="ConHeaderCode" Type="String" Nullable="false" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="true" ed:Guid="300f9b74-9841-45d7-8556-2c510c87396c" />
          <Property Name="ConHeaderDescription" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="e82b4221-bf6c-4fa9-ae7b-d49447167485" />
          <Property Name="ConHeaderDateOrdered" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="db2a11fb-2de1-43c3-9606-6ff0a4c3a52f" />
          <Property Name="ConHeaderMdcTaxCodeFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="057d278c-e020-414b-9033-b8d29ecca518" />
          <Property Name="ConHeaderBpdVatgroupFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="0a8991de-0b9c-408d-9a0b-46f9850821fa" />
          <Property Name="ConTotalValueNet" Type="Decimal" Precision="19" Scale="7" ed:ValidateRequired="false" ed:Guid="729ea973-9c1f-47e0-a4f9-632d515ce93d" />
          <Property Name="ConTotalValueGross" Type="Decimal" Precision="20" Scale="7" ed:ValidateRequired="false" ed:Guid="d51432db-642d-4c2e-8786-3e3a27c836b5" />
          <Property Name="ConStatusCode" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="c2b87b28-c2fc-4a4e-9a7c-c5c589496f7f" />
          <Property Name="ConStatusDescription" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="43186072-512a-4f5f-bea7-29c74b22df66" />
          <Property Name="PrjProjectNo" Type="String" MaxLength="16" Unicode="true" ed:ValidateMaxLength="16" ed:ValidateRequired="false" ed:Guid="a8dcaba4-59e6-4306-8818-c04d7a60fffe" />
          <Property Name="BpdSupplierCode" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="ab99ef3e-0626-4703-9566-44ecdf4286ed" />
          <Property Name="BpdVatgroupReference" Type="String" MaxLength="20" Unicode="true" ed:ValidateMaxLength="20" ed:ValidateRequired="false" ed:Guid="eb3bd17f-20c8-43bc-a721-0d935a8b9a15" />
        </EntityType>
        <EntityType Name="ConHeader2BoqWicCatBoqEntity" ed:Guid="c7b8102c-4819-40bd-8d2e-65f6d975dcb0">
          <Key>
            <PropertyRef Name="Id" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="bcbd26e7-3217-48df-b2d1-d38edcb2d6dc">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="ConHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="f97ed4b1-841a-4c8b-a1d5-178fee798eee">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BoqHeaderFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c75db84a-b3c4-4a13-b5ed-1c395c091033">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="BoqWicCatBoqFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="7a36196b-e68c-40e0-bc34-dbdf7a476e6e">
            <ed:Attributes>
              <ed:Attribute Name="RIB.Visual.Platform.Common.DomainNameAttribute" Assembly="RIB.Visual.Platform.Common, Version=*********, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4">
                <ed:AttributeConstructor />
                <ed:Properties>
                  <ed:PropertyValue Name="Name" Type="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                    <string xmlns="">integer</string>
                  </ed:PropertyValue>
                </ed:Properties>
              </ed:Attribute>
            </ed:Attributes>
          </Property>
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="8f4ee561-d754-4cbf-b094-57cdb57e779c" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="1ba5f5df-cf12-49d6-ae1d-8f0dc8726928" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="55716440-ef30-4bb8-a8cf-b582162625cb" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="93c1b2b0-79d8-487b-a8b4-fdd520018843" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="d64f3c30-f91f-43b5-a0b6-432dddafb45a" />
        </EntityType>
        <EntityType Name="ConHeaderExtendedVEntity" ed:Guid="6add2b50-d3ab-46bc-839b-a291dfb70c2d" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="Id" />
            <PropertyRef Name="RubricCategoryFk" />
            <PropertyRef Name="VatPercent" />
            <PropertyRef Name="InsertedAt" />
            <PropertyRef Name="InsertedBy" />
            <PropertyRef Name="Version" />
          </Key>
          <Property Name="Id" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="f8c2eea1-9a44-478c-977d-423d7d3c0814" />
          <Property Name="RubricCategoryFk" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="ca0c900d-0d3f-4267-b598-a31854eb3192" />
          <Property Name="VatPercent" Type="Decimal" Nullable="false" Precision="9" Scale="3" ed:ValidateRequired="true" ed:Guid="3dc10a28-b24e-4c8f-a5f2-4a741adb79a8" />
          <Property Name="BaselinePath" Type="String" MaxLength="255" Unicode="true" ed:ValidateMaxLength="255" ed:ValidateRequired="false" ed:Guid="df00c529-9d4b-4b3e-aea2-b528bd09d358" />
          <Property Name="PrjStatusFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="d7029271-62dc-4d83-9cfa-9937008e093f" />
          <Property Name="IsStatusReadonly" Type="Boolean" Nullable="false" ed:ValidateRequired="true" ed:Guid="5824b933-ebec-412e-b79a-e367bba3d766" />
          <Property Name="ConHeaderMatFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4a40428d-1eb9-42d5-a282-c827e730ac89" />
          <Property Name="ConHeaderBoqCatFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="49bdbaac-1905-4ec9-8980-df56134abfe3" />
          <Property Name="PackageMaxBoqFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="fc1eeac8-f49e-4181-ac6f-2f5e3b491971" />
          <Property Name="PostedConHeaderFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="36094ae5-12ed-45bc-981d-df44fcf7aa34" />
          <Property Name="AccassignConTypeFk" Type="Int32" ed:ValidateRequired="false" ed:Guid="4af6889b-12f0-4e15-adb6-0d173b548a41" />
          <Property Name="InsertedAt" Type="DateTime" Nullable="false" ed:ValidateRequired="true" ed:Guid="f2382c17-0425-447c-abb4-95c97374176d" />
          <Property Name="InsertedBy" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0921b4be-a2c0-4fc4-9d63-7d56631823bb" />
          <Property Name="UpdatedAt" Type="DateTime" ed:ValidateRequired="false" ed:Guid="1f477c41-f55c-423a-b799-0452a14964af" />
          <Property Name="UpdatedBy" Type="Int32" ed:ValidateRequired="false" ed:Guid="73d5f6c7-5f8e-4ab2-b14c-cbd1c109273f" />
          <Property Name="Version" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="0bacc341-8d60-402e-95de-90c8c2c6511f" />
        </EntityType>
        <EntityType Name="ContractUserAccessCacheEntity" ed:Guid="c844dd62-74a4-4a6d-ac81-c67ed5bdb93f" ed:GenerateDTO="False">
          <Key>
            <PropertyRef Name="UserId" />
            <PropertyRef Name="CompanySignedInId" />
            <PropertyRef Name="AccessRoleId" />
            <PropertyRef Name="FromId" />
            <PropertyRef Name="ToId" />
          </Key>
          <Property Name="UserId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="df097b62-60b5-4146-b331-08c077e1f757" />
          <Property Name="CompanySignedInId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="334eba7c-8de0-40cd-8797-a2cb58511aa4" />
          <Property Name="AccessRoleId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="7d179eb4-0133-4854-8e12-26f4ad42f3a7" />
          <Property Name="FromId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="51f2687c-f3bd-425a-ac6b-e5026dc994bf" />
          <Property Name="ToId" Type="Int32" Nullable="false" ed:ValidateRequired="true" ed:Guid="c3108407-da31-4a10-bd63-59ec9fc4a1ca" />
        </EntityType>
        <ComplexType Name="DescriptionTranslateType" ed:Guid="c73a84e2-a0b7-42d3-9b77-4cd8e364f8ec" ed:GenerateOnlyMapping="True">
          <Property Name="DescriptionTr" Type="Int32" ed:ValidateRequired="false" ed:Guid="ae14925b-19f7-4661-9480-4b0cf35450e3">
            <Documentation>
              <Summary>Integer value</Summary>
            </Documentation>
          </Property>
          <Property Name="Description" Type="String" MaxLength="252" Unicode="true" ed:ValidateMaxLength="252" ed:ValidateRequired="false" ed:Guid="40eaeb1f-db03-49a6-9d8c-fd03f56429d6">
            <Documentation>
              <Summary>The description as a string of 42 alphanumerical characters.</Summary>
            </Documentation>
          </Property>
        </ComplexType>
        <Association Name="CON_STATUS_FK05" ed:Guid="8d031b10-f6e1-49bb-b9c8-d19f2a977dc1">
          <End Role="ConStatusEntity" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConStatusEntity" Multiplicity="1" />
          <End Role="ConStatus2externalEntities" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConStatus2externalEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ConStatusEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="ConStatus2externalEntities">
              <PropertyRef Name="ConStatusFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="CON_HEADER_FK17" ed:Guid="8aaca60c-a627-4dc3-805a-0cdc4d5aae49">
          <End Role="ConHeaderEntity" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderEntity" Multiplicity="1" />
          <End Role="ConHeader2customerEntities" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeader2customerEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ConHeaderEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="ConHeader2customerEntities">
              <PropertyRef Name="ConHeaderFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="ConHeaderEntity_DdTempIdsEntity" ed:Guid="a1e590fa-7a98-4ce1-8317-8242483ecfd5">
          <End Role="ConHeaderEntity" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderEntity" Multiplicity="1" />
          <End Role="DdTempIdsEntities" Type="RIB.Visual.Procurement.Contract.BusinessComponents.DdTempIdsEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ConHeaderEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="DdTempIdsEntities">
              <PropertyRef Name="Id" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="CON_HEADER_FK30" ed:Guid="1af599a9-946b-4cbb-894b-f33dcb3d8246">
          <End Role="HeaderEntity" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderEntity" Multiplicity="1" />
          <End Role="ConHeaderapprovalEntities" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderApprovalEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="HeaderEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="ConHeaderapprovalEntities">
              <PropertyRef Name="HeaderFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="CON_HEADER_FK35" ed:Guid="88af406c-53a4-42cd-9915-dc9737324a3e">
          <End Role="ConHeaderEntity" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderEntity" Multiplicity="0..1" />
          <End Role="ConMasterRestrictionEntities" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConMasterRestrictionEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ConHeaderEntity">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="ConMasterRestrictionEntities">
              <PropertyRef Name="ConHeaderBoqFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="CON_HEADER_FK21" ed:Guid="9d321671-a9e2-467f-9e1c-45b54cca64c6">
          <End Role="ConHeaderEntity1" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderEntity" Multiplicity="1" />
          <End Role="ConMasterRestrictionEntities1" Type="RIB.Visual.Procurement.Contract.BusinessComponents.ConMasterRestrictionEntity" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="ConHeaderEntity1">
              <PropertyRef Name="Id" />
            </Principal>
            <Dependent Role="ConMasterRestrictionEntities1">
              <PropertyRef Name="ConHeaderFk" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
      </Schema>
    </edmx:ConceptualModels>
    <!-- MSL content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="DbContextStoreContainer" CdmEntityContainer="ModelBuilder">
          <EntitySetMapping Name="ConHeaderEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderEntity">
              <MappingFragment StoreEntitySet="CON_HEADERs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ConStatusFk" ColumnName="CON_STATUS_FK" />
                <ScalarProperty Name="CompanyFk" ColumnName="BAS_COMPANY_FK" />
                <ScalarProperty Name="ProjectFk" ColumnName="PRJ_PROJECT_FK" />
                <ScalarProperty Name="PackageFk" ColumnName="PRC_PACKAGE_FK" />
                <ScalarProperty Name="TaxCodeFk" ColumnName="MDC_TAX_CODE_FK" />
                <ScalarProperty Name="ClerkPrcFk" ColumnName="BAS_CLERK_PRC_FK" />
                <ScalarProperty Name="ClerkReqFk" ColumnName="BAS_CLERK_REQ_FK" />
                <ScalarProperty Name="BasCurrencyFk" ColumnName="BAS_CURRENCY_FK" />
                <ScalarProperty Name="ExchangeRate" ColumnName="EXCHANGERATE" />
                <ScalarProperty Name="ProjectChangeFk" ColumnName="PRJ_CHANGE_FK" />
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="HasChanges" ColumnName="HASCHANGES" />
                <ScalarProperty Name="MaterialCatalogFk" ColumnName="MDC_MATERIAL_CATALOG_FK" />
                <ScalarProperty Name="PrcHeaderFk" ColumnName="PRC_HEADER_FK" />
                <ScalarProperty Name="PaymentTermFiFk" ColumnName="BAS_PAYMENT_TERM_FI_FK" />
                <ScalarProperty Name="PaymentTermPaFk" ColumnName="BAS_PAYMENT_TERM_PA_FK" />
                <ScalarProperty Name="Code" ColumnName="CODE" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="SearchPattern" ColumnName="SEARCH_PATTERN" />
                <ScalarProperty Name="DateOrdered" ColumnName="DATE_ORDERED" />
                <ScalarProperty Name="DateReported" ColumnName="DATE_REPORTED" />
                <ScalarProperty Name="DateCanceled" ColumnName="DATE_CANCELED" />
                <ScalarProperty Name="DateDelivery" ColumnName="DATE_DELIVERY" />
                <ScalarProperty Name="DateCallofffrom" ColumnName="DATE_CALLOFFFROM" />
                <ScalarProperty Name="DateCalloffto" ColumnName="DATE_CALLOFFTO" />
                <ScalarProperty Name="ConTypeFk" ColumnName="CON_TYPE_FK" />
                <ScalarProperty Name="AwardmethodFk" ColumnName="PRC_AWARDMETHOD_FK" />
                <ScalarProperty Name="ContracttypeFk" ColumnName="PRC_CONTRACTTYPE_FK" />
                <ScalarProperty Name="ControllingUnitFk" ColumnName="MDC_CONTROLLINGUNIT_FK" />
                <ScalarProperty Name="BusinessPartnerFk" ColumnName="BPD_BUSINESSPARTNER_FK" />
                <ScalarProperty Name="SubsidiaryFk" ColumnName="BPD_SUBSIDIARY_FK" />
                <ScalarProperty Name="SupplierFk" ColumnName="BPD_SUPPLIER_FK" />
                <ScalarProperty Name="IncotermFk" ColumnName="PRC_INCOTERM_FK" />
                <ScalarProperty Name="CompanyInvoiceFk" ColumnName="BAS_COMPANY_INVOICE_FK" />
                <ScalarProperty Name="AddressFk" ColumnName="BAS_ADDRESS_FK" />
                <ScalarProperty Name="CodeQuotation" ColumnName="CODE_QUOTATION" />
                <ScalarProperty Name="DateQuotation" ColumnName="DATE_QUOTATION" />
                <ScalarProperty Name="Remark" ColumnName="REMARK" />
                <ScalarProperty Name="Userdefined1" ColumnName="USERDEFINED1" />
                <ScalarProperty Name="Userdefined2" ColumnName="USERDEFINED2" />
                <ScalarProperty Name="Userdefined3" ColumnName="USERDEFINED3" />
                <ScalarProperty Name="Userdefined4" ColumnName="USERDEFINED4" />
                <ScalarProperty Name="Userdefined5" ColumnName="USERDEFINED5" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="BillingSchemaFk" ColumnName="MDC_BILLING_SCHEMA_FK" />
                <ScalarProperty Name="BusinessPartnerAgentFk" ColumnName="BPD_BUSINESSPARTNER_AGENT_FK" />
                <ScalarProperty Name="Package2HeaderFk" ColumnName="PRC_PACKAGE2HEADER_FK" />
                <ScalarProperty Name="BusinessPartner2Fk" ColumnName="BPD_BUSINESSPARTNER2_FK" />
                <ScalarProperty Name="Subsidiary2Fk" ColumnName="BPD_SUBSIDIARY2_FK" />
                <ScalarProperty Name="Supplier2Fk" ColumnName="BPD_SUPPLIER2_FK" />
                <ScalarProperty Name="ConfirmationCode" ColumnName="CONFIRMATION_CODE" />
                <ScalarProperty Name="ConfirmationDate" ColumnName="CONFIRMATION_DATE" />
                <ScalarProperty Name="ExternalCode" ColumnName="EXTERNAL_CODE" />
                <ScalarProperty Name="ContactFk" ColumnName="BPD_CONTACT_FK" />
                <ScalarProperty Name="Contact2Fk" ColumnName="BPD_CONTACT2_FK" />
                <ScalarProperty Name="PaymentTermAdFk" ColumnName="BAS_PAYMENT_TERM_AD_FK" />
                <ScalarProperty Name="PrcCopyModeFk" ColumnName="PRC_COPYMODE_FK" />
                <ScalarProperty Name="DatePenalty" ColumnName="DATE_PENALTY" />
                <ScalarProperty Name="PenaltyPercentPerDay" ColumnName="PENALTY_PERCENTPERDAY" />
                <ScalarProperty Name="PenaltyPercentMax" ColumnName="PENALTY_PERCENTMAX" />
                <ScalarProperty Name="PenaltyComment" ColumnName="PENALTY_COMMENT" />
                <ScalarProperty Name="DateEffective" ColumnName="DATE_EFFECTIVE" />
                <ScalarProperty Name="BpdVatGroupFk" ColumnName="BPD_VATGROUP_FK" />
                <ScalarProperty Name="ProvingPeriod" ColumnName="PROVING_PERIOD" />
                <ScalarProperty Name="ProvingDealdline" ColumnName="PROVING_DEALDLINE" />
                <ScalarProperty Name="ApprovalPeriod" ColumnName="APPROVAL_PERIOD" />
                <ScalarProperty Name="ApprovalDealdline" ColumnName="APPROVAL_DEALDLINE" />
                <ScalarProperty Name="IsFreeItemsAllowed" ColumnName="ISFREEITEMSALLOWED" />
                <ScalarProperty Name="MdcPriceListFk" ColumnName="MDC_PRICE_LIST_FK" />
                <ScalarProperty Name="BankFk" ColumnName="BPD_BANK_FK" />
                <ScalarProperty Name="QtnHeaderFk" ColumnName="QTN_HEADER_FK" />
                <ScalarProperty Name="ExecutionStart" ColumnName="EXECUTION_START" />
                <ScalarProperty Name="ExecutionEnd" ColumnName="EXECUTION_END" />
                <ScalarProperty Name="BasAccassignBusinessFk" ColumnName="BAS_ACCASSIGN_BUSINESS_FK" />
                <ScalarProperty Name="BasAccassignControlFk" ColumnName="BAS_ACCASSIGN_CONTROL_FK" />
                <ScalarProperty Name="BasAccassignAccountFk" ColumnName="BAS_ACCASSIGN_ACCOUNT_FK" />
                <ScalarProperty Name="BasAccassignConTypeFk" ColumnName="BAS_ACCASSIGN_CON_TYPE_FK" />
                <ScalarProperty Name="ReqHeaderFk" ColumnName="REQ_HEADER_FK" />
                <ScalarProperty Name="OrdHeaderFk" ColumnName="ORD_HEADER_FK" />
                <ScalarProperty Name="OverallDiscount" ColumnName="OVERALL_DISCOUNT" />
                <ScalarProperty Name="OverallDiscountOc" ColumnName="OVERALL_DISCOUNT_OC" />
                <ScalarProperty Name="OverallDiscountPercent" ColumnName="OVERALL_DISCOUNT_PERCENT" />
                <ScalarProperty Name="SalesTaxMethodFk" ColumnName="BAS_SALES_TAX_METHOD_FK" />
                <ScalarProperty Name="ValidFrom" ColumnName="VALIDFROM" />
                <ScalarProperty Name="ValidTo" ColumnName="VALIDTO" />
                <ScalarProperty Name="BoqWicCatFk" ColumnName="BOQ_WIC_CAT_FK" />
                <ScalarProperty Name="BoqWicCatBoqFk" ColumnName="BOQ_WIC_CAT_BOQ_FK" />
                <ScalarProperty Name="BaselineUpdate" ColumnName="BASELINE_UPDATE" />
                <ScalarProperty Name="IsFramework" ColumnName="ISFRAMEWORK" />
                <ScalarProperty Name="IsNotAccrualPrr" ColumnName="ISNOTACCRUAL_PRR" />
                <ScalarProperty Name="BasLanguageFk" ColumnName="BAS_LANGUAGE_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConStatusEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConStatusEntity">
              <MappingFragment StoreEntitySet="CON_STATUS">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Sorting" ColumnName="SORTING" />
                <ScalarProperty Name="IsDefault" ColumnName="ISDEFAULT" />
                <ScalarProperty Name="Icon" ColumnName="ICON" />
                <ScalarProperty Name="IsReadonly" ColumnName="ISREADONLY" />
                <ScalarProperty Name="Iscanceled" ColumnName="ISCANCELED" />
                <ScalarProperty Name="IsReported" ColumnName="ISREPORTED" />
                <ScalarProperty Name="IsVirtual" ColumnName="ISVIRTUAL" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="IsOptionalUpwards" ColumnName="ISOPTIONAL_UPWARDS" />
                <ScalarProperty Name="IsOptionalDownwards" ColumnName="ISOPTIONAL_DOWNWARDS" />
                <ScalarProperty Name="IsDelivered" ColumnName="ISDELIVERED" />
                <ScalarProperty Name="IsPartDelivered" ColumnName="ISPARTDELIVERED" />
                <ScalarProperty Name="IsInvoiced" ColumnName="ISINVOICED" />
                <ScalarProperty Name="IsOrdered" ColumnName="ISORDERED" />
                <ScalarProperty Name="IsAccepted" ColumnName="ISACCEPTED" />
                <ScalarProperty Name="IsPartAccepted" ColumnName="ISPARTACCEPTED" />
                <ScalarProperty Name="IsRejected" ColumnName="ISREJECTED" />
                <ScalarProperty Name="IsChangSent" ColumnName="ISCHANGSENT" />
                <ScalarProperty Name="IsChangeAccepted" ColumnName="ISCHANGEACCEPTED" />
                <ScalarProperty Name="IsChangeRejected" ColumnName="ISCHANGEREJECTED" />
                <ScalarProperty Name="IsLive" ColumnName="ISLIVE" />
                <ScalarProperty Name="AccessRightDescriptorFk" ColumnName="FRM_ACCESSRIGHTDESCRIPTOR_FK" />
                <ScalarProperty Name="Code" ColumnName="CODE" />
                <ScalarProperty Name="IsUpdateImport" ColumnName="ISUPDATEIMPORT" />
                <ScalarProperty Name="IsPesCo" ColumnName="ISPESCO" />
                <ComplexProperty Name="DescriptionInfo" TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType">
                  <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                  <ScalarProperty Name="DescriptionTr" ColumnName="DESCRIPTION_TR" />
                </ComplexProperty>
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConTypeEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConTypeEntity">
              <MappingFragment StoreEntitySet="CON_TYPEs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Sorting" ColumnName="SORTING" />
                <ScalarProperty Name="IsDefault" ColumnName="ISDEFAULT" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="IsLive" ColumnName="ISLIVE" />
                <ComplexProperty Name="DescriptionInfo" TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType">
                  <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                  <ScalarProperty Name="DescriptionTr" ColumnName="DESCRIPTION_TR" />
                </ComplexProperty>
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConTotalEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConTotalEntity">
              <MappingFragment StoreEntitySet="CON_TOTALs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="HeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="TotalTypeFk" ColumnName="PRC_TOTALTYPE_FK" />
                <ScalarProperty Name="ValueNet" ColumnName="VALUE_NET" />
                <ScalarProperty Name="ValueNetOc" ColumnName="VALUE_NET_OC" />
                <ScalarProperty Name="ValueTax" ColumnName="VALUE_TAX" />
                <ScalarProperty Name="ValueTaxOc" ColumnName="VALUE_TAX_OC" />
                <ScalarProperty Name="CommentText" ColumnName="COMMENT_TEXT" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConHeaderLookupVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderLookupVEntity">
              <MappingFragment StoreEntitySet="CON_HEADER_LOOKUP_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="DateOrdered" ColumnName="DATE_ORDERED" />
                <ScalarProperty Name="Code" ColumnName="CODE" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="BpName1" ColumnName="BP_NAME1" />
                <ScalarProperty Name="BpName2" ColumnName="BP_NAME2" />
                <ScalarProperty Name="SupplierCode" ColumnName="SUPPLIER_CODE" />
                <ScalarProperty Name="ProjectNo" ColumnName="PROJECTNO" />
                <ScalarProperty Name="ProjectName" ColumnName="PROJECT_NAME" />
                <ScalarProperty Name="CompanyFk" ColumnName="BAS_COMPANY_FK" />
                <ScalarProperty Name="BusinessPartnerFk" ColumnName="BPD_BUSINESSPARTNER_FK" />
                <ScalarProperty Name="ProjectFk" ColumnName="PRJ_PROJECT_FK" />
                <ScalarProperty Name="PrcPackageFk" ColumnName="PRC_PACKAGE_FK" />
                <ScalarProperty Name="ControllingUnitFk" ColumnName="MDC_CONTROLLINGUNIT_FK" />
                <ScalarProperty Name="PrcStructureFk" ColumnName="PRC_STRUCTURE_FK" />
                <ScalarProperty Name="PaymentTermFiFk" ColumnName="BAS_PAYMENT_TERM_FI_FK" />
                <ScalarProperty Name="PaymentTermPaFk" ColumnName="BAS_PAYMENT_TERM_PA_FK" />
                <ScalarProperty Name="TaxCodeFk" ColumnName="MDC_TAX_CODE_FK" />
                <ScalarProperty Name="ClerkPrcFk" ColumnName="BAS_CLERK_PRC_FK" />
                <ScalarProperty Name="ClerkReqFk" ColumnName="BAS_CLERK_REQ_FK" />
                <ScalarProperty Name="PrcHeaderId" ColumnName="PRC_HEADER_FK" />
                <ScalarProperty Name="StatusIsVirtual" ColumnName="ISVIRTUAL" />
                <ScalarProperty Name="StatusIsReported" ColumnName="ISREPORTED" />
                <ScalarProperty Name="StatusIsCanceled" ColumnName="ISCANCELED" />
                <ScalarProperty Name="StatusIsDelivered" ColumnName="ISDELIVERED" />
                <ScalarProperty Name="StatusIsReadonly" ColumnName="ISREADONLY" />
                <ScalarProperty Name="StatusIsInvoiced" ColumnName="ISINVOICED" />
                <ScalarProperty Name="StatusIsOrdered" ColumnName="ISORDERED" />
                <ScalarProperty Name="ConStatusFk" ColumnName="CONSTATUSFK" />
                <ScalarProperty Name="PrcConfigHeaderFk" ColumnName="PRC_CONFIGHEADER_FK" />
                <ScalarProperty Name="Icon" ColumnName="ICON" />
                <ScalarProperty Name="SearchPattern" ColumnName="SEARCH_PATTERN" />
                <ScalarProperty Name="CurrencyFk" ColumnName="BAS_CURRENCY_FK" />
                <ScalarProperty Name="CodeQuotation" ColumnName="CODE_QUOTATION" />
                <ScalarProperty Name="BusinessPartner2Fk" ColumnName="BPD_BUSINESSPARTNER2_FK" />
                <ScalarProperty Name="Bp2Name1" ColumnName="BP2_NAME1" />
                <ScalarProperty Name="Bp2Name2" ColumnName="BP2_NAME2" />
                <ScalarProperty Name="Supplier2Code" ColumnName="SUPPLIER2_CODE" />
                <ScalarProperty Name="MdcBillingSchemaFk" ColumnName="MDC_BILLING_SCHEMA_FK" />
                <ScalarProperty Name="Exchangerate" ColumnName="EXCHANGERATE" />
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="ProjectChangeFk" ColumnName="PRJ_CHANGE_FK" />
                <ScalarProperty Name="BpdSubsidiaryFk" ColumnName="BPD_SUBSIDIARY_FK" />
                <ScalarProperty Name="BpdSupplierFk" ColumnName="BPD_SUPPLIER_FK" />
                <ScalarProperty Name="ExternalCode" ColumnName="EXTERNAL_CODE" />
                <ScalarProperty Name="PrcPackage2HeaderFk" ColumnName="PRC_PACKAGE2HEADER_FK" />
                <ScalarProperty Name="PrcCopyModeFk" ColumnName="PRC_COPYMODE_FK" />
                <ScalarProperty Name="BpdVatGroupFk" ColumnName="BPD_VATGROUP_FK" />
                <ScalarProperty Name="IsFreeItemsAllowed" ColumnName="ISFREEITEMSALLOWED" />
                <ScalarProperty Name="PrcConfigurationFk" ColumnName="PRC_CONFIGURATION_FK" />
                <ScalarProperty Name="SalesTaxMethodFk" ColumnName="BAS_SALES_TAX_METHOD_FK" />
                <ScalarProperty Name="IsFramework" ColumnName="ISFRAMEWORK" />
                <ScalarProperty Name="BpdContactFk" ColumnName="BPD_CONTACT_FK" />
                <ScalarProperty Name="StatusIsLive" ColumnName="ISLIVE" />
                <ScalarProperty Name="BankFk" ColumnName="BPD_BANK_FK" />
                <ScalarProperty Name="StatusIsRejected" ColumnName="ISREJECTED" />
                <ScalarProperty Name="MdcMaterialCatalogFk" ColumnName="MDC_MATERIAL_CATALOG_FK" />
                <ScalarProperty Name="BoqWicCatFk" ColumnName="BOQ_WIC_CAT_FK" />
                <ScalarProperty Name="BasLanguageFk" ColumnName="BAS_LANGUAGE_FK" />
                <ComplexProperty Name="StatusDescriptionInfo" TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType">
                  <ScalarProperty Name="DescriptionTr" ColumnName="STATUSDESCRIPTIONTR" />
                  <ScalarProperty Name="Description" ColumnName="STATUSDESCRIPTION" />
                </ComplexProperty>
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="TranslationEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.TranslationEntity">
              <MappingFragment StoreEntitySet="BAS_TRANSLATIONs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="BasLanguageFk" ColumnName="BAS_LANGUAGE_FK" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConHeader2prjMaterialVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeader2prjMaterialVEntity">
              <MappingFragment StoreEntitySet="CON_HEADER2PRJ_MATERIAL_Vs">
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="PrjProjectFk" ColumnName="PRJ_PROJECT_FK" />
                <ScalarProperty Name="BasCompanyFk" ColumnName="BAS_COMPANY_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConStatus2externalEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConStatus2externalEntity">
              <MappingFragment StoreEntitySet="CON_STATUS2EXTERNALs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ConStatusFk" ColumnName="CON_STATUS_FK" />
                <ScalarProperty Name="BasExternalsourceFk" ColumnName="BAS_EXTERNALSOURCE_FK" />
                <ScalarProperty Name="ExtCode" ColumnName="EXT_CODE" />
                <ScalarProperty Name="ExtDescription" ColumnName="EXT_DESCRIPTION" />
                <ScalarProperty Name="CommentText" ColumnName="COMMENT_TEXT" />
                <ScalarProperty Name="Sorting" ColumnName="SORTING" />
                <ScalarProperty Name="Isdefault" ColumnName="ISDEFAULT" />
                <ScalarProperty Name="Islive" ColumnName="ISLIVE" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConHeaderUserAccessVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderUserAccessVEntity">
              <MappingFragment StoreEntitySet="CON_HEADERUSERACCESS_Vs">
                <ScalarProperty Name="UserId" ColumnName="USERID" />
                <ScalarProperty Name="Id" ColumnName="ID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConHeader2customerEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeader2customerEntity">
              <MappingFragment StoreEntitySet="CON_HEADER2CUSTOMERs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="BpdBusinesspartnerFk" ColumnName="BPD_BUSINESSPARTNER_FK" />
                <ScalarProperty Name="BpdSubsidiaryFk" ColumnName="BPD_SUBSIDIARY_FK" />
                <ScalarProperty Name="BpdCustomerFk" ColumnName="BPD_CUSTOMER_FK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="DdTempIdsEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.DdTempIdsEntity">
              <MappingFragment StoreEntitySet="BAS_DDTEMPIDS">
                <ScalarProperty Name="RequestId" ColumnName="REQUESTID" />
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Key1" ColumnName="KEY1" />
                <ScalarProperty Name="Key2" ColumnName="KEY2" />
                <ScalarProperty Name="Key3" ColumnName="KEY3" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConAccountAssignmentEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConAccountAssignmentEntity">
              <MappingFragment StoreEntitySet="CON_ACCOUNT_ASSIGNMENTs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ItemNO" ColumnName="ITEMNO" />
                <ScalarProperty Name="BreakdownPercent" ColumnName="BREAKDOWN_PERCENT" />
                <ScalarProperty Name="BreakdownAmount" ColumnName="BREAKDOWN_AMOUNT" />
                <ScalarProperty Name="BasCompanyYearFk" ColumnName="BAS_COMPANY_YEAR_FK" />
                <ScalarProperty Name="MdcControllingUnitFk" ColumnName="MDC_CONTROLLINGUNIT_FK" />
                <ScalarProperty Name="PsdScheduleFk" ColumnName="PSD_SCHEDULE_FK" />
                <ScalarProperty Name="PsdActivityFk" ColumnName="PSD_ACTIVITY_FK" />
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="Remark" ColumnName="REMARK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="ConCrewFk" ColumnName="CON_CREW_FK" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="Quantity" ColumnName="QUANTITY" />
                <ScalarProperty Name="BasAccountFk" ColumnName="BAS_ACCOUNT_FK" />
                <ScalarProperty Name="IsDelete" ColumnName="ISDELETE" />
                <ScalarProperty Name="IsFinalInvoice" ColumnName="ISFINALINVOICE" />
                <ScalarProperty Name="BreakdownAmountOc" ColumnName="BREAKDOWN_AMOUNT_OC" />
                <ScalarProperty Name="BasUomFk" ColumnName="BAS_UOM_FK" />
                <ScalarProperty Name="DateDelivery" ColumnName="DATE_DELIVERY" />
                <ScalarProperty Name="AccountAssignment01" ColumnName="ACCOUNT_ASSIGNMENT01" />
                <ScalarProperty Name="AccountAssignment02" ColumnName="ACCOUNT_ASSIGNMENT02" />
                <ScalarProperty Name="BasAccAssignItemTypeFk" ColumnName="BAS_ACCASSIGN_ITEMTYPE_FK" />
                <ScalarProperty Name="BasAccAssignMatGroupFk" ColumnName="BAS_ACCASSIGN_MAT_GROUP_FK" />
                <ScalarProperty Name="BasAccAssignAccTypeFk" ColumnName="BAS_ACCASSIGN_ACC_TYPE_FK" />
                <ScalarProperty Name="AccountAssignment03" ColumnName="ACCOUNT_ASSIGNMENT03" />
                <ScalarProperty Name="BasAccAssignFactoryFk" ColumnName="BAS_ACCASSIGN_FACTORY_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConHeader2MdlObjectVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeader2MdlObjectVEntity">
              <MappingFragment StoreEntitySet="CON_HEADER2MDL_OBJECT_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ModelFk" ColumnName="MDL_MODEL_FK" />
                <ScalarProperty Name="ObjectFk" ColumnName="MDL_OBJECT_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConCrewEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConCrewEntity">
              <MappingFragment StoreEntitySet="CON_CREWs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="BpdContactFk" ColumnName="BPD_CONTACT_FK" />
                <ScalarProperty Name="Sorting" ColumnName="SORTING" />
                <ScalarProperty Name="IsDefault" ColumnName="ISDEFAULT" />
                <ScalarProperty Name="IsLive" ColumnName="ISLIVE" />
                <ScalarProperty Name="UserDefined1" ColumnName="USERDEFINED1" />
                <ScalarProperty Name="UserDefined2" ColumnName="USERDEFINED2" />
                <ScalarProperty Name="UserDefined3" ColumnName="USERDEFINED3" />
                <ScalarProperty Name="UserDefined4" ColumnName="USERDEFINED4" />
                <ScalarProperty Name="UserDefined5" ColumnName="USERDEFINED5" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ComplexProperty Name="DescriptionInfo" TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType">
                  <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                  <ScalarProperty Name="DescriptionTr" ColumnName="DESCRIPTION_TR" />
                </ComplexProperty>
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConMasterRestrictionEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConMasterRestrictionEntity">
              <MappingFragment StoreEntitySet="CON_MASTERRESTRICTIONs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="MdcMaterialCatalogFk" ColumnName="MDC_MATERIAL_CATALOG_FK" />
                <ScalarProperty Name="BoqWicCatFk" ColumnName="BOQ_WIC_CAT_FK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="BoqHeaderFk" ColumnName="BOQ_HEADER_FK" />
                <ScalarProperty Name="ConHeaderBoqFk" ColumnName="CON_HEADER_BOQ_FK" />
                <ScalarProperty Name="Visibility" ColumnName="VISIBILITY" />
                <ScalarProperty Name="PackageFk" ColumnName="PRC_PACKAGE_FK" />
                <ScalarProperty Name="ProjectFk" ColumnName="PRJ_PROJECT_FK" />
                <ScalarProperty Name="PrjBoqFk" ColumnName="PRJ_BOQ_FK" />
                <ScalarProperty Name="CopyType" ColumnName="COPY_TYPE" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="PrcCopyModeEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.PrcCopyModeEntity">
              <MappingFragment StoreEntitySet="PRC_COPYMODEs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="Sorting" ColumnName="SORTING" />
                <ScalarProperty Name="IsDefault" ColumnName="ISDEFAULT" />
                <ScalarProperty Name="IsLive" ColumnName="ISLIVE" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ComplexProperty Name="DescriptionInfo" TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.DescriptionTranslateType">
                  <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                  <ScalarProperty Name="DescriptionTr" ColumnName="DESCRIPTION_TR" />
                </ComplexProperty>
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConAdvanceEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConAdvanceEntity">
              <MappingFragment StoreEntitySet="CON_ADVANCEs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="PrcAdvanceTypeFk" ColumnName="PRC_ADVANCETYPE_FK" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="DateDue" ColumnName="DATE_DUE" />
                <ScalarProperty Name="AmountDue" ColumnName="AMOUNT_DUE" />
                <ScalarProperty Name="PercentProrata" ColumnName="PERCENT_PRORATA" />
                <ScalarProperty Name="DateDone" ColumnName="DATE_DONE" />
                <ScalarProperty Name="AmountDone" ColumnName="AMOUNT_DONE" />
                <ScalarProperty Name="CommentText" ColumnName="COMMENT_TEXT" />
                <ScalarProperty Name="Userdefined1" ColumnName="USERDEFINED1" />
                <ScalarProperty Name="Userdefined2" ColumnName="USERDEFINED2" />
                <ScalarProperty Name="Userdefined3" ColumnName="USERDEFINED3" />
                <ScalarProperty Name="Userdefined4" ColumnName="USERDEFINED4" />
                <ScalarProperty Name="Userdefined5" ColumnName="USERDEFINED5" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="AmountDueOc" ColumnName="AMOUNT_DUE_OC" />
                <ScalarProperty Name="AmountDoneOc" ColumnName="AMOUNT_DONE_OC" />
                <ScalarProperty Name="PaymentTermFk" ColumnName="BAS_PAYMENT_TERM_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConTransactionEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConTransactionEntity">
              <MappingFragment StoreEntitySet="CON_TRANSACTIONs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="TransactionId" ColumnName="TRANSACTION_ID" />
                <ScalarProperty Name="CompanyFk" ColumnName="BAS_COMPANY_FK" />
                <ScalarProperty Name="CompanyInvoiceFk" ColumnName="BAS_COMPANY_INVOICE_FK" />
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="PrcConfigurationFk" ColumnName="PRC_CONFIGURATION_FK" />
                <ScalarProperty Name="Isconsolidated" ColumnName="ISCONSOLIDATED" />
                <ScalarProperty Name="Ischange" ColumnName="ISCHANGE" />
                <ScalarProperty Name="Currency" ColumnName="CURRENCY" />
                <ScalarProperty Name="ConStatusFk" ColumnName="CON_STATUS_FK" />
                <ScalarProperty Name="Code" ColumnName="CODE" />
                <ScalarProperty Name="Description" ColumnName="DESCRIPTION" />
                <ScalarProperty Name="DateOrdered" ColumnName="DATE_ORDERED" />
                <ScalarProperty Name="DateEffective" ColumnName="DATE_EFFECTIVE" />
                <ScalarProperty Name="DateReported" ColumnName="DATE_REPORTED" />
                <ScalarProperty Name="BusinessPartnerFk" ColumnName="BPD_BUSINESSPARTNER_FK" />
                <ScalarProperty Name="SubsidiaryFk" ColumnName="BPD_SUBSIDIARY_FK" />
                <ScalarProperty Name="SupplierFk" ColumnName="BPD_SUPPLIER_FK" />
                <ScalarProperty Name="ContactFk" ColumnName="BPD_CONTACT_FK" />
                <ScalarProperty Name="BankFk" ColumnName="BPD_BANK_FK" />
                <ScalarProperty Name="VatGroupFk" ColumnName="BPD_VATGROUP_FK" />
                <ScalarProperty Name="PaymentTermFiFk" ColumnName="BAS_PAYMENT_TERM_FI_FK" />
                <ScalarProperty Name="PaymentTermPaFk" ColumnName="BAS_PAYMENT_TERM_PA_FK" />
                <ScalarProperty Name="PaymentTermAdFk" ColumnName="BAS_PAYMENT_TERM_AD_FK" />
                <ScalarProperty Name="Incoterm" ColumnName="INCOTERM" />
                <ScalarProperty Name="DateDelivery" ColumnName="DATE_DELIVERY" />
                <ScalarProperty Name="AddressFk" ColumnName="BAS_ADDRESS_FK" />
                <ScalarProperty Name="ItemReference" ColumnName="ITEM_REFERENCE" />
                <ScalarProperty Name="Amount" ColumnName="AMOUNT" />
                <ScalarProperty Name="VatAmount" ColumnName="VAT_AMOUNT" />
                <ScalarProperty Name="AmountOc" ColumnName="AMOUNT_OC" />
                <ScalarProperty Name="VatAmountOc" ColumnName="VAT_AMOUNT_OC" />
                <ScalarProperty Name="Quantity" ColumnName="QUANTITY" />
                <ScalarProperty Name="Price" ColumnName="PRICE" />
                <ScalarProperty Name="VatPrice" ColumnName="VAT_PRICE" />
                <ScalarProperty Name="PriceOc" ColumnName="PRICE_OC" />
                <ScalarProperty Name="VatPriceOc" ColumnName="VAT_PRICE_OC" />
                <ScalarProperty Name="NominalAccount" ColumnName="NOMINAL_ACCOUNT" />
                <ScalarProperty Name="NominalDimension1" ColumnName="NOMINAL_DIMENSION1" />
                <ScalarProperty Name="NominalDimension2" ColumnName="NOMINAL_DIMENSION2" />
                <ScalarProperty Name="NominalDimension3" ColumnName="NOMINAL_DIMENSION3" />
                <ScalarProperty Name="TaxCodeFk" ColumnName="MDC_TAX_CODE_FK" />
                <ScalarProperty Name="TaxCodeMatrixFk" ColumnName="MDC_TAX_CODE_MATRIX_FK" />
                <ScalarProperty Name="VatCode" ColumnName="VAT_CODE" />
                <ScalarProperty Name="ControllingUnitFk" ColumnName="MDC_CONTROLLINGUNIT_FK" />
                <ScalarProperty Name="ControllingUnitCode" ColumnName="CONTROLLINGUNIT_CODE" />
                <ScalarProperty Name="ControllingUnitAssign01" ColumnName="CONTROLLINGUNIT_ASSIGN01" />
                <ScalarProperty Name="ControllingunitAssign01desc" ColumnName="CONTROLLINGUNIT_ASSIGN01DESC" />
                <ScalarProperty Name="ControllingUnitAssign02" ColumnName="CONTROLLINGUNIT_ASSIGN02" />
                <ScalarProperty Name="ControllingunitAssign02desc" ColumnName="CONTROLLINGUNIT_ASSIGN02DESC" />
                <ScalarProperty Name="ControllingUnitAssign03" ColumnName="CONTROLLINGUNIT_ASSIGN03" />
                <ScalarProperty Name="ControllingunitAssign03desc" ColumnName="CONTROLLINGUNIT_ASSIGN03DESC" />
                <ScalarProperty Name="ControllingUnitAssign04" ColumnName="CONTROLLINGUNIT_ASSIGN04" />
                <ScalarProperty Name="ControllingunitAssign04desc" ColumnName="CONTROLLINGUNIT_ASSIGN04DESC" />
                <ScalarProperty Name="ControllingUnitAssign05" ColumnName="CONTROLLINGUNIT_ASSIGN05" />
                <ScalarProperty Name="ControllingunitAssign05desc" ColumnName="CONTROLLINGUNIT_ASSIGN05DESC" />
                <ScalarProperty Name="ControllingUnitAssign06" ColumnName="CONTROLLINGUNIT_ASSIGN06" />
                <ScalarProperty Name="ControllingunitAssign06desc" ColumnName="CONTROLLINGUNIT_ASSIGN06DESC" />
                <ScalarProperty Name="ControllingUnitAssign07" ColumnName="CONTROLLINGUNIT_ASSIGN07" />
                <ScalarProperty Name="ControllingunitAssign07desc" ColumnName="CONTROLLINGUNIT_ASSIGN07DESC" />
                <ScalarProperty Name="ControllingUnitAssign08" ColumnName="CONTROLLINGUNIT_ASSIGN08" />
                <ScalarProperty Name="ControllingunitAssign08desc" ColumnName="CONTROLLINGUNIT_ASSIGN08DESC" />
                <ScalarProperty Name="ControllingUnitAssign09" ColumnName="CONTROLLINGUNIT_ASSIGN09" />
                <ScalarProperty Name="ControllingunitAssign09desc" ColumnName="CONTROLLINGUNIT_ASSIGN09DESC" />
                <ScalarProperty Name="ControllingUnitAssign10" ColumnName="CONTROLLINGUNIT_ASSIGN10" />
                <ScalarProperty Name="ControllingunitAssign10desc" ColumnName="CONTROLLINGUNIT_ASSIGN10DESC" />
                <ScalarProperty Name="PrcItemFk" ColumnName="PRC_ITEM_FK" />
                <ScalarProperty Name="MaterialFk" ColumnName="MDC_MATERIAL_FK" />
                <ScalarProperty Name="ItemDescription1" ColumnName="ITEM_DESCRIPTION1" />
                <ScalarProperty Name="ItemDescription2" ColumnName="ITEM_DESCRIPTION2" />
                <ScalarProperty Name="ItemSpecification" ColumnName="ITEM_SPECIFICATION" />
                <ScalarProperty Name="ItemUomQuantity" ColumnName="ITEM_UOMQUANTITY" />
                <ScalarProperty Name="IsSuccess" ColumnName="ISSUCCESS" />
                <ScalarProperty Name="HandoverId" ColumnName="HANDOVER_ID" />
                <ScalarProperty Name="ReturnValue" ColumnName="RETURN_VALUE" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="IncAmount" ColumnName="INC_AMOUNT" />
                <ScalarProperty Name="IncVatAmount" ColumnName="INC_VAT_AMOUNT" />
                <ScalarProperty Name="IncAmountOc" ColumnName="INC_AMOUNT_OC" />
                <ScalarProperty Name="IncVatAmountOc" ColumnName="INC_VAT_AMOUNT_OC" />
                <ScalarProperty Name="IncQuantity" ColumnName="INC_QUANTITY" />
                <ScalarProperty Name="VatPercent" ColumnName="VATPERCENT" />
                <ScalarProperty Name="Orderno" ColumnName="ORDERNO" />
                <ScalarProperty Name="Userdefined1" ColumnName="USERDEFINED1" />
                <ScalarProperty Name="Userdefined2" ColumnName="USERDEFINED2" />
                <ScalarProperty Name="Userdefined3" ColumnName="USERDEFINED3" />
                <ScalarProperty Name="IncotermCode" ColumnName="INCOTERM_CODE" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConHeaderApprovalEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderApprovalEntity">
              <MappingFragment StoreEntitySet="CON_HEADERAPPROVALs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="IsApproved" ColumnName="ISAPPROVED" />
                <ScalarProperty Name="Comment" ColumnName="COMMENT" />
                <ScalarProperty Name="ClerkFk" ColumnName="BAS_CLERK_FK" />
                <ScalarProperty Name="ClerkRoleFk" ColumnName="BAS_CLERK_ROLE_FK" />
                <ScalarProperty Name="DueDate" ColumnName="DUEDATE" />
                <ScalarProperty Name="EvaluatedOn" ColumnName="EVALUATEDON" />
                <ScalarProperty Name="EvaluationLevel" ColumnName="EVALUATIONLEVEL" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="HeaderFk" ColumnName="CON_HEADER_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConHeaderItwoFinanceVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderItwoFinanceVEntity">
              <MappingFragment StoreEntitySet="CON_HEADER_ITWOFINANCE_Vs">
                <ScalarProperty Name="ConHeaderId" ColumnName="CON_HEADER_ID" />
                <ScalarProperty Name="ConHeaderConHeaderFk" ColumnName="CON_HEADER_CON_HEADER_FK" />
                <ScalarProperty Name="ConHeaderCode" ColumnName="CON_HEADER_CODE" />
                <ScalarProperty Name="ConHeaderDescription" ColumnName="CON_HEADER_DESCRIPTION" />
                <ScalarProperty Name="ConHeaderDateOrdered" ColumnName="CON_HEADER_DATE_ORDERED" />
                <ScalarProperty Name="ConTotalValueNet" ColumnName="CON_TOTAL_VALUE_NET" />
                <ScalarProperty Name="ConTotalValueGross" ColumnName="CON_TOTAL_VALUE_GROSS" />
                <ScalarProperty Name="ConStatusCode" ColumnName="CON_STATUS_CODE" />
                <ScalarProperty Name="ConStatusDescription" ColumnName="CON_STATUS_DESCRIPTION" />
                <ScalarProperty Name="PrjProjectNo" ColumnName="PRJ_PROJECT_PROJECTNO" />
                <ScalarProperty Name="BpdSupplierCode" ColumnName="BPD_SUPPLIER_CODE" />
                <ScalarProperty Name="BpdVatgroupReference" ColumnName="BPD_VATGROUP_REFERENCE" />
                <ScalarProperty Name="ConHeaderMdcTaxCodeFk" ColumnName="CON_HEADER_MDC_TAX_CODE_FK" />
                <ScalarProperty Name="ConHeaderBpdVatgroupFk" ColumnName="CON_HEADER_BPD_VATGROUP_FK" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConHeader2BoqWicCatBoqEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeader2BoqWicCatBoqEntity">
              <MappingFragment StoreEntitySet="CON_HEADER2BOQ_WIC_CAT_BOQs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="ConHeaderFk" ColumnName="CON_HEADER_FK" />
                <ScalarProperty Name="BoqHeaderFk" ColumnName="BOQ_HEADER_FK" />
                <ScalarProperty Name="BoqWicCatBoqFk" ColumnName="BOQ_WIC_CAT_BOQ_FK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ConHeaderExtendedVEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderExtendedVEntity">
              <MappingFragment StoreEntitySet="CON_HEADEREXT_Vs">
                <ScalarProperty Name="Id" ColumnName="ID" />
                <ScalarProperty Name="RubricCategoryFk" ColumnName="BAS_RUBRIC_CATEGORY_FK" />
                <ScalarProperty Name="VatPercent" ColumnName="VATPERCENT" />
                <ScalarProperty Name="BaselinePath" ColumnName="BASELINE_PATH" />
                <ScalarProperty Name="ConHeaderMatFk" ColumnName="CON_HEADERMAT_FK" />
                <ScalarProperty Name="ConHeaderBoqCatFk" ColumnName="CON_HEADERBOQCAT_FK" />
                <ScalarProperty Name="PackageMaxBoqFk" ColumnName="PRC_PACKAGEMAXBOQ_FK" />
                <ScalarProperty Name="PostedConHeaderFk" ColumnName="POSTED_CON_HEADER_FK" />
                <ScalarProperty Name="AccassignConTypeFk" ColumnName="ACCASSIGN_CON_TYPE_FK" />
                <ScalarProperty Name="InsertedAt" ColumnName="INSERTED" />
                <ScalarProperty Name="InsertedBy" ColumnName="WHOISR" />
                <ScalarProperty Name="UpdatedAt" ColumnName="UPDATED" />
                <ScalarProperty Name="UpdatedBy" ColumnName="WHOUPD" />
                <ScalarProperty Name="Version" ColumnName="VERSION" />
                <ScalarProperty Name="PrjStatusFk" ColumnName="PRJ_STATUS_FK" />
                <ScalarProperty Name="IsStatusReadonly" ColumnName="IS_STATUS_READONLY" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="ContractUserAccessCacheEntities">
            <EntityTypeMapping TypeName="RIB.Visual.Procurement.Contract.BusinessComponents.ContractUserAccessCacheEntity">
              <MappingFragment StoreEntitySet="PRC_CONTRACTUSERACCESSCACHEs">
                <ScalarProperty Name="UserId" ColumnName="USERID" />
                <ScalarProperty Name="CompanySignedInId" ColumnName="COMPANYSIGNEDINID" />
                <ScalarProperty Name="AccessRoleId" ColumnName="ACCESSROLEID" />
                <ScalarProperty Name="FromId" ColumnName="FROMID" />
                <ScalarProperty Name="ToId" ColumnName="TOID" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <edmx:Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </edmx:Connection>
    <edmx:Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
      </DesignerInfoPropertySet>
    </edmx:Options>
    <edmx:Diagrams>
      <Diagram Name="Main" />
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>