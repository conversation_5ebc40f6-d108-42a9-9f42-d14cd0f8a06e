import { _boqPage, _common, _commonAPI, _controllingUnit, _estimatePage, _mainView, _modalView, _package, _procurementConfig, _procurementPage, _projectPage, _sidebar, _validate } from "cypress/pages";
import { app, tile, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import { DataCells } from "cypress/pages/interfaces";
import CommonLocators from "cypress/locators/common-locators";
import { BOQ_HEADER, BOQ_ROOT_ITEM } from "cypress/pages/variables";
import apiConstantData from "cypress/constantData/apiConstantData";

const ESTIMATE_CODE = '1' + Cypress._.random(0, 999);
const LINE_ITEM_DESCRIPTION = 'LI-DESC-' + Cypress._.random(0, 999);
const BOQ_DESC = "BOQ-DESC-" + Cypress._.random(0, 999);
const BOQ_STRUCTURE_DESC = "BOQ-STR-" + Cypress._.random(0, 999);
const PES_DESC=_common.generateRandomString(3)

let PROJECTS_PARAMETERS: DataCells;
let ESTIMATE_PARAMETERS: DataCells;
let CONTAINERS_ESTIMATE;
let CONTAINER_COLUMNS_ESTIMATE;
let GENERATE_LINE_ITEMS_PARAMETERS: DataCells
let LINE_ITEM_PARAMETERS: DataCells
let CONTAINERS_LINE_ITEM;
let CONTAINER_COLUMNS_LINE_ITEM;
let CONTAINER_COLUMNS_BOQ;
let RESOURCE_PARAMETERS: DataCells
let CONTAINERS_RESOURCE;
let CONTAINER_COLUMNS_RESOURCE;
let CONTAINERS_BOQ_STRUCTURE
let CONTAINER_COLUMNS_PESBOQ
let CONTAINER_COLUMNS_BOQ_STRUCTURE;
let BOQ_PARAMETERS: DataCells
let BOQ_STRUCTURE_PARAMETERS: DataCells
let CONTAINER_COLUMNS_PACKAGE
let MODAL_CREATE_UPDATE_BOQ_PACKAGE
let MODAL_CREATE_CONTRACT
let CONTAINER_COLUMNS_CONTROLLING_UNIT
let CONTAINERS_CONTROLLING_UNIT
let CONTROLLING_UNIT_PARAMETERS: DataCells
let CONTAINERS_CONTRACTS
let MODAL_UPDATE_LINE_ITEM_QUANTITIES
let MODAL_UPDATE_LINE_ITEM_QUANTITIES_PARAMETERS
let CONTAINER_COLUMNS_LINE_ITEM_QUANTITY

describe("EST- 3.3 | Update line item quantities from PES", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

  before(function () {
    cy.preLoading(
      Cypress.env('adminUserName'),
      Cypress.env('adminPassword'),
      Cypress.env('parentCompanyName'),
      Cypress.env('childCompanyName')
    );
    cy.fixture("estimate/est-3.3-Update-Line-item-quantities-From-PES.json")
      .then((data) => {
        this.data = data;

        CONTAINERS_ESTIMATE = this.data.CONTAINERS.ESTIMATE;
        CONTAINER_COLUMNS_ESTIMATE = this.data.CONTAINER_COLUMNS.ESTIMATE
      
        CONTAINER_COLUMNS_PACKAGE = this.data.CONTAINER_COLUMNS.PACKAGE
        CONTAINERS_LINE_ITEM = this.data.CONTAINERS.LINE_ITEM
        CONTAINER_COLUMNS_LINE_ITEM = this.data.CONTAINER_COLUMNS.LINE_ITEM
        LINE_ITEM_PARAMETERS = {
          [app.GridCells.DESCRIPTION_INFO]: LINE_ITEM_DESCRIPTION,
          [app.GridCells.QUANTITY_SMALL]: CONTAINERS_LINE_ITEM.QUANTITY,
          [app.GridCells.BAS_UOM_FK]: CONTAINERS_LINE_ITEM.UOM,
        }

        CONTAINERS_RESOURCE = this.data.CONTAINERS.RESOURCE
        CONTAINER_COLUMNS_RESOURCE = this.data.CONTAINER_COLUMNS.RESOURCE
        RESOURCE_PARAMETERS = {
          [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY,
          [app.GridCells.CODE]: CONTAINERS_RESOURCE.CODE,
        };

        CONTAINER_COLUMNS_BOQ = this.data.CONTAINER_COLUMNS.BOQ
        BOQ_PARAMETERS = {
          [app.GridCells.BRIEF_INFO_SMALL]: BOQ_DESC
        }
        CONTAINER_COLUMNS_PESBOQ = this.data.CONTAINER_COLUMNS.PESBOQ
        CONTAINERS_BOQ_STRUCTURE = this.data.CONTAINERS.BOQ_STRUCTURE


        CONTAINER_COLUMNS_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.BOQ_STRUCTURE
        MODAL_CREATE_UPDATE_BOQ_PACKAGE = this.data.MODAL.CREATE_UPDATE_BOQ_PACKAGE
        MODAL_CREATE_CONTRACT = this.data.MODAL.CREATE_CONTRACT

        CONTAINER_COLUMNS_CONTROLLING_UNIT = this.data.CONTAINER_COLUMNS.CONTROLLING_UNIT
        CONTAINERS_CONTROLLING_UNIT = this.data.CONTAINERS.CONTROLLING_UNIT

        CONTAINERS_CONTRACTS = this.data.CONTAINERS.CONTRACTS
        MODAL_UPDATE_LINE_ITEM_QUANTITIES = this.data.MODAL.UPDATE_LINE_ITEM_QUANTITIES
        MODAL_UPDATE_LINE_ITEM_QUANTITIES_PARAMETERS = {
          [commonLocators.CommonLabels.TYPE]: [commonLocators.CommonLabels.SELECT_ESTIMATE_SCOPE, commonLocators.CommonLabels.IQ_QUANTITY_UPDATE],
          [commonLocators.CommonLabels.SELECT_ESTIMATE_SCOPE]: MODAL_UPDATE_LINE_ITEM_QUANTITIES.SELECT_ESTIMATE_SCOPE,
          [commonLocators.CommonLabels.IQ_QUANTITY_UPDATE]: MODAL_UPDATE_LINE_ITEM_QUANTITIES.IQ_QUANTITY_UPDATE
        }

        CONTAINER_COLUMNS_LINE_ITEM_QUANTITY = this.data.CONTAINER_COLUMNS.LINE_ITEM_QUANTITY
      }).then(() => {
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
        _commonAPI.getAccessToken().then((result) => {
          cy.log(`Token Retrieved: ${result.token}`);
        });
      });
  });

  after(() => {
    cy.LOGOUT();
  })

  it('TC - API: Create project', function () {
    _commonAPI.createProject()
      .then(() => {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
      });
  })

  it('TC - API: Create controlling units', function () {

    CONTROLLING_UNIT_PARAMETERS = {
      [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNIT.QUANTITY, CONTAINERS_CONTROLLING_UNIT.QUANTITY],
      [app.GridCells.UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS]
    }
    _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 3, CONTROLLING_UNIT_PARAMETERS)
  })

  it('TC - API: Create BoQ header and BoQ structure', function () {
    let BOQ_API_PARAMETERS: DataCells = {
      [app.GridCells.BRIEF_INFO_SMALL]: BOQ_DESC,
      [app.GridCells.BRIEF_INFO]: BOQ_STRUCTURE_DESC,
      [app.GridCells.QUANTITY_SMALL]: CONTAINERS_BOQ_STRUCTURE.QUANTITY[0],
      [app.GridCells.PRICE_SMALL]: CONTAINERS_BOQ_STRUCTURE.UNIT_RATE,
      [app.GridCells.BAS_UOM_FK]: CONTAINERS_BOQ_STRUCTURE.UOM,
      [app.GridCells.PROJECT_CODE]: Cypress.env('API_PROJECT_NUMBER_1'),
    };

    _commonAPI.createBoQHeaderWithItems(BOQ_API_PARAMETERS);
    _commonAPI.getBoQHeaderList(Cypress.env('API_PROJECT_ID_1')).then(() => {
      const boqIds = Cypress.env('API_BOQ_HEADER_ID');
      Cypress.env(`BOQ_HEADER_ID`, boqIds[0])
    });
  });

  it('TC - API: Create estimate header', function () {
    _commonAPI.createEstimateHeader(Cypress.env('API_PROJECT_ID_1'));
  });

  it('TC - API: Generate boq line item', function () {
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
    });
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
    _common.search_inSubContainer(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));
    _common.select_rowHasValue(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));
    _common.waitForLoaderToDisappear();

    _commonAPI.generateBOQFromLeadingStructure(Cypress.env(`API_EST_ID_1`), Cypress.env(`BOQ_HEADER_ID`), Cypress.env('API_PROJECT_ID_1'));

    _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
    _common.waitForLoaderToDisappear();

    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
    });
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
    _common.clear_searchInSidebar()
    _common.waitForLoaderToDisappear();

    cy.REFRESH_CONTAINER();
    _common.waitForLoaderToDisappear();
    _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC);
  });

  it("TC - Create new record in resource", function () {
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
      _common.setup_gridLayout(cnt.uuid.RESOURCES, CONTAINER_COLUMNS_RESOURCE)
    });
    _common.maximizeContainer(cnt.uuid.RESOURCES)
    _common.clear_subContainerFilter(cnt.uuid.RESOURCES);
    _common.create_newRecord(cnt.uuid.RESOURCES);
    _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS);
    _common.minimizeContainer(cnt.uuid.RESOURCES)
    _common.waitForLoaderToDisappear()
    cy.SAVE();
    _common.waitForLoaderToDisappear()
  });

  it("TC - Create BOQ Package using wizard Create/Update BoQ Package and change package status", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_UPDATE_BOQ_PACKAGE);

    _common.waitForLoaderToDisappear();
    let BOQ_PACKAGE: DataCells = {
      [CommonLocators.CommonLabels.SELECT_ESTIMATE_SCOPE]: CommonLocators.CommonLabels.ENTIRE_ESTIMATE,
      [CommonLocators.CommonKeys.RADIO_INDEX]: "2",
      [CommonLocators.CommonLabels.SELECT_GROUPING_STRUCTURE_TO_CREATE_PACKAGE]: MODAL_CREATE_UPDATE_BOQ_PACKAGE.CREATE_UPDATE_PACKAGE_CASE[0],
      [CommonLocators.CommonLabels.CONSOLIDATE_TO_ONE_PACKAGE_FOR_ALL_SELECTED_CRITERIA]: commonLocators.CommonKeys.CHECK,
      [CommonLocators.CommonLabels.PROCUREMENT_STRUCTURE]: MODAL_CREATE_UPDATE_BOQ_PACKAGE.PROCUREMENT_STRUCTURE[0],
      [CommonLocators.CommonLabels.COLUMN_FILTER_SELECTION]: CommonLocators.CommonKeys.ALL
    }
    _package.enterRecord_toCreateBoQPackage_usingWizard_new(BOQ_PACKAGE)
    _common.clickOn_modalFooterButton(commonLocators.CommonLabels.FINISH)
    cy.wait(2000);
    _package.storePackageCode_fromModal()
    _common.clickOn_modalFooter_goToButton()
    cy.wait(2000);
    _common.waitForLoaderToDisappear()
    cy.wait(2000) // This wait required as UI takes time to load


    _common.openTab(app.TabBar.PACKAGE).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PACKAGE, app.FooterTab.PACKAGE, 0);
      _common.setup_gridLayout(cnt.uuid.PACKAGE, CONTAINER_COLUMNS_PACKAGE)
    });
    _common.clear_subContainerFilter(cnt.uuid.PACKAGE);
    _common.select_rowInContainer(cnt.uuid.PACKAGE);
    _package.changeStatus_ofPackage_inWizard();
  });

  it("TC - Create new contract record", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_CONTRACT);
    _common.waitForLoaderToDisappear()
    _package.create_ContractfromPackage(MODAL_CREATE_CONTRACT.BUSINESS_PARTNER);
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.CONTRACT).then(() => {
      _common.setDefaultView(app.TabBar.CONTRACT)
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS);
    })
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
  });

  it("TC - Assign controlling unit to contract", function () {
    _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
		_common.clear_searchInSidebar();  

    _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
    _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
    _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK)
    _common.waitForLoaderToDisappear()
    _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACTS.CLERK)
    _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK)
    _common.waitForLoaderToDisappear()
    _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_1`))
    _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.YES)
    _common.waitForLoaderToDisappear()
    cy.SAVE();
    _common.waitForLoaderToDisappear()
  });

  it("TC - Change contract status", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED);
    _common.waitForLoaderToDisappear()
  });

  it("TC - Create PES", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES);
    _common.waitForLoaderToDisappear()
    _procurementPage.enterRecord_toCreatePesFromWizard_ifDescriptionExists(PES_DESC)
    _common.waitForLoaderToDisappear()
    _procurementPage.getCode_fromPESModal("PES_CODE")
    _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_PES)
    _common.waitForLoaderToDisappear()
    _common.waitForLoaderToDisappear()
  });

  it("TC - Enter quantity in PES Item", function () {
    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.setDefaultView(app.TabBar.PERFORMANCEENTRYSHEET)
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS);
    })
    _common.clear_subContainerFilter(cnt.uuid.HEADERS)
    _common.search_inSubContainer(cnt.uuid.HEADERS, Cypress.env("PES_CODE"))
    _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE"))
    _common.openTab(app.TabBar.PESBOQ).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQs, 1);
    })
    _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
    _common.openTab(app.TabBar.PESBOQ).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE, 2);
      _common.setup_gridLayout(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINER_COLUMNS_PESBOQ)
    })
    _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.maximizeContainer(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.select_allContainerData(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.clickOn_expandCollapseButton(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
    _common.waitForLoaderToDisappear()
    _common.clickOn_cellHasUniqueValue(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.BRIEF_INFO_SMALL, BOQ_STRUCTURE_DESC)
    _common.enterRecord_inNewRow(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.QUANTITY[1])
    _common.select_activeRowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.minimizeContainer(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS);
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.ACCEPTION)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS)
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.DATE_RECEIVED);
    cy.SAVE()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS)
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.DELIVERED);
    cy.SAVE()
  });

  it('TC - Update line item quantities from wizards option', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
    _common.openTab(app.TabBar.PROJECT).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
    });
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
    });
    _common.waitForLoaderToDisappear()
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE)
    _common.waitForLoaderToDisappear()
    _common.filterCurrentEstimate(cnt.uuid.ESTIMATE, commonLocators.CommonKeys.NO_FILTER)
    _common.waitForLoaderToDisappear()
    _common.select_rowInContainer(cnt.uuid.ESTIMATE)

    _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
    });
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS)
    _common.search_inSubContainer(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC)
    _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC)
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_LINE_ITEM_QUANTITIES);
    _common.waitForLoaderToDisappear()
    _estimatePage.update_lineItem_fromWizard(MODAL_UPDATE_LINE_ITEM_QUANTITIES_PARAMETERS);
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.LINE_ITEM_QUANTITIES, app.FooterTab.LINE_ITEM_QUANTITY);
      cy.wait(1000)
      _common.setup_gridLayout(cnt.uuid.LINE_ITEM_QUANTITIES, CONTAINER_COLUMNS_LINE_ITEM_QUANTITY)
    });
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _common.clear_subContainerFilter(cnt.uuid.LINE_ITEM_QUANTITIES)
    _validate.verify_LineItemQuantities(CONTAINERS_BOQ_STRUCTURE.QUANTITY[1])
  });

  it('TC - Update PES status', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PES);
    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
    });
    _common.clear_subContainerFilter(cnt.uuid.HEADERS)
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
    _common.search_inSubContainer(cnt.uuid.HEADERS, Cypress.env("PES_CODE"))
    _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE"))
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS);
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.DATE_RECEIVED)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS)
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.ACCEPTION);
    cy.SAVE()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS)
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.NEW);
    cy.SAVE()
  })

  it("TC - Enter negative quantity in PES Item", function () {
    _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
      _common.setDefaultView(app.TabBar.PERFORMANCEENTRYSHEET)
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS);
    })
    _common.clear_subContainerFilter(cnt.uuid.HEADERS)
    _common.search_inSubContainer(cnt.uuid.HEADERS, Cypress.env("PES_CODE"))
    _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE"))
    _common.openTab(app.TabBar.PESBOQ).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQs, 1);
    })
    _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
    _common.openTab(app.TabBar.PESBOQ).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE, 2);
      _common.setup_gridLayout(cnt.uuid.PES_BOQS_STRUCTURE, CONTAINER_COLUMNS_PESBOQ)
    })
    _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.maximizeContainer(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.select_allContainerData(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.clickOn_expandCollapseButton(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
    _common.waitForLoaderToDisappear()
    _common.clickOn_cellHasUniqueValue(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.BRIEF_INFO_SMALL, BOQ_STRUCTURE_DESC)
    _common.enterRecord_inNewRow(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BOQ_STRUCTURE.QUANTITY[2])
    _common.select_activeRowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.minimizeContainer(cnt.uuid.PES_BOQS_STRUCTURE)
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS);
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.ACCEPTION)
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS)
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.DATE_RECEIVED);
    cy.SAVE()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS)
    _common.waitForLoaderToDisappear()
    _common.changeStatus_fromModal(commonLocators.CommonKeys.DELIVERED);
    cy.SAVE()
  });

  it('TC - Update line item quantities from wizards option', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT);
    _common.openTab(app.TabBar.PROJECT).then(() => {
      _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
    });
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
    });
    _common.waitForLoaderToDisappear()
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE)
    _common.waitForLoaderToDisappear()
    _common.filterCurrentEstimate(cnt.uuid.ESTIMATE, commonLocators.CommonKeys.NO_FILTER)
    _common.waitForLoaderToDisappear()
    _common.select_rowInContainer(cnt.uuid.ESTIMATE)
    _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
    });
    _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS)
    _common.search_inSubContainer(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC)
    _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, BOQ_STRUCTURE_DESC)
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_LINE_ITEM_QUANTITIES);
    _common.waitForLoaderToDisappear()
    _estimatePage.update_lineItem_fromWizard(MODAL_UPDATE_LINE_ITEM_QUANTITIES_PARAMETERS);
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.LINE_ITEM_QUANTITIES, app.FooterTab.LINE_ITEM_QUANTITY);
      _common.setup_gridLayout(cnt.uuid.LINE_ITEM_QUANTITIES, CONTAINER_COLUMNS_LINE_ITEM_QUANTITY)
    });
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _validate.verify_LineItemQuantities(CONTAINERS_BOQ_STRUCTURE.QUANTITY[2])
  });


});
