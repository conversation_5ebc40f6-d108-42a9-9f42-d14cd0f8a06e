import {_businessPartnerPage,_projectPage, _common, _controllingUnit, _package, _sidebar, _mainView, _validate, _modalView, _rfqPage, _saleContractPage, _procurementContractPage, _materialPage } from 'cypress/pages';
import { cnt, tile, app, sidebar, commonLocators, btn } from 'cypress/locators';
import common from 'mocha/lib/interfaces/common';
import type { DataCells } from 'cypress/pages/interfaces.d.ts'
import Buttons from 'cypress/locators/buttons';
import CommonLocators from 'cypress/locators/common-locators';
import { ContractPage } from 'cypress/pages/module/sales/contract/contractSales-page';

const PROJECT_NO = _common.generateRandomString(4);
const PROJECT_DESC=_common.generateRandomString(4);
const CHANGES_DESC =_common.generateRandomString(4);
const MAT_CODE = "CODE-"+_common.generateRandomString(4);
const MAT_DESC = _common.generateRandomString(4);
const SPEC_DESC = "SPECIFICATION" + _common.generateRandomString(4);
const SPEC_DESC1 = "SPEC-" + _common.generateRandomString(6);
const CONTRACT_DESC = "CONT-0"+_common.generateRandomString(4);

let CONTAINERS_PROJECT
let PROJECTS_PARAMETERS
let PROCUREMENT_CONTRACT_PARAMETER:DataCells
let CONTAINERS_CONTRACT
let CONTAINER_COLUMNS_CONTRACT
let MATERIAL_RECORD_PARAMETER:DataCells
let ITEM_PARAMETER:DataCells
let CONTAINER_COLUMNS_MATERIAL_RECORDS
let CONTAINERS_MATERIAL_RECORDS
let CONTAINER_COLUMNS_ITEMS


describe('PCM- 4.220 | Specification plain text-item in Contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('procurement-and-bpm/con-4.220-specification-plain-text-item-in-contract-module.json').then((data) => {
            this.data = data; 
            CONTAINERS_PROJECT=this.data.CONTAINERS.PROJECT            
            CONTAINER_COLUMNS_MATERIAL_RECORDS= this.data.CONTAINER_COLUMNS.MATERIAL_RECORDS
            CONTAINERS_MATERIAL_RECORDS= this.data.CONTAINERS.MATERIAL_RECORDS
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT  
            CONTAINER_COLUMNS_ITEMS = this.data.CONTAINER_COLUMNS.ITEMS          
            PROJECTS_PARAMETERS = {
                [commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
                [commonLocators.CommonLabels.NAME]: PROJECT_DESC,
                [commonLocators.CommonLabels.CLERK]: CONTAINERS_PROJECT.CLERK
            };
         
            PROCUREMENT_CONTRACT_PARAMETER = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIG,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BP
			}
            MATERIAL_RECORD_PARAMETER={
                [app.GridCells.CODE]:MAT_CODE,
                [app.GridCells.DESCRIPTION_INFO_1]:MAT_DESC,
                [app.GridCells.UOM_FK]:CONTAINERS_MATERIAL_RECORDS.UOM,
                [app.GridCells.RETAIL_PRICE]:CONTAINERS_MATERIAL_RECORDS.RETAIL_PRICE,
                [app.GridCells.LIST_PRICE]:CONTAINERS_MATERIAL_RECORDS.LIST_PRICE
            }
            ITEM_PARAMETER = {
                [app.GridCells.QUANTITY_SMALL]:CONTAINERS_CONTRACT.QUANTITY[0],
                [app.GridCells.DESCRIPTION_1]: MAT_DESC,
                [app.GridCells.MDC_MATERIAL_FK]: MAT_CODE
            };
        
        });
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
       
    });
    after(() => {
        cy.LOGOUT();
    });
    it("TC - Create new project", function () {
        _common.openDesktopTile(tile.DesktopTiles.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.create_newRecord(cnt.uuid.PROJECTS)
        _projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS)        
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
        _common.pinnedItem()
    });    
    it("TC - Create Material record in material module", function() {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.MATERIAL)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.setDefaultView(app.TabBar.RECORDS)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_CATALOG_FILTER, app.FooterTab.MATERIALFILTER, 0);
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_CATALOG_FILTER)
        _common.search_inSubContainer(cnt.uuid.MATERIAL_CATALOG_FILTER, CONTAINERS_MATERIAL_RECORDS.MATERIAL_CATALOG)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.MATERIAL_CATALOG_FILTER, app.GridCells.DESCRIPTION_INFO, CONTAINERS_MATERIAL_RECORDS.MATERIAL_CATALOG)
        _common.set_cellCheckboxValue(cnt.uuid.MATERIAL_CATALOG_FILTER, app.GridCells.IS_CHECKED, commonLocators.CommonKeys.CHECK)
        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_RECORDS, app.FooterTab.MATERIAL_RECORDS, 0);
            _common.setup_gridLayout(cnt.uuid.MATERIAL_RECORDS, CONTAINER_COLUMNS_MATERIAL_RECORDS)
        });
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_RECORDS)
        _common.create_newRecord(cnt.uuid.MATERIAL_RECORDS)
        _materialPage.enterRecord_toCreateNewMaterialRecord(cnt.uuid.MATERIAL_RECORDS, MATERIAL_RECORD_PARAMETER)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.MATERIAL_RECORDS,app.GridCells.SPECIFICATION_INFO,app.InputFields.DOMAIN_TYPE_TRANSLATION,SPEC_DESC)
        cy.SAVE()       

    })
    it("TC - Create new contract record", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear() 
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()      
		_common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT)
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)		
		});
        cy.REFRESH_CONTAINER()        
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESC)
        _common.waitForLoaderToDisappear()
		cy.SAVE()        
		_common.waitForLoaderToDisappear()
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, CONTAINERS_CONTRACT.CONTRACT_CODE)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, CONTRACT_DESC)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 1);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEMS)       
        	_common.set_columnAtTop([CONTAINER_COLUMNS_ITEMS.price,CONTAINER_COLUMNS_ITEMS.specification,CONTAINER_COLUMNS_ITEMS.quantity,CONTAINER_COLUMNS_ITEMS.mdcmaterialfk],cnt.uuid.ITEMSCONTRACT)			
		});       
        _common.maximizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        _procurementContractPage.enterRecord_toCreateContractItems(cnt.uuid.ITEMSCONTRACT,ITEM_PARAMETER);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMSCONTRACT,app.GridCells.SPECIFICATION,SPEC_DESC)        
        _common.minimizeContainer(cnt.uuid.ITEMSCONTRACT) 
        _common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.SPECIFICATION_PLAIN_TEXT_ITEM, app.FooterTab.SPECIFICATION_PLAIN_TEXT_ITEM, 2);            
        }) 
        _validate.validate_Text_In_Container_Textarea(cnt.uuid.SPECIFICATION_PLAIN_TEXT_ITEM,SPEC_DESC)
        _validate.validate_readOnly_Container_Textarea(cnt.uuid.SPECIFICATION_PLAIN_TEXT_ITEM)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 0);            
        })
        _common.select_rowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.ITEMSCONTRACT,app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.QUANTITY[1])
        _common.enterRecord_inNewRow(cnt.uuid.ITEMSCONTRACT, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.PRICE)
        _common.enterRecord_inNewRow(cnt.uuid.ITEMSCONTRACT, app.GridCells.SPECIFICATION, app.InputFields.DOMAIN_TYPE_REMARK, SPEC_DESC1)
        cy.SAVE()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.SPECIFICATION_PLAIN_TEXT_ITEM, app.FooterTab.SPECIFICATION_PLAIN_TEXT_ITEM, 2);            
        }) 
        _validate.validate_Text_In_Container_Textarea(cnt.uuid.SPECIFICATION_PLAIN_TEXT_ITEM,SPEC_DESC1)
        _validate.validate_readOnly_Container_Textarea(cnt.uuid.SPECIFICATION_PLAIN_TEXT_ITEM)
	});
    it("TC - Verify Contract Termination wizard option", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CONTRACT_TERMINATION);
        _common.waitForLoaderToDisappear()
        _saleContractPage.contractTermination_FromWizard(CONTAINERS_CONTRACT.REQUSITION,0,PROJECT_NO,CHANGES_DESC,CommonLocators.CommonKeys.DESIGN_CHANGE,CommonLocators.CommonKeys.CHANGE_REQUEST)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_REQUISITION)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.MAIN).then(() => {
			_common.select_tabFromFooter(cnt.uuid.REQUISITIONS, app.FooterTab.REQUISITIONS, 0);
        })
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar()
        _common.openTab(app.TabBar.MAIN).then(() => {
			_common.select_tabFromFooter(cnt.uuid.REQUISITIONITEMS, app.FooterTab.ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.REQUISITIONITEMS, CONTAINER_COLUMNS_ITEMS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_ITEMS.price,CONTAINER_COLUMNS_ITEMS.specification,CONTAINER_COLUMNS_ITEMS.quantity,CONTAINER_COLUMNS_ITEMS.mdcmaterialfk],cnt.uuid.REQUISITIONITEMS)					
		});        
        _common.maximizeContainer(cnt.uuid.REQUISITIONITEMS)
        _common.clear_subContainerFilter(cnt.uuid.REQUISITIONITEMS)
        _common.select_rowInContainer(cnt.uuid.REQUISITIONITEMS)
        _common.assert_cellData_insideActiveRow(cnt.uuid.REQUISITIONITEMS,app.GridCells.QUANTITY_SMALL,CONTAINERS_CONTRACT.QUANTITY[1])
        _common.assert_cellData_insideActiveRow(cnt.uuid.REQUISITIONITEMS,app.GridCells.MDC_MATERIAL_FK,MAT_CODE)
        _common.assert_cellData_insideActiveRow(cnt.uuid.REQUISITIONITEMS,app.GridCells.SPECIFICATION, SPEC_DESC1)
        _common.assert_cellData_insideActiveRow(cnt.uuid.REQUISITIONITEMS,app.GridCells.PRICE_SMALL,CONTAINERS_CONTRACT.PRICE)
        _common.minimizeContainer(cnt.uuid.REQUISITIONITEMS)
        _common.openTab(app.TabBar.MAIN).then(() => {
			_common.select_tabFromFooter(cnt.uuid.SPECIFICATION_PLAIN_TEXT_ITEM, app.FooterTab.SPECIFICATION_PLAIN_TEXT_ITEM, 0);					
		});
        _validate.validate_Text_In_Container_Textarea(cnt.uuid.SPECIFICATION_PLAIN_TEXT_ITEM,SPEC_DESC1)
        
    });

})