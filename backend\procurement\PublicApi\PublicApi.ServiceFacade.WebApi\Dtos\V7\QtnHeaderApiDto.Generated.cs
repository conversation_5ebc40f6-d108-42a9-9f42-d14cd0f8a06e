﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Procurement.PublicApi.BusinessComponents;

namespace RIB.Visual.Procurement.PublicApi.ServiceFacade.WebApi.V7
{


    /// <summary>
    /// There are no comments for QtnHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("QTN_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(7)]
    public partial class QtnHeaderApiDto : RIB.Visual.Platform.Core.ITypedDto<QtnHeaderApiEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class QtnHeaderApiDto.
        /// </summary>
        public QtnHeaderApiDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class QtnHeaderApiDto.
        /// </summary>
        /// <param name="entity">the instance of class QtnHeaderApiEntity</param>
        public QtnHeaderApiDto(QtnHeaderApiEntity entity)
        {
            Id = entity.Id;
            QtnStatusId = entity.QtnStatusId;
            QtnStatusDescription = entity.QtnStatusDescription;
            CompanyId = entity.CompanyId;
            CompanyCode = entity.CompanyCode;
            ProjectId = entity.ProjectId;
            ProjectCode = entity.ProjectCode;
            ProjectDescription = entity.ProjectDescription;
            ClerkPrcId = entity.ClerkPrcId;
            ClerkPrcCode = entity.ClerkPrcCode;
            ClerkPrcDescription = entity.ClerkPrcDescription;
            ClerkReqId = entity.ClerkReqId;
            ClerkReqCode = entity.ClerkReqCode;
            ClerkReqDescription = entity.ClerkReqDescription;
            RfqHeaderId = entity.RfqHeaderId;
            RfqHeaderCode = entity.RfqHeaderCode;
            RfqHeaderDescription = entity.RfqHeaderDescription;
            CurrencyId = entity.CurrencyId;
            CurrencyDescription = entity.CurrencyDescription;
            Exchangerate = entity.Exchangerate;
            PaymentTermFiId = entity.PaymentTermFiId;
            PaymentTermFiCode = entity.PaymentTermFiCode;
            PaymentTermFiDescription = entity.PaymentTermFiDescription;
            PaymentTermPaId = entity.PaymentTermPaId;
            PaymentTermPaCode = entity.PaymentTermPaCode;
            PaymentTermPaDescription = entity.PaymentTermPaDescription;
            Code = entity.Code;
            Description = entity.Description;
            SearchPattern = entity.SearchPattern;
            DateQuoted = entity.DateQuoted;
            DateReceived = entity.DateReceived;
            DatePricefixing = entity.DatePricefixing;
            QtnTypeId = entity.QtnTypeId;
            QtnTypeDescription = entity.QtnTypeDescription;
            BusinesspartnerId = entity.BusinesspartnerId;
            BusinesspartnerDescription = entity.BusinesspartnerDescription;
            SubsidiaryId = entity.SubsidiaryId;
            SubsidiaryDescription = entity.SubsidiaryDescription;
            SupplierId = entity.SupplierId;
            SupplierCode = entity.SupplierCode;
            SupplierDescription = entity.SupplierDescription;
            PrcIncotermId = entity.PrcIncotermId;
            PrcIncotermDescription = entity.PrcIncotermDescription;
            Isvalidated = entity.Isvalidated;
            Isexcluded = entity.Isexcluded;
            Isshortlisted = entity.Isshortlisted;
            Remark = entity.Remark;
            UserDefined1 = entity.UserDefined1;
            UserDefined2 = entity.UserDefined2;
            UserDefined3 = entity.UserDefined3;
            UserDefined4 = entity.UserDefined4;
            UserDefined5 = entity.UserDefined5;
            OverallDiscount = entity.OverallDiscount;
            OverallDiscountOc = entity.OverallDiscountOc;
            OverallDiscountPercent = entity.OverallDiscountPercent;
            DateDelivery = entity.DateDelivery;
            ExternalCode = entity.ExternalCode;
            BpdEvaluationId = entity.BpdEvaluationId;
            BpdEvaluationCode = entity.BpdEvaluationCode;
            BpdEvaluationDescription = entity.BpdEvaluationDescription;
            QtnHeaderId = entity.QtnHeaderId;
            QtnHeaderCode = entity.QtnHeaderCode;
            QtnHeaderDescription = entity.QtnHeaderDescription;
            QtnVersion = entity.QtnVersion;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            MdcBillingSchemaId = entity.MdcBillingSchemaId;
            MdcBillingSchemaDescription = entity.MdcBillingSchemaDescription;
            PaymentTermAdId = entity.PaymentTermAdId;
            PaymentTermAdCode = entity.PaymentTermAdCode;
            PaymentTermAdDescription = entity.PaymentTermAdDescription;
            Isidealbidder = entity.Isidealbidder;
            DateEffective = entity.DateEffective;
            VatgroupId = entity.VatgroupId;
            VatgroupDescription = entity.VatgroupDescription;
            SalesTaxMethodId = entity.SalesTaxMethodId;
            SalesTaxMethodDesc = entity.SalesTaxMethodDesc;
            UserDefinedDate01 = entity.UserDefinedDate01;
            LanguageId = entity.LanguageId;
            AmountDiscountBasis = entity.AmountDiscountBasis;
            AmountDiscountBasisOc = entity.AmountDiscountBasisOc;
            PercentDiscount = entity.PercentDiscount;
            AmountDiscount = entity.AmountDiscount;
            AmountDiscountOc = entity.AmountDiscountOc;
            PrcConfigurationId = entity.PrcConfigurationId;
            PrcConfigurationDescription = entity.PrcConfigurationDescription;
            BasLanguageFk = entity.BasLanguageFk;
            DateAwardDeadline = entity.DateAwardDeadline;
            DateQuoteDeadline = entity.DateQuoteDeadline;
            TimeQuoteDeadline = entity.TimeQuoteDeadline;
            ContactId = entity.ContactId;
            ContactFirstName = entity.ContactFirstName;
            ContactFamilyName = entity.ContactFamilyName;
            BasLanguageDesc = entity.BasLanguageDesc;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for QtnStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("StatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int QtnStatusId { get; set; }
    
        /// <summary>
        /// There are no comments for QtnStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string QtnStatusDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyId { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string CompanyCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public int? ProjectId { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ProjectCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ProjectDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public int? ClerkPrcId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkPrcCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkPrcDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public int? ClerkReqId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkReqCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkReqDescription { get; set; }
    
        /// <summary>
        /// There are no comments for RfqHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_ID", TypeName = "int", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("RfqHeaderFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int RfqHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for RfqHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_CODE", TypeName = "nvarchar(16)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string RfqHeaderCode { get; set; }
    
        /// <summary>
        /// There are no comments for RfqHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("RFQ_HEADER_DESC", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string RfqHeaderDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 17)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CurrencyId { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 18)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string CurrencyDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Exchangerate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 19)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal Exchangerate { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_ID", TypeName = "int", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermFiFk")]
        public int? PaymentTermFiId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_CODE", TypeName = "nvarchar(16)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermFiCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_DESC", TypeName = "nvarchar(2000)", Order = 22)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermFiDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_ID", TypeName = "int", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermPaFk")]
        public int? PaymentTermPaId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_CODE", TypeName = "nvarchar(16)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermPaCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_DESC", TypeName = "nvarchar(2000)", Order = 25)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermPaDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 26)]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 27)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }
    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 28)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public string SearchPattern { get; set; }
    
        /// <summary>
        /// There are no comments for DateQuoted in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_QUOTED", TypeName = "date", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateQuoted")]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateQuoted { get; set; }
    
        /// <summary>
        /// There are no comments for DateReceived in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_RECEIVED", TypeName = "date", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateReceived")]
        public System.DateTime? DateReceived { get; set; }
    
        /// <summary>
        /// There are no comments for DatePricefixing in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_PRICEFIXING", TypeName = "date", Order = 31)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DatePricefixing")]
        public System.DateTime? DatePricefixing { get; set; }
    
        /// <summary>
        /// There are no comments for QtnTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_TYPE_ID", TypeName = "int", Order = 32)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int QtnTypeId { get; set; }
    
        /// <summary>
        /// There are no comments for QtnTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 33)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string QtnTypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int BusinesspartnerId { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 35)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string BusinesspartnerDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 36)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public int? SubsidiaryId { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 37)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SubsidiaryDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public int? SupplierId { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierCode { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcIncotermId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_ID", TypeName = "int", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IncotermFk")]
        public int? PrcIncotermId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcIncotermDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_DESC", TypeName = "nvarchar(2000)", Order = 42)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcIncotermDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Isvalidated in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISVALIDATED", TypeName = "bit", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsValidated")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isvalidated { get; set; }
    
        /// <summary>
        /// There are no comments for Isexcluded in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISEXCLUDED", TypeName = "bit", Order = 44)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsExcluded")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isexcluded { get; set; }
    
        /// <summary>
        /// There are no comments for Isshortlisted in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISSHORTLISTED", TypeName = "bit", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsShortlisted")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isshortlisted { get; set; }
    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 47)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined1 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined2 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined3 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined4 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined5 { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT", TypeName = "numeric(19,7)", Order = 52)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscount { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscountOc { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscountPercent in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_PERCENT", TypeName = "numeric(9,3)", Order = 54)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscountPercent { get; set; }
    
        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 55)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? DateDelivery { get; set; }
    
        /// <summary>
        /// There are no comments for ExternalCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXTERNAL_CODE", TypeName = "nvarchar(252)", Order = 56)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ExternalCode { get; set; }
    
        /// <summary>
        /// There are no comments for BpdEvaluationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATION_ID", TypeName = "int", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("EvaluationFk")]
        public int? BpdEvaluationId { get; set; }
    
        /// <summary>
        /// There are no comments for BpdEvaluationCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATION_CODE", TypeName = "nvarchar(16)", Order = 58)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string BpdEvaluationCode { get; set; }
    
        /// <summary>
        /// There are no comments for BpdEvaluationDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_EVALUATION_DESC", TypeName = "nvarchar(252)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BpdEvaluationDescription { get; set; }
    
        /// <summary>
        /// There are no comments for QtnHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_ID", TypeName = "int", Order = 60)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("QtnHeaderFk")]
        public int? QtnHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for QtnHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_CODE", TypeName = "nvarchar(16)", Order = 61)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string QtnHeaderCode { get; set; }
    
        /// <summary>
        /// There are no comments for QtnHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_DESC", TypeName = "nvarchar(252)", Order = 62)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string QtnHeaderDescription { get; set; }
    
        /// <summary>
        /// There are no comments for QtnVersion in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_VERSION", TypeName = "int", Order = 63)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("QuoteVersion")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int QtnVersion { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 64)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InsertedAt")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 65)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InsertedBy")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UpdatedAt")]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 67)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UpdatedBy")]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 68)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Version")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }
    
        /// <summary>
        /// There are no comments for MdcBillingSchemaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_ID", TypeName = "int", Order = 69)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BillingSchemaFk")]
        public int? MdcBillingSchemaId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcBillingSchemaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 70)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcBillingSchemaDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_ID", TypeName = "int", Order = 71)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermAdFk")]
        public int? PaymentTermAdId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_CODE", TypeName = "nvarchar(16)", Order = 72)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermAdCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_DESC", TypeName = "nvarchar(2000)", Order = 73)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermAdDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Isidealbidder in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISIDEALBIDDER", TypeName = "bit", Order = 74)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsIdealBidder")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isidealbidder { get; set; }
    
        /// <summary>
        /// There are no comments for DateEffective in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_EFFECTIVE", TypeName = "date", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateEffective")]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateEffective { get; set; }
    
        /// <summary>
        /// There are no comments for VatgroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public int? VatgroupId { get; set; }
    
        /// <summary>
        /// There are no comments for VatgroupDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 77)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string VatgroupDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int SalesTaxMethodId { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 79)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string SalesTaxMethodDesc { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefinedDate01 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINEDDATE01", TypeName = "date", Order = 80)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? UserDefinedDate01 { get; set; }
    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 81)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int LanguageId { get; set; }
    
        /// <summary>
        /// There are no comments for AmountDiscountBasis in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNTBASIS", TypeName = "numeric(19,7)", Order = 82)]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountDiscountBasis { get; set; }
    
        /// <summary>
        /// There are no comments for AmountDiscountBasisOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNTBASIS_OC", TypeName = "numeric(19,7)", Order = 83)]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountDiscountBasisOc { get; set; }
    
        /// <summary>
        /// There are no comments for PercentDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PERCENT_DISCOUNT", TypeName = "numeric(10,2)", Order = 84)]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal PercentDiscount { get; set; }
    
        /// <summary>
        /// There are no comments for AmountDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNT", TypeName = "numeric(19,7)", Order = 85)]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountDiscount { get; set; }
    
        /// <summary>
        /// There are no comments for AmountDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("AMOUNT_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 86)]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal AmountDiscountOc { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 87)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcConfigurationId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_DESC", TypeName = "nvarchar(2000)", Order = 88)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcConfigurationDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public int? BasLanguageFk { get; set; }
    
        /// <summary>
        /// There are no comments for DateAwardDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_AWARDDEADLINE", TypeName = "date", Order = 90)]
        public System.DateTime? DateAwardDeadline { get; set; }
    
        /// <summary>
        /// There are no comments for DateQuoteDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_QUOTEDEADLINE", TypeName = "date", Order = 91)]
        public System.DateTime? DateQuoteDeadline { get; set; }
    
        /// <summary>
        /// There are no comments for TimeQuoteDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("TIME_QUOTEDEADLINE", TypeName = "time", Order = 92)]
        public global::System.TimeSpan? TimeQuoteDeadline { get; set; }
    
        /// <summary>
        /// There are no comments for ContactId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_FK", TypeName = "int", Order = 93)]
        [RIB.Visual.Platform.Common.InternalApiField("ContactFk")]
        public int? ContactId { get; set; }
    
        /// <summary>
        /// There are no comments for ContactFirstName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_FIRST_NAME", TypeName = "nvarchar(252)", Order = 94)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ContactFirstName { get; set; }
    
        /// <summary>
        /// There are no comments for ContactFamilyName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_FAMILY_NAME", TypeName = "nvarchar(252)", Order = 95)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ContactFamilyName { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 96)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BasLanguageDesc { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(QtnHeaderApiEntity); }
        }

        /// <summary>
        /// Copy the current QtnHeaderApiDto instance to a new QtnHeaderApiEntity instance.
        /// </summary>
        /// <returns>a new instance of class QtnHeaderApiEntity</returns>
        public QtnHeaderApiEntity Copy()
        {
          var entity = new QtnHeaderApiEntity();

          entity.Id = this.Id;
          entity.QtnStatusId = this.QtnStatusId;
          entity.QtnStatusDescription = this.QtnStatusDescription;
          entity.CompanyId = this.CompanyId;
          entity.CompanyCode = this.CompanyCode;
          entity.ProjectId = this.ProjectId;
          entity.ProjectCode = this.ProjectCode;
          entity.ProjectDescription = this.ProjectDescription;
          entity.ClerkPrcId = this.ClerkPrcId;
          entity.ClerkPrcCode = this.ClerkPrcCode;
          entity.ClerkPrcDescription = this.ClerkPrcDescription;
          entity.ClerkReqId = this.ClerkReqId;
          entity.ClerkReqCode = this.ClerkReqCode;
          entity.ClerkReqDescription = this.ClerkReqDescription;
          entity.RfqHeaderId = this.RfqHeaderId;
          entity.RfqHeaderCode = this.RfqHeaderCode;
          entity.RfqHeaderDescription = this.RfqHeaderDescription;
          entity.CurrencyId = this.CurrencyId;
          entity.CurrencyDescription = this.CurrencyDescription;
          entity.Exchangerate = this.Exchangerate;
          entity.PaymentTermFiId = this.PaymentTermFiId;
          entity.PaymentTermFiCode = this.PaymentTermFiCode;
          entity.PaymentTermFiDescription = this.PaymentTermFiDescription;
          entity.PaymentTermPaId = this.PaymentTermPaId;
          entity.PaymentTermPaCode = this.PaymentTermPaCode;
          entity.PaymentTermPaDescription = this.PaymentTermPaDescription;
          entity.Code = this.Code;
          entity.Description = this.Description;
          entity.SearchPattern = this.SearchPattern;
          entity.DateQuoted = this.DateQuoted;
          entity.DateReceived = this.DateReceived;
          entity.DatePricefixing = this.DatePricefixing;
          entity.QtnTypeId = this.QtnTypeId;
          entity.QtnTypeDescription = this.QtnTypeDescription;
          entity.BusinesspartnerId = this.BusinesspartnerId;
          entity.BusinesspartnerDescription = this.BusinesspartnerDescription;
          entity.SubsidiaryId = this.SubsidiaryId;
          entity.SubsidiaryDescription = this.SubsidiaryDescription;
          entity.SupplierId = this.SupplierId;
          entity.SupplierCode = this.SupplierCode;
          entity.SupplierDescription = this.SupplierDescription;
          entity.PrcIncotermId = this.PrcIncotermId;
          entity.PrcIncotermDescription = this.PrcIncotermDescription;
          entity.Isvalidated = this.Isvalidated;
          entity.Isexcluded = this.Isexcluded;
          entity.Isshortlisted = this.Isshortlisted;
          entity.Remark = this.Remark;
          entity.UserDefined1 = this.UserDefined1;
          entity.UserDefined2 = this.UserDefined2;
          entity.UserDefined3 = this.UserDefined3;
          entity.UserDefined4 = this.UserDefined4;
          entity.UserDefined5 = this.UserDefined5;
          entity.OverallDiscount = this.OverallDiscount;
          entity.OverallDiscountOc = this.OverallDiscountOc;
          entity.OverallDiscountPercent = this.OverallDiscountPercent;
          entity.DateDelivery = this.DateDelivery;
          entity.ExternalCode = this.ExternalCode;
          entity.BpdEvaluationId = this.BpdEvaluationId;
          entity.BpdEvaluationCode = this.BpdEvaluationCode;
          entity.BpdEvaluationDescription = this.BpdEvaluationDescription;
          entity.QtnHeaderId = this.QtnHeaderId;
          entity.QtnHeaderCode = this.QtnHeaderCode;
          entity.QtnHeaderDescription = this.QtnHeaderDescription;
          entity.QtnVersion = this.QtnVersion;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;
          entity.MdcBillingSchemaId = this.MdcBillingSchemaId;
          entity.MdcBillingSchemaDescription = this.MdcBillingSchemaDescription;
          entity.PaymentTermAdId = this.PaymentTermAdId;
          entity.PaymentTermAdCode = this.PaymentTermAdCode;
          entity.PaymentTermAdDescription = this.PaymentTermAdDescription;
          entity.Isidealbidder = this.Isidealbidder;
          entity.DateEffective = this.DateEffective;
          entity.VatgroupId = this.VatgroupId;
          entity.VatgroupDescription = this.VatgroupDescription;
          entity.SalesTaxMethodId = this.SalesTaxMethodId;
          entity.SalesTaxMethodDesc = this.SalesTaxMethodDesc;
          entity.UserDefinedDate01 = this.UserDefinedDate01;
          entity.LanguageId = this.LanguageId;
          entity.AmountDiscountBasis = this.AmountDiscountBasis;
          entity.AmountDiscountBasisOc = this.AmountDiscountBasisOc;
          entity.PercentDiscount = this.PercentDiscount;
          entity.AmountDiscount = this.AmountDiscount;
          entity.AmountDiscountOc = this.AmountDiscountOc;
          entity.PrcConfigurationId = this.PrcConfigurationId;
          entity.PrcConfigurationDescription = this.PrcConfigurationDescription;
          entity.BasLanguageFk = this.BasLanguageFk;
          entity.DateAwardDeadline = this.DateAwardDeadline;
          entity.DateQuoteDeadline = this.DateQuoteDeadline;
          entity.TimeQuoteDeadline = this.TimeQuoteDeadline;
          entity.ContactId = this.ContactId;
          entity.ContactFirstName = this.ContactFirstName;
          entity.ContactFamilyName = this.ContactFamilyName;
          entity.BasLanguageDesc = this.BasLanguageDesc;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(QtnHeaderApiEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(QtnHeaderApiEntity entity);
    }

}
