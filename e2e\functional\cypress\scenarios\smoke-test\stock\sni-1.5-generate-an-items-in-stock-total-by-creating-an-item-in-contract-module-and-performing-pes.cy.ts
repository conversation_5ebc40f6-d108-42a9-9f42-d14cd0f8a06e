import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _projectPage, _controllingUnit, _businessPartnerPage, _procurementContractPage, _saleContractPage, _procurementPage, _package } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const ALLURE = Cypress.Allure.reporter.getInterface();

let CONTAINER_PROJECT
let PROJECT_PARAMETERS_1: DataCells 
const PROJECT_NO_1 = _common.generateRandomString(4)
const PROJECT_DESC_1 = _common.generateRandomString(4)

let CONTAINERS_CONTROLLING_UNITS
let CONTAINER_COLUMNS_CONTROLLING_UNITS
let CONTROLLING_UNIT_SUB_PARAMETERS_1: DataCells
const CU_SUB_1 = _common.generateRandomString(4)

let CONTAINER_COLUMNS_STOCK_LOCATIONS
const STOCK_LOC_CODE = _common.generateRandomString(4)
const STOCK_LOC_DESC = _common.generateRandomString(4)

let CONTAINER_COLUMNS_STORAGE_LOCATIONS
const STORAGE_LOC_CODE = _common.generateRandomString(5)
const STORAGE_LOC_DESC = _common.generateRandomString(5)

let CONTAINER_COLUMNS_PROJECT_STOCK_MATERIAL
let CONTAINERS_PROJECT_STOCK_MATERIAL
let CONTAINER_COLUMNS_CONTRACT
let CONTRACT_PARAMETERS: DataCells
let CONTAINERS_CONTRACT
let CONTAINER_COLUMNS_CONTRACT_ITEM
let CONTAINER_COLUMNS_HEADER
let CONTAINER_COLUMNS_PES_ITEMS
let CONTAINER_COLUMNS_STOCK_HEADER
let CONTAINER_COLUMNS_STOCK_TOTAL

ALLURE.epic("STOCKS MANAGEMENT");
ALLURE.feature("Stocks Management");
ALLURE.story("SNI- 1.5 | Generate an item/s in Stock total by creating an item in Contract module and performing PES");

describe("SNI- 1.5 | Generate an item/s in Stock total by creating an item in Contract module and performing PES", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("SNI/sni-1.5-generate-an-items-in-stock-total-by-creating-an-item-in-contract-module-and-performing-pes.json").then((data) => {
            this.data = data;
            CONTAINER_PROJECT = this.data.CONTAINERS.PROJECT
            PROJECT_PARAMETERS_1 = {
                [commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO_1,
                [commonLocators.CommonLabels.NAME]: PROJECT_DESC_1,
                [commonLocators.CommonLabels.CLERK]: CONTAINER_PROJECT.CLERK_NAME
            }
            CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNITS
            CONTAINER_COLUMNS_CONTROLLING_UNITS = this.data.CONTAINER_COLUMNS.CONTROLLING_UNITS;
            CONTROLLING_UNIT_SUB_PARAMETERS_1 = {
                [app.GridCells.DESCRIPTION_INFO]: CU_SUB_1,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_CONTROLLING_UNITS.QUANTITY[1],
                [app.GridCells.UOM_FK]: CONTAINERS_CONTROLLING_UNITS.UOM,
                [app.GridCells.BUDGET]: CONTAINERS_CONTROLLING_UNITS.BUDGET[1],
            }
            CONTAINER_COLUMNS_STOCK_LOCATIONS = this.data.CONTAINER_COLUMNS.STOCK_LOCATIONS
            CONTAINER_COLUMNS_STORAGE_LOCATIONS=this.data.CONTAINER_COLUMNS.STORAGE_LOCATIONS
            CONTAINER_COLUMNS_PROJECT_STOCK_MATERIAL=this.data.CONTAINER_COLUMNS.PROJECT_STOCK_MATERIAL
            CONTAINERS_PROJECT_STOCK_MATERIAL=this.data.CONTAINERS.PROJECT_STOCK_MATERIAL
            CONTAINER_COLUMNS_CONTRACT=this.data.CONTAINER_COLUMNS.CONTRACT
            CONTAINERS_CONTRACT=this.data.CONTAINERS.CONTRACT
            CONTRACT_PARAMETERS={
                [commonLocators.CommonLabels.CONFIGURATION]:CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]:CONTAINERS_CONTRACT.BUSINESS_PARTNER
            }
            CONTAINER_COLUMNS_CONTRACT_ITEM=this.data.CONTAINER_COLUMNS.CONTRACT_ITEM

            CONTAINER_COLUMNS_HEADER=this.data.CONTAINER_COLUMNS.HEADER
            CONTAINER_COLUMNS_PES_ITEMS=this.data.CONTAINER_COLUMNS.PES_ITEMS
            CONTAINER_COLUMNS_STOCK_HEADER=this.data.CONTAINER_COLUMNS.STOCK_HEADER
            CONTAINER_COLUMNS_STOCK_TOTAL=this.data.CONTAINER_COLUMNS.STOCK_TOTAL
        })
        .then(()=>{
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        })
    })

    after(() => {
        cy.LOGOUT();
    });

    it('TC - Customizing requisition status', function () {

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ENTITY_TYPES, app.FooterTab.DATA_TYPES, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.ENTITY_TYPES)
        _common.search_inSubContainer(cnt.uuid.ENTITY_TYPES, commonLocators.CommonKeys.PES_STATUS)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.ENTITY_TYPES, app.GridCells.NAME, commonLocators.CommonKeys.PES_STATUS);
        cy.wait(1000)//required wait
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORD, 0);
        });
        _common.search_inSubContainer(cnt.uuid.INSTANCES,commonLocators.CommonKeys.ACCEPTION)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.INSTANCES,app.GridCells.DESCRIPTION_INFO,commonLocators.CommonKeys.ACCEPTION)
        _common.set_cellCheckboxValue(cnt.uuid.INSTANCES,app.GridCells.IS_STOCK,commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create new Project", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.setDefaultView(app.TabBar.PROJECT)
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
        _common.create_newRecord(cnt.uuid.PROJECTS);
        _projectPage.enterRecord_toCreateProject(PROJECT_PARAMETERS_1);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO_1).pinnedItem()
    });

    it("TC - Assign Controlling Units to project", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTROLLING_UNITS)
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO_1).pinnedItem();
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT, app.FooterTab.CONTROLLING_UNITS);
            _common.setup_gridLayout(cnt.uuid.CONTROLLING_UNIT, CONTAINER_COLUMNS_CONTROLLING_UNITS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTROLLING_UNITS.budget, CONTAINER_COLUMNS_CONTROLLING_UNITS.isfixedbudget, CONTAINER_COLUMNS_CONTROLLING_UNITS.isaccountingelement, CONTAINER_COLUMNS_CONTROLLING_UNITS.isbillingelement, CONTAINER_COLUMNS_CONTROLLING_UNITS.isplanningelement, CONTAINER_COLUMNS_CONTROLLING_UNITS.isassetmanagement, CONTAINER_COLUMNS_CONTROLLING_UNITS.isstockmanagement,CONTAINER_COLUMNS_CONTROLLING_UNITS.isintercompany], cnt.uuid.CONTROLLING_UNIT)
        });
        _common.clear_subContainerFilter(cnt.uuid.CONTROLLING_UNIT)
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.CONTROLLING_UNIT)
        _common.clear_subContainerFilter(cnt.uuid.CONTROLLING_UNIT)
        _common.create_newRecord(cnt.uuid.CONTROLLING_UNIT)
        _common.select_rowHasValue(cnt.uuid.CONTROLLING_UNIT, PROJECT_NO_1)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_FIXED_BUDGET, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_BILLING_ELEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_ACCOUNTING_ELEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_PLANNING_ELEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_ASSET_MANAGEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_STOCK_MANAGEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_INTER_COMPANY, commonLocators.CommonKeys.CHECK)
        _common.enterRecord_inNewRow(cnt.uuid.CONTROLLING_UNIT, app.GridCells.BUDGET, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTROLLING_UNITS.BUDGET[0])
        _common.enterRecord_inNewRow(cnt.uuid.CONTROLLING_UNIT, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTROLLING_UNITS.QUANTITY[0])
        _common.enterRecord_inNewRow(cnt.uuid.CONTROLLING_UNIT, app.GridCells.UOM_FK, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTROLLING_UNITS.UOM)
        _common.select_activeRowInContainer(cnt.uuid.CONTROLLING_UNIT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _controllingUnit.enterRecord_toCreateControllingUnit(CONTROLLING_UNIT_SUB_PARAMETERS_1)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_FIXED_BUDGET, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_BILLING_ELEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_ACCOUNTING_ELEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_PLANNING_ELEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_ASSET_MANAGEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_STOCK_MANAGEMENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.IS_INTER_COMPANY, commonLocators.CommonKeys.CHECK)

        _common.enterRecord_inNewRow(cnt.uuid.CONTROLLING_UNIT, app.GridCells.BUDGET, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTROLLING_UNITS.BUDGET[1])
        _common.select_activeRowInContainer(cnt.uuid.CONTROLLING_UNIT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONTROLLING_UNIT)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create Stock Locations record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO_1).pinnedItem()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.MANAGED_PLANTS).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.PROJECT_STOCK_LOCATION, app.FooterTab.STOCK_LOCATIONS, 1);
            _common.setup_gridLayout(cnt.uuid.PROJECT_STOCK_LOCATION, CONTAINER_COLUMNS_STOCK_LOCATIONS)
        });
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.MANAGED_PLANTS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_STORAGE_LOCATION, app.FooterTab.STORAGE_LOCATIONS, 1);
            _common.setup_gridLayout(cnt.uuid.PROJECT_STORAGE_LOCATION, CONTAINER_COLUMNS_STORAGE_LOCATIONS)
        });
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.MANAGED_PLANTS).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROJECT_STOCK_MATERIAL, app.FooterTab.PROJECT_STOCK_MATERIALS,1)
            _common.setup_gridLayout(cnt.uuid.PROJECT_STOCK_MATERIAL, CONTAINER_COLUMNS_PROJECT_STOCK_MATERIAL)
            _common.waitForLoaderToDisappear()
        })
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.MANAGED_PLANTS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_STOCK_LOCATION, app.FooterTab.STOCK_LOCATIONS, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_STOCK_LOCATION)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROJECT_STOCK_LOCATION)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.PROJECT_STOCK_LOCATION, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, STOCK_LOC_CODE)
        _common.edit_containerCell(cnt.uuid.PROJECT_STOCK_LOCATION, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, STOCK_LOC_DESC)
        _common.set_cellCheckboxValue(cnt.uuid.PROJECT_STOCK_LOCATION, app.GridCells.IS_LOCATION_MANDATORY, commonLocators.CommonKeys.CHECK)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROJECT_STOCK_LOCATION, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CU_SUB_1)
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_STOCK_LOCATION)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Assign Stock to Controlling Unit", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTROLLING_UNITS)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO_1).pinnedItem();
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT, app.FooterTab.CONTROLLING_UNITS);
            _common.setup_gridLayout(cnt.uuid.CONTROLLING_UNIT, CONTAINER_COLUMNS_CONTROLLING_UNITS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTROLLING_UNITS.budget, CONTAINER_COLUMNS_CONTROLLING_UNITS.isfixedbudget, CONTAINER_COLUMNS_CONTROLLING_UNITS.isaccountingelement, CONTAINER_COLUMNS_CONTROLLING_UNITS.isbillingelement, CONTAINER_COLUMNS_CONTROLLING_UNITS.isplanningelement, CONTAINER_COLUMNS_CONTROLLING_UNITS.isassetmanagement, CONTAINER_COLUMNS_CONTROLLING_UNITS.isstockmanagement], cnt.uuid.CONTROLLING_UNIT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.CONTROLLING_UNIT)
        _common.waitForLoaderToDisappear()

        _common.clear_subContainerFilter(cnt.uuid.CONTROLLING_UNIT)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.CONTROLLING_UNIT, CU_SUB_1)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTROLLING_UNIT, app.GridCells.STOCK_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, STOCK_LOC_CODE)
        _common.select_activeRowInContainer(cnt.uuid.CONTROLLING_UNIT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it('TC - Create Storage Locations record',function(){
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO_1).pinnedItem()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.MANAGED_PLANTS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_STOCK_LOCATION, app.FooterTab.STOCK_LOCATIONS, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_STOCK_LOCATION)
        _common.select_rowInContainer(cnt.uuid.PROJECT_STOCK_LOCATION)

        _common.openTab(app.TabBar.MANAGED_PLANTS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_STORAGE_LOCATION, app.FooterTab.STORAGE_LOCATIONS, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_STORAGE_LOCATION)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROJECT_STORAGE_LOCATION)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PROJECT_STORAGE_LOCATION,app.GridCells.CODE,app.InputFields.DOMAIN_TYPE_CODE,STORAGE_LOC_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.PROJECT_STORAGE_LOCATION,app.GridCells.DESCRIPTION_INFO,app.InputFields.DOMAIN_TYPE_TRANSLATION,STORAGE_LOC_DESC)
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_STORAGE_LOCATION)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create Project Stock material record", function () {
        _common.openTab(app.TabBar.MANAGED_PLANTS).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROJECT_STOCK_MATERIAL, app.FooterTab.PROJECT_STOCK_MATERIALS,2)
            _common.waitForLoaderToDisappear()
        })
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_STOCK_MATERIAL)
        _common.create_newRecord(cnt.uuid.PROJECT_STOCK_MATERIAL)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROJECT_STOCK_MATERIAL, app.GridCells.MATERIAL_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_PROJECT_STOCK_MATERIAL.MATERIAL)
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_STOCK_MATERIAL)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROJECT_STOCK_MATERIAL, app.GridCells.STOCK_LOCATION_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,STORAGE_LOC_CODE)
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_STOCK_MATERIAL)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PROJECT_STOCK_MATERIAL, app.GridCells.MIN_QUANTITY,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_PROJECT_STOCK_MATERIAL.MIN_QUANTITY)
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_STOCK_MATERIAL)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PROJECT_STOCK_MATERIAL, app.GridCells.MAX_QUANTITY,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_PROJECT_STOCK_MATERIAL.MAX_QUANTITY)
        _common.select_activeRowInContainer(cnt.uuid.PROJECT_STOCK_MATERIAL)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create contract", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 2);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar();
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETERS);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.CLERK_PRC_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTAINER_PROJECT.CLERK_NAME)
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.CODE,"CONTRACT_CODE")
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create contract item and Create PES", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 2);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 2);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_CONTRACT_ITEM)
            _common.waitForLoaderToDisappear()
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT_ITEM.mdccontrollingunitfk,CONTAINER_COLUMNS_CONTRACT_ITEM.mdcmaterialfk,CONTAINER_COLUMNS_CONTRACT_ITEM.quantity,CONTAINER_COLUMNS_CONTRACT_ITEM.prjstocklocationfk],cnt.uuid.ITEMSCONTRACT)
            _common.waitForLoaderToDisappear()
        });
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT,app.GridCells.MDC_MATERIAL_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_PROJECT_STOCK_MATERIAL.MATERIAL)
        _common.select_activeRowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT,app.GridCells.MDC_CONTROLLING_UNIT_FK_SMALL,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CU_SUB_1)
        _common.select_activeRowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.enterRecord_inNewRow(cnt.uuid.ITEMSCONTRACT,app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_PROJECT_STOCK_MATERIAL.QUANTITY)
        _common.select_activeRowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT,app.GridCells.PRJ_STOCK_LOCATION_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,STORAGE_LOC_CODE)
        _common.select_activeRowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS)
        _common.waitForLoaderToDisappear()
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()

      
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES)
        _common.waitForLoaderToDisappear()
        cy.wait(2000) // Added this wait script is getting failed
        _common.clickOn_validationModalFooterButton_ifExists(btn.ButtonText.OK)
        cy.wait(2000) // Added this wait script is getting failed
        _procurementPage.getCode_fromPESModal("PES_CODE")
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_PES)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Change PES status and update PES item quantity", function () {

        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO_1);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
            _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_HEADER)
        });
        _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.HEADERS,Cypress.env("PES_CODE"))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.HEADERS,Cypress.env("PES_CODE"))
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PES_STATUS)
        _common.waitForLoaderToDisappear()
        _common.changeStatus_fromModal(commonLocators.CommonKeys.ACCEPTION)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
            _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_PES_ITEMS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_PES_ITEMS.conheaderfk,CONTAINER_COLUMNS_PES_ITEMS.quantity,CONTAINER_COLUMNS_PES_ITEMS.quantitycontracted],cnt.uuid.ITEMS)
        })
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
        });

        _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.HEADERS,Cypress.env("PES_CODE"))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.HEADERS,Cypress.env("PES_CODE"))
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS, 2);
        })

        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.ITEMS,Cypress.env("CONTRACT_CODE"))
        _common.enterRecord_inNewRow(cnt.uuid.ITEMS,app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_PROJECT_STOCK_MATERIAL.UPDATED_QUANTITY)
        _common.select_activeRowInContainer(cnt.uuid.ITEMS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.ITEMS,app.GridCells.QUANTITY_CONTRACTED,"CONTRACTED_QUANTITY")
    })

    it("TC - Stock information is updated in Stock Header from the contract module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.STOCK)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.STOCK).then(() => {
            _common.select_tabFromFooter(cnt.uuid.STOCK_HEADER, app.FooterTab.STOCK_HEADER, 0);
            _common.setup_gridLayout(cnt.uuid.STOCK_HEADER, CONTAINER_COLUMNS_STOCK_HEADER)
        })
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.STOCK_HEADER)
        _common.select_rowHasValue(cnt.uuid.STOCK_HEADER,PROJECT_NO_1)
        _common.set_cellCheckboxValue(cnt.uuid.STOCK_HEADER,app.GridCells.IS_CHECKED,commonLocators.CommonKeys.CHECK)
        _common.assert_cellData(cnt.uuid.STOCK_HEADER,app.GridCells.PRJ_STOCK_FK,STOCK_LOC_CODE)
        _common.assert_cellData(cnt.uuid.STOCK_HEADER,app.GridCells.PRJ_STOCK_FK_DESCRIPTION,STOCK_LOC_DESC)
    })

    it("TC - Quantity in Stock gets updated from Quantity in PES", function () {
        _common.openTab(app.TabBar.STOCK).then(() => {
            _common.select_tabFromFooter(cnt.uuid.STOCK_STOCKTOTAL, app.FooterTab.STOCK_TOTAL, 2);
            _common.setup_gridLayout(cnt.uuid.STOCK_STOCKTOTAL, CONTAINER_COLUMNS_STOCK_TOTAL)
        })
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.STOCK_STOCKTOTAL)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.STOCK_STOCKTOTAL)
        _common.assert_forNumericValues(cnt.uuid.STOCK_STOCKTOTAL,app.GridCells.QUANTITY_SMALL,CONTAINERS_PROJECT_STOCK_MATERIAL.UPDATED_QUANTITY)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Quantity in Stock gets updated from Quantity in PES", function () {
        _common.openTab(app.TabBar.STOCK).then(() => {
            _common.select_tabFromFooter(cnt.uuid.STOCK_STOCKTOTAL, app.FooterTab.STOCK_TOTAL, 2);
        })
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.STOCK_STOCKTOTAL)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.STOCK_STOCKTOTAL)
        _common.assert_forNumericValues(cnt.uuid.STOCK_STOCKTOTAL,app.GridCells.QUANTITY_ON_ORDER,Cypress.env("CONTRACTED_QUANTITY"))
        _common.waitForLoaderToDisappear()
    })

    it("TC - Material Receipt (transaction type) and quantity are visible in Transaction type under transaction container", function () {
        _common.openTab(app.TabBar.STOCK).then(() => {
            _common.select_tabFromFooter(cnt.uuid.STOCK_TRANSACTION, app.FooterTab.TRANSACTIONS, 1);
        })
        _common.clear_subContainerFilter(cnt.uuid.STOCK_TRANSACTION)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.STOCK_TRANSACTION)
        _common.assert_cellData(cnt.uuid.STOCK_TRANSACTION,app.GridCells.PRC_STOCK_TRANSACTION_TYPE_FK,commonLocators.CommonKeys.MATERIAL_RECEIPT)
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.STOCK_TRANSACTION)
        _common.assert_forNumericValues(cnt.uuid.STOCK_TRANSACTION,app.GridCells.QUANTITY_SMALL,CONTAINERS_PROJECT_STOCK_MATERIAL.UPDATED_QUANTITY)
        _common.waitForLoaderToDisappear()
    })
});