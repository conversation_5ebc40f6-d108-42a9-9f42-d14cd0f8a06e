/*
 * Copyright(c) RIB Software GmbH
 */

import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Meta, moduleMetadata, StoryObj } from '@storybook/angular';
import { action } from '@storybook/addon-actions';
import { ImageViewComponent } from './image-view.component';
import { ImageSizeDirective } from '../../directives/image-size.directive';
import { PlatformConfigurationService, PlatformModuleManagerService, PlatformPermissionService, PlatformTranslateService } from '@libs/platform/common';
import { HttpClientModule } from '@angular/common/http';

// Mock image data using placeholder services
const createMockBase64Image = (width: number, height: number, color: string, text: string): string => {
  // This creates a simple base64 encoded SVG image for demonstration
  const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
    <rect width="100%" height="100%" fill="${color}"/>
    <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16">${text}</text>
  </svg>`;
  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

// Mock image URLs for demonstration
const sampleImages = [
  createMockBase64Image(800, 600, '#2196F3', 'Image 1'),
  createMockBase64Image(800, 600, '#4CAF50', 'Image 2'),
  createMockBase64Image(800, 600, '#FF9800', 'Image 3'),
  createMockBase64Image(800, 600, '#9C27B0', 'Image 4'),
  createMockBase64Image(800, 600, '#F44336', 'Image 5'),
];

const landscapeImages = [
  createMockBase64Image(1200, 600, '#607D8B', 'Landscape 1'),
  createMockBase64Image(1200, 600, '#795548', 'Landscape 2'),
  createMockBase64Image(1200, 600, '#3F51B5', 'Landscape 3'),
];

const portraitImages = [
  createMockBase64Image(600, 900, '#E91E63', 'Portrait 1'),
  createMockBase64Image(600, 900, '#009688', 'Portrait 2'),
  createMockBase64Image(600, 900, '#673AB7', 'Portrait 3'),
];

// Mock a real image URL for demonstration
const realImageUrl = 'https://picsum.photos/800/600?random=1';
const realImages = [
  'https://picsum.photos/800/600?random=1',
  'https://picsum.photos/800/600?random=2',
  'https://picsum.photos/800/600?random=3',
  'https://picsum.photos/800/600?random=4',
];

// Mock services needed by ImageSizeDirective
const mockPlatformModuleManagerService = {
  // Add any methods needed by the directive
  // This is a placeholder - you may need to adjust based on actual service interface
};

const meta: Meta<ImageViewComponent> = {
  title: 'UI Common/Image Viewer/Image View',
  component: ImageViewComponent,
  parameters: {
    docs: {
      description: {
        component: `
A sophisticated image viewer component with carousel functionality for displaying single or multiple images.

**Image Formats:**
- Base64 encoded images
- Blob data (automatically converted to base64)
- Standard image URLs
- SVG images
        `,
      },
    },
    layout: 'fullscreen',
  },
  argTypes: {
    selectedIndex: {
      control: { type: 'number', min: 0, max: 10 },
      description: 'Index of the currently selected image in carousel mode',
    },
    isMultiple: {
      control: 'boolean',
      description: 'Whether to display multiple images in carousel mode',
    },
    items: {
      control: false,
      description: 'Array of image sources (base64 or URLs) for carousel mode',
    },
    itemSrc: {
      control: 'text',
      description: 'Single image source (base64 or URL) for single image mode',
    },
    showIndicators: {
      control: 'boolean',
      description: 'Whether to show dot indicators and image counter',
    },
    showNavigationButtons: {
      control: 'boolean',
      description: 'Whether to show previous/next navigation buttons',
    },
    selectedIndexChanged: {
      action: 'selectedIndexChanged',
      description: 'Event emitted when the selected image index changes',
    },
  },
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FormsModule,HttpClientModule,ImageSizeDirective],
      declarations: [],
      providers: [PlatformModuleManagerService, PlatformPermissionService, 
        PlatformConfigurationService,
        PlatformTranslateService 
        // Add any providers needed by ImageSizeDirective
        // { provide: PlatformModuleManagerService, useValue: mockPlatformModuleManagerService },
      ],
    }),
  ],
};

export default meta;

type Story = StoryObj<ImageViewComponent>;

export const SingleImage: Story = {
  args: {
    selectedIndex: 0,
    isMultiple: false,
    itemSrc: sampleImages[0],
    items: [],
    showIndicators: true,
    showNavigationButtons: true,
  },
  render: (args) => ({
    props: {
      ...args,
      onSelectedIndexChanged: action('selectedIndexChanged'),
    },
    template: `
        <ui-common-image-view
          [selectedIndex]="selectedIndex"
          [isMultiple]="isMultiple"
          [itemSrc]="itemSrc"
          [items]="items"
          (selectedIndexChanged)="onSelectedIndexChanged($event)"
        ></ui-common-image-view>
      
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: 'Display a single image with responsive sizing and aspect ratio preservation.',
      },
    },
  },
};

export const MultipleImagesCarousel: Story = {
  args: {
    selectedIndex: 0,
    isMultiple: true,
    itemSrc: '',
    items: sampleImages,
    showIndicators: true,
    showNavigationButtons: true,
  },
  render: (args) => ({
    props: {
      ...args,
      onSelectedIndexChanged: action('selectedIndexChanged'),
    },
    template: `
     
        <ui-common-image-view
          [selectedIndex]="selectedIndex"
          [isMultiple]="isMultiple"
          [itemSrc]="itemSrc"
          [items]="items"
          (selectedIndexChanged)="onSelectedIndexChanged($event)"
        ></ui-common-image-view>
       
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: 'Navigate through multiple images with carousel controls, indicators, and navigation buttons.',
      },
    },
  },
};

export const WithoutIndicators: Story = {
  args: {
    selectedIndex: 0,
    isMultiple: true,
    itemSrc: '',
    items: landscapeImages,
    showIndicators: false,
    showNavigationButtons: true,
  },
  render: (args) => ({
    props: {
      ...args,
      onSelectedIndexChanged: action('selectedIndexChanged'),
    },
    template: `
      
        <ui-common-image-view
          [selectedIndex]="selectedIndex"
          [isMultiple]="isMultiple"
          [itemSrc]="itemSrc"
          [items]="items"
          (selectedIndexChanged)="onSelectedIndexChanged($event)"
        ></ui-common-image-view>
       
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: 'Carousel without dot indicators, showing only navigation buttons for a cleaner interface.',
      },
    },
  },
};


export const RealImages: Story = {
  args: {
    selectedIndex: 0,
    isMultiple: true,
    itemSrc: '',
    items: realImages,
    showIndicators: true,
    showNavigationButtons: true,
  },
  render: (args) => ({
    props: {
      ...args,
      onSelectedIndexChanged: action('selectedIndexChanged'),
    },
    template: `
     
        <ui-common-image-view
          [selectedIndex]="selectedIndex"
          [isMultiple]="isMultiple"
          [itemSrc]="itemSrc"
          [items]="items"
          (selectedIndexChanged)="onSelectedIndexChanged($event)"
        ></ui-common-image-view>
        
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: 'Carousel with real images from Lorem Picsum service to test actual image loading.',
      },
    },
  },
};

export const Base64Images: Story = {
  args: {
    selectedIndex: 0,
    isMultiple: true,
    itemSrc: '',
    items: [
      // Example of base64 images without the data:image prefix (simulating blob data)
      sampleImages[0].split(',')[1], // Remove data:image/svg+xml;base64, prefix
      sampleImages[1].split(',')[1],
      sampleImages[2].split(',')[1],
    ],
    showIndicators: true,
    showNavigationButtons: true,
  },
  render: (args) => ({
    props: {
      ...args,
      onSelectedIndexChanged: action('selectedIndexChanged'),
    },
    template: `
      
        <ui-common-image-view
          [selectedIndex]="selectedIndex"
          [isMultiple]="isMultiple"
          [itemSrc]="itemSrc"
          [items]="items"
          (selectedIndexChanged)="onSelectedIndexChanged($event)"
        ></ui-common-image-view>
        
    `,
  }),
  parameters: {
    docs: {
      description: {
        story: 'Images provided as base64 blob data, demonstrating automatic conversion to displayable format.',
      },
    },
  },
};
