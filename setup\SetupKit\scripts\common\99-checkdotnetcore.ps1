###############################################################################
## RIB Software GmbH Stuttgart (R) 2025.
## 01-install-web -config cfg.file -deploy $true -install $true
###############################################################################
function InitCheck() {
	Import-Module WebAdministration
	#Requires -RunAsAdministrator
	$script:colorError = 'red'
	$script:colorInfo = 'yellow'
	$script:colorWarning = 'magenta'
	$script:colorOk = 'green'
}

######################################
## CheckPowershellInstalled check if powershell is installed
## sample:
##	CheckPowershellInstalled
###############################################################################
function CheckPowershellInstalled ([int] $major, [int] $minor) {

	$version = $PSVersionTable.PSVersion
	if (($version.major -gt $major ) -or ($version.major -eq $major -and $version.minor -ge $minor )) {
		Write-Host -ForegroundColor $colorOk "Powershell Check found version $version, requested version: $major.$minor. Prerequisites ok."
		return $true
	}

	Write-Host "Required version $major.$minor. of Powershell not installed. Prerequisites failed!" -ForegroundColor $colorError
	Write-Host 'S t a r t  o f  h i n t '-ForegroundColor -ForegroundColor $colorInfo
	Write-Host '   Please install request version of Powershell before you can continue.'-ForegroundColor $colorInfo
	Write-Host '   see url: https://www.microsoft.com/en-us/download/details.aspx?id=54616'-ForegroundColor $colorInfo
	Write-Host '   After installation of Powershell you can restart installation.'-ForegroundColor $colorInfo
	Write-Host '     in certain cases you might have to reboot the system.'-ForegroundColor $colorInfo
	Write-Host '   Check Version by: powershell -c get-host '-ForegroundColor $colorInfo
	Write-Host 'E n d  o f  h i n t '-ForegroundColor $colorInfo
	Write-Host ''
	Write-Host ''
	$script:abortInstallation = $true
	exit
	return $false
}

function GetLatestDotNetRuntimeUrls( [string] $mainversion = '8.0') {
	$dotnetRoot = 'https://dotnet.microsoft.com'
	$dotnetrtUrl = 'https://dotnet.microsoft.com/download/dotnet/' + $mainVersion + '/runtime'

	try {
		$links = (Invoke-WebRequest -Uri $dotnetrtUrl -UseBasicParsing).Links

		$bundleUrl = ($links | Select-Object href | Where-Object { $_.href -like '*windows-hosting-bundle-installer*' }).href
		$res = Invoke-WebRequest -Uri $($dotnetRoot + $bundleUrl) -UseBasicParsing
		$downloadLink1 = $res.Links | Where-Object { $_.id -like 'directLink' }

		$bundleUrl = ($links | Select-Object href | Where-Object { $_.href -like '*desktop*windows-x64-installer*' }).href
		$res = Invoke-WebRequest -Uri $($dotnetRoot + $bundleUrl) -UseBasicParsing
		$downloadLink2 = $res.Links | Where-Object { $_.id -like 'directLink' }

		return @($downloadLink1.href, $downloadLink2.href)
	} catch {
		return $null
	}
}

######################################
## CheckDotNet48Installed check dot net 4.8 is installed
## sample:
##	CheckDotNet48Installed
###############################################################################
function CheckDotNet48Installed () {
	try {
		$v = (Get-Item 'hklm:\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full') | Get-ItemProperty
		if ($v.release -ge 528040) {
			Write-Host -ForegroundColor $colorOk ".Net Framework Version found. Version: $($v.version), Release: $($v.release). Prerequisites ok."
			return $true
		} else {
			Write-Host ''
			Write-Host "Required minimum version '4.8' of Microsoft .Net Framework is not installed!" -ForegroundColor $colorError
			Write-Host " following version of .Net Framework Version found. Version: $($v.version), Release: $($v.release)" -ForegroundColor $colorInfo
			Write-Host ''
			Write-Host 'S t a r t  o f  H i n t ' -ForegroundColor $colorInfo
			Write-Host '   The minimum version of Microsoft .Net Framework 4.8 is required with this RIB 4.0 Version.' -ForegroundColor $colorInfo
			Write-Host '   We recommend you to install the latest version of Microsoft .net framework on your machine.' -ForegroundColor $colorInfo
			Write-Host '   You can download the latest version of Microsoft .Net Framework from:' -ForegroundColor $colorInfo
			Write-Host '   from url: https://dotnet.microsoft.com/download/dotnet-framework' -ForegroundColor $colorInfo
			Write-Host 'E n d  o f  H i n t ' -ForegroundColor $colorInfo
			Write-Host ''
			$script:abortInstallation = $true
			exit
			return $false
		}
	} catch {
		Write-Host '.Net Framework Version check failed.' -ForegroundColor $colorError
		Write-Host $_ -ForegroundColor $colorError
		$script:abortInstallation = $true
		exit
		return $false
	}
}

<# support override of values as well #>
function AddMissingMimeType(  [string] $ext, [string] $mimetype) {

	$frx = Get-WebConfigurationProperty //staticContent -Name collection[fileExtension="$ext"]
	#write-host "$($frx.fileExtension) $($frx.mimeType) >>>$ext,  $mimetype"
	if ((-not $frx) ) {
		Write-Host "Add missing $ext mimetype=$mimetype to Internet Information Server Configuration" -ForegroundColor magenta
		Add-WebConfigurationProperty '//staticContent' -Name collection -Value @{fileExtension = "$ext"; mimeType = "$mimetype" }
		$frx = Get-WebConfigurationProperty //staticContent -Name collection[fileExtension="$ext"]
		if (-not $frx) {
			Write-Host "!Info Added missing $ext mimetype to Internet Information Server Configuration failed" -ForegroundColor red
		}
	} else {
		if ($frx.mimeType -ne $mimetype) {
			Write-Host "Set $ext mimetype=$mimetype to Internet Information Server Configuration" -ForegroundColor DarkMagenta
			set-WebConfigurationProperty //staticContent -Name collection[fileExtension="$ext"] -Value @{fileExtension = "$ext"; mimeType = "$mimetype" }
		} else {
			Write-Host "Info: $ext mimetype $mimetype already installed." -ForegroundColor green
		}
	}
}


######################################
## This function checks if the .net core was properly installed into the IIS.
##   if .net core installed before IIS, there are some modules missing in IIS and nothing works.
##
###############################################################################
function ValidateDotNetCoreIISInstallation {

	# if the following module is missing, installation of .net core was not done in trhe right precedence
	$aspCoreModule = Get-WebGlobalModule -Name 'AspNetCoreModuleV2'

	if (-not $aspCoreModule) {
		Write-Host ''
		Write-Host 'Installation of .net Core not valid for the Internet Information Server!' -ForegroundColor red
		Write-Host 'S t a r t  o f  h i n t '-ForegroundColor yellow
		Write-Host '  If you have installed the .net Core before you added the Internet Information Server (IIS) Feature'-ForegroundColor yellow
		Write-Host '  then the .net Core installation did not added the IIS Components'-ForegroundColor yellow
		Write-Host ''
		Write-Host '  Please reinstall/repair the .Net Core. The installation should add the required component to the IIS.' -ForegroundColor yellow
		Write-Host ''
		Write-Host '  After installation of .net core you can restart the iTWO4.0 installation. Please restart in a new Command Window.' -ForegroundColor yellow
		Write-Host '  P l e a s e   restart in a new Command Window!' -ForegroundColor Red
		Write-Host ''
		Write-Host 'E n d  o f  h i n t '-ForegroundColor yellow
		Write-Host ''
		$script:abortInstallation = $true
		exit
	}
	return $true
}

function CheckDotNetCoreVersion($major, $minor, $revision, $folder, $verify ) {

	$versionPattern = "$major.$minor.*"
	$versionselected = $null
	$versionselectedasInt = 0
	$minSearch = "$major.$minor.$revision"
	$found = Get-ChildItem -Path $folder -Filter $versionPattern
	if ($verify) {
		Write-Host -ForegroundColor green "Search in Folder: $folder for version: $minSearch"
 }
	foreach ($f in $found) {
		$versionsplit = $f.Name.Split('.')
		if ($versionsplit -and $versionsplit.length -eq 3) {
			if ([int]$major -eq [int]$versionsplit[0]) {
				if ([int]$minor -eq [int]$versionsplit[1]) {
					if ([int]$revision -le [int]$versionsplit[2]) {
						$_versionasInteger = [int]$versionsplit[0] * 10000 + [int]$($versionsplit[1]) * 100 + [int]$($versionsplit[2])
						if ($versionselectedasInt -le $_versionasInteger) {
							$versionselectedasInt = $_versionasInteger
							$versionselected = "$($versionsplit[0]).$($versionsplit[1]).$($versionsplit[2])"
							if ($verify) {
								Write-Host -ForegroundColor green "Found valid version in: $($f.FullName)"
       }
						}
					}
				}
			}
		}
	}
	return $versionselected
}

function ReportifMissing ($s, $folder, $rt, $message) {
	if ($null -eq $s) {
		$m = $message -f $rt, $folder
		Write-Host $m -ForegroundColor red
	}
}

function CheckDotNetCoreVersionBundle($version, $verify) {
	$v = $version.split('.')
	return CheckDotNetCoreVersionBundleDetail $v[0] $v[1] $v[2] $verify
}
function CheckDotNetCoreVersionBundleDetail($major, $minor, $revision, $verify) {

	$required = "$major.$minor.$revision"
	$folder1 = 'C:\Program Files\dotnet\shared\\Microsoft.NETCore.App'
	$rt1 = '.NET Core Runtime'
	$folder2 = 'C:\Program Files\dotnet\shared\Microsoft.AspNetCore.App'
	$rt2 = 'ASP.NET Core Runtime'
	$folder3 = 'C:\Program Files\dotnet\shared\Microsoft.WindowsDesktop.App'
	$rt3 = 'Windows Desktop Runtime'

	$selected1 = CheckDotNetCoreVersion $major $minor $revision $folder1 $verify
	$selected2 = CheckDotNetCoreVersion $major $minor $revision $folder2 $verify
	$selected3 = CheckDotNetCoreVersion $major $minor $revision $folder3 $verify

	if ($null -eq $selected1 -or $selected1 -ne $selected2 -or $selected2 -ne $selected3) {
		$msg = "Microsoft {0} $required is not installed."

		ReportifMissing $selected1 $folder1 $rt1 $msg
		ReportifMissing $selected2 $folder2 $rt2 $msg
		ReportifMissing $selected3 $folder3 $rt3 $msg

		return $false
	}

	return $true, $selected1, $selected2
}

######################################
## CheckDotNetCoreInstalled check dot net core installed
## sample:
##	CheckDotNetCoreInstalled
## rei@19.6.20 support new versions of dotnet, compatible with minor version
###############################################################################
function CheckDotNetCoreInstalled ([string] $version, [string] $detectVersion = $null, [bool]$addmimetype = $true) {

	$_result = CheckDotNetCoreVersionBundle $version $false
	$_isInstalled = $_result[0]
	$_installedVersion = $_result[1]

	if ($_isInstalled) {
		Write-Host -ForegroundColor $colorOk ".Net Core Version Check: found installed version: $_installedVersion, request version: $version. Prerequisites ok."
		ValidateDotNetCoreIISInstallation  # rei@25.9.19 add checking of well sequence of installation
		if ($addmimetype) {
			AddMissingMimeType -ext '.mjs' -mimetype 'application/javascript'
			AddMissingMimeType -ext '.wasm' -mimetype 'application/wasm'
			AddMissingMimeType -ext '.data' -mimetype 'application/octet-stream'
			AddMissingMimeType -ext '.json' -mimetype 'application/json'
			AddMissingMimeType -ext '.frx' -mimetype 'text/xml'
			AddMissingMimeType -ext '.woff' -mimetype 'application/x-font-woff'
			AddMissingMimeType -ext '.md' -mimetype 'text/markdown'
			AddMissingMimeType -ext '.bcf' -mimetype 'application/octet-stream'
			AddMissingMimeType -ext '.bcfzip' -mimetype 'application/octet-stream'
			AddMissingMimeType -ext '.bcmap' -mimetype 'application/octet-stream'
			AddMissingMimeType -ext '.vs' -mimetype 'shader/vertex-shader'
			AddMissingMimeType -ext '.fs' -mimetype 'shader/fragment-shader'
			AddMissingMimeType -ext '.properties' -mimetype 'application/octet-stream'
			AddMissingMimeType -ext '.bc3' -mimetype 'application/octet-stream'
			AddMissingMimeType -ext '.ftl' -mimetype 'application/octet-stream'
		}
		return $true
	}

	if ($null -eq $detectVersion) {
		$detectVersion = $version.substring(0, 3)
	}

	$dotNetDownloadUrls = GetLatestDotNetRuntimeUrls -mainversion $detectVersion

	Write-Host ''
	Write-Host "Required .NET '$version' version not installed!" -ForegroundColor $colorError
	Write-Host 'S t a r t  o f  H i n t ' -ForegroundColor $colorInfo
	Write-Host "   Please install latest version of .NET release '$detectVersion' before you can continue." -ForegroundColor $colorInfo
	Write-Host '   see folder: 00-setup\3rdparty\dotnet.core' -ForegroundColor $colorInfo
	Write-Host '      you can download and install .NET via script files found there.' -ForegroundColor $colorInfo
	Write-Host '   or' -ForegroundColor $colorInfo
	Write-Host '   from following urls (both packages must be installed)' -ForegroundColor $colorInfo

	foreach ($url in $dotNetDownloadUrls) {
		Write-Host "   url: $url" -ForegroundColor $colorInfo
	}

	Write-Host ''
	Write-Host '   After installation of .net core you can restart installation. Please restart in a new Command Window.' -ForegroundColor $colorInfo
	Write-Host '   P l e a s e restart in a new Command Window!' -ForegroundColor $colorError
	Write-Host ''
	Write-Host 'E n d  o f  H i n t ' -ForegroundColor $colorInfo
	Write-Host ''
	Write-Host ''

	$script:abortInstallation = $true
	exit
	return $false
}
