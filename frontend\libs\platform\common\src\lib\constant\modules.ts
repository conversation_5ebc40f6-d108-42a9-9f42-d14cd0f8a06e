/*
 * Copyright(c) RIB Software GmbH
 */

export type ModuleName = keyof typeof MODULES;
const MODULES = {
	'cloud.uom': {ico: 'uom', name: {text: 'CloudUoM', key: ''}},
	'basics.characteristic': {ico: 'characteristics', name: {text: 'Characteristics', key: ''}},
	'basics.clerk': {ico: 'clerk', name: {text: 'Clerk', key: ''}},
	'basics.company': {ico: 'company-structure', name: {text: 'Company', key: ''}},
	'basics.costcodes': {ico: 'cost-code', name: {text: 'CostCodes', key: ''}},
	'basics.unit': {ico: 'uom', name: {text: 'Unit', key: ''}},
	'basics.material': {ico: 'materials', name: {text: 'Material', key: ''}},
	'basics.materialcatalog': {ico: 'materials', name: {text: 'MaterialCatalog', key: ''}},
	'basics.taxcode': {ico: 'tax-code', name: {text: 'TaxCode', key: ''}},
	'basics.payment': {ico: 'payment-term', name: {text: 'PaymentTerm', key: ''}},
	'basics.procurementstructure': {ico: 'procurement-structure', name: {text: 'ProcurementStructure', key: ''}},
	'basics.procurementconfiguration': {ico: 'procurement-config', name: {text: 'ProcurementConfiguration', key: ''}},
	'basics.priceCondition': {ico: 'price-condition', name: {text: 'PriceCondition', key: ''}},
	'basics.assetmaster': {ico: 'asset-master', name: {text: 'AssetMaster', key: ''}},
	'basics.billingschema': {ico: 'billing-schema', name: {text: 'BillingSchema', key: ''}},
	'basics.site': {ico: 'site', name: {text: 'Site', key: ''}},
	'boq.main': {ico: 'boq', name: {text: 'BoqMain', key: ''}},
	'businesspartner.main': {ico: 'business-partner', name: {text: 'BusinessPartner', key: ''}},
	'businesspartner.certificate': {ico: 'certificate', name: {text: 'Certificate', key: ''}},
	'businesspartner.evaluationschema': {ico: 'bp-evaluation', name: {text: 'EvaluationSchema', key: ''}},
	'constructionsystem.main': {ico: 'construction-system', name: {text: 'ConstructionSystemInstance', key: ''}},
	'constructionsystem.master': {ico: 'construction-system-master', name: {text: 'ConstructionSystemMaster', key: ''}},
	'change.main': {ico: 'change', name: {text: 'ChangeMain', key: ''}},
	'defect.main': {ico: 'defect-main', name: {text: 'Defect', key: ''}},
	'hsqe.checklist': {ico: 'checklist', name: {text: 'CheckList', key: ''}},
	'hsqe.checklisttemplate': {ico: 'checklist-template', name: {text: 'CheckListTemplate', key: ''}},
	'estimate.main': {ico: 'estimate', name: {text: 'Estimate', key: ''}},
	'logistic.dispatching': {ico: 'dispatching', name: {text: 'LogisticDispatching', key: ''}},
	'logistic.job': {ico: 'logistic-job', name: {text: 'LogisticJob', key: ''}},
	'model.main': {ico: 'model', name: {text: 'Model', key: ''}},
	'model.changeset': {ico: 'model', name: {text: 'ModelChangeSet', key: ''}},
	'model.change': {ico: 'model', name: {text: 'ModelChange', key: ''}},
	'resource.certificate': {ico: 'plant', name: {text: 'PlantCertificate', key: ''}},
	'procurement.package': {ico: 'package', name: {text: 'Package', key: ''}},
	'procurement.requisition': {ico: 'requisition', name: {text: 'Requisition', key: ''}},
	'procurement.contract': {ico: 'contracts', name: {text: 'Contract', key: ''}},
	'procurement.quote': {ico: 'quote', name: {text: 'Quote', key: ''}},
	'procurement.rfq': {ico: 'rfq', name: {text: 'RfQ', key: ''}},
	'procurement.pes': {ico: 'pes', name: {text: 'PerformanceEntrySheet', key: ''}},
	'procurement.invoice': {ico: 'invoice', name: {text: 'Invoice', key: ''}},
	'procurement.pricecomparison': {ico: 'price-comparison', name: {text: 'PriceComparison', key: ''}},
	'project.main': {ico: 'project', name: {text: 'ProjectMain', key: 'platform.modules.moduleNames.projectMain'}},
	'sales.bid': {ico: 'sales-bid', name: {text: 'Bid', key: ''}},
	'sales.contract': {ico: 'sales-contract', name: {text: 'SalesContract', key: ''}},
	'sales.wip': {ico: 'sales-wip', name: {text: 'Wip', key: ''}},
	'sales.billing': {ico: 'sales-billing', name: {text: 'Billing', key: ''}},
	'scheduling.main': {ico: 'scheduling', name: {text: 'SchedulingMain', key: ''}},
	'scheduling.calendar': {ico: 'scheduling', name: {text: 'Calendar', key: ''}},
	'qto.main': {ico: 'qto', name: {text: 'QTO', key: ''}},
	'productionplanning.activity': {ico: 'mounting-activity', name: {text: 'PpsActivity', key: ''}},
	'productionplanning.engineering': {ico: 'engineering-planning', name: {text: 'Engineering', key: ''}},
	'productionplanning.drawing': {ico: 'engineering-tasks', name: {text: 'EngineeringDrawing', key: ''}},
	'productionplanning.item': {ico: 'production-planning', name: {text: 'PPSItem', key: ''}},
	'productionplanning.mounting': {ico: 'mounting', name: {text: 'PpsMounting', key: ''}},
	'productionplanning.productionset': {ico: 'production-sets', name: {text: 'ProductionSet', key: ''}},
	'productionplanning.report': {ico: 'mounting-report', name: {text: 'PpsReport', key: ''}},
	'productionplanning.producttemplate': {ico: 'engineering-product-template', name: {text: 'ProductTemplate', key: ''}},
	'procurement.stock': {ico: 'stock', name: {text: 'Stock', key: ''}},
	'resource.master': {ico: 'resource-master', name: {text: 'ResourceMaster', key: ''}},
	'resource.requisition': {ico: 'resource-requisition', name: {text: 'ResourceRequisition', key: ''}},
	'resource.reservation': {ico: 'resource-reservation', name: {text: 'ResourceReservation', key: ''}},
	'timekeeping.paymentgroup': {ico: 'payment-group', name: {text: 'TimekeepingPaymentGroup', key: ''}},
	'transportplanning.bundle': {ico: 'product-bundles', name: {text: 'Bundle', key: ''}},
	'transportplanning.package': {ico: 'transport-package', name: {text: 'TrsPackage', key: ''}},
	'transportplanning.requisition': {ico: 'transport-requisition', name: {text: 'TransportRequisition', key: ''}},
	'transportplanning.transport': {ico: 'transport', name: {text: 'Transport', key: ''}},
	'logistic.sundryservice': {ico: 'logistic-sundryservice', name: {text: 'LogisticSundryservice', key: ''}},
	'scheduling.schedule': {ico: 'scheduling', name: {text: '', key: ''}},
	'constructionsystem.project': {ico: 'construction-system', name: {text: '', key: ''}},
	'basics.accountingjournals': {ico: '', name: {text: '', key: ''}},
	'procurement.common': {ico: '', name: {text: '', key: ''}},
	'basics.workflowAdministration': {ico: '', name: {text: '', key: ''}},
	'model.annotation': {ico: '', name: {text: '', key: ''}},
	'controlling.structure': {ico: '', name: {text: '', key: ''}},
	'businesspartner.contact': {ico: '', name: {text: '', key: ''}},
	'logistic.settlement': {ico: '', name: {text: '', key: ''}},
	'documents.project': {ico: '', name: {text: '', key: ''}},
} as const;

// pinning context modules declarations
export const MODULE_NAMES_FOR_PINNING_CONTEXT = ['project.main', 'scheduling.main', 'estimate.main', 'constructionsystem.main', 'model.main', 'model.changeset', 'productionplanning.item',
	'productionplanning.mounting', 'productionplanning.activity', 'productionplanning.report', 'productionplanning.engineering', 'productionplanning.drawing',
	'transportplanning.requisition', 'transportplanning.bundle', 'transportplanning.transport', 'logistic.job', 'boq.main'] as const satisfies (ModuleName)[];

export type ModuleNamesForPinningContext = typeof MODULE_NAMES_FOR_PINNING_CONTEXT[number];
type ModulesForPinningContext = {
	[K in ModuleNamesForPinningContext]: typeof MODULES[K] & { token: K, info: string, title: string, sorting: number }
}

export type ModuleForPinningContext = ModulesForPinningContext[ModuleNamesForPinningContext];
export const MODULES_FOR_PINNING_CONTEXT: ModulesForPinningContext = Object.fromEntries(
	MODULE_NAMES_FOR_PINNING_CONTEXT.map((key, index) => {
		const value = {
			...MODULES[key],
			token: key,
			info: '',
			title: '',
			sorting: index + 1
		};
		return [key, value];
	})
) as ModulesForPinningContext;
