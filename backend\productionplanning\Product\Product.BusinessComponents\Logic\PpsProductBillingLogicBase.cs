using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Diagnostics.Contracts;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.LookupData.BusinessComponents;
using RIB.Visual.Basics.Unit.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.ProductionPlanning.Common.BusinessComponents;
using RIB.Visual.ProductionPlanning.Product.BusinessComponents.Workflow;
using RIB.Visual.ProductionPlanning.Product.Localization.Properties;
using static RIB.Visual.Basics.Core.Common.EntityIdentifier.Basics;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using UomEntity = RIB.Visual.Basics.Unit.BusinessComponents.UomEntity;

namespace RIB.Visual.ProductionPlanning.Product.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public abstract class PpsProductBillingLogicBase
	{
		#region ref logics
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<EngProdComponentLogic> prodComponentLogic = new Lazy<EngProdComponentLogic>(() => { return new EngProdComponentLogic(); });
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<IPpsProductLogic> _productLogic = new Lazy<IPpsProductLogic>(() => Injector.Get<IPpsProductLogic>());
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<IPpsPlannedQuantityLogic> _plannedQtyLogic = new Lazy<IPpsPlannedQuantityLogic>(() => Injector.Get<IPpsPlannedQuantityLogic>());
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<IPpsHeaderLogic> _ppsHeaderLogic = new Lazy<IPpsHeaderLogic>(() => Injector.Get<IPpsHeaderLogic>());
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<IPpsQuantityMappingLogic> _qtyMappingLogic = new Lazy<IPpsQuantityMappingLogic>(() => Injector.Get<IPpsQuantityMappingLogic>());
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<IPpsProductTemplateLogic> _prodTemplateLogic = new Lazy<IPpsProductTemplateLogic>(() => Injector.Get<IPpsProductTemplateLogic>());
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<IDispatchingRecordLogic> _dispRecordLogic = new Lazy<IDispatchingRecordLogic>(() => Injector.Get<IDispatchingRecordLogic>());
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<IDispatchingHeaderLogic> _dispHeaderLogic = new Lazy<IDispatchingHeaderLogic>(() => Injector.Get<IDispatchingHeaderLogic>());
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<IPpsItemLogic> _puLogic = new Lazy<IPpsItemLogic>(() => Injector.Get<IPpsItemLogic>());
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<BasicsUnitLogic> uomLogic = new Lazy<BasicsUnitLogic>(() => { return new BasicsUnitLogic(); });
		private Lazy<IDataBaseCreateLogic> boqItemCreationLogic = new Lazy<IDataBaseCreateLogic>(() => Injector.Get<IDataBaseCreateLogic>("Boq.Main.BoqItemEntity"));
		/// <summary>
		/// 
		/// </summary>
		protected Lazy<CreateBillWorkflowHelper> createBillHelper = new Lazy<CreateBillWorkflowHelper>(() => { return new CreateBillWorkflowHelper(); });
		private Lazy<IPpsBoqLogic> ppsBoqLogic = new Lazy<IPpsBoqLogic>(() => Injector.Get<IPpsBoqLogic>());
		#endregion

		#region properties
		/// <summary>
		/// 
		/// </summary>
		protected bool isCreateWIP = false;
		/// <summary>
		/// 
		/// </summary>
		protected DateTime? maxDate, minDate;
		/// <summary>
		/// 
		/// </summary>
		protected List<string> messages = new List<string>();
		/// <summary>
		/// 
		/// </summary>
		protected List<string> processingMessages = new List<string>();
		/// <summary>
		/// 
		/// </summary>
		protected IEnumerable<EngProdComponentEntity> filteredProdComponents = new List<EngProdComponentEntity>();
		/// <summary>
		/// 
		/// </summary>
		protected List<UomEntity> uoms = new List<UomEntity>();
		/// <summary>
		/// 
		/// </summary>
		protected List<ISalesContractHeaderEntity> ordHeaders = new List<ISalesContractHeaderEntity>();

		private static readonly List<int> IgnoreBoQLineTypes = new List<int>() { 10, 11, 101, 102, 104, 105, 106, 107, 110 };
		private string _currentProcess = string.Empty;
		/// <summary>
		/// 
		/// </summary>
		protected string CurrentProcess
		{
			get { return _currentProcess; }
			set
			{
				_currentProcess = value;
				processingMessages.Add(value);
			}
		}
		#endregion

		#region save entities
		/// <summary>
		/// 
		/// </summary>
		protected Dictionary<int, List<int>> wip2Contracts = new();
		/// <summary>
		/// 
		/// </summary>
		protected List<IBoqHeaderEntity> saveBoqHeaders = new List<IBoqHeaderEntity>();
		/// <summary>
		/// 
		/// </summary>
		protected List<IBoqItemEntity> saveRootSalesBoqItems = new List<IBoqItemEntity>();
		/// <summary>
		/// 
		/// </summary>
		protected List<IBoqItemEntity> saveRootPrjBoqItems = new List<IBoqItemEntity>();
		/// <summary>
		/// 
		/// </summary>
		protected List<ISalesBillingHeaderEntity> saveBillHeaders = new List<ISalesBillingHeaderEntity>();
		/// <summary>
		/// 
		/// </summary>
		protected List<IBillBoqEntity> saveBilBoqs = new List<IBillBoqEntity>();
		/// <summary>
		/// 
		/// </summary>
		protected List<ISalesWipHeaderEntity> saveWips = new();
		/// <summary>
		/// 
		/// </summary>
		protected List<IWipBoqEntity> saveWipBoqs = new();
		/// <summary>
		/// 
		/// </summary>
		protected List<IBoqItem2DispatchRecordEntity> saveBoqItem2DispRecords = new List<IBoqItem2DispatchRecordEntity>();
		#endregion

		/// <summary>
		/// 
		/// </summary>
		/// <param name="productIds"></param>
		/// <param name="mode"></param>
		/// <returns></returns>
		protected IEnumerable<EngProdComponentEntity> GenerateFilteredProductComponents(IEnumerable<int> productIds, GetBillComponentMode mode)
		{
			IEnumerable<EngProdComponentEntity> filteredProdComponents = Enumerable.Empty<EngProdComponentEntity>();
			if (productIds?.Any() == true)
			{
				var products = Injector.Get<IPpsProductLogic>().GetProductsWithTempalteInfo(productIds);
				List<EngProdComponentEntity> existComponents = prodComponentLogic.Value.GetByFilter(e => e.PpsProductFk.HasValue && productIds.Contains(e.PpsProductFk.Value)).ToList();

				//filter components
				if (mode == GetBillComponentMode.All) // mode = "1", All Components
				{
					filteredProdComponents = existComponents;
				}
				else if (mode == GetBillComponentMode.ProductOnly)// mode = "2", Products Only
				{
					var materialIds = products.CollectIds(e => e.MaterialFk);
					filteredProdComponents = existComponents.Where(e => e.MdcMaterialFk.HasValue && materialIds.Contains(e.MdcMaterialFk.Value)).ToList();
				}
			}

			return filteredProdComponents;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="productIds"></param>
		/// <param name="dispatchRecords"></param>
		/// <param name="dispatchHeaders"></param>
		/// <returns></returns>
		protected (
			Func<int, int?, int?, (int? boqItemId, int? boqHeaderId, IPpsPlannedQuantityEntity plannedQty)> get4ProdComponent,
			Func<int, (IPpsHeaderEntity PpsHeader, IPpsPlannedQuantityEntity PQ)> get4DispRecOfMaterial,
			Func<int, (IPpsHeaderEntity PpsHeader, IPpsPlannedQuantityEntity PQ)> get4Material,
			Dictionary<int, IPpsHeaderEntity> ppsHeaderDic
			) GenFunctionsToGetPlannedQty(IEnumerable<int> productIds,
			IEnumerable<IDispatchingRecordEntity> dispatchRecords = null, IEnumerable<IDispatchingHeaderEntity> dispatchHeaders = null)
		{
			dispatchRecords ??= Enumerable.Empty<IDispatchingRecordEntity>();
			dispatchHeaders ??= Enumerable.Empty<IDispatchingHeaderEntity>();

			CurrentProcess = "GenFunctionsToGetPlannedQty Start";

			// get various records from DB
			CurrentProcess = "GenFunctionsToGetPlannedQty -  Get various records from DB";
			var products = _productLogic.Value.GetCoresByFilter(x => productIds.Contains(x.Id));
			var puIds = products.CollectIds(x => x.ItemFk).ToList();
			var pus = _puLogic.Value.GetCoresAsListByFilter(x => puIds.Contains(x.Id));
			var headerIdsOfProds = pus.CollectIds(x => x.PPSHeaderFk).ToList();
			var receivingJobIds = dispatchHeaders.CollectIds(x => x.Job2Fk);
			var ppsHeaders = Enumerable.Empty<IPpsHeaderEntity>();
			if (dispatchHeaders.Any()) { ppsHeaders = _ppsHeaderLogic.Value.GetCoresByFilter(x => headerIdsOfProds.Contains(x.Id) || receivingJobIds.Contains(x.LgmJobFk)); }
			else { ppsHeaders = _ppsHeaderLogic.Value.GetCoresByFilter(x => headerIdsOfProds.Contains(x.Id)); }
			var ppsHeaderIds = ppsHeaders.CollectIds(h => h.Id).ToList();
			var plannedQtys = _plannedQtyLogic.Value.GetCoresAsListByFilter(x => ppsHeaderIds.Contains(x.PpsHeaderFk));
			_plannedQtyLogic.Value.BuildupTree(plannedQtys);
			var dicPlannedQty = plannedQtys.ToDictionary(x => x.Id);
			var plannedQtyIds = dicPlannedQty.Keys.ToList();
			var prodTemplateIds = products.CollectIds(p => p.ProductDescriptionFk).ToList();
			var prodTemplates = _prodTemplateLogic.Value.GetCoresByIds(prodTemplateIds);
			var qtyMappings = _qtyMappingLogic.Value.GetMappingsIncludingChildren(prodTemplateIds);

			// a, use case: should be try at first for DispatchRec of product type
			// get planned qty by PPS_QUANTITY_MAPPING
			CurrentProcess = "GenFunctionsToGetPlannedQty -  Get planned qty by PPS_QUANTITY_MAPPING";
			var mappingGrp = from mapping in qtyMappings
							 join pq in plannedQtys on mapping.PlannedQuantityFk equals pq.Id
							 group pq by mapping.ProductDescriptionFk;
			var prod2PQ = from p in products
						  join pd in prodTemplates on p.ProductDescriptionFk equals pd.Id
						  join mg in mappingGrp on p.ProductDescriptionFk equals mg.Key
						  select new
						  {
							  ProductId = p.Id,
							  PQs = mg.Where(x => x.PpsPlannedQuantityTypeFk == 2 && x.MdcMaterialFk == pd.MaterialFk),
							  PQs_Material = mg.Where(x => x.PpsPlannedQuantityTypeFk == 2 && x.MdcMaterialFk.HasValue),
							  PQs_CostCode = mg.Where(x => x.PpsPlannedQuantityTypeFk == 3 && x.MdcCostCodeFk.HasValue),
						  };
			var prod2PQByMapping_Material = prod2PQ.ToLookup(x => x.ProductId, y => y.PQs_Material.ToLookup(x => x.MdcMaterialFk.Value));
			var prod2PQByMapping_CostCode = prod2PQ.ToLookup(x => x.ProductId, y => y.PQs_CostCode.ToLookup(x => x.MdcCostCodeFk.Value));


			// b, use case: whan a is not able to find planned qty
			// get product -> PU -> ItemSource -> external price 
			// (need to consider hierarchical PU and PQ)
			var dicProd2ExternalPrice = BuildDicProduct2ItemSource(products);

			// c, use case: whan a is not able to find planned qty, and b is not able to get the price
			// get planned quantities by pps_headers
			// by product -> pu -> pps_header -> plannedQty
			CurrentProcess = "GenFunctionsToGetPlannedQty - Build lookup Product->PQ(Material/CostCode)";
			var prod2MatQty = from prod in products
							  join pu in pus on prod.ItemFk equals pu.Id
							  join ppsH in ppsHeaders on pu.PPSHeaderFk equals ppsH.Id
							  join pqL in plannedQtys.ToLookup(x => x.PpsHeaderFk) on ppsH.Id equals pqL.Key
							  select new
							  {
								  ProductId = prod.Id,
								  MaterialPQs = pqL.Where(x => x.PpsPlannedQuantityTypeFk == 2 && x.MdcMaterialFk.HasValue),
								  CostCodePQs = pqL.Where(x => x.PpsPlannedQuantityTypeFk == 3 && x.MdcCostCodeFk.HasValue)
							  };
			//var prod2QtyByPpsHeader = prod2MatQty.ToLookup(x => x.ProductId, y => y.ProdMaterialPQs.FirstOrDefault());
			var prod2QtyByPpsHeader_Material = prod2MatQty.ToLookup(x => x.ProductId, y => y.MaterialPQs.ToLookup(x => x.MdcMaterialFk.Value));
			var prod2QtyByPpsHeader_CostCode = prod2MatQty.ToLookup(x => x.ProductId, y => y.CostCodePQs.ToLookup(x => x.MdcCostCodeFk.Value));

			// d, use case: disp-record of material type
			// material dispatching record -> material planned quantities
			CurrentProcess = "GenFunctionsToGetPlannedQty - Build lookup DispRecord(MaterialType)->(PpsHeader, PQ)";
			var matDispRec2PQ = from dispRec in dispatchRecords.Where(x => x.MaterialFk.HasValue && x.RecordTypeFk == 3) // material type
								join dispH in dispatchHeaders on dispRec.DispatchHeaderFk equals dispH.Id
								join ppsH in ppsHeaders on dispH.Job2Fk equals ppsH.LgmJobFk
								join pqLK in plannedQtys.ToLookup(x => x.PpsHeaderFk) on ppsH.Id equals pqLK.Key
								select new
								{
									DispatchingRecordId = dispRec.Id,
									PpsHeader = ppsH,
									MaterialPlannedQtys = pqLK.Where(x => x.PpsPlannedQuantityTypeFk == 2 && x.MdcMaterialFk.HasValue && x.MdcMaterialFk == dispRec.MaterialFk)
								};
			var dispRec2PQByDispHeader = matDispRec2PQ.DistinctBy(x => x.DispatchingRecordId).ToLookup(x => x.DispatchingRecordId, y => (y.PpsHeader, y.MaterialPlannedQtys.FirstOrDefault()));

			var pqByMaterial = from pq in plannedQtys.Where(x => x.PpsPlannedQuantityTypeFk == 2 && x.MdcMaterialFk.HasValue)
							   group pq by pq.MdcMaterialFk.Value into mg
							   join ppsH in ppsHeaders on mg.First().PpsHeaderFk equals ppsH.Id
							   select new
							   {
								   MaterialId = mg.Key,
								   PlannedQty = mg.First(),
								   PpsHeader = ppsH
							   };
			var pqByMaterialLK = pqByMaterial.ToLookup(x => x.MaterialId);

			Func<int, int?, int?, (int? boqItemId, int? boqHeaderId, IPpsPlannedQuantityEntity plannedQty)> get4ProdComponent = (prodId, materialId, costCodeId) =>
			{
				// get plannedQty from QuantityMapping
				IPpsPlannedQuantityEntity fromPQMapping = null;
				if (materialId.HasValue)
				{
					var matPQs = prod2PQByMapping_Material[prodId].FirstOrDefault()?[materialId.Value];
					fromPQMapping = matPQs?.FirstOrDefault(x => x.BoqItemFk.HasValue) ?? matPQs?.FirstOrDefault();
				}
				else
				{
					var ccPQs = prod2PQByMapping_CostCode[prodId].FirstOrDefault()?[costCodeId.Value];
					fromPQMapping = ccPQs?.FirstOrDefault(x => x.BoqItemFk.HasValue) ?? ccPQs?.FirstOrDefault();
				}
				if (fromPQMapping != null && fromPQMapping.BoqItemFk.HasValue) { return (null, null, fromPQMapping); }


				// get boq/plannedQty from itemsource
				IPpsPlannedQuantityEntity fromItemSource = null;
				var itemSrcInfo = dicProd2ExternalPrice[prodId];
				if (itemSrcInfo.planQtyFk.HasValue)
				{
					fromItemSource = SearchPQAndChildByMaterialCostCode(itemSrcInfo.planQtyFk.Value, materialId, costCodeId); // means PU was created from PQ, need to compare childen PQ
				}
				else if (itemSrcInfo.boqItemFk.HasValue) // means PU was created from boq directly, no need to scan PQ of pps_header
				{
					var pq = itemSrcInfo.planQtyFk.HasValue ? dicPlannedQty[itemSrcInfo.planQtyFk.Value] : null;
					return (itemSrcInfo.boqItemFk, itemSrcInfo.boqHeaderFk, pq);
				}
				if (fromItemSource != null && fromItemSource.BoqItemFk.HasValue) { return (null, null, fromItemSource); }


				// no quantity mapping, compare material/costcode between component and PQ of pps_header
				IPpsPlannedQuantityEntity fromPQTree = null;
				if (materialId.HasValue)
				{
					var matPQs = prod2QtyByPpsHeader_Material[prodId].FirstOrDefault()?[materialId.Value];
					fromPQTree = matPQs?.FirstOrDefault(x => x.BoqItemFk.HasValue && !x.PlannedQuantityFk.HasValue) ?? matPQs?.FirstOrDefault(x => x.BoqItemFk.HasValue) ?? matPQs?.FirstOrDefault();
				}
				else
				{
					var ccPQs = prod2QtyByPpsHeader_CostCode[prodId].FirstOrDefault()?[costCodeId.Value];
					fromPQTree = ccPQs?.FirstOrDefault(x => x.BoqItemFk.HasValue && !x.PlannedQuantityFk.HasValue) ?? ccPQs?.FirstOrDefault(x => x.BoqItemFk.HasValue) ?? ccPQs?.FirstOrDefault();
				}
				if (fromPQTree != null && fromPQTree.BoqItemFk.HasValue) { return (null, null, fromPQTree); }


				// either a or b
				var withoutBoq = fromPQMapping ?? fromItemSource ?? fromPQTree;
				if (withoutBoq != null) { return (null, null, withoutBoq); }

				return (null, null, null);
			};

			Func<int, (IPpsHeaderEntity, IPpsPlannedQuantityEntity)> get4DispRecOfMaterial = dispRecId =>
			{
				return dispRec2PQByDispHeader[dispRecId].FirstOrDefault();
			};

			Func<int, (IPpsHeaderEntity, IPpsPlannedQuantityEntity)> get4Material = materialId =>
			{
				var tp = pqByMaterialLK[materialId];
				return (tp.FirstOrDefault()?.PpsHeader, tp.FirstOrDefault()?.PlannedQty);
			};

			CurrentProcess = "GenFunctionsToGetPlannedQty End";
			return (get4ProdComponent, get4DispRecOfMaterial, get4Material, ppsHeaders.ToDictionary(x => x.Id));

			IPpsPlannedQuantityEntity SearchPQAndChildByMaterialCostCode(Int32 planQtyId, Int32? materialId, Int32? costCodeId)
			{
				var pq = dicPlannedQty[planQtyId];
				if (pq.PpsPlannedQuantityTypeFk == 2 && pq.MdcMaterialFk.HasValue && pq.MdcMaterialFk == materialId ||
					pq.PpsPlannedQuantityTypeFk == 3 && pq.MdcCostCodeFk.HasValue && pq.MdcCostCodeFk == costCodeId)
				{ return pq; } // material/costcode match at parent level

				//search children by material/costcode
				IPpsPlannedQuantityEntity child = null;
				var children = pq.Obj2Array().Flatten(x => x.Children);
				if (materialId.HasValue)
				{
					var matchMat = children.Where(x => x.PpsPlannedQuantityTypeFk == 2 && x.MdcMaterialFk == materialId);
					child = matchMat.FirstOrDefault(x => x.BoqItemFk.HasValue) ?? matchMat.FirstOrDefault();
				}
				if (child == null && costCodeId.HasValue)
				{
					var matchCc = children.Where(x => x.PpsPlannedQuantityTypeFk == 3 && x.MdcCostCodeFk == costCodeId);
					child = matchCc.FirstOrDefault(x => x.BoqItemFk.HasValue) ?? matchCc.FirstOrDefault();
				}

				return child;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="products"></param>
		/// <returns></returns>
		protected Dictionary<int, (int? planQtyFk, int? boqItemFk, int? boqHeaderFk)> BuildDicProduct2ItemSource(IEnumerable<IPpsProductEntity> products)
		{
			if (!products.Any()) { return new Dictionary<int, (int?, int?, int?)>(); }

			CurrentProcess = "BuildDicProduct2ItemSource -  Call PPS_ITEM_SOURCE_INFO_SP";
			var puIds = products.CollectIds(x => x.ItemFk).ToList();
			List<PpsItemSourceInfoEntity> puSourceInfo = null;
			using (var dbCtx = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				DataTable puIdsTable = new DataTable();
				puIdsTable.Columns.Add("ID", typeof(int));
				puIds.ForEach(x => puIdsTable.Rows.Add(x));
				puSourceInfo = dbCtx.ExecuteStoredProcedure<PpsItemSourceInfoEntity>("PPS_ITEM_SOURCE_INFO_SP",
						new SqlParameter("@ItemIds", SqlDbType.Structured)
						{
							TypeName = "dbo.UDTT_IDS",
							Value = puIdsTable
						}
					).ToList();
			}

			//get all planned quantities
			CurrentProcess = "BuildDicProduct2ItemSource -  Get all planned quantities and build dictionary Product->PQ/Boq";
			var plannedQtyIds = puSourceInfo.CollectIds(x => x.PPS_PLANNED_QUANTITY_FK); // pu created from PQ
			var allPlannedQtys = plannedQtyIds.Any() ? _plannedQtyLogic.Value.GetCoresAsListByFilter(x => plannedQtyIds.Contains(x.Id)) : Enumerable.Empty<IPpsPlannedQuantityEntity>();

			var dic = from p in products
					  join puInfo in puSourceInfo on p.ItemFk equals puInfo.PUId
					  select new
					  {
						  ProductId = p.Id,
						  ExternalPrice = puInfo.EXTERNALPRICE,
						  BasUomFk = puInfo.BAS_UOM_FK,
						  PlannedQtyFk = puInfo.PPS_PLANNED_QUANTITY_FK,
						  BoqItemFk = puInfo.BOQ_ITEM_FK,
						  BoqHeaderFk = puInfo.BOQ_HEADER_FK
					  };
			return dic.ToDictionary(x => x.ProductId, y => (y.PlannedQtyFk, y.BoqItemFk, y.BoqHeaderFk));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="billingSource"></param>
		/// <param name="ppsHeaderDic"></param>
		/// <returns></returns>
		protected List<BillingItemSource> GetContractedBillingSource(List<BillingItemSource> billingSource, Dictionary<Int32, IPpsHeaderEntity> ppsHeaderDic)
		{
			// a.make sure all pps_header must link to ord_header, because we need to create wip/bill for each main contract
			// b.make sure project is the same between pps_header and ord_header
			var salesHeaderLogic = Injector.Get<ISalesHeaderLogic>("Contract");
			ordHeaders = salesHeaderLogic.GetSalesContractsByKey(ppsHeaderDic.Values.Select(x => x.OrdHeaderFk)).ToList();
			var dicOrdHeader = ordHeaders.ToDictionary(x => x.Id);

			var ppsHeaderWithoutContract = new List<IPpsHeaderEntity>();
			var ppsHeaderLinkedContractWithWrongPrj = new List<int>();
			List<BillingItemSource> billingItemWithContract = new List<BillingItemSource>();
			CurrentProcess = "CreateBillingItemSources - Ensure pps_header linked to ord_header"; // WIP_HEADER.ORD_HEADER_FK is not nullable
			billingSource.ForEach(x =>
			{
				var ppsHeader = ppsHeaderDic[x.PlannedQty.PpsHeaderFk];
				if (ppsHeader.OrdHeaderFk.HasValue)
				{
					var ordHeader = dicOrdHeader[ppsHeader.OrdHeaderFk.Value];
					if (ordHeader.ProjectFk == ppsHeader.PrjProjectFk)
					{
						x.OrdHeaderId = ppsHeader.OrdHeaderFk.Value;
						billingItemWithContract.Add(x);
					}
					else if (!ppsHeaderLinkedContractWithWrongPrj.Contains(ppsHeader.Id))
					{
						ppsHeaderLinkedContractWithWrongPrj.Add(ppsHeader.Id);
						messages.Add(string.Format(Resources.Err_ProjectShouldBeTheSameBetweenContractAndPpsHeader, ppsHeader.Id, ordHeader.Id));
					}
				}
				else { ppsHeaderWithoutContract.Add(ppsHeader); }
			});
			if (ppsHeaderWithoutContract.Any())
			{
				messages.Add(string.Format(Resources.Err_ContractMustLinkedToPpsHeader, string.Join(",", ppsHeaderWithoutContract.CollectIds(x => x.Id))));
			}

			// make sure PlannedQty.BillUoMFk is not null
			var pqWithoutBillUom = billingItemWithContract.Where(x => x.PlannedQty?.BasUomBillFk == null).Select(x => x.PlannedQty);
			if (pqWithoutBillUom.Any())
			{
				var msg = string.Join(", ", pqWithoutBillUom.Select(pq => $"(ID:{pq.Id}, Des:{pq.Description})"));
				messages.Add(string.Format(Resources.Err_PlannedQtyWithoutBillUoM, msg));
			}

			return billingItemWithContract.Where(x => x.PlannedQty?.BasUomBillFk != null).ToList();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected virtual IEnumerable<int> GetUomIdsWithoutBoq(IEnumerable<BillingItemSource> billingSource)
		{
			return filteredProdComponents.CollectIds(e => e.BasUomBillFk)
				.Concat(billingSource.CollectIds(e => e.PlannedQty.BasUomBillFk))
				.Distinct();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="billingSource"></param>
		/// <param name="uomIdsFromBoq"></param>
		/// <returns></returns>
		protected virtual IEnumerable<int> GetUomIdsWithBoq(IEnumerable<BillingItemSource> billingSource, IEnumerable<int> uomIdsFromBoq)
		{
			var uomIds = filteredProdComponents.CollectIds(e => e.BasUomBillFk);
			if (uomIdsFromBoq?.Any() == true)
			{
				return uomIds.Concat(uomIdsFromBoq).Distinct();
			}
			return uomIds.Distinct();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ordHeaderId"></param>
		/// <param name="createWipData"></param>
		/// <returns></returns>
		protected ISalesHeaderEntity CreateWipOrBillHeader(int ordHeaderId, WipHeaderCreationData createWipData = null)
		{
			var orderHeader = ordHeaders.First(e => e.Id == ordHeaderId);

			CurrentProcess = $"CreateSingleSalesHeader - Creating Wip/Bill header for order header:(ID={ordHeaderId})";
			ISalesHeaderEntity saleHeader = null;
			if (createWipData != null)
			{
				try
				{
					saleHeader = Injector.Get<ISalesWipHeaderLogic>().CreateWipHeaderEntity(createWipData, true);
				}
				catch(Exception ex)
				{
					if(ex.Source == "RIB.Visual.Basics.Company.BusinessComponents")
					{
						throw new Exception(string.Format(Resources.Err_FailedCodeGeneration, ex.Message), ex);
					}
					else
					{
						throw;
					}
				}
			}
			else
			{
				saleHeader = Injector.Get<IDataBaseCreateLogic>("Sales.Billing.BillHeaderEntity").Create(new IdentificationData() { Id = orderHeader.ProjectFk }) as ISalesBillingHeaderEntity;
			}

			CurrentProcess = $"CreateSingleSalesHeader - SetBillHeader for order header:(ID={ordHeaderId})";
			var errorMessage = SetBillHeader(saleHeader, orderHeader);
			if (errorMessage != null)
			{
				messages.Add(string.Format(Resources.Err_SetBillHeaderFailed, orderHeader.Id, errorMessage));
				return null;
			}
			return saleHeader;
		}

		/// <summary>
		/// Set bilHeader
		/// </summary>
		/// <param name="salesHeader"></param>
		/// <param name="ordHeader"></param>
		/// <returns>String with error message. Null if no error was found.</returns>
		private string SetBillHeader(ISalesHeaderEntity salesHeader, ISalesContractHeaderEntity ordHeader)
		{
			if (salesHeader is ISalesBillingHeaderEntity bilHeader)
			{
				bilHeader.OrdHeaderFk = ordHeader != null ? ordHeader.Id : bilHeader.OrdHeaderFk;
				bilHeader.PerformedFrom = minDate;
				bilHeader.PerformedTo = maxDate;
				bilHeader.CompanyResponsibleFk = bilHeader.CompanyFk;
				bilHeader.ContractTypeFk = 1;
			}

			if (salesHeader is ISalesWipHeaderEntity wipHeader)
			{
				wipHeader.OrdHeaderFk = ordHeader != null ? ordHeader.Id : wipHeader.OrdHeaderFk;
				wipHeader.PerformedFrom = minDate;
				wipHeader.PerformedTo = maxDate;
				wipHeader.CompanyResponsibleFk = wipHeader.CompanyFk;
			}

			#region Validate and set business information

			//project data as fallback
			var projectInfo = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();
			var project = salesHeader.ProjectFk > 0 ? projectInfo.GetProjectById(salesHeader.ProjectFk) : null;

			int? customerFk = ordHeader != null && ordHeader.CustomerFk.HasValue
				? ordHeader.CustomerFk
				: (project != null ? project.CustomerFk : null);
			int? subsidiaryFk = ordHeader != null && ordHeader.SubsidiaryFk.HasValue
				? ordHeader.SubsidiaryFk
				: (project != null ? project.SubsidiaryFk : null);
			int? businessPartnerFk = ordHeader != null && ordHeader.BusinesspartnerFk.HasValue
				? ordHeader.BusinesspartnerFk
				: (project != null ? project.BusinessPartnerFk : null);

			if (!customerFk.HasValue || !subsidiaryFk.HasValue || !businessPartnerFk.HasValue)
			{
				var missingFieldsList = new List<string>();
				if (!customerFk.HasValue)
				{
					missingFieldsList.Add("CustomerFk");
				}
				if (!subsidiaryFk.HasValue)
				{
					missingFieldsList.Add("SubsidiaryFk");
				}
				if (!businessPartnerFk.HasValue)
				{
					missingFieldsList.Add("BusinessPartnerFk");
				}

				var missingFieldsText = string.Join(",", missingFieldsList);
				var orderHeaderText = ordHeader != null ? ordHeader.Code : "";
				var projectText = project != null ? project.ProjectName : salesHeader.ProjectFk.ToString();
				return string.Format(Resources.MissingBusinesspartnerInfo, missingFieldsText, orderHeaderText, projectText);
			}
			else
			{
				if (salesHeader is ISalesBillingHeaderEntity bilHeader2)
				{
					bilHeader2.CustomerFk = customerFk.Value;
					bilHeader2.SubsidiaryFk = subsidiaryFk;
					bilHeader2.BusinesspartnerFk = businessPartnerFk.Value;
				}

				if (salesHeader is ISalesWipHeaderEntity wipHeader2)
				{
					wipHeader2.CustomerFk = customerFk.Value;
					wipHeader2.SubsidiaryFk = subsidiaryFk;
					wipHeader2.BusinesspartnerFk = businessPartnerFk.Value;
				}
			}

			#endregion

			if (ordHeader != null)
			{
				salesHeader.TaxCodeFk = ordHeader.TaxCodeFk;

				if (salesHeader is ISalesBillingHeaderEntity bilHeader3)
				{
					//use PaymentTerm Fi or Pa
					bilHeader3.PaymentTermFk = ordHeader.PaymentTermFiFk ?? ordHeader.PaymentTermPaFk ?? -1;
					//if neither are set: throw exception
					if (bilHeader3.PaymentTermFk == -1)
					{
						var msg = string.Format(Resources.MissingPaymentTermInfo, ordHeader.Code);
						messages.Add(msg);
						ThrowBZLayerException(msg);
					}

					bilHeader3.PaymentTermFiFk = ordHeader.PaymentTermFiFk;
					bilHeader3.PaymentTermPaFk = ordHeader.PaymentTermPaFk;
				}
			}

			if (salesHeader is ISalesBillingHeaderEntity bilHeaderCode)
			{
				if (string.IsNullOrWhiteSpace(bilHeaderCode.Code))
				{
					try
					{
						var clerk = Injector.Get<IGetClerkLogic>().GetItemByKey(bilHeaderCode.ClerkFk);
						var contract = bilHeaderCode.OrdHeaderFk != null ? Injector.Get<ISalesContractLogic>("Contract").GetContractById((int)bilHeaderCode.OrdHeaderFk, false) : null;
						var objectUnit = bilHeaderCode.ObjUnitFk != null ? Injector.Get<IObjectUnitProvider>("object.main.unit").GetById((int)bilHeaderCode.ObjUnitFk) : null;
						var controllingUnit = bilHeaderCode.ControllingUnitFk != null ? Injector.Get<IControllingUnitLogic>().GetControllingUnitById((int)bilHeaderCode.ControllingUnitFk).FirstOrDefault() : null;
						var prcStructureEntity = bilHeaderCode.PrcStructureFk != null ? Injector.Get<IPrcStructureLogic>().GetPrcStructureById((int)bilHeaderCode.PrcStructureFk) : null;

						var sequenceConfig = new CompanySequenceConfig("Sales.Billing.BilHeaderEntity")
						{
							ProjectNo = project.ProjectNo,
							ProjectFk = bilHeaderCode.ProjectFk,
							ClerkCode = clerk is IClerkInfo ? ((IClerkInfo)clerk).Code : "",
							ClerkFk = bilHeaderCode.ClerkFk,
							SalesContractCode = contract != null ? contract.Code : "",
							SalesContractFk = contract != null ? contract.Id : (int?)null,
							ObjectUnitCode = objectUnit != null ? objectUnit.Code : "",
							ControllingUnitCode = controllingUnit != null ? controllingUnit.Code : "",
							ProcurementStructureCode = prcStructureEntity != null ? prcStructureEntity.Code : "",
							RubricIndex = null,
						};
						bilHeaderCode.Code = new BasicsCompanyNumberLogic().CreateCompanyNumber(bilHeaderCode.RubricCategoryFk, sequenceConfig);
					}
					catch (Exception ex)
					{
						if (ex.Source == "RIB.Visual.Basics.Company.BusinessComponents")
						{
							throw new Exception(string.Format(Resources.Err_FailedCodeGeneration, ex.Message), ex);
						}
						else
						{
							throw;
						}
					}					
				}
			}

			if (string.IsNullOrWhiteSpace(salesHeader.Code))
			{
				// Only billing could be with empty code; WIP would be set a sequence number as for empty
				var msg = string.Format(Resources.Err_NoCodeGenerationForBilling);
				messages.Add(msg);
				ThrowBZLayerException(msg);
			}

			//return no error message
			return null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="billingSource"></param>
		protected void PrepareEntitiesToSaveWithoutBoq(IEnumerable<BillingItemSource> billingSource)
		{
			CurrentProcess = "PrepareEntitiesToSave Start";

			if (billingSource?.Any() != true) { CurrentProcess = "PrepareEntitiesToSave - Empty billingSource"; return; }

			// needs to cache uoms of dispatch records, prod components and planned quantities
			var uomIds = GetUomIdsWithoutBoq(billingSource);
			uoms = uomIds != null && uomIds.Any() ? uomLogic.Value.GetUoM(e => uomIds.Contains(e.Id)).ToList() : new List<UomEntity>();

			WipHeaderCreationData BuildWipHeaderCreationData(int mainContractId)
			{
				if (!isCreateWIP) { return null; }

				var filterRequest = new Basics.Common.Core.Final.LookupSearchRequest();
				filterRequest.AdditionalParameters.Add("Rubric", 17);
				filterRequest.FilterKey = "rubric-category-by-rubric-company-lookup-filter";
				var rubrics = new RubricCategoryLogic().GetRubricCategoryByCompanyId(filterRequest);
				var defaultRubrics = rubrics.FirstOrDefault(e => e.IsDefault) ?? rubrics.First();
				var mainContractEntity = ordHeaders.First(e => e.Id == mainContractId);
				return new WipHeaderCreationData
				{
					RubricCategoryFk = defaultRubrics.Id, //rubricCategoryId,
														  //ConfigurationId = creationData.ConfigurationId,
					CompanyFk = mainContractEntity.CompanyFk,
					ResponsibleCompanyFk = mainContractEntity.CompanyResponsibleFk,
					ProjectFk = mainContractEntity.ProjectFk,
					LanguageFk = mainContractEntity.LanguageFk,
					CurrencyFk = mainContractEntity.CurrencyFk,
					ClerkFk = mainContractEntity.ClerkFk,
					OrdHeaderFk = mainContractEntity.Id,         // TODO: Multi-WIP
				};
			}

			CurrentProcess = "PrepareEntitiesToSave - Creating Wip/Bill Headers+Boqs";
			// foreach project, we create a wip/bill, because wip/bil boq should reference to project boq item
			Dictionary<int, Dictionary<string, IBoqItemEntity>> dicPrjBoqs = new(); // key: projectId, value:<key:boqRefNo, value:prjBoq>
			foreach (var grp in billingSource.GroupBy(x => x.OrdHeaderId.Value))
			{
				var mainContractId = grp.Key;
				var wipHeaderCreationData = isCreateWIP ? BuildWipHeaderCreationData(mainContractId) : null; // CreateBillingItemSources already ensure contract not null
				var salesHeader = CreateWipOrBillHeader(mainContractId, wipHeaderCreationData);
				if (salesHeader is null)
				{
					messages.Add(isCreateWIP ? String.Format(Resources.Err_CannotCreateWIP, grp.First().ProjectId) : String.Format(Resources.Err_CannotCreateBill, grp.First().ProjectId));
					continue;
				}

				CreateSingleSalesHeaderWithoutBoq(mainContractId, salesHeader, grp.AsEnumerable(), dicPrjBoqs, uoms);
				wip2Contracts.Add(salesHeader.Id, new List<int> { mainContractId });
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="ordHeaderId"></param>
		/// <param name="saleHeader"></param>
		/// <param name="billingSource"></param>
		/// <param name="dicPrjBoqs"></param>
		/// <param name="uoms"></param>
		protected void CreateSingleSalesHeaderWithoutBoq(int ordHeaderId, ISalesHeaderEntity saleHeader, IEnumerable<BillingItemSource> billingSource,
			Dictionary<int, Dictionary<string, IBoqItemEntity>> dicPrjBoqs, IEnumerable<UomEntity> uoms)
		{
			var prjId = billingSource.First().ProjectId;

			#region rootPrjBoq, materialPrjBoqs, costCodePrjBoqs
			CurrentProcess = $"CreateSingleSalesHeader - get or create project boqs for contract: " + ordHeaderId;
			Dictionary<int, IBoqItemEntity> pq2PrjBoq;
			GetOrCreatePrjBoqStructure(billingSource, dicPrjBoqs, out pq2PrjBoq); // will build up pq2PrjBoq
			var prjBoqRefDic = dicPrjBoqs[prjId];
			var prjBoqIdDic = dicPrjBoqs[prjId].Values.ToDictionary(x => x.Id);
			IBoqItemEntity rootPrjBoq = prjBoqRefDic.Values.First(x => x.BoqItemFk == null);
			PropertyInfo Prop_BoqItemPrjBoqFk = rootPrjBoq.GetType().GetProperty("BoqItemPrjBoqFk");
			PropertyInfo Prop_BoqItemPrjItemFk = rootPrjBoq.GetType().GetProperty("BoqItemPrjItemFk");
			#endregion

			CurrentProcess = $"CreateSingleSalesHeader - boqHeaderLogic.CreateAndSave a new boq header for WIP/Bill";
			var boqHeaderLogic = Injector.Get<IBoqHeaderLogic>();
			var billBoqHeader = boqHeaderLogic.CreateAndSave(true, saleHeader.CurrencyFk);
			billBoqHeader.GetType().GetProperty("BoqHeaderFk").SetValue(billBoqHeader, rootPrjBoq.BoqHeaderFk);
			boqHeaderLogic.Save(billBoqHeader);
			saveBoqHeaders.Add(billBoqHeader);

			// create bill boq
			Dictionary<string, IBoqItemEntity> billBoqRefDic = new();
			foreach (var billingItem in billingSource)
			{
				billingItem.ClearBoqInfo();
				var billBoq = CopyToNewBoqItem(billingItem);
				billingItem.BillBoq = billBoq;
			}
			var rootSalesBoq = billBoqRefDic[rootPrjBoq.Reference];
			saveRootSalesBoqItems.Add(rootSalesBoq);

			if (!billBoqRefDic.Values.Any())
			{
				CurrentProcess = $"No WIP/Bill boq was created for project (ID:{prjId})";
				messages.Add(CurrentProcess);

				boqHeaderLogic.Delete(billBoqHeader); // remove created boq header
				saveBoqHeaders.Remove(billBoqHeader);
				return;
			}
			CurrentProcess = $"CreateSingleSalesHeader - Created totally {billBoqRefDic.Values.Count()} boq items for contract(ID:{ordHeaderId})";

			// Save connection between WipHeader/BillHeader and boqHeader
			CurrentProcess = "CreateSingleSalesHeader - Save connection between WipHeader/BillHeader and boqHeader";
			if (saleHeader is ISalesWipHeaderEntity)
			{
				saveWips.Add(saleHeader as ISalesWipHeaderEntity);
				var wipBoq = Injector.Get<ISalesWipBoqLogic>("Wip").Create();
				wipBoq.BoqHeaderFk = billBoqHeader.Id;
				wipBoq.WipHeaderFk = saleHeader.Id;
				saveWipBoqs.Add(wipBoq);
			}
			else
			{
				saveBillHeaders.Add(saleHeader as ISalesBillingHeaderEntity);
				var bilBoq = Injector.Get<IDataBaseCreateLogic>("Sales.Billing.BilBoqEntity").Create(new IdentificationData()) as IBillBoqEntity;
				bilBoq.BilHeaderFk = saleHeader.Id;
				bilBoq.BoqHeaderFk = billBoqHeader.Id;
				saveBilBoqs.Add(bilBoq);
			}

			// link salesboq to disp_record
			CurrentProcess = "CreateSingleSalesHeader - HandleBoqItem2DispRecord";
			HandleBoqItem2DispRecord(billingSource);

			IBoqItemEntity CopyToNewBoqItem(BillingItemSource billingItem)
			{
				var prjBoq = pq2PrjBoq[billingItem.PlannedQty.Id];
				var billBoq = GetOrCreateSalesBoq(prjBoq);
				return billBoq;

				IBoqItemEntity GetOrCreateSalesBoq(IBoqItemEntity prjBoq)
				{
					var plannedQty = billingItem.PlannedQty;
					IBoqItemEntity billBoq = null;
					if (billBoqRefDic.TryGetValue(plannedQty.BoqRefNo, out billBoq))
					{
						AggregateQuantity();
					}
					else
					{
						billBoq = CreateSalesBoq(prjBoq, billingItem.PlannedQty, billingItem);
					}
					return billBoq;

					IBoqItemEntity CreateSalesBoq(IBoqItemEntity prjBoq, IPpsPlannedQuantityEntity plannedQty, BillingItemSource billingItem)
					{
						var boq = CreateBoq();
						CopyBoqProperties(boq, prjBoq);
						CopyPropsFromPQToBoq(boq, plannedQty, billingItem);
						boq.BoqHeaderFk = billBoqHeader.Id;
						Prop_BoqItemPrjBoqFk.SetValue(boq, prjBoq.BoqHeaderFk);
						Prop_BoqItemPrjItemFk.SetValue(boq, prjBoq.Id);
						billBoqRefDic[boq.Reference] = boq;

						BuildBoqParent(prjBoq, boq);

						return boq;
					}

					void BuildBoqParent(IBoqItemEntity prjBoq, IBoqItemEntity billBoq)
					{
						if (prjBoq == null || billBoq == null) { return; }

						var currentPrjBoq = prjBoq;
						var currentBillBoq = billBoq;
						while (currentPrjBoq.BoqItemFk != null)
						{
							var parentPrjBoq = prjBoqIdDic[currentPrjBoq.BoqItemFk.Value];
							if (billBoqRefDic.TryGetValue(parentPrjBoq.Reference, out var existingParentBillBoq))
							{
								LinkParentChild(existingParentBillBoq, currentBillBoq);
								break;
							}

							var parentBillBoq = CreateBoq();
							CopyBoqProperties(parentBillBoq, parentPrjBoq);
							parentBillBoq.BoqHeaderFk = billBoqHeader.Id;
							Prop_BoqItemPrjBoqFk.SetValue(parentBillBoq, parentPrjBoq.BoqHeaderFk);
							Prop_BoqItemPrjItemFk.SetValue(parentBillBoq, parentPrjBoq.Id);
							billBoqRefDic[parentBillBoq.Reference] = parentBillBoq;
							LinkParentChild(parentBillBoq, currentBillBoq);

							currentPrjBoq = parentPrjBoq;
							currentBillBoq = parentBillBoq;
						}
					}

					void AggregateQuantity()
					{
						billBoq.Quantity += createBillHelper.Value.ConvertQuantity(billingItem.ProductComponent?.BillingQuantity ?? billingItem.DispatchingRecord.Quantity,
								billingItem.ProductComponent?.BasUomBillFk ?? billingItem.DispatchingRecord.UoMFk, billBoq.BasUomFk, uoms);
					}
				}
			}
		}

		private void GetOrCreatePrjBoqStructure(IEnumerable<BillingItemSource> billingSource, Dictionary<int, Dictionary<string, IBoqItemEntity>> dicPrjBoqs, out Dictionary<int, IBoqItemEntity> pq2PrjBoq)
		{
			pq2PrjBoq = new();
			if (billingSource?.Any() != true) { return; }

			var prjId = billingSource.First().ProjectId;

			IBoqItemEntity rootPrjBoq = null;

			// fetch project boq and build dictionary
			if (dicPrjBoqs.TryGetValue(prjId, out var boqs))
			{
				rootPrjBoq = boqs.Values.First(x => x.BoqItemFk == null);
			}
			else
			{
				var prjBoqs = ppsBoqLogic.Value.GetPrjBoqs(prjId);
				rootPrjBoq = prjBoqs?.FirstOrDefault(x => x.BoqItemFk == null);
				if (rootPrjBoq == null)
				{
					rootPrjBoq = ppsBoqLogic.Value.CreateRootPrjBoq(prjId); // which will create a root boq
					prjBoqs = new List<IBoqItemEntity>() { rootPrjBoq };
				}
				dicPrjBoqs.Add(prjId, prjBoqs.ToDictionary(x => x.Reference));
			}

			var prjBoqDic = dicPrjBoqs[prjId];
			var plannedQtys = billingSource.Select(x => x.PlannedQty);
			var pqWithoutBoqRef = plannedQtys.Where(x => string.IsNullOrWhiteSpace(x.Reference)).ToList();
			var pqWithBoqRef = plannedQtys.Where(x => !string.IsNullOrWhiteSpace(x.Reference)).ToList();
			#region 1. setup specified boq structure by BoqReferenceInfo
			List<(IPpsPlannedQuantityEntity PlannedQty, BoqReferenceInfo[] RefInfo)> boqRefs = new();// Enumerable.Empty<(IPpsPlannedQuantityEntity PlannedQty, BoqReferenceInfo[] RefInfo)>();
			try
			{
				pqWithBoqRef.ForEach(x => boqRefs.Add((x, JsonConvert.DeserializeObject<BoqReferenceInfo[]>(x.Reference))));
			}
			catch (Exception ex)
			{
				messages.Add(Resources.Err_PQReferenceNotJsonFormat);
				ThrowBZLayerException(Resources.Err_PQReferenceNotJsonFormat, ex);
			}
			#region ensure valid BoqReferenceInfo, length should be greater than 1
			// equal to 1, means boq position as a root, not possible
			// equal to 0, means reference like '[]', invalid
			var pqRefPointToRoot = boqRefs.Where(x => x.RefInfo.Length < 2).Select(x => x.PlannedQty);
			if (pqRefPointToRoot.Any())
			{
				var msg = string.Join(", ", pqRefPointToRoot.Select(x => $"Id:{x.Id} Des:{x.Description}"));
				messages.Add(msg);
				ThrowBZLayerException(string.Format(Resources.Err_PQReferenceTooShort, msg));
			}
			// ensure "BoqRefNo" is not empty
			var pqRefWithEmptyRefNo = boqRefs.Where(x => x.RefInfo.Any(y => string.IsNullOrWhiteSpace(y.BoqRefNo))).Select(x => x.PlannedQty);
			if (pqRefWithEmptyRefNo.Any())
			{
				var msg = string.Join(", ", pqRefWithEmptyRefNo.Select(x => $"Id:{x.Id} Des:{x.Description}"));
				messages.Add(msg);
				ThrowBZLayerException(string.Format(Resources.Err_PQReferenceWithEmptyRefNo, msg));
			}
			// ensure the same root
			var rootPrjBoqRefNos = boqRefs.Select(x => x.RefInfo).Select(x => x.First()).DistinctBy(x => x.BoqRefNo);
			if (rootPrjBoqRefNos.Count() > 1)
			{
				var msg = string.Join(", ", rootPrjBoqRefNos.Select(x => x.BoqRefNo));
				messages.Add(msg);
				ThrowBZLayerException(string.Format(Resources.Err_PQReferenceRootNotUnique, msg));
			}
			// ensure leaf is not a node
			var leaves = boqRefs.Select(x => x.RefInfo).Select(x => x.Last()).DistinctBy(x => x.BoqRefNo);
			var nodes = boqRefs.Select(x => x.RefInfo).SelectMany(x => x.SkipLast(1));
			var intersect = nodes.Where(n => leaves.Any(l => l.BoqRefNo == n.BoqRefNo)).DistinctBy(n => n.BoqRefNo);
			if (intersect.Any())
			{
				var msg = string.Join(", ", intersect.Select(x => x.BoqRefNo));
				messages.Add(msg);
				ThrowBZLayerException(string.Format(Resources.Err_PQReferenceRefNoError, msg));
			}
			#endregion
			// create missing prj boqs
			var prjBoqsDic = dicPrjBoqs[prjId];
			var boqLevel = -1;
			var arrayLenLonger = false;
			var needToSavePrjBoq = false;
			do
			{
				boqLevel++;
				arrayLenLonger = false;
				foreach (var tp in boqRefs)
				{
					if (boqLevel >= tp.RefInfo.Length) { continue; }

					arrayLenLonger = true;
					var boqRef = tp.RefInfo[boqLevel];
					if (boqLevel == 0) // sync root prj boq because it must existing
					{
						if (rootPrjBoq.Reference != boqRef.BoqRefNo || rootPrjBoq.BriefInfo?.Description != boqRef.OutlineSpec)
						{
							prjBoqsDic.Remove(rootPrjBoq.Reference);
							needToSavePrjBoq = true;
							rootPrjBoq.Reference = boqRef.BoqRefNo;
							rootPrjBoq.BriefInfo = new DescriptionTranslateType(boqRef.OutlineSpec);
							prjBoqsDic[rootPrjBoq.Reference] = rootPrjBoq;
						}
						break; // already ensure the roots are the same before
					}

					if (prjBoqsDic.TryGetValue(boqRef.BoqRefNo, out var existingPrjBoq)) // existing, prj boq, so skip
					{
						if (boqLevel == tp.RefInfo.Length - 1)
						{
							pq2PrjBoq[tp.PlannedQty.Id] = existingPrjBoq;
							tp.PlannedQty.BoqRefNo = boqRef.BoqRefNo;
							tp.PlannedQty.BoqOutlineSpec = boqRef.OutlineSpec;
						}
						continue;
					}

					// create prj boq because not existing
					needToSavePrjBoq = true;
					var newPrjBoq = CreateBoq();
					newPrjBoq.Reference = boqRef.BoqRefNo;
					newPrjBoq.BriefInfo = new DescriptionTranslateType(boqRef.OutlineSpec);
					newPrjBoq.BoqHeaderFk = rootPrjBoq.BoqHeaderFk;
					if (boqLevel == tp.RefInfo.Length - 1)
					{
						CopyPropsFromPQToBoq(newPrjBoq, tp.PlannedQty, null);
						pq2PrjBoq[tp.PlannedQty.Id] = newPrjBoq;
						tp.PlannedQty.BoqRefNo = boqRef.BoqRefNo;
						tp.PlannedQty.BoqOutlineSpec = boqRef.OutlineSpec;
					}
					var parentBoq = prjBoqsDic[tp.RefInfo[boqLevel - 1].BoqRefNo];
					LinkParentChild(parentBoq, newPrjBoq);
					prjBoqsDic[boqRef.BoqRefNo] = newPrjBoq;
				}
			} while (arrayLenLonger);
			#endregion

			#region 2. without speficif structure, set as children to the rootPrjBoq
			var materialPrjBoqs = prjBoqDic.Values.Where(x => x.MdcMaterialFk.HasValue).ToLookup(x => x.MdcMaterialFk.Value).ToDictionary(x => x.Key, y => y.First());
			var costCodePrjBoqs = prjBoqDic.Values.Where(x => x.MdcCostCodeFk.HasValue).ToLookup(x => x.MdcCostCodeFk.Value).ToDictionary(x => x.Key, y => y.First());
			int autoRefNo = 0;
			foreach (var pq in pqWithoutBoqRef)
			{
				IBoqItemEntity newPrjBoq = null;
				IBoqItemEntity existingPrjBoq = null;
				if (pq.PpsPlannedQuantityTypeFk == 2)
				{
					if (!materialPrjBoqs.TryGetValue(pq.MdcMaterialFk.Value, out existingPrjBoq)) // no prj boq with the same material, need to create new prj boq
					{
						newPrjBoq = CreateBoq();
						materialPrjBoqs[pq.MdcMaterialFk.Value] = newPrjBoq;
					}
				}
				else if (pq.PpsPlannedQuantityTypeFk == 3)
				{
					if (!costCodePrjBoqs.TryGetValue(pq.MdcCostCodeFk.Value, out existingPrjBoq))  // no prj boq with the same cost code, need to create new prj boq
					{
						newPrjBoq = CreateBoq();
						costCodePrjBoqs[pq.MdcCostCodeFk.Value] = newPrjBoq;
					}
				}

				if (existingPrjBoq != null)
				{
					pq2PrjBoq[pq.Id] = existingPrjBoq;
					pq.BoqRefNo = existingPrjBoq.Reference;
					pq.BoqOutlineSpec = existingPrjBoq.BriefInfo.Description;
				}
				if (newPrjBoq != null)
				{
					needToSavePrjBoq = true;
					newPrjBoq.Reference = GetBoqRefNo();
					newPrjBoq.BriefInfo = new DescriptionTranslateType(pq.Description);
					newPrjBoq.BoqHeaderFk = rootPrjBoq.BoqHeaderFk;
					pq.BoqRefNo = newPrjBoq.Reference;
					pq.BoqOutlineSpec = newPrjBoq.BriefInfo.Description;
					CopyPropsFromPQToBoq(newPrjBoq, pq, null);
					LinkParentChild(rootPrjBoq, newPrjBoq);
					pq2PrjBoq[pq.Id] = newPrjBoq;
				}
			}
			#endregion

			if (needToSavePrjBoq)
			{
				saveRootPrjBoqItems.Add(rootPrjBoq);
			}

			string GetBoqRefNo()
			{
				string refNo = null;
				do
				{
					refNo = $"{++autoRefNo}.";
				} while (prjBoqDic.ContainsKey(refNo));
				return refNo ;
			}
		}

		private IBoqItemEntity CreateBoq()
		{
			return boqItemCreationLogic.Value.Create(new IdentificationData()) as IBoqItemEntity;
		}

		private void CopyBoqProperties(IBoqItemEntity target, IBoqItemEntity source)
		{
			//fields that should always be copied!
			target.BoqLineTypeFk = source.BoqLineTypeFk;
			target.Reference = source.Reference;
			target.BriefInfo = new DescriptionTranslateType(source.BriefInfo.Description,
				source.BriefInfo.Description);

			target.MdcMaterialFk = source.MdcMaterialFk;
			target.MdcCostCodeFk = source.MdcCostCodeFk;
			target.BasUomFk = source.BasUomFk;


			//Copy some values used to calculate price
			// TODO-143326: Can the complete function be moved to Boq.Main ?
			// boqItem.BasItemTypeFk = originBoqItem.BasItemTypeFk;

			//if price is not set, calculation does not work
			target.Price = target.PriceOc = source.Price;
			target.DiscountPercent = source.DiscountPercent;
			target.Factor = source.Factor;
			target.Urb1 = source.Urb1;
			target.Urb2 = source.Urb2;
			target.Urb3 = source.Urb3;
			target.Urb4 = source.Urb4;
			target.Urb5 = source.Urb5;
			target.Urb6 = source.Urb6;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="billingItems"></param>
		protected void HandleBoqItem2DispRecord(IEnumerable<BillingItemSource> billingItems)
		{
			foreach (var item in billingItems)
			{
				var boqItem2Record = Injector.Get<IDataBaseCreateLogic>("Boq.Main.BoqItem2DispatchRecordEntity").Create(new IdentificationData()
				{
					PKey1 = item.BillBoq.BoqHeaderFk,
					PKey2 = item.BillBoq.Id,
					PKey3 = item?.DispatchingRecord?.Id
				}) as IBoqItem2DispatchRecordEntity;
				boqItem2Record.Quantity = item.BillBoq.Quantity;
				boqItem2Record.EngProdComponentFk = item.ProductComponent?.Id;
				boqItem2Record.PpsProductFk = item.ProductId;
				boqItem2Record.MdcMaterialFk = item?.MaterialRequest?.Id;

				saveBoqItem2DispRecords.Add(boqItem2Record);
			}
		}

		private void CopyPropsFromPQToBoq(IBoqItemEntity boq, IPpsPlannedQuantityEntity plannedQty, BillingItemSource billingItem)
		{
			if (plannedQty.PpsPlannedQuantityTypeFk == 2) { boq.MdcMaterialFk = plannedQty.MdcMaterialFk; }
			if (plannedQty.PpsPlannedQuantityTypeFk == 3) { boq.MdcCostCodeFk = plannedQty.MdcCostCodeFk; }
			boq.Price = boq.PriceOc = plannedQty.ExternalPrice;
			boq.BasUomFk = plannedQty.BasUomBillFk.Value;
			if (billingItem != null)
			{
				boq.Quantity += createBillHelper.Value.ConvertQuantity(billingItem.Quantity, billingItem.UomFk, boq.BasUomFk, uoms); 
			}
		}

		private void LinkParentChild(IBoqItemEntity parent, IBoqItemEntity child)
		{
			if (parent == null || child == null) { return; }
			child.BoqItemFk = parent.Id;
			BoqItemAddChild(parent, child);
		}

		private MethodInfo addChildMethodInfo = null;
		private void BoqItemAddChild(IBoqItemEntity parent, IBoqItemEntity child)
		{
			if (parent == null || child == null) { return; }
			if (addChildMethodInfo == null) { addChildMethodInfo = parent.GetType().GetMethod("AddChild"); }
			addChildMethodInfo.Invoke(parent, new object[] { child });
		}

		/// <summary>
		/// 
		/// </summary>
		protected void SaveEntities()
		{
			var boqHeaderList = string.Join(",", saveBillHeaders.Select(b => b.Code));
			CurrentProcess = "SaveEntities Start";

			try
			{
				var boqItemLogic = Injector.Get<IBoqItemLogic>();

				CurrentProcess = "Saving project BoQ structure";
				foreach (var rootPrjBoq in saveRootPrjBoqItems)
				{
					//boqItemLogic.CalculateBoqTreeAndSave(rootPrjBoq);
					TemporaryCalculateBoqTreeAndSave(boqItemLogic, rootPrjBoq); // temporary replacement when boqItemLogic.CalculateBoqTreeAndSave(rootItem) not working
				}

				#region save bill
				foreach (var billHeader in saveBillHeaders)
				{
					#region get headers
					CurrentProcess = $"Getting main entities for bill header(ID={billHeader.Id})";
					var orderHeader = ordHeaders.FirstOrDefault(e => billHeader.OrdHeaderFk.HasValue && e.Id == billHeader.OrdHeaderFk.Value);
					var bilboq = saveBilBoqs.FirstOrDefault(e => e.BilHeaderFk == billHeader.Id);
					if (bilboq == null)
					{
						messages.Add(string.Format(Resources.Err_NoBillBoqForBillHeader, billHeader.Id));
						continue;
					}
					var boqHeader = saveBoqHeaders.FirstOrDefault(e => e.Id == bilboq.BoqHeaderFk);
					if (boqHeader == null)
					{
						messages.Add(string.Format(Resources.Err_NoBoqHeaderForBillBoq, bilboq.Id));
						continue;
					}
					var rootItem = saveRootSalesBoqItems.FirstOrDefault(e => !e.BoqItemFk.HasValue && e.BoqHeaderFk == boqHeader.Id);
					if (rootItem == null)
					{
						messages.Add(string.Format(Resources.Err_NoBoqRootItemForBoqHeader, boqHeader.Id));
						continue;
					}
					#endregion

					SaveBoqRootItem(boqItemLogic, rootItem, orderHeader);
					CurrentProcess = "Saving Billing Headers";
					SetBillHeaderFromRootItem(billHeader, rootItem);
					billHeader.BillNo = String.IsNullOrWhiteSpace(billHeader.BillNo) ? "from PPS" : billHeader.BillNo;
					SetDefaultBillNo(billHeader);
					if (billHeader.CompanyFk == 0)
					{
						billHeader.CompanyFk = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.SignedInClientId;
					}
					if (billHeader.CompanyResponsibleFk == 0)
					{
						billHeader.CompanyResponsibleFk = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.SignedInClientId;
					}
					Injector.Get<ISalesBillingLogic>("bill").SaveBillingHeaderEntity(billHeader);
				}
				if (saveBilBoqs.Any()) // bil_boq should be saved after bil_header
				{
					CurrentProcess = "Saving BillBoQ";
					Injector.Get<IDataBaseLogic>("Sales.Billing.BilBoqEntity").Save(saveBilBoqs);
				}
				#endregion

				#region save wip
				if (saveWipBoqs.Any()) // wip_header has been saved during creation, so wip_boq can be saved, and CalculateValueAndVat works
				{
					CurrentProcess = "Saving WIPBoQ";
					Injector.Get<ISalesWipBoqLogic>("Wip").Save(saveWipBoqs);
				}
				foreach (var wipHeader in saveWips)
				{
					#region get entities
					CurrentProcess = $"Getting main entities for WIP header(ID={wipHeader.Id})";
					var orderHeader = ordHeaders.FirstOrDefault(e => e.Id == wipHeader.OrdHeaderFk);
					var wipboq = saveWipBoqs.FirstOrDefault(e => e.WipHeaderFk == wipHeader.Id);
					if (wipboq == null)
					{
						messages.Add(string.Format(Resources.Err_NoWipBoqForWipHeader, wipHeader.Id));
						continue;
					}
					var boqHeader = saveBoqHeaders.FirstOrDefault(e => e.Id == wipboq.BoqHeaderFk);
					if (boqHeader == null)
					{
						messages.Add(string.Format(Resources.Err_NoBoqHeaderForWipBoq, wipboq.Id));
						continue;
					}
					var rootItem = saveRootSalesBoqItems.FirstOrDefault(e => !e.BoqItemFk.HasValue && e.BoqHeaderFk == boqHeader.Id);
					if (rootItem == null)
					{
						messages.Add(string.Format(Resources.Err_NoBoqRootItemForBoqHeader, boqHeader.Id));
						continue;
					}
					#endregion
					SaveBoqRootItem(boqItemLogic, rootItem, orderHeader);
					CurrentProcess = "Calculating and Saving WIP Headers";
					Injector.Get<ISalesWipHeaderLogic>().CalculateValueAndVat(wipHeader);
					Injector.Get<ISalesWip2ContractLogic>("SalesWip2Contract").CreateOrUpdateWip2Ords(wipHeader.Id, wip2Contracts[wipHeader.Id]);
				}
				#endregion

				CurrentProcess = "Saving BoQ Dispatching assignments";
				Injector.Get<IDataBaseLogic>("Boq.Main.BoqItem2DispatchRecordEntity").Save(saveBoqItem2DispRecords);

				CurrentProcess = "SaveEntities End";
			}
			catch (Exception ex)
			{
				CurrentProcess = "SaveEntities Exception";
				var exceptionMessage = string.Format(Resources.BillOrWip_ErrSaveTranscation, boqHeaderList, CurrentProcess);
				messages.Add(exceptionMessage);
				messages.Add(ex.Message);
				ThrowBZLayerException(exceptionMessage, ex);
			}

			void SaveBoqRootItem(IBoqItemLogic boqItemLogic, IBoqItemEntity rootItem, ISalesContractHeaderEntity orderHeader)
			{
				//unfortunately, the current interface methods only allow to pass the tax code via the boq items
				//workaround: set tax code of root item
				var originalTaxCode = rootItem.MdcTaxCodeFk;
				rootItem.MdcTaxCodeFk = orderHeader != null ? (int?)orderHeader.TaxCodeFk : null;
				//calculate and save
				CurrentProcess = "Calculating Billing BoQ structure";
				//boqItemLogic.CalculateBoqTreeAndSave(rootItem);
				TemporaryCalculateBoqTreeAndSave(boqItemLogic, rootItem); // temporary replacement when boqItemLogic.CalculateBoqTreeAndSave(rootItem) not working

				rootItem.MdcTaxCodeFk = originalTaxCode; //set tax code back to null and finally save again
				CurrentProcess = "Saving Billing BoQ root item";
				boqItemLogic.Save(rootItem);
			}

		}

		/// <summary>
		/// temporary replacement when boqItemLogic.CalculateBoqTreeAndSave(rootItem) not working
		/// </summary>
		/// <param name="boqItemLogic"></param>
		/// <param name="rootItem"></param>
		private void TemporaryCalculateBoqTreeAndSave(IBoqItemLogic boqItemLogic, IBoqItemEntity rootItem)
		{
			PropertyInfo prop_Children = rootItem.GetType().GetProperty("BoqItemChildren");
			PropertyInfo prop_Finalprice = rootItem.GetType().GetProperty("Finalprice");
			PropertyInfo prop_FinalpriceOc = rootItem.GetType().GetProperty("FinalpriceOc");

			CalculateFinalPriceForBoqTree(rootItem);
			boqItemLogic.SaveBoqTree2(rootItem);

			decimal CalculateFinalPriceForBoqTree(IBoqItemEntity rootItem)
			{
				decimal price = 0.0m;
				var children = GetBoqChildren(rootItem);
				if (children?.Any() != true)
				{
					price = Math.Round(rootItem.Quantity * rootItem.Price, 3);
				}
				else
				{
					foreach (var child in children) { price += CalculateFinalPriceForBoqTree(child); }
				}

				prop_Finalprice.SetValue(rootItem, price);
				prop_FinalpriceOc.SetValue(rootItem, price);

				return price;
			}

			IEnumerable<IBoqItemEntity> GetBoqChildren(IBoqItemEntity boqItem)
			{
				if (prop_Children == null && boqItem != null)
				{
					prop_Children = boqItem.GetType().GetProperty("BoqItemChildren");
					prop_Finalprice = boqItem.GetType().GetProperty("Finalprice");
					prop_FinalpriceOc = boqItem.GetType().GetProperty("FinalpriceOc");
				}
				return boqItem != null ? prop_Children.GetValue(boqItem) as IEnumerable<IBoqItemEntity> : null;
			}
		}

		private void SetBillHeaderFromRootItem(ISalesBillingHeaderEntity bilHeader, IBoqItemEntity rootItem)
		{
			bilHeader.AmountNet = rootItem.Finalprice;
			bilHeader.AmountNetOc = rootItem.FinalpriceOc;

			bilHeader.AmountGross = rootItem.Finalgross;
			bilHeader.AmountGrossOc = rootItem.FinalgrossOc;
		}

		private const int RubricId_Bill = 7; // rubric id for bill
		private void SetDefaultBillNo(ISalesBillingHeaderEntity billHeader)
		{
			if (billHeader == null) { return; }
			var billTypes = new BasicsCustomizeBillTypeLogic().GetListByFilter(e => e.IsDefault && e.IsLive);
			if (billTypes?.Any() != true)
			{
				messages.Add(Resources.Err_FailedToGenBillNo);
				return;
			}

			var rubicCatId = billTypes.OrderByDescending(e => e.Sorting).First().RubricCategoryFk;
			if (!RubricCodeGenerator.Default.HasToCreateCompanyNumber(rubicCatId))
			{
				messages.Add(string.Format(Resources.Err_NoCodeConfiguration4BillNo, rubicCatId));
				return;
			}
			billHeader.BillNo = RubricCodeGenerator.Default.GetRubricCode(RubricId_Bill, rubicCatId);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="msg"></param>
		/// <param name="innerException"></param>
		/// <exception cref="BusinessLayerException"></exception>
		protected void ThrowBZLayerException(string msg, Exception innerException = null)
		{
			throw new BusinessLayerException(msg, innerException, (Int32)ExceptionErrorCodes.BusinessFatalError);
		}

		private const string LineBreak = "##LineBreak##";
		/// <summary>
		/// 
		/// </summary>
		/// <param name="sb"></param>
		/// <param name="msg"></param>
		protected void AppendLineSymble(StringBuilder sb, string msg = null)
		{
			if (!string.IsNullOrEmpty(msg)) { sb.Append(msg); }
			sb.Append(LineBreak);
		}

		#region With Boq Mode

		private void ThrowError_BoqHeaderWithoutContract(IEnumerable<int> boqHeaderIds)
		{
			var msg = string.Format(Resources.Err_BoqHeaderNotLinkedToContract, string.Join(", ", boqHeaderIds));
			messages.Add(msg);
			ThrowBZLayerException(msg);
		}

		private bool ReplaceOrdHeaderByBoq(ref IEnumerable<BillingItemSource> billingSource, Dictionary<Int32, Int32> ordBoqContractDic, ILookup<Int32, IBoqItemEntity> boqHeaderLK)
		{
			// will only consider change order use case, when there is a real requirement from user that
			//		a. set main contract(Ord_Header) on PPS_Header
			//		b. PQ don't link to any boq
			//		c. there is a change order boq for specific material
			//
			// in this case, I think the user will probably link boq to specific change order boq

			var toReplace = billingSource.Where(x => x.OrdHeaderId.HasValue).ToList();
			if (!toReplace.Any()) { return true; }

			var ordH2Boqs = from bs in toReplace
								 join ordBoq in ordBoqContractDic on bs.OrdHeaderId equals ordBoq.Key
								 join boqHLK in boqHeaderLK on ordBoq.Value equals boqHLK.Key
								 select new
								 {
									 OrdHeaderId = bs.OrdHeaderId.Value,
									 BoqItems = boqHLK.AsEnumerable()
								 };
			var ordH2BoqDic = ordH2Boqs.ToLookup(x => x.OrdHeaderId);
			var dic = new Dictionary<int, (int boqItemId, int boqHeaderFk)>();
			var notLinkedToBoq = new List<BillingItemSource>();
			foreach (var rp in toReplace)
			{
				var ordHeaderId = rp.OrdHeaderId.Value;
				if (dic.TryGetValue(ordHeaderId, out var item))
				{
					rp.BoqItemFk = item.boqItemId;
					rp.BoqHeaderFk = item.boqHeaderFk;
				}
				else
				{
					var matchMaterialBoq = ordH2BoqDic[ordHeaderId].First().BoqItems.FirstOrDefault(x => x.MdcMaterialFk.HasValue && x.MdcMaterialFk == rp.DispatchingRecord.MaterialFk);
					if (matchMaterialBoq != null) // match boq by material
					{
						rp.BoqItemFk = matchMaterialBoq.Id;
						rp.BoqHeaderFk = matchMaterialBoq.BoqHeaderFk;
						dic.Add(ordHeaderId, (matchMaterialBoq.Id, matchMaterialBoq.BoqHeaderFk));
					}
					else if (rp.BoqItemFk == null) // neither planned quantity linked to boqItem 
					{
						// report error
						messages.Add(String.Format(Resources.Err_DispRec_NotLinkedTo_Boq, rp.DispatchingRecord.Id));
						notLinkedToBoq.Add(rp);
					}
				}
			}

			// remove the error ones
			billingSource = billingSource.Except(notLinkedToBoq);

			return !notLinkedToBoq.Any();
		}

		/// <summary>
		/// In auto production case, there is no Planned Quantity, and PU was created from boq directly
		/// we need to go and match on contract boqs by material/costcode
		/// when IsBoqSpecifiedByPlannedQty==false, it means auto production
		/// </summary>
		/// <param name="billingSource"></param>
		/// <param name="boqHeaderLK"></param>
		/// <returns></returns>
		private bool CorrectBoqFkForAutoProduction(ref IEnumerable<BillingItemSource> billingSource, ILookup<Int32, IBoqItemEntity> boqHeaderLK)
		{
			var toCheck = billingSource.Where(x => x.ProductComponent != null && x.BoqItemFk.HasValue && x.BoqHeaderFk.HasValue && !x.IsBoqSpecifiedByPlannedQty).ToList();
			if (!toCheck.Any()) { return true; }

			var notMatchItems = new List<BillingItemSource>();
			var dic = boqHeaderLK.ToDictionary(x => x.Key, y => y.ToDictionary(boq => boq.Id));
			var noError = true;
			foreach (var bs in toCheck)
			{
				var boqsWithinHeader = dic[bs.BoqHeaderFk.Value];
				var currentBoq = boqsWithinHeader[bs.BoqItemFk.Value];
				if (bs.ProductComponent.EngDrwCompTypeFk == (Int32)EDrawingComponentType.Material)
				{
					if (!currentBoq.MdcMaterialFk.HasValue || bs.ProductComponent.MdcMaterialFk != currentBoq.MdcMaterialFk)
					{
						var boqMatchMaterial = boqsWithinHeader.Values.FirstOrDefault(x => x.MdcMaterialFk.HasValue && x.MdcMaterialFk == bs.ProductComponent.MdcMaterialFk);
						if (boqMatchMaterial != null)
						{
							bs.BoqItemFk = boqMatchMaterial.Id; // change boq to the one match material
							bs.BoqHeaderFk = boqMatchMaterial.BoqHeaderFk;
						}
						else
						{
							noError = false;
							messages.Add(string.Format(Resources.Err_CompNotMatchAnySalesBoq, bs.ProductComponent.Id, bs.DispatchingRecord.Id, bs.BoqHeaderFk.Value));
							notMatchItems.Add(bs);
						}
					}
				}
				if (bs.ProductComponent.EngDrwCompTypeFk == (Int32)EDrawingComponentType.CostCode)
				{
					if (!currentBoq.MdcCostCodeFk.HasValue || bs.ProductComponent.MdcCostCodeFk != currentBoq.MdcCostCodeFk)
					{
						var boqMatchCostcode = boqsWithinHeader.Values.FirstOrDefault(x => x.MdcCostCodeFk.HasValue && x.MdcCostCodeFk == bs.ProductComponent.MdcCostCodeFk);
						if (boqMatchCostcode != null)
						{
							bs.BoqItemFk = boqMatchCostcode.Id; // change boq to the one match cost code
							bs.BoqHeaderFk = boqMatchCostcode.BoqHeaderFk;
						}
						else
						{
							noError = false;
							messages.Add(string.Format(Resources.Err_CompNotMatchAnySalesBoq, bs.ProductComponent.Id, bs.DispatchingRecord.Id, bs.BoqHeaderFk.Value));
							notMatchItems.Add(bs);
						}
					}
				}
			}

			// remove the error ones
			billingSource = billingSource.Except(notMatchItems);

			return noError;
		}

		int GetPrjBoqHeaderId(int salesContractId)
		{
			int? boqHeaderId = null;
			var salesContractBoqLogic = Injector.Get<ISalesContractBoqLogic>();
			var curContractBoqEntities = salesContractBoqLogic.GetList(salesContractId);
			if (curContractBoqEntities?.Count() > 1)
			{
				messages.Add(string.Format(Resources.Err_NotSupported_MultiBoqHeadersOfContract, salesContractId));
				throw new Exception(messages.Last());
			}
			var curContractBoqEntity = curContractBoqEntities.First();
			var boqItemLogic = Injector.Get<IBoqItemLogic>();
			if (curContractBoqEntity.BoqHeaderFk != null)
			{
				var curBoqHeaderId = (int)curContractBoqEntity.BoqHeaderFk;
				var boqRootItem = boqItemLogic.GetBoqRootItemAsInterface(curBoqHeaderId);
				if (boqRootItem.BoqItemPrjBoqFk.HasValue)
				{
					boqHeaderId = boqRootItem.BoqItemPrjBoqFk.Value;
				}
			}

			if (!boqHeaderId.HasValue)
			{
				messages.Add(string.Format(Resources.Err_ProjectBoqHeaderNotFound, salesContractId));
				throw new Exception(messages.Last());
			}

			return boqHeaderId.Value;
		}

		private void SetNewBoqWithOldValueOfContractBoq(IBoqItemEntity boqItem, IBoqItemEntity contractBoq)
		{
			//fields that should always be copied!
			boqItem.BoqLineTypeFk = contractBoq.BoqLineTypeFk;
			boqItem.Reference = contractBoq.Reference;
			boqItem.BriefInfo = new DescriptionTranslateType(contractBoq.BriefInfo.Description,
				contractBoq.BriefInfo.Description);

			boqItem.MdcMaterialFk = contractBoq.MdcMaterialFk;
			boqItem.MdcCostCodeFk = contractBoq.MdcCostCodeFk;
			boqItem.BasUomFk = contractBoq.BasUomFk;


			//Copy some values used to calculate price
			// TODO-143326: Can the complete function be moved to Boq.Main ?
			// boqItem.BasItemTypeFk = originBoqItem.BasItemTypeFk;

			//if price is not set, calculation does not work
			boqItem.Price = boqItem.PriceOc = contractBoq.Price;
			boqItem.DiscountPercent = contractBoq.DiscountPercent;
			boqItem.Factor = contractBoq.Factor;
			boqItem.Urb1 = contractBoq.Urb1;
			boqItem.Urb2 = contractBoq.Urb2;
			boqItem.Urb3 = contractBoq.Urb3;
			boqItem.Urb4 = contractBoq.Urb4;
			boqItem.Urb5 = contractBoq.Urb5;
			boqItem.Urb6 = contractBoq.Urb6;
		}

		private void CreateSingleSalesHeaderWithBoq(int ordHeaderId, ISalesHeaderEntity saleHeader, IEnumerable<BillingItemSource> billingSource, Dictionary<int, IBoqItemEntity> dicContractBoq,
			int prjBoqHeaderId, IEnumerable<UomEntity> uoms, WipHeaderCreationData createWipData = null)
		{
			CurrentProcess = $"CreateSingleSalesHeader - get project boqs, boqHeaderId:" + prjBoqHeaderId;
			// project boqs, are the source boq to copy.
			// project boq structure, should be what bill boq structure looks like
			var prjBoqs = Injector.Get<IBoqItemLogic>().GetBoqItemsByBoqHeaderIds(prjBoqHeaderId.Obj2Array());
			var dicPrjBoqRef = prjBoqs.Where(x => !IgnoreBoQLineTypes.Contains(x.BoqLineTypeFk)).ToDictionary(x => x.Reference); // exclude boq with LineType=Note

			CurrentProcess = $"CreateSingleSalesHeader - boqHeaderLogic.CreateAndSave a new boq header";
			var boqHeaderLogic = Injector.Get<IBoqHeaderLogic>();
			var boqHeader = boqHeaderLogic.CreateAndSave(true, saleHeader.CurrencyFk);
			boqHeader.GetType().GetProperty("BoqHeaderFk").SetValue(boqHeader, prjBoqHeaderId);
			boqHeaderLogic.Save(boqHeader);
			saveBoqHeaders.Add(boqHeader);

			// for each BillingItemSource, create a new billing boq item, inherit the parent structure from contracBoqs
			Dictionary<int, IBoqItemEntity> dicOldNewBoq = new Dictionary<int, IBoqItemEntity>();
			foreach (var billingItem in billingSource)
			{
				CopyToNewBoqItem(billingItem, dicContractBoq, prjBoqHeaderId, dicPrjBoqRef, boqHeader, ref dicOldNewBoq);
			}

			if (!dicOldNewBoq.Values.Any())
			{
				CurrentProcess = $"CreateSingleSalesHeader - No boq item for sales contract(ID:{ordHeaderId}) is created, delete created boq header";
				messages.Add(CurrentProcess);

				boqHeaderLogic.Delete(boqHeader); // remove created boq header
				saveBoqHeaders.Remove(boqHeader);
				return;
			}
			CurrentProcess = $"CreateSingleSalesHeader - Created totally {dicOldNewBoq.Values.Count} boq items for sales contract(ID:{ordHeaderId})";

			CurrentProcess = "CreateSingleSalesHeader - Save connection between WipHeader/BillHeader and boqHeader";
			if (createWipData != null)
			{
				saveWips.Add(saleHeader as ISalesWipHeaderEntity);
				var wipBoq = Injector.Get<ISalesWipBoqLogic>("Wip").Create();
				wipBoq.BoqHeaderFk = boqHeader.Id;
				wipBoq.WipHeaderFk = saleHeader.Id;
				saveWipBoqs.Add(wipBoq);
			}
			else
			{
				saveBillHeaders.Add(saleHeader as ISalesBillingHeaderEntity);
				var bilBoq = Injector.Get<IDataBaseCreateLogic>("Sales.Billing.BilBoqEntity").Create(new IdentificationData()) as IBillBoqEntity;
				bilBoq.BilHeaderFk = saleHeader.Id;
				bilBoq.BoqHeaderFk = boqHeader.Id;
				saveBilBoqs.Add(bilBoq);
			}

			saveRootSalesBoqItems.AddRange(dicOldNewBoq.Values.Where(x => x.BoqItemFk == null).ToList());
			HandleBoqItem2DispRecord(billingSource);

			void CopyToNewBoqItem(BillingItemSource billingSource, IDictionary<int, IBoqItemEntity> dicContractBoqs, int prjBoqHeaderId, IDictionary<string, IBoqItemEntity> dicPrjBoqRef, IBoqHeaderEntity boqHeader, ref Dictionary<int, IBoqItemEntity> dicOldNewBoq)
			{
				IBoqItemEntity child = null;
				IBoqItemEntity currentSource = null;
				PropertyInfo Prop_BoqItemPrjBoqFk = dicContractBoqs.First().Value.GetType().GetProperty("BoqItemPrjBoqFk");
				PropertyInfo Prop_BoqItemPrjItemFk = dicContractBoqs.First().Value.GetType().GetProperty("BoqItemPrjItemFk");
				var boqItemCreationLogic = Injector.Get<IDataBaseCreateLogic>("Boq.Main.BoqItemEntity");
				if (!dicPrjBoqRef.TryGetValue(billingSource.ContractBoq.Reference, out currentSource))
				{
					messages.Add(string.Format(Resources.Err_BoqNotFoundFromProjectBoq, billingSource.ContractBoq.Reference, prjBoqHeaderId)); // normally project boq will be sync automatically, so this line can't be hit normally
					throw new Exception(messages.Last());
				}
				if (currentSource == null) { messages.Add($"{Resources.Err_BillingItemSourceNotLinkToBoqItem}, {billingSource.ToString()}"); }

				CurrentProcess = $"CopyToNewBoqItem - Creating boq structure for billingSource, {billingSource.ToString()}";
				while (currentSource != null)
				{
					var contractBoq = currentSource;
					if (dicOldNewBoq.TryGetValue(contractBoq.Id, out var newBoqItem)) // incase multiple components link to same boqItem, aggregate the quantity in this case, 
					{
						CurrentProcess = "CopyToNewBoqItem - BillingItemSource found reusable boq item when creating structure";
						LinkParentChild(newBoqItem, child);
						if (dicContractBoqs[billingSource.BoqItemFk.Value].Reference == contractBoq.Reference)
						{
							newBoqItem.Quantity += createBillHelper.Value.ConvertQuantity(billingSource.Quantity, billingSource.UomFk, newBoqItem.BasUomFk, uoms);
							billingSource.BillBoq = newBoqItem;
						}
						break; // should be break but not continue, because upper lever boqs should be generated in this case
					}

					// create new boq item and build linkage
					var boqItem = boqItemCreationLogic.Create(new IdentificationData()) as IBoqItemEntity;
					dicOldNewBoq[contractBoq.Id] = boqItem;
					LinkParentChild(boqItem, child);

					boqItem.BoqHeaderFk = boqHeader.Id;
					SetNewBoqWithOldValueOfContractBoq(boqItem, contractBoq); //fields that should always be copied!
					Prop_BoqItemPrjBoqFk.SetValue(boqItem, prjBoqHeaderId);
					Prop_BoqItemPrjItemFk.SetValue(boqItem, contractBoq.Id);

					if (contractBoq.Reference == billingSource.ContractBoq.Reference) // quantity only set once for billingSource.ContractBoq
					{
						boqItem.Quantity = createBillHelper.Value.ConvertQuantity(billingSource.Quantity, billingSource.UomFk, boqItem.BasUomFk, uoms);
						billingSource.BillBoq = boqItem;
					}

					// recursive set child/parnet
					child = boqItem;
					currentSource = currentSource.BoqItemFk.HasValue ? dicPrjBoqRef.Values.First(x => x.Id == currentSource.BoqItemFk.Value) : null;
				}

				void LinkParentChild(IBoqItemEntity parent, IBoqItemEntity child)
				{
					if (parent == null || child == null) { return; }
					child.BoqItemFk = parent.Id;
					BoqItemAddChild(parent, child);
				}
			}
		}

		private void LinkSalesHeader2Contract(ISalesHeaderEntity salesHeader, List<BillingItemSource> sourceBoqs, List<(Int32, Int32)> contract2BoqHeader)
		{
			var relatedBoqHeaders = sourceBoqs.CollectIds(x => x.BoqHeaderFk);
			var relatedContracts = contract2BoqHeader.Where(x => relatedBoqHeaders.Contains(x.Item2)).Select(x => x.Item1).Distinct().ToList();
			wip2Contracts.Add(salesHeader.Id, relatedContracts);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="billingSource"></param>
		protected void PrepareEntitiesToSaveWithBoq(IEnumerable<BillingItemSource> billingSource)
		{
			CurrentProcess = "PrepareEntitiesToSave Start";

			if (billingSource?.Any() != true) { CurrentProcess = "PrepareEntitiesToSave - Empty billingSource"; return; }

			// get ORD_BOQ
			// 1. get by boq header ids
			var boqHeaderIds = billingSource.CollectIds(x => x.BoqHeaderFk);
			CurrentProcess = "PrepareEntitiesToSave - Get OrderBoqs by BoqHeaderFk" + " boqHeaderIds: " + string.Join(",", boqHeaderIds);

			var salesContractBoqLogic = Injector.Get<ISalesContractBoqLogic>();
			var ordBoqsByBoqH = salesContractBoqLogic.GetByBoqHeaders(boqHeaderIds);
			if (ordBoqsByBoqH?.Any() != true) { ThrowError_BoqHeaderWithoutContract(boqHeaderIds); }

			// 2. append by contract ids
			var noBoqButOrdHeaderIds = billingSource.Where(x => !x.BoqHeaderFk.HasValue).CollectIds(x => x.OrdHeaderId);
			CurrentProcess = "PrepareEntitiesToSave - Get BoqList by OrdHeaderId" + " noBoqButOrdHeaderIds: " + string.Join(",", noBoqButOrdHeaderIds);
			var ordBoqsByContracts = noBoqButOrdHeaderIds.Any() ? salesContractBoqLogic.GetBoQListByOrdHeaderFks(noBoqButOrdHeaderIds) : Enumerable.Empty<IBoqListEntity>();

			var ordBoqs = ordBoqsByBoqH.Select(x => (x.OrdHeaderFk, x.BoqHeaderFk)).Concat(ordBoqsByContracts.Select(x => (x.MainItemId, x.BoqHeaderFk.Value))).Distinct().ToList();
			CurrentProcess = "OrdBoqs: " + string.Join(",", ordBoqs.Select(e => $"(OrdHeaderFk:{e.Item1}, BoqHeaderFk:{e.Item2})"));
			var ordBoqContractDic = ordBoqs.ToLookup(x => x.Item1).ToDictionary(x => x.Key, y => y.First().Item2); // dictionary OrdHeaderFk => BoqHeaderFk

			var allOrdHeaderIds = ordBoqContractDic.Keys.ToList();
			var allBoqHeaderIds = ordBoqContractDic.Values.ToList();

			// get contract boqs
			CurrentProcess = "PrepareEntitiesToSave - Get all boq items by all boqHeaderIds";
			var allBoqItems = Injector.Get<IBoqItemLogic>().GetBoqItemsByBoqHeaderIds(allBoqHeaderIds);
			var boqHeaderLK = allBoqItems.ToLookup(x => x.BoqHeaderFk);

			// replace OrdHeaderId by BoqItemFk in billingSource, which make sure BoqItemFk not null
			CurrentProcess = "PrepareEntitiesToSave - ReplaceOrdHeaderByBoq";
			ReplaceOrdHeaderByBoq(ref billingSource, ordBoqContractDic, boqHeaderLK);

			// correct boqFk by material -- use case: boq(material_HC1) -> PU -> product -> component(material_HC2)
			CurrentProcess = "PrepareEntitiesToSave - CorrectBoqFkIncaseDifferentMaterialCostcode";
			CorrectBoqFkForAutoProduction(ref billingSource, boqHeaderLK);

			var billingSrcLK = billingSource.ToLookup(x => x.BoqHeaderFk); // after filter, build lookup

			// get existing bill headers and order header
			//var existBillHeaders = Injector.Get<ISalesBillingLogic>("bill").GetBillingHeaderByOrdHeaderIds(allOrdHeaderIds);
			var salesHeaderLogic = Injector.Get<ISalesHeaderLogic>("Contract");
			ordHeaders = salesHeaderLogic.GetSalesContractsByKey(allOrdHeaderIds.Select(e => (int?)e)).ToList();
			var mainContractIdNotLoaded = ordHeaders.CollectIds(x => x.OrdHeaderFk).Except(ordHeaders.Select(x => x.Id)).ToList();
			if (mainContractIdNotLoaded.Any())
			{
				ordHeaders.AddRange(salesHeaderLogic.GetSalesContractsByKey(mainContractIdNotLoaded.Select(e => (int?)e)).ToList());
				allOrdHeaderIds = ordHeaders.CollectIds(x => x.Id).ToList();
			}

			// needs to cache all uoms of prod components and default uoms of all materials and costcodes!
			// additionally, we need to add the uoms of billing items later on!
			var uomIds = GetUomIdsWithBoq(billingSource, allBoqItems.CollectIds(e => e.BasUomFk));
			uoms = uomIds != null && uomIds.Any() ? uomLogic.Value.GetUoM(e => uomIds.Contains(e.Id)).ToList() : new List<UomEntity>();

			WipHeaderCreationData BuildWipHeaderCreationData(int mainContractId)
			{
				if (!isCreateWIP) { return null; }

				var filterRequest = new Basics.Common.Core.Final.LookupSearchRequest();
				filterRequest.AdditionalParameters.Add("Rubric", 17);
				filterRequest.FilterKey = "rubric-category-by-rubric-company-lookup-filter";
				var rubrics = new RubricCategoryLogic().GetRubricCategoryByCompanyId(filterRequest);
				var defaultRubrics = rubrics.FirstOrDefault(e => e.IsDefault) ?? rubrics.First();
				var mainContractEntity = ordHeaders.First(e => e.Id == mainContractId);
				return new WipHeaderCreationData
				{
					RubricCategoryFk = defaultRubrics.Id, //rubricCategoryId,
																	  //ConfigurationId = creationData.ConfigurationId,
					CompanyFk = mainContractEntity.CompanyFk,
					ResponsibleCompanyFk = mainContractEntity.CompanyResponsibleFk,
					ProjectFk = mainContractEntity.ProjectFk,
					LanguageFk = mainContractEntity.LanguageFk,
					CurrencyFk = mainContractEntity.CurrencyFk,
					ClerkFk = mainContractEntity.ClerkFk,
					OrdHeaderFk = mainContractEntity.Id,         // TODO: Multi-WIP
				};
			}

			// should group ordHeaders by main contract, and then use project boq structure for wip/bill
			var contractGrp = ordHeaders.GroupBy(x => x.OrdHeaderFk ?? x.Id);

			CurrentProcess = "PrepareEntitiesToSave - Creating Wip/Bill Headers+Boqs";
			// foreach main contract, we create a wip/bill
			foreach (var grp in contractGrp)
			{
				var prjId = grp.First().ProjectFk;
				var mainContractId = grp.Key;
				var contractIds = grp.Select(x => x.Id).ToList();
				var contractIdsStr = string.Join(",", contractIds);
				var prjBoqHeaderId = GetPrjBoqHeaderId(mainContractId); // we need to get boq structure from prj_boq, and we assume there is only one boq header in project

				// get boq headers from main/side contract
				var boqHeaders = ordBoqs.Where(x => contractIds.Contains(x.Item1)).Select(x => x.Item2).ToList();
				List<BillingItemSource> sourceBoqs = new List<BillingItemSource>();
				boqHeaders.ForEach(boqHeaderId => sourceBoqs.AddRange(billingSrcLK[boqHeaderId]));

				if (!sourceBoqs.Any())
				{
					messages.Add(String.Format(Resources.Warning_NoBillingItemSource_For_SaleContract, contractIdsStr));
					continue;
				}

				var contractBoqs = new List<IBoqItemEntity>();
				boqHeaders.ForEach(boqHeaderId => contractBoqs.AddRange(boqHeaderLK[boqHeaderId]));
				// set source boq
				var dicContractBoq = contractBoqs.ToDictionary(x => x.Id);
				foreach (var billingSrc in sourceBoqs) { billingSrc.ContractBoq = dicContractBoq[billingSrc.BoqItemFk.Value]; };

				var wipHeaderCreationData = isCreateWIP ? BuildWipHeaderCreationData(mainContractId) : null;
				var salesHeader = CreateWipOrBillHeader(mainContractId, wipHeaderCreationData);
				if (salesHeader is null)
				{
					messages.Add(isCreateWIP ? String.Format(Resources.Err_CannotCreateWIP, prjId) : String.Format(Resources.Err_CannotCreateBill, prjId));
					continue;
				}

				CreateSingleSalesHeaderWithBoq(mainContractId, salesHeader, sourceBoqs, dicContractBoq, prjBoqHeaderId, uoms, wipHeaderCreationData);
				LinkSalesHeader2Contract(salesHeader, sourceBoqs, ordBoqs);
			}
		} 
		#endregion
	}

	/// <summary>
	/// 
	/// </summary>
	public enum GetBillComponentMode
	{
		/// <summary>
		/// 
		/// </summary>
		All,
		/// <summary>
		/// 
		/// </summary>
		ProductOnly
	}

	/// <summary>
	/// 
	/// </summary>
	public class PpsItemSourceInfoEntity
	{
		/// <summary>
		/// 
		/// </summary>
		public int PUId { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? ItemSourceFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? BOQ_HEADER_FK { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? BOQ_ITEM_FK { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? PPS_PLANNED_QUANTITY_FK { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal? INTERNALPRICE { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public decimal? EXTERNALPRICE { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? BAS_UOM_FK { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public class BillingItemSource
	{
		/// <summary>
		/// 
		/// </summary>
		public int? BoqItemFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? BoqHeaderFk { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public IDispatchingRecordEntity DispatchingRecord { get; private set; }
		/// <summary>
		/// 
		/// </summary>
		public int? ProductId { get; private set; }
		/// <summary>
		/// 
		/// </summary>
		public IPpsProductComponentEntity ProductComponent { get; private set; }
		/// <summary>
		/// 
		/// </summary>
		public MaterialRequest MaterialRequest { get; private set; }
		/// <summary>
		/// 
		/// </summary>
		public IBoqItemEntity ContractBoq { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public IBoqItemEntity BillBoq { get; set; }
		/// <summary>
		/// 
		/// </summary>
		public int? OrdHeaderId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public BillingItemSource(int boqItemFk, int boqHeaderFk, IDispatchingRecordEntity dispatchingRecord, IPpsProductComponentEntity productComponent, IPpsPlannedQuantityEntity pq)
		{
			this.BoqItemFk = boqItemFk;
			this.BoqHeaderFk = boqHeaderFk;
			this.DispatchingRecord = dispatchingRecord;
			this.ProductComponent = productComponent;
			this.PlannedQty = pq;
			this.ProductId = productComponent?.PpsProductFk;
		}

		/// <summary>
		/// 
		/// </summary>
		public BillingItemSource(int ordHeaderId, IDispatchingRecordEntity dispatchingRecord, IPpsPlannedQuantityEntity pq)
		{
			this.DispatchingRecord = dispatchingRecord;
			this.OrdHeaderId = ordHeaderId;
			this.PlannedQty = pq;
		}

		/// <summary>
		/// Constructure function for billing without boq
		/// </summary>
		/// <param name="dispatchingRecord"></param>
		/// <param name="projectId"></param>
		/// <param name="pq"></param>.
		/// <param name="productComponent"></param>
		public BillingItemSource(IDispatchingRecordEntity dispatchingRecord, int projectId, IPpsPlannedQuantityEntity pq, IPpsProductComponentEntity productComponent = null)
		{
			this.DispatchingRecord = dispatchingRecord;
			this.ProjectId = projectId;
			this.PlannedQty = pq;
			this.ProductComponent = productComponent;
			this.ProductId = productComponent?.PpsProductFk;
		}

		/// <summary>
		/// Constructure function for billing without boq
		/// </summary>
		/// <param name="productId"></param>
		/// <param name="projectId"></param>
		/// <param name="pq"></param>.
		/// <param name="productComponent"></param>
		public BillingItemSource(int productId, int projectId, IPpsPlannedQuantityEntity pq, IPpsProductComponentEntity productComponent)
		{
			this.ProductId = productId;
			this.ProjectId = projectId;
			this.PlannedQty = pq;
			this.ProductComponent = productComponent;
		}

		/// <summary>
		/// Constructure function for billing without boq
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="pq"></param>.
		/// <param name="materialReq"></param>
		public BillingItemSource(int projectId, IPpsPlannedQuantityEntity pq, MaterialRequest materialReq)
		{
			this.ProjectId = projectId;
			this.PlannedQty = pq;
			this.MaterialRequest = materialReq;
		}
		/// <summary>
		/// 
		/// </summary>
		public decimal Quantity
		{
			get
			{
				return ProductComponent?.BillingQuantity ?? DispatchingRecord?.Quantity ?? MaterialRequest.PQuantity;
			}
		}
		/// <summary>
		/// 
		/// </summary>
		public int UomFk
		{
			get
			{
				return ProductComponent?.BasUomBillFk ?? DispatchingRecord?.UoMFk ?? MaterialRequest.PUomFk;
			}
		}

		/// <summary>
		/// Get information
		/// </summary>
		/// <returns></returns>
		public override String ToString()
		{
			return DispatchingRecord != null ?
				$"BillingItemSource -> DispatchingRecord:(ID={DispatchingRecord.Id}), ProductComponent:(ID={ProductComponent?.Id}, ContractBoq:(ID={ContractBoq?.Id}))" :
				$"BillingItemSource -> ProductComponent:(ID={ProductComponent?.Id}, ContractBoq:(ID={ContractBoq?.Id}))";
		}

		/// <summary>
		/// 
		/// </summary>
		public IPpsPlannedQuantityEntity PlannedQty { get; private set; }

		/// <summary>
		/// 
		/// </summary>
		public bool IsBoqSpecifiedByPlannedQty
		{
			get
			{
				return PlannedQty?.BoqItemFk != null && PlannedQty?.BoqItemFk == this.BoqItemFk;
			}
		}

		/// <summary>
		/// Cleanup boq info, which means bill based on PlannedQuantity
		/// </summary>
		public void ClearBoqInfo()
		{
			this.BillBoq = this.ContractBoq = null;
			this.BoqItemFk = this.BoqHeaderFk = null;
		}

		/// <summary>
		/// 
		/// </summary>
		public Int32 ProjectId { get; private set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public enum CreationTypes
	{
		/// <summary>
		/// 
		/// </summary>
		CreateWIP = 0,

		/// <summary>
		/// 
		/// </summary>
		CreateBill = 1,
	}

	/// <summary>
	/// 
	/// </summary>
	public enum BoqModes
	{
		/// <summary>
		/// 
		/// </summary>
		WithoutBoQ = 0,

		/// <summary>
		/// 
		/// </summary>
		WithBoQ = 1,
	}
}
