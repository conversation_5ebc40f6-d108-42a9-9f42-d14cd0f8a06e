/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ILazyInjectionContext, PlatformModuleManagerService } from '@libs/platform/common';
import { CREW_LEADER_LOOKUP_PROVIDER_TOKEN, EMPLOYEE_CERTIFICATION_LOOKUP_PROVIDER_TOKEN, TIME_SYMBOL_LOOKUP_PROVIDER_TOKEN, TIMEKEEPING_GROUP_LOOKUP_PROVIDER_TOKEN, WORK_TIME_MODEL_LOOKUP_PROVIDER_TOKEN, TIMEKEEPING_RECORDING_LOOKUP_PROVIDER_TOKEN } from '@libs/timekeeping/interfaces';


export const LAZY_INJECTABLES: LazyInjectableInfo[] =[
LazyInjectableInfo.create('timekeeping.employee.TimekeepingCrewLeaderLookupProvider', CREW_LEADER_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/timekeeping/employee');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.TimekeepingCrewLeaderLookupProvider) : null;
		
	}),
LazyInjectableInfo.create('timekeeping.employee.TimekeepingEmployeeCertificationLookupProvider', EMPLOYEE_CERTIFICATION_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/timekeeping/employee');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.TimekeepingEmployeeCertificationLookupProvider) : null;
		
	}),
LazyInjectableInfo.create('timekeeping.timesymbols.TimekeepingTimeSymbolLookupProvider', TIME_SYMBOL_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/timekeeping/timesymbols');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.TimekeepingTimeSymbolLookupProvider) : null;
		
	}),
LazyInjectableInfo.create('timekeeping.timesymbols.TimekeepingTimekeepingGroupLookupProviderService', TIMEKEEPING_GROUP_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/timekeeping/timesymbols');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.TimekeepingTimekeepingGroupLookupProviderService) : null;
		
	}),
LazyInjectableInfo.create('timekeeping.worktimemodel.TimekeepingWorkTimeModelFbLookupProvider', WORK_TIME_MODEL_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/timekeeping/worktimemodel');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.TimekeepingWorkTimeModelFbLookupProvider) : null;
		
	}),

	LazyInjectableInfo.create('timekeeping.recording.TimekeepingRecordingLookupProvider', TIMEKEEPING_RECORDING_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/timekeeping/recording');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.TimekeepingRecordingLookupProvider) : null;
		
	}),
];
 