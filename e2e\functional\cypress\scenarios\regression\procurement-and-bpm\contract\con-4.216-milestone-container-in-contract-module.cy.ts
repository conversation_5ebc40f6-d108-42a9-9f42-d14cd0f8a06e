import { commonLocators, sidebar, app, cnt, tile, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _boqPage, _common,_saleContractPage, _controllingUnit, _estimatePage, _procurementContractPage, _projectPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const PROJECT_NO = _common.generateRandomString(4);
const PROJECT_DESC = _common.generateRandomString(4);
const MILESTONES_TEXT1 = _common.generateRandomString(4);
const MILESTONES_TEXT2 = _common.generateRandomString(4);
const MILESTONES_TEXT3 = _common.generateRandomString(4);
const CHANGE_PROJECT_DESC = _common.generateRandomString(4);
const ITEM_DESC = _common.generateRandomString(4);

let PROJECTS_PARAMETERS: DataCells, PROCUREMENT_CONTRACT_PARAMETER: DataCells
let CONTAINER_COLUMNS_CONTRACT,
    CONTAINERS_PROJECT,
	CONTAINER_COLUMNS_MILESTONES,
	CONTAINERS_CONTRACT,CONTAINERS_MILESTONES,
	CONTAINER_COLUMNS_ITEM, CONTAINERS_ITEM

describe("PCM- 4.126 | Milestone container in contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
	before(function () {
		cy.fixture("pcm/con-4.216-milestone-container-in-contract-module.json").then((data) => {
			this.data = data;
			CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
			CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
			CONTAINER_COLUMNS_MILESTONES=this.data.CONTAINER_COLUMNS.MILESTONES
			CONTAINERS_MILESTONES=this.data.CONTAINERS.MILESTONES
			CONTAINERS_PROJECT=this.data.CONTAINERS.PROJECT
			CONTAINER_COLUMNS_ITEM=this.data.CONTAINER_COLUMNS.ITEM
			CONTAINERS_ITEM=this.data.CONTAINERS.ITEM
			PROJECTS_PARAMETERS = {
				[commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
				[commonLocators.CommonLabels.NAME]: PROJECT_DESC,
				[commonLocators.CommonLabels.CLERK]: CONTAINERS_PROJECT.CLERK
			},
			PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESSPARTNER
            }
		}).then(() => {
			cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
		});
	})
	after(() => {
		cy.LOGOUT();
	});

	it("TC - Create new project", function () {
		_common.openDesktopTile(tile.DesktopTiles.PROJECT)
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.PROJECT).then(() => {
			_common.setDefaultView(app.TabBar.PROJECT)
			_common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0)
		});
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.create_newRecord(cnt.uuid.PROJECTS);
		_projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO).pinnedItem()
    })
	it("TC - Create new contract record", function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT)
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
		})
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PROJECT_NO)
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.STRUCTURE_CODE, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.MATERIAL)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.YES)
		_common.waitForLoaderToDisappear()
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	});
	it("TC - Create new item", function () {
		_common.openTab(app.TabBar.CONTRACT).then(()=>{
			cy.wait(1000)//NEED WAIT TO SEARCH CONTAINER
			_common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT,app.FooterTab.ITEMS,0)
			_common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT,CONTAINER_COLUMNS_ITEM)
			_common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
		})
		_common.maximizeContainer(cnt.uuid.ITEMSCONTRACT)
		_common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
		_common.waitForLoaderToDisappear()
		_common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT, app.GridCells.MDC_MATERIAL_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ITEM.MATERIAL_NO)
		_common.edit_containerCell(cnt.uuid.ITEMSCONTRACT,app.GridCells.DESCRIPTION_1,app.InputFields.DOMAIN_TYPE_DESCRIPTION,ITEM_DESC)
		_common.waitForLoaderToDisappear()
		_common.edit_containerCell(cnt.uuid.ITEMSCONTRACT,app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ITEM.QUANTITY)
		_common.waitForLoaderToDisappear()
		cy.SAVE();
		_common.waitForLoaderToDisappear()
		_common.search_inSubContainer(cnt.uuid.ITEMSCONTRACT,ITEM_DESC)
		_common.minimizeContainer(cnt.uuid.ITEMSCONTRACT)
		_common.waitForLoaderToDisappear()
    });
	it("TC - Verify create new milestone record", function () {
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONTRACT_MILESTONE, app.FooterTab.MILESTONES, 2);
			_common.setup_gridLayout(cnt.uuid.CONTRACT_MILESTONE, CONTAINER_COLUMNS_MILESTONES);
		});
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.CONTRACT_MILESTONE)
		_common.create_newRecord(cnt.uuid.CONTRACT_MILESTONE)
		_common.edit_containerCell(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.COMMENT_TEXT,app.InputFields.DOMAIN_TYPE_COMMENT,MILESTONES_TEXT1)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.search_inSubContainer(cnt.uuid.CONTRACT_MILESTONE,MILESTONES_TEXT1)
		_common.select_rowHasValue(cnt.uuid.CONTRACT_MILESTONE,MILESTONES_TEXT1)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.COMMENT_TEXT,MILESTONES_TEXT1)
	})
	it("TC - Verify delete milestone record", function () {
		_common.clear_subContainerFilter(cnt.uuid.CONTRACT_MILESTONE)
		_common.search_inSubContainer(cnt.uuid.CONTRACT_MILESTONE,MILESTONES_TEXT1)
		_common.select_rowHasValue(cnt.uuid.CONTRACT_MILESTONE,MILESTONES_TEXT1)
		_common.delete_recordFromContainer(cnt.uuid.CONTRACT_MILESTONE)
		cy.SAVE()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_validate.verify_recordNotPresentInContainer(cnt.uuid.CONTRACT_MILESTONE,MILESTONES_TEXT1)
	})
	it("TC - Verify each field is working and lookup filter correctly", function () {
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.CONTRACT_MILESTONE)
		_common.create_newRecord(cnt.uuid.CONTRACT_MILESTONE)
		_common.edit_containerCell(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.COMMENT_TEXT,app.InputFields.DOMAIN_TYPE_COMMENT,MILESTONES_TEXT2)
		_common.edit_containerCell(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.MILESTONE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_MILESTONES.DATE[0])
		_common.edit_containerCell(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.AMOUNT_SMALL,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_MILESTONES.AMOUNT[0])
		_common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.PRC_MILESTONE_TYPE_FK,commonLocators.CommonKeys.LIST,CONTAINERS_MILESTONES.TYPE[0])
		_common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.MDC_TAX_CODE_FK_SMALL,commonLocators.CommonKeys.GRID,CONTAINERS_MILESTONES.TAX_CODE[0])
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.select_rowHasValue(cnt.uuid.CONTRACT_MILESTONE,MILESTONES_TEXT2)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.COMMENT_TEXT,MILESTONES_TEXT2)
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.AMOUNT_SMALL,CONTAINERS_MILESTONES.AMOUNT[0])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.MILESTONE,CONTAINERS_MILESTONES.DATE[0])
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.PRC_MILESTONE_TYPE_FK,CONTAINERS_MILESTONES.TYPE[0])
	    _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.MDC_TAX_CODE_FK_SMALL,CONTAINERS_MILESTONES.TAX_CODE[0])
	})
	it("TC - Verify type should be unique", function () {
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.CONTRACT_MILESTONE)
		_common.create_newRecord(cnt.uuid.CONTRACT_MILESTONE)
		_common.edit_containerCell(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.COMMENT_TEXT,app.InputFields.DOMAIN_TYPE_COMMENT,MILESTONES_TEXT3)
		_common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.PRC_MILESTONE_TYPE_FK,commonLocators.CommonKeys.LIST,CONTAINERS_MILESTONES.TYPE[0])
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.wait(1000)//wait required to load pop-up
		_validate.validate_Text_message_In_PopUp("The Milestone type should be unique")
		_common.waitForLoaderToDisappear()
		cy.wait(1000)//wait required to load pop-up
        _common.clickOn_modalFooterButton(btn.ButtonText.CANCEL)
		_common.waitForLoaderToDisappear()	
		_common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.PRC_MILESTONE_TYPE_FK,commonLocators.CommonKeys.LIST,CONTAINERS_MILESTONES.TYPE[2])
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_MILESTONE,app.GridCells.PRC_MILESTONE_TYPE_FK,CONTAINERS_MILESTONES.TYPE[2])
	})
	it("TC - Verify milestones will pass from contract to requisition and contract module by wizard", function () {
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
		_common.search_fromSidebar(CommonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
		_common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CONTRACT_TERMINATION);
		_common.waitForLoaderToDisappear()
	   
		_saleContractPage.contractTermination_FromWizard("New Requisition",0,PROJECT_NO,CHANGE_PROJECT_DESC,CommonLocators.CommonKeys.DESIGN_CHANGE,CommonLocators.CommonKeys.CHANGE_REQUEST)
		_common.waitForLoaderToDisappear()
		cy.wait(2000) //required wait to load page
		_common.waitForLoaderToDisappear()
		_common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_REQUISITION)
		_common.waitForLoaderToDisappear()
	   
		_common.openTab(app.TabBar.MAIN).then(() => {
		  _common.select_tabFromFooter(cnt.uuid.REQUISITIONS, app.FooterTab.REQUISITIONS);
	   });
	   _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
	   _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO)
	  _common.openTab(app.TabBar.MAIN).then(() => {
		_common.select_tabFromFooter(cnt.uuid.REQUISITION_MILESTONE, app.FooterTab.MILESTONES);      
	  });
	  _common.clear_subContainerFilter(cnt.uuid.REQUISITION_MILESTONE);
	  _common.select_rowHasValue(cnt.uuid.REQUISITION_MILESTONE,MILESTONES_TEXT2)
      _common.assert_cellData_insideActiveRow(cnt.uuid.REQUISITION_MILESTONE,app.GridCells.COMMENT_TEXT,MILESTONES_TEXT2)
	
	  _common.select_rowHasValue(cnt.uuid.REQUISITION_MILESTONE,MILESTONES_TEXT2)
      _common.assert_cellData_insideActiveRow(cnt.uuid.REQUISITION_MILESTONE,app.GridCells.COMMENT_TEXT,MILESTONES_TEXT2)  
  })

});