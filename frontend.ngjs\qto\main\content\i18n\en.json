﻿{
  "qto": {
    "main": {
	  "updateBoqWqAq": "Update WQ and AQ quantities to BoQ",
	  "specialUseLength": "The field SpecialUse must be a string with a maximum length of 16.",
	  "checkV": "The field V must be a string with a maximum length of 1.",
	  "nothingToExport": "There is no data to export!",
      "QuantityType": "Quantity Type",
      "errNoQtoDetail": "There is no Qto Detail data can be used.",
		 "existAddressList": "Already existed address:{{linereference}}",
		 "IQTotalFinalPrice": "IQ Total Final Price",
		 "BQTotalFinalPrice": "BQ Total Final Price",
		 "IQFinalPrice": "IQ Final Price",
		 "BQFinalPrice": "BQ Final Price",
		 "OrderFinalPrice": "Contract Final Price",
		 "common": "Common",
		 "quantityPortion": "Quantity Portion",
		 "totalQuantity": "Total Quantity",
		 "subsidiaryFk": "Subsidiary",
		 "customerFk": "Customer",
		"billToEntity": "Bill To",
		"billToListTitle": "Bill To",
      "entityClerkDesc": "Clerk Description",
      "basClerkRoleFk": "Role",
      "basClerkFk": "Clerk",
       "errorQtoAddrssRange": "The following Addressses are outside of the valid Range:{{linereference}}",
       "addressRanges": "AddressRanges",
       "isDialog": "Active Dialog",
       "isImport": "Active Import",
       "sheetArea": "Sheet Area",
       "lineArea": "Line Area",
       "indexArea": "Index Area",
      "DialogConfigDetail": "Dialog Config Detail",
      "ImportConfigDetail": "Import Config Detail",
      "inputError": "input format is not right",
      "contractWrongWarning": "Warning",
      "contractWrongStatus": "WIP could not be created! At least one side order is not contracted yet",
      "contractWrongStatusUpdated": "WIP could not be updated! At least one side order is not contracted yet",
      "wizardCWCreateWipContractsCollectiveWIPOption":"CollectiveWIP",
      "wizardCWCreateWipContractsGridLabel": "Contracts",
      "BillToFk": "Bill To",
      "BillToDesc": "Bill To description",
      "sourceBoqItemBrief": "Source Boq Item Brief",
      "sourceBoqItem": "Source Boq Item",
      "alreadyDisabled": "QTO  {{code}} is already disabled",
      "alreadyEnabled": "QTO {{code}} is already enabled",
      "AssetMaster": "Asset Master",
      "BasRubricCategoryFk": "Rubric Category",
      "boq": "Bill of Quantity",
      "boqForm": "Bill of Quantity Properties",
      "boqHeaderStructureFk": "Header Structure",
      "boqHeaderTypeFk": "Header Type",
      "boqItemReference": "BoQ Item Reference",
      "boqProperties": "BoQ Properties",
      "BudgetCodeDesc": "Budget Code Description",
      "ClassificationDesc": "Classification Description",
      "Comment": "Comment",
      "changeLineInfo": "Unable to change the sheet-Line-No.within multi-Line Details",
      "changeLineInfoTitle": "A problem has occurred",
      "comment": {
        "code": "Code",
        "commentText": "Comment Text",
        "title": "QTO Comment"
      },
      "contactDescription": "Clerk Description",
      "createQtoHeaderDialogTitle": "Create Quantity Takeoff Header",
      "customerCode": "Clerk",
      "dateError": "The date PerformedFrom should earlier than date  PerformedTo",
      "detail": {
        "copyPasteRecord": "Copy/Paste",
        "formTitle": "QTO Detail Properties",
        "gridTitle": "Quantity Takeoff",
        "insertRecord": "Insert Record",
        "createQtoLineTilte": "Create Quantity Takeoff Area",
        "createQtoLineWarning": "Following Line Reference of '{{value0}}' and '{{value1}}' is already used in QTO. Do you want to use Line Reference '{{value2}}' instead?",
        "insertQtoLineWarning": "Preceding Address of '{{value0}}' is '{{value1}}' is already used in the QTO.",
        "addressOverflow": "Address Overflow occured.",
        "ReCalculate": "Recalculate",
        "calculate": "Calculate",
        "qtoFormula": "QTO Formula",
        "userForm": "QTO Formula",
			"turnOnShowUserForm": "Turn On",
			"turnOffShowUserForm": "Turn Off",
			"splitQTOLine": "Split QTO line",
			"splitQTOLineInfo": "Do you really want to split into 2 independent QTO lines, one for IQ Quantity and one for BQ Quantity?",
        "outOfMaxLineNumber": "Out of Max Line Number.Max Line Number of Formula '{{value0}}' is : '{{value1}}'",
        "userFormError": {
          "haveNoQtoFormulaFormData": "Open Qto Formula failed, Please try to reassign User Form to Qto Formula.",
          "addressAreaTooSmall": "Address Area is too small for inserting the Detail. The Detail was moved to the End of the QTO."
        },
			"validationText": {
				"maxRowSize" : "The maximum row number is : ",
				"minRowSize" : "The minimal row number is : ",
                "maxCharacterLenthReb23003" : "The max character length in REB23.003 is 38.",
				"factor": "Factor",
				"result": "Result",
				"displayControlGraphic": "Display Control Graphic",
				"controlGraphic": "Control Graphic",
				"formula": "Formula",
				"qTOFormulaConfig": "QTO Formula Config",
				"dataLanguageConfigText": "Data Language Config",
				"languageText": "Language",
				"basicConfigText": "Basic Config",
				"maximumRowNumber": "Maximum Row Number",
				"minimalRowNumber": "Minimal Row Number",
				"factorInFirstRow": "Factor in first row",
				"setEqualSymbolAutomatically": "Set equal symbol automatically",
				"activateAddOrDeleteButton": "Activate Add or Delete button",
				"activateControlGraphic": "Activate Control Graphic",
				"appendValueAutomaticallyText": "Append Value Automatically",
				"columnConfigText": "Column Config",
				"column1": "Column 1",
				"column2": "Column 2",
				"column3": "Column 3",
				"column4": "Column 4",
				"column5": "Column 5",
				"column6": "Column 6",
				"column7": "Column 7",
				"column8": "Column 8",
				"column9": "Column 9",
				"column10": "Column 10",
				"headTitleText": "Head Title",
				"headerDescriptionText": "Header Description",
				"isOperatorColumn": "Is Operator Column",
				"valueColumnMapping": "Value Column Mapping",
				"onlyActivateInTheFirstRow": "Only activate in the first row",
				"setInputsToTheLastQTOLine":"Set inputs to the last QTO line",
				"notAllowInputValueIsNegative": "Not Allow input value is negative",
				"hideColumnText": "Hide Column",
				"onlySetInputToOneOperator":"Only Set input to One Operator",
				"preview": "Preview",
				"editButtonText": "Edit",
				"saveNclose": "Save & Close",
				"Value1Mapping": "Value1",
				"Value2Mapping": "Value2",
				"Value3Mapping": "Value3",
				"Value4Mapping": "Value4",
				"Value5Mapping": "Value5",
				"Operator1Mapping":"Operator1",
				"Operator2Mapping":"Operator2",
				"Operator3Mapping":"Operator3",
				"Operator4Mapping":"Operator4",
				"Operator5Mapping":"Operator5",
				"valueInputPlaceholder": "This value cannot be null, please check.",
                "column1ConfigText": "Column1 Sub Config",
                "column2ConfigText": "Column2 Sub Config",
                "column3ConfigText": "Column3 Sub Config",
                "column4ConfigText": "Column4 Sub Config",
                "column5ConfigText": "Column5 Sub Config",
                "column6ConfigText": "Column6 Sub Config",
                "column7ConfigText": "Column7 Sub Config",
                "column8ConfigText": "Column8 Sub Config",
                "column9ConfigText": "Column9 Sub Config",
                "column10ConfigText": "Column10 Sub Config",
                "activateColumnSubConfig": "Activate column sub config area"
            },
		  "createMultiItemsInProgress": "Create Multiple Items in Progress"
      },
      "disableDone": "Quantity Takeoff {{code}} is disabled successfully. Please save the record to make this changes permanent.",
      "enableDone": "Quantity Takeoff {{code}} is enabled successfully. Please save the record to make this changes permanent.",
      "Factor": "Factor",
      "formula": "Formula",
      "goniometer": "Angular unit",
      "goToQto": "Open QTO",
      "header": {
        "formTitle": "Quantity Takeoff Header Details",
        "gridTitle": "Quantity Takeoff Header"
      },
      "image": {
        "Title": "image"
      },
      "IsReadonly": "Read-Only",
      "location": "Location",
      "locations": {
        "deleteLocationErrorTitle": "This location cannot be deleted since it (or its child) has been used in QTO detail",
        "deleteLocationHeaderTitle": "Delete Location Error",
        "form": "Location Detail Form",
        "title": "Location"
      },
      "moduleName": "QTO",
      "noDecimals": "Rounding Precision",
      "operatorError": "The operator should be {{object}}",
      "Package": "Package",
      "Package2Header": "Sub Package",
      "performedFrom": "Performed From",
      "performedFromWip": "Performed From(WIP)",
      "performedFromBil": "Performed From(Bill)",
      "performedTo": "Performed To",
      "performedToWip": "Performed To(WIP)",
      "performedToBil": "Performed To(Bill)",
      "PrjLocation": "Location",
      "PrjStructureDesc": "Project Structure Description",
      "projectName": "Project Name",
      "projectNo": "Project No.",
      "qtoDate": "QTO Date",
      "QtoFormulaCode": "QTO Formula",
      "QtoFormulaDesc": "QTO Formula Description",
      "QtoFormulaGonimeter": "QTO Formula Angular unit",
      "QtoFormulaType": "QTO Formula Type",
      "QtoLineType": "Type",
      "QtoLineTypeDesc": "QTO Line Type Description",
      "QtoPresentationFormula": "QTO Presentation Formula",
      "QtoReference": "QTO Reference",
      "Reference": "Reference",
      "remark": {
        "title": "Remark"
      },
      "selectBoqItemError": "You should not select the BoQ Item",
      "SelectQtoFormula": "Please select a QTO Formula",
      "SubTotal": "Subtotal",
      "subtotal": {
        "form": "Subtotal From Detail",
        "title": "Subtotal"
      },
      "tab": {
        "detail": "Detail",
        "header": "Header"
      },
      "useRoundedResults": "Use Rounding",
      "wizard": {
        "changeWizardCaption": "Change Disable/Enable Wizard",
        "disableProgressError": "Please choose a Quantity Takeoff Header",
        "header": "Tools",
        "create": {
          "pes": {
            "NoContract": "Cannot create PES as the BoQ has not been assigned to any contract",
            "noContractError": "PES cannot be created because no contract has been selected. Please select a contract first.",
            "noDateDeliveredError": "PES cannot be created because no Date delivered has been selected. Please select a Date delivered first.",
            "title": "Create PES",
            "noPes": "please select a Quantity Takeoff Header with QTO Purpose 'Procurement / PES'",
            "allAbove": "All lines with the selected Pes number + all select new lines",
            "allQtoNotSelected": "All QTO lines that don't have any PES (new lines) link ",
            "allQtoSelected": "All lines with the selected Pes number",
            "create": "Create",
            "pes": "PES",
            "update": "Update",
            "updateWith": "Update With",
            "dateDelivered": "Date Delivered",
            "fail": "Create PES failed",
            "generteType": "Generate Type",
            "NotCreatedPes": "Can not Create Pes for Procurement WQ/AQ",
            "warningOfQtoLineWithValidationError": "There are QTO lines with validation errors. Please correct the errors before transferring the quantities."
          },
          "wip": {
            "fail": "Create WIP failed",
            "noSalesWip": "please select a Quantity Takeoff Header with QTO Purpose 'Sales / WIP'",
            "title": "Create WIP",
            "allAbove": "All lines with the selected WIP number + all select new lines",
            "allQtoNotSelected": "All QTO lines that don't have any WIP (new lines) link ",
            "allQtoSelected": "All lines with the selected WIP number",
            "create": "Create",
            "generteType": "Generate Type",
            "update": "Update",
            "IsOrdQuantityOnly": "Quantities for Contract only",
			   "IsOrdQuantityOnlyWipHint": "Create/Update Wip which have contract assignment in QTO Lines",
            "IsOrdQuantityOnlyBilHint": "Create/Update Bill which have contract assignment in QTO Lines",
            "NoSelectedQto": "please select a Quantity Takeoff Header",
            "updateWith": "Update With",
            "NotCreatedWip": "Can not Create WIP for Sales WQ/AQ",
            "bill": "Bill",
            "createBillTitle": "Create Bill",
            "createWipTitle": "Create WIP",
				 "scopeConfig": "QtO Scope Configuration",
				 "basicSetting": "Basic Setting",
            "purposeType": "Purpose",
            "wip": "WIP",
				 "collectiveWipHint": "Collective Bill to Contracts"
          },
          "ChangeQTOStatus": {
            "fail": "Change QTO Status fail",
            "NoSelectedQto": "please select a Quantity Takeoff Header",
            "title": "Change QTO Status"
          },
          "bill": {
            "allAbove": "Update lines with Bill number + all new lines",
            "allQtoSelected": "Update lines with Bill number"
          }
        },
        "qtoDetail": {
          "title": "Copy QTO Lines"
        },
        "wizardDialog": {
          "copyQtoDetail": "List of copied Lines",
          "allItems": "Starting from Selected Line",
          "scopeLabel": "Selection",
          "selectItems": "For all selected Lines",
          "bulkEditor": "Bulk Editor",
          "NoSelectedItem": "please select items",
          "copyWarnning": "The two QTO documents have different Rubric and the select QTO has no formula(freeInput).",
          "copyQtoLineWarnning": "The target BoQ Uom is different to the source Boq Uom",
          "renumberQtoDetail": "Renumber QTO Lines"
        },
        "noRenumber": "Unable to change checked details.Incorrect Line:{{value}}",
        "QtoProved": "The Lines cannot be renumbered, because the Status of the QTO document is Read Only.",
        "RenumberFailed": "Some of the selected Lines will not be renumbered, either because they are marked as Read Only or they have been used for a WIP/PES",
        "ChangeQTOStatus": "Change QTO Status",
        "ChangeQTODetailStatus": {
          "title": "Change QTO Detail Status"
        },
        "ChangeQTOSheetStatus": {
          "title": "Change QTO Sheet Status"
        },
        "EntireQto": "Entire Quantity takeoff",
        "HighlightedQto": "Highlighted Quantity takeoff line",
        "QtoScope": "Select QTO Scope",
        "ResultSet": "Current Result Set",
        "warningOfQtoScopeError": "Not all of the Qtos associated with WIP are selected. please correce the errors before transferring the quantities",
        "warningBillQtoScopeError": "Not all of the Qtos associated with Bill are selected. please correce the errors before transferring the quantities",
        "includeQtoDetail": "QTO Detail",
        "includeSheets": "Sheets",
        "includeGenerateDate": "General QTO data"
      },
      "WorkCategoryDesc": "Work Category Description",
      "PrcBoq": "BoQ Reference No.",
      "PrjBoq": "Project BoQ",
      "ProcurementBoQ": "Procurement / PES",
      "QtoTargetType": "QTO Purpose",
      "SaleWIP": "Sales / WIP&Bill",
      "QtoFormulaSpecification": "Specifications/Text",
      "qtoHeaderMissing": "Please select Quantity Takeoff Header!",
      "qtoDetailExists": "Quantity Takeoff already exists",
      "PesHeaderCode": "PES Code",
      "PesHeaderDescription": "PES Description",
      "WipHeaderCode": "Wip Code",
      "WipHeaderDescription": "WIP Description",
      "BusinessPartnerFk": "Business Partner",
      "confirmDelete": "Are you sure you want to delete QTO Header and related QTO detail?",
      "confirmDeleteTitle": "Confirm Delete",
      "ContractCode": "Contract / PO",
      "deleteReferenceItemErrorMessage": "This Item has a reference to another item, can not be deleted",
      "hasUnValidCodeErrorMessage": "The Line Reference does not exist.",
      "OrdHeaderFk": "Contract",
      "ordQuantity": "Contract Quantity",
      "ordUnitRate": "Contract Unit Rate",
      "pes": "PES",
      "PrcStructureFk": "Procurement Structure Code",
      "prevQuantity": "Previous Quantity",
      "QTOStatusFk": "QTO Status",
      "remQuantity": "Remaining Quantity",
      "selfReferenceErrorMessage": "Invalid Line Reference! The Line cannot reference itself.",
      "threeFiledReferenceErrorMessage": "The code[{{code}}] generated by {{field1}} and {{field2}} and {{field3}} has been referenced by another QTO Line.",
      "totalQuantity": "Total Quantity",
      "Value1Detail": "Value1 Detail",
      "wip": "WIP",
      "PrevPeriodPrice": "Previous Period Price",
      "Increment": "Increment",
      "totalPrice": "Total Price",
      "lastOperatorShouldBeEqualSymbolError": "You should use the '=' symbol to denote the end of the formula.",
      "nonEqualSymbolOperatorError": "Missing '=' operator.",
      "cannotDeleteQtoHeader": "Can not delete the selected QTO Header as  its QTO status is Read Only",
      "imagSpecification": "Image and Specification",
      "emptyOrNullValueErrorMessage": "The field {{fieldName}} should not be empty and should not be zero",
      "cycleReferenceErrorMessage": "Cyclic Dependence:{{object}}",
      "RebExport": "Export QTO document",
		 "ExportREBFormConfig": {
			 "AdditionalSetting": "Additional Setting",
			 "ExportQtoDocInfo": "Export QTO Document Information"
		 },
      "RebFormat": "Export Format",
      "from": "From",
      "to": "To",
      "sheetScope": "The sheet referral limit was exceeded.",
      "LineReferenceIsUnique": "Move failed! Some or all the lines in the Sheet have the same Sheet-Line-Index as the ones you are trying to move. The Sheet, Line and Index should be unique.",
      "LineReferenceIsUniqueTitle": "Drag/Drop Error",
		 "errorQtosCode": "The below qto line(s) have invalid number format:{{linereference}}",
		 "multiLineQTOFormulaRefIsFix": "Unable to change the Sheet-Line-Index within multi-Line Details or the qto line(s) is ReadOnly: {{linereference}}",
		 "sheetIsReadonly": "Unable to modify the QTO Line(s) which sheet is ReadOnly",
		 "lineReferenceIsUsedInAnotherGroup": "The LineReference is used in the Address-Range of another Detail. Incorrect qto detail : {{linereference}}",
		 "createQtoDetainWithDiffAssignment": "Unable to add a qto detail into a group with different BoQ or Location.",
		 "dragNDropInfo": "Drag/Drop Info",
		 "changeLineReferenceFailed": "Change failed.",
      "CommentLine": "The CommentLine length can't be >56",
      "sheetDelete": "Are you sure to delete QTO Sheet and relate QTO detail?",
      "sheetDeleteTitle": "Sheet Delete",
      "sheetWarning": "Unable to delete Qto Line with IsReadOnly and Wip/Pes.Incorrect Line:{{value}}",
      "sheetWarningTitle": "Sheet Warning",
      "zeroErrorMessage": "should not be zero.",
      "valueIncludeSpecialCharError": "this field can't contain: {{invalidChar}}",
      "cannotDeleteQtoLines": "Can not delete QTO lines  which have WIP/PES assignment or the  QTO lines are readonly.",
      "updateFormula": {
        "title": "Update Formula from Qto Formula",
        "warning": "Formula has been changed. The QTO document containes data that are using formula. Please check.",
        "warningTitle": "Formula changed"
      },
      "selectBoqTemplate": "Selected BoQ Temp",
      "selectPackageBoq": "Please select Package BoQ(entire BoQ/BoQ Root Item Only).",
      "selectWicBoq": "Please select WIC BoQ(Filter BoQ Item).",
      "dialogTitleQTOHeader": "Assign QTO Header",
      "detailStatus": "QtO Detail Status",
      "customerDescription": "Customer Description",
      "V": "V",
      "BoqReferenceNotFound": "Boq Reference Not Found",
      "boqItem": "BoQ Item",
      "boqItemBrief": "BoQ Item Brief",
      "BoqItemReference": "BoQ Item Reference",
      "boqReference": "BoQ Reference",
      "BudgetCode": "Budget Code",
      "Classification": "Classification",
      "FormulaResult": "Math formula",
      "headerBoq": "BoQ Reference No.",
      "IsBlocked": "Disabled",
      "IsEstimate": "Estimate",
      "isLive": "Live",
      "IsOK": "Is Ok",
      "LineIndex": "Index",
      "LineReference": "Line",
      "Operator1": "Operator 1",
      "Operator2": "Operator 2",
      "Operator3": "Operator 3",
      "Operator4": "Operator 4",
      "Operator5": "Operator 5",
      "outlineSpecification": "Outline Specification",
      "PageNumber": "Sheet",
      "PerformedDate": "Performed Date",
      "PrjLocationReference": "Location Reference",
      "PrjStructure": "Project Structure",
      "QtoDetailReference": "Line Reference",
      "qtoTypeFk": "QTO Type",
      "Remark1Text": "Remark 1 Text",
      "RemarkText": "Remark",
      "Result": "Result",
      "SpecialUse": "Special Use",
      "threeFiledUniqueValueErrorMessage": "The {{field1}} and {{field2}} and {{field3}} should be unique",
      "Value1": "Value 1",
      "Value2": "Value 2",
      "Value3": "Value 3",
      "Value4": "Value 4",
      "Value5": "Value 5",
      "WorkCategory": "Work Category",
      "AssetMasterDesc": "Asset Master Description",
      "BoqHeaderFk": "BoQ Header",
      "qtoDetailPageNumber": "Sheet No.",
      "structure": "Sheet",
      "ConHeaderFk": "Contract / PO",
      "entityQTOStatusFk": "QTO Status",
      "Value2Detail": "Value2 Detail",
      "Value3Detail": "Value3 Detail",
      "Value4Detail": "Value4 Detail",
      "Value5Detail": "Value5 Detail",
      "LineText": "Line Text",
      "isAq": "IsAQ",
      "PrcWqAq": "Procurement WQ/AQ",
      "SalesWqAq": "Sales WQ/AQ",
      "selectWqAqType": "Please select the Procurement WQ/AQ or Sales WQ/AQ",
      "existWqAqQtoByBoq": "The Boq for QTO Header existed. Please select others.",
      "PrjLocationDesc": "Location Description",
      "isWq": "IsWQ",
      "QtoLineTypeCodeNotFound": "Qto Line Type Not Found",
      "isBq": "IsBQ",
      "isIq": "IsIQ",
      "isGq": "IsGQ",
      "GqIsDisabled": "Only BQ Quantities can be marked as guessed quantity.",
      "GqLineTypeError": "On the basis of restrictions of the REB 23003 Edition 2009 Supposed Quantities can have no flag.",
      "bilheaderfk": "Bill NO",
      "BillDescription": "Billing Description",
      "entityBillNo": "Bill NO",
      "BilledQuantity": "BQ Quantity",
      "BQPreviousQuantity": "BQ Previous Quantity",
      "BQPrevQuantity": "BQ Previous Quantity",
      "BQRemainingQuantity": "BQ Remaining Quantity",
      "BQTotalQuantity": "BQ Total Quantity",
      "IQPrevQuantity":  "IQ Previous Quantity",
      "IQPreviousQuantity": "IQ Previous Quantity",
      "IQRemainingQuantity": "IQ Remaining Quantity",
      "InstalledQuantity": "IQ Quantity",
      "GuessedQuantity": "GQ Quantity",
      "document": {
        "containerGridTitle": "Qto Detail  Documents",
        "qtoDetailDocumentType": "Qto Detail Document Type"
      },
      "isForSplit": "Is for Split",
      "splitQtyNo": "SplitQty No.",
      "selectSplitQuantityError": "should select the boq item split quantity.",
      "selectLineItemError": "should select the line item.",
      "selectChangedLineItemWarning": "Changing the assignment leads to make different from Estimate Line Items assignment.<br>Do you want to proceed?",
      "prjChangeStatusReadOnlyInfo": "Forbidden: Cannot create quantity take off as Contract's project change status!",
      "filterByBoqSplitQuantity": "Filter by BoQ Split Quantity",
      "AssignmentError": "No available QTO lines to create /update Pes",
	   "cannotDeleteQtoLinesAsHasQtoComments": "Cannot delete QTO line which have Qto comments assignment.",
	   "cannotDeleteQtoLinesAsReferenceObj": "Cannot delete QTO line which have Qto detail document or comments assignment.",
      "cannotDeleteQtoLinesAsReadOnly": "Cannot delete QTO line which is readonly.",
      "deleteQtoLinesWarningMsg": "Do you want to delete QTO lines which have {{moduleName}} assigment?",
      "IQTotalQuantity": "IQ Total Quantity",
      "missSplitNoTitle": "Splits do not exist in target BoQ item.",
      "missSplitNoWarning": "Splits [{{warning}}] do not exist in target BoQ item. Please create them in the BoQ to copy QTO lines for these splits.",
      "SurchargeBoQItemsErrormsg": "Can not assign QTO lines to surcharge BoQ items and Division BoQ items.",
		"SubQuantityBoQItemsErrormsg": "Can not assign QTO lines to BoQ items which contains sub quantity item(s).",
      "CumulativePercentage": "IQ Cumulative Percentage",
      "PercentageQuantity": "IQ Percentage Quantity",
      "IQPercentageQuantity":  "IQ Percentage Quantity",
      "BQPercentageQuantity":  "BQ Percentage Quantity",
      "QuantityDetail": "Quantity Detail",
      "QuantityAdjDetail": "AQ-Quantity Detail",
      "BQCumulativePercentage": "BQ Cumulative Percentage",
      "BriefInfo": "Outline Specification",
      "basqtoCommentstypefk": "Type",
      "comments": {
        "qtoDetailDocumentType": "Qto Detail Document Type",
        "containerGridTitle": "QTO Comments"
      },
		 "userDefined": "User Defined",
      "sortCode": "Sort Code",
      "sortCode01": "Sort Code 1",
      "sortCode02": "Sort Code 2",
      "sortCode03": "Sort Code 3",
      "sortCode04": "Sort Code 4",
      "sortCode05": "Sort Code 5",
      "sortCode06": "Sort Code 6",
      "sortCode07": "Sort Code 7",
      "sortCode08": "Sort Code 8",
      "sortCode09": "Sort Code 9",
      "sortCode10": "Sort Code 10",
      "entityProgressInvoiceNo": "Progress Invoice(Bill)",
      "filterByNoWipOrBil": "Filter by without Wip or Bill Reference",
		 "filterByCorrectionBill": "Filter by cancellation / correction Bill",
      "sheetReadonly": "The sheet is readonly, please change other sheet.",
      "sheetNoIsLiveOrReadable": "The sheet is not Live or readable.",
	  "qtocommenttitle": "QTO Comment(Project)",
      "uniqCode": "The Code should be unique",
      "qtoConfig": "QTO Comment",
      "qtoConfigSystem": "System",
      "qtoConfigQtoRubric": "Quantity Take off Rubric",
      "qtoConfigPrj": "Project",
      "commentFrom": "Comment From",
      "qtoSheetStatus": "Qto Sheet Status",
      "sourceQto": "Source QTO",
      "sourceQtoConfig": "Document properties",
      "differentRubric": "The Qto Header Rubric are different.",
      "costGroup": "Cost Group",
      "notCreateQtoLines": "Can't create Qto Lines.",
      "noLinkBaseBoqItem": "not Link the base boqItem.",
      "notCreateItem": "can't create Item.",
      "notCreateChildItem": "can't create Child Item.",
      "exQtoQuantity": "Quantity(Project)",
      "exQtoQuantityAdj": "AQ-Quantity(Project)",
      "qtoHeaderReadOnly": "The QtoHeader is readonly.",
      "qtoDetailReadOnly": "The qto detail is readonly.",
      "rebImport": {
        "title": "REB Import",
        "baseGroup": "Basic Setting",
        "filterAddressGroup": "Filter Address Area",
        "filterBoQGroup": "Filter BoQ Area",
        "filterLocationGroup": "Filter Location Area",
        "filterBillToGroup": "Filter Bill To Area",
        "filterCostGroupGroup": "Filter Cost Group Area",
        "selectQtoFile": "Please select QTO file",
        "entire2Import": "Import Entire Quantity Takeoff",
        "wqQuantity": "WQ Quantity",
        "aqQuantity": "AQ Quantity",
        "iqQuantity": "IQ Quantity",
        "bqQuantity": "BQ Quantity",
        "costGroupCatalogEntity": "Cost Group Catalog",
        "importTotal": "Total imported: {{param}}",
        "iQTotal": "IQ Total: {{param}}",
        "bQTotal": "BQ Total: {{param}}",
        "wQTotal": "WQ Total: {{param}}",
        "aQTotal": "AQ Total: {{param}}",
        "sheetArea": "Please ensure Sheet Area '{{value0}}' is within Qto Header Sheet Area '{{value1}}'.",
        "lineArea": "Please ensure Line Area '{{value0}}' is within Qto Header Line Area '{{value1}}'.",
        "indexArea": "Please ensure Index Area '{{value0}}' is within Qto Header Index Area '{{value1}}'.",
        "errorIndex": "Index cannot be greater than {{value0}}.",
        "errorSheet": "Sheet cannot be greater than {{value0}}."
      },
      "filterVersionQto": "Filter Version Qtos",
      "estLineItemFk": "Line Item",
      "lineItem": {
        "title": "Line Items",
        "iqPreviousQuantity": "IQ Previous Quantity",
        "iqQuantity": "IQ Quantity",
        "iqTotalQuantity": "IQ Total Quantity",
        "iqPercentageQuantity": "IQ Percentage Quantity",
        "iqCumulativePercentage": "IQ Cumulative Percentage",
        "bqPreviousQuantity": "BQ Previous Quantity",
        "bqQuantity": "BQ Quantity",
        "bqTotalQuantity": "BQ Total Quantity",
        "bqPercentageQuantity": "BQ Percentage Quantity",
        "bqCumulativePercentage": "BQ Cumulative Percentage"
      },
		 "copyOption": {
			 "IsActivate": "Edit Copy Option",
			 "editCopyOptionHeader": "Edit CopyOption",
			 "assignments": "Assignments",
			 "properties": "Properties",
			 "copyPriority": "Copy Priority",
			 "fromDetails": "From Details",
			 "fromLeadingStructure": "From Leading Structure"
		 }
    }
    }
}
