using RIB.Visual.Basics.Core.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Platform.Common;
using Jint;
using Jint.Native;
using Jint.Runtime;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Platform.Core;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Basics.Company.BusinessComponents;
using NLS = RIB.Visual.Estimate.Main.Localization.Properties.Resources;
using RIB.Visual.Estimate.Common.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;
using System.Security.AccessControl;
using LocalizationProps = RIB.Visual.Estimate.Main.Localization.Properties;
using System.Diagnostics;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public abstract class EstResourceHandler<T> : JsObjectEntity<T> where T : IIdentifyable
	{
		private IEstimateResourceFactory _EstResourceFactory;

		private EstResourceInstance CreateResource(Func<IScriptEstResource> CreateResourceFunc, EstResourceType type, bool triggerAction = true)
		{
			try
			{
				if (CreateResourceFunc == null)
				{
					return null;
				}

				/* check whether the parent is subitem, if not, can not create resource */
				var result = CanAddResource();

				if (result.Disabled)
				{
					Error(string.Format("Create {0} failed because {1}.", type.ToString(), result.Error));

					return null;
				}

				/* do before create resource */
				BeforeCreateResource();

				/* create resource*/

				var entity = CreateResourceFunc();

				if (entity == null)
				{
					return null;
				}

				/* set parent */
				IScriptEstResource parent = this.GetParent() != null ? this.GetParent().Entity : null;

				if (parent != null)
				{
					entity.Parent = parent;
				}

			 /* mark the lineItem as modified */
			 (this.Response as ScriptResponseBase).AddModifiedLineItem(entity.EstLineItemFk);

				/* for estimate */
				IScriptEstLineItem lineItem = this.GetLineItem();

				EstResourceInstance wrapper = GetResourceInstance(entity, lineItem);

				/* for construction */
				this.Resources.Add(wrapper);

				if (triggerAction)
				{
					AfterCreateResource(wrapper);
				}

				AddResourceBase(entity, parent, lineItem);

				return wrapper;
			}
			catch (Exception ex)
			{
				if (ex is EstRuleWarningException)
				{
					Warn(ex.Message);
					return null;
				}
				else
				{
					throw;
				}
			}
		}

		/// <summary>
		/// Resources.
		/// </summary>
		public virtual ICollection<IEstResourceInstance> Resources { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IScriptRequest Request { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public IScriptResponse Response { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public EstResourceHandler()
		{

		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="engine"></param>
		/// <param name="request"></param>
		/// <param name="response"></param>
		public EstResourceHandler(Engine engine, ScriptRequestBase request, ScriptResponseBase response)
			: base(engine, request, response)
		{
			this.Request = request;

			this.Response = response;

			this.Resources = new List<IEstResourceInstance>();

			if (this.Request is IEstimateScriptRequest)
			{
				_EstResourceFactory = (this.Request as IEstimateScriptRequest).GetEstimateResourceFactory();
			}
			else
			{
				_EstResourceFactory = new EstimateResourceFactory(request.ProjectFk);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("getSingleSubItem", JsObjectInstanceConstant.EstResource, "key", MinArgsLength = 0)]
		public IEstResourceInstance GetSingleSubItem(JsValue thisObject, JsValue[] arguments)
		{
			if (arguments.Length < 1)
			{
				throw new Exception("getSingleSubItem function need a key");
			}

			if (this.Request is EstRuleScriptRequest)
			{
				var code = TypeConverter.ToString(arguments.At(0));

				Func<IScriptEstResource> createSubItemFunc = () =>
				{
					return CreateResource(EstResourceType.SubItem).Entity;
				};

				var lineItem = this.GetLineItem();

				var parent = this.GetParent() != null ? this.GetParent().Entity : null;

				var subItem = (this.Request as EstRuleScriptRequest).ApplicationContext.ResourceModificationTracker.GetSingleLineItemByCode(code, lineItem, parent, createSubItemFunc);

				return EstimateContext.EstResourceInstance.New(lineItem, subItem, this.Request, this.Response, this.Engine);
			}

			return null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("createCostCode", JsObjectInstanceConstant.EstResource, "code", MinArgsLength = 0)]
		public JsValue CreateCostCode(JsValue thisObject, JsValue[] arguments)
		{
			if (arguments.Length > 0)
			{
				string code = TypeConverter.ToString(arguments.At(0));

				try
				{
					return this.CreateCostCode(code);
				}
				catch(Exception e)
				{
					Error(new BusinessLayerException(string.Format("Crete costcode {0} failed due to inner exception!", code), e).ToString());
					return null;
				}
			}
			else
			{
				throw new Exception("It must input code, when you use createCostCode function.");
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("createMaterial", JsObjectInstanceConstant.EstResource, "code", "materialCatalogCode", MinArgsLength = 0)]
		public JsValue CreateMaterial(JsValue thisObject, JsValue[] arguments)
		{
			if (arguments.Length > 0)
			{
				string code = TypeConverter.ToString(arguments.At(0));

				string materialCatalogCode = arguments.Length > 1 ? arguments.ElementAt(1).AsString() : string.Empty;

				try
				{
					return this.CreateMaterial(code, materialCatalogCode);//materialGroupCode, 
				}
				catch(Exception e)
				{
					Error(new BusinessLayerException(string.Format("Create material {0} in catalog {1} failed due to inner exception!", code, materialCatalogCode), e).ToString());
					return null;
				}
			}
			else
			{
				throw new Exception("It must input code, when you use createMaterial function.");
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="materialCodes"></param>
		/// <param name="catalogCodes"></param>
		/// <returns></returns>
		[JsProperty("createMaterials", IsAutoType = true)]
		public EstResourceInstance[] CreateMaterials(object[] materialCodes, object[] catalogCodes = null)
		{
			if (materialCodes == null || !materialCodes.Any())
			{
				return new EstResourceInstance[0];
			}

			int index = 0;

			var result = new List<EstResourceInstance>();

			while (index < materialCodes.Length)
			{
				try
				{
					if (catalogCodes == null || catalogCodes.Length <= index)
					{
						result.Add(this.CreateMaterial(materialCodes[index] as string, String.Empty));
					}
					else
					{
						result.Add(this.CreateMaterial(materialCodes[index] as string, catalogCodes[index] as string));
					}
				}
				catch(Exception e)
				{
					Error(new BusinessLayerException(string.Format("Create material {0} in catalog {1} failed due to inner exception!", materialCodes[index], catalogCodes[index]), e).ToString());
				}

				index++;
			}

			return result.ToArray();
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("createPlant", JsObjectInstanceConstant.EstResource, "code", "plantMastercode", MinArgsLength = 1, Doc = InstanceFuncDocExpansion.Func_CreatePlant_Doc)]
		public JsValue CreatePlant(JsValue thisObject, JsValue[] arguments)
		{
			string code = arguments.Length > 0 ? TypeConverter.ToString(arguments.At(0)) : string.Empty;
			string plantMastercode = arguments.Length > 1 ? TypeConverter.ToString(arguments.At(1)) : string.Empty;
			try
			{
				return this.CreatePlant(code, plantMastercode);
			}
			catch (Exception e)
			{
				Error(new BusinessLayerException(string.Format("Create plant {0} failed due to inner exception!", code), e).ToString());
				return null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("createComputationalLine", JsObjectInstanceConstant.EstResource, "quantitydetail", MinArgsLength = 0)]
		public JsValue CreateComputationalLine(JsValue thisObject, JsValue[] arguments)
		{
			string quantityDetail = "";
			if (arguments.Length > 0)
			{
				quantityDetail = TypeConverter.ToString(arguments.At(0));
			}
			try
			{
				var creationInfo = GenerateCreationInfo();
				creationInfo.AfterCreated = e => e.QuantityDetail = string.IsNullOrWhiteSpace(quantityDetail) ? "" : quantityDetail;
				return CreateResource(
					  () => this._EstResourceFactory.CreateResource((int)EstResourceType.ComputationalLine, creationInfo),
					  EstResourceType.ComputationalLine);
			}
			catch (Exception e)
			{
				Error(new BusinessLayerException(string.Format("Create ComputationalLine failed due to inner exception!"), e).ToString());
				return null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("createAssembly", JsObjectInstanceConstant.EstResource, "code", "AssemblyCatCode", "RootAssemblyCatCode", MinArgsLength = 0)]
		public JsValue CreateAssembly(JsValue thisObject, JsValue[] arguments)
		{
			if (arguments.Length > 0)
			{
				string code = TypeConverter.ToString(arguments.At(0));

				string assemblyCatCode = arguments.Length > 1 ? TypeConverter.ToString(arguments.At(1)) : string.Empty;

				string rootAssemblyCatCode = arguments.Length > 2 ? TypeConverter.ToString(arguments.At(2)) : string.Empty;

				try
				{
					return this.CreateAssembly(code, assemblyCatCode, rootAssemblyCatCode, (this.Request as ScriptRequestBase).ProjectFk);
				}
				catch(Exception e)
				{
					Error(new BusinessLayerException(string.Format("Create assembly {0} failed due to inner exception!", code), e).ToString());
					return null;
				}
			}
			else
			{
				throw new Exception("It must input code, when you use createAssembly function.");
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("createSubItem", JsObjectInstanceConstant.EstResource, "code", MinArgsLength = 0)]
		public JsValue CreateSubItem(JsValue thisObject, JsValue[] arguments)
		{
			if (arguments.Length > 0)
			{
				string code = TypeConverter.ToString(arguments.At(0));

				return this.CreateSubItem(code);
			}
			else
			{
				return CreateResource(EstResourceType.SubItem);
			}
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("createText", JsObjectInstanceConstant.EstResource, "description", MinArgsLength = 0)]
		public JsValue CreateText(JsValue thisObject, JsValue[] arguments)
		{
			string desc = "";
			if (arguments.Length > 0)
			{
				desc = TypeConverter.ToString(arguments.At(0));
			}
			return CreateResourceText(desc, EstResourceType.TextLine);
		}
		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("createInternalText", JsObjectInstanceConstant.EstResource, "description", MinArgsLength = 0)]
		public JsValue CreateInternalText(JsValue thisObject, JsValue[] arguments)
		{
			string desc = "";
			if (arguments.Length > 0)
			{
				desc = TypeConverter.ToString(arguments.At(0));
			}
			return CreateResourceText(desc, EstResourceType.InternalTextLine);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="desc"></param>
		/// <param name="resourceType"></param>
		/// <returns></returns>
		public JsValue CreateResourceText(string desc, EstResourceType resourceType)
		{
			if (EstResourceType.TextLine == resourceType || EstResourceType.InternalTextLine == resourceType)
			{
				var creationInfo = GenerateCreationInfo();
				creationInfo.DescriptionInfo = new DescriptionTranslateType()
				{
					Description = desc,
					Translated = desc,
					VersionTr = 0
				};
				return CreateResource(() => this._EstResourceFactory.CreateResource((int)resourceType, creationInfo), resourceType);
			}
			return JsValue.Null;
		}

		/// <summary>
		/// todo-wui: Obsolete, would delete
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		[JsProperty("getResource", IsAutoType = true, Doc = "Obsolete, please using getResourceByCode instead")]
		public EstResourceInstance GetResource(string code)
		{
			return SearchResource(code);
		}

		/// <summary>
		/// todo-wui: Obsolete, would delete
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		[JsProperty("removeResource", IsAutoType = true, Doc = "Obsolete, please using removeResourceByCode instead")]
		public EstResourceInstance RemoveResource(string code)
		{
			return SearchResource(code, true);
		}

		/// <summary>
		/// Get all resource
		/// </summary>
		/// <returns></returns>
		[JsProperty("getResources", Type = "fn() -> [+EstResourceInstance]", IsAutoType = true)]
		public IEstResourceInstance[] GetResources()
		{
			return this.Resources.ToArray();
		}

		/// <summary>
		/// Get resource by code
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		[JsProperty("getResourceByCode", JsObjectInstanceConstant.EstResource, IsAutoType = true)]
		public IEstResourceInstance GetResourceByCode(string code)
		{
			return SearchResource(code);
		}

		/// <summary>
		/// Remove resource by code
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		[JsProperty("removeResourceByCode", JsObjectInstanceConstant.EstResource, IsAutoType = true)]
		public IEstResourceInstance RemoveResourceByCode(string code)
		{
			return SearchResource(code, true);
		}

		/// <summary>
		/// Get Resources By Major CostCode
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("getResourcesByMajorCostCode", Type = "fn(majorCostcode: string) -> [+EstResourceInstance]")]
		public JsValue GetResourceByMajorCostCode(JsValue thisObject, JsValue[] arguments)
		{
			if (arguments.Length > 0)
			{
				string code = TypeConverter.ToString(arguments.At(0));

				var resourceList = this.GetResourcesByMajorCC(code);

				return JsValue.FromObject(this.Engine, resourceList.Select(e => Injector.Get<IEstResourceInstance>().New(e, this.Request, this.Response, this.Engine)).ToArray());
			}
			else
			{
				return this.Engine.Array.Construct(new JsValue[0]);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("replaceCostCode", JsObjectInstanceConstant.EstResource, "oldCode", "newCode")]
		public JsValue ReplaceCostCode(JsValue thisObject, JsValue[] arguments)
		{
			var oldCode = arguments.At(0).AsString();

			var newCode = arguments.At(1).AsString();

			this.RemoveResourceByCode(oldCode);

			return this.CreateCostCode(newCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("replaceMaterial", JsObjectInstanceConstant.EstResource, "oldCode", "newCode", "newCatalogCode")]
		public JsValue ReplaceMaterial(JsValue thisObject, JsValue[] arguments)
		{
			var oldCode = arguments.At(0).AsString();

			var newCode = arguments.At(1).AsString();

			var newCatalogCode = String.Empty;

			if (arguments.Length > 2)
			{
				newCatalogCode = arguments.At(2).AsString();
			}

			this.RemoveResourceByCode(oldCode);

			return this.CreateMaterial(newCode, newCatalogCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("replacePlant", JsObjectInstanceConstant.EstResource, "oldCode", "newCode")]
		public JsValue ReplacePlant(JsValue thisObject, JsValue[] arguments)
		{
			var oldCode = arguments.At(0).AsString();

			var newCode = arguments.At(1).AsString();

			this.RemoveResourceByCode(oldCode);

			return this.CreatePlant(newCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("replaceAssembly", JsObjectInstanceConstant.EstResource, "oldCode", "newCode", "newCatalogCode", "newRootAssemblyCatCode")]
		public JsValue ReplaceAssembly(JsValue thisObject, JsValue[] arguments)
		{
			var oldCode = arguments.At(0).AsString();

			var newCode = arguments.At(1).AsString();

			var newCatalogCode = arguments.Length > 2 ? arguments.At(2).AsString() : String.Empty;

			var newRootAssemblyCatCode = arguments.Length > 3 ? arguments.At(3).AsString() : string.Empty;

			this.RemoveResourceByCode(oldCode);

			return this.CreateAssembly(newCode, newCatalogCode, newRootAssemblyCatCode, (this.Request as ScriptRequestBase).ProjectFk);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="thisObject"></param>
		/// <param name="arguments"></param>
		/// <returns></returns>
		[JsProperty("replaceSubItem", JsObjectInstanceConstant.EstResource, "oldCode", "newCode")]
		public JsValue ReplaceSubItem(JsValue thisObject, JsValue[] arguments)
		{
			var oldCode = arguments.At(0).AsString();

			var newCode = arguments.At(1).AsString();

			this.RemoveResourceByCode(oldCode);

			return this.CreateSubItem(newCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="resourceType"></param>
		/// <returns></returns>
		public EstResourceInstance CreateResource(EstResourceType resourceType)
		{
			return CreateResource(
					  () => this._EstResourceFactory.CreateResource((int)resourceType, GenerateCreationInfo()),
					  resourceType);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public EstResourceInstance CreateCostCode(string code)
		{
			var creationInfo = GenerateCreationInfo();
			string msg = string.Empty;
			if (ScriptHelper.CanCreateCostCode(code, this.Request, creationInfo, out msg))
			{
				return CreateResource(() => this._EstResourceFactory.CreateCostCodeByCode(code, creationInfo),
					EstResourceType.CostCode);
			}
			else
			{
				this.ResponseBase.WriteWarning(this.FormatError(msg));
				return null;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="materialCatalogCode"></param>
		/// <returns></returns>
		public EstResourceInstance CreateMaterial(string code, string materialCatalogCode)
		{
			return CreateResource(
					  () => this._EstResourceFactory.CreateMaterialByCode(code, materialCatalogCode, GenerateCreationInfo()),
					  EstResourceType.Material);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="plantMasterCode"></param>
		/// <returns></returns>
		public EstResourceInstance CreatePlant(string code, string plantMasterCode = "")
		{
			var job = EstJobHelper.GetJobById(this.GetJobId(), (this.Request as ScriptRequestBase).ProjectFk);

			if (job != null && job.PlantEstimatePriceListFk.HasValue)
			{
				var creationInfo = GenerateCreationInfo();
				var unsavedResourceTree = (this.Request as EstRuleScriptRequest).ApplicationContext.ResourceModificationTracker.GetResourcesByLineItemId(this.GetLineItemId());
				creationInfo.UnsavedResourceTree = unsavedResourceTree;
				creationInfo.EstLineItem = this.GetLineItem();
				creationInfo.ParentId = this.GetParentId();
				creationInfo.AfterCreatedEnhance = (entities, existPlant)=>
				{
					var entity = entities.FirstOrDefault();
					if (entities.Count == 1)
					{
						if (!string.IsNullOrWhiteSpace(code) && entity.EstResourceTypeFk == (int)EstResourceType.Plant && entity.EtmPlantFk.HasValue)
						{
							var palntInstance = CreateResource(() => entity, EstResourceType.Plant);

							return palntInstance.Entity.Resources.FirstOrDefault();
						}
						return entity;
					}
					else if (entities.Count > 1 && existPlant != null)
					{
                        var plant = existPlant as EstResourceEntity;

                        foreach (var item in entities.Where(e=>e.EstResourceFk== plant.Id))
						{
							CreateResource(() => item, EstResourceType.Plant);
						}

						return plant;
					}
					return entity;
				};
				return CreateResource(
					  () => this._EstResourceFactory.CreatePlanByCode(code, plantMasterCode, creationInfo),
					  EstResourceType.Plant);
			}
			else
			{
				this.ResponseBase.WriteWarning(this.FormatError(LocalizationProps.Resources.Warn_Rules_Resource_Plant_Not_Create));
			}
			return null;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <returns></returns>
		public EstResourceInstance CreateSubItem(string code)
		{
			return CreateResource(
					  () => this._EstResourceFactory.CreateSubItemByCode(code, GenerateCreationInfo()),
					  EstResourceType.SubItem);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="code"></param>
		/// <param name="assemblyCatCode"></param>
		/// <param name="rootAssemblyCatCode"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public EstResourceInstance CreateAssembly(string code, string assemblyCatCode, string rootAssemblyCatCode, int? projectId)
		{
			var wrapper = CreateResource(
								 () => _EstResourceFactory.CreateAssemblyByCode(code, assemblyCatCode, rootAssemblyCatCode, projectId, GenerateCreationInfo()),
								 EstResourceType.Assembly,
								 false);

			if (wrapper.Entity.Resources != null)
			{
				wrapper.Resources = wrapper.Entity.Resources.Select(e => GetResourceInstance(e, this.GetLineItem()) as IEstResourceInstance).ToList();
			}

			AfterCreateResource(wrapper);

			if (wrapper.Entity.Resources != null)
			{
				SetRuleSourceFk(wrapper.Entity.Resources, wrapper.Entity);
			}

			return wrapper;
		}

		private IEstResourceCreationInfo GenerateCreationInfo()
		{
			return new EstResourceCreationInfo()
			{
				EstHeaderId = this.GetHeaderId(),
				EstLineItemId = this.GetLineItemId(),
				ParentId = this.GetParentId(),
				JobId = this.GetJobId(),
				ProjectId = (this.Request as ScriptRequestBase).ProjectFk,
				GenerateCharacteristicId = false,
				IsGenerateDefaultCharacteristic = true
			};
		}

		private void SetRuleSourceFk(IEnumerable<IScriptEstResource> resourceList, IScriptEstResource resourceParent)
		{
			if (resourceList != null && resourceParent != null)
			{
				foreach (var res in resourceList)
				{
					res.EstRuleSourceFk = resourceParent.EstRuleSourceFk;

					if (res.Resources != null)
					{
						SetRuleSourceFk(res.Resources, res);
					}
				}
			}
		}

		private EstResourceInstance SearchResource(string code, bool isRemove = false)
		{
			EstResourceInstance result = null;

			Queue<IEstResourceInstance> queue = new Queue<IEstResourceInstance>();

			ICollection<IEstResourceInstance> resource = this.Resources;

			while (queue.Count > 0 || resource.Count > 0)
			{
				foreach (var item in resource)
				{
					if (item.Entity.Code == code)
					{
						result = item as EstResourceInstance;

						if (isRemove)
						{
							resource.Remove(result);
						}

						break;
					}

					queue.Enqueue(item);
				}

				if (result == null)
				{
					resource = queue.Dequeue().Resources;
				}
				else
				{
					break;
				}
			}

			return result;
		}

		private IEnumerable<EstResourceInstance> GetResources(string code)
		{
			List<EstResourceInstance> resourceObjInstanceList = new List<EstResourceInstance>();

			if (this.Resources != null)
			{
				resourceObjInstanceList = this.Resources.Where(e => e.Entity.Code.ToUpper() == code.Trim().ToUpper()).Select(e => e as EstResourceInstance).ToList();
			}

			return resourceObjInstanceList;
		}

		private IEnumerable<IScriptEstResource> GetResourcesByMajorCC(string costCode)
		{
			IEnumerable<IScriptEstResource> resourceList = new List<IScriptEstResource>();

			if (this.Resources != null && this.Resources.Any())
			{
				foreach (var item in this.Resources)
				{
					var currenctResources = item.Entity.FlattenResources();

					if (currenctResources != null && currenctResources.Any())
					{
						resourceList = resourceList.Concat(currenctResources);
					}
				}
			}

			return new EstimateMainResourceTotalLogic().GetResoucesByMajorCostCode(resourceList, costCode);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract int? GetParentId();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract int GetHeaderId();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract int GetLineItemId();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected virtual int? GetJobId()
		{
			/* get jobId from request */
			var defaultJobId = (this.Request as ScriptRequestBase).JobFk;

			if (defaultJobId.HasValue)
			{
				return defaultJobId;
			}

			if(this.Request.ApiId == "Estimate.Rule" && this.Request is EstRuleScriptRequest)
			{
				return (this.Request as EstRuleScriptRequest).ApplicationContext.GetEstimateScopeObject().GetDefaultJobId();
			}
			else
			{
				/* get jobId from header(using executionCache) */
				var estHeaderEntity = ExecutionCache.Current.Get<IEstHeaderEntity>(
					String.Format("EstHeader:{0}", (this.Request as ScriptRequestBase).EstHeaderFk),
					() => new EstimateMainHeaderLogic().GetItemById((this.Request as ScriptRequestBase).EstHeaderFk));

				if (estHeaderEntity != null && estHeaderEntity.LgmJobFk.HasValue)
				{
					return estHeaderEntity.LgmJobFk;
				}

				/* get jobId from project(using executionCache) */
				return ExecutionCache.Current.Get<int?>(
					string.Format("getJobIdByProject:{0}", (this.Request as ScriptRequestBase).ProjectFk),
					() => EstJobHelper.GetProjectJobId((this.Request as ScriptRequestBase).ProjectFk));
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract CanAddResourceModel CanAddResource();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract EstResourceInstance GetParent();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		protected abstract IScriptEstLineItem GetLineItem();

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public abstract ICollection<IScriptEstResource> GetRawResources();

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="parent"></param>
		/// <param name="lineItem"></param>
		protected abstract void AddResourceBase(IScriptEstResource entity, IScriptEstResource parent, IScriptEstLineItem lineItem);

		/// <summary>
		/// 
		/// </summary>
		/// <param name="message"></param>
		protected void Warn(string message)
		{
			this.Response.WriteWarning(this.FormatError(message));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="message"></param>
		protected void Error(string message)
		{
			this.Response.WriteError(this.FormatError(message));
		}

		/// <summary>
		/// Before creating resource hook
		/// </summary>
		public virtual void BeforeCreateResource()
		{

		}

		/// <summary>
		/// After creating resource hook
		/// </summary>
		/// <param name="entity"></param>
		public virtual void AfterCreateResource(IEstResourceInstance entity)
		{

		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="lineItem"></param>
		/// <returns></returns>
		public virtual EstResourceInstance GetResourceInstance(IScriptEstResource entity, IScriptEstLineItem lineItem)
		{
			return new EstResourceInstance(lineItem, entity, (this.Request as ScriptRequestBase), (this.Response as ScriptResponseBase), this.Engine);
		}

		/// <summary>
		/// 
		/// </summary>
		public Action<object, TCreateResourceEventArg> AfterCreateResourceAction;

		/// <summary>
		/// 
		/// </summary>
		public Action<IEstResourceInstance> AfterCloneResourceAction;

		/// <summary>
		/// 
		/// </summary>
		public event Action<IEstResourceInstance> DoAfterResourceClone
		{
			add
			{
				AfterCloneResourceAction += value;
			}

			remove
			{
				AfterCloneResourceAction -= value;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		public event Action<object, TCreateResourceEventArg> DoAfterCreateResource
		{
			add
			{
				AfterCreateResourceAction += value;
			}

			remove
			{
				AfterCreateResourceAction -= value;
			}
		}

	}

	/// <summary>
	/// 
	/// </summary>
	public class CanAddResourceModel
	{
		/// <summary>
		/// 
		/// </summary>
		public bool Disabled { get; set; }

		/// <summary>
		/// 
		/// </summary>
		public string Error { get; set; }
	}

	/// <summary>
	/// 
	/// </summary>
	public static class InstanceFuncDocExpansion
    {
		/// <summary>
		/// 
		/// </summary>
        public const string Func_CreatePlant_Doc = "fn(code:? string, plantMastercode:? string) -> [+EstResourceInstance]" +
            "\r\n \r\ncontext.currentLineitem.createPlant(\"your-plant-assembly-code\");//create plant assembly with the first match plant assembly code \"your-plant-assembly-code\"" +
            "\r\n \r\ncontext.currentLineitem.createPlant(\"your-plant-assembly-code\", \"your-plantgroup-code\");//creat plant assembly \"your-plant-assembly-code\" with the plant group \"your-plantgroup-code\"" +
            "\r\n \r\ncontext.currentLineitem.createPlant(\"\", \"your-plantgroup-code\");//create all the plant assembly within the \"your-plantgroup-code\"";
    }
}
