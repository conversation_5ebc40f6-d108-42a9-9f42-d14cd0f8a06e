import { commonLocators, sidebar, app, cnt, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _procurementContractPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const ALLURE = Cypress.Allure.reporter.getInterface();
const COMMENT_TEXT = _common.generateRandomString(4);
const SUPPLIER_LEAD_TIME = Cypress._.random(0, 999);
const EXECUTION_DURATION = Cypress._.random(0, 999);
const COMMENT_TEXT_1 = _common.generateRandomString(4);

let PROCUREMENT_CONTRACT_PARAMETER: DataCells;

let CONTAINERS_CONTRACT;

let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_CALL_OFF_AGREEMENT;

ALLURE.epic("PROCUREMENT AND BPM")
ALLURE.feature("Contract")
ALLURE.story("PCM- 4.127 | Call off agreements container in Contract module")

describe("PCM- 4.127 | Call off agreements container in Contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/con-4.127-call-off-agreements-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }
            CONTAINER_COLUMNS_CALL_OFF_AGREEMENT = this.data.CONTAINER_COLUMNS.CALL_OFF_AGREEMENT
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.projectfk, CONTAINER_COLUMNS_CONTRACT.ProjectFkProjectName, CONTAINER_COLUMNS_CONTRACT.code, CONTAINER_COLUMNS_CONTRACT.businesspartnerfk], cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create new Call Off Agreements record", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CALL_OFF_AGREEMENT, app.FooterTab.CALL_OFF_AGREEMENTS, 1);
            _common.setup_gridLayout(cnt.uuid.CALL_OFF_AGREEMENT, CONTAINER_COLUMNS_CALL_OFF_AGREEMENT)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALL_OFF_AGREEMENT)
        _common.create_newRecord(cnt.uuid.CALL_OFF_AGREEMENT)
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.CALL_OFF_AGREEMENT, app.InputFields.DOMAIN_TYPE_COMMENT, COMMENT_TEXT)
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.LEAD_TIME, app.InputFields.INPUT_GROUP_CONTENT, SUPPLIER_LEAD_TIME)
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.EXECUTION_DURATION, app.InputFields.INPUT_GROUP_CONTENT, EXECUTION_DURATION)
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.EARLIEST_START, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.LATEST_START, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.INCREMENTED, 1))
        _common.set_cellCheckboxValue(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.CONTRACT_PENALTY, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.CONTRACT_PENALTY, commonLocators.CommonKeys.UNCHECK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.CALL_OFF_AGREEMENT, "CALL-OFF-AGREEMENT_01")
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALL_OFF_AGREEMENT)
        _common.create_newRecord(cnt.uuid.CALL_OFF_AGREEMENT)
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.CALL_OFF_AGREEMENT, app.InputFields.DOMAIN_TYPE_COMMENT, COMMENT_TEXT_1)
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.LEAD_TIME, app.InputFields.INPUT_GROUP_CONTENT, SUPPLIER_LEAD_TIME)
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.EXECUTION_DURATION, app.InputFields.INPUT_GROUP_CONTENT, EXECUTION_DURATION)
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.EARLIEST_START, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL))
        _common.edit_containerCell(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.LATEST_START, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(CommonLocators.CommonKeys.INCREMENTED, 1))
        _common.set_cellCheckboxValue(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.CONTRACT_PENALTY, commonLocators.CommonKeys.CHECK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.CALL_OFF_AGREEMENT, app.GridCells.CALL_OFF_AGREEMENT, "CALL-OFF-AGREEMENT")
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Delete Call Off Agreements & verify record is not present", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CALL_OFF_AGREEMENT, app.FooterTab.CALL_OFF_AGREEMENTS, 1);
            _common.setup_gridLayout(cnt.uuid.CALL_OFF_AGREEMENT, CONTAINER_COLUMNS_CALL_OFF_AGREEMENT)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALL_OFF_AGREEMENT)
        _common.search_inSubContainer(cnt.uuid.CALL_OFF_AGREEMENT, Cypress.env("CALL-OFF-AGREEMENT"))
        _common.select_rowHasValue(cnt.uuid.CALL_OFF_AGREEMENT, Cypress.env("CALL-OFF-AGREEMENT"))
        _common.waitForLoaderToDisappear()
        _common.clickOn_toolbarButton(cnt.uuid.CALL_OFF_AGREEMENT, btn.IconButtons.ICO_REC_DELETE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CALL_OFF_AGREEMENT, app.FooterTab.CALL_OFF_AGREEMENTS, 1);
            _common.setup_gridLayout(cnt.uuid.CALL_OFF_AGREEMENT, CONTAINER_COLUMNS_CALL_OFF_AGREEMENT)
            _common.clear_subContainerFilter(cnt.uuid.CALL_OFF_AGREEMENT)
        });
        
        _validate.verify_recordNotPresentInContainer(cnt.uuid.CALL_OFF_AGREEMENT, Cypress.env("CALL-OFF-AGREEMENT"))
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CALL_OFF_AGREEMENT, app.FooterTab.CALL_OFF_AGREEMENTS, 1);
            _common.setup_gridLayout(cnt.uuid.CALL_OFF_AGREEMENT, CONTAINER_COLUMNS_CALL_OFF_AGREEMENT)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALL_OFF_AGREEMENT)
        _common.search_inSubContainer(cnt.uuid.CALL_OFF_AGREEMENT, Cypress.env("CALL-OFF-AGREEMENT_01"))
        _common.select_rowHasValue(cnt.uuid.CALL_OFF_AGREEMENT, Cypress.env("CALL-OFF-AGREEMENT_01"))
        _common.waitForLoaderToDisappear()
    });
});