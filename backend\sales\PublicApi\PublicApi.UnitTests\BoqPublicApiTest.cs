using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.Boq.Project.BusinessComponents;
using RIB.Visual.Boq.PublicApi.ServiceFacade.WebApi;
using RIB.Visual.Boq.PublicApi.ServiceFacade.WebApi.Controllers;
using RIB.Visual.Boq.PublicApi.UnitTests;
using RIB.Visual.Platform.UnitTests.Common;
using RIB.Visual.Sales.Bid.BusinessComponents;
using RIB.Visual.Sales.Contract.BusinessComponents;
using Xunit;
using Xunit.Abstractions;
using BoqMainModelBuilder = RIB.Visual.Boq.Main.BusinessComponents.ModelBuilder;
using BoqWicModelBuilder = RIB.Visual.Boq.Wic.BusinessComponents.ModelBuilder;
using ProcurementModelBuilder = RIB.Visual.Procurement.Common.BusinessComponents.ModelBuilder;
using ProcurementPackageModelBuilder = RIB.Visual.Procurement.Package.BusinessComponents.ModelBuilder;
using BoqPrjModelBuilder = RIB.Visual.Boq.Project.BusinessComponents.ModelBuilder;
using BidModelBuilder = RIB.Visual.Sales.Bid.BusinessComponents.ModelBuilder;
using OrdModelBuilder = RIB.Visual.Sales.Contract.BusinessComponents.ModelBuilder;
using DbContext = RIB.Visual.Platform.BusinessComponents.DbContext;
using RIB.Visual.Procurement.PublicApi.ServiceFacade.WebApi;
using RIB.Visual.Procurement.Contract.BusinessComponents;
using RIB.Visual.Procurement.Common.BusinessComponents;
using PrcCommonModelBuilder = RIB.Visual.Procurement.Common.BusinessComponents.ModelBuilder;
using PrcContractModelBuilder = RIB.Visual.Procurement.Contract.BusinessComponents.ModelBuilder;
using System.IO;
using Newtonsoft.Json;
using static RIB.Visual.Boq.Main.Core.BoqConstants;
using RIB.Visual.Boq.Wic.BusinessComponents;
using RIB.Visual.Basics.Api.ServiceFacade.WebApi;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Common.BusinessComponents;
using System.Security.Cryptography.Xml;
using FluentAssertions.Equivalency;
using RIB.Visual.Procurement.Package.BusinessComponents;
using FluentAssertions;
using RIB.Visual.Platform.Common;
using System.Collections;
using RIB.Visual.Boq.Main.ServiceFacade.WebApi;
using RIB.Visual.Project.PublicApi.ServiceFacade.WebApi;
using RIB.Visual.Platform.Core;
using RIB.Visual.Boq.Main.Core;

namespace RIB.Visual.Sales.PublicApi.UnitTests
{
	[AutoTest(AutoTestBranch.All)]
	public class BoqPublicApiTest : BoqTestBase
	{
		private const Int32 _projectId = 753; // 999-BOQ-PublcAPI  -  Unit Test BOQ PublicAPI
		private string _pathToUnitTestFolder = Path.Combine(Directory.GetParent(Environment.CurrentDirectory).Parent.Parent.FullName, "Testdata");

		public BoqPublicApiTest(ITestOutputHelper output, WebApiHelper webApi) : base(output, webApi)
		{
		}

		[Fact]
		public void PutProjectBoq()
		{
			PutProjectBoqRequest request;
			HttpResponseMessage response;
			String boqReference = "PutProjectBoq";
			String boqOutlineSpecification = boqReference + " AutoGenerateBoq";
			var validSpecification = "<p>GAEB text</p>";

			OutputBoqHeaders(boqReference, "before deletion", boqOutlineSpecification: boqOutlineSpecification);

			ExecuteTest(() =>
			{
				using (var dbContext = new DbContext(BoqPrjModelBuilder.DbModel))
				{
					TestHelper.DeleteBoqs<ProjectBoqEntity>(BoqPrjModelBuilder.DbModel, pr => pr.PrjProjectFk == _projectId, boqReference, boqOutlineSpecification: boqOutlineSpecification);
				}

				request = new PutProjectBoqRequest
				{
					ProjectNumber = "999-BOQ-PublcAPI",
					ImportMode = (int)ImportModes.BoqAutoGenerate,
					BoqReference = boqReference,
					BoqOutlineSpecification = boqOutlineSpecification,
					BoqItems = new[]
					{ new ProjectBoqItemPublicApiV1_0Dto(){ Reference = "10", BoqLineType = 1 },
					  new ProjectBoqItemPublicApiV1_0Dto() { Reference = "20", BoqLineType = 1 },
					  new ProjectBoqItemPublicApiV1_0Dto
					  {
							  Reference = "20.10.",
							  ParentReference = "20",
							  BoqLineType = 2 
					  },
					  new ProjectBoqItemPublicApiV1_0Dto
					  {
							  Reference = "20.10.10.",
							  ParentReference = "20.10.",
							  BoqLineType = 0,
							  Specification = validSpecification,
							  Uom = "KM"
					  }
					}
				};

				response = new BoqPublicApiProjectController().PutProjectBoqs(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
			});

			OutputBoqHeaders(boqReference, "after creation", false, boqOutlineSpecification: boqOutlineSpecification);
		}

		[Fact]
		public void PutSalesBidBoq()
		{
			PutBidBoqRequest request;
			HttpResponseMessage response;
			String bidHeaderCode = "BOQ-PublcAPI-919";
			String boqReference = "PutSalesBidBoq";

			OutputBoqHeaders(boqReference, "before deletion");

			ExecuteTest(() =>
			{
				using (var dbContext = new DbContext(BidModelBuilder.DbModel))
				{
					var bidHeader = dbContext.Entities<BidHeaderEntity>().First(bh => bh.Code == bidHeaderCode);
					TestHelper.DeleteBoqs<BidBoqEntity>(BidModelBuilder.DbModel, bd => bd.BidHeaderFk == bidHeader.Id, boqReference);
					TestHelper.DeleteBoqs<ProjectBoqEntity>(BoqPrjModelBuilder.DbModel, pr => pr.PrjProjectFk == _projectId, boqReference);
				}

				request = new PutBidBoqRequest
				{
					ImportMode = (int)ImportModes.BoqAutoGenerate,
					SalesBidCode = bidHeaderCode,
					BoqReference = boqReference,
					BoqOutlineSpecification = boqReference + " Brief",
					BoqItems = new[] { new ProjectBoqItemPublicApiV1_0Dto() { Reference = "10", BoqLineType = 1 }, new ProjectBoqItemPublicApiV1_0Dto() { Reference = "20", BoqLineType = 1 } }
				};
				response = new BoqPublicApiOrderController().PutSalesBidBoq(request);

				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
			});

			OutputBoqHeaders(boqReference, "after creation", true);
		}

		[Fact]
		public void PutSalesOrdBoq()
		{
			PutOrderBoqRequest request;
			HttpResponseMessage response;
			String ordHeaderCode = "BOQ-PublcAPI-919";
			String boqReference = "PutSalesOrdBoq";

			OutputBoqHeaders(boqReference, "before deletion");

			ExecuteTest(() =>
			{
				using (var dbContext = new DbContext(OrdModelBuilder.DbModel))
				{
					var ordHeader = dbContext.Entities<OrdHeaderEntity>().First(oh => oh.Code == ordHeaderCode);
					TestHelper.DeleteBoqs<OrdBoqEntity>(OrdModelBuilder.DbModel, bd => bd.OrdHeaderFk == ordHeader.Id, boqReference);
					TestHelper.DeleteBoqs<ProjectBoqEntity>(BoqPrjModelBuilder.DbModel, pr => pr.PrjProjectFk == _projectId, boqReference);
				}

				request = new PutOrderBoqRequest
				{
					ImportMode = (int)ImportModes.BoqAutoGenerate,
					SalesOrderCode = ordHeaderCode,
					BoqReference = boqReference,
					BoqOutlineSpecification = boqReference + " Brief",
					BoqItems = new[] { new ProjectBoqItemPublicApiV1_0Dto() { Reference = "10", BoqLineType = 1 } }
				};
				response = new BoqPublicApiOrderController().PutSalesOrdBoq(request);

				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
			});

			OutputBoqHeaders(boqReference, "after creation", true , null);
		}

		private void OutputBoqHeaders(String boqReference, String outputExtension, Boolean doTest = false, String boqOutlineSpecification = null)
		{
			List<Int32> boqHeaderIds;
			List<BoqHeaderEntity> boqHeaders;
			String outputText = String.Empty;

			using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
			{
				if(boqOutlineSpecification != null)
				{
					boqHeaderIds = dbContext.Entities<BoqItemEntity>().Where(bi => bi.BriefInfo.Description == boqOutlineSpecification).Select(bi => bi.BoqHeaderFk).Distinct().ToList();
				}
				else
				{
					boqHeaderIds = dbContext.Entities<BoqItemEntity>().Where(bi => bi.Reference == boqReference).Select(bi => bi.BoqHeaderFk).Distinct().ToList();
				}
				
				boqHeaders = dbContext.Entities<BoqHeaderEntity>().Where(bh => boqHeaderIds.Contains(bh.Id)).ToList();
			}

			foreach (var boqHeader in boqHeaders)
			{
				outputText += String.Format("{0}:{1} - ", boqHeader.Id, boqHeader.BoqHeaderFk.HasValue ? boqHeader.BoqHeaderFk : "null");
			}
			Output.WriteLine(String.Format("{0}BOQ_HEADER.ID's {1}", outputText, outputExtension));

			if (doTest)
			{
				Assert.True(boqHeaders.OrderBy(bh => bh.Id).Last().BoqHeaderFk.HasValue, "A base BoQ is missing!");

				using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
				{
					var boqHeaderIdBase = boqHeaders[1].Id;
					var boqHeaderIdVrsn = boqHeaders[0].Id;
					var countOfVrsnBoqItems = dbContext.Entities<BoqItemEntity>().Count(bi => bi.BoqHeaderFk == boqHeaderIdVrsn);
					var countOfBaseBoqItems = dbContext.Entities<BoqItemEntity>().Count(bi => bi.BoqHeaderFk == boqHeaderIdBase);
					try
					{
						if (countOfVrsnBoqItems == countOfBaseBoqItems) {
							Assert.True(countOfVrsnBoqItems == countOfBaseBoqItems, String.Format("countOfVersionBoqItems={0} countOfBaseBoqItems={1}", countOfVrsnBoqItems, countOfBaseBoqItems));
						}
					}
					catch (Exception e)
					{
						Assert.True(countOfVrsnBoqItems != countOfBaseBoqItems, String.Format("countOfVersionBoqItems={0} countOfBaseBoqItems={1}", countOfVrsnBoqItems, countOfBaseBoqItems));
						Output.WriteLine("countOfVrsnBoqItems != countOfBaseBoqItems", e);
					}
					
				}
			}
		}

		[Fact]
		public void PutProcurementContractBoq()
		{
			PutBoqHeaderRequest request = null;
			HttpResponseMessage response;
			string filename = Path.Combine(_pathToUnitTestFolder, "ImportPrcContractBoq.json");
			if (File.Exists(filename))
			{
				var json = File.ReadAllText(filename);
				request = JsonConvert.DeserializeObject<PutBoqHeaderRequest>(json);
			}

			String contractHeaderCode = "con-7709";
			String boqReference = "PutProcurementContractBoqTestV1";

			OutputBoqHeaders(boqReference, "before deletion");

			ExecuteTest(() =>
			{
				using (var dbContext = new DbContext(PrcContractModelBuilder.DbModel))
				{
					var contractHeader = dbContext.Entities<ConHeaderEntity>().Single(ch => ch.ProjectFk == _projectId && ch.Code == contractHeaderCode);
					TestHelper.DeleteBoqs<PrcBoqEntity>(PrcCommonModelBuilder.DbModel, pr => pr.PrcHeaderFk == contractHeader.PrcHeaderFk, boqReference);
				}

				response = new ProcurementPublicApiBoqController().ImportBoqs(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
			});

			OutputBoqHeaders(boqReference, "after creation");
		}

		//PutBoqItemFlag --> BoqItemFlagEntity --> BOQ_ITEM_FLAG
		[Fact]
		public void PutBoqItemFlag()
		{
			PutBoqItemFlagV1_0Request request = null;
			HttpResponseMessage response;
			String code= "UNIT-TEST-PUTBOQITEM";

			this.DeleteBoqItemFlags(code);

			string filename = Path.Combine(_pathToUnitTestFolder, "PutBoqItemFlag.json");
			if (File.Exists(filename))
			{
				var json = File.ReadAllText(filename);
				request = JsonConvert.DeserializeObject<PutBoqItemFlagV1_0Request>(json);
			}

			ExecuteTest(() =>
			{
				response = new BoqPublicApiWicController().PutBoqItemFlag(request);
				using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
				{
					var boqItemFlag = dbContext.Entities<BoqItemFlagEntity>().FirstOrDefault(boqflg => boqflg.Code == code);
					if (boqItemFlag != null)
					{
						Output.WriteLine(JsonConvert.SerializeObject(boqItemFlag, Formatting.Indented));
					}
				}
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
			});
		}

		private void DeleteBoqItemFlags(String code)
		{
			using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
			{
				var boqItemFlag = dbContext.Entities<BoqItemFlagEntity>().FirstOrDefault(itmFlg => itmFlg.Code == code);
				if (boqItemFlag != null)
				{
					dbContext.Delete(boqItemFlag);
				}
			}			
		}
		[Fact]
		public void PutWicBoqItems()
		{
			PutWICBoQItemsRequest request = null;
			HttpResponseMessage response;

			string filename = Path.Combine(_pathToUnitTestFolder, "PutWicBoqItemV1.json");
			if (File.Exists(filename))
			{
				var json = File.ReadAllText(filename);
				request = JsonConvert.DeserializeObject<PutWICBoQItemsRequest>(json);
			}
			var wicBoqItems = request.WicBoqItems.ToList();
			ExecuteTest(() =>
			{
				response = new BoqPublicApiWicController().PutWICBoqItems(request);

				using (var dbContext = new DbContext(BoqWicModelBuilder.DbModel))
				{
					IEnumerable<BoqItemEntity> boqRootItems = null;
					BoqItemLogic boqItemLogic = new BoqItemLogic();

					var boqHeaderIds = this.getWicBoqHeaderIdsByCode(wicBoqItems[0].WicCatalogCode);
					IEnumerable<BoqItemEntity> boqItems = boqItemLogic.GetSearchList(e => boqHeaderIds.Contains(e.BoqHeaderFk), boqHeaderIds.Any());
					boqRootItems = boqItems.Where(e => e.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root).ToList();
					var boqRoot = boqRootItems?.FirstOrDefault(e => e.Reference == wicBoqItems[0].RootReference && boqHeaderIds.Contains(e.BoqHeaderFk));
					
					var wicBoqItem = boqItems.FirstOrDefault(e => e.BoqHeaderFk == boqRoot.BoqHeaderFk && e.Reference == wicBoqItems[0].Reference);
					if (wicBoqItem != null)
					{
						var settings = new JsonSerializerSettings
						{
							ReferenceLoopHandling = ReferenceLoopHandling.Ignore
						};
						Output.WriteLine(JsonConvert.SerializeObject(wicBoqItem, Formatting.Indented, settings));
					}
				}
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
			});
		}

		public IEnumerable<int> getWicBoqHeaderIdsByCode(String WicCatalogCode)
		{
			using (var dbContext = new DbContext(BoqWicModelBuilder.DbModel))
			{
				BoqItemLogic _boqItemLogic = new BoqItemLogic();
				WicGroupEntity wicGroup = dbContext.Entities<WicGroupEntity>().FirstOrDefault(e => e.Code == WicCatalogCode);
				List<WicBoqEntity> wicBoqs = dbContext.Entities<WicBoqEntity>().Where((WicBoqEntity e) => e.WicGroupFk == wicGroup.Id).ToList();
				var boqHeaderIds = wicBoqs.CollectIds(e => e.BoqHeaderFk);
				return boqHeaderIds;
			}
		}

		[Fact]
		public void PutWicBoqItemV20()
		{
			PutWicBoqItemsV2_0Request request = null;
			HttpResponseMessage response;

			string filename = Path.Combine(_pathToUnitTestFolder, "PutWicBoqItem.json");
			if (File.Exists(filename))
			{
				var json = File.ReadAllText(filename);
				request = JsonConvert.DeserializeObject<PutWicBoqItemsV2_0Request>(json);
			}
			var wicBoqItems = request.WicBoqItems.ToList();

			ExecuteTest(() =>
			{
				response = new BoqPublicApiWicController().PutWicBoqItemV20(request);
				using (var dbContext = new DbContext(BoqWicModelBuilder.DbModel))
				{
					IEnumerable<BoqItemEntity> boqRootItems = null;
					BoqItemLogic boqItemLogic = new BoqItemLogic();

					var boqHeaderIds = this.getWicBoqHeaderIdsByCode(wicBoqItems[0].WicCatalogCode);
					IEnumerable<BoqItemEntity> boqItems = boqItemLogic.GetSearchList(e => boqHeaderIds.Contains(e.BoqHeaderFk), boqHeaderIds.Any());
					boqRootItems = boqItems.Where(e => e.BoqLineTypeFk == (int)BoqConstants.EBoqLineType.Root).ToList();
					var boqRoot = boqRootItems?.FirstOrDefault(e => e.Reference == wicBoqItems[0].RootReference && boqHeaderIds.Contains(e.BoqHeaderFk));

					var wicBoqItem = boqItems.FirstOrDefault(e => e.BoqHeaderFk == boqRoot.BoqHeaderFk && e.Reference == wicBoqItems[0].Reference);
					if (wicBoqItem != null)
					{
						var settings = new JsonSerializerSettings
						{
							ReferenceLoopHandling = ReferenceLoopHandling.Ignore
						};
						Output.WriteLine(JsonConvert.SerializeObject(wicBoqItem, Formatting.Indented, settings));
					}
				}
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
			});
		}


		//TODO-BOQ: Issue with creating record of Assembly Assignment so commented the code.
		//[Fact]
  //      public void PutWicBoqPositionAssemblies()
  //      {
  //          PutWicBoqPositionAssemblyRequest request = null;
  //          HttpResponseMessage response;
		//		String boqReference = "PutWicBoqItemAssemblyTest";
		//	   String wicCatalogCode = "WICPUBLICAPI";
		//		int boqHeaderId = this.GetBoqHeaderIdByCode(wicCatalogCode);
		
		//	//string filename = Path.Combine(_pathToUnitTestFolder, "WicBoqPositionAssemblies.json");
		//	//if (File.Exists(filename))
		//	//{
		//	//	var json = File.ReadAllText(filename);
		//	//	request = JsonConvert.DeserializeObject<PutWicBoqPositionAssemblyRequest>(json);
		//	//}
			
		//	ExecuteTest(() =>
		//		{
		//			var Assemblies = new WicBoqPositionAssemblyPublicApiV1_0Dto[10];
		//			Assemblies[0] = new WicBoqPositionAssemblyPublicApiV1_0Dto { AssemblyCatalogCode = "000010", AssemblyCode = "000010.000610" };
		//			request = new PutWicBoqPositionAssemblyRequest
		//			{
		//				BoqPositions = new[] {
		//					new WicBoqPositionPublicApiV1_0Dto { Reference = "PutWicBoqItemAssembly",RootReference = boqReference,WicCatalogCode = wicCatalogCode , Assemblies = Assemblies }
		//				}

		//			};

		//			response = new BoqPublicApiWicController().PutWicBoqPositionAssemblies(request);
		//			using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
		//			{

		//				var boqWic2AssemblyEntity = dbContext.Entities<BoqWic2assemblyEntity>().FirstOrDefault(obj => obj.BoqHeaderFk == boqHeaderId);
		//				if (boqWic2AssemblyEntity != null)
		//				{
		//					Output.WriteLine(JsonConvert.SerializeObject(boqWic2AssemblyEntity, Formatting.Indented));
		//				}
		//			}

		//			Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
		//		});

		//}

		private int GetBoqHeaderIdByCode(String code)
		{
			WicGroupEntity wicGroup;
			int wicGroupFk;
			using (var dbContext = new DbContext(BoqWicModelBuilder.DbModel))
			{
				wicGroup = dbContext.Entities<WicGroupEntity>().FirstOrDefault(o => o.Code == code);
				wicGroupFk = (Int32)wicGroup.WicGroupFk;
				return dbContext.Entities<WicBoqEntity>().FirstOrDefault(o => o.WicGroupFk == wicGroupFk).BoqHeaderFk;
			}
		}

		[Fact]
		public void PutBoqItemTextConfiguration()
		{
			PutBoqItemTextConfigurationRequest request = null;
			HttpResponseMessage response;
			String reference = "PutBoqItemTextConfiguration";
			String code = "PublicAPI";

			int boqHeaderId = this.GetBoqHeaderIdByReferenceAndCode(reference, code);
			this.DeleteBoqItemTextConfiguration(boqHeaderId);

			string filename = Path.Combine(_pathToUnitTestFolder, "PutBoqItemTextConfiguration.json");
			if (File.Exists(filename))
			{
				var json = File.ReadAllText(filename);
				request = JsonConvert.DeserializeObject<PutBoqItemTextConfigurationRequest>(json);
			}

			ExecuteTest(() =>
			{
				response = new BoqPublicApiWicController().PutBoqItemTextConfiguration(request);
				using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
				{
					var boqTextConfig = dbContext.Entities<BoqTextConfigurationEntity>().FirstOrDefault(oh => oh.BoqHeaderFk == boqHeaderId);
					if (boqTextConfig != null)
					{
						Output.WriteLine(JsonConvert.SerializeObject(boqTextConfig, Formatting.Indented));
					}
				}
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);				
			});
		}
		private int GetBoqHeaderIdByReferenceAndCode(String reference, String code)
		{
			WicGroupEntity wicGroup;	
			using (var dbContext = new DbContext(BoqWicModelBuilder.DbModel))
			{
				wicGroup = dbContext.Entities<WicGroupEntity>().FirstOrDefault(o => o.Code == code);
				return Injector.Get<IWicBoqLogic>().GetBoqHeaderIdByWicGroupIdAndReference((Int32)wicGroup.Id, reference);
			}
		}
		private void DeleteBoqItemTextConfiguration(int boqHeaderId)
		{
			using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
			{
				var record = dbContext.Entities<BoqTextConfigurationEntity>().FirstOrDefault(oh => oh.BoqHeaderFk == boqHeaderId);
				if (record != null)
				{
					dbContext.Delete(record);
				}
			}
		}

		[Fact]
		public void PutWicBoqs()
		{
			PutWICBoQsRequest request = null;
			HttpResponseMessage response;
			String boqReference = "PutWicBoqs";
			String boqOutlineSpecification = boqReference + "AutoGenerateBoq";
	
			int boqHeaderId = this.GetBoqHeaderIdByReference(boqReference);
			string filename = Path.Combine(_pathToUnitTestFolder, "PutWicBoqs.json");
			if (File.Exists(filename))
			{
				var json = File.ReadAllText(filename);
				request = JsonConvert.DeserializeObject<PutWICBoQsRequest>(json);
			}
			OutputBoqHeaders(boqReference, "before deletion");
			
			ExecuteTest(() =>
			{
				using (var dbContext = new DbContext(BoqWicModelBuilder.DbModel))
				{
					TestHelper.DeleteBoqs<WicBoqEntity>(BoqWicModelBuilder.DbModel, wc => wc.BoqHeaderFk == boqHeaderId, boqReference, boqOutlineSpecification: boqOutlineSpecification);
				}			
				response = new BoqPublicApiWicController().PutWICBoQs(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
			});
			OutputBoqHeaders(boqReference, "after deletion");
		}

		[Fact]
		public void GetProjectBoqList()
		{
			GetProjectBoqListV1_0Request request = null;
			HttpResponseMessage response;
			int[] parentIds = { 1, 3, 5 };
			ExecuteTest(() =>
			{
				request = new GetProjectBoqListV1_0Request
				{
					ProjectNo = "999-BOQ-PublcAPI",
					ProjectId= _projectId,
				};

				response = new BoqPublicApiProjectController().GetProjectBoqList(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}


		[Fact]
		public void GetProjectLocation()
		{
			ProjectPublicApiFilterRequest request = null;
			HttpResponseMessage response;

			ExecuteTest(() =>
			{
				request = new ProjectPublicApiFilterRequest
				{
					ProjectIndex = 0,
					ProjectNo = "999-BOQ-PublcAPI",
					CompanyCode = "919"

				};

				response = new ProjectMainLocationPublicApiController().Load(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}

		[Fact]
		public void GaebExportOptions()
		{
			GetGaebBoqExportOptionsPublicApiV1_0Request request = null;
			HttpResponseMessage response;
			string filename = Path.Combine(_pathToUnitTestFolder, "GaebExportOptions.json");
			if (File.Exists(filename))
			{
				var json = File.ReadAllText(filename);
				request = JsonConvert.DeserializeObject<GetGaebBoqExportOptionsPublicApiV1_0Request>(json);
			}

			ExecuteTest(() =>
			{
				response = new ProcurementPublicApiBoqController().GaebExportOptions(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}

		[Fact]
		public void ImportSplitQuantities()
		{
			PutProjectSplitQuantitiesV1_0Request request = null;
			HttpResponseMessage response;
			String boqReference = "ImportSplitQuantity";   
			int boqHeaderId = this.GetBoqHeaderIdByReference(boqReference);
			ExecuteTest(() =>
			{
				request = new PutProjectSplitQuantitiesV1_0Request
				{
					ProjectNo = "999-BOQ-PublcAPI",
					BoqHeaderId = boqHeaderId, 
					BoqHeaderCode = boqReference,
					SplitQuantities = new[] { new Boq.PublicApi.ServiceFacade.WebApi.PutSplitQuantity() {	BoqReference = boqReference } }
				};

				response = new BoqPublicApiProjectSplitQuantityController().PutOrUpdateSplitQuantities(request);
				using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
				{
					var splitQuantity = dbContext.Entities<BoqSplitQuantityEntity>().FirstOrDefault(obj => obj.BoqHeaderFk == boqHeaderId);
					if (splitQuantity != null)
					{
						Output.WriteLine(JsonConvert.SerializeObject(splitQuantity, Formatting.Indented));
					}
				}
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
			});
		}

		[Fact]
		public void ImportPrcBoq()
		{
			PutBoqHeaderRequest request = null;
			HttpResponseMessage response;
			String boqReference = "PAC-6237";
			String code = "999-BOQ-WebAPI";
			ExecuteTest(() =>
			{
				request = new PutBoqHeaderRequest
				{
					ParentCode = code,
					BoqHeaders = new List<GeneralBoqHeaderApiDto>() { new GeneralBoqHeaderApiDto() { Code = code, Gaebtype = "x84", CreationType = 0, BoqHeader = boqReference, Type= "Standard LV Struktur GAEB" } }
				};

				response = new ProcurementPublicApiBoqController().ImportBoqs(request);
				using (var dbContext = new DbContext(ProcurementModelBuilder.DbModel))
				{
					var prcBoq = dbContext.Entities<PrcBoqEntity>().FirstOrDefault(obj => obj.Code == code);
					if (prcBoq != null)
					{
						Output.WriteLine(JsonConvert.SerializeObject(prcBoq, Formatting.Indented));
					}
				}
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
	
	
			});
		}

		[Fact]
		public void ImportPrcSplitQuantity()
		{
			Procurement.PublicApi.ServiceFacade.WebApi.PutSplitQuantitiesV1_0Request request = null;
			HttpResponseMessage response;
			String boqReference = "PAC-0517";
			String code = "PAC-0517";
			int boqHeaderId = this.GetPrcBoqHeaderIdByCode(code);
		
			ExecuteTest(() =>
			{
				request = new Procurement.PublicApi.ServiceFacade.WebApi.PutSplitQuantitiesV1_0Request
				{
					SectionType = (ImportGaebSectionType)1,
					ParentCode = code,
					BoqHeaderCode = boqReference,
					BoqHeaderId = boqHeaderId,
					SplitQuantities = new[] { new Procurement.PublicApi.ServiceFacade.WebApi.PutSplitQuantity { BoqReference = boqReference } }

				};

				response = new ProcurementPublicApiBoqController().PutOrUpdateSplitQuantities(request);

				using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
				{
					var splitQuantity = dbContext.Entities<BoqSplitQuantityEntity>().FirstOrDefault(prcSplitQuantity => prcSplitQuantity.BoqHeaderFk== boqHeaderId);
					if (splitQuantity != null)
					{
						Output.WriteLine(JsonConvert.SerializeObject(splitQuantity, Formatting.Indented));
					}
				}

				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
			});
		}
		private int GetPrcBoqHeaderIdByCode(String code)
		{
			using (var dbContext = new DbContext(ProcurementModelBuilder.DbModel))
			{
				var packageId = 0;
				using (var dbCntx = new DbContext(ProcurementPackageModelBuilder.DbModel)) {
					List<PrcPackageEntity> prcPackageEntity;
					prcPackageEntity = dbCntx.Entities<PrcPackageEntity>().ToList();
					packageId  = prcPackageEntity.FirstOrDefault(a => a.Code == code).Id;
				}
				List<PrcBoqEntity> prcBoqEntity;
				prcBoqEntity = dbContext.Entities<PrcBoqEntity>().ToList();
				var entity = prcBoqEntity.FirstOrDefault(a => a.PackageFk == packageId);
				if (entity == null)
				{
					throw new InvalidOperationException($"No entity found with code {code}");
				}
				return (int)entity.BoqHeaderFk;
			}
		}
		private int GetBoqHeaderIdByReference(String reference)
		{
			List<int?> boqList;
			using (var dbContext = new DbContext(BoqPrjModelBuilder.DbModel))
			{
				boqList = dbContext.Entities<ProjectBoqEntity>().Where(o => o.PrjProjectFk == _projectId).Select(o => o.BoqHeaderFk).ToList();
			}
			using (var dbContext = new DbContext(BoqMainModelBuilder.DbModel))
			{
				return dbContext.Entities<BoqItemEntity>().Where(o => boqList.Contains(o.BoqHeaderFk) && o.BoqItemFk == null && o.Reference == reference).Select(o => o.BoqHeaderFk).FirstOrDefault();
			}
		}


		//----------------------------------WIC PUBLIC API--------------------------------------------------------------//

		//GetWicBoq -> WicBoqEntity -> BOQ_WIC_CAT_BOQ
		[Fact]
		public void GetWicBoq()
		{
			GetWicBoqV1_0Request request = null;
			HttpResponseMessage response;
			String WicCatalogCode = "PublicAPI";

			ExecuteTest(() =>
			{
			
				request = new GetWicBoqV1_0Request
				{

					WicCatalogCode = WicCatalogCode
				};
				response = new BoqPublicApiWicController().GetWicBoq(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}


		[Fact]
		public void GetBoqItemFlag()
		{
			PublicApiGetDataRequest request = null;
			HttpResponseMessage response;
			bool getAll = true;
			List<int> parentIds = null;


			ExecuteTest(() =>
			{
				request = new PublicApiGetDataRequest
				{
					GetAll = getAll,
					ParentIds = parentIds
					
				};
				response = new BoqPublicApiWicController().GetBoqItemFlag(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}

		//GetBoqPositionAssemblyV20 ->  BoqItemEntity -> BOQ_ITEM
		[Fact]
		public void GetBoqPositionAssemblyV20()
		{
			GetWicBoqPositionAssemblyV2_0Request request = null;
			HttpResponseMessage response;
			String wicCatalogCode = "PublicAPI";
			String boqCode = "GetBoqPositionAssemblyV20";
		
			ExecuteTest(() =>
			{
				request = new GetWicBoqPositionAssemblyV2_0Request
				{
					WicCatalogCode = wicCatalogCode,
					BoQCode = boqCode
				};

				response = new BoqPublicApiWicController().GetBoqPositionAssemblyV20(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}


		//GetWicCatalog ->  WicGroupEntity -> BOQ_WIC_CAT
		[Fact]
		public void GetWicCatalog()
		{
			PublicApiGetDataRequest request = null;
			HttpResponseMessage response;
			Boolean getAll = true;
			IEnumerable<int> parentIds = null;

			ExecuteTest(() =>
			{
				request = new PublicApiGetDataRequest
				{
					GetAll = getAll,
					ParentIds = parentIds
				};

				response = new BoqPublicApiWicController().GetWicCatalog(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}


		//GetBoqItemTextConfiguration	-> BoqTextConfigurationEntity -> BOQ_TEXT_CONFIGURATION
		[Fact]
		public void GetBoqItemTextConfiguration()
		{
			GetBoqItemTextConfigurationV1_0Request request = null;
			HttpResponseMessage response;
			string wicCatalogCode = "PublicAPI";
			String boqCode = "GetBoqItemTextConfiguration";
			int? configType = 2;  //Values for CharacteristicType = 1, WorkContentType = 2

			ExecuteTest(() =>
			{
				request = new GetBoqItemTextConfigurationV1_0Request
				{
					WicCatalogCode = wicCatalogCode,
					BoQCode = boqCode,
					ConfigType = configType
				};

				response = new BoqPublicApiWicController().GetBoqItemTextConfiguration(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}

		//GetWicBoqItem(V1) -> BoqItemEntity -> BOQ_ITEM

		[Fact]
		public void GetWicBoqItemV1()
		{
			GetWicBoqItemV1_0Request request = null;
			HttpResponseMessage response;
			string wicCatalogCode = "PublicAPI";
			String boqCode = "GetWicBoqItemV1";
		
			ExecuteTest(() =>
			{
				request = new GetWicBoqItemV1_0Request
				{
					WicCatalogCode = wicCatalogCode,
					BoQCode = boqCode
				};

				response = new BoqPublicApiWicController().GetWicBoqItem(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}

		//GetWicBoqItem(V20) -> BoqItemEntity -> BOQ_ITEM
		[Fact]
		public void GetWicBoqItemV20()
		{
			GetWicBoqItemV2_0Request request = null;
			HttpResponseMessage response;
			string wicCatalogCode = "PublicAPI";
			String boqCode = "GetWicBoqItemV20";

			ExecuteTest(() =>
			{
				request = new GetWicBoqItemV2_0Request
				{
					WicCatalogCode = wicCatalogCode,
					BoQCode = boqCode
				};

				response = new BoqPublicApiWicController().GetWicBoqItemV20(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}

		//GetProjectBoqItem -> BoqItemEntity -> BOQ_ITEM
		[Fact]
		public void GetProjectBoqItem()
		{
			GetProjectBoqItemV1_0Request request = null;
			HttpResponseMessage response;
			int[] parentIds = { 1, 3, 5 };
			ExecuteTest(() =>
			{
				request = new GetProjectBoqItemV1_0Request
				{
					ProjectNumber = "999-BOQ-PublcAPI",
					BoQCode = "GetProjectBoqItem",
					GetAll = false,
					ParentIds = (IEnumerable<Int32>)parentIds
			};

				response = new BoqPublicApiProjectController().GetProjectBoqItem(request);
				Assert.True(response.IsSuccessStatusCode, response.Content.ReadAsStringAsync().Result);
				var obj = JsonConvert.DeserializeObject(response.Content.ReadAsStringAsync().Result);
				Output.WriteLine(JsonConvert.SerializeObject(obj, Formatting.Indented));
			});
		}
	}
}