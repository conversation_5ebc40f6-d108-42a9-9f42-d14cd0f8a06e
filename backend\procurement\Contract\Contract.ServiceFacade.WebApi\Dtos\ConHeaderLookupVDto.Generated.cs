﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Procurement.Contract.BusinessComponents;

namespace RIB.Visual.Procurement.Contract.ServiceFacade.WebApi
{


    /// <summary>
    /// There are no comments for ConHeaderLookupVEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("CON_HEADER_LOOKUP_V")]
    public partial class ConHeaderLookupVDto : RIB.Visual.Platform.Core.ITypedDto<ConHeaderLookupVEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class ConHeaderLookupVDto.
        /// </summary>
        public ConHeaderLookupVDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class ConHeaderLookupVDto.
        /// </summary>
        /// <param name="entity">the instance of class ConHeaderLookupVEntity</param>
        public ConHeaderLookupVDto(ConHeaderLookupVEntity entity)
        {
            Id = entity.Id;
            ControllingUnitFk = entity.ControllingUnitFk;
            PrcPackageFk = entity.PrcPackageFk;
            PrcPackage2HeaderFk = entity.PrcPackage2HeaderFk;
            PrcStructureFk = entity.PrcStructureFk;
            DateOrdered = entity.DateOrdered;
            BusinessPartner2Fk = entity.BusinessPartner2Fk;
            Code = entity.Code;
            PrcConfigurationFk = entity.PrcConfigurationFk;
            PaymentTermFiFk = entity.PaymentTermFiFk;
            ExternalCode = entity.ExternalCode;
            PaymentTermPaFk = entity.PaymentTermPaFk;
            TaxCodeFk = entity.TaxCodeFk;
            ClerkPrcFk = entity.ClerkPrcFk;
            ClerkReqFk = entity.ClerkReqFk;
            Description = entity.Description;
            BpName1 = entity.BpName1;
            PrcHeaderId = entity.PrcHeaderId;
            StatusIsVirtual = entity.StatusIsVirtual;
            StatusIsReported = entity.StatusIsReported;
            SearchPattern = entity.SearchPattern;
            CurrencyFk = entity.CurrencyFk;
            MdcBillingSchemaFk = entity.MdcBillingSchemaFk;
            ProjectChangeFk = entity.ProjectChangeFk;
            Exchangerate = entity.Exchangerate;
            BpdContactFk = entity.BpdContactFk;
            BpdSubsidiaryFk = entity.BpdSubsidiaryFk;
            BpdSupplierFk = entity.BpdSupplierFk;
            BpdVatGroupFk = entity.BpdVatGroupFk;
            StatusIsLive = entity.StatusIsLive;
            StatusIsCanceled = entity.StatusIsCanceled;
            StatusIsDelivered = entity.StatusIsDelivered;
            StatusIsReadonly = entity.StatusIsReadonly;
            StatusIsInvoiced = entity.StatusIsInvoiced;
            StatusIsOrdered = entity.StatusIsOrdered;
            ConStatusFk = entity.ConStatusFk;
            PrcConfigHeaderFk = entity.PrcConfigHeaderFk;
            StatusIsRejected = entity.StatusIsRejected;
            Icon = entity.Icon;
            BpName2 = entity.BpName2;
            SupplierCode = entity.SupplierCode;
            ProjectNo = entity.ProjectNo;
            ProjectName = entity.ProjectName;
            CompanyFk = entity.CompanyFk;
            Bp2Name1 = entity.Bp2Name1;
            Bp2Name2 = entity.Bp2Name2;
            BusinessPartnerFk = entity.BusinessPartnerFk;
            Supplier2Code = entity.Supplier2Code;
            ProjectFk = entity.ProjectFk;
            CodeQuotation = entity.CodeQuotation;
            ConHeaderFk = entity.ConHeaderFk;
            PrcCopyModeFk = entity.PrcCopyModeFk;
            IsFreeItemsAllowed = entity.IsFreeItemsAllowed;
            SalesTaxMethodFk = entity.SalesTaxMethodFk;
            IsFramework = entity.IsFramework;
            BankFk = entity.BankFk;
            MdcMaterialCatalogFk = entity.MdcMaterialCatalogFk;
            BoqWicCatFk = entity.BoqWicCatFk;
            BasLanguageFk = entity.BasLanguageFk;

            if (entity.StatusDescriptionInfo != null )
            {
                StatusDescriptionInfo = new DescriptionTranslateTypeDto(entity.StatusDescriptionInfo);
            }

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for ControllingUnitFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_FK", TypeName = "int", Order = 1)]
        public int? ControllingUnitFk { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_FK", TypeName = "int", Order = 2)]
        public int? PrcPackageFk { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackage2HeaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_FK", TypeName = "int", Order = 3)]
        public int? PrcPackage2HeaderFk { get; set; }
    
        /// <summary>
        /// There are no comments for PrcStructureFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_FK", TypeName = "int", Order = 4)]
        public int? PrcStructureFk { get; set; }
    
        /// <summary>
        /// There are no comments for DateOrdered in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_ORDERED", TypeName = "date", Order = 5)]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateOrdered { get; set; }
    
        /// <summary>
        /// There are no comments for BusinessPartner2Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER2_FK", TypeName = "int", Order = 6)]
        public int? BusinessPartner2Fk { get; set; }
    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_FK", TypeName = "int", Order = 8)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcConfigurationFk { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_FK", TypeName = "int", Order = 9)]
        public int? PaymentTermFiFk { get; set; }
    
        /// <summary>
        /// There are no comments for ExternalCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXTERNAL_CODE", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ExternalCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_FK", TypeName = "int", Order = 11)]
        public int? PaymentTermPaFk { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_FK", TypeName = "int", Order = 12)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int TaxCodeFk { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_FK", TypeName = "int", Order = 13)]
        public int? ClerkPrcFk { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_FK", TypeName = "int", Order = 14)]
        public int? ClerkReqFk { get; set; }
    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }
    
        /// <summary>
        /// There are no comments for BpName1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BP_NAME1", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BpName1 { get; set; }
    
        /// <summary>
        /// There are no comments for PrcHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_HEADER_FK", TypeName = "int", Order = 17)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for StatusIsVirtual in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISVIRTUAL", TypeName = "bit", Order = 18)]
        public bool? StatusIsVirtual { get; set; }
    
        /// <summary>
        /// There are no comments for StatusIsReported in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISREPORTED", TypeName = "bit", Order = 19)]
        public bool? StatusIsReported { get; set; }
    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 20)]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public string SearchPattern { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_FK", TypeName = "int", Order = 21)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CurrencyFk { get; set; }
    
        /// <summary>
        /// There are no comments for MdcBillingSchemaFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_FK", TypeName = "int", Order = 22)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int MdcBillingSchemaFk { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectChangeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_FK", TypeName = "int", Order = 23)]
        public int? ProjectChangeFk { get; set; }
    
        /// <summary>
        /// There are no comments for Exchangerate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 24)]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal Exchangerate { get; set; }
    
        /// <summary>
        /// There are no comments for BpdContactFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_FK", TypeName = "int", Order = 25)]
        public int? BpdContactFk { get; set; }
    
        /// <summary>
        /// There are no comments for BpdSubsidiaryFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_FK", TypeName = "int", Order = 26)]
        public int? BpdSubsidiaryFk { get; set; }
    
        /// <summary>
        /// There are no comments for BpdSupplierFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_FK", TypeName = "int", Order = 27)]
        public int? BpdSupplierFk { get; set; }
    
        /// <summary>
        /// There are no comments for BpdVatGroupFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_FK", TypeName = "int", Order = 28)]
        public int? BpdVatGroupFk { get; set; }
    
        /// <summary>
        /// There are no comments for StatusIsLive in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISLIVE", TypeName = "bit", Order = 29)]
        public bool? StatusIsLive { get; set; }
    
        /// <summary>
        /// There are no comments for StatusIsCanceled in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISCANCELED", TypeName = "bit", Order = 30)]
        public bool? StatusIsCanceled { get; set; }
    
        /// <summary>
        /// There are no comments for StatusIsDelivered in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISDELIVERED", TypeName = "bit", Order = 31)]
        public bool? StatusIsDelivered { get; set; }
    
        /// <summary>
        /// There are no comments for StatusIsReadonly in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISREADONLY", TypeName = "bit", Order = 32)]
        public bool? StatusIsReadonly { get; set; }
    
        /// <summary>
        /// There are no comments for StatusIsInvoiced in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISINVOICED", TypeName = "bit", Order = 33)]
        public bool? StatusIsInvoiced { get; set; }
    
        /// <summary>
        /// There are no comments for StatusIsOrdered in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISORDERED", TypeName = "bit", Order = 34)]
        public bool? StatusIsOrdered { get; set; }
    
        /// <summary>
        /// There are no comments for ConStatusFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONSTATUSFK", TypeName = "int", Order = 36)]
        public int? ConStatusFk { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigHeaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGHEADER_FK", TypeName = "int", Order = 38)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcConfigHeaderFk { get; set; }
    
        /// <summary>
        /// There are no comments for StatusIsRejected in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISREJECTED", TypeName = "bit", Order = 37)]
        public bool? StatusIsRejected { get; set; }
    
        /// <summary>
        /// There are no comments for StatusDescriptionInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STATUSDESCRIPTION", TypeName = "nvarchar(252)", Order = 35, TranslationColumnName = "STATUSDESCRIPTIONTR")]
        public DescriptionTranslateTypeDto StatusDescriptionInfo { get; set; }
    
        /// <summary>
        /// There are no comments for Icon in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ICON", TypeName = "int", Order = 40)]
        public int? Icon { get; set; }
    
        /// <summary>
        /// There are no comments for BpName2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BP_NAME2", TypeName = "nvarchar(252)", Order = 41)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BpName2 { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 42)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectNo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROJECTNO", TypeName = "nvarchar(16)", Order = 43)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ProjectNo { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROJECT_NAME", TypeName = "nvarchar(252)", Order = 44)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ProjectName { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_FK", TypeName = "int", Order = 47)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyFk { get; set; }
    
        /// <summary>
        /// There are no comments for Bp2Name1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BP2_NAME1", TypeName = "nvarchar(252)", Order = 45)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Bp2Name1 { get; set; }
    
        /// <summary>
        /// There are no comments for Bp2Name2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BP2_NAME2", TypeName = "nvarchar(252)", Order = 46)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Bp2Name2 { get; set; }
    
        /// <summary>
        /// There are no comments for BusinessPartnerFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_FK", TypeName = "int", Order = 49)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int BusinessPartnerFk { get; set; }
    
        /// <summary>
        /// There are no comments for Supplier2Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SUPPLIER2_CODE", TypeName = "nvarchar(252)", Order = 48)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Supplier2Code { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_FK", TypeName = "int", Order = 50)]
        public int? ProjectFk { get; set; }
    
        /// <summary>
        /// There are no comments for CodeQuotation in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE_QUOTATION", TypeName = "nvarchar(20)", Order = 51)]
        [System.ComponentModel.DataAnnotations.StringLength(20)]
        public string CodeQuotation { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_FK", TypeName = "int", Order = 52)]
        public int? ConHeaderFk { get; set; }
    
        /// <summary>
        /// There are no comments for PrcCopyModeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_FK", TypeName = "int", Order = 53)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcCopyModeFk { get; set; }
    
        /// <summary>
        /// There are no comments for IsFreeItemsAllowed in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFREEITEMSALLOWED", TypeName = "bit", Order = 54)]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsFreeItemsAllowed { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_FK", TypeName = "int", Order = 55)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int SalesTaxMethodFk { get; set; }
    
        /// <summary>
        /// There are no comments for IsFramework in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFRAMEWORK", TypeName = "bit", Order = 56)]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsFramework { get; set; }
    
        /// <summary>
        /// There are no comments for BankFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_FK", TypeName = "int", Order = 57)]
        public int? BankFk { get; set; }
    
        /// <summary>
        /// There are no comments for MdcMaterialCatalogFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_FK", TypeName = "int", Order = 58)]
        public int? MdcMaterialCatalogFk { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_FK", TypeName = "int", Order = 59)]
        public int? BoqWicCatFk { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 60)]
        public int? BasLanguageFk { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(ConHeaderLookupVEntity); }
        }

        /// <summary>
        /// Copy the current ConHeaderLookupVDto instance to a new ConHeaderLookupVEntity instance.
        /// </summary>
        /// <returns>a new instance of class ConHeaderLookupVEntity</returns>
        public ConHeaderLookupVEntity Copy()
        {
          var entity = new ConHeaderLookupVEntity();

          entity.Id = this.Id;
          entity.ControllingUnitFk = this.ControllingUnitFk;
          entity.PrcPackageFk = this.PrcPackageFk;
          entity.PrcPackage2HeaderFk = this.PrcPackage2HeaderFk;
          entity.PrcStructureFk = this.PrcStructureFk;
          entity.DateOrdered = this.DateOrdered;
          entity.BusinessPartner2Fk = this.BusinessPartner2Fk;
          entity.Code = this.Code;
          entity.PrcConfigurationFk = this.PrcConfigurationFk;
          entity.PaymentTermFiFk = this.PaymentTermFiFk;
          entity.ExternalCode = this.ExternalCode;
          entity.PaymentTermPaFk = this.PaymentTermPaFk;
          entity.TaxCodeFk = this.TaxCodeFk;
          entity.ClerkPrcFk = this.ClerkPrcFk;
          entity.ClerkReqFk = this.ClerkReqFk;
          entity.Description = this.Description;
          entity.BpName1 = this.BpName1;
          entity.PrcHeaderId = this.PrcHeaderId;
          entity.StatusIsVirtual = this.StatusIsVirtual;
          entity.StatusIsReported = this.StatusIsReported;
          entity.SearchPattern = this.SearchPattern;
          entity.CurrencyFk = this.CurrencyFk;
          entity.MdcBillingSchemaFk = this.MdcBillingSchemaFk;
          entity.ProjectChangeFk = this.ProjectChangeFk;
          entity.Exchangerate = this.Exchangerate;
          entity.BpdContactFk = this.BpdContactFk;
          entity.BpdSubsidiaryFk = this.BpdSubsidiaryFk;
          entity.BpdSupplierFk = this.BpdSupplierFk;
          entity.BpdVatGroupFk = this.BpdVatGroupFk;
          entity.StatusIsLive = this.StatusIsLive;
          entity.StatusIsCanceled = this.StatusIsCanceled;
          entity.StatusIsDelivered = this.StatusIsDelivered;
          entity.StatusIsReadonly = this.StatusIsReadonly;
          entity.StatusIsInvoiced = this.StatusIsInvoiced;
          entity.StatusIsOrdered = this.StatusIsOrdered;
          entity.ConStatusFk = this.ConStatusFk;
          entity.PrcConfigHeaderFk = this.PrcConfigHeaderFk;
          entity.StatusIsRejected = this.StatusIsRejected;
          entity.Icon = this.Icon;
          entity.BpName2 = this.BpName2;
          entity.SupplierCode = this.SupplierCode;
          entity.ProjectNo = this.ProjectNo;
          entity.ProjectName = this.ProjectName;
          entity.CompanyFk = this.CompanyFk;
          entity.Bp2Name1 = this.Bp2Name1;
          entity.Bp2Name2 = this.Bp2Name2;
          entity.BusinessPartnerFk = this.BusinessPartnerFk;
          entity.Supplier2Code = this.Supplier2Code;
          entity.ProjectFk = this.ProjectFk;
          entity.CodeQuotation = this.CodeQuotation;
          entity.ConHeaderFk = this.ConHeaderFk;
          entity.PrcCopyModeFk = this.PrcCopyModeFk;
          entity.IsFreeItemsAllowed = this.IsFreeItemsAllowed;
          entity.SalesTaxMethodFk = this.SalesTaxMethodFk;
          entity.IsFramework = this.IsFramework;
          entity.BankFk = this.BankFk;
          entity.MdcMaterialCatalogFk = this.MdcMaterialCatalogFk;
          entity.BoqWicCatFk = this.BoqWicCatFk;
          entity.BasLanguageFk = this.BasLanguageFk;

          if (this.StatusDescriptionInfo != null )
          {

               entity.StatusDescriptionInfo = new DescriptionTranslateType(this.StatusDescriptionInfo);
          }

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(ConHeaderLookupVEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(ConHeaderLookupVEntity entity);
    }

}
