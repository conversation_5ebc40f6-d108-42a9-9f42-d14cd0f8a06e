/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import {
	DataServiceFlatLeaf,
	IDataServiceChildRoleOptions,
	IDataServiceOptions,
	IDataServiceEndPointOptions,
	ServiceRole
} from '@libs/platform/data-access';
import { TimekeepingCertificateDataService } from './timekeeping-certificate-data.service';
import { ICertificateDocumentEntity, ICertificateEntity } from '@libs/resource/interfaces';
import { ITimekeepingCertificateComplete } from '../model/entities/timekeeping-certificate-complete.interface';
import {IIdentificationDataMutable } from '@libs/platform/common';

@Injectable({
	providedIn: 'root'
})
export class TimekeepingCertificateDocumentDataService extends DataServiceFlatLeaf<ICertificateDocumentEntity, ICertificateEntity, ITimekeepingCertificateComplete> {
	public constructor(private certDataService: TimekeepingCertificateDataService) {
		const options: IDataServiceOptions<ICertificateDocumentEntity> = {
			apiUrl: 'timekeeping/certificate/document',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'listbyparent',
				usePost: true,
				prepareParam: (): Readonly<IIdentificationDataMutable> => {
					const selectedCertificate = this.certDataService.getSelectedEntity();
					if (selectedCertificate && selectedCertificate.Id != null) {
						return { id: selectedCertificate.Id, PKey1: selectedCertificate.Id } as Readonly<IIdentificationDataMutable>;
					}
					return { id: 0, PKey1: null } as Readonly<IIdentificationDataMutable>;
				}
			},
			roleInfo: <IDataServiceChildRoleOptions<ICertificateDocumentEntity, ICertificateEntity, ITimekeepingCertificateComplete>>{
				role: ServiceRole.Leaf,
				itemName: 'Documents',
				parent: certDataService
			}
		};
		super(options);
	}

	public override isParentFn(parentKey: ICertificateEntity, entity: ICertificateDocumentEntity): boolean {
		return entity.CertificateFk === parentKey.Id;
	}

	public override registerModificationsToParentUpdate(complete: ITimekeepingCertificateComplete, modified: ICertificateDocumentEntity[], deleted: ICertificateDocumentEntity[]): void {
		if (modified && modified.length > 0) {
			complete.DocumentsToSave = modified;
		}
		if (deleted && deleted.length > 0) {
			complete.DocumentsToDelete = deleted;
		}
	}

	public override getSavedEntitiesFromUpdate(complete: ITimekeepingCertificateComplete): ICertificateDocumentEntity[] {
		return complete.DocumentsToSave || [];
	}
}