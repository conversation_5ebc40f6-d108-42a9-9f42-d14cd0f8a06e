using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EntityFrameworkExtras.EF6;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// SP to delete line item generated by rule
	/// </summary>
	[StoredProcedure("EST_DELETE_LINEITEM_GENERATED_BY_RULE")]
	public class EstDeleteLineItemGeneratedByRule
	{
		/// <summary>
		/// 
		/// </summary>
		[StoredProcedureParameter(SqlDbType.Int, ParameterName = "EstHeaderId")]
		public int EstHeaderId { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[StoredProcedureParameter(SqlDbType.Udt, ParameterName = "RuleSourceIds")]
		public IEnumerable<EstUdttIds> RuleSourceIds { get; set; }

		/// <summary>
		/// 
		/// </summary>
		[StoredProcedureParameter(SqlDbType.Int, ParameterName = "WhoIsr")]
		public int WhoIsr { get; set; }
	}
}
