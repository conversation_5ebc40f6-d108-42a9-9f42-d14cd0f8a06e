# modifies sub-modules so they appear in Typedoc

if (!(Get-Module -ListAvailable -Name "newtonsoft.json")) {
    Install-Module -Name "newtonsoft.json" -Scope CurrentUser -Force
}

Import-Module "newtonsoft.json" -Scope Local

$scriptDir = $PSScriptRoot
$baseDir = [System.IO.Path]::Combine($scriptDir, '..', '..')

$jsonOptions = New-Object -TypeName 'System.Text.Json.JsonWriterOptions'
$jsonOptions.Indented = $True

$jsonSerializer = [Newtonsoft.Json.JsonSerializer]::Create()

Write-Host $baseDir

Write-Host ''

$moduleDirs = @(Get-ChildItem -Path $([System.IO.Path]::Combine($baseDir, 'libs', 'modules')) -Directory) + @(Get-ChildItem -Path $([System.IO.Path]::Combine($baseDir, 'libs')) -Directory -Exclude @('documentation', 'modules'))

foreach ($moduleDir in $moduleDirs)
{
	foreach ($subModuleDir in Get-ChildItem -Path $moduleDir -Directory)
	{
		$moduleName = $moduleDir.Name
		$subModuleName = $subModuleDir.Name
		
		if ($subModuleName -eq 'assets') {
			continue
		}
		if ($moduleName -eq 'example') {
			continue
		}
		
		$typedocFolderPath = "$($moduleDir.Name)/$($subModuleDir.Name)"
		if ($moduleDir.Name -NotIn ('platform', 'ui')) {
			$typedocFolderPath = "modules/$typedocFolderPath"
		}
		
		$pkgJsonPath = [System.IO.Path]::Combine($subModuleDir, 'package.json')
		
		if (Test-Path "$pkgJsonPath") {
			$settings = Get-Content -Raw "$pkgJsonPath" | ConvertFrom-Json -AsHashTable
		} else {
			$settings = @{}
		}
		
		$settings.name = "$typedocFolderPath"
		
		$stringWriter = New-Object System.IO.StringWriter

		$jsonWriter = New-Object Newtonsoft.Json.JsonTextWriter($stringWriter)
	    $jsonWriter.Formatting = [Newtonsoft.Json.Formatting]::Indented
	    $jsonWriter.IndentChar = "`t"
	    $jsonWriter.Indentation = 1
		
	    $jsonSerializer.Serialize($jsonWriter, $settings)
		
		$jsonWriter.Close()

		# Get the JSON string with tab indentation
		$jsonIndented = $stringWriter.ToString()
		
		$jsonIndented | Out-File "$pkgJsonPath" -Encoding UTF8 -Force
		
		Write-Host -ForegroundColor DarkCyan "Saved folder path '$typedocFolderPath' to $pkgJsonPath."
		
		$typedocPath = [System.IO.Path]::Combine($subModuleDir, 'typedoc.json')
		if (-not (Test-Path "$typedocPath"))
		{
			$file = [System.IO.File]::Create($typedocPath)
			$writer = New-Object -TypeName 'System.Text.Json.Utf8JsonWriter' -ArgumentList ($file, $jsonOptions)
			
			try
			{
				$writer.WriteStartObject()
				
				$writer.WriteStartArray('extends')
				$writer.WriteStringValue('../../../../typedoc.base.json')
				$writer.WriteEndArray()
				
				$writer.WriteStartArray('entryPoints')
				$writer.WriteStringValue('src/index.ts')
				$writer.WriteEndArray()
				
				$writer.WriteString('readme', '.typedoc/typedoc-index.md')
				
				$writer.writeEndObject() # root
			}
			finally
			{
				$writer.Dispose()
				$file.Dispose()
			}
		}
		
		$typedocDir = [System.IO.Path]::Combine($subModuleDir, '.typedoc')
		New-Item -Path "$SubModuleDir" -Name '.typedoc' -ItemType 'directory' -Force
		
		$typedocIndexPath = [System.IO.Path]::Combine($subModuleDir, '.typedoc', 'typedoc-index.md')
		if (-not (Test-Path "$typedocIndexPath"))
		{
			"This is the $($subModuleDir.Name) area of the $($moduleDir.Name) main module." | Out-File -FilePath "$typedocIndexPath"
		}
		
		Write-Host "$($moduleDir.Name)-$($subModuleDir.Name) done."
	}
}
