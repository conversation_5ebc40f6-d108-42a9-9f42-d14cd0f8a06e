import { _common, _package, _validate, _modalView, _procurementContractPage, _projectPage, _controllingUnit, _commonAPI } from 'cypress/pages';
import { cnt, tile, app, sidebar, commonLocators, btn } from 'cypress/locators';
import type { DataCells } from 'cypress/pages/interfaces.d.ts'

import CommonLocators from 'cypress/locators/common-locators';

const Code1 = _common.generateRandomString(6)
const Code2 = _common.generateRandomString(6)
const Code3 = _common.generateRandomString(6)
const PROJECT_NO = _common.generateRandomString(4);
const PROJECT_DESC = _common.generateRandomString(4);
let PROCUREMENT_CONTRACT_PARAMETER: DataCells,
	GENERALS_PARAMETERS1: DataCells,
	GENERALS_PARAMETERS2: DataCells,
	GENERALS_PARAMETERS3: DataCells,
	PROJECTS_PARAMETERS: DataCells

let
	CONTAINERS_CONTRACT,
	CONTAINERS_CUSTOMIZING,
	CONTAINER_COLUMNS_CONTRACT,
	CONTAINER_COLUMNS_GENERALS,
	CONTAINERS_GENERALS

describe("PCM- 4.213 | Generals container in contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
	before(function () {
		cy.fixture('pcm/con-4.213-generals-container-in-contract-module.json').then((data) => {
			this.data = data;

			CONTAINERS_CUSTOMIZING = this.data.CONTAINERS.CUSTOMIZING
			CONTAINER_COLUMNS_GENERALS = this.data.CONTAINER_COLUMNS.GENERALS
			CONTAINERS_GENERALS = this.data.CONTAINERS.GENERALS
			CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
			CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
			PROJECTS_PARAMETERS = {
				[commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
				[commonLocators.CommonLabels.NAME]: PROJECT_DESC,
				[commonLocators.CommonLabels.CLERK]: CONTAINERS_CONTRACT.CLERK
			};

			GENERALS_PARAMETERS1 = {
				[app.GridCells.LEDGER_CONTEXT_FK]: CONTAINERS_CUSTOMIZING.LEDGERCONTEXTName,
				[app.GridCells.DESCRIPTION_INFO]: Code1,
				[app.GridCells.IS_COST]: commonLocators.CommonKeys.CHECK,
				[app.GridCells.IS_SALES]: commonLocators.CommonKeys.CHECK,
				[app.GridCells.IS_PROCUREMENT]: commonLocators.CommonKeys.CHECK
			}

			GENERALS_PARAMETERS2 = {
				[app.GridCells.LEDGER_CONTEXT_FK]: CONTAINERS_CUSTOMIZING.LEDGERCONTEXTName,
				[app.GridCells.DESCRIPTION_INFO]: Code2,
				[app.GridCells.IS_SALES]: commonLocators.CommonKeys.CHECK,
				[app.GridCells.IS_PROCUREMENT]: commonLocators.CommonKeys.CHECK,
				[app.GridCells.IS_PERCENT]: commonLocators.CommonKeys.CHECK
			}

			GENERALS_PARAMETERS3 = {
				[app.GridCells.LEDGER_CONTEXT_FK]: CONTAINERS_CUSTOMIZING.LEDGERCONTEXTName,
				[app.GridCells.DESCRIPTION_INFO]: Code3,
				[app.GridCells.IS_COST]: commonLocators.CommonKeys.CHECK,
				[app.GridCells.IS_PROCUREMENT]: commonLocators.CommonKeys.CHECK,
				[app.GridCells.IS_SALES]: commonLocators.CommonKeys.CHECK
			}
			PROCUREMENT_CONTRACT_PARAMETER = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BP
			}
		}).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT)
                _common.waitForLoaderToDisappear()
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            })
            _common.waitForLoaderToDisappear();
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        })
	});
	after(() => {
		cy.LOGOUT();
	});

	 it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

	it("TC - Create Controlling unit ", function () {
			_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTROLLING_UNITS);
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT, app.FooterTab.CONTROLLING_UNITS, 2);
		});
		_common.waitForLoaderToDisappear()
		_common.create_newRecord(cnt.uuid.CONTROLLING_UNIT)
		_common.waitForLoaderToDisappear()
		_common.set_cellCheckboxValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.ISA_ACCOUNTING_ELEMENT, commonLocators.CommonKeys.CHECK)
		cy.SAVE_WITH_ATTEMPTS()
		_common.waitForLoaderToDisappear()
		_common.select_rowInContainer(cnt.uuid.CONTROLLING_UNIT)
		_common.saveCellDataToEnv(cnt.uuid.CONTROLLING_UNIT, app.GridCells.CODE, "CNTSUBCODE")
	});
	it("TC - Create general type for amount and percent ", function () {

		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, commonLocators.CommonLabels.CUSTOMIZING);
		_common.openTab(app.TabBar.MASTER_DATA).then(() => {
			_common.select_tabFromFooter(cnt.uuid.ENTITY_TYPES, app.FooterTab.DATA_TYPES, 0);
		});
		_common.search_inSubContainer(cnt.uuid.ENTITY_TYPES, CONTAINERS_CUSTOMIZING.EntityType)
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.select_rowInContainer(cnt.uuid.ENTITY_TYPES)
		_common.search_inSubContainer(cnt.uuid.INSTANCES, " ")
		_common.clear_subContainerFilter(cnt.uuid.INSTANCES);
		_common.maximizeContainer(cnt.uuid.INSTANCES)
		_common.waitForLoaderToDisappear()
		_common.set_cellCheckboxValueForAllRowCell(cnt.uuid.INSTANCES, app.GridCells.IS_DEFAULT, commonLocators.CommonKeys.UNCHECK)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_package.enterRecord_toCreateGeneralsTypeDataRecord(cnt.uuid.INSTANCES, GENERALS_PARAMETERS1)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_package.enterRecord_toCreateGeneralsTypeDataRecord(cnt.uuid.INSTANCES, GENERALS_PARAMETERS2)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_package.enterRecord_toCreateGeneralsTypeDataRecord(cnt.uuid.INSTANCES, GENERALS_PARAMETERS3)
		_common.minimizeContainer(cnt.uuid.INSTANCES)
	})
	it('TC - Create new contract in contract module', function () {
		_common.waitForLoaderToDisappear()
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);

		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 2);
			_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
		});
		_common.waitForLoaderToDisappear()
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, CommonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
		cy.SAVE_WITH_ATTEMPTS()
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.GENERALS_CONTRACT, app.FooterTab.GENERALS);
		});
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	})
	it('TC - Add new generals record in contract module', function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		_common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE")).pinnedItem();
		_common.waitForLoaderToDisappear()
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.GENERALS_CONTRACT, app.FooterTab.GENERALS, 0);
			_common.setup_gridLayout(cnt.uuid.GENERALS_CONTRACT, CONTAINER_COLUMNS_GENERALS)
		});
		_common.create_newRecord(cnt.uuid.GENERALS_CONTRACT)
		_common.edit_dropdownCellWithInput(cnt.uuid.GENERALS_CONTRACT, app.GridCells.PRC_GENERALS_TYPE_FK, "list", app.InputFields.INPUT_GROUP_CONTENT, Code3)
		_common.enterRecord_inNewRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_GENERALS.VALUE1)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
	})
	it('TC - Verify value type of generals record with istype data type', function () {
		_validate.verify_isRecordPresent(cnt.uuid.GENERALS_CONTRACT, CONTAINERS_GENERALS.VALUE1)
		_common.select_rowInContainer(cnt.uuid.GENERALS_CONTRACT)
		_common.waitForLoaderToDisappear()
		_common.assert_cellData_insideActiveRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells.VALUE_TYPE, CONTAINERS_GENERALS.COSTVALUETYPE)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	})
	it('TC - Verify type is mandatory field, and lookup filter is correct', function () {
		_common.create_newRecord(cnt.uuid.GENERALS_CONTRACT)
		_common.enterRecord_inNewRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_GENERALS.VALUE1)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_validate.validate_Text_message_In_PopUp(CONTAINERS_GENERALS.MSG)
		_common.clickOn_modalFooterButton(btn.ButtonText.CANCEL)
		_common.clickOn_toolbarButton(cnt.uuid.GENERALS_CONTRACT, btn.IconButtons.ICO_REC_DELETE)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	})
	it('TC - Verify type type should not duplicated', function () {
		_common.create_newRecord(cnt.uuid.GENERALS_CONTRACT)
		_common.edit_dropdownCellWithInput(cnt.uuid.GENERALS_CONTRACT, app.GridCells.PRC_GENERALS_TYPE_FK, "list", app.InputFields.INPUT_GROUP_CONTENT, Code3)
		_common.enterRecord_inNewRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_GENERALS.VALUE1)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_validate.validate_Text_message_In_PopUp(CONTAINERS_GENERALS.VALIDATION_MSG)
		_common.clickOn_modalFooterButton(btn.ButtonText.CANCEL)
		_common.clickOn_toolbarButton(cnt.uuid.GENERALS_CONTRACT, btn.IconButtons.ICO_REC_DELETE)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
	})
	it('TC - Verify tax field and controlling unit are editable', function () {

		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.GENERALS_CONTRACT, app.FooterTab.GENERALS, 0);
		});
		_common.clickOn_cellHasUniqueValue(cnt.uuid.GENERALS_CONTRACT, app.GridCells.PRC_GENERALS_TYPE_FK, Code3)
		cy.SAVE()
		_common.waitForLoaderToDisappear()
		_common.edit_dropdownCellWithCaret(cnt.uuid.GENERALS_CONTRACT, app.GridCells.TAX_CODE_FK, CommonLocators.CommonKeys.GRID, CONTAINERS_GENERALS.TAX_CODE)
		_common.edit_dropdownCellWithInput(cnt.uuid.GENERALS_CONTRACT, app.GridCells.CONTROLLING_UNIT_FK, CommonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env("CNTSUBCODE"))
		cy.SAVE_WITH_ATTEMPTS()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_common.waitForLoaderToDisappear()
		_common.select_rowInContainer(cnt.uuid.GENERALS_CONTRACT)
		_common.assert_cellData_insideActiveRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells.CONTROLLING_UNIT_FK, Cypress.env('API_PROJECT_NUMBER_1'))
		_common.assert_cellData_insideActiveRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells.VALUE_TYPE, CONTAINERS_GENERALS.COSTVALUETYPE)
		_common.assert_cellData_insideActiveRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells. TAX_CODE_FK, CONTAINERS_GENERALS.TAX_CODE)

		_common.delete_recordFromContainer(cnt.uuid.GENERALS_CONTRACT)
		cy.SAVE_WITH_ATTEMPTS()
		_common.waitForLoaderToDisappear()
		cy.REFRESH_CONTAINER()
		_validate.verify_recordNotPresentInContainer(cnt.uuid.GENERALS_CONTRACT, Code3)


	})
	it('TC - Verify tax field and controlling unit are not editable', function () {
		_common.create_newRecord(cnt.uuid.GENERALS_CONTRACT)
		_common.edit_dropdownCellWithInput(cnt.uuid.GENERALS_CONTRACT, app.GridCells.PRC_GENERALS_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, Code2)
		_common.enterRecord_inNewRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_GENERALS.VALUE2)
		_common.waitForLoaderToDisappear()
		cy.SAVE_WITH_ATTEMPTS()
		_common.waitForLoaderToDisappear()
		_common.clickOn_cellHasUniqueValue(cnt.uuid.GENERALS_CONTRACT, app.GridCells.PRC_GENERALS_TYPE_FK, Code2)
		_validate.verify_isRecordNotEditable(cnt.uuid.GENERALS_CONTRACT, app.GridCells. TAX_CODE_FK, 0)
		_validate.verify_isRecordNotEditable(cnt.uuid.GENERALS_CONTRACT, app.GridCells.CONTROLLING_UNIT_FK, 0)
		_common.clickOn_activeRowCell(cnt.uuid.GENERALS_CONTRACT, app.GridCells. TAX_CODE_FK)
		_common.clickOn_activeRowCell(cnt.uuid.GENERALS_CONTRACT, app.GridCells.CONTROLLING_UNIT_FK)
		_common.assert_cellData_insideActiveRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells.VALUE_TYPE, CONTAINERS_GENERALS.COSTVALUETYPE_PERCENT)
	})

})
