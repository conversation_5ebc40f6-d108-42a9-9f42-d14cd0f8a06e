/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import {
	UiCommonMessageBoxService
} from '@libs/ui/common';
import { IOrdHeaderEntity } from '@libs/sales/interfaces';
import { SalesCommonUpdateEstimateWizardService } from '@libs/sales/common';
import { SalesContractContractsDataService } from '../services/sales-contract-contracts-data.service';


@Injectable({
    providedIn: 'root'
})
export class SalesContractUpdateEstimateWizardService {

	private readonly dataService = inject(SalesContractContractsDataService);

	private readonly salesCommonUpdateEstimateWizardService = inject(SalesCommonUpdateEstimateWizardService);

    private readonly messageBoxService = inject(UiCommonMessageBoxService);

    public updateEstimate() {
        const selectedContract = this.dataService.getSelectedEntity();
        if (this.assertSelection(selectedContract, 'sales.contract.noContractHeaderSelected')) {
            //TODO: dependency on salesCommonStatusHelperService in assertIsNotReadOnly function
            this.salesCommonUpdateEstimateWizardService.showUpdateEstimateWizard();

        }
    }
    /**
     * This function is used to check if the selected item is not null and has a valid Id.
     * It shows a message box if the item is null or has an invalid Id.
     * 
     * @param selItem selected item
     * @param title message box title
     * @returns check item is not null and has a valid Id
     */
    public assertSelection(selItem: IOrdHeaderEntity | null, title: string): boolean {
        if (selItem && selItem.Id >= 0) {
            return true;
        } else {
            this.messageBoxService.showMsgBox(title, 'sales.common.wizard.updateEstimate', 'ico-info');
            return false;
        }
    }
}