/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable, ProviderToken } from '@angular/core';
import { BaseValidationService, IValidationFunctions, IEntityRuntimeDataRegistry, PlatformSchemaService, ValidationServiceFactory } from '@libs/platform/data-access';
import { ICertifiedEmployeeEntity } from '@libs/timekeeping/interfaces';
import { TimekeepingCertifiedEmployeeDataService } from './timekeeping-certified-employee-data.service';

@Injectable({
	providedIn: 'root'
})

export class TimekeepingCertifiedEmployeeValidationService extends BaseValidationService<ICertifiedEmployeeEntity> {
	private validators: IValidationFunctions<ICertifiedEmployeeEntity> | null = null;

	public constructor(protected dataService: TimekeepingCertifiedEmployeeDataService) {
		super();

		const schemaSvcToken: ProviderToken<PlatformSchemaService<ICertifiedEmployeeEntity>> = PlatformSchemaService<ICertifiedEmployeeEntity>;
		const platformSchemaService = inject(schemaSvcToken);

		platformSchemaService.getSchema({moduleSubModule: 'Timekeeping.Certificate', typeName: 'CertifiedEmployeeDto'}).then(
			(scheme) => {
				this.validators = new ValidationServiceFactory<ICertifiedEmployeeEntity>().provideValidationFunctionsFromScheme(scheme, this);
			}
		);
	}

	protected generateValidationFunctions(): IValidationFunctions<ICertifiedEmployeeEntity> {
		if(this.validators !== null) {
			return this.validators;
		}

		return {};

	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<ICertifiedEmployeeEntity> {
		return this.dataService;
	}

}