{"name": "itwo40", "version": "25.3.0", "license": "UNLICENSED", "scripts": {"ng": "nx", "postinstall": "node ./decorate-angular-cli.js", "merge-junit": "junit-merge --dir=./.jest/test_results --out=./.jest/test_results/complete-junit.xml", "start": "nx serve", "build": "nx build", "compodoc": "npx compodoc -c libs/documentation/.compodocrc.json", "compodoc-serve": "npx compodoc -c libs/documentation/.compodocrc.json -s -w", "storybook": "nx run documentation:storybook", "storybook-build": "nx storybook build documentation --projectBuildConfig=documentation:build-storybook", "typedoc": "npx typedoc", "dto-generator": "dto-generator", "test": "npx nx run-many -t test --projects=* --exclude *-e2e"}, "private": true, "dependencies": {"@angular/animations": "17.1.3", "@angular/cdk": "17.1.2", "@angular/common": "17.1.3", "@angular/compiler": "17.1.3", "@angular/core": "17.1.3", "@angular/elements": "17.1.3", "@angular/forms": "17.1.3", "@angular/material": "17.1.2", "@angular/platform-browser": "17.1.3", "@angular/platform-browser-dynamic": "17.1.3", "@angular/router": "17.1.3", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-xml": "^6.1.0", "@nx/angular": "18.0.4", "@nx/devkit": "18.0.4", "@popperjs/core": "2.11.8", "@rib-4.0/ige-viewer": "^15.3.0", "@schematics/angular": "17.1.3", "@swc/helpers": "0.5.6", "@swimlane/ngx-graph": "^8.3.0", "accounting": "0.4.1", "angular-auth-oidc-client": "17.0.0", "angular-iban": "17.0.0", "bootstrap": "^5.3.3", "caniuse-lite": "^1.0.********", "chart.js": "^4.4.3", "codemirror": "6.0.1", "d3": "^7.9.0", "date-fns": "2.30", "date-fns-tz": "^2.0.0", "eslint-linter-browserify": "^8.56.0", "hoops-communicator": "1.0.6", "iban": "0.0.14", "jquery": "3.7.1", "jquery-ui": "^1.13.2", "jszip": "^3.10.1", "katex": "^0.13.24", "lodash-es": "4.17.21", "mathjs": "^12.4.1", "ngx-quill": "^26.0.8", "path": "^0.12.7", "powerbi-client": "^2.23.1", "prompt-sync": "4.2.0", "qrcode": "^1.5.3", "quill": "^2.0.2", "resumablejs": "^1.1.0", "rib-pdf-overlay": "^1.0.5", "rxjs": "7.8.1", "swagger-ui-dist": "^5.11.3", "temporal-polyfill": "^0.3.0", "tern": "0.24.3", "tslib": "2.6.2", "zone.js": "0.14.3"}, "devDependencies": {"@angular-devkit/architect": "0.1701.3", "@angular-devkit/build-angular": "17.1.3", "@angular-devkit/core": "17.1.3", "@angular-devkit/schematics": "17.1.3", "@angular-devkit/schematics-cli": "^17.1.3", "@angular-eslint/eslint-plugin": "17.2.1", "@angular-eslint/eslint-plugin-template": "17.2.1", "@angular-eslint/template-parser": "17.2.1", "@angular/cli": "~17.1.3", "@angular/compiler-cli": "17.1.3", "@angular/language-service": "17.1.3", "@compodoc/compodoc": "1.1.21", "@nrwl/tao": "18.0.4", "@nx/cypress": "18.0.4", "@nx/esbuild": "^18.0.4", "@nx/eslint-plugin": "18.0.4", "@nx/jest": "18.0.4", "@nx/js": "18.0.4", "@nx/plugin": "18.0.4", "@nx/storybook": "18.0.4", "@nx/webpack": "18.0.4", "@nx/workspace": "18.0.4", "@rib-4.0/build-executor": "^0.0.10", "@rib-4.0/dto-generator": "^1.0.11", "@rib-4.0/ng-schematics": "^5.4.23", "@storybook/addon-essentials": "7.6.14", "@storybook/angular": "7.6.14", "@storybook/core-server": "7.6.14", "@swc-node/register": "1.8.0", "@swc/cli": "~0.3.9", "@swc/core": "1.4.0", "@twittwer/compodoc": "1.12.0", "@types/accounting": "^0.4.5", "@types/d3": "^7.4.3", "@types/dagre": "^0.7.52", "@types/iban": "^0.0.35", "@types/jest": "^29.5.12", "@types/jquery": "^3.5.29", "@types/katex": "^0.16.7", "@types/lodash": "^4.17.0", "@types/node": "^20.12.3", "@types/qrcode": "^1.5.5", "@types/swagger-ui-dist": "^3.30.4", "@types/tern": "^0.23.9", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "cypress": "^13.6.4", "esbuild": "^0.19.2", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-angular-file-naming": "1.0.6", "eslint-plugin-cypress": "2.15.1", "eslint-plugin-storybook": "0.6.15", "gulp": "~4.0.2", "gulp-append-prepend": "^1.0.9", "gulp-cli": "~2.3.0", "gulp-concat": "~2.6.1", "gulp-exec": "~5.0.0", "gulp-merge": "^0.1.1", "gulp-rename": "^2.0.0", "gulp-replace": "~1.1.4", "gulp-sass": "~5.1.0", "gulp-svg-sprite": "~2.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-preset-angular": "14.0.2", "jsonc-eslint-parser": "^2.4.0", "junit-merge": "^2.0.0", "postcss": "^8.4.39", "postcss-url": "^10.1.3", "prettier": "^3.2.5", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^5.0.5", "sass": "^1.72.0", "storybook": "^7.6.14", "ts-jest": "29.1.2", "ts-node": "10.9.2", "typedoc": "^0.25.8", "typedoc-plugin-coverage": "^3.1.0", "typedoc-plugin-extras": "^3.0.0", "typedoc-plugin-replace-text": "^3.3.0", "typescript": "5.3.3", "webpack": "^5.90.1"}, "overrides": {"d3-selection": "3.0.0"}}