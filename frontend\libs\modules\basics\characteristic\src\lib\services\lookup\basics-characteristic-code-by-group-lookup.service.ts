import { BasicsCharacteristicHeader, BasicsSharedCharacteristicCodeLookupService } from '@libs/basics/shared';
import { ILookupSearchRequest, ILookupSearchResponse, LookupHttpCacheService, LookupSearchResponse } from '@libs/ui/common';
import { Observable, of } from 'rxjs';
import { inject, Injectable } from '@angular/core';
import { PlatformHttpService } from '@libs/platform/common';
import { get } from 'lodash';

@Injectable({
	providedIn: 'root',
})
export class BasicsCharacteristicCodeByGroupLookupService extends BasicsSharedCharacteristicCodeLookupService {
	protected override readonly httpCacheService = inject(LookupHttpCacheService);
	private readonly httpService = inject(PlatformHttpService);

	/**
	 * Gets the search list of characteristics by group, remove the one with the given characteristicId if provided.
	 * @param request
	 */

	public override getSearchList(request: ILookupSearchRequest): Observable<ILookupSearchResponse<BasicsCharacteristicHeader>> {
		const groupId = get(request.additionalParameters, 'groupId');
		if (groupId === undefined || groupId === null) {
			return new Observable((observer) => {
				observer.next();
				observer.complete();
			});
		}
		const characteristicId2Remove = get(request.additionalParameters, 'characteristicId2Remove');
		const cacheKey = JSON.stringify(groupId + '-' + characteristicId2Remove);
		const cache = this.cache.getSearchList(cacheKey);
		if (cache) {
			return of(cache);
		}
		return new Observable((observer) => {
			this.httpService
				.get<BasicsCharacteristicHeader[]>('basics/characteristic/characteristic/lookupbygroup', {
					params: {
						GroupId: groupId,
					},
				})
				.then((res) => {
					if (characteristicId2Remove) {
						res = res.filter((item) => item.Id !== characteristicId2Remove);
					}
					const response = new LookupSearchResponse(res);
					this.cache.setSearchList(cacheKey, response);
					observer.next(response);
					observer.complete();
				});
		});
	}
}
