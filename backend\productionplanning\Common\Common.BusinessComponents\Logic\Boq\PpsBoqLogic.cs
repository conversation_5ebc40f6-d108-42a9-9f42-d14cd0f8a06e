using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Reflection.PortableExecutable;
using System.Text.RegularExpressions;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostGroups.BusinessComponents.CrbPrdPartnerService;

// ReSharper disable once CheckNamespace
namespace RIB.Visual.ProductionPlanning.Common.BusinessComponents
{

	/// <summary>
	///
	/// </summary>
	[Export(typeof(IPpsBoqLogic))]
	public partial class PpsBoqLogicLogic : IPpsBoqLogic
	{
		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IBoqItemEntity CreateRootPrjBoq(int projectId)
		{
			var prjBoqComposites = Injector.Get<IBoqCompositeLogic>("Project").GetBoqCompositesByProjectId(projectId);
			var prjBoqComposite = prjBoqComposites.FirstOrDefault();
			if (prjBoqComposite?.BoqRootItem == null)
			{
				return Injector.Get<IBoqCompositeLogic>("Project").CreateNewBoq(projectId, true, true);
			}

			return prjBoqComposite.BoqRootItem;
		}

		IEnumerable<IBoqItemEntity> IPpsBoqLogic.GetPrjBoqs(int projectId)
		{
			IEnumerable<IBoqItemEntity> prjBoqs = Enumerable.Empty<IBoqItemEntity>();
			var prjBoqComposites = Injector.Get<IBoqCompositeLogic>("Project").GetBoqCompositesByProjectId(projectId);
			var prjBoqComposite = prjBoqComposites.FirstOrDefault();
			if (prjBoqComposite?.Boq != null)
			{
				prjBoqs = Injector.Get<IBoqItemLogic>().GetBoqItemsByBoqHeaderIds(new[] { prjBoqComposite.BoqHeader.Id });
			}
			return prjBoqs;
		}
	}
}
