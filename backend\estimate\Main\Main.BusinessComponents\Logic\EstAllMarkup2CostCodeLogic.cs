using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Text;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;
using RIB.Visual.Basics.Core.Core;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RIB.Visual.Basics.CostCodes.BusinessComponents;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Basics.Customize.BusinessComponents;
using System.Diagnostics;
using RIB.Visual.Basics.LookupData.BusinessComponents;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	///
	public class EstAllMarkup2CostCodeLogic : EntityUpdateLogic<EstAllMarkup2costcodeEntity, Platform.Core.IdentificationData>
	{
		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public override DbCompiledModel GetDbModel() => ModelBuilder.DbModel;

		/// <summary>
		/// The singleton identifier instance for the <see cref="EstColumnConfigTypeEntity"/> type.
		/// </summary>
		private static readonly Lazy<CoreFinal.IIdentifier<EstAllMarkup2costcodeEntity>> IdentifierInstance = IdentifierFactory.Create<EstAllMarkup2costcodeEntity>("Id");

		/// <summary>
		/// 
		/// </summary>
		public EstAllMarkup2CostCodeLogic()
		{
			Identifier = IdentifierInstance.Value;
			PermissionGUID = "3416213311ef4b078db786669a80735e";
			SetTempMatchingFunc<DdTempIdsEntity>((e, tmp) => e.Id == tmp.Id);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="estAllowanceFk"></param>
		/// <returns></returns>
		public IEnumerable<EstAllMarkup2costcodeEntity> GetEstAllMarkup2costcodesByEstAllwance(int estAllowanceFk)
		{
			using (var dbcontext = CreateDbContext())
			{
				return dbcontext.Entities<EstAllMarkup2costcodeEntity>().Where(e => e.EstAllowanceFk == estAllowanceFk).ToList();
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="estAllowanceFk"></param>
		/// <returns></returns>
		public IEnumerable<EstAllMarkup2costcodeEntity> GetEstAllMarkup2costcodesByEstAllwances(IEnumerable<int> estAllowanceFk)
		{
			using (var dbcontext = CreateDbContext())
			{
				return dbcontext.Entities<EstAllMarkup2costcodeEntity>().Where(e => estAllowanceFk.Contains(e.EstAllowanceFk)).ToList();
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public IEnumerable<EstAllMarkup2costcodeEntity> SaveEstAllMarkup2costcode(IEnumerable<EstAllMarkup2costcodeEntity> estAllowances)
		{
			this.BulkSave(ModelBuilder.DbModel, estAllowances);

			return estAllowances;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="estAllMarkup2Costcodes"></param>
		public void DeleteEstAllMarkup2costcode(IEnumerable<EstAllMarkup2costcodeEntity> estAllMarkup2Costcodes)
		{
			using (var dbcontext = CreateDbContext())
			{
				dbcontext.Delete(estAllMarkup2Costcodes);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="mdcAllMarkup2CostCodes"></param>
		/// <param name="allowanceEntity"></param>
		/// <param name="estAllowanceAreasToSave"></param>
		/// <returns></returns>
		public IEnumerable<EstAllMarkup2costcodeEntity> ConvertEstAllMarkup2costcodeEntity(List<MdcAllMarkup2CostCodeEntity> mdcAllMarkup2CostCodes, EstAllowanceEntity allowanceEntity, ConcurrentBag<EstAllowanceAreaEntity> estAllowanceAreasToSave = null)
		{
			var graCalculator = EstGraCalculatorFactory.CreateGraCalculator(allowanceEntity);

			ConcurrentBag<EstAllMarkup2costcodeEntity> estAllMarkup2Costcodes = new ConcurrentBag<EstAllMarkup2costcodeEntity>();

			var ids = new Stack<int>(this.SequenceManager.GetNextList("EST_ALL_MARKUP2COSTCODE", mdcAllMarkup2CostCodes.Count));

			foreach (var mdcCodeEntity in mdcAllMarkup2CostCodes)
			{
				EstAllMarkup2costcodeEntity estAllMarkup2CostcodeEntity = new EstAllMarkup2costcodeEntity();
				estAllMarkup2CostcodeEntity.Id = ids.Pop();
				estAllMarkup2CostcodeEntity.EstAllowanceFk = allowanceEntity.Id;
				estAllMarkup2CostcodeEntity.MdcCostCodeFk = mdcCodeEntity.MdcCostCodeFk;
				estAllMarkup2CostcodeEntity.GaPerc = mdcCodeEntity.MarkupGa;
				estAllMarkup2CostcodeEntity.RpPerc = mdcCodeEntity.MarkupRp;
				estAllMarkup2CostcodeEntity.AmPerc = mdcCodeEntity.MarkupAm;
				estAllMarkup2CostcodeEntity.GraPerc = graCalculator.GenerateFinMGra(estAllMarkup2CostcodeEntity);
				estAllMarkup2CostcodeEntity.DefMOp = estAllMarkup2CostcodeEntity.FinMOp = mdcCodeEntity.MarkupDefMOp;
				estAllMarkup2CostcodeEntity.DefMGcPerc = estAllMarkup2CostcodeEntity.FinMGc = mdcCodeEntity.MarkupDefMGcPerc;

				if (allowanceEntity.MdcAllowanceTypeFk == 3 && mdcCodeEntity.MdcAllowanceAreaFk.HasValue && estAllowanceAreasToSave != null && estAllowanceAreasToSave.Count > 0)
				{
					var area = estAllowanceAreasToSave.FirstOrDefault(e => e.MdcAllowanceAreaFk == mdcCodeEntity.MdcAllowanceAreaFk || e.MdcAllowanceGcAreaFk == mdcCodeEntity.MdcAllowanceAreaFk);
					estAllMarkup2CostcodeEntity.EstAllowanceAreaFk = area != null ? area.Id : null;
				}
				estAllMarkup2Costcodes.Add(estAllMarkup2CostcodeEntity);
			}

			return estAllMarkup2Costcodes;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		public EstAllMarkup2costcodeEntity getEstAllMarkup2costcode(ReadData data)
		{
			var id = new Stack<int>(this.SequenceManager.GetNextList("EST_ALL_MARKUP2COSTCODE"));

			EstAllMarkup2costcodeEntity estAllMarkup2Costcode = new EstAllMarkup2costcodeEntity();
			estAllMarkup2Costcode.Id = id.Pop();
			estAllMarkup2Costcode.EstAllowanceFk = data.MdcAllowanceFk;
			if (data.IsCustomProjectCostCode)
			{
				estAllMarkup2Costcode.Project2MdcCstCdeFk = data.MdcCostCodeFk;
				estAllMarkup2Costcode.IsCustomProjectCostCode = true;
			}
			else
			{
				estAllMarkup2Costcode.MdcCostCodeFk = data.MdcCostCodeFk;
			}
			estAllMarkup2Costcode.GaPerc = data.MarkupGa;
			estAllMarkup2Costcode.RpPerc = data.MarkupRp;
			estAllMarkup2Costcode.AmPerc = data.MarkupAm;
			estAllMarkup2Costcode.DefMGcPerc = estAllMarkup2Costcode.FinMGc = data.MarkupDefMGcPerc;
			estAllMarkup2Costcode.DefMOp = estAllMarkup2Costcode.FinMOp	= data.MarkupDefMOp; 
			estAllMarkup2Costcode.CostCodeMainId = data.MdcCostCodeFk;

			if (data.EstAllowanceAreaFk != -1)
			{
				estAllMarkup2Costcode.EstAllowanceAreaFk = data.EstAllowanceAreaFk;
			}

			return estAllMarkup2Costcode;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		public IEnumerable<EstAllMarkup2costcodeEntity> Create(ReadData data)
		{
			List<int> deleteMdcCostCodeIds = new List<int>();

			var entities = new List<EstAllMarkup2costcodeEntity>();

			var allCostCodes = GetMasterCostCodeAndProjectCostCodeChildOnly(data.ProjectId, data.EstHeaderId, false, ref deleteMdcCostCodeIds, false).Flatten(e => e.CostCodeChildren);
			var costCode = allCostCodes.Where(e => e.Id == data.MdcCostCodeFk).FirstOrDefault();

			var levelcostcode = getEstAllMarkup2costcode(data);
			levelcostcode.CostCode = costCode.Code;
			entities.Add(levelcostcode);

			//create Est_ALL_MARKUP2COSTCODE
			if (costCode != null && costCode.CostCodeParentFk != null)
			{
				var parentMarkUpCCs = data.EstAllowanceAreaFk == -1 ? this.GetByFilter(e => e.EstAllowanceFk == data.MdcAllowanceFk && e.MdcCostCodeFk == costCode.CostCodeParentFk.Value)
									: this.GetByFilter(e => e.EstAllowanceFk == data.MdcAllowanceFk && e.MdcCostCodeFk == costCode.CostCodeParentFk.Value && e.EstAllowanceAreaFk == data.EstAllowanceAreaFk);

				if (!parentMarkUpCCs.Any())
				{
					var rootCostCode = GetRootParent(costCode, allCostCodes);

					if (rootCostCode.Id != levelcostcode.CostCodeMainId)
					{
						var parentMarkUpCC = getEstAllMarkup2costcode(data);
						parentMarkUpCC.IsCustomProjectCostCode = false;
						parentMarkUpCC.MdcCostCodeFk = rootCostCode.Id;
						parentMarkUpCC.CostCodeMainId = rootCostCode.Id;
						levelcostcode.CostCodeParentFk = rootCostCode.Id;
						entities.Add(parentMarkUpCC);
					}
				}
			}

			return entities;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="costCodes"></param>
		/// <param name="currentCostCode"></param>
		/// <param name="baseCostCodesFks"></param>
		/// <returns></returns>
		public ICostCodeEntity findParentNode(Dictionary<int, List<CostCodeEntity>> costCodes, ICostCodeEntity currentCostCode, List<int?> baseCostCodesFks)
		{
			if (currentCostCode.CostCodeParentFk == null)
			{
				return null;
			}

			if (baseCostCodesFks.Contains(currentCostCode.CostCodeParentFk.Value))
			{
				return costCodes[currentCostCode.CostCodeParentFk.Value].FirstOrDefault();
			}

			if (costCodes.ContainsKey(currentCostCode.CostCodeParentFk.Value))
			{
				var parentNode = costCodes[currentCostCode.CostCodeParentFk.Value].FirstOrDefault();

				return findParentNode(costCodes, parentNode, baseCostCodesFks);
			}

			return null;
		}


		/// <summary>
		/// 
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="estHeaderId"></param>
		/// <param name="isGetList"></param>
		/// <param name="deleteMdcCostCodeIds"></param>
		/// <param name="filterByType"></param>
		/// <returns></returns>
		public IEnumerable<CostCodeEntity> GetMasterCostCodeAndProjectCostCodeChildOnly(int projectId, int estHeaderId, bool isGetList, ref List<int> deleteMdcCostCodeIds, bool filterByType = true)
		{
			var basicCostCodeLogic = new BasicsCostCodesLogic();
			var basicCostCodesDictionary = basicCostCodeLogic.GetTreeList(new CostCodeTreeFilterOption()
			{
				OnlyGetEstTypeCodeCode = filterByType,
				FilterByCompany = true
			}).ToDictionary(e => e.Id, e => e);

			foreach (var costCode in basicCostCodesDictionary.Values)
			{
				costCode.CostCodeParent = null;
				costCode.CostCodeEntities_CostCodeLevel6Fk = null;
				costCode.CostCodeEntities_CostCodeLevel7Fk = null;
				costCode.CostCodeEntities_CostCodeLevel8Fk = null;

				costCode.CostCodeEntity_CostCodeLevel6Fk = null;
				costCode.CostCodeEntity_CostCodeLevel7Fk = null;
				costCode.CostCodeEntity_CostCodeLevel8Fk = null;
			}


			var projCoCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();

			var projectCostCodesJobRateLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesJobRateLogic>();

			var projectCostCodeDictionary = projCoCodeLogic.GetAllCostCodesInProject(projectId).ToDictionary(e => e.Id, e => e);

			var jobIdOfEstHeader = new EstimateMainHeaderLogic().GetItemById(estHeaderId).LgmJobFk;

			var jobRates = new List<IProjectCostCodesJobRateEntity>();

			if (jobIdOfEstHeader != null && jobIdOfEstHeader.HasValue)
			{
				jobRates = projectCostCodesJobRateLogic.GetItemsByProjectId(projectId).ToList();
				jobRates = jobRates.Where(e => e.LgmJobFk.HasValue && e.LgmJobFk == jobIdOfEstHeader).ToList();
			}
			var waitForMergeCusPrjCostCodeIds = new List<int>();
			var childAllowedPrjCostCodeIds = new List<int>();
			foreach (var item in projectCostCodeDictionary.Values)
			{
				var jobRate = jobRates.FirstOrDefault(e => e.ProjectCostCodeFk == item.Id);
				if (item.IsChildAllowed)
				{
					childAllowedPrjCostCodeIds.Add(item.Id);
				}
				// custom project cost code
				if (!item.MdcCostCodeFk.HasValue)
				{
					if (!item.CostCodeParentFk.HasValue || !projectCostCodeDictionary.ContainsKey(item.CostCodeParentFk.Value))
					{
						continue;
					}

					var parentPrjCostCode = projectCostCodeDictionary[item.CostCodeParentFk.Value];
					if (parentPrjCostCode.MdcCostCodeFk.HasValue && basicCostCodesDictionary.ContainsKey(parentPrjCostCode.MdcCostCodeFk.Value))
					{
						var parent = basicCostCodesDictionary[parentPrjCostCode.MdcCostCodeFk.Value];
						parent.CostCodeChildren.Add(CreateCostCodeByPrjCostCode(parent.Id, item, jobRate, true));
					}
					else
					{
						basicCostCodesDictionary.Add(item.Id, CreateCostCodeByPrjCostCode(parentPrjCostCode.Id, item, jobRate, true));
						waitForMergeCusPrjCostCodeIds.Add(item.Id);
					}
					continue;
				}

				if (!basicCostCodesDictionary.ContainsKey(item.MdcCostCodeFk.Value))
				{
					continue;
				}

				CostCodeEntity basicCostCode = basicCostCodesDictionary[item.MdcCostCodeFk.Value];
				basicCostCode.Rate = jobRate != null ? jobRate.Rate : item.Rate;
				SetCostCodeByPrjCostCode(basicCostCode, item, false);
			}

			foreach (var cusPrjCostCodeId in waitForMergeCusPrjCostCodeIds)
			{
				if (!basicCostCodesDictionary.ContainsKey(cusPrjCostCodeId))
				{
					continue;
				}
				var currentCostCode = basicCostCodesDictionary[cusPrjCostCodeId];
				if (!currentCostCode.CostCodeParentFk.HasValue || !basicCostCodesDictionary.ContainsKey(currentCostCode.CostCodeParentFk.Value))
				{
					continue;
				}
				var parentCostCode = basicCostCodesDictionary[currentCostCode.CostCodeParentFk.Value];
				parentCostCode.CostCodeChildren.Add(currentCostCode);
			}

			// Process old versions of error data to avoid errors
			foreach (var itemId in childAllowedPrjCostCodeIds)
			{
				if (projectCostCodeDictionary.ContainsKey(itemId))
				{
					var projectCostCode = projectCostCodeDictionary[itemId];
					if (!projectCostCode.MdcCostCodeFk.HasValue)
					{
						continue;
					}

					if (!basicCostCodesDictionary.ContainsKey(projectCostCode.MdcCostCodeFk.Value))
					{
						continue;
					}
					var basicCostCode = basicCostCodesDictionary[projectCostCode.MdcCostCodeFk.Value];
					foreach (var projectChildItem in projectCostCode.Children)
					{
						if (projectChildItem.MdcCostCodeFk.HasValue && basicCostCodesDictionary.ContainsKey(projectChildItem.MdcCostCodeFk.Value))
						{
							var jobRate = jobRates.FirstOrDefault(e => e.ProjectCostCodeFk == projectChildItem.Id);
							basicCostCode.CostCodeChildren.Add(CreateCostCodeByPrjCostCode(basicCostCode.Id, projectChildItem, jobRate, true));
							deleteMdcCostCodeIds.Add(projectChildItem.Id);
						}
					}
				}
			}
			var basicCostCodes = basicCostCodesDictionary.Values.Where(e => e.CostCodeParentFk == null).OrderBy(e => e.Code).ToList();
			return basicCostCodes;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="parentId"></param>
		/// <param name="item"></param>
		/// <param name="jobRate"></param>
		/// <param name="isCustomProjectCostCode"></param>
		/// <returns></returns>
		private CostCodeEntity CreateCostCodeByPrjCostCode(int parentId, IProjectCostCodesEntity item, IProjectCostCodesJobRateEntity jobRate = null, bool isCustomProjectCostCode = false)
		{
			return new CostCodeEntity
			{
				CostCodeParentFk = parentId,
				Rate = jobRate != null ? jobRate.Rate : item.Rate,
				Id = item.Id,
				Code = item.Code,
				IsLabour = item.IsLabour,
				Remark = item.Remark,
				DayWorkRate = item.DayWorkRate,
				RealFactorQuantity = item.RealFactorQuantity,
				FactorQuantity = item.FactorQuantity,
				RealFactorCosts = item.RealFactorCosts,
				FactorCosts = item.FactorCosts,
				IsRate = item.IsRate,
				DescriptionInfo = new DescriptionTranslateType() { Description = item.DescriptionInfo.Description, Translated = item.DescriptionInfo.Translated },
				Description2Info = new DescriptionTranslateType() { Description = item.Description2Info.Description, Translated = item.Description2Info.Translated },
				UomFk = item.UomFk,
				CurrencyFk = item.CurrencyFk,
				CostCodeTypeFk = item.CostCodeTypeFk,
				//Flag to differentiate Manually Added Project Cost Code
				IsCustomProjectCostCode = isCustomProjectCostCode,
				//set version, if not, the column search will wrong.
				Version = item.Version
			};
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="basicCostCode"></param>
		/// <param name="item"></param>
		/// <param name="isCustomProjectCostCode"></param>
		private void SetCostCodeByPrjCostCode(CostCodeEntity basicCostCode, IProjectCostCodesEntity item, bool isCustomProjectCostCode)
		{
			basicCostCode.IsLabour = item.IsLabour;
			basicCostCode.Remark = item.Remark;
			basicCostCode.DayWorkRate = item.DayWorkRate;
			basicCostCode.RealFactorQuantity = item.RealFactorQuantity;
			basicCostCode.FactorQuantity = item.FactorQuantity;
			basicCostCode.RealFactorCosts = item.RealFactorCosts;
			basicCostCode.FactorCosts = item.FactorCosts;
			basicCostCode.IsRate = item.IsRate;
			basicCostCode.DescriptionInfo = new DescriptionTranslateType() { Description = item.DescriptionInfo.Description, Translated = item.DescriptionInfo.Translated };
			basicCostCode.Description2Info = new DescriptionTranslateType() { Description = item.Description2Info.Description, Translated = item.Description2Info.Description };
			basicCostCode.UomFk = item.UomFk;
			basicCostCode.CurrencyFk = item.CurrencyFk;
			basicCostCode.CostCodeTypeFk = item.CostCodeTypeFk;
			//Flag to differentiate Manually Added Project Cost Code
			basicCostCode.IsCustomProjectCostCode = isCustomProjectCostCode;
		}

		/// <summary>
		/// Get Root Parent of the entity
		/// </summary>
		/// <returns></returns>
		public CostCodeEntity GetRootParent(CostCodeEntity entity, IEnumerable<CostCodeEntity> allCostCodes)
		{
			var currentEntity = entity;
			while (currentEntity.CostCodeParentFk != null)
			{
				var parent = allCostCodes.FirstOrDefault(e => e.Id == currentEntity.CostCodeParentFk);

				if (parent != null)
				{
					currentEntity = parent;
					continue;
				}

				break;
			}

			return currentEntity;
		}

		/// <summary>
		/// Creates a estimate structure detail entity
		/// </summary>
		/// <returns></returns>
		public List<EstAllMarkup2costcodeEntity> UpdateMajorCCostCode(ReadData data)
		{

			//filter IsEstimateCc costCode type
			var costCodeTypeLogic = new BasicsCustomizeCostCodeTypeLogic();
			var costCodeTypeIds = costCodeTypeLogic.GetByFilter(e => e.IsEstimateCc).Select(e => e.Id);
			if (!costCodeTypeIds.Any())
			{
				return null;
			}

			//create Est_ALL_MARKUP2COSTCODE
			var costCodes = new BasicsCostCodesLogic().GetTreeList(new CostCodeTreeFilterOption()
			{
				OnlyGetEstTypeCodeCode = true,
				FilterByCompany = true
			}).Where(e => !e.CostCodeParentFk.HasValue && e.CostCodeTypeFk.HasValue).ToDictionary(e => e.Id, e => e);

			if (data.estMarkupCostCodeIds != null)
			{
				foreach (var costCodeId in data.estMarkupCostCodeIds)
				{
					costCodes.Remove(costCodeId);
				}
			}
			var costCodeList = costCodes.Where(e => costCodeTypeIds.Contains(e.Value.CostCodeTypeFk.Value)).Select(e => e.Value);
			var count = costCodeList.Count();
			var entities = new List<EstAllMarkup2costcodeEntity>();

			if (count > 0)
			{
				var ids = new Stack<int>(SequenceManager.GetNextList<Int32>("EST_ALL_MARKUP2COSTCODE", count, "ID"));
				foreach (var costCode in costCodeList)
				{
					var item = new EstAllMarkup2costcodeEntity();
					item.Id = ids.Pop();
					item.MdcCostCodeFk = costCode.Id;
					item.CostCode = costCode.Code;
					item.GaPerc = data.MarkupGa;
					item.RpPerc = data.MarkupRp;
					item.AmPerc = data.MarkupAm;
					item.EstAllowanceFk = data.EstAllowanceFk;
					item.CostCodeParentFk = costCode.CostCodeParentFk;
					item.DefMOp = item.FinMOp = data.MarkupDefMOp;
					item.DefMGcPerc = item.FinMGc = data.MarkupDefMGcPerc;


					if (data.EstAllowanceAreaFk != -1)
					{
						item.EstAllowanceAreaFk = data.EstAllowanceAreaFk;
					}
					entities.Add(item);
				}
				this.Save(entities);
			}
			return entities;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="mdcAllMarkup2CostCodes"></param>
		/// <param name="allowanceEntity"></param>
		/// <param name="estAllMarkup2Costcodes"></param>
		/// <param name="estAllowanceAreasToSave"></param>
		public void ConvertEstAllMarkup2costcodeWhenCreateEstimate(List<MdcAllMarkup2CostCodeEntity> mdcAllMarkup2CostCodes, EstAllowanceEntity allowanceEntity, ConcurrentBag<EstAllMarkup2costcodeEntity> estAllMarkup2Costcodes, ConcurrentBag<EstAllowanceAreaEntity> estAllowanceAreasToSave = null)
		{

			var ids = new Stack<int>(this.SequenceManager.GetNextList("EST_ALL_MARKUP2COSTCODE", mdcAllMarkup2CostCodes.Count));

			foreach (var mdcCodeEntity in mdcAllMarkup2CostCodes)
			{
				EstAllMarkup2costcodeEntity estAllMarkup2CostcodeEntity = new EstAllMarkup2costcodeEntity();
				estAllMarkup2CostcodeEntity.Id = ids.Pop();
				estAllMarkup2CostcodeEntity.EstAllowanceFk = allowanceEntity.Id;
				estAllMarkup2CostcodeEntity.MdcCostCodeFk = mdcCodeEntity.MdcCostCodeFk;
				estAllMarkup2CostcodeEntity.GaPerc = mdcCodeEntity.MarkupGa;
				estAllMarkup2CostcodeEntity.RpPerc = mdcCodeEntity.MarkupRp;
				estAllMarkup2CostcodeEntity.AmPerc = mdcCodeEntity.MarkupAm;
		 
				estAllMarkup2CostcodeEntity.DefMOp = estAllMarkup2CostcodeEntity.FinMOp = mdcCodeEntity.MarkupDefMOp;
				estAllMarkup2CostcodeEntity.DefMGcPerc = estAllMarkup2CostcodeEntity.FinMGc = mdcCodeEntity.MarkupDefMGcPerc;


				if (mdcCodeEntity.MdcAllowanceAreaFk.HasValue && estAllowanceAreasToSave != null && estAllowanceAreasToSave.Count > 0)
				{
					var area = estAllowanceAreasToSave.FirstOrDefault(e => e.MdcAllowanceAreaFk == mdcCodeEntity.MdcAllowanceAreaFk || e.MdcAllowanceGcAreaFk == mdcCodeEntity.MdcAllowanceAreaFk);
					estAllMarkup2CostcodeEntity.EstAllowanceAreaFk = area != null ? area.Id : null;
				}
				estAllMarkup2Costcodes.Add(estAllMarkup2CostcodeEntity);
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public bool isExistedMarkupCostCode(IEnumerable<EstAllMarkup2costcodeEntity> estAllMarkup2CostcodeEntities, int costCodeId)
		{
			var checkExistedEnity = estAllMarkup2CostcodeEntities.Where(e => e.MdcCostCodeFk == costCodeId || e.Project2MdcCstCdeFk == costCodeId).FirstOrDefault();
			if (checkExistedEnity == null)
			{
				return false;
			}
			else
			{
				return true;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public EstAllMarkup2costcodeEntity GetTotalEntity()
		{
			return new EstAllMarkup2costcodeEntity()
			{
				Id = -2,
				CostCodeMainId = -2,
				CostCodes = null,
				DjcTotal = 0,
				GcTotal = 0,
				GaValue = 0,
				GcValue = 0,
				AmValue = 0,
				RpValue = 0,
				FmValue = 0,
				AllowanceValue = 0
			};
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		public IEnumerable<int> getIsEstimateCcCostCodeTypeIds()
		{
			//filter IsEstimateCc costCode type
			var costCodeTypeLogic = new BasicsCustomizeCostCodeTypeLogic();
			var costCodeTypeIds = costCodeTypeLogic.GetByFilter(e => e.IsEstimateCc).Select(e => e.Id);
			return costCodeTypeIds;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		public EstAllMarkup2CostCodeComplete GetAllMarkupByAllowance(EstAllowanceMarkupReadData data)
		{

			Stopwatch swl4 = new Stopwatch();
			Stopwatch swl3 = new Stopwatch();

			StringBuilder timeStr = new StringBuilder();

			swl3.Start();
			EstAllMarkup2CostCodeComplete estAllMarkup2CostCodeComplete = new EstAllMarkup2CostCodeComplete();
			EstAllMarkup2costcodeEntity rootEntity = GetTotalEntity();

			swl4.Start();

			var markupQuery = new List<EstAllMarkup2costcodeEntity>();

			if (data.EstAllowanceAreaFk == -1)
			{
				markupQuery = GetEstAllMarkup2costcodesByEstAllwance(data.EstAllowanceFk).ToList();
			}
			else if (data.EstAllowanceAreaFks != null && data.EstAllowanceAreaFks.Any())
			{
				markupQuery = GetByFilter(e => e.EstAllowanceFk == data.EstAllowanceFk && data.EstAllowanceAreaFks.Contains(e.EstAllowanceAreaFk.Value)).ToList();
			}
			else
			{
				markupQuery = GetByFilter(e => e.EstAllowanceFk == data.EstAllowanceFk && e.EstAllowanceAreaFk == data.EstAllowanceAreaFk).ToList();
			}


			var estAllMarkup2Costcodes = markupQuery.ToList();
			var costCodeFks = estAllMarkup2Costcodes.Select(e => e.MdcCostCodeFk).ToList();
			var estAllMarkup2CostcodesDic = estAllMarkup2Costcodes.ToDictionary(e => e.Id, e => e);

			swl4.Stop();
			timeStr.AppendLine("the First Part time of the function GetEstAllMarkup2costcodesByEstAllwance() and markupFk:------------>" + swl3.ElapsedMilliseconds + " ms");
			swl4.Restart();

			var deleteMdcCostCodeIds = new List<int>();
			var costCodes = GetMasterCostCodeAndProjectCostCodeChildOnly(
				 data.ProjectId, data.EstHeaderId, true, ref deleteMdcCostCodeIds, false);

			swl4.Stop();
			timeStr.AppendLine("the First Part time of the function GetMasterCostCodeAndProjectCostCodeChildOnly():------------>" + swl4.ElapsedMilliseconds + " ms");
			swl4.Restart();

			var flattenDict = costCodes.Flatten(e => e.CostCodeChildren)
												.GroupBy(e => e.Id)
												.ToDictionary(g => g.Key, g => g.ToList());

			swl4.Stop();
			timeStr.AppendLine("the First Part time of the function Flatten():------------>" + swl4.ElapsedMilliseconds + " ms");
			swl4.Restart();

			var rootCostCodeId = rootEntity.CostCodeMainId;
			foreach (var item in estAllMarkup2Costcodes)
			{
				var itemCostCodeFk = item.MdcCostCodeFk ?? item.Project2MdcCstCdeFk;
				if (itemCostCodeFk == null) { continue; }

				if (!flattenDict.TryGetValue(itemCostCodeFk.Value, out var costCodeGroup))
				{
					costCodeFks.Remove(itemCostCodeFk.Value);
					estAllMarkup2CostcodesDic.Remove(item.Id);
					continue;
				}
				var currentCostCodes = costCodeGroup;
				if (currentCostCodes.Count == 0) { continue; }

				var cd = currentCostCodes.Count == 1
					 ? currentCostCodes[0]
					 : item.Project2MdcCstCdeFk.HasValue
						  ? currentCostCodes.Find(e => e.IsCustomProjectCostCode)
						  : currentCostCodes.Find(e => !e.IsCustomProjectCostCode);

				if (cd != null)
				{

					var targetItem = estAllMarkup2CostcodesDic[item.Id];
					targetItem.CostCodeMainId = cd.Id;
					targetItem.CostCode = cd.Code;

					targetItem.CostCodeParentFk = findParentNode(flattenDict, cd, costCodeFks)?.Id ?? rootCostCodeId;

					if (cd.IsCustomProjectCostCode)
					{
						targetItem.MdcCostCodeFk = cd.Id;
						targetItem.IsCustomProjectCostCode = true;
					}
				}

				rootEntity.DjcTotal += item.DjcTotal;
				rootEntity.GcTotal += item.GcTotal;
				rootEntity.GaValue += item.GaValue;
				rootEntity.GcValue += item.GcValue;
				rootEntity.AmValue += item.AmValue;
				rootEntity.RpValue += item.RpValue;
				rootEntity.FmValue += item.FmValue;
				rootEntity.AllowanceValue += item.AllowanceValue;
				rootEntity.DjcTotalOp += item.DjcTotalOp;
				rootEntity.GaDjcValue += item.GaDjcValue;
				rootEntity.GcDjcValue += item.GcDjcValue;
				rootEntity.AmDjcValue += item.AmDjcValue;
				rootEntity.RpDjcValue += item.RpDjcValue;
				rootEntity.FmDjcValue += item.FmDjcValue;
				rootEntity.AllowanceDjcValue += item.AllowanceDjcValue;
			}

			swl4.Stop();
			timeStr.AppendLine("the First Part time of the foreach:------------>" + swl4.ElapsedMilliseconds + " ms");

			var dtos = estAllMarkup2CostcodesDic?.Values.ToList();
			estAllMarkup2CostCodeComplete.costCodes = data.isReturnCostCodes ? costCodes : null;
			estAllMarkup2CostCodeComplete.dtos = dtos;
			estAllMarkup2CostCodeComplete.totalEntity = rootEntity;

			swl3.Stop();
			timeStr.AppendLine("the total Part time of the logic:------------>" + swl3.ElapsedMilliseconds + " ms");

			estAllMarkup2CostCodeComplete.timeStr = timeStr.ToString();
			return estAllMarkup2CostCodeComplete;
		}
	}
}
