import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _boqPage, _common, _materialPage, _package, _procurementContractPage, _procurementPage, _projectPage, _schedulePage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const PRICE_CONDITION_CODE_1 = _common.generateRandomString(4);
const PRICE_CONDITION_CODE_2 = _common.generateRandomString(4);
const PRICE_CONDITION_CODE_3 = _common.generateRandomString(4);
const DATA_RECORD_DESC_1 = _common.generateRandomString(4);
const DATA_RECORD_DESC_2 = _common.generateRandomString(4);
const DATA_RECORD_DESC_3 = _common.generateRandomString(4);
const CONTRACT_DESCRIPTION_1 = _common.generateRandomString(4);
const PROCUREMENT_BOQ_NAME = _common.generateRandomString(4);
const PRICE_CONDITION_DESC_1 = _common.generateRandomString(4);
const PRICE_CONDITION_DESC_2 = _common.generateRandomString(4);
const PKG_ITEM_DESC_1 = _common.generateRandomString(4);
const PKG_ITEM_DESC_2 = _common.generateRandomString(4);
const PKG_ITEM_DESC_3 = _common.generateRandomString(4);

let CONTAINERS_DATA_RECORDS, CONTAINERS_CONTRACT, CONTAINERS_BOQ_STRUCTURE;
let PROCUREMENT_CONTRACT_PARAMETER: DataCells, CONTRACT_BOQ_PARAMETER: DataCells;
let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_PROCUREMENT_BOQ, CONTAINER_COLUMNS_BOQ_STRUCTURE;

describe('PCM- 4.125 | Boq price condition container in contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/con-4.125-boq-price-condition-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINERS_DATA_RECORDS = this.data.CONTAINERS.DATA_RECORDS
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }
            CONTAINER_COLUMNS_PROCUREMENT_BOQ = this.data.CONTAINER_COLUMNS.PROCUREMENT_BOQ
            CONTAINER_COLUMNS_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.BOQ_STRUCTURE
            CONTRACT_BOQ_PARAMETER = {
                [commonLocators.CommonLabels.PROCUREMENT_STRUCTURE_SMALL]: commonLocators.CommonKeys.MATERIAL,
                [commonLocators.CommonLabels.SUB_PACKAGE]: commonLocators.CommonKeys.MATERIAL,
                [commonLocators.CommonLabels.OUTLINE_SPECIFICATION]: PROCUREMENT_BOQ_NAME
            }
            CONTAINERS_BOQ_STRUCTURE = this.data.CONTAINERS.BOQ_STRUCTURE
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it("TC - Precondition : create price condition types", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART).search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING)
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ENTITY_TYPES, app.FooterTab.DATA_TYPES, 0)
        })
        _common.search_inSubContainer(cnt.uuid.ENTITY_TYPES, CommonLocators.CommonKeys.PRICE_CONDITION_TYPE)
        cy.REFRESH_CONTAINER()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.ENTITY_TYPES, app.GridCells.NAME, CommonLocators.CommonKeys.PRICE_CONDITION_TYPE)
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.INSTANCES, app.FooterTab.DATA_RECORDS, 2)
        })
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.DATA_RECORDS)
        _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
        _common.set_cellCheckboxValueForAllRowCell(cnt.uuid.DATA_RECORDS, app.GridCells.IS_DEFAULT, commonLocators.CommonKeys.UNCHECK)
        cy.SAVE()
        _common.create_newRecord(cnt.uuid.DATA_RECORDS)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PRICE_CONDITION_CODE_1)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, DATA_RECORD_DESC_1)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_DATA_RECORDS.VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.FORMULA_SMALL, app.InputFields.DOMAIN_TYPE_REMARK, CONTAINERS_DATA_RECORDS.FORMULA[0])
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.HAS_TOTAL, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_PRINTED, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.HAS_VALUE, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_PRICE_COMPONENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_SHOW_IN_TICKET_SYSTEM, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_LIVE, commonLocators.CommonKeys.CHECK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.DATA_RECORDS)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PRICE_CONDITION_CODE_2)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, DATA_RECORD_DESC_2)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_DATA_RECORDS.VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.FORMULA_SMALL, app.InputFields.DOMAIN_TYPE_REMARK, CONTAINERS_DATA_RECORDS.FORMULA[0])
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.HAS_TOTAL, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_PRINTED, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_PRICE_COMPONENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_SHOW_IN_TICKET_SYSTEM, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_LIVE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.DATA_RECORDS)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PRICE_CONDITION_CODE_3)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, DATA_RECORD_DESC_3)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_DATA_RECORDS.VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.FORMULA_SMALL, app.InputFields.DOMAIN_TYPE_REMARK, CONTAINERS_DATA_RECORDS.FORMULA[0])
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.HAS_TOTAL, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_PRINTED, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.HAS_VALUE, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_PRICE_COMPONENT, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_SHOW_IN_TICKET_SYSTEM, commonLocators.CommonKeys.CHECK)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_LIVE, commonLocators.CommonKeys.CHECK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.DATA_RECORDS)
    })

    it('TC - Create new contract', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('projectName')).pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESCRIPTION_1)
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, CONTRACT_DESCRIPTION_1).pinnedItem()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Creation of procurement BoQ and BoQ structure for the selected contract', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 1)
        });
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.waitForLoaderToDisappear()
        _package.create_ProcuremenBoQswithNewReocrd_in_Requisition_module(CONTRACT_BOQ_PARAMETER)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 1)
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQS, CONTAINER_COLUMNS_PROCUREMENT_BOQ)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQS, PROCUREMENT_BOQ_NAME)
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT_BOQS, PROCUREMENT_BOQ_NAME)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROCUREMENTCONTRACTBOQ).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 3)
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINER_COLUMNS_BOQ_STRUCTURE)
            _common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.quantity, CONTAINER_COLUMNS_BOQ_STRUCTURE.briefinfo, CONTAINER_COLUMNS_BOQ_STRUCTURE.basuomfk, CONTAINER_COLUMNS_BOQ_STRUCTURE.price, CONTAINER_COLUMNS_BOQ_STRUCTURE.prcpriceconditionfk], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _boqPage.enterRecord_toCreateBoQStructureUnderpackage(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, PKG_ITEM_DESC_1, CONTAINERS_BOQ_STRUCTURE.QUANTITY, CONTAINERS_BOQ_STRUCTURE.UNIT_RATE, CONTAINERS_BOQ_STRUCTURE.UOM)
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Verify BoQ price condition types value in contract module", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.FooterTab.BOQ_PRICE_CONDITION, 2)
        })
        _common.clear_subContainerFilter(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.create_newRecord(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, DATA_RECORD_DESC_1)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.select_rowHasValue(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, DATA_RECORD_DESC_1)
        _common.assert_forNumericValues(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.GridCells.VALUE, "20")
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.create_newRecord(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, DATA_RECORD_DESC_2)
        _common.select_activeRowInContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, DATA_RECORD_DESC_2)
        _common.clickOn_activeRowCell(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.GridCells.VALUE)
        _validate.verify_isRecordNotEditable(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.GridCells.VALUE, 1)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, DATA_RECORD_DESC_3)
        _common.select_activeRowInContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, DATA_RECORD_DESC_3)
        _common.enterRecord_inNewRow(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, "30")
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.select_rowHasValue(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, DATA_RECORD_DESC_3)
        _common.set_cellCheckboxValue(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.GridCells.IS_ACTIVATED, commonLocators.CommonKeys.UNCHECK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.select_rowHasValue(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, DATA_RECORD_DESC_3)
        _common.delete_recordFromContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, CONTRACT_DESCRIPTION_1).pinnedItem()
        _common.openTab(app.TabBar.CONTRACT).then(function () {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ)
        });
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.openTab(app.TabBar.CONTRACT).then(function () {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE)
        });
        _common.clickOn_cellHasIcon(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.TREE, app.GridCellIcons.ICO_BOQ_ITEM)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.FooterTab.BOQ_PRICE_CONDITION, 2)
        })
        _common.waitForLoaderToDisappear()
        _validate.verify_recordNotPresentInContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, DATA_RECORD_DESC_3)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create price condition in price condition module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PRICE_CONDITION)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 0)
        });
        _common.create_newRecord(cnt.uuid.PRICE_CONDITION)
        _common.enterRecord_inNewRow(cnt.uuid.PRICE_CONDITION, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, PRICE_CONDITION_DESC_1)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PRICE_CONDITION_DETAILS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_DETAILS, app.GridCells.PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, DATA_RECORD_DESC_1)
        _common.create_newRecord(cnt.uuid.PRICE_CONDITION_DETAILS)
        _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_DETAILS, app.GridCells.PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, DATA_RECORD_DESC_2)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PRICE_CONDITION)
        _common.enterRecord_inNewRow(cnt.uuid.PRICE_CONDITION, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, PRICE_CONDITION_DESC_2);
        _common.create_newRecord(cnt.uuid.PRICE_CONDITION_DETAILS)
        _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_DETAILS, app.GridCells.PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, DATA_RECORD_DESC_1)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it('TC - Verify create new BoQ structure item and assign boq price condition to it', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, CONTRACT_DESCRIPTION_1).pinnedItem()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1)
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, PKG_ITEM_DESC_2)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.PRC_PRICE_CONDITION_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, PRICE_CONDITION_DESC_1)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.FooterTab.BOQ_PRICE_CONDITION, 1)
        })
        _common.clear_subContainerFilter(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _validate.verify_isRecordPresent(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, DATA_RECORD_DESC_1)
        _validate.verify_isRecordPresent(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, DATA_RECORD_DESC_2)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1)
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.GridCells.BRIEF_INFO_SMALL, app.InputFields.DOMAIN_TYPE_TRANSLATION, PKG_ITEM_DESC_3)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, app.FooterTab.BOQ_PRICE_CONDITION, 1)
        })
        _common.clear_subContainerFilter(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT)
        _package.search_inLookupSubContainer(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, PRICE_CONDITION_DESC_2)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.BOQ_PRICE_CONDITION_CONTRACT, DATA_RECORD_DESC_1)
        _common.waitForLoaderToDisappear()
    })
});