{"productionplanning": {"item": {"gantt": "<PERSON><PERSON><PERSON>", "planningBoardTitle": "Planning Board", "barChart": "Chart", "barChartTitle": "Weeks - Sites - Status", "createItemTitle": "Create Production Unit", "entityItem": "Production Unit", "entityProductDescription": "Product-Description", "entityDescriptionParam": "Description-Parameter", "entityTranslation": "Translations", "translationDescItem": "Production Unit Description", "listTitle": "Production Units", "treeTitleByJob": "Production Units Structure By Job", "listTitleByJob": "Production Units By Job", "treeTitle": "Production Units Structure", "detailTitle": "Production Unit Detail", "structure": "Production Units Filter", "productTemplateListTitle": "Planning Unit: Product Templates", "productTemplateDetailTitle": "Planning Unit: Product Template Detail", "ppsProductTemplateCode": "Product Template Code", "productdescriptionDocumentListTitle": "Product Template: Documents", "productdescriptionDocumentRevisionListTitle": "Product Template Document: Revision", "descriptionDialogTitle": "Assign Product Template", "productdescparamListTitle": "Product Template Params", "productdescparamDetailTitle": "Product Template Param Detail", "headerEventTitle": "Header Event", "itemEventTitle": "Production Unit Event", "descriptionEventTitle": "Product Template Event", "productEventTitle": "Product Event", "bundleListTitle": "Production Unit: Bundles", "projectLocationListTitle": "Project Location Filter for PU by Job", "ppsItemCostGroupListTitle": "Production Unit Event: Cost Groups", "ppsProductCostGroupListTitle": "Product Event: Cost Groups", "eventsEndDate": "Events End Date", "splitAfterCreation": "Split After Creation", "businessPartnerOrder": "Business Partner Contract", "baseGroup": "Basic Data", "userDefDateTimeGroup": "User-Defined Date Times", "entityUserDefinedDateTime": "Date Time {{p_0}}", "headerFk": "Header", "itemFk": "ItemFk", "itemStausFk": "Production Unit Status", "clerkFk": "Clerk", "siteFk": "Site", "materialGroupFk": "Material Group", "resTypeFk": "Resource Type", "prjLocationFk": "Project Location", "isLive": "IsLive", "productDescription": "Product Template", "defaultDrawing": "De<PERSON>ult Drawing", "drawingStatus": "Drawing Status", "statusBackgroundColor": "Color", "plannedStart": "Planned Start", "plannedFinish": "Planned Finish", "materialFk": "Material", "materialDescription": "Material-Description", "uomFk": "UOM", "quantity": "Quantity", "assignedQuantity": "Assigned Quantity", "dimensions": "Dimensions", "length": "Length", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "weight": "Weight", "area": "Area", "productdescParam": "Product Template Param", "variableName": "Variable Name", "sorting": "Sorting", "deleteItemTitle": "Delete Complete Production Unit", "deleteQuestion": "Do you really want to delete the whole Production Unit with all dependant data?", "alreadyDelete": "The selected Production Unit is already deleted.", "deleteFailed": "The Production Unit can't be deleted!", "wizard": {"wizardGroupname1": "Production Unit Wizards", "disableItemTitle": "Disable Production Unit", "enableDisableItemDone": "The field 'Is Live' of the Production Unit ({{item}}) has been changed.<br>", "itemAlreadyDisabled": "Production Unit ({{item}}) is already disabled", "enableItemTitle": "Enable Production Unit", "itemAlreadyEnabled": "Production Unit ({{item}}) is already enabled", "changeItemStatus": "Change Production Unit Status", "changeSubItemStatus": "Change Sub Planning Unit Status", "noSelectedWarn": "Please select a Production Unit first", "haveChildWarn": "Selected Production Unit(s) have children", "haveParentWarn": "Selected Production Unit(s) have parent", "noPpsHeaderWarn": "No PPS Header on this item.", "wrongStatesWarn": "Selected Units have wrong status", "moreItemsWarn": "At least select two Production Units", "diffMaterialGroupWarn": "MaterialGroup should be found in current material-Context and they must be same or as the higher level", "diffMaterialWarn": "Material must be same", "diffProductDescriptionWarn": "Product-descriptions must be same", "diffEventSequenceWarn": "Event sequences must be same (and Not empty)", "wrongOrderWarn": "Events sequence are in wrong order", "higherMaterialGroupWarn": "Only select higher level Material Group", "notSame": "The planning units must have the same PPS Header.", "itemSplit": {"splitDialogTitle": "Split Production Unit", "copyDialogTitle": "Copy Planning Unit", "config": "Configuration", "locationAssign": "Location Assign", "splitFactor": "SplitFactor", "splitFrom": "Split From", "splitTo": "Split To", "intervalDay": "Interval Day", "asChild": "As Child", "materialGroup": "Material Group", "quantity": "Quantity", "remainQuantity": "<PERSON><PERSON><PERSON> Quantity", "description": "Description", "site": "Site", "mainItemTitle": "Parent Planning Unit", "locationTitle": "Location", "createNewBtn": "Create New", "createSubBtn": "Create Sub", "moveUp": "Move Up", "moveDown": "Move Down", "copyNumber": "Number of copies", "fakeLocationCode": "No Location", "fakeLocationDesc": "No Location Assigned"}, "itemMerge": {"mergeDialogTitle": "Merge Planning Units", "mergeHint": "Select the Planning-Unit to keep"}, "itemGroup": {"groupDialogTitle": "Group Planning Units", "eventSeqTitle": "Event Sequence", "notSameTitle": "Different PPS Header", "match": "Match", "groupBefore": "Group Before", "groupAfter": "Group After", "sumQuantity": "Sum Quantity"}, "reproduction": {"reproductionDlgTitle": "Reproduction", "reason": "Reason", "deliveryStart": "Delivery Start", "reproduce": "Reproduce", "stack": "<PERSON><PERSON>", "element": "Element", "width": "<PERSON><PERSON><PERSON>", "length": "Length", "newLength": "New Length", "newWidth": "New Width", "alertTitle": "Note", "noFittingHeader": "No fitting header found, products scraping deactivated", "msgCreatedSuccessfully": "Reusable products successfully created as bellow:", "msgAllCreatedSuccessfully": "All reusable products created,", "msgSomeCreatedFailed": "Some Resuable products failed to be created for missing fitting Planning Unit", "msgAllCreatedFailed": "All Resuable products failed to be created for missing fitting Planning Unit"}, "multishift": {"dialogTitle": "Multishift", "shiftDays": "Shift Days", "push": "<PERSON><PERSON>", "pull": "<PERSON><PERSON>", "type": "Type", "confirmResult": "Confirm Shift Result", "shiftedEvents": "Shifted Events", "setShiftDistance": "Set Shift Distance", "completionTitle": "Multishift Complete", "completionText": "Multishift for PPS Header saved!", "productionPlanning": "Productionplanning", "daysToShift": "Days to shift", "tolerance": "Tolerance", "toleranceInDays": "Tolerance in Days", "loading": "Loading", "calculating": "Calculating"}, "upstreamItemPrcPackage": {"dialogTitle": "Create Procurement Package for Upstream Items", "noUpstreamSelection": "Please select one or more eligible Upstream Items!", "resultMsg": "Package ({{pkg}}) was created/updated", "goToPackage": "Go to Package"}, "actualTimeRecording": {"title": "Time Assignment", "employeeAreaActionAssgnStepTitle": "Employee / Area / Action - Assignment", "employee": "Employee", "area": "Area", "overallTime": "Overall Time", "assignedTime": "Assigned Time", "remainingTime": "Remaining Time", "filterBtn": "Show only available records", "employeesTitle": "Employees", "employeeToAreasTitle": "Employee To Areas", "areasTitle": "Areas", "areaToEmployeesTitle": "Area To Employees", "timeSymbolIdNotSet": "Please set a valid value for wizard parameter Time Symbol Id first", "prodAssgnStepTitle": "Product Assignment", "prodAssgnCheckingTitle": "Product Assignment Checking", "finishProdAssgnUpdate": "Product assignments have been updated.", "correction": "Correction", "perActionAreasGridTitle": "Actuals per Action & Areas"}, "billingDataOfProductAndMaterialSelection": {"dialogTitle": "Billing Data Of Product Selection", "wizParamsNotSet": "Please set valid values for wizard parameters CreateWIPorBill and mode at first", "selectingOrPinningPUIsRequired": "Please select Planning Unit(s) or pin a Planning Unit at first.", "ppsHeadersIsNotTheSame": "Currently, only support selecting Planning Units within one project and one Production Planning.", "selectingProductIsRequired": "Please select Product(s) at first.", "ppsHeadersIsNotTheSameForProduct": "Currently, only support selecting Products whose Planning Units within one project and one Production Planning.", "createWIP": "Create WIP", "done": "Done", "warning": "Warning", "createWipSucceed": "Create WIP successfully, WIP Codes are:", "createBillSucceed": "Create BILL successfully, Bill Codes are:", "nothingBilled": "Nothing is billed!"}, "drawingComponent": {"enableComponent": "Enable Component", "disableComponent": "Disable Component", "enableComponentDone": "Enable Component successful.", "disableComponentDone": "Disable Component successful.", "componentAlreadyEnabled": "Component is already enabled.", "componentAlreadyDisabled": "Component is already disabled."}, "preliminaryPUCheck": "Preliminary Planning Unit Check", "cannotExecuteWizardForPreliminaryPU": "Wizard can't be executed due to selected Planning Unit(s) ({{item}}) are preliminary Planning Unit(s)."}, "validation": {"errors": {"uniqCode": "The Code should be unique", "uniqName": "The VariableName should be unique", "matAndMatGrpAreNotMatched": "Material and material group mismatch.", "matMatGrpAndSiteAreNotCompatible": "Current site is not able to produce specific material/material-group, please check event sequence configurations.", "cannotChangeSiteIfTransactionIsCreated": "The Site is not allowed to be changed if there are transaction(s) created.", "cannotSelectMaterialIfItIsnotProduct": "The Material without 'IsProduct' marked as true is not allowed."}, "info": {"upstreamIsExisting": "Attention: Site Change can cause issues if upstream items are existing."}}, "document": {"documentListTitle": "Documents", "itemDocumentListTitle": "Production Unit Documents", "drawingDocumentListTitle": "Engineering Drawing Documents", "prodTemplateDocumentListTitle": "Product Template Documents", "ppsDocumentForEngDrwAndProdTmplTitle": "Drawing & Template Documents", "revision": {"gridTitle": "Production Unit Document: Revisions", "drawingGridTitle": "Engineering Drawing Document: Revisions", "prodTemplateGridTitle": "Product Template Document: Revisions", "drawingAndProdTmplGridTitle": "Drawing & Template Document: Revisions"}}, "formData": {"belonging": "Belonging", "parentUnit": "Parent Unit", "currentUnit": "Current Unit"}, "customGroup": "Custom Group", "clerkListTitle": "Clerks", "customClerkGroup": "Custom Clerk Group", "entityProductionOrder": "Production Order", "entityItemType": "Production Unit Type", "creation": {"alertTitle": "Note", "noMappedSeqConfig": "No sequence config was found, no status", "noStatus": "No status could be found", "noDefaultStatusWithRubCat": "No default status with specific rubric category", "noHeaderForSelectedJob": "No header found for the selected logistic job"}, "reassignedProductTitle": "To be Reassigned Products", "taskListTitle": "Production Unit: Engineering Tasks", "detailerTaskTitle": "Detailer Tasks (4 weeks)", "detailerTaskSummaryTitle": "Detailer Tasks Summary (4 weeks)", "startingDate": "Starting Date", "detailersTitle": "Detailers", "role": "Role", "assign": "Assign", "assignDetailerWarningTitle": "Duplicate Assignment", "assignDetailerWarningBody": "The assignment had been done before!", "onlyShowLeaf": "Only show leaves", "applyFilter": "Apply filter", "userDefinedIcon": "User Defined Icon", "readOnlyUserDefinedIcon": "User Defined Icon (readonly)", "editEventQuantityDialogTitle": "Edit Event Quantity", "editEventQuantityGridTitle": "Events", "drawingcode": "Drawing Code", "reference": "Reference", "upstreamItem": {"listTitle": "Upstream Requirements", "entity": "Upstream Requirement", "ppsUpstreamTypeFk": "Upstream Type", "createUpstreamItemTitle": "Create Upstream Item", "ppseventreqfor": "Required For", "ppsupstreamgoodstype": "Upstream Goods Type", "upstreamresult": "Upstream Result", "upstreamgoods": "Upstream Goods", "upstreamresultstatus": "Status Upstream", "availableQuantity": "Available Quantity", "openQuantity": "Open Quantity", "upstreamResultDesc": "Upstream Result Description", "upstreamGoodsDesc": "Upstream Goods Description", "document": {"listTitle": "Upstream Requirement: Documents"}, "prcPkg": "Procurement Package", "belonging": "Belonging", "parentUnit": "Parent Unit", "currentUnit": "Current Unit", "splitQuantity": "Split Quantity", "remainingQuantity": "Remaining Quantity", "TrsAssignedQuantity": "Transport Assigned Quantity", "TrsOpenQuantity": "Transport Open Quantity", "ppsUpstreamItemFk": "Parent Upstream Item", "ppsEventtypeReqforFk": "Required For Type", "split": "UpstreamItem Split", "selection": "UpstreamItem Selection", "allocation": "Quantity Allocation", "upstreamItemQuantity": "UpstreamItem Quantity", "wrongSelect": "PU ({{Code}}) is Invalid", "splitSuccess": "UpstreamItem Split Successfully", "onlyShowCurrentUpstreams": "Only show Upstream Requirements of selected Planning Unit", "productTemplateDetail": "Upstream Requirement: Product Template Detail", "isForTransport": "For Transport", "isImported": "Is Imported", "planningGroup": "Planning", "dueDate": "Due Date", "checkIfStockAvailable": "Check if there is any available stock for material({{materialCode}}), but {{reason}}.", "transactionInfo": "Transaction Info"}, "splitUpstreamItem": {"listTitle": "Split Upstream Requirements"}, "ppsItem2MdcMaterial": {"listTitle": "Upstream Requirement: Materials", "entity": "Upstream Requirement Material", "createTooltip": "New Record \n only enable when the upstream type is \"production\" with assigned upstream result"}, "itemEventLogListTitle": "Planning Unit: Event Logs", "itemLogListTitle": "Planning Unit: Logs", "itemLogPinboardTitle": "Planning Unit: Logs (Pinboard)", "printChart": "Print Chart", "formDataLisTitle": "Planning Unit: Form Data", "upstreamFormDataListTitle": "Planning Unit: Upstream Result Form Data", "ppsItemFormDataLisTitle": "Form Data for PPS Item", "projectFormDataLisTitle": "Form Data for Project", "upstreamFormDataLisTitle": "Upstream Requirement: Form Data", "products": {"productsGroup": "Products", "productsCount": "Products Count", "productsWeightSum": "Products Weight Sum", "productsAreaSum": "Products Area Sum", "productsVolumeSum": "Products Volume Sum", "productsBillingQtySum": "Products Billing Quantity Sum", "productsPlanQtySum": "Products Planning Quantity Sum"}, "materialPreviewTitle": "Material Preview", "productDescParameterTitle": "Product Template: Parameters", "productParameterTitle": "Product: Parameters", "catalogTemplateParameterTitle": "Catalog Template: Parameters", "salecontractCharacteristicTitle": "Sales Contract: Characteristics", "ppsHeaderCharacteristicTitle": "PPS Header: Characteristics", "sourceListTitle": "Sources", "PpsEventSeqConfigFk": "Event Sequence Configuration", "userflag1": "Userflag1", "userflag2": "Userflag2", "detailerPlanningBoardTitle": "Detailer Planning Board", "stateInfoGroup": "State Information", "isUpstreamDefined": "Upstream State", "upstreamState": {"none": "None", "inherited": "Inherited", "linked": "Linked"}, "isTransportPlanned": "Transport State", "reassignProduct": {"errorNotProductionsetFound": "Cannot reassign bundles or products to Planning Unit {0}. Planning Unit {0} doesn't have relative Production Set."}, "listHeader2BpTitle": "PPS Header Partners", "listHeader2BpContactTitle": "PPS Header Partner Contacts", "listBizPartnerTitle": "Business Partners", "listBizpPartnerContactTitle": "Business Partner Contacts", "moveToRoot": "Move to Root", "moveToSelected": "Move to Selected Planning Unit", "createManually": "Create Manually", "transportables": "Planning Unit: Transportables", "dailyProduction": {"listTitle": "Production Set: Daily Production", "fullyCovered": "Fully Covered", "isAssigned": "Is Assigned", "supplier": "Supplier", "planQty": "Plan Quantity", "realQty": "Actual Quantity", "difference": "Difference", "createSubSet": "Create SubSet", "updateSubSet": "Update SubSet", "createSiblingSubSets": "Split Unassign", "updateSiblingSubSets": "Update Sibling SubSets", "splitNumber": "Split Number", "splitUnassign": "Split Unassign", "errorDataMsg": "Plan records have invalid Supplier and Plan Quantity", "errorSupplier": "Plan records have invalid Supplier", "noSiblingError": "No sibling productionSets to update", "unassignOnly": "Only allow to split Unassign subsets", "dailyPlanningBoardTitle": "Daily Planning Board", "dailyplanningboard": {"siteFilterListTitle": "Daily Planning Board: Site Filter"}, "fabricationProduct": "Fabrication Products", "allProducts": "All products", "sumQty": "Sum Quantity", "sumPlanQty": "Sum Plan Quantity", "sumArea": "Sum Area", "sumVolume": "Sum Volume", "plannedResidual": "Plan Quantity - Actual Quantity", "number": "Number", "deliveryDate": "Delivery Date", "lockedDate": "Locked Date", "lockedQty": "Locked Quantity", "lockedDateAndQty": "Locked Date And Quantity", "nested": "Nested", "assigned": "Assigned", "unassigned": "Unassigned", "noChanged": "No Changed", "noState": "No State", "productionStatus": "Production Status", "deleteAssignments": "Delete Assignments", "changingStatus": "Changing Status to", "unnestedQty": "Unnested Planned", "mesAndUnnestedQty": "MES Planned + Unnested Planned"}, "productCharacteristicTitle": "Product: Characteristics", "productCharacteristic2Title": "Product: Characteristics2", "rootItemPinboardTitle": "RootItem PinBoard", "listTitleSubItem": "PPS Sub Items", "selectProjectLocation": {"dialogTitle": "Select Project Location", "quantityPercent": "Quantity %"}, "productionOverview": {"listTitle": "Planning Unit: Production Overview", "plannedProduction": "Planned Production", "plannedDelivery": "Planned Delivery"}, "drawingComponent": {"listTitle": "Planning Unit: Components"}, "productDescCharacteristicTitle": "Product Template Characteristics", "productDescCharacteristic2Title": "Product Template Characteristics2", "createSubPUDialog": "Create Sub Planning Unit", "noNegativeQuantity": "The Quantity should not be Negative", "productCreationNumber": "Product Creation Number", "lockContainer": "Container Locked", "focusContainer": "If highlighted, the container is focus for selections", "createSubPUs": "Create Sub-Items", "sharedDrawingDisableSelect": "Drawing can not be selected for the EngTask is shared by parent.", "source": {"estHeader": "Estimate Header", "estLineItem": "Estimate Line Item", "estResource": "Estimate Resource", "PpsPlannedQuantityFk": "Planned Quantity"}, "highlightAssignment": "Highlight Assignment", "pinForMarkups": "Pin Selected Product(s) For Markups", "productStacker": {"bundlesOfJobTitle": "Bundles of Job", "jobBundleProductListTitle": "Bundle of Job: Products", "bundleUniqCodeErrorByDrw": "The code should be unique over all entities of the same drawing of stack", "bundleUniqCodeErrorByJob": "The code should be unique over all entities of the same Job"}, "productLookupTitle": "Unbundled Product Lookup", "refreshForUpdate": "Please refresh the containers for concurrent data may fail to save!"}}}