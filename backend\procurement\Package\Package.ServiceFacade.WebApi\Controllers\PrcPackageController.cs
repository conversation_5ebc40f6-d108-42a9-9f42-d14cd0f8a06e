/*
 * $Id: PrcPackageController.cs 632361 2021-04-15 11:21:15Z wed $
 * Copyright (c) RIB Software SE
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Web.Http;
using System.Web.Http.ModelBinding;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Common.ServiceFacade.WebApi.Final;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.LookupData.ServiceFacade.WebApi;
using RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.ServiceFacade.WebApi;
using RIB.Visual.Procurement.Common.BusinessComponents;
using RIB.Visual.Procurement.Common.Core;
using RIB.Visual.Procurement.Common.ServiceFacade.WebApi;
using RIB.Visual.Procurement.Package.BusinessComponents;
using RIB.Visual.Procurement.Package.BusinessComponents.Entities;
using RIB.Visual.Procurement.Package.Localization.Properties;
using RIB.Visual.Procurement.Package.ServiceFacade.WebApi.Dtos.CreateDto;
using RIB.Visual.Procurement.Package.ServiceFacade.WebApi.Responses;
using CommonNLS = RIB.Visual.Basics.Common.Localization.Properties.Resources;

namespace RIB.Visual.Procurement.Package.ServiceFacade.WebApi
{
	/// <summary>
	/// ProcurementPackageController.
	/// </summary>
	[RoutePrefix("procurement/package/package")]
	public class PrcPackageController : RootEntityController<PrcPackageLogic, PrcPackageDto, PrcPackageEntity, PrcPackageCompleteDto, PrcPackageComplete, int>
	{
		/// <summary>
		///
		/// </summary>
		private const string PermissionDescriptor = "66cc2a1a9ec04d8a9de1d5e2fe7f598a";

		/// <summary>
		/// 
		/// </summary>
		public PrcPackageController()
		{
			ApplyPermissionObjectInfo = dtos => SetPermissionObjectInfo(dtos);
		}

		/// <summary>
		/// get packages accroding to filter request
		/// </summary>
		/// <returns></returns>
		[Route("listpackage"), HttpPost]
		[Permission(Permissions.Read, PermissionDescriptor)]
		public Dictionary<string, object> GetListByFilter(FilterRequest filterRequest)
		{
			var filterResponse = new FilterResponse();

			var execInfo = new FilterExecutionInfo(filterRequest, filterResponse);

			execInfo.CreateHint(string.Format(CommonNLS.FetchingMsg_StartFetchingOfData, filterRequest.Pattern));
			EvaluateEnhancedFilter(filterRequest);
			this.Logic.SetFilterRequest(filterRequest);
			List<PrcPackageEntity> packageEntity = Logic.GetEntitiesByFilter(filterRequest, out filterResponse).ToList();

			var theReponse = this.GetPackage(packageEntity, filterRequest, filterResponse, execInfo);

			return theReponse;

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="filterRequest"></param>
		/// <returns></returns>
		[Route("GetPackageWithBusinessPartner")]
        [HttpPost]
        public List<PrcPackageDto> GetProjectOfBPContract(FilterRequest filterRequest)
        {
            var dtos = Logic.GetEntitiesByFilter(filterRequest, out _).Select(e=> new PrcPackageDto(e)).ToList();
            return dtos;
        }


		private Dictionary<string, object> GetPackage(List<PrcPackageEntity> packageEntity, FilterRequest filterRequest, FilterResponse filterResponse, FilterExecutionInfo execInfo)
		{
			int? projectId = filterRequest.ProjectContextId;

			if (execInfo != null)
			{
				execInfo.CreateHint(string.Format(CommonNLS.FetchingMsg_FetchedDataRecord, filterResponse.RecordsRetrieved, filterResponse.RecordsFound));
			}

			if (execInfo != null)
			{
				execInfo.CreateHint(string.Format(Resources.ResolvingLookups));
			}

			var contractHeaderLogic = Injector.Get<IContractHeaderInfoProvider>();
			var conHeaderIdList = packageEntity.Select(r => r.Contract2PackageData.ConHeaderFk).Where(r=>r!=0).ToList();
			var dic=contractHeaderLogic.IsContractSamebp(conHeaderIdList);
			foreach (var item in packageEntity)
			{
				if (dic.ContainsKey(item.Contract2PackageData.ConHeaderFk))
				{
					item.Contract2PackageData.IsConPkgSamebp = dic[item.Contract2PackageData.ConHeaderFk];
				}
			}

			var main = packageEntity.ToDtos(x => new PrcPackageDto(x));
			SetPermissionObjectInfo(main);

			var jsdata = main.CollectLookups<PrcPackageDto>(execInfo, collector => collector
				.Add("PackageStatus", x => x.PackageStatusFk)
				.Add("CashProjection", x => x.CashProjectionFk)
				.Add("Company", x => x.CompanyFk)
				.Add("PrcStructure", x => x.StructureFk)
				.Add("PrcConfiguration", x => x.ConfigurationFk)
				.Add("Currency", x => x.CurrencyFk)
				.Add("Uom", x => x.UomFk)
				.Add("Clerk", x => x.ClerkReqFk, x => x.ClerkPrcFk)
				.Add("TaxCode", x => x.TaxCodeFk)
				.Add("PrcPackageType", x => x.PackageTypeFk)
				.Add("ReqHeaderLookupView", x => x.Requisition2PackageData.ReqHeaderFk)
				.Add("RfqHeader", x => x.Rfq2PackageData.RfqHeaderFk)
				.Add("ConHeader", x => x.Contract2PackageData.ConHeaderFk)
				.Add("BusinessPartner", e => e.BusinessPartnerFk)
				.Add("Subsidiary", e => e.SubsidiaryFk)
				.Add("Supplier", e => e.SupplierFk)
				.Add("Project", x=>x.ProjectFk)
				//.Add("Project", () =>
				//{
				//	var projectIds = new HashSet<int>();

				//	foreach (var item in main)
				//	{
				//		projectIds.Add(item.ProjectFk);
				//	}

				//	IEnumerable<IProjectLookupData> projects = null;

				//	if (projectIds.Count > 0)
				//	{
				//		var ProjectLookupDataProvider = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectLookupDataProvider>();

				//		projects = ProjectLookupDataProvider.GetProjectsByID(projectIds);
				//	}

				//	return projects;
				//})
			);

			// try to fix defect #94579 - doing system refresh several times in a row in the contract module, an error pups up

			if (filterRequest.FurtherFilters != null && filterRequest.FurtherFilters.Count() == 1 && filterRequest.FurtherFilters.First().Token == "PRC_PACKAGE")//Jump from favorites
			{
				jsdata.Add("IsFavoritesJump", true);
			}
			// end

			if (execInfo != null)
			{
				filterResponse.ExecutionInfo = execInfo.Info;
				execInfo.CreateHint(string.Format(Resources.ResolvingLookupDone));
			}
			if(execInfo != null)
			{
				execInfo.CreateHint(string.Format(CommonNLS.FetchingMsg_CompletedDataFetching));
			}

			jsdata.Merge("FilterResult", filterResponse);
			return jsdata;
		}

		private void SetPermissionObjectInfo(IEnumerable<PrcPackageDto> dtos)
		{
			var psackageIds = dtos.Select(e => e.Id).ToList();
			var encodeObjectInfos = Permission.GetEncodedObjectId(Permission.ObjectType.Package, psackageIds.Select(pacId => (Key1: pacId, Key2: 0)));
			foreach (var dto in dtos)
			{
				dto.PermissionObjectInfo = encodeObjectInfos[(dto.Id, 0)];
			}
		}

		private IEnumerable<IProjectLookupData> GetProjectLookupData(IEnumerable<PrcPackageEntity> packages)
		{
			var projectIds = new HashSet<int>();

			foreach (var item in packages)
			{
				projectIds.Add(item.ProjectFk);
			}

			IEnumerable<IProjectLookupData> projects = null;

			if (projectIds.Count > 0)
			{
				var ProjectLookupDataProvider = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectLookupDataProvider>();

				projects = ProjectLookupDataProvider.GetProjectsByID(projectIds);
			}

			return projects;
		}

		/// <summary>
		/// For procurementInvoiceContractValidationService ln#154
		/// TODO(lnb): should be get the package from packageid but not prcBoqId.
		/// </summary>
		/// <param name="Id"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getprcboqpackage")]
		public PrcPackageDto GetPrcBoqPackage(long Id)
		{
			var boqItem = new PrcBoqLogic().GetSearchList(x => x.Id == Id).FirstOrDefault();
			var package = Logic.GetSearchList(x => x.Id == boqItem.PackageFk).FirstOrDefault();
			return new PrcPackageDto(package);
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="updateEstimateParameter"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("checkpackagestatus4updateEstimate")]
		[Permission(Permissions.Read, PermissionDescriptor)]
		public object updateEstimateFromWizard(UpdateEstimateParameter updateEstimateParameter)
		{
			//get the package status ,if is the status "IsEstimate" is false then should not upate Estimate for the package
			var result = Logic.CheckPackageStatus4UpdateEstimate(updateEstimateParameter.PrcPackageId);
			return result;
		}

		/// <summary>
		/// Get Package Delivery Address
		/// For #requisition #contact module
		/// </summary>
		/// <param name="context"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getdeliveryaddress")]
		public RIB.Visual.Basics.Common.ServiceFacade.WebApi.AddressDto GetPackageAddress([System.Web.Http.FromUri] PrcParameterContext context)
		{
			IIdentifyable address = new PrcDeliveryAddressProvider().GetDeliveryAddress(context);
			return address == null
				? null
				: new RIB.Visual.Basics.Common.ServiceFacade.WebApi.AddressDto(address as AddressEntity);
		}

		/// <summary>
		/// Creates a new package
		/// </summary>
		/// <returns>a package header Dto</returns>
		[Route("create/createpackage")]
		[HttpPost]
		[Permission(Permissions.Create, PermissionDescriptor)]
		public PrcPackageDto Create(PackageCreateParameter param)
		{
			var packageEntity = Logic.CreateEntity(param.PrjProjectFk, param.ConfigurationFk, param.StructureFk, param.Description, param.AssetMasterFk, true, false, param.Code, param.BasCurrencyFk, param.PrcConfigHeaderFk,
				need2SaveSubEntitiesAutoCreated: param.Need2SaveSubEntitiesAutoCreated);
			packageEntity.AssetMasterFk = param.AssetMasterFk;
			if (string.IsNullOrEmpty(packageEntity.Code) && !string.IsNullOrEmpty(param.Code))
			{
				packageEntity.Code = param.Code.Trim();
			}
			var dto = new PrcPackageDto(packageEntity);
			SetPermissionObjectInfo(new List<PrcPackageDto>() { dto });
			return dto;
		}


		/// <summary>
		/// get default values for configuraable new recored dialog
		/// </summary>
		/// <param name="projectFk"></param>
		/// <param name="configurationMode"></param>
		/// <returns></returns>
		[Route("getdefaultvalues")]
		[HttpGet]
		public PackageCreateCompleteDto GetDefaultValues(int? projectFk = null,
			ProcurementConfigurationMode configurationMode = ProcurementConfigurationMode.None)
		{
			var packageCompleteEntity = Logic.CreateEntityWithDefaultValues(projectFk, configurationMode: configurationMode);
			return new PackageCreateCompleteDto(packageCompleteEntity);
		}

		/// <summary>
		/// Creates package from template
		/// </summary>
		/// <returns>a requisition header Dto</returns>
		[Route("createfromtemplate")]
		[HttpPost]
		[Permission(Permissions.Create, PermissionDescriptor)]
		public IEnumerable<PackageCreateCompleteDto> CreateFromTemplate(PackageCreateParameter param)
		{
			var fromWizard = true;
			var entityCompleteList = this.Logic.CreateEntityFromTemplate(param.PrjProjectFk, param.PrcPackageTemplateFk,param.AssetMasterFk,
				param.ClerkPrcFk, param.ClerkReqFk, fromWizard);
			var dtos = new List<PackageCreateCompleteDto>();
			using (var transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				foreach (var item in entityCompleteList)
				{
					this.Logic.SavePackageFromTemplate(item);
					//var dto = new PackageCreateCompleteDto(item);
					//dtos.Add(dto);
				}
				transaction.Complete();
			}
			// #89891 - Requirement for new Subscribed Events in Workflow
			foreach (var item in entityCompleteList)
			{
				PrcCommonWorkflowEventHelper.TriggerEvent(PrcCommonWorkflowEventUuids.NewPackageCreated, item.Package.Id);
			}

			var packages = this.Logic.GetItemsByKey(entityCompleteList.Select(e => e.Package.Id));
			foreach (var complete in entityCompleteList)
			{
				int packageId = complete.Package.Id;
				var newEntity = packages.FirstOrDefault(e => e.Id == packageId);

				complete.Package = newEntity;

				var dto = new PackageCreateCompleteDto(complete);
				dtos.Add(dto);
			}

			return dtos;
		}

		/// <summary>
		/// Creates package from template
		/// </summary>
		/// <returns>a requisition header Dto</returns>
		[Route("getloginclerk")]
		[HttpGet]
		public int? GetLoginClerk()
		{

			return this.Logic.GetLoginClerk();
		}

		/// <summary>
		/// delete package
		/// </summary>
		/// <returns>saved header</returns>
		[Route("deletepackage"), HttpPost]
		[Permission(Permissions.Delete, PermissionDescriptor)]
		public void DeletePackage(PrcPackageDto dto)
		{
			var entity = dto.Copy();
			Logic.Delete(entity);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="dtos"></param>
		/// <exception cref="HttpResponseException"></exception>
		[Route("deletepackages"), HttpPost]
		[Permission(Permissions.Delete, PermissionDescriptor)]
		public void DeletePackages(IEnumerable<PrcPackageDto> dtos)
		{
			if (dtos == null)
			{
				throw new HttpResponseException(HttpStatusCode.BadRequest);
			}

			Logic.Delete(dtos.ToEntities(e => e.Copy()));
		}

		/// <summary>
		/// update child item when change header taxcode
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[Route("updateHeaderAndChildTaxCode"), HttpPost]
		public PrcPackageCompleteDto UpdateHeaderTaxCode(PrcPackageCompleteDto dto)
		{
			if (dto.PrcPackage != null && dto.PrcPackage.Version == 0 && dto.PrcPackage.Code == "IsGenerated")
			{
				dto.PrcPackage.Code = "";
			}
			var entity = dto.Copy() as PrcPackageComplete;
			// update the controllingUnit of itmes and boqItems
			if (dto.NeedUpdateUcToItemsBoqs == true)
			{
				Logic.UpdateControllingUnitOfItemsBoqItems(entity);
			}
			Logic.UpdateBoqItemsByTaxCodeFk(entity);
			var prcComp = new PrcPackageCompleteDto(entity);

			return prcComp;
		}

		/// <summary>
		/// Save all changes in package
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[Route("updatepackage"), HttpPost]
		//[Permission(Permissions.Write, PermissionDescriptor)]
		public PrcPackageCompleteDto UpdatePackage(PrcPackageCompleteDto dto)
		{
			if (dto.PrcPackage != null && dto.PrcPackage.Version == 0 && dto.PrcPackage.Code == "IsGenerated")
            {
				dto.PrcPackage.Code = "";
			}
			var entity = dto.Copy();
			#region DEV-40907
			var upstreamOfPackage = Injector.Get<IPpsUpstreamItemLogic>().GetByPrcPackageFks([dto.MainItemId]);
			if (upstreamOfPackage.Any())
			{
				var ppsItemIds = upstreamOfPackage.CollectIds(e => e.PpsItemFk);
				if (upstreamOfPackage.Any(e => !e.PpsItemFk.HasValue))
				{
					var ppsItems = Injector.Get<IPpsItemLogic>().GetItemsByPpsHeaderIds(upstreamOfPackage.Where(e => !e.PpsItemFk.HasValue).CollectIds(e => e.PpsHeaderFk));
					ppsItemIds = ppsItems.CollectIds(e => e.Id);
				}
				var parentItemEvents = Injector.Get<IPpsEventLogic>().GetEventsByItemIds(ppsItemIds);

				var upstreamRequiredFor = upstreamOfPackage.CollectIds(e => e.PpsEventReqforFk);
				var parentEventsRequiredForByUpstreams = parentItemEvents.Where(e => upstreamRequiredFor.Contains(e.Id));
				if (parentEventsRequiredForByUpstreams.Any())
				{
					if (parentEventsRequiredForByUpstreams.Any(e => dto.PrcPackage?.DateDelivery >= e.PlannedStart))
					{
						throw new BusinessLayerException(Resources.ErrorMsg_GreaterThanPUProductionDate);
					}
				}
				if (parentEventsRequiredForByUpstreams.Count() < ppsItemIds.Count())
				{
					var prodSetTypes = Injector.Get<IEventTypeLogic>().GetCoresByFilter(e => e.PpsEntityFk == (int)EPPSEntity.PPSProductionSet);
					var parentItemProductionSetEvents = parentItemEvents.Where(e => prodSetTypes.Any(i => i.Id == e.EventTypeFk)); ;

					if (parentItemProductionSetEvents.Any(e => dto.PrcPackage?.DateDelivery >= e.PlannedStart))
					{
						throw new BusinessLayerException(Resources.ErrorMsg_GreaterThanPUProductionDate);
					}
				}				
			}
			#endregion
			var validator = PrcCommonWorkflowEventHelper.BeginCreatedCheck(entity.PrcPackage);

			using (var transaction = TransactionScopeFactory.Create())
			{

				try
				{
					entity = Logic.UpdateWithoutTrans(entity);
				}
				catch (Exception ex)
				{
					throw ConcurrencyExceptionHandle.ConverToHttpException(ex, entity);
				}
				transaction.Complete();
			}

			// #89891 - Requirement for new Subscribed Events in Workflow
			validator.EndCreatedCheck(entity.PrcPackage, PrcCommonWorkflowEventUuids.NewPackageCreated);

			if (validator.IsNewEntityCreated() && entity.PrcPackage != null)
			{
				entity.PrcPackage = this.Logic.GetItemByKeyComplete(entity.PrcPackage.Id);
			}

			// update the controllingUnit of itmes and boqItems
			if (dto.NeedUpdateUcToItemsBoqs == true)
			{
				Logic.UpdateControllingUnitOfItemsBoqItems(entity);
			}

			var prcComp = new PrcPackageCompleteDto(entity);

			return prcComp;
		}



		/// <summary>
		///
		/// </summary>
		/// <param name="id"></param>
		/// <param name="projectFk"></param>
		/// <param name="code"></param>
		/// <returns></returns>
		[Route("isunique")]
		[HttpGet]
		public bool IsUnique(int id, int projectFk, string code)
		{
			return Logic.IsUnique(id, projectFk, code);
		}

		/// <summary>
		/// Gets Procurement Packages for given project id
		/// </summary>
		/// <param name="projectId">Project Id</param>
		/// <returns>procurement packages</returns>
		/// <remark>if projectId is 'null', all available packages are returned</remark>
		[HttpGet]
		[Route("lookup")]
		public IEnumerable<PrcPackageDto> GetList(int? projectId = null)
		{
			var filterRequest = new FilterRequest { ProjectContextId = projectId };
			FilterResponse filterResponse;
			var entities = Logic.GetEntitiesByFilter(filterRequest, out filterResponse);

			#region query isLive =1 
			var status = new PrcPackageStatusLogic().GetList().Where(e => e.IsLive);
			var statusFks = status.Select(e => e.Id);
			entities = entities.Where(e => statusFks.Contains(e.PackageStatusFk));
			#endregion

			return entities.Select(i => new PrcPackageDto(i)).ToList();
		}


		/// <summary>
		/// For deep copy
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("deepcopy")]
		public PrcPackageCompleteDto DeepCopy(PrcPackageDto dto)
		{
			return new PrcPackageCompleteDto(this.Logic.DoCreateDeepCopy(dto.Copy()));
		}

		/// <summary>
		/// Handles out all projects created for a given asset
		/// </summary>
		/// <returns></returns>
		/// <param name="assetId">Id of the asset the projects should be handled out for</param>
		[HttpGet]
		[Route("toAsset")]
		public IEnumerable<PrcPackageDto> PackagesToAsset(int assetId)
		{
			var prjToAssetProvider = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();

			var projectIds = prjToAssetProvider.ToAsset(assetId).Select(p => p.Id);

			return Logic.GetSearchList(p => projectIds.Contains(p.ProjectFk)).Select(pta => new PrcPackageDto(pta)).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		[Route("ValidateAndUpdateItemQuantity")]
		[HttpPost]
		public int ValidateAndUpdateItemQuantity(RIB.Visual.Procurement.Package.BusinessComponents.ValidateAndUpdateItemQuantityParams param)
		{
			return Logic.ValidateAndUpdateItemQuantity(param);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="packageFk"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("getprcboqdefault")]
		public PrcBoqDefaultValue GetPrcBoqDefaultValue(int packageFk)
		{
			return this.Logic.GetPrcBoqDefaultValue(packageFk, true);
		}

        /// <summary>
        /// GetPackageByProjectFK
        /// </summary>
        /// <param name="projectfk"></param>
        /// <returns>just return this collection count.</returns>
        [Route("getpackagebyprojectfk"), HttpGet]
        public int GetPackageByProjectFK(int projectfk)
        {

            var packageEntitiese = Logic.GetPrcPackageByProjectId(projectfk);

            return packageEntitiese != null && packageEntitiese.Any() ? packageEntitiese.Count() : 0;

        }

		/// <summary>
		/// GetPackageDefaultByProjectFK
		/// </summary>
		/// <param name="projectfk"></param>
		/// <returns>just return this Default data.</returns>
		[Route("getpackagedefaultbyprojectfk"), HttpGet]
		public int GetPackageDefaultByProjectFK(int projectfk)
		{
			var packageRes = Logic.GetPrcPackageByProjectId(projectfk);
			if (packageRes != null && packageRes.Count() == 1)
			{
				return packageRes.First().Id;
			}
			return 0;
		}

		/// <summary>
		/// Retrieves model object IDs for a given set of package IDs.
		/// </summary>
		/// <param name="packageIds">The package IDs.</param>
		/// <param name="modelId">An optional model ID whose objects are being queried.</param>
		/// <returns>The object IDs in compressed string format.</returns>
		[Route("QueryModelObjects")]
		[HttpGet]
		[Permission(Permissions.Read, PermissionDescriptor)]
		public String QueryModelObjects([ModelBinder(typeof(Int32ArrayModelBinder))] IEnumerable<Int32> packageIds, Int32? modelId = null)
		{
			return
				new QueryModelObjectLogic().Query("PRC_PACKAGE2MDL_OBJECT_V", packageIds.Select(id => (Int64) id), modelId)
					.CreateCompressedString();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerId"></param>
		/// <returns></returns>
		[Route("getitembyid")]
		[HttpGet]
		[Permission(Permissions.Read, PermissionDescriptor)]
		public PrcPackageDto GetById(int headerId)
		{
			if (headerId > 0)
			{
				var entity = Logic.GetItemById(headerId);
				var dtos = entity != null ? new PrcPackageDto(entity) : null;
				return dtos;
			}
			else
			{
				return null;
			}
		}


        /// <summary>
        ///
        /// </summary>
        /// <param name="headerIds"></param>
        /// <returns></returns>
        [Route("getitembyids")]
        [HttpPost]
        [Permission(Permissions.Read, PermissionDescriptor)]
        public List<PrcPackageDto> GetByIds(int?[] headerIds)
        {
            var dtos = new List<PrcPackageDto>();
            if (headerIds != null && headerIds.Any())
            {
                var entities = Logic.GetItemsByKey(headerIds);
                dtos = entities.Select(e => new PrcPackageDto(e)).ToList();
            }
            return dtos;
        }

		/// <summary>
		///
		/// </summary>
		/// <param name="statusId"></param>
		/// <returns></returns>
		[Route("isStatusReadonly")]
		[HttpGet]
		public bool IsStatusReadonly(int statusId)
		{
			return new PrcPackageStatusLogic().IsStatusReadonly(statusId);
		}

        /// <summary>
        ///
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [Route("CheckPackageCodeIsUnique")]
        [HttpGet]
        public bool CheckPackageCodeIsUnique(string code)
        {
            var result = true;
            if (string.IsNullOrWhiteSpace(code))
            {
                result = true;
            }
            var loginCompanyFk = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
            var packages = Logic.GetSearchList(e => e.Code == code && e.CompanyFk == loginCompanyFk).ToList();
            if (packages.Any())
            {
                result = false;
            }
            return result;
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="templateFk"></param>
        /// <returns></returns>
        [Route("cangeneratecodefromtemplate")]
        [HttpGet]
        public CanGenerateCodeResult CanGenerateCodeFromTemplate(int templateFk)
        {
            var canGenerateCodeResult = new CanGenerateCodeResult();
            var templateItems = new PrcPackageTemplateItemLogic().GetSearchList(x => x.PrcPackageTemplateFk == templateFk, true).ToList();
            if (templateItems.Any())
            {
                var result = Logic.CanGenerateCodeFromTemplate(templateFk);
                canGenerateCodeResult.Result = result;
                canGenerateCodeResult.TemplateItemCount = templateItems.Count;
            }
            else
            {
                canGenerateCodeResult.Result = false;
                canGenerateCodeResult.TemplateItemCount = 0;
            }
            return canGenerateCodeResult;
        }
        /// <summary>
        ///
        /// </summary>
        [Route("RecalculationBoQ")]
        [HttpGet]
        public void RecalculationBoQ(int packageId, int? vatGroupFk, int? TaxCodeFkOfPackageHeader)
        {
            var prcBoqLogic = new PrcBoqLogic();
            var prcHeaderFks = new PrcPackage2HeaderLogic().GetPackage2HeaderByPackageId(new int[] { packageId });
            if (prcHeaderFks != null && prcHeaderFks.Any())
            {
                var boqList = prcBoqLogic.GetList(e => prcHeaderFks.Contains(e.PrcHeaderFk)).ToList();
                var boqHeaderFks = boqList.Select(e => e.BoqHeaderFk).ToList();
                var boqItemLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IBoqItemLogic>();
                boqItemLogic.CalcTaxGrossForBoqItemTree(boqHeaderFks, vatGroupFk, TaxCodeFkOfPackageHeader);
				this.Logic.RecalculateTotalsByHeaderIdAfterRecalculatingBoq(packageId);
            }
        }

		/// <summary>
		/// Gets all Packages used in given header at line items or resources
		/// </summary>
		/// <param name="estHeaderFk"></param>
		/// <param name="selectedLineItemId"></param>
		/// <returns>just return this Default data.</returns>
		[Route("getpackagebyestimateheaderfk")]
        [HttpPost]
        public List<PrcPackageDto> GetPackageByEstimateHeaderFk(int estHeaderFk, int selectedLineItemId)
        {
            var packageRes = Logic.GetPrcPackageByEstHeaderId(estHeaderFk, selectedLineItemId).Select(e => new PrcPackageDto(e)).ToList(); ;
            return packageRes;
        }

		/// <summary>
		/// Gets all Packages,PrcItemAssignments used in given header at line items or resources
		/// </summary>
		/// <param name="data"></param>
		/// <returns>just return this Default data.</returns>
		[Route("getprcitemassignmentsandpackages")]
		[HttpPost]
		public List<Tuple<IEnumerable<PrcPackageDto>, IEnumerable<PrcItemAssignmentDto>>> GetPrcItemAssignmentsAndPackages(GetPackageData data)
		{
			List<Tuple<IEnumerable<PrcPackageDto>, IEnumerable<PrcItemAssignmentDto>>> result = new List<Tuple<IEnumerable<PrcPackageDto>, IEnumerable<PrcItemAssignmentDto>>>();

			var prcItemAssignPackageTuple = GetPackageAssignments(data.estHeaderId);

			if (prcItemAssignPackageTuple.Item1.Any() && prcItemAssignPackageTuple.Item2.Any())
			{
				if (data.selectedLineItemIds.Any())
				{
					foreach (var selectedLineItemId in data.selectedLineItemIds)
					{
						var selectedPrcItemAssignments = prcItemAssignPackageTuple.Item2.Where(e => e.EstLineItemFk == selectedLineItemId).Distinct().ToList();

						var selectedPrcPackageIds = selectedPrcItemAssignments.Select(s => (int?)s.PrcPackageFk).Distinct().ToList();

						var selectedPrcPackages = prcItemAssignPackageTuple.Item1.Where(e => selectedPrcPackageIds.Contains(e.Id)).Distinct().ToList();

						result.Add(new Tuple<IEnumerable<PrcPackageDto>, IEnumerable<PrcItemAssignmentDto>>(selectedPrcPackages, selectedPrcItemAssignments));
					}
				}
				else
				{
					result.Add(prcItemAssignPackageTuple);
				}
			}

			return result;
		}

		private Tuple<IEnumerable<PrcPackageDto>, IEnumerable<PrcItemAssignmentDto>> GetPackageAssignments(int estHeaderId)
		{
			var prcItemAssingPackageTupels = Logic.GetPrcItemAssignmentsAndPackages(estHeaderId);

			var prcPackages = prcItemAssingPackageTupels.Item1;
			var prcItemAssignments = prcItemAssingPackageTupels.Item2;
			var prcItemAssingPackageTupel = new Tuple<IEnumerable<PrcPackageDto>, IEnumerable<PrcItemAssignmentDto>>(prcPackages.Select(e => new PrcPackageDto(e)), prcItemAssignments.Select(e => new PrcItemAssignmentDto(e)));

			return prcItemAssingPackageTupel;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectFk"></param>
		/// <returns></returns>
		[Route("gettelephone"), HttpGet]
		public ProjectTelephoneNumberResponse GetTelephoneNumberByProject(int projectFk)
		{
			var getProjectLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IGetProjectLogic>();
			var projects = getProjectLogic.GetAddressByProjectIds(new List<int>() { projectFk });
			var project = projects.FirstOrDefault(e => e.Id == projectFk) as IProjectAddressData;

			TelephoneNumberLogic telLogic = new TelephoneNumberLogic();
			var telephoneNumber = new ProjectTelephoneNumberResponse
			{
				ProjectFk = projectFk,
				CountryFk = project?.CountryFk,
				RegionFk = project?.RegionFk,
				Email = project?.Email
			};
			if (project != null)
			{
				if (project.AddressFk.HasValue)
				{
					var addressLogic = new AddressLogic();
					telephoneNumber.AddressEntity = addressLogic.Clone(project.AddressFk.Value);
					if (telephoneNumber.AddressEntity != null)
					{
						telephoneNumber.AddressFk = telephoneNumber.AddressEntity.Id;
					}
				}
				if (project.TelephoneNumberFk.HasValue)
				{
					telephoneNumber.TelephoneNumber = telLogic.Clone(project.TelephoneNumberFk.Value);
					if (telephoneNumber.TelephoneNumber != null)
					{
						telephoneNumber.TelephoneNumberFk = telephoneNumber.TelephoneNumber.Id;
					}
				}
				if (project.TelephoneTelefaxFk.HasValue)
				{
					telephoneNumber.TelephoneNumberTelefax = telLogic.Clone(project.TelephoneTelefaxFk.Value);
					if (telephoneNumber.TelephoneNumberTelefax != null)
					{
						telephoneNumber.TelephoneTelefaxFk = telephoneNumber.TelephoneNumberTelefax.Id;
					}
				}
				if (project.TelephoneMobilFk.HasValue)
				{
					telephoneNumber.TelephoneMobil = telLogic.Clone(project.TelephoneMobilFk.Value);
					if (telephoneNumber.TelephoneMobil != null)
					{
						telephoneNumber.TelephoneMobileFk = telephoneNumber.TelephoneMobil.Id;
					}
				}
			}
			return telephoneNumber;
		}

		/// <summary>
		/// generate package code
		/// </summary>
		/// <param name="dto"></param>
		/// <returns>just return package code.</returns>
		[Route("generatepackagecode")]
		[HttpPost]
		public string GeneratePackageCode(PrcPackageDto dto)
		{
			var prcPackageEntity = dto.Copy();
			Logic.GeneratePackageCode(prcPackageEntity);
			return prcPackageEntity.Code;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[Route("splitoveralldiscount")]
		[HttpPost]
		public SplitOverAllDiscountResult SplitOverallDiscount(SplitOverAllDiscountParams dto)
		{
			var splitDiscountLogic = new SplitOverAllDiscountLogic();
			var package2HeaderLogic = new PrcPackage2HeaderLogic();

			var subPackages = package2HeaderLogic.GetSearchList(e => e.PrcPackageFk == dto.Id);
			var prcHeaderIds = subPackages.Select(e => e.PrcHeaderFk).ToList();
			decimal rate = dto.ExchangeRate != 0 ? dto.ExchangeRate : 1m;
			var result = splitDiscountLogic.SplitOverallDiscount(prcHeaderIds, dto.OverallDiscount, dto.OverallDiscountOc, dto.TaxCodeFk, dto.BpdVatGroupFk, rate, ProcurementCommonModuleDataConfiguration.PackageModuleIdentifier);
            if (result.Result && (result.Item != 0 || result.Boq != 0 ))
            {
				var pkgTotalLogic = new PrcPackageTotalLogic();
				var totals = pkgTotalLogic.GetSearchList(x => x.HeaderFk == dto.Id);
				var entity = Logic.GetItemById(dto.Id);
				pkgTotalLogic.RecalculateTotalsFromHeader(entity, totals);
				pkgTotalLogic.Save(totals);
			}

			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="options"></param>
		[Route("updateExchangeRate")]
		[HttpPost]
		public bool UpdateExchangeRate(UpdateExchangeRateRequest options)
        {
			var header = Logic.GetItemByKey(options.HeaderId);
			var subPackages = new PrcPackage2HeaderLogic().GetSearchList(e => e.PrcPackageFk == options.HeaderId);
			if (subPackages.Any())
			{
				var prcHeaderIds = subPackages.Select(e => e.PrcHeaderFk).ToList();
				var rate = header.ExchangeRate;
				var remainNet = options.RemainNet;
				var result = new CommonUpdateExchangeRateLogic().UpdateExchangeRate(prcHeaderIds, header.CurrencyFk, rate, header.BpdVatGroupFk, remainNet);
				if (result.Result && (result.haveItems || result.haveBoqs))
				{
					var pkgTotalLogic = new PrcPackageTotalLogic();
					var totals = pkgTotalLogic.GetSearchList(x => x.HeaderFk == header.Id);
					pkgTotalLogic.RecalculateTotalsFromHeader(header, totals);
					pkgTotalLogic.Save(totals);
				}
				return result.Result;
			}
			else
			{
				return false;
			}
		}

		///// <summary>
		/////   Creates a Dto instance.
		///// </summary>
		///// <param name="creationData"></param>
		///// <returns></returns>
		//[HttpPost]
		//[Route("configuredcreate")]
		//public new PackageCreateCompleteDto CreateFromConfiguredInput([FromBody] JObject creationData)
		//{
		//	var createLogic = new PackageCreateFromConfigurableDialogLogic();
		//	var package = createLogic.CreateFromConfiguredInput(creationData);

		//	var packageCompleteEntity = Logic.CreateEntityFromConfigurableDialog(package);
		//	return new PackageCreateCompleteDto(packageCompleteEntity);
		//}

		/// <summary>
		/// return package belong to given project
		/// </summary>
		/// <param name="data">filter</param>
		/// <returns></returns>
		[HttpPost]
		[Route("getlistforgcadditionalexpensepackage")]
		[Permission(Permissions.Read, PermissionDescriptor)]
		public IEnumerable<PrcPackageDto> GetListForGCAdditionalExpensePackage(PackageCreateParameter data)
		{
			var dtos = Logic.GetListForGCAdditionalExpensePackage(data.Filter,data.PrjProjectFk,data.MdcControllingUnitFk).Select(e => new PrcPackageDto(e)).ToList();
			return dtos;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		[HttpPost]
		[Route("GetRestBudget")]
		public object GetRestBudget(PackageCreateParameter data)
		{
			return this.Logic.GetRestBudget(data.MdcControllingUnitFk, data.PrcPackageFk, data.PrjProjectFk);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="configurationFk"></param>
		/// <returns></returns>
		[HttpGet]
		[Route("GetTotalTypeWithBudgetKind")]
		public object GetTotalTypeWithBudgetKind(int configurationFk)
		{
			var hasBudgetKind = true;
			DescriptionTranslateType budgetKindDesctiption = null;

			var configurationLogic = new PrcConfigurationLogic();
			var configuration = configurationLogic.GetSearchList(e => configurationFk == e.Id).FirstOrDefault();

			if (configuration != null)
			{
				var prcTotalTypeLogic = new PrcTotalTypeLogic();
				var prcTotalTypeEntity = prcTotalTypeLogic.GetSearchList(e => e.PrcConfigHeaderFk == configuration.PrcConfigHeaderFk && e.PrcTotalKindFk == TotalKindConstant.BudgetNet).FirstOrDefault();

				if (prcTotalTypeEntity == null)
				{
					hasBudgetKind = false;
					var prcTotalKindLogic = new PrcTotalKindLogic();

					var totalKindEntity = prcTotalKindLogic.GetSearchList(e => e.Id == TotalKindConstant.BudgetNet).FirstOrDefault();
					if (totalKindEntity != null)
					{
						budgetKindDesctiption = totalKindEntity.DescriptionInfo;
					}
				}
			}
			return new
			{
				HasBudgetKind = hasBudgetKind,
				BudgetKindDesctiption = budgetKindDesctiption
			};
		}

		/// <summary>
		/// Creates a new package
		/// </summary>
		/// <returns>a package header Dto</returns>
		[Route("createOrUpdatePackageByGccWizard")]
		[HttpPost]
		[Permission(Permissions.Create, PermissionDescriptor)]
		public PrcPackageDto createOrUpdatePackageByGccWizard(PackageCreateParameter param)
		{
			var entity = new PrcPackageEntity();
			try
			{
				if (param.GenerateType == "1")
				{
					var packageEntity = Logic.CreateEntity(param.PrjProjectFk, param.ConfigurationFk, param.StructureFk, param.Description, param.AssetMasterFk, true, false,param.Code);
					if (packageEntity != null)
					{
						param.PrcPackageFk = packageEntity.Id;
						packageEntity.Remark = param.Remark;
						packageEntity.Remark2 = param.Remark2;
						packageEntity.Remark3 = param.Remark3;
						packageEntity.MdcControllingUnitFk = param.MdcControllingUnitFk;
						entity = Logic.Save(packageEntity);
					}
				}
				else if (param.GenerateType == "2" && param.PrcPackageFk.HasValue)
				{
					var packageEntity = Logic.GetItemById(param.PrcPackageFk.Value);
					if (packageEntity != null)
					{
						packageEntity.StructureFk = param.StructureFk;
						packageEntity.Remark = param.Remark;
						packageEntity.Remark2 = param.Remark2;
						packageEntity.Remark3 = param.Remark3;
						entity = Logic.Save(packageEntity);
					}
				}

				#region create or update PRC_PACKAGETOTAL
				var configurationLogic = new PrcConfigurationLogic();
				var configuration = configurationLogic.GetSearchList(e => param.ConfigurationFk == e.Id).FirstOrDefault();

				if (configuration != null)
				{
					var prcTotalTypeLogic = new PrcTotalTypeLogic();
					var prcTotalTypeEntity = prcTotalTypeLogic.GetSearchList(e => e.PrcConfigHeaderFk == configuration.PrcConfigHeaderFk && e.PrcTotalKindFk == TotalKindConstant.BudgetNet).FirstOrDefault();
					if (prcTotalTypeEntity != null)
					{
						var prcPackageTotalLogic = new PrcPackageTotalLogic();
						var prcPackageTotalEntity = prcPackageTotalLogic.GetSearchList(e => prcTotalTypeEntity.Id == e.TotalTypeFk && param.PrcPackageFk == e.HeaderFk).FirstOrDefault();

						if (prcPackageTotalEntity == null)
						{
							prcPackageTotalEntity = prcPackageTotalLogic.Create(param.PrcPackageFk.Value, prcTotalTypeEntity.Id);
						}

						prcPackageTotalEntity.ValueNet = param.Budget;
						prcPackageTotalEntity.ValueNetOc = param.Budget;

						prcPackageTotalLogic.Save(prcPackageTotalEntity);

					}
				}
				#endregion


			}
			catch (Exception)
			{
				return new PrcPackageDto();
			}

			return new PrcPackageDto(entity);
		}

		/// <summary>
		/// Get packages including partial sub entities
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		[Route("getpackagestructure"), HttpPost]
		public IEnumerable<PackageDataCompleteDto> GetPackageStructure(GetPackageStructureParam param)
		{
			ArgumentNullException.ThrowIfNull(param);

			// get packages
			var packages = this.Logic.GetItemsByProjectId(param.ProjectId, param.IncludeNonActiveItems, param.PackageType, param.PackageIds);
			var packageIds = packages.CollectIds(e => e.Id);

			// get sub packages
			var subPackages = new PrcPackage2HeaderLogic().GetItemsByPackageIds(packageIds);
			var prcHeaderIds = subPackages.CollectIds(e => e.PrcHeaderFk);
			var subPackagesMap = subPackages.GroupBy(e => e.PrcPackageFk).ToDictionary(e => e.Key, e => e.ToList());
			var prcHeaderIdData = prcHeaderIds.Select(e => new RIB.Visual.Platform.Core.IdentificationData() { Id = e }).ToList();
			var prcHeaders = new PrcHeaderLogic().GetByIds(prcHeaderIdData);
			var prcHeadersMap = prcHeaders.ToDictionary(e => e.Id, e => e);
			IDictionary<int, List<PrcBoqExtendedEntity>> prcBoqExtendsMap = null;
			IDictionary<int, bool> prcHeaderIdHasPrcItems = null;

			// get prc boqs according to package type
			if (param.PackageType == ProcurementConfigurationMode.Service)
			{
				var prcBoqExtends = new PrcBoqLogic().GetPrcBoqExtendedsByPrcHeaderIds(prcHeaderIds);
				prcBoqExtendsMap = prcBoqExtends.GroupBy(e => e.PrcBoq.PrcHeaderFk).ToDictionary(e => e.Key, e => e.ToList());
			}
			else if (param.PackageType == ProcurementConfigurationMode.Material) // check whether there are prc items or not for the prc header ids
			{
				prcHeaderIdHasPrcItems = PrcItemLogic.GetPrcHeaderIdsHasItemsParallel(prcHeaderIds);
			}

			var results = new List<PackageDataCompleteDto>();
			foreach (var package in packages)
			{
				var completeDto = new PackageDataCompleteDto(package);
				if (subPackagesMap.TryGetValue(package.Id, out var curSubPackages))
				{
					var subCompleteDtos = new List<SubPackageCompleteDto>();
					foreach (var subPackage in curSubPackages)
					{
						var subCompleteDto = new SubPackageCompleteDto(subPackage);
						subCompleteDto.SubPackage.CurrencyFk = package.CurrencyFk;
						if (prcHeadersMap.TryGetValue(subPackage.PrcHeaderFk, out var prcHeader))
						{
							subCompleteDto.SubPackage.PrcHeaderEntity = new PrcHeaderDto(prcHeader);
						}
						if (prcBoqExtendsMap != null && prcBoqExtendsMap.TryGetValue(subPackage.PrcHeaderFk, out var prcBoqExtends))
						{
							subCompleteDto.PrcBoqExtendeds = prcBoqExtends.ToDtos(e => new PrcBoqExtendedDto(e));
						}
						if (prcHeaderIdHasPrcItems != null && prcHeaderIdHasPrcItems.ContainsKey(subPackage.PrcHeaderFk))
						{
							subCompleteDto.HasPrcItems = prcHeaderIdHasPrcItems[subPackage.PrcHeaderFk];
						}
						subCompleteDtos.Add(subCompleteDto);
					}
					completeDto.SubPackageCompletes = subCompleteDtos;
				}
				results.Add(completeDto);
			}

			return results;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <returns></returns>
		[Route("getcolumnreferencelength4package"), HttpGet]
		public decimal GetColumnReferenceLength4Package()
		{
			return this.Logic.GetColumnReferenceLength4Package();
		}
	}


	/// <summary>
	///
	/// </summary>
	public class FilterPackageData
	{
		/// <summary>
		///
		/// </summary>
		public string Filter { get; set; }

		/// <summary>
		///
		/// </summary>
		public int ProjectId { get; set; }

	}

	/// <summary>
	///
	/// </summary>
	public class GetPackageData
	{
		/// <summary>
		///
		/// </summary>
		public int estHeaderId { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<int> selectedLineItemIds { get; set; }

	}
}