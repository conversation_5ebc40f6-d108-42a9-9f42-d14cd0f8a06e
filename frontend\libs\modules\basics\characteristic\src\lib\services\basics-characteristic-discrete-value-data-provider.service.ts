/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable } from '@angular/core';
import { IIdentificationData, LazyInjectable, ServiceLocator } from '@libs/platform/common';
import { IDfmDefectEntity } from '@libs/defect/interfaces';
import { BASICS_CHARACTERISTIC_DISCRETE_VALUE_DATA_PROVIDER, IBasicsCharacteristicDiscreteValueDataProvider, ICharacteristicValueEntity } from '@libs/basics/interfaces';
import { BasicsCharacteristicDiscreteValueDataService } from './basics-characteristic-discrete-value-data.service';

@Injectable({
	providedIn: 'root',
})
@LazyInjectable<IDfmDefectEntity>({
	token: BASICS_CHARACTERISTIC_DISCRETE_VALUE_DATA_PROVIDER,
	useAngularInjection: true,
})
export class BasicsCharacteristicDiscreteValueDataProviderService implements IBasicsCharacteristicDiscreteValueDataProvider<ICharacteristicValueEntity> {
	private readonly service = ServiceLocator.injector.get(BasicsCharacteristicDiscreteValueDataService);

	public getList(): ICharacteristicValueEntity[] | null {
		return this.service.getList();
	}

	public loadSubEntities(identificationData: IIdentificationData | null): Promise<void> {
		return this.service.loadSubEntities(identificationData);
	}

	public getSelectedCharacteristicId(): number | null {
		return this.service.getSelectedCharacteristicId();
	}
}
