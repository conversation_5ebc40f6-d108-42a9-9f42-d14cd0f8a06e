import apiConstantData from "cypress/constantData/apiConstantData";
import { tile, app, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import { _estimatePage, _schedulePage, _common, _commonAPI, _validate, } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import { EST_HEADER } from 'cypress/pages/variables';

const LINE_ITEM_DESCRIPTION1 = _common.generateRandomString(5);

let LINE_ITEM_PARAMETERS
let CONTAINERS_LINE_ITEM,
  CONTAINERS_ACTIVITY_STRUCTURE;
let SCHEDULE_API_PARAMETERS: DataCells;
let CONTAINER_AS_DATA
let RESOURCE_PARAMETERS
let CONTAINER_COLUMNS_LINE_ITEM
let CONTAINER_SCHEDULING
let CONTAINER_COLUMNS_ACTIVITY_STRUCTURE
let UPDATE_SCHEDULING_QUANTITIES_PARAMETERS
let MODAL_UPDATE_SCHEDULING_QUANTITIES
let MOD<PERSON>_MESSAGE
let GENERATE_ACTIVITY_PARAMETERS


describe('SCH- 3.201 | Duration estimation driven include logic for case 2', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  before(function () {
    cy.fixture('scheduling/psd-3.201-duration-estimation-driven-include-logic-for-case-2.json')
      .then((data) => {
        this.data = data;
        CONTAINER_COLUMNS_LINE_ITEM = this.data.CONTAINER_COLUMNS.LINE_ITEM
        CONTAINERS_LINE_ITEM = this.data.CONTAINERS.LINE_ITEM
        CONTAINER_SCHEDULING = this.data.CONTAINERS.SCHEDULING
        CONTAINER_AS_DATA = this.data.CONTAINERS.ACTIVITY_DATA
        CONTAINERS_ACTIVITY_STRUCTURE = this.data.CONTAINERS.ACTIVITY_STRUCTURE
        CONTAINER_COLUMNS_ACTIVITY_STRUCTURE = this.data.CONTAINER_COLUMNS.ACTIVITY_STRUCTURE
        RESOURCE_PARAMETERS = {
          [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINER_AS_DATA.SHORT_CODE,
          [app.GridCells.CODE]: CONTAINER_AS_DATA.COST_CODE
        };
        MODAL_MESSAGE = this.data.MODAL.MESSAGE
        MODAL_UPDATE_SCHEDULING_QUANTITIES = this.data.MODAL.UPDATE_SCHEDULING_QUANTITIES
        UPDATE_SCHEDULING_QUANTITIES_PARAMETERS = {
          [commonLocators.CommonKeys.CHECKBOX]: MODAL_UPDATE_SCHEDULING_QUANTITIES
        }


      }).then(() => {
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.setDefaultView(app.TabBar.PROJECT)
          _common.waitForLoaderToDisappear()
          _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        })
        _common.waitForLoaderToDisappear();
        _commonAPI.getAccessToken().then((result) => {
          cy.log(`Token Retrieved: ${result.token}`);
        });
      })
  });
  after(() => {
    cy.LOGOUT();
  });

  it('TC - API: Create project', function () {
    _commonAPI.createProject().then(() => {
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
    });
  });

  it('TC - API: Create estimate header', function () {
    _commonAPI.createEstimateHeader(Cypress.env('API_PROJECT_ID_1'));
  });

  it('TC - API: Create line item', function () {
    LINE_ITEM_PARAMETERS = {
      [app.GridCells.DESCRIPTION_INFO]: LINE_ITEM_DESCRIPTION1,
      [app.GridCells.QUANTITY_SMALL]: CONTAINERS_LINE_ITEM.QUANTITY[0],
      [app.GridCells.BAS_UOM_FK]: apiConstantData.ID.UOM_BAGS,
    };
    _commonAPI.createEstimateLineItems(Cypress.env('API_EST_ID_1'), LINE_ITEM_PARAMETERS);
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
    _common.waitForLoaderToDisappear()
  });

  it('TC - Add resource to line item', function () {
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
      _common.clear_subContainerFilter(cnt.uuid.ESTIMATE)
    });
    _common.clickOn_cellHasUniqueValue(cnt.uuid.ESTIMATE, app.GridCells.DESCRIPTION_INFO, Cypress.env(`API_EST_CODE_1`), EST_HEADER)
    _common.select_activeRowInContainer(cnt.uuid.ESTIMATE)
    _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS, CONTAINER_COLUMNS_LINE_ITEM)
      _common.set_columnAtTop([CONTAINER_COLUMNS_LINE_ITEM.psdactivityfk, CONTAINER_COLUMNS_LINE_ITEM.quantity, CONTAINER_COLUMNS_LINE_ITEM.hourstotal, CONTAINER_COLUMNS_LINE_ITEM.estqtyrelactfk], cnt.uuid.ESTIMATE_LINEITEMS)
      _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
      _common.waitForLoaderToDisappear();
    });
    _common.waitForLoaderToDisappear()
    _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_DESCRIPTION1);
    _common.clickOn_activeRowCell(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.EST_QTY_REL_ACT_FK)
    _common.edit_dropdownCellWithCaret(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.EST_QTY_REL_ACT_FK, commonLocators.CommonKeys.SPAN, commonLocators.CommonKeys.TO_STRUCTURE)
    _common.clickOn_activeRowCell(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.QUANTITY_SMALL)
    _common.waitForLoaderToDisappear();
    cy.SAVE();
    _common.waitForLoaderToDisappear();
    cy.SAVE();
    _common.waitForLoaderToDisappear();
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
    });
    _common.clear_subContainerFilter(cnt.uuid.RESOURCES);
    _common.create_newRecord(cnt.uuid.RESOURCES);
    _common.waitForLoaderToDisappear();
    _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS);
    _common.waitForLoaderToDisappear();
    cy.SAVE();
    _common.waitForLoaderToDisappear();
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS);
    });
    _common.waitForLoaderToDisappear()
    _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_DESCRIPTION1);
    _common.clickOn_activeRowCell(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.EST_QTY_REL_ACT_FK)
    _common.edit_dropdownCellWithCaret(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.EST_QTY_REL_ACT_FK, commonLocators.CommonKeys.SPAN, commonLocators.CommonKeys.TO_STRUCTURE)
    _common.clickOn_activeRowCell(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.QUANTITY_SMALL)
    _common.waitForLoaderToDisappear();
    cy.SAVE();
    _common.waitForLoaderToDisappear();
    cy.SAVE();
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
    _common.waitForLoaderToDisappear();
  });

  it('TC - API: Create schedule header with an activity', function () {
    SCHEDULE_API_PARAMETERS = {
      [app.GridCells.BAS_UOM_FK]: CONTAINERS_ACTIVITY_STRUCTURE.UOM
    };
    _commonAPI.createSchedulingAndSchedulingActivity(Cypress.env('API_PROJECT_ID_1'), 2, SCHEDULE_API_PARAMETERS);
  });

  it("TC - Navigate to schedule ", function () {
    _common.openTab(app.TabBar.SCHEDULING).then(() => {
      _common.select_tabFromFooter(cnt.uuid.SCHEDULES, app.FooterTab.SCHEDULES, 2);
    });
    _common.select_rowInSubContainer(cnt.uuid.SCHEDULES)
    _common.edit_dropdownCellWithCaret(cnt.uuid.SCHEDULES, app.GridCells.SCHEDULE_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_SCHEDULING.SCHEDULE_TYPE[1])
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.select_rowInSubContainer(cnt.uuid.SCHEDULES)
    _common.clickOn_toolbarButton(cnt.uuid.SCHEDULES, btn.ToolBar.ICO_GO_TO);
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.RESOURCES).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ACTIVITY_STRUCTURE, app.FooterTab.ACTIVITY_STRUCTURE)
      _common.setup_gridLayout(cnt.uuid.ACTIVITY_STRUCTURE, CONTAINER_COLUMNS_ACTIVITY_STRUCTURE)
      _common.set_columnAtTop([CONTAINER_COLUMNS_ACTIVITY_STRUCTURE.quantity, CONTAINER_COLUMNS_ACTIVITY_STRUCTURE.plannedduration, CONTAINER_COLUMNS_ACTIVITY_STRUCTURE.progressreportmethodfk, CONTAINER_COLUMNS_ACTIVITY_STRUCTURE.isdurationestimationdriven], cnt.uuid.ACTIVITY_STRUCTURE)
    })
  });

  it("TC - Generate Activity by  using Wizard", function () {
    GENERATE_ACTIVITY_PARAMETERS = {
      [commonLocators.CommonElements.NAV_TABS]: commonLocators.CommonLabels.GENERATE_ACTIVITIES,
      [commonLocators.CommonKeys.LABEL]: commonLocators.CommonLabels.GENERATE_ACTIVITIES_FROM_ONE_ACTIVE_ESTIMATE,
      [commonLocators.CommonKeys.RADIO_INDEX]: "0",
      [commonLocators.CommonKeys.CODE]: Cypress.env('API_EST_CODE_1'),
      [commonLocators.CommonKeys.CRITERIA_LABEL]: { "Criteria 1": "Line Item: " + `${Cypress.env("API_EST_CODE_1")}` },
      [commonLocators.CommonLabels.CREATE_RELATIONS]: commonLocators.CommonKeys.CHECK,
      [commonLocators.CommonLabels.KIND_CAPS]: commonLocators.CommonLabels.FINISH_START
    };
    _common.openTab(app.TabBar.PLANNING).then(() => {
      _common.waitForLoaderToDisappear()
      _common.setDefaultView(app.TabBar.PLANNING, commonLocators.CommonKeys.DEFAULT)
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.ACTIVITY_STRUCTURE, app.FooterTab.ACTIVITY_STRUCTURE, 0);
    });
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.GENERATE_ACTIVITIES)
    _common.waitForLoaderToDisappear()
    _schedulePage.generate_activityStructureRecord_byWizard(GENERATE_ACTIVITY_PARAMETERS)
    _common.clickOn_modalFooterButton(btn.ButtonText.OK)
    cy.wait(1000)
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    cy.REFRESH_CONTAINER()
    _common.waitForLoaderToDisappear()
  });

  it("TC - Assert values in Activity container in the scheduling module", function () {
    _common.openTab(app.TabBar.PLANNING).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ACTIVITY_STRUCTURE, app.FooterTab.ACTIVITY_STRUCTURE)
      _common.setup_gridLayout(cnt.uuid.ACTIVITY_STRUCTURE, CONTAINER_COLUMNS_ACTIVITY_STRUCTURE)
    });
    _common.clear_subContainerFilter(cnt.uuid.ACTIVITY_STRUCTURE)
    _common.select_allContainerData(cnt.uuid.ACTIVITY_STRUCTURE);
    _common.clickOn_expandCollapseButton(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_SELECTED);
    cy.wait(1000);
    _common.select_rowHasValue(cnt.uuid.ACTIVITY_STRUCTURE, LINE_ITEM_DESCRIPTION1)
    _common.assert_cellData_insideActiveRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.DESCRIPTION, LINE_ITEM_DESCRIPTION1)
    _common.assert_cellData_insideActiveRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.QUANTITY_SMALL, CONTAINERS_LINE_ITEM.QUANTITY[0])
    _common.assert_cellData_insideActiveRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.PLANNED_DURATION, "7")
    _common.assert_cellData_insideActiveRow(cnt.uuid.ACTIVITY_STRUCTURE, app.GridCells.QUANTITY_UOM_FK, apiConstantData.ID.UOM_BAGS)
  });

  it('TC - Update line item quantity for IsExecution=false & IsProcurement=true', function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
    _common.openTab(app.TabBar.SCHEDULING).then(() => {
      _common.select_tabFromFooter(cnt.uuid.SCHEDULES, app.FooterTab.SCHEDULES, 2);
    });
    _common.select_rowInSubContainer(cnt.uuid.SCHEDULES)
    _common.edit_dropdownCellWithCaret(cnt.uuid.SCHEDULES, app.GridCells.SCHEDULE_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_SCHEDULING.SCHEDULE_TYPE[2])
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS);
    });
    _common.waitForLoaderToDisappear()
    _common.select_rowInSubContainer(cnt.uuid.PROJECTS)
    _common.openTab(app.TabBar.ESTIMATE).then(() => {
      _common.waitForLoaderToDisappear()
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
    });
    _common.select_allContainerData(cnt.uuid.ESTIMATE)
    _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
    _common.waitForLoaderToDisappear()
    _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
      _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS);
      _common.setup_gridLayout(cnt.uuid.ESTIMATE_LINEITEMS, CONTAINER_COLUMNS_LINE_ITEM)
      _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
    });
    _common.select_rowHasValue(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_DESCRIPTION1)
    _common.edit_dropdownCellWithCaret(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.EST_QTY_REL_ACT_FK, commonLocators.CommonKeys.SPAN, commonLocators.CommonKeys.TO_STRUCTURE)
    _common.edit_containerCell(cnt.uuid.ESTIMATE_LINEITEMS, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_LINE_ITEM.QUANTITY[2])
    _common.waitForLoaderToDisappear()
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
    _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ACTIVITIES)
    _common.waitForLoaderToDisappear()
    _estimatePage.update_schedulingQuantities_fromWizard(UPDATE_SCHEDULING_QUANTITIES_PARAMETERS)
    _common.waitForLoaderToDisappear()
    _validate.verify_ModalMessage(MODAL_MESSAGE.FAILURE)
    cy.SAVE()
    _common.waitForLoaderToDisappear()
  });

});




