import apiConstantData from "cypress/constantData/apiConstantData";
import { tile, app, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _estimatePage, _validate, _boqPage, _logesticPage, _controllingUnit, _projectPage, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const PLANT_CODE = _common.generateRandomString(3);
const PLANT_DESCRIPTION = _common.generateRandomString(3);
const CONDITION_CODE = _common.generateRandomString(3)
const CONDITION_DESC = _common.generateRandomString(3)

let CONTAINER_PROJECT,
    PROJECT_PARAMETERS1,
    PROJECT_PARAMETERS2
let CONTAINER_DATA_RECORD
let CONTAINERS_CONTROLLING_UNITS,
    CONTAINER_COLUMNS_CONTROLLING_UNITS,
    CONTROLLING_UNIT_SUB_PARAMETERS
let CONTAINER_COLUMNS_CONTROLLING_UNIT2;
let CONTROLLING_UNIT_PARAMETERS2: DataCells,
    ALLOCATION_FOR_PLANTS_PARAMETER: DataCells
let MODAL_PLANT_ALLOCATION
let PLANT_PARAMETERS: DataCells
let CONTAINERS_PLANT, 
    CONTAINER_COLUMNS_PLANT,
    CONTAINERS_PLANT_PRICE_LISTS
let CONTAINER_COLUMNS_JOBS;
let CONTAINER_JOBS;
let CONTAINER_COLUMNS_CONDITIONS
let CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS
let CONTAINER_COLUMNS_DISPATCHING_HEADER,
    CONTAINER_COLUMNS_DISPATCHING_RECORD,
    CONTAINERS_DISPATCHING_HEADER
  
describe("LRM- 1.80 | Verify edit of price portion in dispatch note module from manually from logistic job record", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("LRM/lgm-1.80-verify-edit-of-price-portion-in-dispatch-note-module-manually-from-logistic-job-record.json").then((data) => {
            this.data = data;
            CONTAINER_PROJECT = this.data.CONTAINERS.PROJECT,
            CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNITS
            CONTAINER_COLUMNS_CONTROLLING_UNITS = this.data.CONTAINER_COLUMNS.CONTROLLING_UNITS;
          
            CONTAINER_COLUMNS_CONTROLLING_UNIT2 = this.data.CONTAINER_COLUMNS.CONTROLLING_UNIT2;
          
            CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;
            CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
            CONTAINERS_PLANT_PRICE_LISTS = this.data.CONTAINERS.PLANT_PRICE_LISTS;
            PLANT_PARAMETERS = {
                [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION,
                [app.GridCells.UOM_FK]:CONTAINERS_PLANT.UOM,
                [app.GridCells.PLANT_GROUP_FK]: CONTAINERS_PLANT.GROUP,
                [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
                [app.GridCells.PLANT_TYPE_FK]: CONTAINERS_PLANT.PLANT_TYPE,
                [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
                [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
            },
            MODAL_PLANT_ALLOCATION = this.data.MODAL.PLANT_ALLOCATION;
          
            CONTAINER_COLUMNS_JOBS = this.data.CONTAINER_COLUMNS.JOBS
            CONTAINER_JOBS = this.data.CONTAINERS.JOBS
            CONTAINER_COLUMNS_CONDITIONS = this.data.CONTAINER_COLUMNS.CONDITIONS
            CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS = this.data.CONTAINERS.WORK_OPERATION_TYPE_CONDITIONS
            CONTAINER_COLUMNS_DISPATCHING_HEADER = this.data.CONTAINER_COLUMNS.DISPATCHING_HEADER
            CONTAINERS_DISPATCHING_HEADER = this.data.CONTAINERS.DISPATCHING_HEADER
            CONTAINER_DATA_RECORD = this.data.CONTAINERS.DATA_RECORD;
            CONTAINER_COLUMNS_DISPATCHING_RECORD = this.data.CONTAINER_COLUMNS.DISPATCHING_RECORD

            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });
    })

    after(() => {
        cy.LOGOUT();
    });

    it("TC - API: Create project A with logistic job and controlling unit", function () {
        let CONTROLLING_UNIT_A_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true"]
        }
        

        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _commonAPI.createProject()
            .then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
                _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_A_PARAMETERS)
            });
    })

    it("TC - API: Create project B with logistic job and controlling unit", function () {
        let CONTROLLING_UNIT_B_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
            [app.GridCells.UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true"],
            [app.GridCells.IS_PLANNING_ELEMENT]:["true", "true"],
            [app.GridCells.IS_ACCOUNTING_ELEMENT]:["true", "true"],
            [app.GridCells.IS_TIMEKEEPING_ELEMENT]:["true", "true"],
            [app.GridCells.IS_BILLING_ELEMENT]:["true", "true"]
        }

        _commonAPI.createProject()
            .then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2')).pinnedItem();
                cy.wait(1000)
                _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_2'), 2, CONTROLLING_UNIT_B_PARAMETERS)
            });
    })

    it("TC - Create plant in plant master and price list in commercial data", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
            _common.setup_gridLayout(cnt.uuid.PLANT, CONTAINER_COLUMNS_PLANT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PLANT)
        _common.create_newRecord(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE);
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT,  app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_PLANT.UOM);
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to save data
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        cy.wait(1000)//required wait to save data
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_CONTROLLING, app.FooterTab.CONTROLLING_UNIT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_CONTROLLING)
        });
        _common.create_newRecord(cnt.uuid.PLANT_CONTROLLING)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to appear dropdown
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_CONTROLLING, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_CNT_CODE_0'))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICE_LISTS, app.FooterTab.PRICE_LISTS)
            _common.waitForLoaderToDisappear()
        });
        _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICE_LISTS)
        _common.create_newRecord(cnt.uuid.PLANT_PRICE_LISTS)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, CONTAINERS_PLANT.PRICE_LIST2)
        cy.wait(1000)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.UOM2)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, CONTAINERS_PLANT.PRICE_LIST)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, CONTAINERS_PLANT.PRICE_LIST)
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_3, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_4, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_5, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_6, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Create plant location record from wizard', function () {
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_INITIAL_ALLOCATION_FOR_PLANTS)
        _common.waitForLoaderToDisappear()
        ALLOCATION_FOR_PLANTS_PARAMETER = {
            [commonLocators.CommonLabels.JOB]: Cypress.env('API_PROJECT_NUMBER_1'),
            [commonLocators.CommonLabels.ALLOCATED_FROM]: MODAL_PLANT_ALLOCATION.ALLOCATED_FROM,
            [app.GridCells.WORK_OPERATION_TYPE_FK]: MODAL_PLANT_ALLOCATION.WORK_OPERATION_TYPE
        }

        _logesticPage.create_initialAllocationForPlants_fromWizard_byClass(ALLOCATION_FOR_PLANTS_PARAMETER, CONTAINERS_PLANT.PLANT_TYPE)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
    })

    it("TC - Create job record in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS, 0);
            _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_JOBS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_JOBS.controllingunitfk, CONTAINER_COLUMNS_JOBS.pricinggroupfk], cnt.uuid.JOBS)
        });
        _common.clear_subContainerFilter(cnt.uuid.JOBS)
        _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.maximizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
        cy.wait(1000)//required wait to enable data input fields
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.JOBS, app.GridCells.CONTROLLING_UNIT_FK)
        cy.wait(1000)//required wait to save data
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()        
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.CONTROLLING_UNIT_FK,Cypress.env('API_CNT_CODE_1'), commonLocators.CommonKeys.GRID)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICE, app.FooterTab.PLANT_PRICES);
            _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICE)
        });
        _common.create_newRecord(cnt.uuid.PLANT_PRICE)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_PRICE, app.GridCells.PLANT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE)
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, MODAL_PLANT_ALLOCATION.WORK_OPERATION)
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE,app.GridCells.IS_MANUAL,commonLocators.CommonKeys.CHECK)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE, app.GridCells.PRICE_PORTION_3, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE, app.GridCells.PRICE_PORTION_4, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE, app.GridCells.PRICE_PORTION_5, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE, app.GridCells.PRICE_PORTION_6, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])       
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create logistic price condition", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_PRICE_CONDITION)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONDITIONS, app.FooterTab.CONDITIONS);
            _common.setup_gridLayout(cnt.uuid.CONDITIONS, CONTAINER_COLUMNS_CONDITIONS)
            _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        });
        _common.maximizeContainer(cnt.uuid.CONDITIONS)
        _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        _common.create_newRecord(cnt.uuid.CONDITIONS)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, CONDITION_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, CONDITION_DESC)
        _common.set_cellCheckboxValue(cnt.uuid.CONDITIONS, app.GridCells.IS_HANDLING_CHARGE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONDITIONS)
        _common.select_rowHasValue(cnt.uuid.CONDITIONS, CONDITION_CODE)
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.FooterTab.PLANT_CATALOG_PRICELISTS);
        });
        _common.clear_subContainerFilter(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, CONTAINER_DATA_RECORD.DATA_RECORD1)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_ITEM, app.FooterTab.WORK_OPERATION_TYPE_CONDITIONS);
            _common.maximizeContainer(cnt.uuid.PRICE_CONDITION_ITEM);
            _common.clear_subContainerFilter(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.minimizeContainer(cnt.uuid.PRICE_CONDITION_ITEM);
        });
        _common.create_newRecord(cnt.uuid.PRICE_CONDITION_ITEM)
        _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, MODAL_PLANT_ALLOCATION.WORK_OPERATION)
        _common.enterRecord_inNewRow(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_01, app.InputFields.INPUT_GROUP_CONTENT, MODAL_PLANT_ALLOCATION.PORTION_PERCENT[0])
        _common.enterRecord_inNewRow(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_02, app.InputFields.INPUT_GROUP_CONTENT, MODAL_PLANT_ALLOCATION.PORTION_PERCENT[0])
        _common.enterRecord_inNewRow(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_03, app.InputFields.INPUT_GROUP_CONTENT, MODAL_PLANT_ALLOCATION.PORTION_PERCENT[0])
        _common.enterRecord_inNewRow(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_04, app.InputFields.INPUT_GROUP_CONTENT, MODAL_PLANT_ALLOCATION.PORTION_PERCENT[0])
        _common.enterRecord_inNewRow(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_05, app.InputFields.INPUT_GROUP_CONTENT, MODAL_PLANT_ALLOCATION.PORTION_PERCENT[0])
        _common.enterRecord_inNewRow(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.PERCENTAGE_06, app.InputFields.INPUT_GROUP_CONTENT, MODAL_PLANT_ALLOCATION.PORTION_PERCENT[0])       
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        _common.edit_caretDropdown_fromModal_byClass(app.ModalInputFields.PRICING_GROUP_FK)
        _common.select_ItemFromPopUpList(commonLocators.CommonKeys.LIST, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PLANT_PRICING_GROUP1)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Assign price condition in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'));
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS);
            _common.search_inSubContainer(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
            _common.waitForLoaderToDisappear()
            _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        });
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PRICE_CONDITION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONDITION_CODE)
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_JOBS.BUSINESS_PARTNER)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PRICING_GROUP_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,CONTAINER_JOBS.PLANT_PRICING_GROUP1)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create record in dispatching notes", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.create_newRecord(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()    

        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, CONTAINERS_DISPATCHING_HEADER.EFFECTIVE_DATE)
         _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()    

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB1_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB2_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_DISPATCHING_HEADER.RUBRIC_CATEGORY)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()

        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE)
        cy.wait(1000)//required wait to capture code
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE, "DISPATCH_CODE")
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue, (cnt.uuid.DISPATCHING_HEADER,Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        cy.REFRESH_SELECTED_ENTITIES()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, app.FooterTab.PERFORMING_JOB_PLANT_LOCATIONS,0);
        });
        _common.select_rowHasValue(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS,PLANT_CODE) 
        _common.dragDrop_dataToContainer(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS,cnt.uuid.DISPATCHING_RECORD,app.GridCells.PLANT_FK,PLANT_CODE)
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_RECORD, CONTAINER_COLUMNS_DISPATCHING_RECORD)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_RECORD, CONTAINERS_DISPATCHING_HEADER.RECORD_TYPE1)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
        });
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.TRANSPORT_PLANNING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.PICKING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.LOADED)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.DELIVERED)
        _common.waitForLoaderToDisappear()
    })
    
    it("TC - Verify dispatch record and plant list added to plant in plant master should not get reflect as per the plant pricing logic or hierarchy", function () {
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        });
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_RECORD, PLANT_CODE)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.DISPATCHING_RECORD, app.GridCells.ARTICLE_FK, PLANT_CODE)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICE_LISTS, app.FooterTab.PRICE_LISTS)
            _common.waitForLoaderToDisappear()
        });
        _common.select_rowHasValue(cnt.uuid.PLANT_PRICE_LISTS,CONTAINERS_PLANT.PRICE_LIST)
        _common.assert_forNumericValues(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.assert_forNumericValues(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.assert_forNumericValues(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_3, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.assert_forNumericValues(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_4, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.assert_forNumericValues(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_5, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.assert_forNumericValues(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_6, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
    })

    it("TC - Validation price portion should get reflect same as logistic job plant prices", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_01,CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_02,CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_03,CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_04,CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_05,CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_06,CONTAINERS_PLANT_PRICE_LISTS.LOGISTICE_PRICE_LIST[0])
        _common.minimizeContainer(cnt.uuid.DISPATCHING_RECORD)
    }) 
     
    it("TC - Verify price portion should be editable in dispatch record if both the checkboxes is manual and is manual edit dispatch are checked", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES);
            _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        });
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES,CONTAINER_DATA_RECORD.DATA_TYPE)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.DATA_TYPES,CONTAINER_DATA_RECORD.DATA_TYPE)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORDS);
            _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
        });
        _common.search_inSubContainer(cnt.uuid.DATA_RECORDS,CONTAINER_DATA_RECORD.DATA_RECORD1)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.DATA_RECORDS,CONTAINER_DATA_RECORD.DATA_RECORD1)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS,app.GridCells.IS_MANUAL_EDIT_DISPATCHING,commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_RECORD, CONTAINERS_DISPATCHING_HEADER.RECORD_TYPE1)
        _common.waitForLoaderToDisappear()        
        _common.maximizeContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.edit_containerCell(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_01,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[0])
        _common.edit_containerCell(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_02,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[1])
        _common.edit_containerCell(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_03,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[2])
        _common.edit_containerCell(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_04,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[0])
        _common.edit_containerCell(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_05,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[1])
        _common.edit_containerCell(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_06,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[2])
        cy.SAVE()
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_01,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[0])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_02,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[1])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_03,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[2])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_04,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[0])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_05,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[1])
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD,app.GridCells.PRICE_PORTION_06,CONTAINERS_DISPATCHING_HEADER.RECORD_VALUE[2])
        _common.minimizeContainer(cnt.uuid.DISPATCHING_RECORD)
    })   
})

