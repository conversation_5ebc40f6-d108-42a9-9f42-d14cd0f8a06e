/*
 * Copyright(c) RIB Software GmbH
 */
import { Injectable } from '@angular/core';
import { BUSINESSPARTNER_CERTIFICATE_CHANGE_CERTIFICATE_STATUS_WIZARD, IChangeCertificateStatusWizardOptions } from '@libs/businesspartner/interfaces';
import { IBasicsChangeCertificateStatusWizardService } from '@libs/basics/interfaces';
import { IInitializationContext } from '@libs/platform/common';
import { IOrdHeaderEntity } from '@libs/sales/interfaces';
import { SalesContractContractsComplete } from '../model/complete-class/sales-contract-contracts-complete.class';
import { SalesContractActualCertificateDataService } from '../services/sales-contract-actual-certificate-data.service';
import { SalesContractContractsDataService } from '../services/sales-contract-contracts-data.service';
@Injectable({
	providedIn: 'root'
})
/**
 * Sales contract Change Certificate Status wizard service
 */
export class SalesContractChangeCertificateStatusWizardService implements IBasicsChangeCertificateStatusWizardService {
	public async execute(context: IInitializationContext) {
		const options: IChangeCertificateStatusWizardOptions<IOrdHeaderEntity, SalesContractContractsComplete> = {
			title: 'businesspartner.main.certificateStatusTitle',
			guid: '538325604b524f328fdf436fb14f1fc8',
			dataService: context.injector.get(SalesContractActualCertificateDataService),
			rootDataService: context.injector.get(SalesContractContractsDataService),
		};
		const statusService = await context.lazyInjector.inject(BUSINESSPARTNER_CERTIFICATE_CHANGE_CERTIFICATE_STATUS_WIZARD);
		return statusService.changeCertificateStatus<IOrdHeaderEntity, SalesContractContractsComplete>(options);
	}
}