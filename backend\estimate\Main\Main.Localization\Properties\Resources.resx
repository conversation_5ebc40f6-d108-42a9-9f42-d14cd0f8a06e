<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Err_DeleteAllResFailed" xml:space="preserve">
    <value>The Resources could not be deleted!</value>
  </data>
  <data name="Err_DeleteLineItem" xml:space="preserve">
    <value>The selected Line Item cannot be deleted!</value>
  </data>
  <data name="Err_DeleteResFailed" xml:space="preserve">
    <value>The selected Resource Item can not be deleted!</value>
  </data>
  <data name="Err_DeleteResWithDependencies" xml:space="preserve">
    <value>Selected Resource or Sub Items can not be deleted! First delete dependency and then try to delete again!</value>
  </data>
  <data name="Err_DisableProject" xml:space="preserve">
    <value>The project({0}) has been deleted. Please enable it manually!</value>
  </data>
  <data name="Err_GenrateLineItemFailed" xml:space="preserve">
    <value>Generate the line item  failed reason:</value>
  </data>
  <data name="Err_GetMaterailGroupByMaterailIdFailed" xml:space="preserve">
    <value>Can't find materialGroup by material({0}) in project({1})!</value>
  </data>
  <data name="Err_InvalidCompany" xml:space="preserve">
    <value>Invalid company for project({0})!</value>
  </data>
  <data name="Err_InvalidLineItem" xml:space="preserve">
    <value>Invalid Line Item!</value>
  </data>
  <data name="Err_MaterailCostCodeFkIsNull" xml:space="preserve">
    <value>The Material({0})'s CostCodeFk in PrcStructure is Null in project({1})!</value>
  </data>
  <data name="Err_NoBoQCurrencyDefined" xml:space="preserve">
    <value>BoQ's currency is missing in project({0})!</value>
  </data>
  <data name="Err_NoBoQDefined" xml:space="preserve">
    <value>No BoQ defined in project({0})!</value>
  </data>
  <data name="Err_NoBoQKeyDefined" xml:space="preserve">
    <value>No BoQ's Key defined in project({0})!</value>
  </data>
  <data name="Err_NoBoQNameSet" xml:space="preserve">
    <value>BoQ's Name is not set in project({0})!</value>
  </data>
  <data name="Err_NoCoCAndParentDefined" xml:space="preserve">
    <value>No Cost Code({0}) and its parent(not the root cost code) defined or not use in company in project({1})!</value>
  </data>
  <data name="Err_NoCoCCurrencyDefined" xml:space="preserve">
    <value>Cost Code({0})'s Currency doesn't defined in project({1})!</value>
  </data>
  <data name="Err_NoCoCForeignCurrencyDefined" xml:space="preserve">
    <value>Cost Code({0})'s ForeignCurrency({1}) doesn't defined in project({2})!</value>
  </data>
  <data name="Err_NoCompanyNoFound" xml:space="preserve">
    <value>Project{0}'s companyNo({1}) \r\n is not found in RIB 4.0!</value>
  </data>
  <data name="Err_NoCompanyNOFoundInXmlFile" xml:space="preserve">
    <value>No CompanyNo found in xml file:{0}!</value>
  </data>
  <data name="Err_NoCurrencyExchangeDefined" xml:space="preserve">
    <value>No currency exchange define in RIB 4.0!</value>
  </data>
  <data name="Err_NoDefineBidEstimateInSystemOption" xml:space="preserve">
    <value>RIB 4.0 Bid Estimate Folder is not in sysoption table.Please custormizing system option!</value>
  </data>
  <data name="Err_NoEstimatePACodeDefined" xml:space="preserve">
    <value>No estimate's PACode defined in project({0})!</value>
  </data>
  <data name="Err_NoMaterailDefined" xml:space="preserve">
    <value>Material({0}) not Found in project({1}) in RIB 4.0!</value>
  </data>
  <data name="Err_NoUoMDefind" xml:space="preserve">
    <value>No Uom({0}) define in RIB 4.0!</value>
  </data>
  <data name="Err_NoXmlFileFound" xml:space="preserve">
    <value>{0} file can be found!</value>
  </data>
  <data name="Err_SaveAssignResOrSubItemFailed" xml:space="preserve">
    <value>Assigned Resource or Sub Items can not be saved!</value>
  </data>
  <data name="Err_SaveBoQFailed" xml:space="preserve">
    <value>Improt the BoQ Item failed!</value>
  </data>
  <data name="Err_SaveProjectFailedNoPrjPrjGroup" xml:space="preserve">
    <value>Project({0}) is not found in RIB 4.0!\n No project's PrjGroup(ProjectGroup) to ceate new project!</value>
  </data>
  <data name="Err_SaveProjectFailedNoPrjRubric" xml:space="preserve">
    <value>Project({0}) is not found in RIB 4.0!\n No project's Rubric(ProjectCat) to create new project!</value>
  </data>
  <data name="Err_SaveResFailed" xml:space="preserve">
    <value>The selected Resource Item could not be saved!!</value>
  </data>
  <data name="Warn_CoCParentReplace" xml:space="preserve">
    <value>Warning:Fixed unit Rate and Labor Cost Code in costcode({0}) are different from its parent({1}) in baseLine!</value>
  </data>
  <data name="_ImportSuccess" xml:space="preserve">
    <value>Data Export to RIB 4.0 successful!</value>
  </data>
  <data name="_ModuleName" xml:space="preserve">
    <value>Estimate.Main</value>
    <comment>string to identify module name for translation</comment>
  </data>
  <data name="Err_NullBidEstimateInSystemOption" xml:space="preserve">
    <value>RIB 4.0 Bid Estimate is null in system option!</value>
  </data>
  <data name="Err_NoPrjGroupDefine" xml:space="preserve">
    <value>Create Project failed:projectGroup({0}) is not found in RIB 4.0!</value>
  </data>
  <data name="Label_ActivitySchedule" xml:space="preserve">
    <value>Activity Schedule</value>
  </data>
  <data name="Label_Boq" xml:space="preserve">
    <value>BoQ</value>
  </data>
  <data name="Err_FailedReason" xml:space="preserve">
    <value>The failed reason is:</value>
  </data>
  <data name="Err_NoFolder" xml:space="preserve">
    <value>Can't find the folder({0}).Please check RIB 4.0 Bid Estimate in system option!</value>
  </data>
  <data name="Err_NoSubitemToCreateLineItem" xml:space="preserve">
    <value>No subitem to create line item!</value>
  </data>
  <data name="Err_NoXmlFile" xml:space="preserve">
    <value>Can't find the xml's file({0}).Please check xml’s file path setting both baseline and 4.0!  </value>
  </data>
  <data name="Err_NoEstimate" xml:space="preserve">
    <value>No estimate to create!</value>
  </data>
  <data name="Err_XMLExceptionTips" xml:space="preserve">
    <value>The XML's formatting error: </value>
  </data>
  <data name="Err_NoActivityIdDefined" xml:space="preserve">
    <value>No activity Id({0}) Defined!</value>
  </data>
  <data name="Warn_AssemblyFound" xml:space="preserve">
    <value>{0} assembly [{1}] is found</value>
    <comment>Cos script hint</comment>
  </data>
  <data name="Warn_AssemblyMissing" xml:space="preserve">
    <value>Assembly [{0}] is missing, create new assembly automatically</value>
    <comment>Cos script hint</comment>
  </data>
  <data name="Warn_ResFacade" xml:space="preserve">
    <value>IScriptResourceTypeFacade [{0}] is missing</value>
    <comment>Cos script hint </comment>
  </data>
  <data name="Warn_ResMissing" xml:space="preserve">
    <value>{0} [{1}] is missing, create new {0} automatically</value>
    <comment>Cos script hint </comment>
  </data>
  <data name="Err_CreateLogFailed" xml:space="preserve">
    <value>Create log failed:</value>
  </data>
  <data name="Warn_BidImport" xml:space="preserve">
    <value>Warning:</value>
  </data>
  <data name="Label_LeadingStructure_BasicCostGroup1" xml:space="preserve">
    <value>Basic Cost Group 1</value>
  </data>
  <data name="Label_LeadingStructure_BasicCostGroup2" xml:space="preserve">
    <value>Basic Cost Group 2</value>
  </data>
  <data name="Label_LeadingStructure_BasicCostGroup3" xml:space="preserve">
    <value>Basic Cost Group 3</value>
  </data>
  <data name="Label_LeadingStructure_BasicCostGroup4" xml:space="preserve">
    <value>Basic Cost Group 4</value>
  </data>
  <data name="Label_LeadingStructure_BasicCostGroup5" xml:space="preserve">
    <value>Basic Cost Group 5</value>
  </data>
  <data name="Label_LeadingStructure_ControllingUnit" xml:space="preserve">
    <value>Controlling Unit</value>
  </data>
  <data name="Label_LeadingStructure_Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Label_LeadingStructure_ProjectCostGroup1" xml:space="preserve">
    <value>Project Cost Group 1</value>
  </data>
  <data name="Label_LeadingStructure_ProjectCostGroup2" xml:space="preserve">
    <value>Project Cost Group 2</value>
  </data>
  <data name="Label_LeadingStructure_ProjectCostGroup3" xml:space="preserve">
    <value>Project Cost Group 3</value>
  </data>
  <data name="Label_LeadingStructure_ProjectCostGroup4" xml:space="preserve">
    <value>Project Cost Group 4</value>
  </data>
  <data name="Label_LeadingStructure_ProjectCostGroup5" xml:space="preserve">
    <value>Project Cost Group 5</value>
  </data>
  <data name="Warn_MissBoq" xml:space="preserve">
    <value>Boq [{0}] is missing</value>
    <comment>Cos script hint</comment>
  </data>
  <data name="Label_ColumId_BasCurrencyFk" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="Label_ColumId_Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="Label_ColumId_CommentText" xml:space="preserve">
    <value>Comment</value>
  </data>
  <data name="Label_ColumId_CostFactor1" xml:space="preserve">
    <value>Cost-Factor 1</value>
  </data>
  <data name="Label_ColumId_CostFactor2" xml:space="preserve">
    <value>Cost-Factor 2</value>
  </data>
  <data name="Label_ColumId_CostFactorCc" xml:space="preserve">
    <value>Cost-Factor CC</value>
  </data>
  <data name="Label_ColumId_CostFactorDetail1" xml:space="preserve">
    <value>Cost-Factor Details 1</value>
  </data>
  <data name="Label_ColumId_CostFactorDetail2" xml:space="preserve">
    <value>Cost-Factor Details 2</value>
  </data>
  <data name="Label_ColumId_CostTotal" xml:space="preserve">
    <value>Cost Total</value>
  </data>
  <data name="Label_ColumId_CostUnit" xml:space="preserve">
    <value>Cost/Unit</value>
  </data>
  <data name="Label_ColumId_CostUnitLineitem" xml:space="preserve">
    <value>Cost/Unit Line Item</value>
  </data>
  <data name="Label_ColumId_CostUnitSubitem" xml:space="preserve">
    <value>Cost/Unit Sub-Item</value>
  </data>
  <data name="Label_ColumId_CostUnitTarget" xml:space="preserve">
    <value>Cost/Unit Item</value>
  </data>
  <data name="Label_ColumId_EfficiencyFactor1" xml:space="preserve">
    <value>Efficiency-Factor 1</value>
  </data>
  <data name="Label_ColumId_EfficiencyFactor2" xml:space="preserve">
    <value>Efficiency-Factor 2</value>
  </data>
  <data name="Label_ColumId_EfficiencyFactorDetail1" xml:space="preserve">
    <value>Efficiency-Factor Details 1</value>
  </data>
  <data name="Label_ColumId_EfficiencyFactorDetail2" xml:space="preserve">
    <value>Efficiency-Factor Details 2</value>
  </data>
  <data name="Label_ColumId_HoursTotal" xml:space="preserve">
    <value>Hours Total</value>
  </data>
  <data name="Label_ColumId_HoursUnit" xml:space="preserve">
    <value>Hours/Unit</value>
  </data>
  <data name="Label_ColumId_HoursUnitLineitem" xml:space="preserve">
    <value>Hours/Unit Line Item</value>
  </data>
  <data name="Label_ColumId_HoursUnitSubitem" xml:space="preserve">
    <value>Hours/Unit Sub-Item</value>
  </data>
  <data name="Label_ColumId_HoursUnitTarget" xml:space="preserve">
    <value>Hours/Unit Item</value>
  </data>
  <data name="Label_ColumId_IsDisabled" xml:space="preserve">
    <value>Disabled</value>
  </data>
  <data name="Label_ColumId_IsLumpSum" xml:space="preserve">
    <value>Lump Sum</value>
  </data>
  <data name="Label_ColumId_ProductivityFactor" xml:space="preserve">
    <value>Productivity-Factor</value>
  </data>
  <data name="Label_ColumId_ProductivityFactorDetail" xml:space="preserve">
    <value>Productivity-Factor Detail</value>
  </data>
  <data name="Label_ColumId_Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="Label_ColumId_QuantityDetail" xml:space="preserve">
    <value>Quantity Details</value>
  </data>
  <data name="Label_ColumId_QuantityFactor1" xml:space="preserve">
    <value>Quantity-Factor 1</value>
  </data>
  <data name="Label_ColumId_QuantityFactor2" xml:space="preserve">
    <value>Quantity-Factor 2</value>
  </data>
  <data name="Label_ColumId_QuantityFactor3" xml:space="preserve">
    <value>Quantity-Factor 3</value>
  </data>
  <data name="Label_ColumId_QuantityFactor4" xml:space="preserve">
    <value>Quantity-Factor 4</value>
  </data>
  <data name="Label_ColumId_QuantityFactorCc" xml:space="preserve">
    <value>Quantity-Factor CC</value>
  </data>
  <data name="Label_ColumId_QuantityFactorDetail1" xml:space="preserve">
    <value>Quantity-Factor Details 1</value>
  </data>
  <data name="Label_ColumId_QuantityFactorDetail2" xml:space="preserve">
    <value>Quantity-Factor Details 2</value>
  </data>
  <data name="Label_ColumId_QuantityInternal" xml:space="preserve">
    <value>Quantity Internal</value>
  </data>
  <data name="Label_ColumId_QuantityReal" xml:space="preserve">
    <value>Quantity Real</value>
  </data>
  <data name="Label_ColumId_QuantityTotal" xml:space="preserve">
    <value>Quantity Total</value>
  </data>
  <data name="Label_ColumId_QuantityUnitTarget" xml:space="preserve">
    <value>Quantity/Unit Item</value>
  </data>
  <data name="Label_ColumId_UomFk" xml:space="preserve">
    <value>Uom</value>
  </data>
  <data name="Warn_MissProject" xml:space="preserve">
    <value>Missing project, please select a project</value>
    <comment>Cos script hint</comment>
  </data>
  <data name="Err_TransferRevenueFailed" xml:space="preserve">
    <value>Failed To Transfer Revenue To Estimate, Error Message is ({0}).</value>
  </data>
  <data name="Err_NoMaterialIdDefined" xml:space="preserve">
    <value>No Material's ExternalID({0})  defined in xml's flie!</value>
  </data>
  <data name="Err_CanNotSwitchToCompany" xml:space="preserve">
    <value>Can not switch to company</value>
  </data>
  <data name="Err_InvalidCompanyForProjectPublicApi" xml:space="preserve">
    <value>Invalid Company for Project Public Api</value>
  </data>
  <data name="Err_NoExecutePermissionForUseProjecPublicApi" xml:space="preserve">
    <value>No execute permission for Use Project Public Api!</value>
  </data>
  <data name="Err_HasToBePassedUrl" xml:space="preserve">
    <value>Either the project number or the project ID has to be passed to this Url</value>
  </data>
  <data name="Modify_Function_Replace_CCByAssembly" xml:space="preserve">
    <value>Replace Cost Code By Assembly</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Modify_Function_Replace_CCByCostCode" xml:space="preserve">
    <value>Replace Cost Code By Cost Code</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Modify_Function_Replace_CCByMaterial" xml:space="preserve">
    <value>Replace Cost Code By Material</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Modify_Function_Replace_CostCode" xml:space="preserve">
    <value>Cost Code</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Modify_Function_Replace_MAByAssembly" xml:space="preserve">
    <value>Replace Material By Assembly</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Modify_Function_Replace_MAByCostCode" xml:space="preserve">
    <value>Replace Material By Cost Code</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Modify_Function_Replace_MAByMaterial" xml:space="preserve">
    <value>Replace Material By Material</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Modify_Function_Replace_Material" xml:space="preserve">
    <value>Material</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Modify_Function_Replace_Root" xml:space="preserve">
    <value>Estimate - Elements</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Label_ColumId_CostTypeFk" xml:space="preserve">
    <value>Cost Type</value>
  </data>
  <data name="Label_ColumId_ResourceFlagFk" xml:space="preserve">
    <value>Resource Flag</value>
  </data>
  <data name="Label_ColumId_Budget" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="Label_ColumId_ProcurementPackage" xml:space="preserve">
    <value>Procurement-Package</value>
  </data>
  <data name="Warn_Resource_NotFound" xml:space="preserve">
    <value>No modifiable resource can be found in this leading structure filter and resource filter condition ! Please recheck your resources or filter.</value>
  </data>
  <data name="Err_Out_Of_Range_Update_Estimate" xml:space="preserve">
    <value>An exception is thrown when updating estimation because the calculation value is out of range. Some data can't be saved to system. Please recheck your input.</value>
    <comment>number too bid when update estimate</comment>
  </data>
  <data name="EstIcoDown" xml:space="preserve">
    <value>From Structure</value>
  </data>
  <data name="EstIcoMinus" xml:space="preserve">
    <value>No Relation</value>
  </data>
  <data name="EstIcoUp" xml:space="preserve">
    <value>To Structure</value>
  </data>
  <data name="Error_ModelObjectId" xml:space="preserve">
    <value>Must define a obj property in property object</value>
  </data>
  <data name="Error_ModelObjectId_Type" xml:space="preserve">
    <value>Type Error: Assign a model object to obj property</value>
  </data>
  <data name="ERR_ConSysInstanceCannotBeDeletedAsItIsUsedInLineItem" xml:space="preserve">
    <value>Construction System Instance cannot be deleted as it is used in Line Items.</value>
    <comment>EstemateMainLineItemRelationInfo</comment>
  </data>
  <data name="ERR_ConSysMasterCannotBeDeletedAsItIsUsedInLineItem" xml:space="preserve">
    <value>Construction System Master cannot be deleted as it is used in Line Items.</value>
    <comment>EstemateMainLineItemRelationInfo</comment>
  </data>
  <data name="Err_OverwriteLineitemWhenChangeAssemblyTemplate" xml:space="preserve">
    <value>Resource cannot be deleted as it is used in Procurement Package. </value>
  </data>
  <data name="Err_NoCurrencyDefinded" xml:space="preserve">
    <value>Not found currency conversion({0}) in Home Currency ({1}).</value>
  </data>
  <data name="Label_ExportColumn_AssemblyCategory" xml:space="preserve">
    <value>Assembly Category</value>
  </data>
  <data name="Label_ExportColumn_BoQHeader" xml:space="preserve">
    <value>BoQ Header RefNo</value>
  </data>
  <data name="Label_ExportColumn_ParentLevelAssembly" xml:space="preserve">
    <value>Parent Level of Assembly Category</value>
  </data>
  <data name="Label_ExportColumn_RecordType" xml:space="preserve">
    <value>Record Type</value>
  </data>
  <data name="Label_ExportColumn_Structure" xml:space="preserve">
    <value>Structure FK</value>
  </data>
  <data name="Label_ExportColumn_WICCategory" xml:space="preserve">
    <value>WIC BoQ Root Item Ref No</value>
  </data>
  <data name="Label_ExportColumn_WICGruop" xml:space="preserve">
    <value>WIC Group Ref No</value>
  </data>
  <data name="Label_ExportColumn_WarnningRecordType" xml:space="preserve">
    <value>Mapping Record Type is not integer!</value>
  </data>
  <data name="Label_ImportColumn_Code" xml:space="preserve">
    <value>The Code should not be setted default Value!</value>
  </data>
  <data name="Label_ColumId_DescriptionInfo" xml:space="preserve">
    <value>Decription</value>
  </data>
  <data name="Label_ColumId_IndirectCost" xml:space="preserve">
    <value>Indirect Cost</value>
  </data>
  <data name="Label_ColumId_IsGerneratePrc" xml:space="preserve">
    <value>IsGerneratePrc</value>
  </data>
  <data name="Label_ColumId_Job" xml:space="preserve">
    <value>Job</value>
  </data>
  <data name="Label_ColumId_ShortKey" xml:space="preserve">
    <value>Short Key</value>
  </data>
  <data name="Label_ColumId_ProcurementSubPackage" xml:space="preserve">
    <value>Procurement-Sub-Package</value>
  </data>
  <data name="Label_ColumId_ProcurementStructure" xml:space="preserve">
    <value>Procurement Structure</value>
  </data>
  <data name="Warn_AA" xml:space="preserve">
    <value>Warning:AA (Advanced AllowanceCc) has not been transferred, Cost Code Definition of AA was not found in RIB 4.0.</value>
  </data>
  <data name="Warn_FM" xml:space="preserve">
    <value>Warning:FM (Final Markup) has not been transferred, Cost Code Definition of FM was not found in RIB 4.0.</value>
  </data>
  <data name="Warn_GC" xml:space="preserve">
    <value>Warning:GC (General Cost) has not been transferred, Cost Code Definition of GC was not found in RIB 4.0.</value>
  </data>
  <data name="Warn_Prj_CostFacterCC_IsZero" xml:space="preserve">
    <value>Project Cost Code({0}) CostFactorCC is Zero.</value>
  </data>
  <data name="Warn_Prj_QuantityFacterCC_IsZero" xml:space="preserve">
    <value>Project Cost Code({0}) QuantityFactorCC is Zero.</value>
  </data>
  <data name="Error_NoEstimateRoot" xml:space="preserve">
    <value>No estimate's EstimateRoot defined in project({0})!</value>
  </data>
  <data name="LineItem_Without_Structure" xml:space="preserve">
    <value>Line Item without Structure</value>
  </data>
  <data name="Action_WF_DeepCopy_Estimate" xml:space="preserve">
    <value>DeepCopyEstimateAction</value>
  </data>
  <data name="Action_WF_Est_HeaderId" xml:space="preserve">
    <value>EstHeaderId</value>
  </data>
  <data name="Action_WF_IsCpoy_Budget" xml:space="preserve">
    <value>Copy Budget</value>
  </data>
  <data name="Action_WF_New_Estimate_Type" xml:space="preserve">
    <value>New Estimate Type</value>
  </data>
  <data name="Action_WF_DeepCopy_Fialed" xml:space="preserve">
    <value>DeepCopy Fialed!</value>
  </data>
  <data name="Action_WF_EstHeader_No_Defined" xml:space="preserve">
    <value>1, No EstHeader Defined; 2, The EstHeader is Version Estimate or GC Estimate.</value>
  </data>
  <data name="Action_WF_Miss_EstHeaderId" xml:space="preserve">
    <value>Parameter EstHeaderId is missing!</value>
  </data>
  <data name="Action_WF_Miss_ProjecctId" xml:space="preserve">
    <value>Parameter ProjecctId is missing!</value>
  </data>
  <data name="ERR_ConSysInstanceIsUsedInLineItemDoYouDelete" xml:space="preserve">
    <value>Construction System Instance is used in Line Items. Do you really want to delete it?</value>
    <comment>EstemateMainLineItemRelationInfo</comment>
  </data>
  <data name="Label_ColumId_CostUnitOriginal" xml:space="preserve">
    <value>Cost/Unit Original</value>
  </data>
  <data name="Warn_ExistingCharacteristic" xml:space="preserve">
    <value>Characteristic {0} already exists in line item </value>
    <comment>Cos script hint</comment>
  </data>
  <data name="Warn_ItemCode_NoSetting_NumberRangs" xml:space="preserve">
    <value>Please setting the Number Rangs in Company module.</value>
  </data>
  <data name="Warn_ItemCode_NullOrEmpty" xml:space="preserve">
    <value>The code is Null or Empty.Please check Number Rangs setting.</value>
  </data>
  <data name="Warn_ItemCode_Same" xml:space="preserve">
    <value>The code is same with existing item({0}).Please check Number Rangs setting.</value>
  </data>
  <data name="Modify_Function_Replace_Assembly" xml:space="preserve">
    <value>Assembly</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Modify_Function_Replace_AssemblyByAssembly" xml:space="preserve">
    <value>Replace Assembly By Assembly</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Error_No_CostCode_Assign2Material" xml:space="preserve">
    <value>No assigned Cost Code to Material({0}) at BoQ({1})!</value>
  </data>
  <data name="Error_CostCode_NoCurrency" xml:space="preserve">
    <value>The CostCode({0}) has no Currency.</value>
  </data>
  <data name="Error_No_default_CostGroupConfigType" xml:space="preserve">
    <value>No defualt CostGroup Configuration Type to create Project.</value>
  </data>
  <data name="Warn_CostGroup_EmptyCode" xml:space="preserve">
    <value>Some Cost Group Codes are empty!</value>
  </data>
  <data name="Warn_SurchargeCostCode" xml:space="preserve">
    <value>Warning:Surcharge Cost Code has not been transferred, Cost Code Definition of Surcharge Item was not found in System Option.</value>
  </data>
  <data name="Hint_ExecuteEveryone" xml:space="preserve">
    <value>Finised: {0} / Total: {1}</value>
  </data>
  <data name="Hint_ExecuteForLineItemAdjustment" xml:space="preserve">
    <value>Execute for line item adjustment</value>
  </data>
  <data name="Hint_ExecuteOnce" xml:space="preserve">
    <value>Execute only for current level once</value>
  </data>
  <data name="Hint_CalculateDetail" xml:space="preserve">
    <value>Calculate detail cost: {0} seconds.</value>
  </data>
  <data name="Hint_DeleteOldData" xml:space="preserve">
    <value>Delete old data cost: {0} seconds.</value>
  </data>
  <data name="Hint_ExecuteScript" xml:space="preserve">
    <value>Execute script cost: {0} seconds</value>
  </data>
  <data name="Hint_RecalculateLineItems" xml:space="preserve">
    <value>Recalculate lineItems and resources cost: {0} seconds.</value>
  </data>
  <data name="Hint_RegisterRule" xml:space="preserve">
    <value>Register rules cost: {0} seconds.</value>
  </data>
  <data name="Hint_SaveChanges" xml:space="preserve">
    <value>Save changes to database cost: {0} seconds</value>
  </data>
  <data name="Hint_UpdateEstimate" xml:space="preserve">
    <value>Update estimate cost: {0} hours, {1} minutes, and {2} seconds.</value>
  </data>
  <data name="Warn_Language_NoDefined" xml:space="preserve">
    <value>Warning:No Language({0}) Defined in RIB 4.0.</value>
  </data>
  <data name="Label_ExportColumn_Activity" xml:space="preserve">
    <value>Activity Code</value>
  </data>
  <data name="Warn_No_CostGroup_Assigned" xml:space="preserve">
    <value>Some Cost Groups were not assigned.Please do synchronization for enterprise Cost Group.</value>
  </data>
  <data name="Err_No_Defined_Project" xml:space="preserve">
    <value>Can not find the Project({0})!</value>
  </data>
  <data name="Error_Not_Defined_Data" xml:space="preserve">
    <value>Not Defined Data to export, please check the cost group assignment.</value>
  </data>
  <data name="LineItemRefName" xml:space="preserve">
    <value>Line Items</value>
  </data>
  <data name="ProgressReportsRefName" xml:space="preserve">
    <value>Reported Progress</value>
  </data>
  <data name="Warn_Language_Reduplicate" xml:space="preserve">
    <value>Warning: {0} converted as {1}, but {1} language already has added, the translation({0}: {2}) will be ignored</value>
  </data>
  <data name="Label_ExportColumn_Schedule" xml:space="preserve">
    <value>Schedule Code</value>
  </data>
  <data name="Hint_LineItem_Code" xml:space="preserve">
    <value>The line item causing the error has a code of {0}</value>
  </data>
  <data name="Hint_LineItem_Parse_Error" xml:space="preserve">
    <value>The  {0} with a value of {1} cannot be parsed.  {2}.  Detail: {3}</value>
  </data>
  <data name="Hint_Param_Parse_Error" xml:space="preserve">
    <value>Parameter code {0} with a value of {1} cannot be parsed.  {2}. Detail: {3}</value>
  </data>
  <data name="Label_ColumId_RuleGenerated" xml:space="preserve">
    <value>Rule Generated</value>
  </data>
  <data name="ObjectFilter_ObjectProps_Parent" xml:space="preserve">
    <value>Object properties</value>
  </data>
  <data name="Lable_ExportColumn_Model" xml:space="preserve">
    <value>Model Code</value>
  </data>
  <data name="Warn_No_CostCode_AllowedChild" xml:space="preserve">
    <value>Warning: The Cost Code({0}) not find and the parent Cost Code({1}) not Project Child Allowed.</value>
  </data>
  <data name="Warn_BoQs_NotAllowToDelete" xml:space="preserve">
    <value>The BoQ Items with ids [{0}] are not allowed to delete!</value>
  </data>
  <data name="Warn_LineItemsWithActivity_Package_Found" xml:space="preserve">
    <value>The LineItems with codes [{0}] are assigned to either Activity or Procurement Package! Not allowed to delete!</value>
  </data>
  <data name="Warn_LineItems_NotDeleted" xml:space="preserve">
    <value>The LineItems with codes [{0}] can not be deleted because </value>
  </data>
  <data name="Err_Parameter_Code_Maximum_Length" xml:space="preserve">
    <value>The Parameter Code({0}) must be a string with a maximum length of 16 in project var.</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value />
  </data>
  <data name="Hint_Start_BidEstimate" xml:space="preserve">
    <value>Start Export to 4.0.</value>
  </data>
  <data name="Hint_Calculate_Estimate" xml:space="preserve">
    <value>Calculate Estimate.</value>
  </data>
  <data name="Hint_Create_Ord" xml:space="preserve">
    <value>Create Sales Contract.</value>
  </data>
  <data name="Hint_Insert_boqs" xml:space="preserve">
    <value>Import Boqs.</value>
  </data>
  <data name="Hint_Insert_Catalogs" xml:space="preserve">
    <value>Create/Update Parameters, Location and Classification.</value>
  </data>
  <data name="Hint_Insert_LineItems" xml:space="preserve">
    <value>Import Line Items.</value>
  </data>
  <data name="Hint_Read_Catalogs" xml:space="preserve">
    <value>Read the Catalogs: Activity, Controlling Unit, SubPackage, Location and Classification infos.</value>
  </data>
  <data name="Hint_Read_Esimate_Resource" xml:space="preserve">
    <value>Read Estimate  Resource Info.</value>
  </data>
  <data name="Hint_Read_Project_Info" xml:space="preserve">
    <value>Read Project Info.</value>
  </data>
  <data name="Action_WF_DeleteResources_Estimate" xml:space="preserve">
    <value>Delete Resources Action</value>
  </data>
  <data name="Action_WF_DeleteResources_Estimate_Failed" xml:space="preserve">
    <value>Delete Resources Failed</value>
  </data>
  <data name="Hint_Delete_Estimate" xml:space="preserve">
    <value>Before import LineItems and Boqs, Delete the Estimate first.</value>
  </data>
  <data name="Hint_Delete_BoQ" xml:space="preserve">
    <value>Before impor BoQ, delete BoQ first.</value>
  </data>
  <data name="Action_WF_Miss_EstLineItemIds" xml:space="preserve">
    <value>Parameter LineItem Ids is missing!</value>
  </data>
  <data name="Err_Invalid_Estimate" xml:space="preserve">
    <value>Invalid Estimate</value>
  </data>
  <data name="Warn_Estimate_IsGc" xml:space="preserve">
    <value>Esitmate is General Contractor</value>
  </data>
  <data name="Warn_Estimate_IsReadonly" xml:space="preserve">
    <value>Estimate is readonly</value>
  </data>
  <data name="Warn_LineItem_IsReadonly" xml:space="preserve">
    <value>{0}  is readonly</value>
  </data>
  <data name="Warn_ResourceUsedByPackage" xml:space="preserve">
    <value>Some resources are used by package.</value>
  </data>
  <data name="Hint_CalculateRiskEscalation" xml:space="preserve">
    <value>Calculate Risk And Escalation: cost: {0} seconds.</value>
  </data>
  <data name="Warn_Lumpsum_Absolute_Detail" xml:space="preserve">
    <value>The Boq RN({0}) and SubItemNo({1}).</value>
  </data>
  <data name="Warn_Lumpsum_Absolute_Title" xml:space="preserve">
    <value>There are lumpsum absolute records in following Items/sub items in the Project which needs user attention in Line Items in RIB 4.0. User has to manually update the cost for all such line items in RIB 4.0, Detail Info:</value>
  </data>
  <data name="Label_ColumId_CostFactors" xml:space="preserve">
    <value>Cost Factors</value>
    <comment>Estimate Rounding Column</comment>
  </data>
  <data name="Label_ColumId_ItemPriceTotal" xml:space="preserve">
    <value>Item Price Total</value>
    <comment>Estimate Rounding Column</comment>
  </data>
  <data name="Label_ColumId_PriceUnitItem" xml:space="preserve">
    <value>Price/Unit Item</value>
    <comment>Estimate Rounding Column</comment>
  </data>
  <data name="Label_ColumId_QuantityFactors" xml:space="preserve">
    <value>Quantity Factors</value>
    <comment>Estimate Rounding Column</comment>
  </data>
  <data name="Label_ColumId_QuantityTarget" xml:space="preserve">
    <value>AQ Quantity</value>
    <comment>Estimate Rounding Column</comment>
  </data>
  <data name="Label_ColumId_WqQuantityTarget" xml:space="preserve">
    <value>WQ Quantity</value>
    <comment>Estimate Rounding Column</comment>
  </data>
  <data name="Estimate_Confidence" xml:space="preserve">
    <value>Estimate Confidence</value>
  </data>
  <data name="Est_Confidence_BoqItemFk" xml:space="preserve">
    <value>Not assigned to BoQ</value>
  </data>
  <data name="Est_Confidence_Budget" xml:space="preserve">
    <value>Zero Budget (budget = 0)</value>
  </data>
  <data name="Est_Confidence_Budgeting" xml:space="preserve">
    <value>Budgeting</value>
  </data>
  <data name="Est_Confidence_Costing" xml:space="preserve">
    <value>Costing</value>
  </data>
  <data name="Est_Confidence_Grand_Total" xml:space="preserve">
    <value>Unpriced (grand total = 0)</value>
  </data>
  <data name="Est_Confidence_IsDisabled" xml:space="preserve">
    <value>Disabled (disabled = true)</value>
  </data>
  <data name="Est_Confidence_IsFixedBudget" xml:space="preserve">
    <value>Fixed Budget (fixed budget = true or fixed budget unit = true)</value>
  </data>
  <data name="Est_Confidence_IsFixedPrice" xml:space="preserve">
    <value>Plug Rate(fixed price = true)</value>
  </data>
  <data name="Est_Confidence_IsGc_False" xml:space="preserve">
    <value>Direct Cost Items (general cost = false)</value>
  </data>
  <data name="Est_Confidence_IsGc_True" xml:space="preserve">
    <value>General Cost Items (general cost = true)</value>
  </data>
  <data name="Est_Confidence_IsIncluded" xml:space="preserve">
    <value>Included Items(included = true)</value>
  </data>
  <data name="Est_Confidence_IsLumpsum" xml:space="preserve">
    <value>Lumpsum (lumpsum = true)</value>
  </data>
  <data name="Est_Confidence_IsNoEscalation" xml:space="preserve">
    <value>No Escalation (no escalation = true)</value>
  </data>
  <data name="Est_Confidence_IsNoMarkup" xml:space="preserve">
    <value>No Markup(no mark up = true)</value>
  </data>
  <data name="Est_Confidence_MdcControllingUnitFk" xml:space="preserve">
    <value>Not assigned to Controlling Unit</value>
  </data>
  <data name="Est_Confidence_Metadata_Association" xml:space="preserve">
    <value>Metadata Association</value>
  </data>
  <data name="Est_Confidence_Packages" xml:space="preserve">
    <value>Packages</value>
  </data>
  <data name="Est_Confidence_PrcPackageFk_IsMaterial" xml:space="preserve">
    <value>Number of Material Packages</value>
  </data>
  <data name="Est_Confidence_PrcPackageFk_IsService" xml:space="preserve">
    <value>Number of Service Packages</value>
  </data>
  <data name="Est_Confidence_Pricing" xml:space="preserve">
    <value>Pricing</value>
  </data>
  <data name="Est_Confidence_PsdActivityFk" xml:space="preserve">
    <value>Not assigned to Activities</value>
  </data>
  <data name="Bulk_Err_BoqSplitQuantityFkExist" xml:space="preserve">
    <value>The current line item (or one of the Line items) that you want to assign a BoQ item has a link to one of the BoQ Split Quantities</value>
  </data>
  <data name="Bulk_Err_SelectBoqItemRef" xml:space="preserve">
    <value>Please select a BoQ Item</value>
  </data>
  <data name="Est_Confidence_Project_Changes" xml:space="preserve">
    <value>Project Changes</value>
  </data>
  <data name="Est_Confidence_PrjChanges_AcceptedInPrinciple" xml:space="preserve">
    <value>Accepted in principle</value>
  </data>
  <data name="Est_Confidence_PrjChanges_Announced" xml:space="preserve">
    <value>Announced</value>
  </data>
  <data name="Est_Confidence_PrjChanges_Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="Est_Confidence_PrjChanges_Identified" xml:space="preserve">
    <value>Identified</value>
  </data>
  <data name="Est_Confidence_PrjChanges_Rejected" xml:space="preserve">
    <value>Rejected</value>
  </data>
  <data name="Est_Confidence_PrjChanges_RejectedWithProtest" xml:space="preserve">
    <value>Rejected with protest</value>
  </data>
  <data name="Est_Confidence_PrjChanges_Submitted" xml:space="preserve">
    <value>Submitted</value>
  </data>
  <data name="Est_Confidence_PrjChanges_Withdrawn" xml:space="preserve">
    <value>Withdrawn</value>
  </data>
  <data name="Modify_Function_Revmoe_Resource" xml:space="preserve">
    <value>Remove Resources</value>
  </data>
  <data name="Warn_Not_Exist_EstType" xml:space="preserve">
    <value>DeepCopy Failed! Estimate Type does not exist, please check Input Parameters.</value>
  </data>
  <data name="Bulk_Err_SubQuantityBoQItemsErrormsg" xml:space="preserve">
    <value>Can not assign LineItem to BoQ items which contains sub quantity item(s).</value>
  </data>
  <data name="Est_Confidence_PrcPackageFk_PrcPackageGeneratedPrc" xml:space="preserve">
    <value>Package Created but Price not updated from Prc</value>
  </data>
  <data name="Modify_Function_Replace_EquipmentAssemblyByEquipmentAssembly" xml:space="preserve">
    <value>Replace Equipment Assembly By Equipment Assembly</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Plant_Totals_Assembly" xml:space="preserve">
    <value>Total Plant Assemblies</value>
  </data>
  <data name="Plant_Totals_Difference" xml:space="preserve">
    <value>Difference</value>
  </data>
  <data name="Plant_Totals_Stand" xml:space="preserve">
    <value>Total Stand-by Plant</value>
  </data>
  <data name="Warn_LineItems_NotDeleted_EstHeader_Readonly" xml:space="preserve">
    <value>The LineItems cannot be deleted because the status of Estimate Headers with codes [{0}] is Locked or Readonly</value>
  </data>
  <data name="Warn_LineItems_NotDelete_Ref_EstHeader_Readonly" xml:space="preserve">
    <value>The LineItems references to the BoQ items cannot be deleted, because the status of Estimate Headers with codes [{0}] is Locked or Readonly</value>
  </data>
  <data name="Structure_Type_LeadingStructure_Activity" xml:space="preserve">
    <value>Activity</value>
  </data>
  <data name="Structure_Type_LeadingStructure_Assembly_Category" xml:space="preserve">
    <value>Assembly Category</value>
  </data>
  <data name="Structure_Type_LeadingStructure_BoQ" xml:space="preserve">
    <value>BoQ</value>
  </data>
  <data name="Structure_Type_LeadingStructure_Controlling_Unit" xml:space="preserve">
    <value>Controlling Unit</value>
  </data>
  <data name="Structure_Type_LeadingStructure_Cost_Group" xml:space="preserve">
    <value>Cost Group</value>
  </data>
  <data name="Structure_Type_LeadingStructure_Estimate_Root" xml:space="preserve">
    <value>Estimate Root</value>
  </data>
  <data name="Structure_Type_LeadingStructure_Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Structure_Type_LeadingStructure_Procurement_Structure" xml:space="preserve">
    <value>Procurement Structure</value>
  </data>
  <data name="Structure_Type_LeadingStructure_Project_BoQ" xml:space="preserve">
    <value>Project BoQ</value>
  </data>
  <data name="Structure_Type_LineItem" xml:space="preserve">
    <value>LineItem</value>
  </data>
  <data name="Structure_Type_System" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="Hint_Rule_Executed_Success" xml:space="preserve">
    <value>Rule executed success</value>
  </data>
  <data name="Hint_System_Info" xml:space="preserve">
    <value>Current rule executed in machine {0}, ProductVersion is {1}, BuildVersion is {2}.</value>
  </data>
  <data name="Hint_Can_Not_Found_CostType" xml:space="preserve">
    <value>Can not found costType[{0}]</value>
  </data>
  <data name="Hint_Can_Not_Found_ResourceFlag" xml:space="preserve">
    <value>Can not found resourceFlag[{0}]</value>
  </data>
  <data name="Hint_Function_Need_Parameters" xml:space="preserve">
    <value>this function need {0} parameters.</value>
  </data>
  <data name="Hint_Function_Need_Parameters_At_least" xml:space="preserve">
    <value>this function must provide {0} parameters at least</value>
  </data>
  <data name="Hint_Function_Need_Parameters_At_least_Enhance" xml:space="preserve">
    <value>{0} function must provide {1} parameters at least</value>
  </data>
  <data name="Hint_Is_Fixed_Unit_Rate" xml:space="preserve">
    <value>This costCode is fixed unit price,can not be modify</value>
  </data>
  <data name="Hint_Replace_Code" xml:space="preserve">
    <value>The code [{0}] is exeist , and replace with code [{1}]</value>
  </data>
  <data name="Hint_Resource_Is_Not_SubItem" xml:space="preserve">
    <value>Current resource isn't a sub item</value>
  </data>
  <data name="Hint_Resource_Not_Found" xml:space="preserve">
    <value>Can not found any resources</value>
  </data>
  <data name="Hint_Resource_With_Code_Not_Found" xml:space="preserve">
    <value>Can not found any resources which code = {0}</value>
  </data>
  <data name="Hint_Rule_Execution_Cost" xml:space="preserve">
    <value>It costs {0} seconds to run current script.</value>
  </data>
  <data name="Warn_Rules_Running_Background" xml:space="preserve">
    <value>This transaction is currently not allowed. Please try again after Rules are executed completely.</value>
  </data>
  <data name="Est_Confidence_Total_Budget" xml:space="preserve">
    <value>Total Budget &gt; Grand Total</value>
  </data>
  <data name="Warn_Rules_Resource_Modified_Prototype" xml:space="preserve">
    <value>Can not modify the {0} of raw resource</value>
  </data>
  <data name="CreatePackage_CostCodeless" xml:space="preserve">
    <value>CostCodeless Package</value>
  </data>
  <data name="CreatePackage_MatchnessType_CriteriaMatched" xml:space="preserve">
    <value>Criteria Matched</value>
  </data>
  <data name="CreatePackage_MatchnessType_New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="CreatePackage_MatchnessType_PerfectlyMatched" xml:space="preserve">
    <value>Perfectly Matched</value>
  </data>
  <data name="CreatePackage_Structureless" xml:space="preserve">
    <value>Structureless Package</value>
  </data>
  <data name="Warn_Rules_Resource_Plant_Not_Create" xml:space="preserve">
    <value>Can not create Plant, The Estimate Job no set Plant Estimate Pricelist</value>
  </data>
  <data name="Err_Estimate_is_not_active" xml:space="preserve">
    <value>There is no activate Estimate, please check the isActivate flag</value>
  </data>
  <data name="Err_No_data_to_update" xml:space="preserve">
    <value>There is no data to update.</value>
  </data>
  <data name="Prc_Update_Estimate_Ok" xml:space="preserve">
    <value>Update Estimate finished !</value>
  </data>
  <data name="Warn_Resouce_Job_ReadOnly_Msg" xml:space="preserve">
    <value>Related Job is in Read-Only Status</value>
  </data>
  <data name="ERR_CurrentDataIsUsing" xml:space="preserve">
    <value>User '{0}' is also using this data to generate lineItem in this Estimate Header.</value>
  </data>
  <data name="ERR_Current_estimate_updating" xml:space="preserve">
    <value>A version conflict occurred, please retry your action or refresh whole estimate..</value>
  </data>
  <data name="Err_Isnot_estimate_package" xml:space="preserve">
    <value>The status of Packages [{0}] is  not IsEstimate,</value>
  </data>
  <data name="Err_Is_contract_package" xml:space="preserve">
    <value>The status of Packages [{0}] is  contracted,</value>
  </data>
  <data name="Err_Prc_update_estimat_reject" xml:space="preserve">
    <value> Update Estimate action is rejected.</value>
  </data>
  <data name="Modify_Function_Replace_PlantByPlant" xml:space="preserve">
    <value>Replace Plant by Plant</value>
    <comment>Select function in modify estimate</comment>
  </data>
  <data name="Err_Prj_Boq_Driven" xml:space="preserve">
    <value>BoQ Driven Estimate,An error occurred in create line items from project BoQ:</value>
  </data>
  <data name="Err_Prj_Boq_Driven_Bulk_Save" xml:space="preserve">
    <value>BoQ Driven Estimate,Error during bulk save in create line items from project BoQ :</value>
  </data>
  <data name="Err_Prj_Boq_Driven_Process_Assembly" xml:space="preserve">
    <value>BoQ Driven Estimate,Error processing project boq assembly Id {0} in create line items from project BoQ :</value>
  </data>
  <data name="Err_Wic_Boq_Driven" xml:space="preserve">
    <value>BoQ Driven Estimate, an error occurred in create line items from WiC BoQ:</value>
  </data>
  <data name="Err_Wic_Boq_Driven_Bulk_Save" xml:space="preserve">
    <value>BoQ Driven Estimate, Error during bulk save in create line items from WiC BoQ :</value>
  </data>
  <data name="Err_Wic_Boq_Driven_Process_Assembly" xml:space="preserve">
    <value>BoQ Driven Estimate, Error processing wic assembly id {0} in create line items from WIC BoQ :</value>
  </data>
  <data name="Del_LineItem_Reference" xml:space="preserve">
    <value>Line Item Reference</value>
  </data>
  <data name="Del_LineItem_PrcItemAssinged" xml:space="preserve">
    <value>Package Item Assignment</value>
  </data>
  <data name="Del_LineItem_QtoLine" xml:space="preserve">
    <value>Quantity Takeoff</value>
  </data>
  <data name="Del_LineItem_Qty" xml:space="preserve">
    <value>Line Item Quantity</value>
  </data>
  <data name="Modify_Function_Replace_AssemblyByCostCode" xml:space="preserve">
    <value>Replace Assembly By CostCode</value>
  </data>
  <data name="Modify_Function_Replace_AssemblyByMaterial" xml:space="preserve">
    <value>Replace Assembly By Material</value>
  </data>
  <data name="Hint_Generated_LineItems" xml:space="preserve">
    <value>Start Generating LineItems and Resources</value>
  </data>
  <data name="Hint_Save_LineItems" xml:space="preserve">
    <value>Saving Lineitems and Resouces</value>
  </data>
  <data name="Modify_Function_Replace_MaterialByPlant" xml:space="preserve">
    <value>Replace Material By Plant</value>
  </data>
  <data name="Modify_Function_Replace_CostCodeByPlant" xml:space="preserve">
    <value>Replace Cost Code By Plant</value>
  </data>
  <data name="Err_SplitBudget_ConcurrencyIssue" xml:space="preserve">
    <value>Conflicting data is inserted by User {0}. This conflict cannot be resolved, so the data is not saved</value>
  </data>
  <data name="Err_SplitBudget_Failed" xml:space="preserve">
    <value>Split Budget Failed</value>
  </data>
  <data name="Warn_All_Data_Are_Assigned_PrcItem" xml:space="preserve">
    <value>All the line item and resource are assigned to prcItem assignment.</value>
  </data>
  <data name="Warn_BoqOrSubpackage_Not_Found" xml:space="preserve">
    <value>Source BoQ item or sub package is not found</value>
  </data>
  <data name="Warn_RootBoqItems_Not_Found" xml:space="preserve">
    <value>No BoQ root items found in the sub package</value>
  </data>
  <data name="Warn_Parameter_Is_Null" xml:space="preserve">
    <value>The parameter is null</value>
  </data>
  <data name="Err_No_lineItem_Create_Package_info" xml:space="preserve">
    <value>There is no matched LineItem in current scope can be found to create Package info</value>
  </data>
  <data name="Err_SourceAndTargetBoqsNotCompatible" xml:space="preserve">
    <value>The source and target boq are not compatible</value>
  </data>
  <data name="Err_ParameterCodeTooLong" xml:space="preserve">
    <value>The parameter code ({0}) is too long. The maximum length is {1}.</value>
  </data>
  <data name="Warn_Rules_Resource_Create_Not_Found" xml:space="preserve">
    <value>{0} can not find or not exist which code is [{1}]</value>
  </data>
  <data name="Warn_Rules_Resource_Create_Unique_Code" xml:space="preserve">
    <value>There already exists a [{1}] code for the {0}</value>
  </data>
</root>