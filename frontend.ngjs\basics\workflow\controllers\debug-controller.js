(function (angular) {
	'use strict';

	function debugCtrl($scope, basicsWorkflowDebugService, basicsWorkflowTemplateService, platformModuleStateService, $timeout, workflowDesignerBreakpointService, platformModalService) {
		var state = platformModuleStateService.state('basics.workflow');
		var debugBtn, nextBtn, cancelBtn, continueBtn, deleteBrkPoint;

		var disabledIfMultiSelect = function (newVal) {
			debugBtn.disabled = newVal === undefined || angular.isArray(newVal);
			$scope.tools.update();
		};

		let templateVersionWatch = $scope.$watch(function () {
			return state.selectedTemplateVersion;
		}, disabledIfMultiSelect);

		debugBtn = {
			id: 'debug',
			caption: 'Debug',
			type: 'item',
			iconClass: 'tlb-icons ico-workflow-run',
			disabled: true,
			fn: function () {
				state.debugCanceled = false;
				debugBtn.disabled = true;
				nextBtn.disabled = false;
				cancelBtn.disabled = false;
				$scope.tools.update();
				basicsWorkflowDebugService.startDebugCurrent().then(responseFunction);
			},
		};

		nextBtn = {
			id: 'next',
			caption: 'Next',
			type: 'item',
			iconClass: 'tlb-icons ico-workflow-next',
			disabled: true,
			fn: function () {
				debugBtn.disabled = true;
				nextBtn.disabled = false;
				cancelBtn.disabled = false;
				$scope.tools.update();
				basicsWorkflowDebugService.nextActionFromCurrent().then(responseFunction);
			},
		};

		cancelBtn = {
			id: 'cancel',
			caption: 'Cancel',
			type: 'item',
			iconClass: 'tlb-icons ico-workflow-cancel',
			disabled: true,
			fn: cancelDebug,
		};

		continueBtn = {
			id: 'continue',
			caption: 'Continue',
			type: 'item',
			iconClass: 'tlb-icons ico-play',
			disabled: true,
			fn: function () {
				var templateVersionId = state.selectedTemplateVersion.Id;
				var breakpoints = workflowDesignerBreakpointService.getBreakpoints(templateVersionId);
				if (!breakpoints || breakpoints.length === 0) {
					platformModalService.showErrorBox('basics.workflow.debug.debugBreakpoint.breakpointAlert', 'basics.workflow.debug.debugBreakpoint.missingBreakpoint');
				} else {
					basicsWorkflowDebugService.addBreakpoint().then(responseFunction);
				}
			},
		};

		deleteBrkPoint = {
			id: 'deleteBrkPoint',
			caption: 'Delete Breakpoint',
			type: 'item',
			iconClass: 'control-icons ico-input-delete2',
			disabled: false,
			fn: function () {
				var templateVersionId = state.selectedTemplateVersion.Id;
				var breakpoints = workflowDesignerBreakpointService.getBreakpoints(templateVersionId);
				if (!breakpoints || breakpoints.length === 0) {
					platformModalService.showErrorBox('basics.workflow.debug.debugBreakpoint.noBreakpoints', 'basics.workflow.debug.debugBreakpoint.noBreakpointsTitle');
				} else {
					state.breakpointsChanged = true;
				}
			},
		};

		responseFunction({ context: state.debugContext, action: state.currentWorkflowAction });

		function checkNextButton() {
			nextBtn.disabled = state.debugContext === null || !state.currentWorkflowAction || !state.currentWorkflowAction.transitions || state.currentWorkflowAction.transitions.length <= 0;
			if ($scope.tools) {
				$timeout($scope.tools.update);
			}
		}

		function checkContinueButton(_context, isException = false) {
			continueBtn.disabled = _context === null || _context === '' || _context === undefined || isException || nextBtn.disabled;
			if ($scope.tools) {
				$scope.tools.update();
			}
		}

		function responseFunction(response) {
			if (!response) {
				return;
			}
			if (response.disableContinue) {
				checkContinueButton(response.context, true);
			} else {
				checkContinueButton(response.context);
			}
			state.currentWorkflowAction = response.action;
			checkNextButton();
			state.debugContext = response.context;

			if (response.context && response.context.Truncated === true) {
				$scope.truncated = response.context.Truncated;
			} else {
				$scope.truncated = false;
			}
			$scope.formatedContext = JSON.stringify(response.context, null, 2);

			checkNextButton();
		}

		function cancelDebug() {
			debugBtn.disabled = angular.isArray(state.selectedTemplateVersion);
			nextBtn.disabled = true;
			cancelBtn.disabled = true;
			continueBtn.disabled = true;
			$scope.tools.update();
			state.currentWorkflowAction = null;
			state.debugContext = '';
			$scope.formatedContext = '';
		}

		state.clearContext = function () {
			cancelDebug();
		};

		var tools = {
			showImages: true,
			showTitles: true,
			cssClass: 'tools',
			items: [debugBtn, nextBtn, cancelBtn, continueBtn, deleteBrkPoint],
		};

		$scope.setTools(tools);

		$scope.$watch(
			function () {
				return state.currentWorkflowAction;
			},
			function (newVal, oldVal) {
				if (newVal !== oldVal && newVal) {
					checkNextButton();
				}
			}
		);

		$scope.$watch(
			function () {
				return state.selectedMainEntity;
			},
			function (newVal, oldVal) {
				if (newVal) {
					if (!newVal.Id) {
						debugBtn.disabled = true;
						$scope.tools.update();
					} else {
						if (oldVal && newVal.Id !== oldVal.Id) {
							cancelDebug();
						}
					}
				}
			}
		);

		$scope.$watch(
			function () {
				return state.debugCanceled;
			},
			function (newVal) {
				if (newVal === true) {
					cancelDebug();
				}
			}
		);

		$scope.$on('$destroy', function () {
			templateVersionWatch();
		});
	}

	debugCtrl.$inject = ['$scope', 'basicsWorkflowDebugService', 'basicsWorkflowTemplateService', 'platformModuleStateService', '$timeout', 'workflowDesignerBreakpointService', 'platformModalService'];

	angular.module('basics.workflow').controller('basicsWorkflowDebugController', debugCtrl);
})(angular);
