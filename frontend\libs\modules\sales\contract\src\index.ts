/*
 * Copyright(c) RIB Software GmbH
 */

import { IApplicationModuleInfo } from '@libs/platform/common';
import { SalesContractModuleInfo } from './lib/model/sales-contract-module-info.class';

export * from './lib/sales-contract.module';
export * from './lib/model/wizards/sales-contract-main-wizard';
export * from './lib/lookup-helper/sales-contract-lookup-overload-provider.class';
export * from './lib/model/interface/sales-contract-bill-type-entity.interface';
export * from './lib/wizards/sales-contract-change-status-for-project-document-wizard.service';
export * from './lib/services/sales-contract-contracts-data.service';
export * from './lib/services/lookups/index';
export * from './lib/wizards/index';
/**
 * Returns the module info object for the sales contract module.
 *
 * This function implements the {@link IApplicationModule.getModuleInfo} method.
 * Do not remove it.
 * It may be called by generated code.
 *
 * @return The singleton instance of the module info object.
 *
 * @see {@link IApplicationModule.getModuleInfo}
 */
export function getModuleInfo(): IApplicationModuleInfo {
	return SalesContractModuleInfo.instance;
}