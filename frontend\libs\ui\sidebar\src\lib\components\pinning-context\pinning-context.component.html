<div *ngFor="let item of items" class="form-control shaded" title="{{ item.title }}">
	<span class="header-body app-small-icons" [ngClass]="'ico-' + item.ico"></span>
	<div class="input-group-content">
		<div class="ellipsis" [textContent]="item.info"></div>
	</div>
	<span class="input-group-btn">
		<button class="btn btn-default control-icons ico-input-delete" title="{{'cloud.desktop.sbdeletePinningContext' | platformTranslate}}" (click)="removePinningContext(item.token)"></button>
	</span>
</div>
