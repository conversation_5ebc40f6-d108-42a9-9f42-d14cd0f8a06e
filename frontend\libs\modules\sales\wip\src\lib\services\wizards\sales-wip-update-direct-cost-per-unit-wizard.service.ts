/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { SalesWipWipsDataService } from '../sales-wip-wips-data.service';
import { PlatformHttpService } from '@libs/platform/common';
import { UiCommonMessageBoxService } from '@libs/ui/common';
import { IWipHeaderEntity } from '@libs/sales/interfaces';

/**
 * Response interface for updating direct cost per unit
 */
interface IUpdateDirectCostPerUnitResponse {
	errorCode?: string;
	wipHeader: IWipHeaderEntity;
}
/**
 * Service for handling the update of direct cost per unit
 * in the Sales WIP wizard.
 */
@Injectable({
	providedIn: 'root',
})
export class SalesWipUpdateDirectCostPerUnitWizardService {
	/**
	 * inject SalesWipWipsDataService
	 */
	private readonly salesWipService = inject(SalesWipWipsDataService);
	/**
	 * inject PlatformHttpService
	 */
	private readonly http = inject(PlatformHttpService);
	/**
	 * inject UiCommonMessageBoxService
	 */
	private readonly messageBoxService = inject(UiCommonMessageBoxService);

	/**
	 * Updates the direct cost per unit for the selected WIP entity.
	 * If no WIP is selected, the operation is skipped.
	 * On success, the WIP list is refreshed and an info message is shown.
	 * On failure, a specific message is displayed if no BOQs are found.
	 */
	public updateDirectCostPerUnit(): void {
		const selectedWip = this.salesWipService.getSelectedEntity();

		if (!selectedWip) {
			return;
		}
		const wipHeaderId = selectedWip.Id;
		if (wipHeaderId > 0) {
			this.http.post$<IUpdateDirectCostPerUnitResponse>('sales/wip/updatedirectcostsperunit', wipHeaderId).subscribe((response: IUpdateDirectCostPerUnitResponse) => {
				if (!response.errorCode) {
					const updatedWipHeader = response.wipHeader;
					if (updatedWipHeader) {
						this.salesWipService.updateEntities([updatedWipHeader]);
					}
					this.messageBoxService.showInfoBox('sales.billing.updateDirectCostPerUnitSuccessMessage', 'info', false);
				} else {
					if (response.errorCode === 'noBoqs') {
						this.messageBoxService.showInfoBox('Selected WIP does not contain any boq.', 'info', true); // TODO: translation
					}
				}
				this.salesWipService.refreshAll();
			});
		}
	}
}
