-------------------------------------------------------
-- Ignore Errors (OPTIONAL): Off
-- JIRA Number(REQUIRED): DEV-48069
-- Script Type (REQUIRED): Required schema change script
-- Reason(REQUIRED): [Timekeeping Period] Plant transactions are not created when there is no "to time" in the plant allocation table 
-- InstallOn(OPTIONAL): Release 25.2 + Trunk
-------------------------------------------------------
exec FRM_DROPSPVIEWFNIFEXISTS_PROC 'TKS_CREATEPERIODTRANSACTION_V'
GO

CREATE VIEW [dbo].[TKS_CREATEPERIODTRANSACTION_V]
AS

select
-- TKS_RESULT for employee
  txn_recording.ID AS TKS_RECORDING_FK,
  txn_recording.BAS_RUBRIC_CATEGORY_FK AS REC_RUBRIC_CATEGORY_FK,
  txn_recording.[DESCRIPTION] AS RECORDING_DESC,
  txn_period.ID AS TKS_PERIOD_FK,
  txn_period.BAS_COMPANY_FK AS PERIOD_COMPANY_FK,
  txn_period.VOUCHER_NUMBER AS PERIOD_VOUCHER_NUMBER,
  txn_period.VOUCHER_DATE AS PERIOD_VOUCHER_DATE,
  txn_period.POSTING_DATE AS PERIOD_POSTING_DATE,
  txn_period.END_DATE AS PERIOD_END_DATE,
  ISNULL(txn_result.ID, 0) AS RESULT_FK,
  ISNULL(txn_result.[HOURS], 0.000) AS RESULT_HOURS,
  txn_result.ETM_PLANT_FK AS ETM_PLANT_FK,
  txn_result.ISSUCCESSFINANCE AS ISSUCCESSFINANCE,
  txn_employee.ID AS TKS_EMPLOYEE_FK,
  txn_employee.TKS_COSTGROUP_FK AS EMPLOYEETKSCOSTGROUP_FK,
  txn_employee.[DESCRIPTION] AS EMPLOYEE_DESC,
  ISNULL(CAST(0 as int), 0) AS TKS_REPORT_FK,
  ISNULL(txn_result.DUE_DATE, 0) AS DUE_DATE,
  CAST(null as datetime) AS REPORTFROM,
  CAST(null as datetime) AS REPORTTO,
  CAST(null as numeric(19,6)) AS REPORTDURATION,
  CAST(null as int) AS REPORTCONTROLLINGUNIT_FK,
  txn_resulttimesymbol.ID AS TKS_TIMESYMBOL_FK,
  txn_resulttimesymbol.CODE AS TIMESYMBOL_CODE,
  txn_resulttimesymbol.MDC_TAX_CODE_FK AS MDC_TAX_CODE_FK,
  txn_resulttimesymbol.BAS_UOM_FK AS BAS_UOM_FK,
  txn_resulttimesymbol.ISPRODUCTIVE AS TIMESYMBOL_ISPRODUCTIVE,
  txn_resulttimesymbol.ISCUMANDATORY AS TIMESYMBOL_ISCUMANDATORY,
  txn_resulttimesymbol.ISUPLIFT AS TIMESYMBOL_ISUPLIFT,
  txn_resulttimesymbol.ISEXPENSE AS TIMESYMBOL_ISEXPENSE,
  txn_resulttimesymbol.VALUATION_PERCENT AS TIMESYMBOL_VALUATION_PERCENT,
  txn_resulttimesymbol.VALUATION_RATE AS TIMESYMBOL_VALUATION_RATE,
  txn_chargedaction.ID as PRJ_ACTIONCHARGED_FK,
  txn_chargedactivity.ID AS PSD_ACTIVITYCHARGED_FK,
  txn_chargedactivity.[DESCRIPTION] AS PSD_ACTIVITYCHARGED_DESC,
  txn_chargedactioncu.ID as MDC_CONTROLLINGUNITCHARGED_FK,
  txn_chargedactioncu.CODE as MDC_CONTROLLINGUNITCHARGED_CODE,
  txn_chargedproject.ID AS PRJ_PROJECTCHARGED_FK,
  txn_chargedproject.BAS_COMPANY_FK AS BAS_COMPANYCHARED_FK,
  txn_resultsymbolaccount.BAS_ACCOUNT_COST_FK AS SYMBOLACCOUNTCOST_FK,
  txn_resultsymbolaccount.BAS_ACCOUNT_REV_FK AS SYMBOLACCOUNTREV_FK,
  txn_resultsymbolaccount.BAS_ACCOUNT_ICCOST_FK AS SYMBOLACCOUNTICCOST_FK,
  txn_resultsymbolaccount.BAS_ACCOUNT_ICREV_FK AS SYMBOLACCOUNTICREV_FK,
  txn_resultsymbolaccount.NOMINAL_DIMENSON1_COST AS SYMBOLACCOUNTNOMDIM1COST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON1_REV AS SYMBOLACCOUNTNOMDIM1REV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON1_ICCOST AS SYMBOLACCOUNTNOMDIM1ICCOST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON1_ICREV AS SYMBOLACCOUNTNOMDIM1ICREV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON2_COST AS SYMBOLACCOUNTNOMDIM2COST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON2_REV AS SYMBOLACCOUNTNOMDIM2REV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON2_ICCOST AS SYMBOLACCOUNTNOMDIM2ICCOST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON2_ICREV AS SYMBOLACCOUNTNOMDIM2ICREV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON3_COST AS SYMBOLACCOUNTNOMDIM3COST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON3_REV AS SYMBOLACCOUNTNOMDIM3REV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON3_ICCOST AS SYMBOLACCOUNTNOMDIM3ICCOST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON3_ICREV AS SYMBOLACCOUNTNOMDIM3ICREV,

  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_01_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_01_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_01_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_02_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_02_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_02_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_03_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_03_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_03_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_04_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_04_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_04_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_05_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_05_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_05_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_06_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_06_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_06_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_01_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_01_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_01_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_02_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_02_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_02_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_03_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_03_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_03_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_04_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_04_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_04_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_05_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_05_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_05_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_06_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_06_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_06_03,

    -- plant accounts internal
  CAST(null as int) AS ACCOUNT_REVENUE_01_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_02_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_03_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_04_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_05_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_06_FK,
  -- plant accounts for IC
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_01_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_02_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_03_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_04_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_05_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_06_FK,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION01,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION02,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION03,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION04,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION05,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION06,
  ''  AS ETM_WORKOPERATIONTYPECODE,
  ''  AS ETM_WORKOPERATIONTYPEDESC,
  ISNULL(txn_surchargesurchargetype.ID, ISNULL(txn_ratesurchargetype.ID, 0)) AS SURCHARGETYPE_FK,
  ISNULL(txn_surchargesurchargetype.EVALUATIONLEVEL, ISNULL(txn_ratesurchargetype.EVALUATIONLEVEL, 0)) AS SURCHARGETYPE_EVALUATIONLEVEL,
  ISNULL(txn_surchargesurchargetype.ISSTANDARDRATE, ISNULL(txn_ratesurchargetype.ISSTANDARDRATE, 1)) AS SURCHARGETYPE_ISSTANDARDRATE,
  txn_ratepaymentgroup.ID AS PAYMENTGROUPRATE_FK,
  txn_ratepaymentgroup.TKS_SURCHARGETYPE_FK AS PAYMENTGROUPRATE_SURCHARGETYPE,
  txn_ratepaymentgroup.RATE AS PAYMENTGROUPRATE_RATE,
  txn_ratepaymentgroup2.ID AS PAYMENTGROUPRATE_FK2,
  txn_ratepaymentgroup2.TKS_SURCHARGETYPE_FK AS PAYMENTGROUPRATE_SURCHARGETYPE2,
  txn_ratepaymentgroup2.RATE AS PAYMENTGROUPRATE_RATE2,
  ISNULL(txn_surchargepaymentgroup.ID,0) AS PAYMENTGROUPSURCHARGE_FK,
  ISNULL(txn_surchargepaymentgroup.TKS_SURCHARGETYPE_FK,0) AS PAYMENTGROUPSURCHARGE_SURCHARGETYPE,
  ISNULL(txn_surchargepaymentgroup.RATE,0.0) AS PAYMENTGROUPSURCHARGE_RATE,
  txn_symbolaccount_cu.ID AS SYMBOLACCOUNTCU_FK,
  txn_symbolaccount_cu.CODE AS SYMBOLACCOUNTCU_CODE,
  txn_cucostresult_iccu.ID AS COSTRESULTINTERCOMPCOSTCU_FK,
  txn_cucostresult_iccu.CODE AS COSTRESULTINTERCOMPCOSTCU_CODE,
  txn_curatepaymentgroup.ID AS PAYMENTGROUPRATECU_FK,
  txn_curatepaymentgroup.CODE AS PAYMENTGROUPRATECU_CODE,
  txn_cusurchargepaymentgroup.ID AS PAYMENTGROUPSURCHARGECU_FK,
  txn_cusurchargepaymentgroup.CODE AS PAYMENTGROUPSURCHARGECU_CODE,
  txn_cuchargedaction.ID AS CHARGEDACTIONCU_FK,
  txn_cuchargedaction.CODE AS CHARGEDACTIONCU_CODE,
  txn_cuchargedaction.BAS_COMPANY_FK AS CHARGEDACTIONCOMPANY_FK,
  txn_cucompanyrevenue.ID AS COMPANYREVENUECU_FK,
  txn_cucompanyrevenue.CODE AS COMPANYREVENUECU_CODE,
  txn_cuchargedjob.ID AS PLANTJOBCU_FK,
  txn_cuchargedjob.CODE AS PLANTJOBCU_CODE,
  txn_change.ID AS PRJ_CHANGE_FK
 
from

  TKS_PERIOD txn_period
  INNER JOIN TKS_RECORDING txn_recording on txn_recording.TKS_PERIOD_FK=txn_period.ID
  INNER JOIN TKS_RESULT txn_result on txn_result.TKS_RECORDING_FK=txn_recording.ID
    and txn_result.ETM_PLANT_FK is null and txn_result.HOURS > 0.00
  INNER JOIN TKS_SHEET txn_sheet on txn_sheet.TKS_RECORDING_FK = txn_recording.ID and txn_sheet.ID = txn_result.TKS_SHEET_FK
  INNER JOIN TKS_TIMESYMBOL txn_resulttimesymbol ON txn_resulttimesymbol.ID = txn_result.TKS_TIMESYMBOL_FK
  INNER JOIN BAS_COMPANY txn_company on txn_company.ID=txn_period.BAS_COMPANY_FK
  INNER JOIN TKS_EMPLOYEE txn_employee on txn_employee.ID = txn_sheet.TKS_EMPLOYEE_FK
  --
  LEFT JOIN PRJ_ACTION txn_chargedaction on txn_chargedaction.ID=txn_result.PRJ_ACTION_FK
  LEFT JOIN PRJ_CHANGE txn_change on txn_change.Id = txn_chargedaction.PRJ_CHANGE_FK
  LEFT JOIN PSD_ACTIVITY txn_chargedactivity ON txn_chargedactivity.ID = txn_chargedaction.PSD_ACTIVITY_FK
  LEFT JOIN PRJ_PROJECT txn_chargedproject ON txn_chargedproject.ID = txn_chargedaction.PRJ_PROJECT_FK 
  LEFT JOIN LGM_JOB txn_chargedjob ON txn_chargedjob.ID = txn_chargedaction.LGM_JOB_FK
  LEFT JOIN BAS_COMPANYICCU txn_intercompanyiccu ON txn_intercompanyiccu.BAS_COMPANY_FK = txn_period.BAS_COMPANY_FK AND
    txn_intercompanyiccu.BAS_COMPANY_RECEIVING_FK = txn_chargedproject.BAS_COMPANY_FK
  LEFT JOIN BAS_COMPANYICCU txn_intercompanyreviccu ON txn_intercompanyreviccu.BAS_COMPANY_FK = txn_intercompanyiccu.BAS_COMPANY_RECEIVING_FK AND
    txn_intercompanyreviccu.BAS_COMPANY_RECEIVING_FK = txn_period.BAS_COMPANY_FK
  --
  LEFT JOIN MDC_CONTROLLINGUNIT txn_chargedactioncu on txn_chargedactioncu.ID=txn_chargedaction.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN TKS_TIMESYMBOLACCOUNT txn_resultsymbolaccount on 
    txn_resultsymbolaccount.BAS_COMPANY_FK=txn_company.ID and
	 (txn_chargedactioncu.ID IS NULL OR txn_resultsymbolaccount.BAS_COMPANY_CHARGED_FK=txn_chargedactioncu.BAS_COMPANY_FK) and
    txn_resultsymbolaccount.TKS_COSTGROUP_FK=txn_employee.TKS_COSTGROUP_FK and
    txn_resultsymbolaccount.TKS_TIMESYMBOL_FK=txn_result.TKS_TIMESYMBOL_FK
OUTER APPLY (
	SELECT TOP (1) *
	FROM TKS_PAYMENTGROUPRATE pgr
	WHERE pgr.BAS_COMPANY_FK = txn_period.BAS_COMPANY_FK
		AND pgr.VALIDFROM < txn_period.END_DATE
		AND pgr.TKS_PAYMENTGROUP_FK = txn_employee.TKS_PAYMENTGROUP_FK
		and pgr.TKS_SURCHARGETYPE_FK = txn_resultsymbolaccount.TKS_SURCHARGETYPE_FK
	ORDER BY pgr.VALIDFROM DESC
	) txn_ratepaymentgroup
  LEFT JOIN TKS_SURCHARGETYPE txn_ratesurchargetype on txn_ratesurchargetype.ID = txn_ratepaymentgroup.TKS_SURCHARGETYPE_FK
  LEFT JOIN TKS_SURCHARGETYPE txn_surchargesurchargetype on txn_surchargesurchargetype.ID = txn_resultsymbolaccount.TKS_SURCHARGETYPE_FK
  --JOIN TKS_PAYMENTGROUPSUR txn_surchargepaymentgroup on txn_surchargepaymentgroup.TKS_PAYMENTGROUPRATE_FK = txn_ratepaymentgroup.ID
  OUTER APPLY (
    SELECT TOP(1) * FROM TKS_PAYMENTGROUPSUR pgs
    WHERE pgs.BAS_COMPANY_FK = txn_period.BAS_COMPANY_FK
		and pgs.VALIDFROM < txn_period.END_DATE
		and pgs.TKS_PAYMENTGROUP_FK = txn_employee.TKS_PAYMENTGROUP_FK
		and pgs.TKS_SURCHARGETYPE_FK = txn_surchargesurchargetype.ID
		or pgs.TKS_PAYMENTGROUPRATE_FK = txn_ratepaymentgroup.ID
    ORDER BY pgs.VALIDFROM desc
  ) txn_surchargepaymentgroup
    --
  OUTER APPLY (
	SELECT TOP (1) *
	FROM TKS_PAYMENTGROUPRATE pgr
	WHERE pgr.BAS_COMPANY_FK = txn_period.BAS_COMPANY_FK
		AND pgr.VALIDFROM < txn_period.END_DATE
		AND pgr.TKS_PAYMENTGROUP_FK = txn_employee.TKS_PAYMENTGROUP_FK
		and pgr.ID = txn_surchargepaymentgroup.TKS_PAYMENTGROUPRATE_FK
	ORDER BY pgr.VALIDFROM DESC
	) txn_ratepaymentgroup2
  --
  LEFT JOIN MDC_CONTROLLINGUNIT txn_symbolaccount_cu ON txn_symbolaccount_cu.ID = txn_resultsymbolaccount.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cucostresult_iccu ON txn_cucostresult_iccu.ID = txn_intercompanyiccu.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_curatepaymentgroup ON txn_curatepaymentgroup.ID = txn_ratepaymentgroup.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cusurchargepaymentgroup ON txn_cusurchargepaymentgroup.ID = txn_surchargepaymentgroup.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cuchargedaction ON txn_cuchargedaction.ID = txn_chargedaction.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cucompanyrevenue ON txn_cucompanyrevenue.ID = txn_intercompanyreviccu.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cuchargedjob ON txn_cuchargedjob.ID = txn_chargedjob.MDC_CONTROLLINGUNIT_FK

union all

select
--TKS_REPORT for employee
  txn_recording.ID AS TKS_RECORDING_FK,
  txn_recording.BAS_RUBRIC_CATEGORY_FK AS REC_RUBRIC_CATEGORY_FK,
  txn_recording.[DESCRIPTION] AS RECORDING_DESC,
  txn_period.ID AS TKS_PERIOD_FK,
  txn_period.BAS_COMPANY_FK AS PERIOD_COMPANY_FK,
  txn_period.VOUCHER_NUMBER AS PERIOD_VOUCHER_NUMBER,
  txn_period.VOUCHER_DATE AS PERIOD_VOUCHER_DATE,
  txn_period.POSTING_DATE AS PERIOD_POSTING_DATE,
  txn_period.END_DATE AS PERIOD_END_DATE,
  ISNULL(CAST(0 AS int), 0) AS RESULT_FK,
  ISNULL(CAST(0.000 AS numeric(19,7)), 0.000) AS RESULT_HOURS,
  CAST(NULL AS int) AS ETM_PLANT_FK,
  txn_report.ISSUCCESSFINANCE AS ISSUCCESSFINANCE,
  txn_employee.ID AS TKS_EMPLOYEE_FK,
  txn_employee.TKS_COSTGROUP_FK AS EMPLOYEETKSCOSTGROUP_FK,
  txn_employee.[DESCRIPTION] AS EMPLOYEE_DESC,
  txn_report.ID AS TKS_REPORT_FK,
  txn_report.DUE_DATE AS DUE_DATE,
  txn_report.FROMDATETIME AS REPORTFROM,
  txn_report.TODATETIME AS REPORTTO,
  txn_report.DURATION AS REPORTDURATION,
  txn_report.MDC_CONTROLLINGUNIT_FK AS REPORTCONTROLLINGUNIT_FK,
  txn_reporttimesymbol.ID AS TKS_TIMESYMBOL_FK,
  txn_reporttimesymbol.CODE AS TIMESYMBOL_CODE,
  txn_reporttimesymbol.MDC_TAX_CODE_FK AS MDC_TAX_CODE_FK,
  txn_reporttimesymbol.BAS_UOM_FK AS BAS_UOM_FK,
  txn_reporttimesymbol.ISPRODUCTIVE AS TIMESYMBOL_ISPRODUCTIVE,
  txn_reporttimesymbol.ISCUMANDATORY AS TIMESYMBOL_ISCUMANDATORY,
  txn_reporttimesymbol.ISUPLIFT AS TIMESYMBOL_ISUPLIFT,
  txn_reporttimesymbol.ISEXPENSE AS TIMESYMBOL_ISEXPENSE,
  txn_reporttimesymbol.VALUATION_PERCENT AS TIMESYMBOL_VALUATION_PERCENT,
  txn_reporttimesymbol.VALUATION_RATE AS TIMESYMBOL_VALUATION_RATE,
  txn_reportaction.ID as PRJ_ACTIONCHARGED_FK,
  txn_chargedactivity.ID AS PSD_ACTIVITYCHARGED_FK,
  txn_chargedactivity.[DESCRIPTION] AS PSD_ACTIVITYCHARGED_DESC,
  txn_chargedactioncu.ID as MDC_CONTROLLINGUNITCHARGED_FK,
  txn_chargedactioncu.CODE as MDC_CONTROLLINGUNITCHARGED_CODE,
  txn_chargedproject.ID AS PRJ_PROJECTCHARGED_FK,
  txn_chargedproject.BAS_COMPANY_FK AS BAS_COMPANYCHARED_FK,
  txn_reportsymbolaccount.BAS_ACCOUNT_COST_FK AS SYMBOLACCOUNTCOST_FK,
  txn_reportsymbolaccount.BAS_ACCOUNT_REV_FK AS SYMBOLACCOUNTREV_FK,
  txn_reportsymbolaccount.BAS_ACCOUNT_ICCOST_FK AS SYMBOLACCOUNTICCOST_FK,
  txn_reportsymbolaccount.BAS_ACCOUNT_ICREV_FK AS SYMBOLACCOUNTICREV_FK,
  txn_reportsymbolaccount.NOMINAL_DIMENSON1_COST AS SYMBOLACCOUNTNOMDIM1COST,
  txn_reportsymbolaccount.NOMINAL_DIMENSON1_REV AS SYMBOLACCOUNTNOMDIM1REV,
  txn_reportsymbolaccount.NOMINAL_DIMENSON1_ICCOST AS SYMBOLACCOUNTNOMDIM1ICCOST,
  txn_reportsymbolaccount.NOMINAL_DIMENSON1_ICREV AS SYMBOLACCOUNTNOMDIM1ICREV,
  txn_reportsymbolaccount.NOMINAL_DIMENSON2_COST AS SYMBOLACCOUNTNOMDIM2COST,
  txn_reportsymbolaccount.NOMINAL_DIMENSON2_REV AS SYMBOLACCOUNTNOMDIM2REV,
  txn_reportsymbolaccount.NOMINAL_DIMENSON2_ICCOST AS SYMBOLACCOUNTNOMDIM2ICCOST,
  txn_reportsymbolaccount.NOMINAL_DIMENSON2_ICREV AS SYMBOLACCOUNTNOMDIM2ICREV,
  txn_reportsymbolaccount.NOMINAL_DIMENSON3_COST AS SYMBOLACCOUNTNOMDIM3COST,
  txn_reportsymbolaccount.NOMINAL_DIMENSON3_REV AS SYMBOLACCOUNTNOMDIM3REV,
  txn_reportsymbolaccount.NOMINAL_DIMENSON3_ICCOST AS SYMBOLACCOUNTNOMDIM3ICCOST,
  txn_reportsymbolaccount.NOMINAL_DIMENSON3_ICREV AS SYMBOLACCOUNTNOMDIM3ICREV,

  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_01_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_01_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_01_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_02_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_02_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_02_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_03_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_03_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_03_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_04_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_04_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_04_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_05_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_05_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_05_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_06_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_06_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_06_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_01_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_01_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_01_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_02_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_02_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_02_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_03_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_03_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_03_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_04_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_04_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_04_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_05_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_05_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_05_03,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_06_01,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_06_02,
  CAST(null as nvarchar(32)) AS NOMINAL_DIMENSION_IC_06_03,
    -- plant accounts internal
  CAST(null as int) AS ACCOUNT_REVENUE_01_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_02_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_03_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_04_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_05_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_06_FK,
  -- plant accounts for IC
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_01_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_02_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_03_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_04_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_05_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_06_FK,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION01,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION02,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION03,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION04,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION05,
  CAST(0.00 as numeric(19,6)) AS PRICEPORTION06,
  ''  AS ETM_WORKOPERATIONTYPECODE,
  ''  AS ETM_WORKOPERATIONTYPEDESC,
  ISNULL(txn_surchargesurchargetype.ID, ISNULL(txn_ratesurchargetype.ID, 0)) AS SURCHARGETYPE_FK,
  ISNULL(txn_surchargesurchargetype.EVALUATIONLEVEL, ISNULL(txn_ratesurchargetype.EVALUATIONLEVEL, 0)) AS SURCHARGETYPE_EVALUATIONLEVEL,
  ISNULL(txn_surchargesurchargetype.ISSTANDARDRATE, ISNULL(txn_ratesurchargetype.ISSTANDARDRATE, 1)) AS SURCHARGETYPE_ISSTANDARDRATE,
  txn_ratepaymentgroup.ID AS PAYMENTGROUPRATE_FK,
  txn_ratepaymentgroup.TKS_SURCHARGETYPE_FK AS PAYMENTGROUPRATE_SURCHARGETYPE,
  txn_ratepaymentgroup.RATE AS PAYMENTGROUPRATE_RATE,
  txn_ratepaymentgroup2.ID AS PAYMENTGROUPRATE_FK2,
  txn_ratepaymentgroup2.TKS_SURCHARGETYPE_FK AS PAYMENTGROUPRATE_SURCHARGETYPE2,
  txn_ratepaymentgroup2.RATE AS PAYMENTGROUPRATE_RATE2,
  txn_surchargepaymentgroup.ID AS PAYMENTGROUPSURCHARGE_FK,
  txn_surchargepaymentgroup.TKS_SURCHARGETYPE_FK AS PAYMENTGROUPSURCHARGE_SURCHARGETYPE,
  txn_surchargepaymentgroup.RATE AS PAYMENTGROUPSURCHARGE_RATE,
  txn_symbolaccount_cu.ID AS SYMBOLACCOUNTCU_FK,
  txn_symbolaccount_cu.CODE AS SYMBOLACCOUNTCU_CODE,
  txn_cucostresult_iccu.ID AS COSTRESULTINTERCOMPCOSTCU_FK,
  txn_cucostresult_iccu.CODE AS COSTRESULTINTERCOMPCOSTCU_CODE,
  txn_curatepaymentgroup.ID AS PAYMENTGROUPRATECU_FK,
  txn_curatepaymentgroup.CODE AS PAYMENTGROUPRATECU_CODE,
  txn_cusurchargepaymentgroup.ID AS PAYMENTGROUPSURCHARGECU_FK,
  txn_cusurchargepaymentgroup.CODE AS PAYMENTGROUPSURCHARGECU_CODE,
  txn_cuchargedaction.ID AS CHARGEDACTIONCU_FK,
  txn_cuchargedaction.CODE AS CHARGEDACTIONCU_CODE,
  txn_cuchargedaction.BAS_COMPANY_FK AS CHARGEDACTIONCOMPANY_FK,
  txn_cucompanyrevenue.ID AS COMPANYREVENUECU_FK,
  txn_cucompanyrevenue.CODE AS COMPANYREVENUECU_CODE,
  txn_cuchargedjob.ID AS PLANTJOBCU_FK,
  txn_cuchargedjob.CODE AS PLANTJOBCU_CODE,
  txn_change.ID AS PRJ_CHANGE_FK
 
from

  TKS_PERIOD txn_period
  INNER JOIN TKS_RECORDING txn_recording on txn_recording.TKS_PERIOD_FK=txn_period.ID
  INNER JOIN TKS_SHEET txn_sheet on txn_sheet.TKS_RECORDING_FK = txn_recording.ID
  INNER JOIN TKS_REPORT txn_report on txn_report.TKS_SHEET_FK = txn_sheet.ID AND txn_report.ISLIVE = 1
  INNER JOIN TKS_TIMESYMBOL txn_resulttimesymbol ON txn_resulttimesymbol.ID = txn_report.TKS_TIMESYMBOL_FK
  INNER JOIN BAS_COMPANY txn_company on txn_company.ID=txn_period.BAS_COMPANY_FK
  INNER JOIN TKS_TIMESYMBOL txn_reporttimesymbol ON txn_reporttimesymbol.ID = txn_report.TKS_TIMESYMBOL_FK
  INNER JOIN TKS_EMPLOYEE txn_employee on txn_employee.ID = txn_sheet.TKS_EMPLOYEE_FK
  LEFT JOIN PRJ_ACTION txn_reportaction on txn_reportaction.ID=txn_report.PRJ_ACTION_FK
  LEFT JOIN PRJ_CHANGE txn_change on txn_change.Id = txn_reportaction.PRJ_CHANGE_FK
  LEFT JOIN PSD_ACTIVITY txn_chargedactivity ON txn_chargedactivity.ID = txn_reportaction.PSD_ACTIVITY_FK
  LEFT JOIN PRJ_PROJECT txn_chargedproject ON txn_chargedproject.ID = txn_reportaction.PRJ_PROJECT_FK 
  LEFT JOIN LGM_JOB txn_chargedjob ON txn_chargedjob.ID = txn_reportaction.LGM_JOB_FK
  LEFT JOIN BAS_COMPANYICCU txn_intercompanyiccu ON txn_intercompanyiccu.BAS_COMPANY_FK = txn_period.BAS_COMPANY_FK AND
    txn_intercompanyiccu.BAS_COMPANY_RECEIVING_FK = txn_chargedproject.BAS_COMPANY_FK
  LEFT JOIN BAS_COMPANYICCU txn_intercompanyreviccu ON txn_intercompanyreviccu.BAS_COMPANY_FK = txn_intercompanyiccu.BAS_COMPANY_RECEIVING_FK AND
    txn_intercompanyreviccu.BAS_COMPANY_RECEIVING_FK = txn_period.BAS_COMPANY_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_chargedactioncu on txn_chargedactioncu.ID=txn_report.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_reportactioncu on txn_reportactioncu.ID=txn_reportaction.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN TKS_TIMESYMBOLACCOUNT txn_reportsymbolaccount on 
    txn_reportsymbolaccount.BAS_COMPANY_FK=txn_company.ID and
    (txn_reportactioncu.ID IS NULL OR txn_reportsymbolaccount.BAS_COMPANY_CHARGED_FK = txn_reportactioncu.BAS_COMPANY_FK) and
    txn_reportsymbolaccount.TKS_COSTGROUP_FK=txn_employee.TKS_COSTGROUP_FK and
    txn_reportsymbolaccount.TKS_TIMESYMBOL_FK=txn_report.TKS_TIMESYMBOL_FK
  OUTER APPLY (
SELECT TOP (1) *
FROM TKS_PAYMENTGROUPRATE pgr
WHERE pgr.BAS_COMPANY_FK = txn_period.BAS_COMPANY_FK
	AND pgr.VALIDFROM < txn_period.END_DATE
	AND pgr.TKS_PAYMENTGROUP_FK = txn_employee.TKS_PAYMENTGROUP_FK
	AND pgr.TKS_SURCHARGETYPE_FK = txn_reportsymbolaccount.TKS_SURCHARGETYPE_FK
ORDER BY pgr.VALIDFROM DESC
	) txn_ratepaymentgroup
  LEFT JOIN TKS_SURCHARGETYPE txn_ratesurchargetype on txn_ratesurchargetype.ID = txn_ratepaymentgroup.TKS_SURCHARGETYPE_FK
  LEFT JOIN TKS_SURCHARGETYPE txn_surchargesurchargetype on txn_surchargesurchargetype.ID = txn_reportsymbolaccount.TKS_SURCHARGETYPE_FK
  OUTER APPLY (
    SELECT TOP(1) * FROM TKS_PAYMENTGROUPSUR pgs
    WHERE pgs.BAS_COMPANY_FK = txn_period.BAS_COMPANY_FK
		and pgs.VALIDFROM < txn_period.END_DATE
		and pgs.TKS_PAYMENTGROUP_FK = txn_employee.TKS_PAYMENTGROUP_FK
		and pgs.TKS_SURCHARGETYPE_FK = txn_surchargesurchargetype.ID

    ORDER BY pgs.VALIDFROM desc
  ) txn_surchargepaymentgroup
    OUTER APPLY (
	SELECT TOP (1) *
	FROM TKS_PAYMENTGROUPRATE pgr
	WHERE pgr.BAS_COMPANY_FK = txn_period.BAS_COMPANY_FK
		AND pgr.VALIDFROM < txn_period.END_DATE
		AND pgr.TKS_PAYMENTGROUP_FK = txn_employee.TKS_PAYMENTGROUP_FK
		and pgr.ID = txn_surchargepaymentgroup.TKS_PAYMENTGROUPRATE_FK
	ORDER BY pgr.VALIDFROM DESC
	) txn_ratepaymentgroup2
  --
  LEFT JOIN MDC_CONTROLLINGUNIT txn_symbolaccount_cu ON txn_symbolaccount_cu.ID = txn_reportsymbolaccount.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cucostresult_iccu ON txn_cucostresult_iccu.ID = txn_intercompanyiccu.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_curatepaymentgroup ON txn_curatepaymentgroup.ID = txn_ratepaymentgroup.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cusurchargepaymentgroup ON txn_cusurchargepaymentgroup.ID = txn_surchargepaymentgroup.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cuchargedaction ON txn_cuchargedaction.ID = txn_reportaction.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cucompanyrevenue ON txn_cucompanyrevenue.ID = txn_intercompanyreviccu.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cuchargedjob ON txn_cuchargedjob.ID = txn_chargedjob.MDC_CONTROLLINGUNIT_FK

union all 
select
--TKS_RESULT for plant
  txn_recording.ID AS TKS_RECORDING_FK,
  txn_recording.BAS_RUBRIC_CATEGORY_FK AS REC_RUBRIC_CATEGORY_FK,
  txn_recording.[DESCRIPTION] AS RECORDING_DESC,
  txn_period.ID AS TKS_PERIOD_FK,
  txn_period.BAS_COMPANY_FK AS PERIOD_COMPANY_FK,
  txn_period.VOUCHER_NUMBER AS PERIOD_VOUCHER_NUMBER,
  txn_period.VOUCHER_DATE AS PERIOD_VOUCHER_DATE,
  txn_period.POSTING_DATE AS PERIOD_POSTING_DATE,
  txn_period.END_DATE AS PERIOD_END_DATE,
  ISNULL(txn_result.ID, 0) AS RESULT_FK,
  ISNULL(txn_result.[HOURS], 0.000) AS RESULT_HOURS,
  txn_result.ETM_PLANT_FK AS ETM_PLANT_FK,
  CAST(0 as bit) AS ISSUCCESSFINANCE,
  CAST(null as int) AS TKS_EMPLOYEE_FK,
  CAST(null as int) AS EMPLOYEETKSCOSTGROUP_FK,
  CAST(null as nvarchar(252)) AS EMPLOYEE_DESC,
  ISNULL(CAST(0 as int), 0) AS TKS_REPORT_FK,
  ISNULL(txn_result.DUE_DATE, 0) AS DUE_DATE,
  CAST(null as datetime) AS REPORTFROM,
  CAST(null as datetime) AS REPORTTO,
  CAST(0.00 as numeric(19,6)) AS REPORTDURATION,
  CAST(null as int) AS REPORTCONTROLLINGUNIT_FK,
  txn_resulttimesymbol.ID AS TKS_TIMESYMBOL_FK,
  txn_resulttimesymbol.CODE AS TIMESYMBOL_CODE,
  txn_resulttimesymbol.MDC_TAX_CODE_FK AS MDC_TAX_CODE_FK,
  txn_resulttimesymbol.BAS_UOM_FK AS BAS_UOM_FK,
  txn_resulttimesymbol.ISPRODUCTIVE AS TIMESYMBOL_ISPRODUCTIVE,
  txn_resulttimesymbol.ISCUMANDATORY AS TIMESYMBOL_ISCUMANDATORY,
  txn_resulttimesymbol.ISUPLIFT AS TIMESYMBOL_ISUPLIFT,
  txn_resulttimesymbol.ISEXPENSE AS TIMESYMBOL_ISEXPENSE,
  txn_resulttimesymbol.VALUATION_PERCENT AS TIMESYMBOL_VALUATION_PERCENT,
  txn_resulttimesymbol.VALUATION_RATE AS TIMESYMBOL_VALUATION_RATE,
  txn_chargedaction.ID as PRJ_ACTIONCHARGED_FK,
  txn_chargedactivity.ID AS PSD_ACTIVITYCHARGED_FK,
  txn_chargedactivity.[DESCRIPTION] AS PSD_ACTIVITYCHARGED_DESC,
  txn_cuchargedaction.ID as MDC_CONTROLLINGUNITCHARGED_FK,
  txn_cuchargedaction.CODE as MDC_CONTROLLINGUNITCHARGED_CODE,
  txn_chargedproject.ID AS PRJ_PROJECTCHARGED_FK,
  txn_chargedproject.BAS_COMPANY_FK AS BAS_COMPANYCHARED_FK,
  txn_resultsymbolaccount.BAS_ACCOUNT_COST_FK AS SYMBOLACCOUNTCOST_FK,
  txn_resultsymbolaccount.BAS_ACCOUNT_REV_FK AS SYMBOLACCOUNTREV_FK,
  txn_resultsymbolaccount.BAS_ACCOUNT_ICCOST_FK AS SYMBOLACCOUNTICCOST_FK,
  txn_resultsymbolaccount.BAS_ACCOUNT_ICREV_FK AS SYMBOLACCOUNTICREV_FK,
  txn_resultsymbolaccount.NOMINAL_DIMENSON1_COST AS SYMBOLACCOUNTNOMDIM1COST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON1_REV AS SYMBOLACCOUNTNOMDIM1REV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON1_ICCOST AS SYMBOLACCOUNTNOMDIM1ICCOST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON1_ICREV AS SYMBOLACCOUNTNOMDIM1ICREV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON2_COST AS SYMBOLACCOUNTNOMDIM2COST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON2_REV AS SYMBOLACCOUNTNOMDIM2REV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON2_ICCOST AS SYMBOLACCOUNTNOMDIM2ICCOST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON2_ICREV AS SYMBOLACCOUNTNOMDIM2ICREV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON3_COST AS SYMBOLACCOUNTNOMDIM3COST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON3_REV AS SYMBOLACCOUNTNOMDIM3REV,
  txn_resultsymbolaccount.NOMINAL_DIMENSON3_ICCOST AS SYMBOLACCOUNTNOMDIM3ICCOST,
  txn_resultsymbolaccount.NOMINAL_DIMENSON3_ICREV AS SYMBOLACCOUNTNOMDIM3ICREV,

  -- Nominal Dimensions for plant
  etm_plantgroupaccount.NOMINAL_DIMENSION_01_01 AS NOMINAL_DIMENSION_01_01,
  etm_plantgroupaccount.NOMINAL_DIMENSION_01_02 AS NOMINAL_DIMENSION_01_02,
  etm_plantgroupaccount.NOMINAL_DIMENSION_01_03 AS NOMINAL_DIMENSION_01_03,
  etm_plantgroupaccount.NOMINAL_DIMENSION_02_01 AS NOMINAL_DIMENSION_02_01,
  etm_plantgroupaccount.NOMINAL_DIMENSION_02_02 AS NOMINAL_DIMENSION_02_02,
  etm_plantgroupaccount.NOMINAL_DIMENSION_02_03 AS NOMINAL_DIMENSION_02_03,
  etm_plantgroupaccount.NOMINAL_DIMENSION_03_01 AS NOMINAL_DIMENSION_03_01,
  etm_plantgroupaccount.NOMINAL_DIMENSION_03_02 AS NOMINAL_DIMENSION_03_02,
  etm_plantgroupaccount.NOMINAL_DIMENSION_03_03 AS NOMINAL_DIMENSION_03_03,
  etm_plantgroupaccount.NOMINAL_DIMENSION_04_01 AS NOMINAL_DIMENSION_04_01,
  etm_plantgroupaccount.NOMINAL_DIMENSION_04_02 AS NOMINAL_DIMENSION_04_02,
  etm_plantgroupaccount.NOMINAL_DIMENSION_04_03 AS NOMINAL_DIMENSION_04_03,
  etm_plantgroupaccount.NOMINAL_DIMENSION_05_01 AS NOMINAL_DIMENSION_05_01,
  etm_plantgroupaccount.NOMINAL_DIMENSION_05_02 AS NOMINAL_DIMENSION_05_02,
  etm_plantgroupaccount.NOMINAL_DIMENSION_05_03 AS NOMINAL_DIMENSION_05_03,
  etm_plantgroupaccount.NOMINAL_DIMENSION_06_01 AS NOMINAL_DIMENSION_06_01,
  etm_plantgroupaccount.NOMINAL_DIMENSION_06_02 AS NOMINAL_DIMENSION_06_02,
  etm_plantgroupaccount.NOMINAL_DIMENSION_06_03 AS NOMINAL_DIMENSION_06_03,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_01_01 AS NOMINAL_DIMENSION_IC_01_01,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_01_02 AS NOMINAL_DIMENSION_IC_01_02,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_01_03 AS NOMINAL_DIMENSION_IC_01_03,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_02_01 AS NOMINAL_DIMENSION_IC_02_01,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_02_02 AS NOMINAL_DIMENSION_IC_02_02,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_02_03 AS NOMINAL_DIMENSION_IC_02_03,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_03_01 AS NOMINAL_DIMENSION_IC_03_01,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_03_02 AS NOMINAL_DIMENSION_IC_03_02,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_03_03 AS NOMINAL_DIMENSION_IC_03_03,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_04_01 AS NOMINAL_DIMENSION_IC_04_01,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_04_02 AS NOMINAL_DIMENSION_IC_04_02,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_04_03 AS NOMINAL_DIMENSION_IC_04_03,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_05_01 AS NOMINAL_DIMENSION_IC_05_01,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_05_02 AS NOMINAL_DIMENSION_IC_05_02,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_05_03 AS NOMINAL_DIMENSION_IC_05_03,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_06_01 AS NOMINAL_DIMENSION_IC_06_01,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_06_02 AS NOMINAL_DIMENSION_IC_06_02,
  etm_plantgroupaccount_ic.NOMINAL_DIMENSION_06_03 AS NOMINAL_DIMENSION_IC_06_03,
 -- plant accounts internal
  bas_account01.ID AS ACCOUNT_REVENUE_01_FK,
  bas_account02.ID AS ACCOUNT_REVENUE_02_FK,
  bas_account03.ID AS ACCOUNT_REVENUE_03_FK,
  bas_account04.ID AS ACCOUNT_REVENUE_04_FK,
  bas_account05.ID AS ACCOUNT_REVENUE_05_FK,
  bas_account06.ID AS ACCOUNT_REVENUE_06_FK,
  -- plant accounts for IC
  bas_account_ic01.ID AS ACCOUNT_REVENUE_IC_01_FK,
  bas_account_ic02.ID AS ACCOUNT_REVENUE_IC_02_FK,
  bas_account_ic03.ID AS ACCOUNT_REVENUE_IC_03_FK,
  bas_account_ic04.ID AS ACCOUNT_REVENUE_IC_04_FK,
  bas_account_ic05.ID AS ACCOUNT_REVENUE_IC_05_FK,
  bas_account_ic06.ID AS ACCOUNT_REVENUE_IC_06_FK,
  lgm_dispatch_record.PRICEPORTION01 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION01,
  lgm_dispatch_record.PRICEPORTION02 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION02,
  lgm_dispatch_record.PRICEPORTION03 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION03,
  lgm_dispatch_record.PRICEPORTION04 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION04,
  lgm_dispatch_record.PRICEPORTION05 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION05,
  lgm_dispatch_record.PRICEPORTION06 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION06,
  /*
      -- plant accounts internal
  CAST(null as int) AS ACCOUNT_REVENUE_01_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_02_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_03_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_04_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_05_FK,
  CAST(null as int) AS ACCOUNT_REVENUE_06_FK,
  -- plant accounts for IC
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_01_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_02_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_03_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_04_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_05_FK,
  CAST(null as int)  AS ACCOUNT_REVENUE_IC_06_FK,
  lgm_dispatch_record.PRICEPORTION_OC01 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION01,
  lgm_dispatch_record.PRICEPORTION_OC02 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION02,
  lgm_dispatch_record.PRICEPORTION_OC03 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION03,
  lgm_dispatch_record.PRICEPORTION_OC04 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION04,
  lgm_dispatch_record.PRICEPORTION_OC05 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION05,
  lgm_dispatch_record.PRICEPORTION_OC06 * lgm_dispatch_record.DELIVEREDQUANTITY AS PRICEPORTION06,
  */
  etm_workoperationtype.CODE as ETM_WORKOPERATIONTYPECODE,
  etm_workoperationtype.DESCRIPTION as ETM_WORKOPERATIONTYPEDESC,
  ISNULL(CAST(0 as int), 0) AS SURCHARGETYPE_FK,
  ISNULL(CAST(0 as int), 0) AS SURCHARGETYPE_EVALUATIONLEVEL,
  ISNULL(CAST(1 as bit), 1) AS SURCHARGETYPE_ISSTANDARDRATE,
  ISNULL(CAST(0 as int), 0) AS PAYMENTGROUPRATE_FK,
  1 AS PAYMENTGROUPRATE_FK2,
  1 AS PAYMENTGROUPRATE_SURCHARGETYPE2,
  1 AS PAYMENTGROUPRATE_RATE2,
  ISNULL(CAST(0 as int), 0) AS PAYMENTGROUPRATE_SURCHARGETYPE,
  ISNULL(txn_result.RATE, 0.00) AS PAYMENTGROUPRATE_RATE,
  ISNULL(CAST(0 as int), 0) AS PAYMENTGROUPSURCHARGE_FK,
  ISNULL(CAST(0 as int), 0) AS PAYMENTGROUPSURCHARGE_SURCHARGETYPE,
  ISNULL(CAST(0.00 as numeric(9,3)), 0.00) AS PAYMENTGROUPSURCHARGE_RATE,
  txn_symbolaccount_cu.ID AS SYMBOLACCOUNTCU_FK,
  txn_symbolaccount_cu.CODE AS SYMBOLACCOUNTCU_CODE,
  txn_cucostresult_iccu.ID AS COSTRESULTINTERCOMPCOSTCU_FK,
  txn_cucostresult_iccu.CODE AS COSTRESULTINTERCOMPCOSTCU_CODE,
  txn_cuperformjob.ID AS PAYMENTGROUPRATECU_FK,
  txn_cuperformjob.CODE AS PAYMENTGROUPRATECU_CODE,
  CAST(null as int) AS PAYMENTGROUPSURCHARGECU_FK,
  CAST(null as nvarchar(32)) AS PAYMENTGROUPSURCHARGECU_CODE,
  txn_cuchargedaction.ID AS CHARGEDACTIONCU_FK,
  txn_cuchargedaction.CODE AS CHARGEDACTIONCU_CODE,
  txn_cuchargedaction.BAS_COMPANY_FK AS CHARGEDACTIONCOMPANY_FK,
  txn_cucompanyrevenue.ID AS COMPANYREVENUECU_FK,
  txn_cucompanyrevenue.CODE AS COMPANYREVENUECU_CODE,
  txn_cuchargedjob.ID AS PLANTJOBCU_FK,
  txn_cuchargedjob.CODE AS PLANTJOBCU_CODE,
  txn_change.ID AS PRJ_CHANGE_FK

from
  TKS_PERIOD txn_period
  INNER JOIN TKS_RECORDING txn_recording on txn_recording.TKS_PERIOD_FK=txn_period.ID
  INNER JOIN TKS_RESULT txn_result on txn_result.TKS_RECORDING_FK=txn_recording.ID
    and txn_result.TKS_SHEET_FK is null and txn_result.ETM_PLANT_FK is not null
  INNER JOIN TKS_TIMESYMBOL txn_resulttimesymbol ON txn_resulttimesymbol.ID = txn_result.TKS_TIMESYMBOL_FK
  INNER JOIN BAS_COMPANY txn_company on txn_company.ID=txn_period.BAS_COMPANY_FK
  INNER JOIN PRJ_ACTION txn_chargedaction on txn_chargedaction.ID=txn_result.PRJ_ACTION_FK
  LEFT JOIN PRJ_CHANGE txn_change on txn_change.Id = txn_chargedaction.PRJ_CHANGE_FK
  LEFT JOIN PSD_ACTIVITY txn_chargedactivity ON txn_chargedactivity.ID = txn_chargedaction.PSD_ACTIVITY_FK
  INNER JOIN PRJ_PROJECT txn_chargedproject ON txn_chargedproject.ID = txn_chargedaction.PRJ_PROJECT_FK 
  LEFT JOIN LGM_JOB txn_chargedjob ON txn_chargedjob.ID = txn_chargedaction.LGM_JOB_FK
  LEFT JOIN BAS_COMPANYICCU txn_intercompanyiccu ON txn_intercompanyiccu.BAS_COMPANY_FK = txn_period.BAS_COMPANY_FK AND
    txn_intercompanyiccu.BAS_COMPANY_RECEIVING_FK = txn_chargedproject.BAS_COMPANY_FK
  LEFT JOIN BAS_COMPANYICCU txn_intercompanyreviccu ON txn_intercompanyreviccu.BAS_COMPANY_FK = txn_intercompanyiccu.BAS_COMPANY_RECEIVING_FK AND
    txn_intercompanyreviccu.BAS_COMPANY_RECEIVING_FK = txn_period.BAS_COMPANY_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cuchargedaction on txn_cuchargedaction.ID=txn_chargedaction.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN TKS_TIMESYMBOLACCOUNT txn_resultsymbolaccount on 
    txn_resultsymbolaccount.BAS_COMPANY_FK=txn_company.ID and
    txn_resultsymbolaccount.BAS_COMPANY_CHARGED_FK=txn_cuchargedaction.BAS_COMPANY_FK and
    txn_resultsymbolaccount.TKS_TIMESYMBOL_FK=txn_result.TKS_TIMESYMBOL_FK 
	and BAS_ACCOUNT_COST_FK is not null
  LEFT JOIN TKS_TIMEALLOCATIONHEADER allocheader on allocHeader.PRJ_PROJECT_FK = txn_chargedproject.ID
  and allocHeader.LGM_JOB_FK = txn_chargedaction.LGM_JOB_FK
  and allocHeader.ALLOCATIONDATE = txn_result.DUE_DATE
  and (allocHeader.ALLOCATIONENDDATE >= txn_result.DUE_DATE OR allocHeader.ALLOCATIONENDDATE IS NULL)
  LEFT JOIN LGM_DISPATCH_HEADER dispatching on dispatching.ID = allocheader.LGM_DISPATCH_HEADER_FK
  LEFT JOIN LGM_JOB performingjob on performingjob.ID = dispatching.LGM_JOB1_FK  
  --
  INNER JOIN ETM_PLANT etm_plant on etm_plant.ID = txn_result.ETM_PLANT_FK
  LEFT JOIN LGM_JOBPLANTALLOCATION lgm_jobplantallocation on lgm_jobplantallocation.ETM_PLANT_FK = etm_plant.ID
  and lgm_jobplantallocation.ALLOCATEDFROM <= txn_result.DUE_DATE 
  and( lgm_jobplantallocation.ALLOCATEDTO >= txn_result.DUE_DATE OR lgm_jobplantallocation.ALLOCATEDTO IS NULL)
  --and allocheader.LGM_JOB_FK = lgm_jobplantallocation.LGM_JOB_FK
  LEFT JOIN ETM_WORKOPERATIONTYPE etm_workoperationtype ON etm_workoperationtype.ID =  lgm_jobplantallocation.ETM_WORKOPERATIONTYPE_FK
  and etm_workoperationtype.ISHIRE = 0
  and etm_workoperationtype.ISMINOREQUIPMENT = 0
  --dev-24618
  INNER JOIN ETM_PLANTGROUPACCOUNT etm_plantgroupaccount on etm_plantgroupaccount.ETM_PLANTGROUP_FK = etm_plant.ETM_PLANTGROUP_FK
  and etm_plantgroupaccount.BAS_ACCOUNTTYPE_FK = 1 -- Cost
  and etm_workoperationtype.ID = etm_plantgroupaccount.ETM_WORKOPERATIONTYPE_FK
  LEFT JOIN BAS_ACCOUNT bas_account01 on bas_account01.ID = etm_plantgroupaccount.BAS_ACCOUNT01_FK
  LEFT JOIN BAS_ACCOUNT bas_account02 on bas_account02.ID = etm_plantgroupaccount.BAS_ACCOUNT02_FK
  LEFT JOIN BAS_ACCOUNT bas_account03 on bas_account03.ID = etm_plantgroupaccount.BAS_ACCOUNT03_FK
  LEFT JOIN BAS_ACCOUNT bas_account04 on bas_account04.ID = etm_plantgroupaccount.BAS_ACCOUNT04_FK
  LEFT JOIN BAS_ACCOUNT bas_account05 on bas_account05.ID = etm_plantgroupaccount.BAS_ACCOUNT05_FK
  LEFT JOIN BAS_ACCOUNT bas_account06 on bas_account06.ID = etm_plantgroupaccount.BAS_ACCOUNT06_FK
  LEFT JOIN ETM_PLANTGROUPACCOUNT etm_plantgroupaccount_ic on etm_plantgroupaccount_ic.ETM_PLANTGROUP_FK = etm_plant.ETM_PLANTGROUP_FK
  and etm_plantgroupaccount_ic.BAS_ACCOUNTTYPE_FK = 5 -- Cost Inter Company
  and etm_workoperationtype.ID = etm_plantgroupaccount_ic.ETM_WORKOPERATIONTYPE_FK
  LEFT JOIN BAS_ACCOUNT bas_account_ic01 on bas_account_ic01.ID = etm_plantgroupaccount_ic.BAS_ACCOUNT01_FK 
  LEFT JOIN BAS_ACCOUNT bas_account_ic02 on bas_account_ic02.ID = etm_plantgroupaccount_ic.BAS_ACCOUNT02_FK
  LEFT JOIN BAS_ACCOUNT bas_account_ic03 on bas_account_ic03.ID = etm_plantgroupaccount_ic.BAS_ACCOUNT03_FK
  LEFT JOIN BAS_ACCOUNT bas_account_ic04 on bas_account_ic04.ID = etm_plantgroupaccount_ic.BAS_ACCOUNT04_FK
  LEFT JOIN BAS_ACCOUNT bas_account_ic05 on bas_account_ic05.ID = etm_plantgroupaccount_ic.BAS_ACCOUNT05_FK
  LEFT JOIN BAS_ACCOUNT bas_account_ic06 on bas_account_ic06.ID = etm_plantgroupaccount_ic.BAS_ACCOUNT06_FK
  --
  INNER JOIN LGM_DISPATCH_RECORD lgm_dispatch_record on lgm_dispatch_record.LGM_DISPATCH_HEADER_FK = allocheader.LGM_DISPATCH_HEADER_FK
  and lgm_dispatch_record.ETM_PLANT_FK = etm_plant.ID

  --
  LEFT JOIN MDC_CONTROLLINGUNIT txn_symbolaccount_cu ON txn_symbolaccount_cu.ID = txn_resultsymbolaccount.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cucostresult_iccu ON txn_cucostresult_iccu.ID = txn_intercompanyiccu.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cucompanyrevenue ON txn_cucompanyrevenue.ID = txn_intercompanyreviccu.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cuchargedjob ON txn_cuchargedjob.ID = txn_chargedjob.MDC_CONTROLLINGUNIT_FK
  LEFT JOIN MDC_CONTROLLINGUNIT txn_cuperformjob ON txn_cuperformjob.ID = performingjob.MDC_CONTROLLINGUNIT_FK  
GO


