import apiConstantData from 'cypress/constantData/apiConstantData';
import { tile, app, cnt, btn, commonLocators, sidebar } from 'cypress/locators';
import { _common, _projectPage, _procurementPage, _estimatePage, _validate, _controllingUnit, _materialPage, _assembliesPage, _boqPage, _mainView, _package, _copyMainEntryToDocumentProject, _commonAPI, _salesPage, _saleContractPage } from 'cypress/pages';
import { DataCells } from 'cypress/pages/interfaces';


const BOQ_DESC = _common.generateRandomString(4),
      BOQ_STRUCTURE_DESC = _common.generateRandomString(4),
      STR_1 = _common.generateRandomString(8),
      STR_2 = _common.generateRandomString(8),
      STR_3 = _common.generateRandomString(8),
      PROJECT_CHANGE_DESCRIPTION = _common.generateRandomString(4)


let CONTAINER_COLUMNS_BOQS,
    CONTAINERS_BOQ_STRUCTURE,
    CONTAINER_COLUMNS_BOQ_STRUCTURE,
    BOQ_API_PARAMETERS_PROJECT: DataCells,
    CONTAINER_COLUMNS_HEADER,
    CONTROLLING_UNIT_PARAMETERS:DataCells,
    CONTAINERS_CONTROLLING_UNIT,
    RESOURCE_PARAMETERS:DataCells,
    CONTAINERS_RESOURCE,
    CONTAINERS_PACKAGE,
    CONTAINER_COLUMNS_CONTRACT,
    CONTAINER_COLUMNS_PES_BOQ_STRUCTURE,
    CONTAINER_COLUMNS_PES_BOQS

describe('PCM- 4.309 | Wizard change project change status in pes module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.preLoading('admin1', Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        cy.fixture('pcm/pes-4.309-wizard-change-project-change-status-in-pes-module.json').then((data) => {
            this.data = data;
            CONTAINER_COLUMNS_BOQS = this.data.CONTAINER_COLUMNS.BOQS;
            CONTAINER_COLUMNS_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.BOQSTRUCTURE;
            CONTAINER_COLUMNS_HEADER=this.data.CONTAINER_COLUMNS.HEADER
            CONTAINERS_CONTROLLING_UNIT=this.data.CONTAINERS.CONTROLLING_UNIT
            CONTAINERS_BOQ_STRUCTURE = this.data.CONTAINERS.BOQSTRUCTURE;
            CONTAINERS_RESOURCE=this.data.CONTAINERS.RESOURCE
            CONTAINERS_PACKAGE=this.data.CONTAINERS.PACKAGE
            CONTAINER_COLUMNS_CONTRACT=this.data.CONTAINER_COLUMNS.CONTRACT
            CONTAINER_COLUMNS_PES_BOQ_STRUCTURE=this.data.CONTAINER_COLUMNS.PES_BOQ_STRUCTURE
            CONTAINER_COLUMNS_PES_BOQS=this.data.CONTAINER_COLUMNS.PES_BOQS
            CONTROLLING_UNIT_PARAMETERS={
                [app.GridCells.QUANTITY_SMALL]:[CONTAINERS_CONTROLLING_UNIT.QUANTITY[0],CONTAINERS_CONTROLLING_UNIT.QUANTITY[0]],
                [app.GridCells.BAS_UOM_FK]:[apiConstantData.ID.UOM_BAGS,apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]:["true","true"]
            }      
            RESOURCE_PARAMETERS = {
                [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY,
                [app.GridCells.CODE]: CONTAINERS_RESOURCE.CODE,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_RESOURCE.QUANTITY,
            };
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear();
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

    it('TC - API: Create BoQ header and BoQ structure', function () {
        BOQ_API_PARAMETERS_PROJECT = {
            [app.GridCells.BRIEF_INFO_SMALL]: BOQ_DESC,
            [app.GridCells.BRIEF_INFO]: [BOQ_STRUCTURE_DESC],
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_BOQ_STRUCTURE.QUANTITY[0]],
            [app.GridCells.PRICE_SMALL]: [CONTAINERS_BOQ_STRUCTURE.UNIT_RATE[0]],
            [app.GridCells.BAS_UOM_FK]: [CONTAINERS_BOQ_STRUCTURE.UOM[0]],
            [app.GridCells.PROJECT_CODE]: Cypress.env('API_PROJECT_NUMBER_1'),
        };
        _commonAPI.createBoQHeaderWithItems(BOQ_API_PARAMETERS_PROJECT,1);
        _commonAPI.getBoQHeaderList(Cypress.env('API_PROJECT_ID_1')).then(() => {
			const boqIds = Cypress.env('API_BOQ_HEADER_ID');
			Cypress.env(`BOQ_HEADER_ID`, boqIds[0]);
		});
    });

    it("TC - API: Create controlling unit", function () {
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'),2,CONTROLLING_UNIT_PARAMETERS)
    })

    it('TC - API: Create estimate header', function () {
		_commonAPI.createEstimateHeader(Cypress.env('API_PROJECT_ID_1'));
	});

    it('TC - API: Generate boq line item', function () {
        _common.openTab(app.TabBar.ESTIMATE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
        _common.search_inSubContainer(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));
        _common.select_rowHasValue(cnt.uuid.ESTIMATE, Cypress.env('API_EST_DESCRIPTION_1'));

        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO);
        _common.waitForLoaderToDisappear();
        _commonAPI.generateBOQFromLeadingStructure(Cypress.env(`API_EST_ID_1`), Cypress.env(`BOQ_HEADER_ID`), Cypress.env('API_PROJECT_ID_1'));

        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
        _common.clear_searchInSidebar();
        _common.waitForLoaderToDisappear();
        cy.REFRESH_CONTAINER();
        _common.waitForLoaderToDisappear();
        _common.select_rowInContainer(cnt.uuid.ESTIMATE_LINEITEMS);
    });

    it('TC - Create resources for line item', function () {
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 1);
        });
        _common.maximizeContainer(cnt.uuid.RESOURCES);
        _common.clear_subContainerFilter(cnt.uuid.RESOURCES);
        _common.create_newRecord(cnt.uuid.RESOURCES);
        _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS);
        _common.minimizeContainer(cnt.uuid.RESOURCES);
        _common.waitForLoaderToDisappear();
        cy.SAVE();
        _common.waitForLoaderToDisappear();
        cy.SAVE();
        _common.waitForLoaderToDisappear();
    });

    it('TC - Create BoQ package from the estimate', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_UPDATE_BOQ_PACKAGE);
        cy.wait(2000) // added this wait so that loader can load;
        let BOQ_PACKAGE:DataCells={
            [commonLocators.CommonLabels.SELECT_ESTIMATE_SCOPE]:CONTAINERS_PACKAGE.ESTIMATE_SCOPE,
            [commonLocators.CommonKeys.RADIO_INDEX]:CONTAINERS_PACKAGE.ESTIMATE_SCOPE_INDEX,
            [commonLocators.CommonLabels.SELECT_GROUPING_STRUCTURE_TO_CREATE_PACKAGE]:CONTAINERS_PACKAGE.BASED_ON,
            [commonLocators.CommonLabels.CONSOLIDATE_TO_ONE_PACKAGE_FOR_ALL_SELECTED_CRITERIA]:commonLocators.CommonKeys.CHECK,
            [commonLocators.CommonLabels.PROCUREMENT_STRUCTURE]:CONTAINERS_PACKAGE.PROCUREMENT_STRUCTURE,
            [commonLocators.CommonLabels.COLUMN_FILTER_SELECTION]:commonLocators.CommonKeys.ALL
        }
        _package.enterRecord_toCreateBoQPackage_usingWizard_new(BOQ_PACKAGE)
        _common.clickOn_modalFooterButton(commonLocators.CommonLabels.FINISH)
        cy.wait(2000) // added this wait so that loader can load;
        _package.storePackageCode_fromModal()

        _common.clickOn_modalFooter_goToButton()
        cy.wait(2000) // added this wait so that loader can load;

    });
    
    it("TC - Create contract", function () {
        _common.waitForLoaderToDisappear();
         _common.openTab(app.TabBar.PACKAGE).then(() => {
            _common.setDefaultView(app.TabBar.PACKAGE);
            _common.select_tabFromFooter(cnt.uuid.PACKAGE, app.FooterTab.PACKAGE, 0);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'));
        _common.waitForLoaderToDisappear()
      
        _common.select_rowHasValue(cnt.uuid.PACKAGE,Cypress.env("PK-Code"));
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PACKAGE_STATUS);
        _common.waitForLoaderToDisappear()
        _common.changeStatus_fromModal(commonLocators.CommonKeys.IN_MINUS_PROGRESS);
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_CONTRACT);
        _common.waitForLoaderToDisappear()
        _package.create_ContractfromPackage(CONTAINERS_BOQ_STRUCTURE.BUSINESS_PARTNER)
        cy.wait(2000) // added this wait so that loader can load // This wait required as UI takes time to load
        _common.waitForLoaderToDisappear()
    })

    it("TC - Update contract status", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
            _common.waitForLoaderToDisappear()
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT,CONTAINER_COLUMNS_CONTRACT)
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("API_PROJECT_NUMBER_1"))
            _common.waitForLoaderToDisappear()
        })
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("CONTRACT_CODE"))
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PACKAGE.CLERK[0])
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_REQ_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PACKAGE.CLERK[1])
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env("API_CNT_CODE_0"))
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
        _common.waitForLoaderToDisappear()
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED)
        _common.waitForLoaderToDisappear()

    })

    it("TC - Project changes",function(){
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CHANGES);
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.clear_searchInSidebar()
		_common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CHANGES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CHANGES, app.FooterTab.CHANGES, 0)
        })
        _common.clear_subContainerFilter(cnt.uuid.CHANGES)
        _common.create_newRecord(cnt.uuid.CHANGES);
        cy.wait(2000) // added this wait so that loader can load
        _common.waitForLoaderToDisappear()
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.PROJECT_NAME,Cypress.env(`API_PROJECT_NUMBER_1`),commonLocators.CommonKeys.GRID)
        _common.waitForLoaderToDisappear()
        _common.edit_dropDownWithCaret_fromModal_byClass(app.ModalInputFields.CHANGE_TYPE,commonLocators.CommonKeys.DESIGN_CHANGE,commonLocators.CommonKeys.LIST)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.CHANGES, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, PROJECT_CHANGE_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.CHANGES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })

    it('TC - Create PES', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PES);
        _common.waitForLoaderToDisappear()
        cy.wait(2000) // added this wait so that loader can load // added this wait as script was getting failed

        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()
        
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.setDefaultView(app.TabBar.PERFORMANCEENTRYSHEET)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
            _common.setup_gridLayout(cnt.uuid.HEADERS,CONTAINER_COLUMNS_HEADER)
            _common.waitForLoaderToDisappear()
            _common.set_columnAtTop([CONTAINER_COLUMNS_HEADER.projectfk,CONTAINER_COLUMNS_HEADER.conheaderfk,CONTAINER_COLUMNS_HEADER.description],cnt.uuid.HEADERS)
            _common.waitForLoaderToDisappear()
        });

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS, 1);
            _common.setup_gridLayout(cnt.uuid.PES_ITEMS,CONTAINER_COLUMNS_PES_BOQS)
        });


        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
            _common.setup_gridLayout(cnt.uuid.PES_BOQS_STRUCTURE,CONTAINER_COLUMNS_PES_BOQ_STRUCTURE)
            _common.waitForLoaderToDisappear()
            _common.set_columnAtTop([CONTAINER_COLUMNS_PES_BOQ_STRUCTURE.prjchangefk,CONTAINER_COLUMNS_PES_BOQ_STRUCTURE.prjchangestatusfk,CONTAINER_COLUMNS_PES_BOQ_STRUCTURE.briefinfo],cnt.uuid.PES_BOQS_STRUCTURE)
            _common.waitForLoaderToDisappear()
        });

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.HEADERS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.HEADERS,app.GridCells.CON_HEADER_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, Cypress.env("CONTRACT_CODE"))
        _common.select_activeRowInContainer(cnt.uuid.HEADERS)
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
         _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.search_inSubContainer(cnt.uuid.HEADERS,Cypress.env(`API_PROJECT_NUMBER_1`))


        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQS, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PES_ITEMS)
        _common.search_inSubContainer(cnt.uuid.PES_ITEMS,Cypress.env(`CONTRACT_CODE`))

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
        });

        _common.clear_subContainerFilter(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.select_allContainerData(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.create_newRecord(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_BOQS_STRUCTURE,app.GridCells.PRJ_CHANGE_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, PROJECT_CHANGE_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PES_BOQS_STRUCTURE,app.GridCells.BRIEF_INFO_SMALL,app.InputFields.DOMAIN_TYPE_TRANSLATION, STR_1)
        _common.select_activeRowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.select_allContainerData(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.create_newRecord(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_BOQS_STRUCTURE,app.GridCells.PRJ_CHANGE_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, PROJECT_CHANGE_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PES_BOQS_STRUCTURE,app.GridCells.BRIEF_INFO_SMALL,app.InputFields.DOMAIN_TYPE_TRANSLATION, STR_2)
        _common.select_activeRowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()


        _common.select_allContainerData(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.create_newRecord(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PES_BOQS_STRUCTURE,app.GridCells.PRJ_CHANGE_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT, PROJECT_CHANGE_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PES_BOQS_STRUCTURE,app.GridCells.BRIEF_INFO_SMALL,app.InputFields.DOMAIN_TYPE_TRANSLATION, STR_3)
        _common.select_activeRowInContainer(cnt.uuid.PES_BOQS_STRUCTURE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })

    it('TC - It allows to change one record by wizard at one time', function () {
       
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
        });

        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,STR_1)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PROJECT_CHANGE_STATUS);
        cy.wait(1000) // added this wait so that loader can load
		_common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED);
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)

		_common.waitForLoaderToDisappear()
		cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()

        _common.clickOn_expandCollapseMenu_columnByLevels(cnt.uuid.PES_BOQS_STRUCTURE,btn.GridButtons.UI_STRUCTURE_MENU,btn.GridButtons.LEVEL_3)

        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,STR_1)
		_common.assert_cellData_insideActiveRow(cnt.uuid.PES_BOQS_STRUCTURE, app.GridCells.PRJ_CHANGE_STATUS_FK, commonLocators.CommonKeys.APPROVED);
    })

    it('TC - In change status ui, check the status filter', function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_BOQS_STRUCTURE, app.FooterTab.BOQ_STRUCTURE, 1);
        });

        _common.select_rowHasValue(cnt.uuid.PES_BOQS_STRUCTURE,STR_1)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PROJECT_CHANGE_STATUS);
        cy.wait(2000) // added this wait so that loader can load

        _common.clickOn_checkboxByLabel_fromModal(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonElements.ONLY_SHOW_AVAILABLE_STATUS, 0)
        _validate.verify_statusRecordNotPresentInModal(commonLocators.CommonKeys.ACCEPTED_IN_PRINCIPLE)

        _common.clickOn_checkboxByLabel_fromModal(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonElements.ONLY_SHOW_AVAILABLE_STATUS, 0,commonLocators.CommonKeys.UNCHECK)
        _validate.verify_statusRecordPresentInModal(commonLocators.CommonKeys.ACCEPTED_IN_PRINCIPLE)
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)

        _common.waitForLoaderToDisappear()
        _common.clickOn_expandCollapseMenu_columnByLevels(cnt.uuid.PES_BOQS_STRUCTURE,btn.GridButtons.UI_STRUCTURE_MENU,btn.GridButtons.LEVEL_3)
    })


    after(() => {
    	cy.LOGOUT();
    });
});