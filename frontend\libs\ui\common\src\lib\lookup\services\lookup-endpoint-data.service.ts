/*
 * Copyright(c) RIB Software GmbH
 */

import { get, isObject } from 'lodash';
import { Observable, map, of, firstValueFrom, from } from 'rxjs';

import { IEntityContext, IIdentificationData } from '@libs/platform/common';

import { ILookupSearchRequest } from '../model/interfaces/lookup-search-request.interface';
import { ILookupSearchResponse } from '../model/interfaces/lookup-search-response.interface';
import { UiCommonLookupReadonlyDataService } from './lookup-readonly-data.service';
import { ILookupConfig } from '../model/interfaces/lookup-options.interface';
import { ILookupEndpointConfig } from '../model/interfaces/lookup-endpoint-config.interface';
import { LookupSearchResponse } from '../model/lookup-search-response';
import { LookupIdentificationData } from '../model/lookup-identification-data';
import { ILookupContext } from '../model/interfaces/lookup-context.interface';
import { LookupUrlParams } from '../model/interfaces/lookup-http-cache.model';

/**
 * The lookup data service whose data is from web api endpoint
 */
export class UiCommonLookupEndpointDataService<TItem extends object, TEntity extends object = object> extends UiCommonLookupReadonlyDataService<TItem, TEntity> {
	/**
	 * The constructor
	 * @param endpoint
	 * @param config
	 */
	public constructor(
		public endpoint: ILookupEndpointConfig<TItem, TEntity>,
		config: ILookupConfig<TItem, TEntity>,
	) {
		super(config);
		this.cache.enabled = true;
		this.paging.enabled = false;
		this.config.canListAll = !endpoint.filterParam;
		this.syncService = {
			getListSync: () => {
				return this.cache.getAll();
			},
		};
		this.config.searchSync = true;
		this.config.isClientSearch = true;

		if (endpoint.dataProcessors) {
			this.dataProcessors = this.dataProcessors.concat(endpoint.dataProcessors);
		}
	}

	/**
	 * Get data item by identification data
	 * @param key
	 * @param context
	 * @deprecated use getItemByKeyAsync instead
	 */
	public getItemByKey(key: IIdentificationData, context?: IEntityContext<TEntity>): Observable<TItem> {
		const cacheItem = this.cache.getItem(key);

		if (cacheItem) {
			return of(cacheItem);
		}

		if (this.endpoint.httpRead.endpointItem) {
			const url = `${this.endpoint.httpRead.route}/${this.endpoint.httpRead.endpointItem}`;
			return this.post<TItem>(url, key, (e) => {
				const item = e as TItem;
				this.cache.setItem(item);
				return item;
			});
		}

		// The default implementation of getItemByKey for this kind of lookup service which assume that lookup is with small data and all data could be returned from backend
		// If lookup doesn't support to get all list from backend, then provide endpoint "endpointItem" or override this method
		return this.getList(context).pipe(
			map((list) => {
				let dataItem: TItem | null | undefined = list.find((item) => LookupIdentificationData.equal(key, this.identify(item)));

				// maybe this key is invalid, try to get invalid item
				if (!dataItem) {
					dataItem = this.cache.getItem(key);
				}

				return dataItem as TItem;
			}),
		);
	}

	/**
	 * Get list
	 * @param context
	 * @deprecated use getListAsync instead
	 */
	public getList(context?: IEntityContext<TEntity>): Observable<TItem[]> {
		return from(this.getListAsync(context));
	}

	/**
	 * Get search list
	 * @param request
	 * @param context
	 * @deprecated use getSearchListAsync instead
	 */
	public getSearchList(request: ILookupSearchRequest, context?: IEntityContext<TEntity>): Observable<ILookupSearchResponse<TItem>> {
		return from(this.getSearchListAsync(request, context));
	}

	public override async getListAsync(context?: IEntityContext<TEntity>): Promise<TItem[]> {
		if (this.endpoint.filterParam) {
			const filter = await this.prepareListFilterAsync(context);
			const response = await this.getListByFilter(filter);
			return response.items;
		}

		if (this.cache.loaded) {
			return this.cache.list;
		}

		return this.readData<TItem[]>(undefined, (res) => {
			let list = res as TItem[];

			list = this.handleList(list);

			if (this.cache.enabled) {
				this.cache.setList(list);
			}

			return list;
		});
	}

	public override async getSearchListAsync(request: ILookupSearchRequest, context?: IEntityContext<TEntity>): Promise<ILookupSearchResponse<TItem>> {
		const filter = await this.prepareSearchFilterAsync(request, context);
		return this.getListByFilter(filter);
	}

	protected override convertList(list: TItem[]) {
		if (this.endpoint.tree) {
			list = this.flattenTreeList(list);
		}

		return list;
	}

	private async getListByFilter(filter: string | object | undefined): Promise<ILookupSearchResponse<TItem>> {
		const cacheKey = JSON.stringify(filter);
		const cache = this.cache.getSearchList(cacheKey);

		if (cache) {
			return cache;
		}

		return this.readData<ILookupSearchResponse<TItem>>(filter, (res) => {
			let list = res as TItem[];

			list = this.handleList(list);

			const response = new LookupSearchResponse(list);

			if (this.cache.enabled) {
				this.cache.setSearchList(cacheKey, response);
			}

			return response;
		});
	}

	private flattenTreeList(treeList: TItem[], flatList?: TItem[]) {
		if (!flatList) {
			flatList = [];
		}

		treeList.forEach((item) => {
			flatList!.push(item);

			const children = get(item, this.endpoint.tree!.childProp);

			if (children && children.length > 0) {
				this.flattenTreeList(children, flatList);
			}
		});

		return flatList;
	}

	private async readData<T>(payload?: unknown, mapper?: (result: unknown) => T): Promise<T> {
		const httpRead = this.endpoint.httpRead;
		let route = httpRead.route;

		// Regularly the route should not end with '/' which will be automatically inserted in combination
		if (!route.endsWith('/')) {
			route += '/';
		} else {
			console.log('Please remove the last "/" character from route string, it is not necessary!');
		}

		if (httpRead.usePostForRead) {
			return firstValueFrom(this.post<T>(route + httpRead.endPointRead, payload, mapper));
		}

		if (payload && isObject(payload)) {
			return firstValueFrom(this.get<T>(route + httpRead.endPointRead, payload as LookupUrlParams, mapper));
		}

		return firstValueFrom(this.get<T>(route + httpRead.endPointRead + (payload ? `?${payload}` : ''), undefined, mapper));
	}

	protected async prepareSearchFilterAsync(request: ILookupSearchRequest, context?: IEntityContext<TEntity>): Promise<string | object | undefined> {
		const filter = this.prepareSearchFilter(request, context);

		if (filter) {
			return filter;
		}

		return await this.prepareListFilterAsync(context);
	}

	protected prepareSearchFilter(request: ILookupSearchRequest, context?: IEntityContext<TEntity>): string | object | undefined {
		if (this.endpoint.prepareSearchFilter) {
			return this.endpoint.prepareSearchFilter(request, context);
		}
		return undefined;
	}

	protected async prepareListFilterAsync(context?: IEntityContext<TEntity>): Promise<string | object | undefined> {
		let payload: object | undefined;
		const lookupContext = context as ILookupContext<TItem, TEntity>;

		if (lookupContext?.lookupConfig?.lookupPayloadProvider) {
			payload = await lookupContext.lookupConfig.lookupPayloadProvider(lookupContext);
		}

		return this.prepareListFilter(context, payload);
	}

	protected prepareListFilter(context?: IEntityContext<TEntity>, payload?: object): string | object | undefined {
		if (this.endpoint.prepareListFilter) {
			return this.endpoint.prepareListFilter(context, payload);
		}
		if (payload) {
			return payload;
		}
		if (this.endpoint.filterParam) {
			throw new Error('prepareListFilter is not defined!');
		}
		return undefined;
	}
}
