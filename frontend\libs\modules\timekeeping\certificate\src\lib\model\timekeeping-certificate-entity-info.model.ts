/*
 * Copyright(c) RIB Software GmbH
 */

import { EntityInfo } from '@libs/ui/business-base';
import { ICertificateEntity } from '@libs/resource/interfaces';
import { TimekeepingCertificateDataService } from '../services/timekeeping-certificate-data.service';
import { TimekeepingEmployeeCertificateValidationService } from '../services/timekeeping-certificate-validation.service';
import { ILayoutConfiguration } from '@libs/ui/common';
import { prefixAllTranslationKeys } from '@libs/platform/common';
import {
	BasicsSharedCustomizeLookupOverloadProvider,
	BasicsSharedLookupOverloadProvider,
} from '@libs/basics/shared';
import { EMPLOYEE_CERTIFICATION_LOOKUP_PROVIDER_TOKEN } from '@libs/timekeeping/interfaces';
import { BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN } from '@libs/basics/interfaces';
import { BUSINESSPARTNER_RELATED_LOOKUP_PROVIDER_TOKEN } from '@libs/businesspartner/interfaces';


export const TIMEKEEPING_CERTIFICATE_ENTITY_INFO: EntityInfo = EntityInfo.create<ICertificateEntity> ({
	grid: {
		title: {key: 'timekeeping.certificate' + '.certificateListTitle'},
	},
	form: {
		title: { key: 'timekeeping.certificate' + '.certificateDetailTitle' },
		containerUuid: '1c474c77cb944482833296349056c317',
	},
	dataService: ctx => ctx.injector.get(TimekeepingCertificateDataService),
	validationService: ctx => ctx.injector.get(TimekeepingEmployeeCertificateValidationService),
	dtoSchemeId: {moduleSubModule: 'Timekeeping.Certificate', typeName: 'EmployeeCertificateDto'},
	permissionUuid: 'c9420131ec4a48a1a6524c4927252f47',
	layoutConfiguration: async ctx => {
		const certificateLookupProvider = await ctx.lazyInjector.inject(EMPLOYEE_CERTIFICATION_LOOKUP_PROVIDER_TOKEN);
		const basicsSharedCustomizeLookupOverloadProvider = await ctx.lazyInjector.inject(BASICS_CUSTOMIZE_LOOKUP_PROVIDER_TOKEN);
		const bpRelatedLookupProvider = await ctx.lazyInjector.inject(BUSINESSPARTNER_RELATED_LOOKUP_PROVIDER_TOKEN);
		return <ILayoutConfiguration<ICertificateEntity>>{
			groups: [
				{
					gid: 'default-group',
					attributes: [
						'CertificateTypeFk',
						'CertificateStatusFk',
						'ValidFrom',
						'ValidTo',
						'Comment',
						'Remark',
						'ClerkFk',
						'DescriptionInfo',
						'BusinessPartnerFk',
						'ContactFk',
						'SupplierFk'
					]
				}
			],
			overloads: {
				CertificateTypeFk: basicsSharedCustomizeLookupOverloadProvider.provideTimekeepingEmployeeCertificateTypeReadonlyLookupOverload(),
				CertificateStatusFk: certificateLookupProvider.generateEmployeeCertificationLookup(),
				ClerkFk: BasicsSharedLookupOverloadProvider.providerBasicsClerkLookupOverload(true),
				BusinessPartnerFk: BasicsSharedCustomizeLookupOverloadProvider.provideRfqBusinessPartnerStatusLookupOverload(false),
				ContactFk: bpRelatedLookupProvider.getContactLookupOverload({showClearButton: true, displayMember: 'FullName'}),
				SupplierFk: bpRelatedLookupProvider.getSupplierLookupOverload({showClearButton: true}),
			},
			labels: {
				...prefixAllTranslationKeys('timekeeping.certificate.', {
					CertificateTypeFk: { key: 'entityType' },
					CertificateStatusFk: { key: 'entityStatus' },
					ValidFrom: { key: 'entityValidFrom' },
					ValidTo: { key: 'entityValidTo' },
					Comment: { key: 'entityComment' },
					Remark: { key: 'entityRemark' },
					ClerkFk: { key: 'entityClerkFk' },
					DescriptionInfo: { key: 'entityDescription' },
					BusinessPartnerFk: { key: 'entityBusinessPartnerFk' },
					ContactFk: { key: 'entityContactFk' },
					SupplierFk: { key: 'entitySupplierFk' }
				})
			}
		};
	}
});
