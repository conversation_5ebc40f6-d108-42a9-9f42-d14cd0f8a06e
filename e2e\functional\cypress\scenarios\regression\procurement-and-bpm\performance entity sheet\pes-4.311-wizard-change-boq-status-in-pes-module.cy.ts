import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _procurementContractPage, _boqPage, _validate, _procurementPage } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import { BOQ_HEADER } from "cypress/pages/variables";


const OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const BOQ_OUTLINE_SPECIFICATION = _common.generateRandomString(4);
const CONTRACT_DESC = _common.generateRandomString(4);
const DATA_TYPE_DESC = _common.generateRandomString(4);
const DATA_TYPE_CODE = _common.generateRandomString(4);

let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_PROCUREMENT_BOQ, CONTAINER_COLUMNS_BOQ_STRUCTURE;
let PROCUREMENT_CONTRACT_PARAMETER: DataCells, BOQ_STRUCTURE_PARAMETERS: DataCells;
let CONTAINERS_DATA_TYPES, CONTAINERS_PROCUREMENT_STRUCTURE, CONTAINERS_CONFIGURATION, CONTAINERS_BOQ_STRUCTURE;

describe('PCM- 4.311 | Wizard change boq status in pes module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture('pcm/pes-4.311-wizard-change-boq-status-in-pes-module.json').then((data) => {
            this.data = data;
            CONTAINERS_DATA_TYPES = this.data.CONTAINERS.DATA_TYPES
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            CONTAINERS_CONFIGURATION = this.data.CONTAINERS.CONFIGURATION
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: commonLocators.CommonKeys.SERVICE,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONFIGURATION.BUSINESS_PARTNER
            }
            CONTAINERS_PROCUREMENT_STRUCTURE = this.data.CONTAINERS.PROCUREMENT_STRUCTURE
            CONTAINER_COLUMNS_PROCUREMENT_BOQ = this.data.CONTAINER_COLUMNS.PROCUREMENT_BOQ;
            CONTAINER_COLUMNS_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.BOQ_STRUCTURE
            CONTAINERS_BOQ_STRUCTURE = this.data.CONTAINERS.BOQ_STRUCTURE
            BOQ_STRUCTURE_PARAMETERS = {
                [commonLocators.CommonLabels.TYPE]: commonLocators.CommonLabels.NEW_RECORD,
                [app.GridCells.BRIEF_INFO_SMALL]: BOQ_OUTLINE_SPECIFICATION,
                [app.GridCells.BAS_UOM_FK]: CONTAINERS_BOQ_STRUCTURE.UOM,
                [app.GridCells.PRICE_SMALL]: CONTAINERS_BOQ_STRUCTURE.CORRECTION,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_BOQ_STRUCTURE.QUANTITY
            }
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, CONTAINERS_CONFIGURATION.PROJECT).pinnedItem();
            });
        });
    })

    after(() => {
        cy.LOGOUT();
    });

    it('TC - Create Contract for respective project and with structure', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT);
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_REQ_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONFIGURATION.CLERK);
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONFIGURATION.CLERK);
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONFIGURATION.CONTROLLING_UNIT);

        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.STRUCTURE_CODE, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PROCUREMENT_STRUCTURE.PROCUREMENT_STRUCTURE);
        cy.SAVE();
        _common.waitForLoaderToDisappear
        _common.clickOn_modalFooterButton(btn.ButtonText.YES)
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, CONTRACT_DESC)
    });

    it('TC - Create Procurement BOQ for the respective contract', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT_BOQS)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS);
        _common.waitForLoaderToDisappear()
        _boqPage.enterRecord_ToCreate_procurementBoQs(CommonLocators.CommonKeys.SERVICE, OUTLINE_SPECIFICATION, commonLocators.CommonLabels.CREATE_NEW_BOQ);
        cy.SAVE()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS);
        _common.waitForLoaderToDisappear()
        _boqPage.enterRecord_ToCreate_procurementBoQs(CommonLocators.CommonKeys.SERVICE, OUTLINE_SPECIFICATION, commonLocators.CommonLabels.CREATE_NEW_BOQ);
        cy.SAVE()
    });

    it('TC - Create pes from contract wizard option', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED)
        _common.waitForLoaderToDisappear()

        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES);
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _procurementPage.getCode_fromPESModal("PES_CODE")
        _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_PES)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS, 0)
            _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        })
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.HEADERS)
    });

    it("TC - Verify to change one reocrd by wizard at one time ", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.setDefaultView(app.TabBar.PERFORMANCEENTRYSHEET)
            _common.set_containerLayoutUnderEditView(commonLocators.CommonLabels.LAYOUT_6)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQs, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PES_ITEMS)
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
        _common.changeStatus_fromModal(commonLocators.CommonKeys.LOCKED);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        cy.REFRESH_CONTAINER()
        _common.select_rowHasValue(cnt.uuid.PES_ITEMS, commonLocators.CommonKeys.LOCKED)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_ITEMS, app.GridCells.BOQ_STATUS_FK, commonLocators.CommonKeys.LOCKED, BOQ_HEADER);
        _common.select_rowHasValue(cnt.uuid.PES_ITEMS, commonLocators.CommonKeys.LOCKED)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
        _common.changeStatus_fromModal(commonLocators.CommonKeys.IN_PROGRESS_SPACE);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        cy.REFRESH_CONTAINER()

    })

    it("TC - Verify to change mupltile records by wizard at one time ", function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQs, 1);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, (Cypress.env("PES_CODE")));
        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _common.select_allContainerData(cnt.uuid.PES_ITEMS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
        _common.changeStatus_ofMultipleRecord_fromModal(commonLocators.CommonKeys.LOCKED)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_ITEMS, commonLocators.CommonKeys.LOCKED)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_ITEMS, app.GridCells.BOQ_STATUS_FK, commonLocators.CommonKeys.LOCKED, BOQ_HEADER);
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PES_ITEMS, app.GridCells.BOQ_STATUS_FK, commonLocators.CommonKeys.LOCKED, BOQ_HEADER);
    })

    it('TC - Verify message added to history of boq', function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQs, 1);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, (Cypress.env("PES_CODE")));
        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
        _common.changeStatus_fromModal(commonLocators.CommonKeys.IN_PROGRESS_SPACE, CONTAINERS_CONFIGURATION.PROJECT)
        cy.SAVE()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PES_ITEMS, commonLocators.CommonKeys.IN_PROGRESS_SPACE)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.HISTORY)
        _common.clickOn_cellHasValue_fromModal(app.GridCells.REMARK, CONTAINERS_CONFIGURATION.PROJECT)
        _validate.validate_activeRowText_inModal(app.GridCells.REMARK, CONTAINERS_CONFIGURATION.PROJECT);
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
    });

    it("TC - Change status ui, check the status filter", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES, 0);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CONTAINERS_DATA_TYPES.DATA_TYPE)
        _common.select_rowHasValue(cnt.uuid.DATA_TYPES, CONTAINERS_DATA_TYPES.DATA_TYPE)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORDS, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
        _common.create_newRecord(cnt.uuid.DATA_RECORDS)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, DATA_TYPE_DESC)
        _common.enterRecord_inNewRow(cnt.uuid.DATA_RECORDS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, DATA_TYPE_CODE)
        cy.SAVE()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PES);
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PES_ITEMS, app.FooterTab.BOQs, 1);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, (Cypress.env("PES_CODE")));
        _common.select_rowInContainer(cnt.uuid.HEADERS)
        _common.select_rowInContainer(cnt.uuid.PES_ITEMS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_BOQ_STATUS);
        _common.clickOn_checkboxByLabel_fromModal(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonElements.ONLY_SHOW_AVAILABLE_STATUS, 0)
        _validate.verify_recordNotPresentInmodal(DATA_TYPE_DESC)
        _common.waitForLoaderToDisappear()
        _common.clickOn_checkboxByLabel_fromModal(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonElements.ONLY_SHOW_AVAILABLE_STATUS, 0)
        _validate.verify_recordPresentInmodal(DATA_TYPE_DESC)
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
    });
});