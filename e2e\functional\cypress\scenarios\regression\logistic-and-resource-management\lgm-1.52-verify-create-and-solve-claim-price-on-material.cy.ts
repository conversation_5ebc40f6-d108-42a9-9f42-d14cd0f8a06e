import apiConstantData from "cypress/constantData/apiConstantData";
import { tile, app, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _estimatePage, _validate, _mainView, _boqPage, _modalView, _logesticPage, _controllingUnit, _projectPage, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const PLANT_DESCRIPTION = "Excavator" + Cypress._.random(0, 999);
const CONDITION_CODE = _common.generateRandomString(3)
const CONDITION_DESC = _common.generateRandomString(3)


let CONTAINER_PROJECT,
    PROJECT_PARAMETERS1,
    PROJECT_PARAMETERS2

let CONTAINER_COLUMNS_DATA_TYPES,
    CONTAINER_DATA_RECORD


let CONTAINERS_CONTROLLING_UNITS,
    CONTAINER_COLUMNS_CONTROLLING_UNITS,
    CONTROLLING_UNIT_MAIN_PARAMETERS,
    CONTROLLING_UNIT_SUB_PARAMETERS

let CONTAINER_COLUMNS_CONTROLLING_UNIT2;
let CONTROLLING_UNIT_PARAMETERS2: DataCells;

let CONTAINERS_PLANT_GROUP,
    CONTAINER_COLUMNS_PLANT_GROUP

let CONTAINERS_PLANT,
    CONTAINER_COLUMNS_PLANT,
    PLANT_PARAMETERS,
    CONTAINER_COLUMNS_PLANT_CONTROLLING;

let CONTAINERS_PLANT_PRICE_LISTS
let ALLOCATION_FOR_PLANTS_PARAMETER

let MODAL_PLANT_ALLOCATION,
    MODAL_SETTLEMENT_CLAIM

let CONTAINER_COLUMNS_JOBS;
let CONTAINER_JOBS;
let CONTAINER_COLUMNS_CONDITIONS
let CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS
let CONTAINER_COLUMNS_DISPATCHING_HEADER,
    CONTAINERS_DISPATCHING_HEADER,
    CONTAINER_COLUMNS_SETTLEMENT_ITEMS,
    CONTAINER_COLUMNS_CLERK
let CONTAINER_COLUMNS_DISPATCHING_RECORD
let SETTLEMENT_CLAIM_PARAMETER,
    CONTAINER_MATERIAL_PRICE,
    CONTAINER_COLUMNS_MATERIAL_PRICE


describe("LRM- 1.52 | Verify Create and solve Claim - Price on Material", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });

    before(function () {
        cy.fixture("LRM/lgm-1.52-verify-create-and-solve-claim-price-on-material.json").then((data) => {
            this.data = data;
            CONTAINER_DATA_RECORD = this.data.CONTAINERS.DATA_RECORD;
            CONTAINER_COLUMNS_DATA_TYPES = this.data.CONTAINER_COLUMNS.DATA_TYPES;
            CONTAINER_PROJECT = this.data.CONTAINERS.PROJECT
            CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNITS
            CONTAINERS_PLANT_GROUP = this.data.CONTAINERS.PLANT_GROUP;
            CONTAINER_COLUMNS_PLANT_GROUP = this.data.CONTAINER_COLUMNS.PLANT_GROUP;
            CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;
            CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
            PLANT_PARAMETERS = {
                [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION,
                [app.GridCells.PLANT_GROUP_FK]: CONTAINERS_PLANT.PLANT_GROUP,
                [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
                [app.GridCells.PLANT_TYPE_FK]: CONTAINERS_PLANT.PLANT_TYPE,
                [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
                [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
            }
            CONTAINER_COLUMNS_PLANT_CONTROLLING = this.data.CONTAINER_COLUMNS.PLANT_CONTROLLING;
            CONTAINERS_PLANT_PRICE_LISTS = this.data.CONTAINERS.PLANT_PRICE_LISTS;
            CONTAINER_COLUMNS_DISPATCHING_RECORD = this.data.CONTAINER_COLUMNS.DISPATCHING_RECORD;
            MODAL_PLANT_ALLOCATION = this.data.MODAL.PLANT_ALLOCATION;
            ALLOCATION_FOR_PLANTS_PARAMETER = {
                [commonLocators.CommonLabels.JOB]: Cypress.env('API_PROJECT_NUMBER_2'),
                [commonLocators.CommonLabels.ALLOCATED_FROM]: MODAL_PLANT_ALLOCATION.ALLOCATED_FROM,
                [app.GridCells.WORK_OPERATION_TYPE_FK]: MODAL_PLANT_ALLOCATION.WORK_OPERATION
            }

            CONTAINER_COLUMNS_JOBS = this.data.CONTAINER_COLUMNS.JOBS
            CONTAINER_JOBS = this.data.CONTAINERS.JOBS
            CONTAINER_COLUMNS_CONDITIONS = this.data.CONTAINER_COLUMNS.CONDITIONS
            CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS = this.data.CONTAINERS.WORK_OPERATION_TYPE_CONDITIONS
            CONTAINER_COLUMNS_DISPATCHING_HEADER = this.data.CONTAINER_COLUMNS.DISPATCHING_HEADER
            CONTAINERS_DISPATCHING_HEADER = this.data.CONTAINERS.DISPATCHING_HEADER
            CONTAINER_COLUMNS_SETTLEMENT_ITEMS = this.data.CONTAINER_COLUMNS.SETTLEMENT_ITEMS
            CONTAINER_COLUMNS_CLERK = this.data.CONTAINER_COLUMNS.CLERK
            MODAL_SETTLEMENT_CLAIM = this.data.MODAL.SETTLEMENT_CLAIM
            SETTLEMENT_CLAIM_PARAMETER = {
                [commonLocators.CommonLabels.CLAIM_REASON]: "Price too high /low for this Material",
                [commonLocators.CommonLabels.COMMENTS]: "Price too high /low for this Material",

            }
            CONTAINER_MATERIAL_PRICE = this.data.CONTAINERS.MATERIAL_PRICE
            CONTAINER_COLUMNS_MATERIAL_PRICE = this.data.CONTAINER_COLUMNS.MATERIAL_PRICE
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT)
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });

    })

    after(() => {
        cy.LOGOUT();
    });

    it("TC - API call to assign logged-in user a clerk", function () {
        _commonAPI.getULoggedInUserId(Cypress.env("USER_NAME"))
            .then(() => {
                _commonAPI.assignUserToClerk(Cypress.env("API_LOGGED_IN_USER_ID"), Cypress.env("USER_NAME"), apiConstantData.CONSTANT.SMIJ)
            })
    });

    it('TC - API: Create project A', function () {
        _commonAPI.createProject()
    });

    it("TC - API: Create Controlling Units", function () {
        CONTROLLING_UNIT_SUB_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true"]
        }
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_SUB_PARAMETERS)

    })

    it('TC - API: Create project B', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2')).pinnedItem();
        });
    });

    it("TC - API: Create Controlling Units", function () {

        CONTROLLING_UNIT_SUB_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_BILLING_ELEMENT]: ["true", "true"],
            [app.GridCells.IS_ACCOUNTING_ELEMENT]: ["true", "true"],
            [app.GridCells.IS_PLANNING_ELEMENT]: ["true", "true"],
            [app.GridCells.IS_TIMEKEEPING_ELEMENT]: ["true", "true"],
        }
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_2'), 2, CONTROLLING_UNIT_SUB_PARAMETERS)

    })

    it("TC - Create Job record in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(3000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        cy.wait(1000)//required wait to enter value of project
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS, 0);
            _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_JOBS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_JOBS.controllingunitfk], cnt.uuid.JOBS)

        });
        _common.clear_subContainerFilter(cnt.uuid.JOBS)
        _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.maximizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
        cy.wait(1000)//required wait to enable data input fields
        _common.clickOn_activeRowCell(cnt.uuid.JOBS, app.GridCells.CONTROLLING_UNIT_FK)
        cy.wait(1000)//required wait to save data
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.CONTROLLING_UNIT_FK, Cypress.env(`API_CNT_CODE_1`), commonLocators.CommonKeys.GRID)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create Job record in logistic price condition module", function () {

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_PRICE_CONDITION)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONDITIONS, app.FooterTab.CONDITIONS);
            _common.setup_gridLayout(cnt.uuid.CONDITIONS, CONTAINER_COLUMNS_CONDITIONS)
            _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        });
        _common.maximizeContainer(cnt.uuid.CONDITIONS)
        _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        _common.create_newRecord(cnt.uuid.CONDITIONS)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, CONDITION_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, CONDITION_DESC)
        _common.set_cellCheckboxValue(cnt.uuid.CONDITIONS, app.GridCells.IS_HANDLING_CHARGE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONDITIONS)
        _common.select_rowHasValue(cnt.uuid.CONDITIONS, CONDITION_CODE)
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.FooterTab.PLANT_CATALOG_PRICELISTS);
        });
        _common.maximizeContainer(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.clear_subContainerFilter(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, CONTAINER_DATA_RECORD.DATA_RECORD1)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, CONTAINER_DATA_RECORD.DATA_RECORD2)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.minimizeContainer(cnt.uuid.EQUIPMENT_CATALOG_PRICES)

        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_ITEM, app.FooterTab.WORK_OPERATION_TYPE_CONDITIONS);
            _common.maximizeContainer(cnt.uuid.PRICE_CONDITION_ITEM)

            _common.maximizeContainer(cnt.uuid.PRICE_CONDITION_ITEM);
            _common.clear_subContainerFilter(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.minimizeContainer(cnt.uuid.PRICE_CONDITION_ITEM);
            _common.create_newRecord(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, MODAL_PLANT_ALLOCATION.WORK_OPERATION)
        });
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        _common.edit_caretDropdown_fromModal_byClass(app.ModalInputFields.PRICING_GROUP_FK)
        _common.select_ItemFromPopUpList(commonLocators.CommonKeys.LIST, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PLANT_PRICING_GROUP)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PRICE_CONDITION_ITEM)

    })

    it("TC - Assign Price condition in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'));
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS);
            _common.search_inSubContainer(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
            _common.waitForLoaderToDisappear()
            _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        });
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PRICE_CONDITION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONDITION_CODE)
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_JOBS.BUSINESS_PARTNER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create record in dispatching notes", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.create_newRecord(cnt.uuid.DISPATCHING_HEADER)
        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, CONTAINERS_DISPATCHING_HEADER.EFFECTIVE_DATE[0])
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB1_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.JOB_2_FK, Cypress.env('API_PROJECT_NUMBER_2'), commonLocators.CommonKeys.GRID)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE)
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE, "DISPATCH_CODE")
        _common.minimizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue, (cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        cy.REFRESH_SELECTED_ENTITIES()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, app.FooterTab.PERFORMING_JOB_PLANT_LOCATIONS);
        });
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_RECORD, CONTAINER_COLUMNS_DISPATCHING_RECORD)
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.create_newRecord(cnt.uuid.DISPATCHING_RECORD)
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.RECORD_TYPE_FK, commonLocators.CommonKeys.GRID, CONTAINERS_DISPATCHING_HEADER.RECORD_TYPE)
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_RECORD, app.GridCells.ARTICLE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_DISPATCHING_HEADER.ARTICLE_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.DISPATCHING_RECORD, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_DISPATCHING_HEADER.QUANTITY)
        _common.clickOn_activeRowCell(cnt.uuid.DISPATCHING_RECORD, app.GridCells.WORK_OPERATION_TYPE_FK)
        cy.wait(1000)
        cy.wait(1000) //required wait to enable work operations type cell
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
        });
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.TRANSPORT_PLANNING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.PICKING)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.LOADED)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.DELIVERED)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create settlement In settlement module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.SETTLEMENT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.SETTLEMENT)
        });
        //cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.START_SETTLEMENT_BATCH)
        _common.inputField_fromModal(commonLocators.CommonElements.ROW, commonLocators.CommonLabels.DUE_DATE, 0, app.InputFields.INPUT_GROUP_CONTENT).clear({ force: true }).type(_common.getDate(CommonLocators.CommonKeys.INCREMENTED_SMALL, 10), { force: true })
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.wait(3000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        cy.wait(3000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        cy.wait(3000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS, 0)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to search project and select row
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.SETTLEMENT, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.saveCellDataToEnv(cnt.uuid.SETTLEMENT, app.GridCells.SETTLEMENT_NO, "SettlementNo")
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create settlement claims from wizard", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.SETTLEMENT)
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT, app.FooterTab.SETTLEMENTS, 0)
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT, Cypress.env("SettlementNo"))
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT, Cypress.env("SettlementNo"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_ITEMS, app.FooterTab.SETTLEMENT_ITEMS)
        });
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT_ITEMS, Cypress.env("DISPATCH_CODE"))
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_SETTLEMENT_CLAIMS)
        _common.waitForLoaderToDisappear()
        _logesticPage.create_settlementClaim_fromWizard(SETTLEMENT_CLAIM_PARAMETER)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.SETTLEMENT_ITEMS).then(() => {
            _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.SETTLEMENT_CLAIMS, app.FooterTab.SETTLEMENT_CLAIMS, 1)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.SETTLEMENT_CLAIMS)
        _common.clickOn_toolbarButton(cnt.uuid.SETTLEMENT_CLAIMS, btn.ToolBar.ICO_REFRESH)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.SETTLEMENT_CLAIMS, Cypress.env("SettlementNo"))
        _common.select_rowHasValue(cnt.uuid.SETTLEMENT_CLAIMS, Cypress.env("SettlementNo"))
        _validate.verify_isRecordPresent(cnt.uuid.SETTLEMENT_CLAIMS, Cypress.env("SettlementNo"))
        _common.assert_cellData_insideActiveRow(cnt.uuid.SETTLEMENT_CLAIMS, app.GridCells.EXPECTED_QUANTITY, CONTAINERS_DISPATCHING_HEADER.QUANTITY)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_SETTLEMENT_CLAIM_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.ACCEPTED)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_SETTLEMENT_CLAIM_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.DONE)

    })

    it("TC - Create Material Prices in Logistic Price condition Module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_PRICE_CONDITION)
        _common.openTab(app.TabBar.COST_CODES_AND_MATERIALS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_PRICES, app.FooterTab.MATERIAL_PRICES);
            _common.setup_gridLayout(cnt.uuid.MATERIAL_PRICES, CONTAINER_COLUMNS_MATERIAL_PRICE)
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.MATERIAL_PRICES)
        _common.clickOn_activeRowCell(cnt.uuid.MATERIAL_PRICES, app.GridCells.MATERIAL_CATALOG_FK)
        _common.edit_dropdownCellWithInput(cnt.uuid.MATERIAL_PRICES, app.GridCells.MATERIAL_CATALOG_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_MATERIAL_PRICE.CATALOG)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.MATERIAL_PRICES, app.GridCells.MATERIAL_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_MATERIAL_PRICE.MATERIAL)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.MATERIAL_PRICES, app.GridCells.CURRENCY_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_MATERIAL_PRICE.CURRENCY)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.MATERIAL_PRICES, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_MATERIAL_PRICE.PRICE)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.MATERIAL_PRICES, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, CONTAINER_MATERIAL_PRICE.VALID_FROM)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.MATERIAL_PRICES, CONTAINER_MATERIAL_PRICE.MATERIAL)
    })

    it("TC - Verify Recalculate Dispatch Notes", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env("DISPATCH_CODE"))
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_RECORD, CONTAINER_COLUMNS_DISPATCHING_RECORD)
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_RECORD)
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.RECALCULATE_DISPATCH_NOTE)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        cy.REFRESH_CONTAINER()
        _common.select_rowInContainer(cnt.uuid.DISPATCHING_RECORD)
        _common.assert_cellData_insideActiveRow(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_SMALL, CONTAINER_MATERIAL_PRICE.PRICE)
        _common.assert_cellData_insideActiveRow(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_OC, CONTAINER_MATERIAL_PRICE.PRICE)
        _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.DISPATCHING_RECORD, CONTAINERS_DISPATCHING_HEADER.QUANTITY, CONTAINER_MATERIAL_PRICE.PRICE, app.GridCells.PRICE_TOTAL)

    })

});


