/*
 * Copyright(c) RIB Software GmbH
 */

import { IPaymentScheduleBaseEntity } from '@libs/basics/interfaces';
import { IPrcHeaderDataService } from './prc-header-data-service.interface';
import { CompleteIdentification, IEntityIdentification } from '@libs/platform/common';
import { IBasicsSharedPaymentScheduleDataServiceInterface } from '@libs/basics/shared';
import { IEntityModification } from '@libs/platform/data-access';
import { IReadonlyRootService } from '@libs/procurement/shared';

/**
 * Payment schedule basics data service interface
 */
export interface IPrcCommonPaymentScheduleDataServiceInterface<
	T extends IPaymentScheduleBaseEntity,
	PT extends IEntityIdentification,
	PU extends CompleteIdentification<PT>,
	RT extends IEntityIdentification = PT,
	RU extends CompleteIdentification<RT> = PU>
	extends IBasicsSharedPaymentScheduleDataServiceInterface<T, PT, PU> {

	/**
	 * module name
	 * Now use in Procurement.Package, Procurement.Contract, Procurement.Requisition
	 */
	moduleName: string

	/**
	 * Parent data service
	 */
	parentService: IPrcHeaderDataService<PT, PU> & IEntityModification<PT>

	/**
	 * Root data service
	 * RootService and ParentService are not the same service in Procurement.Package module
	 */
	rootService: IPrcHeaderDataService<RT, RU> & IReadonlyRootService<RT, RU> & IEntityModification<RT>

	/**
	 * Current total source
	 */
	totalSource: {
		SourceNetOc: number,
		SourceGrossOc: number,
		SourceId?: number | null
	}
}