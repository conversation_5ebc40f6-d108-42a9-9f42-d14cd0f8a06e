﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{99C3553F-C9BC-41FA-BFE8-A952408810DB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RIB.Visual.Logistic.Plantcostalloc.BusinessComponents</RootNamespace>
    <AssemblyName>RIB.Visual.Logistic.Plantcostalloc.BusinessComponents</AssemblyName>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <TargetFramework>net8.0</TargetFramework>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>bin\Debug\RIB.Visual.Logistic.Plantcostalloc.BusinessComponents.xml</DocumentationFile>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>none</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Configurations\BillingSheetStatusWorkflowConfigurationProvider.cs" />
    <Compile Include="DBFunctions.cs" />
    <Compile Include="DBFunctions\BillingSheetFunctionEntity.cs" />
    <Compile Include="Entities\BillingSheetCompleteEntity.cs" />
    <Compile Include="Entities\BillingSheetEntity.cs" />
    <Compile Include="Entities\BillingSheetEntity.Generated.cs">
      <DependentUpon>BillingSheetEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\CreateBillingSheetsData.cs" />
    <Compile Include="Entities\DdTempIdsEntity.cs" />
    <Compile Include="Entities\DdTempIdsEntity.Generated.cs">
      <DependentUpon>DdTempIdsEntity.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\PlantCostAllocationProjectUpdateEntity.cs" />
    <Compile Include="Entities\PriceOrgInfoEntity.cs" />
    <Compile Include="Entities\PriceOrgInfoEntity.Generated.cs" />
    <Compile Include="Entities\PriceOriginInfoVEntity.cs" />
    <Compile Include="Entities\PriceOriginInfoVEntity.Generated.cs" />
    <Compile Include="EntityModel\EntityModel.ModelBuilder.cs" />
    <Compile Include="EntityModel\ModelBuilder.cs" />
    <Compile Include="Logic\BillingSheetLogic.cs" />
    <Compile Include="Logic\CreateReservationBillingSheetsWizardLogic.cs" />
    <Compile Include="Logic\PlantCostAllocationProjectLogic.cs" />
    <Compile Include="Logic\PriceOrgInfoLogic.cs" />
    <Compile Include="Logic\PricePerUnitLogic.cs" />
    <Compile Include="Logic\SchedulerTask\BillingSheetSchedulerTask.cs" />
    <Compile Include="Logic\SchedulerTask\CreateJobForBillingSheetsLogic.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="../../../AssemblyVersion.cs" Link="Properties/AssemblyVersion.cs" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Platform.Core, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessEnvironment, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessEnvironment.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.BusinessComponents, Version=5.0.13.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Common, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.BusinessComponents, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.BusinessComponents.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Common, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core, Version=2.3.136.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.Functions">
      <HintPath>$(RIBvisualBinPool)\EntityFramework.Functions.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Server.Common">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Server.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.SqlClient">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Data.SqlClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
	<Reference Include="RIB.Visual.Services.Scheduler.BusinessComponents, Version=25.2.404.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
		<SpecificVersion>false</SpecificVersion>
		<HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Scheduler.BusinessComponents.dll</HintPath>
	</Reference>
	<Reference Include="RIB.Visual.Services.Scheduler.Core, Version=25.2.404.0, Culture=neutral, PublicKeyToken=fe361e97ed5eb2d4, processorArchitecture=MSIL">
		<SpecificVersion>false</SpecificVersion>
		<HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Scheduler.Core.dll</HintPath>
	</Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Plantcostalloc.Common\RIB.Visual.Logistic.Plantcostalloc.Common.csproj">
      <Project>{9E10954B-D999-4A13-94D5-7FF6A8592252}</Project>
      <Name>RIB.Visual.Logistic.Plantcostalloc.Common</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Plantcostalloc.Core\RIB.Visual.Logistic.Plantcostalloc.Core.csproj">
      <Project>{6C9FCA6E-6726-4563-8384-CCA9DE6E274F}</Project>
      <Name>RIB.Visual.Logistic.Plantcostalloc.Core</Name>
      <Private>False</Private>
    </ProjectReference>
    <ProjectReference Include="..\Plantcostalloc.Localization\RIB.Visual.Logistic.Plantcostalloc.Localization.csproj">
      <Project>{7E8CACD9-DE59-4426-8380-2DDB4D04686D}</Project>
      <Name>RIB.Visual.Logistic.Plantcostalloc.Localization</Name>
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="EntityModel\DataTransferObject.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\DbContext.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <DevartEntityDeploy Include="EntityModel\EntityModel.edml">
      <Generator>DevartEfGenerator</Generator>
      <LastGenOutput>EntityModel.info</LastGenOutput>
      <SubType>Designer</SubType>
    </DevartEntityDeploy>
    <None Include="EntityModel\EntityModel.edps">
      <DependentUpon>EntityModel.edml</DependentUpon>
      <SubType>Designer</SubType>
    </None>
    <None Include="EntityModel\EntityModel.info">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\EntityModel.MainDiagram.view">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\Validation.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\GroupingAttributes.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="EntityModel\LookupFilterKeysAttributes.T4">
      <DependentUpon>EntityModel.edml</DependentUpon>
    </None>
    <None Include="RIBvisual.snk">
    </None>
  </ItemGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
</Project>