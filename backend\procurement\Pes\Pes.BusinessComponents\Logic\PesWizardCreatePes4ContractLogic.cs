using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Core.Core.Basics.ProcurementConfiguration;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Procurement.Contract.BusinessComponents;

namespace RIB.Visual.Procurement.Pes.BusinessComponents
{
	/// <summary>
	/// Logic for PES creation wizard from procurement contracts
	/// </summary>
	public class PesWizardCreatePes4ContractLogic : LogicBase
	{
		private readonly PesHeaderLogic pesHeaderLogic = new();
		private readonly PesItemLogic pesItemLogic = new();
		private readonly PesStatusLogic statusLogic = new();
		private readonly ConHeaderLogic conHeaderLogic = new();

		/// <summary>
		/// Gets the database model for Entity Framework
		/// </summary>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel() => ModelBuilder.DbModel;

		/// <summary>
		/// Prepares PES creation for valid contracts
		/// </summary>
		/// <param name="validContracts">List of valid contracts for PES creation</param>
		/// <returns>List of processing results for PES contracts</returns>
		public List<PesCreationParam> PreparePesCreation(List<ConHeaderEntity> validContracts)
		{
			var results = ProcessContractPESDetails(validContracts);

			foreach (var result in results)
			{
				var dialogType = DetermineDialogType(result);
				result.DialogType = (int)dialogType;

				// If dialog type is ShowNonContractedItems but no non-contracted items exist,
				// change to CreateCompletely to skip showing dialog
				if (dialogType == CreatePes2ShowDialogType.ShowNonContractedItems &&
					  !GetExistNonContractedItems(result.PesHeaders).Any())
				{
					result.DialogType = (int)CreatePes2ShowDialogType.CreateCompletely;
				}
			}

			// Group by PesContract.Id and take first from each group to avoid duplicates
			return results
				  .GroupBy(result => result.PesContract.Id)
				  .Select(group => group.First())
				  .ToList();
		}

		/// <summary>
		///  Creates PES headers for valid contracts
		/// </summary>
		/// <param name="creationParams"></param>
		/// <returns></returns>
		public IEnumerable<PesHeaderEntity> CreatePes(IEnumerable<PesCreationParam> creationParams)
		{
			// List to store created PES header entities
			var createdEntities = new List<PesHeaderEntity>();
			foreach (var param in creationParams)
			{
				PesHeaderEntity entity = null;
				// Determine whether to include non-contracted items
				var include = param.IsIncluded ?? false;
				// Delegate for appending BOQs
				var appendBoqsFunc = new Action<int, int, int, bool>(pesHeaderLogic.AppendBoqs);
				// Delegate for appending PES items
				var appendItemsFunc = new Action<PesHeaderEntity, ConHeaderEntity, List<long>, bool>(pesHeaderLogic.AppendPesItems);

				// If the case type is ShowOptionDialog and options are provided
				if (param.DialogType == (int)CreatePes2ShowDialogType.ShowOptionDialog && param.Options != null)
				{
					switch (param.Options.Value)
					{
						case PesCreationMode.New:
							// If the source is Variance, use the extended creation method
							if (param.Options.Source == PesCreationSource.Variance)
							{
								entity = pesHeaderLogic.CreateOrUpdatePesByContract(param.ContractId, include, true, null, null, appendBoqsFunc, appendItemsFunc);
							}
							else
							{
								// Otherwise, use the basic creation method
								entity = pesHeaderLogic.CreateOrUpdatePesByContract(param.ContractId, include, true, null);
							}
							break;

						case PesCreationMode.Update:
							// For update mode, use the extended update method
							{
								entity = pesHeaderLogic.CreateOrUpdatePesByContract(param.ContractId, include, true, null, param.Options.SelectPesHeaderId, appendBoqsFunc, appendItemsFunc);
							}
							break;
					}
				}
				else
				{
					// Default creation if no options are provided
					entity = pesHeaderLogic.CreateOrUpdatePesByContract(param.ContractId, include, true, null);
				}

				// Add the created entity to the result list if not null
				if (entity != null)
				{
					createdEntities.Add(entity);
				}
			}
			// Return all created PES header entities
			return createdEntities;
		}

		/// <summary>
		/// Determines the appropriate dialog type based on the contract processing result
		/// </summary>
		/// <param name="result">Processing result for a contract</param>
		/// <returns>Dialog type to be shown for this contract</returns>
		private CreatePes2ShowDialogType DetermineDialogType(PesCreationParam result)
		{
			switch (result.CaseType)
			{
				case (int)ContractCaseType.BaseOrCallOff:
					// For base/calloff contracts: If PES headers exist but no BOQ items,
					// show confirmation dialog, otherwise show non-contracted items
					return result.PesHeaders.Any() && !result.BoqPrcItems.Any()
						  ? CreatePes2ShowDialogType.ShowCoverConfirmDialog
						  : CreatePes2ShowDialogType.ShowNonContractedItems;

				case (int)ContractCaseType.BaseAndChangeOrder:
					if (result.PesHeaders.Any())
					{
						// For contracts with change orders: If BOQ items exist,
						// show options dialog, otherwise show confirmation dialog
						return result.BoqPrcItems.Any()
							  ? CreatePes2ShowDialogType.ShowOptionDialog
							  : CreatePes2ShowDialogType.ShowCoverConfirmDialog;
					}
					break;
			}

			// Default to complete creation without dialog
			return CreatePes2ShowDialogType.CreateCompletely;
		}

		/// <summary>
		/// Processes contract details to prepare for PES creation
		/// </summary>
		/// <param name="validContracts">List of valid contracts</param>
		/// <returns>List of processed contract results</returns>
		private List<PesCreationParam> ProcessContractPESDetails(List<ConHeaderEntity> validContracts)
		{
			// Get contract IDs and related contract IDs
			var contractIds = validContracts.CollectIds(e => e.Id);
			var relatedContractIds = validContracts.CollectIds(e => e.ConHeaderFk).Except(contractIds);

			// Build mapping of contract IDs to contract entities
			var contractMap = conHeaderLogic.GetItemsByKey(relatedContractIds)
				  .Concat(validContracts)
				  .ToDictionary(e => e.Id);

			// Get change orders related to these contracts
			var relatedChangeOrders = conHeaderLogic.GetByFilter(e =>
				  e.ConHeaderFk != null &&
				  contractIds.Contains(e.ConHeaderFk.Value) &&
				  e.ProjectChangeFk != null);

			// Get active (non-readonly) PES status IDs
			var activeStatusIds = statusLogic.GetSearchList(e => !e.IsReadonly)
				  .CollectIds(e => e.Id);

			// Get contract-related PES headers and group by contract ID
			var allRelevantIds = contractIds.Concat(relatedContractIds);
			var pesHeadersByContract = pesHeaderLogic.GetSearchList(e =>
						 e.ConHeaderFk.HasValue &&
						 allRelevantIds.Contains(e.ConHeaderFk.Value) &&
						 activeStatusIds.Contains(e.PesStatusFk))
				  .GroupBy(e => e.ConHeaderFk)
				  .ToDictionary(g => g.Key, g => g.ToList());

			var results = new List<PesCreationParam>();

			foreach (var headerId in contractIds)
			{
				var contract = contractMap[headerId];

				// Determine contract type: base/calloff or contract with change orders
				bool isBaseOrCallOff = (contract.ConHeaderFk == null &&
					  !relatedChangeOrders.Any(co => co.ConHeaderFk == contract.Id)) ||
					  conHeaderLogic.IsCallOff(contract);

				bool isConsolidateChange = true;
				var baseContract = contract.ConHeaderFk.HasValue ? contractMap[contract.ConHeaderFk.Value] : null;

				// Determine which contract will be used for PES creation
				var pesContract = isBaseOrCallOff
					  ? contract
					  : GetWillBePesContract(contract, baseContract, contractMap, out isConsolidateChange);

				// Get PES headers and BOQ items for the contract
				var pesHeaders = pesHeadersByContract.TryGetValue(pesContract.Id, out var headers)
					  ? headers
					  : new List<PesHeaderEntity>();
				var boqPrcItems = pesHeaderLogic.GetBoqPrcItemEntities(pesContract.Id);

				results.Add(new PesCreationParam
				{
					ContractId = headerId,
					ContractCode = contract.Code,
					CaseType = isBaseOrCallOff
							 ? (int)ContractCaseType.BaseOrCallOff
							 : (int)ContractCaseType.BaseAndChangeOrder,
					PesContract = pesContract,
					PesHeaders = pesHeaders,
					BoqPrcItems = boqPrcItems,
					IsConsolidateChange = isConsolidateChange,
				});
			}

			return results;
		}

		/// <summary>
		/// Determines which contract entity should be used for PES creation
		/// </summary>
		/// <param name="contract">Current contract entity</param>
		/// <param name="baseContract">Base contract entity (if exists)</param>
		/// <param name="contractMap">Dictionary mapping contract ID to contract entity</param>
		/// <param name="isConsolidateChange">Output parameter indicating if changes should be consolidated</param>
		/// <returns>Contract entity to be used for PES creation</returns>
		private ConHeaderEntity GetWillBePesContract(
			  ConHeaderEntity contract,
			  ConHeaderEntity baseContract,
			  Dictionary<int, ConHeaderEntity> contractMap,
			  out bool isConsolidateChange)
		{
			// Get configuration settings
			var configLogic = Injector.Get<IPrcConfigHeaderLogic>();
			var configId = contract.PrcHeaderEntity.ConfigurationFk;
			var configHeader = configLogic.MapConfigurationsToHeaders(new[] { configId })
				  .GetValueOrDefault(configId);

			// Determine whether to consolidate changes
			isConsolidateChange = configHeader?.IsConsolidateChange ?? true;

			// Select appropriate contract based on consolidation setting
			var pesContract = !isConsolidateChange || baseContract == null ? contract : baseContract;

			// If PES contract has no structure FK, try to get one from other contracts
			if (!pesContract.PrcHeaderEntity.StructureFk.HasValue)
			{
				pesContract.PrcHeaderEntity.StructureFk = contractMap.Values
					  .FirstOrDefault(e => e.PrcHeaderEntity.StructureFk.HasValue)
					  ?.PrcHeaderEntity.StructureFk;
			}

			return pesContract;
		}

		/// <summary>
		/// Gets existing non-contracted items for the given PES headers
		/// </summary>
		/// <param name="pesHeaders">List of PES header entities</param>
		/// <returns>List of PES item entities that are not linked to contract items</returns>
		private List<PesItemEntity> GetExistNonContractedItems(List<PesHeaderEntity> pesHeaders)
		{
			if (!pesHeaders.Any())
			{
				return new List<PesItemEntity>();
			}

			var pesHeaderIds = pesHeaders.CollectIds(e => e.Id);
			return pesItemLogic.GetSearchList(e => pesHeaderIds.Contains(e.PesHeaderFk))
				  .Where(e => !e.PrcItemFk.HasValue)
				  .ToList();
		}
	}
}