/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectionToken } from '@libs/platform/common';
import { TypedConcreteFieldOverload } from '@libs/ui/common';

/**
 * Options for timekeeping employee certified document lookup.
 */
export interface ITimekeepingEmployeeCertifiedDocumentLookupOptions {
	// Add more options here if needed later
}

/**
 * Provides lookup overloads for certified employee documents.
 */
export interface ITimekeepingEmployeeCertifiedDocumentLookupProvider {
	/**
	 * Generates a lookup field overload definition to pick a certified employee document.
	 *
	 * @param options The options to apply to the lookup.
	 *
	 * @returns The lookup field overload.
	 */
	generateEmployeeCertifiedDocumentLookup<T extends object>(
		options?: ITimekeepingEmployeeCertifiedDocumentLookupOptions
	): TypedConcreteFieldOverload<T>;
}

/**
 * A lazy injection to retrieve an object that generates certified document lookup field overloads.
 */
export const EMPLOYEE_CERTIFIED_DOCUMENT_LOOKUP_PROVIDER_TOKEN =
	new LazyInjectionToken<ITimekeepingEmployeeCertifiedDocumentLookupProvider>(
		'timekeeping.certificate.EmployeeCertifiedDocumentLookupProvider'
	);