/*
 * Copyright(c) RIB Software GmbH
 */

import { LazyInjectableInfo, ILazyInjectionContext, PlatformModuleManagerService } from '@libs/platform/common';
import { CHECKLIST_LOOKUP_PROVIDER_TOKEN, CHECKLIST_GROUP_LOOKUP_PROVIDER_TOKEN, CHECKLIST_TEMPLATE_LOOKUP_PROVIDER_TOKEN } from '@libs/hsqe/interfaces';


export const LAZY_INJECTABLES: LazyInjectableInfo[] =[
LazyInjectableInfo.create('hsqe.checklist.HsqeChecklistLookupProviderService', CHECKLIST_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/hsqe/checklist');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.HsqeChecklistLookupProviderService) : null;
		
	}),

	LazyInjectableInfo.create('hsqe.checklisttemplate.ChecklistGroupLookupProvider', CHECKLIST_GROUP_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/hsqe/checklisttemplate');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.ChecklistGroupLookupProvider) : null;
		
	}),

	LazyInjectableInfo.create('hsqe.checklisttemplate.HsqeChecklistTemplateLookupProviderService', CHECKLIST_TEMPLATE_LOOKUP_PROVIDER_TOKEN, async (context: ILazyInjectionContext) =>{
		const importedModule = await import('@libs/hsqe/checklisttemplate');
		const platformModuleManagerService = context.injector.get(PlatformModuleManagerService);
		await platformModuleManagerService.initializeModule(importedModule);
		return context.doInstantiate ? context.injector.get(importedModule.HsqeChecklistTemplateLookupProviderService) : null;
		
	}),
];
 