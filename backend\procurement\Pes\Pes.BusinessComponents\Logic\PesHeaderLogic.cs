/*
 * $Id: PesHeaderLogic.cs 634403 2021-04-28 07:53:22Z alm $
 * Copyright (c) RIB Software SE
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using System.Transactions;
using RIB.Visual.Basics.BillingSchema.BusinessComponents;
using RIB.Visual.Basics.Characteristic.Common;
using RIB.Visual.Basics.Clerk.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Basics.MasterData.BusinessComponents;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Basics.PriceCondition.BusinessComponents.Logic;
using RIB.Visual.Basics.PriceCondition.Common.Enum;
using RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents;
using RIB.Visual.Basics.ProcurementStructure.BusinessComponents;
using RIB.Visual.Basics.TaxCode.BusinessComponents;
using RIB.Visual.Boq.Main.BusinessComponents;
using RIB.Visual.BusinessPartner.Main.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Cloud.Common.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Procurement.Common.BusinessComponents;
using RIB.Visual.Procurement.Common.BusinessComponents.Logic;
using RIB.Visual.Procurement.Common.Core;
using RIB.Visual.Procurement.Contract.BusinessComponents;
using RIB.Visual.Procurement.Package.BusinessComponents;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using NLS = RIB.Visual.Procurement.Pes.Localization.Properties.Resources;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;

namespace RIB.Visual.Procurement.Pes.BusinessComponents
{
	/// <summary>
	///
	/// </summary>
	[Export(typeof(IEntityInfoProvider))]
	public class LastObjectEntityInfo : LastObjectEntityInfoBase
	{
		/// <summary>
		///
		/// </summary>
		public LastObjectEntityInfo()
			: base("procurement.pes") { }

		/// <summary>
		/// Get Entity Info Data
		/// </summary>
		/// <param name="entityInfos"></param>
		/// <returns></returns>
		public override IEnumerable<IEntityInfoData> GetEntityInfoData(IEnumerable<IEntityInfoData> entityInfos)
		{
			var result = GetEntityInfo<PesHeaderEntity>(
					ModelBuilder.DbModel,
					entityInfos,
					(query, ids) => query.Where(entity => ids.Contains(entity.Id)),
					entity => entity.Id,
					entity => String.Join(",", new String[] { entity.Code, entity.Description })
					);
			return result;
		}
	}

	/// <summary>
	/// Pes Header Logic
	/// </summary>
	[Export("pes", typeof(IChangeStatus))]
	[Export(typeof(IInsertTransactionByPesHeaderStatus))]
	[Export(typeof(IPesHeaderProvider))]
	[EntityStatus("PES_STATUS", "procurement.pes", "PES Status")]
	[Export("procurement.pes", typeof(IPrcRecalculateTotalsLogic))]
	public class PesHeaderLogic : ProcurementCommonLogicBaseNew<PesHeaderEntity, DdTempIdsEntity>, IEntityFacade, IChangeStatus, IPesHeaderProvider, ICreateEntityFacade, IInsertTransactionByPesHeaderStatus,
		IRecalculateProcurementFacade, IPrcRecalculateTotalsLogic
	{
		/// <summary>
		///
		/// </summary>
		public PesHeaderLogic()
		{
			this.SetRelationInfoIdentifier(RelationInfoIdentifier.PesHeader);
			this.PermissionGUID = "ebe726dbf2c5448f90b417bf2a30b4eb";
			SetTempMatchingFunc<DdTempIdsEntity>((e, tmp) => (e.Id == tmp.Id));
			this.FilterByPermission = (dbContext, query) =>
			{
				ParseAccessFunctionParams(FilterRequest);
				query = JoinAccessFunctionWithFilter(query, new DbFunctions(dbContext));
				return query;
			};
			OrderByExpressions = new[]
			{
				OrderTerm.Create(e => e.Id, true)
			};
			this.ParentIdFnForNonTree = e => e.PesHeaderFk;
			this.ParentPropertyName = "PesHeaderFk";
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesId"></param>
		/// <param name="pesStatusId"></param>
		/// <param name="pesUpdateItemList"></param>
		/// <param name="DateDelivered"></param>
		public void UpdatePesStatus(int pesId, int pesStatusId, List<PesUpdatedItemEntity> pesUpdateItemList, DateTime DateDelivered)
		{
			//update pes status
			List<PesItemEntity> pesItemList = null;
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var pes = dbcontext.Entities<PesHeaderEntity>().Where(e => e.Id == pesId).FirstOrDefault();
				pesItemList = dbcontext.Entities<PesItemEntity>().Where(e => e.PesHeaderFk == pesId).ToList();
				var pesItemsToSave = new List<PesItemEntity>();

				//update pes item qty.
				foreach (var pesItem in pesItemList)
				{
					decimal quantityConfirmed = pesItem.QuantityAskedFor;

					//if api provide the new quantity confirmed value, use that value; else use the Qty. asked for value.
					if (pesUpdateItemList != null)
					{
						PesUpdatedItemEntity entity = pesUpdateItemList.FirstOrDefault(e => e.PesItemId == pesItem.Id);
						if (entity != null)
						{
							quantityConfirmed = entity.QuantityConfirmed;
						}
					}

					//only update quantity confirmed if it's 0
					if (pesItem.Quantity == 0m)
					{
						pesItem.Quantity = quantityConfirmed;
						pesItem.QuantityDelivered += quantityConfirmed;
						pesItemsToSave.Add(pesItem);
					}
				}

				if (pesItemsToSave.Any())
				{
					dbcontext.Save(pesItemsToSave);
				}

				pes.PesStatusFk = pesStatusId;

				//update Date_Delivered
				if (DateDelivered != new DateTime())
				{
					pes.DateDelivered = DateDelivered;
				}

				dbcontext.Save(pes);
			}

			PesCompleteNewEntity pesComplete = new PesCompleteNewEntity();
			pesComplete.MainItemId = pesItemList[0].PesHeaderFk;
			var arr = new List<PesItemCompleteEntity>();
			var i = 0;
			foreach (var pesItem in pesItemList)
			{
				arr[i].MainItemId = pesItem.Id;
				arr[i].Item = pesItem;
				i++;
			}
			pesComplete.PesItemToSave = arr;
			this.UpdateNew(pesComplete);

			this.CalculateTotalAndVat(pesId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="filterValue"></param>
		/// <returns></returns>
		public List<PesHeaderEntity> GetPesHeaderListForYTWO(string filterValue)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var currentContext = BusinessApplication.BusinessEnvironment.CurrentContext;

				var companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				var company = new BasicsCompanyLogic().GetSearchList(e => e.Id == companyId).FirstOrDefault();
				if (string.IsNullOrEmpty(company.DunsNo))
				{
					throw new Exception($"{company.Code}: {NLS.ERR_DunsNoIsEmpty}");
				}
				var businessPartner = new RIB.Visual.BusinessPartner.Main.BusinessComponents.BusinessPartnerLogic().GetFilteredList(e => e.DunsNo == company.DunsNo).FirstOrDefault();//todo livia modified this getMethod

				IQueryable<PesHeaderEntity> query = dbContext.Entities<PesHeaderEntity>()
					.Where(e => e.BusinessPartnerFk == businessPartner.Id);

				if (!string.IsNullOrEmpty(filterValue))
				{
					query = query.Where(filterValue);
				}

				return query.ToList();
			}
		}

		/// <summary>
		/// Create Clone PES Header
		/// </summary>
		/// <param name="lastHeaderId"></param>
		/// <param name="currencyFk"></param>
		/// <param name="configurationId"></param>
		/// <param name="conHeaderId"></param>
		/// <returns></returns>
		public PesHeaderEntity CreateCloneHeader(int lastHeaderId, int? currencyFk = null, int? configurationId = null, int? conHeaderId = null)
		{
			PesHeaderEntity header = new PesHeaderEntity();
			var lastHeader = GetByFilter(e => e.Id == lastHeaderId).FirstOrDefault();
			if (lastHeader != null)
			{
				header.Code = "";
				header.ClerkPrcFk = lastHeader.ClerkPrcFk;
				header.ClerkReqFk = lastHeader.ClerkReqFk;
				header.PrcStructureFk = lastHeader.PrcStructureFk;
				header.BusinessPartnerFk = lastHeader.BusinessPartnerFk;
				header.SubsidiaryFk = lastHeader.SubsidiaryFk;
				header.SupplierFk = lastHeader.SupplierFk;
				header.ProjectFk = lastHeader.ProjectFk;
				header.BpdVatGroupFk = lastHeader.BpdVatGroupFk;
				header.SalesTaxMethodFk = lastHeader.SalesTaxMethodFk;

				var conStatusIds = new ConStatusLogic().GetSearchList(e => e.IsOrdered && !e.Iscanceled && !e.IsInvoiced && !e.IsVirtual).Select(e => e.Id);
				var conHeaders = new ConHeaderLogic().GetSearchList(e => e.Id == lastHeader.ConHeaderFk.Value && conStatusIds.Contains(e.ConStatusFk), lastHeader.ConHeaderFk.HasValue && conStatusIds.Any());
				if (conHeaders != null && conHeaders.Any())
				{
					header.ConHeaderFk = lastHeader.ConHeaderFk;
					header.SalesTaxMethodFk = conHeaders.FirstOrDefault().SalesTaxMethodFk;
				}

				InitUserDefind(header, conHeaderId, configurationId);

				header.PackageFk = lastHeader.PackageFk;
				header.ControllingUnitFk = lastHeader.ControllingUnitFk;

				var configId = lastHeader.PrcConfigurationFk;
				header.PrcConfigurationFk = new PrcConfigurationLookUpVLogic().GetDefaultIdByRubric(RubricConstant.PerformanceEntrySheet, configId, NLS.ERR_CopyFail).Value;

				header.CurrencyFk = currencyFk.HasValue ? currencyFk.Value : lastHeader.CurrencyFk;
				header.ExchangeRate = lastHeader.ExchangeRate;
				header.ExternalCode = lastHeader.ExternalCode;

				var loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				var company = new BasicsCompanyLogic().GetItemByKey(loginCompanyId);
				if (company.CurrencyFk == header.CurrencyFk)
				{
					header.ExchangeRate = 1m;
				}
				else
				{
					var defaultRate = new ProcurementCommonExchangeRateLogic().GetExchangeRateByConversionFk(company.Id, header.CurrencyFk, header.ProjectFk);
					header.ExchangeRate = (defaultRate != 0 && defaultRate != null) ? defaultRate.Value : header.ExchangeRate;
				}
			}
			else
			{
				//var clerk = new BasicsClerkLogic().GetLoginClerk();
				//if (clerk != null)
				//{
				//	header.ClerkPrcFk = clerk.Id;
				//}
				//header.BusinessPartnerFk = -1;
				header = this.CreateBlankHeader(null, configurationId);
				if (currencyFk.HasValue)
				{
					header.CurrencyFk = currencyFk.Value;

					var loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
					var company = new BasicsCompanyLogic().GetItemByKey(loginCompanyId);
					if (company.CurrencyFk == header.CurrencyFk)
					{
						header.ExchangeRate = 1m;
					}
					else
					{
						var defaultRate = new ProcurementCommonExchangeRateLogic().GetExchangeRateByConversionFk(company.Id, header.CurrencyFk, header.ProjectFk);
						header.ExchangeRate = (defaultRate != 0 && defaultRate != null) ? defaultRate.Value : header.ExchangeRate;
					}
				}
				InitUserDefind(header, conHeaderId, configurationId);
				return header;
			}
			if (!conHeaderId.HasValue)
			{
				header.BasLanguageFk = lastHeader.BasLanguageFk;
			}

			header.DocumentDate = lastHeader != null && lastHeader.DocumentDate != null ? lastHeader.DocumentDate : DateTime.UtcNow;
			header.DateDelivered = header.DocumentDate.Value;

			SetCommonProperties(ref header);

			header.BillingSchemaFk = lastHeader.BillingSchemaFk;

			// RubricCategory
			header.RubricCategoryFk = CommonBillingSchemaLogic.GetRubricCategoryByConfiguration(header.PrcConfigurationFk);
			header.ConfigHeaderIsConsolidateChange = IsConsolidateChange(header.PrcConfigurationFk, null);
			return header;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="header"></param>
		/// <param name="conHeaderId"></param>
		/// <param name="configurationId"></param>
		/// <returns></returns>
		private void InitUserDefind(PesHeaderEntity header, int? conHeaderId, int? configurationId)
		{
			if (conHeaderId.HasValue)
			{
				var conHeaderEntity = new ConHeaderLogic().GetSearchList(e => e.Id == conHeaderId.Value).FirstOrDefault();
				if (conHeaderEntity != null)
				{
					if (configurationId.HasValue)
					{
						PrcConfigurationEntity configurationEntity = new PrcConfigurationLogic().GetSearchList(e => e.Id == configurationId.Value).FirstOrDefault();
						if (configurationEntity != null)
						{
							PrcConfigHeaderEntity configHeaderEntity = new PrcConfigHeaderLogic().GetItemByKey(configurationEntity.PrcConfigHeaderFk);
							if (configHeaderEntity != null)
							{
								if (configHeaderEntity.IsInheritUserDefined)
								{
									header.UserDefined1 = conHeaderEntity.Userdefined1;
									header.UserDefined2 = conHeaderEntity.Userdefined2;
									header.UserDefined3 = conHeaderEntity.Userdefined3;
									header.UserDefined4 = conHeaderEntity.Userdefined4;
									header.UserDefined5 = conHeaderEntity.Userdefined5;
								}
							}
						}
					}
					header.BasLanguageFk = conHeaderEntity.BasLanguageFk;
				}
			}
		}

		/// <summary>
		///  it is to be used to create pesHeader From a basic pesHeader.
		/// </summary>
		/// <param name="basicPHeader"></param>
		/// <param name="basicPesHeaderFk"></param>
		/// <param name="save"></param>
		/// <returns></returns>
		public PesHeaderEntity CloneBaiscHeader(PesHeaderEntity basicPHeader, int? basicPesHeaderFk = null, bool save = true)
		{
			if (basicPHeader == null && basicPesHeaderFk.HasValue)
			{
				basicPHeader = GetItemByKey(basicPesHeaderFk.Value);
			}
			if (basicPHeader == null)
			{
				return null;
			}
			basicPesHeaderFk = basicPHeader.Id;

			var pesHeader = basicPHeader.Clone() as PesHeaderEntity;
			var statusLogic = new PesStatusLogic();
			var newId = this.SequenceManager.GetNext("PES_HEADER");
			pesHeader.InitVersionInfo();
			pesHeader.Id = newId;
			pesHeader.PesHeaderFk = basicPesHeaderFk;
			pesHeader.PesValue = 0;
			pesHeader.PesValueOc = 0;
			pesHeader.PesVat = 0;
			pesHeader.PesVatOc = 0;
			pesHeader.PesShipmentinfoFk = null;

			var status = statusLogic.GetDefault();
			if (status != null)
			{
				pesHeader.PesStatusFk = status.Id;
			}
			pesHeader.Code = GenerateCode(pesHeader);

			if (save)
			{
				//save and update search pattern.
				pesHeader = Save(pesHeader);
				UpdateSearchPattern(pesHeader.Id);
			}

			return pesHeader;
		}

		/// <summary>
		/// Create Blank PES Header
		/// </summary>
		/// <returns></returns>
		public PesHeaderEntity CreateBlankHeader(string companyCode = null, int? prcConfigurationId = 0, int? projectId = null)
		{
			int loginCompanyId;
			var vatGroupLogic = Injector.Get<IVatGroupLogic>();
			if (companyCode != null)
			{
				loginCompanyId = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
			}
			else
			{
				loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			}
			var company = new BasicsCompanyLogic().GetItemByKey(loginCompanyId);
			//PrcConfigurationFk
			int? doGetDefaultAction()
			{
				var prcConfiguration = prcConfigurationId == null ? null : new PrcConfigurationLookUpVLogic().GetItemByKey(prcConfigurationId);
				if (prcConfiguration == null)
				{
					prcConfiguration = new PrcConfigurationLookUpVLogic().GetDefaultByRubric(RubricConstant.PerformanceEntrySheet);
				}
				return prcConfiguration.Id;
			}

			prcConfigurationId = new PrcConfigurationLookUpVLogic().GetDefaultIdByRubric(RubricConstant.PerformanceEntrySheet, prcConfigurationId, NLS.ERR_ConfigInfo, null, true, doGetDefaultAction);

			PesHeaderEntity header = new PesHeaderEntity
			{
				Code = "",//Code
				BusinessPartnerFk = -1,
				PrcConfigurationFk = prcConfigurationId.Value,
				DocumentDate = DateTime.UtcNow,
				DateDelivered = DateTime.UtcNow,
				DateEffective = DateTime.UtcNow
			};

			if (Context.Portal.HasValue && Context.Portal.Value && Context.BusinessPartnerId.HasValue)
			{
				header.BusinessPartnerFk = Context.BusinessPartnerId.Value;
				var _header = this.BusinessPartnerFkValidate(header, header.BusinessPartnerFk);
				header.SubsidiaryFk = _header.SubsidiaryFk;
				header.SupplierFk = _header.SupplierFk;
			}

			if (company != null)
			{
				header.CurrencyFk = company.CurrencyFk;
				header.ExchangeRate = 1;
				header.BasLanguageFk = company.LanguageFk;
			}

			var loginClerk = new BasicsClerkLogic().GetLoginClerk();
			if (loginClerk != null)
			{
				header.ClerkPrcFk = loginClerk.Id;
			}

			SetCommonProperties(ref header, companyCode);

			// Billing Schema
			var billingSchema = CommonBillingSchemaLogic.GetDefaultBillingSchema(header.PrcConfigurationFk);
			if (billingSchema != null)
			{
				header.BillingSchemaFk = billingSchema.Id;
			}

			// RubricCategory
			header.RubricCategoryFk = CommonBillingSchemaLogic.GetRubricCategoryByConfiguration(header.PrcConfigurationFk);

			var vatGroupDefault = vatGroupLogic.GetVatGroupDefault();
			if (vatGroupDefault != null)
			{
				header.BpdVatGroupFk = vatGroupDefault.Id;
			}
			header.SalesTaxMethodFk = ProcurementCommonSalesTaxMethodLogic.GetSalesTaxMethodDefault();

			if (projectId != null)
			{
				header.ProjectFk = projectId;
				var defaultUnit = BusinessApplication.BusinessEnvironment.GetExportedValue<IControllingUnitLogic>().GetControllingUnitForPesCreation((Int32)projectId);
				if (defaultUnit != null)
				{
					header.ControllingUnitFk = defaultUnit.Id;
				}
				return header;
			}
			return header;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		IPesHeaderEntity IPesHeaderProvider.Create()
		{
			var entity = new PesHeaderEntity() { Id = this.SequenceManager.GetNext("PES_HEADER") };
			var vatGroupDefault = Injector.Get<IVatGroupLogic>().GetVatGroupDefault();
			if (vatGroupDefault != null)
			{
				entity.BpdVatGroupFk = vatGroupDefault.Id;
			}
			return entity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="header"></param>
		/// <param name="companyCode"></param>
		/// <returns></returns>
		private void SetCommonProperties(ref PesHeaderEntity header, string companyCode = null)
		{
			if (companyCode != null)
			{
				header.CompanyFk = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
			}
			else
			{
				header.CompanyFk = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			}

			PesStatusEntity defaultPesStatus = new PesStatusLogic().GetDefault();
			if (defaultPesStatus != null)
			{
				header.PesStatusFk = defaultPesStatus.Id;
			}
			else
			{
				throw new Exception(string.Format(Basics.Common.Localization.Properties.Resources.ERR_DefaultStatusNullMessage, "Pes Status"));
			}
			header.Id = this.SequenceManager.GetNext("PES_HEADER");
			header.ConfigHeaderIsConsolidateChange = IsConsolidateChange(header.PrcConfigurationFk, null);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		protected override void SavePreProcessing(IEnumerable<PesHeaderEntity> entities)
		{
			var hasNewItems = entities.Any(e => e.Version == 0);
			var pesShipmentInfoLogic = hasNewItems ? new PesShipmentInfoLogic() : null;
			var numberLogic = hasNewItems ? new BasicsCompanyNumberLogic() : null;
			var configs = hasNewItems ? new PrcConfigurationLogic().GetItemsByKey(entities.Select(e => e.PrcConfigurationFk)) : null;

			foreach (var pes in entities)
			{
				if (pes.Version == 0 && !pes.PesShipmentinfoFk.HasValue)
				{
					var newShipmentInfo = pesShipmentInfoLogic.CreateBlankInfo();
					pes.PesShipmentinfoFk = newShipmentInfo.Id;
					pesShipmentInfoLogic.Save(newShipmentInfo);
				}

				// Code Auto generated
				if (pes.Version == 0 && string.IsNullOrWhiteSpace(pes.Code))
				{
					var config = configs.FirstOrDefault(c => c.Id == pes.PrcConfigurationFk);
					if (config != null && numberLogic.HasToCreateCompanyNumber(config.RubricCategoryFk))
					{
						pes.Code = GenerateCode(pes);
						if (string.IsNullOrWhiteSpace(pes.Code))
						{
							throw new Exception(Basics.Common.Localization.Properties.Resources.AutoGenerateCodeFail);
						}
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesComplete"></param>
		/// <returns></returns>
		public PesCompleteEntity Update(PesCompleteEntity pesComplete)
		{
			var validator = PrcCommonWorkflowEventHelper.BeginCreatedCheck(pesComplete.PesHeader);
			var pes = pesComplete.PesHeader ?? this.GetItemByKey(pesComplete.MainItemId);

			using (TransactionScope transaction = TransactionScopeFactory.Create())
			{
				PesItemLogic pesItemLogic = new PesItemLogic();
				PesShipmentInfoLogic pesShipmentInfoLogic = new PesShipmentInfoLogic();
				PesBoqLogic pesBoqLogic = new PesBoqLogic();

				if (pesComplete.PesShipmentinfoToSave != null)
				{
					if (pesComplete.PesShipmentinfoToSave != null && pesComplete.PesShipmentinfoToSave.Any())
					{
						pesComplete.PesShipmentinfoToSave = pesShipmentInfoLogic.Save(pesComplete.PesShipmentinfoToSave);
						if (!string.IsNullOrEmpty(pesComplete.PesShipmentinfoToSave.First().Trackingnumber) && !string.IsNullOrEmpty(pesComplete.PesShipmentinfoToSave.First().Carrierlink))
						{
							string urlstr = "/trackings/post";
							string requestdata = "{\"tracking_number\": \"" + pesComplete.PesShipmentinfoToSave.First().Trackingnumber + "\",\"carrier_code\":\"" + pesComplete.PesShipmentinfoToSave.First().Carrierlink + "\"}";
							new PesTrackingLogic().TrackLogistics(urlstr, requestdata, TrackingHttpMethod.POST);
						}
					}
				}

				if (pesComplete.PesHeader != null)
				{
					var config = new PrcConfigurationLogic().GetItemByKey(pesComplete.PesHeader.PrcConfigurationFk);
					var hasToCreate = new BasicsCompanyNumberLogic().HasToCreateCompanyNumber(config.RubricCategoryFk);
					if (pesComplete.PesHeader.Version == 0 && hasToCreate)
					{
						var code = GenerateCode(pesComplete.PesHeader);
						if (!string.IsNullOrEmpty(code))
						{
							pesComplete.PesHeader.Code = code;
						}
					}
					HandleDataEffective(pesComplete.PesHeader);
					HandleDataTruncate(pesComplete.PesHeader);
					this.Save(pesComplete.PesHeader);
					UpdateSearchPattern(pesComplete.PesHeader.Id);
				}

				if (pesComplete.PesItemToSave != null || pesComplete.PesItemToDelete != null)
				{
					if (pesComplete.PesItemToSave != null && pesComplete.PesItemToSave.Any())
					{
						if (pesComplete.IsFromYtwo)
						{
							pesItemLogic.Save(pesComplete.PesItemToSave);
						}
						else
						{
							pesComplete.PesItemToSave = pesItemLogic.SavePesItems(pesComplete.MainItemId, pesComplete.PesItemToSave);
						}
					}

					if (pesComplete.PesItemToDelete != null && pesComplete.PesItemToDelete.Any())
					{
						pesItemLogic.DeletePesItems(pesComplete.MainItemId, pesComplete.PesItemToDelete);
					}
				}

				if (pesComplete.PesShipmentinfoToDelete != null)
				{
					if (pesComplete.PesShipmentinfoToDelete != null && pesComplete.PesShipmentinfoToDelete.Any())
					{
						pesShipmentInfoLogic.Delete(pesComplete.PesShipmentinfoToDelete);
					}
				}

				if (pesComplete.PesBoqToSave != null || pesComplete.PesBoqToDelete != null)
				{
					if (pesComplete.PesBoqToSave != null && pesComplete.PesBoqToSave.Any())
					{
						pesComplete.PesBoqToSave = pesBoqLogic.Save(pesComplete.PesBoqToSave, pes);
					}

					if (pesComplete.PesBoqToDelete != null && pesComplete.PesBoqToDelete.Any())
					{
						pesBoqLogic.Delete(pesComplete.PesBoqToDelete);
					}
				}

				// BillingSchema
				var header = this.GetItemByKey(pesComplete.MainItemId);
				var billingSchemaLogic = new PesBillingSchemaLogic();
				bool IsFromRecalucteBtn = billingSchemaLogic.IsFromRecalucteBtn(header, pesComplete.BillingSchemaToDelete, pesComplete.BillingSchemaToSave);
				pesComplete.BillingSchemaToSave = billingSchemaLogic.Update(header, pesComplete.BillingSchemaToDelete, pesComplete.BillingSchemaToSave, true, IsFromRecalucteBtn);

				transaction.Complete();
			}

			// #89891 - Requirement for new Subscribed Events in Workflow
			validator.EndCreatedCheck(pesComplete.PesHeader, PrcCommonWorkflowEventUuids.NewPesCreated);

			if (validator.IsNewEntityCreated() && pesComplete.PesHeader != null)
			{
				pesComplete.PesHeader = this.GetItemByKeyComplete(pesComplete.PesHeader.Id);
			}

			return pesComplete;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesHeaderEntity"></param>
		/// <returns></returns>
		bool IPesHeaderProvider.SaveNewOrUpdate(IPesHeaderEntity pesHeaderEntity)
		{
			this.Save(pesHeaderEntity as PesHeaderEntity);
			return true;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesComplete"></param>
		/// <returns></returns>
		public PesCompleteNewEntity UpdateNew(PesCompleteNewEntity pesComplete)
		{
			var validator = PrcCommonWorkflowEventHelper.BeginCreatedCheck(pesComplete.PesHeader);
			var pes = pesComplete.PesHeader ?? this.GetItemByKey(pesComplete.MainItemId);
			var BeforePesEntitiesToSave = this.GetPesItems(pesComplete.PesItemToSave);
			bool notEqualWarn = false;
			var prcItemCostGrpLogic = new MainItem2CostGroupLogic("PRC_ITEM2COSTGRP");
			var pesItemCostGrpLogic = new MainItem2CostGroupLogic("PES_ITEM2COSTGRP");
			var newCostGroups = new List<MainItem2CostGroupEntity>();
			var pes2StockLogic = new Pes2StockLogic();

			using (TransactionScope transaction = TransactionScopeFactory.Create())
			{
				PesItemLogic pesItemLogic = new PesItemLogic();
				PesShipmentInfoLogic pesShipmentInfoLogic = new PesShipmentInfoLogic();
				PesBoqLogic pesBoqLogic = new PesBoqLogic();
				PesSelfBillingLogic pesSelfBillingLogic = new PesSelfBillingLogic();
				var prcPackage2ExtBidderLogic = new PrcPackage2ExtBidderLogic();

				if (pesComplete.PesShipmentinfoToSave != null && pesComplete.PesShipmentinfoToSave.Any())
				{
					pesComplete.PesShipmentinfoToSave = pesShipmentInfoLogic.Save(pesComplete.PesShipmentinfoToSave);
					if (!string.IsNullOrEmpty(pesComplete.PesShipmentinfoToSave.First().Trackingnumber) && !string.IsNullOrEmpty(pesComplete.PesShipmentinfoToSave.First().Carrierlink))
					{
						string urlstr = "/trackings/post";
						string requestdata = "{\"tracking_number\": \"" + pesComplete.PesShipmentinfoToSave.First().Trackingnumber + "\",\"carrier_code\":\"" + pesComplete.PesShipmentinfoToSave.First().Carrierlink + "\"}";
						new PesTrackingLogic().TrackLogistics(urlstr, requestdata, TrackingHttpMethod.POST);
					}
				}

				if (pesComplete.PesHeader != null)
				{
					var oldPesHeader = this.GetItemByKey(pesComplete.MainItemId);

					var config = new PrcConfigurationLogic().GetItemByKey(pesComplete.PesHeader.PrcConfigurationFk);
					var hasToCreate = new BasicsCompanyNumberLogic().HasToCreateCompanyNumber(config.RubricCategoryFk);
					if (pesComplete.PesHeader.Version == 0 && hasToCreate)
					{
						var code = GenerateCode(pesComplete.PesHeader);
						if (!string.IsNullOrEmpty(code))
						{
							pesComplete.PesHeader.Code = code;
						}
						else
						{
							throw new BusinessLayerException(Basics.Common.Localization.Properties.Resources.AutoGenerateCodeFail);
						}
					}
					if (pesComplete.PesHeader.Version == 0 && !pesComplete.PesHeader.PesShipmentinfoFk.HasValue)
					{
						var newShipmentInfo = pesShipmentInfoLogic.CreateBlankInfo();
						pesComplete.PesHeader.PesShipmentinfoFk = newShipmentInfo.Id;
						pesShipmentInfoLogic.Save(newShipmentInfo);
					}
					// Billing Schema
					if (pesComplete.PesHeader.BillingSchemaFk == null || pesComplete.PesHeader.BillingSchemaFk == 0)
					{
						var billingSchema = CommonBillingSchemaLogic.GetDefaultBillingSchema(pesComplete.PesHeader.PrcConfigurationFk);
						if (billingSchema != null)
						{
							pesComplete.PesHeader.BillingSchemaFk = billingSchema.Id;
						}
					}
					HandleDataEffective(pesComplete.PesHeader);
					HandleDataTruncate(pesComplete.PesHeader);
					this.Save(pesComplete.PesHeader);
					UpdateSearchPattern(pesComplete.PesHeader.Id);

					pes2StockLogic.UpdateStockTransactionDate(pesComplete.PesHeader);

					if (null != pesComplete.PesHeader.ConHeaderFk && ((null != oldPesHeader && oldPesHeader.ConHeaderFk != pesComplete.PesHeader.ConHeaderFk) || null == oldPesHeader))
					{
						HeaderPparamFactory.CopyHeaderPparamToTargetHeader(PparamType.Contract, PparamType.Pes, pesComplete.PesHeader.ConHeaderFk.Value, pesComplete.PesHeader.Id);
					}
					else
					{
						if (null != pesComplete.PesHeader.ProjectFk && ((null != oldPesHeader && oldPesHeader.ProjectFk != pesComplete.PesHeader.ProjectFk) || null == oldPesHeader))
						{
							HeaderPparamFactory.CopyHeaderPparamToTargetHeader(PparamType.Project, PparamType.Pes, pesComplete.PesHeader.ProjectFk.Value, pesComplete.PesHeader.Id);
						}
					}
					if (null != pesComplete.PesHeader.PesHeaderFk && ((null != oldPesHeader && oldPesHeader.PesHeaderFk != pesComplete.PesHeader.PesHeaderFk) || null == oldPesHeader))
					{
						HeaderPparamFactory.CopyHeaderPparamToTargetHeader(PparamType.Pes, PparamType.Pes, pesComplete.PesHeader.PesHeaderFk.Value, pesComplete.PesHeader.Id);
					}
				}

				if (pesComplete.PesItemToSave != null || pesComplete.PesItemToDelete != null)
				{
					pes2StockLogic.UpdateStock(null, null, pesComplete.PesItemToDelete);
					IEnumerable<PesItemEntity> pesItemToSave = pesComplete.PesItemToSave != null ? pesComplete.PesItemToSave.Select(e => e.Item).Where(e => e != null).ToList() : null;
					var pesItemToDelete = pesComplete.PesItemToDelete;

					if (pesComplete.PesItemToSave != null && pesComplete.PesItemToSave.Any())
					{
						#region cost group
						foreach (var pesItem in pesComplete.PesItemToSave)
						{
							if (pesItem.CostGroupToSave != null && pesItem.CostGroupToSave.Any())
							{
								new MainItem2CostGroupLogic("PES_ITEM2COSTGRP").Save(pesItem.CostGroupToSave);
							}
							if (pesItem.CostGroupToDelete != null && pesItem.CostGroupToDelete.Any())
							{
								new MainItem2CostGroupLogic("PES_ITEM2COSTGRP").Delete(pesItem.CostGroupToDelete);
							}
							//set the cost group from prc_item
							if (pesItem.Item != null && pesItem.Item.Version == 0 && pesItem.Item.PrcItemFk != null)
							{
								var pesCostGroups = pesItemCostGrpLogic.GetByFilter(e => e.MainItemId.Value == pesItem.Item.Id).ToList();
								//get cost group from prc_item
								var prcItemCostGroups = prcItemCostGrpLogic.GetByFilter(e => e.MainItemId64.Value == pesItem.Item.PrcItemFk.Value).ToList();
								foreach (var ctGroup in prcItemCostGroups)
								{
									var existEntity = pesCostGroups.FirstOrDefault(e => e.CostGroupFk == ctGroup.CostGroupFk && e.CostGroupCatFk == ctGroup.CostGroupCatFk);
									if (existEntity == null)
									{
										MainItem2CostGroupEntity costGroup = new MainItem2CostGroupEntity();
										costGroup.CostGroupCatFk = ctGroup.CostGroupCatFk;
										costGroup.CostGroupFk = ctGroup.CostGroupFk;
										costGroup.MainItemId = pesItem.Item.Id;
										newCostGroups.Add(costGroup);
									}
								}
							}
						}
						#endregion cost group

						#region controllingGrpSetLogic

						var _logic = new PesItemGrpSetLogic();
						var _entities = _logic.Get(pesComplete.PesItemToSave);
						pesItemToSave = _entities["Header"] as IEnumerable<PesItemEntity>;

						if (pesComplete.IsFromYtwo)
						{
							pesItemLogic.Save(pesItemToSave);
						}
						else
						{
							if (pesItemToSave != null && pesItemToSave.Any())
							{
								var savedEntities = pesItemLogic.SavePesItemsNew(pesItemToSave, pes);
							}
						}

						var AfterDeleteset = _entities["AfterDeleteset"] as IEnumerable<int>;
						if (AfterDeleteset.Any() && AfterDeleteset != null)
						{
							_logic.DeleteSet(AfterDeleteset);
						}
						#endregion controllingGrpSetLogic
						var conHeaderLogic = new ConHeaderLogic();
						var pesItemPriceConditionLogic = new PesItemPriceConditionLogic();
						var pesItemPriceConditionToSave = new List<PesItemPriceConditionEntity>();
						var pesItemPriceConditionToDelete = new List<PesItemPriceConditionEntity>();

						var pesItems = pesComplete.PesItemToSave.Select(e => e.Item).Where(e => e != null);
						var conHeaderFks = pesItems.CollectIds(e => e.ConHeaderFk).ToHashSet();
						var prcitemIds = pesItems.CollectIds(e => e.PrcItemFk).ToList();

						var callOffList = conHeaderLogic.GetItemsByKey(conHeaderFks)
																  .Where(conHeaderLogic.IsCallOff)
																  .CollectIds(c => c.Id)
																  .ToList();
						var prcItems = new PrcItemLogic().GetItemsByIds(prcitemIds);

						foreach (var item in pesComplete.PesItemToSave)
						{
							if (item.Item != null)
							{
								var prcItemTemps = new PrcItemMergedLookupLogic().GetSearchList(e => e.Id == item.Item.PrcItemFk).FirstOrDefault();
								item.Item.IsChangePrice = false;
								item.Item.IsChangePriceOc = false;
								item.Item.IsChangePriceGross = false;
								item.Item.IsChangePriceGrossOc = false;
								if (prcItemTemps != null)
								{
									if (prcItemTemps.Price != item.Item.Price)
									{
										item.Item.IsChangePrice = true;
									}
									if (prcItemTemps.PriceOc != item.Item.PriceOc)
									{
										item.Item.IsChangePriceOc = true;
									}

									if (prcItemTemps.PriceGross != item.Item.PriceGross)
									{
										item.Item.IsChangePriceGross = true;
									}
									if (prcItemTemps.PriceGrossOc != item.Item.PriceGrossOc)
									{
										item.Item.IsChangePriceGrossOc = true;
									}
								}
								if (item.Item.PrcItemFk != null)
								{
									var prcItem = prcItems.Find(e => e.Id == item.Item.PrcItemFk.Value);

									if (item.Item.BasBlobsSpecificationFk == null)
									{
										var blobLogic = new BlobLogic();
										if (prcItem.BasBlobsSpecificationFk != null)
										{
											item.Item.BasBlobsSpecificationFk = blobLogic.CopyBlob((int)prcItem.BasBlobsSpecificationFk);
											item.Item = pesItemLogic.Save(item.Item);
										}
										else if (prcItem.MdcMaterialFk != null)
										{
											var mdcCommoditySearchLogic = new MdcCommoditySearchVLogic();
											var mdcCommodity = mdcCommoditySearchLogic.GetCommodityById((int)prcItem.MdcMaterialFk);
											if (mdcCommodity != null && mdcCommodity.BasBlobsSpecificationFk != null)
											{
												item.Item.BasBlobsSpecificationFk = blobLogic.CopyBlob((int)mdcCommodity.BasBlobsSpecificationFk);
												item.Item = pesItemLogic.Save(item.Item);
											}
										}
									}
									item.Item.CallOffQuantity = (item.Item.ConHeaderFk is int fk && callOffList.Contains(fk)) ? prcItem.Quantity : null;
								}
								if (item.BulkEditPriceConditionToSave != null)
								{
									pesItemPriceConditionLogic.BulkEditToSave(item);
									var currentPesItem = item.Item;
									pesItemLogic.RecalculatePriceExtra(item.PesItemPriceConditionToSave, ref currentPesItem);
									pesItemLogic.RecalculatePesItemTotalAndGross(item.Item, pes.BpdVatGroupFk);
								}
							}

							var priceConditionSave = item.PesItemPriceConditionToSave;
							if (priceConditionSave != null && priceConditionSave.Any())
							{
								pesItemPriceConditionToSave.AddRange(priceConditionSave);
							}
							var priceConditionDelete = item.PesItemPriceConditionToDelete;
							if (priceConditionDelete != null && priceConditionDelete.Any())
							{
								pesItemPriceConditionToDelete.AddRange(priceConditionDelete);
							}
						}
						if (pesItemPriceConditionToDelete.Any())
						{
							pesItemPriceConditionLogic.Delete(pesItemPriceConditionToDelete);
						}
						if (pesItemPriceConditionToSave.Any())
						{
							pesItemPriceConditionLogic.Save(pesItemPriceConditionToSave);
						}
					}

					if (pesComplete.PesItemToDelete != null && pesComplete.PesItemToDelete.Any())
					{
						var pesItemPriceConditionLogic = new PesItemPriceConditionLogic();
						var pesItemPriceConditionEntities = new List<PesItemPriceConditionEntity>();
						foreach (var item in pesComplete.PesItemToDelete)
						{
							//if the pes item used in stock transaction, then it will can not be deleted
							int pesId = pes2StockLogic.StockTransactionIdByPesItem(item.Id);
							if (pesId > 0)
							{
								throw new BusinessLayerException(NLS.ERR_PesItemCannotBeDeletedAsItIsUsedInStockTransaction);
							}
							var priceConditions = pesItemPriceConditionLogic.GetSearchList(e => e.PesItemFk == item.Id);
							if (priceConditions != null && priceConditions.Any())
							{
								pesItemPriceConditionEntities.AddRange(priceConditions);
							}
						}
						if (pesItemPriceConditionEntities.Any())
						{
							pesItemPriceConditionLogic.Delete(pesItemPriceConditionEntities);
						}
						pesItemLogic.DeletePesItemsNew(pesComplete.MainItemId, pesItemToDelete, pes);

						#region controllingGrpSetLogic
						PesItemGrpSetLogic _logic = new PesItemGrpSetLogic();
						_logic.DeleteEntities(pesComplete.PesItemToDelete);
						#endregion controllingGrpSetLogic
					}
					var toSave = pesComplete.PesItemToSave?.Where(e => e.Item != null).Select(e => e.Item);
					notEqualWarn = pes2StockLogic.UpdateStock(BeforePesEntitiesToSave, toSave, null);

					pesItemLogic.UpdateCumulativeInvoicedQuantities(toSave, pesComplete.PesHeader);
				}

				if (pesComplete.PesShipmentinfoToDelete != null && pesComplete.PesShipmentinfoToDelete.Any())
				{
					pesShipmentInfoLogic.Delete(pesComplete.PesShipmentinfoToDelete);
				}

				if (pesComplete.PesBoqToSave != null || pesComplete.PesBoqToDelete != null)
				{
					if (pesComplete.PesBoqToSave != null && pesComplete.PesBoqToSave.Any())
					{
						decimal? exchangeRate = pes != null ? pes.ExchangeRate : new Nullable<decimal>();
						pesComplete.PesBoqToSave = pesBoqLogic.SaveNew(pesComplete.PesBoqToSave, exchangeRate);
					}

					if (pesComplete.PesBoqToDelete != null && pesComplete.PesBoqToDelete.Any())
					{
						pesBoqLogic.Delete(pesComplete.PesBoqToDelete);
					}
				}

				if (pesComplete.PesSelfBillingToSave != null || pesComplete.PesSelfBillingToDelete != null)
				{
					if (pesComplete.PesSelfBillingToSave != null && pesComplete.PesSelfBillingToSave.Any())
					{
						pesComplete.PesSelfBillingToSave = pesSelfBillingLogic.Save(pesComplete.MainItemId, pesComplete.PesSelfBillingToSave);
					}
					if (pesComplete.PesSelfBillingToDelete != null && pesComplete.PesSelfBillingToDelete.Any())
					{
						pesSelfBillingLogic.Delete(pesComplete.PesSelfBillingToDelete);
					}
				}

				if (pesComplete.PrcPackage2ExtBidderToSave != null)
				{
					pesComplete.PrcPackage2ExtBidderToSave = prcPackage2ExtBidderLogic.SavePackage2ExtBidderComplete(pesComplete.PrcPackage2ExtBidderToSave);
				}

				if (pesComplete.PrcPackage2ExtBidderToDelete != null)
				{
					prcPackage2ExtBidderLogic.Delete(pesComplete.PrcPackage2ExtBidderToDelete);
				}

				pesComplete.NotEqualWarn = notEqualWarn;

				// BillingSchema
				var billingSchemaLogic = new PesBillingSchemaLogic();
				if (pes != null)
				{
					bool IsFromRecalucteBtn = billingSchemaLogic.IsFromRecalucteBtn(pes, pesComplete.BillingSchemaToDelete, pesComplete.BillingSchemaToSave);
					pesComplete.BillingSchemaToSave = billingSchemaLogic.Update(pes, pesComplete.BillingSchemaToDelete, pesComplete.BillingSchemaToSave, true, IsFromRecalucteBtn);
				}

				// update quantities of PrcItem
				if (pes != null && (pesComplete.PesItemToSave != null || pesComplete.PesItemToDelete != null))
				{
					bool isConsolidateChange = IsConsolidateChange(pes.PrcConfigurationFk, null);
					CalculatePrcItemsQuantities(pesComplete.PesItemToSave, pesComplete.PesItemToDelete, isConsolidateChange);
				}

				// recalucate price condition
				if (pes != null && pes.DealWithRateUpdateLater != true)
				{
					CalculatePriceCondition(pes);
				}
				if (pes != null && pes.DealWithRateUpdateLater == true)
				{
					pes.DealWithRateUpdateLater = false;
				}

				transaction.Complete();
			}
			//save new cost group
			if (newCostGroups.Any())
			{
				pesItemCostGrpLogic.Save(newCostGroups);
			}

			// #89891 - Requirement for new Subscribed Events in Workflow
			validator.EndCreatedCheck(pesComplete.PesHeader, PrcCommonWorkflowEventUuids.NewPesCreated);

			if (validator.IsNewEntityCreated() && pesComplete.PesHeader != null)
			{
				pesComplete.PesHeader = this.GetItemByKeyComplete(pesComplete.PesHeader.Id);
			}

			return pesComplete;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pes"></param>
		private void CalculatePriceCondition(PesHeaderEntity pes)
		{
			PesItemPriceConditionLogic pesPriceConditionLogic = new PesItemPriceConditionLogic();
			PriceConditionCalculationEntity calcEntity = new PriceConditionCalculationEntity();
			PesItemLogic itemLogic = new PesItemLogic();
			var pesItems = itemLogic.GetSearchList(e => e.PesHeaderFk == pes.Id);
			var itemIds = pesItems.Select(e => e.Id);
			IEnumerable<PesItemPriceConditionEntity> allPriceConditions = pesPriceConditionLogic.GetSearchList(e => itemIds.Contains(e.PesItemFk));
			List<PesItemPriceConditionEntity> PesItemPriceConditionEntities = new List<PesItemPriceConditionEntity>();
			foreach (var item in pesItems)
			{
				calcEntity.ExchangeRate = pes.ExchangeRate;
				calcEntity.HeaderId = pes.Id;
				calcEntity.MainItem = item;
				calcEntity.HeaderName = "procurement.pes";
				calcEntity.ProjectFk = null != pes.ProjectFk ? pes.ProjectFk.Value : 0;
				var priceConditions = allPriceConditions.Where(e => e.PesItemFk == item.Id);
				if (priceConditions.Any())
				{
					calcEntity.PriceConditions = priceConditions;
					pesPriceConditionLogic.Recalculate(calcEntity);
					PesItemPriceConditionEntities.AddRange(priceConditions);
				}
			}
			pesPriceConditionLogic.Save(PesItemPriceConditionEntities);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcConfigurationFK"></param>
		/// <returns></returns>
		public bool HasToCreateCompanyNumber(int prcConfigurationFK)
		{
			var config = new PrcConfigurationLogic().GetItemByKey(prcConfigurationFK);
			var hasToCreate = new BasicsCompanyNumberLogic().HasToCreateCompanyNumber(config.RubricCategoryFk);
			return hasToCreate;
		}

		#region Validate

		/// <summary>
		///
		/// </summary>
		/// <param name="companyId"></param>
		/// <param name="codes"></param>
		/// <param name="prcConfigurationIds"></param>
		/// <returns></returns>
		private IEnumerable<PesHeaderEntity> GetList(int companyId, IEnumerable<string> codes, IEnumerable<int> prcConfigurationIds)
		{
			return this.GetByFilter(entity => entity.CompanyFk == companyId && prcConfigurationIds.Contains(entity.PrcConfigurationFk) && codes.Contains(entity.Code)).ToList();
		}

		/// <summary>
		/// Validator
		/// </summary>
		/// <param name="pesComplete"></param>
		/// <param name="modelValidateError"></param>
		/// <returns></returns>
		public Boolean ModelValidator(PesCompleteNewEntity pesComplete, ref List<String> modelValidateError)
		{
			Boolean result = true;
			PesHeaderEntity header = pesComplete.PesHeader;

			//Description
			if (header != null && !DescriptionUniqueValidate(header.Id, header.BusinessPartnerFk, header.Description))
			{
				result = false;
				modelValidateError.Add("Description");
			}

			//Code
			//if it is new entity and the configuration can auto generate code then not need check
			if (header != null && header.Version == 0 && HasToCreateCompanyNumber(header.PrcConfigurationFk))
			{
				return result;
			}
			if (header != null && !CodeUniqueValidate(header.Id, header.PrcConfigurationFk, header.Code))
			{
				result = false;
				modelValidateError.Add("Code");
			}

			return result;
		}

		/// <summary>
		/// Code Unique Validate
		/// </summary>
		/// <param name="id"></param>
		/// <param name="prcConfigurationFk"></param>
		/// <param name="code"></param>
		/// <param name="companyCode"></param>
		/// <returns></returns>
		public Boolean CodeUniqueValidate(int id, int prcConfigurationFk, String code, string companyCode = null)
		{
			if (String.IsNullOrEmpty(code))
			{
				return true;
			}
			int loginCompanyFk = companyCode != null
				? new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id
				: BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			return this.GetByFilter(entity => entity.CompanyFk == loginCompanyFk && entity.Id != id && entity.PrcConfigurationFk == prcConfigurationFk && entity.Code == code).Count() == 0;
		}

		/// <summary>
		/// External Reference No. (DESCRIPTION) Unique Validate
		/// </summary>
		/// <param name="id"></param>
		/// <param name="bpFk"></param>
		/// <param name="description"></param>
		/// <param name="companyCode"></param>
		/// <returns></returns>
		public Boolean DescriptionUniqueValidate(int id, int bpFk, String description, string companyCode = null)
		{
			if (String.IsNullOrEmpty(description))
			{
				return true;
			}

			bool isVerifyUniquePesDescription = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.VerifyUniquePesDescriptionBaseOnBP);
			if (!isVerifyUniquePesDescription)
			{
				return true;
			}

			int loginCompanyFk;
			if (companyCode != null)
			{
				loginCompanyFk = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
			}
			else
			{
				loginCompanyFk = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			}
			return this.GetByFilter(entity => entity.CompanyFk == loginCompanyFk && entity.Id != id && entity.BusinessPartnerFk == bpFk && entity.Description == description).Count() == 0;
		}

		/// <summary>
		/// PackageFk Validate
		/// </summary>
		/// <param name="header"></param>
		/// <param name="packageFk"></param>
		/// <param name="companyCode"></param>
		/// <returns></returns>
		public PesHeaderEntity PackageFkValidate(PesHeaderEntity header, int packageFk, string companyCode = null)
		{
			int loginCompanyFk;
			if (companyCode != null)
			{
				loginCompanyFk = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
			}
			else
			{
				loginCompanyFk = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			}
			//			var loginCompanyFk = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			var conHeaderLogic = new ConHeaderLogic();
			var prcHeaderLogic = new PrcHeaderLogic();
			var conStatusLogic = new ConStatusLogic();

			PrcPackageEntity prcPackage = null;
			IEnumerable<ConHeaderEntity> conHeaders = null;
			int? originSupplier = header.SupplierFk;

			// ConHeader is filtered by login company, PesHeader.ProjectFk, PesHeader.PackageFk, PesHeader.BusinessPartnerFk (if has), PesHeader.ControllingUnitFk(if has) and case of ConHeader.ConHeaderFk which is null.
			conHeaders = conHeaderLogic.GetSearchList(headerEntity => headerEntity.CompanyFk == loginCompanyFk &&
																	headerEntity.ProjectFk == header.ProjectFk &&
																	headerEntity.PackageFk == header.PackageFk &&
																	(header.BusinessPartnerFk != -1 ? headerEntity.BusinessPartnerFk == header.BusinessPartnerFk : true) &&
																	(header.ControllingUnitFk.HasValue ? headerEntity.ControllingUnitFk == header.ControllingUnitFk.Value : true) &&
																	headerEntity.ConHeaderFk == null, true, entity => entity);

			if (conHeaders.Any())
			{
				IEnumerable<int> prcHeaderIds = null;
				IEnumerable<int> conStatusIds = null;
				IEnumerable<PrcHeaderEntity> prcHeaders = null;
				IEnumerable<ConStatusEntity> conStatuses = null;
				var validConHeaders = new List<ConHeaderEntity>();

				// ConHeader is filtered by ConStatus.IsInvoiced = false, ConStatus.Iscanceled = false, ConStatus.IsVirtual = false, ConStatus.IsOrdered = true and ConStatus.IsDelivered = false
				conStatusIds = conHeaders.CollectIds(e => e.ConStatusFk);
				conStatuses = conStatusLogic.GetSearchList(e => conStatusIds.Contains(e.Id) && !e.IsInvoiced && !e.Iscanceled && !e.IsVirtual && e.IsOrdered && !e.IsDelivered, conStatusIds.Any());

				// if the ConStatus of the ConHeader does not match the case, no information of the ConHeader is filled into the PesHeader
				if (conStatuses != null && conStatuses.Any())
				{
					// ConHeader is filtered by PrcHeader.PrcStructureFk
					if (header.PrcStructureFk.HasValue)
					{
						prcHeaderIds = conHeaders.CollectIds(e => e.PrcHeaderFk);
						prcHeaders = prcHeaderLogic.GetSearchList(e => prcHeaderIds.Contains(e.Id) && header.PrcStructureFk.Value == e.StructureFk, prcHeaderIds.Any());
					}

					// if there's only one valid ConHeader for the Package, set the ConHeaderFk to the PesHeader.ConHeaderFk. Orelse, nothing to set.
					foreach (var item in conHeaders)
					{
						if (validConHeaders.Count > 1)
						{
							break;
						}

						var conStatus = conStatuses.Where(e => e.Id == item.ConStatusFk).FirstOrDefault();
						if (conStatus == null)
						{
							continue;
						}

						var isNeed = true;

						if (header.PrcStructureFk.HasValue)
						{
							var prcHeader = prcHeaders.Where(e => e.Id == item.PrcHeaderFk).FirstOrDefault();
							isNeed = prcHeader != null;
						}

						if (isNeed)
						{
							validConHeaders.Add(item);
						}
					}

					if (validConHeaders.Count == 1)
					{
						ConHeaderEntity conHeader = validConHeaders.ElementAt(0);
						header.ConHeaderFk = conHeader.Id;
						header.BusinessPartnerFk = conHeader.BusinessPartnerFk;
						header.SubsidiaryFk = conHeader.SubsidiaryFk;
						header.SupplierFk = conHeader.SupplierFk;

						PrcHeaderEntity prcHeader = new PrcHeaderLogic().GetItemByKey(conHeader.PrcHeaderFk);
						var defaultConfig = new PrcConfigurationLookUpVLogic().GetRubricConfiguration(prcHeader.ConfigurationFk, RubricConstant.PerformanceEntrySheet);
						header.PrcConfigurationFk = defaultConfig.Id;
					}
				}
			}
			else if (header.PackageFk.HasValue)
			{
				prcPackage = new PrcPackageLogic().GetItemByKey(header.PackageFk.Value);
				header.ProjectFk = prcPackage.ProjectFk;
				if (prcPackage.ClerkPrcFk.HasValue)
				{
					header.ClerkPrcFk = prcPackage.ClerkPrcFk.Value;
				}
				else
				{
					header.ClerkPrcFk = -1;
				}
				header.ClerkReqFk = prcPackage.ClerkReqFk;
				//header.BusinessPartnerFk = -1;
				header.ConHeaderFk = null;
			}

			if (header.ConHeaderFk != null)
			{
				var id = GetStructureId(header.ConHeaderFk.Value);
				if (id > 0)
				{
					header.PrcStructureFk = id;
				}
			}

			if (originSupplier != header.SupplierFk)
			{
				header.BpdVatGroupFk = this.GetVatGroupBySupplierId(header.SupplierFk);
			}
			return header;
		}

		/// <summary>
		/// ConHeader Validate
		/// </summary>
		/// <param name="header"></param>
		/// <param name="conHeaderFk"></param>
		/// <returns></returns>
		public PesHeaderEntity ConHeaderFkValidate(PesHeaderEntity header, int conHeaderFk)
		{
			ConHeaderEntity conHeader = new ConHeaderLogic().GetItemByKey(conHeaderFk);
			int? originSupplier = header.SupplierFk;

			if (conHeader != null)
			{
				header.ProjectFk = conHeader.ProjectFk;
				if (conHeader.ClerkPrcFk.HasValue)
				{
					header.ClerkPrcFk = conHeader.ClerkPrcFk.Value;
				}
				header.ClerkReqFk = conHeader.ClerkReqFk;
				header.PackageFk = conHeader.PackageFk;
				header.ControllingUnitFk = conHeader.ControllingUnitFk;

				header.BusinessPartnerFk = conHeader.BusinessPartnerFk;
				header.SupplierFk = conHeader.SupplierFk;
				header.SubsidiaryFk = conHeader.SubsidiaryFk;

				header.CurrencyFk = conHeader.BasCurrencyFk;
				header.ExchangeRate = conHeader.ExchangeRate;
				header.BillingSchemaFk = conHeader.BillingSchemaFk;
				header.SalesTaxMethodFk = conHeader.SalesTaxMethodFk;

				PrcHeaderEntity prcHeader = new PrcHeaderLogic().GetItemByKey(conHeader.PrcHeaderFk);
				var defaultConfig = new PrcConfigurationLookUpVLogic().GetRubricConfiguration(prcHeader.ConfigurationFk, RubricConstant.PerformanceEntrySheet);
				header.PrcConfigurationFk = defaultConfig.Id;

				if (header.ConHeaderFk != null)
				{
					var id = GetStructureId(header.ConHeaderFk.Value);
					if (id > 0)
					{
						header.PrcStructureFk = id;
					}
				}
				if (conHeader.BpdVatGroupFk != null)
				{
					header.BpdVatGroupFk = conHeader.BpdVatGroupFk;
				}
				else
				{
					var vatgroupIdGetBySupplier = this.GetVatGroupBySupplierId(header.SupplierFk);
					if (vatgroupIdGetBySupplier != null)
					{
						header.BpdVatGroupFk = vatgroupIdGetBySupplier;
					}
				}
			}
			return header;
		}

		/// <summary>
		/// Get the configuration by contract
		/// </summary>
		/// <param name="contractId"></param>
		/// <returns></returns>
		public int GetConfigurationByContract(int contractId)
		{
			var conHeader = new ConHeaderLogic().GetSearchList(e => e.Id == contractId).FirstOrDefault();
			if (conHeader != null)
			{
				PrcHeaderEntity prcHeader = new PrcHeaderLogic().GetItemByKey(conHeader.PrcHeaderFk);
				int configurationFk = prcHeader.ConfigurationFk;
				var defaultConfig = new PrcConfigurationLookUpVLogic().GetRubricConfiguration(configurationFk, RubricConstant.PerformanceEntrySheet);
				return defaultConfig.Id;
			}
			return 0;
		}

		/// <summary>
		/// BusinessPartnerFk Validate
		/// </summary>
		/// <param name="header"></param>
		/// <param name="businessPartnerFk"></param>
		/// <returns></returns>
		public PesHeaderEntity BusinessPartnerFkValidate(PesHeaderEntity header, int businessPartnerFk)
		{
			SubsidiaryLogic subsidiaryLogic = new SubsidiaryLogic();
			var supplierLogic = new SupplierSearchViewLogic();
			var oldPes = GetItemByKey(header.Id);
			int? originSupplier = null;
			if (oldPes != null)
			{
				originSupplier = oldPes.SupplierFk;
			}
			SubsidiaryEntity subsidiaryEntity = null;
			if (header.SubsidiaryFk != null)
			{
				subsidiaryEntity = subsidiaryLogic.GetSearchList(entity => entity.BusinessPartnerFk == businessPartnerFk && entity.Id == header.SubsidiaryFk).FirstOrDefault();
			}
			if (subsidiaryEntity == null)
			{
				subsidiaryEntity = subsidiaryLogic.GetSearchList(entity => entity.BusinessPartnerFk == businessPartnerFk && entity.IsMainAddress).FirstOrDefault();
			}

			if (subsidiaryEntity != null)
			{
				header.SubsidiaryFk = subsidiaryEntity.Id;
			}
			else
			{
				header.SubsidiaryFk = null;
			}
			var supplier = supplierLogic.GetByFilter(e => e.BusinessPartnerFk == businessPartnerFk && e.SubsidiaryFk == header.SubsidiaryFk && e.Id == header.SupplierFk).FirstOrDefault();
			if (supplier == null)
			{
				supplier = supplierLogic.GetByFilter(e => e.BusinessPartnerFk == businessPartnerFk && e.SubsidiaryFk == header.SubsidiaryFk).FirstOrDefault();
			}
			if (supplier == null)
			{
				supplier = supplierLogic.GetByFilter(e => e.BusinessPartnerFk == businessPartnerFk).FirstOrDefault();
			}
			if (supplier != null)
			{
				header.SupplierFk = supplier.Id;
			}
			else
			{
				header.SupplierFk = null;
			}
			if (originSupplier != header.SupplierFk)
			{
				header.BpdVatGroupFk = this.GetVatGroupBySupplierId(header.SupplierFk);
			}
			return header;
		}

		/// <summary>
		/// SupplierFk Validate
		/// </summary>
		/// <param name="header"></param>
		/// <param name="supplierFk"></param>
		/// <returns></returns>
		public PesHeaderEntity SupplierFkValidate(PesHeaderEntity header, int supplierFk)
		{
			SupplierLogic supplierLogic = new SupplierLogic();
			SupplierEntity supplierEntity = supplierLogic.GetItemByKey(supplierFk);
			header.BusinessPartnerFk = supplierEntity.BusinessPartnerFk;

			return BusinessPartnerFkValidate(header, header.BusinessPartnerFk);
		}

		/// <summary>
		/// PrcConfigurationFk Validate
		/// </summary>
		/// <param name="header"></param>
		/// <param name="prcConfigurationFk"></param>
		/// <returns></returns>
		public PesHeaderEntity PrcConfigurationFkValidate(PesHeaderEntity header, int prcConfigurationFk)
		{
			int? originSupplier = header.SupplierFk;
			if (header.ConHeaderFk != null)
			{
				var id = GetStructureId(header.ConHeaderFk.Value);
				if (id > 0)
				{
					header.PrcStructureFk = id;
				}
			}
			if (originSupplier != header.SupplierFk)
			{
				header.BpdVatGroupFk = this.GetVatGroupBySupplierId(header.SupplierFk);
			}
			return header;
		}

		#endregion Validate

		/// <summary>
		/// Update the serch pattern
		/// </summary>
		public void UpdateSearchPattern(int Id)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				dbcontext.ExecuteStoredProcedure("PES_HEADER_PROC", Id);
			}
		}

		/// <summary>
		/// get the last PesHeader
		/// </summary>
		/// <param name="companyId"></param>
		/// <returns></returns>
		public PesHeaderEntity GetLastPesHeader(int companyId)
		{
			return this.GetByFilter(pesHeader => pesHeader.CompanyFk == companyId).OrderByDescending(pesHeader => pesHeader.Id).FirstOrDefault();
		}

		/// <summary>
		/// Calculate the total and vat field.
		/// </summary>
		/// <param name="mainItemId"></param>
		/// <param name="companyCode"></param>
		/// <returns></returns>
		public PesHeaderEntity CalculateTotalAndVat(int mainItemId, string companyCode = null)
		{
			PesHeaderEntity currentEntity = new PesHeaderEntity();
			if (mainItemId != 0)
			{
				PesItemLogic itemLogic = new PesItemLogic();
				PrcItemLogic prcItemLogic = new PrcItemLogic();
				TaxCodeLogic taxCodeLogic = new TaxCodeLogic();
				PesBoqLogic PesBoqLogic = new PesBoqLogic();
				BoqHeaderLogic boqHeaderLogic = new BoqHeaderLogic();
				BoqItemLogic boqItemLogic = new BoqItemLogic();
				IEnumerable<PesItemEntity> pesItems;
				IEnumerable<PesBoqEntity> pesBoqs;
				List<PrcItemEntity> prcItems;
				var taxCodeIds = Enumerable.Empty<int>();
				decimal allTotalValueOc = 0;
				decimal allTotalValue = 0;
				//decimal allTotalGrossOc = 0;
				//decimal allTotalGross = 0;
				decimal allVatValueOc = 0;
				decimal allVatValue = 0;
				decimal allFinalPriceOC = 0;
				decimal allFinalPrice = 0;
				decimal allFinalPriceOcT = 0;
				decimal allFinalPriceT = 0;
				decimal allTotalStandardCost = 0;

				//get currentEntity, contracetHeader.
				currentEntity = this.GetByFilter(e => e.Id == mainItemId).FirstOrDefault();

				if (currentEntity != null)
				{
					pesItems = itemLogic.GetSearchList(e => e.PesHeaderFk == mainItemId); //get pesItems
					pesBoqs = PesBoqLogic.GetSearchList(i => i.PesHeaderFk == mainItemId);//get pesBoqs

					//Step 1 Get all PES_ITEM.TOTAL_OC
					//Step 2 Get all PES_ITEM.VAT_OC
					if (pesItems != null && pesItems.Any())
					{
						var prcItemIds = pesItems.CollectIds(item => item.PrcItemFk).ToList();
						prcItems = prcItemLogic.GetItemsByIds(prcItemIds);
						if (prcItems != null)
						{
							taxCodeIds = prcItems.CollectIds(item => item.MdcTaxCodeFk);
						}
						//when pesItem has no prcItem but material, then MDCTaxCodeFk is from material.
						taxCodeIds = taxCodeIds.Union(pesItems.CollectIds(e => e.MdcTaxCodeFk));
						var taxCodes = taxCodeLogic.GetSearchList(e => taxCodeIds.Contains(e.Id), taxCodeIds.Any());
						var materialIds = pesItems.CollectIds(e => e.MdcMaterialFk);
						var materialLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IGetMaterialLogic>();
						var materials = materialLogic.GetMaterialsByIds(materialIds);

						foreach (PesItemEntity pesItem in pesItems)
						{
							decimal tempTotalOc = 0;
							decimal tempTotal = 0;

							tempTotalOc = pesItem.TotalOc;
							tempTotal = pesItem.Total;
							allVatValueOc += pesItem.TotalGrossOc - tempTotalOc;
							allVatValue += pesItem.TotalGross - tempTotal;

							allTotalValueOc += tempTotalOc;
							allTotalValue += tempTotal;
							if (pesItem.StandardCost > 0)
							{
								allTotalStandardCost += pesItem.StandardCost * pesItem.Quantity;
							}
						}
					}

					//Step 3 Get all PES_BOQ.BOQ_HEADER.BOQ_ITEM.FINALPRICE where line type = 103
					// TODO chi: no currency, no OC, how to calculate?
					int companyId;
					if (companyCode != null)
					{
						companyId = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
					}
					else
					{
						companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
					}
					//var companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
					var companyEntity = new BasicsCompanyLogic().GetItemByKey(companyId);
					var homeCurrency = companyEntity.CurrencyFk;

					if (pesBoqs != null)
					{
						var exchangeRateLogic = new ProcurementCommonExchangeRateLogic();
						var boqHeaderIds = pesBoqs.CollectIds(e => e.BoqHeaderFk).ToList();
						var boqHeaders = boqHeaderLogic.GetSearchList(e => boqHeaderIds.Contains(e.Id), boqHeaderIds.Any());
						var boqItems = boqItemLogic.GetSearchList(i => boqHeaderIds.Contains(i.BoqHeaderFk) && i.BoqLineTypeFk == 103);
						if (boqItems != null)
						{
							decimal allFinalGrossOc = 0;
							decimal allFinalGross = 0;
							foreach (var boqHeader in boqHeaders)
							{
								allFinalPriceOC += boqItems.Where(e => e.BoqHeaderFk == boqHeader.Id).Sum(e => e.FinalpriceOc);
								allFinalPrice += boqItems.Where(e => e.BoqHeaderFk == boqHeader.Id).Sum(e => e.Finalprice);
								allFinalGrossOc += boqItems.Where(e => e.BoqHeaderFk == boqHeader.Id).Sum(e => e.FinalgrossOc);
								allFinalGross += boqItems.Where(e => e.BoqHeaderFk == boqHeader.Id).Sum(e => e.Finalgross);
							}

							allFinalPriceOcT = (allFinalGrossOc - allFinalPriceOC);
							allFinalPriceT = (allFinalGross - allFinalPrice);
						}
					}

					//Step 4 Set PES_VALUE_OC = Step 1 + Step 3
					//PES_VALUE = PES_VALUE_OC / PES_HEADER.EXCHANGERATE
					currentEntity.PesValueOc = CalculationHelper.RoundAwayFromZero(allTotalValueOc + allFinalPriceOC);
					currentEntity.PesValue = CalculationHelper.RoundAwayFromZero(allTotalValue + allFinalPrice);
					currentEntity.PesVatOc = CalculationHelper.RoundAwayFromZero(allVatValueOc + allFinalPriceOcT);
					currentEntity.PesVat = CalculationHelper.RoundAwayFromZero(allVatValue + allFinalPriceT);
					currentEntity.TotalStandardCost = allTotalStandardCost;
					this.Save(currentEntity);
				}
			}

			return currentEntity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="mainItemId"></param>
		/// <param name="companyCode"></param>
		/// <returns></returns>
#pragma warning disable CS1066 // The default value specified will have no effect because it applies to a member that is used in contexts that do not allow optional arguments

		IPesHeaderEntity IPesHeaderProvider.CalculateTotalAndVat(int mainItemId, string companyCode = null)
		{
			return this.CalculateTotalAndVat(mainItemId, companyCode);
		}

		/// <summary>
		/// Generate code according company numbering configuration
		/// </summary>
		/// <param name="pesHeaderEntity"></param>
		/// <returns></returns>
		public string GenerateCode(PesHeaderEntity pesHeaderEntity)
		{
			var parameter = new GenerateCodeParameter()
			{
				ProjectFk = pesHeaderEntity.ProjectFk,
				ClerkFk = pesHeaderEntity.ClerkPrcFk,
				ContractFk = pesHeaderEntity.ConHeaderFk,
				ControllingUnitFk = pesHeaderEntity.ControllingUnitFk,
				StructureFk = pesHeaderEntity.PrcStructureFk,
				PackageFk = pesHeaderEntity.PackageFk,
				ConfigurationFk = pesHeaderEntity.PrcConfigurationFk,
				EntityQualifier = "Procurement.Pes.PesHeaderEntity"
			};
			return GenerateCodeUntilUnique(pesHeaderEntity, parameter);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesHeaderEntity"></param>
		/// <returns></returns>
		string IPesHeaderProvider.GenerateCode(IPesHeaderEntity pesHeaderEntity)
		{
			return this.GenerateCode(pesHeaderEntity as PesHeaderEntity);
		}

		private string GenerateCodeUntilUnique(PesHeaderEntity entity, GenerateCodeParameter parameter)
		{
			var counter = 0;
			String code = String.Empty;
			Boolean isUnique = false;
			while (!isUnique && counter < 1000)
			{
				code = new ProcurementCommonCodeLogic().GenerateCode(parameter);
				isUnique = CodeUniqueValidate(entity.Id, entity.PrcConfigurationFk, code);
				counter++;
			}

			if (!isUnique)
			{
				throw new Exception(Basics.Common.Localization.Properties.Resources.AutoGenerateCodeReduplicate);
			}

			return code;
		}

		/// <summary>
		/// Get the structure id by con header id.
		/// </summary>
		public int GetStructureId(int conHeaderId, ConHeaderEntity contract = null)
		{
			var conHeaderLogic = new ConHeaderLogic();
			var prcHeaderLogic = new PrcHeaderLogic();
			var prcHeader = new PrcHeaderEntity();
			var conHeader = contract != null ? contract : conHeaderLogic.GetItemByKey(conHeaderId);
			if (conHeader != null)
			{
				prcHeader = prcHeaderLogic.GetItemByKey(conHeader.PrcHeaderFk);
			}

			if (prcHeader != null && prcHeader.StructureFk != null)
			{
				return prcHeader.StructureFk.Value;
			}
			return -1;
		}

		/// <summary>
		/// Update the status
		/// </summary>
		/// <param name="id"></param>
		/// <param name="statusId"></param>
		/// <returns></returns>
		public PesHeaderEntity UpdateStatus(int id, int statusId)
		{
			var item = this.GetByFilter(e => e.Id == id).FirstOrDefault();
			if (item == null)
			{
				return null;
			}

			var oldstatus = new PesStatusLogic().GetItemByKey(item.PesStatusFk);
			var status = new PesStatusLogic().GetItemByKey(statusId);
			item.PesStatusFk = statusId;
			this.Save(item);
			//Defect #110805 when change to pes status to delvered,need add new items and update package boq item.
			if (!oldstatus.IsDelivered && status.IsDelivered)
			{
				var prcBoqLogic = new PrcBoqLogic();
				var prcHeaderFks = new List<int> { id };
				var boqItemCompleteToSave = new List<BoqItemComplete>();
				prcBoqLogic.UpdateSyncBaseBoqAndRel(prcHeaderFks, "pes", item.ExchangeRate);
			}

			return item;
		}

		#region IEntityFacade Methods

		private static ConvertProperties _entityProperties = new ConvertProperties()
			.Add("Id")
			.Add("PesStatusFk")
			.Add("CompanyFk")
			.Add("ProjectFk")
			.Add("PackageFk")
			.Add("ClerkPrcFk")
			.Add("ClerkReqFk")
			.Add("CurrencyFk")
			.Add("Code")
			.Add("Description")
			.Add("DocumentDate")
			.Add("SearchPattern")
			.Add("DateDelivered")
			.Add("DateDeliveredFrom")
			.Add("ConHeaderFk")
			.Add("ControllingUnitFk")
			.Add("BusinessPartnerFk")
			.Add("SubsidiaryFk")
			.Add("SupplierFk")
			.Add("Remark")
			.Add("UserDefined1")
			.Add("UserDefined2")
			.Add("UserDefined3")
			.Add("UserDefined4")
			.Add("UserDefined5")
			.Add("PrcConfigurationFk")
			.Add("PrcStructureFk")
			.Add("PesValue")
			.Add("PesVat")
			.Add("ExchangeRate")
			.Add("PesValueOc")
			.Add("PesVatOc")
			.Add("PesShipmentinfoFk")
			.Add("BpdVatGroupFk")
			.Add("SalesTaxMethodFk")
			.Add("ClerkPrcFamilyName")
			.Add("ClerkPrcFirstName")
			.Add("ClerkReqFamilyName")
			.Add("ClerkReqFirstName")
			.Add("TotalGross")
			.Add("TotalGrossOC")
			.Add("ExternalCode");

		/// <summary>
		/// Name of the entity
		/// </summary>
		string IEntityFacade.Name
		{
			get { return "PesHeaderEntity"; }
		}

		/// <summary>
		/// UUID to clearly determine the entity provider
		/// </summary>
		string IEntityFacade.Id
		{
			get { return "C1E35634DC4944DCB429948C769BF889"; }
		}

		/// <summary>
		/// Module name
		/// </summary>
		string IEntityFacade.ModuleName
		{
			get { return "procurement.pes"; }
		}

		IDictionary<string, object> IEntityFacade.Get(int id)
		{
			var entity = GetItemByKey(id);
			var objectDic = ToDictionary(entity);

			return objectDic;
		}

		private IDictionary<string, object> ToDictionary(PesHeaderEntity entity)
		{
			var objectDic = entity.AsDictionary(_entityProperties);

			return objectDic;
		}

		/// <summary>
		/// Save a PesHeaderEntity
		/// </summary>
		IDictionary<string, object> IEntityFacade.Save(IDictionary<string, object> entityDictionary)
		{
			var id = entityDictionary.GetId<int>();
			var newEntity = this.GetItemByKey(id);

			if (newEntity == null)
			{
				newEntity = new PesHeaderEntity() { Id = this.SequenceManager.GetNext("PES_HEADER") };
			}

			newEntity.SetObject(entityDictionary, _entityProperties);
			var complete = new PesCompleteNewEntity()
			{
				PesHeader = newEntity,
				MainItemId = newEntity.Id,
			};
			this.UpdateNew(complete);
			//this.CalculateTotalAndVat(complete.MainItemId);
			this.RecalculateBillingSchema(complete.MainItemId, complete);
			return newEntity.AsDictionary(_entityProperties);
		}

		/// <summary>
		/// Get Entity Properties
		/// </summary>
		string[] IEntityFacade.Properties
		{
			get
			{
				return _entityProperties.GetPropertyNames();
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		IDictionary<string, object> ICreateEntityFacade.Create()
		{
			var entity = new PesHeaderEntity() { Id = this.SequenceManager.GetNext("PES_HEADER") };
			var vatGroupDefault = Injector.Get<IVatGroupLogic>().GetVatGroupDefault();
			if (vatGroupDefault != null)
			{
				entity.BpdVatGroupFk = vatGroupDefault.Id;
			}
			return entity.AsDictionary(_entityProperties);
		}

		#endregion IEntityFacade Methods

		/// <summary>
		/// GetDbModel
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// Change the status of entity according to the given id
		/// </summary>
		/// <param name="identification">entity identification data</param>
		/// <param name="statusId">new status id</param>
		public EntityBase ChangeStatus(IStatusIdentifyable identification, int statusId)
		{
			var entity = UpdateStatus(identification.Id, statusId);
			//Fixed issue #82887 change the PES/INV status should trigger the stock logic.
			//Fixed issue #96655 STOCK . Cannot see a stock location in the module Stock
			InsertTransactionByPesHeaderStatus(identification.Id);
			return entity;
		}

		/// <summary>
		/// Get the status of the entity according to the given id
		/// </summary>
		/// <param name="identification">entity identification data</param>
		/// <returns>Current status id</returns>
		public int GetCurrentStatus(IStatusIdentifyable identification)
		{
			var item = this.GetItemByKey(identification.Id);
			return item != null ? item.PesStatusFk : 0;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="identifications"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public IEnumerable<int> GetCanChangeStatusEntities(IEnumerable<IStatusIdentifyable> identifications)
		{
			// todo-jie.wen code review(wui): please implement it.
			int companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			var ids = identifications.Select(e => e.Id).ToList();
			var pesHeaders = GetSearchList(e => ids.Contains(e.Id) && (e.CompanyFk == companyId));
			return pesHeaders.Select(e => e.Id);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesEntitiesToSave"></param>
		/// <returns></returns>
		public IEnumerable<PesItemEntity> GetPesItems(IEnumerable<PesItemCompleteEntity> pesEntitiesToSave)
		{
			if (pesEntitiesToSave == null || !pesEntitiesToSave.Any())
			{
				return null;
			}
			List<PesItemEntity> beforePesEntitiesToSave = new List<PesItemEntity>();
			PesItemLogic logic = new PesItemLogic();

			var pesItemIds = pesEntitiesToSave.Select(e => e.Item).CollectIds(x => x.Id).ToList();
			var entities = logic.GetSearchList(e => pesItemIds.Contains(e.Id));
			foreach (var pesItemCompleteEntity in pesEntitiesToSave)
			{
				var pesEntity = pesItemCompleteEntity.Item;
				if (pesEntity != null)
				{
					PesItemEntity entity = entities.FirstOrDefault(e => e.Id == pesEntity.Id);
					if (entity != null)
					{
						beforePesEntitiesToSave.Add(entity);
					}
				}
			}
			return beforePesEntitiesToSave;
		}

		#region Api

		/// <summary>
		///
		/// </summary>
		/// <param name="configId"></param>
		/// <returns></returns>
		public PrcConfigurationLookUpVEntity GetPrcConfigurationByPrcHeaderConfigId(int configId)
		{
			PrcConfigurationLookUpVEntity config = null;

			if (configId != -1)
			{
				var prcConfigurationHeaderFk = new PrcConfigurationLogic().GetItemByKey(configId).PrcConfigHeaderFk;
				IList<PrcConfigurationLookUpVEntity> prcConfugurationLookupVEntityList = new PrcConfigurationLookUpVLogic().GetSearchList(entity => entity.PrcConfigHeaderFk == prcConfigurationHeaderFk && entity.RubricFk == RubricConstant.PerformanceEntrySheet, true).ToList();
				if (prcConfugurationLookupVEntityList.Any())
				{
					config = prcConfugurationLookupVEntityList.Where(e => e.IsDefault).OrderBy(e => e.Sorting).FirstOrDefault() ??
						prcConfugurationLookupVEntityList.Where(e => e.Sorting > 0).OrderBy(e => e.Sorting).FirstOrDefault();
				}
			}
			return config;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="contract"></param>
		/// <param name="pesDescription"></param>
		/// <param name="companyCode"></param>
		/// <returns></returns>
		public string GetUniqueDescriptionByContract(ConHeaderEntity contract, string pesDescription = null, string companyCode = null)
		{
			if (contract == null)
			{
				return pesDescription;
			}
			int loginCompanyId;
			if (companyCode != null)
			{
				loginCompanyId = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
			}
			else
			{
				loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			}
			//var loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			var businessPartnerLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IGetBusinessPartnerLogic>();
			var bpId = contract.BusinessPartnerFk;
			var bpName = businessPartnerLogic.GetBusinessPartnerNameByKey(bpId);
			pesDescription = bpName + "-" + DateTime.Now.ToShortDateString();
			if (pesDescription.Length > 41)
			{
				pesDescription = pesDescription.Substring(0, 41);
			}
			return pesDescription;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="contract"></param>
		/// <param name="pes"></param>
		/// <param name="needToCreateProject"></param>
		/// <param name="companyCode"></param>
		public void FillinValuesByContract(ConHeaderEntity contract, ref PesHeaderEntity pes, bool needToCreateProject = false, string companyCode = null)
		{
			if (contract != null)
			{
				int loginCompanyId;
				if (companyCode != null)
				{
					loginCompanyId = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
				}
				else
				{
					loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				}
				//				var loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;

				// project
				if (!pes.ProjectFk.HasValue && !needToCreateProject)
				{
					pes.ProjectFk = contract.ProjectFk;
				}

				// package
				if (pes.ProjectFk.HasValue && !pes.PackageFk.HasValue)
				{
					pes.PackageFk = contract.PackageFk;
				}

				// contract
				pes.ConHeaderFk = contract.Id;
				// company
				if (pes.CompanyFk == 0)
				{
					pes.CompanyFk = contract.CompanyFk;
				}

				// clerk
				if (pes.ClerkPrcFk == 0)
				{
					pes.ClerkPrcFk = contract.ClerkPrcFk.HasValue ? contract.ClerkPrcFk.Value : 0;
				}

				if (!pes.ClerkReqFk.HasValue)
				{
					pes.ClerkReqFk = contract.ClerkReqFk;
				}

				// currency
				if (pes.CurrencyFk == 0)
				{
					pes.CurrencyFk = contract.CurrencyFk;
					// exchange rate
					if (pes.ExchangeRate == 0)
					{
						var exchangeRate = new ProcurementCommonExchangeRateLogic().GetExchangeRate(loginCompanyId, pes.CurrencyFk, pes.ProjectFk);
						pes.ExchangeRate = exchangeRate != null && exchangeRate.HasValue ? exchangeRate.Value : 0;
					}
				}

				// controlling unit
				if (!pes.ControllingUnitFk.HasValue)
				{
					pes.ControllingUnitFk = contract.ControllingUnitFk;
				}

				// procurement structure
				if (!pes.PrcStructureFk.HasValue)
				{
					pes.PrcStructureFk = contract.PrcHeaderInstance.StructureFk;
				}

				if (pes.BusinessPartnerFk == 0)
				{
					pes.BusinessPartnerFk = contract.BusinessPartnerFk;
					pes.SubsidiaryFk = contract.SubsidiaryFk;
					pes.SupplierFk = contract.SupplierFk;
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="header"></param>
		/// <param name="items"></param>
		/// <param name="logger"></param>
		/// <param name="companyCode"></param>
		/// <returns></returns>
		public bool DoValidation(PesHeaderEntity header, ref IEnumerable<PesItemEntity> items, IPublicApiExecutionLogger logger, string companyCode = null)
		{
			if (header == null)
			{
				return true;
			}

			var isValid = true;
			var businessPartnerLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IGetBusinessPartnerLogic>();
			var businessPartnerName = businessPartnerLogic.GetBusinessPartnerNameByKey(header.BusinessPartnerFk);
			IProjectEntity project = null;
			IPrcPackageEntity package = null;

			// unique description
			//if (!this.DescriptionUniqueValidate(-1, header.BusinessPartnerFk, header.Description, companyCode))
			//{
			//    logger.WriteError(string.Format("Description \"{0}\" is duplicate with Business Partner \"{1}\" in the login company.", header.Description, businessPartnerName));
			//    isValid = false;
			//}

			// check whether the package belongs to the pes' project or not.
			if (header.ProjectFk.HasValue)
			{
				var projectLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();
				project = projectLogic.GetProjectById(header.ProjectFk.Value);
			}

			if (header.PackageFk.HasValue && project != null)
			{
				var packageLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcPackageLogic>();

				var packages = packageLogic.GetPrcPackageByProjectId(header.ProjectFk.Value);
				package = packages.FirstOrDefault(e => e.Id == header.PackageFk.Value);
				if (package == null)
				{
					logger.WriteWarning(string.Format("The Package does not belong to the Pes' Project \"{0} {1}\".", project.ProjectNo, project.ProjectName));
				}
			}
			else if (header.PackageFk.HasValue && project == null)
			{
				logger.WriteWarning("The Package does not belong to the empty Project.");
			}

			ConHeaderEntity contract = null;
			if (header.ConHeaderFk.HasValue)
			{
				int loginCompanyId;
				if (companyCode != null)
				{
					loginCompanyId = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
				}
				else
				{
					loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				}
				//				var loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				contract = new ConHeaderLogic().GetSearchList(e => e.Id == header.ConHeaderFk.Value && e.CompanyFk == loginCompanyId).FirstOrDefault();
				if (contract != null)
				{
					ConStatusLogic conStatusLogic = new ConStatusLogic();
					var conStatusId = contract.ConStatusFk;
					var conStatusEntity = conStatusLogic.GetItemByPredicate(e => e.Id == conStatusId);
					if (!(!conStatusEntity.IsInvoiced && !conStatusEntity.Iscanceled && !conStatusEntity.IsVirtual && !conStatusEntity.IsDelivered && conStatusEntity.IsOrdered))
					{
						logger.WriteError(string.Format("The status of Contract \"{0}\" is not allowed to create a PES.", contract.Code));
						isValid = false;
					}
				}
				else
				{
					logger.WriteError(string.Format("Contract Id \"{0}\" is not found or does not belong to the login company.", header.ConHeaderFk));
					isValid = false;
				}
			}

			if (items != null && items.Any())
			{
				var prcItemIds = items.CollectIds(e => e.PrcItemFk);
				if (contract == null)
				{
					foreach (var prcItemId in prcItemIds)
					{
						logger.WriteError(string.Format("Procurement Item Id \"{0}\" is not valid because of the empty Contract.", prcItemId));
						isValid = false;
					}
				}
				else
				{
					var prcHeaderId = contract.PrcHeaderFk;
					var prcItems = new PrcItemLookupVLogic().GetSearchList(e => prcItemIds.Contains(e.Id) && e.PrcHeaderFk == prcHeaderId, prcItemIds.Any());

					foreach (var prcItemId in prcItemIds)
					{
						var prcItem = prcItems.FirstOrDefault(e => e.Id == prcItemId);
						if (prcItem == null)
						{
							logger.WriteWarning(string.Format("Procurement Item Id \"{0}\" is not belong to the Contract.", prcItemId));
							items = items.Where(e => e.PrcItemFk != prcItemId);
						}
						else if (prcItem.IsCanceled || prcItem.IsDelivered)
						{
							logger.WriteWarning(string.Format("The status of Procurement Item \"{0}\" is not allowed to create a Pes Item.", prcItem.ItemNO));
							items = items.Where(e => e.PrcItemFk != prcItemId);
						}
					}
				}
			}

			if (header.ClerkPrcFk == 0)
			{
				ClerkEntity loginClerk = loginClerk = new BasicsClerkLogic().GetLoginClerk();
				if (loginClerk == null)
				{
					logger.WriteError("The field Clerk Responsible is required.");
					isValid = false;
				}
			}

			if (header.PrcConfigurationFk > 0)
			{
				var config = new PrcConfigurationLookUpVLogic().GetSearchList(entity => entity.Id == header.PrcConfigurationFk).FirstOrDefault();
				if (config != null)
				{
					if (config.RubricFk != RubricConstant.PerformanceEntrySheet)
					{
						logger.WriteWarning(string.Format("Configuration \"{0}\" does not belong to Performance Entry Sheets.", config.DescriptionInfo.Translated));
					}
					else if (!this.CodeUniqueValidate(-1, header.PrcConfigurationFk, header.Code, companyCode)) // unique code
					{
						logger.WriteError(string.Format("Code \"{0}\" is duplicate with Configuration \"{1}\" in the login company.", header.Code, config.DescriptionInfo.Translated));
						isValid = false;
					}
				}
			}

			// check whether subsidiary matches business partner or not.
			if (header.SubsidiaryFk.HasValue)
			{
				var subsidiaryLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ISubsidiaryLookupDataProvider>();
				var subsidiary = subsidiaryLogic.GetSubsidiarysByID(new List<int?>() { header.SubsidiaryFk }).FirstOrDefault();
				if (subsidiary != null && subsidiary.BusinessPartnerFk != header.BusinessPartnerFk)
				{
					logger.WriteWarning(string.Format("Subsidiary \"{0}\" does not belong to Pes' Business Partner \"{1}\".", subsidiary.Description, businessPartnerName));
					header.SubsidiaryFk = null;
				}
			}

			// check whether supplier matches business partner or not.
			if (header.SupplierFk.HasValue)
			{
				var supplierLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ISupplierLookupDataProvider>();
				var suppliers = supplierLogic.GetSupplieries(header.BusinessPartnerFk);
				var supplier = suppliers.FirstOrDefault(e => e.Id == header.SupplierFk.Value);
				if (supplier == null)
				{
					logger.WriteWarning(string.Format("Supplier does not belong to Pes' Business Partner \"{0}\".", businessPartnerName));
					header.SupplierFk = null;
				}
			}

			return isValid;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="header"></param>
		/// <param name="info"></param>
		/// <param name="items"></param>
		/// <param name="needToCreateProject"></param>
		/// <param name="projectNo"></param>
		/// <param name="projectName"></param>
		/// <param name="isFromYtwo"></param>
		/// <returns></returns>
		public PesCompleteEntity ImportPes(PesHeaderEntity header, PesShipmentinfoEntity info, IEnumerable<PesItemEntity> items, bool needToCreateProject = false, string projectNo = null, string projectName = null, bool isFromYtwo = false)
		{
			if (header == null)
			{
				return null;
			}

			var conApiLogic = new ConHeaderApiLogic();
			IProjectEntity project = null;

			if (needToCreateProject && !string.IsNullOrEmpty(projectNo))
			{
				var companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				int clerkId = header.ClerkPrcFk;
				if (clerkId == 0)
				{
					var logic = new BasicsClerkLogic();
					var defaultClerk = new BasicsClerkLogic().GetSearchList(e => e.CompanyFk == companyId).FirstOrDefault();
					clerkId = defaultClerk != null ? defaultClerk.Id : clerkId;
				}
				var projectCreateInfo = conApiLogic.GetCreateProjectInfo(projectNo, projectName, companyId, clerkId);
				project = conApiLogic.CreateProject(projectCreateInfo);
			}
			if (header.Version == 0)
			{
				this.CreateAndMapEntity(ref header);
				header.ProjectFk = project != null ? project.Id : header.ProjectFk;
				new PesItemLogic().CreateAndMapEntities(ref items, header.Id);
				new PesShipmentInfoLogic().CreateAndMapEntity(ref info);
				PesShipmentInfoLogic logical = new PesShipmentInfoLogic();
				info = logical.Save(info);
				header.PesShipmentinfoFk = info.Id;
			}

			var pesComplete = new PesCompleteEntity();
			List<PesShipmentinfoEntity> infoList = new List<PesShipmentinfoEntity>();
			pesComplete.MainItemId = header.Id;
			pesComplete.PesHeader = header;
			pesComplete.PesItemToSave = items;
			if (info != null)
			{
				infoList.Add(info);
			}

			pesComplete.PesShipmentinfoToSave = infoList;

			if (project != null)
			{
				project = conApiLogic.SaveProject(project);
			}

			pesComplete.IsFromYtwo = isFromYtwo;
			this.Update(pesComplete);
			pesComplete.PesHeader = this.CalculateTotalAndVat(pesComplete.MainItemId);
			return pesComplete;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="header"></param>
		/// <param name="items"></param>
		/// <param name="needToCreateProject"></param>
		/// <param name="projectNo"></param>
		/// <param name="projectName"></param>
		/// <param name="companyCode"></param>
		/// <param name="isFromYtwo"></param>
		/// <returns></returns>
		public PesCompleteEntity ImportPes(PesHeaderEntity header, IEnumerable<PesItemEntity> items, bool needToCreateProject = false, string projectNo = null, string projectName = null, string companyCode = null, bool isFromYtwo = false)
		{
			if (header == null)
			{
				return null;
			}

			var conApiLogic = new ConHeaderApiLogic();
			IProjectEntity project = null;

			if (needToCreateProject && !string.IsNullOrEmpty(projectNo))
			{
				int companyId;
				if (companyCode != null)
				{
					companyId = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
				}
				else
				{
					companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				}
				//				var companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				int clerkId = header.ClerkPrcFk;
				if (clerkId == 0)
				{
					var logic = new BasicsClerkLogic();
					var defaultClerk = new BasicsClerkLogic().GetSearchList(e => e.CompanyFk == companyId).FirstOrDefault();
					clerkId = defaultClerk != null ? defaultClerk.Id : clerkId;
				}
				var projectCreateInfo = conApiLogic.GetCreateProjectInfo(projectNo, projectName, companyId, clerkId);
				project = conApiLogic.CreateProject(projectCreateInfo);
			}
			this.CreateAndMapEntity(ref header, companyCode);
			header.ProjectFk = project != null ? project.Id : header.ProjectFk;
			new PesItemLogic().CreateAndMapEntities(ref items, header.Id);

			var pesComplete = new PesCompleteEntity();
			pesComplete.MainItemId = header.Id;
			pesComplete.PesHeader = header;
			pesComplete.PesItemToSave = items;

			if (project != null)
			{
				project = conApiLogic.SaveProject(project);
			}

			pesComplete.IsFromYtwo = isFromYtwo;
			this.Update(pesComplete);
			pesComplete.PesHeader = this.CalculateTotalAndVat(pesComplete.MainItemId, companyCode);
			return pesComplete;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="header"></param>
		/// <param name="items"></param>
		/// <param name="needToCreateProject"></param>
		/// <param name="projectNo"></param>
		/// <param name="projectName"></param>
		/// <param name="companyCode"></param>
		/// <param name="isFromYtwo"></param>
		/// <returns></returns>
		public PesCompleteNewEntity ImportPesNew(PesHeaderEntity header, IEnumerable<PesItemEntity> items, bool needToCreateProject = false, string projectNo = null, string projectName = null, string companyCode = null, bool isFromYtwo = false)
		{
			if (header == null)
			{
				return null;
			}
			var conApiLogic = new ConHeaderApiLogic();
			IProjectEntity project = null;

			if (needToCreateProject && !string.IsNullOrEmpty(projectNo))
			{
				int companyId;
				if (companyCode != null)
				{
					companyId = new BasicsCompanyLogic().GetSearchList(e => e.Code == companyCode).FirstOrDefault().Id;
				}
				else
				{
					companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				}
				//				var companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
				int clerkId = header.ClerkPrcFk;
				if (clerkId == 0)
				{
					var logic = new BasicsClerkLogic();
					var defaultClerk = new BasicsClerkLogic().GetSearchList(e => e.CompanyFk == companyId).FirstOrDefault();
					clerkId = defaultClerk != null ? defaultClerk.Id : clerkId;
				}
				var projectCreateInfo = conApiLogic.GetCreateProjectInfo(projectNo, projectName, companyId, clerkId);
				project = conApiLogic.CreateProject(projectCreateInfo);
			}
			this.CreateAndMapEntity(ref header, companyCode);
			header.ProjectFk = project != null ? project.Id : header.ProjectFk;
			new PesItemLogic().CreateAndMapEntities(ref items, header.Id);

			var pesComplete = new PesCompleteNewEntity();
			pesComplete.MainItemId = header.Id;
			pesComplete.PesHeader = header;

			var arr = new List<PesItemCompleteEntity>();
			var i = 0;
			foreach (var pesItem in items)
			{
				arr[i].MainItemId = pesItem.Id;
				arr[i].Item = pesItem;
				i++;
			}
			pesComplete.PesItemToSave = arr;

			if (project != null)
			{
				project = conApiLogic.SaveProject(project);
			}

			pesComplete.IsFromYtwo = isFromYtwo;
			this.UpdateNew(pesComplete);
			pesComplete.PesHeader = this.CalculateTotalAndVat(pesComplete.MainItemId, companyCode);
			return pesComplete;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="header"></param>
		/// <param name="companyCode"></param>
		private void CreateAndMapEntity(ref PesHeaderEntity header, string companyCode = null)
		{
			var newHeader = this.CreateBlankHeader(companyCode);
			header.Id = newHeader.Id;
			if (header.CompanyFk == 0)
			{
				header.CompanyFk = newHeader.CompanyFk;
			}

			if (header.PrcConfigurationFk == 0)
			{
				header.PrcConfigurationFk = newHeader.PrcConfigurationFk;
			}

			if (header.CurrencyFk == 0)
			{
				header.CurrencyFk = newHeader.CurrencyFk;
				header.ExchangeRate = newHeader.ExchangeRate;
			}

			if (header.ClerkPrcFk == 0)
			{
				header.ClerkPrcFk = newHeader.ClerkPrcFk;
			}

			if (!header.DocumentDate.HasValue)
			{
				header.DocumentDate = newHeader.DocumentDate;
			}

			if (header.PesStatusFk == 0)
			{
				header.PesStatusFk = newHeader.PesStatusFk;
			}

			if (string.IsNullOrEmpty(header.Code))
			{
				header.Code = newHeader.Code;
			}
		}

		#endregion Api

		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IEnumerable<PesHeaderEntity> GetList(int projectId)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				return dbcontext.Entities<PesHeaderEntity>().Where(e => e.ProjectFk == projectId).ToList();
			}
		}

		/// <summary>
		/// get the pes header by id without permission check
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public PesHeaderEntity GetPesHeaderById(int id)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				return dbcontext.Entities<PesHeaderEntity>().Where(e => e.Id == id).FirstOrDefault();
			}
		}

		/// <summary>
		///  get pesHeaders by projectId
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		IEnumerable<IPesHeaderEntity> IPesHeaderProvider.GetPesHeaders(int projectId)
		{
			return this.GetList(projectId);
		}

		/// <summary>
		///  get pesHeaders by projectId
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="controllingUnitFks"></param>
		/// <returns></returns>
		IEnumerable<IPesHeaderEntity> IPesHeaderProvider.GetPesHeaders(int projectId, List<int> controllingUnitFks)
		{
			var entities = new List<PesHeaderEntity>();
			if (controllingUnitFks != null && controllingUnitFks.Any())
			{
				var identificationData = controllingUnitFks.Select(e => new IdentificationData() { Id = e, PKey1 = projectId }).ToArray();

				entities = this.GetPesByTempIds(identificationData, (e, tmp) => e.ControllingUnitFk.HasValue && e.ControllingUnitFk == tmp.Id && e.ProjectFk == tmp.Key1).ToList();
				if (entities.Any())
				{
					var billingSchemas = Injector.Get<ICommonBillingSchemaLogic>().GetBillingSchemas(entities.Select(x => x.Id).ToList(), "procurement.pes.billingschmema");
					foreach (var item in entities)
					{
						var currentBillingSchemas = billingSchemas.Where(x => x.HeaderFk == item.Id).ToList();
						if (currentBillingSchemas.Any(x => x.IsNetAdjusted))
						{
							item.PesValue = currentBillingSchemas.Where(x => x.IsNetAdjusted).OrderBy(x => x.Sorting).Last().Result;
						}
					}
				}
			}
			return entities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="requestId"></param>
		public void CleanupDDTempIdsTable(string requestId)
		{
			Task.Run(() =>
			{
				using (var dbContext = new DbContext(ModelBuilder.DbModel))
				{
					dbContext.ExecuteStoredProcedure("BAS_DDTEMPIDS_CLEANBYUUID_SP", requestId);
				}
			});
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="identificationData"></param>
		/// <param name="matchFunc"></param>
		/// <returns></returns>
		private IEnumerable<PesHeaderEntity> GetPesByTempIds(IdentificationData[] identificationData, Expression<Func<PesHeaderEntity, DdTempIdsEntity, bool>> matchFunc)
		{
			if (identificationData == null || !identificationData.Any())
			{
				return new List<PesHeaderEntity>();
			}

			var requestId = BusinessApplication.BusinessEnvironment.RegisterTempIdsRequestUuid();
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				try
				{
					AddIdentificationDataToTempTable(identificationData, requestId);

					IQueryable<PesHeaderEntity> query = dbContext.Entities<PesHeaderEntity>();

					var tempDataIntegrator = new TempDataIntegrator<DdTempIdsEntity>(matchFunc);

					query = tempDataIntegrator.ReduceByTempData(query, dbContext, requestId);

					var entities = query.ToList();

					return entities;
				}
				catch (Exception e)
				{
					throw new BusinessLayerException("GetPes Failed!..." + e.Message, e);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="contractIds"></param>
		/// <returns></returns>
		public IEnumerable<IPesHeaderEntity> GetPesByContractIds(IEnumerable<int> contractIds)
		{
			var entities = this.GetSearchList(e => e.ConHeaderFk.HasValue && contractIds.Contains(e.ConHeaderFk.Value));
			return entities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="ids"></param>
		/// <param name="containStatus"></param>
		/// <returns></returns>
		public IEnumerable<IPesHeaderEntity> GetPesHeaderListByIDs(List<int> ids, bool containStatus = false)
		{
			var entities = this.GetSearchList(e => ids.Contains(e.Id)).ToList();
			if (containStatus)
			{
				var pesStatus = new PesStatusLogic().GetList();
				foreach (var item in entities)
				{
					var status = pesStatus.FirstOrDefault(x => x.Id == item.PesStatusFk);
					item.IsInvoicedStatus = status != null && status.IsInvoiced;
					item.IsReadOnly = status != null && status.IsReadonly;
				}
			}
			return entities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="codes"></param>
		/// <returns></returns>
		public IEnumerable<IPesHeaderEntity> GetPesHeaderListByCodes(IEnumerable<string> codes)
		{
			var entities = this.GetSearchList(e => codes.Contains(e.Code)).ToList();
			return entities;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public IEnumerable<IPesHeaderEntity> GetFilterHeaderList()
		{
			var loginCompanyFk = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			IEnumerable<IPesHeaderEntity> pesHeaders = new List<IPesHeaderEntity>();
			return this.GetSearchList(e => e.CompanyFk == loginCompanyFk).ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public IPesHeaderEntity GetCreateData()
		{
			int loginCompanyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			var company = new BasicsCompanyLogic().GetItemByKey(loginCompanyId);
			PesHeaderEntity header = new PesHeaderEntity();

			//PrcConfigurationFk
			var defaultConfig = new PrcConfigurationLookUpVLogic().GetRubricConfiguration(RubricConstant.PerformanceEntrySheet);
			header.PrcConfigurationFk = defaultConfig.Id;

			header.BusinessPartnerFk = -1;

			if (company != null)
			{
				header.CurrencyFk = company.CurrencyFk;
				header.ExchangeRate = 1;
			}

			header.DocumentDate = header.DateDelivered = DateTime.UtcNow;

			var loginClerk = new BasicsClerkLogic().GetLoginClerk();
			if (loginClerk != null)
			{
				header.ClerkPrcFk = loginClerk.Id;
			}

			header.CompanyFk = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			// PesStatusFk
			PesStatusEntity defaultPesStatus = new PesStatusLogic().GetDefault();
			if (defaultPesStatus != null)
			{
				header.PesStatusFk = defaultPesStatus.Id;
			}
			else
			{
				throw new Exception(string.Format(Basics.Common.Localization.Properties.Resources.ERR_DefaultStatusNullMessage, "Pes Status"));
			}
			return header;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesHeaderId"></param>
		public void InsertTransactionByPesHeaderStatus(int pesHeaderId)
		{
			var pes2StockLogic = new Pes2StockLogic();
			var entities = new PesItemLogic().GetSearchList(e => e.PesHeaderFk == pesHeaderId);
			pes2StockLogic.UpdateStock(null, entities, null, pesHeaderId, true);
		}

		/// <summary>
		/// CanDelete
		/// </summary>
		/// <param name="pesId"></param>
		/// <returns>PES with reference in QTO Detail and can not be deleted</returns>
		public int CanDelete(int pesId)
		{
			int pesWithReference = 0;
			IQtoDetailLogic qtoDetailLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IQtoDetailLogic>();
			IQtoHeaderLogic qtoHeaderLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IQtoHeaderLogic>();

			List<int> pesFks = new List<int>();
			pesFks.Add(pesId);

			var qtoEntities = qtoDetailLogic.GetQtoDetailByPesFks(pesFks);
			if (qtoEntities.Any())
			{
				pesWithReference = 1;
			}

			//if the qto is readonly directly return the result
			var qtoEntitiesHasReadOnly = qtoEntities.Where(e => e.IsReadonly).ToList();
			if (qtoEntitiesHasReadOnly.Any())
			{
				pesWithReference = 2;
				return pesWithReference;
			}

			//the qto status
			var qtoHeaderFks = qtoEntities.Select(e => e.QtoHeaderFk).Distinct().ToList();
			var qtoHeaderEntities = qtoHeaderLogic.GetQtoHeaderByIds(qtoHeaderFks);
			var qtoStatusFks = qtoHeaderEntities.Where(e => e.QTOStatusFk.HasValue).Select(e => e.QTOStatusFk.Value).ToList();

			if (qtoStatusFks.Any())
			{
				var basicsCustomizeQtOStatusLogic = new BasicsCustomizeQtOStatusLogic();
				var qtoStatusEnntities = basicsCustomizeQtOStatusLogic.GetListByFilter(e => qtoStatusFks.Contains(e.Id)).ToList();

				foreach (var qtoStatus in qtoStatusEnntities)
				{
					var qStatus = (BasicsCustomizeQtOStatusEntity)qtoStatus;
					if (qStatus.IsReadOnly)
					{
						pesWithReference = 2; // cannot delete
						break;
					}
				}
			}

			return pesWithReference;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="items"></param>
		/// <returns></returns>
		protected override void PostProcess(IEnumerable<PesHeaderEntity> items)
		//protected override IEnumerable<PesHeaderEntity> FillRelated(IEnumerable<PesHeaderEntity> items)
		{
			var configurations = new PrcConfigurationLogic().GetItemsByKey(items.Select(e => e.PrcConfigurationFk));
			var configHeaderIds = configurations.CollectIds(e => e.PrcConfigHeaderFk);
			var configHeaders = new PrcConfigHeaderLogic().GetItemsByKey(configHeaderIds);

			var ProjectLookupDataProvider = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectLookupDataProvider>();
			var projectIds = items.CollectIds(e => e.ProjectFk);
			var projects = ProjectLookupDataProvider.GetProjectsByID(projectIds);

			foreach (var entity in items)
			{
				var configuration = configurations.FirstOrDefault(e => e.Id == entity.PrcConfigurationFk);
				if (configuration != null)
				{
					entity.RubricCategoryFk = configuration.RubricCategoryFk;
					var configHeader = configHeaders.FirstOrDefault(e => e.Id == configuration.PrcConfigHeaderFk);
					if (configHeader != null)
					{
						entity.ConfigHeaderIsConsolidateChange = configHeader.IsConsolidateChange;
					}
				}
				if (entity.ProjectFk.HasValue)
				{
					var project = projects.SingleOrDefault(e => e.Id == entity.ProjectFk.Value);
					if (project != null)
					{
						entity.ProjectStatusFk = project.StatusFk;
					}
				}
			}

			//return items;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		protected override void PrepareDelete(IEnumerable<PesHeaderEntity> entities)
		{
			var ids = entities.Select(e => e.Id).ToList();

			// BillingSchema
			var billingSchemaLogic = new PesBillingSchemaLogic();
			foreach (var id in ids)
			{
				billingSchemaLogic.Delete(id);
			}

			#region remove the wipFk of the refernece QTO Detail
			IQtoDetailLogic qtoDetailLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IQtoDetailLogic>();

			var qtoEntities = qtoDetailLogic.GetQtoDetailByPesFks(ids);
			if (qtoEntities.Any())
			{
				foreach (var item in qtoEntities)
				{
					item.PesHeaderFk = null;
				}
			}
			qtoDetailLogic.BatchSave(qtoEntities);
			#endregion remove the wipFk of the refernece QTO Detail

			#region controllingGrpSetLogic
			PesItemGrpSetLogic pesItemGrpSetLogic = new PesItemGrpSetLogic();
			PesItemLogic pesItemLogic = new PesItemLogic();
			var pesItems = pesItemLogic.GetSearchList(e => ids.Contains(e.PesHeaderFk));
			pesItemGrpSetLogic.DeleteEntities(pesItems);
			#endregion controllingGrpSetLogic

			#region
			var pesTransactionLogic = new PesTransactionLogic();
			var pesTransactions = pesTransactionLogic.GetByFilter(e => ids.Contains(e.PesHeaderFk));
			pesTransactionLogic.Delete(pesTransactions);
			#endregion
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerFk"></param>
		public void CreateBillingSchemas(int headerFk)
		{
			var header = this.GetItemByKey(headerFk);
			var billingSchemaLogic = new PesBillingSchemaLogic();

			if (header.BillingSchemaFk.HasValue)
			{
				var rubricCategoryFk = CommonBillingSchemaLogic.GetRubricCategoryByConfiguration(header.PrcConfigurationFk);
				billingSchemaLogic.CopyFromBasicBillingSchema(header, header.BillingSchemaFk.Value, rubricCategoryFk);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerFk"></param>
		/// <param name="pesComplete"></param>
		/// <returns></returns>
		public IEnumerable<CommonBillingSchemaEntity> RecalculateBillingSchema(int headerFk, PesCompleteNewEntity pesComplete)
		{
			var billingSchemaLogic = new PesBillingSchemaLogic();
			var header = this.GetItemByKey(headerFk);
			bool IsFromRecalucteBtn = true;
			if (pesComplete != null)
			{
				IsFromRecalucteBtn = billingSchemaLogic.IsFromRecalucteBtn(header, pesComplete.BillingSchemaToDelete, pesComplete.BillingSchemaToSave);
			}
			return billingSchemaLogic.Recalculate(header, IsFromRecalucteBtn);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerFk"></param>
		public void CalculateBillingSchema(int headerFk)
		{
			RecalculateBillingSchema(headerFk, null);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="conId"></param>
		/// <returns></returns>
		public IEnumerable<IIdentifyable> GetPesIdsForNavByConId(int conId)
		{
			var pesEntities = this.GetSearchList(e => e.ConHeaderFk == conId);
			return pesEntities;
		}

		private void HandleDataEffective(PesHeaderEntity entity)
		{
			if (entity == null || entity.DateEffective != DateTime.MinValue)
			{
				return;
			}
			if (entity.DateDelivered != DateTime.MinValue)
			{
				entity.DateEffective = entity.DateDelivered;
			}
			else if (entity.InsertedAt != DateTime.MinValue)
			{
				entity.DateEffective = entity.InsertedAt;
			}
			else
			{
				entity.DateEffective = DateTime.Now;
			}
		}

		private void HandleDataTruncate(PesHeaderEntity entity)
		{
			if (!string.IsNullOrEmpty(entity.Description))
			{
				int valueLength = ProcurementCommonColumnConfigLogic.GetColumnSize("DESCRIPTION", "REQ_HEADER");
				entity.Description = StringExtension.Truncate(entity.Description, valueLength);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesItemsToSave"></param>
		/// <param name="pesItemsToDelete"></param>
		/// <param name="IsConsolidateChange"></param>
		public void CalculatePrcItemsQuantities(IEnumerable<PesItemCompleteEntity> pesItemsToSave, IEnumerable<PesItemEntity> pesItemsToDelete, bool IsConsolidateChange)
		{
			HashSet<long> prcItems = new HashSet<long>();
			if (pesItemsToSave != null)
			{
				foreach (var pesItemToSave in pesItemsToSave)
				{
					if (pesItemToSave != null && pesItemToSave.Item != null && pesItemToSave.Item.PrcItemFk != null)
					{
						prcItems.Add(pesItemToSave.Item.PrcItemFk.Value);
					}
				}
			}
			if (pesItemsToDelete != null)
			{
				foreach (var pesItemToDelete in pesItemsToDelete)
				{
					if (pesItemToDelete != null && pesItemToDelete.PrcItemFk != null)
					{
						prcItems.Add(pesItemToDelete.PrcItemFk.Value);
					}
				}
			}

			if (prcItems.Any())
			{
				var prcItemLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IPrcItemLogic>();
				prcItemLogic.CalculateQuantities(prcItems, IsConsolidateChange);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcConfigurationId"></param>
		/// <param name="pesHeaderId"></param>
		/// <returns></returns>
		public bool IsConsolidateChange(int? prcConfigurationId, int? pesHeaderId)
		{
			bool bConsolidateChange = true;

			if (!prcConfigurationId.HasValue && pesHeaderId.HasValue)
			{
				List<int> pesHeaderIds = new List<int>();
				pesHeaderIds.Add(pesHeaderId.Value);

				var pesHeaderProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IPesHeaderProvider>();
				IPesHeaderEntity pesHeaderEntity = pesHeaderProvider.GetPesHeaderListByIDs(pesHeaderIds).FirstOrDefault();

				if (pesHeaderEntity != null)
				{
					prcConfigurationId = pesHeaderEntity.PrcConfigurationFk;
				}
			}

			if (prcConfigurationId.HasValue)
			{
				bConsolidateChange = new PrcConfigurationLogic().IsConsolidateChange(prcConfigurationId.Value);
			}

			return bConsolidateChange;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="supplierId"></param>
		/// <returns></returns>
		public int? GetVatGroupBySupplierId(int? supplierId)
		{
			int? vatGroup = null;
			if (supplierId != null)
			{
				var supplier = new SupplierLogic().GetItemByKey(supplierId);
				var supplierCompany = new SupplierCompanyLogic().GetSearchList(e => e.BpdSupplierFk == supplierId).OrderBy(e => e.Id).FirstOrDefault();

				if (supplierCompany != null && supplierCompany.VatGroupFk != null)
				{
					return supplierCompany.VatGroupFk.Value;
				}
				else if (supplier != null && supplier.VatGroupFk != null)
				{
					return supplier.VatGroupFk.Value;
				}
			}
			return vatGroup;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public List<TaxCodeMatrixEntity> GetTaxCodeMatrixes()
		{
			TaxCodeMatrixLogic taxcodeMatrixLogic = new TaxCodeMatrixLogic();
			return taxcodeMatrixLogic.GetSearchList(e => e.TaxCodeFk > 0).ToList();
		}

		/// <summary>
		/// CheckForPlantTransfer
		/// </summary>
		/// <param name="pesHeaderId"></param>
		/// <returns></returns>
		public IEnumerable<PesItemEntity> GetPesItemsWithPlants(int pesHeaderId)
		{
			var pesItemWithPlants = new List<PesItemEntity>();
			var plantTypeLogic = new BasicsCustomizePlantTypeLogic();
			var plantLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEquipmentPlantLogic>();
			var pesHeader = GetPesHeaderListByIDs(new List<int>() { pesHeaderId }).First();
			var isStock = new PesStatusLogic().GetItemByKey(pesHeader.PesStatusFk).Isstock;
			if (isStock)
			{
				var pesItems = new PesItemLogic().GetSearchList(e => e.PesHeaderFk == pesHeader.Id && e.PrcItemFk != null);
				foreach (var pesItem in pesItems)
				{
					var prcItemWithPlant = new PrcItemLogic().GetSearchList(prcItem => pesItem.PrcItemFk == prcItem.Id && prcItem.PlantFk != null).FirstOrDefault();
					if (prcItemWithPlant != null && prcItemWithPlant.PlantFk.HasValue)
					{
						pesItem.PlantFk = prcItemWithPlant.PlantFk.Value;
						var plant = plantLogic.GetPlant(new IdentificationData() { Id = pesItem.PlantFk });
						pesItem.PlantTypeFk = plant.PlantTypeFk;
						pesItemWithPlants.Add(pesItem);
					}
				}
			}

			return pesItemWithPlants;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="contractId"></param>
		/// <returns></returns>
		public IEnumerable<PesHeaderEntity> GetListByContract(int contractId)
		{
			return this.GetSearchList(e => e.ConHeaderFk == contractId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="baseContractId"></param>
		/// <returns></returns>
		public DiffBoqInfo GetDiffBoq(int baseContractId)
		{
			var diffInfo = new DiffBoqInfo()
			{
				BoqItems = new List<BoqItemEntity>(),
				Boqs = new List<PrcBoqLookupVEntity>(),
				AllBoqs = new List<PrcBoqLookupVEntity>()
			};

			var contractLogic = new ConHeaderLogic();

			var contract = contractLogic.GetItemByKey(baseContractId);
			var pess = this.GetListByContract(baseContractId);

			if (contract != null && pess != null && pess.Any())
			{
				var contracts = new List<ConHeaderEntity>() { contract };
				contracts.AddRange(contractLogic.GetSearchList(e => e.ConHeaderFk == contract.Id && e.ProjectChangeFk != null));
				var prcHeaders = contracts.Select(e => e.PrcHeaderFk);

				var boqItemLogic = new BoqItemLogic();

				// Contract
				var contractBoqs = new List<PrcBoqLookupVEntity>();
				var contractBoqItems = new List<BoqItemEntity>();

				var vlookupBoqs = new PrcBoqLookupVLogic().GetSearchList(e => prcHeaders.Contains(e.PrcHeaderFk));
				if (vlookupBoqs.Any())
				{
					var vlookupBoqIds = vlookupBoqs.Select(e => e.Id);
					diffInfo.AllBoqs = vlookupBoqs.ToList();

					var vMergeBoqIds = vlookupBoqs.GroupBy(e => e.BoqItemPrjBoqFk).Select(e => e.FirstOrDefault()).Select(e => e.Id);
					contractBoqs = diffInfo.AllBoqs.Where(e => vMergeBoqIds.Contains(e.Id)).ToList();

					var headerIds = vlookupBoqs.Select(x => x.BoqHeaderFk);
					contractBoqItems.AddRange(boqItemLogic.GetBoqItems(e => headerIds.Contains(e.BoqHeaderFk)));
				}

				// Pes
				var pesIds = pess.Select(e => e.Id);
				var pesBoqs = new List<PesBoqEntity>();
				var pesBoqItems = new List<BoqItemEntity>();
				var pesBoqList = new PesBoqLogic().GetSearchList(e => pesIds.Contains(e.PesHeaderFk));
				if (pesBoqList != null && pesBoqList.Any())
				{
					pesBoqs.AddRange(pesBoqList);
					var headerIds = pesBoqList.Where(x => x.BoqHeaderFk != null).Select(s => s.BoqHeaderFk.Value);
					pesBoqItems.AddRange(boqItemLogic.GetBoqItems(e => headerIds.Contains(e.BoqHeaderFk)));
				}

				// Boq
				if (contractBoqs.Any())
				{
					contractBoqs.ForEach(cb =>
					{
						if (!pesBoqs.Any(e => e.PrcBoqFk == cb.Id))
						{
							diffInfo.Boqs.Add(cb);
						}
					});
				}

				// BoqItem
				if (contractBoqItems.Any() && pesBoqItems.Any())
				{
					var mergeContractBoqItems = contractBoqItems.GroupBy(e => e.BoqItemPrjBoqFk + "." + e.BoqItemPrjItemFk).Select(s =>
					{
						return s.FirstOrDefault();
					}).ToList();

					mergeContractBoqItems.ForEach(cb =>
					{
						var pesItem = pesBoqItems.FirstOrDefault(e => e.BoqItemPrjBoqFk == cb.BoqItemPrjBoqFk && e.BoqItemPrjItemFk == cb.BoqItemPrjItemFk);
						if (pesItem == null)
						{
							diffInfo.BoqItems.Add(cb);
						}
					});
				}
			}

			return diffInfo;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="baseOrCOContractId"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetDiffItem(int baseOrCOContractId)
		{
			var contractLogic = new ConHeaderLogic();

			var newItems = new List<PrcItemEntity>();
			var contract = contractLogic.GetItemByKey(baseOrCOContractId);
			var pess = this.GetListByContract(baseOrCOContractId);
			var configHeaderIsConsolidateChange = IsConsolidateChange(contract.PrcHeaderEntity.ConfigurationFk, null);

			IEnumerable<IContractHeaderData> headers = null;
			if (contract != null && pess != null && pess.Any())
			{
				var contractInfoProvider = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IContractHeaderInfoProvider>();
				var basConHeaderId = contract.ConHeaderFk.HasValue ? contract.ConHeaderFk.Value : contract.Id;
				if (configHeaderIsConsolidateChange)
				{
					headers = contractInfoProvider.GetValidBaseNChangeOrdersByIds4PesNInv(new List<int>() { basConHeaderId }, configHeaderIsConsolidateChange);
				}
				else
				{
					headers = contractInfoProvider.GetValidBaseNChangeOrdersByIds4PesNInv(new List<int>() { baseOrCOContractId }, configHeaderIsConsolidateChange);
				}
				var prcHeaderIds = headers.CollectIds(e => e.PrcHeaderFk);

				#region Item

				var contractItems = new PrcItemLogic().GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk) && !e.IsDisabled).ToList();

				var pesIds = pess.Select(e => e.Id);
				var pesItems = new PesItemLogic().GetSearchList(e => pesIds.Contains(e.PesHeaderFk)).OrderByDescending(e => e.Id);

				if (contractItems.Any() && pesItems.Any())
				{
					var mergeContractItems = contractItems.GroupBy(e => { return e.PrcItemFk ?? e.Id; }).Select(s =>
					{
						var mainItem = s.FirstOrDefault();
						mainItem.Quantity = s.Sum(m => m.Quantity);
						return mainItem;
					}).ToList();

					mergeContractItems.ForEach(ci =>
					{
						var pesItem = pesItems.FirstOrDefault(e => e.PrcItemFk == ci.Id);
						if (pesItem == null || ci.Quantity != pesItem.QuantityContracted)
						{
							if (pesItem != null)
							{
								ci.Quantity = ci.Quantity - pesItem.QuantityContracted;
							}
							newItems.Add(ci);
						}
					});
				}

				#endregion
			}

			return newItems;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="ids"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetMergeItems(IEnumerable<long> ids)
		{
			var itemLogic = new PrcItemLogic();
			var items = itemLogic.GetSearchList(e => ids.Contains(e.Id));

			var mergeItemIds = items.Select(e => e.PrcItemFk);
			var mergeItems = itemLogic.GetSearchList(e => mergeItemIds.Contains(e.PrcItemFk.Value));

			foreach (var item in items)
			{
				var mergeItem = mergeItems.FirstOrDefault(e => e.Id == item.PrcItemFk);
				if (mergeItem != null)
				{
					item.Quantity = mergeItem.Quantity;
				}
			}

			return items;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="baseContractId"></param>
		/// <param name="ids"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetChangeItems(int baseContractId, IEnumerable<long> ids)
		{
			var itemLogic = new PrcItemLogic();
			var changeItems = new List<PrcItemEntity>();

			var items = itemLogic.GetSearchList(e => ids.Contains(e.Id));

			var relatedItemIds = items.Select(e => e.PrcItemFk);
			var relatedItems = itemLogic.GetSearchList(e => relatedItemIds.Contains(e.PrcItemFk.Value));

			var contracts = new ConHeaderLogic().GetSearchList(e => e.Id == baseContractId || e.ConHeaderFk == baseContractId).ToList();
			var prcHeaderIds = contracts.Select(e => e.PrcHeaderFk);
			var prcItems = new PrcItemLogic().GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk)).ToList();

			foreach (var item in items)
			{
				if (item.Id == item.PrcItemFk)
				{
					// New item
					changeItems.AddRange(relatedItems.Where(e => e.Id == item.Id));
				}
				else
				{
					// Update item (item is from base contract, here we need find the change order item)
					var changeItem = prcItems.FirstOrDefault(p =>
					{
						return p.PrcItemFk == item.PrcItemFk && contracts.Any(c => c.PrcHeaderFk == p.PrcHeaderFk && c.ConHeaderFk != null);
					});
					if (changeItem != null)
					{
						changeItems.Add(changeItem);
					}
				}
			}

			return changeItems;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="baseContractId"></param>
		/// <param name="pesId"></param>
		/// <param name="controllingUnitFk"></param>
		/// <param name="autoCompleteStructure"></param>
		public void AppendBoqs(int baseContractId, int pesId, int controllingUnitFk, bool autoCompleteStructure = false)
		{
			var contract = new ConHeaderLogic().GetItemByKey(baseContractId);
			var diffBoqInfo = this.GetDiffBoq(baseContractId);

			if (contract != null && (diffBoqInfo.Boqs.Any() || diffBoqInfo.BoqItems.Any()))
			{
				var pesBoqLogic = new PesBoqLogic();
				int structureId = pesBoqLogic.GetStructureWhenCreatePesByCon(contract);

				#region Boqs

				if (diffBoqInfo.Boqs.Any())
				{
					foreach (var boq in diffBoqInfo.Boqs)
					{
						pesBoqLogic.Create(pesId, boq, boq.PackageFk, contract.Id, controllingUnitFk, contract.TaxCodeFk, structureId);
					}
				}

				#endregion

				#region BoqItems

				if (diffBoqInfo.BoqItems.Any())
				{
					var boqItemLogic = new BoqItemLogic();

					var boqHeaderIds = diffBoqInfo.Boqs.Select(e => e.BoqHeaderFk);
					var appendItems = diffBoqInfo.BoqItems.Where(e => !boqHeaderIds.Contains(e.BoqHeaderFk)).ToList();
					if (appendItems.Any())
					{
						var boqHeaders = appendItems.Select(s => s.BoqHeaderFk);

						var prcBoqs = new PrcBoqLogic().GetSearchList(e => boqHeaders.Contains(e.BoqHeaderFk));
						Dictionary<long, PrcBoqLookupVEntity> actualBoqs = new Dictionary<long, PrcBoqLookupVEntity>();
						foreach (var boq in prcBoqs)
						{
							var vBoq = diffBoqInfo.AllBoqs.FirstOrDefault(e => e.Id == boq.Id);
							var actualBoq = diffBoqInfo.AllBoqs.FirstOrDefault(e => e.BoqItemPrjBoqFk == vBoq.BoqItemPrjBoqFk);
							actualBoqs[vBoq.Id] = actualBoq;
						}

						var prcBoqItems = boqItemLogic.GetBoqItems(e => boqHeaders.Contains(e.BoqHeaderFk));

						if (autoCompleteStructure)
						{
							foreach (var boq in prcBoqs)
							{
								var actualBoq = actualBoqs[boq.Id];
								pesBoqLogic.Create(pesId, actualBoq, boq.PackageFk, contract.Id, contract.ControllingUnitFk, contract.TaxCodeFk, structureId, false);
							}

							var appendAncestorItems = new List<BoqItemEntity>();
							foreach (var appendItem in appendItems)
							{
								var currItem = appendItem;
								while (currItem.BoqItemFk != null && !appendItems.Any(e => e.BoqHeaderFk == currItem.BoqHeaderFk && e.Id == currItem.BoqItemFk.Value) && !appendAncestorItems.Any(e => e.BoqHeaderFk == currItem.BoqHeaderFk && e.Id == currItem.BoqItemFk.Value))
								{
									var parent = prcBoqItems.FirstOrDefault(e => e.BoqHeaderFk == currItem.BoqHeaderFk && e.Id == currItem.BoqItemFk.Value);
									appendAncestorItems.Add(parent);
									currItem = parent;
								}
							}
							appendAncestorItems.Reverse();
							appendItems.AddRange(appendAncestorItems);
						}

						var sortedList = new List<BoqItemEntity>();
						var idIndex = 0;
						var newIds = boqItemLogic.SequenceManager.GetNextList("BOQ_ITEM", appendItems.Count());

						var parentIds = appendItems.Where(e => e.BoqItemFk != null).Select(e => e.BoqItemFk.Value);
						var parentList = prcBoqItems.Where(e => parentIds.Contains(e.Id));

						var pesBoqs = pesBoqLogic.GetSearchList(e => e.PesHeaderFk == pesId);
						var pesBoqHeaders = pesBoqs.Where(e => e.BoqHeaderFk != null).Select(e => e.BoqHeaderFk.Value);
						var pesBoqItems = boqItemLogic.GetBoqItems(e => pesBoqHeaders.Contains(e.BoqHeaderFk));

						var rootItems = appendItems.Where(e => e.BoqLineTypeFk == Convert.ToInt32(BoqLineType.Root)).ToList();
						var levelItems = appendItems.Where(e => e.BoqLineTypeFk >= Convert.ToInt32(BoqLineType.Level1) && e.BoqLineTypeFk <= Convert.ToInt32(BoqLineType.Level9)).OrderBy(e => e.Id).ToList();
						var leafItems = appendItems.Where(e => !rootItems.Contains(e) && !levelItems.Contains(e)).ToList();

						sortedList.AddRange(rootItems);
						sortedList.AddRange(levelItems);
						sortedList.AddRange(leafItems);

						var idMapping = new Dictionary<string, int>();
						var boqHeaderMapping = new Dictionary<int, int>();
						var saveItems = new List<BoqItemEntity>();

						foreach (var boqItem in sortedList)
						{
							var item = boqItem.Clone() as BoqItemEntity;

							var prcBoq = prcBoqs.FirstOrDefault(e => e.BoqHeaderFk == item.BoqHeaderFk);
							var actualBoq = actualBoqs[prcBoq.Id];
							var pesBoq = pesBoqs.FirstOrDefault(e => e.PrcBoqFk == actualBoq.Id);

							var newId = newIds.ElementAt(idIndex++);
							idMapping.Add(item.BoqHeaderFk + "." + item.Id, newId);

							var newHeaderId = pesBoq.BoqHeaderFk.GetValueOrDefault(0);

							if (!boqHeaderMapping.ContainsKey(item.BoqHeaderFk))
							{
								boqHeaderMapping.Add(item.BoqHeaderFk, newHeaderId);
							}

							if (item.BoqItemFk != null)
							{
								if (idMapping.ContainsKey(item.BoqHeaderFk + "." + item.BoqItemFk.Value))
								{
									item.BoqItemFk = idMapping[item.BoqHeaderFk + "." + item.BoqItemFk.Value];
								}
								else
								{
									var orgItem = parentList.FirstOrDefault(e => e.BoqHeaderFk == item.BoqHeaderFk && e.Id == item.BoqItemFk.Value);
									var pesItem = pesBoqItems.FirstOrDefault(e => e.BoqItemPrjBoqFk == orgItem.BoqItemPrjBoqFk && e.BoqItemPrjItemFk == orgItem.BoqItemPrjItemFk);
									if (pesItem != null)
									{
										item.BoqItemFk = pesItem.Id;
									}
								}
							}

							if (item.BoqItemBasisFk != null)
							{
								var orgItem = prcBoqItems.FirstOrDefault(e => e.BoqHeaderFk == item.BoqHeaderFk && e.Id == item.BoqItemBasisFk.Value);
								var pesItem = pesBoqItems.FirstOrDefault(e => e.BoqItemPrjBoqFk == orgItem.BoqItemPrjBoqFk && e.BoqItemPrjItemFk == orgItem.BoqItemPrjItemFk);
								if (pesItem != null)
								{
									item.BoqItemBasisFk = pesItem.Id;
								}
								else
								{
									item.BoqItemBasisFk = null;
								}
							}

							if (item.BoqItemReferenceFk != null)
							{
								var orgItem = prcBoqItems.FirstOrDefault(e => e.BoqHeaderFk == item.BoqHeaderFk && e.Id == item.BoqItemReferenceFk.Value);
								var pesItem = pesBoqItems.FirstOrDefault(e => e.BoqItemPrjBoqFk == orgItem.BoqItemPrjBoqFk && e.BoqItemPrjItemFk == orgItem.BoqItemPrjItemFk);
								if (pesItem != null)
								{
									item.BoqItemReferenceFk = pesItem.Id;
								}
								else
								{
									item.BoqItemReferenceFk = null;
								}
							}

							item.BoqItemParent = null;
							item.BoqItemChildren = null;
							item.BoqItemReferenceParent = null;
							item.BoqItemBasisParent = null;

							item.Id = newId;
							item.Version = 0;
							item.BoqHeaderFk = newHeaderId;
							saveItems.Add(item);
						}

						if (saveItems.Any())
						{
							var saveBoqHederIds = saveItems.Select(e => e.BoqHeaderFk).Distinct();
							boqItemLogic.Import(saveItems, saveBoqHederIds);

							this.SyncBoqItemCostGroup(sortedList, saveItems, newItem =>
							{
								return sortedList.ElementAt(saveItems.IndexOf(newItem));
							}, contract.ProjectFk);
						}
					}
				}

				#endregion
			}
		}

		/// <summary>
		/// Appends PES items to a PES header based on contract items.
		/// </summary>
		/// <param name="header">The PES header entity to which items will be added</param>
		/// <param name="baseContract">The contract entity from which items are referenced</param>
		/// <param name="itemIds">List of procurement item IDs to be added to the PES</param>
		/// <param name="isCreateNew">If true, creates new PES items; if false, updates existing ones</param>
		public void AppendPesItems(PesHeaderEntity header, ConHeaderEntity baseContract, List<long> itemIds, bool isCreateNew)
		{
			// Validate input parameters
			if (header == null || baseContract == null || itemIds?.Any() != true)
			{
				return;
			}
			// Create filter for retrieving procurement items
			var itemCreateFilter = new ItemCreateFilterOptions
			{
				IsCanceled = false,
				Ids = itemIds,
				ContractId = baseContract.Id,
				PesHeaderId = header.Id,
				IncludeDeliveredCon = true,
				IsConsolidateChange = true
			};

			// Get existing PES items for the header
			var pesItems = new PesItemLogic().GetSearchList(e => e.PesHeaderFk == header.Id).ToList();

			// Get procurement items that should be added to the PES
			var createItems = new PrcItemLookupVLogic().GetItems4Create(itemCreateFilter).ToList();
			var actualCreateItems = new List<PrcItemLookupVEntity>();

			if (!isCreateNew)
			{
				// Update mode: update quantities of existing items or add new ones if they don't exist
				foreach (var item in createItems)
				{
					var pesItem = pesItems.FirstOrDefault(p => p.PrcItemFk == item.Id);
					if (pesItem != null)
					{
						// Update quantity for existing PES item
						pesItem.QuantityContracted = item.Quantity;
					}
					else
					{
						// Item doesn't exist, add it to creation list
						actualCreateItems.Add(item);
					}
				}
			}
			else
			{
				// Create new mode: add all items as new PES items
				actualCreateItems = createItems;
			}

			if (actualCreateItems.Any())
			{
				// Create new PES items for each procurement item
				var index = 0;
				foreach (var item in actualCreateItems)
				{
					CreatePesItemByPrcItem(header, item, index++, null);
				}
				return;
			}

			if (pesItems.Any())
			{
				// Save updated PES items
				new PesItemLogic().Save(pesItems);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="originalEntities"></param>
		/// <param name="newEntities"></param>
		/// <param name="finder"></param>
		/// <param name="projectId"></param>
		private void SyncBoqItemCostGroup(IEnumerable<BoqItemEntity> originalEntities, IEnumerable<BoqItemEntity> newEntities, Func<BoqItemEntity, BoqItemEntity> finder, int? projectId)
		{
			var originalEntityHelper = new CostGroupMigrateHelper<BoqItemEntity>("BOQ_ITEM2COSTGRP", new IdentityDefinition<BoqItemEntity>()
			{
				RootItemGetter = e => e.BoqHeaderFk,
				MainItemGetter = e => e.Id,
				ProjectGetter = e => projectId
			});

			var newEntityHelper = new CostGroupMigrateHelper<BoqItemEntity>("BOQ_ITEM2COSTGRP", new IdentityDefinition<BoqItemEntity>()
			{
				RootItemGetter = e => e.BoqHeaderFk,
				MainItemGetter = e => e.Id,
				ProjectGetter = e => projectId
			});

			originalEntityHelper.Load(originalEntities);

			foreach (var item in newEntities)
			{
				var originalItem = finder(item);
				if (originalItem != null)
				{
					var licCostGroups = originalEntityHelper.GetCostGroupInfo(originalItem);
					if (licCostGroups != null && licCostGroups.Any())
					{
						newEntityHelper.AttachCostGroup(item, licCostGroups);
					}

					var prjCostGroups = originalEntityHelper.GetCostGroupInfo(originalItem, true);
					if (prjCostGroups != null && prjCostGroups.Any())
					{
						newEntityHelper.AttachCostGroup(item, prjCostGroups, true);
					}
				}
			}

			newEntityHelper.SaveChanges();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="contractId"></param>
		/// <returns></returns>
		public PrcConfigurationLookUpVEntity GetConfiguration(int contractId)
		{
			var contract = new ConHeaderLogic().GetSearchList(e => e.Id == contractId).FirstOrDefault();
			if (contract == null)
			{
				return null;
			}

			PrcHeaderEntity prcHeader = new PrcHeaderLogic().GetItemByKey(contract.PrcHeaderFk);
			int configurationFk = prcHeader.ConfigurationFk;

			PrcConfigurationLookUpVEntity entity = null;
			var prcConfigurationHeaderFk = new PrcConfigurationLogic().GetItemByKey(configurationFk).PrcConfigHeaderFk;
			IList<PrcConfigurationLookUpVEntity> prcConfugurationLookupVEntityList = new PrcConfigurationLookUpVLogic().GetSearchList(x => x.PrcConfigHeaderFk == prcConfigurationHeaderFk && x.RubricFk == RubricConstant.PerformanceEntrySheet, true).ToList();
			if (prcConfugurationLookupVEntityList.Any())
			{
				entity = prcConfugurationLookupVEntityList.Where(e => e.IsDefault).OrderBy(e => e.Sorting).FirstOrDefault() ?? prcConfugurationLookupVEntityList.Where(e => e.Sorting > 0).OrderBy(e => e.Sorting).FirstOrDefault();
			}
			return entity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesId"></param>
		public void UpdatePesContractStatus(int pesId)
		{
			var pes = this.GetItemByKey(pesId);
			if (pes != null && pes.ConHeaderFk != null)
			{
				var conHeaderFks = new int[] { pes.ConHeaderFk.Value };
				var itemLogic = new ProcurementItemUpdateLogic<PesItemEntity>();
				itemLogic.UpdateConHeadersStatus(conHeaderFks);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaders"></param>
		/// <returns></returns>
		public IEnumerable<PrcBoqEntity> ListContractBOQs(IEnumerable<int> prcHeaders)
		{
			return new PrcBoqLogic().GetSearchList(e => prcHeaders.Contains(e.PrcHeaderFk));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesHeaderId"></param>
		/// <returns></returns>
		public bool UpdateTaxCodeOfContractItem(int pesHeaderId)
		{
			var result = CheckItemsOrUpdateTaxCode(pesHeaderId, true);
			if (result.ContainsKey("hasBeenUpdated"))
			{
				return result["hasBeenUpdated"];
			}

			return false;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="pesHeaderId"></param>
		/// <param name="updated"></param>
		/// <returns></returns>
		public Dictionary<string, bool> CheckItemsOrUpdateTaxCode(int pesHeaderId, bool updated = false)
		{
			var result = new Dictionary<string, bool>();
			result.Add("hasRelatedItems", false);
			var pesHeader = this.GetItemByKey(pesHeaderId);
			if (pesHeader.ConHeaderFk == null)
			{
				return result;
			}
			var conHeaderLogic = new ConHeaderLogic();
			var prcHeaderLogic = new PrcHeaderLogic();
			var prcBoqLogic = new PrcBoqLogic();
			var boqItemLogic = new BoqItemLogic();
			var pesItemLogic = new PesItemLogic();
			var pesBoqLogic = new PesBoqLogic();
			var prcItemLogic = new PrcItemLogic();
			var conTotalLogic = new ConTotalLogic();
			var boqPriceConditionLogic = new BoqPriceconditionLogic();

			var contract = conHeaderLogic.GetItemByKey(pesHeader.ConHeaderFk.Value);
			var conPrcHeaderId = contract.PrcHeaderFk;
			//contract boq
			var conPrcBoqs = prcBoqLogic.GetSearchList(e => e.PrcHeaderFk == conPrcHeaderId);
			var conBoqHeaderIds = conPrcBoqs.CollectIds(e => e.BoqHeaderFk);
			//pes boq.
			var pesBoqs = pesBoqLogic.GetSearchList(e => e.PesHeaderFk == pesHeader.Id);
			var pesBoqHeaderIds = pesBoqs.CollectIds(e => e.BoqHeaderFk);
			var boqItems = boqItemLogic.GetSearchList(e => conBoqHeaderIds.Contains(e.BoqHeaderFk) || pesBoqHeaderIds.Contains(e.BoqHeaderFk));
			//boqItems of contract
			var conBoqItems = boqItems.Where(e => conBoqHeaderIds.Contains(e.BoqHeaderFk)).ToList();
			var conPrcItems = prcItemLogic.GetSearchList(e => e.PrcHeaderFk == conPrcHeaderId);
			//check
			if (!conBoqItems.Any() && !conPrcItems.Any())
			{
				// has no items(boq Items) on this contract.
				return result;
			}
			//boq items of pes
			var pesBoqItems = boqItems.Where(e => pesBoqHeaderIds.Contains(e.BoqHeaderFk)).ToList();
			var pesItems = pesItemLogic.GetSearchList(e => e.PesHeaderFk == pesHeader.Id);
			var pesPrcItemIds = pesItems.CollectIds(e => e.PrcItemFk);
			//check
			if (!pesBoqItems.Any() && !pesItems.Any())
			{
				// has no related items(boq items) to that contract.
				return result;
			}
			//
			var hasRelatedItems = false;
			var updatedBoqItems = new List<BoqItemEntity>();
			var boqPriceConditionsToSave = new List<BoqPriceconditionEntity>();

			if (conBoqItems.Any() && pesBoqItems.Any())
			{
				foreach (var conBoqItem in conBoqItems)
				{
					var pesBoqItem = pesBoqItems.FirstOrDefault(e => e.BoqItemPrjBoqFk == conBoqItem.BoqItemPrjBoqFk && e.BoqItemPrjItemFk == conBoqItem.BoqItemPrjItemFk);
					if (pesBoqItem == null)
					{
						continue;
					}
					if (pesBoqItem.MdcTaxCodeFk != conBoqItem.MdcTaxCodeFk)
					{
						if (updated)
						{
							conBoqItem.MdcTaxCodeFk = pesBoqItem.MdcTaxCodeFk;
							updatedBoqItems.Add(conBoqItem);
						}

						hasRelatedItems = true;
					}
				}
			}
			var updatedPrcItems = new List<PrcItemEntity>();
			if (conPrcItems.Any() && pesPrcItemIds.Any())
			{
				foreach (var conPrcItem in conPrcItems)
				{
					var pesItem = pesItems.FirstOrDefault(e => e.PrcItemFk == conPrcItem.Id);
					if (pesItem == null)
					{
						continue;
					}
					if (pesItem != null && pesItem.MdcTaxCodeFk != conPrcItem.MdcTaxCodeFk)
					{
						if (updated)
						{
							conPrcItem.MdcTaxCodeFk = pesItem.MdcTaxCodeFk;
							updatedPrcItems.Add(conPrcItem);
						}

						hasRelatedItems = true;
					}
				}
			}
			//save boq items updated.
			if (updatedBoqItems.Any() && updated)
			{
				var toUpdateBoqItems = StructureRootBoqItem(updatedBoqItems, conBoqItems);
				var conExchangeRate = contract.ExchangeRate;
				boqItemLogic.SetCurrentExchangeRateHc2Oc(conExchangeRate);
				foreach (var updatedBoqItem in toUpdateBoqItems)
				{
					var options = new BoqItemCalculationOption();
					options.ExchangeRate = conExchangeRate;
					options.VatGroupId = contract.BpdVatGroupFk;
					var calResults = boqItemLogic.CalculateBoqTreeAndPriceconditions(updatedBoqItem, options);
					boqItemLogic.SaveBoqTree(updatedBoqItem);

					if (calResults != null && calResults.BoqPriceconditionChanged != null)
					{
						boqPriceConditionsToSave.AddRange(calResults.BoqPriceconditionChanged);
					}
				}
			}
			//save prcitems updated.
			if (updatedPrcItems.Any() && updated)
			{
				var isCalculateOverGross = new BasicsCompanyLogic().IsCalculateOverGross();
				var vatEvalutor = new VatEvaluator();
				foreach (var item in updatedPrcItems)
				{
					var vatEntity = vatEvalutor.EvaluateVat(item.MdcTaxCodeFk.Value, contract.BpdVatGroupFk);
					prcItemLogic.RecalculatePrcItemAfterVatPrecentChanged(item, vatEntity.Percent, isCalculateOverGross);
				}
				prcItemLogic.Save(updatedPrcItems);
			}
			if ((updatedBoqItems.Any() || updatedPrcItems.Any()) && updated)
			{
				var totals = conTotalLogic.GetSearchList(x => x.HeaderFk == contract.Id);
				conTotalLogic.RecalculateTotalsFromHeader(contract, totals);
				conTotalLogic.Save(totals);
			}

			if (boqPriceConditionsToSave.Any())
			{
				boqPriceConditionLogic.SaveEntities(boqPriceConditionsToSave);
			}

			if (updated)
			{
				result.Add("hasBeenUpdated", updatedBoqItems.Any() || updatedPrcItems.Any());
			}

			result["hasRelatedItems"] = hasRelatedItems;
			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="updatedBoqItems"></param>
		/// <param name="boqItems"></param>
		/// <returns></returns>
		public IEnumerable<BoqItemEntity> StructureRootBoqItem(IEnumerable<BoqItemEntity> updatedBoqItems, IEnumerable<BoqItemEntity> boqItems)
		{
			var result = new List<BoqItemEntity>();
			var boqItemGroups = updatedBoqItems.GroupBy(e => e.BoqHeaderFk).ToList();
			foreach (var group in boqItemGroups)
			{
				var boqHeaderFk = group.Key;
				var boqItemByGrp = group.ToList();
				var notUpdatedBoqItems = boqItems.Where(e => e.BoqHeaderFk == boqHeaderFk).ToList();
				var boqItemIds = boqItemByGrp.CollectIds(e => e.Id);
				notUpdatedBoqItems.RemoveAll(e => boqItemIds.Contains(e.Id) && e.BoqHeaderFk == boqHeaderFk);
				notUpdatedBoqItems = notUpdatedBoqItems.Concat(boqItemByGrp).ToList();
				//BoqLineTypeFk = 103 is a root.
				var root = notUpdatedBoqItems.FirstOrDefault(e => e.BoqLineTypeFk == 103);

				notUpdatedBoqItems.Remove(root);
				foreach (var item in notUpdatedBoqItems)
				{
					var itemId = item.Id;
					var children = notUpdatedBoqItems.Where(e => e.BoqItemFk == itemId).ToList();
					item.BoqItemChildren = children;
				}

				var rootChildren = notUpdatedBoqItems.Where(e => e.BoqItemFk == root.Id).ToList();
				root.BoqItemChildren = rootChildren;

				result.Add(root);
			}

			return result;
		}

		#region IRecalculateProcurementFacade

		/// <summary>
		///
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		bool IRecalculateProcurementFacade.Recalculate(int id)
		{
			var result = true;
			var header = GetItemByKey(id);
			using (TransactionScope transaction = TransactionScopeFactory.Create())
			{
				result &= new PesItemLogic().RecalculatePesItems(header);
				result &= new PesBoqLogic().RecalculatePesBoqs(header);
				CalculateTotalAndVat(id);
				transaction.Complete();
			}
			return result;
		}

		#endregion

		/// <summary>
		///
		/// </summary>
		/// <param name="pesHeaderId"></param>
		/// <returns></returns>
		public IEnumerable<PesHeaderEntity> GetPreviousPesHeaders(int pesHeaderId)
		{
			var header = new PesHeaderLogic().GetItemByKey(pesHeaderId);
			if (header == null || !header.ConHeaderFk.HasValue)
			{
				return new List<PesHeaderEntity>();
			}

			var tempContractId = header.ConHeaderFk.Value;
			var baseNCOContractIds = new ConHeaderLogic().GetContractIdSeries(tempContractId);

			return new PesHeaderLogic().GetSearchList(e => e.Id < pesHeaderId && e.ConHeaderFk.HasValue && baseNCOContractIds.Contains(e.ConHeaderFk.Value));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerId"></param>
		/// <returns></returns>
		public bool RecalculateTotalsByHeaderId(int headerId)
		{
			RecalculateTotalsByHeaderIdAfterRecalculatingBoq(headerId);
			return true;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerId"></param>
		public void RecalculateTotalsByHeaderIdAfterRecalculatingBoq(int headerId)
		{
			CalculateTotalAndVat(headerId);
		}

		/// <summary>
		/// Create PesHeader By Contract
		/// </summary>
		/// <param name="contractFk">contractFk</param>
		/// <param name="isIncludeNoConItem">is Include No Contract Item</param>
		public IPesHeaderEntity CreatePesByContract(int contractFk, bool isIncludeNoConItem)
		{
			var entity = CreateCloneHeader(-1);
			entity.ConHeaderFk = contractFk;
			entity = ConHeaderFkValidate(entity, contractFk);
			entity.RubricCategoryFk = CommonBillingSchemaLogic.GetRubricCategoryByConfiguration(entity.PrcConfigurationFk);
			entity.Code = GenerateCode(entity);
			UpdateEntity(entity);
			CopyCharacteristicData(entity, true);

			if (entity.ConHeaderFk != null)
			{
				CreatePrcBoq((int)entity.ConHeaderFk, entity.Id, isIncludeNoConItem);
			}
			//pesItem
			CreatePesItems(entity, isIncludeNoConItem);
			return entity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="isDelete"></param>
		private static void CopyCharacteristicData(PesHeaderEntity entity, bool isDelete)
		{
			var param = new PrcCopyCharacteristicDataParam
			{
				SourceHeaderId = entity.ConHeaderFk,
				TargetHeaderId = entity.Id,
				SourceSectionId = CharacteristicSectionConstants.Contract,
				TargetSectionId = CharacteristicSectionConstants.ProcurementPes,
				PrcStructureId = entity.PrcStructureFk,
				PrcConfigurationId = entity.PrcConfigurationFk
			};

			if (isDelete)
			{
				PrcCharacteristicLogic.DeleteCharacteristicData(entity.Id, CharacteristicSectionConstants.ProcurementPes);
			}
			PrcCharacteristicLogic.SaveCopyCharacteristicData(param);

			if (isDelete)
			{
				PrcCharacteristicLogic.DeleteCharacteristicData(entity.Id, CharacteristicSectionConstants.PesCharacteristics2);
			}
			param.SourceSectionId = CharacteristicSectionConstants.ContractCharacteristics2;
			param.TargetSectionId = CharacteristicSectionConstants.PesCharacteristics2;
			param.IgnoreValidate = false;
			param.StructureSectionConstant = CharacteristicSectionConstants.PrcStructureCharacteristics2;
			param.ConfigurationSectionConstant = CharacteristicSectionConstants.PrcConfigurationCharacteristics2;
			PrcCharacteristicLogic.SaveCopyCharacteristicData(param);
		}

		/// <summary>
		///CreatePesByContractId
		/// </summary>
		/// <param name="contractId"></param>
		/// <param name="isIncludeNonContractedItem"></param>
		/// <param name="isContinueCreatePesAgain">PES(s) already existed and cover all contracted item(s), do you still want to create new PES?</param>
		/// <param name="additionInfo"></param>
		/// <returns></returns>
		public IPesHeaderEntity CreatePesByContractId(int contractId, bool isIncludeNonContractedItem, bool isContinueCreatePesAgain, IAdditionalInfo4CreatePes additionInfo)
		{
			return this.CreateOrUpdatePesByContract(contractId, isIncludeNonContractedItem, isContinueCreatePesAgain, additionInfo);
		}

		/// <summary>
		/// Creates or updates a PES based on contract information with extended functionality for reusing existing PES
		/// </summary>
		/// <param name="contractId">Contract ID to create PES from</param>
		/// <param name="isIncludeNonContractedItem">Flag to include non-contracted items</param>
		/// <param name="isContinueCreatePesAgain">Flag to continue creating PES even if there is already one for the contract</param>
		/// <param name="additionInfo">Additional information for PES creation</param>
		/// <param name="pesHeaderId">Optional existing PES header ID to update instead of creating new</param>
		/// <param name="appendBoqsFunc">Optional function to append BOQs to existing PES</param>
		/// <param name="appendItemsFunc">Optional function to append items to existing PES</param>
		/// <returns>Created or updated PES header entity</returns>
		public PesHeaderEntity CreateOrUpdatePesByContract(
			 int contractId,
			 bool isIncludeNonContractedItem,
			 bool isContinueCreatePesAgain,
			 IAdditionalInfo4CreatePes additionInfo,
			 int? pesHeaderId = null,
			 Action<int, int, int, bool> appendBoqsFunc = null,
			 Action<PesHeaderEntity, ConHeaderEntity, List<long>, bool> appendItemsFunc = null)
		{
			var contractLogic = new ConHeaderLogic();
			var contract = contractLogic.GetSearchList(e => e.Id == contractId).FirstOrDefault();
			if (contract == null)
			{
				throw new BusinessLayerException($"Contract is not found with {nameof(contractId)}.");
			}

			var contractPrcHeader = new PrcHeaderLogic().GetSearchList(e => e.Id == contract.PrcHeaderFk).FirstOrDefault();

			// Check contract status
			var isValidContractStatus = contractPrcHeader != null && contractLogic.CheckContractsStatusValid(contractId, contractPrcHeader.ConfigurationFk);

			if (!isValidContractStatus)
			{
				throw new BusinessLayerException("The status of Contract is not valid to create PES.");
			}

			// Get contract configuration header is consolidate change
			var isConsolidateChange = false;
			var configuration = new PrcConfigurationLogic().GetSearchList(e => e.Id == contractPrcHeader.ConfigurationFk).FirstOrDefault();
			if (configuration != null)
			{
				var configHeader = new PrcConfigHeaderLogic().GetSearchList(e => e.Id == configuration.PrcConfigHeaderFk).FirstOrDefault();
				if (configHeader != null)
				{
					isConsolidateChange = configHeader.IsConsolidateChange;
				}
			}

			// Get base and change order contracts
			var contractIds = new List<int>() { contractId };
			if (contract.ConHeaderFk.HasValue)
			{
				contractIds.Add(contract.ConHeaderFk.Value);
			}

			var expression = contractLogic.GetValidBaseNChangeOrdersExpression(isConsolidateChange, true);
			var contracts = contractLogic.GetBaseNChangeOrdersByIds(contractIds, expression, isConsolidateChange);
			var baseContract = contracts.FirstOrDefault(e => !e.ProjectChangeFk.HasValue && !e.ConHeaderFk.HasValue);
			var changeOrderContracts = contracts.Where(e => e.ProjectChangeFk.HasValue && e.ConHeaderFk.HasValue).ToList();
			var callOfContracts = contracts.Where(e => !e.ProjectChangeFk.HasValue && e.ConHeaderFk.HasValue).ToList();
			contract = contracts.FirstOrDefault(e => e.Id == contract.Id);

			var toBePesContract = contract;
			if (isConsolidateChange)
			{
				toBePesContract = baseContract;
			}

			if (toBePesContract == null)
			{
				throw new BusinessLayerException("The contract is not valid. Maybe its Base Contract is not supported to create PES.");
			}

			// Check whether it is framework contract
			if (toBePesContract.IsFramework)
			{
				throw new BusinessLayerException(String.Format("Framework contract ({0}, {1}) is not supported!", toBePesContract.Id, toBePesContract.Code));
			}

			// Get contract PRC structure
			var defaultPrcStructureFk = toBePesContract.PrcHeaderEntity.StructureFk;

			if (!defaultPrcStructureFk.HasValue)
			{
				if (contract.PrcHeaderEntity.StructureFk.HasValue)
				{
					defaultPrcStructureFk = contract.PrcHeaderEntity.StructureFk;
				}
				else
				{
					var contractHasStructureFk = contracts.FirstOrDefault(e => e.PrcHeaderEntity.StructureFk.HasValue);
					if (contractHasStructureFk != null)
					{
						defaultPrcStructureFk = contractHasStructureFk.PrcHeaderEntity.StructureFk;
					}
				}
			}

			var prcBoqsToCheck = GetPrcBoq(toBePesContract.Id, null, isConsolidateChange).ToList();
			var prcBoqsWithoutControllingUnitFk = new List<PrcBoqLookupVEntity>();
			foreach (var prcBoq in prcBoqsToCheck)
			{
				if (!prcBoq.ControllingUnitFk.HasValue)
				{
					prcBoqsWithoutControllingUnitFk.Add(prcBoq);
				}
			}

			var itemCreateFilter = new ItemCreateFilterOptions
			{
				IsCanceled = false,
				ContractId = toBePesContract.Id,
				IncludeDeliveredCon = true,
				IsConsolidateChange = isConsolidateChange
			};

			var prcItemsToCheck = new PrcItemLookupVLogic().GetItems4Create(itemCreateFilter).ToList();
			var prcItemsWithoutStructureFk = new List<PrcItemLookupVEntity>();
			var prcItemsWithoutControllingUnitFk = new List<PrcItemLookupVEntity>();

			var defaultPrcStructureFk4Boq = defaultPrcStructureFk;
			if (prcBoqsToCheck.Any() && !defaultPrcStructureFk4Boq.HasValue)
			{
				var prcStructure4Boq = new PrcStructureLogic().GetDefaultStructure4PesBoq();
				if (prcStructure4Boq != null)
				{
					defaultPrcStructureFk4Boq = prcStructure4Boq.Id;
				}
			}

			foreach (var item in prcItemsToCheck)
			{
				if (!item.PrcStructureFk.HasValue)
				{
					prcItemsWithoutStructureFk.Add(item);
				}
				if (!item.ControllingUnitFk.HasValue)
				{
					prcItemsWithoutControllingUnitFk.Add(item);
				}
			}

			// Check structure
			if (prcBoqsToCheck.Any() && !defaultPrcStructureFk4Boq.HasValue)
			{
				throw new BusinessLayerException(String.Format("Contract({0}, {1}) cannot create PES as no structure can be set to Pes Boq!", toBePesContract.Id, toBePesContract.Code));
			}

			if (prcItemsWithoutStructureFk.Any() && !defaultPrcStructureFk.HasValue)
			{
				throw new BusinessLayerException(String.Format("Contract({0}, {1}) cannot create PES as no structure can be set to Pes Item!", toBePesContract.Id, toBePesContract.Code));
			}

			// Check controlling unit
			if ((prcBoqsWithoutControllingUnitFk.Any() || prcItemsWithoutControllingUnitFk.Any()) && !toBePesContract.ControllingUnitFk.HasValue)
			{
				throw new BusinessLayerException(String.Format("Contract({0}, {1}) cannot create PES as no controlling unit can be set to Pes Item or Boq!", toBePesContract.Id, toBePesContract.Code));
			}

			// get pes
			if (!isContinueCreatePesAgain)
			{
				var pesCreated = this.GetListByContract(toBePesContract.Id);

				// get new change items
				var boqItems = this.GetDiffBoq(toBePesContract.Id).BoqItems;
				var prcItems = this.GetDiffItem(toBePesContract.Id);
				if (pesCreated != null && pesCreated.Any() && !boqItems.Any() && !prcItems.Any())
				{
					return null;
				}
			}

			// Get default clerk if no clerk of contract
			if (!toBePesContract.ClerkPrcFk.HasValue || !toBePesContract.ClerkReqFk.HasValue)
			{
				var contractInfo = new ContractInfo()
				{
					ClerkPrcFk = toBePesContract.ClerkPrcFk,
					ClerkReqFk = toBePesContract.ClerkReqFk,
					Id = toBePesContract.Id,
					ProjectFk = toBePesContract.ProjectFk,
					PrcStructureFk = toBePesContract.PrcHeaderEntity.StructureFk ?? 0
				};
				var contractInfoUpdated = contractLogic.UpdateDefaultClerkIds4ContractInfo(new List<ContractInfo>() { contractInfo }).FirstOrDefault();
				if (contractInfoUpdated != null)
				{
					if (!toBePesContract.ClerkPrcFk.HasValue)
					{
						toBePesContract.ClerkPrcFk = contractInfoUpdated.ClerkPrcFk;
					}
					if (!toBePesContract.ClerkReqFk.HasValue)
					{
						toBePesContract.ClerkReqFk = contractInfoUpdated.ClerkReqFk;
					}
				}
			}

			if (!toBePesContract.ClerkPrcFk.HasValue)
			{
				throw new BusinessLayerException(String.Format("Cannot create PES for contract({0}, {1}) has no clerks found.", toBePesContract.Id, toBePesContract.Code));
			}

			PesHeaderEntity entity;

			// Use existing PES header or create new one based on pesHeaderId parameter
			if (pesHeaderId.HasValue)
			{
				// Use existing PES header
				entity = this.GetItemByKey(pesHeaderId.Value);
				if (entity == null)
				{
					throw new BusinessLayerException($"PES header with ID {pesHeaderId.Value} not found.");
				}
			}
			else
			{
				// Create new PES header
				entity = CreateCloneHeader(-1, toBePesContract.BasCurrencyFk);
				var exchangeRate = entity.ExchangeRate;
				entity = this.ConHeaderFkValidate(entity, toBePesContract.Id);
				entity.ExchangeRate = exchangeRate;
				entity.ConHeaderFk = toBePesContract.Id;
				entity.PrcStructureFk = defaultPrcStructureFk;
				if (entity.ClerkPrcFk == 0)
				{
					entity.ClerkPrcFk = toBePesContract.ClerkPrcFk.Value;
				}
				if (!entity.ClerkReqFk.HasValue)
				{
					entity.ClerkReqFk = toBePesContract.ClerkReqFk;
				}
				//entity.Code = GenerateCode(entity);
				//In UpdateNew(pesComplete) the code is automatically generated, there is no need to generate the auto-generated code here
				if (additionInfo != null && additionInfo.DispatchRecordDateEffective.HasValue)
				{
					entity.DateEffective = additionInfo.DispatchRecordDateEffective.Value;
				}

				// RubricCategory
				entity.RubricCategoryFk = CommonBillingSchemaLogic.GetRubricCategoryByConfiguration(entity.PrcConfigurationFk);
				entity.ConfigHeaderIsConsolidateChange = IsConsolidateChange(entity.PrcConfigurationFk, null);

				var pesComplete = new PesCompleteNewEntity()
				{
					MainItemId = entity.Id,
					PesHeader = entity
				};

				// Create billing schema
				if (entity.BillingSchemaFk.HasValue)
				{
					var billingSchemaLogic = new CommonBillingSchemaLogic("procurement.pes.billingschmema");
					pesComplete.BillingSchemaToSave = billingSchemaLogic.Create(entity.Id, entity.BillingSchemaFk.Value, entity.RubricCategoryFk);
				}

				this.UpdateNew(pesComplete);

				// Create and save characteristics
				CopyCharacteristicData(entity, false);
			}

			// Handle BOQ creation/appending
			if (appendBoqsFunc != null)
			{
				var autoComplete = pesHeaderId == null;
				appendBoqsFunc(toBePesContract.Id, entity.Id, entity.ControllingUnitFk ?? toBePesContract.ControllingUnitFk ?? -1, autoComplete);
			}
			else
			{
				// Create BOQs using default logic
				var prcBoqs = GetPrcBoq(toBePesContract.Id, entity.Id, isConsolidateChange).ToList();
				foreach (var prcBoq in prcBoqs)
				{
					var pesBoq = new PesBoqEntity()
					{
						PrcBoqFk = prcBoq.Id,
						ConHeaderFk = toBePesContract.Id,
						PackageFk = toBePesContract.PackageFk,
						MdcTaxCodeFk = toBePesContract.TaxCodeFk,
						PrcStructureFk = defaultPrcStructureFk4Boq.Value,
						ControllingUnitFk = GetControllingUnitFk(prcBoq, entity, toBePesContract)
					};
					new PesBoqLogic().Create(entity.Id, pesBoq, prcBoq.BoqItemPrjBoqFk, isIncludeNotContractedItem: isIncludeNonContractedItem);
				}
			}

			// Handle items creation/appending
			if (appendItemsFunc != null)
			{
				var isCreateNew = pesHeaderId == null;
				var nwChangeItemIds = GetBoqPrcItemEntities(toBePesContract.Id).CollectIds(e => e.Id).ToList();
				appendItemsFunc(entity, toBePesContract, nwChangeItemIds, isCreateNew);
			}
			else
			{
				// Create items using default logic
				CreatePesItems(entity, isIncludeNonContractedItem, isConsolidateChange, true, additionInfo);
			}

			return CalculateTotalAndVat(entity.Id);
		}

		/// <summary>
		/// Gets the appropriate controlling unit FK based on priority order
		/// </summary>
		private int? GetControllingUnitFk(PrcBoqLookupVEntity prcBoq, IPesHeaderEntity pesHeader, ConHeaderEntity contract)
		{
			// Priority 1: Use BOQ's controlling unit if available
			if (prcBoq.ControllingUnitFk.HasValue)
			{
				return prcBoq.ControllingUnitFk.Value;
			}

			// Priority 2: Use PES header's controlling unit if available
			if (pesHeader.ControllingUnitFk.HasValue)
			{
				return pesHeader.ControllingUnitFk.Value;
			}

			// Priority 3: Use contract's controlling unit if available
			if (contract.ControllingUnitFk.HasValue)
			{
				return contract.ControllingUnitFk.Value;
			}

			// No controlling unit found
			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="request"></param>
		/// <param name="response"></param>
		/// <returns></returns>
		protected override SearchSpecification<PesHeaderEntity, int> GetListSearchContext(FilterRequest request, out FilterResponse response)
		{
			var context = base.GetListSearchContext(request, out response);

			context.UseCurrentClientPredicate = e => e.CompanyFk == BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			if (request.ProjectContextId != null)
			{
				context.CustomPredicate = (e => e.ProjectFk == request.ProjectContextId);
			}

			// Portal Business Partner Filter  rei@21.3.2019
			//# defect 137435,remove logic about further external BPs
			if (Context.Portal.HasValue && Context.Portal.Value && Context.BusinessPartnerId.HasValue)
			{
				var contactLogic = Injector.Get<IContactLogic>();
				var bpIds = contactLogic.GetBpIds4PortalLoginUser();
				Expression<Func<PesHeaderEntity, bool>> filter = e => bpIds.Contains(e.BusinessPartnerFk);

				if (context.CustomPredicate == null)
				{
					context.CustomPredicate = filter;
				}
				else
				{
					context.CustomPredicate = System.Linq.Dynamic.PredicateBuilder.And<PesHeaderEntity>(context.CustomPredicate, filter);
				}
			}

			if (PrcClerkLogic.IsFilterByLoginClerk())
			{
				var loginClerk = this.ClerkInfo.GetLoginClerkInfo();

				if (loginClerk != null)
				{
					var clerkIds = PrcClerkLogic.AttachClerkGroup(loginClerk.Id);
					Expression<Func<PesHeaderEntity, bool>> clerkFilter = e => e.ClerkPrcFk > 0 || clerkIds.Contains(e.ClerkPrcFk);

					if (context.CustomPredicate == null)
					{
						context.CustomPredicate = clerkFilter;
					}
					else
					{
						context.CustomPredicate = System.Linq.Dynamic.PredicateBuilder.And<PesHeaderEntity>(context.CustomPredicate, clerkFilter);
					}
				}
			}

			return context;
		}

		/// <summary>
		/// Evaluates grouping filter.
		/// </summary>
		/// <param name="filterIn"></param>
		/// <param name="dbContext"></param>
		/// <param name="query"></param>
		/// <returns></returns>
		protected override IQueryable<PesHeaderEntity> EvaluateGroupingFilter(FilterRequest filterIn, RVPBizComp.DbContext dbContext, IQueryable<PesHeaderEntity> query)
		{
			var queryResult = new PesStatusLogic().GetUserReadableStatusQuery(dbContext);
			query = query.Where(e => (from r in queryResult where (r.Id == e.PesStatusFk) select r).Any());

			var objectSelection = filterIn.GetFurtherFiltersValue("MDL_OBJECT_SELECTION");
			if (objectSelection != null)
			{
				Expression<Func<PesHeader2MdlObjectVEntity, Boolean>> modelFilterExpression = null;

				var selectedObjectIds = ModelElementIdCompressor.ParseCompressedString(objectSelection).ToArray();
				if (selectedObjectIds.Length > 0)
				{
					modelFilterExpression =
						selectedObjectIds.CreateEntityFilterExpression<PesHeader2MdlObjectVEntity>(h2O => h2O.ObjectFk);
				}
				else
				{
					var selectedModelIdText = filterIn.GetFurtherFiltersValue("MDL_MODEL");
					if (selectedModelIdText != null)
					{
						Int32 selectedModelId;
						if (Int32.TryParse(selectedModelIdText, out selectedModelId))
						{
							var subModelIds = BusinessApplication.BusinessEnvironment.GetExportedValue<ISubModelLogic>().GetSubModelIdsForModel(selectedModelId);
							modelFilterExpression = h2O => subModelIds.Contains(h2O.ModelFk);
						}
					}
				}

				if (modelFilterExpression != null)
				{
					query =
						query.Where(
							e => dbContext.Set<PesHeader2MdlObjectVEntity>().Where(h2O => h2O.Id == e.Id).Any(modelFilterExpression));
				}
			}

			return query;
		}

		private IEnumerable<PesBoqEntity> CreatePrcBoq(int conHeaderFk, int pesHeaderId, bool isIncludeNoConItem)
		{
			var prcBoqs = GetPrcBoq(conHeaderFk, pesHeaderId).ToList();
			var pesBoqLogic = new PesBoqLogic();
			var result = new List<PesBoqEntity>();
			var conHeaderLogic = new ConHeaderLogic();
			var contract = conHeaderLogic.GetItemByKey(conHeaderFk);
			prcBoqs.ForEach(boq =>
			{
				var pesBoqEntity = new PesBoqEntity()
				{
					PrcBoqFk = boq.Id,
					ConHeaderFk = conHeaderFk,
					PackageFk = contract.PackageFk,
					MdcTaxCodeFk = contract.TaxCodeFk,
					ControllingUnitFk = boq.ControllingUnitFk
				};
				if (contract.PrcHeaderEntity != null && contract.PrcHeaderEntity.StructureFk.HasValue)
				{
					pesBoqEntity.PrcStructureFk = contract.PrcHeaderEntity.StructureFk.Value;
				}
				if (pesBoqEntity.ControllingUnitFk == null && contract.ControllingUnitFk != null)
				{
					pesBoqEntity.ControllingUnitFk = contract.ControllingUnitFk;
				}
				var newItems = pesBoqLogic.Create(pesHeaderId, pesBoqEntity, boq.BoqItemPrjBoqFk, isIncludeNotContractedItem: isIncludeNoConItem);
				result.AddRange(newItems);
			});
			return result;
		}

		private IEnumerable<PrcBoqLookupVEntity> GetPrcBoq(int contractId, int? headerId = null, bool isConsolidateChange = true)
		{
			var searchRequest = new List<Basics.Common.Core.Final.LookupSearchRequest>();
			var param = new Dictionary<string, object>();
			param.Add("isCanceled", false);
			param.Add("contractId", contractId);
			param.Add("isConsolidateChange", isConsolidateChange);
			if (headerId != null)
			{
				param.Add("pesHeaderId", headerId);
			}
			searchRequest.Add(new Basics.Common.Core.Final.LookupSearchRequest
			{
				AdditionalParameters = param,
				FilterKey = "pes-boq-con-merge-boq-filter",
				PageState = new Basics.Common.Core.Final.LookupSearchRequest.PageInfo { PageNumber = 0, PageSize = 10000 }
			});
			var prcBoqs = new PrcBoqLookupVLogic().GetMergedPrcBoqsByLookupRquests(searchRequest).ToList();
			return prcBoqs;
		}

		private void CreatePesItems(PesHeaderEntity entity, bool isIncludeNoConItem, bool isConsolidateChange = true, bool ignoreDiffItem = false, IAdditionalInfo4CreatePes additionalInfo = null, bool? includeDeliveredCon = null)
		{
			var itemCreateFilter = new ItemCreateFilterOptions
			{
				IsCanceled = false,
				ContractId = entity.ConHeaderFk,
				PesHeaderId = entity.Id,
				IsConsolidateChange = isConsolidateChange,
				IncludeDeliveredCon = includeDeliveredCon ?? false
			};

			var prcItems = new PrcItemLookupVLogic().GetItems4Create(itemCreateFilter).ToList();
			if (!prcItems.Any())
			{
				return;
			}

			Dictionary<long, decimal> mergeItemsDict = null;
			if (!ignoreDiffItem)
			{
				var conHeaderId = entity.ConHeaderFk ?? 0;
				var pesItemIds = this.GetDiffItem(conHeaderId)?.CollectIds(e => e.Id) ?? [];
				var changeItems = this.GetChangeItems(conHeaderId, pesItemIds) ?? [];

				mergeItemsDict = this.GetMergeItems(pesItemIds)?
					.ToDictionary(
						e => e.Id,
						e => changeItems.Where(x => x.PrcItemFk == e.PrcItemFk).Sum(s => s.Quantity)
					 ) ?? [];
			}

			var index = 0;
			foreach (var item in prcItems)
			{
				if (mergeItemsDict != null && mergeItemsDict.TryGetValue(item.Id, out var quantity))
				{
					item.Quantity = quantity;
				}

				CreatePesItemByPrcItem(entity, item, index++, additionalInfo);
			}

			if (isIncludeNoConItem)
			{
				new PesItemLogic().CreateNoContractedItems(entity.Id, entity.ConHeaderFk);
			}
		}

		private void CreatePesItemByPrcItem(PesHeaderEntity entity, PrcItemLookupVEntity prcItem, int maxNo, IAdditionalInfo4CreatePes additionInfo = null)
		{
			var pesItemLogic = new PesItemLogic();
			var prcPriceConditionLogic = new PrcItemPriceConditionNewLogic();
			var createItem = pesItemLogic.Create(entity.Id, maxNo, entity.ConHeaderFk, entity.ProjectFk, entity.PackageFk, entity.PrcStructureFk, entity.ControllingUnitFk, entity.PrcConfigurationFk);
			var nonContractedItemInstanceIds = new List<int>() { createItem.InstanceId };
			createItem.InstanceId = prcItem.InstanceId;
			createItem.PrcItemFk = prcItem.Id;
			createItem.QuantityContracted = prcItem.Quantity;
			createItem.PriceGrossOc = prcItem.PriceGrossOc;
			createItem.PriceGross = prcItem.PriceGross;
			createItem.PriceOc = prcItem.PriceOc;
			createItem.Price = prcItem.Price;
			createItem.PriceExtraOc = prcItem.PriceExtraOc;
			createItem.PriceExtra = prcItem.PriceExtra;
			createItem.TotalPriceOc = prcItem.TotalPriceOc;
			createItem.TotalPrice = prcItem.TotalPrice;
			createItem.TotalPriceGrossOc = prcItem.TotalPriceGrossOc;
			createItem.TotalPriceGross = prcItem.TotalPriceGross;
			createItem.PrcItemFactorPriceUnit = prcItem.FactorPriceUnit;
			createItem.MdcTaxCodeFk = prcItem.TaxCodeFk;
			createItem.UomFk = prcItem.BasUomFk;
			if (additionInfo != null && additionInfo.DispatchHeaderPerformingControllingUnit.HasValue)
			{
				createItem.ControllingUnitFk = additionInfo.DispatchHeaderPerformingControllingUnit.Value;
			}
			else
			{
				createItem.ControllingUnitFk = prcItem.ControllingUnitFk;
				if (createItem.ControllingUnitFk == null)
				{
					createItem.ControllingUnitFk = entity.ControllingUnitFk;
				}
			}
			createItem.PrcStructureFk = prcItem.PrcStructureFk;
			createItem.PrcItemStatusFk = prcItem.PrcItemStatusFk;
			createItem.MdcMaterialFk = prcItem.MdcMaterialFk;
			createItem.PrcPriceConditionFk = prcItem.PrcPriceConditionFk;
			createItem.MaterialExternalCode = prcItem.PrcItemMaterialExternalCode;
			createItem.Discount = prcItem.Discount;
			createItem.DiscountAbsolute = prcItem.DiscountAbsolute;
			createItem.DiscountAbsoluteOc = prcItem.DiscountAbsoluteOc;
			createItem.DiscountAbsoluteGross = prcItem.DiscountAbsoluteGross;
			createItem.DiscountAbsoluteGrossOc = prcItem.DiscountAbsoluteGrossOc;
			createItem.Description1 = prcItem.PrcItemDescription;
			createItem.Description2 = prcItem.PrcItemDescription2;
			createItem.UserDefined1 = prcItem.UserDefined1;
			createItem.UserDefined2 = prcItem.UserDefined2;
			createItem.UserDefined3 = prcItem.UserDefined3;
			createItem.UserDefined4 = prcItem.UserDefined4;
			createItem.UserDefined5 = prcItem.UserDefined5;
			createItem.ExternalCode = prcItem.ExternalCode;
			createItem.AlternativeUomFk = prcItem.AlternativeUomFk;

			var prcItemLogic = new PrcItemLogic();
			var prcItemEntity = prcItemLogic.GetSearchList(e => e.Id == prcItem.Id).FirstOrDefault();
			var controllingSets = new List<IControllingGrpSetEntity>();
			var controllingSetDtls = new List<IControllingGrpSetDTLEntity>();
			if (prcItemEntity != null)
			{
				if (prcItemEntity.ControllinggrpsetFk != null)
				{
					var newControllingSet = Injector.Get<IControllingGrpSetLogic>().CopyItemById(prcItemEntity.ControllinggrpsetFk.Value);
					var controllingSetDtl = Injector.Get<IControllingGrpSetDTLELogic>().CopyBysetFk(prcItemEntity.ControllinggrpsetFk.Value, createItem.Id);
					if (controllingSetDtl != null)
					{
						controllingSetDtls.AddRange(controllingSetDtl);
					}
					if (newControllingSet != null)
					{
						if (controllingSetDtl != null)
						{
							controllingSetDtl.ExtendEach(e => e.ControllinggrpsetFk = newControllingSet.Id);
						}
						controllingSets.Add(newControllingSet);
						createItem.ControllinggrpsetFk = newControllingSet.Id;
					}
				}
				if (prcItemEntity.BasBlobsSpecificationFk != null)
				{
					createItem.BasBlobsSpecificationFk = new BlobLogic().CopyBlob((int)prcItemEntity.BasBlobsSpecificationFk);
				}
			}
			List<PesItemPriceConditionEntity> conditionList = new List<PesItemPriceConditionEntity>();
			var itemPrcPriceConditions = prcPriceConditionLogic.GetSearchList(e => e.PrcItemFk == prcItem.Id);
			if (itemPrcPriceConditions.Any())
			{
				var conditionEnumerator = SequenceManager.GetNextList("PES_ITEMPRICECONDITION", itemPrcPriceConditions.Count()).GetEnumerator();
				conditionEnumerator.MoveNext();
				foreach (var p in itemPrcPriceConditions)
				{
					var condition = new PesItemPriceConditionEntity
					{
						Id = conditionEnumerator.Current,
						PesItemFk = createItem.Id,
						PrcPriceConditionTypeFk = p.PrcPriceConditionTypeFk,
						Description = p.Description,
						Value = p.Value,
						Total = p.Total,
						TotalOc = p.TotalOc,
						IsPriceComponent = p.IsPriceComponent,
						IsActivated = p.IsActivated,
						Date = p.Date,
						Version = 0
					};
					conditionList.Add(condition);
					conditionEnumerator.MoveNext();
				}
			}

			if (additionInfo != null && additionInfo.DispatchRecordQuantity.HasValue)
			{
				createItem.Quantity = additionInfo.DispatchRecordQuantity.Value;
			}
			var isConsolidateChange = this.IsConsolidateChange(entity.PrcConfigurationFk, null);
			var relatedPrcItems = prcItemLogic.GetRelatedPrcItems(new List<PrcItemEntity>() { prcItemEntity }, true);
			createItem.QuantityDelivered = pesItemLogic.CalculateDeliveredQuantity(entity.Id, createItem.PrcItemFk.Value, isConsolidateChange, relatedPrcItems.Select(e => (IPrcItemEntity)e).ToList()) + createItem.Quantity;
			createItem.QuantityRemaining = createItem.QuantityContracted - createItem.QuantityDelivered;

			pesItemLogic.RecalculatePesItems(entity, new List<PesItemEntity>(), new List<PesItemEntity>() { createItem }, conditionList, false);

			createItem.TotalDelivered = pesItemLogic.CalculateDeliveredTotal(entity.Id, createItem.PrcItemFk.Value, isConsolidateChange, relatedPrcItems) + createItem.Total;
			createItem.TotalOcDelivered = pesItemLogic.CalculateDeliveredTotalOc(entity.Id, createItem.PrcItemFk.Value, isConsolidateChange, relatedPrcItems) + createItem.TotalOc;

			IEnumerable<PrcItemLookupVEntity> prcItemList = new List<PrcItemLookupVEntity>();

			var prcItemIds = new List<long>();
			var param = new PesItemsCreateParameter
			{
				MainItemId = entity.Id,
				ExchangeRate = entity.ExchangeRate,
				ConHeaderFk = createItem.ConHeaderFk,
				ProjectFk = createItem.ProjectFk,
				PackageFk = createItem.PrcPackageFk,
				PrcItemIds = prcItemIds,
				MaxItemNo = createItem.ItemNo,
				ControllingUnitFk = createItem.ControllingUnitFk,
				IsIncludeNonContractedPesItems = false,
				ExcludeInstanceIds = nonContractedItemInstanceIds
			};
			var createPesItem = pesItemLogic.CreateItems(param, ref prcItemList);
			if (createPesItem != null && createPesItem.Any())
			{
				var createList = createPesItem.ToList();
				var _prcItemIds = createList.Select(t => t.Item.PrcItemFk).ToList();
				var _prcItemList = prcItemLogic.GetSearchList(t => _prcItemIds.Contains(t.Id)).ToList();
				createList.ForEach(item =>
				{
					if (item.Item.PrcItemFk.HasValue)
					{
						var _prcItem = _prcItemList.FirstOrDefault(t => t.Id == item.Item.PrcItemFk);
						if (_prcItem != null)
						{
							item.Item.MdcMaterialFk = _prcItem.MdcMaterialFk;
							item.Item.PrcItemFactorPriceUnit = _prcItem.FactorPriceUnit;
							item.Item.QuantityContracted = _prcItem.Quantity;
							item.Item.AlternativeUomFk = _prcItem.AlternativeUomFk;
							item.Item.AlternativeQuantity = 0;
							if (_prcItem.ControllinggrpsetFk != null)
							{
								Injector.Get<IControllingGrpSetDTLELogic>().CopyBysetFk(_prcItem.ControllinggrpsetFk.Value, item.Item.Id);
							}
						}
					}
				});
				pesItemLogic.SavePesItemsNew(createList.Select(t => t.Item).ToList(), entity);
			}
			if (controllingSets.Any())
			{
				Injector.Get<IControllingGrpSetLogic>().SaveEntities(controllingSets);
			}
			pesItemLogic.Save(createItem);
			if (conditionList.Any())
			{
				new PesItemPriceConditionLogic().Save(conditionList);
			}
			if (controllingSetDtls.Any())
			{
				Injector.Get<IControllingGrpSetDTLELogic>().SaveEntities(controllingSetDtls);
			}
		}

		private void UpdateEntity(PesHeaderEntity entity)
		{
			var oldPesHeader = this.GetItemByKey(entity.Id);
			var pes2StockLogic = new Pes2StockLogic();
			HandleDataEffective(entity);
			this.Save(entity);
			UpdateSearchPattern(entity.Id);

			pes2StockLogic.UpdateStockTransactionDate(entity);

			if (null != entity.ConHeaderFk && ((null != oldPesHeader && oldPesHeader.ConHeaderFk != entity.ConHeaderFk) || null == oldPesHeader))
			{
				HeaderPparamFactory.CopyHeaderPparamToTargetHeader(PparamType.Contract, PparamType.Pes, entity.ConHeaderFk.Value, entity.Id);
			}
			else
			{
				if (null != entity.ProjectFk && ((null != oldPesHeader && oldPesHeader.ProjectFk != entity.ProjectFk) || null == oldPesHeader))
				{
					HeaderPparamFactory.CopyHeaderPparamToTargetHeader(PparamType.Project, PparamType.Pes, entity.ProjectFk.Value, entity.Id);
				}
			}
			if (null != entity.PesHeaderFk && ((null != oldPesHeader && oldPesHeader.PesHeaderFk != entity.PesHeaderFk) || null == oldPesHeader))
			{
				HeaderPparamFactory.CopyHeaderPparamToTargetHeader(PparamType.Pes, PparamType.Pes, entity.PesHeaderFk.Value, entity.Id);
			}
			// BillingSchema
			var header = this.GetItemByKey(entity.Id);
			var billingSchemaLogic = new PesBillingSchemaLogic();
			if (header != null)
			{
				var billingSchemaToSave = billingSchemaLogic.Update(header, null, null, true, true);
				if (billingSchemaToSave != null && billingSchemaToSave.Any())
				{
					billingSchemaLogic.Save(billingSchemaToSave);
					var isFromRecalucteBtn = billingSchemaLogic.IsFromRecalucteBtn(header, null, billingSchemaToSave);
					billingSchemaLogic.Recalculate(header, true);
				}
			}
		}

		/// <summary>
		/// Retrieves a combined set of entities representing items from both BOQ and procurement.
		/// </summary>
		/// <param name="contractId">The ID of the contract.</param>
		/// <returns>An enumerable of <see cref="BoqPrcItemEntity"/> instances.</returns>
		public IEnumerable<BoqPrcItemEntity> GetBoqPrcItemEntities(int contractId)
		{
			var boqItems = this.GetDiffBoq(contractId).BoqItems;
			var prcItems = this.GetDiffItem(contractId);

			var materialIds = prcItems.Where(e => e.MdcMaterialFk != null).Select(e => e.MdcMaterialFk.Value);
			var materials = new MaterialLogic().GetItemsByKey(materialIds);
			const string dataTypeBoq = "boq";
			const string dataTypePrcItem = "item";

			var boqEntities = boqItems.Select(e => new BoqPrcItemEntity
			{
				Id = e.Id,
				DataType = dataTypeBoq,
				Code = e.Reference,
				Description = e.BriefInfo != null ? (e.BriefInfo.Translated ?? e.BriefInfo.Description) : string.Empty,
				UomFK = e.BasUomFk,
				Quantity = e.Quantity
			});

			var prcEntitiesWithMaterials = prcItems.Select(e =>
			{
				var material = e.MdcMaterialFk.HasValue ? materials.FirstOrDefault(x => x.Id == e.MdcMaterialFk.Value) : null;
				return new BoqPrcItemEntity
				{
					Id = e.Id,
					DataType = dataTypePrcItem,
					Code = material?.Code ?? string.Empty,
					Description = material?.Description1 ?? (e.Description1 ?? e.Description2),
					UomFK = e.BasUomFk,
					Quantity = e.Quantity,
				};
			});

			return boqEntities.Concat(prcEntitiesWithMaterials);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		public override void Validate(IEnumerable<PesHeaderEntity> entities)
		{
			base.Validate(entities);

			var codeEntities = entities.Where(e => !string.IsNullOrWhiteSpace(e.Code));
			var codes = codeEntities.Select(e => e.Code);
			var configs = codeEntities.Select(e => e.PrcConfigurationFk);
			var existedCodes = this.GetList(BusinessApplication.BusinessEnvironment.CurrentContext.ClientId, codes, configs);
			var errors = new List<string>();

			for (int i = 0, len = entities.Count(); i < len; i++)
			{
				var entity = entities.ElementAt(i);

				if (string.IsNullOrWhiteSpace(entity.Code))
				{
					errors.Add($"{NLS.ERR_CodeShouldNotBeNullOrEmpty} - index:{i}");
				}
				else
				{
					if (existedCodes.Any(e => e.Code == entity.Code && e.Id != entity.Id))
					{
						errors.Add($"{NLS.ERR_CodeAlreadyExists} - index:{i}, code:{entity.Code}");
					}
				}
			}

			if (errors.Any())
			{
				throw new Exception(string.Join("\r\n", errors));
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="filterExpression"></param>
		/// <returns></returns>
		public List<int> GetPesIdsByExpression(Expression<Func<PesHeaderEntity, bool>> filterExpression)
		{
			using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var filterExpressionFactory = (RIB.Visual.Platform.BusinessComponents.DbContext dbCtx) => filterExpression;
				Expression<Func<PesHeaderEntity, bool>> expression = filterExpressionFactory(dbContext);
				if (expression != null)
				{
					IQueryable<int> queryable = dbContext.Entities<PesHeaderEntity>().Where(expression).Select(e => e.Id);
					return queryable.ToList();
				}
				return new List<int>();
			}
		}
	}
}