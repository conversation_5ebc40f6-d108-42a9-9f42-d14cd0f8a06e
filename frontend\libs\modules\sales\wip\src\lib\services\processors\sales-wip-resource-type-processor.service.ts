/*
 * Copyright(c) RIB Software GmbH
 */

import { ServiceLocator } from '@libs/platform/common';
import { IEntityProcessor, IEntityRuntimeDataRegistry } from '@libs/platform/data-access';
import { SalesWipLineItemDataService } from '../sales-wip-line-item-data.service';
import { ISalesWipEstResourceEntity } from '@libs/sales/interfaces';

/**
 * Sales Wip Resource Type Processor Service
 */
export class SalesWipResourceTypeProcessorService implements IEntityProcessor<ISalesWipEstResourceEntity> {
	private readonly lineItemService = ServiceLocator.injector.get(SalesWipLineItemDataService);

	public constructor(protected dataService: IEntityRuntimeDataRegistry<ISalesWipEstResourceEntity>) {
	}

	public process(resource: ISalesWipEstResourceEntity) {
		if (resource) {
			resource.EstResourceTypeFkExtend = resource.EstAssemblyTypeFk ? (4000 + resource.EstAssemblyTypeFk) : resource.EstResourceTypeFk;
		}

		const lineItem = this.lineItemService.getSelectedEntity();
		if (lineItem) {
			if (lineItem.EstLineItemFk && lineItem.EstLineItemFk > 0) {
				resource.cssClass = 'row-readonly-background';
				this.readOnly([resource], true);
			} else {
				if (resource.cssClass === 'row-readonly-background') {
					resource.cssClass = '';
				}
			}
		}
	}

	public revertProcess(): void {
	}

	private readOnly(items: ISalesWipEstResourceEntity[], isReadOnly: boolean): void {
		if (!items || items.length === 0) {
			return;
		}
		items.forEach(item => {
			if (item && item.Id) {
				this.dataService.setEntityReadOnly(item, isReadOnly);
			}
		});
	}
}