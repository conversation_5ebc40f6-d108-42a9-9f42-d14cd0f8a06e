-------------------------------------------------------
-- Ignore Errors (OPTIONAL): Off
-- JIRA Number (REQUIRED): DEV-47782
-- Script Type (REQUIRED): Required Vanilla Change
-- Reason (REQUIRED): Logistic Plant Cost Alloc - Wizard to change Billing Sheet Status
-- Install On (OPTIONAL): Trunk, Release 25.2
-------------------------------------------------------

-- Provide Wizard to create 
DECLARE	@BAS_WIZARD_PK_ID_AFFECTED int

EXEC	[dbo].[BAS_WIZARD_INSERT_UPDATE_PROC]
			@ID = 1297,						 
			@WIZARDGUID ='d9270ce787a949aa8084bf6660caf312',
			@NAME ='Change Billing Sheet Status',
			@DESCRIPTION ='Change Billing Sheet Status',
			@TRANSLATIONKEY = 'logistic.plantcostalloc.changeBillingSheetStatus',
			@PK_ID_AFFECTED = @BAS_WIZARD_PK_ID_AFFECTED OUTPUT
SELECT	@BAS_WIZARD_PK_ID_AFFECTED


-- Add wizard to diverse modules i.e. add to BAS_WIZARD2MODULE
DECLARE	@BAS_WIZARD2MODULE_PK_ID_AFFECTED int

EXEC	[dbo].[BAS_WIZARD2MODULE_INSERT_UPDATE_PROC]
			@ID = 2047,						 
			@BAS_MODULE_INTERNAL_NAME ='logistic.plantcostalloc',
			@WIZARDGUID ='d9270ce787a949aa8084bf6660caf312',
			@PK_ID_AFFECTED = @BAS_WIZARD2MODULE_PK_ID_AFFECTED OUTPUT
SELECT	@BAS_WIZARD2MODULE_PK_ID_AFFECTED

