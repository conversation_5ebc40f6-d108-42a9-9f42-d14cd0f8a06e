/*
 * $Id: QuoteHeaderLogic.cs 633783 2021-04-25 08:11:56Z lvy $
 * Copyright (c) RIB Software SE
 */

using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Transactions;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Company.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.BusinessPartner.Main.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Cloud.Common.Core;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Procurement.Common.BusinessComponents;
using RIB.Visual.Procurement.Requisition.BusinessComponents;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Core.Common;
using System.Linq.Expressions;
using RIB.Visual.Platform.Core;
using RIB.Visual.Basics.Material.BusinessComponents;
using CertificateBC = RIB.Visual.BusinessPartner.Certificate.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;
using System.Linq.Dynamic;
using RIB.Visual.Basics.Api.BusinessComponents;
using RIB.Visual.Basics.Api.Common;
using RIB.Visual.Procurement.RfQ.BusinessComponents;
using RIB.Visual.Procurement.Common.Core;
using RIB.Visual.Basics.BillingSchema.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Procurement.Package.BusinessComponents;
using RIB.Visual.Boq.Main.BusinessComponents;
using Newtonsoft.Json;
using RIB.Visual.Basics.AssetMaster.BusinessComponents;
using RIB.Visual.Basics.ProcurementStructure.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Basics.ProcurementConfiguration.BusinessComponents;
using RIB.Visual.Basics.Characteristic.BusinessComponents;
using RIB.Visual.Basics.MaterialCatalog.BusinessComponents;

namespace RIB.Visual.Procurement.Quote.BusinessComponents
{
	/// <summary/>
	[Export(typeof(IEntityInfoProvider))]
	public class LastObjectEntityInfo : LastObjectEntityInfoBase
	{
		/// <summary/>
		public LastObjectEntityInfo()
			: base("procurement.quote") { }

		/// <summary>
		///
		/// </summary>
		/// <param name="entityInfos"></param>
		/// <returns></returns>
		public override IEnumerable<IEntityInfoData> GetEntityInfoData(IEnumerable<IEntityInfoData> entityInfos)
		{

			var result = GetEntityInfo<QuoteHeaderEntity>(
					ModelBuilder.DbModel,
					entityInfos,
					(query, ids) => query.Where(entity => ids.Contains(entity.Id)),
					entity => entity.Id,
					entity => String.Join(",", new string[] { entity.Code, entity.Description })
					);
			return result;
		}
	}

	/// <summary>
	/// Header logic.
	/// </summary>
	///
	[Export("procurement.quote", typeof(IPrcRecalculateTotalsLogic))]
	[Export(typeof(IQuoteHeaderLogic))]
	[Export("quote", typeof(IChangeStatus))]
	[Export(typeof(IQuoteBidderBusinessPartnerLogic))]
	[EntityStatus("QTN_STATUS", "procurement.quote", "Quotation Status")]
	public partial class QuoteHeaderLogic : ProcurementCommonLogicBaseNew<QuoteHeaderEntity, DdTempIdsEntity>, IEntityFacade, IChangeStatus, IQuoteHeaderLogic, IQuoteBidderBusinessPartnerLogic,
		  IPrcRecalculateTotalsLogic, IRecalculateProcurementFacade
	{
		/// <summary>
		///
		/// </summary>
		public QuoteHeaderLogic()
		{
			this.SetRelationInfoIdentifier(RelationInfoIdentifier.QuoteHeader);
			this.PermissionGUID = "338048ac80f748b3817ed1faea7c8aa5";
			SetTempMatchingFunc<DdTempIdsEntity>((e, tmp) => (e.Id == tmp.Id));
			this.FilterByPermission = (dbContext, query) =>
			{
				ParseAccessFunctionParams(FilterRequest);
				query = JoinAccessFunctionWithFilter(query, new DbFunctions(dbContext));
				return query;
			};
			OrderByExpressions =
			[
				OrderTerm.Create(e => e.Id, true)
			];

			this.ParentIdFnForNonTree = e => e.QtnHeaderFk;
			this.ParentPropertyName = "QtnHeaderFk";

			this.InstanceProvider.AddFactory(() => new QuoteRequisitionLogic());
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}


		//add by jack
		/// <summary>
		///
		/// </summary>
		/// <param name="filterIn"></param>
		/// <returns></returns>
		public IDictionary<string, object> GetSearchListByPaging(QuoteFilterRequests filterIn)
		{

			IDictionary<string, object> dicResult = new Dictionary<string, object>();
			dicResult["Count"] = 0;
			dicResult["Data"] = null;
			Expression<Func<QuoteHeaderEntity, bool>> filterExpression = e => true;
			var companyFk = this.CompanyInfo.GetLoginCompanyId();
			if (filterIn.projectfk > 0)
			{
				filterExpression = filterExpression.And(e => e.ProjectFk == filterIn.projectfk);
			}
			if (filterIn.statusfk.HasValue)
			{
				filterExpression = filterExpression.And(e => e.StatusFk == filterIn.statusfk.Value);
			}
			if (!string.IsNullOrEmpty(filterIn.pattern))
			{
				filterExpression = filterExpression.And(e => e.SearchPattern.Contains(filterIn.pattern));
			}
			filterExpression = filterExpression.And(e => e.CompanyFk == companyFk);


			using (var dbcontext1 = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{

				var qtnHeaderIds = dbcontext1.Entities<QtnHeader2prjMaterialVEntity>().Where(e => e.BasCompanyFk == companyFk);

				var qtnHeaderEntities = dbcontext1.Entities<QuoteHeaderEntity>().Where(filterExpression);

				var result = from qtn in qtnHeaderEntities
							 where qtnHeaderIds.Any(e => e.QtnHeaderFk == qtn.Id)
							 select qtn;
				dicResult["Count"] = result.Count();

				var qtns = result.OrderBy(e => e.Id).Skip(filterIn.pageNumber * filterIn.pageSize).Take(filterIn.pageSize).ToList();

				dicResult["Data"] = qtns;

				var projectIds = qtns.Where(e => e.ProjectFk.HasValue).Select(e => e.ProjectFk.Value).ToList();

				if (projectIds.Count > 0)
				{
					var ProjectLookupDataProvider = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectLookupDataProvider>();

					dicResult["Project"] = ProjectLookupDataProvider.GetProjectsByID(projectIds);
				}
			}
			return dicResult;

		}

		/// <summary>
		/// Return quote heads as list of general interface objects
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IEnumerable<IIdentifyable> GetQuotesByProjectId(int projectId)
		{
			var quotes = GetSearchList(e => e.ProjectFk == projectId);
			var rfqHeaderIds = quotes.Select(e => e.RfqHeaderFk).Distinct();
			List<QuoteHeaderEntity> quoteEntities = new List<QuoteHeaderEntity>();
			int[] basMaterialIds = { };
			List<QuoteHeaderEntity> resultEntities = new List<QuoteHeaderEntity>();
			var itemLogic = new PrcItemLogic();

			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var prjMaterialLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectMaterialLogic>();
				var prjMaterials = prjMaterialLogic.GetProjectMaterials(projectId);
				basMaterialIds = prjMaterials.Select(e => e.MdcMaterialFk).ToArray();
			}

			foreach (var id in rfqHeaderIds)
			{
				var temps = GetQuotesByRfqHeaderFk(id);
				var bpIds = temps.Select(e => e.BusinessPartnerFk).Distinct().ToArray();

				foreach (var bpId in bpIds)
				{
					var quote = temps.FirstOrDefault(e => e.BusinessPartnerFk == bpId);
					quoteEntities.Add(quote);
				}
			}

			var requisitionLogic = new QuoteRequisitionLogic();
			var prcHeaderLogic = new PrcHeaderLogic();
			List<long> prcItemIds = new List<long>();

			foreach (var quote in quoteEntities)
			{
				var requisitions = requisitionLogic.GetSearchList(e => e.QtnHeaderFk == quote.Id);
				var prcHeaderIds = requisitions.Select(e => e.PrcHeaderFk);
				var hasPrjMaterial = false;

				foreach (var id in prcHeaderIds)
				{
					var items = itemLogic.GetSearchList(e => e.PrcHeaderFk == id);

					foreach (var item in items)
					{
						var prcItem = item;
						prcItemIds.Add(item.Id);
						var replacementItems = itemLogic.GetSearchList(e => e.PrcReplacementItemFk != null && prcItemIds.Contains(e.PrcReplacementItemFk.Value), prcItemIds.Any());

						if (replacementItems.Any())
						{
							foreach (var replace in replacementItems)
							{
								if (replace.PrcReplacementItemFk == item.Id)
								{
									prcItem = replace;
								}
							}
						}

						foreach (var basMdcId in basMaterialIds)
						{
							if (prcItem.MdcMaterialFk == basMdcId)
							{
								hasPrjMaterial = true;
								break;
							}
						}

						if (hasPrjMaterial)
						{
							break;
						}

						prcItemIds.Clear();
					}
				}

				if (hasPrjMaterial && (requisitions.Any()))
				{
					quote.PrcHeaderFk = requisitions.FirstOrDefault().PrcHeaderFk;
					resultEntities.Add(quote);
				}
			}

			return resultEntities;
		}

		/// <summary>
		/// export the function get exchange rate to project material module
		/// </summary>
		/// <returns></returns>
		public decimal GetExchangeRate(int companyFk, int currencyForeignFk)
		{
			var exchangeLogic = new ProcurementCommonExchangeRateLogic();
			var materialExchangeRate = exchangeLogic.GetExchangeRate(companyFk, currencyForeignFk);
			return materialExchangeRate.Value;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="rfqHeaderFk"></param>
		/// <returns></returns>
		public IEnumerable<IQuoteData> GetQuoteHeadersByRfqHeaderFk(int rfqHeaderFk)
		{
			return this.GetSearchList(e => e.RfqHeaderFk == rfqHeaderFk);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="rfqHeaderIds"></param>
		/// <returns></returns>
		public IEnumerable<IQuoteData> GetQuoteHeadersByRfqHeaderFk(IEnumerable<int> rfqHeaderIds)
		{
			return this.GetSearchList(e => rfqHeaderIds.Contains(e.RfqHeaderFk)).ToList();
		}

		/// <summary>
		/// export the function get exchange rate oc to project material module
		/// </summary>
		/// <returns></returns>
		public decimal GetExchangeRateOc(int documentCurrencyFk, int currencyForeignFk)
		{
			var exchangeLogic = new ProcurementCommonExchangeRateLogic();
			var materialExchangeRate = exchangeLogic.GetExchangeRateOc(documentCurrencyFk, currencyForeignFk);
			return materialExchangeRate.Value;
		}
		class QtnData
		{
			public int QtnId { get; set; }
			public int RfqFk { get; set; }
			public int QtnVersion { get; set; }
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <returns></returns>
		public QuoteHeaderEntity GetQuoteByPrcHeaderFk(int prcHeaderId)
		{
			var requisitionLogic = this.InstanceProvider.GetInstance<QuoteRequisitionLogic>();
			var quoteReqEntity = requisitionLogic.GetSearchList(e => e.PrcHeaderFk == prcHeaderId).FirstOrDefault();
			if (quoteReqEntity != null)
			{
				var quoteEntity = this.GetItemByKey(quoteReqEntity.QtnHeaderFk);
				return quoteEntity;
			}
			return null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="prcHeaderIds"></param>
		/// <returns></returns>
		public IEnumerable<QuoteHeaderEntity> GetQuoteListByPrcHeaderFks(IEnumerable<int> prcHeaderIds)
		{
			var requisitionLogic = new QuoteRequisitionLogic();
			var quoteReqEntityList = requisitionLogic.GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk));

			if (quoteReqEntityList != null && quoteReqEntityList.Any())
			{
				var quoteHeaderFkList = quoteReqEntityList.Select(e => e.QtnHeaderFk);
				var quoteEntityList = this.GetSearchList(e => quoteHeaderFkList.Contains(e.Id));
				return quoteEntityList;
			}
			return [];
		}

		/// <summary>
		/// Return materials after cauculated
		/// </summary>
		/// <returns></returns>
		public IEnumerable<IMaterialUpdateEntity> GetMaterialsFromQuote(int[] quoteIds, IEnumerable<IMaterialUpdateEntity> materials)
		{
			var entities = GetSearchList(e => quoteIds.Contains(e.Id));
			var requisitionLogic = new QuoteRequisitionLogic();
			var prcItemLogic = new PrcItemLogic();
			Dictionary<int, IMaterialUpdateEntity> resultMaterialsKValue = new Dictionary<int, IMaterialUpdateEntity>();
			List<long> prcItemIds = new List<long>();
			var itemLogic = new PrcItemLogic();

			foreach (var quote in entities)
			{
				var qtnId = quote.Id;
				var rfqFk = quote.RfqHeaderFk;
				var qtnVer = quote.QuoteVersion;

				var requisitions = requisitionLogic.GetSearchList(e => e.QtnHeaderFk == quote.Id);
				var prcFks = requisitions.Select(e => e.PrcHeaderFk).ToArray();
				var prcItems = prcItemLogic.GetSearchList(e => prcFks.Contains(e.PrcHeaderFk)).ToList();

				foreach (var item in prcItems)
				{
					var prcItem = item;
					prcItemIds.Add(prcItem.Id);
					var replacementItems = itemLogic.GetSearchList(e => e.PrcReplacementItemFk != null && e.PrcReplacementItemFk.Value == item.Id, prcItemIds.Any());
					if (replacementItems.Any())
					{
						foreach (var replaceitem in replacementItems)
						{
							prcItem = replaceitem;
							CompareMaterials(quote, prcItem, materials, ref resultMaterialsKValue);
						}
					}
					else
					{
						CompareMaterials(quote, prcItem, materials, ref resultMaterialsKValue);
					}
				}
			}
			return resultMaterialsKValue.Values.ToList();
		}
		/// <summary>
		/// return the data of compareMaterial
		/// </summary>
		/// <param name="quote"></param>
		/// <param name="item"></param>
		/// <param name="materials"></param>
		/// <param name="resultCompareMaterials"></param>
		public void CompareMaterials(QuoteHeaderEntity quote, PrcItemEntity item, IEnumerable<IMaterialUpdateEntity> materials, ref Dictionary<int, IMaterialUpdateEntity> resultCompareMaterials)
		{
			int companyId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			var companyEntity = new BasicsCompanyLogic().GetItemByKey(companyId);
			var exchangeLogic = new ProcurementCommonExchangeRateLogic();
			var prcPriceConditionLogic = new PrcItemPriceConditionNewLogic();
			Dictionary<int, QtnData> history = new Dictionary<int, QtnData>();
			var qtnId = quote.Id;
			var rfqFk = quote.RfqHeaderFk;
			var qtnVer = quote.QuoteVersion;
			foreach (var material in materials)
			{
				if (item.MdcMaterialFk.HasValue && (item.MdcMaterialFk.Value == material.Id))
				{
					var exchangeRate = exchangeLogic.GetExchangeRateOc(material.BasCurrencyFk, quote.CurrencyFk);
					MaterialUpdateResultEntity resultMaterial = new MaterialUpdateResultEntity();
					if ((material.BasCurrencyFk == companyEntity.CurrencyFk) && (material.BasCurrencyFk != quote.CurrencyFk))
					{
						resultMaterial.EstimatePrice = item.Price + item.PriceExtra;
					}
					else if ((material.BasCurrencyFk == quote.CurrencyFk) && (material.BasCurrencyFk != companyEntity.CurrencyFk))
					{
						resultMaterial.EstimatePrice = item.PriceOc + item.PriceExtraOc;
					}
					else
					{
						if (exchangeRate.Value != 0)
						{
							resultMaterial.EstimatePrice = (item.PriceOc + item.PriceExtraOc) / (exchangeRate.Value);
						}
					}

					var itemPrcPriceConditions = prcPriceConditionLogic.GetSearchList(e => e.PrcItemFk == item.Id);
					List<IMaterialPriceConditionEntity> materialsPriceConditions = new List<IMaterialPriceConditionEntity>();
					foreach (var prcCondition in itemPrcPriceConditions)
					{
						MaterialPriceConditionEntity materialPriceCondition = new MaterialPriceConditionEntity();
						materialPriceCondition.Id = prcCondition.Id;
						materialPriceCondition.PrcPriceConditionTypeFk = prcCondition.PrcPriceConditionTypeFk;
						materialPriceCondition.Value = prcCondition.Value;
						materialPriceCondition.IsPriceComponent = prcCondition.IsPriceComponent;
						materialsPriceConditions.Add(materialPriceCondition);
					}

					resultMaterial.Id = material.Id;
					resultMaterial.PrcPriceConditionFk = item.PrcPriceConditionFk;
					resultMaterial.CommentText = quote.Code + ' ' + quote.Description;
					resultMaterial.PriceConditions = materialsPriceConditions;
					resultMaterial.Source = "Quote";
					resultMaterial.Co2Project = item.Co2Project;
					resultMaterial.Co2Source = item.Co2Source;

					var replace = false;

					if (history.ContainsKey(resultMaterial.Id))
					{
						var last = history[resultMaterial.Id];
						if (last.RfqFk == rfqFk)
						{
							if (last.QtnVersion < qtnVer)
							{
								replace = true;
							}
						}
						else
						{
							if (last.QtnId < qtnId)
							{
								replace = true;
							}
						}
					}
					else
					{
						replace = true;
					}

					if (replace)
					{
						var qtnData = new QtnData() { QtnId = qtnId, QtnVersion = qtnVer, RfqFk = rfqFk };
						history[resultMaterial.Id] = qtnData;
						resultCompareMaterials[resultMaterial.Id] = resultMaterial;
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="items"></param>
		/// <returns></returns>
		protected override void PostProcess(IEnumerable<QuoteHeaderEntity> items)
		//protected override IEnumerable<QuoteHeaderEntity> FillRelated(IEnumerable<QuoteHeaderEntity> items)
		{
			if (items == null || !items.Any())
			{
				return;
			}
			var ProjectLookupDataProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectLookupDataProvider>();
			var projectIds = items.CollectIds(e => e.ProjectFk);
			var projects = ProjectLookupDataProvider.GetProjectsByID(projectIds);
			var rfqHeaderIds = items.CollectIds(e => e.RfqHeaderFk);
			var rfqBps = new RfqBusinessPartnerLogic().GetListByFilter(e => rfqHeaderIds.Contains(e.RfqHeaderFk));

			foreach (var entity in items)
			{
				if (entity.ProjectFk.HasValue)
				{
					var project = projects.SingleOrDefault(e => e.Id == entity.ProjectFk.Value);
					if (project != null)
					{
						entity.ProjectStatusFk = project.StatusFk;
					}
				}

				var rfqBp = rfqBps.FirstOrDefault(e => e.RfqHeaderFk == entity.RfqHeaderFk && e.BusinessPartnerFk == entity.BusinessPartnerFk);
				if (rfqBp != null)
				{
					entity.ExtendedDate = rfqBp.ExtendedDate;
				}
			}

			GetPackageForQtnHeaders(items);

			this.AssignRubricCatagory(items);

			this.RecalculateTotalFields(items);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		/// <param name="searchSpecification"></param>
		protected override void RootPostProcess(IEnumerable<QuoteHeaderEntity> entities, SearchSpecification<QuoteHeaderEntity, int> searchSpecification)
		{
			GetQuotesFromSameRFQ(searchSpecification.FilterIn as QuoteFilterRequest, entities);

			GetQuotesFromOnlyLatestVersion(searchSpecification.FilterIn as QuoteFilterRequest, entities);

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="quoteHeaderEntity"></param>
		public void GetPackageForQtnHeader(QuoteHeaderEntity quoteHeaderEntity)
		{
			if (quoteHeaderEntity == null)
			{
				return;
			}
			var quoteRequsitionLogic = new QuoteRequisitionLogic();
			var reqHeaderIds = quoteRequsitionLogic.GetSearchList(e => e.QtnHeaderFk == quoteHeaderEntity.Id).Select(e => e.ReqHeaderFk);
			var reqHeaders = new ReqHeaderLogic().GetSearchList(e => reqHeaderIds.Contains(e.Id));

			var packageIds = reqHeaders.Select(e => e.PackageFk);
			var packages = new PrcPackageLogic().GetSearchList(e => packageIds.Contains(e.Id));

			if (packageIds.Count() > 1 && packages.Any())
			{
				quoteHeaderEntity.PackageNumber = "*";
				quoteHeaderEntity.PackageDescription = "*";
				quoteHeaderEntity.AssetMasterCode = "*";
				quoteHeaderEntity.AssetMasterDescription = "*";
				quoteHeaderEntity.PackageDeliveryAddress = "*";
				quoteHeaderEntity.PackageTextInfo = "*";
			}
			else if (packageIds.Count() == 1 && packages.Count() == 1)
			{
				var package = packages.FirstOrDefault();
				quoteHeaderEntity.PackageNumber = package?.Code;
				quoteHeaderEntity.PackageDescription = package.Description;
				quoteHeaderEntity.PackageTextInfo = package.TextInfo;

				if (package.AssetMasterFk.HasValue)
				{
					var assetEntity = new AssetMasterLogic().GetItemByKey(package.AssetMasterFk);
					if (assetEntity != null)
					{
						quoteHeaderEntity.AssetMasterCode = assetEntity.Code;
						quoteHeaderEntity.AssetMasterDescription = assetEntity.DescriptionInfo.Translated;
					}
				}
				if (package.AddressFk.HasValue)
				{
					var adddressLogic = new AddressLogic();
					var address = adddressLogic.GetSearchList(e => e.Id == package.AddressFk).FirstOrDefault();
					quoteHeaderEntity.PackageDeliveryAddress = address != null ? address.AddressLine : "";
				}
			}
		}

		/// <summary>
		///  GetCo2Total
		/// </summary>
		/// <param name="quotes"></param>
		public void GetCo2Total(IEnumerable<QuoteHeaderEntity> quotes)
		{
			var qtnHeaderIds = quotes.CollectIds(e => e.Id);
			var qtnRequisitions = new QuoteRequisitionLogic().GetSearchList(e => qtnHeaderIds.Contains(e.QtnHeaderFk), qtnHeaderIds.Any());
			if (qtnRequisitions.Any())
			{
				var prcHeaderIds = qtnRequisitions.CollectIds(e => e.PrcHeaderFk).Distinct();
				var prcItems = new PrcItemLogic().GetPrcItemsByPrcHeader(prcHeaderIds);
				foreach (var quoteItem in quotes)
				{
					var prcHeaderIdList = qtnRequisitions.Where(x => x.QtnHeaderFk == quoteItem.Id).CollectIds(e => e.PrcHeaderFk);
					var prcItemsByPrcHeader = prcItems.Where(e => prcHeaderIdList.Contains(e.PrcHeaderFk)).ToList();
					if (prcItemsByPrcHeader.Any())
					{
						quoteItem.Co2ProjectTotal = prcItemsByPrcHeader.Sum(r => r.Co2ProjectTotal);
						quoteItem.Co2SourceTotal = prcItemsByPrcHeader.Sum(r => r.Co2SourceTotal);
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="quoteHeaderEntities"></param>
		public void GetPackageForQtnHeaders(IEnumerable<QuoteHeaderEntity> quoteHeaderEntities)
		{
			if (quoteHeaderEntities == null)
			{
				return;
			}
			var qtnHeaderIds = quoteHeaderEntities.CollectIds(e => e.Id);
			var qtnReqHeaders = new QuoteRequisitionLogic().GetSearchList(e => qtnHeaderIds.Contains(e.QtnHeaderFk)).ToArray();

			var prcHeaderIds = qtnReqHeaders.Select(e => e.PrcHeaderFk).ToList();
			var boqs = new PrcBoqLogic().GetList(e => prcHeaderIds.Contains(e.PrcHeaderFk)).OrderByDescending(e => e.PackageFk);

			var reqHeaderIds = qtnReqHeaders.CollectIds(e => e.ReqHeaderFk);
			var reqHeaders = new ReqHeaderLogic().GetSearchList(e => reqHeaderIds.Contains(e.Id)).ToArray();

			var allPackageIds = reqHeaders.Where(e => e.PackageFk.HasValue).Select(e => e.PackageFk.Value);
			var allPackages = new PrcPackageLogic().GetSearchList(e => allPackageIds.Contains(e.Id)).ToArray();

			var assetMasterIds = allPackages.Where(e => e.AssetMasterFk.HasValue).Select(e => e.AssetMasterFk.Value);
			var assetEntities = new AssetMasterLogic().GetSearchList(e => assetMasterIds.Contains(e.Id)).ToArray();

			var addressIds = allPackages.Where(e => e.AddressFk.HasValue).Select(e => e.AddressFk.Value);
			var addresses = new AddressLogic().GetSearchList(e => addressIds.Contains(e.Id)).ToArray();

			foreach (var quoteHeaderEntity in quoteHeaderEntities)
			{
				var subQtnReqHeaders = qtnReqHeaders.Where(e => e.QtnHeaderFk == quoteHeaderEntity.Id).ToArray();
				var subQtnReqHeaderIds = subQtnReqHeaders.CollectIds(e => e.ReqHeaderFk);
				var subReqHeaders = reqHeaders.Where(e => subQtnReqHeaderIds.Contains(e.Id)).ToArray();
				var packageIds = subReqHeaders.Where(e => e.PackageFk.HasValue).Select(e => e.PackageFk.Value);
				var packages = allPackages.Where(e => packageIds.Contains(e.Id)).ToArray();
				var targetBoqs = boqs.Where(x => subQtnReqHeaders.Select(y => y.PrcHeaderFk).Contains(x.PrcHeaderFk));
				quoteHeaderEntity.BoqWithMaxPackageId = 0;
				if (targetBoqs.Any())
				{
					quoteHeaderEntity.BoqWithMaxPackageId = targetBoqs.Max(e => e.PackageFk);
				}
				if (packageIds.Count() > 1 && packages.Length > 0)
				{
					quoteHeaderEntity.PackageNumber = "*";
					quoteHeaderEntity.PackageDescription = "*";
					quoteHeaderEntity.AssetMasterCode = "*";
					quoteHeaderEntity.AssetMasterDescription = "*";
					quoteHeaderEntity.PackageDeliveryAddress = "*";
					quoteHeaderEntity.PackageTextInfo = "*";
				}
				else if (packageIds.Count() == 1 && packages.Length == 1)
				{
					var package = packages.First();
					quoteHeaderEntity.PackageNumber = package?.Code;
					quoteHeaderEntity.PackageDescription = package.Description;
					quoteHeaderEntity.PackageTextInfo = package.TextInfo;

					if (package.AssetMasterFk.HasValue)
					{
						var assetEntity = assetEntities.FirstOrDefault(e => e.Id == package.AssetMasterFk.Value);
						if (assetEntity != null)
						{
							quoteHeaderEntity.AssetMasterCode = assetEntity.Code;
							quoteHeaderEntity.AssetMasterDescription = assetEntity.DescriptionInfo.Translated;
						}
					}
					if (package.AddressFk.HasValue)
					{
						var address = addresses.FirstOrDefault(e => e.Id == package.AddressFk.Value);
						quoteHeaderEntity.PackageDeliveryAddress = address != null ? address.AddressLine : "";
					}
				}
			}
		}

		/// <summary>
		/// get chained base entity by base id
		/// </summary>
		/// <param name="baseId"></param>
		/// <returns></returns>
		public IEnumerable<QuoteHeaderEntity> GetChainedBaseQuoteHeaders(int baseId)
		{
			List<int> ids = new List<int>();
			using (var dbContext = this.CreateDbContext())
			{

				ids = dbContext.Entities<QuoteHeaderEntity>().Join(
					dbContext.Entities<QuoteHeaderEntity>().Where(e => e.Id == baseId),
					o => new { rfqFk = o.RfqHeaderFk, bpFk = o.BusinessPartnerFk },
					i => new { rfqFk = i.RfqHeaderFk, bpFk = i.BusinessPartnerFk },
					(o, i) => o.Id).ToList();
			}

			return GetSearchList(e => ids.Contains(e.Id) || ids.Contains(e.QtnHeaderFk.Value));
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		private IEnumerable<QuoteHeaderEntity> GetQuotesFromSameRFQ(QuoteFilterRequest filterIn, IEnumerable<QuoteHeaderEntity> entities)
		{
			var quotesFromSameRFQStr = filterIn.GetFurtherFiltersValue("quotesFromSameRFQ");
			var quotesFromSameRFQ = string.IsNullOrEmpty(quotesFromSameRFQStr) ? false : Convert.ToBoolean(quotesFromSameRFQStr);

			if (quotesFromSameRFQ && entities.Any())
			{
				var quoteHeaderLogic = new QuoteHeaderLogic();
				var quoteIds = entities.Select(e => e.Id).Distinct();
				var rfqIds = entities.Select(e => e.RfqHeaderFk).Distinct();
				var resultEntities = quoteHeaderLogic.GetSearchList(e => rfqIds.Contains(e.RfqHeaderFk)).Distinct();

				var finalEntities = resultEntities
					.OrderBy(e => (e.QtnHeaderFk == null ? e.Id : e.QtnHeaderFk.Value))
					.ThenBy(e => (e.QtnHeaderFk == null ? 0 : e.Id))
					.ToList();

				return finalEntities;
			}

			return entities;
		}

		private IEnumerable<QuoteHeaderEntity> GetQuotesFromOnlyLatestVersion(QuoteFilterRequest filterIn, IEnumerable<QuoteHeaderEntity> entities)
		{
			var onlyDisplayLatestQuoteVersionStr = filterIn.GetFurtherFiltersValue("onlyDisplayLatestQuoteVersion");
			var onlyDisplayLatestQuoteVersion = string.IsNullOrEmpty(onlyDisplayLatestQuoteVersionStr) ? false : Convert.ToBoolean(onlyDisplayLatestQuoteVersionStr);

			if (onlyDisplayLatestQuoteVersion && entities.Any())
			{

				List<int> latestQuoteIds = new List<int>();
				var grouped = entities.GroupBy(e => e.RfqHeaderFk);
				foreach (var quoteHeaderEntities in grouped)
				{
					var groupedByBpList = quoteHeaderEntities.GroupBy(e => e.BusinessPartnerFk);

					foreach (var group in groupedByBpList)
					{
						var latestQuoteVersion = group.OrderBy(e => e.QuoteVersion);
						var latestQuoteElement = latestQuoteVersion.LastOrDefault();
						if (latestQuoteElement != null)
						{
							latestQuoteIds.Add(latestQuoteElement.Id);
						}
					}
				}

				var result = entities.Where(e => latestQuoteIds.Contains(e.Id));
				result = result.OrderBy(e => (e.QtnHeaderFk == null ? e.Id : e.QtnHeaderFk.Value))
					 .ThenBy(e => (e.QtnHeaderFk == null ? 0 : e.Id))
					 .ToList();

				return result;
			}

			return entities;
		}


		/// <summary>
		/// get request data by the request id from business partner portal.
		/// </summary>
		/// <param name="requestId"></param>
		/// <returns></returns>
		public IEnumerable<MaterialEntity> GetMaterialsFromRequestItem(string requestId)
		{
			IEnumerable<MaterialEntity> list = new List<MaterialEntity>();
			//Get the data of materials in the module material.
			var requestItem = new BasicsApiInquiryLogic().GetInquiry(requestId); ;
			if (requestItem != null)
			{
				var items = InquiryFactory.GetInquireItemFromString<RequestMateriaEntity>(requestItem.ItemData);
				if (items != null && items.Any())
				{
					var materialIds = items.CollectIds(e => e.materialId);
					var materialList = new MaterialLogic().GetSearchList(e => materialIds.Contains(e.Id), materialIds.Any()).ToList();
					var materialGroupIdList = materialList.CollectIds(e => e.MaterialGroupFk).Distinct();
					var materialGroupDic = new MaterialGroupLogic().GetSearchList(e => materialGroupIdList.Contains(e.Id), materialGroupIdList.Any()).ToDictionary(x => x.Id, x => x.MaterialCatalogFk);
					foreach (var material in materialList)
					{
						if (materialGroupDic.ContainsKey(material.MaterialGroupFk))
						{
							material.MaterialCatalogFk = materialGroupDic[material.MaterialGroupFk];
						}
					}
					list = materialList;
				}
			}
			return list;
		}

		/// <summary>
		///  GetTotalLeadTime
		/// </summary>
		/// <param name="quote"></param>
		public void GetTotalLeadTime(IEnumerable<QuoteHeaderEntity> quote)
		{
			var qtnHeaderIds = quote.CollectIds(e => e.Id);
			var qtnRequisition = new QuoteRequisitionLogic().GetSearchList(e => qtnHeaderIds.Contains(e.QtnHeaderFk), qtnHeaderIds.Any());
			if (qtnRequisition != null)
			{
				var prcHeaderIds = qtnRequisition.CollectIds(e => e.PrcHeaderFk);
				var prcItems = new PrcItemLogic().GetPrcItemsByPrcHeader(prcHeaderIds).OrderByDescending(e => e.TotalLeadTime);

				foreach (var entity in qtnRequisition)
				{
					//TotalLeadTime
					var prcItemsByPrcHeader = prcItems.FirstOrDefault(x => x.PrcHeaderFk == entity.PrcHeaderFk);
					var quoteItem = quote.FirstOrDefault(e => e.Id == entity.QtnHeaderFk);
					if (quoteItem != null && prcItemsByPrcHeader != null)
					{
						quoteItem.TotalLeadTime = prcItemsByPrcHeader.TotalLeadTime;
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="rfqHeaderFk"></param>
		/// <returns></returns>
		public IEnumerable<QuoteHeaderEntity> GetQuotesByRfqHeaderFk(int rfqHeaderFk)
		{
			using var dbContext = this.CreateDbContext();
			return [.. dbContext.Entities<QuoteHeaderEntity>()
					.Where(e => e.RfqHeaderFk == rfqHeaderFk)
					.OrderByDescending(e => e.QuoteVersion)];
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="rfqFk"></param>
		public void DeleteIdealBidder(int rfqFk)
		{
			var dataQuote = this.GetSearchList(e => e.RfqHeaderFk == rfqFk && e.IsIdealBidder == true).FirstOrDefault();
			if (dataQuote != null)
			{
				Delete(dataQuote);
			}
		}
		/// <summary>
		/// delete item (status IsOrdered and IsReadonly is false)
		/// </summary>
		/// <param name="deleteEntity"></param>
		public new void Delete(QuoteHeaderEntity deleteEntity)
		{
			var quoteRequisitionLogic = new QuoteRequisitionLogic();
			var idealBidder2ItemLogic = new QuoteIdealBidder2ItemLogic();
			var idealBidder2BoqLogic = new QuoteIdealBidder2BoqLogic();
			var bpEvaluationLogic = new EvaluationLogic();
			IEnumerable<QuoteIdealBidder2ItemEntity> idealBidder2ItemToDelete = null;
			IEnumerable<QuoteIdealBidder2BoqEntity> idealBidder2BoqToDelete = null;
			if (deleteEntity.IsIdealBidder)
			{
				idealBidder2ItemToDelete = idealBidder2ItemLogic.GetByQuoteId(deleteEntity.Id);
				idealBidder2BoqToDelete = idealBidder2BoqLogic.GetByQuoteId(deleteEntity.Id);
			}
			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{

				// BillingSchema
				var billingSchemaLogic = new QuoteBillingSchemaLogic();
				billingSchemaLogic.Delete(deleteEntity.Id);

				// ideal bidder to item
				if (idealBidder2ItemToDelete != null && idealBidder2ItemToDelete.Any())
				{
					idealBidder2ItemLogic.Delete(idealBidder2ItemToDelete);
				}

				// ideal bidder to boq
				if (idealBidder2BoqToDelete != null && idealBidder2BoqToDelete.Any())
				{
					idealBidder2BoqLogic.Delete(idealBidder2BoqToDelete);
				}

				quoteRequisitionLogic.Delete(quoteRequisitionLogic.GetSearchList(e => deleteEntity.Id == e.QtnHeaderFk));

				#region Evaluation
				var rfqHeader = new RfqHeaderLogic().GetItemByKey(deleteEntity.RfqHeaderFk);
				var evaluations = bpEvaluationLogic.GetListByFilter(p => p.QtnHeaderFk == deleteEntity.Id);
				if (evaluations != null && evaluations.Any())
				{
					IEnumerable<EvaluationEntity> toUpdateList = evaluations;
					if (rfqHeader.EvaluationSchemaFk.HasValue)
					{
						var toDeleteList = evaluations.Where(p => p.EvaluationSchemaFk == rfqHeader.EvaluationSchemaFk.Value);
						toUpdateList = evaluations.Where(p => p.EvaluationSchemaFk != rfqHeader.EvaluationSchemaFk.Value);
						bpEvaluationLogic.DeleteEntities(toDeleteList);
					}
					foreach (var evaluation in toUpdateList)
					{
						evaluation.QtnHeaderFk = null;
					}
					bpEvaluationLogic.Save(toUpdateList);
				}
				#endregion
				base.Delete(deleteEntity);

				transaction.Complete();
			}
		}

		/// <summary>
		/// Update Search Pattern
		/// </summary>
		/// <param name="Id"></param>
		public void UpdateSearchPattern(int Id)
		{
			using (var dbcontext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
			{
				var result = dbcontext.ExecuteStoredProcedure("QTN_HEADER_PROC", Id);
			}
		}


		/// <summary>
		/// Update the status
		/// </summary>
		/// <param name="id"></param>
		/// <param name="statusId"></param>
		/// <returns></returns>
		public QuoteHeaderEntity UpdateStatus(int id, int statusId)
		{
			using (var dbcontext = this.CreateDbContext())
			{
				var item = dbcontext.Entities<QuoteHeaderEntity>().Where(e => e.Id == id).FirstOrDefault();
				if (item == null)
				{
					return null;
				}
				item.StatusFk = statusId;
				this.Save(item);
				return item;
			}
		}

		/// <summary>
		/// Update the quote document.
		/// </summary>
		/// <param name="entitiesCount"></param>
		/// <param name="mainItemId"></param>
		/// <param name="planningChange"></param>
		/// <param name="oldHeader"></param>
		/// <param name="header"></param>
		/// <param name="evaluation"></param>
		/// <param name="evaluationComplete"></param>
		/// <param name="qtnRequisitionToSave"></param>
		/// <param name="qtnRequisitionToDelete"></param>
		/// <param name="qtnTotalToSave"></param>
		/// <param name="qtnTotalToDelete"></param>
		/// <param name="callOffAgreementToSave"></param>
		/// <param name="callOffAgreementToDelete"></param>
		/// <param name="mandatoryDeadlineToSave"></param>
		/// <param name="mandatoryDeadlineToDelete"></param>
		/// <param name="certificateToSave"></param>
		/// <param name="certificateToDelete"></param>
		/// <param name="billingSchemaToSave"></param>
		/// <param name="billingSchemaToDelete"></param>
		/// <param name="evaluationToSave"></param>
		/// <param name="evaluationToDelete"></param>
		/// <param name="evaluationDocumentToSave"></param>
		/// <param name="evaluationDocumentToDelete"></param>
		/// <param name="prcWarrantyEntityToSave"></param>
		/// <param name="prcWarrantyEntityToDelete"></param>
		/// <param name="prcPackage2ExtBidderToSave"></param>
		/// <param name="prcPackage2ExtBidderToDelete"></param>
		/// <param name="MtgHeaderToSave"></param>
		/// <param name="MtgHeaderToDelete"></param>
		/// <returns></returns>
		public QuoteCompleteEntity Update(int entitiesCount, int mainItemId, Object planningChange,
			QuoteHeaderEntity oldHeader, QuoteHeaderEntity header, EvaluationEntity evaluation, ScreenEvaluationCompleteEntity evaluationComplete,
			ref List<QuoteRequisitionComplete> qtnRequisitionToSave, IEnumerable<QuoteRequisitionEntity> qtnRequisitionToDelete,
			IEnumerable<QuoteTotalEntity> qtnTotalToSave, IEnumerable<ReqTotalEntity> qtnTotalToDelete, IEnumerable<PrcCallOffAgreementEntity> callOffAgreementToSave, IEnumerable<PrcCallOffAgreementEntity> callOffAgreementToDelete,
			IEnumerable<PrcMandatoryDeadlineEntity> mandatoryDeadlineToSave, IEnumerable<PrcMandatoryDeadlineEntity> mandatoryDeadlineToDelete, IEnumerable<CertificateBC.CertificateEntity> certificateToSave, IEnumerable<CertificateBC.CertificateEntity> certificateToDelete,
			IEnumerable<CommonBillingSchemaEntity> billingSchemaToSave, IEnumerable<CommonBillingSchemaEntity> billingSchemaToDelete,
			IEnumerable<ScreenEvaluationCompleteEntity> evaluationToSave, IEnumerable<EvaluationEntity> evaluationToDelete,
			IEnumerable<EvaluationDocumentEntity> evaluationDocumentToSave, IEnumerable<EvaluationDocumentEntity> evaluationDocumentToDelete, IEnumerable<PrcWarrantyEntity> prcWarrantyEntityToSave, IEnumerable<PrcWarrantyEntity> prcWarrantyEntityToDelete, IEnumerable<PrcPackage2ExtBidderComplete> prcPackage2ExtBidderToSave, IEnumerable<PrcPackage2ExtBidderEntity> prcPackage2ExtBidderToDelete, IEnumerable<IIdentifyable> MtgHeaderToSave, IEnumerable<IIdentifyable> MtgHeaderToDelete)
		{

			var completeEntity = new QuoteCompleteEntity();

			var _qtnTotalLogic = new QuoteTotalLogic();
			var _prcItemLogic = new PrcItemLogic();
			var prcCallOffAgreementLogic = new PrcCallOffAgreementLogic();
			var prcMandatoryDeadlineLogic = new PrcMandatoryDeadlineLogic();
			CertificateBC.CertificateLogic _certificateLogic = new CertificateBC.CertificateLogic();
			var _evaluationLogic = new EvaluationLogic();
			var validator = PrcCommonWorkflowEventHelper.BeginCreatedCheck(header);
			var _evaluationDocumentLogic = new EvaluationDocumentLogic();
			var _prcWarrantyLogic = new PrcWarrantyLogic();
			var _prcPackage2ExtBidderLogic = new PrcPackage2ExtBidderLogic();

			if (callOffAgreementToSave != null)
			{
				prcCallOffAgreementLogic.Save(callOffAgreementToSave);
			}
			if (callOffAgreementToDelete != null)
			{
				prcCallOffAgreementLogic.Delete(callOffAgreementToDelete);
			}


			if (mandatoryDeadlineToSave != null)
			{
				prcMandatoryDeadlineLogic.Save(mandatoryDeadlineToSave);
			}
			if (mandatoryDeadlineToDelete != null)
			{
				prcMandatoryDeadlineLogic.Delete(mandatoryDeadlineToDelete);
			}

			// certificate
			if (certificateToSave != null && certificateToSave.Any())
			{
				_certificateLogic.Save(certificateToSave);
			}
			if (certificateToDelete != null && certificateToDelete.Any())
			{
				var certificateDbModel = CertificateBC.ModelBuilder.DbModel;
				_certificateLogic.Delete(certificateDbModel, certificateToDelete);
			}

			var needRecalculateTotals = false;

			if (header != null && header.CurrencyFk != oldHeader.CurrencyFk)
			{
				needRecalculateTotals = true;
			}
			IEnumerable<int> qtnTotalIds = null;
			IEnumerable<QuoteTotalEntity> qtnTotals = null;
			if (qtnTotalToSave != null && qtnTotalToSave.Any())
			{
				qtnTotalIds = qtnTotalToSave.CollectIds(e => e.Id);
				qtnTotals = _qtnTotalLogic.GetSearchList(e => e.HeaderFk == mainItemId && !qtnTotalIds.Contains(e.Id));
				qtnTotals = qtnTotals.ConcatNoRepeat((a, b) => a.Id == b.Id, qtnTotalToSave);
			}
			else
			{
				qtnTotals = _qtnTotalLogic.GetSearchList(e => e.HeaderFk == mainItemId);
			}

			if (prcWarrantyEntityToDelete != null && prcWarrantyEntityToDelete.Any())
			{
				_prcWarrantyLogic.Delete(prcWarrantyEntityToDelete);
			}
			if (prcWarrantyEntityToSave != null && prcWarrantyEntityToSave.Any())
			{
				_prcWarrantyLogic.Save(prcWarrantyEntityToSave);
			}

			if (prcPackage2ExtBidderToDelete != null && prcPackage2ExtBidderToDelete.Any())
			{
				_prcPackage2ExtBidderLogic.Delete(prcPackage2ExtBidderToDelete);
			}
			if (prcPackage2ExtBidderToSave != null && prcPackage2ExtBidderToSave.Any())
			{
				_prcPackage2ExtBidderLogic.SavePackage2ExtBidderComplete(prcPackage2ExtBidderToSave);
			}

			#region Validation
			IEnumerable<PrcItemComplete> prcItemToSave = null;
			IEnumerable<PrcItemEntity> prcItemToDelete = null;

			if (qtnRequisitionToSave != null)
			{
				prcItemToSave = qtnRequisitionToSave.Where(e => e.PrcItemToSave != null).SelectMany(e => e.PrcItemToSave);
				prcItemToDelete = qtnRequisitionToSave.Where(e => e.PrcItemToDelete != null).SelectMany(e => e.PrcItemToDelete);

				foreach (var item in qtnRequisitionToSave)
				{
					var toSave = item.PrcItemToSave ?? new List<PrcItemComplete>();
					var toDelete = item.PrcItemToDelete ?? new List<PrcItemEntity>();
					_prcItemLogic.Validator(toSave, toDelete);
				}
			}
			#endregion

			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				var _requisitionLogic = new QuoteRequisitionLogic();

				var _prcHeaderLogic = new PrcHeaderLogic();

				var procurementCommonDbModel = RIB.Visual.Procurement.Common.BusinessComponents.ModelBuilder.DbModel;
				var cloudCommonDbModel = RIB.Visual.Cloud.Common.BusinessComponents.ModelBuilder.DbModel;
				var procurementRequisitionDbModel = RIB.Visual.Procurement.Requisition.BusinessComponents.ModelBuilder.DbModel;

				if (header != null)
				{
					var evaluationLogic = new EvaluationLogic();
					var evaluationEntityList = new List<EvaluationEntity>();
					if (evaluation != null)
					{
						evaluation.QtnHeaderFk = header.Id;
						evaluationLogic.Save(evaluation);
						header.EvaluationFk = evaluation.Id;
					}
					else if (header.EvaluationFk != null)  // Delete the Evluation
					{
						var evaluationEntity = evaluationLogic.GetItemByKey(header.EvaluationFk.Value);
						if (evaluationEntity != null && evaluationEntity.Id > 0)
						{
							evaluationEntityList.Add(evaluationEntity);
						}
						header.EvaluationFk = null;
					}

					if (evaluationComplete != null)
					{
						if (evaluation != null)
						{
							evaluationComplete.Evaluation = null;
						}
						else if (evaluationComplete.Evaluation != null)
						{
							header.EvaluationFk = evaluationComplete.Evaluation.Id;
							evaluationComplete.Evaluation.QtnHeaderFk = header.Id;
						}
						new EvaluationLogic().Update(evaluationComplete);
					}
					HandleDataEffective(header);
					HandleDataTruncate(header);
					this.Save(header);
					if (evaluationEntityList.Count > 0)
					{
						evaluationLogic.DeleteEntities(evaluationEntityList);
					}
				}

				var qtnVatGroupFk = header != null ? header.BpdVatGroupFk : oldHeader.BpdVatGroupFk;
				_requisitionLogic.Update(qtnRequisitionToSave, (header ?? oldHeader).ExchangeRate, qtnVatGroupFk);

				if ((header ?? oldHeader).ExchangeRate != oldHeader.ExchangeRate && header.DealWithRateUpdateLater != true)
				{
					// the procurement items are updated but is not merged to UI. So when updating again, the program pops up error.
					qtnRequisitionToSave = _requisitionLogic.Recalculate(mainItemId, (header ?? oldHeader).ExchangeRate, qtnRequisitionToSave).ToList();
				}

				if (prcItemToSave != null || prcItemToDelete != null || needRecalculateTotals)
				{
					_qtnTotalLogic.RecalculateTotalsFromHeader((header ?? oldHeader), qtnTotals);
					qtnTotalToSave = qtnTotals;
				}

				_qtnTotalLogic.Save(qtnTotalToSave);

				UpdateSearchPattern(mainItemId);

				// business partner evaluation
				if (evaluationToSave != null && evaluationToSave.Any())
				{
					_evaluationLogic.UpdateComplete(evaluationToSave);
				}
				if (evaluationDocumentToSave != null && evaluationDocumentToSave.Any())
				{
					_evaluationDocumentLogic.Save(evaluationDocumentToSave);
				}
				if (evaluationDocumentToDelete != null && evaluationDocumentToDelete.Any())
				{
					_evaluationDocumentLogic.Delete(evaluationDocumentToDelete);
				}
				if (evaluationToDelete != null && evaluationToDelete.Any())
				{
					_evaluationLogic.DeleteEntities(evaluationToDelete);
				}

				// Save and Recalculate
				if ((header != null && header.DealWithRateUpdateLater != true) || header == null)
				{
					var billingSchemaLogic = new QuoteBillingSchemaLogic();
					var saveHeader = this.GetLookupItemByKey(mainItemId);
					bool IsFromRecalucteBtn = billingSchemaLogic.IsFromRecalucteBtn(saveHeader, billingSchemaToDelete, billingSchemaToSave);
					completeEntity.BillingSchemaToSave = billingSchemaLogic.Update(saveHeader, billingSchemaToDelete, billingSchemaToSave, true, IsFromRecalucteBtn);
				}

				if (header != null && header.DealWithRateUpdateLater != true)
				{
					var quote2prcHeader = Injector.Get<IQuoteRequisitionLogic>().GetQuoteRequisitionByQtnHeaderFk(header.Id);
					if (quote2prcHeader != null)
					{
						int projectId = null != header.ProjectFk ? header.ProjectFk.Value : 0;
						var prcItemPriceConditionLogic = new PrcItemPriceConditionLogic();
						prcItemPriceConditionLogic.ReCalculatePriceCondition("procurement.quote.requisition", quote2prcHeader.PrcHeaderFk, header.Id, projectId, header.ExchangeRate);
					}
				}

				if (header != null && header.DealWithRateUpdateLater == true)
				{
					header.DealWithRateUpdateLater = false;
				}

				transaction.Complete();
			}

			// #89891 - Requirement for new Subscribed Events in Workflow
			validator.EndCreatedCheck(header, PrcCommonWorkflowEventUuids.NewQuoteCreated);

			if (validator.IsNewEntityCreated() && header != null)
			{
				header = this.GetItemByKeyComplete(header.Id);
			}

			completeEntity.QuoteHeader = header;
			GetPackageForQtnHeader(completeEntity.QuoteHeader);

			// Save meeting
			if (MtgHeaderToSave != null && MtgHeaderToSave.Any())
			{
				var meetingLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IDataBaseLogic>("Basics.Meeting.MeetingLogic");
				meetingLogic.Save(MtgHeaderToSave);
			}

			// Delete meeting
			if (MtgHeaderToDelete != null && MtgHeaderToDelete.Any())
			{
				var meetingLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IDataBaseLogic>("Basics.Meeting.MeetingLogic");
				meetingLogic.Delete(MtgHeaderToDelete);
			}

			// Total Fields
			if (completeEntity.QuoteHeader == null)
			{
				completeEntity.QuoteHeader = this.GetLookupItemByKey(mainItemId);
			}
			var qthHeaders = new List<QuoteHeaderEntity>() { completeEntity.QuoteHeader };
			GetCo2Total(qthHeaders);
			this.RecalculateTotalFields(qthHeaders);
			this.FillProjectFkAndPackageFkFromRequisition(qthHeaders);
			completeEntity.QuoteHeader = qthHeaders[0];
			return completeEntity;
		}

		#region IEntityFacade Methods

		private static ConvertProperties _entityProperties = new ConvertProperties()
			.Add("Id")
			.Add("StatusFk")
			.Add("CompanyFk")
			.Add("ProjectFk")
			.Add("ClerkPrcFk")
			.Add("ClerkReqFk")
			.Add("CurrencyFk")
			.Add("ExchangeRate")
			.Add("PaymentTermFiFk")
			.Add("PaymentTermPaFk")
			.Add("Code")
			.Add("Description")
			.Add("SearchPattern")
			.Add("DateQuoted")
			.Add("DateReceived")
			.Add("TypeFk")
			.Add("BusinessPartnerFk")
			.Add("SubsidiaryFk")
			.Add("SupplierFk")
			.Add("IncotermFk")
			.Add("Remark")
			.Add("UserDefined1")
			.Add("UserDefined2")
			.Add("UserDefined3")
			.Add("UserDefined4")
			.Add("UserDefined5")
			.Add("RfqHeaderFk")
			.Add("QuoteVersion")
			.Add("DatePricefixing")
			.Add("IsValidated")
			.Add("IsExcluded")
			.Add("IsShortlisted")
			.Add("EvaluationFk")
			.Add("BpdVatGroupFk")
			.Add("SalesTaxMethodFk");

		/// <summary>
		/// Name of the entity
		/// </summary>
		string IEntityFacade.Name
		{
			get { return "QuoteHeaderEntity"; }
		}
		/// <summary>
		/// UUID to clearly determine the entity provider
		/// </summary>
		public string Id
		{
			get { return "0D54719184C94DDEB5F725B4FA5922D2"; }
		}
		/// <summary>
		/// Module name
		/// </summary>
		string IEntityFacade.ModuleName
		{
			get { return "procurement.quote"; }
		}
		/// <summary>
		/// Get a QuoteHeaderEntity by Id
		/// </summary>
		IDictionary<string, object> IEntityFacade.Get(int id)
		{
			var entity = GetItemByKey(id);
			var objectDic = ToDictionary(entity);

			return objectDic;
		}

		private IDictionary<string, object> ToDictionary(QuoteHeaderEntity entity)
		{
			var objectDic = entity.AsDictionary(_entityProperties);
			return objectDic;
		}


		/// <summary>
		/// Save a QuoteHeaderEntity
		/// </summary>
		IDictionary<string, object> IEntityFacade.Save(IDictionary<string, object> entityDictionary)
		{
			int id = entityDictionary.GetId<int>();
			var entity = GetItemByKey(id);
			entity.SetObject(entityDictionary, _entityProperties);
			Save(entity);
			return ToDictionary(entity);
		}

		/// <summary>
		/// Get Entity Properties
		/// </summary>
		string[] IEntityFacade.Properties
		{
			get
			{
				return _entityProperties.GetPropertyNames();
			}
		}

		#endregion

		/// <summary>
		/// Change the status of entity according to the given id
		/// </summary>
		/// <param name="identification">entity identification data</param>
		/// <param name="statusId">new status id</param>
		public EntityBase ChangeStatus(IStatusIdentifyable identification, int statusId)
		{
			return UpdateStatus(identification.Id, statusId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="quoteId"></param>
		/// <param name="billingSchemaList"></param>
		/// <returns></returns>
		public List<QtnBillingSchemaRowEntity> GetBillingSchemasByQuoteId(int quoteId, IEnumerable<CommonBillingSchemaEntity> billingSchemaList = null)
		{
			var cache = new Dictionary<int, IEnumerable<CommonBillingSchemaEntity>>();
			if (billingSchemaList != null)
			{
				cache.Add(quoteId, billingSchemaList);
			}
			return this.GetBillingSchemasByQuoteIds(new List<int>() { quoteId }, cache).FirstOrDefault().Value.ToList();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="quoteIds"></param>
		/// <param name="cache"></param>
		/// <returns></returns>
		public Dictionary<int, IEnumerable<QtnBillingSchemaRowEntity>> GetBillingSchemasByQuoteIds(IEnumerable<int> quoteIds, Dictionary<int, IEnumerable<CommonBillingSchemaEntity>> cache = null)
		{
			var simpleLookupLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<ISimpleLookupValueProvider>();
			var simpleLookupDescriptor = BusinessApplication.BusinessEnvironment.GetExportedValue<ISimpleLookupDescriptor>();
			simpleLookupDescriptor.DisplayProperty = "Description";
			simpleLookupDescriptor.ValueProperty = "Id";
			simpleLookupDescriptor.IsLive = false;
			simpleLookupDescriptor.LookupQualifier = "basics.customize.costlinetype";
			var costLineTypeList = simpleLookupLogic.GetList(simpleLookupDescriptor);

			var allBillingSchemaList = new QuoteBillingSchemaLogic().GetBillingSchemas(quoteIds);
			var allBillingSchemaDetailIds = allBillingSchemaList.Select(e => e.MdcBillingSchemaDetailFk);
			var allBillingSchemaDetailList = new BillingSchemaDetailLogic().GetItemsByKeyComplete(allBillingSchemaDetailIds).Where(e => e.CostLineTypeFk != null);

			var results = new Dictionary<int, IEnumerable<QtnBillingSchemaRowEntity>>();

			foreach (var quoteId in quoteIds.Distinct())
			{
				var resultList = new List<QtnBillingSchemaRowEntity>();

				var billingSchemaList = cache != null && cache.ContainsKey(quoteId) && cache[quoteId] != null ? cache[quoteId] : allBillingSchemaList.Where(e => e.HeaderFk == quoteId);
				var billingSchemaDetailIds = billingSchemaList.Select(e => e.MdcBillingSchemaDetailFk);
				var mdcBillingSchemaList = allBillingSchemaDetailList.Where(e => billingSchemaDetailIds.Contains(e.Id));

				foreach (var basic in mdcBillingSchemaList)
				{
					var billingSchema = billingSchemaList.FirstOrDefault(e => e.MdcBillingSchemaDetailFk == basic.Id);
					if (billingSchema != null && billingSchema.CostLineTypeFk.HasValue)
					{
						var qtnBillingSchemaRow = resultList.FirstOrDefault(e => e.CostLineTypeId == billingSchema.CostLineTypeFk.Value);
						if (qtnBillingSchemaRow != null)
						{
							qtnBillingSchemaRow.CostLineTypeResult += billingSchema.Result;
						}
						else
						{
							var costLineType = costLineTypeList.FirstOrDefault(e => e.Id == billingSchema.CostLineTypeFk.Value);
							qtnBillingSchemaRow = new QtnBillingSchemaRowEntity()
							{
								CostLineTypeId = billingSchema.CostLineTypeFk.Value,
								CostLineTypeDescription = costLineType?.DescriptionInfo?.Description,
								CostLineTypeTranlate = costLineType?.DescriptionInfo?.Translated,
								CostLineTypeResult = billingSchema.Result,
								QuoteId = quoteId
							};

							resultList.Add(qtnBillingSchemaRow);
						}
					}
				}

				results.Add(quoteId, resultList);
			}

			return results;
		}

		/// <summary>
		/// Get the status of the entity according to the given id
		/// </summary>
		/// <param name="identification">entity identification data</param>
		/// <returns>Current status id</returns>
		public int GetCurrentStatus(IStatusIdentifyable identification)
		{
			var item = this.GetItemByKey(identification.Id);
			return item != null ? item.StatusFk : 0;
		}

		/// <summary>
		/// Try convert string to int,if fail return defValue.
		/// </summary>
		/// <param name="value">The value to be convert.</param>
		/// <param name="defValue">The default value to be return when convert fail.</param>
		/// <returns></returns>
		private int TryParseInt(string value, int defValue = 0)
		{
			int result;
			if (!int.TryParse(value, out result))
			{
				result = defValue;
			}
			return result;
		}

		/// <summary>
		/// Get bidder ids.
		/// </summary>
		/// <param name="rfqHeaderFk"></param>
		/// <returns></returns>
		public IEnumerable<int> GetBidderBusinessPartnerIds(int rfqHeaderFk)
		{
			var bidders = this.GetBidders(rfqHeaderFk);
			if (bidders != null)
			{
				return bidders.Select(e => e.BusinessPartnerFk).ToList();
			}
			return [];
		}

		/// <summary>
		/// Gets bidders.
		/// </summary>
		/// <param name="rfqHeaderFk"></param>
		/// <returns></returns>
		public IEnumerable<RfqBusinessPartnerEntity> GetBidders(int rfqHeaderFk)
		{
			var rfqBusinessPartners = new RfqBusinessPartnerLogic().GetSearchList(e => e.RfqHeaderFk == rfqHeaderFk);

			var result = new List<RfqBusinessPartnerEntity>();

			var selfBps = new List<RfqBusinessPartnerEntity>();

			if (rfqBusinessPartners != null)
			{
				selfBps = rfqBusinessPartners.ToList();
			}


			//if have base rfq only return bidders with the base rfq has created quotes.
			var rfqHeaderLogic = new RfqHeaderLogic();
			var rfqHeader = rfqHeaderLogic.GetItemByKey(rfqHeaderFk);
			if (rfqHeader != null && rfqHeader.RfqHeaderFk.HasValue)
			{
				var baseQuotes = this.GetQuotesByRfqHeaderFk(rfqHeader.RfqHeaderFk.Value);
				var baseRfqBusinessPartners = new RfqBusinessPartnerLogic().GetSearchList(e => e.RfqHeaderFk == rfqHeader.RfqHeaderFk.Value);
				if (baseRfqBusinessPartners != null && baseRfqBusinessPartners.Any())
				{
					var availableStatus = new RfqBusinessPartnerStatusLogic().GetSearchList(e => e.IsRequested || e.IsQuoted);
					var statusIds = availableStatus.Select(e => e.Id).ToList();
					if (statusIds.Any())
					{
						baseRfqBusinessPartners = baseRfqBusinessPartners.Where(e => statusIds.Contains(e.RfqBusinesspartnerStatusFk));
					}

					var bps = baseRfqBusinessPartners.Where(bp => baseQuotes.Select(q => q.BusinessPartnerFk).Contains(bp.BusinessPartnerFk));

					var defaultstatus = new RfqBusinessPartnerStatusLogic().GetDefault();

					selfBps = selfBps.Where(e => statusIds.Contains(e.RfqBusinesspartnerStatusFk)).ToList();

					var tempBidders = new List<RfqBusinessPartnerEntity>();
					foreach (var bp in bps)
					{
						if (!selfBps.Any(e => e.BusinessPartnerFk == bp.BusinessPartnerFk))
						{
							bp.RfqBusinesspartnerStatusFk = defaultstatus.Id;
							tempBidders.Add(bp);
						}
						else
						{
							tempBidders.Add(selfBps.First(e => e.BusinessPartnerFk == bp.BusinessPartnerFk));
						}
					}

					var baseRfqHeaderFk = rfqHeader.RfqHeaderFk.Value;
					var baseQtns = this.GetSearchList(e => e.RfqHeaderFk == baseRfqHeaderFk);

					foreach (var tempBidder in tempBidders)
					{
						var hasBase = baseQtns.Any(e => e.BusinessPartnerFk == tempBidder.BusinessPartnerFk);
						if (hasBase)
						{
							result.Add(tempBidder);
						}
					}

				}
			}
			else
			{
				result = selfBps;
			}

			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerId"></param>
		/// <returns></returns>
		public IEnumerable<IBoqItemEntity> GetBoqItemsByHeaderId(int headerId)
		{

			var prcHeaders = new QuoteRequisitionLogic().GetSearchList(e => e.QtnHeaderFk == headerId).Select(e => e.PrcHeaderFk).ToList();
			var boqHeaders = new PrcBoqLogic().GetSearchList(e => prcHeaders.Contains(e.PrcHeaderFk)).Select(e => e.BoqHeaderFk);

			var boqItemLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IBoqItemLogic>();
			return boqItemLogic.GetBoqRootItemsByBoqHeaderIds(boqHeaders);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="headerId"></param>
		/// <returns></returns>
		public IEnumerable<PrcItemEntity> GetPrcItemsByHeaderId(int headerId)
		{
			var itemLogic = new PrcItemLogic();
			var requisitionLogic = new QuoteRequisitionLogic();

			var prcHeaders = requisitionLogic.GetSearchList(e => e.QtnHeaderFk == headerId).Select(e => e.PrcHeaderFk).ToList();

			var roots = itemLogic.GetSearchList(e => e.PrcReplacementItemFk == null && prcHeaders.Contains(e.PrcHeaderFk));
			if (roots != null && roots.Any())
			{
				var rootIds = roots.Select(e => e.Id);

				var children = itemLogic.GetSearchList(e => e.PrcReplacementItemFk != null && rootIds.Contains(e.PrcReplacementItemFk.Value));
				if (children != null && children.Any())
				{
					foreach (var root in roots)
					{
						var items = children.Where(e => e.PrcReplacementItemFk == root.Id);
						root.HasReplacementItem = items.Any();
						if (root.HasReplacementItem)
						{
							root.ReplacementItems = items.ToList();
						}
					}
				}
			}

			return roots;
		}

		/// <summary>
		/// Gets first requisiton's package and related prc structures by quote.
		/// </summary>
		/// <param name="entities"></param>
		public void FillProjectFkAndPackageFkFromRequisition(IEnumerable<QuoteHeaderEntity> entities)
		{

			Dictionary<int, int> quote2ReqCaches = new Dictionary<int, int>();
			Dictionary<int, int> quote2StructureIds = new Dictionary<int, int>();
			Dictionary<int, (string, string)> quote2PrcStructures = new Dictionary<int, (string, string)>();
			var quoteFks = entities.Select(e => e.Id);
			var requisitions = new QuoteRequisitionLogic().GetSearchList(e => quoteFks.Contains(e.QtnHeaderFk));


			if (requisitions != null && requisitions.Any())
			{
				var prcHeaderIds = requisitions.Select(e => e.PrcHeaderFk).Distinct().ToArray();
				var prcHeaders = new PrcHeaderLogic().GetSearchList(e => prcHeaderIds.Contains(e.Id)).ToArray();
				var strunctureIds = prcHeaders.Select(p => p.StructureFk).Distinct().ToArray();
				var prcStructures = new PrcStructureLogic().GetSearchList(e => strunctureIds.Contains(e.Id)).ToArray();

				var reqGroups = requisitions.GroupBy(e => e.QtnHeaderFk);
				foreach (var reqGroup in reqGroups)
				{
					var first = reqGroup.OrderBy(e => e.Id).FirstOrDefault();
					if (first != null)
					{
						quote2ReqCaches[reqGroup.Key] = first.ReqHeaderFk;
					}
					// fill prcStructures.
					var prcHeader = prcHeaders.FirstOrDefault(e => e.Id == first.PrcHeaderFk);
					var structure = prcStructures.FirstOrDefault(e => e.Id == prcHeader?.StructureFk);
					if (reqGroup.Count() == 1)
					{
						quote2PrcStructures[reqGroup.Key] = (structure?.Code, structure?.DescriptionInfo?.Description);
					}
					else if (reqGroup.Count() > 1)
					{
						quote2PrcStructures[reqGroup.Key] = ("*", "*");
					}
					//set the first req structure as the quote structure id, to fixed issue:#136131
					if (structure != null)
					{
						quote2StructureIds[reqGroup.Key] = structure.Id;
					}

				}
			}

			var reqFks = quote2ReqCaches.Select(e => e.Value);
			var reqHeaders = new ReqHeaderLogic().GetSearchList(e => reqFks.Contains(e.Id));

			Dictionary<int, int?> quote2Package = new Dictionary<int, int?>();
			Dictionary<int, int?> quote2ReqProject = new Dictionary<int, int?>();

			foreach (var item in quote2ReqCaches)
			{
				var first = reqHeaders.FirstOrDefault(e => e.Id == item.Value);
				if (first != null)
				{
					quote2Package[item.Key] = first.PackageFk;
					quote2ReqProject[item.Key] = first.ProjectFk;
				}

			}

			foreach (var entity in entities)
			{
				if (quote2Package.ContainsKey(entity.Id))
				{
					entity.PackageFk = quote2Package[entity.Id];
				}

				if (quote2ReqProject.ContainsKey(entity.Id))
				{
					entity.RequisitonProjectFk = quote2ReqProject[entity.Id];
				}

				if (quote2PrcStructures.TryGetValue(entity.Id, out var structure))
				{
					entity.PrcStructureCode = structure.Item1;
					entity.PrcStructureDescription = structure.Item2;
				}
				if (quote2StructureIds.ContainsKey(entity.Id))
				{
					entity.PrcStructureFk = quote2StructureIds[entity.Id];
				}
			}
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public IQuoteData GetQutoeHeaderByKey(int id)
		{
			return this.GetItemByKey(id);
		}

		/// <summary>
		/// Get quote data only.
		/// </summary>
		/// <param name="id">quote id</param>
		/// <returns></returns>
		public IQuoteData GetPureHeaderById(int id)
		{
			return this.GetSearchList(e => e.Id == id).FirstOrDefault();
		}

		/// <summary>
		/// implement from IQuoteHeaderLogic
		/// </summary>
		/// <param name="companyId"></param>
		/// <returns></returns>
		public IEnumerable<IQuoteData> GetQuoteHeadersByCompanyId(int companyId)
		{
			return this.GetSearchList(e => e.CompanyFk == companyId);
		}

		/// <summary>
		/// implement from IQuoteHeaderLogic
		/// </summary>
		/// <param name="companyId"></param>
		/// <returns></returns>
		public IEnumerable<IQuoteData> GetValidQuotesByCompanyId(int companyId)
		{
			var quoteStatus = new QuoteStatusLogic().GetList().Where(e => !e.IsVirtual && e.IsLive);
			var quoteStatusIds = quoteStatus.Select(e => e.Id);
			return this.GetSearchList(e => e.CompanyFk == companyId && quoteStatusIds.Contains(e.StatusFk));
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="ids"></param>
		/// <returns></returns>
		public IEnumerable<IQuoteData> GetQutoeHeadersByIds(IEnumerable<int> ids)
		{
			return this.GetSearchList(e => ids.Contains(e.Id));
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IEnumerable<IQuoteData> GetQuoteHeadersByProjectId(int projectId)
		{
			return this.GetSearchList(e => e.ProjectFk == projectId);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="filePath"></param>
		/// <param name="mainItemId"></param>
		public ProcurementMaterialXmlData ImportQuote(string filePath, int mainItemId)
		{
			XmlReaderLogic XMLlogic = new XmlReaderLogic();
			var prcItemLogic = new PrcItemLogic();
			var data = XMLlogic.ReadXmlFile(filePath, mainItemId);
			return data != null ? XMLlogic.ImportQuote(data, mainItemId) : null;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="xmlData"></param>
		/// <param name="mainItemId"></param>
		public ProcurementMaterialXmlData ImportQuote(ProcurementMaterialXmlData xmlData, int mainItemId)
		{
			XmlReaderLogic XMLlogic = new XmlReaderLogic();
			return xmlData != null ? XMLlogic.ImportQuote(xmlData, mainItemId, true) : null;
		}


		/// <summary>
		/// Add Prc Item / Boq Item to a specific quote
		/// </summary>
		/// <param name="quoteHeader"></param>
		/// <param name="qtnRequisitionToSave"></param>
		/// <returns>if add prc item, return the new packge item</returns>
		private PrcItemEntity AddPrcItemToOneQuote(QuoteHeaderEntity quoteHeader, IEnumerable<QuoteRequisitionComplete> qtnRequisitionToSave)
		{
			if (qtnRequisitionToSave.IsNullOrEmpty())
			{
				return null;
			}

			var requisitionLogic = new QuoteRequisitionLogic();

			var quoteId = quoteHeader.Id;
			PrcItemEntity newQuoteItem = null;

			// update prcItem
			if (qtnRequisitionToSave.Any())
			{

				var prcItemToSave = qtnRequisitionToSave.Where(e => e.PrcItemToSave != null).SelectMany(e => e.PrcItemToSave).ToList();
				if (prcItemToSave.Any())
				{
					RemoveRedundantPrcItem(prcItemToSave); //
					newQuoteItem = prcItemToSave.ElementAt(0).PrcItem;
					newQuoteItem.PrcItemFk = newQuoteItem.Id;
					newQuoteItem.InstanceId = newQuoteItem.PrcHeaderFk;
				}
			}

			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				requisitionLogic.Update(qtnRequisitionToSave, quoteHeader.ExchangeRate);

				this.RecalculateBoqTree(quoteHeader, qtnRequisitionToSave);

				this.RecalculateTotalsFromHeader(quoteHeader);

				transaction.Complete();
			}

			return newQuoteItem;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="rfqHeaderId"></param>
		/// <param name="billingSchemaId"></param>
		/// <param name="selectedQtnId"></param>
		/// <returns></returns>
		public Dictionary<string, object> ChangeBillingSchema(int rfqHeaderId, int? billingSchemaId, int selectedQtnId)
		{

			Dictionary<string, object> result = new Dictionary<string, object>();
			var rfqHeaderLogic = new RfqHeaderLogic();
			var rfqHeader = rfqHeaderLogic.GetSearchList(e => e.Id == rfqHeaderId).FirstOrDefault();
			if (rfqHeader != null)
			{
				rfqHeader.BillingSchemaFk = billingSchemaId;
			}

			var qtnHeaderList = GetQuotesByRfqHeaderFk(rfqHeaderId);

			var billingSchemaLogic = new QuoteBillingSchemaLogic();
			var selectedQuote = new QuoteHeaderEntity();
			if (qtnHeaderList != null && qtnHeaderList.Any())
			{
				foreach (var qtn in qtnHeaderList)
				{
					qtn.BillingSchemaFk = billingSchemaId;
				}
			}

			// Save and Recalculate
			using (TransactionScope transaction = TransactionScopeFactory.CreateRequiresNew())
			{
				rfqHeaderLogic.Save(rfqHeader);
				this.Save(qtnHeaderList);

				if (qtnHeaderList != null && qtnHeaderList.Any())
				{
					var rubricCategories = new RIB.Visual.Basics.MasterData.BusinessComponents.RubricCategoryLogic().GetFilteredEntity(e => e.RubricFk == RubricConstant.Quotation);
					var prcConfigurations = new PrcConfigurationLogic().GetList();
					foreach (var qtn in qtnHeaderList)
					{
						var prcConfiguration = prcConfigurations.FirstOrDefault(e => e.Id == qtn.PrcConfigurationFk);
						var rubricCategory = rubricCategories.FirstOrDefault(e => e.Id == prcConfiguration.RubricCategoryFk);
						if (rubricCategory != null)
						{
							var billingSchemaDelete = billingSchemaLogic.GetBillingSchemas(qtn.Id);
							var billingSchemaToDelete = billingSchemaDelete != null ? billingSchemaDelete.ToList() : null;
							var billingSchemaSave = billingSchemaLogic.Create(qtn.Id, qtn.BillingSchemaFk ?? -1, rubricCategory.Id);
							var billingSchemaToSave = billingSchemaSave != null ? billingSchemaSave.ToList() : null;
							billingSchemaLogic.Update(qtn, billingSchemaToDelete, billingSchemaToSave, true);

							if (qtn.Id == selectedQtnId)
							{
								selectedQuote = qtn;
							}
						}
					}
				}
				transaction.Complete();

			}

			result.Add("selectedQuote", selectedQuote);
			result.Add("quoteHeaders", qtnHeaderList);
			result.Add("rfqHeader", rfqHeader);
			return result;

		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtnHeader"></param>
		private void RecalculateTotalsFromHeader(QuoteHeaderEntity qtnHeader)
		{
			var qtnTotalLogic = new QuoteTotalLogic();
			var qtnTotals = qtnTotalLogic.GetSearchList(e => e.HeaderFk == qtnHeader.Id);
			qtnTotalLogic.RecalculateTotalsFromHeader(qtnHeader, qtnTotals);
			qtnTotalLogic.Save(qtnTotals);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="quoteHeader"></param>
		/// <param name="qtnRequisitionToSave"></param>
		private void RecalculateBoqTree(QuoteHeaderEntity quoteHeader, IEnumerable<QuoteRequisitionComplete> qtnRequisitionToSave)
		{
			var prcBoqCompleteToSave = qtnRequisitionToSave.Where(e => e.PrcBoqCompleteToSave != null).Select(e => e.PrcBoqCompleteToSave).Where(e => e.BoqItemCompleteToSave != null).SelectMany(e => e.BoqItemCompleteToSave).ToList();
			if (prcBoqCompleteToSave.Any())
			{
				var boqItemLogic = new BoqItemLogic();
				var boqPriceConditionLogic = new BoqPriceconditionLogic();
				var boqItemToSave = prcBoqCompleteToSave.Where(e => e.BoqItem != null).Select(e => e.BoqItem).ToList();
				var boqItemIds = boqItemToSave.CollectIds(e => e.Id);
				if (prcBoqCompleteToSave.ElementAt(0).BoqItem != null)
				{
					var boqHeaderId = prcBoqCompleteToSave.ElementAt(0).BoqItem.BoqHeaderFk;
					var boqItems = boqItemLogic.GetBoqItems(boqHeaderId);
					var boqRootItem = boqItems.FirstOrDefault(e => e.BoqItemFk == null);
					boqItemLogic.SetCurrentExchangeRateHc2Oc(quoteHeader.ExchangeRate);
					var boqCalculateLogic = new BoqItemCalculateLogic("procurement.quote", quoteHeader.Id, quoteHeader.ExchangeRate);
					var calculationResult = boqCalculateLogic.CalculateBoqTreeAndPriceconditions(boqRootItem);
					boqItemLogic.SaveBoqTree(boqRootItem);

					if (calculationResult != null && calculationResult.BoqPriceconditionChanged != null)
					{
						boqPriceConditionLogic.SaveEntities(calculationResult.BoqPriceconditionChanged);
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="currentQuoteHeaderId"></param>
		/// <param name="getCloneQtnRequisitionToSave"></param>
		/// <param name="addToOne"></param>
		/// <param name="insertOptions"></param>
		public void InsertItem(int currentQuoteHeaderId,Func<IEnumerable<QuoteRequisitionComplete>> getCloneQtnRequisitionToSave, bool addToOne, CreateInsertItemOptions insertOptions = null)
		{
			var quoteHeader = this.GetItemByKey(currentQuoteHeaderId);

			if (insertOptions != null && insertOptions.ToRequisition)
			{
				this.AddItemToRequisitionAndQuotes(quoteHeader, getCloneQtnRequisitionToSave, addToOne, insertOptions);
			}
			else
			{
				this.AddItemToQuotes(quoteHeader, getCloneQtnRequisitionToSave, addToOne, insertOptions);
			}
		}

		/// <summary>
		/// Add new prc item / boq item to a specific quote.
		/// </summary>
		/// <param name="quoteHeader"></param>
		/// <param name="getCloneQtnRequisitionToSave"></param>
		/// <param name="addToOne"></param>
		/// <param name="insertOptions"></param>
		public void AddItemToQuotes(QuoteHeaderEntity quoteHeader, Func<IEnumerable<QuoteRequisitionComplete>> getCloneQtnRequisitionToSave, bool addToOne, CreateInsertItemOptions insertOptions = null)
		{
			var qtnRequisitionToSave = getCloneQtnRequisitionToSave();
			var firstQtnReqToSave = qtnRequisitionToSave.FirstOrDefault();

			if (firstQtnReqToSave == null)
			{
				return;
			}

			var rfqHeaderId = quoteHeader.RfqHeaderFk;
			var prcBoqCompleteToSave = qtnRequisitionToSave.Where(e => e.PrcBoqCompleteToSave != null).Select(e => e.PrcBoqCompleteToSave).Where(e => e.BoqItemCompleteToSave != null).SelectMany(e => e.BoqItemCompleteToSave).ToList();
			var otherQuotes = !addToOne && insertOptions?.ToOtherQuotations != null ? this.GetSearchList(e => insertOptions.ToOtherQuotations.Contains(e.Id)) : null;

			if (insertOptions != null && insertOptions.ToNewVersion)
			{
				var copyResult = this.CopyQuote(quoteHeader);
				var newQuoteHeader = copyResult.TargetQtnHeaders.First();

				if (!addToOne && !otherQuotes.IsNullOrEmpty())
				{
					otherQuotes = this.CopyQuote(otherQuotes).TargetQtnHeaders;
				}

				#region Move Boq Items

				var fromEntities = GetBoqItemsByQuoteId(quoteHeader.Id);
				var toEntities = GetBoqItemsByQuoteId(newQuoteHeader.Id);
				var boqItems = CollectBoqItems(qtnRequisitionToSave);

				// Because boq is add from quote, it should be move to new quote first.
				MoveBoqItemsToOtherTree(boqItems, fromEntities, toEntities);

				#endregion

				#region Move Prc Items
				var fromPrcEntities = GetPrcItemsByQuoteId(quoteHeader.Id);
				var toPrcEntities = GetPrcItemsByQuoteId(newQuoteHeader.Id);
				var prcItems = CollectPrcItems(qtnRequisitionToSave);

				MovePrcItemsToOtherTree(prcItems, fromPrcEntities, toPrcEntities);
				#endregion

				quoteHeader = newQuoteHeader;
			}

			// Prc Item
			if (qtnRequisitionToSave.Any())
			{
				var newQuoteItem = AddPrcItemToOneQuote(quoteHeader, qtnRequisitionToSave);
				if (newQuoteItem != null)
				{
					CreateAndSaveQuotePrcItemsFromNewItemToOtherQuotes(newQuoteItem, rfqHeaderId, firstQtnReqToSave.ReqHeaderId, quoteHeader.Id, addToOne, otherQuotes, insertOptions);
				}
			}
			// BoQ Item
			if (qtnRequisitionToSave.Any())
			{
				var boqItems = prcBoqCompleteToSave.Where(e => e.BoqItem != null).Select(e => e.BoqItem).ToList();
				if (boqItems.Any())
				{
					CreateAndSaveQuoteBoqItemsFromPackageToOtherQuotes(boqItems, rfqHeaderId, quoteHeader.Id, addToOne, otherQuotes, insertOptions);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="quoteId"></param>
		/// <returns></returns>
		private static IEnumerable<BoqItemEntity> GetBoqItemsByQuoteId(int quoteId)
		{
			var prcHeaders = new QuoteRequisitionLogic().GetSearchList(e => e.QtnHeaderFk == quoteId).Select(e => e.PrcHeaderFk).ToList();
			var boqHeaders = new PrcBoqLogic().GetSearchList(e => prcHeaders.Contains(e.PrcHeaderFk)).Select(e => e.BoqHeaderFk);

			return new BoqItemLogic().GetSearchList(e => boqHeaders.Contains(e.BoqHeaderFk));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="quoteId"></param>
		/// <returns></returns>
		private static IEnumerable<PrcItemEntity> GetPrcItemsByQuoteId(int quoteId)
		{
			var prcHeaderIds = new QuoteRequisitionLogic().GetSearchList(e => e.QtnHeaderFk == quoteId).Select(e => e.PrcHeaderFk).ToList();

			return new PrcItemLogic().GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="quoteHeader"></param>
		/// <param name="getCloneQtnRequisitionToSave"></param>
		/// <param name="addToOne"></param>
		/// <param name="insertOptions"></param>
		private void AddItemToRequisitionAndQuotes(QuoteHeaderEntity quoteHeader, Func<IEnumerable<QuoteRequisitionComplete>> getCloneQtnRequisitionToSave, bool addToOne, CreateInsertItemOptions insertOptions = null)
		{
			var qtnRequisitionToSave = getCloneQtnRequisitionToSave();
			if (quoteHeader == null || qtnRequisitionToSave == null || insertOptions == null)
			{
				return;
			}

			var boqItemLogic = new BoqItemLogic();
			var reqLogic = new ReqHeaderLogic();
			var qtnReqLogic = new QuoteRequisitionLogic();
			var otherQuotes = !addToOne && insertOptions?.ToOtherQuotations != null ? this.GetSearchList(e => insertOptions.ToOtherQuotations.Contains(e.Id)) : null;
			var quotes = new List<QuoteHeaderEntity>() { quoteHeader }.Concat(otherQuotes ?? []);

			#region BoQ Items

			var fromEntities = GetBoqItemsByQuoteId(quoteHeader.Id);
			var toEntities = reqLogic.GetBoqItemsByReqHeaderId(qtnRequisitionToSave.First().ReqHeaderId);

			// If the new item already exists in requisition, remove it from the complete list.
			var boqItems = CollectBoqItems(qtnRequisitionToSave, toEntities, true);

			if (boqItems.Any(e => e.Version == 0))
			{
				// Because boq is add from quote, it should be move to requisition first.
				MoveBoqItemsToOtherTree(boqItems.Where(e => e.Version == 0), fromEntities, toEntities);
			}

			#endregion

			#region Prc Item

			var fromPrcEntities = GetPrcItemsByQuoteId(quoteHeader.Id);
			var toPrcEntities = reqLogic.GetPrcItemsByReqHeaderId(qtnRequisitionToSave.First().ReqHeaderId);

			var prcItems = CollectPrcItems(qtnRequisitionToSave, toPrcEntities, true);

			if (prcItems.Any(e => e.Version == 0))
			{
				MovePrcItemsToOtherTree(prcItems.Where(e => e.Version == 0), fromPrcEntities, toPrcEntities);
			}

			#endregion

			if (boqItems.Any(e => e.Version == 0) || prcItems.Any(e => e.Version == 0))
			{
				// Save to requisition.
				reqLogic.Update(qtnRequisitionToSave.First());
			}

			var originQtnHeaderId = quoteHeader.Id;
			if (insertOptions.ToNewVersion)
			{
				var copyResult = this.CopyQuote(quotes);
				quotes = copyResult.TargetQtnHeaders;

				originQtnHeaderId = copyResult.QtnHeaderSourceTargetIds.First(e => e.Key == originQtnHeaderId).Value;
			}

			fromEntities = reqLogic.GetBoqItemsByReqHeaderId(qtnRequisitionToSave.First().ReqHeaderId);
			fromPrcEntities = reqLogic.GetPrcItemsByReqHeaderId(qtnRequisitionToSave.First().ReqHeaderId);

			// Copy boq items and prc items from requisition to quotes
			foreach (var quote in quotes)
			{
				var newQtnRequisitionToSave = getCloneQtnRequisitionToSave();

				var newToEntities = GetBoqItemsByQuoteId(quote.Id);
				var replacements = boqItems.Select(e => (e.CloneAll() as BoqItemEntity).InitVersionInfo());
				var resetPrice = !quote.IsIdealBidder && (insertOptions.ToNewVersion ? quote.Id != originQtnHeaderId : quote.Id != quoteHeader.Id);
				var newBoqItems = CollectBoqItems(newQtnRequisitionToSave, replacements, null, true, resetPrice);
				MoveBoqItemsToOtherTree(newBoqItems, fromEntities, newToEntities);

				var newToPrcEntities = GetPrcItemsByQuoteId(quote.Id);
				var prcReplacements = prcItems.Select(e => (e.Clone() as PrcItemEntity).InitVersionInfo());
				var newPrcItems = CollectPrcItems(newQtnRequisitionToSave, prcReplacements, null, true);
				if ((!quote.IsIdealBidder || !addToOne) && quote.Id != originQtnHeaderId)
				{
					foreach (var prcItem in newPrcItems)
					{
						ClonePrcHeaderLogic.ResetFieldsIfFromReq2Qtn(prcItem, false);
					}
				}
				MovePrcItemsToOtherTree(newPrcItems, fromPrcEntities, newToPrcEntities);

				qtnReqLogic.Update(newQtnRequisitionToSave, quote.ExchangeRate, quote.BpdVatGroupFk);

				this.RecalculateBoqTree(quote, newQtnRequisitionToSave);
				this.RecalculateTotalsFromHeader(quote);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="tobeChanged"></param>
		/// <param name="fromEntities"></param>
		/// <param name="toEntities"></param>
		private static void MoveBoqItemsToOtherTree(IEnumerable<BoqItemEntity> tobeChanged, IEnumerable<BoqItemEntity> fromEntities, IEnumerable<BoqItemEntity> toEntities)
		{
			if (tobeChanged.IsNullOrEmpty() || fromEntities.IsNullOrEmpty() || toEntities.IsNullOrEmpty())
			{
				return;
			}

			var boqItemLogic = new BoqItemLogic();
			var idEnumerator = boqItemLogic.GetNextIds(tobeChanged.Count()).GetEnumerator();
			foreach (var change in tobeChanged)
			{
				idEnumerator.MoveNext();
				change.BoqHeaderFk = toEntities.First().BoqHeaderFk;
				change.Id = idEnumerator.Current;
				change.BoqItemParent = null;
				change.BoqItemChildren = null;

				if (change.BoqItemFk.HasValue)
				{
					var parent = fromEntities.FirstOrDefault(e => e.Id == change.BoqItemFk.Value);
					if (parent != null)
					{
						var toParent = toEntities.FirstOrDefault(e => e.Reference == parent.Reference);
						if (toParent != null)
						{
							change.BoqItemFk = toParent.Id;
						}
					}
				}

				if (change.BoqItemReferenceFk.HasValue)
				{
					var parent = fromEntities.FirstOrDefault(e => e.Id == change.BoqItemReferenceFk.Value);
					if (parent != null)
					{
						var toParent = toEntities.FirstOrDefault(e => e.Reference == parent.Reference);
						change.BoqItemReferenceFk = toParent?.BoqItemFk;
					}
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="tobeChanged"></param>
		/// <param name="fromEntities"></param>
		/// <param name="toEntities"></param>
		private static void MovePrcItemsToOtherTree(IEnumerable<PrcItemEntity> tobeChanged, IEnumerable<PrcItemEntity> fromEntities, IEnumerable<PrcItemEntity> toEntities)
		{
			if (tobeChanged.IsNullOrEmpty() || fromEntities.IsNullOrEmpty() || toEntities.IsNullOrEmpty())
			{
				return;
			}
			var prcItemLogic = new PrcItemLogic();
			var idEnumerator = prcItemLogic.GetNextIds(tobeChanged.Count()).GetEnumerator();
			foreach (var change in tobeChanged)
			{
				idEnumerator.MoveNext();
				change.PrcHeaderFk = toEntities.First().PrcHeaderFk;
				change.Id = idEnumerator.Current;
				var parent = fromEntities.FirstOrDefault(e => e.Itemno == change.Itemno);

				change.PrcItemFk = parent != null ? parent.PrcItemFk : change.Id;
				change.InstanceId = change.PrcHeaderFk;
			}
		}

		/// <summary>
		/// Rest unit reate (The reset rules best provided by BoQ itself)
		/// </summary>
		/// <param name="targetItem"></param>
		private static void ResetUnitRate(BoqItemEntity targetItem)
		{
			targetItem.Price = 0;
			targetItem.Urb1 = 0;
			targetItem.Urb2 = 0;
			targetItem.Urb3 = 0;
			targetItem.Urb4 = 0;
			targetItem.Urb5 = 0;
			targetItem.Urb6 = 0;
			targetItem.Discount = 0;

			targetItem.PriceOc = 0;
			targetItem.Urb1Oc = 0;
			targetItem.Urb2Oc = 0;
			targetItem.Urb3Oc = 0;
			targetItem.Urb4Oc = 0;
			targetItem.Urb5Oc = 0;
			targetItem.Urb6Oc = 0;
			targetItem.DiscountOc = 0;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtnRequisitionToSave"></param>
		/// <param name="replacements"></param>
		/// <param name="ignoreIfInReplacements"></param>
		/// <param name="syncFieldIfInReplacements"></param>
		/// <param name="resetPrice"></param>
		/// <returns></returns>
		private static IEnumerable<BoqItemEntity> CollectBoqItems(
			IEnumerable<QuoteRequisitionComplete> qtnRequisitionToSave,
			IEnumerable<BoqItemEntity> replacements = null,
			bool? ignoreIfInReplacements = null,
			bool? syncFieldIfInReplacements = null,
			bool? resetPrice = null
			)
		{
			if (qtnRequisitionToSave == null)
			{
				return [];
			}

			var result = new List<BoqItemEntity>();

			foreach (var req in qtnRequisitionToSave)
			{
				if (req.PrcBoqCompleteToSave != null && req.PrcBoqCompleteToSave.BoqItemCompleteToSave != null)
				{
					foreach (var boq in req.PrcBoqCompleteToSave.BoqItemCompleteToSave)
					{
						if (boq.BoqItem != null)
						{
							var replacement = replacements?.FirstOrDefault(e => e.Reference == boq.BoqItem.Reference);
							if (replacement != null)
							{
								if (syncFieldIfInReplacements != null && syncFieldIfInReplacements.Value)
								{
									replacement.SyncBoqItem(boq.BoqItem, new SyncBoqItemOptions()
									{
										IncludePrices = false,
										IncludeBriefInfo = false
									});
								}
								boq.BoqItem = replacement;
							}

							if (resetPrice != null && resetPrice.Value)
							{
								ResetUnitRate(boq.BoqItem);
							}

							result.Add(boq.BoqItem);

							if (ignoreIfInReplacements != null && ignoreIfInReplacements.Value && replacement != null)
							{
								boq.BoqItem = null;
							}
						}
					}
				}
			}

			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtnRequisitionToSave"></param>
		/// <param name="replacements"></param>
		/// <param name="ignoreIfInReplacements"></param>
		/// <param name="syncFieldIfInReplacements"></param>
		/// <returns></returns>
		private static IEnumerable<PrcItemEntity> CollectPrcItems(
			IEnumerable<QuoteRequisitionComplete> qtnRequisitionToSave,
			IEnumerable<PrcItemEntity> replacements = null,
			bool? ignoreIfInReplacements = null,
			bool? syncFieldIfInReplacements = null)
		{
			if (qtnRequisitionToSave == null)
			{
				return [];
			}
			var prcItemLogic = new PrcItemLogic();
			var result = new List<PrcItemEntity>();
			foreach (var req in qtnRequisitionToSave)
			{
				if (req.PrcItemToSave != null)
				{
					foreach (var item in req.PrcItemToSave)
					{
						if (item.PrcItem != null)
						{
							var replacement = replacements?.FirstOrDefault(e => e.Itemno == item.PrcItem.Itemno);
							if (replacement != null)
							{
								if (syncFieldIfInReplacements != null && syncFieldIfInReplacements.Value)
								{
									replacement = prcItemLogic.CopyPrcItemValues(replacement, item.PrcItem);
								}
								item.PrcItem = replacement;
							}

							result.Add(item.PrcItem);

							if (ignoreIfInReplacements != null && ignoreIfInReplacements.Value && replacement != null)
							{
								item.PrcItem = null;
							}
						}
					}
				}
			}
			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="sourceHeader"></param>
		/// <param name="options"></param>
		/// <returns></returns>
		public CopyQuoteResult CopyQuote(QuoteHeaderEntity sourceHeader, CopyQuoteOptions options = null)
		{
			return this.CopyQuote([sourceHeader], options);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="sourceHeaders"></param>
		/// <param name="options"></param>
		/// <returns></returns>
		public CopyQuoteResult CopyQuote(IEnumerable<QuoteHeaderEntity> sourceHeaders, CopyQuoteOptions options = null)
		{
			var copyResult = new CopyQuoteResult();

			const int qtnSectionId = 7;
			var qtnHeaderSourceTargetIds = sourceHeaders.Select(x => x.Id).GetNewIds(length => SequenceManager.GetNextList("QTN_HEADER", length));
			var targetQtnHeaders = sourceHeaders.Select(x => (x.Clone() as QuoteHeaderEntity).InitVersionInfo(entity =>
			{
				entity.Id = qtnHeaderSourceTargetIds[entity.Id];
				entity.Code = this.GetCode(entity, entity.PrcConfigurationFk);

				// set max QuoteVersion
				var qtnHeader = this.GetSearchList(e => e.RfqHeaderFk == entity.RfqHeaderFk && e.BusinessPartnerFk == entity.BusinessPartnerFk).OrderByDescending(e => e.QuoteVersion).FirstOrDefault();
				var newQtnHeader = sourceHeaders.Where(e => e.RfqHeaderFk == entity.RfqHeaderFk && e.BusinessPartnerFk == entity.BusinessPartnerFk).OrderByDescending(e => e.QuoteVersion).FirstOrDefault();
				if (qtnHeader != null && newQtnHeader != null && newQtnHeader.QuoteVersion > qtnHeader.QuoteVersion)
				{
					qtnHeader = newQtnHeader;
				}
				if (qtnHeader != null)
				{
					entity.QuoteVersion = qtnHeader.QuoteVersion + 1;
				}
				else
				{
					entity.QuoteVersion += 1;
				}

				if (options != null && options.InitVersionInfo != null)
				{
					options.InitVersionInfo(entity);
				}
			})).ToList();

			copyResult.QtnHeaderSourceTargetIds = qtnHeaderSourceTargetIds;
			copyResult.TargetQtnHeaders = targetQtnHeaders;

			// (1) copy Qtn_Requisitions
			var sourceQtnRequisitions = new QuoteRequisitionLogic().GetSearchList(e => qtnHeaderSourceTargetIds.Keys.Contains(e.QtnHeaderFk));
			var qtnRequisitionSourceTargetIds = sourceQtnRequisitions.Select(e => e.Id).GetNewIds(length => SequenceManager.GetNextList("QTN_REQUISITION", length));
			var targetQtnRequisitions = sourceQtnRequisitions.Select(e => e.InitVersionInfo(entity =>
			{
				// update QtnRequisition id
				entity.Id = qtnRequisitionSourceTargetIds[entity.Id];
				// update QtnRequisition's QtnHeaderFk
				if (qtnHeaderSourceTargetIds.TryGetValue(entity.QtnHeaderFk, out var value))
				{
					entity.QtnHeaderFk = value;
				}
			})).ToList();

			copyResult.SourceQtnRequisitions = sourceQtnRequisitions;
			copyResult.TargetQtnRequisitions = targetQtnRequisitions;

			//copy Qtn_Totals
			var totalLogic = new QuoteTotalLogic();
			var targetTotals = new List<QuoteTotalEntity>();
			var sourceTotals = totalLogic.GetSearchList(e => qtnHeaderSourceTargetIds.Keys.Contains(e.HeaderFk));
			if (sourceTotals.Any())
			{
				var totalSourceTargetIds = sourceTotals.Select(e => e.Id).GetNewIds(count => SequenceManager.GetNextList("QTN_TOTAL", count));
				targetTotals = sourceTotals.Select(e => e.InitVersionInfo(entity =>
				{
					entity.Id = totalSourceTargetIds[entity.Id]; //update id
					if (qtnHeaderSourceTargetIds.TryGetValue(entity.HeaderFk, out var value))
					{
						//update QtnTotal's QtnHeaderFk
						entity.HeaderFk = value;
					}
				})).ToList();
			}

			copyResult.TargetTotals = targetTotals;

			//(2) copy PrcHeaders
			var cloneObj  = ClonePrcHeaderLogic.ClonePrcHeaders(sourceQtnRequisitions.ToDictionary(e => e.Id, e => e.PrcHeaderFk), Wizards.PriceComparsion_Save, "procurement.quote");

			copyResult.ClonePrcHeader = cloneObj;

			// update QtnRequisition PrcHeaderFk
			foreach (var item in targetQtnRequisitions)
			{
				if (cloneObj.TryGetValue(item.Id, out var value))
				{
					item.PrcHeaderFk = value.NewPrcHeader.Id;
				}
			}

			#region Copy Charactrical data

			var characteristicDataLogic = new CharacteristicDataLogic();
			// for the target characteristic list
			List<CharacteristicDataEntity> characterLists = new List<CharacteristicDataEntity>();
			foreach (var qtnHeaderSourceTargetId in qtnHeaderSourceTargetIds)
			{
				// copy the charateristic
				var characteristicData = characteristicDataLogic.CopyCharacteristicData(qtnHeaderSourceTargetId.Key, qtnHeaderSourceTargetId.Value, qtnSectionId, qtnSectionId) as IEnumerable<CharacteristicDataEntity>;
				characterLists.AddRange(characteristicData);
			}

			copyResult.Characteristics = characterLists;

			#endregion

			#region save data

			using (TransactionScope ts = new TransactionScope(TransactionScopeOption.Required, new TransactionOptions() { IsolationLevel = IsolationLevel.ReadCommitted }))
			{
				//Note: defect 134289, the save steps of 'save to new version' should be adjusted
				//  since PRC_BOQ's trigger [PRC_BOQ_TAIU] in which after adding PRC_BOQ it gonna to validate
				//  new QTN_Header's bas_currency_fk and its boq_header's bas_currency_fk whether match or not
				//  through the view [BOQ_HEADER_CONTEXT_V], thereby QTN_Header, PRC_Header, QTN_Requisition should be saved before saving PRC_BOQ.

				//(1) save QtnHeaders and update the searchPattern
				this.Save(targetQtnHeaders);

				targetQtnHeaders.ForEach(e => this.UpdateSearchPattern(e.Id));

				//(2) Save PrcHeader
				new PrcHeaderLogic().Save(cloneObj.Select(e => e.Value.NewPrcHeader).Where(e => e != null));

				//(3) save QtnRequisitions
				new QuoteRequisitionLogic().Save(targetQtnRequisitions);

				//Note: PrcItems will saved to database when PrcHeader saved.
				//  But BoqItems already saved to database when PrcHeader copied, so here need to save the modified fields again.
				cloneObj.Save(true, false);

				//(4) Save copied totals and Update recalculated quote totals based on modified PrcItems and BoqItems
				if (targetTotals.Count != 0)
				{
					totalLogic.Save(targetTotals);
				}

				#region save characteristic

				if (characterLists.Count > 0)
				{
					characteristicDataLogic.Save(characterLists);
				}

				#endregion

				ts.Complete();

				return copyResult;
			}

			#endregion
		}

		/// <summary>
		/// Get Max Instance Id
		/// </summary>
		/// <param name="prcHeaderId"></param>
		/// <returns></returns>
		public int GetMaxInstanceId(int prcHeaderId)
		{
			var prcItems = new PrcItemLogic().GetPrcItemsByPrcHeader(prcHeaderId);
			var maxInstanceId = 1;
			if (prcItems != null && prcItems.Any())
			{
				maxInstanceId = prcItems.Max(e => e.InstanceId) + 1;
			}

			return maxInstanceId;
		}


		private void RemoveRedundantPrcItem(List<PrcItemComplete> items)
		{
			if (items != null && items.Count > 1)
			{
				var lastItem = items.ElementAt(items.Count - 1);
				items.Clear();
				items.Add(lastItem);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="newQuoteItem"></param>
		/// <param name="rfqHeaderId"></param>
		/// <param name="reqHeaderId"></param>
		/// <param name="quoteHeaderId"></param>
		/// <param name="addToOne"></param>
		/// <param name="otherQuotes"></param>
		/// <param name="insertOptions"></param>
		private void CreateAndSaveQuotePrcItemsFromNewItemToOtherQuotes(PrcItemEntity newQuoteItem, int rfqHeaderId, int reqHeaderId, int quoteHeaderId, bool addToOne, IEnumerable<QuoteHeaderEntity> otherQuotes, CreateInsertItemOptions insertOptions = null)
		{
			if (newQuoteItem == null)
			{
				return;
			}

			var quoteLogic = new QuoteHeaderLogic();
			var qtnReqLogic = new QuoteRequisitionLogic();
			var reqHeaderLogic = new ReqHeaderLogic();
			var prcItemLogic = new PrcItemLogic();
			var addressLogic = new AddressLogic();
			var idealBidder2ItemLogic = new QuoteIdealBidder2ItemLogic();
			var qtnTotalLogic = new QuoteTotalLogic();

			List<AddressEntity> addressToSave = new List<AddressEntity>();
			List<PrcItemEntity> itemToSave = new List<PrcItemEntity>();
			List<QuoteIdealBidder2ItemEntity> idealBidder2ItemToSave = new List<QuoteIdealBidder2ItemEntity>();
			IEnumerable<QuoteTotalEntity> qtnTotalToSave = new List<QuoteTotalEntity>();

			IEnumerable<QuoteHeaderEntity> quotes;

			if (addToOne)
			{
				quotes = quoteLogic.GetSearchList(e => e.RfqHeaderFk == rfqHeaderId && e.Id != quoteHeaderId && e.IsIdealBidder);
			}
			else
			{
				quotes = otherQuotes ?? quoteLogic.GetSearchList(e => e.RfqHeaderFk == rfqHeaderId && e.Id != quoteHeaderId);
			}

			var quoteIds = quotes.CollectIds(e => e.Id);
			var qtnReqToBeCopied = qtnReqLogic.GetSearchList(e => quoteIds.Contains(e.QtnHeaderFk) && e.ReqHeaderFk == reqHeaderId);
			PrcItemEntity firstNewItemNotIdeal = newQuoteItem;
			var quoteItemCreatedCount = 0;
			if (qtnReqToBeCopied != null)
			{
				var qtnReqId2ReqHeaderIdMap = qtnReqToBeCopied.ToDictionary(e => e.Id, e => e.ReqHeaderFk);
				var reqHeaderIds = qtnReqToBeCopied.CollectIds(e => e.ReqHeaderFk);
				var reqHeaders = reqHeaderLogic.GetItemsByKey(reqHeaderIds);
				var qtnReqPrcHeaderIds = qtnReqToBeCopied.CollectIds(e => e.PrcHeaderFk);
				var allPrcItems = prcItemLogic.GetSearchList(e => qtnReqPrcHeaderIds.Contains(e.PrcHeaderFk));
				var existQuoteItems = allPrcItems.Where(e => e.Itemno == newQuoteItem.Itemno).ToList();
				var quoteStatuses = new QuoteStatusLogic().GetList();

				foreach (var qtnReq in qtnReqToBeCopied)
				{
					var quote = quotes.FirstOrDefault(e => e.Id == qtnReq.QtnHeaderFk);

					if (quote == null)
					{
						continue;
					}

					var quoteStatus = quoteStatuses.FirstOrDefault(e => e.Id == quote.StatusFk);

					if (quote.BusinessPartnerFk != 0 && (quoteStatus == null || quoteStatus.IsReadOnly || !quoteStatus.ShowInPriceComparison))
					{
						continue;
					}

					var needMaxItemNo = false;
					var found = existQuoteItems.FirstOrDefault(e => e.PrcHeaderFk == qtnReq.PrcHeaderFk);
					if (found != null) // TODO chi: how about qtnReq.PrcHeaderFk == e.InstatnceId
					{
						if (found.PrcItemFk == newQuoteItem.PrcItemFk)
						{
							continue;
						}

						needMaxItemNo = found.Itemno == newQuoteItem.Itemno;
					}

					var qtnTotals = qtnTotalLogic.GetSearchList(e => e.HeaderFk == quote.Id);
					if (qtnReqId2ReqHeaderIdMap.ContainsKey(qtnReq.Id))
					{
						var foundReqHeaderId = qtnReqId2ReqHeaderIdMap[qtnReq.Id];
						var quoteId2SorceItemPrcHeaderIdMap = new Dictionary<int, int>();
						var quoteId2TargetPrcHeaderIdMap = new Dictionary<int, int>();

						quoteId2SorceItemPrcHeaderIdMap[qtnReq.PrcHeaderFk] = newQuoteItem.PrcHeaderFk;
						quoteId2TargetPrcHeaderIdMap[qtnReq.PrcHeaderFk] = qtnReq.PrcHeaderFk;

						var wizard = !quote.IsIdealBidder || !addToOne || (quoteItemCreatedCount > 0 && newQuoteItem.PriceOc > 0) ? Wizards.CreateItemWithResetPrices : Wizards.CreateItemWithoutResetPrices;
						var cloneObject = ClonePrcHeaderLogic.ClonePrcItems(quoteId2SorceItemPrcHeaderIdMap, new List<long>() { newQuoteItem.Id }, wizard, quoteId2TargetPrcHeaderIdMap);

						foreach (var keyValue in cloneObject)
						{
							var entity = keyValue.Value;
							var quoteItem = entity.PrcItems.Values.FirstOrDefault(e => e.PrcItemFk == newQuoteItem.PrcItemFk);
							if (quoteItem == null)
							{
								continue;
							}
							if (needMaxItemNo)
							{
								// Insert item case.
								if (insertOptions != null && insertOptions.SelectedItem != null)
								{
									var prcItems = allPrcItems.Where(e => e.PrcHeaderFk == qtnReq.PrcHeaderFk).OrderBy(e => e.Itemno);
									var selectedItem = prcItems.FirstOrDefault(e => e.PrcItemFk == insertOptions.SelectedItem.PrcItemFk);
									quoteItem.Itemno = selectedItem != null
										? prcItemLogic.TryCreateInsertItemNumber(selectedItem, prcItems, insertOptions.InsertBefore)
										: prcItemLogic.GetMaxItemNo(quoteItem.PrcHeaderFk);
								}
								else
								{
									quoteItem.Itemno = prcItemLogic.GetMaxItemNo(quoteItem.PrcHeaderFk);
								}
							}

							quoteItem.PrcItemFk = newQuoteItem.PrcItemFk;
							quoteItem.InstanceId = quoteItem.PrcHeaderFk;
							if (quote.IsIdealBidder)
							{
								quoteItem.PriceOc = quoteItem.Price * quote.ExchangeRate;
							}
							itemToSave.Add(quoteItem);

							var addresses = cloneObject.SelectMany(e => e.Value.Addresses.Select(x => x.Value));
							if (addresses != null && addresses.Any())
							{
								var address = addresses.FirstOrDefault(e => e.Id == quoteItem.BasAddressFk);
								if (address != null)
								{
									addressToSave.Add(address);
								}
							}

							if (quote.IsIdealBidder)
							{
								var map = new Dictionary<long, long>();
								quoteItem.PrcItemEvaluationFk = 6;
								map.Add(newQuoteItem.Id, quoteItem.Id);
								var newIdealBidder2Item = idealBidder2ItemLogic.GetCreateEntitiesBySourceId2TargetIdMap(map, quote.Id);
								idealBidder2ItemToSave.AddRange(newIdealBidder2Item);
							}
							else
							{
								quoteItemCreatedCount++;
								if (firstNewItemNotIdeal == newQuoteItem && newQuoteItem.PriceOc > 0)
								{
									firstNewItemNotIdeal = quoteItem;
								}
							}
						}

						qtnTotalLogic.RecalculateTotalsFromHeader(quote, qtnTotals);
						qtnTotalToSave = qtnTotalToSave.Concat(qtnTotals);
					}
				}

				if (firstNewItemNotIdeal != newQuoteItem)
				{
					idealBidder2ItemToSave.ForEach(e =>
					{
						if (e.PrcItemSourceFk != firstNewItemNotIdeal.Id)
						{
							e.PrcItemSourceFk = firstNewItemNotIdeal.Id;
						}
					});
				}

				using (var transaction = TransactionScopeFactory.Create())
				{
					addressLogic.Save(addressToSave);
					prcItemLogic.Save(itemToSave);
					idealBidder2ItemLogic.Save(idealBidder2ItemToSave);
					qtnTotalLogic.Save(qtnTotalToSave);
					transaction.Complete();
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="boqItems"></param>
		/// <param name="rfqHeaderId"></param>
		/// <param name="qtnHeaderId"></param>
		/// <param name="isAddToOne"></param>
		/// <param name="otherQuotes"></param>
		/// <param name="insertOptions"></param>
		private void CreateAndSaveQuoteBoqItemsFromPackageToOtherQuotes(IEnumerable<BoqItemEntity> boqItems, int rfqHeaderId, int qtnHeaderId, bool isAddToOne, IEnumerable<QuoteHeaderEntity> otherQuotes, CreateInsertItemOptions insertOptions = null)
		{
			if (boqItems == null || !boqItems.Any())
			{
				return;
			}

			var boqItemLogic = new BoqItemLogic();
			var wizardLogic = new QuoteWizardCopyNewItemLogic();

			var tempBoq = boqItems.ElementAt(0);
			var boqItemTemp = boqItemLogic.GetSearchList(e => e.Id == tempBoq.Id && e.BoqHeaderFk == tempBoq.BoqHeaderFk);
			var copyOptions = new CopyBoqItemOptions<BoqItemEntity>()
			{
				RfqHeaderFk = rfqHeaderId,
				QtnHeaderFk = qtnHeaderId,
				IsConsiderIdealBidder = true,
				IsOnlyAddToIdealBidder = isAddToOne,
				NewBoqItems = boqItemTemp.ToList()
			};

			wizardLogic.CopyNewBoqItemToOther(copyOptions, otherQuotes, null, insertOptions != null && insertOptions.SelectedBoq != null);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="mainItemId"></param>
		public void RecalucateTotals(int mainItemId)
		{
			var qtnTotalLogic = new QuoteTotalLogic();
			var QuoteRequisitionLogic = new QuoteRequisitionLogic();
			var header = QuoteRequisitionLogic.GetSearchList(e => e.PrcHeaderFk == mainItemId).FirstOrDefault();
			if (header != null)
			{
				//recalucate totals
				var totals = qtnTotalLogic.GetSearchList(x => x.HeaderFk == header.QtnHeaderFk);
				var entity = this.GetItemByKey(header.QtnHeaderFk);
				qtnTotalLogic.RecalculateTotalsFromHeader(entity, totals);
				qtnTotalLogic.Save(totals);
			}
		}

		private void HandleDataEffective(QuoteHeaderEntity entity)
		{
			if (entity == null || entity.DateEffective != DateTime.MinValue)
			{
				return;
			}

			if (entity.DateQuoted != DateTime.MinValue)
			{
				entity.DateEffective = entity.DateQuoted;
			}
			else if (entity.InsertedAt != DateTime.MinValue)
			{
				entity.DateEffective = entity.InsertedAt;
			}
			else
			{
				entity.DateEffective = DateTime.Now;
			}
		}

		private void HandleDataTruncate(QuoteHeaderEntity entity)
		{
			if (!string.IsNullOrEmpty(entity.Description))
			{
				int valueLength = ProcurementCommonColumnConfigLogic.GetColumnSize("DESCRIPTION", "QTN_HEADER");
				entity.Description = StringExtension.Truncate(entity.Description, valueLength);
			}
		}

		/// <summary>
		/// recalculate totals for boq import.
		/// </summary>
		/// <param name="headerId"></param>
		/// <returns></returns>
		public bool RecalculateTotalsByHeaderId(int headerId)
		{
			var qtnTotalLogic = new QuoteTotalLogic();
			//recalucate totals
			var totals = qtnTotalLogic.GetSearchList(x => x.HeaderFk == headerId);
			var entity = this.GetItemByKey(headerId);
			qtnTotalLogic.RecalculateTotalsFromHeader(entity, totals);
			qtnTotalLogic.Save(totals);

			if (entity.PackageFk.HasValue)
			{
				// recalucate package totals
				// because of req boq will sync to pacakge, so we need to recalculate package totals.
				var recalculateTotalsLogic = Injector.Get<IPrcRecalculateTotalsLogic>("procurement.package");
				recalculateTotalsLogic.RecalculateTotalsByHeaderId(entity.PackageFk.Value);
			}

			return true;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="mainItemId"></param>
		public void RecalculateTotalsByHeaderIdAfterRecalculatingBoq(int mainItemId)
		{
			RecalucateTotals(mainItemId);
		}

		#region IRecalculateProcurementFacade.recalculate
		/// <summary>
		/// Recalculate
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		bool IRecalculateProcurementFacade.Recalculate(int id)
		{
			var result = true;
			var header = GetItemByKey(id);
			using (TransactionScope transaction = TransactionScopeFactory.Create())
			{
				var prcHeaderIds = new QuoteRequisitionLogic().GetSearchList(e => e.QtnHeaderFk == header.Id).Select(e => e.PrcHeaderFk).Distinct();
				foreach (var prcHeader in prcHeaderIds)
				{
					new PrcItemLogic().Recalculate(prcHeader, header.ExchangeRate);
					var reqBoqs = new PrcBoqLogic().GetList(e => e.PrcHeaderFk == prcHeader);
					var boqHeaderIds = reqBoqs.CollectIds(e => e.BoqHeaderFk);

					new BoqItemLogic().RecalculateBoqs(
						boqHeaderIds,
						true,
						header.CurrencyFk,
						new BoqItemStructureOption() { ExchangeRate = header.ExchangeRate },
						new BoqItemCalculationOption()
						{
							ModuleName = "procurement.quote",
							ModuleHeaderId = header.Id,
							ExchangeRate = header.ExchangeRate,
							VatGroupId = header.BpdVatGroupFk,
							TaxCodeId = header.TaxCodeFk
						}
					);
				}

				result &= RecalculateTotalsByHeaderId(id);
				transaction.Complete();
			}
			return result;
		}
		#endregion

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public bool IsBidderDeniedRequestForQuote(int quoteHeaderId)
		{
			var quoteHeader = new QuoteHeaderLogic().GetItemByKey(quoteHeaderId);
			if (quoteHeader == null)
			{
				return false;
			}
			var rfqHeaderLogic = new RfqHeaderLogic();
			var rfqHeaderEntity = rfqHeaderLogic.GetSearchListComplete(e => e.Id == quoteHeader.RfqHeaderFk, true).FirstOrDefault();
			if (rfqHeaderEntity != null && rfqHeaderEntity.IsBidderDeniedRequest)
			{
				return rfqHeaderEntity.IsBidderDeniedRequest;
			}

			return false;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public IEnumerable<IQuoteData> GetQuoteHeadersByPrcHederId(int id)
		{
			var qtnReqs = new QuoteRequisitionLogic().GetSearchList(e => id == e.PrcHeaderFk);
			if (qtnReqs.Any())
			{
				var qtnId = qtnReqs.First().QtnHeaderFk;
				return this.GetSearchList(o => qtnId == o.Id);
			}
			return new List<IQuoteData>();
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="qtnHeaderId"></param>
		/// <returns></returns>
		public ValueOrGrossNoDiscountSplit getValueOrGrossNoDiscountSplit(int qtnHeaderId)
		{
			var quoteRequisitions = new QuoteRequisitionLogic().GetSearchList(e => e.QtnHeaderFk == qtnHeaderId);
			var prcItems = new List<PrcItemEntity>();
			var boqHeaders = new List<BoqHeaderEntity>();
			var boqItems = new List<BoqItemEntity>();

			var prcHeaderIds = new HashSet<int>();
			var prcBoqLogic = new PrcBoqLogic();
			var boqHeaderLogic = new BoqHeaderLogic();
			var boqItemLogic = new BoqItemLogic();

			#region Collect Id
			foreach (var entity in quoteRequisitions)
			{
				prcHeaderIds.Add(entity.PrcHeaderFk);
			}
			#endregion

			#region PrcItems
			if (prcHeaderIds.Any())
			{
				var prcItemLogic = new PrcItemLogic();
				foreach (var prcHeaderId in prcHeaderIds)
				{
					var items = prcItemLogic.GetSearchList(e => e.PrcHeaderFk == prcHeaderId);
					if (items.Any())
					{
						prcItems.AddRange(items);

					}
				}
			}
			#endregion

			var prcBoqList = prcBoqLogic.GetSearchList(e => prcHeaderIds.Contains(e.PrcHeaderFk), prcHeaderIds.Any());

			if (prcBoqList != null && prcBoqList.Any())
			{
				foreach (var prcBoq in prcBoqList)
				{
					var boqHeader = boqHeaderLogic.GetEntityById(prcBoq.BoqHeaderFk);
					boqHeaders.Add(boqHeader);
					var boqRootItem = boqItemLogic.GetBoqRootItemByHeaderId(boqHeader.Id);
					boqItems.Add(boqRootItem);
				}
			}
			var relativeValueNet = new SplitOverAllDiscountLogic().GetTotalNoDiscountSplit(prcItems, boqHeaders, boqItems);
			return relativeValueNet;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="request"></param>
		/// <param name="response"></param>
		/// <returns></returns>
		protected override SearchSpecification<QuoteHeaderEntity, int> GetListSearchContext(FilterRequest request, out FilterResponse response)
		{
			var context = base.GetListSearchContext(request, out response);
			context.UseCurrentClientPredicate = e => e.CompanyFk == BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
			var request2 = request as QuoteFilterRequest ?? new();
			if (request.ProjectContextId != null && request2.RfqHeaderId.HasValue)
			{
				context.CustomPredicate = (e => e.ProjectFk == request.ProjectContextId && e.RfqHeaderFk == request2.RfqHeaderId.Value);
			}
			else if (request.ProjectContextId != null)
			{
				context.CustomPredicate = (e => e.ProjectFk == request.ProjectContextId);
			}
			else if (request2.RfqHeaderId.HasValue)
			{
				context.CustomPredicate = (e => e.RfqHeaderFk == request2.RfqHeaderId.Value);
			}

			var context_Portal = Context.Portal;
			var context_BusinessPartnerId = Context.BusinessPartnerId;

			//# defect 137435,remove logic about further external BPs
			if (context_Portal.HasValue && context_Portal.Value && context_BusinessPartnerId.HasValue)
			{
				var contactLogic = Injector.Get<IContactLogic>();
				var bpIds = contactLogic.GetBpIds4PortalLoginUser();
				Expression<Func<QuoteHeaderEntity, bool>> filter = e => bpIds.Contains(e.BusinessPartnerFk);
				if (context.CustomPredicate == null)
				{
					context.CustomPredicate = filter;
				}
				else
				{
					context.CustomPredicate = System.Linq.Dynamic.PredicateBuilder.And<QuoteHeaderEntity>(context.CustomPredicate, filter);
				}
			}
			if (PrcClerkLogic.IsFilterByLoginClerk())
			{
				var loginClerk = this.ClerkInfo.GetLoginClerkInfo();

				if (loginClerk != null)
				{
					var clerkIds = PrcClerkLogic.AttachClerkGroup(loginClerk.Id);
					Expression<Func<QuoteHeaderEntity, bool>> clerkFilter = e => e.ClerkPrcFk == null || clerkIds.Contains(e.ClerkPrcFk.Value);

					if (context.CustomPredicate == null)
					{
						context.CustomPredicate = clerkFilter;
					}
					else
					{
						context.CustomPredicate = System.Linq.Dynamic.PredicateBuilder.And<QuoteHeaderEntity>(context.CustomPredicate, clerkFilter);
					}
				}
			}

			if (request.FurtherFilters != null && request.FurtherFilters.Any())
			{
				request.FurtherFilters.ToList().ForEach(m =>
					{
						switch (m.Token.ToUpper())
						{
							case "CODE":
								Expression<Func<QuoteHeaderEntity, bool>> codeFilter = e => e.Code == m.Value;
								if (context.CustomPredicate == null)
								{
									context.CustomPredicate = codeFilter;
								}
								else
								{
									context.CustomPredicate = System.Linq.Dynamic.PredicateBuilder.And<QuoteHeaderEntity>(context.CustomPredicate, codeFilter);
								}
								break;
							case "RFQID":
								int rfqid = this.TryParseInt(m.Value, 0);
								Expression<Func<QuoteHeaderEntity, bool>> rfqIdFilter = e => e.RfqHeaderFk == rfqid;
								if (context.CustomPredicate == null)
								{
									context.CustomPredicate = rfqIdFilter;
								}
								else
								{
									context.CustomPredicate = System.Linq.Dynamic.PredicateBuilder.And<QuoteHeaderEntity>(context.CustomPredicate, rfqIdFilter);
								}
								break;
							default: break;
						}
					});
			}
			;

			return context;
		}

		/// <summary>
		/// Evaluates grouping filter.
		/// </summary>
		/// <param name="filterIn"></param>
		/// <param name="dbContext"></param>
		/// <param name="query"></param>
		/// <returns></returns>
		protected override IQueryable<QuoteHeaderEntity> EvaluateGroupingFilter(FilterRequest filterIn, RVPBizComp.DbContext dbContext, IQueryable<QuoteHeaderEntity> query)
		{

			var queryResult = new QuoteStatusLogic().GetUserReadableStatusQuery(dbContext);
			query = query.Where(e => (from r in queryResult where (r.Id == e.StatusFk) select r).Any());

			var objectSelection = filterIn.GetFurtherFiltersValue("MDL_OBJECT_SELECTION");
			if (objectSelection != null)
			{
				Expression<Func<QtnHeader2MdlObjectVEntity, Boolean>> modelFilterExpression = null;

				var selectedObjectIds = ModelElementIdCompressor.ParseCompressedString(objectSelection).ToArray();
				if (selectedObjectIds.Length > 0)
				{
					modelFilterExpression =
						selectedObjectIds.CreateEntityFilterExpression<QtnHeader2MdlObjectVEntity>(h2O => h2O.ObjectFk);
				}
				else
				{
					var selectedModelIdText = filterIn.GetFurtherFiltersValue("MDL_MODEL");
					if (selectedModelIdText != null)
					{
						Int32 selectedModelId;
						if (Int32.TryParse(selectedModelIdText, out selectedModelId))
						{
							var subModelIds = BusinessApplication.BusinessEnvironment.GetExportedValue<ISubModelLogic>().GetSubModelIdsForModel(selectedModelId);
							modelFilterExpression = h2O => subModelIds.Contains(h2O.ModelFk);
						}
					}
				}

				if (modelFilterExpression != null)
				{
					query =
						query.Where(
							e => dbContext.Set<QtnHeader2MdlObjectVEntity>().Where(h2O => h2O.Id == e.Id).Any(modelFilterExpression));
				}
			}

			return query;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entities"></param>
		public IEnumerable<QuoteHeaderEntity> RecalculateTotalFields(IEnumerable<QuoteHeaderEntity> entities)
		{
			var results = entities != null ? entities.ToList() : entities;

			if (results != null && results.Any())
			{
				var quoteIds = results.Select(e => e.Id);
				var finallBillingSchemas = new QuoteBillingSchemaLogic().GetBillingSchemas(quoteIds).OrderBy(e => e.Sorting).Where(e => e.FinalTotal);

				var netTotalIds = new PrcTotalTypeLogic().GetSearchList(x => x.PrcTotalKindFk.Value == TotalKindConstant.NetTotal).Select(e => e.Id);
				var netTotalItems = new QuoteTotalLogic().GetSearchList(x => quoteIds.Contains(x.HeaderFk) && netTotalIds.Contains(x.TotalTypeFk)).ToList();

				foreach (var entity in results)
				{
					var finallBillingSchema = finallBillingSchemas.FirstOrDefault(e => e.HeaderFk == entity.Id);
					var netTotalItem = netTotalItems.Where(x => x.HeaderFk == entity.Id).ToList();

					entity.ValueNet = netTotalItem.Sum(x => x.ValueNet);
					entity.ValueNetOc = netTotalItem.Sum(x => x.ValueNetOc);
					entity.ValueTax = netTotalItem.Sum(x => x.ValueTax);
					entity.ValueTaxOc = netTotalItem.Sum(x => x.ValueTaxOc);
					entity.Gross = entity.ValueNet + entity.ValueTax;
					entity.GrossOc = entity.ValueNetOc + entity.ValueTaxOc;
					entity.ValueNetFinal = finallBillingSchema != null ? finallBillingSchema.Result : entity.ValueNet;
				}

			}

			return results;
		}

		/// <summary>
		/// Returns business partners for the rfq who have submitted a quote.
		/// </summary>
		/// <param name="rfqHeaderId"></param>
		/// <returns></returns>
		public IEnumerable<int> GetRfqBpIdsWithQuote(int rfqHeaderId)
		{
			using (var dbContext = this.CreateDbContext())
			{
				// Creating query to get business partners with quotes
				var businessPartnerIdsWithQuote = dbContext.Set<QuoteHeaderEntity>().Where(e => e.RfqHeaderFk == rfqHeaderId).Select(e => e.BusinessPartnerFk);
				return businessPartnerIdsWithQuote.ToList();
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="param"></param>
		/// <returns></returns>
		public int ValidateAndUpdateItemQuantity(ValidateAndUpdateItemQuantityParams param)
		{
			var quoteRequisitionLogic = new QuoteRequisitionLogic();
			var reqHeaderLogic = new ReqHeaderLogic();
			int result = 0;
			int mainItemId = param.QuoteId;

			QuoteHeaderEntity quoteHeaderEntity = GetSearchList(p => p.Id == mainItemId).FirstOrDefault();

			if (quoteHeaderEntity != null)
			{
				var quoteStatus = new QuoteStatusLogic().GetList().Where(e => e.IsOrdered == false && e.IsVirtual == false && e.IsLive);
				var quoteStatusIds = quoteStatus.Select(e => e.Id);

				PrcItemLogic prcItemLogic = new PrcItemLogic();
				if (param.ValidateAndUpdateScope == ValidateAndUpdateScopeType.CurrentLeadingRecord)
				{
					var qtnRequisitions = quoteRequisitionLogic.GetSearchList(e => e.QtnHeaderFk == quoteHeaderEntity.Id);
					var prcHeaderIds = qtnRequisitions.Select(c => c.PrcHeaderFk);
					List<PrcItemEntity> prcItemList = prcItemLogic.GetSearchList(i => prcHeaderIds.Contains(i.PrcHeaderFk)).ToList();
					result += prcItemLogic.ValidateAndUpdateItemQuantity(prcItemList);
				}
				else if (param.ValidateAndUpdateScope == ValidateAndUpdateScopeType.CurrentPackage)
				{
					var reqHeaderId = quoteRequisitionLogic.GetSearchList(e => e.QtnHeaderFk == quoteHeaderEntity.Id).Select(e => e.ReqHeaderFk).FirstOrDefault();
					var packageId = reqHeaderLogic.GetSearchList(e => e.Id == reqHeaderId).Select(e => e.PackageFk).FirstOrDefault();

					var reqHeaderIds = reqHeaderLogic.GetSearchList(e => e.PackageFk == packageId).Select(c => c.Id);
					var qtnHeaderFks = quoteRequisitionLogic.GetSearchList(e => reqHeaderIds.Contains(e.ReqHeaderFk)).Select(c => c.QtnHeaderFk);
					var quoteIds = GetSearchList(p => qtnHeaderFks.Contains(p.Id)).Where(p => quoteStatusIds.Contains(p.StatusFk)).Select(c => c.Id);

					var prcHeaderIdList = quoteRequisitionLogic.GetSearchList(e => quoteIds.Contains(e.QtnHeaderFk)).Select(p => p.PrcHeaderFk);
					List<PrcItemEntity> prcItemList = prcItemLogic.GetSearchList(i => prcHeaderIdList.Contains(i.PrcHeaderFk)).ToList();
					result += prcItemLogic.ValidateAndUpdateItemQuantity(prcItemList);
				}
				else if (param.ValidateAndUpdateScope == ValidateAndUpdateScopeType.CurrentProject)
				{
					var quoteIds = GetSearchList(p => p.ProjectFk == quoteHeaderEntity.ProjectFk).Where(p => quoteStatusIds.Contains(p.StatusFk)).Select(c => c.Id);
					var prcHeaderIdList = quoteRequisitionLogic.GetSearchList(e => quoteIds.Contains(e.QtnHeaderFk)).Select(p => p.PrcHeaderFk);
					List<PrcItemEntity> prcItemList = prcItemLogic.GetSearchList(i => prcHeaderIdList.Contains(i.PrcHeaderFk)).ToList();
					result += prcItemLogic.ValidateAndUpdateItemQuantity(prcItemList);
				}
			}
			return result;
		}
	}

	/// <summary>
	///
	/// </summary>
	public class ValidateAndUpdateItemQuantityParams
	{
		/// <summary>
		///
		/// </summary>
		public int QuoteId { get; set; }
		/// <summary>
		///
		/// </summary>
		public ValidateAndUpdateScopeType ValidateAndUpdateScope { get; set; }
	}

	/// <summary>
	///
	/// </summary>
	public class CopyQuoteOptions
	{
		/// <summary>
		///
		/// </summary>
		public Action<QuoteHeaderEntity> InitVersionInfo { get; set; }
	}

	/// <summary>
	///
	/// </summary>
	public class CopyQuoteResult
	{
		/// <summary>
		///
		/// </summary>
		public IDictionary<int, DuplicateObjectEntity> ClonePrcHeader { get;set; }

		/// <summary>
		///
		/// </summary>
		public IDictionary<int, int> QtnHeaderSourceTargetIds { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<QuoteHeaderEntity> TargetQtnHeaders { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<QuoteRequisitionEntity> SourceQtnRequisitions { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<QuoteRequisitionEntity> TargetQtnRequisitions { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<QuoteTotalEntity> TargetTotals { get; set; }

		/// <summary>
		///
		/// </summary>
		public IEnumerable<CharacteristicDataEntity> Characteristics { get; set; }
	}
}
