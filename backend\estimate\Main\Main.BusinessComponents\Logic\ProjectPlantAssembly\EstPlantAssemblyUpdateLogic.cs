using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using System.Transactions;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Estimate.Assemblies.BusinessComponents;
using RIB.Visual.Estimate.Parameter.BusinessComponents;
using RIB.Visual.Platform.Common;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RVPBC = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Common.Core;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class EstPlantAssemblyUpdateLogic : CompanyScopeService
	{
		private readonly int EstHeaderId;

		private readonly EstResourceUpdateHelper _EstResourceUpdateHelper = new EstResourceUpdateHelper();

		private readonly EstAssemblyTypeLogic _EstAssemblyTypeLogic = new EstAssemblyTypeLogic();

		private readonly MdcCommoditySearchVLogic _MdcCommoditySearchVLogic = new MdcCommoditySearchVLogic();

		//private Dictionary<int, EstAssemblyTypeEntity> _AssemblyCategoryId2AssemblyTypeMap = new Dictionary<int, EstAssemblyTypeEntity>();

		private readonly Dictionary<int, MdcCommoditySearchVEntity> _MdcCommoditySearchVEntityCache = new Dictionary<int, MdcCommoditySearchVEntity>();

		private List<ICostCodeEntity> _masterCostCodes = null;

		private List<IProjectCostCodesEntity> _prjCostCodes = null;

		internal int _mdcContextId = -1;

		internal int? _defaultJobId = null;

		private readonly EstLineItemAttachmentInitializer _LineItemAttachmentInitializer;

		private readonly EstResourceAttachmentInitializer _ResourceAttachmentInitializer;

		//private AssemblyParamUpdateItem _AssemblyParamUpdateResult;

		private readonly EstimateAssemblyUpdateLogic _EstimateAssemblyUpdateLogic;


		/// <summary>
		/// 
		/// </summary>
		/// <param name="estHeaderId"></param>
		public EstPlantAssemblyUpdateLogic(int estHeaderId)
		{
			this.EstHeaderId = estHeaderId;

			_LineItemAttachmentInitializer = new EstLineItemAttachmentInitializer(EstHeaderId);

			_ResourceAttachmentInitializer = new EstResourceAttachmentInitializer(EstHeaderId);

			_EstimateAssemblyUpdateLogic = new EstimateAssemblyUpdateLogic(estHeaderId);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerId"></param>
		/// <param name="filterInfo"></param>
		/// <param name="plantAssemblyIds"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> UpdatePlantAssembliesByIds(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<int> plantAssemblyIds)
		{
			var plantAssemblyLevelMap = this.GetPlantAssemblyLevelByAssemblyIds(plantAssemblyIds, headerId);

			var plantAssemblyIdsToUpdate = plantAssemblyLevelMap.Select(e => e.AssemblyId).ToList();

			var plantAssembliesToUpdate = (filterInfo != null && filterInfo.IsPrjAssembly) ? new EstimateMainLineItemLogic().GetPrjPlantAssembliesByIds(plantAssemblyIdsToUpdate, filterInfo.ProjectId) :
				new EstimateMainLineItemLogic().GetAssembliesByIds(plantAssemblyIdsToUpdate, (int)CommonLogic.LineItemTypes.PlantAssembly);

			var resourcesToUpdate = new EstimateMainResourceLogic().GetSearchList(e => plantAssemblyIdsToUpdate.Contains(e.EstLineItemFk)).ToList();

			UpdateMarkupCostUnitFromPlantAssembly(resourcesToUpdate, headerId);

			//get the parameter of assembly, include from assembly catalog
			//_AssemblyParamUpdateResult = UpdatePlantAssemblyParam(headerId, filterInfo, plantAssembliesToUpdate);

			return UpdatePlantAssembliesBase(headerId, filterInfo, plantAssemblyLevelMap, plantAssembliesToUpdate, resourcesToUpdate);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="assemblyIds"></param>
		/// <param name="headerId"></param>
		/// <returns></returns>
		public IEnumerable<AssemblyRefTreeStructure> GetPlantAssemblyLevelByAssemblyIds(IEnumerable<int> assemblyIds, int headerId)
		{
			var result = new List<AssemblyRefTreeStructure>();

			object _lock = new object();

			if (assemblyIds == null || !assemblyIds.Any())
			{
				return result;
			}

			var assemblyGroup = assemblyIds.Select((e, i) => new { Index = i, Value = e }).GroupBy(e => e.Index / 1000).Select(e => e.Select(x => x.Value)).ToList();

			ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };

			Parallel.ForEach(assemblyGroup, parallelOptions, ids =>
			{
				using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{
					var entities = dbContext.SqlQuery<AssemblyRefTreeStructure>(GetSqlString(ids, headerId)).ToList();

					lock (_lock)
					{
						result.AddRange(entities);
					}
				}
			});

			return result.GroupBy(e => e.AssemblyId).Select(e => e.OrderByDescending(i => i.Level).FirstOrDefault()).ToList();
		}

		private string GetSqlString(IEnumerable<int> assemblyIds, int headerId)
		{
			var idsStr = string.Join(",", assemblyIds);

			string queryStr = "WITH BASE_REFERENCE AS ";
			queryStr += "(";
			queryStr += "SELECT A.ID, Level = 1 from EST_LINE_ITEM A WHERE A.EST_HEADER_FK = {1} AND A.LINE_ITEM_TYPE = 2 ";
			queryStr += "AND A.ID IN ({0}) ";
			queryStr += "UNION ALL ";
			queryStr += "SELECT A.EST_LINE_ITEM_FK AS ID, Level = B.Level + 1 FROM EST_RESOURCE A JOIN BASE_REFERENCE B ON B.ID = A.EST_ASSEMBLY_FK AND A.EST_HEADER_FK = {1} AND A.EST_RESOURCE_TYPE_FK = 3 ";
			queryStr += ")";
			queryStr += "SELECT ID AS AssemblyId, MAX(Level) AS Level FROM BASE_REFERENCE GROUP BY ID ORDER BY Level;";

			return string.Format(queryStr, idsStr, headerId);
		}

		private void UpdateMarkupCostUnitFromPlantAssembly(IEnumerable<EstResourceEntity> resourcesToUpdate, int estHeaderId, IEnumerable<EstLineItemEntity> assembliesOfHeader = null)
		{
			if (resourcesToUpdate == null || !resourcesToUpdate.Any())
			{
				return;
			}

			var referenceAssemblyIds = resourcesToUpdate.Where(e => e.EstAssemblyFk.HasValue && !e.EstAssemblyTypeFk.HasValue).Select(e => e.EstAssemblyFk.Value).ToList();

			var assemblies = assembliesOfHeader != null && assembliesOfHeader.Any() ? assembliesOfHeader : new EstimateMainLineItemLogic().GetAssembliesByIds(referenceAssemblyIds, 2, estHeaderId);

			foreach (var resource in resourcesToUpdate)
			{
				if (!resource.EstAssemblyFk.HasValue || resource.EstAssemblyTypeFk.HasValue)
				{
					continue;
				}

				var referenceAssembly = assemblies.FirstOrDefault(e => e.Id == resource.EstAssemblyFk.Value);

				if (referenceAssembly == null)
				{
					continue;
				}

				resource.MarkupCostUnit = referenceAssembly.MarkupCostUnit;
			}
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="headerId"></param>
		/// <param name="filterInfo"></param>
		/// <param name="assemblyLevelList"></param>
		/// <param name="lineItemsToUpdate"></param>
		/// <param name="resourcesToUpdate"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> UpdatePlantAssembliesBase(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<AssemblyRefTreeStructure> assemblyLevelList, IEnumerable<EstLineItemEntity> lineItemsToUpdate, IEnumerable<EstResourceEntity> resourcesToUpdate)
		{
			var result = new List<EstLineItemEntity>();

			if (assemblyLevelList == null || !assemblyLevelList.Any())
			{
				return result;
			}

			if (lineItemsToUpdate == null || !lineItemsToUpdate.Any())
			{
				return result;
			}

			/* initialize the changeTracker */
			var lineItemChangedTracker = new ObjectChangedTracker<EstLineItemEntity>(lineItemsToUpdate);

			var resourceChangedTracker = new ObjectChangedTracker<EstResourceEntity>(resourcesToUpdate);

			_LineItemAttachmentInitializer.AttachUDPEntityToLineItems(lineItemsToUpdate);

			_ResourceAttachmentInitializer.AttachUDPEntityToResources(resourcesToUpdate);

			if (filterInfo != null && filterInfo.IsPrjPlantAssembly)
			{
				// for old data, change resoure assigment: master plant assembly to project plant assembly
				List<EstLineItemEntity> prjPlantAssemblies = new List<EstLineItemEntity>();
				var plantAssemblyHeaderId = new EstimateAssembliesLogic().GetHeaderPlantFkByCurrentLineItemContext();
				foreach (var resource in resourcesToUpdate)
				{
					if (resource.EstHeaderAssemblyFk.HasValue && resource.EstHeaderAssemblyFk.Value == plantAssemblyHeaderId)
					{
						if (prjPlantAssemblies.Count == 0)
						{
							prjPlantAssemblies = new EstimateMainLineItemLogic().GetPrjPlantAssembliesByPrjId(filterInfo.ProjectId).ToList();
						}

						var prjPlantAssembly = prjPlantAssemblies.Where(e => e.EstHeaderAssemblyFk == resource.EstHeaderAssemblyFk && e.EstAssemblyFk == resource.EstAssemblyFk).FirstOrDefault();
						if (prjPlantAssembly != null)
						{
							resource.EstHeaderAssemblyFk = prjPlantAssembly.EstHeaderFk;
							resource.EstAssemblyFk = prjPlantAssembly.Id;
						}
					}
				}
			
				// for old data, change resoure assigment: master assembly to project assembly
				List<EstLineItemEntity> prjAssemblies = new List<EstLineItemEntity>();
				var assemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();
				foreach (var resource in resourcesToUpdate)
				{
					if (resource.EstHeaderAssemblyFk.HasValue && resource.EstHeaderAssemblyFk.Value == assemblyHeaderId)
					{
						if (prjAssemblies.Count == 0)
						{
							prjAssemblies = new EstimateMainLineItemLogic().GetPrjAssembliesByPrjId(filterInfo.ProjectId).ToList();
						}

						var prjAssembly = prjAssemblies.Where(e => e.EstHeaderAssemblyFk == resource.EstHeaderAssemblyFk && e.EstAssemblyFk == resource.EstAssemblyFk).FirstOrDefault();
						if (prjAssembly != null)
						{
							resource.EstHeaderAssemblyFk = prjAssembly.EstHeaderFk;
							resource.EstAssemblyFk = prjAssembly.Id;
						}
					}
				}
			}

			//generate a map (assembly id to assembly)
			var plantAssembly2Dictionary = lineItemsToUpdate.ToDictionary(e => e.Id, e => e);

			/* update resources from costcode and material */
			if (filterInfo != null)
			{
				_mdcContextId = new EstimateMainLineItemLogic().GetCompanyInfoProvider().GetMasterDataContext();

				Dictionary<int, int?> resId2JobId = new Dictionary<int, int?>();
				// get resource job mapping
				if (filterInfo.IsPrjPlantAssembly && (filterInfo.UpdateCostCodes || filterInfo.UpdateMaterials))
				{
					resId2JobId = _EstimateAssemblyUpdateLogic.GetResourceJobWithAssembly(resourcesToUpdate, plantAssembly2Dictionary);
				}

				if (filterInfo.IsPrjPlantAssembly && filterInfo.UpdateCostCodes)
				{
					_EstimateAssemblyUpdateLogic.UpdateCostCodeTypeResourcesFromPrjCostCode(filterInfo.ProjectId, resourcesToUpdate, resId2JobId, filterInfo.UpdateCostTypes);
				}
				else if (filterInfo.UpdateCostCodes)
				{
					_EstimateAssemblyUpdateLogic.UpdateCostCodeTypeResources(_mdcContextId, resourcesToUpdate, filterInfo.UpdateCostTypes);
				}

				if (filterInfo.IsPrjPlantAssembly && filterInfo.UpdateMaterials)
				{
					var noMapPrjMaterialResIds = _EstimateAssemblyUpdateLogic.UpdateMaterialTypeResourcesFromPrjCostCode(filterInfo.ProjectId, resourcesToUpdate, resId2JobId, filterInfo.UpdateCostTypes);
					// update from master material
					if (noMapPrjMaterialResIds.Any())
					{
						var noMapPrjMaterialResources = resourcesToUpdate.Where(e => noMapPrjMaterialResIds.Contains(e.Id));
						_EstimateAssemblyUpdateLogic.UpdateMaterialTypeResources(_mdcContextId, noMapPrjMaterialResources, filterInfo.UpdateCostTypes, filterInfo.IsPrjAssembly, filterInfo.ProjectId);
					}
				}
				else if (filterInfo.UpdateMaterials)
				{
					_EstimateAssemblyUpdateLogic.UpdateMaterialTypeResources(_mdcContextId, resourcesToUpdate, filterInfo.UpdateCostTypes, filterInfo.IsPrjAssembly, filterInfo.ProjectId);
				}
			}

			//generate a map (lineItem Id to its resources)
			var resourceOfLineItem2Dictionary = resourcesToUpdate.GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.Select(i => i));

			//generate a map (assembly id to resources reference this assembly)
			var resourceOfAssembly2Dictionary = resourcesToUpdate.Where(e => e.EstAssemblyFk.HasValue).GroupBy(e => e.EstAssemblyFk).ToDictionary(e => e.Key, e => e.Select(i => i));

			//load exchange rate
			new ExchangeRateHelper(filterInfo != null && filterInfo.IsPrjPlantAssembly ? filterInfo.ProjectId : default(int?)).ExchangeRate(resourcesToUpdate);

			var userDefinedColumnValueLogic = new AssemblyUDPCalculationLogic(headerId);

			var estTotalCalculator = new AssemblyTotalCalculator(headerId, null);

			var estDetailCalculateHelper = new EstDetailCalculateHelper(headerId, null);

			foreach (var assemblyLevelItem in assemblyLevelList)
			{
				if (!plantAssembly2Dictionary.ContainsKey(assemblyLevelItem.AssemblyId) || plantAssembly2Dictionary[assemblyLevelItem.AssemblyId] == null)
				{
					continue;
				}

				//get assembly
				var plantAssembly = plantAssembly2Dictionary[assemblyLevelItem.AssemblyId];

				//get resources of current assembly
				var resourcesOfLineItem = (!resourceOfLineItem2Dictionary.ContainsKey(assemblyLevelItem.AssemblyId) || resourceOfLineItem2Dictionary[assemblyLevelItem.AssemblyId] == null) ? new List<EstResourceEntity>() : resourceOfLineItem2Dictionary[assemblyLevelItem.AssemblyId];

				//calculate detail property
				//if (parametersOfAssemblies.ContainsKey(assemblyLevelItem.AssemblyId) && parametersOfAssemblies[assemblyLevelItem.AssemblyId] != null)
				//{
				//	var parameter = parametersOfAssemblies[assemblyLevelItem.AssemblyId].GroupBy(e => e.Code).ToDictionary(e => e.Key, e => e.First().ParameterValue);

				//	Func<IScriptEstLineItem, Dictionary<string, decimal>> getParameters = (entity) =>
				//	{
				//		return parameter;
				//	};

				//	estDetailCalculateHelper.CalculateLineItemAndResourceDetails(plantAssembly, resourcesOfLineItem, getParameters);
				//}
				//else
				//{
					estDetailCalculateHelper.CalculateLineItemAndResourceDetails(plantAssembly, resourcesOfLineItem, null);
				//}

				//calculate quantity ,cost unit, cost total and so on
				estTotalCalculator.CalculateLineItemAndResourcesInList(plantAssembly, resourcesOfLineItem);

				_LineItemAttachmentInitializer.AttachUDPEntityToLineItem(plantAssembly);

				userDefinedColumnValueLogic.CalUserDefinedValOfLiAndResInList(plantAssembly, resourcesOfLineItem);

				//update the resource which reference to current assembly
				if (resourceOfAssembly2Dictionary.ContainsKey(assemblyLevelItem.AssemblyId))
				{
					var resourceRef2Assembly = resourceOfAssembly2Dictionary[assemblyLevelItem.AssemblyId];

					UpdateResourcesFromAssembly(resourceRef2Assembly, plantAssembly);

					foreach (var refAssemblyRes in resourceRef2Assembly)
					{
						if (refAssemblyRes.MdcCostCodeFk.HasValue)
						{
							if (filterInfo != null && filterInfo.IsPrjPlantAssembly)
							{
								if (_prjCostCodes == null)
								{
									var prjCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();
									_prjCostCodes = prjCostCodeLogic.GetProjectCostCodes(filterInfo.ProjectId).ToList();
								}

								var costCode = _prjCostCodes.FirstOrDefault(e => e.MdcCostCodeFk == refAssemblyRes.MdcCostCodeFk);
								if (costCode != null)
								{
									refAssemblyRes.IsBudget = costCode.IsBudget;
									refAssemblyRes.IsCost = costCode.IsCost;
								}
							}
							else
							{
								if (_masterCostCodes == null)
								{
									var costCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<ICostCodesInfoProviderLogic>();
									_masterCostCodes = costCodeLogic.GetCostCodesByMdcContext(_mdcContextId).ToList();
								}

								var costCode = _masterCostCodes.FirstOrDefault(e => e.Id == refAssemblyRes.MdcCostCodeFk.Value);
								if (costCode != null)
								{
									refAssemblyRes.IsBudget = costCode.IsBudget;
									refAssemblyRes.IsCost = costCode.IsCost;
								}
							}
						}
					}
				}
			}

			/* save the modification lineItems and resources*/
			var lineItemToSave = lineItemChangedTracker.GetChangedObjects();

			var resourceToSave = resourceChangedTracker.GetChangedObjects();

			using (var transactionScope = TransactionScopeFactory.Create())
			{
				new EstimateMainLineItemLogic().BulkSave(ModelBuilder.DbModel, lineItemToSave);

				new EstimateMainResourceLogic().BulkSave(ModelBuilder.DbModel, resourceToSave);

				userDefinedColumnValueLogic.SaveModifiedUDP();

				//if (_AssemblyParamUpdateResult != null)
				//{
				//	if (_AssemblyParamUpdateResult.AssemblyParamsToUpdate != null && _AssemblyParamUpdateResult.AssemblyParamsToUpdate.Any())
				//	{
				//		var newItems = _AssemblyParamUpdateResult.AssemblyParamsToUpdate.Where(e => e.Id == 0 || e.Version == 0).ToList();

				//		if (newItems.Any())
				//		{
				//			var newIds = new Queue<int>(new EstimateParameterLineItemLogic().GetNextIds(newItems.Count()));

				//			foreach (var item in newItems)
				//			{
				//				item.Id = newIds.Dequeue();
				//			}
				//		}

				//		_AssemblyParamUpdateResult.AssemblyParamsToUpdate.SaveTranslate(this.UserLanguageId, new Func<EstLineItemParamEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

				//		new EstimateParameterLineItemLogic().Save(_AssemblyParamUpdateResult.AssemblyParamsToUpdate);
				//	}

				//	if (_AssemblyParamUpdateResult.AssemblyCatParamsToUpdate != null && _AssemblyParamUpdateResult.AssemblyCatParamsToUpdate.Any())
				//	{
				//		var newItems = _AssemblyParamUpdateResult.AssemblyCatParamsToUpdate.Where(e => e.Id == 0 || e.Version == 0).ToList();

				//		if (newItems.Any())
				//		{
				//			var newIds = new Queue<int>(new EstimateParameterAssemblyCatLogic().GetNextIds(newItems.Count()));

				//			foreach (var item in newItems)
				//			{
				//				item.Id = newIds.Dequeue();
				//			}
				//		}

				//		_AssemblyParamUpdateResult.AssemblyCatParamsToUpdate.SaveTranslate(this.UserLanguageId, new Func<EstAssemblyParamEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });

				//		new EstimateParameterAssemblyCatLogic().Save(_AssemblyParamUpdateResult.AssemblyCatParamsToUpdate);
				//	}
				//}

				transactionScope.Complete();
			}

			return lineItemsToUpdate;
		}

		private void UpdateResourcesFromAssembly(IEnumerable<EstResourceEntity> resourceRef2Assembly, EstLineItemEntity assembly)
		{
			if (assembly == null)
			{
				return;
			}

			if (resourceRef2Assembly != null && resourceRef2Assembly.Any())
			{
				foreach (var refAssemblyRes in resourceRef2Assembly)
				{
					refAssemblyRes.CostUnit = assembly.CostUnit + assembly.MarkupCostUnit;

					refAssemblyRes.MarkupCostUnit = assembly.MarkupCostUnit;

					refAssemblyRes.BasUomFk = assembly.BasUomFk;

					refAssemblyRes.HoursUnit = assembly.HoursUnit;

					refAssemblyRes.DayWorkRateUnit = assembly.DayWorkRateUnit;

					if (!refAssemblyRes.EstHeaderAssemblyFk.HasValue)
					{
						refAssemblyRes.EstHeaderAssemblyFk = assembly.EstHeaderFk;
					}

					if (assembly.UserDefinedcolValEntity == null)
					{
						continue;
					}
				}
			}
		}

		private IEnumerable<EstLineItemParamEntity> GetAssemblyParams(int headerId, EstimateAssemblyResourcesUpdateData filterInfo, IEnumerable<EstLineItemEntity> lineItemsToUpdate)
		{
			if (filterInfo.SelectUpdateScope == 1)
			{
				return new EstimateParameterLineItemLogic().GetByFilter(e => e.EstHeaderFk == headerId);
			}
			else
			{
				return new EstimateParameterLineItemLogic().GetListByLineItemIds(lineItemsToUpdate.Select(e => e.Id).Distinct().ToList(), headerId);
			}
		}

		private Dictionary<int, IEnumerable<IEstimateRuleCommonParamEntity>> CreateAssemblyId2ParamsMap(IEnumerable<EstLineItemEntity> assembliesToUpdate, IEnumerable<EstLineItemParamEntity> assemblyParams, IEnumerable<EstAssemblyParamEntity> assemblyCatParams, IEnumerable<EstAssemblyCatEntity> assemblyCats)
		{
			var assemblyId2ParamsMap = new Dictionary<int, IEnumerable<IEstimateRuleCommonParamEntity>>();

			var assemblyId2AssemblyParamMap = assemblyParams.GroupBy(e => e.EstLineItemFk).ToDictionary(e => e.Key, e => e.ToList());

			var assemblyCatId2Entity = assemblyCats.GroupBy(e => e.Id).ToDictionary(e => e.Key, e => e.FirstOrDefault());

			var assemblyCatId2ParamsMap = assemblyCatParams.GroupBy(e => e.EstAssemblyCatFk).ToDictionary(e => e.Key, e => e.ToList());

			foreach (var assembly in assembliesToUpdate)
			{
				if (assemblyId2ParamsMap.ContainsKey(assembly.Id))
				{
					continue;
				}

				var paramsOfCurrentAssembly = new List<IEstimateRuleCommonParamEntity>();

				//add assembly parameter
				if (assemblyId2AssemblyParamMap.ContainsKey(assembly.Id))
				{
					paramsOfCurrentAssembly.AddRange(assemblyId2AssemblyParamMap[assembly.Id]);
				}

				//add assemblyCat and its parent parameter
				if (assembly.EstAssemblyCatFk.HasValue && assemblyCatId2Entity.ContainsKey(assembly.EstAssemblyCatFk.Value))
				{
					if (assemblyCatId2ParamsMap.ContainsKey(assembly.EstAssemblyCatFk.Value))
					{
						paramsOfCurrentAssembly.AddRange(assemblyCatId2ParamsMap[assembly.EstAssemblyCatFk.Value]);
					}

					var assemblyCatParent = assemblyCatId2Entity[assembly.EstAssemblyCatFk.Value].AssemblyCatParent;

					while (assemblyCatParent != null)
					{
						if (assemblyCatId2ParamsMap.ContainsKey(assemblyCatParent.Id))
						{
							paramsOfCurrentAssembly.AddRange(assemblyCatId2ParamsMap[assemblyCatParent.Id]);
						}

						assemblyCatParent = assemblyCatParent.AssemblyCatParent;
					}
				}

				//assemblyId2ParamsMap.Add(assembly.Id, DistinctParams(paramsOfCurrentAssembly));
			}

			return assemblyId2ParamsMap;
		}

		/// <summary>
		/// UpdatePlantResourceTreeByWot method is used to update the plant resource tree by work operation type
		/// </summary>
		/// <param name="data"></param>
		/// <returns></returns>
		public object UpdatePlantResourceTreeByWot(EquipmentAssemblyData data)
		{
			var plantResource = data.EstPlantResource;

			var estimateMainResourceLogic = new EstimateMainResourceLogic();
			var allResources = estimateMainResourceLogic.GetAssemblyResourceTree(plantResource.EstLineItemFk, plantResource.EstHeaderFk);
			var otherThanSelectedPlantResources = allResources.Where(r => r.Id != plantResource.Id).ToList();
			var completeResource = allResources.Where(r => r.Id == plantResource.Id).FirstOrDefault();
			if (completeResource != null)
			{
				plantResource.ResourceChildren = completeResource.ResourceChildren;
				plantResource.Resources = completeResource.Resources;

				var lineItem = new EstimateMainLineItemLogic().GetLineItemByFk(plantResource.EstLineItemFk, plantResource.EstHeaderFk);

				var plantAssemblyUpdateManager = new PlantAssemblyUpdateManager(data.ProjectId, plantResource.EstHeaderFk, null);

				// Ensure Job ID is set
				data.LgmJobId ??= EstJobHelper.GetProjectJobId(data.ProjectId);

				plantAssemblyUpdateManager.UpdateMultipliersFrmPlantEstimate(plantResource, data.LgmJobId, plantResource.WorkOperationTypeFk, false);

				// Add the current plant resource back to the list
				otherThanSelectedPlantResources.Add(plantResource);

				// Perform total calculation
				new EstimateTotalCalculator(plantResource.EstHeaderFk, lineItem.ProjectFk).CalculateLineItemAndResourcesInTree(lineItem, otherThanSelectedPlantResources);

				// Check system option for plant assembly view
				var showEquipmentAssembliesAsOneRecordSystemOption = new SystemOptionLogic().GetLatestValueAsBool(SystemOption.ShowEquipmentAssembliesAsOneRecord);

				if (showEquipmentAssembliesAsOneRecordSystemOption)
				{
					// Save only plant resource children, because Quantity Multipliers applied on childeren resources Quantity Factors1
					if (plantResource.ResourceChildren != null && plantResource.ResourceChildren.Any())
					{
						estimateMainResourceLogic.SaveResourcesInTree(plantResource.ResourceChildren);
					}

					// Clear the resource lists, don't display in UI
					plantResource.ResourceChildren.Clear();
					plantResource.Resources.Clear();
				}

				return new Dictionary<string, object>()
				{
					{ "LineItem", lineItem },
					{ "PlantResource", plantResource }
				};
			}

			// Return default values if resource not found
			return new Dictionary<string, object>()
			{
				{ "LineItem", new EstLineItemEntity() },
				{ "PlantResource", plantResource }
			};
		}
	}
}
