using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;

namespace RIB.Visual.Logistic.Plantcostalloc.BusinessComponents.Configurations
{
	/// <summary>
	/// BillingSheetStatusWorkflowConfigurationProvider
	/// </summary>
	[Export(typeof(IStatusConfigurationProvider))]
	public class BillingSheetStatusWorkflowConfigurationProvider : IStatusConfigurationProvider
	{
		/// <summary>
		/// GetConfiguration
		/// </summary>
		/// <returns></returns>
		IEnumerable<IStatusConfiguration> IStatusConfigurationProvider.GetConfiguration()
		{
			return new List<IStatusConfiguration>
			{
				new StatusConfiguration("logisticbillingsheetstatus", "LGM_BILLINGSHEET", "LGM_BILLINGSHEETSTATUS", true, false)
				{
					WorkFlowTableName = "LGM_BILLINGSHEETSTATWRKFLW",
					HistoryTableName = "LGM_BILLINGSHEETSTATUSHISTORY",
					RuleTableName = "LGM_BILLINGSHEETSTATUSRULE",
					RoleTableName = "LGM_BILLINGSHEETSTATUSROLE",
					RuleTableStatusFkColumnName = "LGM_BILLINGSHEETSTATUS_FK",
					RuleTableStatusTargetFkColumName = "LGM_BILLINGSHEETSTATUS_TARGET_FK"
				}
			};
		}
	}
}
