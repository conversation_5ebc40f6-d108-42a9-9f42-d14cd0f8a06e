﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Procurement.Contract.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Procurement.Contract.BusinessComponents.ConHeaderLookupVEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("CON_HEADER_LOOKUP_V")]
    public partial class ConHeaderLookupVEntity : ICloneable
    {
        /// <summary>
        /// Initialize a new ConHeaderLookupVEntity object.
        /// </summary>
        public ConHeaderLookupVEntity()
        {
          this.StatusDescriptionInfo = new DescriptionTranslateType();
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ControllingUnitFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_FK", TypeName = "int", Order = 1)]
        public virtual int? ControllingUnitFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcPackageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_FK", TypeName = "int", Order = 2)]
        public virtual int? PrcPackageFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcPackage2HeaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_FK", TypeName = "int", Order = 3)]
        public virtual int? PrcPackage2HeaderFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcStructureFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_STRUCTURE_FK", TypeName = "int", Order = 4)]
        public virtual int? PrcStructureFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateOrdered in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_ORDERED", TypeName = "date", Order = 5)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateOrdered {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinessPartner2Fk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER2_FK", TypeName = "int", Order = 6)]
        public virtual int? BusinessPartner2Fk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcConfigurationFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_FK", TypeName = "int", Order = 8)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcConfigurationFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_FK", TypeName = "int", Order = 9)]
        public virtual int? PaymentTermFiFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ExternalCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXTERNAL_CODE", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ExternalCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_FK", TypeName = "int", Order = 11)]
        public virtual int? PaymentTermPaFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TaxCodeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_FK", TypeName = "int", Order = 12)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int TaxCodeFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_FK", TypeName = "int", Order = 13)]
        public virtual int? ClerkPrcFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_FK", TypeName = "int", Order = 14)]
        public virtual int? ClerkReqFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpName1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BP_NAME1", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BpName1 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_HEADER_FK", TypeName = "int", Order = 17)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusIsVirtual in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISVIRTUAL", TypeName = "bit", Order = 18)]
        public virtual bool? StatusIsVirtual {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusIsReported in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISREPORTED", TypeName = "bit", Order = 19)]
        public virtual bool? StatusIsReported {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 20)]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public virtual string SearchPattern {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CurrencyFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_FK", TypeName = "int", Order = 21)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CurrencyFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcBillingSchemaFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_FK", TypeName = "int", Order = 22)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int MdcBillingSchemaFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectChangeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_FK", TypeName = "int", Order = 23)]
        public virtual int? ProjectChangeFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Exchangerate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 24)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal Exchangerate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdContactFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_FK", TypeName = "int", Order = 25)]
        public virtual int? BpdContactFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdSubsidiaryFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_FK", TypeName = "int", Order = 26)]
        public virtual int? BpdSubsidiaryFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdSupplierFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_FK", TypeName = "int", Order = 27)]
        public virtual int? BpdSupplierFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpdVatGroupFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_FK", TypeName = "int", Order = 28)]
        public virtual int? BpdVatGroupFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusIsLive in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISLIVE", TypeName = "bit", Order = 29)]
        public virtual bool? StatusIsLive {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusIsCanceled in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISCANCELED", TypeName = "bit", Order = 30)]
        public virtual bool? StatusIsCanceled {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusIsDelivered in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISDELIVERED", TypeName = "bit", Order = 31)]
        public virtual bool? StatusIsDelivered {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusIsReadonly in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISREADONLY", TypeName = "bit", Order = 32)]
        public virtual bool? StatusIsReadonly {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusIsInvoiced in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISINVOICED", TypeName = "bit", Order = 33)]
        public virtual bool? StatusIsInvoiced {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusIsOrdered in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISORDERED", TypeName = "bit", Order = 34)]
        public virtual bool? StatusIsOrdered {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConStatusFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONSTATUSFK", TypeName = "int", Order = 36)]
        public virtual int? ConStatusFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcConfigHeaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGHEADER_FK", TypeName = "int", Order = 38)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcConfigHeaderFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusIsRejected in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISREJECTED", TypeName = "bit", Order = 37)]
        public virtual bool? StatusIsRejected {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for StatusDescriptionInfo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STATUSDESCRIPTION", TypeName = "nvarchar(252)", Order = 35, TranslationColumnName = "STATUSDESCRIPTIONTR")]
        public virtual DescriptionTranslateType StatusDescriptionInfo {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Icon in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ICON", TypeName = "int", Order = 40)]
        public virtual int? Icon {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BpName2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BP_NAME2", TypeName = "nvarchar(252)", Order = 41)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BpName2 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 42)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectNo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROJECTNO", TypeName = "nvarchar(16)", Order = 43)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ProjectNo {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectName in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROJECT_NAME", TypeName = "nvarchar(252)", Order = 44)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ProjectName {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_FK", TypeName = "int", Order = 47)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Bp2Name1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BP2_NAME1", TypeName = "nvarchar(252)", Order = 45)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Bp2Name1 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Bp2Name2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BP2_NAME2", TypeName = "nvarchar(252)", Order = 46)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Bp2Name2 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinessPartnerFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_FK", TypeName = "int", Order = 49)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int BusinessPartnerFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Supplier2Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SUPPLIER2_CODE", TypeName = "nvarchar(252)", Order = 48)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Supplier2Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_FK", TypeName = "int", Order = 50)]
        public virtual int? ProjectFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CodeQuotation in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE_QUOTATION", TypeName = "nvarchar(20)", Order = 51)]
        [System.ComponentModel.DataAnnotations.StringLength(20)]
        public virtual string CodeQuotation {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ConHeaderFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_FK", TypeName = "int", Order = 52)]
        public virtual int? ConHeaderFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcCopyModeFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_FK", TypeName = "int", Order = 53)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcCopyModeFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsFreeItemsAllowed in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFREEITEMSALLOWED", TypeName = "bit", Order = 54)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsFreeItemsAllowed {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SalesTaxMethodFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_FK", TypeName = "int", Order = 55)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int SalesTaxMethodFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for IsFramework in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFRAMEWORK", TypeName = "bit", Order = 56)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool IsFramework {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BankFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_FK", TypeName = "int", Order = 57)]
        public virtual int? BankFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcMaterialCatalogFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_FK", TypeName = "int", Order = 58)]
        public virtual int? MdcMaterialCatalogFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_FK", TypeName = "int", Order = 59)]
        public virtual int? BoqWicCatFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 60)]
        public virtual int? BasLanguageFk {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            ConHeaderLookupVEntity obj = new ConHeaderLookupVEntity();
            obj.Id = Id;
            obj.ControllingUnitFk = ControllingUnitFk;
            obj.PrcPackageFk = PrcPackageFk;
            obj.PrcPackage2HeaderFk = PrcPackage2HeaderFk;
            obj.PrcStructureFk = PrcStructureFk;
            obj.DateOrdered = DateOrdered;
            obj.BusinessPartner2Fk = BusinessPartner2Fk;
            obj.Code = Code;
            obj.PrcConfigurationFk = PrcConfigurationFk;
            obj.PaymentTermFiFk = PaymentTermFiFk;
            obj.ExternalCode = ExternalCode;
            obj.PaymentTermPaFk = PaymentTermPaFk;
            obj.TaxCodeFk = TaxCodeFk;
            obj.ClerkPrcFk = ClerkPrcFk;
            obj.ClerkReqFk = ClerkReqFk;
            obj.Description = Description;
            obj.BpName1 = BpName1;
            obj.PrcHeaderId = PrcHeaderId;
            obj.StatusIsVirtual = StatusIsVirtual;
            obj.StatusIsReported = StatusIsReported;
            obj.SearchPattern = SearchPattern;
            obj.CurrencyFk = CurrencyFk;
            obj.MdcBillingSchemaFk = MdcBillingSchemaFk;
            obj.ProjectChangeFk = ProjectChangeFk;
            obj.Exchangerate = Exchangerate;
            obj.BpdContactFk = BpdContactFk;
            obj.BpdSubsidiaryFk = BpdSubsidiaryFk;
            obj.BpdSupplierFk = BpdSupplierFk;
            obj.BpdVatGroupFk = BpdVatGroupFk;
            obj.StatusIsLive = StatusIsLive;
            obj.StatusIsCanceled = StatusIsCanceled;
            obj.StatusIsDelivered = StatusIsDelivered;
            obj.StatusIsReadonly = StatusIsReadonly;
            obj.StatusIsInvoiced = StatusIsInvoiced;
            obj.StatusIsOrdered = StatusIsOrdered;
            obj.ConStatusFk = ConStatusFk;
            obj.PrcConfigHeaderFk = PrcConfigHeaderFk;
            obj.StatusIsRejected = StatusIsRejected;
            obj.StatusDescriptionInfo = (DescriptionTranslateType)StatusDescriptionInfo.Clone();
            obj.Icon = Icon;
            obj.BpName2 = BpName2;
            obj.SupplierCode = SupplierCode;
            obj.ProjectNo = ProjectNo;
            obj.ProjectName = ProjectName;
            obj.CompanyFk = CompanyFk;
            obj.Bp2Name1 = Bp2Name1;
            obj.Bp2Name2 = Bp2Name2;
            obj.BusinessPartnerFk = BusinessPartnerFk;
            obj.Supplier2Code = Supplier2Code;
            obj.ProjectFk = ProjectFk;
            obj.CodeQuotation = CodeQuotation;
            obj.ConHeaderFk = ConHeaderFk;
            obj.PrcCopyModeFk = PrcCopyModeFk;
            obj.IsFreeItemsAllowed = IsFreeItemsAllowed;
            obj.SalesTaxMethodFk = SalesTaxMethodFk;
            obj.IsFramework = IsFramework;
            obj.BankFk = BankFk;
            obj.MdcMaterialCatalogFk = MdcMaterialCatalogFk;
            obj.BoqWicCatFk = BoqWicCatFk;
            obj.BasLanguageFk = BasLanguageFk;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(ConHeaderLookupVEntity clonedEntity);

    }


}
