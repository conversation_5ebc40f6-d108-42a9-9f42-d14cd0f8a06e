import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _package, _procurementContractPage, _procurementPage, _projectPage, _schedulePage, _validate, _wizardCreateRequest } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let CONTAINERS_CONTRACT,CONTAINERS_ADVANCES
let CONTAINER_COLUMNS_CONTRACT,CONTAINER_COLUMNS_ADVANCES

const ADVANCES_DES = _common.generateRandomString(4);
const COMMENTTEXT = "TEXT"+_common.generateRandomString(4);

describe("PCM- 4.122 | Advance container in contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("procurement-and-bpm/con-4.122-advance-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACTS
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACTS
            CONTAINERS_ADVANCES = this.data.CONTAINERS.ADVANCES
            CONTAINER_COLUMNS_ADVANCES = this.data.CONTAINER_COLUMNS.ADVANCES
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER
            }
         
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, "M_TRAINING_HB").pinnedItem();
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create and verify new advances record", function () {
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CONTRACT_CODE"))
        _common.select_rowHasValue(cnt.uuid.PROCUREMENTCONTRACT,Cypress.env("CONTRACT_CODE"))
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ADVANCES, app.FooterTab.ADVANCES);
            _common.setup_gridLayout(cnt.uuid.ADVANCES, CONTAINER_COLUMNS_ADVANCES)
        });
        _common.clear_subContainerFilter(cnt.uuid.ADVANCES)
        _common.create_newRecord(cnt.uuid.ADVANCES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.ADVANCES,app.GridCells.PRC_ADVANCE_TYPE_FK,commonLocators.CommonKeys.LIST,CONTAINERS_ADVANCES.ADVANCE_TYPE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _validate.verify_isRecordPresent(cnt.uuid.ADVANCES,CONTAINERS_ADVANCES.ADVANCE_TYPE)
    });

    it("TC - Delete and verify advances record", function () {
        _common.select_rowHasValue(cnt.uuid.ADVANCES,CONTAINERS_ADVANCES.ADVANCE_TYPE)
        _common.delete_recordFromContainer(cnt.uuid.ADVANCES)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _validate.verify_isRecordDeleted(cnt.uuid.ADVANCES,CONTAINERS_ADVANCES.ADVANCE_TYPE)
    })
    it("TC - Fill input filed and lookup filter correctly", function () {
        _common.clear_subContainerFilter(cnt.uuid.ADVANCES)
        _common.maximizeContainer(cnt.uuid.ADVANCES)
        _common.create_newRecord(cnt.uuid.ADVANCES)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.DESCRIPTION,app.InputFields.DOMAIN_TYPE_DESCRIPTION,ADVANCES_DES)
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.DATE_DUE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ADVANCES.DATE_DUE)
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.AMOUNT_DUE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ADVANCES.AMOUNT_DUE[0])
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.DATE_DONE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ADVANCES.DATE_DONE)
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.AMOUNT_DONE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ADVANCES.AMOUNT_DONE[0])
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.COMMENT_TEXT,app.InputFields.DOMAIN_TYPE_COMMENT,COMMENTTEXT)
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.USER_DEFINED_1,app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_ADVANCES.USER1)
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.USER_DEFINED_2,app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTAINERS_ADVANCES.USER2)
        _common.edit_dropdownCellWithCaret(cnt.uuid.ADVANCES,app.GridCells.PRC_ADVANCE_TYPE_FK,commonLocators.CommonKeys.LIST,CONTAINERS_ADVANCES.ADVANCE_TYPE)
        _common.edit_dropdownCellWithCaret(cnt.uuid.ADVANCES,app.GridCells.PAYMENT_TERM_FK,commonLocators.CommonKeys.GRID,CONTAINERS_ADVANCES.PAYMENT_TERM)
        cy.SAVE()
    })
    it("TC - Verify amount due(oc) from amount due", function () {
        _common.clear_subContainerFilter(cnt.uuid.ADVANCES)
        _common.select_rowHasValue(cnt.uuid.ADVANCES,ADVANCES_DES)
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.AMOUNT_DUE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ADVANCES.AMOUNT_DUE[1])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.assert_cellData_insideActiveRow(cnt.uuid.ADVANCES,app.GridCells.AMOUNT_DUE,CONTAINERS_ADVANCES.AMOUNT_DUE[1])
    })
    it("TC - Verify amount done(oc) from amount done", function () {
        _common.clear_subContainerFilter(cnt.uuid.ADVANCES)
        _common.select_rowHasValue(cnt.uuid.ADVANCES,ADVANCES_DES)
        _common.edit_containerCell(cnt.uuid.ADVANCES,app.GridCells.AMOUNT_DONE,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_ADVANCES.AMOUNT_DONE[1])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.assert_cellData_insideActiveRow(cnt.uuid.ADVANCES,app.GridCells.AMOUNT_DONE_OC,CONTAINERS_ADVANCES.AMOUNT_DONE[1])
        _common.minimizeContainer(cnt.uuid.ADVANCES)
    })
});