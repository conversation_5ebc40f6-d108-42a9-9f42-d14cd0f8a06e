﻿//------------------------------------------------------------------------------
// This is auto-generated code. by GenerateEntityFileHeader
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.BusinessComponents;


namespace RIB.Visual.Procurement.PublicApi.BusinessComponents
{

    /// <summary>
    /// There are no comments for RIB.Visual.Procurement.PublicApi.BusinessComponents.ReqHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("REQ_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(6)]
    public partial class ReqHeaderApiEntity : EntityBase, ICloneable
    {
        /// <summary>
        /// Initialize a new ReqHeaderApiEntity object.
        /// </summary>
        public ReqHeaderApiEntity()
        {
            OnConstruct(); // call partial constructor if present
        }

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int Id {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ReqStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReqStatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ReqStatusId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ReqStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ReqStatusDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CompanyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string CompanyCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public virtual int? ProjectId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ProjectCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ProjectDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PackageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PackageFk")]
        public virtual int? PackageId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PackageCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PackageCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PackageDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_DESC", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string PackageDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TaxCodeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TaxCodeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int TaxCodeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TaxCodeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_CODE", TypeName = "nvarchar(16)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string TaxCodeCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for TaxCodeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_DESC", TypeName = "nvarchar(2000)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string TaxCodeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public virtual int? ClerkPrcId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkPrcCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkPrcDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 17)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public virtual int? ClerkReqId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 18)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ClerkReqCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 19)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ClerkReqDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasCurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int CurrencyId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string CurrencyDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Exchangerate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 22)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal Exchangerate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrjChangeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_ID", TypeName = "int", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectChangeFk")]
        public virtual int? PrjChangeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrjChangeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_CODE", TypeName = "nvarchar(16)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PrjChangeCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrjChangeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_DESC", TypeName = "nvarchar(252)", Order = 25)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string PrjChangeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ReqHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_HEADER_ID", TypeName = "int", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReqHeaderFk")]
        public virtual int? ReqHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ReqHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_HEADER_CODE", TypeName = "nvarchar(16)", Order = 27)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string ReqHeaderCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ReqHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_HEADER_DESC", TypeName = "nvarchar(252)", Order = 28)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string ReqHeaderDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Haschanges in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("HASCHANGES", TypeName = "bit", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("HasChanges")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual bool Haschanges {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MaterialCatalogId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_ID", TypeName = "int", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("MaterialCatalogFk")]
        public virtual int? MaterialCatalogId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MaterialCatalogCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_CODE", TypeName = "nvarchar(16)", Order = 31)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string MaterialCatalogCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MaterialCatalogDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_DESC", TypeName = "nvarchar(2000)", Order = 32)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MaterialCatalogDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_HEADER_ID", TypeName = "int", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcHeaderFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcHeaderId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 34)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcConfigurationId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 35)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual string Code {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 36)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string Description {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 37)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public virtual string SearchPattern {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateReceived in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_RECEIVED", TypeName = "date", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateReceived")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateReceived {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateCanceled in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CANCELED", TypeName = "date", Order = 39)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCanceled")]
        public virtual System.DateTime? DateCanceled {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateRequired in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REQUIRED", TypeName = "date", Order = 40)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateRequired")]
        public virtual System.DateTime? DateRequired {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ReqTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_TYPE_ID", TypeName = "int", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReqTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int ReqTypeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for ReqTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 42)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string ReqTypeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcControllingunitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingUnitFk")]
        public virtual int? MdcControllingunitId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcControllingunitCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(32)", Order = 44)]
        [System.ComponentModel.DataAnnotations.StringLength(32)]
        public virtual string MdcControllingunitCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for MdcControllingunitDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 45)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string MdcControllingunitDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinesspartnerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        public virtual int? BusinesspartnerId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BusinesspartnerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 47)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BusinesspartnerDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SubsidiaryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public virtual int? SubsidiaryId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SubsidiaryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 49)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SubsidiaryDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public virtual int? SupplierId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 51)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SupplierDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 52)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string SupplierDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcIncotermId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_ID", TypeName = "int", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IncotermFk")]
        public virtual int? PrcIncotermId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcIncotermDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_DESC", TypeName = "nvarchar(2000)", Order = 54)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcIncotermDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AddressId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_ID", TypeName = "int", Order = 55)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AddressFk")]
        public virtual int? AddressId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for AddressDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_DESC", TypeName = "nvarchar(2000)", Order = 56)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string AddressDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string Remark {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_ID", TypeName = "int", Order = 58)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasPaymentTermPaFk")]
        public virtual int? PaymentTermPaId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_CODE", TypeName = "nvarchar(16)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermPaCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermPaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_DESC", TypeName = "nvarchar(2000)", Order = 60)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermPaDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_ID", TypeName = "int", Order = 61)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasPaymentTermFiFk")]
        public virtual int? PaymentTermFiId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_CODE", TypeName = "nvarchar(16)", Order = 62)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermFiCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermFiDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_DESC", TypeName = "nvarchar(2000)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermFiDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcContracttypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_ID", TypeName = "int", Order = 64)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcContracttypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcContracttypeId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcContracttypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_DESC", TypeName = "nvarchar(2000)", Order = 65)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcContracttypeDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcAwardmethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_ID", TypeName = "int", Order = 66)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcAwardmethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int PrcAwardmethodId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcAwardmethodDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_DESC", TypeName = "nvarchar(2000)", Order = 67)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PrcAwardmethodDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcPackage2headerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_ID", TypeName = "int", Order = 68)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Package2HeaderFk")]
        public virtual int? PrcPackage2headerId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PrcPackage2headerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_DESC", TypeName = "nvarchar(252)", Order = 69)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string PrcPackage2headerDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 70)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined1 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 71)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined2 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 72)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined3 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 73)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined4 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 74)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UserDefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string UserDefined5 {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OverallDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT", TypeName = "numeric(19,7)", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscount {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OverallDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscountOc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for OverallDiscountPercent in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_PERCENT", TypeName = "numeric(9,3)", Order = 77)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual decimal OverallDiscountPercent {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_ID", TypeName = "int", Order = 83)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasPaymentTermAdFk")]
        public virtual int? PaymentTermAdId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_CODE", TypeName = "nvarchar(16)", Order = 84)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string PaymentTermAdCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PaymentTermAdDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_DESC", TypeName = "nvarchar(2000)", Order = 85)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string PaymentTermAdDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateEffective in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_EFFECTIVE", TypeName = "date", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateEffective")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual System.DateTime DateEffective {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for VatGroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 87)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public virtual int? VatGroupId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for VatGroupDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 88)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string VatGroupDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int SalesTaxMethodId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 90)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string SalesTaxMethodDesc {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DeadlineDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DEADLINE_DATE", TypeName = "date", Order = 91)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? DeadlineDate {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DeadlineTime in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DEADLINE_TIME", TypeName = "time", Order = 97)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual global::System.TimeSpan? DeadlineTime {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 98)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? DateDelivery {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatBoqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_BOQ_ID", TypeName = "int", Order = 92)]
        public virtual int? BoqWicCatBoqId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatBoqReference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_BOQ_REFERENCE", TypeName = "nvarchar(252)", Order = 94)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BoqWicCatBoqReference {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_ID", TypeName = "int", Order = 93)]
        public virtual int? BoqWicCatId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_CODE", TypeName = "nvarchar(16)", Order = 95)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public virtual string BoqWicCatCode {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BoqWicCatDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_DESC", TypeName = "nvarchar(2000)", Order = 96)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public virtual string BoqWicCatDescription {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 99)]
        [System.ComponentModel.DataAnnotations.Required()]
        public virtual int LanguageId {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DatePriceFixing in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_PRICEFIXING", TypeName = "date", Order = 100)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual System.DateTime? DatePriceFixing {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlannedStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_START", TypeName = "date", Order = 101)]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedStart")]
        public virtual System.DateTime? PlannedStart {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for PlannedEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PLANNED_END", TypeName = "date", Order = 102)]
        [RIB.Visual.Platform.Common.InternalApiField("PlannedEnd")]
        public virtual System.DateTime? PlannedEnd {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateAwardDeadline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_AWARDDEADLINE", TypeName = "date", Order = 103)]
        [RIB.Visual.Platform.Common.InternalApiField("DateAwardDeadline")]
        public virtual System.DateTime? DateAwardDeadline {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for DateRequested in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REQUESTED", TypeName = "date", Order = 104)]
        [RIB.Visual.Platform.Common.InternalApiField("DateRequested")]
        public virtual System.DateTime? DateRequested {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 105)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public virtual int? BasLanguageFk {
            get; set;
        }

    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 106)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public virtual string BasLanguageDesc {
            get; set;
        }


        #endregion

        #region ICloneable Members

        /// <summary/>
        public virtual object Clone()
        {
            ReqHeaderApiEntity obj = new ReqHeaderApiEntity();
            obj.Id = Id;
            obj.ReqStatusId = ReqStatusId;
            obj.ReqStatusDescription = ReqStatusDescription;
            obj.CompanyId = CompanyId;
            obj.CompanyCode = CompanyCode;
            obj.ProjectId = ProjectId;
            obj.ProjectCode = ProjectCode;
            obj.ProjectDescription = ProjectDescription;
            obj.PackageId = PackageId;
            obj.PackageCode = PackageCode;
            obj.PackageDescription = PackageDescription;
            obj.TaxCodeId = TaxCodeId;
            obj.TaxCodeCode = TaxCodeCode;
            obj.TaxCodeDescription = TaxCodeDescription;
            obj.ClerkPrcId = ClerkPrcId;
            obj.ClerkPrcCode = ClerkPrcCode;
            obj.ClerkPrcDescription = ClerkPrcDescription;
            obj.ClerkReqId = ClerkReqId;
            obj.ClerkReqCode = ClerkReqCode;
            obj.ClerkReqDescription = ClerkReqDescription;
            obj.CurrencyId = CurrencyId;
            obj.CurrencyDescription = CurrencyDescription;
            obj.Exchangerate = Exchangerate;
            obj.PrjChangeId = PrjChangeId;
            obj.PrjChangeCode = PrjChangeCode;
            obj.PrjChangeDescription = PrjChangeDescription;
            obj.ReqHeaderId = ReqHeaderId;
            obj.ReqHeaderCode = ReqHeaderCode;
            obj.ReqHeaderDescription = ReqHeaderDescription;
            obj.Haschanges = Haschanges;
            obj.MaterialCatalogId = MaterialCatalogId;
            obj.MaterialCatalogCode = MaterialCatalogCode;
            obj.MaterialCatalogDescription = MaterialCatalogDescription;
            obj.PrcHeaderId = PrcHeaderId;
            obj.PrcConfigurationId = PrcConfigurationId;
            obj.Code = Code;
            obj.Description = Description;
            obj.SearchPattern = SearchPattern;
            obj.DateReceived = DateReceived;
            obj.DateCanceled = DateCanceled;
            obj.DateRequired = DateRequired;
            obj.ReqTypeId = ReqTypeId;
            obj.ReqTypeDescription = ReqTypeDescription;
            obj.MdcControllingunitId = MdcControllingunitId;
            obj.MdcControllingunitCode = MdcControllingunitCode;
            obj.MdcControllingunitDescription = MdcControllingunitDescription;
            obj.BusinesspartnerId = BusinesspartnerId;
            obj.BusinesspartnerDescription = BusinesspartnerDescription;
            obj.SubsidiaryId = SubsidiaryId;
            obj.SubsidiaryDescription = SubsidiaryDescription;
            obj.SupplierId = SupplierId;
            obj.SupplierCode = SupplierCode;
            obj.SupplierDescription = SupplierDescription;
            obj.PrcIncotermId = PrcIncotermId;
            obj.PrcIncotermDescription = PrcIncotermDescription;
            obj.AddressId = AddressId;
            obj.AddressDescription = AddressDescription;
            obj.Remark = Remark;
            obj.PaymentTermPaId = PaymentTermPaId;
            obj.PaymentTermPaCode = PaymentTermPaCode;
            obj.PaymentTermPaDescription = PaymentTermPaDescription;
            obj.PaymentTermFiId = PaymentTermFiId;
            obj.PaymentTermFiCode = PaymentTermFiCode;
            obj.PaymentTermFiDescription = PaymentTermFiDescription;
            obj.PrcContracttypeId = PrcContracttypeId;
            obj.PrcContracttypeDescription = PrcContracttypeDescription;
            obj.PrcAwardmethodId = PrcAwardmethodId;
            obj.PrcAwardmethodDescription = PrcAwardmethodDescription;
            obj.PrcPackage2headerId = PrcPackage2headerId;
            obj.PrcPackage2headerDescription = PrcPackage2headerDescription;
            obj.UserDefined1 = UserDefined1;
            obj.UserDefined2 = UserDefined2;
            obj.UserDefined3 = UserDefined3;
            obj.UserDefined4 = UserDefined4;
            obj.UserDefined5 = UserDefined5;
            obj.OverallDiscount = OverallDiscount;
            obj.OverallDiscountOc = OverallDiscountOc;
            obj.OverallDiscountPercent = OverallDiscountPercent;
            obj.InsertedAt = InsertedAt;
            obj.InsertedBy = InsertedBy;
            obj.UpdatedAt = UpdatedAt;
            obj.UpdatedBy = UpdatedBy;
            obj.Version = Version;
            obj.PaymentTermAdId = PaymentTermAdId;
            obj.PaymentTermAdCode = PaymentTermAdCode;
            obj.PaymentTermAdDescription = PaymentTermAdDescription;
            obj.DateEffective = DateEffective;
            obj.VatGroupId = VatGroupId;
            obj.VatGroupDescription = VatGroupDescription;
            obj.SalesTaxMethodId = SalesTaxMethodId;
            obj.SalesTaxMethodDesc = SalesTaxMethodDesc;
            obj.DeadlineDate = DeadlineDate;
            obj.DeadlineTime = DeadlineTime;
            obj.DateDelivery = DateDelivery;
            obj.BoqWicCatBoqId = BoqWicCatBoqId;
            obj.BoqWicCatBoqReference = BoqWicCatBoqReference;
            obj.BoqWicCatId = BoqWicCatId;
            obj.BoqWicCatCode = BoqWicCatCode;
            obj.BoqWicCatDescription = BoqWicCatDescription;
            obj.LanguageId = LanguageId;
            obj.DatePriceFixing = DatePriceFixing;
            obj.PlannedStart = PlannedStart;
            obj.PlannedEnd = PlannedEnd;
            obj.DateAwardDeadline = DateAwardDeadline;
            obj.DateRequested = DateRequested;
            obj.BasLanguageFk = BasLanguageFk;
            obj.BasLanguageDesc = BasLanguageDesc;
            // call partial method if implemented
            OnClone(obj);

            return obj;
        }

        #endregion

    /// <summary> prototypes for partial OnConstruct Method </summary>
    partial void OnConstruct();

    /// <summary> prototypes for partial OnClone Method </summary>
		/// <param name="clonedEntity"></param>
    partial void OnClone(ReqHeaderApiEntity clonedEntity);

    }


}
