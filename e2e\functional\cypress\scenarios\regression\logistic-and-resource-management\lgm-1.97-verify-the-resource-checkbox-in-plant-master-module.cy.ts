
import { _common, _commonAPI, _logesticPage, _validate } from "cypress/pages";
import { app, tile, cnt, commonLocators, sidebar, btn } from "cypress/locators";
import { DataCells } from "cypress/pages/interfaces";
import apiConstantData from "cypress/constantData/apiConstantData";

let CONTAINER_COLUMNS_CONTROLLING_UNIT
let CONTAINERS_CONTROLLING_UNIT
let CONTROLLING_UNIT_A_PARAMETERS:DataCells,
PLANT_COMPONENT_PARAMETERS:DataCells,
MAINTENANCE_PARAMETERS:DataCells,
RESOURCE_PARAMETERS:DataCells

let CONTAINERS_PLANT_GROUP,
    CONTAINER_COLUMNS_PLANT_GROUP
let CONTAINERS_PLANT,
    CONTAINER_COLUMNS_PLANT,
    PLANT_PARAMETERS,
    CONTAINER_COLUMNS_PLANT_CONTROLLING,
    CONTAINERS_PLANT_COMPONENT,
    CONTAINERS_PLANT_RESOURCE
let CONTAINERS_PLANT_PRICE_LISTS


const PLANT_GROUP = _common.generateRandomString(3)
const PLANT_CODE = _common.generateRandomString(3)
const PLANT_GROUP_DESC = _common.generateRandomString(3)
const SUB_PLANT_GROUP = _common.generateRandomString(3)
const SUB_PLANT_GROUP_DESC = _common.generateRandomString(3)
const PLANT_DESCRIPTION = _common.generateRandomString(3)
const SITE_DESCRIPTION = _common.generateRandomString(3)
const RESOURCE_TYPE_DESCRIPTION = _common.generateRandomString(3)
const SITE_CODE = _common.generateRandomString(3)
const MAINTENANCE_DESCRIPTION = _common.generateRandomString(3)
const MAINTENANCE_CODE = _common.generateRandomString(3)



describe("LRM- 1.97 | Verify the Resource checkbox in plant master module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
   
    before(function () {
        cy.fixture("LRM/lgm-1.97-verify-the-resource-checkbox-in-plant-master-module.json")
          .then((data) => {
            this.data = data;
            CONTAINER_COLUMNS_CONTROLLING_UNIT=this.data.CONTAINER_COLUMNS.CONTROLLING_UNIT
            CONTAINERS_CONTROLLING_UNIT=this.data.CONTAINERS.CONTROLLING_UNIT
            CONTROLLING_UNIT_A_PARAMETERS={
                [app.GridCells.QUANTITY_SMALL]:[CONTAINERS_CONTROLLING_UNIT.QUANTITY[0],CONTAINERS_CONTROLLING_UNIT.QUANTITY[0]],
                [app.GridCells.BAS_UOM_FK]:[apiConstantData.ID.UOM_BAGS,apiConstantData.ID.UOM_BAGS],
                [app.GridCells.IS_PLANTMANAGEMENT]:["true","true"]
            }
            CONTAINERS_PLANT_GROUP = this.data.CONTAINERS.PLANT_GROUP;
            CONTAINER_COLUMNS_PLANT_GROUP = this.data.CONTAINER_COLUMNS.PLANT_GROUP;
            CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;
            CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
            PLANT_PARAMETERS = {
                [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION,
                [app.GridCells.PLANT_GROUP_FK]: SUB_PLANT_GROUP,
                [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
                [app.GridCells.PLANT_TYPE_FK]: CONTAINERS_PLANT.PLANT_TYPE,
                [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
                [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
            }
            CONTAINER_COLUMNS_PLANT_CONTROLLING = this.data.CONTAINER_COLUMNS.PLANT_CONTROLLING;
            CONTAINERS_PLANT_PRICE_LISTS = this.data.CONTAINERS.PLANT_PRICE_LISTS;
          
            CONTAINERS_PLANT_COMPONENT = this.data.CONTAINERS.PLANT_COMPONENT;
            PLANT_COMPONENT_PARAMETERS = {
                [app.GridCells.MAINTENANCE_SCHEMA_FK]: CONTAINERS_PLANT_COMPONENT.MAINTENANCE_SCHEMA,
                [app.GridCells.PLANT_COMPONENT_TYPE_FK]: CONTAINERS_PLANT_COMPONENT.PLANT_COMPONENT_TYPE,
                [app.GridCells.VALID_FROM]: _common.getDate(commonLocators.CommonKeys.CURRENT),
                [app.GridCells.VALID_TO]: _common.getDate(commonLocators.CommonKeys.CURRENT,30),
                [app.GridCells.UOM_FK]:CONTAINERS_PLANT_COMPONENT.UOM
            }  
            
            MAINTENANCE_PARAMETERS = {
                [app.GridCells.CODE]: MAINTENANCE_CODE,
                [app.GridCells.DESCRIPTION]: MAINTENANCE_DESCRIPTION,
                [app.GridCells.START_DATE]: _common.getDate(commonLocators.CommonKeys.CURRENT),
                [app.GridCells.END_DATE_SMALL]: _common.getDate(commonLocators.CommonKeys.CURRENT,30),
            }   

            CONTAINERS_PLANT_RESOURCE=this.data.CONTAINERS.PLANT_RESOURCE
            RESOURCE_PARAMETERS= {
                [app.ModalInputFields.SITE]: SITE_DESCRIPTION,
                [app.ModalInputFields.TYPE]: RESOURCE_TYPE_DESCRIPTION,
                [app.ModalInputFields.UOM_TIME]: CONTAINERS_PLANT_RESOURCE.UOM_TIME,
                [app.ModalInputFields.RESOURCE_GROUP]:CONTAINERS_PLANT_RESOURCE.RESOURCE_GROUP,
                [app.ModalInputFields.RESOURCE_KIND]:CONTAINERS_PLANT_RESOURCE.RESOURCE_KIND
            }   
        })
        .then(()=>{
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
            _commonAPI.getAccessToken()
                      .then((result) => {
                        cy.log(`Token Retrieved: ${result.token}`);
                      });
        })
    });

    after(() => {
        cy.LOGOUT();
    })

    it("TC - API: Create project and controlling unit", function () {
        _commonAPI.createProject()
                  .then(() => {
                    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
                    _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'),2,CONTROLLING_UNIT_A_PARAMETERS)
                  });
    })

    it("TC - Create site", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.SITE)
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar()

        _common.openTab(app.TabBar.SITE).then(() => {
            _common.setDefaultView(app.TabBar.SITE,commonLocators.CommonKeys.DEFAULT)
            _common.select_tabFromFooter(cnt.uuid.SITES, app.FooterTab.SITES, 0)
            _common.clear_subContainerFilter(cnt.uuid.SITES)
        })
        _common.create_newRecord(cnt.uuid.SITES)
        _common.enterRecord_inNewRow(cnt.uuid.SITES,app.GridCells.CODE,app.InputFields.DOMAIN_TYPE_CODE,SITE_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.SITES,app.GridCells.DESCRIPTION_INFO,app.InputFields.DOMAIN_TYPE_TRANSLATION,SITE_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.SITES)
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create resource type", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.RESOURCE_TYPE)
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar()

        _common.openTab(app.TabBar.TYPES_RESOURCE_TYPE).then(() => {
            _common.setDefaultView(app.TabBar.TYPES_RESOURCE_TYPE,commonLocators.CommonKeys.DEFAULT)
            _common.select_tabFromFooter(cnt.uuid.RESOURCE_TYPES, app.FooterTab.RESOURCE_TYPES, 0)
            _common.clear_subContainerFilter(cnt.uuid.RESOURCE_TYPES)
        })

        _common.create_newRecord(cnt.uuid.RESOURCE_TYPES)
        _common.edit_dropdownCellWithInput(cnt.uuid.RESOURCE_TYPES,app.GridCells.UOM_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_CONTROLLING_UNIT.UOM)
        _common.enterRecord_inNewRow(cnt.uuid.RESOURCE_TYPES,app.GridCells.DESCRIPTION_INFO,app.InputFields.DOMAIN_TYPE_TRANSLATION,RESOURCE_TYPE_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.RESOURCE_TYPES)
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })

    it("TC - Create new plant group and sub record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_GROUP)
        _common.openTab(app.TabBar.PLANT_GROUP_AND_LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_GROUP, app.FooterTab.PLANT_GROUPS, 0)
            _common.setup_gridLayout(cnt.uuid.PLANT_GROUP, CONTAINER_COLUMNS_PLANT_GROUP)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_GROUP)
        })
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.PLANT_GROUP)
        _common.clickOn_expandCollapseButton(cnt.uuid.PLANT_GROUP, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.COLLAPSE_ALL)
        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.PLANT_GROUP)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, PLANT_GROUP_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.PLANT_GROUP, PLANT_GROUP_DESC)
        _common.select_rowHasValue(cnt.uuid.PLANT_GROUP, PLANT_GROUP_DESC)
        _common.create_newSubRecord(cnt.uuid.PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, SUB_PLANT_GROUP)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_GROUP, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, SUB_PLANT_GROUP_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_GROUP, app.GridCells.RUBRIC_CATEGORY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_GROUP.RUBRIC_CATEGORY)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create new plant", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
            _common.setup_gridLayout(cnt.uuid.PLANT, CONTAINER_COLUMNS_PLANT)
            _common.set_columnAtTop([CONTAINER_COLUMNS_PLANT.isresource],cnt.uuid.PLANT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()


        _common.maximizeContainer(cnt.uuid.PLANT)
        _common.create_newRecord(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS)
        _common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE);
        cy.wait(1000) // Added this wait as script is getting failed as loader is taking time to load
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
    })

    it('TC - Add controlling unit record to plant in plant master module', function () {
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            cy.REFRESH_CONTAINER()
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PLANT_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.PLANT)
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_CONTROLLING, app.FooterTab.CONTROLLING_UNIT)
            _common.setup_gridLayout(cnt.uuid.PLANT_CONTROLLING, CONTAINER_COLUMNS_PLANT_CONTROLLING)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_CONTROLLING)
        });
        _common.create_newRecord(cnt.uuid.PLANT_CONTROLLING)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to appear dropdown
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_CONTROLLING, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Assign price list', function () {
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICE_LISTS, app.FooterTab.PRICE_LISTS_SMALL, 1)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICE_LISTS)
        });
        _common.create_newRecord(cnt.uuid.PLANT_PRICE_LISTS)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//  Added this wait as script is getting failed as loader is taking time to load
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, commonLocators.CommonKeys.DAY_RENTAL_INTERNALLY)
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_1)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE_2)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Create plant component, maintenance and update plant status', function () {
        _common.openTab(app.TabBar.MAINTENANCE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
        });
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _logesticPage.click_On_CloseButton(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PLANT_DESCRIPTION)
        _common.waitForLoaderToDisappear()
        
        cy.wait(1000)//required wait to read data from cell
        _common.openTab(app.TabBar.MAINTENANCE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_COMPONENT, app.FooterTab.COMPONENTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_COMPONENT)
            cy.REFRESH_CONTAINER()
        });
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PLANT_COMPONENT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlantComponent(cnt.uuid.PLANT_COMPONENT,PLANT_COMPONENT_PARAMETERS)
        _common.waitForLoaderToDisappear() 
        cy.SAVE()
        _common.waitForLoaderToDisappear()        


         _common.openTab(app.TabBar.MAINTENANCE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MAINTENANCE, app.FooterTab.MAINTENANCE, 3)
            _common.clear_subContainerFilter(cnt.uuid.MAINTENANCE)
        });

        _common.clear_subContainerFilter(cnt.uuid.MAINTENANCE)
        _common.create_newRecord(cnt.uuid.MAINTENANCE)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreateMaintenance(cnt.uuid.MAINTENANCE,MAINTENANCE_PARAMETERS)
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
    })

    it('TC - "Resource" checkbox in plant container should not get checked if resource does not exists', function () {
        
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE)

        _validate.verify_checkBoxDisabled_forActiveCell(cnt.uuid.PLANT,app.GridCells.IS_RESOURCE)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.PLANT,app.GridCells.IS_RESOURCE,commonLocators.CommonKeys.UNCHECK)        
    })

    it('TC - Verify Resource should get generate', function () {
        
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_RESOURCE_FROM_PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreateResourceFromPlant(RESOURCE_PARAMETERS)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()

        _validate.verify_ModalMessage("Done Successfully")

        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()

        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.PLANT,app.GridCells.IS_RESOURCE,commonLocators.CommonKeys.CHECK)   
    })

    it('TC - "Resource" checkbox in plant container should get checked if resource exist', function () {
        
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.PLANT,app.GridCells.IS_RESOURCE,commonLocators.CommonKeys.CHECK)   
    })

    it('TC - The ETM_PLANT should display this with a checkbox that is read only true', function () {
        
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE)

        _validate.verify_checkBoxDisabled_forActiveCell(cnt.uuid.PLANT,app.GridCells.IS_RESOURCE)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.PLANT,app.GridCells.IS_RESOURCE,commonLocators.CommonKeys.CHECK)   
    })

})