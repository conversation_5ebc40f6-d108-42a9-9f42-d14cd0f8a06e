
import apiConstantData from "cypress/constantData/apiConstantData";
import { tile, app, cnt, btn, commonLocators, sidebar } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _projectPage, _bidPage, _procurementContractPage, _procurementPage, _saleContractPage, _estimatePage, _boqPage, _mainView, _modalView, _salesPage, _billPage, _package, _wicpage, _procurementConfig, _rfqPage, _validate, _controllingUnit, _materialPage, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";


let MODAL_PROJECTS
const CHANGE_PROJECT_DESC = "CHANGE_PRJ_DESC_" + Cypress._.random(0, 999);

let CONTAINERS_CONTROLLING_UNIT
let CONTRACT_PARAMETER: DataCells
let CONTAINER_COLUMNS_CONTRACT;
let CONTAINERS_CONTRACT;
let CONTAINER_COLUMNS_ITEM;
let CONTAINERS_ITEM, CONTAINERS_CATALOG, CONTAINER_COLUMNS_MATERIAL_RECORD


describe("PCM- 4.119 | Price condition container in Requisition module", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });

    before(function () {
        cy.fixture("pcm/con-4.119-price-condition-container-in-contract-module.json")
            .then((data) => {
                this.data = data;
                CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
                CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
                CONTAINER_COLUMNS_ITEM = this.data.CONTAINER_COLUMNS.ITEM
                CONTAINERS_ITEM = this.data.CONTAINERS.ITEM;
                CONTAINERS_CATALOG = this.data.CONTAINERS.CATALOG;
                CONTAINER_COLUMNS_MATERIAL_RECORD = this.data.CONTAINER_COLUMNS.MATERIAL_RECORD

                CONTAINERS_CONTROLLING_UNIT = this.data.CONTAINERS.CONTROLLING_UNIT
                MODAL_PROJECTS = this.data.MODAL.PROJECTS

                CONTRACT_PARAMETER = {
                    [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                    [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESSPARTNER
                }
            })
            .then(() => {
                cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
                _common.openDesktopTile(tile.DesktopTiles.PROJECT);
                _common.waitForLoaderToDisappear()
                _common.openTab(app.TabBar.PROJECT).then(() => {
                    _common.setDefaultView(app.TabBar.PROJECT)
                    _common.waitForLoaderToDisappear()
                    _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
                });
                _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
                _commonAPI.getAccessToken().then((result) => {
                    cy.log(`Token Retrieved: ${result.token}`);
                });
            })
    });

    after(() => {
        cy.LOGOUT();
    });

    it("TC - API: Create project and controlling unit", function () {
        let CONTROLLING_UNIT_A_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNIT.QUANTITY, CONTAINERS_CONTROLLING_UNIT.QUANTITY],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
        }


        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _commonAPI.createProject()
            .then(() => {
                _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
                _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
                _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_A_PARAMETERS)
            });
    })

    it("TC - Verify value for price condition type in customizing", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.MASTERDATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES, 0);
        })
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        _common.search_inSubContainer(cnt.uuid.ENTITY_TYPES, CommonLocators.CommonKeys.PRICE_CONDITION_TYPE)
        cy.REFRESH_CONTAINER()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.ENTITY_TYPES, app.GridCells.NAME, CommonLocators.CommonKeys.PRICE_CONDITION_TYPE)
        _common.openTab(app.TabBar.MASTER_DATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.INSTANCES, app.FooterTab.DATA_RECORDS, 2);
        })
        cy.wait(1000)//required wait to load page
        _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasValue(cnt.uuid.DATA_RECORDS, app.GridCells.DESCRIPTION_INFO, commonLocators.CommonKeys.COPPER_SURCHARGE)
        _common.edit_containerCell(cnt.uuid.DATA_RECORDS, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEM.QUANTITY[0])
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.HAS_VALUE, commonLocators.CommonKeys.UNCHECK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()


        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PRICE_CONDITION);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 0);
        });

        _common.clickOn_cellHasUniqueValue(cnt.uuid.PRICE_CONDITION, app.GridCells.DESCRIPTION_INFO, commonLocators.CommonKeys.SURCHARGE)
        cy.wait(1000) //required wait to load page
        _common.openTab(app.TabBar.PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_DETAILS, app.FooterTab.PRICE_CONDITION_DETAILS, 0);
        });
        _common.select_rowInSubContainer(cnt.uuid.PRICE_CONDITION_DETAILS)
        _common.saveCellDataToEnv(cnt.uuid.PRICE_CONDITION_DETAILS, app.GridCells.PRICE_CONDITION_TYPE_FK, "PRICECONDITIONTYPE_CODE")

        _common.openTab(app.TabBar.PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 0);
        });

        _common.clickOn_cellHasUniqueValue(cnt.uuid.PRICE_CONDITION, app.GridCells.DESCRIPTION_INFO, commonLocators.CommonKeys.COPPER_SURCHARGE)
        cy.wait(1000) //required wait to load page

    });

    it('TC - Create new contract', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT);
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.waitForLoaderToDisappear()
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT);
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
        _common.waitForLoaderToDisappear()
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal_byClass(CONTRACT_PARAMETER)

        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_CNT_CODE_0'))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000) //wait needed to store value of Contract code
        _common.getText_fromCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE).then(($value) => {
            Cypress.env("CONTRACT_CODE", $value.text())
        })

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEM)

        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.priceextra], cnt.uuid.ITEMSCONTRACT)
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT);
        _common.waitForLoaderToDisappear()
        _saleContractPage.enterRecord_toCreateNewOrderItem(CONTAINERS_ITEM.MATERIAL_NO, CONTAINERS_ITEM.QUANTITY[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()


    });

    it("TC - Verify Create and delete button are working in price condition", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
        })
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.create_newRecord(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.COPPER_SURCHARGE)
        cy.wait(1000)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
        })
        _common.select_rowHasValue(cnt.uuid.CONTRACT_PRICE_CONDITION, commonLocators.CommonKeys.COPPER_SURCHARGE)
        _common.delete_recordFromContainer(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.YES)
        cy.wait(1000)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordDeleted(cnt.uuid.CONTRACT_PRICE_CONDITION, commonLocators.CommonKeys.COPPER_SURCHARGE)

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
        })
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.create_newRecord(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.COPPER_SURCHARGE)
        cy.wait(1000)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEM)

        });
        _common.select_rowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.saveCellDataToEnv(cnt.uuid.ITEMSCONTRACT, app.GridCells.PRICE_EXTRA, "PRICE_EXTRA")
    })

    it("TC - Verify type lookup is correct ,after select type, the value will copy from type, total and total oc will recalculate according formula", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
        })
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.clickOn_cellHasValue(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.COPPER_SURCHARGE)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.CONTRACT_PRICE_CONDITION, Cypress.env("PRICE_EXTRA"), CONTAINERS_ITEM.QUANTITY[0], app.GridCells.CONDITION_TOTAL)
        _validate.verify_isRecordMultiplyTwoValuesInRow(cnt.uuid.CONTRACT_PRICE_CONDITION, Cypress.env("PRICE_EXTRA"), CONTAINERS_ITEM.QUANTITY[0], app.GridCells.TOTAL_OC)


    })

    it("TC - Verify select price condition in items container, it should auto generate records to price condition container", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEM)
            _common.set_columnAtTop([CONTAINER_COLUMNS_ITEM.prcpriceconditionfk],cnt.uuid.ITEMSCONTRACT)
            _common.waitForLoaderToDisappear()
        });
        _common.maximizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.search_inSubContainer(cnt.uuid.ITEMSCONTRACT, CONTAINERS_ITEM.MATERIAL_NO)
        _common.select_rowInContainer(cnt.uuid.ITEMSCONTRACT)

        _common.edit_dropdownCellWithCaret(cnt.uuid.ITEMSCONTRACT, app.GridCells.PRC_PRICE_CONDITION_FK, commonLocators.CommonKeys.LIST, commonLocators.CommonKeys.SURCHARGE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMSCONTRACT)

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 2)
        })
        _common.select_rowInSubContainer(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.CODE, Cypress.env("PRICECONDITIONTYPE_CODE"))
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.CONDITION_TOTAL, "Total")
    })

    it("TC - Verify if type hasvalue is false then Value is read only and blank", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION)
        })
        _common.create_newRecord(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.COPPER_SURCHARGE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordNotEditable(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.VALUE, 1)

    })

    it("TC - Verify If is pricecomponent=false or islive=false, this record's total should not add to price extra of items", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION)
        })
        _common.maximizeContainer(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.clickOn_cellHasValue(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.COPPER_SURCHARGE)

        _common.set_cellCheckboxValue(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.IS_ACTIVATED, commonLocators.CommonKeys.UNCHECK)
        cy.wait(1000)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEM)

        });
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.select_rowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.clickOn_activeRowCell(cnt.uuid.ITEMSCONTRACT, app.GridCells.PRICE_EXTRA)
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMSCONTRACT, app.GridCells.PRICE_EXTRA, Cypress.env("Total"))

    })


    it("TC - Set value to material in items container, it will copy price condition from material", function () {

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.MATERIAL_CATALOG);
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
	    
        
    

        _common.openTab(app.TabBar.CATALOGS).then(() => {
            _common.setDefaultView(app.TabBar.CATALOGS)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_CATALOGS, app.FooterTab.MATERIALCATALOG, 0)
        })
        _common.waitForLoaderToDisappear()
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH);
		_common.clear_searchInSidebar();
		_common.waitForLoaderToDisappear();

        _common.maximizeContainer(cnt.uuid.MATERIAL_CATALOGS)
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_CATALOGS)
        _common.search_inSubContainer(cnt.uuid.MATERIAL_CATALOGS, CONTAINERS_CATALOG.DESC)
        _common.select_rowInContainer(cnt.uuid.MATERIAL_CATALOGS)
        _common.edit_containerCell(cnt.uuid.MATERIAL_CATALOGS, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT, " ")

        _common.edit_containerCell(cnt.uuid.MATERIAL_CATALOGS, app.GridCells.VALID_TO, app.InputFields.INPUT_GROUP_CONTENT, " ")
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.MATERIAL);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_CATALOG_FILTER, app.FooterTab.MATERIALFILTER, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_CATALOG_FILTER)
        _common.maximizeContainer(cnt.uuid.MATERIAL_CATALOG_FILTER)
        _common.search_inSubContainer(cnt.uuid.MATERIAL_CATALOG_FILTER, CONTAINERS_CATALOG.DESC)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.MATERIAL_CATALOG_FILTER, app.GridCells.DESCRIPTION_INFO, CONTAINERS_CATALOG.DESC)
        _common.clickOn_activeRowCell(cnt.uuid.MATERIAL_CATALOG_FILTER, app.GridCells.IS_CHECKED)
        _common.minimizeContainer(cnt.uuid.MATERIAL_CATALOG_FILTER)

        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_RECORDS, app.FooterTab.MATERIAL_RECORDS, 1);
            _common.setup_gridLayout(cnt.uuid.MATERIAL_RECORDS, CONTAINER_COLUMNS_MATERIAL_RECORD)
        });

        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_RECORDS)
        _common.search_inSubContainer(cnt.uuid.MATERIAL_RECORDS, CONTAINERS_ITEM.MATERIAL_NO1)
        _common.select_rowInContainer(cnt.uuid.MATERIAL_RECORDS)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.MATERIAL_RECORDS, app.GridCells.PRC_PRICE_CONDITION_FK, "MATERIAL_PRICE_CONDITION")



        _common.openTab(app.TabBar.RECORDS).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.MATERIAL_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION)
        })
        _common.maximizeContainer(cnt.uuid.MATERIAL_PRICE_CONDITION)
        _common.clear_subContainerFilter(cnt.uuid.MATERIAL_PRICE_CONDITION)
        _common.create_newRecordInContainer_ifNoRecordExists(cnt.uuid.MATERIAL_PRICE_CONDITION, 0)
        _common.edit_dropdownCellWithInput(cnt.uuid.MATERIAL_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonKeys.COPPER_SURCHARGE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.MATERIAL_PRICE_CONDITION)
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEM)

        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))

        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT);
        _common.waitForLoaderToDisappear()
        _saleContractPage.enterRecord_toCreateNewOrderItem(CONTAINERS_ITEM.MATERIAL_NO1, CONTAINERS_ITEM.QUANTITY[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })

    it("TC - Verify if selected material is using price list, then should copy from material price condition", function () {

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 0);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEM)

        });
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.search_inSubContainer(cnt.uuid.ITEMSCONTRACT, CONTAINERS_ITEM.MATERIAL_NO1)
        _common.select_rowInSubContainer(cnt.uuid.ITEMSCONTRACT);
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.ITEMSCONTRACT, app.GridCells.PRC_PRICE_CONDITION_FK, Cypress.env("MATERIAL_PRICE_CONDITION"))
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_PRICE_CONDITION)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_PRICE_CONDITION, app.GridCells.PRC_PRICE_CONDITION_TYPE_FK, commonLocators.CommonKeys.COPPER_SURCHARGE)
    })


    it("TC - Verify Price condition will pass from contract to requisition module by wizard", function () {

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
        })
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CLERK_PRC_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEM.CLERK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(CommonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CONTRACT_TERMINATION);
        _common.waitForLoaderToDisappear()

        _saleContractPage.contractTermination_FromWizard("New Requisition", 0, Cypress.env('API_PROJECT_NUMBER_1'), CHANGE_PROJECT_DESC,
            CommonLocators.CommonKeys.DESIGN_CHANGE, CommonLocators.CommonKeys.CHANGE_REQUEST)
        _common.waitForLoaderToDisappear()
        cy.wait(2000) //required wait to load page
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_REQUISITION)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.MAIN).then(() => {
            _common.select_tabFromFooter(cnt.uuid.REQUISITIONS, app.FooterTab.REQUISITIONS, 2);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))

        _common.openTab(app.TabBar.MAIN).then(() => {
            _common.select_tabFromFooter(cnt.uuid.REQUISITIONITEMS, app.FooterTab.ITEMS, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.REQUISITIONITEMS);
        _common.select_rowInContainer(cnt.uuid.REQUISITIONITEMS)

        _common.openTab(app.TabBar.MAIN).then(() => {
            _common.select_tabFromFooter(cnt.uuid.REQUISITION_PRICE_CONDITION, app.FooterTab.PRICE_CONDITION, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.REQUISITIONITEMS);
        _common.select_rowHasValue(cnt.uuid.REQUISITIONITEMS,commonLocators.CommonKeys.COPPER_SURCHARGE)
        _validate.verify_isRecordPresent(cnt.uuid.REQUISITION_PRICE_CONDITION, commonLocators.CommonKeys.COPPER_SURCHARGE)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.REQUISITIONITEMS,commonLocators.CommonKeys.SURCHARGE)
        _validate.verify_isRecordPresent(cnt.uuid.REQUISITION_PRICE_CONDITION, commonLocators.CommonKeys.SURCHARGE)
    })
});