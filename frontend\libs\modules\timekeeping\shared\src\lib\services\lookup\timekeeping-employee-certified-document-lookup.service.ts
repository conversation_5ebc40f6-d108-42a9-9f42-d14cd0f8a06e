/*
 * Copyright(c) RIB Software GmbH
 */

import { Injectable } from '@angular/core';
import { FieldType, ILookupConfig, UiCommonLookupEndpointDataService } from '@libs/ui/common';
import { ICertifiedEmployeeEntity } from '@libs/timekeeping/interfaces';

/**
 * Lookup service for employee certified documents
 */
@Injectable({
	providedIn: 'root'
})
export class TimekeepingEmployeeCertifiedDocumentLookupService<TEntity extends object> extends UiCommonLookupEndpointDataService<ICertifiedEmployeeEntity, TEntity> {
	public constructor() {
		const endpoint = { httpRead: { route: 'timekeeping/certificate/document/', endPointRead: 'lookup' } };
		const config: ILookupConfig<ICertifiedEmployeeEntity> = {
			uuid: '3b925a3e95a44a83bbf80e1f4bfc91f5',
			valueMember: 'Id',
			displayMember: 'Description',
			gridConfig: {
				columns: [
					{
						id: 'Description',
						model: 'Description',
						type: FieldType.Description,
						label: { text: 'Description' },
						sortable: true,
						visible: true,
						readonly: true,
						width: 200
					}
				]
			}
		};

		super(endpoint, config);
	}
}
