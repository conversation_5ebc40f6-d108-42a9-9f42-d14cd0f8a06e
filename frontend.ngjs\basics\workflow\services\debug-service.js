(function (angular) {
	/* global globals, Platform */
	'use strict';

	function DebugService(
		_,
		$http,
		$q,
		basicsWorkflowTemplateService,
		basicsWorkflowClientActionService,
		platformModuleStateService,
		basicsWorkflowInstanceService,
		platformModalService,
		platformDialogService,
		basicsWorkflowDtoService,
		platformObjectHelper,
		workflowDesignerBreakpointService,
		$translate
	) {
		var state = platformModuleStateService.state('basics.workflow');
		var self = this;
		var actionEvent = new Platform.Messenger();

		self.startDebugCurrent = function startDebugCurrent() {
			return self.startDebug(state.selectedMainEntity, state.selectedTemplateVersion, state.selectedMainEntity.EntityId !== '0');
		};

		self.startDebug = function startDebug(template, version, hasCallingEntity) {
			function start(identification) {
				return $http({
					method: 'POST',
					url: globals.webApiBaseUrl + 'basics/workflow/instance/debug/createcontext',
					data: {
						TemplateId: template.Id,
						VersionId: version.Id,
						Identification: identification,
						JsonContext: '',
					},
				}).then(function (response) {
					var context = angular.fromJson(response.data);
					_.each(version.context, function (item) {
						context[item.key] = item.value;
					});

					return self.nextAction(version, version.WorkflowAction, context);
				});
			}

			if (hasCallingEntity) {
				state.debugInfo = {
					template: template,
					version: version,
					identification: {},
				};
				return platformDialogService
					.showDialog({
						bodyTemplateUrl: globals.appBaseUrl + 'basics.workflow/templates/dialogs/workflow-start-debug-dialog.html',
						headerTextKey: 'basics.workflow.start.idDialog.header',
						backdrop: false,
						width: '200px',
						height: '250px',
						buttons: [
							{
								id: 'cancel',
							},
							{
								id: 'ok',
							},
						],
					})
					.then(
						function (result) {
							if (result.ok) {
								return start(state.debugInfo.identification[0]);
							}
						},
						function () {
							state.debugCanceled = true;
							return $q.reject();
						}
					);
			} else {
				return start(null);
			}
		};

		self.nextAction = function nextAction(templateVersion, currentAction, context) {
			currentAction.context = context;
			var nxAction;
			var openActions = [];
			var clientAction = basicsWorkflowClientActionService.getAction(currentAction.actionId);
			var currentTask = basicsWorkflowTemplateService.copyAction(currentAction);
			let executePromise = currentTask.executeCondition ? executeConditionEvaluation(currentTask.executeCondition, context) : $q.when(true);
			return executePromise.then(function (executeResult) {
				let evaluatedExecuteCondition = executeResult === 'true' || executeResult === '' ? true : executeResult === 'false' ? false : executeResult;
				if (currentAction.actionId === 'E0000000000000000000000000000000' && executeResult) {
					nxAction = findInTree(templateVersion.WorkflowAction, currentAction.input[0].value);
					return $q.when({
						context: context,
						action: nxAction,
						openActions: openActions,
					});
				} else {
					if (clientAction) {
						if (evaluatedExecuteCondition) {
							return $http({
								method: 'POST',
								url: globals.webApiBaseUrl + 'basics/workflow/actions/codeTranslations',
								data: {
									input: currentTask.input[0].value,
									language: '',
								},
							}).then(function (response) {
								currentTask.input[0].value = response.data.output;
								basicsWorkflowDtoService.extendObject(currentTask);
								currentTask.Context = context;
								currentTask.IsContextLoaded = true;
								basicsWorkflowInstanceService.prepareTask(currentTask);
								return basicsWorkflowClientActionService.executeTask(currentTask, context).then(function (response) {
									if (response && response.data && response.data.context.Context) {
										response.data.context = _.merge(response.data.context, response.data.context.Context);
										delete response.data.context.Context;
									}
									if (currentAction.context.Context) {
										delete currentAction.context.Context;
									}
									return executeActionResponseFactory(currentAction)(response);
								});
							});
						} else {
							var actionContext = currentAction.context;
							if (currentAction.context.SkippedActions) {
								currentAction.context.SkippedActions.push(`Action ${currentAction.Description} skipped with execute Condition ${currentAction.ExecuteCondition}`);
							} else {
								currentAction.context.SkippedActions = [`Action ${currentAction.Description} skipped with execute Condition ${currentAction.ExecuteCondition}`];
							}
							return executeActionResponseFactory(currentAction)({
								data: {
									task: currentAction,
									context: actionContext,
									result: currentAction.Result || 'true',
								},
							});
						}
					} else {
						return $http({
							method: 'POST',
							url: globals.webApiBaseUrl + 'basics/workflow/actions/debug',
							data: {
								wfAction: angular.toJson(basicsWorkflowTemplateService.copyAction(currentAction, false)),
								context: angular.toJson(context),
							},
						}).then(executeActionResponseFactory(currentAction));
					}
				}
			});
		};

		function executeConditionEvaluation(expression, context) {
			return $http({
				method: 'POST',
				url: globals.webApiBaseUrl + 'basics/workflow/actions/executeCondition',
				data: {
					executeCondition: expression,
					context: angular.toJson(context),
				},
			}).then(function (response) {
				return response.data.result.toLowerCase() === 'true';
			});
		}

		self.nextActionFromCurrent = function nextActionFromCurrent() {
			return self.nextAction(state.selectedTemplateVersion, state.currentWorkflowAction, state.debugContext);
		};

		self.registerActionEvent = function (callback) {
			actionEvent.register(callback);
		};

		self.unregisterActionEvent = function (callback) {
			actionEvent.unregister(callback);
		};

		function executeActionResponseFactory(currentAction) {
			return function (response) {
				var context = currentAction.context;
				var result = currentAction.result ? currentAction.result : currentAction.context.Result;
				var nextAction = currentAction;
				if (platformObjectHelper.isSet(response)) {
					if (angular.isObject(response)) {
						if (response.actionEvent) {
							switch (response.actionEvent) {
								case basicsWorkflowClientActionService.actionEvent.stop:
									actionEvent.fire();
									return;
							}
						}

						context = angular.fromJson(response.data.context);
						result = platformObjectHelper.isSet(response.data.result) ? response.data.result : null;
					}
					if (currentAction.transitions.length <= 1) {
						nextAction = currentAction.transitions[0].workflowAction;
					} else {
						var currentTrans = _.find(currentAction.transitions, function (item) {
							return item.parameter.toUpperCase() === result.toString().toUpperCase();
						});
						if (currentTrans) {
							nextAction = currentTrans.workflowAction;
						} else {
							nextAction = currentAction.transitions[0].workflowAction;
						}
					}
				}

				basicsWorkflowDtoService.extendObject(nextAction);
				setTimeout(function () {
					let formatedContext = jQuery('#formatedContext');
					formatedContext.scrollTop(formatedContext.prop('scrollHeight'));
				}, 0);
				return {
					context: context,
					action: nextAction,
				};
			};
		}

		function findInTree(action, code) {
			var actionCode = action.code;
			var result = null;
			if (actionCode === code) {
				return action;
			}
			if (!action.transitions) {
				return null;
			}
			for (var i = 0; _.isNil(result) && i < action.transitions.length; i++) {
				result = findInTree(action.transitions[i].workflowAction, code);
			}

			return result;
		}

		self.addBreakpoint = function addBreakpoint() {
			const version = state.selectedTemplateVersion;
			const currentAction = state.currentWorkflowAction;
			const breakpoints = workflowDesignerBreakpointService.getBreakpoints(version.Id);
			const breakpointIds = new Set(breakpoints.map((bp) => bp.workflowAction.id));
			if (!(state.visitedBreakpoints instanceof Set)) {
				state.visitedBreakpoints = new Set();
			}
			const initialContext = state.debugContext;
			const visitedBreakpoints = state.visitedBreakpoints;
			let newAction = currentAction;

			function traverseUntilBreakpoint(action, context) {
				if (!action) {
					return $q.when({ action: null, context: null });
				}

				// If current action has a breakpoint or its an end action, terminate the traversal.
				if (
					(breakpointIds.has(action.id) && !visitedBreakpoints.has(action.id)) || action.actionTypeId === 2
				) {
					// visitedBreakpoints :- keeping the track of visited breakpoints to ensure we do not stop at the same breakpoint again.
					if (hasNextBreakpoint(action, visitedBreakpoints, breakpointIds)) {
						visitedBreakpoints.add(action.id);

					}
					// If the action has a breakpoint, return it along with the context.
					return $q.when({
						action: action,
						context: context,
					});
				}
				// in case of an exception in context, keep the track of action that is causing it.
				newAction = action;
				// Continue traversal
				return self.nextAction(version, action, context).then(function (response) {
					//if the response contains exception in context, terminate the loop.
					if (response.context && typeof response.context === 'object' && 'Exception' in response.context) {
						const exception = response.context.Exception ? response.context.Exception : '';
						const exceptionMessage = exception.Message ? exception.Message : '';

						const title = $translate.instant('basics.workflow.debug.debugBreakpoint.title');
						const detailedMessage = $translate.instant('basics.workflow.debug.debugBreakpoint.detailedErrorMessage', {
							actionDescription: newAction.Description || '',
							exceptionMessage: exceptionMessage || '[No error message provided]',
						});
						platformModalService.showErrorBox(detailedMessage, title);
						return $q.when({
							action: response.action,
							context: response.context,
							disableContinue: true,
						});
					}
					return traverseUntilBreakpoint(response.action, response.context);
				});
			}

			return traverseUntilBreakpoint(currentAction, initialContext);
		};

		/**
		 * Check if there is a next breakpoint in the workflow action tree.
		 * If we want to continue from one breakpoint to another, we need to check if there is a next breakpoint
		 * in the workflow action tree, starting from the current action.
		 * @param {*} action 
		 * @param {*} visitedSet 
		 * @param {*} breakpointIds 
		 * @returns {boolean}
		 */
		function hasNextBreakpoint(action, visitedSet, breakpointIds) {
			if (!action || !Array.isArray(action.transitions)) {
				return false;
			}
			for (const transition of action.transitions) {
				const next = transition.workflowAction;
				if (!next) continue;

				if (breakpointIds.has(next.id) && !visitedSet.has(next.id)) {
					return true;
				}

				if (hasNextBreakpoint(next, visitedSet, breakpointIds)) {
					return true;
				}
			}
			return false;
		}


	}

	DebugService.$inject = [
		'_',
		'$http',
		'$q',
		'basicsWorkflowTemplateService',
		'basicsWorkflowClientActionService',
		'platformModuleStateService',
		'basicsWorkflowInstanceService',
		'platformModalService',
		'platformDialogService',
		'basicsWorkflowDtoService',
		'platformObjectHelper',
		'workflowDesignerBreakpointService',
		'$translate',
	];
	angular.module('basics.workflow').service('basicsWorkflowDebugService', DebugService);
})(angular);
