/*
 * Copyright(c) RIB Software GmbH
 */

/**
 * Represents the base procurement main context, which includes project and tax-related information.
 * This interface defines the fundamental context required for procurement operations.
 */
export interface IProcurementMainContext {
	/**
	 * Indicates whether the procurement header and its sub-containers are in a readonly state.
	 * When true, prevents modifications to the header and related entities.
	 */
	readonly: boolean;

	/**
	 * Foreign key reference to the project associated with this procurement header.
	 */
	projectFk: number;

	/**
	 * Foreign key reference to the tax code used for tax calculations.
	 * Optional field that may not be set for all procurement headers.
	 */
	taxCodeFk?: number;

	/**
	 * Foreign key reference to the controlling unit responsible for this procurement.
	 * Can be null if no specific controlling unit is assigned.
	 */
	controllingUnitFk?: number | null;

	/**
	 * Foreign key reference to the currency used for this procurement.
	 * Required for all monetary calculations and conversions.
	 */
	currencyFk: number;

	/**
	 * Exchange rate for currency conversion calculations.
	 * Used to convert between different currencies in procurement operations.
	 */
	exchangeRate: number;

	/**
	 * Foreign key reference to the VAT (Value Added Tax) group.
	 * Used for tax calculations and compliance with regional tax requirements.
	 */
	vatGroupFk?: number | null;

	/**
	 * Foreign key reference to the organizational structure.
	 * Can be null if no specific structure is assigned to this procurement.
	 */
	structureFk?: number;

	/**
	 * Foreign key reference to the procurement clerk responsible for processing.
	 * Identifies the person handling the procurement operations.
	 */
	clerkPrcFk?: number;

	/**
	 * Foreign key reference to the request clerk who initiated the procurement.
	 * Identifies the person who created or requested the procurement.
	 */
	clerkReqFk?: number;

	/**
	 * Foreign key reference to the business partner (vendor/supplier).
	 * Identifies the external party involved in the procurement transaction.
	 */
	businessPartnerFk?: number;

	/**
	 * Foreign key reference to the procurement configuration.
	 * Determines the behavior and validation rules for this procurement.
	 */
	prcConfigFk?: number;

	/**
	 * Foreign key reference to the package associated with this procurement.
	 * Can be null if the procurement is not part of a specific package.
	 */
	packageFk?: number | null;
}

/**
 * Extends the procurement main context with additional fields specific to BOQ (Bill of Quantities).
 * This interface is used when dealing with BOQ-related procurement operations.
 */
export interface IProcurementBoqHeaderContext extends IProcurementMainContext {
	/**
	 * Foreign key reference to the framework WIC (Work Item Category) group.
	 * Used for categorizing work items in BOQ operations. Can be null if not applicable.
	 */
	boqWicCatFk?: number | null;

	/**
	 * Foreign key reference to the framework WIC BOQ entity.
	 * Links to specific BOQ items within the WIC framework. Can be null if not set.
	 */
	boqWicCatBoqFk?: number | null;
}

/**
 * Extends the procurement main context with additional fields specific to PRC_Header entity.
 * This interface is used when dealing with PRC_Header-related procurement operations.
 */
export interface IPrcHeaderContext extends IProcurementBoqHeaderContext {
	/**
	 * Foreign key reference to the procurement header entity.
	 * Required field that uniquely identifies the procurement header.
	 */
	prcHeaderFk: number;

	/**
	 * Foreign key reference to the material catalog used for item selection.
	 * Optional field for procurement headers that utilize material catalogs.
	 */
	materialCatalogFk?: number;

	/**
	 * Foreign key reference to the financial payment terms.
	 * Defines the payment schedule and conditions for financial transactions.
	 */
	paymentTermFiFk?: number;

	/**
	 * Foreign key reference to the procurement-specific payment terms.
	 * May differ from financial payment terms for procurement-specific conditions.
	 */
	paymentTermPaFk?: number;

	/**
	 * Foreign key reference to the Incoterm (International Commercial Terms).
	 * Defines the responsibilities and risks in international trade transactions.
	 */
	incotermFk?: number;

	/**
	 * Date when the procurement order was placed.
	 * Can be either a Date object or a string representation of the date.
	 */
	dateOrdered?: Date | string;

	/**
	 * Foreign key reference to the contract governing this procurement.
	 * Can be null if the procurement is not bound by a specific contract.
	 */
	contractFk?: number | null;
}
