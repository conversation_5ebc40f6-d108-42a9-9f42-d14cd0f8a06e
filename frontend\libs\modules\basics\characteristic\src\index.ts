import { IApplicationModuleInfo } from '@libs/platform/common';
import { BasicsCharacteristicModuleInfo } from './lib/model/basics-characteristic-module-info.class';

export * from './lib/basics-characteristic.module';
export * from './lib/services/basics-characteristic-discrete-value-data-provider.service';

export function getModuleInfo(): IApplicationModuleInfo {
	return BasicsCharacteristicModuleInfo.instance;
}
