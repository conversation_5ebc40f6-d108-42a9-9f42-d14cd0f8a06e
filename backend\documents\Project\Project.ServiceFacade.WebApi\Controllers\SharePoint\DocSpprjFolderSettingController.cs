using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Documents.Project.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Platform.ServiceFacade.WebApi;

namespace RIB.Visual.Documents.Project.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	[RoutePrefix("documents/centralquery/sharepoint/foldersetting")]
	public class DocSpprjFolderSettingController : ApiControllerBase<DocSpprjFolderSettingLogic>
	{
		/// <summary>
		/// 
		/// </summary>
		public DocSpprjFolderSettingController()
		{
		}

		/// <summary>
		/// Get folder setting info: folder structures and project meta data
		/// </summary>
		/// <param name="folderSettingId"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		[Route("get"), HttpGet]
		public SharePointFolderSettingInfoDto GetFolderSettingInfo(int folderSettingId, int projectId)
		{
			var folderStrucLogic = new DocSpprjFolderStructLogic();

			var folderSetting = this.Logic.GetById(new RIB.Visual.Platform.Core.IdentificationData() { Id = folderSettingId });
			var folderStructures = folderStrucLogic.GetByFilter(e => e.PrjDocSpprjFolderSettingFk == folderSettingId).OrderBy(e => e.Level).ToList();
			var projectMetaData = new DocSPProjectMetaDataLogic().GetByFilter(e => e.PrjDocSpprjFolderSettingFk == folderSettingId).FirstOrDefault();
			var projectConfig = new DocSpProjectConfigLogic().GetByFilter(e => e.PrjProjectFk == projectId).FirstOrDefault();
			DocSpprjFolderStructLogic.SetUsersAssigned(folderStructures);

			return new SharePointFolderSettingInfoDto()
			{
				FolderSetting = folderSetting != null ? new DocSpprjFolderSettingDto(folderSetting) : null,
				FolderStructures = folderStructures.ToDtos(e => new DocSpprjFolderStructDto(e)),
				MetaData = projectMetaData != null ? new DocSpprjMetaDataDto(projectMetaData) : null,
				IsProjectSync = projectConfig != null && projectConfig.IsProjectSynced
			};
		}

		/// <summary>
		/// Save folder setting info: folder setting, folder structures and project meta data. May need to update project config if necessary
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[Route("save"), HttpPost]
		public DocSpprjFolderSettingDto SaveFolderSetting(SaveSharePointFolderSettingParamDto dto)
		{
			ArgumentNullException.ThrowIfNull(dto);

			var param = dto.Copy();
			var setting = this.Logic.SaveSetting(param);
			if (setting != null)
			{
				return new DocSpprjFolderSettingDto(setting);
			}
			return null;
		}

		/// <summary>
		/// Save folder setting info: folder setting, folder structures and project meta data to projects selected not sync. Also need to update project configs
		/// </summary>
		/// <param name="dto"></param>
		/// <returns></returns>
		[Route("savetoprojectsselected"), HttpPost]
		public DocSpprjFolderSettingDto SaveFolderSettingToProjectsSelected(SaveSharePointFolderSettingParamDto dto)
		{
			ArgumentNullException.ThrowIfNull(dto);

			var param = dto.Copy();
			var setting = this.Logic.SaveSettingToProjectsSelected(param);
			if (setting != null && setting.Id > 0)
			{
				return new DocSpprjFolderSettingDto(setting);
			}
			return null;
		}

		/// <summary>
		/// Save folder setting info as template: folder setting, folder structures and project meta data.
		/// </summary>
		/// <param name="compeleteDto"></param>
		/// <returns></returns>
		[Route("saveastemplate"), HttpPost]
		public DocSpprjFolderSettingDto SaveAsTemplate(SharePointFolderSettingCompleteDto compeleteDto)
		{
			ArgumentNullException.ThrowIfNull(compeleteDto);

			var compelete = compeleteDto.Copy();
			var entity = this.Logic.SaveAsTemplate(compelete);
			if (entity != null)
			{
				return new DocSpprjFolderSettingDto(entity);
			}
			return null;
		}
	}
}
