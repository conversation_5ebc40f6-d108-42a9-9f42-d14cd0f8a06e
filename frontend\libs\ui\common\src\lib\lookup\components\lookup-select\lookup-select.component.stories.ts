import { <PERSON><PERSON>, StoryObj, moduleMetadata } from '@storybook/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { UiCommonLookupSelectComponent } from './lookup-select.component';
import { UiCommonPopupComponent } from '../../../popup/components/popup/popup.component';
import { UiCommonPopupContainerComponent } from '../../../popup/components/popup-container/popup-container.component';
import { Directive, Input, Pipe, PipeTransform } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { PlatformConfigurationService, PlatformTranslateService } from '@libs/platform/common';
import { PopupResizeDirection } from '../../../popup';

// Stub for platformTranslate pipe
@Pipe({
   name: 'platformTranslate'
})
class PlatformTranslatePipeStub implements PipeTransform {
  public transform(value: string | { text: string } | undefined): string {
    if (typeof value === 'string') {
      return value;
    }
    if (value && typeof value === 'object' && 'text' in value) {
      return value.text;
    }
    return '';
  }
}

@Directive({
  selector: '[uiCommonPopupResizable]'
})
class UiCommonPopupResizableStubDirective {
  @Input('uiCommonPopupResizable')
  public directions: PopupResizeDirection[] = [];
}

// Mock data for the lookup select
interface Item {
  id: number;
  name: { text: string };
}

const mockItems: Item[] = [
  { id: 1, name: { text: 'Contact Form Data' } },
  { id: 2, name: { text: 'Contact Clerk' } },
  { id: 3, name: { text: 'External Roles' } },
  { id: 4, name: { text: 'Characteristics' } },
  { id: 5, name: { text: 'Contact Detail' } },
];

export default {
  title: 'UI Common/Lookup/LookupSelect',
  component: UiCommonLookupSelectComponent,
  decorators: [
    moduleMetadata({
      imports: [CommonModule, FormsModule,HttpClientModule],
      declarations: [
        UiCommonLookupSelectComponent,
        UiCommonPopupComponent,
        UiCommonPopupContainerComponent,
        UiCommonPopupResizableStubDirective,
        PlatformTranslatePipeStub,
      ],
      providers:[
        PlatformConfigurationService,
        PlatformTranslateService
      ]
    }),
  ],
  render: (args) => ({
    template: `
      <ui-common-popup-container></ui-common-popup-container>
      <ui-common-lookup-select
        [valueMember]="valueMember"
        [displayMember]="displayMember"
        [value]="value"
        [dataItems]="dataItems"
      ></ui-common-lookup-select>
    `,
    props: {
      ...args,
    },
  }),
} as Meta<UiCommonLookupSelectComponent<Item, number>>;

type Story = StoryObj<UiCommonLookupSelectComponent<Item, number>>;

export const Default: Story = {
  args: {
    valueMember: 'id',
    displayMember: 'name',
    value: undefined,
    dataItems: mockItems,
  },
};

export const WithSelectedValue: Story = {
  args: {
    valueMember: 'id',
    displayMember: 'name',
    value: 2,
    dataItems: mockItems,
  },
};