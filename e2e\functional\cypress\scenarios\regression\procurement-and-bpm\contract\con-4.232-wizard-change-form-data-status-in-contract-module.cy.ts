import { commonLocators, tile, app, cnt, sidebar, btn } from "cypress/locators";
import { _common, _commonAPI, _procurementContractPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const CONTRACT_DESC = _common.generateRandomString(5);
const USER_FORM_DESC = _common.generateRandomString(5);
const REMARK = _common.generateRandomString(6);
const FORM_DATA_DESC = _common.generateRandomString(6);
const FORM_DATA_DESC_1 = _common.generateRandomString(6)

let CONTAINERS_FORM_LIST, CONTAINER_COLUMNS_FORM_LIST;
let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let CONTAINERS_CONTRACT
let CONTAINER_COLUMNS_CONTRACT
let CONTAINER_COLUMNS_FORM_DATA

describe('PCM- 4.232 | Wizard change form data status in Contract module', () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture('procurement-and-bpm/con-4.232-wizard-change-form-data-status-in-contract-module.json').then((data) => {
            this.data = data;
            CONTAINERS_FORM_LIST = this.data.CONTAINERS.FORM_LIST
            CONTAINER_COLUMNS_FORM_LIST = this.data.CONTAINER_COLUMNS.FORM_LIST
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT

            CONTAINER_COLUMNS_FORM_DATA = this.data.CONTAINER_COLUMNS.FORM_DATA;
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIG,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BP[0]
            }


        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            cy.WaitUntilLoaderComplete_Trial();
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT);
                cy.WaitUntilLoaderComplete_Trial();
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });
    it("TC - Create new record in user form", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.USER_FORMS)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.FORMS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.FORM_LIST, app.FooterTab.FORM_LIST, 0);
            _common.setup_gridLayout(cnt.uuid.FORM_LIST, CONTAINER_COLUMNS_FORM_LIST)
        });
        _common.clear_subContainerFilter(cnt.uuid.FORM_LIST)
        _common.clickOn_toolbarButton(cnt.uuid.FORM_LIST, btn.ToolBar.ICO_REC_NEW)
        _common.edit_dropdownCellWithInput(cnt.uuid.FORM_LIST, app.GridCells.RUBRIC_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, sidebar.SideBarOptions.CONTRACT)
        _common.edit_containerCell(cnt.uuid.FORM_LIST, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, USER_FORM_DESC)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });
    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT)
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        });
        cy.REFRESH_CONTAINER()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_DESC)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_SELECTED_ENTITIES()
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, CONTRACT_DESC)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, CONTAINERS_CONTRACT.CONTRACT_CODE)

    });
    it("TC - Create two record in form data", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_FORMDATA, app.FooterTab.FORM_DATA, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_FORMDATA, CONTAINER_COLUMNS_FORM_DATA)
        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_FORMDATA)
        _common.clickOn_toolbarButton(cnt.uuid.CONTRACT_FORMDATA, btn.ToolBar.ICO_REC_NEW)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_FORMDATA, app.GridCells.FORM_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, USER_FORM_DESC)
        _common.edit_containerCell(cnt.uuid.CONTRACT_FORMDATA, app.GridCells.DESCRIPTION_CAPS, app.InputFields.DOMAIN_TYPE_TRANSLATION, FORM_DATA_DESC)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_FORMDATA)
        _common.clickOn_toolbarButton(cnt.uuid.CONTRACT_FORMDATA, btn.ToolBar.ICO_REC_NEW)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_FORMDATA, app.GridCells.FORM_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, USER_FORM_DESC)
        _common.edit_containerCell(cnt.uuid.CONTRACT_FORMDATA, app.GridCells.DESCRIPTION_CAPS, app.InputFields.DOMAIN_TYPE_TRANSLATION, FORM_DATA_DESC_1)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });
    it("TC - Select first record and change form data status using wizard", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_FORMDATA, app.FooterTab.FORM_DATA, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_FORMDATA, CONTAINER_COLUMNS_FORM_DATA)
        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_FORMDATA)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC)
        _common.select_rowHasValue(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_FORMDATA_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.APPROVED, REMARK)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.CONTRACT_FORMDATA)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_FORMDATA_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.RECORDED)
        _common.waitForLoaderToDisappear()
    });
    it("TC - Select all record and change form data status using wizard at the same time", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_FORMDATA, app.FooterTab.FORM_DATA, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_FORMDATA, CONTAINER_COLUMNS_FORM_DATA)
        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_FORMDATA)
        _common.select_allContainerData(cnt.uuid.CONTRACT_FORMDATA)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_FORMDATA_STATUS)
        _common.changeStatus_ofMultipleRecord_fromModal(commonLocators.CommonKeys.APPROVED)
        _common.waitForLoaderToDisappear()
        _common.select_allContainerData(cnt.uuid.CONTRACT_FORMDATA)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_FORMDATA, app.GridCells.FORM_DATA_STATUS_FK, commonLocators.CommonKeys.APPROVED)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC_1)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_FORMDATA, app.GridCells.FORM_DATA_STATUS_FK, commonLocators.CommonKeys.APPROVED)
    });
    it("TC - Add message to history by wizard", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_FORMDATA, app.FooterTab.FORM_DATA, 2);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_FORMDATA, CONTAINER_COLUMNS_FORM_DATA)
        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_FORMDATA)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC)
        _common.select_rowHasValue(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_FORMDATA_STATUS)
        _common.clickOn_modalFooterButton(btn.ButtonText.HISTORY)
        _common.clickOn_cellHasValue_fromModal(app.GridCells.REMARK, REMARK)
        _common.assert_cellDataByContent_fromModal(app.GridCells.REMARK, REMARK)
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()
    });
    it("TC - Change status ui, check the status filter", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_FORMDATA, app.FooterTab.FORM_DATA, 2);

        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_FORMDATA)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC)
        _common.select_rowHasValue(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_FORMDATA_STATUS)
        _common.clickOn_checkboxByLabel_fromModal(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonElements.ONLY_SHOW_AVAILABLE_STATUS, 0)
        cy.wait(1000) // wait is required to validate the checkbox
        _validate.verify_recordNotPresentInmodal(commonLocators.CommonKeys.REJECTED)
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_FORMDATA)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC)
        _common.select_rowHasValue(cnt.uuid.CONTRACT_FORMDATA, FORM_DATA_DESC)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_FORMDATA_STATUS)
        _common.clickOn_checkboxByLabel_fromModal(commonLocators.CommonElements.TEXT_RIGHT, commonLocators.CommonElements.ONLY_SHOW_AVAILABLE_STATUS, 0)
        cy.wait(1000) // wait is required to enable Close button
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()
    });

})