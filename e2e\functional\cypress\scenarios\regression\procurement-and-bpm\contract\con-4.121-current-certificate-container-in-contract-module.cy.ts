
import {_saleContractPage, _common, _projectPage, _procurementContractPage,_procurementPage, _validate, _businessPartnerPage } from "cypress/pages";
import {btn, app, tile, cnt, sidebar, commonLocators } from "cypress/locators";
import Buttons from "cypress/locators/buttons";
import { DataCells } from "cypress/pages/interfaces";

const allure = Cypress.Allure.reporter.getInterface();
const USER_DEFINED_1 = "US_D_1-" + Cypress._.random(0, 9999);
const USER_DEFINED_2 = "US_D_2-" + Cypress._.random(0, 9999);


let CONTAINER_COLUMNS_CURRENT_CERTIFICATE;
let CONTAINER_COLUMNS_CONTRACTS
let CONTAINERS_CONTRACT
let PROCUREMENT_CONTRACT_PARAMETER:DataCells;
let CONTRACT_PARAMETER
let CREATE_BUSINESSPARTNER_PARAMETERS
let CONTAINERS_BUSINESS_PARTNER


allure.epic("PROCUREMENT AND BPM");
allure.feature("Contract");
allure.story("PCM- 4.121 | Current certificate container in Contract module");

describe("PCM- 4.121 | Current certificate container in Contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

   before(function () {
        cy.fixture("pcm/con-4.121-current-certificate-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINER_COLUMNS_CURRENT_CERTIFICATE =this.data.CONTAINER_COLUMNS.CURRENT_CERTIFICATES
            CONTAINER_COLUMNS_CONTRACTS = this.data.CONTAINER_COLUMNS.CONTRACTS
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINERS_BUSINESS_PARTNER = this.data.CONTAINERS.CERTIFICATES
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]:CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]:CONTAINERS_CONTRACT.BUSINESS_PARTNER
              }

            CONTRACT_PARAMETER = {
             
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_BUSINESS_PARTNER.ANTON_WALISER

            }
            CREATE_BUSINESSPARTNER_PARAMETERS={
                [commonLocators.CommonLabels.BUSINESS_PARTNER]:CONTAINERS_BUSINESS_PARTNER.ANTON_WALISER
              };
            
        });
        cy.preLoading(
            Cypress.env("adminUserName"),
            Cypress.env("adminPassword"),           
            Cypress.env("parentCompanyName"), 
            Cypress.env("childCompanyName"));

            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
		    _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('PROJECT_NUMBER')).pinnedItem();
   });

   after(() => {
        cy.LOGOUT();
   });


   it("TC - AC4 -Create certificates", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CERTIFICATE);

    _common.openTab(app.TabBar.CERTIFICATE).then(() => {
        _common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 1);
    });
    _common.create_newRecord(cnt.uuid.CERTIFICATE)
    cy.wait(1000)//need wait to load 
    _common.select_rowInContainer(cnt.uuid.CERTIFICATE)
    _common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE,app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BUSINESS_PARTNER.ANTON_WALISER)
     cy.wait(1000)//need wait to load 
    _common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE,app.GridCells.CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, commonLocators.CommonLabels.BUILDINGSITE_SUPERVISORCERTIFICATE)
    cy.SAVE()
    _common.waitForLoaderToDisappear()
    _common.create_newRecord(cnt.uuid.CERTIFICATE)
    _common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE,app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BUSINESS_PARTNER.ADOLF_KOCH)
    cy.SAVE()
    _common.waitForLoaderToDisappear() 
   })
   

   it("TC - Verify certificate under contract", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
        _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE, app.FooterTab.CURRENT_CERTIFICATES, 1);
        });
        _common.search_inSubContainer(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE,CONTAINERS_BUSINESS_PARTNER.ANTON_WALISER)
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE,CONTAINERS_BUSINESS_PARTNER.ANTON_WALISER)
   
   })
   it("TC - A5 -Verify certificate under  Sub contract", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
        _common.select_tabFromFooter(cnt.uuid.CONTRACT_SUBCONTRACTOR, app.FooterTab.SUBCONTRACTOR, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_SUBCONTRACTOR)

        _common.create_newRecord(cnt.uuid.CONTRACT_SUBCONTRACTOR)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_SUBCONTRACTOR,app.GridCells.BPD_BUSINESS_PARTNER_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,"Adolf Koch")
         cy.wait(1000)//need wait to load 
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env('PROJECT_NUMBER'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE, app.FooterTab.CURRENT_CERTIFICATES, 1);
        });
        _common.search_inSubContainer(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE,CONTAINERS_BUSINESS_PARTNER.ADOLF_KOCH)
        cy.wait(1000)
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE,CONTAINERS_BUSINESS_PARTNER.ADOLF_KOCH)

   
   })
   it("TC - AC-2-Verify delete certificate under contract", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            cy.wait(1000)
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE, app.FooterTab.CURRENT_CERTIFICATES, 1);
        });
   
       _common.clear_subContainerFilter(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE)
       cy.wait(1000)//need wait to load 
        _common.select_allContainerData(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE)
        cy.wait(1000)//need wait to load 
        _common.delete_recordFromContainer(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,Cypress.env('PROJECT_NUMBER'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            cy.wait(1000)//need wait to load 
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE, app.FooterTab.CURRENT_CERTIFICATES, 1);
        });
        _common.search_inSubContainer(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE,CONTAINERS_BUSINESS_PARTNER.ANTON_WALISER)
        cy.wait(1000)
        _validate.verify_isRecordDeleted(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE,CONTAINERS_BUSINESS_PARTNER.ANTON_WALISER)
   })
   it("TC - AC-1 and AC3-Verify delete certificate under contract", function () {
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            cy.wait(1000)//need wait to load 
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE, app.FooterTab.CURRENT_CERTIFICATES, 1);
            _common.waitForLoaderToDisappear()
            _common.setup_gridLayout(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE,CONTAINER_COLUMNS_CURRENT_CERTIFICATE)


        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE)
        _common.maximizeContainer(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE)
        _common.create_newRecord(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE)
         cy.wait(1000).then(() => {//Need wait to load 
           _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CERTIFICATES,app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BUSINESS_PARTNER.ANTON_WALISER)
           cy.wait(1000)//need wait to load 
            _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_CERTIFICATES,app.GridCells.EXPIRATION_DATE,app.InputFields.INPUT_GROUP_CONTENT,_common.getDate(commonLocators.CommonKeys.CURRENT_SMALL))
            cy.wait(1000)//need wait to load 
            _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_CERTIFICATES,app.GridCells.USER_DEFINED_1,app.InputFields.DOMAIN_TYPE_DESCRIPTION,USER_DEFINED_1)
            cy.wait(1000)//need wait to load 
            _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_CERTIFICATES,app.GridCells.USER_DEFINED_2,app.InputFields.DOMAIN_TYPE_DESCRIPTION,USER_DEFINED_2)
            cy.wait(1000)//need wait to load 
            _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_CERTIFICATES,app.GridCells.REQUIRED_DATE,app.InputFields.INPUT_GROUP_CONTENT,_common.getDate(commonLocators.CommonKeys.CURRENT_SMALL))
            cy.wait(1000)//need wait to load 
            _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_CERTIFICATES,app.GridCells.VALIDATED_DATE,app.InputFields.INPUT_GROUP_CONTENT,_common.getDate(commonLocators.CommonKeys.CURRENT_SMALL))
            cy.wait(1000)//need wait to load 
            _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(commonLocators.CommonKeys.CURRENT_SMALL))
            cy.wait(1000)//need wait to load 
            _common.enterRecord_inNewRow(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.VALID_TO, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(commonLocators.CommonKeys.CURRENT_SMALL))
            cy.wait(1000)//need wait to load 
            })
            _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_CERTIFICATES,app.GridCells.CERTIFICATE_TYPE_FK)
            _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_BUSINESS_PARTNER.CIS_CERTIFICATE)
            _common.minimizeContainer(cnt.uuid.CONTRACT_ACTUAL_CERTIFICATE)
   })
})