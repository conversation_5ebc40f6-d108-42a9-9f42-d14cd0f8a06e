/*
 * Copyright (c) RIB Software GmbH
 */

using Microsoft.AspNet.OData.Query;
using Newtonsoft.Json.Linq;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Core.Core.UserManagement;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.ServerCommon;
using RIB.Visual.UserManagement.Main.BusinessComponents.Entities;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Data.Entity;
using System.Linq;
using System.Linq.Expressions;
using RIB.Visual.Platform.AppServer.Runtime;
using DbContext = RIB.Visual.Platform.BusinessComponents.DbContext;
using NLS = RIB.Visual.UserManagement.Main.Localization.Properties.Resources;

// ReSharper disable once CheckNamespace
namespace RIB.Visual.UserManagement.Main.BusinessComponents
{
	/// <summary>
	/// Contains the UserLogic
	/// </summary>
	[Export(typeof(IUserLogic))]
	public class UserLogic : EntityUpdateLogic<UserEntity, int>, IUpdateCompleteData, IUserLogic
	{

		private static readonly Lazy<Basics.Core.Core.Final.IIdentifier<UserEntity>> IdentifierInstance =
			IdentifierFactory.Create<UserEntity>("Id");

		private static readonly LazyExportedValue<IApiCallbackLogic> ApiCallbackLogic = new();

		/// <summary>
		/// A fixed string that uniquely identifies the entity in the public API.
		/// </summary>
		public const String ApiEntityId = "user";

		/// <summary>
		/// Default constructor
		/// </summary>
		public UserLogic()
		{
			Identifier = IdentifierInstance.Value;
			//PermissionGUID = _permissionGUID;
			SetRelationInfoIdentifier("usermanagement.main.user");
			CompleteUpdater = this;
			SetTempMatchingFunc<DdtempidsEntity>((e, tmp) => (e.Id == tmp.Id));
			PermissionGUID = "dfdd06a581624d11ab1e6aa1103e76e2";
		}

		/// <summary>
		/// Gets DbModel.
		/// </summary>
		/// <returns/>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// Modifies the search context based upon the request.
		/// </summary>
		/// <param name="request">The original search request.</param>
		/// <param name="response">The filter response.</param>
		/// <returns>The modified search context.</returns>
		protected override SearchSpecification<UserEntity, int> GetListSearchContext(FilterRequest request, out FilterResponse response)
		{
			var ctx = base.GetListSearchContext(request, out response);
			if (request.FurtherFilters != null)
			{
				var customPredicates = new List<Expression<Func<UserEntity, Boolean>>>();

				if (!String.IsNullOrWhiteSpace(request.Pattern))
				{
					// expand filterIn.Pattern to Like expression
					int resId;
					if (int.TryParse(request.Pattern, out resId))  // support search for primary key
					{
						customPredicates.Add(e => e.Id == resId);
					}

					customPredicates.Add(e => e.LogonName.Contains(request.Pattern));
					customPredicates.Add(e => e.Name.Contains(request.Pattern));
					customPredicates.Add(e => e.Email.Contains(request.Pattern));
					customPredicates.Add(e => e.DomainSID.Contains(request.Pattern));
					customPredicates.Add(e => e.Description.Contains(request.Pattern));
				}

				if (customPredicates.Count > 0)
				{
					ctx.CustomPredicate = customPredicates.JoinWithOr();
				}

			}
			return ctx;
		}

		#region IUpdateCompleteData member

		IEnumerable<Platform.Core.IIdentifyable> IUpdateCompleteData.HandleUpdate(IEnumerable<Platform.Core.IIdentifyable> completeData)
		{
			var users2Update = completeData.Where(c => (UserComplete)c != null).Select(r => (UserComplete)r).ToList();

			foreach (var res2Update in users2Update)
			{
				//DoUpdate(res2Update);
				UpdateEntity(res2Update);
			}

			return users2Update;
		}

		#endregion

		/// <summary>
		/// Create UserEntity
		/// </summary>
		/// <returns>new UserEntity</returns>
		public UserEntity CreateEntity()
		{

			var entity = new UserEntity
			{
				GUID = Guid.NewGuid().ToString("N"),
				Id = -1,
				IntegratedAccess = false,
				State = 1,
				ExplicitAccess = false
			};

			return entity;

		}

		/// <summary>
		/// Create UserEntity based on the Azure User property list
		/// </summary>
		/// <returns>new UserEntity</returns>
		public static UserEntity CreateEntitywithIdpProvider(int idpId, bool accountEnabled, string displayName, string idpProviderId, string userPrincipalName, string surName,
			string givenName, string eMail, string[] otherMails)
		{
			var theMail = eMail ?? (otherMails.Any() ? otherMails[0] : string.Empty);

			// AlmCase 136111 rei@26.10.22 make sure display name not exceeding max target length;
			displayName = displayName.Truncate(64);
			var description = $"{givenName} {surName}";
			description = description.Truncate(255).Trim();

			var entity = new UserEntity
			{
				GUID = Guid.NewGuid().ToString("N"),
				Id = -1,
				IntegratedAccess = true,
				State = 1,
				ExplicitAccess = false,
				LogonName = userPrincipalName,
				Email = theMail,
				FrmIdentityproviderFk = idpId,
				ProviderUniqueIdentifier = idpProviderId,
				Description = description,
				Name = displayName,
			};
			if (!accountEnabled)
			{
				entity.State = 2;  // user deactivated
				entity.IntegratedAccess = false;
			}

			return entity;

		}


		/// <summary>
		/// Create UserEntity based on the Azure User property list
		/// </summary>
		/// <returns>new UserEntity</returns>
		public static bool CheckUpdateUserwithIdpProvider(UserEntity entity, int idpId, bool accountEnabled, string displayName, string idpProviderId, string userPrincipalName,
			string surName, string givenName, string eMail, string[] otherMails)
		{
			if (entity.FrmIdentityproviderFk != idpId) // should never appear
			{
				throw new ArgumentException(string.Format("User {0} does not match idpId: {1}", entity.Name, idpId), "entity");
			}
			if (entity.ProviderUniqueIdentifier != idpProviderId) // should never appear
			{
				throw new ArgumentException(string.Format("User {0} must have same ipd unique id: {1}, new: {2}", entity.Name, entity.ProviderUniqueIdentifier, idpProviderId), "idpProviderId");
			}

			var theMail = eMail ?? (otherMails.Any() ? otherMails[0] : string.Empty);
			bool hasChanged = false;

			if (!Helper.StringComparer(entity.Name, displayName)) { entity.Name = displayName; hasChanged = true; }
			var theName = string.Format("{0} {1}", givenName, surName).Trim();
			if (!Helper.StringComparer(entity.Description, theName)) { entity.Description = theName; hasChanged = true; }
			if (!Helper.StringComparer(entity.Email, theMail)) { entity.Email = theMail; hasChanged = true; }
			if (!Helper.StringComparer(entity.LogonName, userPrincipalName)) { entity.LogonName = userPrincipalName; hasChanged = true; }

			if (accountEnabled && entity.State != 1)
			{
				entity.State = 1;
				entity.DisabledHint = "";
				entity.Setinactivedate = null;
				hasChanged = true;
			}
			if (!accountEnabled && entity.State == 1)
			{
				entity.State = 2;  // user state is deactivated 
				entity.DisabledHint = "disabled via Azure AD Synchronization";
				entity.Setinactivedate = DateTime.Now;
				hasChanged = true;
			}

			if (entity.State == 1)
			{
				if (!(entity.IntegratedAccess.HasValue && entity.IntegratedAccess.Value))
				{
					entity.IntegratedAccess = true;
					hasChanged = true;
				}
				if (!(entity.ExplicitAccess.HasValue && !entity.ExplicitAccess.Value))
				{
					entity.ExplicitAccess = false;
					hasChanged = true;
				}
			}
			return hasChanged;
		}



		/// <summary>
		/// Create UserEntity
		/// </summary>
		/// <returns>new UserEntity</returns>
		public UserEntity CreateEntity(UserEntity user)
		{
			user.GUID = Guid.NewGuid().ToString("N");
			user.ExplicitAccess = true;
			user.PasswordSalt = Guid.NewGuid().ToString("N");
			user.PasswordHash = ServerUtils.HashPassword(user.Password, user.PasswordSalt);

			var userExtProviderLogic = new UserExtProviderLogic();
			var identityProvider = userExtProviderLogic.GetIdentityProvider();

			try
			{
				using var transaction = TransactionScopeFactory.Create();
				using (var dbcontext = new DbContext(ModelBuilder.DbModel))
				{
					dbcontext.Save(user);

					if (identityProvider != null)
					{
						var userExtProvider = new UserExtProviderEntity
						{
							FrmUserFk = user.Id,
							Provider = identityProvider.Code,
							ProviderId = user.LogonName,
							CompanyName = identityProvider.Name,
							FrmIdentityProviderFk = identityProvider.Id,
							FirstName = "-",
							FamilyName = "-",
							Email = String.IsNullOrWhiteSpace(user.Email) ? "-" : user.Email
						};
						dbcontext.Save(userExtProvider);
					}
				}
				transaction.Complete();
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}
			user.FrmUserextproviderEntities.Clear();
			return user;
		}


		/// <summary>
		/// returns all User from frm_user, sorted by name
		/// 
		/// </summary>
		/// <param name="filterOutProtected">exclude system users from list if true</param>
		/// <returns></returns>
		public IEnumerable<UserEntity> GetAllUsers(bool filterOutProtected = false)
		{
			// GetSortedUsers();
			return FilteredOutSystemUsers(filterOutProtected, GetSortedUsers());
		}

		/// <summary>
		/// GetUserById
		/// </summary>
		/// <returns>UserEntity</returns>
		public UserEntity GetUserById(int id)
		{
			return GetById(new Platform.Core.IdentificationData(id));
		}

		/// <summary>
		/// GetUserById
		/// </summary>
		/// <returns>UserEntity</returns>
		public IUserEntity GetUserByEmail(string email)
		{
			using var dbContext = new DbContext(ModelBuilder.DbModel);
			var entity = dbContext.Entities<UserEntity>().FirstOrDefault(e => e.Email == email);
			return entity;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		IUserEntity IUserLogic.GetUserById(Int32 id)
		{
			return GetUserById(id);
		}

		/// <summary>
		/// see 
		/// </summary>
		/// <param name="email"></param>
		/// <returns></returns>
		IUserEntity IUserLogic.GetUserByEmail(string email)
		{
			return GetUserByEmail(email);
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="logonName"></param>
		/// <returns></returns>
		IUserEntity IUserLogic.GetUserByLogonName(string logonName)
		{
			return GetUserByLogonName(logonName);
		}

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        IUserEntity IUserLogic.UpdateUser(IUserEntity entity) 
		{
			Permission.EnsureWrite(PermissionGUID);

			var existingEntity = GetUserById(entity.Id);
			existingEntity.Email = entity.Email;
			existingEntity.Description = entity.Description;
			existingEntity.LogonName = entity.LogonName;
			existingEntity.Name = entity.Name;
			existingEntity.IsAnonymized = entity.IsAnonymized;
			return SaveEntity(existingEntity);
		}

        /// <summary>
        /// 
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        IEnumerable<IUserEntity> IUserLogic.UpdateUsers(IEnumerable<IUserEntity> entities)
        {
            Permission.EnsureWrite(PermissionGUID);
			
			List<UserEntity> usersToBeSaved = new List<UserEntity>();
			foreach (var entity in entities)
			{
                var existingEntity = GetUserById(entity.Id);
                existingEntity.Email = entity.Email;
                existingEntity.Description = entity.Description;
                existingEntity.LogonName = entity.LogonName;
                existingEntity.Name = entity.Name;
                existingEntity.IsAnonymized = entity.IsAnonymized;
                usersToBeSaved.Add(existingEntity);
            }

			SaveEntities(usersToBeSaved);
			return usersToBeSaved;
        }


        /// <summary>
        /// This is new approach to deal with GetSearchList request from front end and dynamic linq is not suggested to use because of possible misuse to cause security issue.
        /// </summary>
        /// <param name="predicate"></param>
        /// <returns></returns>
        public virtual IEnumerable<UserEntity> GetFilteredUsers(Expression<Func<UserEntity, bool>> predicate)
		{
			return GetByFilter(predicate);
		}

		/// <summary>
		/// Returns the users sorted by the name field.
		/// if filter is not null, this expression is used for filtering the data
		/// if filter is null, all items will be returned
		/// </summary>
		/// <returns></returns>
		private IEnumerable<UserEntity> GetSortedUsers(Expression<Func<UserEntity, bool>> filter = null)
		{
			var entities = filter != null ?
				GetByFilter(filter).OrderBy(i => i.Name)
				: GetByFilter(e => e.Id != 0).OrderBy(i => i.Name);
			return entities;
		}

		/// <summary>
		/// Get filtered Users
		/// </summary>
		/// /// <param name="filterIn"></param>
		/// <param name="filterOut"></param>
		/// <param name="filterOutProtected"></param>
		/// <returns>Collection of filtered UserEntity</returns>
		public IEnumerable<UserEntity> GetFilteredUsers(FilterRequest<int> filterIn, ref FilterResponse<int> filterOut, bool filterOutProtected = false)
		{
			var execInfo = new FilterExecutionInfo<int>(filterIn, filterOut);
			execInfo.CreateHint("GetList() ");

			try
			{
				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				IQueryable<UserEntity> query = dbcontext.Entities<UserEntity>();

				if (!string.IsNullOrWhiteSpace(filterIn.Pattern))
				{
					query = FilterRequest<int>.ExpandToSearchPattern
						(filterIn, query, filterVal => "(Description.Contains(\"" + filterVal + "\") or Name.Contains(\"" + filterVal + "\")or Logonname.Contains(\"" + filterVal + "\"))");
				}
				if (filterIn.PKeys != null)
				{
					query = query.Where(i => filterIn.PKeys.Contains(i.Id));
				}

				var entities = filterIn.OrderBy == null
					? FilterRequest<int>.RetrieveEntities(filterIn, filterOut, query, i => i.Name, i => i.Id)
					: FilterRequest<int>.RetrieveEntities(filterIn, filterOut, query);

				execInfo.CreateHint("GetList() -> done");
				return FilteredOutSystemUsers(filterOutProtected, entities);
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}
		}

		private IEnumerable<UserEntity> FilteredOutSystemUsers(bool filterOutProtected, IEnumerable<UserEntity> entities)
		{
			var systemUserIds = GetSystemUserIds();
			var resEntities = entities.Where(e =>
			{
				e.IsSystemUser = systemUserIds.Contains(e.Id);
				if (filterOutProtected)
				{
					return !e.IsSystemUser;
				}
				return true;
			});
			return resEntities;
		}

		/// GetFilteredUsersExtended
		/// <param name="filterMap"></param>
		/// <returns></returns>
		/// <returns>Collection of filtered RoleEntity</returns>
		public IEnumerable<UserEntity> GetFilteredUsersExtended(IEnumerable<MItemType> filterMap)
		{
			try
			{
				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				IQueryable<UserExtProviderEntity> queryProvider =
					dbcontext.Entities<UserExtProviderEntity>().Where(i => i.FrmIdentityProviderFk == 6);
				IQueryable<UserEntity> query = dbcontext.Entities<UserEntity>().Where(i => i.IsExternal == false).Include(i => i.FrmUserextproviderEntities);
				var mItemTypes = filterMap.ToList();
				var pageIndex = 1;
				var pageSize = 0;
				foreach (var filter in mItemTypes)
				{
					var filterValue = filter.Value.Replace("*", "").Replace("?", "_");
					// ReSharper disable once SwitchStatementMissingSomeCases
					switch (filter.Key)
					{
						case "pageIndex":
							Int32.TryParse(filter.Value, out pageIndex);
							break;
						case "pageSize":
							Int32.TryParse(filter.Value, out pageSize);
							break;
						case "lastModified":
							var dateTime = DateTime.Parse(filterValue).ToUniversalTime();
							// ReSharper disable AccessToModifiedClosure
							query = query.Where(i => i.UpdatedAt != null && i.UpdatedAt >= dateTime || i.InsertedAt >= dateTime);
							break;
						case "accountId":
							query = query.Where(i => i.LogonName.Contains(filterValue));
							break;
						case "email":
							query = query.Where(i => i.Email.Contains(filterValue));
							break;
						case "firstname":
						case "lastname":
							query = query.Where(i => i.Name.Contains(filterValue));
							break;
						case "department":
							queryProvider = queryProvider.Where(i => i.Comment.Contains(filterValue));
							break;
					}
				}
				//query = queryProvider.Join(query, outer => outer.FrmUserFk, inner => inner.Id, (outer, inner) => inner);
				query = query.Join(queryProvider, outer => outer.Id, inner => inner.FrmUserFk, (outer, inner) => outer);
				query = query.Include(i => i.FrmUserextproviderEntities);
				if (pageSize != 0 && pageIndex >= 0)
				{
					query = query
						.OrderBy(t => t.Id)
						.Skip(pageIndex * pageSize)
						.Take(pageSize);
				}

				var result = query.ToList();
				return result;
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (Int32)ExceptionErrorCodes.BusinessFatalError };
			}
		}

		/// <summary>
		/// Save UserEntity
		/// </summary>
		/// /// <param name="entity"></param>
		/// <param name="passwordChanged"></param>
		/// <returns></returns>
		public UserEntity SaveEntity(UserEntity entity, bool passwordChanged = false)
		{
			CheckChangeUsersPermissions(new[] { entity });

			using var context = new DbContext(ModelBuilder.DbModel);
			//context.Entities<UserEntity>().Add(entity);
			//context.Entry<UserEntity>(entity).Property(i => i.PasswordSalt).IsModified = false;
			//context.Entry<UserEntity>(entity).Property(i => i.PasswordHash).IsModified = false;

			// read only if this item is not new, read HASH and Salt, because we do not have this send to client
			if (entity.Version != 0)
			{
				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				var entityToValidate = dbcontext.GetFirst<UserEntity>(i => i.Id == entity.Id);
				entity.PasswordSalt = entityToValidate.PasswordSalt;
				entity.PasswordHash = entityToValidate.PasswordHash;
			}

			if (passwordChanged)
			{
				//context.Entry<UserEntity>(entity).State = System.Data.Entity.EntityState.Modified;
				//context.Entry<UserEntity>(entity).Property(i => i.PasswordHash).IsModified = true;

				if (!string.IsNullOrEmpty(entity.Password))
				{
					var passwordRulesLogic = new PasswordRulesLogic();
					if (!passwordRulesLogic.ValidatePassword(entity.Password))
					{
						throw new BusinessLayerException(passwordRulesLogic.Hint);
					}
				}

				// if there is no salt already, we encrypt with a new salt.
				if (string.IsNullOrEmpty(entity.PasswordSalt))
				{
					entity.PasswordSalt = Guid.NewGuid().ToString("N");
					// context.Entry<UserEntity>(entity).Property(i => i.PasswordSalt).IsModified = true;
				}
				entity.PasswordHash = ServerUtils.HashPassword(entity.Password, entity.PasswordSalt);
			}

			context.Save(entity);
			return entity;
		}

		/// <summary>
		/// SaveEntities
		/// </summary>
		/// <param name="users"></param>
		public void SaveEntities(IEnumerable<UserEntity> users)
		{
			var allUsers = users.ToArray();

			using var dbcontext = new DbContext(ModelBuilder.DbModel);
			dbcontext.Save(allUsers);
		}

		private void CheckChangeUsersPermissions(IEnumerable<UserEntity> users)
		{
			Permission.EnsureItemUpdate(PermissionGUID, users, u => u.Version);
		}

		/// <summary>
		/// Update
		/// </summary>
		/// <param name="user"></param>
		public UserEntity Update(UserEntity user)
		{
			CheckChangeUsersPermissions(new[] { user });

			try
			{
				var userExtProviderLogic = new UserExtProviderLogic();
				var identityProvider = userExtProviderLogic.GetIdentityProvider();

				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				var frmUser = GetUserByLogonName(user.LogonName);

				if (user.Name != null)
				{
					frmUser.Name = user.Name;
				}

				if (user.Description != null)
				{
					frmUser.Description = user.Description;
				}

				if (user.State != null)
				{
					frmUser.State = user.State;
				}

				if (user.Email != null)
				{
					frmUser.Email = user.Email;
				}

				if (!string.IsNullOrEmpty(user.Password))
				{
					PasswordRulesLogic passwordRulesLogic = new PasswordRulesLogic();
					if (passwordRulesLogic.Rules.Publicwebapi)
					{
						if (!passwordRulesLogic.ValidatePassword(user.Password))
						{
							throw new BusinessLayerException(passwordRulesLogic.Hint);
						}
					}

					frmUser.PasswordSalt = Guid.NewGuid().ToString("N");
					frmUser.PasswordHash = ServerUtils.HashPassword(user.Password, frmUser.PasswordSalt);
				}

				var result = dbcontext.Save(frmUser);

				if (identityProvider != null)// update UserExtProviderEntity.
				{
					UserExtProviderEntity userExtProviderEntity = userExtProviderLogic.GetUserExtproviderByProviderLogonName(identityProvider.Id, frmUser.LogonName);

					if (userExtProviderEntity == null)
					{
						userExtProviderEntity = new UserExtProviderEntity()
						{
							FrmUserFk = frmUser.Id,
							FrmIdentityProviderFk = identityProvider.Id,

							FirstName = "-",
							FamilyName = "-"
						};
					}
					userExtProviderEntity.UserEntity = null; //!! set navigation property to null to ensure save successfully.

					userExtProviderEntity.Provider = identityProvider.Code;
					userExtProviderEntity.CompanyName = identityProvider.Name;

					userExtProviderEntity.ProviderId = frmUser.LogonName;
					userExtProviderEntity.Email = String.IsNullOrWhiteSpace(frmUser.Email) ? "-" : frmUser.Email;

					dbcontext.Save(userExtProviderEntity);
				}

				result.FrmUserextproviderEntities = new List<UserExtProviderEntity>(); // avoid self referencing loop

				return result;
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}
		}


		/// <summary>
		/// Delete UserEntity
		/// </summary>
		/// <returns></returns>
		public int DeleteEntity(UserEntity entity)
		{
			var clerkUserProvider = BusinessApplication.BusinessEnvironment.GetExportedValue<IClerkUserProvider>();
			var linkedClerksCount = clerkUserProvider.GetClerksByUser(entity.Id)?.Length ?? 0;
			if (linkedClerksCount > 0)
			{
				throw new BusinessLayerException(String.Format(NLS.UserUndeletableLinkedToClerk, linkedClerksCount));
			}

			var data = new Platform.Core.IdentificationData(entity.Id);
			var context = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
			ApiCallbackLogic.Value.NotifyAllCallbacks(
				entityType: ApiEntityId,
				eventType: ApiCallbackStandardEvent.Deletion,
				itemId: null,
				data: data,
				context: context);

			using var dbcontext = new DbContext(ModelBuilder.DbModel);
			dbcontext.Delete(entity);

			return entity.Id;
		}

		private bool IsProtectedUsersProtectedFieldChanged(UserEntity userInDb, UserEntity updateUser)
		{

			if (userInDb == null || !GetSystemUserIds().Contains(userInDb.Id))
			{
				return false;
			}
			var protectedFieldsChanged = (userInDb.IntegratedAccess != updateUser.IntegratedAccess) || (userInDb.IsExternal != updateUser.IsExternal) || (userInDb.LogonName != updateUser.LogonName);
			return protectedFieldsChanged;
		}

		/// <summary>
		/// Saves a singles entity with all dependent data
		/// </summary>
		/// <param name="completeEntity">The element and its entire to be saved</param>
		/// <returns></returns>
		public void UpdateEntity(UserComplete completeEntity)
		{
			using var transaction = TransactionScopeFactory.Create();
			if (completeEntity.User != null)
			{
				var userInDb = GetById(new Platform.Core.IdentificationData(completeEntity.User.Id));
				if (IsProtectedUsersProtectedFieldChanged(userInDb, completeEntity.User))
				{
					completeEntity.User.IntegratedAccess = userInDb.IntegratedAccess;
					completeEntity.User.IsExternal = userInDb.IsExternal;
					completeEntity.User.LogonName = userInDb.LogonName;
				}
				var changePasswordRequested = !string.IsNullOrEmpty(completeEntity.User.Password);
				if (changePasswordRequested)
				{
					PasswordRulesLogic passwordRulesLogic = new PasswordRulesLogic();
					if (!passwordRulesLogic.ValidatePassword(completeEntity.User.Password))
					{
						throw new BusinessLayerException(passwordRulesLogic.Hint);
					}

					using var dbcontext = new DbContext(ModelBuilder.DbModel);
					var entityToValidate = dbcontext.GetFirst<UserEntity>(i => i.Id == completeEntity.User.Id);
					completeEntity.User.PasswordHash = ServerUtils.HashPassword(completeEntity.User.Password, entityToValidate != null ? entityToValidate.PasswordSalt : null);
				}

				if (completeEntity.User.State == 1 && completeEntity.User.FailedLogon > 0)
				{ // rei@04.08.20 reset failed login properties
					completeEntity.User.FailedLogon = 0;
					completeEntity.User.DisabledHint = string.Empty;
					completeEntity.User.LoginBlockedUntil = null;
				}
				completeEntity.User = SaveEntity(completeEntity.User, changePasswordRequested);
				completeEntity.MainItemId = completeEntity.User.Id;
			}

			if (completeEntity.User2GroupToSave != null || completeEntity.User2GroupToDelete != null)
			{
				var logic = new AccessUser2GroupLogic();

				if (completeEntity.User2GroupToSave != null)
				{
					foreach (var item in completeEntity.User2GroupToSave)
					{
						item.UserFk = completeEntity.MainItemId;
					}

					completeEntity.User2GroupToSave = logic.SaveAccessUser2Group(completeEntity.User2GroupToSave.ToList());
				}

				if (completeEntity.User2GroupToDelete != null)
				{
					completeEntity.User2GroupToDelete = logic.DeleteAccessUser2Group(completeEntity.User2GroupToDelete.ToList());
				}
			}

			if (completeEntity.User != null)
			{
				var data = new Platform.Core.IdentificationData(completeEntity.User.Id);
				var context = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
				ApiCallbackLogic.Value.NotifyAllCallbacks(
					entityType: ApiEntityId,
					eventType: completeEntity.User.Version == 1 ? ApiCallbackStandardEvent.Creation : ApiCallbackStandardEvent.Modification,
					itemId: null,
					data:	data,
					context: context);
			}

			transaction.Complete();
		}


		///<summary>
		///HasPassword
		///</summary>
		public bool HasPassword(UserEntity entity)
		{
			return !string.IsNullOrEmpty(entity.PasswordHash);
		}

		/// <summary>
		/// GetUserByLogonName
		/// </summary>
		/// <param name="logonName"></param>
		/// <returns></returns>
		public UserEntity GetUserByLogonName(string logonName)
		{
			using var dbcontext = new DbContext(ModelBuilder.DbModel);
			return dbcontext.Entities<UserEntity>().FirstOrDefault(i => i.LogonName == logonName);
		}

		/// <summary>
		/// GetUserByLogonNameAddUserExtProvider
		/// </summary>
		/// <param name="logonName"></param>
		/// <returns></returns>
		public UserEntity GetUserByLogonNameAddUserExtProvider(String logonName)
		{
			try
			{
				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				return dbcontext.Entities<UserEntity>().Where(i => i.LogonName == logonName).Include(i => i.FrmUserextproviderEntities).FirstOrDefault();
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}

		}

		/// <summary>
		/// GetUserByLogonNameAddUserExtProviderFiltered
		/// </summary>
		/// <param name="logonName"></param>
		/// <returns></returns>
		public UserEntity GetUserByLogonNameAddUserExtProviderFiltered(String logonName)
		{
			try
			{
				using var dbContext = new DbContext(ModelBuilder.DbModel);
				return dbContext.Entities<UserEntity>().Where(i => i.LogonName == logonName && i.IsExternal == false).Include(i => i.FrmUserextproviderEntities).FirstOrDefault();
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}

		}

		/// <summary>
		/// DeleteEntities
		/// </summary>
		/// <param name="deleteUsers"></param>
		public void DeleteEntities(List<UserEntity> deleteUsers)
		{
			using var dbcontext = new DbContext(ModelBuilder.DbModel);
			dbcontext.Delete(deleteUsers);
		}

		/// <summary>
		/// GetUsersByIds
		/// </summary>
		/// <param name="userIds"></param>
		/// <returns></returns>
		public IEnumerable<UserEntity> GetUsersByIds(IEnumerable<int> userIds)
		{
			try
			{
				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				return dbcontext.GetFiltered<UserEntity>(i => userIds.Contains(i.Id)).ToList();
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}
		}

		/// <summary>
		/// InactivateUsers
		/// </summary>
		/// <param name="userIds"></param>
		/// <param name="markLogonName">if true we change the logonname and prefix it with guid - make sure its unique</param>
		public void InactivateUsers(IEnumerable<int> userIds, bool markLogonName = false)
		{
			try
			{
				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				var frmUsers = dbcontext.GetFiltered<UserEntity>(i => userIds.Contains(i.Id)).ToList()
					.Select(i =>
					{
						if (markLogonName && i.State != 2) { i.LogonName = (Guid.NewGuid().ToString("N") + '(' + i.LogonName + ')').Truncate(255); }
						if (i.Setinactivedate == null) { i.Setinactivedate = DateTime.UtcNow; }
						i.Description = "inactivated by InactivateUsers";
						i.State = 2; // set inactive
						return i;
					});
				dbcontext.Save(frmUsers);
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}
		}

		/// <summary>
		/// DeleteInactivateUsers
		/// this method tries to delete a user, if not possible it will be inactivated
		/// </summary>
		/// <param name="userIds"></param>
		public void DeleteInactivateUsers(IEnumerable<int> userIds)
		{
			var frmUsers = GetUsersByIds(userIds);

			foreach (var user in frmUsers)
			{
				try
				{
					DeleteEntity(user);
				}
				catch (ResourceAccessLayerException)
				{
					// try to set user inactive
					InactivateUsers(new List<int>() { user.Id }, markLogonName: true);
				}
			}
		}



		/// <summary>
		/// InactivateUser
		/// </summary>
		/// <param name="logonName"></param>
		public UserEntity InactivateUser(String logonName)
		{
			var frmUser = CheckUserExistElseThrowException(logonName, GetUserByLogonName(logonName));
			frmUser.State = 2;
			frmUser.DisabledHint = "platform.identityserver.disabledByUserManagement";  // we use json based translations
			return JustSaveFrmUser(frmUser);
		}

		/// <summary>
		/// ActivateUser
		/// </summary>
		/// <param name="logonName"></param>
		public UserEntity ActivateUser(String logonName)
		{
			var frmUser = CheckUserExistElseThrowException(logonName, GetUserByLogonName(logonName));
			frmUser.State = 1;
			frmUser.FailedLogon = 0;
			frmUser.LoginBlockedUntil = null;
			frmUser.DisabledHint = string.Empty;
			return JustSaveFrmUser(frmUser);
		}


		/// <summary>
		/// Activate/(Inactivate of a System user.
		/// 
		/// </summary>
		/// <param name="userId">Primary key of User from FRM_USER</param>
		/// <param name="state">allowed states: 1 active, 2 inactive</param>
		public UserEntity SetUserActivationState(int userId, int state)
		{
			var frmUser = CheckUserExistElseThrowException(userId, GetUserById(userId));
			if (state != 1 && state != 2 && state != 0)
			{
				throw new BusinessLayerException(string.Format("Invalid Parameter, State only accept 1 or 2 not {0}", state)) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}
			if (frmUser.State == state) { return frmUser; } // nothing todo

			if (frmUser.State == 2 && state == 1) // reactivate
			{
				frmUser.FailedLogon = 0;
				frmUser.DisabledHint = string.Empty;
				frmUser.LoginBlockedUntil = null;
			}
			if (frmUser.State == 1 && state == 2) // deactivate
			{
				frmUser.DisabledHint = "platform.identityserver.disabledByUserManagement";  // we use json based translations
			}
			frmUser.State = (short)state;
			frmUser.Setinactivedate = (state == 2) ? DateTime.Now : null;

			return JustSaveFrmUser(frmUser);
		}

		/// <summary>
		/// Helper Utility
		/// just do this: Check UserExist Else Throw Exception
		/// </summary>
		/// <param name="userId"></param>
		/// <param name="frmUser"></param>
		private static UserEntity CheckUserExistElseThrowException(int userId, UserEntity frmUser)
		{
			if (frmUser == null)
			{
				throw new BusinessLayerException(string.Format("User with Id={0} not found!", userId))
				{
					ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError
				};
			}
			return frmUser;
		}
		/// <summary>
		/// Helper Utility
		/// just do this: Check UserExist Else Throw Exception
		/// </summary>
		/// <param name="logonName"></param>
		/// <param name="frmUser"></param>
		private static UserEntity CheckUserExistElseThrowException(string logonName, UserEntity frmUser)
		{
			if (frmUser == null)
			{
				throw new BusinessLayerException(string.Format("User with LogonName={0} not found!", logonName))
				{
					ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError
				};
			}
			return frmUser;
		}



		/// <summary>
		/// Helper Utility
		/// Save the user nothing else, but with Error Handler
		/// </summary>
		/// <param name="frmUser"></param>
		private UserEntity JustSaveFrmUser(UserEntity frmUser)
		{
			try
			{
				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				dbcontext.Save(frmUser);
				return frmUser;
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError };
			}
		}


		/// <summary>
		/// This method validates the logonname against his password....
		/// 
		/// 
		/// </summary>
		/// rei@26.1.18
		/// <param name="logonName"></param>
		/// <param name="password"></param>
		/// <returns>
		/// if validation is successful:
		///		userentity 
		///		else null
		/// </returns>
		internal UserEntity CheckUserLogonName(string logonName, string password)
		{
			var userEntity = GetUserById(Context.UserId);
			if (userEntity == null)
			{
				return null;
			}

			// checks password hash against saved one...
			// ReSharper disable once ConvertIfStatementToReturnStatement
			if (!ServerUtils.ValidatePasswordHashCode(userEntity.PasswordHash, password, userEntity.PasswordSalt))
			{
				return userEntity;
			}
			return null;
		}

		/// <summary>
		/// decodes the encoded password
		/// </summary>
		/// <param name="encoded"></param>
		/// <param name="salt"></param>
		/// <returns></returns>
		public string GetDecoded(string encoded, string salt = "base64.")
		{
			string pwd;
			try
			{
				var encodedTextBytes = System.Convert.FromBase64String(encoded);
				pwd = System.Text.Encoding.UTF8.GetString(encodedTextBytes);
				pwd = System.Net.WebUtility.UrlDecode(pwd);
				pwd = pwd.StartsWith(salt) ? pwd.Substring(salt.Length) : encoded;
			}
			catch (Exception)
			{
				pwd = encoded;
			}
			return pwd;
		}


		/// <summary>
		/// Changes internal database Password for current logon
		///
		/// if something wrong throw BusinessLayerException
		/// </summary>
		/// rei@26.1.18
		/// <param name="logonName"></param>
		/// <param name="oldPassword"></param>
		/// <param name="newPassword"></param>
		/// <param name="fromPublicApi"></param>
		/// <returns>
		/// true if password is changed
		/// false if same password, it's not changed
		/// 
		/// </returns>
		public bool ChangeUserPassword(string logonName, string oldPassword, string newPassword, bool fromPublicApi = false)
		{
			var result = false;

			using var transaction = TransactionScopeFactory.Create();
			var userEntity = this.GetUserById(Context.UserId);
			if (userEntity != null)
			{

				if (!userEntity.LogonName.ToLower().Equals(logonName.ToLower()))
				{
					throw new BusinessLayerException("Change Password only allowed for current user!");
				}
				if (!userEntity.ExplicitAccess.HasValue || !userEntity.ExplicitAccess.Value)
				{
					throw new BusinessLayerException("Change Password only allowed for local database (explicit access) users!");
				}
				if (!userEntity.State.HasValue || userEntity.State.Value == 0)
				{
					throw new BusinessLayerException("Change Password only allowed for activated users!");
				}

				// check password hash against saved one...
				if (!ServerUtils.ValidatePasswordHashCode(userEntity.PasswordHash, oldPassword, userEntity.PasswordSalt))
				{
					throw new BusinessLayerException("Change Password not allowed, invalid old password!");
				}
				var passwordRulesLogic = new PasswordRulesLogic();
				if (fromPublicApi)
				{
					if (passwordRulesLogic.Rules.Publicwebapi)
					{
						ValidatePasswordWithRules(newPassword, passwordRulesLogic);
					}
				}
				else
				{
					ValidatePasswordWithRules(newPassword, passwordRulesLogic);
				}

				// if there is no salt already, we encrypt with a new salt.
				if (string.IsNullOrEmpty(userEntity.PasswordSalt))
				{
					userEntity.PasswordSalt = Guid.NewGuid().ToString("N");
				}
				var newPasswordHash = ServerUtils.HashPassword(newPassword, userEntity.PasswordSalt);

				if (!userEntity.PasswordHash.Equals(newPasswordHash))
				{
					userEntity.PasswordHash = newPasswordHash;
					userEntity.IsPasswordChangeNeeded = false;
				}

				// save user with new password and eventually changed salt
				using var context = new DbContext(ModelBuilder.DbModel);
				context.Save(userEntity);
				result = true;
			}
			transaction.Complete();
			return result;
		}

		private void ValidatePasswordWithRules(string password, PasswordRulesLogic passwordRulesLogic)
		{
			var valid = passwordRulesLogic.ValidatePassword(password);
			if (!valid)
			{
				throw new BusinessLayerException("Change Password not allowed. " + passwordRulesLogic.Hint);
			}
		}

		/// <summary>
		/// GetUserByDomainSid
		/// </summary>
		/// <param name="domainSid"></param>
		/// <returns></returns>
		public UserEntity GetUserByDomainSid(String domainSid)
		{
			try
			{
				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				return dbcontext.GetFirst<UserEntity>(i => i.DomainSID == domainSid);
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (Int32)ExceptionErrorCodes.BusinessFatalError };
			}
		}

		/// <summary>
		/// Patches an existing entity 
		/// </summary>
		/// <param name="id">id of entity to be patched</param>
		/// <param name="patch">properties to be patched</param>
		/// <returns>updated entity</returns>
		public UserEntity Patch(Int32 id, JObject patch)
		{
			using var dbContext = new DbContext(ModelBuilder.DbModel);
			var entity = dbContext.Entities<UserEntity>().First(e => e.Id == id);
			var version = patch["Version"] == null ? 0 : patch["Version"].Value<Int32>();

			if (version != 0 && version != entity.Version)
			{
				throw new BusinessLayerException("The resource has already been changed")
				{
					ErrorCode = (Int32)ExceptionErrorCodes.ResourceConcurrency
				};
			}

			if (patch["Name"] != null)
			{
				entity.Name = patch["Name"].Value<string>();
			}

			if (patch["Description"] != null)
			{
				entity.Description = patch["Description"].Value<string>();
			}

			if (patch["LogonName"] != null)
			{
				entity.LogonName = patch["LogonName"].Value<string>();
			}

			if (patch["Email"] != null)
			{
				entity.Email = patch["Email"].Value<string>();
			}

			if (patch["IntegratedAccess"] != null)
			{
				entity.IntegratedAccess = patch["IntegratedAccess"].Value<bool>();
			}

			if (patch["State"] != null)
			{
				entity.State = patch["State"].Value<short>();
			}

			if (patch["ExplicitAccess"] != null)
			{
				entity.ExplicitAccess = patch["ExplicitAccess"].Value<bool>();
			}

			dbContext.Save(entity);

			return dbContext.Entities<UserEntity>().First(e => e.Id == id);
		}

		/// <summary>
		/// Filter/Search by using odata query syntax. 
		/// Supported: $top, $skip, $filter, $orderby, $select 
		/// Unsupported: $include
		/// </summary>
		/// <param name="queryOptions">odata query options</param>
		/// <returns>result set filtered by odata query parameters</returns>
		public IQueryable Filter(ODataQueryOptions<UserEntity> queryOptions)
		{
			var context = new DbContext(ModelBuilder.DbModel);

			return queryOptions.ApplyTo(context.Entities<UserEntity>());
		}


		/// <summary>
		/// find all users from specific ipd with not being member of un accessgroup and ending with domainname.
		/// </summary>
		/// <param name="idpId"></param>
		/// <param name="domainName"></param>
		/// <returns></returns>
		public static IList<UserEntity> GetOrphaneIpdUsers(int idpId, string domainName)
		{
			if (string.IsNullOrWhiteSpace(domainName))
			{
				throw new ArgumentNullException("domainName", "you must specify a valid domainname");
			}
			if (idpId == 0)
			{
				throw new ArgumentException("No valid idp id defined.", "idpId");
			}
			try
			{
				using var dbcontext = new DbContext(ModelBuilder.DbModel);
				var subQuery = dbcontext.Entities<AccessUser2GroupEntity>();
				var res = from u in dbcontext.Entities<UserEntity>()
							 where u.ProviderUniqueIdentifier != null && u.FrmIdentityproviderFk == idpId && u.LogonName.EndsWith(domainName)
									 && !subQuery.Any(e => u.Id == e.UserFk)
							 select u;
				return res.ToList();
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (Int32)ExceptionErrorCodes.BusinessFatalError };
			}
			/*
			from s in context.shift
				where !context.employeeshift.Any(es => (es.shiftid == s.shiftid) && (es.empid == 57))
				select s;
			select* from shift where not exists
			(select 1 from employeeshift where shift.shiftid = employeeshift.shiftid
			and employeeshift.empid = 57);
			*/
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="userId"></param>
		/// <returns></returns>
		public bool _IsProtected(int userId)
		{
			return GetSystemUserIds().Contains(userId);
		}

		private static readonly object LockSystemUsersIds = new();
		private static IList<int> _systemUsersIds;
		/// <summary>
		/// Gets the list of protected users ids
		/// rei@23.7.2020
		/// System users: translate logonname to ids and save in static list.
		/// </summary>
		/// <returns></returns>
		public IEnumerable<int> GetSystemUserIds()
		{
			lock (LockSystemUsersIds)
			{
				if (_systemUsersIds != null)
				{
					return _systemUsersIds;
				}
				// init list from names
				var protectedLogonNames = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetSystemUsers();
				_systemUsersIds = (GetSortedUsers(f => protectedLogonNames.Contains(f.LogonName)).Select(u => u.Id)).ToList();
				_systemUsersIds.Add(0);  // add required system account as well, should never be deleted never
				return _systemUsersIds;
			}
		}

		/// <summary>
		/// return true if userid
		/// </summary>
		/// <param name="userId">id of user</param>
		/// <returns></returns>
		public bool IsSystemUser(int userId)
		{
			return GetSystemUserIds().Contains(userId);
		}

		/// <summary>
		/// ModifyStateBulk
		/// </summary>
		/// <param name="newState"></param>
		/// <param name="userIds"></param>
		/// <returns></returns>
		public bool ModifyStateBulk(short newState, int[] userIds)
		{
			try
			{
				var selectedUsers = GetByFilter(e => userIds.Contains(e.Id)).ToList();
				foreach (var user in selectedUsers)
				{
					user.State = newState;
				}
				Save(selectedUsers);
				return true;
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException("ModifyStateBulk()", ex);
			}
		}

		/// <summary>
		/// Retrieves user IDs based on their GUID (or the GUID of groups containing the users).
		/// </summary>
		/// <param name="userUuids">User GUIDs.</param>
		/// <param name="groupUuids">Group GUIDs.</param>
		/// <returns>The user IDs.</returns>
		public IEnumerable<Int32> FindUserIdsByUuids(IEnumerable<String> userUuids = null, IEnumerable<String> groupUuids = null)
		{
			using var dbCtx = CreateDbContext();

			var result = new HashSet<Int32>();

			if (userUuids != null)
			{
				foreach (var chunk in userUuids.ToSizedChunks(50, 15))
				{
					var uIds = chunk.ToArray();
					result.UnionWith(dbCtx.Entities<UserEntity>().Where(e => uIds.Contains(e.GUID)).Select(e => e.Id).Distinct());
				}
			}

			if (groupUuids != null)
			{
				foreach (var chunk in groupUuids.ToSizedChunks(50, 15))
				{
					var gIds = chunk.ToArray();
					result.UnionWith(dbCtx.Entities<AccessGroupEntity>().Where(e => gIds.Contains(e.GUID)).Include(e => e.AccessUser2GroupEntities).SelectMany(e => e.AccessUser2GroupEntities.Select(u2g => u2g.UserFk)).Distinct());
				}
			}

			return result.ToArray();
		}
	}

	/// <summary>
	/// Contains the AccessRightDescriptorLogic
	/// </summary>
	[Export(typeof(IUserRoleLogic))]
	public class UserRoleLogic : IUserRoleLogic
	{
		/// <summary>
		/// UserHasRole by FRM_ACCESSROLE2USER_V
		/// </summary>
		/// <param name="userId"></param>
		/// <param name="roleId"></param>
		/// <returns></returns>
		public Boolean UserHasRole(Int32 userId, Int32 roleId)
		{
			if (userId <= 0)
			{
				throw new ArgumentOutOfRangeException("userId");
			}

			if (roleId <= 0)
			{
				throw new ArgumentOutOfRangeException("roleId");
			}

			try
			{
				using var dbContext = new DbContext(ModelBuilder.DbModel);
				var entity = dbContext.Entities<AccessRole2UserVEntity>().FirstOrDefault(i => i.UserFk == userId && i.AccessroleFk == roleId);
				return entity != null;
			}
			catch (Exception ex)
			{
				throw new BusinessLayerException(ex.Message, ex) { ErrorCode = (Int32)ExceptionErrorCodes.BusinessFatalError };
			}
		}
	}
}
