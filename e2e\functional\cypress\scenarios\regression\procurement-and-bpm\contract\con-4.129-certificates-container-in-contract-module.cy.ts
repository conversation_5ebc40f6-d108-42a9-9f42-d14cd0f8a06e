import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _package, _procurementContractPage, _procurementPage, _projectPage, _schedulePage, _validate, _wizardCreateRequest } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import { PACKAGE_TOTAL_TRANSLATION } from "cypress/pages/variables";

const COMMENT = 'COMMENT-' + _common.generateRandomString(3)

let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let CONTAINER_COLUMNS_CONTRACT
let CONTAINERS_CONTRACT
let CONTAINER_COLUMNS_CERTIFICATES

describe("PCM- 4.129 | Certificates container in Contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/con-4.129-certificates-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }

            CONTAINER_COLUMNS_CERTIFICATES = this.data.CONTAINER_COLUMNS.CERTIFICATES

        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.projectfk, CONTAINER_COLUMNS_CONTRACT.ProjectFkProjectName, CONTAINER_COLUMNS_CONTRACT.code, CONTAINER_COLUMNS_CONTRACT.businesspartnerfk], cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create and delete certificates", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CERTIFICATES, app.FooterTab.CERTIFICATES, 1);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_CERTIFICATES, CONTAINER_COLUMNS_CERTIFICATES)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.BPD_CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.CIS_CERTIFICATE1)
        _common.select_activeRowInContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.delete_recordFromContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Validate is record deleted in certificates", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('CONTRACT_CODE'))
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CERTIFICATES, app.FooterTab.CERTIFICATES, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordDeleted(cnt.uuid.CONTRACT_CERTIFICATES, CONTAINERS_CONTRACT.CIS_CERTIFICATE1)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Validate duplicate warning message in certificates", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CERTIFICATES, app.FooterTab.CERTIFICATES, 1);
            
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.REQUIRED_AMOUNT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.BPD_CERTIFICATE_TYPE_FK)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.BPD_CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.CIS_CERTIFICATE1)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton_ifExists(CommonLocators.CommonKeys.CANCEL)
        _common.waitForLoaderToDisappear()
        _validate.validate_textIn_invalidCell(CONTAINERS_CONTRACT.VALIDATION_TEXT)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.BPD_CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.CIS_CERTIFICATE2)
        _common.select_activeRowInContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.REQUIRED_AMOUNT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.BPD_CERTIFICATE_TYPE_FK)
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.BPD_CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.CIS_CERTIFICATE3)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })

    it("TC - Edit record in customizing", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.MASTERDATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_TYPES, app.FooterTab.DATA_TYPES)

        })
        _common.clear_subContainerFilter(cnt.uuid.DATA_TYPES)
        _common.search_inSubContainer(cnt.uuid.DATA_TYPES, CONTAINERS_CONTRACT.CERTIFICATE_TYPE)
        _common.select_rowHasValue(cnt.uuid.DATA_TYPES, CONTAINERS_CONTRACT.CERTIFICATE_TYPE)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.MASTERDATA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DATA_RECORDS, app.FooterTab.DATA_RECORDS)
        })
        _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
        _common.search_inSubContainer(cnt.uuid.DATA_RECORDS, CONTAINERS_CONTRACT.CIS_CERTIFICATE2)
        _common.select_rowInContainer(cnt.uuid.DATA_RECORDS)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_VALUED, CommonLocators.CommonKeys.UNCHECK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.DATA_RECORDS)
        _common.search_inSubContainer(cnt.uuid.DATA_RECORDS, CONTAINERS_CONTRACT.CIS_CERTIFICATE3)
        _common.select_rowInContainer(cnt.uuid.DATA_RECORDS)
        _common.set_cellCheckboxValue(cnt.uuid.DATA_RECORDS, app.GridCells.IS_VALUED, CommonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })

    it("TC - Verify required amount field for check uncheck isvalued field in certificate", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('CONTRACT_CODE'))
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CERTIFICATES, app.FooterTab.CERTIFICATES, 1);
            _common.setup_gridLayout(cnt.uuid.CONTRACT_CERTIFICATES, CONTAINER_COLUMNS_CERTIFICATES)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_CERTIFICATES, CONTAINERS_CONTRACT.CIS_CERTIFICATE2)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _validate.verify_inputFieldVisibility(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.REQUIRED_AMOUNT, commonLocators.CommonKeys.NOT_VISIBLE)
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.search_inSubContainer(cnt.uuid.CONTRACT_CERTIFICATES, CONTAINERS_CONTRACT.CIS_CERTIFICATE3)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.REQUIRED_AMOUNT, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.AMOUNT)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, COMMENT)
        _common.edit_containerCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.GUARANTEE_COST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.GUARANTEE_COST)
        _common.edit_containerCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.GUARANTEE_COST_PERCENT, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.GUARANTEE_COST_PERCENT)
        _common.edit_containerCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(commonLocators.CommonKeys.CURRENT_SMALL))
        _common.edit_containerCell(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.VALID_TO, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL,2))
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });
});