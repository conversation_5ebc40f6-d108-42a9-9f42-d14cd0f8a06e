/**
 * $Id$
 * Copyright (c) RIB Software SE
 */

(function () {
	'use strict';
	var moduleName = 'sales.billing';
	var salesBillingModule = angular.module(moduleName);

	salesBillingModule.factory('salesBillingCreateCancellationBillDialogService',
		['globals', 'platformRuntimeDataService', '$injector', '$http', '_', 'salesBillingCreateBillDialogBaseServiceProvider', 'salesBillingService',
			function (globals, platformRuntimeDataService, $injector, $http, _, salesBillingCreateBillDialogBaseServiceProvider, salesBillingService) {

				var apiService = salesBillingCreateBillDialogBaseServiceProvider.getApiService();

				// API for create cancellation bill
				var api = {
					createCancellationBill: function createCancellationBill(payload) {
						return $http.post(globals.webApiBaseUrl + 'sales/billing/' + 'createcancellationbill', payload);
					}
				};

				function setTotalQuantityOptionByType(entity, typeEntity) {
					if (_.isObject(entity) && _.isObject(typeEntity)) {
						var IsFinalBill = typeEntity.IsFinalInvoiceCorrection && !typeEntity.IsSingle && !typeEntity.Isprogress; // TODO: took over from credit-memo-patch.diff; review needed
						var IsProgressBill = typeEntity.Isprogress;
						entity.IsTotalQuantity = IsFinalBill;
						platformRuntimeDataService.readonly(entity, [{field: 'IsTotalQuantity', readonly: !(IsFinalBill || IsProgressBill)}]);
					}
				}

				var service = salesBillingCreateBillDialogBaseServiceProvider.getInstance({
					titleKey: 'sales.billing.createCancellationBillTitle',
					formConfig: {
						fid: 'sales.billing.createCancellationBillModal',
						version: '0.1.0'
					},
					customizeInitData: function customizeInitData(initDataItem) {
						if (_.isObject(initDataItem)) {
							// init total quantity option
							setTotalQuantityOptionByType(initDataItem, initDataItem.TypeEntity);
						}
					},
					customizeFormConfig: function customizeFormConfig(formConfig) {
						if (_.has(formConfig, 'groups[0].attributes') && _.has(formConfig, 'rows')) {
							// add "Total Quantity" checkbox
							formConfig.groups[0].attributes.push('istotalquantity');
							formConfig.rows.push({
								gid: 'baseGroup',
								rid: 'istotalquantity',
								model: 'IsTotalQuantity',
								sortOrder: 6,
								label: 'Total Quantity',
								label$tr$: 'sales.billing.createCancellationBill.IsTotalQuantity',
								type: 'boolean'
							});
						}
					},
					onTypeChanged: function onTypeChanged(entity /* , newTypeId */) {
						setTotalQuantityOptionByType(entity, entity.TypeEntity);
					},
					dialogHandlers: {
						handleOk: function handleOk(selectedBill, newBillCreationInfo) {
							var postData = apiService.preparePayload(selectedBill.Id, newBillCreationInfo);

							// add total quantity option
							postData.creationData.IsTotalQuantity = newBillCreationInfo.IsTotalQuantity;

							// server request
							return api.createCancellationBill(postData).then(function (response) {
								var newCancellationBill = response.data.BilHeader;
								salesBillingService.addNewBill(newCancellationBill).then(function () {
									$injector.get('salesBillingSchemaService').recalculateBillingSchema();
								});
								return true;
							});
						}
					}
				});

				service.showCreateCancellationBillDialog = service.showCreateBillDialog;

				return service;
			}
		]);
})();