
import { _businessPartnerPage, _commonAPI, _projectPage, _common, _controllingUnit, _package, _sidebar, _mainView, _validate, _modalView, _rfqPage, _saleContractPage, _procurementContractPage } from 'cypress/pages';
import { cnt, tile, app, sidebar, commonLocators, btn } from 'cypress/locators';
import CommonLocators from 'cypress/locators/common-locators';

const PROJECT_NO = _common.generateRandomString(3);
const PROJECT_DESC = _common.generateRandomString(3);
const PACKAGE_DESC = _common.generateRandomString(4);

let PROJECTS_PARAMETERS
let CONTAINERS_PACKAGE
let CONTAINER_COLUMNS_PACKAGE
let PACKAGE_PARAMETERS
let CONTAINER_COLUMNS_CONTRACT
let CONTAINERS_CONTRACT
let CONTRACT_PARAMETER
let CONTAINER_COLUMNS_PROCUREMENT_EVENT
let CONTAINERS_PROCUREMENT_EVENT


describe('PCM- 4.226 | Procurement event container in contract module', () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture('pcm/con-4.226-procurement-event-container-in-contract-module.json').then((data) => {
            this.data = data;
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
            PROJECTS_PARAMETERS = {
                [commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
                [commonLocators.CommonLabels.NAME]: PROJECT_DESC,
                [commonLocators.CommonLabels.CLERK]: CONTAINERS_CONTRACT.CLERK
            };
            CONTAINER_COLUMNS_PACKAGE = this.data.CONTAINER_COLUMNS.PACKAGE
            CONTAINERS_PACKAGE = this.data.CONTAINERS.PACKAGE;

            CONTAINER_COLUMNS_PROCUREMENT_EVENT = this.data.CONTAINER_COLUMNS.CONTAINER_COLUMNS_PROCUREMENT_EVENT
            CONTAINERS_PROCUREMENT_EVENT = this.data.CONTAINERS.PROCUREMENT_EVENT;

        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear()
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT)
                _common.waitForLoaderToDisappear()
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            })
            _common.waitForLoaderToDisappear();
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        })
    });
    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

    it("TC - Create new package and procurement event", function () {
        PACKAGE_PARAMETERS = {
            [commonLocators.CommonLabels.PROJECT_NAME]: Cypress.env('API_PROJECT_NUMBER_1'),
            [commonLocators.CommonLabels.PROCUREMENT_STRUCTURE_SMALL]: commonLocators.CommonKeys.MATERIAL,
            [commonLocators.CommonLabels.DESCRIPTION]: PACKAGE_DESC
        };
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PACKAGE)
        _common.openTab(app.TabBar.PACKAGE).then(() => {
            _common.setDefaultView(app.TabBar.PACKAGE)
            _common.select_tabFromFooter(cnt.uuid.PACKAGE, app.FooterTab.PACKAGE, 0)
            _common.setup_gridLayout(cnt.uuid.PACKAGE, CONTAINER_COLUMNS_PACKAGE)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH)
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.clear_subContainerFilter(cnt.uuid.PACKAGE)
        _common.create_newRecord(cnt.uuid.PACKAGE)
        _package.enterRecord_toCreatePackageWithStructure(PACKAGE_PARAMETERS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PACKAGE, app.GridCells.CODE, "PACKAGE_CODE")
        _common.waitForLoaderToDisappear
        _common.openTab(app.TabBar.PACKAGE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENT_EVENT, app.FooterTab.PROCUREMENT_EVENTS, 2);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENT_EVENT)
        });
        _common.maximizeContainer(cnt.uuid.PROCUREMENT_EVENT)
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENT_EVENT)
        _common.create_newRecord(cnt.uuid.PROCUREMENT_EVENT)
        _common.edit_dropdownCellWithCaret(cnt.uuid.PROCUREMENT_EVENT, app.GridCells.PRC_EVENT_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINERS_PROCUREMENT_EVENT.EVENT_TYPE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - Create contract and edit procurement events record', function () {
        CONTRACT_PARAMETER = {
            [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.ADOLF_KOCH
        }
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT);
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.projectfk, CONTAINER_COLUMNS_CONTRACT.packagefk], cnt.uuid.PROCUREMENTCONTRACT)
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to save and modal to appear
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton_ifExists(CONTAINERS_CONTRACT.OK)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
    })

    it('TC - Validate procurement events record', function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, app.FooterTab.PROCUREMENT_EVENTS);
        });
        _validate.verify_toolbarButton_visibility(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, btn.ToolBar.ICO_REC_NEW)
        _validate.verify_toolbarButton_visibility(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, btn.ToolBar.ICO_DELETE)
        _validate.verify_recordNotPresentInContainer(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, CONTAINERS_PROCUREMENT_EVENT.EVENT_TYPE)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PACKAGE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env("PACKAGE_CODE"))
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(CONTAINERS_CONTRACT.YES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton_ifExists(CONTAINERS_CONTRACT.YES)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, app.FooterTab.PROCUREMENT_EVENTS);
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS)
        });
        _common.select_rowInContainer(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS)
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, app.GridCells.START_OVERTWRITE)
        _common.edit_containerCell(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, app.GridCells.START_OVERTWRITE, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL, 3))
        _common.edit_containerCell(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, app.GridCells.END_OVERWRITE, app.InputFields.INPUT_GROUP_CONTENT, _common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL, 6))
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, app.GridCells.START_OVERTWRITE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
    });

    it("TC - Assert record in procurement events container in contract module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, app.FooterTab.PROCUREMENT_EVENTS);
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS)
        });
        _common.select_rowInContainer(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS)
        _common.assert_cellData(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, app.GridCells.START_OVERTWRITE, _common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL, 3))
        _common.assert_cellData(cnt.uuid.CONTRACT_PROCUREMENT_EVENTS, app.GridCells.END_OVERWRITE, _common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL, 6))
    })

    it("TC - Assert record in procurement events container in package module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PACKAGE);
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("PACKAGE_CODE"))
        _common.openTab(app.TabBar.PACKAGE).then(() => {
            _common.setDefaultView(app.TabBar.PACKAGE)
            _common.select_tabFromFooter(cnt.uuid.PACKAGE, app.FooterTab.PACKAGE, 0)
        });
        _common.select_rowInContainer(cnt.uuid.PACKAGE)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PACKAGE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENT_EVENT, app.FooterTab.PROCUREMENT_EVENTS);
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENT_EVENT)
        });
        _common.assert_cellData(cnt.uuid.PROCUREMENT_EVENT, app.GridCells.START_OVERTWRITE, _common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL, 3))
        _common.assert_cellData(cnt.uuid.PROCUREMENT_EVENT, app.GridCells.END_OVERWRITE, _common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL, 6))
    })
});
