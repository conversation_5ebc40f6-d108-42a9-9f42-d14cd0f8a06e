/*
 * Copyright(c) RIB Software GmbH
 */
import { IInitializationContext } from '@libs/platform/common';
import { SalesContractChangeCertificateStatusService } from './sales-contract-change-certificate-status.service';
import { SalesContractActualCertificateDataService } from '../../services/sales-contract-actual-certificate-data.service';
import { SalesContractContractsDataService } from '../../services/sales-contract-contracts-data.service';

jest.mock('../../services/sales-contract-contracts-data.service');
jest.mock('../../services/sales-contract-actual-certificate-data.service');

describe('SalesContractChangeCertificateStatusService', () => {
    let service: SalesContractChangeCertificateStatusService;

    beforeEach(() => {
        service = new SalesContractChangeCertificateStatusService();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should call changeCertificateStatus with correct options', async () => {
        const mockStatusService = {
            changeCertificateStatus: jest.fn().mockResolvedValue('changed')
        };
        const mockContext = {
            injector: {
                get: jest.fn((token) => {
                    if (token === SalesContractActualCertificateDataService) {
                        return {};
                    }

                    if (token === SalesContractContractsDataService) {
                        return {};
                    }
                    return undefined;
                })
            },
            lazyInjector: {
                inject: jest.fn().mockResolvedValue(mockStatusService)
            }
        } as unknown as IInitializationContext;

        const result = await service.execute(mockContext);
        expect(mockContext.injector.get).toHaveBeenCalledWith(SalesContractActualCertificateDataService);
        expect(mockContext.injector.get).toHaveBeenCalledWith(SalesContractContractsDataService);
        expect(mockContext.lazyInjector.inject).toHaveBeenCalled();
        expect(mockStatusService.changeCertificateStatus).toHaveBeenCalled();
        expect(result).toBe('changed');
    });
});