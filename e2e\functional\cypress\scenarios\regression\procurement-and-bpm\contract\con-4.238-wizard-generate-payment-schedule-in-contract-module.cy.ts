import { _common, _controllingUnit, _package, _sidebar,_boqPage,_procurementContractPage,_projectPage, _mainView, _validate, _modalView, _saleContractPage } from 'cypress/pages';
import { cnt, tile, app, sidebar, commonLocators, btn } from 'cypress/locators';
import common from 'mocha/lib/interfaces/common';
import type { DataCells } from 'cypress/pages/interfaces.d.ts'


const CU_DESCRIPTION = 'CU-DESC-' + _common.generateRandomString(3);
const PROJECT_NO=_common.generateRandomString(2)
const PROJECT_DESC="PRDESC-" + _common.generateRandomString(3);
const INCREMENTEDDATE = 8


let PROJECTS_PARAMETERS,CONTROLLING_UNIT_PARAMETERS,CONTRACT_PARAMETER: DataCells;
let CONTAINERS_BOQ;
let CONTAINERS_PACKAGE;
let CONTAINERS_CONTROLLING_UNIT;

let CONTAINER_COLUMNS_BOQ,CONTAINER_COLUMNS_BOQ_STRUCTURE;
let CONTAINER_COLUMNS_CONTROLLING_UNIT;
let CONTAINER_COLUMNS_ITEMS;
let CONTAINER_COLUMNS_PROCUREMENT_BOQ;
let CONTAINER_COLUMNS_PACKAGE,CONTAINERS_CONTRACT;
let CONTAINER_COLUMNS_PAYMENT_SCHEDULE;
let GENERATE_PAYMENT_SCHEDULE: DataCells

describe('PCM- 4.238 | Wizard generate payment schedule in contract module', () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

    before(function () {
        cy.fixture('pcm/con-4.238-wizard-generate-payment-schedule-in-contract-module.json').then((data) => {
            this.data = data;
            CONTAINERS_BOQ = this.data.CONTAINERS.BOQ;
            CONTAINERS_PACKAGE = this.data.CONTAINERS.PACKAGE
            CONTAINERS_CONTROLLING_UNIT = this.data.CONTAINERS.CONTROLLING_UNIT;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;

            CONTAINER_COLUMNS_BOQ = this.data.CONTAINER_COLUMNS.BOQ;
            CONTAINER_COLUMNS_CONTROLLING_UNIT = this.data.CONTAINER_COLUMNS.CONTROLLING_UNIT
            CONTAINER_COLUMNS_PROCUREMENT_BOQ = this.data.CONTAINER_COLUMNS.PROCUREMENT_BOQ
            CONTAINER_COLUMNS_PACKAGE = this.data.CONTAINER_COLUMNS.PACKAGE
            CONTAINER_COLUMNS_ITEMS = this.data.CONTAINER_COLUMNS.ITEMS
            CONTAINER_COLUMNS_PAYMENT_SCHEDULE = this.data.CONTAINER_COLUMNS.PAYMENT_SCHEDULE
            CONTAINER_COLUMNS_BOQ_STRUCTURE = this.data.CONTAINER_COLUMNS.BOQ_STRUCTURE

            CONTROLLING_UNIT_PARAMETERS = {
                [app.GridCells.DESCRIPTION_INFO]: CU_DESCRIPTION,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_CONTROLLING_UNIT.QUANTITY,
                [app.GridCells.UOM_FK]: CONTAINERS_CONTROLLING_UNIT.UOM,
            };

            GENERATE_PAYMENT_SCHEDULE = {
                [commonLocators.CommonKeys.RADIO]: CONTAINERS_PACKAGE.RADIO,
                [commonLocators.CommonLabels.TOTAL_OC_NET]: CONTAINERS_PACKAGE.TOTAL_OC,
                [commonLocators.CommonLabels.START_DATE]: _common.getDate("current"),
                [commonLocators.CommonLabels.END_DATE]: _common.getDate("incremented", INCREMENTEDDATE)
            }
            PROJECTS_PARAMETERS={
                [commonLocators.CommonLabels.PROJECT_NUMBER]:PROJECT_NO,
                [commonLocators.CommonLabels.NAME]:PROJECT_DESC,
                [commonLocators.CommonLabels.CLERK]:CONTAINERS_BOQ.CLERK
            }
            CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]:commonLocators.CommonKeys.MATERIAL_PO,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESSPARTNER
  
            };
        });

        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        _common.waitForLoaderToDisappear()
        
    });

    after(() => {
    	cy.LOGOUT();
    });

    it('TC - Create project', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT); 
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
        _common.setDefaultView(app.TabBar.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
        _common.create_newRecord(cnt.uuid.PROJECTS);
        _projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS);
        cy.SAVE(); 
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO).pinnedItem();
        _common.waitForLoaderToDisappear()
       
    
    })

    it('TC - Create new controlling unit', function () {

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTROLLING_UNITS);

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT, app.FooterTab.CONTROLLING_UNITS, 2);
            _common.setup_gridLayout(cnt.uuid.CONTROLLING_UNIT, CONTAINER_COLUMNS_CONTROLLING_UNIT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.CONTROLLING_UNIT)
        _controllingUnit.enterRecord_toCreateControllingUnit(CONTROLLING_UNIT_PARAMETERS);
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasValue(cnt.uuid.CONTROLLING_UNIT, app.GridCells.DESCRIPTION_INFO, CU_DESCRIPTION)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.CONTROLLING_UNIT, app.GridCells.CODE, "CNTSUBCODE")
        cy.log(Cypress.env("CNTSUBCODE"))
        _common.minimizeContainer(cnt.uuid.CONTROLLING_UNIT)

    });

    it("TC - Create new contract record ", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);

        _common.openTab(app.TabBar.CONTRACT).then(()=>{
        _common.setDefaultView(app.TabBar.CONTRACT)
        _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT,app.FooterTab.CONTRACTS,0)
        })
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD,PROJECT_NO)
        _common.waitForLoaderToDisappear()

        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT,0)
            _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(CONTRACT_PARAMETER);
    cy.SAVE();
     _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
    cy.SAVE()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.STRUCTURE_CODE, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT,"D");
		cy.SAVE();
		cy.wait(3000)
		_common.clickOn_modalFooterButton(btn.ButtonText.YES)
		cy.SAVE();
		_common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "Contract_Code")
    })

    it('TC - Creation of procurement BoQ for the selected contract', function () {
      
        _common.openTab(app.TabBar.CONTRACT).then(function () {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQS, app.FooterTab.PROCUREMENT_BOQ);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQS, CONTAINER_COLUMNS_PROCUREMENT_BOQ);
        });
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT_BOQS);
        _common.waitForLoaderToDisappear()
        _package.create_ProcuremenBoQswithNewReocrd(PROJECT_DESC, CONTAINERS_BOQ.PROCUREMENT_STRUCTURE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    });

    it('TC - Creation of BoQ for the selected contract', function () {

        _common.openTab(app.TabBar.CONTRACT).then(function () {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, app.FooterTab.BOQ_STRUCTURE);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINER_COLUMNS_BOQ_STRUCTURE);
            _common.set_columnAtTop([CONTAINER_COLUMNS_BOQ_STRUCTURE.briefinfo,CONTAINER_COLUMNS_BOQ_STRUCTURE.quantity, CONTAINER_COLUMNS_BOQ_STRUCTURE.basuomfk, CONTAINER_COLUMNS_BOQ_STRUCTURE.price], cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
      
        });
        _common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE);
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE) 
        _boqPage.enterRecord_toCreateBoQStructureUnderpackage(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE, CONTAINERS_BOQ.DESCRIPTION, CONTAINERS_BOQ.QUANTITY, CONTAINERS_BOQ.UNIT_RATE,  CONTAINERS_BOQ.UOM)
        cy.SAVE();

       
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT_BOQSTRUCTURE)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS)
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEMS);
            _common.set_columnAtTop([CONTAINER_COLUMNS_ITEMS.quantity, CONTAINER_COLUMNS_ITEMS.basuomfk, CONTAINER_COLUMNS_ITEMS.price], cnt.uuid.ITEMSCONTRACT)
        })
         _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        _common.enterRecord_inNewRow(cnt.uuid.ITEMSCONTRACT, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PACKAGE.QUANTITY)
        _common.edit_containerCell(cnt.uuid.ITEMSCONTRACT, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT,CONTAINERS_PACKAGE.PRICE)
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT, app.GridCells.BAS_UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, "BAGS")
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.CONTRACT).then(function () {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_TOTALS, app.FooterTab.TOTALS); 
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.CONTRACT_TOTALS,CONTAINERS_PACKAGE.TOTAL)
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_TOTALS, app.GridCells.VALUE_NET,"VALUE_NET")
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_TOTALS, app.GridCells.GROSS,"VALUE_GROSS")
       
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to read cell data
    });

    it('TC - Generate payment schedule and verify wizard and code', function () {
        _common.openTab(app.TabBar.CONTRACT).then(function () {
            _common.set_containerLayoutUnderEditView(commonLocators.CommonKeys.LAYOUT_6)
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS); 
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.GENERATE_PAYMENT_SCHEDULE)
        _saleContractPage.enterRecord_toGeneratePaymentScheduleInContract(GENERATE_PAYMENT_SCHEDULE)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to read cell data
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(function () {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS); 
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(function () {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PAYMENT_SCHEDULE, app.FooterTab.PAYMENT_SCHEDULE,3);
            _common.waitForLoaderToDisappear()
            _common.setup_gridLayout(cnt.uuid.PAYMENT_SCHEDULE,CONTAINER_COLUMNS_PAYMENT_SCHEDULE)
        });
        _common.waitForLoaderToDisappear()
        _common.set_columnAtTop([CONTAINER_COLUMNS_PAYMENT_SCHEDULE.amountnet,CONTAINER_COLUMNS_PAYMENT_SCHEDULE.percentofcontract,CONTAINER_COLUMNS_PAYMENT_SCHEDULE.remaining,CONTAINER_COLUMNS_PAYMENT_SCHEDULE.amountgross,CONTAINER_COLUMNS_PAYMENT_SCHEDULE.amountgrossoc,CONTAINER_COLUMNS_PAYMENT_SCHEDULE.amountnetoc],cnt.uuid.PAYMENT_SCHEDULE)

        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PAYMENT_SCHEDULE)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PAYMENT_SCHEDULE, CONTAINERS_PACKAGE.CODE01)
        _common.saveCellDataToEnv(cnt.uuid.PAYMENT_SCHEDULE,app.GridCells.AMOUNT_NET,"PAYMENT1_NET")
        _common.select_rowHasValue(cnt.uuid.PAYMENT_SCHEDULE, CONTAINERS_PACKAGE.CODE02)
        _common.saveCellDataToEnv(cnt.uuid.PAYMENT_SCHEDULE,app.GridCells.AMOUNT_NET,"PAYMENT2_NET")
        _common.clickOn_cellHasValue(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.CODE, CONTAINERS_PACKAGE.CODE)
            
    });

    it("TC - Verify percent in payment schedule container", function () {

       
        _common.assert_cellDataByContent_inContainer(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.CODE, CONTAINERS_PACKAGE.CODE)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.AMOUNT_NET, Cypress.env("VALUE_NET"))
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.PERCENT_OF_CONTRACT, CONTAINERS_PACKAGE.PERCENT)
        _common.select_rowHasValue(cnt.uuid.PAYMENT_SCHEDULE, CONTAINERS_PACKAGE.CODE01)
        _common.assert_activeRow_cellDataByContent_inContainer(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.DESCRIPTION,CONTAINERS_PACKAGE.DESCRIPTION01)
        _common.waitForLoaderToDisappear()
       
        _validate.verify_amounts_inPaymentSchedule(cnt.uuid.PAYMENT_SCHEDULE, CONTAINERS_PACKAGE.PERCENT1,Cypress.env("VALUE_NET"),CONTAINERS_PACKAGE.PERCENT,app.GridCells.AMOUNT_NET)
        _validate.verify_amounts_inPaymentSchedule(cnt.uuid.PAYMENT_SCHEDULE, CONTAINERS_PACKAGE.PERCENT1,Cypress.env("VALUE_GROSS"),CONTAINERS_PACKAGE.PERCENT,app.GridCells.AMOUNT_GROSS)  
        _validate.verify_amounts_inPaymentSchedule(cnt.uuid.PAYMENT_SCHEDULE, CONTAINERS_PACKAGE.PERCENT1,Cypress.env("VALUE_NET"),CONTAINERS_PACKAGE.PERCENT,app.GridCells.AMOUNT_NET_OC)
        _validate.verify_amounts_inPaymentSchedule(cnt.uuid.PAYMENT_SCHEDULE,CONTAINERS_PACKAGE.PERCENT1, Cypress.env("VALUE_GROSS"),CONTAINERS_PACKAGE.PERCENT,app.GridCells.AMOUNT_GROSS_OC)
        _validate.verify_isRecordSubstractTwoValues(cnt.uuid.PAYMENT_SCHEDULE, Cypress.env("VALUE_NET"),Cypress.env("PAYMENT1_NET"),app.GridCells.REMAINING)

        _common.select_rowInContainer(cnt.uuid.PAYMENT_SCHEDULE)
        _common.clickOn_cellHasValue(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.CODE, CONTAINERS_PACKAGE.CODE02)
        _common.assert_activeRow_cellDataByContent_inContainer(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.DESCRIPTION, CONTAINERS_PACKAGE.DESCRIPTION02)
        _common.waitForLoaderToDisappear()
        _validate.verify_amounts_inPaymentSchedule(cnt.uuid.PAYMENT_SCHEDULE, CONTAINERS_PACKAGE.PERCENT1,Cypress.env("VALUE_NET"),CONTAINERS_PACKAGE.PERCENT,app.GridCells.AMOUNT_NET)
        _validate.verify_amounts_inPaymentSchedule(cnt.uuid.PAYMENT_SCHEDULE, CONTAINERS_PACKAGE.PERCENT1,Cypress.env("VALUE_GROSS"),CONTAINERS_PACKAGE.PERCENT,app.GridCells.AMOUNT_GROSS)  
        _validate.verify_amounts_inPaymentSchedule(cnt.uuid.PAYMENT_SCHEDULE, CONTAINERS_PACKAGE.PERCENT1,Cypress.env("VALUE_NET"),CONTAINERS_PACKAGE.PERCENT,app.GridCells.AMOUNT_NET_OC)
        _validate.verify_amounts_inPaymentSchedule(cnt.uuid.PAYMENT_SCHEDULE,CONTAINERS_PACKAGE.PERCENT1, Cypress.env("VALUE_GROSS"),CONTAINERS_PACKAGE.PERCENT,app.GridCells.AMOUNT_GROSS_OC)
        _validate.verify_isRecordSubstractTwoValues(cnt.uuid.PAYMENT_SCHEDULE,Cypress.env("PAYMENT1_NET"),Cypress.env("PAYMENT1_NET"), app.GridCells.REMAINING)

        _common.minimizeContainer(cnt.uuid.PAYMENT_SCHEDULE)

    });

    it('TC -  If selected contract has payment schedule isdone=true then contract should not allow to run wizard', function () {
        _common.openTab(app.TabBar.CONTRACT).then(function () {
            _common.select_tabFromFooter(cnt.uuid.PAYMENT_SCHEDULE, app.FooterTab.PAYMENT_SCHEDULE);
    
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PAYMENT_SCHEDULE)
        _common.clickOn_cellHasValue(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.CODE, CONTAINERS_PACKAGE.CODE01)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.IS_DONE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasValue(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.CODE,CONTAINERS_PACKAGE.CODE02)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.PAYMENT_SCHEDULE, app.GridCells.IS_DONE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.GENERATE_PAYMENT_SCHEDULE)
        _common.waitForLoaderToDisappear()
        _validate.verify_WizardMessage(CONTAINERS_PACKAGE.WIZARD_MSG)
        _common.waitForLoaderToDisappear()
    });

});
