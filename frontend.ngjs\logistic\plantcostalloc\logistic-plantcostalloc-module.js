﻿/*
 * $Id$
 * Copyright (c) RIB Software SE
 */

(function (angular) {
	/* global globals */
	'use strict';

	const moduleName = 'logistic.plantcostalloc';

	angular.module(moduleName, []);
	globals.modules.push(moduleName);

	/*
	 ** Module definition.
	 */
	angular.module(moduleName).config(['mainViewServiceProvider',
		function (mainViewServiceProvider) {

			const wizardData = [
				{
					serviceName: 'logisticPlantCostAllocationSidebarWizardService',
					wizardGuid: '43c0e15ad5f14b9864fbe5fc27cc676e',
					methodName: 'createRecordsForReserveBillingSheets',
					canActivate: true
				},
				{
					serviceName: 'logisticPlantCostAllocationSidebarWizardService',
					wizardGuid: '53c0b15ad5f14b9864fbe5fb28cc678e',
					methodName: 'createJobForBillingSheets',
					canActivate: true
				},
				{
					serviceName: 'logisticPlantCostAllocationSidebarWizardService',
					wizardGuid: 'd9270ce787a949aa8084bf6660caf312',
					methodName: 'changeBillingSheetStatus',
					canActivate: true
				},
			];
			const options = {
				moduleName: moduleName,
				resolve: {
					loadDomains: ['platformSchemaService', 'logisticPlantCostAllocConstantValues', 'basicsConfigWizardSidebarService', function (platformSchemaService, logisticPlantCostAllocConstantValues, basicsConfigWizardSidebarService) {
						basicsConfigWizardSidebarService.registerWizard(wizardData);
						return platformSchemaService.getSchemas([
							logisticPlantCostAllocConstantValues.schemes.project,
							logisticPlantCostAllocConstantValues.schemes.billingSheet,
							logisticPlantCostAllocConstantValues.schemes.priceOrgInfo
						]);
					}],
					loadTranslation: ['platformTranslationUtilitiesService', function (platformTranslationUtilitiesService) {
						return platformTranslationUtilitiesService.loadTranslationsOfMainModules(['logistic', 'services', 'resource', 'cloud', 'basics', 'project', 'documents']);
					}]
				}
			};

			mainViewServiceProvider.registerModule(options);
		}
	]);

})(angular);
