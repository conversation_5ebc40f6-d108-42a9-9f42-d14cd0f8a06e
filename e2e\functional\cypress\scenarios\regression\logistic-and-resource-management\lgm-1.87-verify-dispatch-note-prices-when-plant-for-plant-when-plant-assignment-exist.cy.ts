import cypress from "cypress";
import apiConstantData from "cypress/constantData/apiConstantData";
import { tile, app, cnt, sidebar, commonLocators, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _estimatePage, _validate, _boqPage, _logesticPage, _controllingUnit, _projectPage, _commonAPI } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const PLANT_CODE1 = _common.generateRandomString(3);
const PLANT_DESCRIPTION1 = _common.generateRandomString(3);
const PLANT_CODE2 = _common.generateRandomString(3);
const PLANT_DESCRIPTION2 = _common.generateRandomString(3);
const PLANT_CODE3 = _common.generateRandomString(3);
const PLANT_DESCRIPTION3 = _common.generateRandomString(3);
const CONDITION_CODE = _common.generateRandomString(3)
const CONDITION_DESC = _common.generateRandomString(3)
const DISPATCH_HDESC1 = _common.generateRandomString(3)
const PLANT_TYPE = _common.generateRandomString(3)
const RENTAL = "AA-" + _common.generateRandomString(3)
const DESC_WORK = _common.generateRandomString(3)
const PLANT_PRICE_LIST = "AA-" + _common.generateRandomString(3)
const LOGISTICB_JOB1 = _common.generateRandomString(3)

let CONTAINER_PROJECT,
    OPERATION_TYPE_PARAMETER_WORK: DataCells
let CONTAINER_DATA_RECORD
let CONTAINERS_CONTROLLING_UNITS,
    CONTAINER_COLUMNS_CONTROLLING_UNITS,
    CONTROLLING_UNIT_SUB_PARAMETERS1, CONTROLLING_UNIT_MAIN_PARAMETERS
let CONTAINER_COLUMNS_CONTROLLING_UNIT2;
let CONTROLLING_UNIT_SUB_PARAMETERS2: DataCells,
    ALLOCATION_FOR_PLANTS_PARAMETER: DataCells
let MODAL_PLANT_ALLOCATION
let PLANT_PARAMETERS1,
    PLANT_PARAMETERS2, PLANT_PARAMETERS3, DATA_RECORD_PARAMETER1: DataCells
let CONTAINERS_PLANT, CONTAINER_OPERATION_TYPES,
    CONTAINER_COLUMNS_PLANT,
    CONTAINERS_PLANT_PRICE_LISTS
let CONTAINER_COLUMNS_JOBS;
let CONTAINER_JOBS;
let CONTAINER_COLUMNS_CONDITIONS
let CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS
let CONTAINER_COLUMNS_DISPATCHING_HEADER,
    CONTAINERS_DISPATCHING_HEADER,
    CONTAINER_COLUMNS_ASSIGNMENTS

describe("LRM- 1.87 | Verify dispatch note prices when plant for plant when plant assignment exist", () => {
    beforeEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    afterEach(() => {
        cy.clearCookies();
        cy.clearLocalStorage();
        cy.WaitUntilLoaderComplete_Trial();
        cy.waitUntilDOMLoaded();
    });
    before(function () {
        cy.fixture("LRM/lgm-1.87-verify-dispatch-note-prices-when-plant-for-plant-when-plant-assignment-exist.json").then((data) => {
            this.data = data;
            CONTAINER_PROJECT = this.data.CONTAINERS.PROJECT;
            CONTAINERS_CONTROLLING_UNITS = this.data.CONTAINERS.CONTROLLING_UNITS;
            CONTAINER_COLUMNS_CONTROLLING_UNITS = this.data.CONTAINER_COLUMNS.CONTROLLING_UNITS;
            CONTAINER_COLUMNS_CONTROLLING_UNIT2 = this.data.CONTAINER_COLUMNS.CONTROLLING_UNIT2;
            CONTAINER_COLUMNS_PLANT = this.data.CONTAINER_COLUMNS.PLANT;
            CONTAINERS_PLANT = this.data.CONTAINERS.PLANT;
            CONTAINERS_PLANT_PRICE_LISTS = this.data.CONTAINERS.PLANT_PRICE_LISTS;
            MODAL_PLANT_ALLOCATION = this.data.MODAL.PLANT_ALLOCATION;
            CONTAINER_COLUMNS_JOBS = this.data.CONTAINER_COLUMNS.JOBS;
            CONTAINER_JOBS = this.data.CONTAINERS.JOBS;
            CONTAINER_COLUMNS_CONDITIONS = this.data.CONTAINER_COLUMNS.CONDITIONS;
            CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS = this.data.CONTAINERS.WORK_OPERATION_TYPE_CONDITIONS;
            CONTAINER_COLUMNS_DISPATCHING_HEADER = this.data.CONTAINER_COLUMNS.DISPATCHING_HEADER;
            CONTAINERS_DISPATCHING_HEADER = this.data.CONTAINERS.DISPATCHING_HEADER;
            CONTAINER_DATA_RECORD = this.data.CONTAINERS.DATA_RECORD;
            CONTAINER_OPERATION_TYPES = this.data.CONTAINERS.OPERATION_TYPES;
            CONTAINER_COLUMNS_ASSIGNMENTS = this.data.CONTAINER_COLUMNS.ASSIGNMENTS;
            PLANT_PARAMETERS1 = {
                [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION1,
                [app.GridCells.UOM_FK]: CONTAINERS_PLANT.UOM,
                [app.GridCells.PLANT_GROUP_FK]: CONTAINERS_PLANT.GROUP,
                [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
                [app.GridCells.PLANT_TYPE_FK]: RENTAL,
                [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
                [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
            }
            PLANT_PARAMETERS2 = {
                [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION2,
                [app.GridCells.UOM_FK]: CONTAINERS_PLANT.UOM,
                [app.GridCells.PLANT_GROUP_FK]: CONTAINERS_PLANT.GROUP,
                [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
                [app.GridCells.PLANT_TYPE_FK]: RENTAL,
                [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
                [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
            }
            PLANT_PARAMETERS3 = {
                [app.GridCells.DESCRIPTION_INFO]: PLANT_DESCRIPTION3,
                [app.GridCells.UOM_FK]: CONTAINERS_PLANT.UOM,
                [app.GridCells.PLANT_GROUP_FK]: CONTAINERS_PLANT.GROUP,
                [app.GridCells.PLANT_KIND_FK]: CONTAINERS_PLANT.PLANT_KIND,
                [app.GridCells.PLANT_TYPE_FK]: RENTAL,
                [app.GridCells.PROCUREMENT_STRUCTURE_FK]: CONTAINERS_PLANT.STRUCTURE,
                [app.GridCells.CLERK_RESPONSIBLE_FK]: CONTAINERS_PLANT.CLERK_RESPONSIBLE
            }

            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT)
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.waitForLoaderToDisappear()
            _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });
    })

    after(() => {
        cy.LOGOUT();
    });

    it("TC - Assign uom to the calender", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CALENDAR)
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CALENDER).then(() => {
            _common.select_tabFromFooter(cnt.uuid.SCHEDULE, app.FooterTab.CALENDARS)
        })
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.SCHEDULE)
        _common.search_inSubContainer(cnt.uuid.SCHEDULE, CONTAINER_DATA_RECORD.DE)
        _common.select_rowHasValue(cnt.uuid.SCHEDULE, CONTAINER_DATA_RECORD.DE)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.SCHEDULE, app.GridCells.BAS_UOM_DAY_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT.UOM);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

    })

    it("TC - API call to assign logged-in user a clerk", function () {
        _commonAPI.getULoggedInUserId(Cypress.env("USER_NAME"))
            .then(() => {
                _commonAPI.assignUserToClerk(Cypress.env("API_LOGGED_IN_USER_ID"), Cypress.env("USER_NAME"), apiConstantData.CONSTANT.SMIJ)
            })
    });

    it('TC - API: Create project', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)

        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });
    });

    it("TC - Create Controlling Units", function () {

        CONTROLLING_UNIT_SUB_PARAMETERS1 = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_PLANTMANAGEMENT]: ["true", "true"]
        }
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, CONTROLLING_UNIT_SUB_PARAMETERS1)
    })

    it("TC - Add plant list type record", function () {
        const PLANT_LIST_TYPE_PARAMETERS = {
            [app.GridCells.DESCRIPTION_INFO]: PLANT_TYPE
        }

        _commonAPI.createPlantListType(PLANT_LIST_TYPE_PARAMETERS)
    })

    it("TC - Create new plant price list record", function () {
        DATA_RECORD_PARAMETER1 = {
            [app.GridCells.CONTEXT_FK]: apiConstantData.ID.PLANT_CONTEXT_RIB_DEMO,
            [app.GridCells.PRICE_LIST_TYPE_FK]: Cypress.env('API_PLANT_LIST_TYPE_ID_1'),
            [app.GridCells.CURRENCY]: apiConstantData.ID.CURRENCY_EUR,
            [app.GridCells.UOM_FK]: apiConstantData.ID.UOM_DAY,
            [app.GridCells.DESCRIPTION_INFO]: PLANT_PRICE_LIST,
            [app.GridCells.CALCULATION_TYPE_FK]: apiConstantData.ID.CALCULATION_TYPE_AVERAGE_CATALOG_VALUE,
            [app.GridCells.PERCENT]: CONTAINER_DATA_RECORD.PERCENT,
            [app.GridCells.IS_MANUAL_EDIT_PLANT_MASTER]: "true"
        }
        _commonAPI.createPlantPriceList(DATA_RECORD_PARAMETER1)
    })

    it("TC - Create plant type", function () {
        let PLANT_TYPE_RENTAL_PARAMETER: DataCells = {
            [app.GridCells.IS_CLUSTER]: "true",
            [app.GridCells.DESCRIPTION_INFO]: RENTAL
        }
        _commonAPI.createPlantType(PLANT_TYPE_RENTAL_PARAMETER)
    })

    it("TC - Create work operation types and assign plant type to work operation ", function () {

        OPERATION_TYPE_PARAMETER_WORK = {
            [app.GridCells.IS_HIRE]: "true",
            [app.GridCells.UOM]: apiConstantData.ID.UOM_DAY,
            [app.GridCells.IS_LIVE]: "true",
            [app.GridCells.DESCRIPTION_INFO]: DESC_WORK

        }
        _commonAPI.createWorkOperationType(OPERATION_TYPE_PARAMETER_WORK).then(() => {
            let PLANT_TYPE_RENTAL: DataCells = {
                [app.GridCells.WORK_OPERATION_TYPE_FK]: Cypress.env(`API_WORK_OPERATION_TYPE_ID_1`),
                [app.GridCells.PLANT_TYPE_FK]: Cypress.env(`API_PLANT_TYPE_ID_1`)
            }
            _commonAPI.createWorkOperationPlantType(PLANT_TYPE_RENTAL)
        })
    })

    it("TC - Create plant in plant master and price list in commercial data -1", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PLANT_MASTER)
        _common.waitForLoaderToDisappear()
        cy.wait(8000)

        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.clear_searchInSidebar();

        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
            _common.setup_gridLayout(cnt.uuid.PLANT, CONTAINER_COLUMNS_PLANT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PLANT)
        _common.create_newRecord(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS1)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE1);
        _common.select_activeRowInContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()

        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT.UOM);
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PLANT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT.UOM);
        _common.select_activeRowInContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()

        cy.wait(1000)//required wait to save data
        _common.waitForLoaderToDisappear()
        cy.SAVE()

        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_CONTROLLING, app.FooterTab.CONTROLLING_UNIT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_CONTROLLING)
        });
        _common.create_newRecord(cnt.uuid.PLANT_CONTROLLING)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to appear dropdown
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_CONTROLLING, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICE_LISTS, app.FooterTab.PRICE_LISTS)
            _common.waitForLoaderToDisappear()
        });
        _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICE_LISTS)
        _common.create_newRecord(cnt.uuid.PLANT_PRICE_LISTS)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.UOM2)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_PRICE_LIST)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_3, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_4, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_5, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_6, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[0])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1)
        _common.saveCellDataToEnv(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_SUM, "PRICE_TOTAL_1")
        _common.waitForLoaderToDisappear()
    })

    it('TC - Create plant location record from wizard', function () {
        ALLOCATION_FOR_PLANTS_PARAMETER = {
            [commonLocators.CommonLabels.JOB]: Cypress.env('API_PROJECT_NUMBER_1'),
            [commonLocators.CommonLabels.ALLOCATED_FROM]: MODAL_PLANT_ALLOCATION.ALLOCATED_FROM,
            [app.GridCells.WORK_OPERATION_TYPE_FK]: DESC_WORK
        }
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS, 0)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
            _common.search_inSubContainer(cnt.uuid.PLANT, PLANT_DESCRIPTION1)
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION1)
        _common.openTab(app.TabBar.LOCATIONS).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_LOCATION, app.FooterTab.PLANT_LOCATION)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_INITIAL_ALLOCATION_FOR_PLANTS)
        _common.waitForLoaderToDisappear()
        _logesticPage.create_initialAllocationForPlants_fromWizard_byClass(ALLOCATION_FOR_PLANTS_PARAMETER, RENTAL)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION1)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create plant in plant master and price list in commercial data -2", function () {
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
            _common.setup_gridLayout(cnt.uuid.PLANT, CONTAINER_COLUMNS_PLANT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PLANT)
        _common.create_newRecord(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS2)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE2);
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT.UOM);
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to save data
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_CONTROLLING, app.FooterTab.CONTROLLING_UNIT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_CONTROLLING)
        });
        _common.create_newRecord(cnt.uuid.PLANT_CONTROLLING)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to appear dropdown
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_CONTROLLING, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICE_LISTS, app.FooterTab.PRICE_LISTS)
            _common.waitForLoaderToDisappear()
        });
        _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICE_LISTS)
        _common.create_newRecord(cnt.uuid.PLANT_PRICE_LISTS)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.UOM2)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_PRICE_LIST)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[1])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[1])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_3, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[1])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_4, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[1])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_5, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[1])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_6, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[1])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1)
        _common.saveCellDataToEnv(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_SUM, "PRICE_TOTAL_2")
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION2)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create plant in plant master and price list in commercial data -3", function () {
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
            _common.setup_gridLayout(cnt.uuid.PLANT, CONTAINER_COLUMNS_PLANT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PLANT)
        _common.create_newRecord(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _logesticPage.enterRecord_toCreatePlant(cnt.uuid.PLANT, PLANT_PARAMETERS3)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.PLANT, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, PLANT_CODE3);
        _common.select_activeRowInContainer(cnt.uuid.PLANT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT.UOM);
        _common.select_activeRowInContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT.UOM);
        _common.select_activeRowInContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to save data
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PLANT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_CONTROLLING, app.FooterTab.CONTROLLING_UNIT)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_CONTROLLING)
        });
        _common.create_newRecord(cnt.uuid.PLANT_CONTROLLING)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to appear dropdown
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_CONTROLLING, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`API_CNT_CODE_0`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.COMMERTIAL_DATA).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PLANT_PRICE_LISTS, app.FooterTab.PRICE_LISTS)
            _common.waitForLoaderToDisappear()
        });
        _common.clear_subContainerFilter(cnt.uuid.PLANT_PRICE_LISTS)
        _common.create_newRecord(cnt.uuid.PLANT_PRICE_LISTS)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.UOM2)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//wait required to load the row
        _common.edit_dropdownCellWithCaret(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_PRICE_LIST)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.IS_MANUAL, commonLocators.CommonKeys.CHECK)
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[2])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_2, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[2])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_3, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[2])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_4, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[2])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_5, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[2])
        _common.enterRecord_inNewRow(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_6, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PLANT_PRICE_LISTS.PORTION_VALUE[2])
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_1)
        _common.saveCellDataToEnv(cnt.uuid.PLANT_PRICE_LISTS, app.GridCells.PRICE_PORTION_SUM, "PRICE_TOTAL_3")
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_DESCRIPTION3)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_PLANT_STATUS)
        _common.changeStatus_fromModal(commonLocators.CommonKeys.AVAILABLE)
        _common.waitForLoaderToDisappear()
    })

    it('TC - Add sub plants to the assignment container', function () {
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT, app.FooterTab.PLANTS)
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PLANT_CODE1)

        _common.clear_subContainerFilter(cnt.uuid.PLANT)
        _common.select_rowHasValue(cnt.uuid.PLANT, PLANT_CODE1)
        _common.openTab(app.TabBar.PLANTS_OVERVIEW).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PLANT_ASSIGNMENT, app.FooterTab.ASSIGNMENTS, 1)
            _common.setup_gridLayout(cnt.uuid.PLANT_ASSIGNMENT, CONTAINER_COLUMNS_ASSIGNMENTS)
            _common.clear_subContainerFilter(cnt.uuid.PLANT_ASSIGNMENT)
        })
        _common.create_newRecord(cnt.uuid.PLANT_ASSIGNMENT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_ASSIGNMENT, app.GridCells.PLANT_2_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE2)
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_ASSIGNMENT, app.GridCells.PLANT_2_FK_DESCRIPTION)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.PLANT_ASSIGNMENT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PLANT_ASSIGNMENT, app.GridCells.PLANT_2_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PLANT_CODE3)
        _common.clickOn_activeRowCell(cnt.uuid.PLANT_ASSIGNMENT, app.GridCells.PLANT_2_FK_DESCRIPTION)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it('TC - API: Create project - B', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2')).pinnedItem();
        });
    })

    it("TC - Add Controlling Unit", function () {
        CONTROLLING_UNIT_SUB_PARAMETERS2 = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNITS.QUANTITY, CONTAINERS_CONTROLLING_UNITS.QUANTITY],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS],
            [app.GridCells.IS_BILLING_ELEMENT]: ["true", "true"],
            [app.GridCells.ISA_ACCOUNTING_ELEMENT]: ["true", "true"],
            [app.GridCells.IS_PLANNING_ELEMENT]: ["true", "true"],
            [app.GridCells.IS_TIMEKEEPING_ELEMENT]: ["true", "true"]
        }
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_2'), 2, CONTROLLING_UNIT_SUB_PARAMETERS2)
    })

    it("TC - Create job record in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.clear_searchInSidebar()
        cy.wait(1000)//required wait to enter value of project
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS, 0);
            _common.setup_gridLayout(cnt.uuid.JOBS, CONTAINER_COLUMNS_JOBS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_JOBS.controllingunitfk, CONTAINER_COLUMNS_JOBS.pricinggroupfk], cnt.uuid.JOBS)
        });
        _common.clear_subContainerFilter(cnt.uuid.JOBS)
        _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        _common.maximizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.JOBS, app.GridCells.SETTLED_BY_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINER_JOBS.SETTELED_BY)
        cy.wait(1000)//required wait to enable data input fields
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to save data
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.edit_dropDownWithInput_fromModal_byClass(app.ModalInputFields.CONTROLLING_UNIT_FK, Cypress.env(`API_CNT_CODE_1`), commonLocators.CommonKeys.GRID)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.select_activeRowInContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.minimizeContainer(cnt.uuid.JOBS)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create job record in logistic price condition module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_PRICE_CONDITION)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_PRICE_CONDITION).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONDITIONS, app.FooterTab.CONDITIONS);
            _common.setup_gridLayout(cnt.uuid.CONDITIONS, CONTAINER_COLUMNS_CONDITIONS)
            _common.clear_subContainerFilter(cnt.uuid.CONDITIONS)
        });
        _common.maximizeContainer(cnt.uuid.CONDITIONS)
        _common.create_newRecord(cnt.uuid.CONDITIONS)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, CONDITION_CODE)
        _common.enterRecord_inNewRow(cnt.uuid.CONDITIONS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, CONDITION_DESC)
        _common.set_cellCheckboxValue(cnt.uuid.CONDITIONS, app.GridCells.IS_HANDLING_CHARGE, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONDITIONS)
        _common.select_rowHasValue(cnt.uuid.CONDITIONS, CONDITION_CODE)
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.FooterTab.PLANT_CATALOG_PRICELISTS);
        });
        _common.create_newRecord(cnt.uuid.EQUIPMENT_CATALOG_PRICES)
        _common.edit_dropdownCellWithCaret(cnt.uuid.EQUIPMENT_CATALOG_PRICES, app.GridCells.EQUIPMENT_PRICE_LIST_FK, commonLocators.CommonKeys.LIST, PLANT_PRICE_LIST)

        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_CONDITION_ITEM, app.FooterTab.WORK_OPERATION_TYPE_CONDITIONS);
            _common.create_newRecord(cnt.uuid.PRICE_CONDITION_ITEM)
            _common.edit_dropdownCellWithInput(cnt.uuid.PRICE_CONDITION_ITEM, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, DESC_WORK)
        });
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to enable data input fields
        _common.edit_caretDropdown_fromModal_byClass(app.ModalInputFields.PRICING_GROUP_FK)
        _common.select_ItemFromPopUpList(commonLocators.CommonKeys.LIST, CONTAINERS_WORK_OPERATION_TYPE_CONDITIONS.PLANT_PRICING_GROUP)
        cy.wait(1000)//required wait to enable button
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Assign price condition in logistic job module", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.LOGISTIC_JOB)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.LOGISTIC_JOB).then(() => {
            _common.select_tabFromFooter(cnt.uuid.JOBS, app.FooterTab.JOBS);
            _common.clear_subContainerFilter(cnt.uuid.JOBS)
            _common.waitForLoaderToDisappear()
            _common.maximizeContainer(cnt.uuid.JOBS)
            _common.waitForLoaderToDisappear()
            _common.select_rowHasValue(cnt.uuid.JOBS, Cypress.env('API_PROJECT_NUMBER_2'))
        });
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.JOBS, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, LOGISTICB_JOB1)
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.PRICE_CONDITION_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONDITION_CODE)
        _common.edit_dropdownCellWithInput(cnt.uuid.JOBS, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINER_JOBS.BUSINESS_PARTNER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create record in dispatching notes", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.DISPATCHING_NOTES)
        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to open sidebar
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_2)
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
            _common.setup_gridLayout(cnt.uuid.DISPATCHING_HEADER, CONTAINER_COLUMNS_DISPATCHING_HEADER)
            _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.create_newRecord(cnt.uuid.DISPATCHING_HEADER)
        cy.wait(1000)
        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, DISPATCH_HDESC1)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.DISPATCHING_HEADER, app.GridCells.EFFECTIVE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, CONTAINERS_DISPATCHING_HEADER.EFFECTIVE_DATE[0])
        _common.clickOn_activeContainerButton(cnt.uuid.DISPATCHING_HEADER, btn.IconButtons.BTN_DEFAULT)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB1_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.DISPATCHING_HEADER, app.GridCells.JOB2_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, LOGISTICB_JOB1)
        _common.select_activeRowInContainer(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        cy.wait(1000)//wait required to load value
        _common.saveCellDataToEnv(cnt.uuid.DISPATCHING_HEADER, app.GridCells.CODE, "DISPATCH_CODE1")
        cy.wait(1000)//wait required to load value
        _common.minimizeContainer(cnt.uuid.DISPATCHING_HEADER)
    })

    it("TC - Create record in dispatching records", function () {
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, app.FooterTab.PERFORMING_JOB_PLANT_LOCATIONS, 0);
        });
        _common.select_rowHasValue(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, PLANT_CODE1)
        _common.dragDrop_dataToContainer(cnt.uuid.PERFORMING_JOB_PLANT_LOCATIONS, cnt.uuid.DISPATCHING_RECORD, app.GridCells.PLANT_FK, PLANT_CODE1)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_HEADER, app.FooterTab.DISPATCHING_HEADER);
        });
        _common.clear_subContainerFilter(cnt.uuid.DISPATCHING_HEADER)
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.DISPATCHING_HEADER, Cypress.env('API_PROJECT_NUMBER_1'))
        cy.wait(1000)//wait required to load search record
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_HEADER, Cypress.env("DISPATCH_CODE1"))
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PLANT_DELIVERY).then(() => {
            _common.select_tabFromFooter(cnt.uuid.DISPATCHING_RECORD, app.FooterTab.DISPATCHING_RECORDS);
        });
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_RECORD, PLANT_CODE1)
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, DESC_WORK)
        cy.wait(1000)//required wait to enable button
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_RECORD, PLANT_CODE2)
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, DESC_WORK)
        cy.wait(1000)//required wait to enable button
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.DISPATCHING_RECORD, PLANT_CODE3)
        _common.edit_dropdownCellWithCaret(cnt.uuid.DISPATCHING_RECORD, app.GridCells.WORK_OPERATION_TYPE_FK, commonLocators.CommonKeys.GRID, DESC_WORK)
        cy.wait(1000)//required wait to enable button
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Verify the prices in dispatch record coming for plant and sub-plant", function () {
        _common.select_rowHasValue_onIndexBased(cnt.uuid.DISPATCHING_RECORD, PLANT_CODE1, 0)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_SMALL, Cypress.env("PRICE_TOTAL_1"))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue_onIndexBased(cnt.uuid.DISPATCHING_RECORD, PLANT_CODE2, 0)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_SMALL, Cypress.env("PRICE_TOTAL_2"))
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue_onIndexBased(cnt.uuid.DISPATCHING_RECORD, PLANT_CODE3, 0)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.DISPATCHING_RECORD, app.GridCells.PRICE_SMALL, Cypress.env("PRICE_TOTAL_3"))
        _common.waitForLoaderToDisappear()
    });

})
