using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Platform.Core;
using RIB.Visual.Scheduling.Schedule.BusinessComponents;
using RVPBC = RIB.Visual.Platform.BusinessComponents;
using NLS = RIB.Visual.Scheduling.Main.Localization.Properties.Resources;
using RIB.Visual.Scheduling.Calendar.BusinessComponents;
using Constants = RIB.Visual.Scheduling.Main.Core.Constants;
using RIB.Visual.Basics.Common.BusinessComponents;
using static RIB.Visual.Basics.Core.Common.EntityIdentifier.Scheduling;
using RIB.Visual.Basics.Customize.BusinessComponents;

namespace RIB.Visual.Scheduling.Main.BusinessComponents
{
	/// <summary>
	///    Logic class for validating and calculating scheduling activities
	/// </summary>
	public class ScheduleDataPool
	{
		//private int _calendarId = 1;
		private Dictionary<int, Boolean> _loadedSchedules = new Dictionary<int, Boolean>();
		private Dictionary<int, ScheduleEntity> _scheduleCandidates = new Dictionary<int, ScheduleEntity>();
		private Dictionary<int, ActivityEntity> _actCandidates = new Dictionary<int, ActivityEntity>();
		private Dictionary<int, ActivityRelationshipEntity> _relCandidates = new Dictionary<int, ActivityRelationshipEntity>();
		private Dictionary<int, EventEntity> _eveCandidates = new Dictionary<int, EventEntity>();
		private Dictionary<int, ActivityProgressReportEntity> _progCandidates = new Dictionary<int, ActivityProgressReportEntity>();
		private Dictionary<int, CalendarData> _calCandidates = new Dictionary<int, CalendarData>();
		private Dictionary<int, HammockActivityEntity> _hamCandidates = new Dictionary<int, HammockActivityEntity>();
		private Dictionary<int, ActivitySplitEntity> _splitActCandidates = new Dictionary<int, ActivitySplitEntity>();
		private Dictionary<int, Activity2ModelObjectEntity> _act2MdlObjCandidates = new Dictionary<int, Activity2ModelObjectEntity>();

		/// <summary>
		///    Constructor
		/// </summary>
		public ScheduleDataPool()
		{
		}

		/// <summary>
		/// Constructor which initialize the data pool with Activities, Relationships, Events and ProgressReports from the Schedule of the MainItemId
		/// </summary>
		/// <param name="activityComplete">Activity Complete</param>
		/// <param name="dbContext">DB Context for Database call</param>
		public ScheduleDataPool(ActivityComplete activityComplete, RVPBC.DbContext dbContext)
		{
			if (activityComplete.Activity != null && activityComplete.Activity.Version != 0)
			{
				LoadScheduleData(activityComplete.Activity.ScheduleFk, dbContext);
				var dbEntity = dbContext.Entities<ActivityEntity>().Find(activityComplete.Activity.Id);
				if (dbEntity == null)
				{
					dbContext.Entry(activityComplete.Activity).State = EntityState.Modified;
					_actCandidates[activityComplete.Activity.Id] = activityComplete.Activity;
				}
				else if (activityComplete.Activity.Version > 0)
				{
					dbContext.Entry(dbEntity).State = EntityState.Modified;
					dbContext.Entry(dbEntity).CurrentValues.SetValues(activityComplete.Activity);
					activityComplete.Activity.ActivityEntities_ActivitySubFk = dbEntity.ActivityEntities_ActivitySubFk;
					activityComplete.Activity.ActivityEntities_BaseActivityFk = dbEntity.ActivityEntities_BaseActivityFk;
					activityComplete.Activity.ActivityEntities_ParentActivityFk = dbEntity.ActivityEntities_ParentActivityFk;
					activityComplete.Activity.ActivityEntity_ActivitySubFk = dbEntity.ActivityEntity_ActivitySubFk;
					activityComplete.Activity.ActivityEntity_BaseActivityFk = dbEntity.ActivityEntity_BaseActivityFk;
					activityComplete.Activity.ActivityEntity_ParentActivityFk = dbEntity.ActivityEntity_ParentActivityFk;
					_actCandidates[activityComplete.Activity.Id] = activityComplete.Activity;
					//activityComplete.Activity = dbEntity;
					//_actCandidates[activityComplete.Activity.Id] = dbEntity;
				}
				if (activityComplete.Activity.ParentActivityFk.HasValue)
				{
					ActivityEntity parent = null;
					if (_actCandidates.TryGetValue(activityComplete.Activity.ParentActivityFk.Value, out parent))
					{
						var me = parent.ActivityEntities_ParentActivityFk.FirstOrDefault(a => a.Id == activityComplete.Activity.Id);
						parent.ActivityEntities_ParentActivityFk.Remove(me);
						parent.ActivityEntities_ParentActivityFk.Add(activityComplete.Activity);
					}
				}
			}
			else if (activityComplete.Activity != null && activityComplete.Activity.Version == 0)
			{
				LoadScheduleData(activityComplete.Activity.ScheduleFk, dbContext);
				_actCandidates[activityComplete.Activity.Id] = activityComplete.Activity;
				dbContext.Entry(activityComplete.Activity).State = EntityState.Added;
			}

			// new was else if, but is so NEVER reached
			if (activityComplete.EffectedActivities != null && activityComplete.EffectedActivities.Any())
			{
				var act = activityComplete.EffectedActivities.First();
				LoadScheduleData(act.ScheduleFk, dbContext);
				_actCandidates[act.Id] = act;
			}

			if (activityComplete.MainItemId > 0)
			{
				AssertActivityLoaded(activityComplete.MainItemId, dbContext);
			}

			var groupedCal = _actCandidates.Values.GroupBy(e => e.CalendarFk);
			AddCalendar(groupedCal, null);

			if (activityComplete.Activity != null && activityComplete.Activity.ActivityTypeFk == Constants.ActivityTypeHammock)
			{
				//Delete actvity relations belonging to activity type hammock
				var deleteRelations = Relationships.Where(rel => rel.ParentActivityFk == activityComplete.MainItemId);
				foreach (var rel in deleteRelations)
				{
					if (activityComplete.RelationshipsToDelete != null && !activityComplete.RelationshipsToDelete.Contains(rel))
					{
						activityComplete.RelationshipsToDelete.Add(rel);
					}
					else
					{
						activityComplete.RelationshipsToDelete = new List<ActivityRelationshipEntity>();
						activityComplete.RelationshipsToDelete.Add(rel);
					}
				}
				if (activityComplete.RelationshipsToSave != null)
				{
					activityComplete.RelationshipsToSave.RemoveAll(rel => rel.ParentActivityFk == activityComplete.MainItemId);
				}
				//Delete actvity hammock relation belonging to activity with none type hammock
				var deleteHammocks = Hammocks.Where(ham => ham.ActivityMemberFk == activityComplete.MainItemId);
				foreach (var ham in deleteHammocks)
				{
					if (activityComplete.HammockActivityToDelete != null && !activityComplete.HammockActivityToDelete.Contains(ham))
					{
						activityComplete.HammockActivityToDelete = activityComplete.HammockActivityToDelete.Concat(new[] { ham });
						//activityComplete.HammockActivityToDelete.Add(ham);
					}
					else
					{
						activityComplete.HammockActivityToDelete = new List<HammockActivityEntity>();
						activityComplete.HammockActivityToDelete = activityComplete.HammockActivityToDelete.Concat(new[] { ham });
						//activityComplete.HammockActivityToDelete.Add(ham);
					}
				}
				if (activityComplete.HammockActivityToSave != null)
				{
					var temp = activityComplete.HammockActivityToSave.ToList();
					temp.RemoveAll(ham => ham.ActivityMemberFk == activityComplete.MainItemId);
					activityComplete.HammockActivityToSave = temp;
				}
			}

			UpdateEntitiesInDataPool(activityComplete.EffectedActivities, null, _actCandidates, dbContext);
			UpdateEntitiesInDataPool(activityComplete.RelationshipsToSave, activityComplete.RelationshipsToDelete, _relCandidates, dbContext);
			UpdateEntitiesInDataPool(activityComplete.EventsToSave, activityComplete.EventsToDelete, _eveCandidates, dbContext);
			UpdateEntitiesInDataPool(activityComplete.ProgressReportsToSave, activityComplete.ProgressReportsToDelete, _progCandidates, dbContext);
			//UpdateEntitiesInDataPool(activityComplete.ObjModelSimulationToSave, activityComplete.ObjModelSimulationToDelete, _act2MdlObjCandidates, dbContext);
			UpdateEntitiesInDataPool(activityComplete.SplitsToSave, activityComplete.SplitsToDelete, _splitActCandidates, dbContext);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="scheduleId"></param>
		public bool IsScheduleLoaded(int scheduleId)
		{
			return _loadedSchedules.ContainsKey(scheduleId) && _loadedSchedules[scheduleId];
		}

		private void UpdateEntitiesInDataPool<T>(List<T> entitiesToSave, IEnumerable<T> entitiesToDelete, Dictionary<int, T> candidates, RVPBC.DbContext dbContext) where T : RVPBC.EntityBase, IIdentifyable
		{
			if (entitiesToSave != null && entitiesToSave.Any())
			{
				var toShift = entitiesToSave.ToArray();
				entitiesToSave.Clear();
				foreach (var can in toShift)
				{
					var dbEntity = dbContext.Entities<T>().Find(can.Id);
					if (dbEntity != null)
					{
						dbContext.Entry(dbEntity).CurrentValues.SetValues(can);
						entitiesToSave.Add(dbEntity);
						candidates[dbEntity.Id] = dbEntity;
					}
					else
					{
						dbContext.Entry(can).State = can.Version == 0 ? EntityState.Added : EntityState.Modified;
						entitiesToSave.Add(can);
						candidates[can.Id] = can;
					}
				}
			}
			if (entitiesToDelete != null && entitiesToDelete.Any())
			{
				foreach (var entity in entitiesToDelete)
				{
					var dbEntity = dbContext.Entities<T>().Find(entity.Id);
					if (dbEntity != null)
					{
						dbContext.Entry(dbEntity).State = EntityState.Deleted;
					}
				}
			}
		}

		/// <summary>
		/// Constructor which initialize the data pool with Activities, Relationships, Events and ProgressReports from the Schedule of the MainItemId
		/// </summary>
		/// <param name="mainItemId">id of the mainItem</param>
		/// <param name="dbContext">DB Context for Database call</param>
		public ScheduleDataPool(int mainItemId, RVPBC.DbContext dbContext)
		{
			AssertActivityLoaded(mainItemId, dbContext);
		}

		/// <summary>
		/// Constructor which initialize the data pool with Activities, Relationships, Events and ProgressReports from the Schedule of the MainItemId
		/// </summary>
		/// <param name="mainActivity">activity</param>
		/// <param name="dbContext">DB Context for Database call</param>
		public ScheduleDataPool(ActivityEntity mainActivity, RVPBC.DbContext dbContext)
		{
			AssertActivityLoaded(mainActivity.Id, dbContext);
			var dbEntity = dbContext.Entities<ActivityEntity>().Find(mainActivity.Id);
			dbContext.Entry(dbEntity).CurrentValues.SetValues(mainActivity);
		}

		/// <summary>
		///    Provides access to all loaded activities
		/// </summary>
		public IEnumerable<ActivityMappingEntity> ActivityMappings
		{
			set
			{
				foreach (var act in value)
				{
					if (!_actCandidates.ContainsKey(act.Id))
					{
						_actCandidates[act.Id] = act.AsActivity();
					}
				}
			}
		}

		/// <summary>
		///    Provides access to all loaded activities
		/// </summary>
		public IEnumerable<ActivityEntity> Activities
		{
			get { return _actCandidates.Values; }
			set
			{
				foreach (var act in value)
				{
					_actCandidates[act.Id] = act;
				}
			}
		}

		/// <summary>
		/// Provides access to all loaded schedules
		/// </summary>
		public IEnumerable<ScheduleEntity> Schedules
		{
			get { return _scheduleCandidates.Values; }
			set
			{
				foreach (var scheduleEntity in value)
				{
					_scheduleCandidates[scheduleEntity.Id] = scheduleEntity;
				}
			}
		}

		/// <summary>
		///    Provides access to all loaded activities
		/// </summary>
		public IEnumerable<KeyValuePair<int, ActivityEntity>> ActivitiesMap
		{
			get { return _actCandidates; }
		}

		/// <summary>
		///    Provides access to all loaded relationships
		/// </summary>
		public IEnumerable<RelationshipMappingEntity> RelationshipMappings
		{
			set
			{
				foreach (var rel in value)
				{
					if (!_relCandidates.ContainsKey(rel.Id))
					{
						_relCandidates[rel.Id] = rel.AsActivityRelationship();
					}
				}
			}
		}

		/// <summary>
		///    Provides access to all loaded relationships
		/// </summary>
		public IEnumerable<ActivityRelationshipEntity> Relationships
		{
			get { return _relCandidates.Values; }
			set
			{
				foreach (var rel in value)
				{
					_relCandidates[rel.Id] = rel;
				}
			}
		}

		/// <summary>
		///    Provides access to all loaded events
		/// </summary>
		public IEnumerable<EventEntity> Events
		{
			get { return _eveCandidates.Values; }
			set
			{
				foreach (var eve in value)
				{
					_eveCandidates[eve.Id] = eve;
				}
			}
		}

		/// <summary>
		///    Provides access to all loaded activity progress reports
		/// </summary>
		public IEnumerable<ActivityProgressReportEntity> ProgressReports
		{
			get { return _progCandidates.Values; }
			set
			{
				foreach (var progs in value)
				{
					_progCandidates[progs.Id] = progs;
				}
			}
		}

		/// <summary>
		///    Provides access to all loaded activity2ModelObjects
		/// </summary>
		public IEnumerable<Activity2ModelObjectEntity> Act2MdlObjects
		{
			get { return _act2MdlObjCandidates.Values; }
			set
			{
				foreach (var act2MdlObj in value)
				{
					_act2MdlObjCandidates[act2MdlObj.Id] = act2MdlObj;
				}
			}
		}

		/// <summary>
		///    Removes activity progress reports from data pool
		/// </summary>
		public void RemoveProgressReports(IEnumerable<ActivityProgressReportEntity> progressReports)
		{
			foreach (var progReport in progressReports)
			{
				if (_progCandidates.ContainsKey(progReport.Id))
				{
					_progCandidates.Remove(progReport.Id);
				}
			}
		}
		/// <summary>
		/// Add activity progress reports to data pool
		/// </summary>
		/// <param name="progressReports"></param>
		public void AddProgressReports(IEnumerable<ActivityProgressReportEntity> progressReports)
		{
			foreach (var progReport in progressReports)
			{
				if (!_progCandidates.ContainsKey(progReport.Id))
				{
					_progCandidates.Add(progReport.Id, progReport);
				}
			}
		}

		/// <summary>
		///    Provides access to all loaded hammocks
		/// </summary>
		public IEnumerable<HammockActivityEntity> Hammocks
		{
			get { return _hamCandidates.Values; }
			set
			{
				foreach (var hammocks in value)
				{
					_hamCandidates[hammocks.Id] = hammocks;
				}
			}
		}


		/// <summary>
		///    Provides access to all loaded SplitActivities
		/// </summary>
		public IEnumerable<ActivitySplitEntity> SplitActivities
		{
			get { return _splitActCandidates.Values; }
			set
			{
				foreach (var splitActivities in value)
				{
					_splitActCandidates[splitActivities.Id] = splitActivities;
				}
			}
		}

		/// <summary>
		/// Provides access to all loaded calendars
		/// </summary>
		public IDictionary<int, CalendarData> Calendar
		{
			get { return _calCandidates; }
			set
			{
				foreach (var dic in value)
				{
					if (_calCandidates.ContainsKey(dic.Key))
					{
						_calCandidates[dic.Key] = dic.Value;
					}
					else
					{
						_calCandidates.Add(dic.Key, dic.Value);
					}
				}
			}
		}

		/// <summary>
		/// Savely gets the nonworking days for the calendar of the given activity
		/// </summary>
		/// <param name="activity"></param>
		/// <returns></returns>
		public NonWorkingDayCheck GetNonWorkingDays(ActivityEntity activity)
		{
			if (!_calCandidates.ContainsKey(activity.CalendarFk))
			{
				var filter = new UtilitiesDataFilter();
				var calUtils = new CalendarUtilitiesLogic();

				var max = activity.PlannedStart.AddDays(365);
				var min = activity.PlannedStart.AddDays(-365);

				AddCalendar(calUtils, filter, activity.CalendarFk, min, max);
			}

			return _calCandidates[activity.CalendarFk].NonWorkingDays;
		}

		/// <summary>
		///    Asserts that the activities determined by the ids passed are loaded (this includes their companions in plan with all relations, events, ...)
		/// </summary>
		/// <param name="actIDs">IDs of activities to be loaded</param>
		/// <param name="dbContext">Database context for eventually necessary database call</param>
		public IEnumerable<ActivityEntity> AssertActivitiesLoaded(IEnumerable<int> actIDs, RVPBC.DbContext dbContext)
		{
			var actList = new List<ActivityEntity>();
			foreach (var id in actIDs)
			{
				actList.Add(AssertActivityLoaded(id, dbContext));
			}

			return actList;
		}

		/// <summary>
		///   Gives access to activities matches the filter, if they loaded
		/// </summary>
		/// <param name="filter">filter condition</param>
		public IEnumerable<ActivityEntity> GetActivities(Func<ActivityEntity, bool> filter)
		{
			if (filter != null)
			{
				return _actCandidates.Values.Where(filter);
			}
			return null;
		}

		/// <summary>
		///   Gives access to split activities matches the filter, if they loaded
		/// </summary>
		/// <param name="filter">filter condition</param>
		public IEnumerable<ActivitySplitEntity> GetSplitActivities(Func<ActivitySplitEntity, bool> filter)
		{
			if (filter != null)
			{
				return _splitActCandidates.Values.Where(filter);
			}
			return null;
		}

		/// <summary>
		///   Gives access to an activity with a given ID, if it is loaded
		/// </summary>
		/// <param name="actID">ID of activity to be found</param>
		public ActivityEntity GetActivityById(int actID)
		{
			ActivityEntity res = null;
			_actCandidates.TryGetValue(actID, out res);

			return res;
		}

		/// <summary>
		///   Gives access to activities with given IDs, if they loaded
		/// </summary>
		/// <param name="actIds">IDs of activities to be found</param>
		public IEnumerable<ActivityEntity> GetActivityByIds(IEnumerable<int> actIds)
		{
			return actIds.Select(a => _actCandidates[a]).ToList();
		}

		/// <summary>
		///    Asserts that the activity determined by the id passed are loaded (this includes their companions in plan with all relations, events, ...)
		/// </summary>
		/// <param name="actID">ID of activity to be loaded</param>
		/// <param name="dbContext">Database context for eventually necessary database call</param>
		public ActivityEntity AssertActivityLoaded(int actID, RVPBC.DbContext dbContext)
		{
			ActivityEntity res;
			if (!_actCandidates.TryGetValue(actID, out res) && actID > 0)
			{
				res = dbContext.Entities<ActivityEntity>().FirstOrDefault(act => act.Id == actID);
				if (res != null)
				{
					LoadScheduleData(res.ScheduleFk, dbContext);
				}
			}

			return res;
		}

		/// <summary>
		///    Returns the list of activities being direct child of the activity identified by actID. Does not access database
		/// </summary>
		/// <param name="actID">ID of activity the children are requested from</param>
		public IEnumerable<ActivityEntity> GetChildActivitiesTo(int actID)
		{
			return _actCandidates.Values.Where(act => act.ParentActivityFk == actID).ToList();
		}

		/// <summary>
		///    Asserts that the relation determined by the id passed are loaded (this includes their companions in plan with all activities, events, ...)
		/// </summary>
		/// <param name="relID">ID of relation to be loaded</param>
		/// <param name="dbContext">Database context for eventually necessary database call</param>
		public ActivityRelationshipEntity AssertRelationshipLoaded(int relID, RVPBC.DbContext dbContext)
		{
			ActivityRelationshipEntity res;
			if (!_relCandidates.TryGetValue(relID, out res))
			{
				res = dbContext.Entities<ActivityRelationshipEntity>().FirstOrDefault(rel => rel.Id == relID);
				LoadScheduleData(res.ScheduleFk, dbContext);
			}

			return res;
		}


		/// <summary>
		///   Gives access to events matches the filter, if they loaded
		/// </summary>
		/// <param name="filter">filter condition</param>
		public IEnumerable<ActivityRelationshipEntity> GetRelationships(Func<ActivityRelationshipEntity, bool> filter)
		{
			if (filter != null)
			{
				return _relCandidates.Values.Where(filter);
			}
			return null;
		}

		/// <summary>
		///   Gives access to events matches the filter, if they loaded
		/// </summary>
		/// <param name="toRemove">Relationships to be removed from the internal data</param>
		public void RemoveRelationships(IEnumerable<ActivityRelationshipEntity> toRemove)
		{
			foreach (var rela in toRemove)
			{
				if (_relCandidates.ContainsKey(rela.Id))
				{
					_relCandidates.Remove(rela.Id);
				}
			}
		}

		/// <summary>
		///    Returns the list of relations being owned by the activity identified by actID. Does not access database
		/// </summary>
		/// <param name="actIDs">ID of activity the children are requested from</param>
		public IEnumerable<ActivityRelationshipEntity> GetRelationshipsLoadedByParent(IEnumerable<int> actIDs)
		{
			return _relCandidates.Values.Where(rel => actIDs.Contains(rel.ParentActivityFk)).ToArray();
		}

		/// <summary>
		/// Get all hammock activities that contain activity with Id = actId
		/// </summary>
		/// <param name="actId"></param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetAllAssignedHammockActivities(int actId)
		{
			var affectedActIds = _hamCandidates.Where(ham => ham.Value.ActivityMemberFk == actId).Select(ham => ham.Value.ActivityFk);
			return _actCandidates.Where(act => affectedActIds.Contains(act.Value.Id)).Select(act => act.Value);
		}

		/// <summary>
		/// Get all activities that belongs to hammock "hammockActivity"
		/// </summary>
		/// <param name="hammockActivity"></param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetAllAssignedActivitiesToHammockActivity(ActivityEntity hammockActivity)
		{
			return ActivityLogicHammockExtension.GetAllAssignedActivitiesToHammockActivity(hammockActivity, Activities, Hammocks);
		}

		/// <summary>
		///    Returns the list of relations being linked to the activity identified by actID. Does not access database
		/// </summary>
		/// <param name="actID">ID of activity the children are requested from</param>
		public IEnumerable<ActivityRelationshipEntity> GetRelationshipsLoadedRelatedToActivity(int actID)
		{
			return _relCandidates.Values.Where(rel => rel.ParentActivityFk == actID || rel.ChildActivityFk == actID);
		}

		/// <summary>
		///    Returns the list of relations being linked to a list of activities identified by actID. Does not access database
		/// </summary>
		/// <param name="actIds">ID of activity the children are requested from</param>
		public IEnumerable<ActivityRelationshipEntity> GetRelationshipsLoadedRelatedToActivities(IEnumerable<int> actIds)
		{
			return _relCandidates.Values.Where(rel => actIds.Contains(rel.ChildActivityFk) || actIds.Contains(rel.ParentActivityFk)).Distinct().ToArray();
		}

		/// <summary>
		///    Asserts that the event determined by the id passed are loaded (this includes their companions in plan with all activities, relations, ...)
		/// </summary>
		/// <param name="eveID">ID of event to be loaded</param>
		/// <param name="dbContext">Database context for eventually necessary database call</param>
		public EventEntity AssertEventLoaded(int eveID, RVPBC.DbContext dbContext)
		{
			EventEntity res;
			if (!_eveCandidates.TryGetValue(eveID, out res))
			{
				res = dbContext.Entities<EventEntity>().FirstOrDefault(eve => eve.Id == eveID);
				LoadScheduleData(res.ScheduleFk, dbContext);
			}

			return res;
		}


		/// <summary>
		///   Gives access to events matches the filter, if they loaded
		/// </summary>
		/// <param name="filter">filter condition</param>
		public IEnumerable<EventEntity> GetEvents(Func<EventEntity, bool> filter)
		{
			if (filter != null)
			{
				return _eveCandidates.Values.Where(filter);
			}
			return null;
		}

		/// <summary>
		///    Returns the list of events being owned by the activity identified by actID. Does not access database
		/// </summary>
		/// <param name="actID">ID of activity the children are requested from</param>
		public IEnumerable<EventEntity> GetEventsLoadedByParent(int actID)
		{
			IEnumerable<EventEntity> res = _eveCandidates.Values.Where(eve => eve.ActivityFk == actID).ToList();
			return res;
		}

		/// <summary>
		///    Returns the list of events being owned by the activity identified by activity ids. Does not access database
		/// </summary>
		/// <param name="actIds">IDs of activities the events are requested from</param>
		public IEnumerable<EventEntity> GetEventsLoadedByParents(IEnumerable<int> actIds)
		{
			List<EventEntity> res = new List<EventEntity>();
			foreach (var listOfEvents in actIds.Select(a => GetEventsLoadedByParent(a)))
			{
				res.AddRange(listOfEvents);
			}
			return res;
		}

		/// <summary>
		///    Asserts that the event determined by the id passed are loaded (this includes their companions in plan with all activities, relations, ...)
		/// </summary>
		/// <param name="eveID">ID of event to be loaded</param>
		/// <param name="dbContext">Database context for eventually necessary database call</param>
		public ActivityProgressReportEntity AssertReportLoaded(int eveID, RVPBC.DbContext dbContext)
		{
			ActivityProgressReportEntity res;
			if (!_progCandidates.TryGetValue(eveID, out res))
			{
				res = dbContext.Entities<ActivityProgressReportEntity>().FirstOrDefault(eve => eve.Id == eveID);
				LoadScheduleData(res.ScheduleFk, dbContext);
			}

			return res;
		}
		/// <summary>
		/// Load progress reports, split activities adn hammocks for loaded activities
		/// </summary>
		/// <param name="dbContext"></param>
		internal void LoadForCompletionCalcOfLoadedActivities(RVPBC.DbContext dbContext)
		{
			var activities = this.Activities.Where(e => e.HasReports).CollectIds(e => e.Id).ToArray();
			var schedules = this.Activities.Where(e => e.HasReports).CollectIds(e => e.ScheduleFk).ToArray();
			if (activities != null && activities.Length > 0)
			{
				foreach (var item in schedules)
				{
					if (!IsScheduleLoaded(item))
					{
						LoadScheduleData(item, dbContext);
					}

				}
				foreach (var prog in dbContext.Entities<ActivityProgressReportEntity>().Where(pr => pr.ActivityFk.HasValue && activities.Contains(pr.ActivityFk.Value)).ToList())
				{
					if (!_progCandidates.ContainsKey(prog.Id))
					{
						_progCandidates[prog.Id] = prog;
					}
				}
				foreach (var split in dbContext.Entities<ActivitySplitEntity>().Where(s => activities.Contains(s.ActivityFk)).ToList())
				{
					if (!_splitActCandidates.ContainsKey(split.Id))
					{
						_splitActCandidates[split.Id] = split;
					}
				}
			}
		}
		/// <summary>
		///   Gives access to progress reports matches the filter, if they loaded
		/// </summary>
		/// <param name="filter">filter condition</param>
		public IEnumerable<ActivityProgressReportEntity> GetReports(Func<ActivityProgressReportEntity, bool> filter)
		{
			if (filter != null)
			{
				return _progCandidates.Values.Where(filter);
			}
			return null;
		}


		/// <summary>
		///    Returns the list of events being owned by the activity identified by actID. Does not access database
		/// </summary>
		/// <param name="actID">ID of activity the children are requested from</param>
		public IEnumerable<ActivityProgressReportEntity> GetReportLoadedByParent(int actID)
		{
			return _progCandidates.Values.Where(eve => eve.ActivityFk == actID);
		}

		/// <summary>
		/// Load Calendar for rescheduling acivities
		/// </summary>
		/// <param name="scheduleId"></param>
		/// <param name="dbContext"></param>
		public void AssertCalendarLoaded(int scheduleId, RVPBC.DbContext dbContext)
		{
			var groupedCal = _actCandidates.Values.GroupBy(e => e.CalendarFk);
			var filter = new UtilitiesDataFilter();
			var calUtils = new CalendarUtilitiesLogic();

			foreach (var cal in groupedCal)
			{
				//var max =_actCandidates.Values.Where(e => e.CalendarFk == cal.Key).Max(a => a.PlannedFinish.HasValue ? a.PlannedFinish : a.PlannedStart);
				var max = _actCandidates.Values.Where(e => e.CalendarFk == cal.Key).Max(a => a.PlannedStart);
				var min = _actCandidates.Values.Where(e => e.CalendarFk == cal.Key).Min(a => a.PlannedStart);

				AddCalendar(calUtils, filter, cal.Key, min, max);

			}
		}

		/// <summary>
		/// Load Hammock for rescheduling acivities
		/// </summary>
		/// <param name="scheduleId"></param>
		/// <param name="dbContext"></param>
		public void LoadHammocks(int scheduleId, RVPBC.DbContext dbContext)
		{
			var tempHammocks = new HammockActivityLogic().GetByFilter(ham => ham.ScheduleFk == scheduleId);
			foreach (var hammock in tempHammocks)
			{
				if (!_hamCandidates.ContainsKey(hammock.Id))
				{
					_hamCandidates[hammock.Id] = hammock;
				}
			}
		}

		/// <summary>
		///    Loads all activities, relations and events of a schedule
		/// </summary>
		/// <param name="scheduleID">ID of schedule to be loaded</param>
		/// <param name="dbContext">Database context for eventually necessary database call</param>
		public void LoadScheduleData(int scheduleID, RVPBC.DbContext dbContext)
		{
			var loadHammocks = false;
			var hasToLoadAll = false;
			if (!_loadedSchedules.ContainsKey(scheduleID))
			{
				_scheduleCandidates[scheduleID] = new ScheduleLogic().GetScheduleById(scheduleID);
				hasToLoadAll = true;
			}
			if (hasToLoadAll)
			{
				foreach (var act in dbContext.Entities<ActivityEntity>().Where(ac => ac.ScheduleFk == scheduleID && ac.IsLive && ac.BaseActivityFk == null).ToList())
				{
					if (!_actCandidates.ContainsKey(act.Id))
					{
						_actCandidates.Add(act.Id, act);
						if (!loadHammocks && act.ActivityTypeFk == Constants.ActivityTypeHammock)
						{
							loadHammocks = true;
						}
					}
				}

				foreach (var rel in dbContext.Entities<ActivityRelationshipEntity>().Where(re => re.ScheduleFk == scheduleID).ToList())
				{
					if (!_relCandidates.ContainsKey(rel.Id))
					{
						_relCandidates[rel.Id] = rel;
					}
				}

				foreach (var eve in dbContext.Entities<EventEntity>().Where(ev => ev.ScheduleFk == scheduleID).ToList())
				{
					if (!_eveCandidates.ContainsKey(eve.Id))
					{
						_eveCandidates[eve.Id] = eve;
					}
				}

				foreach (var prog in dbContext.Entities<ActivityProgressReportEntity>().Where(pr => pr.ScheduleFk == scheduleID).ToList())
				{
					if (!_progCandidates.ContainsKey(prog.Id))
					{
						_progCandidates[prog.Id] = prog;
					}
				}

				if (loadHammocks)
				{
					foreach (var hammock in dbContext.Entities<HammockActivityEntity>().Where(ham => ham.ScheduleFk == scheduleID).ToList())
					{
						if (!_hamCandidates.ContainsKey(hammock.Id))
						{
							_hamCandidates[hammock.Id] = hammock;
						}
					}
				}
				foreach (var mdlObject in dbContext.Entities<Activity2ModelObjectEntity>().Where(mdl => _actCandidates.Keys.Contains(mdl.ActivityFk)).ToList())
				{
					if (!_act2MdlObjCandidates.ContainsKey(mdlObject.Id))
					{
						_act2MdlObjCandidates[mdlObject.Id] = mdlObject;
					}
				}

				foreach (var split in dbContext.Entities<ActivitySplitEntity>().Where(e => _actCandidates.Keys.Contains(e.ActivityFk)).ToList())
				{
					if (!_splitActCandidates.ContainsKey(split.Id))
					{
						_splitActCandidates[split.Id] = split;
					}
				}
				var actLogic = new ActivityLogic();
				actLogic.IncludeTransientProperties(_actCandidates.Values, this);
				var groupedCal = _actCandidates.Values.GroupBy(e => e.CalendarFk);
				AddCalendar(groupedCal, scheduleID);
				_loadedSchedules[scheduleID] = true;
			}
		}

		private void AddCalendar(IEnumerable<IGrouping<int, ActivityEntity>> groupedCal, int? scheduleID)
		{
			var mindate = DateTime.Today.AddDays(-365);
			var maxdate = DateTime.Today.AddDays(1461);
			var filter = new UtilitiesDataFilter();
			var calUtils = new CalendarUtilitiesLogic();

			foreach (var cal in groupedCal)
			{
				//var max =_actCandidates.Values.Where(e => e.CalendarFk == cal.Key).Max(a => a.PlannedFinish.HasValue ? a.PlannedFinish : a.PlannedStart);
				var max = _actCandidates.Values.Where(e => e.CalendarFk == cal.Key).Max(a => a.PlannedStart);
				var min = _actCandidates.Values.Where(e => e.CalendarFk == cal.Key).Min(a => a.PlannedStart);
				min = (mindate < min) ? mindate : min;
				max = (maxdate > max) ? maxdate : max;


				if (!_calCandidates.ContainsKey(cal.Key))
				{
					AddCalendar(calUtils, filter, cal.Key, min, max);
				}
			}

			if (scheduleID.HasValue)
			{
				var sid = scheduleID.Value;
				var sched = _scheduleCandidates[sid];
				if (sched.CalendarFk.HasValue && !_calCandidates.ContainsKey(sched.CalendarFk.Value))
				{
					var calData = new CalendarData(sched.CalendarFk.Value);

					//set non working days
					filter.Calendar = sched.CalendarFk.Value;
					filter.Start = mindate.AddDays(-365);
					filter.End = maxdate.AddDays(365);
					calData.NonWorkingDays = new CalendarUtilitiesLogic().GetNonWorkingDayChecker(filter) as NonWorkingDayCheck;

					_calCandidates.Add(sched.CalendarFk.Value, calData);
				}
			}
		}

		private void AddCalendar(CalendarUtilitiesLogic calUtils, UtilitiesDataFilter filter, int calendarKey, DateTime min, DateTime max)
		{
			if (!_calCandidates.ContainsKey(calendarKey))
			{
				var calData = new CalendarData(calendarKey);

				//set non working days
				filter.Calendar = calendarKey;
				filter.Start = min.AddDays(-365);
				filter.End = max.AddDays(365);
				calData.NonWorkingDays = new CalendarUtilitiesLogic().GetNonWorkingDayChecker(filter) as NonWorkingDayCheck;

				_calCandidates.Add(calendarKey, calData);
			}
		}

		/// <summary>
		///   Gives access to a calendar with a given Id, if it is loaded
		/// </summary>
		/// <param name="calId">Id of calendar to be found</param>
		public CalendarEntity GetCalendarById(int calId)
		{
			CalendarEntity res = null;
			CalendarData cal = null;
			_calCandidates.TryGetValue(calId, out cal);
			if (cal != null)
			{
				res = cal.Calendar;
			}
			return res;
		}

		/// <summary>
		///   Gives access to model objects matches the filter, if they loaded
		/// </summary>
		/// <param name="filter">filter condition</param>
		public IEnumerable<Activity2ModelObjectEntity> GetModelObjects(Func<Activity2ModelObjectEntity, bool> filter)
		{
			if (filter != null)
			{
				return _act2MdlObjCandidates.Values.Where(filter);
			}
			return null;
		}

		/// <summary>
		///    Returns the list of modelobjects being owned by the activity identified by actID. Does not access database
		/// </summary>
		/// <param name="actID">ID of activity the children are requested from</param>
		public IEnumerable<Activity2ModelObjectEntity> GetModelObjectsLoadedByParent(int actID)
		{
			IEnumerable<Activity2ModelObjectEntity> res = _act2MdlObjCandidates.Values.Where(obj => obj.ActivityFk == actID).ToList();
			return res;
		}

		/// <summary>
		///    Returns the list of model objects being owned by the activity identified by activity ids. Does not access database
		/// </summary>
		/// <param name="actIds">IDs of activities the events are requested from</param>
		public IEnumerable<Activity2ModelObjectEntity> GetModelObjectsLoadedByParents(IEnumerable<int> actIds)
		{
			List<Activity2ModelObjectEntity> res = new List<Activity2ModelObjectEntity>();
			foreach (var listOfModelObjects in actIds.Select(a => GetModelObjectsLoadedByParent(a)))
			{
				res.AddRange(listOfModelObjects);
			}
			return res;
		}
		/// <summary>
		/// Load progress reports, split activities adn hammocks for loaded activities
		/// </summary>
		/// <param name="scheduleIds"></param>
		/// <param name="dbContext"></param>
		internal void LoadRelationsForSchedules(int[] scheduleIds, RVPBC.DbContext dbContext)
		{
			var schedules = new ScheduleLogic().GetByFilter(e=>scheduleIds.Contains(e.Id)).ToList();
			foreach (var scheduleID in scheduleIds)
			{
				if (!_loadedSchedules.ContainsKey(scheduleID))
				{
					_scheduleCandidates[scheduleID] = schedules.FirstOrDefault(e => e.Id == scheduleID);
				}

				foreach (var rel in dbContext.Entities<ActivityRelationshipEntity>().Where(re => re.ScheduleFk == scheduleID).ToList())
				{
					if (!_relCandidates.ContainsKey(rel.Id))
					{
						_relCandidates[rel.Id] = rel;
					}
				}
				var groupedCal = _actCandidates.Values.GroupBy(e => e.CalendarFk);
				AddCalendar(groupedCal, scheduleID);
				_loadedSchedules[scheduleID] = true;
			}
		}
	}
}