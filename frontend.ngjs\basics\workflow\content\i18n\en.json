{"basics": {"workflow": {"moduleName": "Workflow Designer", "tabHeader": "Header", "ContainerEntityApproversDefaultTitle": {"conApprovers": "Contract Approvers", "rfqApprovers": "RFQ Approvers"}, "task": {"list": {"header": "ToDo", "headerTask": "Tasks", "mainEntityFilter": "Filter by <PERSON> En<PERSON>", "companyFilter": "Filter by Login Company", "clear": "Clear Noticification", "groupOrSortingSetting": "Group & Sorting Setting", "groupOrSortingSettingHeaderText": "Save setting: Please enter location and setting name.", "groupOrSortingSettingnameLabelText": "Setting name", "groupOrSortingSettingareaLabelText": "setting name", "newSetting": "New Setting", "settingdefConfirmDeleteTitle": "Delete Setting", "settingdefConfirmDeleteBody": "Do you really want to delete the setting '{{p1}}'?", "grouping": {"noGrouping": "No Grouping", "clerk": "Grouped by Clerk", "prio": "Group by Priority", "lifeTime": "Group by Lifetime", "entity": "Entity Description", "template": "Template", "endDate": "End Date", "description": "Group by Description", "noEndDate": "No End Date", "ended": "ended", "MyTasks": "My Tasks", "LoginCompany": "Group by Login Company", "userDefined1": "User Defined 1", "userDefined2": "User Defined 2", "userDefined3": "User Defined 3", "userDefined4": "User Defined 4", "userDefined5": "User Defined 5", "userDefinedMoney1": "User Defined Money 1", "userDefinedMoney2": "User Defined Money 2", "userDefinedMoney3": "User Defined Money 3", "userDefinedMoney4": "User Defined Money 4", "userDefinedMoney5": "User Defined Money 5", "userDefinedDate1": "User Defined Date 1", "userDefinedDate2": "User Defined Date 2", "userDefinedDate3": "User Defined Date 3", "userDefinedDate4": "User Defined Date 4", "userDefinedDate5": "User Defined Date 5"}, "sorting": {"noSorting": "Default Sorting", "startAsc": "By Start (Asc)", "startDesc": "By Start (Desc)", "prioAsc": "By Priority (Asc)", "prioDesc": "By Priority (Desc)", "endTimeDesc": "By End Time (Desc)", "endTimeAsc": "By End Time (Asc)", "userDefined1Desc": "By User Defined 1 (Desc)", "userDefined1Asc": "By User Defined 1 (Asc)", "userDefined2Desc": "By User Defined 2 (Desc)", "userDefined2Asc": "By User Defined 2 (Asc)", "userDefined3Desc": "By User Defined 3 (Desc)", "userDefined3Asc": "By User Defined 3 (Asc)", "userDefined4Desc": "By User Defined 4 (Desc)", "userDefined4Asc": "By User Defined 4 (Asc)", "userDefined5Desc": "By User Defined 5 (Desc)", "userDefined5Asc": "By User Defined 5 (Asc)", "userDefinedMoney1Asc": "By User Defined Money 1 (Asc)", "userDefinedMoney2Asc": "By User Defined Money 2 (Asc)", "userDefinedMoney3Asc": "By User Defined Money 3 (Asc)", "userDefinedMoney4Asc": "By User Defined Money 4 (Asc)", "userDefinedMoney5Asc": "By User Defined Money 5 (Asc)", "userDefinedDate1Asc": "By User Defined Date 1 (Asc)", "userDefinedDate2Asc": "By User Defined Date 2 (Asc)", "userDefinedDate3Asc": "By User Defined Date 3 (Asc)", "userDefinedDate4Asc": "By User Defined Date 4 (Asc)", "userDefinedDate5Asc": "By User Defined Date 5 (Asc)", "userDefinedMoney1Desc": "By User Defined Money 1 (Desc)", "userDefinedMoney2Desc": "By User Defined Money 2 (Desc)", "userDefinedMoney3Desc": "By User Defined Money 3 (Desc)", "userDefinedMoney4Desc": "By User Defined Money 4 (Desc)", "userDefinedMoney5Desc": "By User Defined Money 5 (Desc)", "userDefinedDate1Desc": "By User Defined Date 1 (Desc)", "userDefinedDate2Desc": "By User Defined Date 2 (Desc)", "userDefinedDate3Desc": "By User Defined Date 3 (Desc)", "userDefinedDate4Desc": "By User Defined Date 4 (Desc)", "userDefinedDate5Desc": "By User Defined Date 5 (Desc)"}, "showFilter": "Filter Tasks", "executefilter": "Execute filter", "clearfilter": "Clear filter", "filter": "filter"}, "detail": {"group": {"history": "History", "comment": "Comments", "detail": "Details", "document": "Documents"}, "noGroupHeader": "All", "headerText": {"group-running": "Group Task", "user-running": "User Task", "group-edit": "In Progress", "group-locked": "In Progress by ", "user-done": "Done", "group-done": "Done by "}, "headerButtons": {"group-running": "Edit", "group-edit": "Cancel", "group-locked": "Take"}}}, "actionForm": {"title": "Action", "groups": {"actionDetail": "Action Detail"}}, "designer": {"menu": {"delete": "Delete", "copy": "Copy", "cut": "Cut", "paste": "Paste"}, "containerHeader": "Designer", "start": "Start", "end": "End", "decision": "Decision", "object": "Object Action", "script": "Script Action", "userTask": "User Task", "externalFn": "External Function", "message": "Send Message", "userForm": "User Form", "workflow": "Workflow Action"}, "template": {"templateVersion": "Version", "helpText": "Help Text", "containerHeader": "Workflow Templates", "description": "Description", "comment": "Comment", "errorDialog": {"version": {"header": "Workflow Version Errors", "errorText": "The following errors where found in the design:", "noErrorText": "No errors found"}, "readOnly": {"header": "Selected version is readonly!", "text": "The selected version is readonly and can not be saved. Changes on other versions will be saved."}, "workflowKeyword": {"header": "Selected version has Workflow defined Keyword(s)!", "text": "Please change the variable name in action(s). Variable name as 'Result' cannot be used in the Workflow Template Designer. The 'Result' word is reserved in the Workflow Engine."}, "templateDescription": {"header": "Save failed", "body": "Enter a Description", "descriptionEmpty": "Description is empty.", "descriptionExists": "Description already exists."}}, "kind": "Kind", "lifeTime": "Lifetime", "type": {"1": "Workflow", "2": "Escalation Workflow", "description": "Type"}, "entity": "Entity", "owner": "Owner", "keyUser": "Key User", "startWorkflow": "Start Workflow", "version": {"containerHeader": "Workflow Templates Versions", "toolbar": {"changeStatus": "Change Status", "copyVersion": "Copy Version", "validate": "Validate workflow"}, "errorDialog": {"concurency": {"header": "Concurrency Error", "text": "The version has already been changed by another user. Your changes had been copied to a new version. Please check the data and save it again."}}, "globalScripts": {"containerName": "Global Version Scripts", "include": "Include", "fileName": "File Name", "filePath": "File Path", "hash": "Hash", "selectedTemplateVersionMissing": "Select a Template Version that is not read-only to include Global Version Scripts"}}, "context": {"key": "key", "value": "value", "containerHeader": "Template Context"}, "isSingleton": "Single Entity Instance", "confirmDelete": {"title": "Delete Workflow Template", "templateHeader": "Do you want to delete the selected Workflow Template?"}, "selectedTemplateMissing": "Please select a Workflow Template", "RetentionTime": "Instances Retention Time (Days)", "addAccessRightDescriptor": "Add Permission for Template", "deleteAccessRightDescriptor": "Delete Permission for Template", "enterAccessRightDescriptorName": "Template Permission Name", "plsEnterName": "Please enter a Name for this Template Permission", "disablePermission": "Disable Permission Check", "showDialog": {"disablePermissionHeaderText": "Attention !", "disablePermissionWarn": "If you enable the Disable Permission Check feature you potentially give normal user administrator privileges when running. Please be aware!"}, "useTextModuleTranslation": "Use Text Module Translation"}, "subscribedEvents": {"event": "Event", "containerHeader": "Subscribed Events"}, "statusMatrix": {"containerHeader": "Status Matrix"}, "action": {"key": "key", "value": "value", "detail": {"actionType": "Action Type", "description": "Description", "comment": "Comment", "executeCondition": "Execute Condition", "executeConditionToolTipContent": "Defines if the action is executed or skipped.When there is no entry, then the action is executed normally.No semicolon at the end necessary.", "executeConditionPlaceholder": "Example", "code": "Code", "actionTypeId": "Action Type", "actionId": "Action", "parameter": "Parameter", "userId": "Clerk", "action": "Action", "lifeTime": "Lifetime", "result": "Result", "priority": "Priority", "endTime": "End Date", "businessPartner": "Business Partner", "userDefined1": "User Defined 1", "userDefined2": "User Defined 2", "userDefined3": "User Defined 3", "userDefined4": "User Defined 4", "userDefined5": "User Defined 5", "userDefinedMoney1": "User Defined Money 1", "userDefinedMoney2": "User Defined Money 2", "userDefinedMoney3": "User Defined Money 3", "userDefinedMoney4": "User Defined Money 4", "userDefinedMoney5": "User Defined Money 5", "userDefinedDate1": "User Defined Date 1", "userDefinedDate2": "User Defined Date 2", "userDefinedDate3": "User Defined Date 3", "userDefinedDate4": "User Defined Date 4", "userDefinedDate5": "User Defined Date 5", "ownerId": "Owner Id", "userTaskDetail": "User Task Details"}, "containerHeader": "Workflow Action Detail", "input": {"containerHeader": "Input Parameters"}, "output": {"containerHeader": "Output Parameters"}, "html": {"containerHeader": "HTML"}, "script": {"containerHeader": "<PERSON><PERSON><PERSON>"}, "customEditor": {"module": "<PERSON><PERSON><PERSON>", "wizard": "<PERSON>", "genericWizard": "Generic Wizard", "genericWizardTooltipCaption": "Generic Wizard which will be executed", "companyClerk": "Company Clerk", "wizardResult": "WizardResult", "wizardResultTooltipCaption": "Shows wizard result", "workflowTemplateId": "Follow Workflow", "workflowTemplateIdTooltipCaption": "The workflow to be started afterwards", "approverWorkflow": "Approver Workflow Template", "useCaseConfig": "Use Case Configuration", "portal": {"contactId": "ContactId", "portalAccessGroupId": "Portal Access Group Id", "portalInvitationId": "Portal Invitation Id", "portalBaseUrl": "Portal Base Url", "eMailSubject": "EMail Subject", "eMailBody": "EMail Body", "eMailRecipients": "EMail Recipients", "resultCode": "Result Code", "resultMessage": "Result Message", "confirmed": "Confirmed", "userEntity": "User.Ext.Provider.Entity", "portalAccessGroupList": "Portal Access Group List", "recipient": "Receiver", "invitationUrl": "Invitation Url", "reInvitationUrl": "Re-Invitation Url", "urlValidUntil": "<PERSON><PERSON>", "timeToLive": "Time to live in hours"}, "extendedUser": {"extendedScript": "<PERSON><PERSON><PERSON>", "extendedScriptTooltipCaption": "Js script for the extended user action", "extendedTitle": "Title", "extendedTitleTooltipCaption": "The title of the extended user action", "extendedSubtitle": "Subtitle", "extendedSubtitleTooltipCaption": "The subtitle of the extended user action", "extendedDialogConfig": "Dialog Config", "extendedDialogConfigTooltipCaption": "For configuring the user actions look and feel", "extendedDialogConfigExample": "Example"}, "startWorkflowApproverAction": {"useCaseConfig": "Configuration of Entity specific Wizards for which approval process shall be initialized.", "entityIdTooltipCaption": "Id of Header Entity. e.g. Contract header Id", "approverWorkflowTemplateTooltipCaption": "Entity specific templates for which approval process will be exceuted", "classifiedNumberTooltipCaption": "Should be equal to the classified number defined in approver config", "classifiedDateTooltipCaption": "Should be equal to the classified date defined in approver config", "classifiedAmountTooltipCaption": "Should be equal to the classified amount defined in approver config", "classifiedTextTooltipCaption": "Should be same as the classified text defined in approver config", "aproverInfo": "Ids of the assigned clerk for configured roles", "configErrors": "Errors encountered during assignment of approvers"}, "entityDataAction": {"entityType": "Entity Type", "entityTypeTooltipCaption": "Configuration of module specific entity action", "entityDataType": "Entity Data Type", "entityDataTypeTooltipCaption": "Defines the action to be performed on entity data", "entityObjectList": "Entity Object List", "entityObjectListTooltipCaption": "Operations like Create or Update to be performed on entity specific response", "entityIdentificationList": "Entity Identification List", "entityIdentificationListTooltipCaption": "Selection of entity Id to perform Read or Delete operation", "entityPropertyTooltipCaption": "Displays the reponse of entity output object from operations like read,delete,create or update", "entityInforMessageTooltipCaption": "Dsiplays any errors encountered during the entity data action"}, "savePath": "Save Path", "appendToExistingFile": "Append To Existing File", "warningMessage": "Warning Message", "language": "Language", "emailCount": "Email Count", "sender": "Sender", "transactionHeaderId": "Transaction Header Id", "isPopUp": "IsPopUp", "isPopUpTooltipCaption": "Should the user task pop up instantly or just be added to the taskbar?", "isNotification": "IsNotification", "isNotificationTooltipCaption": "Sends a notification message to the user which can easily be cleared via Clear Notification button on the taskbar", "EvaluateProxy": "EvaluateAbsentsClerkProxy", "EvaluateProxyTooltipCaption": "In case of absence the task will be assigned to the configured clerk proxy", "DisableRefresh": "DisableRefresh", "DisableRefreshTooltipCaption": "Disables the refresh of the associated records in the respective module", "AllowReassign": "Allow Reassign", "AllowReassignRefreshTooltipCaption": "Allow clerk reassignment of this task", "accessToken": "AccessToken", "accessTokenTooltipCaption": "To authenticate access to the API", "accessTokenType": "AccessTokenType", "accessTokenTypeTooltipCaption": "Type of the AccessToken", "action": "Action", "startContext": "Start Context", "isReadOnly": "The selected Template is read-only. The editor is inactive.", "containerHeader": "Action Parameters", "formula": "Formula", "message": "Message", "entityName": "Entity", "entityId": "Entity Id", "entityIdTooltipCaption": "The ID for the start entity e.g RfqHeaderId", "entityIdArray": "Array of Entity Id´s", "id": "Id", "mainEntityId": "Main Entity Id", "groupString": "Group By", "displayWorkflow": "By workflow", "displayInstance": "By instance", "fileList": "File List", "fileStructure": "File Structure", "archiveName": "Archive Name", "property": "Property", "statusName": "Entity Name", "objectId": "Object Id", "newStatus": "New Status", "newStatusId": "New Status Id", "oldStatus": "Old Status", "oldStatusId": "Old Status Id", "projectId": "Project Id", "remark": "Remark", "webApiURL": "WebApi URL", "outputClientGet": "Data", "params": "Parameter", "headers": "Headers", "headersTooltipCaption": "additional headers for the HTTP request", "editorDisplayError": "You use a reserved character like ':' or ';' in one of your (key:value) pairs, please use 'Expert-Mode'.", "body": "Body", "data": "Data", "bp": "Business Partner", "company": "Company", "bpId": "Business Partner Id", "bpIds": "Business Partner Ids", "bpIdsTips": "Business Partner Ids, eg: 100,101,102,103,104.", "contactIds": "Contact Ids", "contactIdsTips": "Contact Ids, eg: 100,101,22,,100.", "companyId": "Company Id", "outputUpdateFinanceCode": "Result Code", "outputUpdateFinance": "Result Message", "sectionId": "SectionId", "code": "Code", "invoiceId": "Invoice Id", "newCode": "New Code", "isSuccess": "Is Success", "returnValue": "Return Value", "documentNo": "Document No.", "assetNo": "Asset No.", "assetDescription": "Asset Description", "invTransactionId": "InvTransaction Id", "invTransactionIds": "InvTransaction Ids", "InventoryHeaderIds": "InventoryHeader Ids", "ProjectDocumentIds": "Project Document Ids", "FileArchiveDocIds": "FileArchive Doc Ids", "deleteRevisions": "Delete Revisions", "deleteHistories": "Delete Histories", "deleteFileArchives": "Delete FileArchives", "billNo": "Bill No", "billingId": "Billing Id", "newBillNo": "New BillNo", "bilTransactionIds": "BilTransaction Ids", "transIds": "TransactionHeader Ids", "commentText": "CommentText", "outputResult": "Result", "outputError": "Failure", "mail": {"from": "From", "receivers": "Receivers", "cc": "CC", "bcc": "BCC", "subject": "Subject", "body": "Body", "attachements": "Attachments", "replyToList": "Reply To List", "isHtml": "HTML", "isHtmlTooltipCaption": "Custom HTML that will be shown in the extended user action dialog", "saveMail": "save Mail", "useDefaultCredentials": "Use Default Credentials"}, "decisionDeletePathHeader": "Decision path selection", "decisionPatchSelection": "Select the path that should remain.", "report": "Report", "exportType": "Export Type", "fileName": "File Name", "fileNameTooltipCaption": "Filename for form data", "characteristic": "Characteristic", "url": "Url", "urlTooltipCaption": "URL for the HTTP request", "fileId": "File ID", "fileIdTooltipCaption": "ID or UUID for filearchive", "content": "Content", "contentTooltipCaption": "Encoded with UTF8 and content type is application/json", "projectDocument": "Project Document", "projectDocumentTooltipCaption": "The project document which is passed in the content of the request", "externalAPI": "External API", "selfAPI": "Self API", "sql": "SQL", "fieldSeparator": "Field Separator", "server": "Server", "database": "Database", "username": "Username", "password": "Password", "columns": "Columns", "workflow": "Template", "storedProcedureName": "Stored Procedure Name", "userform": "Form", "userformId": "Form Id", "userformData": "Form Data", "context": "Context", "contextOutputTooltipCaption": "The result of the extended user action will be written in the output context", "contextTooltipCaption": "The start context for this action instance", "resultContex": "Result Context", "description": "Description", "outputUserform": "Form Data Id", "messageBox": "Message Box", "docId": "Doc Id", "docRef": "Document Reference", "projectDocu": {"project": "Project Id", "bp": "Business Partner Id", "certificate": "Certificate Id", "structure": "Structure Id", "materialCatalog": "MaterialCatalog Id", "package": "Package Id", "rfq": "RFQ Id", "qtn": "QTN Id", "con": "CON Id", "pes": "PES Id", "inv": "INV Id", "schedule": "Schedule Id", "activity": "Activity Id", "est": "EST Id", "req": "REQ Id", "output": "Document List"}, "project": "Project", "template": "Templatename", "templateId": "Template Id", "costcode": "Update Cost Code", "commodity": "Update Commodity", "succeeded": "Succeeded", "errorMessage": "Error Message", "ProjectId": "Project", "Templates": "Templates", "outputCharacteristic": "Characteristic", "sqlActionOutput": "Output", "outputSaveObject": "Entity Property", "outputInfoMessage": "Entity Info Message", "outputReport": "Doc Id", "outputChangeStatus": "New Status", "outputClientPost": "Response", "outputClientPostStatus": "Response Status", "outputTextDocu": "Text", "outputServerAction": "Output", "outputServerActionTooltipCaption": "Content of the response body", "responseHeader": "Response Header", "responseHeaderTooltipCaption": "Content of the response header", "outputStored": "Output", "clerkRole": "Clerk Role", "clerkRoleId": "Clerk Role Id", "clerk": "Clerk", "projectClerk": "Project Clerk", "structure": "Structure", "structureId": "Structure Id", "status": "Status", "category": "Document Category", "docType": "Document Type", "docTypeId": "Document Type Id", "prjDocType": "Project Document Type", "prjDocTypeId": "Project Document Type Id", "certificate": "Certificate", "certificateId": "Certificate Id", "materialCatalog": "Material Catalog", "materialCatalogId": "Material Catalog Id", "materialCatalogCodes": "Material Catalog Codes", "updatedMaterialCatalogIds": "Updated Material Catalog Ids", "package": "Package", "packageId": "Package Id", "subPackageId": "Sub Package Id", "RequisitionId": "Requisition Id", "requisitionId": "Requisition Id", "requisitionIdTips": "Requisition Id.", "autoCopyBidder": "<PERSON><PERSON> suggested bidders to RFQ", "autoCopyBidderTips": "true or false, default to true.", "rfq": "RFQ", "rfqId": "RFQ Id", "qtn": "Quote", "qtnId": "Quote Id", "con": "Contract", "conId": "Contract Id", "pes": "PES", "pesId": "PES Id", "inv": "Invoice", "invId": "Invoice Id", "prjStockId": "PrjStock Id", "materialId": "Material Id", "productId": "Product Id", "schedule": "Schedule", "scheduleId": "Schedule Id", "activity": "Activity", "activityId": "Activity Id", "est": "Estimate", "estId": "Estimate Id", "req": "REQ", "reqId": "REQ Id", "qto": "QTO", "qtoId": "QTO Id", "infoRequest": "Info Request", "infoRequestId": "Info Request Id", "controlUnit": "Controlling Unit", "controlUnitId": "Controlling Unit Id", "location": "Location", "locationId": "Location Id", "ppsItem": "PPS Item", "ppsItemId": "PPS Item Id", "ppsHeaderId": "PPS Header Id", "ppsHeader": "PPS Header", "source": "Source", "sourceId": "Source Id", "dispatchHeader": "Dispatch Header", "dispatchHeaderId": "Dispatch Header Id", "lgmJob": "LGM Job", "lgmJobId": "LGM Job Id", "trsRoute": "Transport Route", "trsRouteId": "Transport Route Id", "earliestStart": "Earliest Start", "latestEnd": "Latest End", "duration": "Duration", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "probability": "Probability", "salesBid": "Sales Bid", "salesBidId": "Sales Bid Id", "salesOrder": "Sales Contract", "salesOrderId": "Sales Contract Id", "salesWip": "Sales Wip", "salesWipId": "Sales Wip Id", "salesBill": "Sales Billing", "salesBillId": "Sales Billing Id", "barCode": "Bar Code", "comment": "Comment", "revision": "Revision", "contract": "Contract", "contractId": "Contract Id", "commentBox": "Comment Box", "idIntern": "<PERSON><PERSON> <PERSON><PERSON>", "msgStatus": "Message Status", "rfqHeaderId": "RfqHeaderId", "bidderId": "BidderIds", "exceptionStopped": "Abort if exception", "calendarEvent": {"receiverId": "Receiver Id", "startTime": "Start Time", "endTime": "End Time", "importance": "Importance", "sensitivity": "Sensitivity", "isReminderOn": "<PERSON>minder On", "reminderMinutes": "Reminder Minutes Before Start", "result": "Result"}, "filterChain": {"inFilterChain": "Original Filter Chain", "outFilterChain": "Resulting Filter Chain"}, "model": {"propKeys": "Property Keys", "propKey": "Property Key", "propKeyAlias": "<PERSON><PERSON>"}, "rubricCategory": "Rubric Category", "rubricCategoryId": "Rubric Category Id", "trsRequisitionId": "Transport Requisition Id", "shiftTime": "Shift Time", "resRequisitionId": "Resource Requisition Id", "characteristicValues": "Characteristic Values", "receivingJobId": "Receiving Job Id", "wizardParas": "Wizard Parameters", "communicationChannelId": "Communication Channel Id", "reportType": "Report Type", "addressId": "Address Id", "businessPartnerId": "Business Partner Id", "orderHeaderId": "Sales Contract(Order) Id", "boqOrEstimateHeaderId": "Boq/Estimate Header Id", "endDate": "End Date", "logisticJobId": "Logistic Job Id", "checkValidate": "Check Validate", "options": "Options", "basisContractId": "Basis Contract Id", "projectChangeId": "Change Request Id", "autoCorrect": "Auto Correct", "runDependentWorkflow": "Run Dependent Workflow", "dateShift": {"eventId": "Event Id", "mode": "DateShift Mode", "isFullShift": "Is FullShift", "dateType": "DateType", "startDate": "Start Date", "endDate": "End Date", "logReasonGroup": "Log Reason Group", "updateReason": "Update Reason", "updateRemark": "Update Remark"}, "boq": {"boqHeaderId": "<PERSON><PERSON> Header Id", "targetHeaderId": "Target Header Id", "moduleName": "Module Name"}, "updateContract": {"contractId": "Contract Id", "orderNo": "Order No.", "handOverId": "Hand Over Id", "userDefined1": "User Defined 1", "userDefined2": "User Defined 2", "userDefined3": "User Defined 3", "pesId": "Pes Id", "pesNo": "Pes No.", "pesTransactionIds": "PesTransaction Ids", "conTransactionIds": "ContractTransaction Ids"}, "updateTransaction": {"wipId": "WIP Id", "wipNo": "WIP No", "transactionIds": "Transaction Ids", "ordId": "Sales Contract Id", "ordNo": "Sales Contract No"}, "evaluationId": "Evaluation Id", "evaluationSchemaId": "Evaluation Schema Id", "evaluationMotiveId": "Evaluation Motive Id", "reqClerkId": "<PERSON>q <PERSON> Id", "prcClerkId": "Prc Clerk Id", "contact1Id": "Contact 1 Id", "contact2Id": "Contact 2 Id", "subsidiaryId": "Subsidiary Id", "evaluationDate": "Evaluation Date", "authorizationLevel": "Authorization Level", "clerkRoleArray": "Clerk-<PERSON>", "authorizationLevelOfEvaluation": "Evaluation", "authorizationLevelOfGroup": "Group", "authorizationLevelOfSubGroup": "Sub Group", "createContracts": "Create seperate contract by each requisition", "createOneContract": "Create one contract by all requisitions", "approvers": "Approvers", "approvalLevel": "Approval Level", "clerkBase": "Clerk Base", "roleBase": "Role Base", "isParallel": "IsParallel", "clerkId": "Clerk Id", "level": "Level", "approval": "Approval", "dispRecordIds": "Dispatching Record Ids", "dispatchingRecords": "Dispatching Records Data", "mode": "Mode", "isCreateWIP": "Create WIP", "classifiedNumber": "Classfied Number", "classifiedDate": "Classfied Date", "classifiedAmount": "Classfied Amount", "classifiedText": "Classfied Text", "aproverInfo": "Assigned Clerks", "configErrors": "Configuration Errors", "vatNo": "Vat No.", "outputCheckVatResponse": "Response", "outputCheckVatValid": "<PERSON><PERSON>", "modelCase": "Model Case", "aiInputData": "AI Input Data", "isCanceled": "Is Canceled", "oneDrive": {"accessToken": "Access Token", "archiveIds": "Archive Id(s)", "shareLink": "Generate a share link", "accessRight": "Access Right:", "anyoneLink": "Anyone with the link", "organizationLink": "People in your organization with the link", "specificPeople": "Specific People", "lifespan": "Lifespan", "outputOneDrive": "Output", "allowEdit": "Allow Editing", "blockDownload": "Block Download", "people": "People", "oneDriveItemId": "OneDrive Item Id(s)", "archive": "Archive", "cleanedDocumentCount": "Cleaned Document Count", "syncDocumentCount": "Synced Document Count", "exceedsHours": "Only Trigger When Document's Last Modified Time Exceeds (X) Hours"}, "chatbot": {"question": "Question", "hint": "Hint", "isOptional": "Optional", "text": "Text", "card": "Card", "grid": "Grid", "list": "List", "displayType": "Display Type", "content": "Content", "cardCaptain": "Card Captain", "cardStyle": "Card Style", "isNavigate": "Is Navigate", "module": "<PERSON><PERSON><PERSON>", "navigateField": "Navigation Field"}, "replaceVariable": {"textModuleId": "Text Module", "queryItemId": "Context Entity Id", "result": "Result", "textModule": "Text Module Id", "language": "Data Language", "languageId": "Data Language Id"}, "teams": {"accessToken": "AccessToken", "from": "From", "to": "To", "sendToAUser": "Send to a user", "sendToAChannel": "Send to a channel", "userId": "User Id", "userPrincipalName": "User Principal Name", "message": "Message", "fileArchiveId": "File Archive Id", "html": "html", "teamsId": "Teams Id", "teamsName": "Teams Name", "channelId": "Channel Id", "channelName": "Channel Name", "resultCode": "Result Code", "resultMessage": "Result Message"}, "plannedQtySource": {"boqItem": "Bo<PERSON>", "estLineItem": "Est Line Item", "estResource": "Est Resource", "estimate": "Estimate"}, "estimateHeaderId": "Estimate Header Id", "jobTypeId": "Job Type Id", "createPpsHeader": "Create<PERSON><PERSON><PERSON><PERSON>er", "siteIdForCreatingPpsHeader": "SiteId(For Creating PpsHeader)", "clerkIdForCreatingPpsHeader": "ClerkId(For Creating PpsHeader)", "customFunction": "Custom Function", "getDetailedHttpErrorMessage": "Get detailed http Error Message", "getDetailedHttpErrorMessageTooltipCaption": "The Body and all Headers of the unsuccessful http Request Response get written into the Exception Message", "NotMappedToSource": "NotMappedToSource"}, "status": {"started": "Started", "running": "Running", "failed": "Failed", "finished": "Finished", "validationError": "Validation Error"}, "checklist": {"PrjProject": "Enter project ID", "HsqCheckListTemplateId": "Enter template ID", "createFromCheckListTemplate": "Create Checklist from Checklist Template", "createFromActivityTemplate": "Create Checklist via today's Activity of specified project", "distinctChecklist": "Create distinct checklist from each eligible activity", "createNewCheckListForNewActivityOnly": "Create check list with new activity only", "createNewCheckListForAllActivities": "Create check list with all activities regardless existence", "note": "Note : only work for activity assgined with checklist template."}}, "sidebar": {"no": "No", "startWorkflow": "Start Workflow for selected Entity", "startWorkflowForEveryEntity": "Start Workflow for every Entity", "startWorkflowForEverySelectedEntity": "Start one Workflow for all selected Entities", "description": "Description", "status": "Status", "error": "Error", "history": "History", "sorting": {"description": {"asc": "Description (Asc)", "desc": "Description (Desc)"}}, "grouping": {}, "ReassignToClerk": "Reassign to a different clerk", "ReassignToSender": "Reassign to me?", "AssignNewTaskToClerk": "Add another clerk in approval"}, "workflowAction": {"entity": "Workflow Action", "errors": {"actionIdMissing": "No action is defined.", "actionNotExist": "The action does not exist.", "actionTypeWrong": "The action type was wrong. It was automatic corrected, please save the workflow.", "obsoleteWarning": "The action 'user decision' is obsolete. The support for this action elapse.", "saveEntityAction": {"entityPropertyError": "The EntityProperty parameter has a incorrect value. You should use the curly braces syntax."}, "actionNewParam": "Action has new params: ", "actionNonexistentParam": "The following params do not exist in Action: ", "actionNotAvailable": "The action is no longer available", "textModuleTranslation": "The following translation where not found in Text Module"}}, "modalDialogs": {"defaultSecurity": "Default Security", "integratedSecurity": "Integrated Security", "wizardLinkHeader": "Wizard Link Editor", "workflowLinkHeader": "Workflow Link Editor", "documentLinkHeader": "Document Link Editor", "selectContentHeader": "Dropdown Data Editor", "pinboardContentHeader": "Pinboard Data Editor", "wizard": "<PERSON>", "linkName": "Display Name", "reportLinkHeader": "Report Link Editor", "pinboardOnlyForWorkflow": "Internal grouping in workflow", "module": "<PERSON><PERSON><PERSON>", "report": "Report", "textInputHeader": "Text Input", "defaulttext": "Default Text", "type": "Type", "comboboxContentHeader": "Combobox Data Editor", "defaultRadio": "Standard-Mode", "expertRadio": "Expert-Mode", "singleRadio": "Single-Select-Mode", "multiRadio": "Multi-Select-Mode", "singleDocRadio": "Single-Doc-Mode", "multiDocRadio": "Multi-Doc-Mode", "selectItems": "Dropdown Items", "labelEditorHeader": "Label Editor", "tableEditorHeader": "Table Editor", "Reassign": "Reassign", "tableData": "Table Data", "labelText": "Label Text", "labelFormat": "Label Formatting", "url": "Address", "wwwLinkHeader": "Link Editor", "reportParameter": "Report Parameters", "documentId": "Document ID", "documentIdsAndNames": "Document ids and names", "coverLetter": "Cover Letter", "reports": "Reports", "reportError": "The report could not be generated.", "reportBody": "The report is being generated...", "clerkLookup": "Clerk Lookup", "materialLookup": "Material Lookup", "clerk": "Clerk", "uploadDocuments": "Upload Documents", "singleDocument": "Single Document", "mulTipleDocument": "Multiple Document", "fileName": "File name", "filePath": "File path", "scriptPreview": "Script Preview", "cancelButton": "Cancel", "closeButton": "Close", "ReAssign": "ReAssign", "labelTextEscapeHtml": "Escape HTML", "validateCurrentAction": "Are you sure you want to update the following action:", "deleteCurrentActionFromWorkflow": "Are you sure you want to delete the following action:", "AllowReassign": "Reassign", "Assign": "Assign", "remove": "Remove"}, "debug": {"containerHeader": "Debug", "dialog": {"header": "Workflow Debug", "context": "Context", "action": "Action"}, "alertMessage": {"title": "The response from an current action is too large"}, "debugBreakpoint": {"missingBreakpoint": " Missing Breakpoint", "breakpointAlert": "Please add at least one breakpoint before starting to debug.", "noBreakpointsTitle": "No Breakpoints", "noBreakpoints": "Cannot delete, No breakpoints found.", "title": "Exception while debugging", "detailedErrorMessage": "An exception has occurred likely due to misconfiguration at action: {{actionDescription}}. \n The error message says: \"{{exceptionMessage}}\"\nPlease check context for more detail."}}, "toolbar": {"editOptions": "Edit Options", "insertNumberElements": "Number Fields", "insertTextElements": "Text Fields", "insertSelectElements": "Select Fields", "insertDesignElements": "Design Items", "insertLinkElements": "Links"}, "controls": {"table": "Table", "space": "Space", "divider": "Divider", "label": "Label", "linkToProject": "Link to Project", "linkToDocument": "Link to Document", "linkToEntity": "Link to Entity", "linkToWizard": "Link to Wizard", "linkToReport": "Link to Report", "linkToUrl": "Link to URL", "linkToWorkflow": "Link to Workflow", "linkToPinboard": "Link to Pin Board", "defaults": {"openWorkflow": "Start Workflow", "openReport": "Open Report", "openDocument": "Open Document", "openUrl": "Open Address", "openEntity": "Open Entity"}}, "escalate": "Escalate", "start": {"idDialog": {"header": "Start Workflow", "body": "Please insert the id of the calling entity."}}, "version": {"download": "Download Version", "upload": "Import Version", "createByVersion": "Create new Template by Version", "deleteTemplateVersion": {"header": "Confirm Template Version Delete?", "deleteConfirmation": "Do you want to delete the selected Template Version(s) permamently? All corresponding working data will also be deleted."}}, "wizard": {"header": "Workflow Wizard", "text": "Status of the current workflow:", "statusText": {"1": "The workflow is currently running.", "2": "The workflow is finished correctly.", "3": "The workflow is escalated.", "4": "The workflow waits for a user input.", "5": "The workflow is failed.", "6": "The workflow is killed.", "7": "The workflow encountered a validation exception.", "missingTemplate": "The parameter TemplateId of the wizard ist empty.", "workflowFailed": "failed"}}, "entity": {"lookup": {"title": "Assign <PERSON>"}}, "sendRfQDialog": {"header": "Send RFQ", "currentConfiguartion": "Current Configuration", "selectedBidders": "Selected Bidders", "coverLetter": "Cover Letter / E-Mail Body", "dataFormat": "Data Formats", "Reports": "Reports", "mandatoryReports": "Mandatory Reports", "additionalSelectedReports": "Additional selected reports:", "documents": "Documents", "projectDocuments": "Project Documents:", "reQDocuments": "ReQ Documents:", "rfQDocuments": "RfQ Documents:", "clerkDocuments": "Clerk Documents", "clerkEmailInBcc": "Clerk Email in BCC", "sendWithOwn": "Send with own Mailaddress", "generateSafeLink": "Generate Safe Link", "disableDataFormatExport": "Disable Data Format Export"}, "errorRfQ": {"errorWhileSendingEMail": "Errors occurred while sending the E-mail(s). Please contact your System Administrator.", "wrongReportSelectedError": "At least one wrong Cover Letter Template is selected. For Emails with Safe Link select 'RfQ Coverletter Portal', for Emails without Safe Link select 'RfQ Coverletter Email'."}, "sendRfQBidderSelection": {"bidderSelection": "Bidder Selection", "select": "Select", "companyName": "Company Name", "status": "Status", "contact": "Contact Selection", "contactFirstName": "Contact First Name", "contactLastName": "Contact Last Name", "contactEMail": "Contact E-Mail", "cCSelect": "CC Select", "cCCompanyEMail": "CC Company E-Mail", "settings": "Settings"}, "sendRfQDataformat": {"dataFormats": "Data Formats", "select": "Select", "dataFormat": "Data Format"}, "sendRfQDocuments": {"projectDocuments": "Project Documents", "rEQDocuments": "REQ Documents", "rfQDocuments": "RfQ Documents", "select": "Select", "type": "Type", "documentType": "Document Type", "documentCategory": "Document Category", "status": "Status", "description": "Description", "date": "Date", "originFileName": "Origin File Name", "preview": "Preview"}, "sendRfQEmailBody": {"subject": "Subject", "coverLetterEMailBody": "Cover Letter / E-Mail Body Templates", "placeholder": "Placeholder", "select": "Select", "templateName": "Template Name", "description": "Description", "preview": "Preview"}, "sendRfQReports": {"mandatoryReports": "Mandatory Reports", "reportSelection": "Report Selection", "select": "Select", "templateName": "Template Name", "description": "Description", "preview": "Preview"}, "sendRfQTransmission": {"transmissionProtocol": "Transmission Protocol", "bidder": "<PERSON><PERSON><PERSON>", "status": "Status", "warningOccurred": "Following errors occurred after sending out the email", "reportErrorMessage": "Error while generating report with Id {{reportId}} and Template Name {{templateName}}", "gaebErrorMessage": "Error while exporting Boq with Id {{boqId}} and Reference No. {{boqRefNo}}", "zipErrorMessage": "Error while creating Zip File", "materialErrorMessage": "Error while exporting Material with PrcHeaderFk {{materialId}} and Code {{materialCode}}", "sendMailErrorMessage": "Error while sending Email, Error Message: \"{{errorMessage}}\"", "contactMailErrorMessage": "No valid Email Address found for Bidder \"{{bidderName}}\"", "undefinedErrorMessage": "An Error occurred with no specified Error Message", "sendAccessTokenMailErrorMessage": "Error while sending access token email, error message: \"{{errorMessage}}\"", "mailSaveErrorMessage": "Error while saving email to project documents: \"{{errorMessage}}\"", "generateSafeLinkErrorMessage": "Error while generating safe link: \"{{errorMessage}}\"", "projectDocumentMailSaveErrorMessage": "Error while saving the mail to project documents: \"{{errorMessage}}\"", "projectDocumentAttachmentSaveErrorMessage": "Error while saving the attachments to project documents: \"{{errorMessage}}\"", "businessPartnerStatusUpdateErrorMessage": "Error while updating the business partner status: \"{{errorMessage}}\"", "rfqStatusHistoryUpdateErrorMessage": "Error while updating the rfq status history: \"{{errorMessage}}\"", "rfqSendHistoryMailUpdateErrorMessage": "Error while updating the rfq send history (mail): \"{{errorMessage}}\"", "rfqSendHistoryAttachmentUpdateErrorMessage": "Error while updating the rfq send history (attachments): \"{{errorMessage}}\""}, "approvementProcessWizard": {"wizardHeader": "Approvement Process Wizard", "header": "Create Approvement Process", "entityTab": {"header": "Header Information", "entity": "Entity", "templateName": "Template Name"}, "endStatesDefintionTab": {"header": "End States Definition", "approved": "Approved State", "rejected": "Rejected State"}, "stepDefinitionTab": {"header": "Step Definition", "table": {"position": "Position", "status": "Status", "role": "Role", "action": "Action", "rule": "Rule"}}, "CreateBtn": "Create Template"}, "exportZipWizard": {"wizardHeader": "Export Workflow Template(s) Wizard", "header": "Workflow Export Templates", "leftPanel": {"templateSelections": "Template Selections", "exportOptions": "Export Options", "exportFileName": "Export File Name"}, "searchTemplatesTab": {"header": "Search Templates", "selectedTemplates": "Selected Templates", "workflowTemplateID": "Workflow Template ID", "description": "Description", "comment": "Comment", "searchList": "Search List", "selection": "Selection"}, "templateExportOptionsTab": {"header": "Template Export Options", "zipFileName": "File Name", "statusMatrix": "Status Matrix", "subscribedEvents": "Subscribed Events", "versionContext": "Template Context", "wizardDefinition": "Wizard Definition", "schedulerDefinition": "Scheduler Definition", "allTemplateVersions": "All Template Versions"}, "successMsz": {"successExportHeader": "Export Successfully", "successExportBodyText": "Workflow template(s) have been successfully exported.", "successImportHeader": "Import Successfully", "successImportBodyText": "Workflow template(s) have been successfully imported."}}, "importWizard": {"wizardHeader": "Import Workflow Template(s) Wizard", "importBtn": "Import"}, "approve": {"roleBased": "Role Based", "apprCount": "Number of Approved levels:", "ccCount": "Number of Cc levels", "apprLevel": "Approved level ", "refresh": "Refresh", "delete": "Delete", "save": "Save", "saveAs": "Save As", "TemplateSaveAreaLabel": "Location", "TemplateSaveNamePlaceHolder": "Template name", "TemplateSaveNameLabel": "Template name", "TemplateSaveTitle": "Save template: Please enter location and template name.", "TemplateConfirmDeleteTitle": "Delete template", "TemplateConfirmDeleteBody": "Do you really want to delete the template '{{p1}}'?"}, "transaction": {"selectDate": "Select Data to Transfer", "selectSearch": "Search", "filterLabel": "Eligible Transaction Headers"}, "service": {"errorMessage": "Only JSON formated Workflow Template can be Imported. For Zip format please use \"Importing Templates Wizard\" ", "errorHeaderTitle": "Importing wrong format", "errorImportVersionMessage": "The selected version of workflow template in Not a Valid JSON format", "errorImportVersionHeaderTitle": "Invalid format"}, "fileUpload": {"title": "File Upload"}, "insertedAt": "Inserted At", "insertedBy": "Inserted By", "updatedAt": "Updated At", "updatedBy": "Updated By", "startWorkflow": "Start Workflow", "pinned": "Pinned", "entityIdList": "Ids", "approver": {"basicData": "Basic Data", "clerkRole": "Clerk Role", "evaluationLevel": "Evaluation Level", "timeToApprove": "Time To Approve (hours)", "classifiedNum": "Classified Number", "classifiedDate": "Classified Date", "classifiedAmount": "Classified Amount", "classifiedText": "Classified Text", "formular": "Formular", "approverConfigContainerTitle": "Approver Config", "isMail": "Send Mail To Approver", "isSendMailToClerk": "Send Mail To Clerk", "needComment4Approve": "Approval Comment Required", "needComment4Reject": "Rejection Comment Required", "allowreject2level": "Allow Rejection To Level", "approverContainerTitle": "Approver", "instanceFk": "Instance", "actionInstanceFk": "Action Instance", "clerkFk": "Clerk", "isApproved": "Is Approved", "approverDecision": "Approver Decision", "approved": "Approved", "rejected": "Rejected", "pending": "Pending", "evaluatedOn": "Evaluation Date", "comment": "Comment", "dueDate": "Duedate", "selectedApproverConfigMissing": "Please select an Approver Configuration", "approverLevels": "Reject to level", "currentLevel": "Current Approval Level", "approverComment": "Comments", "rejectToClerk": "Select clerk(s) for rejection"}, "entityDataAction": {"create": "Create", "read": "Read", "update": "Update", "delete": "Delete"}}}}