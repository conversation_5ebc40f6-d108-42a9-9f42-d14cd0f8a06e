/*
* Copyright(c) RIB Software GmbH
*/

import { inject, Injectable, ProviderToken } from '@angular/core';
import { BaseValidationService, IEntityRuntimeDataRegistry, IValidationFunctions, PlatformSchemaService, ValidationServiceFactory } from '@libs/platform/data-access';
import { TimekeepingCertificateDataService } from './timekeeping-certificate-data.service';
import { ICertificateDocumentEntity } from '@libs/resource/interfaces';

@Injectable({
	providedIn: 'root'
})

export class TimekeepingCertificateDocumentValidationService extends BaseValidationService<ICertificateDocumentEntity> {
	private validators: IValidationFunctions<ICertificateDocumentEntity> | null = null;

	public constructor(protected dataService: TimekeepingCertificateDataService) {
		super();

		const schemaSvcToken: ProviderToken<PlatformSchemaService<ICertificateDocumentEntity>> = PlatformSchemaService<ICertificateDocumentEntity>;
		const platformSchemaService = inject(schemaSvcToken);

		platformSchemaService.getSchema({moduleSubModule: 'Timekeeping.Certificate', typeName: 'EmployeeCertifiedDocumentDto'}).then(
			(scheme) => {
				this.validators = new ValidationServiceFactory<ICertificateDocumentEntity>().provideValidationFunctionsFromScheme(scheme, this);
			}
		);
	}

	protected generateValidationFunctions(): IValidationFunctions<ICertificateDocumentEntity> {
		if(this.validators !== null) {
				return this.validators;
		}

		return {};
	}

	protected getEntityRuntimeData(): IEntityRuntimeDataRegistry<ICertificateDocumentEntity> {
		return this.dataService;
	}


}
