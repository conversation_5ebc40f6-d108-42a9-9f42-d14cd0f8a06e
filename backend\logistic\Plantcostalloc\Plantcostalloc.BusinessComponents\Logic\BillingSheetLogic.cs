using System;
using System.ComponentModel.Composition;
using System.Transactions;

using RIB.Visual.Platform.Core;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;
using RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Platform.OperationalManagement;

using RIB.Visual.Basics.Core.Core;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.BusinessComponents;

using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RVPBC = RIB.Visual.Platform.BusinessComponents;
using System.Collections.Generic;
using System.Linq;

namespace RIB.Visual.Logistic.Plantcostalloc.BusinessComponents
{
	/// <summary>
	/// Logic class for type BillingSheetEntity stored in LGM_BILLINGSHEET
	/// </summary>
	[Export("logisticbillingsheetstatus", typeof(IChangeStatus))]
	public class BillingSheetLogic : EntityUpdateLogic<BillingSheetEntity, IdentificationData>, IUpdateCompleteData, IChangeStatus
	{
		/// <summary>
		/// The singleton identifier instance for the <see cref="BillingSheetEntity"/> type
		/// </summary>
		private static readonly Lazy<CoreFinal.IIdentifier<BillingSheetEntity>> IdentifierInstance = IdentifierFactory.Create<BillingSheetEntity>("Id");

		/// <summary>
		/// BillingSheetLogic constructor
		/// </summary>
		public BillingSheetLogic()
		{
			PermissionGUID = "900884e23ac448c2b6003b48327cb0c9";
			Identifier = IdentifierInstance.Value;

			CompleteUpdater = this;

			OrderByExpressions = new[]
			{
				OrderTerm.Create(e => e.Id)
			};
			OrderByKey = e => Identifier.GetEntityIdentification(e);

			SetTempMatchingFunc<DdTempIdsEntity>((e, tmp) => (e.Id == tmp.Id));
		}

		/// <summary>
		/// Create an entity via creation data.
		/// </summary>
		/// <returns>The created instance</returns>
		public override System.Data.Entity.Infrastructure.DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// Gives access to the database mapping
		/// </summary>
		public override BillingSheetEntity Create(IdentificationData creationData)
		{
			IdentificationDataValidation.EnsurePKey1AndThrowExceptionIfNull(creationData, "BillingSheetLogic.Create");

         RVPBC.Permission.Ensure(PermissionGUID, Permissions.Create);

         var res = new BillingSheetEntity { Id = SequenceManager.GetNext(GetEntityTableName()) };
         res.ProjectFk = creationData.PKey1.Value;
         res.RecordNo = creationData.PKey3.HasValue ? (creationData.PKey3.Value + 1) * 10 : 10;

         res.RecordDate = DateTime.UtcNow;
         res.OperationQuantity = 1;
         res.Percentage01 = 100.00m;
         res.Percentage02 = 100.00m;
         res.Percentage03 = 100.00m;
         res.Percentage04 = 100.00m;
         res.Percentage05 = 100.00m;
         res.Percentage06 = 100.00m;

         RelationDefaultValueSetter.Handle(res, new List<Tuple<string, Action<BillingSheetEntity, int>, Func<BillingSheetEntity, int>>>() {
            new Tuple<string, Action<BillingSheetEntity, int>, Func<BillingSheetEntity, int>>("basics.customize.logisticsbillingsheetstatus", (e, i) => e.BillingSheetStatusFk = i, null),
            new Tuple<string, Action<BillingSheetEntity, int>, Func<BillingSheetEntity, int>>("basics.customize.logisticsbillingsheettype", (e, i) => e.BillingSheetTypeFk = i, null)
         });

         return res;
		}

		/// <summary>
		/// Get name of database table the entity is stored in
		/// </summary>
		/// <returns>The fix string "BillingSheet"</returns>
		protected override string GetEntityTableName()
		{
			return "LGM_BILLINGSHEET";
		}

		#region IUpdateCompleteData implementation

		IEnumerable<IIdentifyable> IUpdateCompleteData.HandleUpdate(IEnumerable<IIdentifyable> completeData)
		{
			var toBeUpdated = completeData.OfType<BillingSheetCompleteEntity>().ToArray();

			return this.DoUpdate(toBeUpdated);
		}

		private IEnumerable<BillingSheetCompleteEntity> DoUpdate(IEnumerable<BillingSheetCompleteEntity> completeData)
		{
			using (TransactionScope transaction = TransactionScopeFactory.Create())
			{
				foreach (var item in completeData)
				{
					if (item.BillingSheets != null)
					{
						Save(item.BillingSheets);
					}

					EntityUpdateDispatcher.Handle(item);
				}
				transaction.Complete();
			}

			return completeData;
		}

		#endregion IUpdateCompleteData implementation
		#region ILogisticPlantcostallocBillingSheetLogic implementation

		#endregion


		#region IChangeStatus implementation

		EntityBase IChangeStatus.ChangeStatus(IStatusIdentifyable identification, int statusId)
		{
			var entity = this.GetByFilter(e => e.Id == identification.Id).First();

			return SetStatusAndSave(entity, statusId, entity.BillingSheetStatusFk);
		}

		int IChangeStatus.GetCurrentStatus(IStatusIdentifyable identification)
		{
			var entity = this.GetByFilter(e => e.Id == identification.Id).First();

			return entity.BillingSheetStatusFk;
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="identifications"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public IEnumerable<int> GetCanChangeStatusEntities(IEnumerable<IStatusIdentifyable> identifications)
		{
			throw new NotImplementedException();
		}


		private BillingSheetEntity SetStatusAndSave(BillingSheetEntity billingSheet, int newStatusId, int oldStatusId)
		{			
			if (billingSheet != null)
			{
				billingSheet.BillingSheetStatusFk = newStatusId;
				Save(billingSheet);
			}

			return billingSheet;		
		}

		#endregion IChangeStatus implementation

		/// <summary>
		/// Change status
		/// </summary>
		/// <param name="identification">Identification of the element which state is to be set</param>
		/// <param name="newStatusId">Id of new state to be set</param>
		/// <param name="oldStatusId">Id of current (old) state</param>
		/// <returns></returns>
		public BillingSheetEntity SetStatus(IdentificationData identification, int newStatusId, int oldStatusId)
		{
			var entity = this.GetByFilter(e => e.Id == identification.Id).First();

			return SetStatusAndSave(entity, newStatusId, oldStatusId);
		}
	}
}
