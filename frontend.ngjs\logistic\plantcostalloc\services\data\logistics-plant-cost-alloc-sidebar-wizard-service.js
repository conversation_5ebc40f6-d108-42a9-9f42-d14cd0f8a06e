/**
 * Created by <PERSON><PERSON> on 16.06.2025
 */

(function (angular) {
	'use strict';
	/* global globals */
	/**
	 * @ngdoc factory
	 * @name logisticPlantCostAllocationSidebarWizardService
	 * @description
	 * Provides wizard configuration and implementation of all wizards of logistics price condition module
	 */

	const moduleName = 'logistic.plantcostalloc';
	angular.module(moduleName).service('logisticPlantCostAllocationSidebarWizardService', LogisticPlantCostAllocationSidebarWizardService);

	LogisticPlantCostAllocationSidebarWizardService.$inject = ['$http', '$translate', 'platformDialogService', 'basicsLookupdataSimpleLookupService', 'logisticPlantCostAllocProjectDataService', 'platformTranslateService', 'platformModalFormConfigService',
		'platformRuntimeDataService', 'platformSidebarWizardCommonTasksService', 'moment', 'servicesSchedulerUIFrequencyValues', 'basicsCommonChangeStatusService', 'logisticPlantCostAllocBillingSheetDataService'];

	function LogisticPlantCostAllocationSidebarWizardService($http, $translate, platformDialogService, basicsLookupdataSimpleLookupService, logisticPlantCostAllocProjectDataService, platformTranslateService, platformModalFormConfigService,
		platformRuntimeDataService, platformSidebarWizardCommonTasksService, moment, servicesSchedulerUIFrequencyValues, basicsCommonChangeStatusService, logisticPlantCostAllocBillingSheetDataService) {
		this.createRecordsForReserveBillingSheets = function createRecordsForReserveBillingSheets() {
			const title = $translate.instant('logistic.plantcostalloc.createRecordsForReserveBillingSheetsWizard.title');
			const selectedProject = logisticPlantCostAllocProjectDataService.getSelected();
			if (!selectedProject) {
				{
					platformDialogService.showInfoBox('logistic.plantcostalloc.createRecordsForReserveBillingSheetsWizard.selectedProjectMissing');
					return;
				}
			}

			const dataItem = {
				recordDate: null
			};

			let modalCreateConfig = {
				title: title,
				dataItem: dataItem,
				formConfiguration: {
					fid: 'logistic.plantcostalloc.createRecordsForReserveBillingSheetsWizard',
					version: '1.0.0',
					showGrouping: false,
					groups: [
						{
							gid: 'baseGroup'
						}
					],
					rows: [
						{
							gid: 'baseGroup',
							rid: 'recordDate',
							label: 'Record Date',
							label$tr$: 'cloud.common.entityDate',
							type: 'dateutc',
							model: 'recordDate',
							sortOrder: 2,
							required: true,
							validator: async function (entity, value) {
								const projectId = selectedProject.Id;
								const recordDate = value;

								let isReadOnly = false;
								if (projectId && recordDate) {
									isReadOnly = await operationQuantityValidator(projectId, recordDate);
								}

								if (isReadOnly) {
									entity.operationQuantity = 1.0;
									platformRuntimeDataService.readonly(entity, [{field: 'OperationQuantity', readonly: true}]);
								}
							}
						}
					]
				},
				handleOK: function handleOK(result) {
					if (result && result.data) {
						let data = {
							ProjectId: selectedProject.Id,
							RecordDate: result.data.recordDate
						};
						$http.post(globals.webApiBaseUrl + 'logistic/plantcostalloc/billingsheet/createbillingsheet', data
						).then(function (response) {
							if (response && response.data) {

								let modalOptions = {
									headerText$tr$: 'logistic.plantcostalloc.createRecordsForReserveBillingSheetsWizard.title',
									bodyTemplate: response.data,
									showOkButton: true,
									showCancelButton: true,
									resizeable: true,
								};
								platformDialogService.showDialog(modalOptions);
							}
						});
					}
				}

			};

			platformTranslateService.translateFormConfig(modalCreateConfig.formConfiguration);

			platformModalFormConfigService.showDialog(modalCreateConfig);

		};

		// helper function
		async function operationQuantityValidator(projectId, recordDate) {
			if (!projectId || !recordDate) {
				return false;
			}

		   const formattedRecordDate = recordDate.toISOString();
			const response = await $http.get(globals.webApiBaseUrl + 'logistic/plantcostalloc/billingsheet/operationquantityreadonlystatus', {
				params: {
					projectId: projectId,
					recordDate: formattedRecordDate
				}
			});

			if (response && typeof response.data === 'boolean') {
				return response.data;
			} else {
				console.warn('');
				return false;
			}
		}

		///////////////////////////  create job for billing sheet wizard /////////////////////////////
		this.createJobForBillingSheets = function () {

			platformTranslateService.translateObject(servicesSchedulerUIFrequencyValues, ['description']);
			let modalCreateConfig = {

				dataItem: {
					jobRepeatCount: null,
					jobRepeatUnit: null,
					startTime: moment(),
					comment: ''
				},
				title: $translate.instant('logistic.plantcostalloc.createJobForBillingSheetsTitle'),

				// formConfiguration
				formConfiguration: {
					fid: 'dailyBillingSheetJob',
					version: '1.0.0',
					showGrouping: false,
					groups: [
						{
							gid: 'baseGroup',
						}
					],
					rows: [
						{
							gid: 'baseGroup',
							rid: 'comment',
							label: 'Comment',
							label$tr$: 'common.comment',
							type: 'text',
							model: 'comment',
							sortOrder: 1
						},
						{
							gid: 'baseGroup',
							rid: 'repeatUnit',
							label: 'Repeat Unit',
							label$tr$: 'services.schedulerui.columns.repeatunit',
							type: 'select',
							model: 'jobRepeatUnit',
							options: {
								displayMember: 'description',
								valueMember: 'Id',
								items: 'servicesSchedulerUIFrequencyValues'
							},
							sortOrder: 2
						},
						{
							gid: 'baseGroup',
							rid: 'repeatCount',
							label: 'Repeat Count',
							label$tr$: 'services.schedulerui.columns.repeatcount',
							type: 'integer',
							model: 'jobRepeatCount',
							sortOrder: 3
						},
						{
							gid: 'baseGroup',
							rid: 'startTime',
							label: 'Start Time',
							label$tr$: 'services.schedulerui.columns.starttime',
							type: 'datetime',
							model: 'startTime',
							sortOrder: 4
						}
					]
				},
				handleOK: function handleOK(result) {
					let data = {
						Comment: result.data.comment,
						JobRepeatCount: result.data.jobRepeatCount,
						JobRepeatUnit: result.data.jobRepeatUnit,
						StartDate: result.data.startTime.utc().format()
					};
					$http.post(globals.webApiBaseUrl + 'logistic/plantcostalloc/billingsheet/createjobtoexecute', data)
						.then(function () {
							platformSidebarWizardCommonTasksService.showSuccessfullyDoneMessage(modalCreateConfig.title);
						});
				},

			};

			platformTranslateService.translateFormConfig(modalCreateConfig.formConfiguration);
			platformModalFormConfigService.showDialog(modalCreateConfig);
		};

		function changeBillingSheetStatus() {
			return basicsCommonChangeStatusService.provideStatusChangeInstance({
				refreshMainService: false,
				mainService: logisticPlantCostAllocProjectDataService,
				dataService: logisticPlantCostAllocBillingSheetDataService,
				statusField: 'BillingSheetStatusFk',
				codeField: 'Code',
				descField: 'Description',
				projectField: '',
				statusDisplayField: 'Description',
				title: 'logistic.plantcostalloc.changeBillingSheetStatus',
				statusName: 'logisticbillingsheetstatus',
				statusProvider: function () {
					return basicsLookupdataSimpleLookupService.getList({
						valueMember: 'Id',
						displayMember: 'Description',
						lookupModuleQualifier: 'basics.customize.logisticsbillingsheetstatus',
					}).then(function (respond) {
						return _.filter(respond, function (item) {
							return item.isLive;
						});
					});
				},
				updateUrl: 'logistic/plantcostalloc/billingsheet/changestatus',
				id: 1
			});
		}
		this.changeBillingSheetStatus = changeBillingSheetStatus().fn;
	}
})(angular);