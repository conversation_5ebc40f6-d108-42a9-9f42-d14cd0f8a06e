import { Directive, ElementRef, Renderer2, After<PERSON>ontentInit, OnDestroy, Input, Output, EventEmitter } from '@angular/core';
import { PopupResizeDirection } from '../model/resize-direction.enum';
import { ResizeEvent } from '../model/resize-event';

@Directive({
	selector: '[uiCommonPopupResizable]',
	standalone: true,
})
export class UiCommonPopupResizableDirective implements AfterContentInit, OnDestroy {
	private readonly zIndex = 90;
	private readonly el: HTMLElement;
	private readonly listeners: (() => void)[] = [];

	@Input()
	public uiCommonPopupResizable: PopupResizeDirection[] = [];

	@Output()
	public resizeStart: EventEmitter<ResizeEvent> = new EventEmitter();

	@Output()
	public resizing: EventEmitter<ResizeEvent> = new EventEmitter();

	@Output()
	public resizeEnd: EventEmitter<ResizeEvent> = new EventEmitter();

	public constructor(
		private elRef: ElementRef,
		private renderer: Renderer2,
	) {
		this.el = elRef.nativeElement;
	}

	public ngAfterContentInit() {
		this.uiCommonPopupResizable.forEach((d) => {
			switch (d) {
				case PopupResizeDirection.North:
					this.listeners.push(this.appendNorthDiv());
					break;
				case PopupResizeDirection.South:
					this.listeners.push(this.appendSouthDiv());
					break;
				case PopupResizeDirection.West:
					this.listeners.push(this.appendWestDiv());
					break;
				case PopupResizeDirection.East:
					this.listeners.push(this.appendEastDiv());
					break;
				case PopupResizeDirection.NorthEast:
					this.listeners.push(this.appendNorthEastDiv());
					break;
				case PopupResizeDirection.NorthWest:
					this.listeners.push(this.appendNorthWestDiv());
					break;
				case PopupResizeDirection.SouthEast:
					this.listeners.push(this.appendSouthEastDiv());
					break;
				case PopupResizeDirection.SouthWest:
					this.listeners.push(this.appendSouthWestDiv());
					break;
				default:
					throw new Error(`uiCommonResize doesn't recognize ${d} direction`);
			}
		});
	}

	public ngOnDestroy() {
		this.listeners.forEach((e) => e());
	}

	private createLineDiv(): HTMLDivElement {
		const div = this.renderer.createElement('div');
		this.renderer.setStyle(div, 'z-index', this.zIndex);
		this.renderer.addClass(div, 'ui-resizable-handle');
		return div;
	}

	private appendNorthDiv() {
		const div = this.createLineDiv();
		this.renderer.addClass(div, 'ui-resizable-n');
		this.renderer.appendChild(this.el, div);
		return this.resizeDiv(div, true, false, PopupResizeDirection.North);
	}

	private appendSouthDiv() {
		const div = this.createLineDiv();
		this.renderer.addClass(div, 'ui-resizable-s');
		this.renderer.appendChild(this.el, div);
		return this.resizeDiv(div, true, false, PopupResizeDirection.South);
	}

	private appendWestDiv() {
		const div = this.createLineDiv();
		this.renderer.addClass(div, 'ui-resizable-w');
		this.renderer.appendChild(this.el, div);
		return this.resizeDiv(div, false, true, PopupResizeDirection.West);
	}

	private appendEastDiv() {
		const div = this.createLineDiv();
		this.renderer.addClass(div, 'ui-resizable-e');
		this.renderer.appendChild(this.el, div);
		return this.resizeDiv(div, false, true, PopupResizeDirection.East);
	}

	private resizeDiv(div: HTMLDivElement, height: boolean, width: boolean, direction: PopupResizeDirection): () => void {
		let mouseMoveListener: (() => void) | null = null;
		let mouseUpListener: (() => void) | null = null;

		const mouseDownListener = this.renderer.listen(div, 'mousedown', (e: MouseEvent) => {
			const size = this.getOriginalSize(this.el);

			this.resizeStart.emit(new ResizeEvent(e, size.width, size.height));

			e.preventDefault();

			mouseMoveListener = this.renderer.listen('document', 'mousemove', (e: MouseEvent) => {
				if (width) {
					if (direction.includes(PopupResizeDirection.West)) {
						size.width -= e.movementX;
					} else {
						size.width += e.movementX;
					}
					this.el.style.width = `${size.width}px`;
				}
				if (height) {
					if (direction.includes(PopupResizeDirection.North)) {
						size.height -= e.movementY;
					} else {
						size.height += e.movementY;
					}
					this.el.style.height = `${size.height}px`;
				}
				this.resizing.emit(new ResizeEvent(e, size.width, size.height));
			});
			mouseUpListener = this.renderer.listen('document', 'mouseup', (e: MouseEvent) => {
				if (mouseMoveListener) {
					mouseMoveListener();
					mouseMoveListener = null;
				}
				if (mouseUpListener) {
					mouseUpListener();
					mouseUpListener = null;
				}
				this.resizeEnd.emit(new ResizeEvent(e, size.width, size.height));
			});
		});

		return () => {
			mouseDownListener();
			if (mouseMoveListener) {
				mouseMoveListener();
				mouseMoveListener = null;
			}
			if (mouseUpListener) {
				mouseUpListener();
				mouseUpListener = null;
			}
		};
	}

	private getOriginalSize(el: HTMLElement) {
		const size = {
			width: el.clientWidth,
			height: el.clientHeight,
		};

		const style = getComputedStyle(el);
		if (style.width) {
			size.width = parseFloat(style.width.substring(0, style.width.length - 2));
		}
		if (style.height) {
			size.height = parseFloat(style.height.substring(0, style.height.length - 2));
		}

		return size;
	}

	private createBlockDiv(): HTMLDivElement {
		const div = this.renderer.createElement('div');
		this.renderer.setStyle(div, 'z-index', this.zIndex);
		// TODO - The following line could be removed once resize icon can be loaded,
		this.renderer.setStyle(div, 'background', 'red');
		this.renderer.addClass(div, 'ui-resizable-handle');
		return div;
	}

	private appendSouthEastDiv() {
		const div = this.createBlockDiv();
		this.renderer.addClass(div, 'ui-resizable-se');
		this.renderer.appendChild(this.el, div);
		return this.resizeDiv(div, true, true, PopupResizeDirection.SouthEast);
	}

	private appendSouthWestDiv() {
		const div = this.createBlockDiv();
		this.renderer.addClass(div, 'ui-resizable-sw');
		this.renderer.appendChild(this.el, div);
		return this.resizeDiv(div, true, true, PopupResizeDirection.SouthWest);
	}

	private appendNorthEastDiv() {
		const div = this.createBlockDiv();
		this.renderer.addClass(div, 'ui-resizable-ne');
		this.renderer.appendChild(this.el, div);
		return this.resizeDiv(div, true, true, PopupResizeDirection.NorthEast);
	}

	private appendNorthWestDiv() {
		const div = this.createBlockDiv();
		this.renderer.addClass(div, 'ui-resizable-nw');
		this.renderer.appendChild(this.el, div);
		return this.resizeDiv(div, true, true, PopupResizeDirection.NorthWest);
	}
}
