﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by <PERSON>art Entity Developer tool using Entity Framework DbContext template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Data.Common;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Data.Entity.ModelConfiguration;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using System.Runtime.Serialization;
using System.Xml.Serialization;
using RIB.Visual.Platform.Common;
using System.Data.Entity.Core.EntityClient;
using System.Data.Entity.Core.Objects;
using System.Data.Entity.Core.Objects.DataClasses;
using RIB.Visual.Procurement.Contract.BusinessComponents;

namespace RIB.Visual.Procurement.Contract.BusinessComponents
{
    /// <summary/>
    public partial class ModelBuilder
    {
		#region Constructors
		/// <summary>
		/// Initialize a new ModelBuilder object.
		/// </summary>
		public ModelBuilder()
    {
		}
		#endregion

		private static readonly object Locking = new object();
		private static DbCompiledModel _model;

		/// <summary>Creates a compiled entity model </summary>
		public static DbCompiledModel DbModel
		{
			get
			{
				if (_model == null)
				{
					lock (Locking)
					{
						if (_model != null) return _model;
            var modelBuilder = new DbModelBuilder();

            AddMappings(modelBuilder);
            AddAdditionalMappings(modelBuilder);

            modelBuilder.Conventions.Remove<StoreGeneratedIdentityKeyConvention>();

            _model = modelBuilder.Build(RIB.Visual.Platform.BusinessComponents.DbContext.CreateConnection()).Compile();
					}
				}

				return _model;
			}
		}

		// partial method to add special/additional mappings
		static partial void AddAdditionalMappings(DbModelBuilder modelBuilder);

		/// <summary>
		/// Adds the mapping for each entity of this db context.
		/// </summary>
		/// <param name="modelBuilder"></param>
		public static void AddMappings(DbModelBuilder modelBuilder)
		{

            #region ConHeaderEntity

            modelBuilder.Entity<ConHeaderEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_HEADER");
            // Properties:
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ConStatusFk)
                    .HasColumnName(@"CON_STATUS_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.CompanyFk)
                    .HasColumnName(@"BAS_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ProjectFk)
                    .HasColumnName(@"PRJ_PROJECT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.PackageFk)
                    .HasColumnName(@"PRC_PACKAGE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.TaxCodeFk)
                    .HasColumnName(@"MDC_TAX_CODE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ClerkPrcFk)
                    .HasColumnName(@"BAS_CLERK_PRC_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ClerkReqFk)
                    .HasColumnName(@"BAS_CLERK_REQ_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BasCurrencyFk)
                    .HasColumnName(@"BAS_CURRENCY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ExchangeRate)
                    .HasColumnName(@"EXCHANGERATE")
                    .IsRequired()
                    .HasPrecision(10, 5)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ProjectChangeFk)
                    .HasColumnName(@"PRJ_CHANGE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.HasChanges)
                    .HasColumnName(@"HASCHANGES")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.MaterialCatalogFk)
                    .HasColumnName(@"MDC_MATERIAL_CATALOG_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.PrcHeaderFk)
                    .HasColumnName(@"PRC_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.PaymentTermFiFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_FI_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.PaymentTermPaFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_PA_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Code)
                    .HasColumnName(@"CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.SearchPattern)
                    .HasColumnName(@"SEARCH_PATTERN")
                    .HasMaxLength(450)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.DateOrdered)
                    .HasColumnName(@"DATE_ORDERED")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.DateReported)
                    .HasColumnName(@"DATE_REPORTED")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.DateCanceled)
                    .HasColumnName(@"DATE_CANCELED")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.DateDelivery)
                    .HasColumnName(@"DATE_DELIVERY")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.DateCallofffrom)
                    .HasColumnName(@"DATE_CALLOFFFROM")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.DateCalloffto)
                    .HasColumnName(@"DATE_CALLOFFTO")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ConTypeFk)
                    .HasColumnName(@"CON_TYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.AwardmethodFk)
                    .HasColumnName(@"PRC_AWARDMETHOD_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ContracttypeFk)
                    .HasColumnName(@"PRC_CONTRACTTYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ControllingUnitFk)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BusinessPartnerFk)
                    .HasColumnName(@"BPD_BUSINESSPARTNER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.SubsidiaryFk)
                    .HasColumnName(@"BPD_SUBSIDIARY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.SupplierFk)
                    .HasColumnName(@"BPD_SUPPLIER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ContactFk)
                    .HasColumnName(@"BPD_CONTACT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BusinessPartner2Fk)
                    .HasColumnName(@"BPD_BUSINESSPARTNER2_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Subsidiary2Fk)
                    .HasColumnName(@"BPD_SUBSIDIARY2_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Supplier2Fk)
                    .HasColumnName(@"BPD_SUPPLIER2_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Contact2Fk)
                    .HasColumnName(@"BPD_CONTACT2_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.IncotermFk)
                    .HasColumnName(@"PRC_INCOTERM_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.CompanyInvoiceFk)
                    .HasColumnName(@"BAS_COMPANY_INVOICE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.AddressFk)
                    .HasColumnName(@"BAS_ADDRESS_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.CodeQuotation)
                    .HasColumnName(@"CODE_QUOTATION")
                    .HasMaxLength(20)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BusinessPartnerAgentFk)
                    .HasColumnName(@"BPD_BUSINESSPARTNER_AGENT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Package2HeaderFk)
                    .HasColumnName(@"PRC_PACKAGE2HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.DateQuotation)
                    .HasColumnName(@"DATE_QUOTATION")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Remark)
                    .HasColumnName(@"REMARK")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Userdefined1)
                    .HasColumnName(@"USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Userdefined2)
                    .HasColumnName(@"USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Userdefined3)
                    .HasColumnName(@"USERDEFINED3")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Userdefined4)
                    .HasColumnName(@"USERDEFINED4")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.Userdefined5)
                    .HasColumnName(@"USERDEFINED5")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BillingSchemaFk)
                    .HasColumnName(@"MDC_BILLING_SCHEMA_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ConfirmationCode)
                    .HasColumnName(@"CONFIRMATION_CODE")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ConfirmationDate)
                    .HasColumnName(@"CONFIRMATION_DATE")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ExternalCode)
                    .HasColumnName(@"EXTERNAL_CODE")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.PaymentTermAdFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_AD_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.PrcCopyModeFk)
                    .HasColumnName(@"PRC_COPYMODE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.DatePenalty)
                    .HasColumnName(@"DATE_PENALTY")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.PenaltyPercentPerDay)
                    .HasColumnName(@"PENALTY_PERCENTPERDAY")
                    .IsRequired()
                    .HasPrecision(9, 3)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.PenaltyPercentMax)
                    .HasColumnName(@"PENALTY_PERCENTMAX")
                    .IsRequired()
                    .HasPrecision(9, 3)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.PenaltyComment)
                    .HasColumnName(@"PENALTY_COMMENT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.DateEffective)
                    .HasColumnName(@"DATE_EFFECTIVE")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BpdVatGroupFk)
                    .HasColumnName(@"BPD_VATGROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ProvingPeriod)
                    .HasColumnName(@"PROVING_PERIOD")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ProvingDealdline)
                    .HasColumnName(@"PROVING_DEALDLINE")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ApprovalPeriod)
                    .HasColumnName(@"APPROVAL_PERIOD")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ApprovalDealdline)
                    .HasColumnName(@"APPROVAL_DEALDLINE")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.IsFreeItemsAllowed)
                    .HasColumnName(@"ISFREEITEMSALLOWED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.MdcPriceListFk)
                    .HasColumnName(@"MDC_PRICE_LIST_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BankFk)
                    .HasColumnName(@"BPD_BANK_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.QtnHeaderFk)
                    .HasColumnName(@"QTN_HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ReqHeaderFk)
                    .HasColumnName(@"REQ_HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ExecutionStart)
                    .HasColumnName(@"EXECUTION_START")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ExecutionEnd)
                    .HasColumnName(@"EXECUTION_END")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BasAccassignBusinessFk)
                    .HasColumnName(@"BAS_ACCASSIGN_BUSINESS_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BasAccassignControlFk)
                    .HasColumnName(@"BAS_ACCASSIGN_CONTROL_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BasAccassignAccountFk)
                    .HasColumnName(@"BAS_ACCASSIGN_ACCOUNT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BasAccassignConTypeFk)
                    .HasColumnName(@"BAS_ACCASSIGN_CON_TYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.OrdHeaderFk)
                    .HasColumnName(@"ORD_HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.OverallDiscount)
                    .HasColumnName(@"OVERALL_DISCOUNT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.OverallDiscountOc)
                    .HasColumnName(@"OVERALL_DISCOUNT_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.OverallDiscountPercent)
                    .HasColumnName(@"OVERALL_DISCOUNT_PERCENT")
                    .IsRequired()
                    .HasPrecision(9, 3)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.SalesTaxMethodFk)
                    .HasColumnName(@"BAS_SALES_TAX_METHOD_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ValidFrom)
                    .HasColumnName(@"VALIDFROM")
                    .HasColumnType("datetime");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.ValidTo)
                    .HasColumnName(@"VALIDTO")
                    .HasColumnType("datetime");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BoqWicCatFk)
                    .HasColumnName(@"BOQ_WIC_CAT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BoqWicCatBoqFk)
                    .HasColumnName(@"BOQ_WIC_CAT_BOQ_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BaselineUpdate)
                    .HasColumnName(@"BASELINE_UPDATE")
                    .HasColumnType("datetime");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.IsFramework)
                    .HasColumnName(@"ISFRAMEWORK")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.IsNotAccrualPrr)
                    .HasColumnName(@"ISNOTACCRUAL_PRR")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderEntity>()
                .Property(p => p.BasLanguageFk)
                    .HasColumnName(@"BAS_LANGUAGE_FK")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConHeaderEntity>(modelBuilder);
            // Associations:
            modelBuilder.Entity<ConHeaderEntity>()
                .HasMany(p => p.ConHeader2customerEntities)
                    .WithRequired(c => c.ConHeaderEntity)
                .HasForeignKey(p => new { p.ConHeaderFk })
                    .WillCascadeOnDelete(false);
            modelBuilder.Entity<ConHeaderEntity>()
                .HasMany(p => p.DdTempIdsEntities)
                    .WithRequired()
                .HasForeignKey(p => new { p.Id })
                    .WillCascadeOnDelete(false);
            modelBuilder.Entity<ConHeaderEntity>()
                .HasMany(p => p.ConHeaderapprovalEntities)
                    .WithRequired(c => c.HeaderEntity)
                .HasForeignKey(p => new { p.HeaderFk })
                    .WillCascadeOnDelete(false);

            #endregion

            #region ConStatusEntity

            modelBuilder.Entity<ConStatusEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_STATUS");
            // Properties:
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.Sorting)
                    .HasColumnName(@"SORTING")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsDefault)
                    .HasColumnName(@"ISDEFAULT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.Icon)
                    .HasColumnName(@"ICON")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsReadonly)
                    .HasColumnName(@"ISREADONLY")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.Iscanceled)
                    .HasColumnName(@"ISCANCELED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsReported)
                    .HasColumnName(@"ISREPORTED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsVirtual)
                    .HasColumnName(@"ISVIRTUAL")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsDelivered)
                    .HasColumnName(@"ISDELIVERED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsPartDelivered)
                    .HasColumnName(@"ISPARTDELIVERED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsInvoiced)
                    .HasColumnName(@"ISINVOICED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsOrdered)
                    .HasColumnName(@"ISORDERED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsOptionalUpwards)
                    .HasColumnName(@"ISOPTIONAL_UPWARDS")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsChangSent)
                    .HasColumnName(@"ISCHANGSENT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsChangeAccepted)
                    .HasColumnName(@"ISCHANGEACCEPTED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsChangeRejected)
                    .HasColumnName(@"ISCHANGEREJECTED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsOptionalDownwards)
                    .HasColumnName(@"ISOPTIONAL_DOWNWARDS")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsAccepted)
                    .HasColumnName(@"ISACCEPTED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsPartAccepted)
                    .HasColumnName(@"ISPARTACCEPTED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsRejected)
                    .HasColumnName(@"ISREJECTED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsLive)
                    .HasColumnName(@"ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.AccessRightDescriptorFk)
                    .HasColumnName(@"FRM_ACCESSRIGHTDESCRIPTOR_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.Code)
                    .HasColumnName(@"CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsUpdateImport)
                    .HasColumnName(@"ISUPDATEIMPORT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatusEntity>()
                .Property(p => p.IsPesCo)
                    .HasColumnName(@"ISPESCO")
                    .IsRequired()
                    .HasColumnType("bit");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConStatusEntity>(modelBuilder);
            // Association:
            modelBuilder.Entity<ConStatusEntity>()
                .HasMany(p => p.ConStatus2externalEntities)
                    .WithRequired(c => c.ConStatusEntity)
                .HasForeignKey(p => new { p.ConStatusFk })
                    .WillCascadeOnDelete(false);

            #endregion

            #region ConTypeEntity

            modelBuilder.Entity<ConTypeEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_TYPE");
            // Properties:
            modelBuilder.Entity<ConTypeEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTypeEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTypeEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTypeEntity>()
                .Property(p => p.Sorting)
                    .HasColumnName(@"SORTING")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTypeEntity>()
                .Property(p => p.IsDefault)
                    .HasColumnName(@"ISDEFAULT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConTypeEntity>()
                .Property(p => p.IsLive)
                    .HasColumnName(@"ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConTypeEntity>(modelBuilder);

            #endregion

            #region ConTotalEntity

            modelBuilder.Entity<ConTotalEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_TOTAL");
            // Properties:
            modelBuilder.Entity<ConTotalEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTotalEntity>()
                .Property(p => p.HeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTotalEntity>()
                .Property(p => p.TotalTypeFk)
                    .HasColumnName(@"PRC_TOTALTYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTotalEntity>()
                .Property(p => p.ValueNet)
                    .HasColumnName(@"VALUE_NET")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTotalEntity>()
                .Property(p => p.ValueNetOc)
                    .HasColumnName(@"VALUE_NET_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTotalEntity>()
                .Property(p => p.ValueTax)
                    .HasColumnName(@"VALUE_TAX")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTotalEntity>()
                .Property(p => p.ValueTaxOc)
                    .HasColumnName(@"VALUE_TAX_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTotalEntity>()
                .Property(p => p.CommentText)
                    .HasColumnName(@"COMMENT_TEXT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConTotalEntity>(modelBuilder);

            #endregion

            #region ConHeaderLookupVEntity

            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_HEADER_LOOKUP_V");
            // Properties:
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ControllingUnitFk)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.PrcPackageFk)
                    .HasColumnName(@"PRC_PACKAGE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.PrcPackage2HeaderFk)
                    .HasColumnName(@"PRC_PACKAGE2HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.PrcStructureFk)
                    .HasColumnName(@"PRC_STRUCTURE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.DateOrdered)
                    .HasColumnName(@"DATE_ORDERED")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BusinessPartner2Fk)
                    .HasColumnName(@"BPD_BUSINESSPARTNER2_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.Code)
                    .HasColumnName(@"CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.PrcConfigurationFk)
                    .HasColumnName(@"PRC_CONFIGURATION_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.PaymentTermFiFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_FI_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ExternalCode)
                    .HasColumnName(@"EXTERNAL_CODE")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.PaymentTermPaFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_PA_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.TaxCodeFk)
                    .HasColumnName(@"MDC_TAX_CODE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ClerkPrcFk)
                    .HasColumnName(@"BAS_CLERK_PRC_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ClerkReqFk)
                    .HasColumnName(@"BAS_CLERK_REQ_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BpName1)
                    .HasColumnName(@"BP_NAME1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.PrcHeaderId)
                    .HasColumnName(@"PRC_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusIsVirtual)
                    .HasColumnName(@"ISVIRTUAL")
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusIsReported)
                    .HasColumnName(@"ISREPORTED")
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.SearchPattern)
                    .HasColumnName(@"SEARCH_PATTERN")
                    .HasMaxLength(450)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.CurrencyFk)
                    .HasColumnName(@"BAS_CURRENCY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.MdcBillingSchemaFk)
                    .HasColumnName(@"MDC_BILLING_SCHEMA_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ProjectChangeFk)
                    .HasColumnName(@"PRJ_CHANGE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.Exchangerate)
                    .HasColumnName(@"EXCHANGERATE")
                    .IsRequired()
                    .HasPrecision(10, 5)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BpdContactFk)
                    .HasColumnName(@"BPD_CONTACT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BpdSubsidiaryFk)
                    .HasColumnName(@"BPD_SUBSIDIARY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BpdSupplierFk)
                    .HasColumnName(@"BPD_SUPPLIER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BpdVatGroupFk)
                    .HasColumnName(@"BPD_VATGROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusIsLive)
                    .HasColumnName(@"ISLIVE")
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusIsCanceled)
                    .HasColumnName(@"ISCANCELED")
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusIsDelivered)
                    .HasColumnName(@"ISDELIVERED")
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusIsReadonly)
                    .HasColumnName(@"ISREADONLY")
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusIsInvoiced)
                    .HasColumnName(@"ISINVOICED")
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusIsOrdered)
                    .HasColumnName(@"ISORDERED")
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ConStatusFk)
                    .HasColumnName(@"CONSTATUSFK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.PrcConfigHeaderFk)
                    .HasColumnName(@"PRC_CONFIGHEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusIsRejected)
                    .HasColumnName(@"ISREJECTED")
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusDescriptionInfo.DescriptionTr)
                    .HasColumnName(@"STATUSDESCRIPTIONTR")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.StatusDescriptionInfo.Description)
                    .HasColumnName(@"STATUSDESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.Icon)
                    .HasColumnName(@"ICON")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BpName2)
                    .HasColumnName(@"BP_NAME2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.SupplierCode)
                    .HasColumnName(@"SUPPLIER_CODE")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ProjectNo)
                    .HasColumnName(@"PROJECTNO")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ProjectName)
                    .HasColumnName(@"PROJECT_NAME")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.CompanyFk)
                    .HasColumnName(@"BAS_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.Bp2Name1)
                    .HasColumnName(@"BP2_NAME1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.Bp2Name2)
                    .HasColumnName(@"BP2_NAME2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BusinessPartnerFk)
                    .HasColumnName(@"BPD_BUSINESSPARTNER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.Supplier2Code)
                    .HasColumnName(@"SUPPLIER2_CODE")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ProjectFk)
                    .HasColumnName(@"PRJ_PROJECT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.CodeQuotation)
                    .HasColumnName(@"CODE_QUOTATION")
                    .HasMaxLength(20)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.PrcCopyModeFk)
                    .HasColumnName(@"PRC_COPYMODE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.IsFreeItemsAllowed)
                    .HasColumnName(@"ISFREEITEMSALLOWED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.SalesTaxMethodFk)
                    .HasColumnName(@"BAS_SALES_TAX_METHOD_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.IsFramework)
                    .HasColumnName(@"ISFRAMEWORK")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BankFk)
                    .HasColumnName(@"BPD_BANK_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.MdcMaterialCatalogFk)
                    .HasColumnName(@"MDC_MATERIAL_CATALOG_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BoqWicCatFk)
                    .HasColumnName(@"BOQ_WIC_CAT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderLookupVEntity>()
                .Property(p => p.BasLanguageFk)
                    .HasColumnName(@"BAS_LANGUAGE_FK")
                    .HasColumnType("int");

            #endregion

            #region TranslationEntity

            modelBuilder.Entity<TranslationEntity>()
                .HasKey(p => new { p.BasLanguageFk, p.Id })
                .ToTable("BAS_TRANSLATION");
            // Properties:
            modelBuilder.Entity<TranslationEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<TranslationEntity>()
                .Property(p => p.BasLanguageFk)
                    .HasColumnName(@"BAS_LANGUAGE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<TranslationEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<TranslationEntity>(modelBuilder);

            #endregion

            #region ConHeader2prjMaterialVEntity

            modelBuilder.Entity<ConHeader2prjMaterialVEntity>()
                .HasKey(p => new { p.BasCompanyFk, p.ConHeaderFk, p.PrjProjectFk })
                .ToTable("CON_HEADER2PRJ_MATERIAL_V");
            // Properties:
            modelBuilder.Entity<ConHeader2prjMaterialVEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2prjMaterialVEntity>()
                .Property(p => p.PrjProjectFk)
                    .HasColumnName(@"PRJ_PROJECT_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2prjMaterialVEntity>()
                .Property(p => p.BasCompanyFk)
                    .HasColumnName(@"BAS_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");

            #endregion

            #region ConStatus2externalEntity

            modelBuilder.Entity<ConStatus2externalEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_STATUS2EXTERNAL");
            // Properties:
            modelBuilder.Entity<ConStatus2externalEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConStatus2externalEntity>()
                .Property(p => p.ConStatusFk)
                    .HasColumnName(@"CON_STATUS_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConStatus2externalEntity>()
                .Property(p => p.BasExternalsourceFk)
                    .HasColumnName(@"BAS_EXTERNALSOURCE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConStatus2externalEntity>()
                .Property(p => p.ExtCode)
                    .HasColumnName(@"EXT_CODE")
                    .IsRequired()
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConStatus2externalEntity>()
                .Property(p => p.ExtDescription)
                    .HasColumnName(@"EXT_DESCRIPTION")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConStatus2externalEntity>()
                .Property(p => p.CommentText)
                    .HasColumnName(@"COMMENT_TEXT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConStatus2externalEntity>()
                .Property(p => p.Sorting)
                    .HasColumnName(@"SORTING")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConStatus2externalEntity>()
                .Property(p => p.Isdefault)
                    .HasColumnName(@"ISDEFAULT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConStatus2externalEntity>()
                .Property(p => p.Islive)
                    .HasColumnName(@"ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConStatus2externalEntity>(modelBuilder);

            #endregion

            #region ConHeaderUserAccessVEntity

            modelBuilder.Entity<ConHeaderUserAccessVEntity>()
                .HasKey(p => new { p.Id, p.UserId })
                .ToTable("CON_HEADERUSERACCESS_V");
            // Properties:
            modelBuilder.Entity<ConHeaderUserAccessVEntity>()
                .Property(p => p.UserId)
                    .HasColumnName(@"USERID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderUserAccessVEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");

            #endregion

            #region ConHeader2customerEntity

            modelBuilder.Entity<ConHeader2customerEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_HEADER2CUSTOMER");
            // Properties:
            modelBuilder.Entity<ConHeader2customerEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2customerEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2customerEntity>()
                .Property(p => p.BpdBusinesspartnerFk)
                    .HasColumnName(@"BPD_BUSINESSPARTNER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2customerEntity>()
                .Property(p => p.BpdSubsidiaryFk)
                    .HasColumnName(@"BPD_SUBSIDIARY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2customerEntity>()
                .Property(p => p.BpdCustomerFk)
                    .HasColumnName(@"BPD_CUSTOMER_FK")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConHeader2customerEntity>(modelBuilder);

            #endregion

            #region DdTempIdsEntity

            modelBuilder.Entity<DdTempIdsEntity>()
                .HasKey(p => new { p.Id, p.RequestId })
                .ToTable("BAS_DDTEMPIDS");
            // Properties:
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.RequestId)
                    .HasColumnName(@"REQUESTID")
                    .IsRequired()
                    .HasMaxLength(32)
                    .HasColumnType("char");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key1)
                    .HasColumnName(@"KEY1")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key2)
                    .HasColumnName(@"KEY2")
                    .HasColumnType("int");
            modelBuilder.Entity<DdTempIdsEntity>()
                .Property(p => p.Key3)
                    .HasColumnName(@"KEY3")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<DdTempIdsEntity>(modelBuilder);

            #endregion

            #region ConAccountAssignmentEntity

            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_ACCOUNT_ASSIGNMENT");
            // Properties:
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.ItemNO)
                    .HasColumnName(@"ITEMNO")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BreakdownPercent)
                    .HasColumnName(@"BREAKDOWN_PERCENT")
                    .IsRequired()
                    .HasPrecision(10, 2)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BreakdownAmount)
                    .HasColumnName(@"BREAKDOWN_AMOUNT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BasCompanyYearFk)
                    .HasColumnName(@"BAS_COMPANY_YEAR_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.MdcControllingUnitFk)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.PsdScheduleFk)
                    .HasColumnName(@"PSD_SCHEDULE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.PsdActivityFk)
                    .HasColumnName(@"PSD_ACTIVITY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.Remark)
                    .HasColumnName(@"REMARK")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.ConCrewFk)
                    .HasColumnName(@"CON_CREW_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.Quantity)
                    .HasColumnName(@"QUANTITY")
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BasAccountFk)
                    .HasColumnName(@"BAS_ACCOUNT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.IsDelete)
                    .HasColumnName(@"ISDELETE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.IsFinalInvoice)
                    .HasColumnName(@"ISFINALINVOICE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BreakdownAmountOc)
                    .HasColumnName(@"BREAKDOWN_AMOUNT_OC")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BasUomFk)
                    .HasColumnName(@"BAS_UOM_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.DateDelivery)
                    .HasColumnName(@"DATE_DELIVERY")
                    .HasColumnType("datetime");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.AccountAssignment01)
                    .HasColumnName(@"ACCOUNT_ASSIGNMENT01")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.AccountAssignment02)
                    .HasColumnName(@"ACCOUNT_ASSIGNMENT02")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BasAccAssignItemTypeFk)
                    .HasColumnName(@"BAS_ACCASSIGN_ITEMTYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BasAccAssignMatGroupFk)
                    .HasColumnName(@"BAS_ACCASSIGN_MAT_GROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BasAccAssignAccTypeFk)
                    .HasColumnName(@"BAS_ACCASSIGN_ACC_TYPE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.AccountAssignment03)
                    .HasColumnName(@"ACCOUNT_ASSIGNMENT03")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAccountAssignmentEntity>()
                .Property(p => p.BasAccAssignFactoryFk)
                    .HasColumnName(@"BAS_ACCASSIGN_FACTORY_FK")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConAccountAssignmentEntity>(modelBuilder);

            #endregion

            #region ConHeader2MdlObjectVEntity

            modelBuilder.Entity<ConHeader2MdlObjectVEntity>()
                .HasKey(p => new { p.Id, p.ModelFk, p.ObjectFk })
                .ToTable("CON_HEADER2MDL_OBJECT_V");
            // Properties:
            modelBuilder.Entity<ConHeader2MdlObjectVEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2MdlObjectVEntity>()
                .Property(p => p.ModelFk)
                    .HasColumnName(@"MDL_MODEL_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2MdlObjectVEntity>()
                .Property(p => p.ObjectFk)
                    .HasColumnName(@"MDL_OBJECT_FK")
                    .IsRequired()
                    .HasColumnType("int");

            #endregion

            #region ConCrewEntity

            modelBuilder.Entity<ConCrewEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_CREW");
            // Properties:
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.BpdContactFk)
                    .HasColumnName(@"BPD_CONTACT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.Sorting)
                    .HasColumnName(@"SORTING")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.IsDefault)
                    .HasColumnName(@"ISDEFAULT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.IsLive)
                    .HasColumnName(@"ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.UserDefined1)
                    .HasColumnName(@"USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.UserDefined2)
                    .HasColumnName(@"USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.UserDefined3)
                    .HasColumnName(@"USERDEFINED3")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.UserDefined4)
                    .HasColumnName(@"USERDEFINED4")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConCrewEntity>()
                .Property(p => p.UserDefined5)
                    .HasColumnName(@"USERDEFINED5")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConCrewEntity>(modelBuilder);

            #endregion

            #region ConMasterRestrictionEntity

            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_MASTERRESTRICTION");
            // Properties:
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.MdcMaterialCatalogFk)
                    .HasColumnName(@"MDC_MATERIAL_CATALOG_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.BoqWicCatFk)
                    .HasColumnName(@"BOQ_WIC_CAT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.BoqHeaderFk)
                    .HasColumnName(@"BOQ_HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.ConHeaderBoqFk)
                    .HasColumnName(@"CON_HEADER_BOQ_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.Visibility)
                    .HasColumnName(@"VISIBILITY")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.PackageFk)
                    .HasColumnName(@"PRC_PACKAGE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.ProjectFk)
                    .HasColumnName(@"PRJ_PROJECT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.PrjBoqFk)
                    .HasColumnName(@"PRJ_BOQ_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConMasterRestrictionEntity>()
                .Property(p => p.CopyType)
                    .HasColumnName(@"COPY_TYPE")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConMasterRestrictionEntity>(modelBuilder);

            #endregion

            #region PrcCopyModeEntity

            modelBuilder.Entity<PrcCopyModeEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("PRC_COPYMODE");
            // Properties:
            modelBuilder.Entity<PrcCopyModeEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<PrcCopyModeEntity>()
                .Property(p => p.Sorting)
                    .HasColumnName(@"SORTING")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<PrcCopyModeEntity>()
                .Property(p => p.IsDefault)
                    .HasColumnName(@"ISDEFAULT")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<PrcCopyModeEntity>()
                .Property(p => p.IsLive)
                    .HasColumnName(@"ISLIVE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<PrcCopyModeEntity>()
                .Property(p => p.DescriptionInfo.DescriptionTr)
                    .HasColumnName(@"DESCRIPTION_TR")
                    .HasColumnType("int");
            modelBuilder.Entity<PrcCopyModeEntity>()
                .Property(p => p.DescriptionInfo.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<PrcCopyModeEntity>(modelBuilder);

            #endregion

            #region ConAdvanceEntity

            modelBuilder.Entity<ConAdvanceEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_ADVANCE");
            // Properties:
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.PrcAdvanceTypeFk)
                    .HasColumnName(@"PRC_ADVANCETYPE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.DateDue)
                    .HasColumnName(@"DATE_DUE")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.AmountDue)
                    .HasColumnName(@"AMOUNT_DUE")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.PercentProrata)
                    .HasColumnName(@"PERCENT_PRORATA")
                    .IsRequired()
                    .HasPrecision(9, 3)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.DateDone)
                    .HasColumnName(@"DATE_DONE")
                    .HasColumnType("date");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.AmountDone)
                    .HasColumnName(@"AMOUNT_DONE")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.CommentText)
                    .HasColumnName(@"COMMENT_TEXT")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.Userdefined1)
                    .HasColumnName(@"USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.Userdefined2)
                    .HasColumnName(@"USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.Userdefined3)
                    .HasColumnName(@"USERDEFINED3")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.Userdefined4)
                    .HasColumnName(@"USERDEFINED4")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.Userdefined5)
                    .HasColumnName(@"USERDEFINED5")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.AmountDueOc)
                    .HasColumnName(@"AMOUNT_DUE_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.AmountDoneOc)
                    .HasColumnName(@"AMOUNT_DONE_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConAdvanceEntity>()
                .Property(p => p.PaymentTermFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_FK")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConAdvanceEntity>(modelBuilder);

            #endregion

            #region ConTransactionEntity

            modelBuilder.Entity<ConTransactionEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_TRANSACTION");
            // Properties:
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.TransactionId)
                    .HasColumnName(@"TRANSACTION_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.CompanyFk)
                    .HasColumnName(@"BAS_COMPANY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.CompanyInvoiceFk)
                    .HasColumnName(@"BAS_COMPANY_INVOICE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.PrcConfigurationFk)
                    .HasColumnName(@"PRC_CONFIGURATION_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Isconsolidated)
                    .HasColumnName(@"ISCONSOLIDATED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Ischange)
                    .HasColumnName(@"ISCHANGE")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Currency)
                    .HasColumnName(@"CURRENCY")
                    .IsRequired()
                    .HasMaxLength(3)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ConStatusFk)
                    .HasColumnName(@"CON_STATUS_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Code)
                    .HasColumnName(@"CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Description)
                    .HasColumnName(@"DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.DateOrdered)
                    .HasColumnName(@"DATE_ORDERED")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.DateEffective)
                    .HasColumnName(@"DATE_EFFECTIVE")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.DateReported)
                    .HasColumnName(@"DATE_REPORTED")
                    .HasColumnType("date");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.BusinessPartnerFk)
                    .HasColumnName(@"BPD_BUSINESSPARTNER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.SubsidiaryFk)
                    .HasColumnName(@"BPD_SUBSIDIARY_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.SupplierFk)
                    .HasColumnName(@"BPD_SUPPLIER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ContactFk)
                    .HasColumnName(@"BPD_CONTACT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.BankFk)
                    .HasColumnName(@"BPD_BANK_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.VatGroupFk)
                    .HasColumnName(@"BPD_VATGROUP_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.PaymentTermFiFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_FI_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.PaymentTermPaFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_PA_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.PaymentTermAdFk)
                    .HasColumnName(@"BAS_PAYMENT_TERM_AD_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Incoterm)
                    .HasColumnName(@"INCOTERM")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.DateDelivery)
                    .HasColumnName(@"DATE_DELIVERY")
                    .HasColumnType("date");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.AddressFk)
                    .HasColumnName(@"BAS_ADDRESS_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ItemReference)
                    .HasColumnName(@"ITEM_REFERENCE")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Amount)
                    .HasColumnName(@"AMOUNT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.VatAmount)
                    .HasColumnName(@"VAT_AMOUNT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.AmountOc)
                    .HasColumnName(@"AMOUNT_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.VatAmountOc)
                    .HasColumnName(@"VAT_AMOUNT_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.IncAmount)
                    .HasColumnName(@"INC_AMOUNT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.IncVatAmount)
                    .HasColumnName(@"INC_VAT_AMOUNT")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.IncAmountOc)
                    .HasColumnName(@"INC_AMOUNT_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.IncVatAmountOc)
                    .HasColumnName(@"INC_VAT_AMOUNT_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.IncQuantity)
                    .HasColumnName(@"INC_QUANTITY")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Quantity)
                    .HasColumnName(@"QUANTITY")
                    .IsRequired()
                    .HasPrecision(19, 6)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Price)
                    .HasColumnName(@"PRICE")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.VatPrice)
                    .HasColumnName(@"VAT_PRICE")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.PriceOc)
                    .HasColumnName(@"PRICE_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.VatPriceOc)
                    .HasColumnName(@"VAT_PRICE_OC")
                    .IsRequired()
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.NominalAccount)
                    .HasColumnName(@"NOMINAL_ACCOUNT")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.NominalDimension1)
                    .HasColumnName(@"NOMINAL_DIMENSION1")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.NominalDimension2)
                    .HasColumnName(@"NOMINAL_DIMENSION2")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.NominalDimension3)
                    .HasColumnName(@"NOMINAL_DIMENSION3")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.TaxCodeFk)
                    .HasColumnName(@"MDC_TAX_CODE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.TaxCodeMatrixFk)
                    .HasColumnName(@"MDC_TAX_CODE_MATRIX_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.VatPercent)
                    .HasColumnName(@"VATPERCENT")
                    .IsRequired()
                    .HasPrecision(9, 3)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.VatCode)
                    .HasColumnName(@"VAT_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitFk)
                    .HasColumnName(@"MDC_CONTROLLINGUNIT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitCode)
                    .HasColumnName(@"CONTROLLINGUNIT_CODE")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign01)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN01")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign01desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN01DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign02)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN02")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign02desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN02DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign03)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN03")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign03desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN03DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign04)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN04")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign04desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN04DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign05)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN05")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign05desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN05DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign06)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN06")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign06desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN06DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign07)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN07")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign07desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN07DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign08)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN08")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign08desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN08DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign09)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN09")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign09desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN09DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingUnitAssign10)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN10")
                    .HasMaxLength(32)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ControllingunitAssign10desc)
                    .HasColumnName(@"CONTROLLINGUNIT_ASSIGN10DESC")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.PrcItemFk)
                    .HasColumnName(@"PRC_ITEM_FK")
                    .HasColumnType("bigint");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.MaterialFk)
                    .HasColumnName(@"MDC_MATERIAL_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ItemDescription1)
                    .HasColumnName(@"ITEM_DESCRIPTION1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ItemDescription2)
                    .HasColumnName(@"ITEM_DESCRIPTION2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ItemSpecification)
                    .HasColumnName(@"ITEM_SPECIFICATION")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ItemUomQuantity)
                    .HasColumnName(@"ITEM_UOMQUANTITY")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.IsSuccess)
                    .HasColumnName(@"ISSUCCESS")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.HandoverId)
                    .HasColumnName(@"HANDOVER_ID")
                    .HasColumnType("int");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Orderno)
                    .HasColumnName(@"ORDERNO")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Userdefined1)
                    .HasColumnName(@"USERDEFINED1")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Userdefined2)
                    .HasColumnName(@"USERDEFINED2")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.Userdefined3)
                    .HasColumnName(@"USERDEFINED3")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.ReturnValue)
                    .HasColumnName(@"RETURN_VALUE")
                    .HasMaxLength(2000)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConTransactionEntity>()
                .Property(p => p.IncotermCode)
                    .HasColumnName(@"INCOTERM_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConTransactionEntity>(modelBuilder);

            #endregion

            #region ConHeaderApprovalEntity

            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_HEADERAPPROVAL");
            // Properties:
            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .Property(p => p.IsApproved)
                    .HasColumnName(@"ISAPPROVED")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .Property(p => p.Comment)
                    .HasColumnName(@"COMMENT")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .Property(p => p.ClerkFk)
                    .HasColumnName(@"BAS_CLERK_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .Property(p => p.ClerkRoleFk)
                    .HasColumnName(@"BAS_CLERK_ROLE_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .Property(p => p.DueDate)
                    .HasColumnName(@"DUEDATE")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .Property(p => p.EvaluatedOn)
                    .HasColumnName(@"EVALUATEDON")
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .Property(p => p.EvaluationLevel)
                    .HasColumnName(@"EVALUATIONLEVEL")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderApprovalEntity>()
                .Property(p => p.HeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConHeaderApprovalEntity>(modelBuilder);

            #endregion

            #region ConHeaderItwoFinanceVEntity

            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .HasKey(p => new { p.ConHeaderCode, p.ConHeaderDateOrdered, p.ConHeaderId })
                .ToTable("CON_HEADER_ITWOFINANCE_V");
            // Properties:
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConHeaderId)
                    .HasColumnName(@"CON_HEADER_ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConHeaderConHeaderFk)
                    .HasColumnName(@"CON_HEADER_CON_HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConHeaderCode)
                    .HasColumnName(@"CON_HEADER_CODE")
                    .IsRequired()
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConHeaderDescription)
                    .HasColumnName(@"CON_HEADER_DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConHeaderDateOrdered)
                    .HasColumnName(@"CON_HEADER_DATE_ORDERED")
                    .IsRequired()
                    .HasColumnType("date");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConHeaderMdcTaxCodeFk)
                    .HasColumnName(@"CON_HEADER_MDC_TAX_CODE_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConHeaderBpdVatgroupFk)
                    .HasColumnName(@"CON_HEADER_BPD_VATGROUP_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConTotalValueNet)
                    .HasColumnName(@"CON_TOTAL_VALUE_NET")
                    .HasPrecision(19, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConTotalValueGross)
                    .HasColumnName(@"CON_TOTAL_VALUE_GROSS")
                    .HasPrecision(20, 7)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConStatusCode)
                    .HasColumnName(@"CON_STATUS_CODE")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.ConStatusDescription)
                    .HasColumnName(@"CON_STATUS_DESCRIPTION")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.PrjProjectNo)
                    .HasColumnName(@"PRJ_PROJECT_PROJECTNO")
                    .HasMaxLength(16)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.BpdSupplierCode)
                    .HasColumnName(@"BPD_SUPPLIER_CODE")
                    .HasMaxLength(252)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderItwoFinanceVEntity>()
                .Property(p => p.BpdVatgroupReference)
                    .HasColumnName(@"BPD_VATGROUP_REFERENCE")
                    .HasMaxLength(20)
                    .HasColumnType("nvarchar");

            #endregion

            #region ConHeader2BoqWicCatBoqEntity

            modelBuilder.Entity<ConHeader2BoqWicCatBoqEntity>()
                .HasKey(p => new { p.Id })
                .ToTable("CON_HEADER2BOQ_WIC_CAT_BOQ");
            // Properties:
            modelBuilder.Entity<ConHeader2BoqWicCatBoqEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2BoqWicCatBoqEntity>()
                .Property(p => p.ConHeaderFk)
                    .HasColumnName(@"CON_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2BoqWicCatBoqEntity>()
                .Property(p => p.BoqHeaderFk)
                    .HasColumnName(@"BOQ_HEADER_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeader2BoqWicCatBoqEntity>()
                .Property(p => p.BoqWicCatBoqFk)
                    .HasColumnName(@"BOQ_WIC_CAT_BOQ_FK")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConHeader2BoqWicCatBoqEntity>(modelBuilder);

            #endregion

            #region ConHeaderExtendedVEntity

            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .HasKey(p => new { p.Id, p.InsertedAt, p.InsertedBy, p.RubricCategoryFk, p.VatPercent, p.Version })
                .ToTable("CON_HEADEREXT_V");
            // Properties:
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.Id)
                    .HasColumnName(@"ID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.RubricCategoryFk)
                    .HasColumnName(@"BAS_RUBRIC_CATEGORY_FK")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.VatPercent)
                    .HasColumnName(@"VATPERCENT")
                    .IsRequired()
                    .HasPrecision(9, 3)
                    .HasColumnType("numeric");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.BaselinePath)
                    .HasColumnName(@"BASELINE_PATH")
                    .HasMaxLength(255)
                    .HasColumnType("nvarchar");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.PrjStatusFk)
                    .HasColumnName(@"PRJ_STATUS_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.IsStatusReadonly)
                    .HasColumnName(@"IS_STATUS_READONLY")
                    .IsRequired()
                    .HasColumnType("bit");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.ConHeaderMatFk)
                    .HasColumnName(@"CON_HEADERMAT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.ConHeaderBoqCatFk)
                    .HasColumnName(@"CON_HEADERBOQCAT_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.PackageMaxBoqFk)
                    .HasColumnName(@"PRC_PACKAGEMAXBOQ_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.PostedConHeaderFk)
                    .HasColumnName(@"POSTED_CON_HEADER_FK")
                    .HasColumnType("int");
            modelBuilder.Entity<ConHeaderExtendedVEntity>()
                .Property(p => p.AccassignConTypeFk)
                    .HasColumnName(@"ACCASSIGN_CON_TYPE_FK")
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ConHeaderExtendedVEntity>(modelBuilder);

            #endregion

            #region ContractUserAccessCacheEntity

            modelBuilder.Entity<ContractUserAccessCacheEntity>()
                .HasKey(p => new { p.AccessRoleId, p.CompanySignedInId, p.FromId, p.ToId, p.UserId })
                .ToTable("PRC_CONTRACTUSERACCESSCACHE");
            // Properties:
            modelBuilder.Entity<ContractUserAccessCacheEntity>()
                .Property(p => p.UserId)
                    .HasColumnName(@"USERID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ContractUserAccessCacheEntity>()
                .Property(p => p.CompanySignedInId)
                    .HasColumnName(@"COMPANYSIGNEDINID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ContractUserAccessCacheEntity>()
                .Property(p => p.AccessRoleId)
                    .HasColumnName(@"ACCESSROLEID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ContractUserAccessCacheEntity>()
                .Property(p => p.FromId)
                    .HasColumnName(@"FROMID")
                    .IsRequired()
                    .HasColumnType("int");
            modelBuilder.Entity<ContractUserAccessCacheEntity>()
                .Property(p => p.ToId)
                    .HasColumnName(@"TOID")
                    .IsRequired()
                    .HasColumnType("int");
            RIB.Visual.Platform.BusinessComponents.DbContext.AddEntityBaseMappings<ContractUserAccessCacheEntity>(modelBuilder);

            #endregion

            #region ComplexTypes

            modelBuilder.ComplexType<DescriptionTranslateType>();

            #endregion

            #region Disabled conventions


            #endregion

        }

    
        /// <summary>
        /// There are no comments for ConHeaderEntity in the schema.
        /// </summary>
        public DbSet<ConHeaderEntity> ConHeaderEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConStatusEntity in the schema.
        /// </summary>
        public DbSet<ConStatusEntity> ConStatusEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConTypeEntity in the schema.
        /// </summary>
        public DbSet<ConTypeEntity> ConTypeEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConTotalEntity in the schema.
        /// </summary>
        public DbSet<ConTotalEntity> ConTotalEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderLookupVEntity in the schema.
        /// </summary>
        public DbSet<ConHeaderLookupVEntity> ConHeaderLookupVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for TranslationEntity in the schema.
        /// </summary>
        public DbSet<TranslationEntity> TranslationEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeader2prjMaterialVEntity in the schema.
        /// </summary>
        public DbSet<ConHeader2prjMaterialVEntity> ConHeader2prjMaterialVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConStatus2externalEntity in the schema.
        /// </summary>
        public DbSet<ConStatus2externalEntity> ConStatus2externalEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderUserAccessVEntity in the schema.
        /// </summary>
        public DbSet<ConHeaderUserAccessVEntity> ConHeaderUserAccessVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeader2customerEntity in the schema.
        /// </summary>
        public DbSet<ConHeader2customerEntity> ConHeader2customerEntities { get; set; }
    
        /// <summary>
        /// There are no comments for DdTempIdsEntity in the schema.
        /// </summary>
        public DbSet<DdTempIdsEntity> DdTempIdsEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConAccountAssignmentEntity in the schema.
        /// </summary>
        public DbSet<ConAccountAssignmentEntity> ConAccountAssignmentEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeader2MdlObjectVEntity in the schema.
        /// </summary>
        public DbSet<ConHeader2MdlObjectVEntity> ConHeader2MdlObjectVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConCrewEntity in the schema.
        /// </summary>
        public DbSet<ConCrewEntity> ConCrewEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConMasterRestrictionEntity in the schema.
        /// </summary>
        public DbSet<ConMasterRestrictionEntity> ConMasterRestrictionEntities { get; set; }
    
        /// <summary>
        /// There are no comments for PrcCopyModeEntity in the schema.
        /// </summary>
        public DbSet<PrcCopyModeEntity> PrcCopyModeEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConAdvanceEntity in the schema.
        /// </summary>
        public DbSet<ConAdvanceEntity> ConAdvanceEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConTransactionEntity in the schema.
        /// </summary>
        public DbSet<ConTransactionEntity> ConTransactionEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderApprovalEntity in the schema.
        /// </summary>
        public DbSet<ConHeaderApprovalEntity> ConHeaderApprovalEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderItwoFinanceVEntity in the schema.
        /// </summary>
        public DbSet<ConHeaderItwoFinanceVEntity> ConHeaderItwoFinanceVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeader2BoqWicCatBoqEntity in the schema.
        /// </summary>
        public DbSet<ConHeader2BoqWicCatBoqEntity> ConHeader2BoqWicCatBoqEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderExtendedVEntity in the schema.
        /// </summary>
        public DbSet<ConHeaderExtendedVEntity> ConHeaderExtendedVEntities { get; set; }
    
        /// <summary>
        /// There are no comments for ContractUserAccessCacheEntity in the schema.
        /// </summary>
        public DbSet<ContractUserAccessCacheEntity> ContractUserAccessCacheEntities { get; set; }
    }
}
