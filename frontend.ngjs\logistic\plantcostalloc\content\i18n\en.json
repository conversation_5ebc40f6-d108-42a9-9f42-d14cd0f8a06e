﻿{
	"logistic": {
		"plantcostalloc": {
			"billingSheetListTitle": "Billing Sheets",
			"billingSheetDetailTitle": "Billing Sheet Details",
			"billingSheetEntity": "Billing Sheet",
			"controllingUnitCost": "Cost Controlling Unit",
			"controllingUnitRevenue": "Revenue Controlling Unit",
			"isRequisitionBased": "Requisition Based",
			"operationQuantity": "Operation Quantity",
			"workHourPerDay": "Workhour per Day",
			"percentageNo": "Percentage {{no}}",
			"pricePortionNo": "Price Portion {{no}}",
			"pricePortionSum": "Sum Price Portions",
			"calcPricePortionNo": "Calculated Portion {{no}}",
			"calcPricePortionSum": "Sum Calculated Portions",
			"resourceGroup": "Resource",
			"workGroup": "Work",
			"priceGroup": "Price Portions",
			"calculatedPriceGroup": "Calculated Portions",
			"plantQuantity": "Plant Quantity",
			"createRecordsForReserveBillingSheetsWizard": {
				"title": "Create Records For Reservation Billing Sheets",
				"selectedProjectMissing": "Please select a project"
			},
			"entityPriceCalcType": "Price Info",
			"entityReceivingJob": "Receiving Job",
			"entityPerformingJob": "Performing Job",
			"entityJobPlantPrice": "Plant Price",
			"entityPriceCondition": "Price Condition",
			"entityPriceConditionPlantPrice": "Plant Price",
			"entityPlantPricelist": "Plant Pricelist",
			"entityPricelist": "Plant Pricelist",
			"entityPlantGroupPrice": "Plant Group Price",
			"entityPlantGroupPricelist": "Plant Group Price List",
			"entityValidFrom": "Valid From",
			"entityValidTo": "Valid To",
			"entityPricePUnit": "Price Per Unit",
			"entityIsManual": "Is Manual",
			"plantCostAllocPriceOrgInfoDetail": "Price Origin Info Details",
			"entityGroup": "Entity",
			"createJobForBillingSheetsTitle": "Create Job for Billing Sheets",
			"changeBillingSheetStatus": "Change Billing Sheet Status"
		}
	}
}
