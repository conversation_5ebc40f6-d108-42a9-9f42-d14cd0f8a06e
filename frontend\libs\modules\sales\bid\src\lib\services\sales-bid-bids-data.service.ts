/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { DataServiceFlatRoot, ServiceRole, IDataServiceOptions, IDataServiceEndPointOptions, IDataServiceRoleOptions } from '@libs/platform/data-access';
import { BidHeaderComplete } from '../model/complete-class/bid-header-complete.class';
import { IBidHeaderEntity, SalesBidEntityToken } from '@libs/sales/interfaces';
import { LazyInjectable, PlatformTranslateService } from '@libs/platform/common';
import { SalesCommonNumberGenerationService } from '@libs/sales/common';

@Injectable({
	providedIn: 'root'
})
@LazyInjectable({
	token: SalesBidEntityToken,
	useAngularInjection: true
})
/**
 * Sales bid bids data service
 */
export class SalesBidBidsDataService extends DataServiceFlatRoot<IBidHeaderEntity, BidHeaderComplete> {
	private readonly numberGenerationService = inject(SalesCommonNumberGenerationService);
	private readonly translateService = inject(PlatformTranslateService);

	public constructor() {
		const options: IDataServiceOptions<IBidHeaderEntity> = {
			apiUrl: 'sales/bid',
			readInfo: <IDataServiceEndPointOptions>{
				endPoint: 'listfiltered',
				usePost: true
			},
			deleteInfo: <IDataServiceEndPointOptions>{
				endPoint: 'delete'
			},
			roleInfo: <IDataServiceRoleOptions<IBidHeaderEntity>>{
				role: ServiceRole.Root,
				itemName: 'BidHeader',
			}
		};

		super(options);
	}

	protected override onCreateSucceeded(created: object): IBidHeaderEntity {
		const bidComplete = created as BidHeaderComplete;

		if (bidComplete.BidHeader && bidComplete.BidHeader.RubricCategoryFk) {
			const rubricCategoryId = bidComplete.BidHeader.RubricCategoryFk;
			const rubricIndex = this.getRubricIndex(bidComplete.BidHeader);

			if (this.numberGenerationService.hasToGenerateForRubricCategory('bid', rubricCategoryId, rubricIndex)) {
				bidComplete.BidHeader.Code = this.translateService.instant({ key: 'cloud.common.isGenerated' }).text;
			}
		}

		return bidComplete.BidHeader as IBidHeaderEntity;
	}

	public override createUpdateEntity(modified: IBidHeaderEntity | null): BidHeaderComplete {
		const complete = new BidHeaderComplete();
		if (modified !== null) {
			complete.MainItemId = modified.Id;
			complete.BidHeader = modified;
		}

		return complete;
	}

	public override getModificationsFromUpdate(complete: BidHeaderComplete): IBidHeaderEntity[] {
		if (complete.BidHeader === null) {
			return [];
		}

		return [complete.BidHeader];
	}

	/**
	 * Gets the rubric index for a bid header entity
	 *
	 * @param entity The bid header entity
	 * @returns The rubric index
	 */
	private getRubricIndex(entity: IBidHeaderEntity): number {
		const changeTypeFk = 2; // Change Quote type

		if (entity.BidHeaderFk) {
			return entity.TypeFk === changeTypeFk ? 1 : 2; // 1 = Change Quote, 2 = Side Quote
		}

		return 0;
	}
}