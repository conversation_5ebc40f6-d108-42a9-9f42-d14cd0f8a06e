/*
 * Copyright(c) RIB Software GmbH
 */

import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { SalesWipNumberGenerationService } from './sales-wip-number-generation.service';
import { SalesCommonNumberGenerationService } from '@libs/sales/common';
import { ISalesCommonNumberGenerationSetting } from '@libs/sales/interfaces';

describe('SalesWipNumberGenerationService', () => {
  let service: SalesWipNumberGenerationService;
  let mockCommonService: jest.Mocked<SalesCommonNumberGenerationService>;

  const mockSettings: ISalesCommonNumberGenerationSetting[] = [
    {
      RubricCatID: 1,
      CanCreate: true,
      HasToCreate: true,
      NumberIndex: 0
    },
    {
      RubricCatID: 2,
      CanCreate: true,
      HasToCreate: false,
      NumberIndex: 0
    }
  ];

  beforeEach(() => {
    const commonServiceMock = {
      loadSettings: jest.fn(),
      getSettings: jest.fn(),
      hasToGenerateForRubricCategory: jest.fn(),
      provideNumberDefaultText: jest.fn(),
      ensureSettingsLoaded: jest.fn()
    };

    TestBed.configureTestingModule({
      providers: [
        SalesWipNumberGenerationService,
        { provide: SalesCommonNumberGenerationService, useValue: commonServiceMock }
      ]
    });

    service = TestBed.inject(SalesWipNumberGenerationService);
    mockCommonService = TestBed.inject(SalesCommonNumberGenerationService) as jest.Mocked<SalesCommonNumberGenerationService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('loadSettings', () => {
    it('should call common service with wip module name', () => {
      mockCommonService.loadSettings.mockReturnValue(of(mockSettings));

      service.loadSettings();

      expect(mockCommonService.loadSettings).toHaveBeenCalledWith('wip');
    });

    it('should return observable from common service', () => {
      mockCommonService.loadSettings.mockReturnValue(of(mockSettings));

      const result = service.loadSettings();

      expect(result).toEqual(of(mockSettings));
    });
  });

  describe('getSettings', () => {
    it('should call common service with wip module name', () => {
      mockCommonService.getSettings.mockReturnValue(mockSettings);

      service.getSettings();

      expect(mockCommonService.getSettings).toHaveBeenCalledWith('wip');
    });

    it('should return settings from common service', () => {
      mockCommonService.getSettings.mockReturnValue(mockSettings);

      const result = service.getSettings();

      expect(result).toEqual(mockSettings);
    });
  });

  describe('hasToGenerateForRubricCategory', () => {
    it('should call common service with wip module name and default rubric index', () => {
      mockCommonService.hasToGenerateForRubricCategory.mockReturnValue(true);

      service.hasToGenerateForRubricCategory(1);

      expect(mockCommonService.hasToGenerateForRubricCategory).toHaveBeenCalledWith('wip', 1, 0);
    });

    it('should call common service with wip module name and provided rubric index', () => {
      mockCommonService.hasToGenerateForRubricCategory.mockReturnValue(true);

      service.hasToGenerateForRubricCategory(1, 2);

      expect(mockCommonService.hasToGenerateForRubricCategory).toHaveBeenCalledWith('wip', 1, 2);
    });

    it('should return result from common service', () => {
      mockCommonService.hasToGenerateForRubricCategory.mockReturnValue(true);

      const result = service.hasToGenerateForRubricCategory(1);

      expect(result).toBe(true);
    });
  });

  describe('provideNumberDefaultText', () => {
    it('should call common service with wip module name and default parameters', () => {
      mockCommonService.provideNumberDefaultText.mockReturnValue('Generated');

      service.provideNumberDefaultText(1);

      expect(mockCommonService.provideNumberDefaultText).toHaveBeenCalledWith('wip', 1, '', 0);
    });

    it('should call common service with wip module name and provided parameters', () => {
      mockCommonService.provideNumberDefaultText.mockReturnValue('Generated');

      service.provideNumberDefaultText(1, 'current', 2);

      expect(mockCommonService.provideNumberDefaultText).toHaveBeenCalledWith('wip', 1, 'current', 2);
    });

    it('should return result from common service', () => {
      mockCommonService.provideNumberDefaultText.mockReturnValue('Generated');

      const result = service.provideNumberDefaultText(1);

      expect(result).toBe('Generated');
    });
  });

  describe('ensureSettingsLoaded', () => {
    it('should call common service with wip module name', async () => {
      mockCommonService.ensureSettingsLoaded.mockResolvedValue();

      await service.ensureSettingsLoaded();

      expect(mockCommonService.ensureSettingsLoaded).toHaveBeenCalledWith('wip');
    });

    it('should return promise from common service', async () => {
      mockCommonService.ensureSettingsLoaded.mockResolvedValue();

      const result = service.ensureSettingsLoaded();

      await expect(result).resolves.toBeUndefined();
    });
  });
});
