% Timekeeping

# General Information

![](image/tile-timekeeping.png)

In the module **Timekeeping** crew recordings, employee reports and timekeeping results can be created and reviewed. The main container of this module is the **Crew Recordings** container. **Employee Sheets** and **Timekeeping Results** are connected to the crew recording records. In addition to that there are wizards available in the timekeeping module to automatically generate **Overtime** as well as **Other Derivations** as defined in the **Working Time Model** module.

The module supports the following use cases:

- Record times for employees

- Record qualified times assigned to control units for employees and plant

- Calculate overtime and other derivations

# Pre-conditions

To record times in the module timekeeping the minimal required master data are:

- Employee ([EN](../../timekeeping.createemployee/en/index.html#create-an-employee) / [DE](../../timekeeping.createemployee/de/index.html#create-an-employee))

- Timekeeping Period


With these master data records created it is possible to start with very basic timekeeping. Other master data like status for crew recording, employee sheet and employee reports are provided with the vanilla data of 4.0.

# Overview

**Abstract**

The RIB 4.0 Timekeeping allows employees to record their working hours. Recorded hours are associated with booking dates, wage types (time symbols) and actions that can be used in the further process.
The timekeeping is embedded into the RIB 4.0 end to end process. Timesheets can be prefilled fully automated based on the crew structure defined in the RIB 4.0 employee master data, planned absences such as vacation and trainings and even resource reservations are taken into consideration to prefill the project and job of the employee for each record.
Prefilled reports can be updated by the employees on the desktop and mobile devices via the RIB | 4.0 mobile APP. With the matrix reporting (time allocation) RIB 4.0 provides a further interface for project managers and foreman to distribute the reported hours to actions.
Base on the reported hours transactions with actual cost based on the payment group masterdata catalog and hours can be created for control units and activities.

![](image/tkprocess.png)

**Timekeeping Highlights**

1. **Fully embedded to the RIB 4.0 end to end process** - Integrates to resource planning and to controlling
2. **Employee rights system for timesheet entry** - Additional safety for employee related data
3. **Integration to RIB Personal** - Integration of reports and master data
4. **Time controlling view for the HR team** - Overview of employee reports and bulk editing accross multiple reports
5. **Time allocation view for project managers** - Matrix view for fast allocation of productive hours
6. **Overtime Calculation** - Covers up to 4 phases per day. Including nightshift, weekend and milage surcharges
7. **Generation of detailed actual cost and hours for controlling** - Tight Integration with controlling
8. **Mobile features** Possiblity to report times on a mobile device using the RIB 4.0 app
9. **Clocking Mode** Possiblity to set employees to clocking mode

**1. Fully embedded to the RIB 4.0 end to end process**

Employee reports are generated based on the module **resource reservations** as well as the timekeeping master data catalogs **Employees**, **Shift Model**, **Time Symbols** and **Payment Groups**.
Assignment of individual shift models and holiday and training calendars to each employee. Combined with the resource planning, training and vacation calendars RIB 4.0 pre-generates the timesheets for each employee individually.
Prefilled reports can be updated by employees on the desktop or the RIB 4.0 mobile app. The reported times can be validated by the HR team and using the integration to RIB HR will be automatically available in the payroll process.
After times have been reported transactions are generated that add detailed preliminary actual cost and reported hours to the controlling process.

**2. Employee rights system for timesheet entry**

To ensure data privacy of the employees the timekeeping modules have an additional rights system to make sure that users cannot access private employee data. The detailed description of the rights system can be found in the module
Employee ([EN](../../timekeeping.employee\en/index.html#rights-system)).

**3. Integration to RIB Personal**

There is an out of the box integration with RIB Personal for master data **Employees** and **Time Symbols**. In addition to that **Employee Reports** are automatically transffered to RIB Personal for the further payroll process.

**4. Time controlling view for the HR team**

The module **Time Controlling** is designed to provide the tools for the HR team to quickly evaluate reported times and bulk edit them quickly.

**5. Time allocation view for project managers**

The module **Time Allocation** has been designed with site managers in mind. The time allocation comes with a matrix view to allow the fast distribution of reported hours to actions for each employee and plant on a project or job.
Using wizards the hours can be reported back to the timekeeping results and from there transactions for controlling can be generated. In addition to that dispatch notes can be generated via wizard. The dispatch notes can then be used for the logistics settlement process.

**6. Overtime Calculation**

RIB 4.0 is capable of calculating the overtimes of the employees. Each employee has their individual working time account to review their available recuperation hours.
In the module **Working Time Model** individual targets can be defined. It is possible to set daily, weekly, monthly targets. Hours exceeding the targets can be further divided into 3 phases.
For example: an employee has a target of 8 hours per day. The 9th hour is awarded to his working time account for recuperation. The 10th hour is awarded with additional compensation pay. Everything exceeding 10 hours is awarded with a different compensation pay.
Additionally daily, weekly, monthly and yearly saving limits can be defined in case there are regulations in place that only a certain amount of recuperation time can be earned per period.
It is also possible to define hours and days where additional surcharges are awarded. For example for automated evaluation of night shift and weekend surcharges. The same functionality can also be applied to quantities.
For example it is possible to award different rates depending on driven milage.

**7. Generation of detailed actual cost and hours for controlling**

In the module **Payment Groups** individual base rates and surcharges can be defined. Each employee can have an individual payment group assigned to them in the module **Employee**. Via the time symbols **Account Allocation Rules** base rate and surcharges can be selected for time records.
Base on this information the preliminary actual cost are generated.

**8. Mobile features**

For employees reporting their time from the construction site the RIB 4.0 mobile app ([EN](../../rib40.mobileapp/en/index.html)) has a mobile module for timekeeping available.

**9. Clocking Mode for employees**

It is possible to set employees to clocking mode in the module **Employee** by setting the **Is Clocking** checkbox on the employee. For clocking employees all containers in the modules **Timekeeping** and **Time Controlling** 
are read only. Theses employees are able to clock times but cannot edit the records later.

# Containers

## Crew Recordings

The **crew recordings** is the main container in the timekeeping module. Below the crew recordings the **Employee Sheets** and the **Timekeeping Results** can be found. Typically there is one crew recording per period and crew leader.

The crew recordings container has the following columns:

| Field Name              | Description                                                  |
| ----------------------- | ------------------------------------------------------------ |
| Code                    | Code of the recording. A format can be defined by the **Rubric Category**.|
| Comments                | Free field for comments.                                     |
| Date 1-5                | A free date for any purpose can be entered here.             |
| Description             | Description of the recording that can be freely defined.     |
| Employee                | Employee assigned to the crew recording. New employees can be defined in the module **Employee**.         |
| Employee-Description    | Description of the employee.                                 |
| Inserted At             | Date and time when the crew recording has been created.      |
| Inserted By             | User who created the crew recording.                         |
| Number 1-5              | A free number for any purpose can be entered here.           |
| Payroll Year            | Payroll year of the selected period. This is taken from the module **Timekeeping Period** and is a read only column. It can be used to sort the list of crew recordings. |
| Periods                 | Description of the period.                                   |
| Periods-Code            | Code of the period of the crew recording. Periods can be defined in the module **Timekeeping Period**.                          |
| Plant                   | Plant assigned to the crew recording.                        |
| Plant-Description       | Description of the plant.                                    |
| Plant-Status            | Status of the plant.                                         |
| Rubric Category         | The rubric category of the recording can be set. In the module **Company** in the container **Number Range** under **Timekeeping Recording** the rubric category for the crew recordings can be adjusted or additional rubric categories can be defined. Since crew recordings can be generated using the wizard **Generate time sheet records** in the module **Timekeeping Period** the system needs to know what sequence should be used for the **Code** when generating the recordings. This can be defined here.                                         |
| Shift Model             | The shift model that is used by the employee assigned to the recording.                                         |
| Shift Model-Calendar    | Calendar that is used by the shift model.                    |
| Text 1-5                | A free text for any purpose can be entered here.             |
| Timekeeping recording Status               | Status of the recording. New status can be defined in the module **Customizing** under **Timekeeping Recording Status**.             |
| Updated At              | Date and time when the crew recording has been last updated. |
| Updated By              | User who last updated the crew recording.                    |


## Employee Sheets

The container **Employee Sheets** holds the sheets for each member of a crew. Each crew member has their own time recordings in the container **Employee Reports**.

| Field Name              | Description                                                  |
| ----------------------- | ------------------------------------------------------------ |
| Comments                | Free field for comments.									 |
| Employee                | Employee assigned to the sheet. New employees can be defined in the module **Employee**.                                     |
| Employee-Description    | Description of the employee.             |
| Inserted At             | Date and time when the crew recording has been created.      |
| Inserted By             | User who created the crew recording.                         |
| Sheet Symbol            | A free symbol that can be addded to the sheet. There is no functionality attached to this field out of the box. New symbols can be defined in the module **Customizing** under **Timekeeping Sheet Symbol**.      |
| Timekeeping Sheet Status| Status of the sheet. New status can be defined in the module **Customizing** under **Timekeeping Sheet Status**.                         |

<a id="employee_reports"></a>

## Employee Reports

The container **Employee Reports** can hold reports of multiple types and status. The reports can be generated from the shift model, recorded by employees and generated by the wizards for overtime and other derivations.
The purpose of the table Employee Reports is to hold reported hours such as presence, vacation, trainings, driving times as well as results of overtime evaluation and surchared times such as night shifts. This should refelct all times that an employee has reported on a workday.
From here the reported times can be forwarded to payroll systems such as RIB | Personal.

The employee reports container has the following columns:

| Field Name              | Description                                                  |
| ----------------------- | ------------------------------------------------------------ |
| Booking Date            | Date on that the recording will be booked. For example if record has a start time after midnight but the recording should be booked to the day where the shift started the booking date can be diferent from the **From Date**. |
| Break From              | Start time of the break.                                     |
| Break To                | End time of the break.                                       |
| Comments                | Free field for comments.                                     |
| Controlling Unit        | The controlling unit where the time should be booked to can be entered here.                          |
| Date 1-10               | A free date for any purpose can be entered here.             |
| Driver                  | This indicates that the employee of this report was selected as the driver for the project reported in this report. The field is set by the mobile app if **calculate travel disctance** is selected for the timekeeping group of the employee.      |
| Duration                | The duration of the record. If a **From Time** and **To Time** are recorded the duration will be calculated based in the entered times and the field is read only.       |
| From Date               | Date the recording started.                                  |
| From Time               | Time the recording started.                                  |
| Inserted At             | Date and time when the employee report has been created.     |
| Inserted By             | User who created the employee report.                        |
| Job                     | The job where the time should be booked to can be entered here.                        |
| Job-Description         | Description of the job.					                     |
| Latitude                | The latitude where the time has been recorded can be entered here.					                     |
| Longitude               | The longitude where the time has been recorded can be entered here.					                     |
| Number 1-10             | A free number for any purpose can be entered here.           |
| Project                 | The project where the time should be booked to can be entered here. It is possible to call a search assistant for this field by clicking into the field and clicking the icon with the 3 dots in the field. In the search assistant it is possible so select a **clerk**. If a clerk is selected the list of projects is filtered for projects where the selected clerk is assigned to the project via the container **Clerk Rights** in the module **Project**. The list of projects can be further filtered by using the search field. The text entered to the seach field will be compared with the fields **Project Number**, **Name** and **Name 2**.                    |
| Project-Description     | Description of the project.					                     |
| Project Action          | The project action where the time should be booked to can be entered here. Project Actions can be defined in the module **Project** in the container **Actions**.                 |
| Project Action-Description     | Description of the project action.					                     |
| Recording States		  | This field is not available in the UI. The field is used for clocking workflows to define in what state an employee report is. For example: working or on a break. This information can be used for filters like show all employees who are currently working. If this field is changed manually it may cause errors in the system. Values: **NULL** - Initial value. Clocking not yet started **1** - Clocked in. The employee is currently working **2** - On Break. The employee clocked out for a break **999** - Clocked out. The employee has ended time recording on this report. 1000 Recording state for travel times to site. 2000 recording state for travel distance to site. 3000 recording state for return travel time from site. 4000 recording state for return travel disctance from site. |
| Text 1-10               | A free text for any purpose can be entered here.             |
| Time Symbol    	      | Time symbol used for the time record. Time symbols can be defined in the module **Time Symbols**.	                     |
| Time Symbol-Description | Description of the time symbol.					              |
| Time Symbol Group       | Description of the time symbol group. The time symbol group can be assigned to a time symbol in the module **Time Symbols** and can be used here for sorting of the records.            |
| Time Symbol-UoM		  | Unit of measurement of the used time symbol. The UoM can be assigned to a time symbol in the module **Time Symbols**.					              |
| To Date                 | Date the recording ended.                                  |
| To Time                 | Time the recording ended.                                  |
| Updated At              | Date and time when the employee report has been last updated. |
| Updated By              | User who last updated the employee report.                    |
| Weekday                 | Weekday of the booking date of the recording. This is for information and sorting purposes.                                  |

<a id="break"></a>

## Break

The container **Break** can hold breaks for the selected **Employee Report**. Using this container it is comfortably possible to record more than one break per employee report. 
As soon as a break is added here the **break from** and **break to** fields of the selected employee report will become read only. If there is only one break record the **Break from** and **Break to** fields
of the employee report will show the values of the **Break from time** and **break to time** fields of the break container. If there are more than one break records the **break from** and **break to** fields of the employee report
will appear empty.

The break container has the following columns:

| Field Name              | Description                                                  |
| ----------------------- | ------------------------------------------------------------ |
| Break From Date            | Start date of the break.                                     |
| Break To Date               | End date of the break.                                       |
| Break From Time             | Start time of the break.                                     |
| Break To Time               | End time of the break.                                       |
| Duration                | The duration of the record. If a **From Time** and **To Time** are recorded the duration will be calculated based in the entered times.       |
| Inserted At             | Date and time when the employee report has been created.     |
| Inserted By             | User who created the employee report.                        |
| Latitude                | The latitude where the time has been recorded can be entered here.					                     |
| Longitude               | The longitude where the time has been recorded can be entered here.					                     |
| Updated At              | Date and time when the employee report has been last updated. |
| Updated By              | User who last updated the employee report.                    |

## Timekeeping Report Verification
The container **Timekeeping Report Verification** container can be used as an audit trail. It can be activated by setting the column **Is Report Verification** in the module **Company** in the container **Timekeeping Groups**.
If activated a new record is automatically created if one of the values **From Time**, **From Date**, **To Time**, **To Date**, **Break From** or **Break to** is modified.
Using this container it is possible to see where a user has recorded the time, if he has modified it, and if the recorded time was recorded at the same time the user set the value to.
The Timekeeping Report Verification container has the following columns:
  
| Field Name          | Description		|
| -------------------| ------------------------------------------------------------- |
| Comments           |        Free field for comments. Can be edited.|
| Inserted At        |        Date and time when the employee report has been created. |
| Inserted At Original   |    Time the employee report was changed. |
| Inserted By            |    User who created the employee report. | 
| Inserted By Original   |   User who change the employee report. |
| Report Verification Type |  States if the update was **From Time**, **To Time**, **Break From** or **Break to**.  |
| Time Recorded            |  The time that was recorded. |
| Latitude                 |  The latitude where the time has been recorded can be entered here. |
| Longitude                |  The longitude where the time has been recorded can be entered here. |
| Updated At               |  Date and time when the employee report has been last updated. |
| Updated By               |  User who last updated the employee report. |

## Timekeeping Results

The container **Timekeeping Results** holds time records that have been allocated to control units already. It is best practice to report productive hours in the container employee reports and then create additinal records in the container timekeeping results to distribute this productive hours to actions.
Typically the records in this table are the result of the work flow in the **Time Allocation** module.

| Field Name              | Description                                                  |
| ----------------------- | ------------------------------------------------------------ |
| Comments                | Free field for comments.                                     |
| From Time               | Start time of the action reported. This field is read only on the desktop and can only be set via clocking on the mobile RIB 4.0 timekeeping app by clocking in with an action or switching an action. If from and to time are set the duration is calculated automatically. Breaks will be deducted. The value in this field will be removed if the duration is adjusted manually.                                     |
| Hours                   | Hours worked on an action.                                  |
| Inserted At             | Date and time when the employee report has been created.     |
| Inserted By             | User who created the employee report.                        |
| Plant                   | Code of the plant that worked on the action.                        |
| Plant-Description       | Description of the plant.					                     |
| Plant-Status            | Status of the plant.           |
| Project                 | The project where the time is booked to can be entered here.                        |
| Project Action          | The project action where the time should be booked to can be entered here. Project Actions can be defined in the module **Project** in the container **Actions**.                 |
| Project Action-Description     | Description of the project action.					                     |
| Project-Name            | Name of the project.					                     |
| Rate                    | If this record is for a plant the rate can be entered here. For employees the rate will be evaluated based on the payment group and should be empty.             |
| Sheet                   | The sheet of the employee can be selected here. This is a selectable field the rest of the sheet fields will be filled based on this selection.					                     |
| Sheet-Comments          | Shows comments of the employee sheet.					                     |
| Sheet-Employee          | Shows the employee connected to the selected sheet.					                     |
| Sheet-Family Name       | Shows the family name of the employee connected to the selected sheet.				                     |
| Sheet-First Name        | Shows the first name of the employee connected to the selected sheet.					                     |
| Sheet-Sheet Symbol      | Shows the sheet symbol of the selected sheet.					                     |
| Sheet-Timekeeping Sheet Status | Shows the status of the selected sheet.						                     |
| Time Symbol    	      | Time symbol used for the time record. Time symbols can be defined in the module **Time Symbols**. Since time symbols can be filtered by the timekeeping group, the employee has to be known in order to set a time symbol. Please select Sheet-Employee before selecting the time symbol.	                     |
| Time Symbol-Description | Description of the time symbol.					              |
| Time Symbol Group       | Description of the time symbol group. The time symbol group can be assigned to a time symbol in the module **Time Symbols** and can be used here for sorting of the records.            |
| Time Symbol-UoM		  | Unit of measurement of the used time symbol. The UoM can be assigned to a time symbol in the module **Time Symbols**.					              |
| To Time                 | End time of the action reported. This field is read only on the desktop and can only be set via clocking on the mobile RIB 4.0 timekeeping app by clocking in with an action or switching an action. If from and to time are set the duration is calculated automatically. Breaks will be deducted. The value in this field will be removed if the duration is adjusted manually.                                     |
| Updated At              | Date and time when the employee report has been last updated. |
| Updated By              | User who last updated the employee report.                    |

# Logic to find crew recording
To create an employee report the crew recording and the employee sheet have to be set on the employee report. Otherwise the employee report would not be shown. In the module **Timekeeping** when selecting a **Crew Recording** 
and an **Employee Sheet** the **Employee Reports** are filtered by the selected records. This can only be done if the crew recording and the employee sheet are recorded on the employee report. When a new employee report
is created in the module **Timekeeping** the values for crew recording and employee sheet can be taken from the currently selected crew recording and employee sheet. However in the modules **Time Allocation** and **Time Controlling**
as well as when creating a new employee report via the public API the crew recording and the employee sheet are not selected. They have to be found by the system. To find the crew recording and the employee sheet the
following logic is applied:

## Find the period
The period can by identified by the booking date for that the employee report should be created and the timekeeping group of the employee who wants to report. 
To find the period filter for:
timekeeping group == timekeeping group of the employee
booking date >= period start date
booking date <= period end date
This will provide a unique period.

## Find the crew recording
The employee attached the the crew recording can be found with the following logic:
Filter the crew recordings for the period that has already been identified.
Filter the crew recordings for the employee the record should be created for.
If no record can be found find the Crew Assignments of the current employee and use the current one. (based on booking date and valid from / to of the crew assignment). Find the employee of the crew assignment.
Try to filter the crew recordings again using the found crew leader.
If no result can be found repeat the step searching for crew assignments of the found crew leader.
Do this until a crew recording is found.

## Find the employee sheet
Using the crew recording ID and ID of the employee the record should be created for the employee sheet ID can be found.

# Wizards

By default the wizards are not shown in the module **Timekeeping** however they can be added in the module **modules** in the tab **wizards**. In the container **modules** look for **timekeeping.recording** in the column **Internal Name**. Add a wizard group in the container **Wizard Group** and add the wizards to your group in the container **Wizards to Group**.

##Overview

| Wizard Name                                                  | Wizard Group | Description                                                  |
| ------------------------------------------------------------ | ------------ | ------------------------------------------------------------ |
| [Calculate Overtime](wz-calculate-overtime-en.html)| Working Time  | Calculates Overtime. |
| [Calculate Other Derivations](wz-calculate-other-derivations-en.html)| Working Time  | Calculates other derivations. |
| [Calculate Travel Distance](wz-calculate-travel-distance-en.html)| Allowance  | Calculates distance between place of work and project. |
| [Copy Repororts](wz-copy-reports-en.html)| Reports  | Copies Reports. |
| [Set Repororts inactive](wz-reports-inactive-en.html)| Reports  | Disables Reports. |
| [Set Repororts active](wz-reports-active-en.html)| Reports  | Enables Reports. |
| [Bulk Report Status](wz-bulk-set-status-en.html)| Reports  | Sets the status of all reports in the selected time frame. |
| [Clocking your times for timekeeping](wz-clock-your-times-en.html)| Reports  | Desktop Clocking. |