using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Estimate.Assemblies.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	/// 
	/// </summary>
	public class EstProjectRelEntityCollectionResult
	{
		/// <summary>
		/// 
		/// </summary>
		public List<Tuple<int, int>> ProjectCostCodesToSave = new List<Tuple<int, int>>();

		/// <summary>
		/// 
		/// </summary>
		public List<Tuple<int, int, IScriptEstResource>> ProjectMaterialsToSave = new List<Tuple<int, int, IScriptEstResource>>();

		/// <summary>
		/// 
		/// </summary>
		public List<Tuple<int, int, int>> ProjectAssembliesToSave = new List<Tuple<int, int, int>>();

		/// <summary>
		/// 
		/// </summary>
		public List<Tuple<int, int>> PrjCostCodesJobRatesToSave = new List<Tuple<int, int>>();

		/// <summary>
		/// 
		/// </summary>
		public List<Tuple<int, int, int>> ProjectPlantAssembliesToSave = new List<Tuple<int, int, int>>();

		/// <summary>
		/// 
		/// </summary>
		public List<int> ProjectExchangeRateToSave = new List<int>();

		/// <summary>
		/// 
		/// </summary>
		/// <param name="resourcesToSave"></param>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// <param name="estMainComplete"></param>
		public EstProjectAssemblyRelCollectionResult Save(List<EstResourceEntity> resourcesToSave, int? projectId, int? jobId, EstMainCompleteEntity estMainComplete)
		{
			var result = new EstProjectAssemblyRelCollectionResult();

			if (!projectId.HasValue)
			{
				return result;
			}
			IEnumerable<IProjectPlantAssemblyData> projPlantAssemblyDatas = new List<IProjectPlantAssemblyData>();
			IEnumerable<IProjectAssemblyData> projAssemblyDatas = new List<IProjectAssemblyData>();

			// create project assemblies, then assign back to resource; collect cost code and marerial to create project side
			if (this.ProjectAssembliesToSave != null && this.ProjectAssembliesToSave.Any())
			{
				//Assembly Header Id
				var assemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();

				//Assembly Ids
				var assemblyIds = this.ProjectAssembliesToSave.Select(e => e.Item1).ToList();

				projAssemblyDatas = new EstProjectAssemblyLogic(resourcesToSave).CreateProjectAssemblyAndCopyCompleteInfo(assemblyIds, (int)estMainComplete.ProjectId, assemblyHeaderId, true, jobId, this.ProjectCostCodesToSave, this.ProjectMaterialsToSave, result.PrjAssemblyIdsToUpdate);

				if (projAssemblyDatas.Any())
				{
					result.PrjAssemblyHeaderId = projAssemblyDatas.FirstOrDefault().PrjAssemblyHeaderId;
				}

				if (projAssemblyDatas.Any() && estMainComplete.IsPrjectAssembly)
				{
					estMainComplete.IsLoadPrjAssembly = true;
				}
			}

			if (this.ProjectCostCodesToSave != null && this.ProjectCostCodesToSave.Any())
			{
				var prjCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();

				prjCostCodeLogic.SaveCostCodes(this.ProjectCostCodesToSave, (int)estMainComplete.ProjectId);
			}

			if (this.PrjCostCodesJobRatesToSave != null && this.PrjCostCodesJobRatesToSave.Any())
			{
				var prjCostCodeLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectCostCodesLogic>();

				prjCostCodeLogic.SavePrjCostCodesJobRates(this.PrjCostCodesJobRatesToSave, (int)estMainComplete.ProjectId);
			}

			if (this.ProjectMaterialsToSave != null && this.ProjectMaterialsToSave.Any())
			{
				var prjMaterialLogic = RVPARB.BusinessEnvironment.GetExportedValue<IProjectMaterialLogic>();

				prjMaterialLogic.SaveMaterials(this.ProjectMaterialsToSave, (int)estMainComplete.ProjectId, true);
			}

			//Project Currency Exchange Rate per Project
			if (this.ProjectExchangeRateToSave != null && this.ProjectExchangeRateToSave.Any() && estMainComplete.ProjectId > 0)
			{
				var prjInfoProvider = RVPARB.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();

				prjInfoProvider.SaveCurrencyRateItems(this.ProjectExchangeRateToSave, (int)estMainComplete.ProjectId);
			}

			if (this.ProjectPlantAssembliesToSave != null && this.ProjectPlantAssembliesToSave.Any())
			{
				var plantAssemblyIds = this.ProjectPlantAssembliesToSave.Select(e => e.Item1).ToList();

				var plantAssemblyHeaderId = new EstimateAssembliesLogic().GetHeaderPlantFkByCurrentLineItemContext();

				projPlantAssemblyDatas = new EstProjectPlantAssemblyLogic(resourcesToSave).CreateProjectPlantAssemblyAndCopyCompleteInfo(plantAssemblyIds, (int)estMainComplete.ProjectId, plantAssemblyHeaderId, false, this.ProjectPlantAssembliesToSave.Select(e => e.Item3).FirstOrDefault(), this.ProjectCostCodesToSave, this.ProjectMaterialsToSave, result.PrjPlantAssemblyIdsToUpdate);

				if (projPlantAssemblyDatas.Any())
				{
					result.PrjPlantAssemblyHeaderId = projPlantAssemblyDatas.FirstOrDefault().PrjPlantAssemblyHeaderId;
				}
			}

			return result;
		}
	}
}
