using System.Collections.Generic;
using System.Linq;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Scheduling.Main.Core;
using System;
using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Scheduling.Main.BusinessComponents.Logic;
using NLS = RIB.Visual.Scheduling.Main.Localization.Properties.Resources;
using RIB.Visual.Scheduling.Schedule.BusinessComponents;
using RIB.Visual.Scheduling.Calendar.BusinessComponents;

namespace RIB.Visual.Scheduling.Main.BusinessComponents
{
	/// <summary>
	/// Logic class for validating and calculating scheduling activities
	/// </summary>
	public class ProgressCalculation
	{
		private List<IEstLineItemEntity> _lineItems = null;
		private List<IEstHeaderEntity> _estHeader = null;
		private bool _withTransientProperties = true;

		/// <summary>
		/// Validates progress data of an activity
		/// </summary>
		/// <param name="activityComplete">The complete update data containing also information about changes in progress reporting</param>
		/// <param name="pool">Schedule data pool</param>
		/// <param name="bAssertDueDateReport">Controls if due date related calc is started</param>
		/// <param name="withTransientProperties"></param>
		public ActivityComplete ValidateProgress(ActivityComplete activityComplete, ScheduleDataPool pool, bool bAssertDueDateReport, bool withTransientProperties = true)
		{
			//var progressLogic = new ActivityProgressReportLogic();
			var execCalc = new ExecutionCalculation();
			ActivityProgressReportEntity progressReportEntity = null;
			ActivityEntity activity = GetActivity(activityComplete, pool);

			_withTransientProperties = withTransientProperties;

			bool progReportIsLineItemBased = activity.ProgressReportMethodFk == Constants.ProgressReportMethodByLineItemQuantity ||
														activity.ProgressReportMethodFk == Constants.ProgressReportMethodByLineItemWork ||
														activity.ProgressReportMethodFk == Constants.ProgressReportMethodByModelObject;

			if (activityComplete.MainItemId == 0)
			{
				activityComplete.MainItemId = activityComplete.Activity.Id;
			}
			if (activityComplete.Activity == null)
			{
				activityComplete.Activity = activity;
			}
			if (bAssertDueDateReport)
			{
				if (progReportIsLineItemBased)
				{
					progressReportEntity = activity.ProgressReportMethodFk == Constants.ProgressReportMethodByModelObject ? AssertDueDateReportForMdlObjects(activityComplete, activityComplete.LineItemProgress, pool) :  AssertDueDateReport(activityComplete, activityComplete.LineItemProgress, pool);
				}
				else
				{
					progressReportEntity = AssertDueDateReport(activityComplete, pool);
				}
			}

			var reps = pool.GetReports(rep => rep.ActivityFk == activityComplete.MainItemId && rep.EstimateHeaderIsActive && rep.Version >= 1);

			var oldCount = reps.Count();

			if (reps.Any() && activityComplete.ProgressReportsToDelete != null)
			{
				foreach (var rep in activityComplete.ProgressReportsToDelete)
				{
					reps = reps.Where(e => e.Id != rep.Id).Select(e => e);
				}
			}

			if (activityComplete.ProgressReportsToSave != null)
			{
				foreach (var rep in activityComplete.ProgressReportsToSave)
				{
					reps = reps.Where(e => e.Id != rep.Id).Select(e => e);
				}
			}

			var tmp = new List<ActivityProgressReportEntity>();
			tmp.AddRange(reps);
			if (activityComplete.ProgressReportsToSave != null)
			{
				tmp.AddRange(activityComplete.ProgressReportsToSave);
			}

			var progReports = tmp.OrderBy(e => e.PerformanceDate);

			if (progReports.Any())
			{
				CalculateReportTotals(progReports,
					activityComplete.Activity.ProgressReportMethodFk.Value == Constants.ProgressReportMethodByLineItemQuantity ||
					activityComplete.Activity.ProgressReportMethodFk.Value == Constants.ProgressReportMethodByLineItemWork,
					activityComplete.Activity.ProgressReportMethodFk.Value == Constants.ProgressReportMethodByModelObject);

				if (activityComplete.Activity.ProgressReportMethodFk.HasValue && progressReportEntity != null)
				{
					Dispatch(progReports, activityComplete, progressReportEntity, pool);
				}

				if (activityComplete.Activity != null && activityComplete.ActivityPlanningChange != null && _withTransientProperties)
				{
					if (activityComplete.LineItemProgress != null)
					{
						var lineItemCalc = new LineItemCompletionCalculation();
						lineItemCalc.ProvideCompletionInformationTo(activityComplete.Activity, activityComplete.LineItemProgress, progReports, activityComplete.ActivityPlanningChange.DueDate);
					}
					var cl = new CompletionCalculation();
					cl.ProvideCompletionInformationTo(activityComplete.Activity, progReports,
						activityComplete.ActivityPlanningChange.DueDate, pool);
					var parentId = activityComplete.Activity.ParentActivityFk;
					while (parentId.HasValue)
					{
						var parent = pool.GetActivityById(parentId.Value);
						if (parent != null)
						{
							cl.ProvideCompletionInformationTo(parent, progReports, activityComplete.ActivityPlanningChange.DueDate, pool);
							if (activityComplete.EffectedActivities == null)
							{
								activityComplete.EffectedActivities = new List<ActivityEntity>();
							}
							if (!activityComplete.EffectedActivities.Any(a => a.Id == parent.Id))
							{
								activityComplete.EffectedActivities.Add(parent);
							}
						}
						parentId = parent.ParentActivityFk;
					}
				}

				if (oldCount == 0 && progReports.Any())
				{
					execCalc.AssertActivityStarted(activityComplete, pool);
				}
			}

			return activityComplete;
		}

		private ActivityEntity GetActivity(ActivityComplete activityComplete, ScheduleDataPool pool)
		{
			return activityComplete.Activity ?? pool.Activities.FirstOrDefault(a => a.Id == activityComplete.MainItemId);
		}

		/// <summary>Handle the case of no progress report</summary>
		/// <returns></returns>
		private void HandleNoProgReport(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete)
		{
			//Nothing to do, we do not report
		}

		/// <summary>Handle the case of progress report by quantity</summary>
		/// <returns></returns>
		private void HandleProgReportByQuantity(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, ActivityProgressReportEntity progressReportEntity)
		{
			if (activityComplete.Activity.Quantity.HasValue)
			{
				PrepareReportByQuantity(progReports, activityComplete, progressReportEntity);

				decimal activityQuantity = activityComplete.Activity.Quantity.Value;

				bool saveAsChanged = false;
				decimal sumOfQuantity = (decimal)0.0;


				foreach (var prog in progReports)
				{
					if (!saveAsChanged)
					{
						saveAsChanged = CheckIfNeedToSave(prog, activityComplete);
					}
					sumOfQuantity += (prog.Quantity.HasValue) ? prog.Quantity.Value : (decimal)0.0;

					if (saveAsChanged)
					{
						prog.RemainingQuantity = activityQuantity - sumOfQuantity;

						prog.PCo = (sumOfQuantity / activityQuantity) * (decimal)100.0;
						prog.RemainingPCo = ((activityQuantity - sumOfQuantity) / activityQuantity) * (decimal)100.0;

						if (!activityComplete.ProgressReportsToSave.Contains(prog))
						{
							activityComplete.ProgressReportsToSave.Add(prog);
						}
					}
				}

				//update Activity values from last progress report
				if (activityComplete.Activity != null && activityComplete.ProgressReportsToSave.Any() && _withTransientProperties)
				{
					var lastProg = activityComplete.ProgressReportsToSave.OrderByDescending(e => e.PerformanceDate).FirstOrDefault();
					new CompletionCalculation().CalculateActivityPerformanceFromLastReportByActivityQuantity(activityComplete.Activity, lastProg);
				}
			}
		}

		/// <summary>Handle the case of progress report by work</summary>
		/// <returns></returns>
		private void HandleProgReportByWork(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, ActivityProgressReportEntity progressReportEntity)
		{
			if (activityComplete.Activity.Work.HasValue)
			{
				PrepareReportByWork(progReports, activityComplete, progressReportEntity);
				decimal activityWork = activityComplete.Activity.Work.Value;

				bool saveAsChanged = false;
				decimal sumOfWork = (decimal)0.0;


				foreach (var prog in progReports)
				{
					if (!saveAsChanged)
					{
						saveAsChanged = CheckIfNeedToSave(prog, activityComplete);
					}

					sumOfWork += (prog.Work.HasValue) ? prog.Work.Value : (decimal)0.0;

					if (saveAsChanged)
					{
						prog.RemainingWork = activityWork - sumOfWork;

						prog.PCo = (sumOfWork / activityWork) * (decimal)100.0;
						prog.RemainingPCo = ((activityWork - sumOfWork) / activityWork) * (decimal)100.0;

						if (!activityComplete.ProgressReportsToSave.Contains(prog))
						{
							activityComplete.ProgressReportsToSave.Add(prog);
						}
					}
				}

				//update Activity values from last progress report
				if (activityComplete.Activity != null && activityComplete.ProgressReportsToSave.Any() && _withTransientProperties)
				{
					var lastProg = activityComplete.ProgressReportsToSave.OrderByDescending(e => e.PerformanceDate).FirstOrDefault();
					new CompletionCalculation().CalculateActivityPerformanceFromLastReportByActivityWork(activityComplete.Activity, lastProg);
				}
			}
		}

		/// <summary>Handle the case of progress report by quantity of assigned line items</summary>
		/// <returns></returns>
		private void HandleProgReportByLineItemQuantity(IEnumerable<ActivityProgressReportEntity> progReports,
			ActivityComplete activityComplete, LineItemProgressEntity lineItemProgress,
			ActivityProgressReportEntity progressReportEntity)
		{
			if (lineItemProgress == null)
			{
				HandleMultipleLineItemProgressRequest(progReports, activityComplete, lineItemProgress, progressReportEntity,
					HandleProgReportByLineItemQuantity);
			}
			else
			{
				var lineItemProgReports = progReports.Where(e => e.EstLineItemFk == lineItemProgress.LineItemFk && lineItemProgress.EstimationHeaderFk == e.EstHeaderFk);
				PrepareReportByLineItemQuantity(lineItemProgReports, activityComplete, lineItemProgress, progressReportEntity);

				bool saveAsChanged = false;
				decimal sumOfQuantity = (decimal)0.0;

				foreach (var prog in lineItemProgReports)
				{
					if (!saveAsChanged)
					{
						saveAsChanged = CheckIfNeedToSave(prog, activityComplete);
					}
					sumOfQuantity += (prog.Quantity.HasValue) ? prog.Quantity.Value : (decimal)0.0;

					if (saveAsChanged && prog.EstLineItemFk.HasValue)
					{
						decimal quantity = lineItemProgress.Quantity.GetValueOrDefault();

						if (quantity != 0)
						{
							prog.RemainingQuantity = quantity - sumOfQuantity;
							prog.PCo = (sumOfQuantity / quantity) * (decimal)100.0;
							prog.RemainingPCo = ((quantity - sumOfQuantity) / quantity) * (decimal)100.0;
							prog.QuantityTotal = quantity;
						}

						if (!activityComplete.ProgressReportsToSave.Contains(prog))
						{
							activityComplete.ProgressReportsToSave.Add(prog);
						}

					}
				}

				//update Activity values from last progress report
				if (activityComplete.ProgressReportsToSave.Any())
				{
					//var lastProg = _activityUpdateData.ProgressReportsToSave.OrderByDescending(e => e.PerformanceDate).FirstOrDefault();
					var lastProg =
						activityComplete.ProgressReportsToSave.Where(e => e.EstLineItemFk == lineItemProgress.LineItemFk && lineItemProgress.EstimationHeaderFk == e.EstHeaderFk)
							.OrderByDescending(e => e.PerformanceDate)
							.FirstOrDefault();
					if (lastProg != null)
					{
						lineItemProgress.PCo = lastProg.PCo;
						lineItemProgress.DueDateQuantityPerformance = lineItemProgress.Quantity - lastProg.RemainingQuantity;
						lineItemProgress.RemainingLineItemQuantity = lastProg.RemainingQuantity;
						activityComplete.LineItemProgress = lineItemProgress;
					}
				}
				//update Activity values from last progress report
				if (activityComplete.Activity != null && activityComplete.ProgressReportsToSave.Any())
				{
					var lastProg = activityComplete.ProgressReportsToSave.OrderByDescending(e => e.PerformanceDate).FirstOrDefault();
					if (lastProg != null)
					{
						activityComplete.Activity.PercentageCompletion = lastProg.PCo;
						activityComplete.Activity.DueDateQuantityPerformance = activityComplete.Activity.Quantity -
																								 lastProg.RemainingQuantity;
						activityComplete.Activity.RemainingActivityQuantity = lastProg.RemainingQuantity;
						activityComplete.Activity.LastProgressDate = lastProg.PerformanceDate;
					}
				}
			}
		}

		/// <summary>Handle the case of progress report by work of assigned line items</summary>
		/// <returns></returns>
		private void HandleProgReportByLineItemWork(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, LineItemProgressEntity lineItemProgress, ActivityProgressReportEntity progressReportEntity)
		{
			if (lineItemProgress == null)
			{
				HandleMultipleLineItemProgressRequest(progReports, activityComplete, lineItemProgress, progressReportEntity,
					HandleProgReportByLineItemWork);
			}
			else if (lineItemProgress.Work.HasValue)
			{
				PrepareReportByLineItemWork(progReports, activityComplete, progressReportEntity);

				bool saveAsChanged = false;
				decimal sumOfWork = (decimal)0.0;

				foreach (var prog in progReports)
				{
					if (!saveAsChanged)
					{
						saveAsChanged = CheckIfNeedToSave(prog, activityComplete);
					}

					if (saveAsChanged && prog.EstLineItemFk.HasValue)
					{
						decimal work = (decimal)0.0;
						work = lineItemProgress.Work.GetValueOrDefault();

						sumOfWork += prog.WorkTotal;

						prog.RemainingWork = work - sumOfWork;

						prog.PCo = (sumOfWork / work) * (decimal)100.0;
						prog.RemainingPCo = ((work - sumOfWork) / work) * (decimal)100.0;
						//prog.Work = lineItemProgress.DueDateQuantityPerformance;
						if (!activityComplete.ProgressReportsToSave.Contains(prog))
						{
							activityComplete.ProgressReportsToSave.Add(prog);
						}

					}
				}

				//update Activity values from last progress report
				if (activityComplete.LineItemProgress != null && activityComplete.ProgressReportsToSave.Any())
				{
					//var lastProg = _activityUpdateData.ProgressReportsToSave.OrderByDescending(e => e.PerformanceDate).FirstOrDefault();
					var lastProg = activityComplete.ProgressReportsToSave.Where(e => e.EstLineItemFk == activityComplete.LineItemProgress.LineItemFk &&
					lineItemProgress.EstimationHeaderFk == e.EstHeaderFk).OrderByDescending(e => e.PerformanceDate).FirstOrDefault();
					if (lastProg != null)
					{
						activityComplete.LineItemProgress.PCo = lastProg.PCo;

						//quantity
						activityComplete.LineItemProgress.DueDateQuantityPerformance = activityComplete.LineItemProgress.Quantity - lastProg.RemainingQuantity;
						activityComplete.LineItemProgress.RemainingLineItemQuantity = lastProg.RemainingQuantity;

						//work
						//activityComplete.LineItemProgress.DueDateWorkPerformance = activityComplete.LineItemProgress.Work - lastProg.RemainingWork;
						activityComplete.LineItemProgress.DueDateWorkPerformance = activityComplete.LineItemProgress.RemainingLineItemWork - lastProg.RemainingWork;
						activityComplete.LineItemProgress.RemainingLineItemWork = lastProg.RemainingWork ?? 0;
					}
				}
			}
		}

		private bool CheckIfNeedToSave(ActivityProgressReportEntity prog, ActivityComplete activityComplete)
		{
			//1) Check if in data to be saved -> true
			var filtered = activityComplete.ProgressReportsToSave.Where(p => p.Id == prog.Id).ToList();
			bool bSave = filtered.Any();

			if (!bSave && activityComplete.ProgressReportsToDelete != null)
			{//Check if some progress dated before own instance is deleted
				bSave = (activityComplete.ProgressReportsToDelete.Select(p => p.PerformanceDate <= prog.PerformanceDate).Any());
			}

			return bSave;
		}

		private void PrepareReportByQuantity(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, ActivityProgressReportEntity progressReportEntity)
		{
			if (activityComplete.ActivityPlanningChange == null)
			{
				return;
			}

			ActivityProgressReportEntity lastReport = null;
			if (progressReportEntity != null)
			{
				lastReport = progReports.Where(p => p.PerformanceDate < progressReportEntity.PerformanceDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}
			else
			{
				lastReport = progReports.Where(p => p.PerformanceDate < activityComplete.ActivityPlanningChange.DueDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}

			bool needAdjustment = false;

			//remaining percentage changed
			if (activityComplete.ActivityPlanningChange.PercentageRemaining.HasValue)
			{
				activityComplete.ActivityPlanningChange.PercentageCompletion = 100 -
																									activityComplete.ActivityPlanningChange
																										.PercentageRemaining.Value;
			}
			//percentage completion value changed
			if (activityComplete.ActivityPlanningChange.PercentageCompletion.HasValue)
			{
				if (activityComplete.ActivityPlanningChange.PercentageCompletion.Value == 100)
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = activityComplete.Activity.Quantity;
					needAdjustment = true;
				}
				else
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = (activityComplete.Activity.Quantity *
																												 activityComplete.ActivityPlanningChange
																													 .PercentageCompletion.Value) / 100;
				}
			}
			//Remaining Quantity changed
			if (activityComplete.ActivityPlanningChange.RemainingActivityQuantity.HasValue && !activityComplete.ActivityPlanningChange.DueDateQuantityPerformance.HasValue)
			{
				if (activityComplete.ActivityPlanningChange.RemainingActivityQuantity.Value == 0)
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = activityComplete.Activity.Quantity;
					needAdjustment = true;
				}
				else
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = activityComplete.Activity.Quantity -
																												activityComplete.ActivityPlanningChange
																													.RemainingActivityQuantity.Value;
				}
			}
			//Period Quantity changed
			if (activityComplete.ActivityPlanningChange.PeriodQuantityPerformance.HasValue)
			{
				activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = activityComplete.ActivityPlanningChange.PeriodQuantityPerformance;
				if (activityComplete.Activity.PeriodQuantityPerformance.HasValue)
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -= activityComplete.Activity.PeriodQuantityPerformance.Value;
				}
				if (activityComplete.Activity.RemainingActivityQuantity.HasValue)
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance += activityComplete.Activity.Quantity - activityComplete.Activity.RemainingActivityQuantity.Value;
				}
			}

			//Due Date Quantity value changed
			if (activityComplete.ActivityPlanningChange.DueDateQuantityPerformance.HasValue)
			{
				if (lastReport != null)
				{//remove performance of former reports
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -= lastReport.QuantityTotal;

					if (needAdjustment)
					{
						var delta = lastReport.PlannedQuantity - activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -
										lastReport.QuantityTotal;
						activityComplete.ActivityPlanningChange.DueDateQuantityPerformance += delta;
					}
				}
				progressReportEntity.Quantity = activityComplete.ActivityPlanningChange.DueDateQuantityPerformance.Value;
			}

			//Quantity changed in progress report history container
			if (activityComplete.ActivityPlanningChange.ProgressReportQuantity.HasValue)
			{
				progressReportEntity.Quantity = activityComplete.ActivityPlanningChange.ProgressReportQuantity.Value;
				if (lastReport != null)
				{
					//remove performance of former reports
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = progressReportEntity.Quantity +
																												lastReport.QuantityTotal;
				}
				else
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = progressReportEntity.Quantity;
				}
			}
		}

		private void PrepareReportByWork(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, ActivityProgressReportEntity progressReportEntity)
		{
			if (activityComplete.ActivityPlanningChange == null)
			{
				return;
			}

			ActivityProgressReportEntity lastReport = null;
			if (progressReportEntity != null)
			{
				lastReport = progReports.Where(p => p.PerformanceDate < progressReportEntity.PerformanceDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}
			else
			{
				lastReport = progReports.Where(p => p.PerformanceDate < activityComplete.ActivityPlanningChange.DueDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}

			//measured performance value changed
			if (activityComplete.ActivityPlanningChange.PercentageCompletion.HasValue)
			{
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance = (activityComplete.Activity.Work * activityComplete.ActivityPlanningChange.PercentageCompletion.Value) / 100;
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance -= progressReportEntity.WorkTotal;
			}

			//Remaining Work changed
			if (activityComplete.ActivityPlanningChange.RemainingActivityWork.HasValue)
			{
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance = activityComplete.Activity.Work - activityComplete.ActivityPlanningChange.RemainingActivityWork.Value;
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance -= progressReportEntity.WorkTotal;
			}
			//Period Work changed
			if (activityComplete.ActivityPlanningChange.PeriodWorkPerformance.HasValue)
			{
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance = activityComplete.ActivityPlanningChange.PeriodWorkPerformance;
				if (activityComplete.Activity.PeriodWorkPerformance.HasValue)
				{
					activityComplete.ActivityPlanningChange.DueDateWorkPerformance -= activityComplete.Activity.PeriodWorkPerformance;
				}
			}

			//Due Date Work value changed
			if (activityComplete.ActivityPlanningChange.DueDateWorkPerformance.HasValue)
			{
				if (lastReport != null)
				{
					progressReportEntity.WorkTotal = lastReport.WorkTotal +
																activityComplete.ActivityPlanningChange.DueDateWorkPerformance.Value;
					progressReportEntity.Work = activityComplete.ActivityPlanningChange.DueDateWorkPerformance;
				}
				else
				{
					progressReportEntity.Work = progressReportEntity.WorkTotal = activityComplete.ActivityPlanningChange.DueDateWorkPerformance.Value;
				}
			}
		}

		private void PrepareReportByLineItemQuantity(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, LineItemProgressEntity lineItemProgress, ActivityProgressReportEntity progressReportEntity)
		{
			if (activityComplete.ActivityPlanningChange == null)
			{
				return;
			}

			ActivityProgressReportEntity lastReport = null;
			if (progressReportEntity != null)
			{
				lastReport = progReports.Where(p => p.PerformanceDate < progressReportEntity.PerformanceDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}
			else
			{
				lastReport = progReports.Where(p => p.PerformanceDate < activityComplete.ActivityPlanningChange.DueDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}

			bool needAdjustment = false;

			//remaining percentage changed
			if (activityComplete.ActivityPlanningChange.PercentageRemaining.HasValue)
			{
				activityComplete.ActivityPlanningChange.PercentageCompletion = 100 -
																									activityComplete.ActivityPlanningChange
																										.PercentageRemaining.Value;
			}

			//percentage completion value changed
			if (activityComplete.ActivityPlanningChange.PercentageCompletion.HasValue)
			{
				if (activityComplete.ActivityPlanningChange.PercentageCompletion.Value == 100)
				{

					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = lineItemProgress.Quantity;
					needAdjustment = true;

				}
				else
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = (lineItemProgress.Quantity *
																												 activityComplete.ActivityPlanningChange
																													 .PercentageCompletion.Value) / 100;
				}
			}
			//Remaining Quantity changed
			if (activityComplete.ActivityPlanningChange.RemainingActivityQuantity.HasValue)
			{
				if (activityComplete.ActivityPlanningChange.RemainingActivityQuantity.Value == 0)
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = lineItemProgress.Quantity;
					needAdjustment = true;
				}
				else
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = lineItemProgress.Quantity -
																												activityComplete.ActivityPlanningChange
																													.RemainingActivityQuantity.Value;
				}
			}

			//Period Quantity changed
			if (activityComplete.ActivityPlanningChange.PeriodQuantityPerformance.HasValue)
			{
				activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = activityComplete.ActivityPlanningChange.PeriodQuantityPerformance;
				if (lineItemProgress.PeriodQuantityPerformance.HasValue)
				{//determain change of due date quantity performance
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -= lineItemProgress.PeriodQuantityPerformance.Value;
				}
				if (lineItemProgress.RemainingLineItemQuantity.HasValue)
				{//determain resulting due date quantity performance, still includinging quantity performance of former reports
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance += lineItemProgress.Quantity - lineItemProgress.RemainingLineItemQuantity.Value;
				}
			}

			//Due Date Quantity value changed
			if (activityComplete.ActivityPlanningChange.DueDateQuantityPerformance.HasValue)
			{
				if (lastReport != null)
				{//remove performance of former reports
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -= lastReport.QuantityTotal;
					if (needAdjustment)
					{
						var delta = lastReport.PlannedQuantity - activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -
										lastReport.QuantityTotal;
						activityComplete.ActivityPlanningChange.DueDateQuantityPerformance += delta;
					}
				}
				progressReportEntity.Quantity = activityComplete.ActivityPlanningChange.DueDateQuantityPerformance.Value;
			}

			//Quantity changed in progress report history container
			if (activityComplete.ActivityPlanningChange.ProgressReportQuantity.HasValue)
			{
				progressReportEntity.Quantity = activityComplete.ActivityPlanningChange.ProgressReportQuantity.Value;
				if (lastReport != null)
				{
					//remove performance of former reports
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = progressReportEntity.Quantity +
																												lastReport.QuantityTotal;
				}
				else
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = progressReportEntity.Quantity;
				}
			}
		}

		private void PrepareReportByLineItemWork(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, ActivityProgressReportEntity progressReportEntity)
		{
			if (activityComplete.ActivityPlanningChange == null)
			{
				return;
			}

			ActivityProgressReportEntity lastReport = null;
			if (progressReportEntity != null)
			{
				lastReport = progReports.Where(p => p.PerformanceDate < progressReportEntity.PerformanceDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}
			else
			{
				lastReport = progReports.Where(p => p.PerformanceDate < activityComplete.ActivityPlanningChange.DueDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}

			//measured performance value changed
			if (activityComplete.ActivityPlanningChange.PercentageCompletion.HasValue)
			{
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance = (activityComplete.LineItemProgress.Work * activityComplete.ActivityPlanningChange.PercentageCompletion.Value) / 100;
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance -= progressReportEntity.WorkTotal;
			}

			//Remaining Work changed
			if (activityComplete.ActivityPlanningChange.RemainingActivityWork.HasValue)
			{
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance = activityComplete.LineItemProgress.Work - activityComplete.ActivityPlanningChange.RemainingActivityWork.Value;
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance -= progressReportEntity.WorkTotal;
			}
			//Period Work changed
			if (activityComplete.ActivityPlanningChange.PeriodWorkPerformance.HasValue)
			{
				activityComplete.ActivityPlanningChange.DueDateWorkPerformance = activityComplete.ActivityPlanningChange.PeriodWorkPerformance;
				if (activityComplete.LineItemProgress.PeriodWorkPerformance.HasValue)
				{
					activityComplete.ActivityPlanningChange.DueDateWorkPerformance -= activityComplete.LineItemProgress.PeriodWorkPerformance;
				}
			}

			//Due Date Work value changed
			if (activityComplete.ActivityPlanningChange.DueDateWorkPerformance.HasValue)
			{
				if (lastReport != null)
				{
					progressReportEntity.WorkTotal = lastReport.WorkTotal +
																activityComplete.ActivityPlanningChange.DueDateWorkPerformance.Value;
					progressReportEntity.Work = activityComplete.ActivityPlanningChange.DueDateWorkPerformance;
				}
				else
				{
					progressReportEntity.Work = progressReportEntity.WorkTotal = activityComplete.ActivityPlanningChange.DueDateWorkPerformance.Value;
				}
			}
		}

		private ActivityProgressReportEntity AssertDueDateReport(ActivityComplete activityComplete, ScheduleDataPool pool)
		{
			ActivityProgressReportEntity progressReportEntity = null;
			if (activityComplete.Activity != null && activityComplete.ActivityPlanningChange != null)
			{
				var dueDate = activityComplete.ActivityPlanningChange.DueDate;

				if (activityComplete.ProgressReportsToSave != null && activityComplete.ProgressReportsToSave.Any())
				{
					progressReportEntity =
						activityComplete.ProgressReportsToSave.FirstOrDefault(e => e.ActivityFk == activityComplete.MainItemId
																										  && e.PerformanceDate.Value.Year == dueDate.Year &&
																										  e.PerformanceDate.Value.Month == dueDate.Month &&
																										  e.PerformanceDate.Value.Day == dueDate.Day);
				}

				if (progressReportEntity == null)
				{
					progressReportEntity = pool.GetReports(e => e.ActivityFk == activityComplete.MainItemId
																&& e.PerformanceDate.Value.Year == dueDate.Year && e.PerformanceDate.Value.Month == dueDate.Month && e.PerformanceDate.Value.Day == dueDate.Day).FirstOrDefault();

					if (progressReportEntity == null)
					{
						ActivityEntity activity = activityComplete.Activity ?? pool.Activities.FirstOrDefault(a => a.Id == activityComplete.MainItemId);
						ScheduleEntity schedule = pool.Schedules.FirstOrDefault(s => s.Id == activity.ScheduleFk);

						// check pco > 100
						var progress100 = pool.GetReports(e => e.ActivityFk == activityComplete.MainItemId && e.PCo >= 100)
							.FirstOrDefault();

						if (progress100 == null || schedule != null && !schedule.IsFinishedWith100Percent)
						{
							if (activityComplete.ActivityPlanningChange == null)
							{
								progressReportEntity =
										new ActivityProgressReportLogic().Create(activityComplete.Activity.Id, activityComplete.Activity, pool);
							}
							else
							{
								progressReportEntity = new ActivityProgressReportLogic().Create(activityComplete.Activity.Id,
										activityComplete.ActivityPlanningChange.DueDate, activityComplete.ActivityPlanningChange.ProgressDescription,
										activityComplete.Activity, pool);
							}
						}
					}
				}

				if (progressReportEntity != null)
				{
					if (string.IsNullOrEmpty(progressReportEntity.Description) &&
						 !string.IsNullOrEmpty(activityComplete.ActivityPlanningChange.ProgressDescription))
					{
						progressReportEntity.Description = activityComplete.ActivityPlanningChange.ProgressDescription;
					}
					if (!progressReportEntity.PerformanceDate.HasValue)
					{
						progressReportEntity.PerformanceDate = activityComplete.ActivityPlanningChange.DueDate;
					}

					if (!progressReportEntity.Quantity.HasValue)
					{
						progressReportEntity.Quantity = (decimal)0.0;
					}

					if (activityComplete.ProgressReportsToSave == null)
					{
						activityComplete.ProgressReportsToSave = new List<ActivityProgressReportEntity>();
					}

					if (!activityComplete.ProgressReportsToSave.Any(e => e.Id == progressReportEntity.Id))
					{
						activityComplete.ProgressReportsToSave.Add(progressReportEntity);
					}
				}
			}
			return progressReportEntity;
		}

		private ActivityProgressReportEntity AssertDueDateReport(ActivityComplete activityComplete, LineItemProgressEntity lineItemProgress, ScheduleDataPool pool)
		{
			ActivityProgressReportEntity progressReportEntity = null;
			if (lineItemProgress == null)
			{
				var lineItemLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();
				_lineItems = lineItemLogic.ByActivity(activityComplete.MainItemId).ToList();
				if (activityComplete.ProgressReportsToSave == null)
				{
					activityComplete.ProgressReportsToSave = new List<ActivityProgressReportEntity>();
				}
				if (activityComplete.Activity != null && _lineItems.Any())
				{
					var lineItemProgLogic = new LineItemProgressLogic();
					//lineItemProgress = new LineItemProgressEntity();
					_estHeader = GetNeededEstimateHeader();
					IEstHeaderEntity header = null;
					foreach (var lineItem in _lineItems)
					{
						header = _estHeader.FirstOrDefault(h => h.Id == lineItem.EstHeaderFk);
						lineItemProgress = new LineItemProgressEntity();

						lineItemProgLogic.Initialize(lineItemProgress, activityComplete.Activity, lineItem, header);
						progressReportEntity = AssertDueDateReport(activityComplete, lineItemProgress, pool);
						if (!activityComplete.ProgressReportsToSave.Any(p => p.Id == progressReportEntity.Id))
						{
							activityComplete.ProgressReportsToSave.Add(progressReportEntity);
						}
					}
				}
			}
			else if (activityComplete.Activity != null && activityComplete.ActivityPlanningChange != null)
			{
				var dueDate = activityComplete.ActivityPlanningChange.DueDate;

				if (activityComplete.ProgressReportsToSave != null && activityComplete.ProgressReportsToSave.Any())
				{
					progressReportEntity = activityComplete.ProgressReportsToSave.FirstOrDefault(e => e.ActivityFk == activityComplete.MainItemId && e.EstLineItemFk == lineItemProgress.LineItemFk
					&&	lineItemProgress.EstimationHeaderFk == e.EstHeaderFk && e.PerformanceDate.HasValue && e.PerformanceDate.Value.Year == dueDate.Year
					&& e.PerformanceDate.Value.Month == dueDate.Month && e.PerformanceDate.Value.Day == dueDate.Day);
				}

				if (progressReportEntity == null)
				{
					var progressLogic = new ActivityProgressReportLogic();
					progressReportEntity = progressLogic.GetByFilter(e => e.ActivityFk == activityComplete.MainItemId && e.EstLineItemFk == lineItemProgress.LineItemFk
					&& lineItemProgress.EstimationHeaderFk == e.EstHeaderFk && e.PerformanceDate.HasValue && e.PerformanceDate.Value.Year == dueDate.Year
					&& e.PerformanceDate.Value.Month == dueDate.Month && e.PerformanceDate.Value.Day == dueDate.Day).FirstOrDefault();

					if (progressReportEntity == null)
					{
						// check pco > 100
						var progress100 = progressLogic.GetByFilter(e => e.ActivityFk == activityComplete.MainItemId && e.EstLineItemFk == lineItemProgress.LineItemFk
						&& lineItemProgress.EstimationHeaderFk == e.EstHeaderFk && e.PCo >= 100).FirstOrDefault();
						if (progress100 == null)
						{
							if (activityComplete.ProgressReportsToSave != null && activityComplete.ProgressReportsToSave.Any())
							{
		                        progressReportEntity = activityComplete.ProgressReportsToSave.FirstOrDefault(e => e.LineItems != null && e.ActivityFk == activityComplete.MainItemId && e.LineItems.FirstOrDefault(li => li.Id == lineItemProgress.LineItemFk) != null && e.LineItems.FirstOrDefault(li => li.Id == lineItemProgress.LineItemFk).EstLineItemFk == lineItemProgress.LineItemFk && e.PerformanceDate.HasValue && e.PerformanceDate.Value.Year == dueDate.Year && e.PerformanceDate.Value.Month == dueDate.Month && e.PerformanceDate.Value.Day == dueDate.Day);

		                        progressReportEntity ??= progressLogic.Create(activityComplete.Activity.Id, activityComplete.ActivityPlanningChange.DueDate, null, activityComplete.Activity);
							}
							else
							{
								progressReportEntity =
									progressLogic.Create(activityComplete.Activity.Id, activityComplete.ActivityPlanningChange.DueDate, null, activityComplete.Activity);
							}

							pool.ProgressReports = new List<ActivityProgressReportEntity>() { progressReportEntity };
							progressReportEntity.EstLineItemFk = lineItemProgress.LineItemFk;
							progressReportEntity.EstHeaderFk = lineItemProgress.EstimationHeaderFk;
							progressReportEntity.PlannedQuantity = lineItemProgress.Quantity.GetValueOrDefault();
							progressReportEntity.PlannedWork = lineItemProgress.Work.GetValueOrDefault();
							progressReportEntity.BasUomFk = lineItemProgress.UoMFk.GetValueOrDefault();
						}
					}
				}

				if (progressReportEntity != null)
				{
					if (string.IsNullOrEmpty(progressReportEntity.Description) &&
						 !string.IsNullOrEmpty(activityComplete.ActivityPlanningChange.ProgressDescription))
					{
						progressReportEntity.Description = activityComplete.ActivityPlanningChange.ProgressDescription;
					}
					if (!progressReportEntity.PerformanceDate.HasValue)
					{
						progressReportEntity.PerformanceDate = activityComplete.ActivityPlanningChange.DueDate;
					}

					if (!progressReportEntity.Quantity.HasValue)
					{
						progressReportEntity.Quantity = (decimal)0.0;
					}

					if (activityComplete.ProgressReportsToSave == null)
					{
						activityComplete.ProgressReportsToSave = new List<ActivityProgressReportEntity>();
					}
					if (!activityComplete.ProgressReportsToSave.Any(e => e.Id == progressReportEntity.Id))
					{
						activityComplete.ProgressReportsToSave.Add(progressReportEntity);
					}
				}
			}
			return progressReportEntity;
		}

		private void HandleMultipleLineItemProgressRequest(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, LineItemProgressEntity lineItemProgress, ActivityProgressReportEntity progressReportEntity, Action<IEnumerable<ActivityProgressReportEntity>, ActivityComplete, LineItemProgressEntity, ActivityProgressReportEntity> handler)
		{
			var lineItemLogic =
				Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();
			_lineItems = lineItemLogic.ByActivity(activityComplete.MainItemId).ToList();
			if (activityComplete.ProgressReportsToSave == null)
			{
				activityComplete.ProgressReportsToSave = new List<ActivityProgressReportEntity>();
			}
			if (activityComplete.Activity != null && _lineItems.Any())
			{
				var lineItemProgLogic = new LineItemProgressLogic();
				//lineItemProgress = new LineItemProgressEntity();
				_estHeader = GetNeededEstimateHeader();
				IEstHeaderEntity header = null;
				foreach (var lineItem in _lineItems)
				{
					lineItemProgress = new LineItemProgressEntity();
					header = _estHeader.FirstOrDefault(h => h.Id == lineItem.EstHeaderFk);

					lineItemProgLogic.Initialize(lineItemProgress, activityComplete.Activity, lineItem, header);
					var pp =
						progReports.FirstOrDefault(p => p.ActivityFk == activityComplete.Activity.Id && p.EstLineItemFk == lineItem.Id && p.EstHeaderFk == lineItem.EstHeaderFk);
					handler(progReports, activityComplete, lineItemProgress, pp);
				}
			}
		}

		private List<IEstHeaderEntity> GetNeededEstimateHeader()
		{
			var res = new List<IEstHeaderEntity>();
			var headerIds = _lineItems.Select(li => li.EstHeaderFk).Distinct().ToArray();
			var lineItemHeaderLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainHeaderLogic>();

			res = lineItemHeaderLogic.GetEstimateHeaders(headerIds).ToList();
			//foreach (var headerId in headerIds)
			//{
			//	res.Add(lineItemHeaderLogic.Get(headerId));
			//}

			return res;
		}

		private void CalculateReportTotals(IEnumerable<ActivityProgressReportEntity> reports, bool byLineItem, bool byModelObject)
		{
			if (byModelObject)
			{
				var totals = new Dictionary<int, Tuple<Decimal, Decimal>>();
				foreach (ActivityProgressReportEntity report in reports)
				{
					if (report.Activity2ModelObjectFk.HasValue)
					{
						var activity2ModelObjectFk = report.Activity2ModelObjectFk.Value;
						Tuple<decimal, decimal> values = null;

						decimal quantity = 0;
						decimal work = 0;

						if (totals.TryGetValue(activity2ModelObjectFk, out values))
						{
							quantity = values.Item1;
							work = values.Item2;
						}
						if (report.Quantity.HasValue)
						{
							quantity += report.Quantity.Value;
						}
						totals[activity2ModelObjectFk] = new Tuple<decimal, decimal>(quantity, work);
						report.QuantityTotal = quantity;
						report.WorkTotal = work;
					}
				}
			}
			else if (byLineItem)
			{
				//the pair in totals contains the summed qunatity and work for the line item idetified by the int key
				var totals = new Dictionary<int, Tuple<decimal, decimal>>();
				foreach (var report in reports)
				{
					if (report.EstLineItemFk != null)
					{
						var lineItemId = report.EstLineItemFk.Value;
						Tuple<decimal, decimal> values = null;

						decimal quantity = 0;
						decimal work = 0;

						if (totals.TryGetValue(lineItemId, out values))
						{
							quantity = values.Item1;
							work = values.Item2;
						}
						if (report.Quantity.HasValue)
						{
							quantity += report.Quantity.Value;
						}
						if (report.Work.HasValue)
						{
							work += report.Work.Value;
						}
						totals[lineItemId] = new Tuple<decimal, decimal>(quantity, work);
						report.QuantityTotal = quantity;
						report.WorkTotal = work;
					}
					else
					{
						throw new SchedulingBusinessLayerException
						{
							ErrorCode = (int)ExceptionErrorCodes.ResourceFatalError,
							ErrorDetail = NLS.ERR_NoLineItemAssigned,
							ErrorMessage = NLS.ERR_NoLineItemAssigned
						};
					}
				}
			}
			else
			{
				decimal quantity = 0;
				decimal work = 0;
				foreach (var report in reports)
				{
					if (report.Quantity.HasValue)
					{
						quantity += report.Quantity.Value;
					}
					if (report.Work.HasValue)
					{
						work += report.Work.Value;
					}
					report.QuantityTotal = quantity;
					report.WorkTotal = work;
				}
			}
		}

		/// <summary>Calls the right handling method of the passed dispactchable</summary>
		/// <param name="progReports">progress reports to be evaluated</param>
		/// <param name="activityComplete">container for changed data</param>
		/// <param name="progressReportEntity">progress report effected</param>
		/// <param name="pool"></param>
		private void Dispatch(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, ActivityProgressReportEntity progressReportEntity, ScheduleDataPool pool)
		{
			switch (activityComplete.Activity.ProgressReportMethodFk.Value)
			{
				case Constants.ProgressReportMethodNo: HandleNoProgReport(progReports, activityComplete); break;
				case Constants.ProgressReportMethodByActivityQuantity: HandleProgReportByQuantity(progReports, activityComplete, progressReportEntity); break;
				case Constants.ProgressReportMethodByActivityWork: HandleProgReportByWork(progReports, activityComplete, progressReportEntity); break;
				case Constants.ProgressReportMethodByLineItemQuantity:
					HandleProgReportByLineItemQuantity(progReports, activityComplete, activityComplete.LineItemProgress, progressReportEntity); break;
				case Constants.ProgressReportMethodByModelObject:
					HandleProgReportByModelObject(progReports, activityComplete, activityComplete.LineItemProgress, progressReportEntity, pool); break;
				case Constants.ProgressReportMethodByLineItemWork: HandleProgReportByLineItemWork(progReports, activityComplete, activityComplete.LineItemProgress, progressReportEntity); break;
				default: HandleNoProgReport(progReports, activityComplete); break;
			}
		}
		private ActivityProgressReportEntity AssertDueDateReportForMdlObjects(ActivityComplete activityComplete, LineItemProgressEntity lineItemProgress, ScheduleDataPool pool)
		{
			ActivityProgressReportEntity progressReportEntity = null;
			var mdlObject = activityComplete.ObjModelSimulationToSave.FirstOrDefault();
			var progressLogic = new ActivityProgressReportLogic();

			if (lineItemProgress == null)
			{
				var lineItemLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();
				_lineItems = lineItemLogic.ByActivity(activityComplete.MainItemId).ToList();
				if (activityComplete.ProgressReportsToSave == null)
				{
					activityComplete.ProgressReportsToSave = new List<ActivityProgressReportEntity>();
				}
				if (activityComplete.Activity != null && _lineItems.Any())
				{
					var lineItemProgLogic = new LineItemProgressLogic();
					//lineItemProgress = new LineItemProgressEntity();
					_estHeader = GetNeededEstimateHeader();
					IEstHeaderEntity header = null;
					foreach (var lineItem in _lineItems)
					{
						header = _estHeader.FirstOrDefault(h => h.Id == lineItem.EstHeaderFk);
						lineItemProgress = new LineItemProgressEntity();

						lineItemProgLogic.Initialize(lineItemProgress, activityComplete.Activity, lineItem, header);
						if (mdlObject.EstHeaderFk == lineItem.EstHeaderFk && mdlObject.EstLineItemFk == lineItem.Id)
						{
							activityComplete.LineItemProgress = lineItemProgress;
							progressReportEntity = AssertDueDateReportForMdlObjects(activityComplete, lineItemProgress, pool);
							if (!activityComplete.ProgressReportsToSave.Any(p => p.Id == progressReportEntity.Id))
							{
								activityComplete.ProgressReportsToSave.Add(progressReportEntity);
							}
						}
					}
				}
			}
			else if (activityComplete.Activity != null && activityComplete.ActivityPlanningChange != null
				&& lineItemProgress.EstimationHeaderFk == mdlObject.EstHeaderFk && lineItemProgress.LineItemFk == mdlObject.EstLineItemFk)
			{
				var dueDate = activityComplete.ActivityPlanningChange.DueDate;

				if (activityComplete.ProgressReportsToSave != null && activityComplete.ProgressReportsToSave.Any())
				{
					progressReportEntity = activityComplete.ProgressReportsToSave.FirstOrDefault(e => e.ActivityFk == activityComplete.MainItemId && e.EstLineItemFk == lineItemProgress.LineItemFk
								&& e.EstHeaderFk == lineItemProgress.EstimationHeaderFk && e.Activity2ModelObjectFk == mdlObject.Id
								&& e.PerformanceDate.HasValue && e.PerformanceDate.Value.Year == dueDate.Year && e.PerformanceDate.Value.Month == dueDate.Month && e.PerformanceDate.Value.Day == dueDate.Day);
				}

				if (progressReportEntity == null)
				{
					progressReportEntity = progressLogic.GetCoresAsListByFilter(e => e.ActivityFk == activityComplete.MainItemId && e.EstLineItemFk == lineItemProgress.LineItemFk
								&& e.EstHeaderFk == lineItemProgress.EstimationHeaderFk && e.Activity2ModelObjectFk == mdlObject.Id && e.PerformanceDate.HasValue
								&& e.PerformanceDate.Value.Year == dueDate.Year && e.PerformanceDate.Value.Month == dueDate.Month && e.PerformanceDate.Value.Day == dueDate.Day).FirstOrDefault();

					if (progressReportEntity == null)
					{
						// check pco > 100
						var progress100 = progressLogic.GetCoresAsListByFilter(e => e.ActivityFk == activityComplete.MainItemId && e.EstLineItemFk == lineItemProgress.LineItemFk
							&& e.EstHeaderFk == lineItemProgress.EstimationHeaderFk && e.Activity2ModelObjectFk == mdlObject.Id && e.PCo >= 100).FirstOrDefault();
						if (progress100 == null)
						{
							if (activityComplete.ProgressReportsToSave != null && activityComplete.ProgressReportsToSave.Any())
							{
								progressReportEntity = activityComplete.ProgressReportsToSave.FirstOrDefault(e => e.ActivityFk == activityComplete.MainItemId && e.EstLineItemFk == lineItemProgress.LineItemFk && e.EstHeaderFk == lineItemProgress.EstimationHeaderFk
								 && e.Activity2ModelObjectFk == mdlObject.Id && e.PerformanceDate.HasValue && e.PerformanceDate.Value.Year == dueDate.Year && e.PerformanceDate.Value.Month == dueDate.Month && e.PerformanceDate.Value.Day == dueDate.Day);

								progressReportEntity ??= progressLogic.Create(activityComplete.Activity.Id, activityComplete.ActivityPlanningChange.DueDate, null, activityComplete.Activity);
							}
							else
							{
								progressReportEntity =
									progressLogic.Create(activityComplete.Activity.Id, activityComplete.ActivityPlanningChange.DueDate, null, activityComplete.Activity);
							}

							pool.ProgressReports = new List<ActivityProgressReportEntity>() { progressReportEntity };
							progressReportEntity.EstLineItemFk = lineItemProgress.LineItemFk;
							progressReportEntity.EstHeaderFk = lineItemProgress.EstimationHeaderFk;
							progressReportEntity.PlannedQuantity = mdlObject.PlannedQuantity;
							progressReportEntity.BasUomFk = mdlObject.BasUomFk;
							progressReportEntity.Activity2ModelObjectFk = mdlObject.Id;
						}
					}
				}

				if (progressReportEntity != null)
				{
					if (string.IsNullOrEmpty(progressReportEntity.Description) &&
						 !string.IsNullOrEmpty(activityComplete.ActivityPlanningChange.ProgressDescription))
					{
						progressReportEntity.Description = activityComplete.ActivityPlanningChange.ProgressDescription;
					}
					if (!progressReportEntity.PerformanceDate.HasValue)
					{
						progressReportEntity.PerformanceDate = activityComplete.ActivityPlanningChange.DueDate;
					}

					if (!progressReportEntity.Quantity.HasValue)
					{
						progressReportEntity.Quantity = (decimal)0.0;
					}
					progressLogic.FillModelValues(new List<ActivityProgressReportEntity>() { progressReportEntity });

					if (activityComplete.ProgressReportsToSave == null)
					{
						activityComplete.ProgressReportsToSave = new List<ActivityProgressReportEntity>();
					}
					if (!activityComplete.ProgressReportsToSave.Any(e => e.Id == progressReportEntity.Id))
					{
						activityComplete.ProgressReportsToSave.Add(progressReportEntity);
					}
				}
			}
			return progressReportEntity;
		}
		private void PrepareReportByModelObject(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, LineItemProgressEntity lineItemProgress, ActivityProgressReportEntity progressReportEntity, Activity2ModelObjectEntity mdlObject)
		{
			if (activityComplete.ActivityPlanningChange == null)
			{
				return;
			}

			ActivityProgressReportEntity lastReport = null;
			if (progressReportEntity != null)
			{
				lastReport = progReports.Where(p => p.PerformanceDate < progressReportEntity.PerformanceDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}
			else
			{
				lastReport = progReports.Where(p => p.PerformanceDate < activityComplete.ActivityPlanningChange.DueDate).OrderBy(e => e.PerformanceDate).LastOrDefault();
			}

			bool needAdjustment = false;

			//remaining percentage changed
			if (activityComplete.ActivityPlanningChange.PercentageRemaining.HasValue)
			{
				activityComplete.ActivityPlanningChange.PercentageCompletion = 100 -
																									activityComplete.ActivityPlanningChange
																										.PercentageRemaining.Value;
			}

			//percentage completion value changed
			if (activityComplete.ActivityPlanningChange.PercentageCompletion.HasValue)
			{
				if (activityComplete.ActivityPlanningChange.PercentageCompletion.Value == 100)
				{

					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = mdlObject.PlannedQuantity;
					needAdjustment = true;

				}
				else
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = (mdlObject.PlannedQuantity *
																												 activityComplete.ActivityPlanningChange
																													 .PercentageCompletion.Value) / 100;
				}
			}
			//Remaining Quantity changed
			if (activityComplete.ActivityPlanningChange.RemainingActivityQuantity.HasValue)
			{
				if (activityComplete.ActivityPlanningChange.RemainingActivityQuantity.Value == 0)
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = mdlObject.PlannedQuantity;
					needAdjustment = true;
				}
				else
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = mdlObject.PlannedQuantity -
																												activityComplete.ActivityPlanningChange
																													.RemainingActivityQuantity.Value;
				}
			}

			//Period Quantity changed
			if (activityComplete.ActivityPlanningChange.PeriodQuantityPerformance.HasValue)
			{
				activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = activityComplete.ActivityPlanningChange.PeriodQuantityPerformance;
				//determain change of due date quantity performance
				//if (mdlObject.Quantity > 0)
				//{
				//	activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -= mdlObject.Quantity;
				//}
				////determain resulting due date quantity performance, still includinging quantity performance of former reports
				//if (mdlObject.RemainingQuantity > 0)
				//{
				//	activityComplete.ActivityPlanningChange.DueDateQuantityPerformance += mdlObject.PlannedQuantity - mdlObject.RemainingQuantity;
				//}

				if (mdlObject.PeriodQuantityPerformance.HasValue)
				{//determain change of due date quantity performance
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -= mdlObject.PeriodQuantityPerformance.Value;
				}
				if (mdlObject.RemainingQuantity > 0)
				{//determain resulting due date quantity performance, still includinging quantity performance of former reports
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance += mdlObject.PlannedQuantity - mdlObject.RemainingQuantity;
				}
			}

			//Due Date Quantity value changed
			if (activityComplete.ActivityPlanningChange.DueDateQuantityPerformance.HasValue)
			{
				if (lastReport != null)
				{//remove performance of former reports
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -= lastReport.QuantityTotal;
					if (needAdjustment)
					{
						var delta = lastReport.PlannedQuantity - activityComplete.ActivityPlanningChange.DueDateQuantityPerformance -
										lastReport.QuantityTotal;
						activityComplete.ActivityPlanningChange.DueDateQuantityPerformance += delta;
					}
				}
				progressReportEntity.Quantity = activityComplete.ActivityPlanningChange.DueDateQuantityPerformance.Value;
			}

			//Quantity changed in progress report history container
			if (activityComplete.ActivityPlanningChange.ProgressReportQuantity.HasValue)
			{
				progressReportEntity.Quantity = activityComplete.ActivityPlanningChange.ProgressReportQuantity.Value;
				if (lastReport != null)
				{
					//remove performance of former reports
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = progressReportEntity.Quantity +
																												lastReport.QuantityTotal;
				}
				else
				{
					activityComplete.ActivityPlanningChange.DueDateQuantityPerformance = progressReportEntity.Quantity;
				}
			}
		}
		/// <summary>Handle the case of progress report by quantity of assigned line items</summary>
		/// <returns></returns>
		private void HandleProgReportByModelObject(IEnumerable<ActivityProgressReportEntity> progReports,
			ActivityComplete activityComplete, LineItemProgressEntity lineItemProgress,
			ActivityProgressReportEntity progressReportEntity, ScheduleDataPool pool)
		{
			if (lineItemProgress == null)
			{
				HandleMultipleMdlObjectProgressRequest(progReports, activityComplete, lineItemProgress, progressReportEntity, pool,
					HandleProgReportByModelObject);
			}
			else
			{
				var mdlObject = activityComplete.ObjModelSimulationToSave.FirstOrDefault();
				var mdlObjProgReports = progReports.Where(e => e.EstLineItemFk == lineItemProgress.LineItemFk && e.EstHeaderFk == lineItemProgress.EstimationHeaderFk && e.Activity2ModelObjectFk == mdlObject.Id);
				if (mdlObjProgReports != null && mdlObjProgReports.Any())
				{
					PrepareReportByModelObject(mdlObjProgReports, activityComplete, lineItemProgress, progressReportEntity, mdlObject);

					bool saveAsChanged = false;
					decimal sumOfQuantity = (decimal)0.0;

					decimal quantity = mdlObject.PlannedQuantity;
					foreach (var prog in mdlObjProgReports)
					{
						if (!saveAsChanged)
						{
							saveAsChanged = CheckIfNeedToSave(prog, activityComplete);
						}
						sumOfQuantity += (prog.Quantity.HasValue) ? prog.Quantity.Value : (decimal)0.0;

						if (saveAsChanged && prog.EstLineItemFk.HasValue)
						{
							if (quantity != 0)
							{
								prog.RemainingQuantity = quantity - sumOfQuantity;
								prog.PCo = (sumOfQuantity / quantity) * (decimal)100.0;
								prog.RemainingPCo = ((quantity - sumOfQuantity) / quantity) * (decimal)100.0;
								if (prog.PCo.HasValue && (double)Math.Round(prog.PCo.Value, 2) >= 99.9)
								{
									prog.PCo = 100;
									prog.RemainingPCo = 0;
								}
								prog.QuantityTotal = quantity;
							}

							if (!activityComplete.ProgressReportsToSave.Contains(prog))
							{
								activityComplete.ProgressReportsToSave.Add(prog);
							}

						}
					}
					if (quantity > 0)
					{
						mdlObject.PCo = sumOfQuantity / quantity * 100;
					}
					mdlObject.Quantity = sumOfQuantity;
					mdlObject.DueDateQuantityPerformance = sumOfQuantity;
					mdlObject.RemainingQuantity = mdlObject.Quantity - sumOfQuantity;
					if (mdlObject.PCo.HasValue && (double)Math.Round(mdlObject.PCo.Value,2) >= 99.9)
					{
						mdlObject.PCo = 100;
						mdlObject.RemainingPCo = 0;
						if (mdlObject.ActualStart == null)
						{
							mdlObject.ActualStart = mdlObject.PlannedStart;
						}

						if (mdlObject.ActualFinish == null)
						{
							mdlObject.ActualFinish = mdlObject.PlannedFinish;
						}
						mdlObject.ExecutionFinished = true;
						if (pool != null) {
							NonWorkingDayCheck nonWorkingDays = pool.GetNonWorkingDays(activityComplete.Activity);
							mdlObject.ActualDuration = new CalendarUtilitiesLogic().GetDuration(mdlObject.ActualStart.Value, mdlObject.ActualFinish.Value, nonWorkingDays);
						}
					}
				}
				//update Activity values from last progress report
				if (activityComplete.ProgressReportsToSave.Any())
				{
					var lastProg =
				activityComplete.ProgressReportsToSave.Where(e => e.EstLineItemFk == mdlObject.EstLineItemFk && mdlObject.EstHeaderFk == e.EstHeaderFk
					&& e.Activity2ModelObjectFk == mdlObject.Id).OrderByDescending(e => e.PerformanceDate).FirstOrDefault();
					if (lastProg != null)
					{
						mdlObject.PCo = lastProg.PCo;
						mdlObject.DueDateQuantityPerformance = mdlObject.PlannedQuantity - lastProg.RemainingQuantity;
						mdlObject.RemainingQuantity = lastProg.RemainingQuantity ?? 0m;
					}

					var lineItemReports = progReports.Where(e => e.EstLineItemFk == lineItemProgress.LineItemFk && e.EstHeaderFk == lineItemProgress.EstimationHeaderFk);
					decimal sumOfLiQuantity = (decimal)0.0;

					foreach (var prog in lineItemReports)
					{
						sumOfLiQuantity += (prog.Quantity.HasValue) ? prog.Quantity.Value : (decimal)0.0;

					}
					if (lineItemProgress.Quantity > 0)
					{
						lineItemProgress.PCo = sumOfLiQuantity / lineItemProgress.Quantity * 100;
					}
					lineItemProgress.DueDateQuantityPerformance = sumOfLiQuantity;
					lineItemProgress.RemainingLineItemQuantity = lineItemProgress.Quantity - sumOfLiQuantity;
					activityComplete.LineItemProgress = lineItemProgress;
				}
				//update Activity values from last progress report
				if (activityComplete.Activity != null && activityComplete.ProgressReportsToSave.Any())
				{
					var lastProg =
						activityComplete.ProgressReportsToSave.Where(e => e.EstLineItemFk == lineItemProgress.LineItemFk)
							.OrderByDescending(e => e.PerformanceDate)
							.FirstOrDefault();
					var lineItemReports = progReports.GroupBy(e => e.Activity2ModelObjectFk).ToDictionary(d => d.Key, d => d.ToList());
					decimal sumOfQuantity = (decimal)0.0;
					decimal sumOfPlannedQuantity = (decimal)0.0;

					foreach (var reports in lineItemReports)
					{
						sumOfPlannedQuantity += reports.Value.FirstOrDefault().PlannedQuantity;

						foreach (var prog in reports.Value)
						{
							sumOfQuantity += (prog.Quantity.HasValue) ? prog.Quantity.Value : (decimal)0.0;
						}
					}
					if (sumOfPlannedQuantity > 0)
					{
						activityComplete.Activity.PercentageCompletion = sumOfQuantity / sumOfPlannedQuantity * 100;
					}
					activityComplete.Activity.RemainingActivityQuantity = sumOfPlannedQuantity - sumOfQuantity;
					activityComplete.Activity.DueDateQuantityPerformance = activityComplete.Activity.Quantity -
																								 activityComplete.Activity.RemainingActivityQuantity;
					if (lastProg != null)
					{
						activityComplete.Activity.LastProgressDate = lastProg.PerformanceDate;
					}
				}
			}
		}
		private void HandleMultipleMdlObjectProgressRequest(IEnumerable<ActivityProgressReportEntity> progReports, ActivityComplete activityComplete, LineItemProgressEntity lineItemProgress,
			ActivityProgressReportEntity progressReportEntity, ScheduleDataPool pool, Action<IEnumerable<ActivityProgressReportEntity>, ActivityComplete, LineItemProgressEntity, ActivityProgressReportEntity, ScheduleDataPool> handler)
		{
			var lineItemLogic =
				Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();
			_lineItems = lineItemLogic.ByActivity(activityComplete.MainItemId).ToList();
			if (activityComplete.ProgressReportsToSave == null)
			{
				activityComplete.ProgressReportsToSave = new List<ActivityProgressReportEntity>();
			}
			if (activityComplete.Activity != null && _lineItems.Any())
			{
				var lineItemProgLogic = new LineItemProgressLogic();
				//lineItemProgress = new LineItemProgressEntity();
				_estHeader = GetNeededEstimateHeader();
				IEstHeaderEntity header = null;
				var mdlObject = activityComplete.ObjModelSimulationToSave.FirstOrDefault();
				foreach (var lineItem in _lineItems)
				{
					if (lineItem.EstHeaderFk == mdlObject.EstHeaderFk && lineItem.EstLineItemFk == mdlObject.EstLineItemFk)
					{
						lineItemProgress = new LineItemProgressEntity();
						header = _estHeader.FirstOrDefault(h => h.Id == lineItem.EstHeaderFk);

						lineItemProgLogic.Initialize(lineItemProgress, activityComplete.Activity, lineItem, header);
						var pp =
							progReports.FirstOrDefault(p => p.ActivityFk == activityComplete.Activity.Id && p.EstLineItemFk == lineItem.Id && p.EstHeaderFk == lineItem.EstHeaderFk && p.Activity2ModelObjectFk == mdlObject.Id);
						handler(progReports, activityComplete, lineItemProgress, pp, pool);
					}
				}
			}
		}

	}
}
