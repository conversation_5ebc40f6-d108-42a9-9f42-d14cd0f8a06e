import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _mainView, _package, _procurementContractPage, _procurementPage, _projectPage, _schedulePage, _validate, _wizardCreateRequest } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const BILLING_DESCRIPTION = "Billing" + _common.generateRandomString(3);
const ACCOUNT_NO = _common.generateRandomString(3);
const ACCOUNT_NOA2 = _common.generateRandomString(3);
const ACCOUNT_NOB2 = _common.generateRandomString(3);
const DESC1 = _common.generateRandomString(3);
const DESC2 = _common.generateRandomString(3);
const DESCA1 = _common.generateRandomString(3);
const DESCB1 = _common.generateRandomString(3);
const OFFSET_ACCOUNT = _common.generateRandomString(4);
const CODE_RETENSION = _common.generateRandomString(4);
const USER_DEFINED_1 = _common.generateRandomString(4);

let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let CONTAINER_COLUMNS_CONTRACT
let CONTAINERS_CONTRACT
let CONTAINER_COLUMNS_BILLING_SCHEMA
let CONTAINER_COLUMNS_CONTRACT_BILLING_SCHEMA
let CONTAINERS_BILLING_SCHEMA
let CONTAINERS_ITEMS
let CONTAINER_COLUMNS_ITEMS

describe("PCM- 4.120 | Billing schema container in contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("pcm/con-4.120-billing-schema-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINER_COLUMNS_BILLING_SCHEMA = this.data.CONTAINER_COLUMNS.BILLING_SCHEMA
            CONTAINERS_BILLING_SCHEMA = this.data.CONTAINERS.BILLING_SCHEMA
            CONTAINER_COLUMNS_CONTRACT_BILLING_SCHEMA = this.data.CONTAINER_COLUMNS.CONTRACT_BILLING_SCHEMA
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }

            CONTAINERS_ITEMS = this.data.CONTAINERS.ITEMS
            CONTAINER_COLUMNS_ITEMS = this.data.CONTAINER_COLUMNS.ITEMS

        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        });
    })
    after(() => {
        cy.LOGOUT();
    });


    it('TC - Prerequisites - Add record in Billing Schema module', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.BILLING_SCHEMA);

        _common.openTab(app.TabBar.BILLING_SCHEMA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BILLING_SCHEMA, app.FooterTab.BILLINGSCHEMA, 0);
            _common.clear_subContainerFilter(cnt.uuid.BILLING_SCHEMA)
        });
        _common.maximizeContainer(cnt.uuid.BILLING_SCHEMA)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.BILLING_SCHEMA)
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, BILLING_DESCRIPTION)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.BILLING_SCHEMA, BILLING_DESCRIPTION)
        _common.select_rowInContainer(cnt.uuid.BILLING_SCHEMA)
        _common.minimizeContainer(cnt.uuid.BILLING_SCHEMA)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.BILLING_SCHEMA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BILLING_SCHEMA_DETAILS, app.FooterTab.BILLINGSCHEMADETAILS, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.BILLING_SCHEMA_DETAILS)
        _common.select_allContainerData(cnt.uuid.BILLING_SCHEMA_DETAILS);
        _common.waitForLoaderToDisappear()
        _common.clickOn_expandCollapseButton(cnt.uuid.BILLING_SCHEMA_DETAILS,app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.COLLAPSE_ALL)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)//required wait to click on cell
        _common.clickOn_cellHasUniqueValue(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_INFO, CONTAINERS_BILLING_SCHEMA.CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_expandCollapseButton(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_SELECTED)  
        _common.clickOn_cellHasUniqueValue(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_INFO, CONTAINERS_BILLING_SCHEMA.CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_INFO, CONTAINERS_BILLING_SCHEMA.CONSTRUCTION)
        _common.openTab(app.TabBar.BILLING_SCHEMA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BILLING_SCHEMA_DETAILS, app.FooterTab.BILLINGSCHEMADETAILS, 1);
            _common.setup_gridLayout(cnt.uuid.BILLING_SCHEMA_DETAILS, CONTAINER_COLUMNS_BILLING_SCHEMA)
        });
        _common.waitForLoaderToDisappear()
        _common.create_newRecordInSubContainer_ifNoRecordExists(cnt.uuid.BILLING_SCHEMA_DETAILS, 1);
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.ACCOUNT_NO, app.InputFields.DOMAIN_TYPE_CODE, ACCOUNT_NO)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, DESC1)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_2_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, DESC2)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.BILLING_LINE_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINERS_BILLING_SCHEMA.BILLING_TYPE)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.FINAL_TOTAL, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.GENERALS_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BILLING_SCHEMA.GENERALS_TYPE)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.HAS_CONTROLLING_UNIT, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.OFFSET_ACCOUNT_NO, app.InputFields.DOMAIN_TYPE_CODE, OFFSET_ACCOUNT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.TAX_CODE_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BILLING_SCHEMA.TAX_CODE)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.CRED_LINE_TYPE_FK, commonLocators.CommonKeys.LIST, CONTAINERS_BILLING_SCHEMA.CRED_LINE_TYPE)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.CODE_RETENSION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CODE_RETENSION)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.PAYMENT_TERM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_BILLING_SCHEMA.PAYMENT_TERM)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.USER_DEFINED_1, app.InputFields.DOMAIN_TYPE_DESCRIPTION, USER_DEFINED_1)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.BILLING_SCHEMA_DETAILS)
    })

    it('TC - Create record in procurement configuration', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROCUREMENT_CONFIGURATION);
        _common.openTab(app.TabBar.HEADER).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONFIGURATION_HEADER, app.FooterTab.CONFIGURATION_HEADER, 0);
            _common.clear_subContainerFilter(cnt.uuid.CONFIGURATION_HEADER)
        });
        _common.select_rowHasValue(cnt.uuid.CONFIGURATION_HEADER, CommonLocators.CommonKeys.MATERIAL)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.HEADER).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BILLINGSCHEMATA, app.FooterTab.BILLING_SCHEMATA);
        });
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.BILLINGSCHEMATA)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.BILLINGSCHEMATA, app.GridCells.BILLING_SCHEMA_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, BILLING_DESCRIPTION)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.set_containerLayoutUnderEditView(commonLocators.CommonLabels.LAYOUT_6)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.billingschemafk, CONTAINER_COLUMNS_CONTRACT.code, CONTAINER_COLUMNS_CONTRACT.businesspartnerfk], cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK)
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.BILLING_SCHEMA_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, BILLING_DESCRIPTION)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONSTATUS_FK)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT)
    });

    it("TC - Assert billing schema record in contract module", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.FooterTab.BILLING_SCHEMA, 1);
        });
        cy.log(Cypress.env("CONTRACT_CODE"))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA, ACCOUNT_NO)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Create record in items container", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 1);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_ITEMS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_ITEMS.basuomfk, CONTAINER_COLUMNS_ITEMS.price, CONTAINER_COLUMNS_ITEMS.quantity], cnt.uuid.ITEMSCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        //required wait to enter quantity as page updates before typing the value
        cy.wait(1000).then(() => {
            _common.edit_containerCell(cnt.uuid.ITEMSCONTRACT, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEMS.QUANTITY1)
            _common.waitForLoaderToDisappear()
            _common.edit_containerCell(cnt.uuid.ITEMSCONTRACT, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEMS.PRICE1)
            _common.waitForLoaderToDisappear()
            _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT, app.GridCells.BAS_UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEMS.UOM)
            _common.waitForLoaderToDisappear()
            _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
            _common.waitForLoaderToDisappear()
            _common.edit_containerCell(cnt.uuid.ITEMSCONTRACT, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEMS.QUANTITY2)
            _common.waitForLoaderToDisappear()
            _common.edit_containerCell(cnt.uuid.ITEMSCONTRACT, app.GridCells.PRICE_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEMS.PRICE2)
            _common.waitForLoaderToDisappear()
            _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT, app.GridCells.BAS_UOM_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEMS.UOM)
        })
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_TOTALS, app.FooterTab.TOTAL, 2);
        });
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_TOTALS)
        _common.select_rowHasValue(cnt.uuid.CONTRACT_TOTALS, CONTAINERS_CONTRACT.T10)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.CONTRACT_TOTALS, app.GridCells.VALUE_NET)
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_TOTALS, app.GridCells.VALUE_NET, "NET_VALUE")
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_TOTALS, app.GridCells.VALUE_NET_OC, "NET_VALUE_OC")
    })

    it("TC - Assert record in contract billing schema result", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.FooterTab.BILLING_SCHEMA, 1);
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT_BILLING_SCHEMA.coderetention, CONTAINER_COLUMNS_CONTRACT_BILLING_SCHEMA.paymenttermfk,CONTAINER_COLUMNS_CONTRACT_BILLING_SCHEMA.resultoc, CONTAINER_COLUMNS_CONTRACT_BILLING_SCHEMA.result,CONTAINER_COLUMNS_CONTRACT_BILLING_SCHEMA.userdefined1], cnt.uuid.CONTRACT_BILLING_SCHEMA)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.maximizeContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.select_rowInContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.GridCells.RESULT, Cypress.env("NET_VALUE"))
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.GridCells.RESULT_OC, Cypress.env("NET_VALUE_OC"))
        _common.waitForLoaderToDisappear()
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.GridCells.FINAL_TOTAL, CommonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA,ACCOUNT_NO)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA,DESC1)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA,DESC2)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA,OFFSET_ACCOUNT)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA,CODE_RETENSION)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA,USER_DEFINED_1)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA,CONTAINERS_BILLING_SCHEMA.BILLING_TYPE)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA,CONTAINERS_BILLING_SCHEMA.GENERALS_TYPE)
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
    })

    it("TC - Change currency in contract and verify recalculated result(oc)", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.bascurrencyfk,CONTAINER_COLUMNS_CONTRACT.exchangerate], cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('CONTRACT_CODE'))
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.BAS_CURRENCY_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.INR)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalButtonByClass(CommonLocators.CommonElements.RADIO_SPACE_TO_UP)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.YES)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.EXCHANGE_RATE)
        _common.waitForLoaderToDisappear()
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE)
        cy.wait(1000)//required wait to change exchange rate
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.FooterTab.BILLING_SCHEMA, 1);
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        });
        _common.waitForLoaderToDisappear() 
    })

    it("TC - Assert result in billing schema after changing currency", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.FooterTab.BILLING_SCHEMA);
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        });
        _common.maximizeContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.waitForLoaderToDisappear()
        _common.assert_forNumericValues(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.GridCells.RESULT_OC, CONTAINERS_BILLING_SCHEMA.VALUE[1])
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.waitForLoaderToDisappear()
    })

    it('TC - Create second record in billing schema module', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.BILLING_SCHEMA);
        _common.openTab(app.TabBar.BILLING_SCHEMA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BILLING_SCHEMA, app.FooterTab.BILLINGSCHEMA, 0);
            _common.clear_subContainerFilter(cnt.uuid.BILLING_SCHEMA)
        });
        _common.waitForLoaderToDisappear()
        _common.search_inSubContainer(cnt.uuid.BILLING_SCHEMA, BILLING_DESCRIPTION)
        _common.select_rowInContainer(cnt.uuid.BILLING_SCHEMA)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.BILLING_SCHEMA).then(() => {
            _common.select_tabFromFooter(cnt.uuid.BILLING_SCHEMA_DETAILS, app.FooterTab.BILLINGSCHEMADETAILS, 1);
        });
        _common.maximizeContainer(cnt.uuid.BILLING_SCHEMA_DETAILS)
        _common.select_allContainerData(cnt.uuid.BILLING_SCHEMA_DETAILS);
        _common.waitForLoaderToDisappear()
        _common.clickOn_expandCollapseButton(cnt.uuid.BILLING_SCHEMA_DETAILS,app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.COLLAPSE_ALL)
        _common.waitForLoaderToDisappear()
        cy.wait(1000)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_INFO, CONTAINERS_BILLING_SCHEMA.CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_expandCollapseButton(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCellIcons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_SELECTED)  
        _common.clickOn_cellHasUniqueValue(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_INFO, CONTAINERS_BILLING_SCHEMA.CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_INFO, CONTAINERS_BILLING_SCHEMA.CONSTRUCTION)
        _common.create_newRecord(cnt.uuid.BILLING_SCHEMA_DETAILS, 1);
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.ACCOUNT_NO, app.InputFields.DOMAIN_TYPE_CODE, ACCOUNT_NOA2)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, DESCA1)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.BILLING_SCHEMA_DETAILS, 1);
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.ACCOUNT_NO, app.InputFields.DOMAIN_TYPE_CODE, ACCOUNT_NOB2)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, DESCB1)
        _common.waitForLoaderToDisappear()
        _common.set_cellCheckboxValue(cnt.uuid.BILLING_SCHEMA_DETAILS, app.GridCells.FINAL_TOTAL, commonLocators.CommonKeys.CHECK)
        _common.waitForLoaderToDisappear()
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.BILLING_SCHEMA_DETAILS)
    })

    it("TC - Assert result in billing schema after recalculation", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.FooterTab.BILLING_SCHEMA, 1);
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        });
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.clickOn_toolbarButton(cnt.uuid.CONTRACT_BILLING_SCHEMA, btn.ToolBar.ICO_RECALCULATE)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA, DESCA1)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.CONTRACT_BILLING_SCHEMA, DESCB1)
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA, DESCB1)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.GridCells.RESULT, "RESULTB")
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.CONTRACT_BILLING_SCHEMA, DESC1)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.GridCells.RESULT, "RESULTA")
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.GridCells.RESULT_OC, "RESULT_OC-B")
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.CONTRACT_BILLING_SCHEMA, DESC1)
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.GridCells.RESULT_OC, "RESULT_OC-A")
        _common.minimizeContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
    })

    it("TC - Verify billing schema final is sum of headers billing schema final", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_BILLING_SCHEMA, app.FooterTab.BILLING_SCHEMA, 1);
            _common.clear_subContainerFilter(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        });
        _common.waitForLoaderToDisappear()
        _common.clickOn_toolbarButton(cnt.uuid.CONTRACT_BILLING_SCHEMA, btn.ToolBar.ICO_RECALCULATE)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA, DESCA1)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordPresent(cnt.uuid.CONTRACT_BILLING_SCHEMA, DESCB1)
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONTRACT_BILLING_SCHEMA)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.BillingSchemaFinal, CONTAINER_COLUMNS_CONTRACT.BillingSchemaFinalOC], cnt.uuid.PROCUREMENTCONTRACT)
            _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env('CONTRACT_CODE'))
        _common.waitForLoaderToDisappear()
        _common.select_rowInContainer(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env('CONTRACT_CODE'))
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("RESULTA"), Cypress.env("RESULTB"), app.GridCells.BILLING_SCHEMA_FINAL)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordAdditionOfTwoValuesInRow(cnt.uuid.PROCUREMENTCONTRACT, Cypress.env("RESULT_OC-A"), Cypress.env("RESULT_OC-B"), app.GridCells.BILLING_SCHEMA_FINAL_OC)
        _common.waitForLoaderToDisappear()
    })
});