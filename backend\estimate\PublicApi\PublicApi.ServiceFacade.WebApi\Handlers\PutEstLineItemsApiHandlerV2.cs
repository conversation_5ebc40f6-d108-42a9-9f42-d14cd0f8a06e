using RIB.Visual.Basics.AssetMaster.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.PublicApi;
using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.CostCodes.BusinessComponents;
using RIB.Visual.Basics.CostGroups.BusinessComponents;
using RIB.Visual.Basics.MasterData.BusinessComponents;
using RIB.Visual.Basics.Material.BusinessComponents;
using RIB.Visual.Basics.ProcurementStructure.BusinessComponents;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Estimate.Common.BusinessComponents;
using RIB.Visual.Estimate.Assemblies.BusinessComponents;
using RIB.Visual.Estimate.Main.BusinessComponents;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using RIB.Visual.Platform.AppServer.Runtime;
using RIB.Visual.Platform.Common;
using RIB.Visual.Platform.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using EstMainBizComp = RIB.Visual.Estimate.Main.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using EstRuleBizComp = RIB.Visual.Estimate.Rule.BusinessComponents;
using RIB.Visual.Estimate.Parameter.BusinessComponents;
using EstAssemblyBizComp = RIB.Visual.Estimate.Assemblies.BusinessComponents;
using System.Globalization;
using RIB.Visual.Basics.MaterialCatalog.BusinessComponents;
using RIB.Visual.Basics.Core.Common.Enum;

namespace RIB.Visual.Estimate.PublicApi.ServiceFacade.WebApi
{
	/// <summary>
	/// 
	/// </summary>
	public class PutEstLineItemsApiHandlerV2 : PutEstLineItemsApiHandlerBase
	{
		private List<EstLineItemApiDtoV2> duplicatedCodeItems = new List<EstLineItemApiDtoV2>();
		private List<EstLineItemApiDtoV2> referenceLineItemCodeNotFoundItems = new List<EstLineItemApiDtoV2>();
		private List<EstLineItemApiDtoV2> requredColumnValidateErrorItems = new List<EstLineItemApiDtoV2>();
		private List<EstLineItemApiDtoV2> optionalColumnValidateErrorItems = new List<EstLineItemApiDtoV2>();

		private CostGroupMigrateHelper<EstLineItemApiDtoV2> _costGroupHelper = new CostGroupMigrateHelper<EstLineItemApiDtoV2>("EST_LINE_ITEM2COSTGRP",
		new CodeDefinition<EstLineItemApiDtoV2>()
		{
		},
		null,
		new CostGroupDefinition<EstLineItemApiDtoV2>()
		{
			EnterpriseGetter = e => e.LicCostGroups,
			ProjectGetter = e => e.PrjCostGroups
		});

		/// <summary>
		/// 
		/// </summary>
		public PutEstLineItemsApiHandlerV2()
		{
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="request"></param>
		/// <param name="logger"></param>
		public void PutLineItems(EstimatePublicApiV2_0LineItemRequest request, IPublicApiExecutionLogger logger)
		{
			#region Do request validation
			if (request.EstimateLineItems == null || !request.EstimateLineItems.Any())
			{
				logger.WriteError(string.Format("No estimate line item data found to put into the database."));
				return;
			}

			_companyId = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext.SignedInClientId;
			if (_companyId < 0)
			{
				logger.WriteError(string.Format("Please check yuor login company, No company found in the database."));
				return;
			}

			var projectEntity = _projectLogic.GetProjectByNumber(request.ProjectNo, request.ProjectIndex, _companyId);
			if (projectEntity == null)
			{
				logger.WriteError(string.Format("No project found in the database using project No and project index."));
				return;
			}
			_projectId = projectEntity.Id;

			_estHeaders = new EstimateMainHeaderLogic().GetLookup(_projectId.Value).ToList();
			if (_estHeaders != null && _estHeaders.Count > 0)
			{
				if (request.EstHeaderCode != null && !string.IsNullOrEmpty(request.EstHeaderCode.Trim()))
				{
					_estHeader = _estHeaders.FirstOrDefault(e => e.Code == request.EstHeaderCode.Trim());
					if (_estHeader == null)
					{
						logger.WriteError(string.Format("No estimate header found in the database using estHeaderCode( {0} ).", request.EstHeaderCode.Trim()));
						return;
					}

					_estHeaderJobId = _estHeader.LgmJobFk.HasValue ? _estHeader.LgmJobFk.Value : -1;
					if (_estHeaderJobId == -1)
					{
						var prjDefaultJobId = _lgmJobLogic.GetDefaultLogisticJobIdByProject(_projectId.Value);
						if (prjDefaultJobId.HasValue)
						{
							_estHeaderJobId = prjDefaultJobId.Value;
						}
						else
						{
							logger.WriteError(string.Format("Estimate header(Code : {0}) have a wrong Job.", request.EstHeaderCode.Trim()));
							return;
						}

					}
				}
			}
			else
			{
				logger.WriteInfo(string.Format("No estimate header found in the database using project No and project index."));
				return;
			}

			#endregion

			#region Do line items data validation           
			List<EstMainBizComp.EstLineItemEntity> newLineItemEntities = new List<EstMainBizComp.EstLineItemEntity>();
			List<EstLineItemApiDtoV2> tempLineItemDtos = request.EstimateLineItems.ToList();
			List<EstResourceApiDto> tempResourecDtos = new List<EstResourceApiDto>();
			foreach (EstLineItemApiDtoV2 lineItemDto in tempLineItemDtos)
			{
				//Check the lineitem code is existed or not.
				if (lineItemDto.Code == null)
				{
					var newEntity = _estLineItemLogic.Create();
					var generatedCode = new EstLineItemCodeGenerator(_estHeader.Id).GenerateCode(newEntity);
					if (tempLineItemDtos.FirstOrDefault(e => e.Code != null && e.Code.Equals(generatedCode)) != null)
					{
						generatedCode += "_generated";
					}
					lineItemDto.Code = generatedCode;
					newEntity.Code = generatedCode;
					newLineItemEntities.Add(newEntity);
				}

				if (lineItemDto.EstResources != null && lineItemDto.EstResources.Any())
				{
					//to keep the relation from the lineItem dto
					foreach (EstResourceApiDto resourceItem in lineItemDto.EstResources)
					{
						resourceItem.EstLineItemCode = lineItemDto.Code;
						resourceItem.EstHeaderCode = _estHeader.Code;

						//TODO, resource's assembly and assemblyHeader must be the same with its lineitem?
						//resourceItem.EstAssemblyCode = lineItemDto.EstAssemblyCode;
						//resourceItem.Header4AssemblyCode = lineItemDto.EstAssemblyHeaderCode;
					}
					tempResourecDtos.AddRange(lineItemDto.EstResources);
				}
			}

			//(1) get lookup data.
			GetLookupDataV2(request.EstimateLineItems, projectEntity.Id);

			//(2) validation
			bool hasCodeUniqueValidationError = DoCodeUniqueValidation(tempLineItemDtos, duplicatedCodeItems, duplicatedResourceCodeItems, conflictResourceCodeItems, logger);

			//validate code and parentCode
			bool hasParentCodeValidationError = DoReferenceLineItemCodeValidation(tempLineItemDtos, logger);

			//validation foreign key
			bool hasOptionalForeignKeyAndResoureValidationError = DoOptionalForeignKeyAndResoureValidation(tempLineItemDtos, logger, request.CanOverWrite);

			//validation Master Data Filter(Material Catalog)
			bool hasMaterialCatalogFilterValidationError = DoMaterialCatalogFilterValidation(_projectId.Value, _mdcMaterials,_prjMaterials, _mdcMaterialGroups, logger);

			//WorkLevel
			// 0. omit, default value, when found required column validation error, remove the item, omit it, and continue
			// 1. log warning, log it, and continue
			// 2. abort
			if (request.WorkLevel > 1 &&
				 (!hasCodeUniqueValidationError ||
				 !hasParentCodeValidationError ||
				 !hasOptionalForeignKeyAndResoureValidationError)) { return; }

			if (!hasMaterialCatalogFilterValidationError) { return; }

			if (request.WorkLevel == 1)
			{
				removeValidateFailedItems(tempLineItemDtos, duplicatedCodeItems);
				removeValidateFailedItems(tempLineItemDtos, referenceLineItemCodeNotFoundItems);
				removeValidateFailedItems(tempLineItemDtos, requredColumnValidateErrorItems);
				//resource
				removeValidateFailedItems(tempResourecDtos, duplicatedResourceCodeItems);
				removeValidateFailedItems(tempResourecDtos, requredColumnValidateErrorItems4Resource);
			}
			else if (request.WorkLevel == 0)
			{
				removeValidateFailedItems(tempLineItemDtos, duplicatedCodeItems);
				removeValidateFailedItems(tempLineItemDtos, referenceLineItemCodeNotFoundItems);
				//resource
				removeValidateFailedItems(tempResourecDtos, duplicatedResourceCodeItems);
				//If resource code does not existed in database should it continue to set into lineitem or give a default value?
				removeValidateFailedItems(tempResourecDtos, optionalColumnValidateErrorItems4Resource);
			}
			#endregion

			#region Import Line items
			//(3). reOreder the post data, make sure the save action have no problem

			//keyGetter
			Func<IEstLineItemApiDto, EstLineItemApiDtoV2, bool> parentLIGetter = (e, k) =>
			{
				return e.Code == k.LineItemReferenceCode;
			};

			List<EstLineItemApiDtoV2> estLineItemApiDtos = reQueueDtos<EstLineItemApiDtoV2>(tempLineItemDtos, parentLIGetter);
			List<EstResourceApiDto> estResourceApiDtos = reQueueDtos(tempResourecDtos);

			//4. handle parent code and mapping 
			List<EstMainBizComp.EstLineItemEntity> createEstLineItemEntities = new List<EstMainBizComp.EstLineItemEntity>();
			List<EstMainBizComp.EstLineItemEntity> updateEstLineItemEntities = new List<EstMainBizComp.EstLineItemEntity>();

			//use to collect whether the lineitem is need to clearResource
			var lineItemId2ClearResource = new Dictionary<int, bool>();

			//
			var estResourceCopyLogic = new EstResourceCopyLogic(projectEntity.Id);
			var userDefinedcolValEntitys = new UserDefinedColumnValueLogic().GetListByKeys((int)userDefinedColumnTableIds.EstimateLineItem, _estHeader.Id, null, null).ToList();
			foreach (var dto in estLineItemApiDtos)
			{
				EstMainBizComp.EstLineItemEntity createdItem = null;
				EstMainBizComp.EstLineItemEntity updatedItem = _existedLineItems.Where(e => e.Code == dto.Code && e.EstHeaderFk == _estHeader.Id).FirstOrDefault();
				

				//create
				if (updatedItem == null)
				{
					createdItem = newLineItemEntities.FirstOrDefault(e => e.Code == dto.Code) != null ? newLineItemEntities.FirstOrDefault(e => e.Code == dto.Code) : _estLineItemLogic.Create();
					createdItem.LineItemType = (int)CommonLogic.LineItemTypes.LineItem;
					
					if (!string.IsNullOrEmpty(dto.LineItemReferenceCode))
					{
						var parentItemExistedInDb = _existedLineItems.Where(e => e.Code == dto.LineItemReferenceCode).FirstOrDefault();
						if (parentItemExistedInDb == null)
						{
							var parentItemCreated = createEstLineItemEntities.Where(e => e.Code == dto.LineItemReferenceCode).FirstOrDefault();
							//here should be valiation before, so will not appear
							if (parentItemCreated == null)
							{
								logger.WriteError(string.Format("The EstLineItem(Code: {0}).{1} ({2})  does not have the reference item code in the put data and database.", dto.Code, "ParentCode", dto.LineItemReferenceCode));
							}
							else
							{
								createdItem.EstLineItemFk = parentItemCreated.Id;
							}
						}
						else
						{
							createdItem.EstLineItemFk = parentItemExistedInDb.Id;
						}
						dto.EstResources = null;
					}
					List<int> UserDefinedcolValEntityIds = new List<int>();
					if (dto.EstResources != null && dto.EstResources.Any() && dto.EstResources.Where(s => s.UserDefineds!=null).Any())
					{
						 UserDefinedcolValEntityIds = estResourceCopyLogic.SequenceManager.GetNextList("BAS_USER_DEFINEDCOL_VAL", estResourceApiDtos.Where(e => e.EstLineItemCode.Equals(dto.Code)).Count()).ToList();
					}
					MapLineItemDto2Entity(dto, ref createdItem, true, projectEntity.Id, estResourceApiDtos, logger, UserDefinedcolValEntityIds);
					createdItem.UserDefinedcolValEntity = new UserDefinedcolValEntity { TableId = (int)userDefinedColumnTableIds.EstimateLineItem, Pk1 = _estHeader.Id, Pk2 = createdItem.Id };
					createEstLineItemEntities.Add(createdItem);
				}
				//update
				else if (request.CanOverWrite && updatedItem != null)
				{
					if (!string.IsNullOrEmpty(dto.LineItemReferenceCode))
					{
						var parentItemExistedInDb = _existedLineItems.Where(e => e.Code == dto.LineItemReferenceCode).FirstOrDefault();
						if (parentItemExistedInDb == null)
						{
							var parentItemCreated = createEstLineItemEntities.Where(e => e.Code == dto.LineItemReferenceCode).FirstOrDefault();
							//here should be valiation before, so will not appear
							if (parentItemCreated == null)
							{
								logger.WriteError(string.Format("The EstLineItem(Code: {0}).{1} ({2})  does not have the reference item code in the put data and database.", dto.Code, "ParentCode", dto.LineItemReferenceCode));
							}
							else
							{
								updatedItem.EstLineItemFk = parentItemCreated.Id;
							}
						}
						else
						{
							updatedItem.EstLineItemFk = parentItemExistedInDb.Id;
						}
						dto.EstResources = null;
					}
					List<int> UserDefinedcolValEntityIds = new List<int>();
					if (dto.EstResources.Any() && dto.EstResources.Where(s => s.UserDefineds !=null).Any())
					{
						UserDefinedcolValEntityIds = estResourceCopyLogic.SequenceManager.GetNextList("BAS_USER_DEFINEDCOL_VAL", estResourceApiDtos.Where(e => e.EstLineItemCode.Equals(dto.Code)).Count()).ToList();
						var updatedItemUserDefinedcolValEntity = userDefinedcolValEntitys.Where(s => s.Pk2 == updatedItem.Id);
						updatedItem.UserDefinedcolValEntity = updatedItemUserDefinedcolValEntity.Any() ? updatedItemUserDefinedcolValEntity.First(): new UserDefinedcolValEntity { TableId = (int)userDefinedColumnTableIds.EstimateLineItem, Pk1 = _estHeader.Id, Pk2 = updatedItem.Id };
					}
					updatedItem.PopulateOtherLanguage(new Func<EstMainBizComp.EstLineItemEntity, DescriptionTranslateType>[] { e => e.DescriptionInfo });
					MapLineItemDto2Entity(dto, ref updatedItem, request.CanOverWrite, projectEntity.Id, estResourceApiDtos, logger, UserDefinedcolValEntityIds);
					
					updateEstLineItemEntities.Add(updatedItem);

					lineItemId2ClearResource.TryAdd(updatedItem.Id, dto.ClearResource);
				}
			}
			#endregion

			#region Recalculat import line items and resources
			//Calculator
			//Caluate Resources and EstLineitems but do no save.
			//var calculatorHelp = new EstLineItemUpdateHelper(_estHeader.Id, projectEntity.Id).SaveAfterUpdate(true);
			List<EstResourceEntity> estLineItemResources = new List<EstResourceEntity>() { };

			estLineItemResources = createEstResourceEntities.Union(updateEstResourceEntities).ToList();
			_estResourceLogic.FillWithExchangeRatesByProject(estLineItemResources.Concat(_liResources), projectEntity);

			var calculationOption = new EstimateCoreCalculationOption(_estHeader.Id, projectEntity.Id);

			//CheckResourceIsRate(createEstLineItemEntities.Any() ? createEstLineItemEntities : updateEstLineItemEntities, _estHeader.Id, (int)_projectId, _estHeaderJobId, estLineItemResources);

			var estLineItemUpdateFrmPrjLogic = new EstLineItemUpdateFrmPrjLogic(_estHeader.Id, projectEntity.Id, new EstResourceUpdateOption()
			{
				ConsiderIsRate = true,
				IsUpdateCostType = false,
				IsUpdateOriginalValue = false
			});

			if (createEstLineItemEntities.Count > 0)
			{
				var UserDefinedcolValEntityIds = estResourceCopyLogic.SequenceManager.GetNextList("BAS_USER_DEFINEDCOL_VAL", createEstLineItemEntities.Count());
				var index = 0;
				//calculatorHelp.CalculateLineItems(createEstLineItemEntities);
				foreach (var lineitem in createEstLineItemEntities)
				{
					var resources = estLineItemResources.Where(e => e.EstLineItemFk == lineitem.Id).Any() ? estLineItemResources.Where(e => e.EstLineItemFk == lineitem.Id).ToList() : new List<EstResourceEntity>() { };
					resources.AddRange(_liResources.Where(res => res.EstLineItemFk == lineitem.Id).ToList());

					var lineItemDto = estLineItemApiDtos.FirstOrDefault(e => e.Code.Equals(lineitem.Code));

					estLineItemUpdateFrmPrjLogic.UpdateRateFromProjectInList(resources, lineitem.LgmJobFk ?? _estHeaderJobId);
					RecalculateLineitem(lineItemDto, lineitem, resources, calculationOption);
					lineitem.UserDefinedcolValEntity.Id = UserDefinedcolValEntityIds[index];
					index++;
				}
			}
			if (updateEstLineItemEntities.Count > 0)
			{
				//calculatorHelp.CalculateLineItems(updateEstLineItemEntities);
				foreach (var lineitem in updateEstLineItemEntities)
				{
					var resources = estLineItemResources.Where(e => e.EstLineItemFk == lineitem.Id).Any() ? estLineItemResources.Where(e => e.EstLineItemFk == lineitem.Id).ToList() : new List<EstResourceEntity>() { };

					var existResourcesOfLineItem = _liResources.Where(res => res.EstLineItemFk == lineitem.Id).ToList();

					//check if lineitem is clearResource is true, need to exclude the resource which same code
					if (lineItemId2ClearResource.ContainsKey(lineitem.Id) && lineItemId2ClearResource[lineitem.Id])
					{
						var excludeCode = resources.Select(e => e.Code).ToList();
						resources.AddRange(existResourcesOfLineItem.Where(res => !excludeCode.Contains(res.Code)));
					}
					else
					{
						resources.AddRange(existResourcesOfLineItem);
					}

					var lineItemDto = estLineItemApiDtos.FirstOrDefault(e => e.Code.Equals(lineitem.Code));
					estLineItemUpdateFrmPrjLogic.UpdateRateFromProjectInList(resources, lineitem.LgmJobFk ?? _estHeaderJobId);
					RecalculateLineitem(lineItemDto, lineitem, resources, calculationOption);
				}
			}
			#endregion
			
			#region save the collected data here
			//(4) save
			if (createEstLineItemEntities.Count > 0)
			{
				_estLineItemLogic.Save(createEstLineItemEntities);

				var LgmJobFk = (createEstResourceEntities?.FirstOrDefault()?.LgmJobFk) ?? null;
				SaveProjectAssemblys(createEstLineItemEntities.First(), createEstResourceEntities, createEstResourceEntities, LgmJobFk);

				logger.WriteInfo(string.Format("{0} estLineItem are created.", createEstLineItemEntities.Count));
			}

			if (updateEstLineItemEntities.Count > 0)
			{
				_estLineItemLogic.Save(updateEstLineItemEntities);
				SaveProjectAssemblys(updateEstLineItemEntities.First(), createEstResourceEntities, createEstResourceEntities, createEstResourceEntities.First().LgmJobFk);

				logger.WriteInfo(string.Format("{0} estLineItem are updated.", updateEstLineItemEntities.Count));
			}

			if (createEstResourceEntities.Count > 0)
			{
				_estResourceLogic.Save(createEstResourceEntities.OrderBy(e => e.EstResourceFk));

				logger.WriteInfo(string.Format("{0} estResource are created.", createEstResourceEntities.Count));
			}
			if (updateEstResourceEntities.Count > 0)
			{
				_estResourceLogic.Save(updateEstResourceEntities.OrderBy(e => e.EstResourceFk));
				logger.WriteInfo(string.Format("{0} estResource are updated.", updateEstResourceEntities.Count));
			}

			_entityCostGroupHelper.SaveChanges();

			//EstLineItem2EstRuleEntity
			//refresh estimate project rule cache 
			if (lineItem2RuleForCreated.Count > 0 || lineItem2RuleForUpdated.Count > 0)
			{
				_prjEstRules = _estRulePrjEstRuleLogic.GetListByFilter(e => e.ProjectFk == projectEntity.Id).ToList();
			}
			if (lineItem2RuleForCreated.Count > 0)
			{
				_estRuleLineItemLogic.Save(lineItem2RuleForCreated, _prjEstRules);
				logger.WriteInfo(string.Format("{0} EstLineItem2EstRule are created.", lineItem2RuleForCreated.Count));
			}
			if (lineItem2RuleForUpdated.Count > 0)
			{
				_estRuleLineItemLogic.Save(lineItem2RuleForUpdated, _prjEstRules);
				logger.WriteInfo(string.Format("{0} EstLineItem2EstRule are updated.", lineItem2RuleForUpdated.Count));
			}
			//PrjEstRuleParamEntity
			if (createdPrjEstRuleParams.Count > 0)
			{
				new EstimateRulePrjEstRuleParamLogic().Save(createdPrjEstRuleParams);
				logger.WriteInfo(string.Format("{0} prjEstRuleParameter are created.", createdPrjEstRuleParams.Count));
			}
			if (updatedPrjEstRuleParams.Count > 0)
			{
				new EstimateRulePrjEstRuleParamLogic().Save(updatedPrjEstRuleParams);
				logger.WriteInfo(string.Format("{0} prjEstRuleParameter are updated.", updatedPrjEstRuleParams.Count));
			}
			//EstLineItemParams
			if (createdEstLineItemParams.Count > 0)
			{
				new EstimateParameterLineItemLogic().Save(createdEstLineItemParams);
				logger.WriteInfo(string.Format("{0} estLineItemParameter are created.", createdEstLineItemParams.Count));
			}
			if (updatedEstLineItemParams.Count > 0)
			{
				new EstimateParameterLineItemLogic().Save(updatedEstLineItemParams);
				logger.WriteInfo(string.Format("{0} estLineItemParameter are updated.", updatedEstLineItemParams.Count));
			}
			//EstLineItem2MdlObject
			if (createEstLineItem2MdlObject.Count > 0)
			{
				_estLineItem2MdlObejctLogic.Save(createEstLineItem2MdlObject, true);
				logger.WriteInfo(string.Format("{0} estLineItemObject are created.", createEstLineItem2MdlObject.Count));
			}

			if (updatedEstLineItem2MdlObject.Count > 0)
			{
				_estLineItem2MdlObejctLogic.Save(updatedEstLineItem2MdlObject, true);
				logger.WriteInfo(string.Format("{0} estLineItemObject are updated.", updatedEstLineItem2MdlObject.Count));
			}
			#endregion
			//get databas resource data  caculate udp
			var createdEstLineItemudps = createEstLineItemEntities.Where(s => s.UserDefinedcolValEntity != null).ToList();
			var updatedEstLineItemudps = updateEstLineItemEntities.Where(s => s.UserDefinedcolValEntity != null).ToList();
			var estLineItemEntitiesUpd = createdEstLineItemudps;
			estLineItemEntitiesUpd.AddRange(updatedEstLineItemudps);
			var resourceEntityList = new EstMainBizComp.EstimateMainResourceLogic().GetResourcesByLineItemIdsCore(estLineItemEntitiesUpd.Select(s => s.Id), _estHeader.Id, new EstResourceSearchOption()
			{
				IncludeExchangeRates = true,
				ProjectId = projectEntity.Id
			}).ToList();
			var userDefinedcolValsOfResources = new UserDefinedColumnValueLogic().GetList((int)userDefinedColumnTableIds.EstimateResource, _estHeader.Id, null, null).ToList();
			if (estLineItemEntitiesUpd.Any() && resourceEntityList.Any())
			{
				foreach (var lineItem in estLineItemEntitiesUpd)
				{
					foreach (var resourceEntity in resourceEntityList)
					{
						if(lineItem.Resources.Where(s=>s.Id == resourceEntity.Id).Any())
						{
							continue;
						}
						var userDefined = userDefinedcolValsOfResources.Find(s => s.Pk1 == _estHeader.Id && s.Pk2 == lineItem.Id && s.Pk3 == resourceEntity.Id);
						if(userDefined != null)
						{
							resourceEntity.UserDefinedcolValEntity = userDefined;
							lineItem.Resources.Add(resourceEntity);
						}

					}
				}
				var estLineItemUpdateHelper = new EstLineItemUpdateHelper(_estHeader.Id, _projectId, new EstLineItemUpdateOption()
				{
					ToSave = true,
					ConsiderIsRate = true,
					IsUpdateAssembly = false,
					IsUpdateSplitQuantity = false
				});
				createEstResourceEntities.AddRange(GetResourceUserDefined(createEstLineItemEntities.Any() ? createEstLineItemEntities : updateEstLineItemEntities, _estHeader.Id, (int)_projectId));
				estLineItemUpdateHelper.CalculateLineItems(estLineItemEntitiesUpd);
			}
			else
			{
				logger.WriteInfo("please check udp data");
			}
			
		}
		/// <summary>
		/// save project assemblycat and project assembly data
		/// </summary>
		/// <param name="currentEstLineItem"></param>
		/// <param name="estResourceEntities"></param>
		/// <param name="resourcesOfLineItems"></param>
		/// <param name="jobId"></param>
		public void SaveProjectAssemblys(EstMainBizComp.EstLineItemEntity currentEstLineItem, List<EstResourceEntity> estResourceEntities, List<EstResourceEntity> resourcesOfLineItems,int? jobId)
		{
			var projectAssembliesToSave = new EstProjectRelationEntityCollector().CollectProjectAssemblyToSave(currentEstLineItem, estResourceEntities, resourcesOfLineItems, jobId, e => e.EstAssemblyFk, e => e.EstHeaderAssemblyFk);

			//Assembly Header Id
			var assemblyHeaderId = new EstimateAssembliesLogic().GetEstHeaderByCurLineItemContext();
			//Assembly Ids
			var assemblyIds = projectAssembliesToSave.Select(e => e.Item1).ToList();
			var projAssemblyDatas = new EstProjectAssemblyLogic(estResourceEntities).CreateProjectAssemblyAndCopyCompleteInfo(assemblyIds, (int)_projectId, assemblyHeaderId, true, jobId, new List<Tuple<int, int>>(), new List<Tuple<int, int, IScriptEstResource>>(), new List<int>());

			/* collect project plant assembly*/
			var	projectPlantAssembliesToSave = new EstProjectRelationEntityCollector().CollectProjectPlantAssemblyToSave(currentEstLineItem, estResourceEntities, resourcesOfLineItems, jobId, e => e.EstAssemblyFk, e => e.EstHeaderAssemblyFk);
			if (projectPlantAssembliesToSave != null && projectPlantAssembliesToSave.Any())
			{
				new EstProjectPlantAssemblyHelper().SaveEntities(projectPlantAssembliesToSave, (int)_projectId);
			}
		}
		/// <summary>
		/// check resource code type isRate
		/// </summary>
		/// <param name="lineItemEntitys"></param>
		/// <param name="headerId"></param>
		/// <param name="projectId"></param>
		/// <param name="jobId"></param>
		/// <param name="estLineItemResources"></param>
		public void CheckResourceIsRate(IEnumerable<EstMainBizComp.EstLineItemEntity> lineItemEntitys, int headerId, int projectId, int jobId, List<EstResourceEntity> estLineItemResources)
		{
			var estProjectCostCodeHelper = new EstProjectCostCodeHelper(projectId);
			var estResourceBasList = new EstMainBizComp.EstimateMainResourceLogic().GetResourcesByLineItemIdsCore(lineItemEntitys.Select(s => s.Id), headerId, new EstResourceSearchOption()
			{
				IncludeExchangeRates = true,
				ProjectId = projectId
			}).ToList();
			estLineItemResources.AddRange(estResourceBasList);
			foreach (EstResourceEntity ResourceEntitie in createEstResourceEntities)
			{
				var isFixRate = estProjectCostCodeHelper.IsFixedRate(ResourceEntitie, projectId, jobId, (waring) => { });
				if (!isFixRate)
				{
					var filterResource = estResourceBasList.Find(s => s.Code == ResourceEntitie.Code || s.Id == ResourceEntitie.Id);
					if (filterResource != null)
					{
						ResourceEntitie.CostUnit = filterResource.CostUnit;
						ResourceEntitie.DayWorkRateUnit = filterResource.DayWorkRateUnit;
						ResourceEntitie.DayWorkRateTotal = filterResource.DayWorkRateTotal;
					}
				}
			}
		}
		/// <summary>
		/// Get Resources UserDefined data
		/// </summary>
		/// <param name="lineItemEntitys"></param>
		/// <param name="headerId"></param>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public List<EstResourceEntity> GetResourceUserDefined(IEnumerable<EstMainBizComp.EstLineItemEntity> lineItemEntitys, int headerId, int projectId)
		{
			var estResourceEntityList = new EstMainBizComp.EstimateMainResourceLogic().GetResourcesByLineItemIdsCore(lineItemEntitys.Select(s => s.Id), headerId, new EstResourceSearchOption()
			{
				IncludeExchangeRates = true,
				ProjectId = projectId
			}).ToList();
			var userDefinedcolValsOfResources = new UserDefinedColumnValueLogic().GetListByKeys((int)userDefinedColumnTableIds.EstimateResource, headerId, lineItemEntitys.Select(e => e.Id).OfType<int?>().ToList(), null).ToList();
			foreach (var resource in estResourceEntityList)
			{
				var userDefined = userDefinedcolValsOfResources.Find(s => s.Pk3 == resource.Id);
				resource.UserDefinedcolValEntity = userDefined;
			}
			return estResourceEntityList;
		}
		private bool DoReferenceLineItemCodeValidation(IEnumerable<EstLineItemApiDtoV2> dtos, IPublicApiExecutionLogger logger)
		{
			bool isValid = true;
			int parentIndex = 0;
			foreach (EstLineItemApiDtoV2 dto in dtos)
			{
				if (string.IsNullOrEmpty(dto.LineItemReferenceCode))
				{
					if (!dtos.Where(e => e.Code == dto.LineItemReferenceCode).Any() && _existedLineItems.Where(e => e.Code == dto.LineItemReferenceCode).Any())
					{
						logger.WriteError(string.Format("The LineItem[{0}](Code: {1}).{2} ({3})  does not have the reference item code in the put data and database.", parentIndex, dto.Code, "ParentCode", dto.LineItemReferenceCode));

						isValid = false;
						referenceLineItemCodeNotFoundItems.Add(dto);
					}
				}
				parentIndex++;
			}
			return isValid;
		}

		private bool DoOptionalForeignKeyAndResoureValidation(IEnumerable<EstLineItemApiDtoV2> dtos, IPublicApiExecutionLogger logger, bool canOverwrite)
		{
			bool hasNoValidationError = true;
			bool isValid = true;
			int parentIndex = 0;

			//_costGroupHelper.Load(dtos);
			foreach (var dto in dtos)
			{
				//EstAssemblyFk
				EstAssemblyBizComp.EstAssemblyCatEntity assemblyCatalog = null;
				if (!string.IsNullOrEmpty(dto.EstAssemblyCode))
				{
					//EstAssemblyCatalogCode
					if (!string.IsNullOrEmpty(dto.EstAssemblyCatalogCode))
					{
						var assemblyCatalogs = _assemblyCatalogs.Where(e => e.Code == dto.EstAssemblyCatalogCode).ToList();
						foreach (var item in assemblyCatalogs)
						{
							var firstLevelAssemblyCat = item.EstAssemblyCatFk.HasValue ? _estAssembliesStructureLogic.GetFirstLevelAssemblyCatByKey(item.EstAssemblyCatFk.Value) : item;
							var firstLevelCode = firstLevelAssemblyCat != null ? firstLevelAssemblyCat.Code : item.Code;

							if (firstLevelCode == dto.AssemblyCatFirstLevelCode)
							{
								assemblyCatalog = item;
								break;
							}
						}
						var assemblyCatalogId = assemblyCatalog != null ? assemblyCatalog.Id : -1;
						isValid &= LogErrorForInvalidForeignKey(logger, _assemblyCatalogs.Where(e => e.Id == assemblyCatalogId), e => e.Code, parentIndex, dto.Code, "EstAssemblyCatalogCode", dto.EstAssemblyCatalogCode, "does not exist in database or filtered by Master Data Filter");
						//assembly = assemblyCatalog != null ? _estAssemblies.FirstOrDefault(e => e.Code == dto.EstAssemblyCode && e.EstAssemblyCatFk == assemblyCatalog.Id) : null;
					}

					//EstAssemblyFirstLevelCatalogCode
					if (!string.IsNullOrEmpty(dto.AssemblyCatFirstLevelCode))
					{
						isValid &= LogErrorForInvalidForeignKey(logger, _assemblyCatalogs.Where(e => e.EstAssemblyCatFk == null), e => e.Code, parentIndex, dto.Code, "AssemblyCatFirstLevelCode", dto.AssemblyCatFirstLevelCode, "does not exist in database or filtered by Master Data Filter");
					}

					if (string.IsNullOrEmpty(dto.AssemblyCatFirstLevelCode) || string.IsNullOrEmpty(dto.EstAssemblyCatalogCode))
					{
						logger.WriteWarning(string.Format("The LineItems[{0}](Code: {1}).{2} : {3} does not have EstAssemblyCatalogCode or AssemblyCatFirstLevelCode, cannot get the assembly accurately and the first assembly with this code in database will be used.", parentIndex, dto.Code, "EstAssemblyCode", dto.EstAssemblyCode));
					}

					isValid &= LogErrorForInvalidForeignKey(logger, _estAssemblies, e => e.Code, parentIndex, dto.Code, "EstAssemblyCode", dto.EstAssemblyCode);
				}
				//UomFk
				if (!string.IsNullOrEmpty(dto.Uom))
				{
					isValid &= LogErrorForInvalidForeignKey(logger, _uoms, e => e.UnitInfo.Description.ToUpper().Trim(), parentIndex, dto.Code, "UomCode", dto.Uom.ToUpper().Trim());
				}
				//UomTargetFk
				if (!string.IsNullOrEmpty(dto.UomItem))
				{
					isValid &= LogErrorForInvalidForeignKey(logger, _uoms, e => e.UnitInfo.Description.ToUpper().Trim(), parentIndex, dto.Code, "UomTargetCode", dto.UomItem.ToUpper().Trim());
				}

				#region validate lineitem leading structure forergn keys

				LogWarningForInvalidForeignKey(logger, _estActivities, e => e.Code, parentIndex, dto.Code, "ActivityCode", dto.ActivityCode);
				LogWarningForInvalidForeignKey(logger, _estBoqItems, e => e.Reference, parentIndex, dto.Code, "BoqReference", dto.BoqReferenceNo);
				LogWarningForInvalidForeignKey(logger, _estControllingUnits, e => e.Code, parentIndex, dto.Code, "ControllingUnitCode", dto.ControllingUnitCode);

				LogWarningForInvalidForeignKey(logger, _prcPackages, e => e.Code, parentIndex, dto.Code, "PrcPackageCode", dto.PrcPackageCode);
				#endregion

				#region Validate assembly optional foreign keys (if not found, report warning)
				//MdcAssetMasterFk
				LogWarningForInvalidForeignKey(logger, _mdcAssetMasters, e => e.Code, parentIndex, dto.Code, "MdcAssetMasterCode", dto.MdcAssetMasterCode);
				//PrcStructureFk
				LogWarningForInvalidForeignKey(logger, _prcStructures, e => e.Code, parentIndex, dto.Code, "PrcStructureCode", dto.PrcStructureCode);
				//PrjChangedFk
				LogWarningForInvalidForeignKey(logger, _prjChanges, e => e.Code, parentIndex, dto.Code, "PrjChangeCode", dto.PrjChangeCode);

				#endregion

				//check datetime
				if (dto.FromDate != null && dto.ToDate != null && DateTime.Compare(dto.FromDate.Value, dto.ToDate.Value) > 0)
				{
					logger.WriteWarning(string.Format("The assembly (Code: {0}) start date '{1}' should not larger than the end date '{2}'.", dto.Code, dto.FromDate, dto.ToDate));
				}

				//validate assembly EstResources foreign keys (abort if has error).
				isValid &= DoResourceValidation(dto.EstResources, optionalColumnValidateErrorItems4Resource, logger, parentIndex, dto.Code);

				//validate EstLineitemModel and Model-Object
				dto.LineItemModelList = DoModelValidation(dto.LineItemModelList, logger, parentIndex, dto.Code);

				//validate prjRules and mdcRules foreign keys
				isValid &= DoRuleValidation(dto.LineItemRules, logger, parentIndex, dto.Code);
				parentIndex++;
				if (!isValid)
				{
					isValid = true;
					hasNoValidationError = false;
					optionalColumnValidateErrorItems.Add(dto);
					continue;
				}
			}

			return hasNoValidationError;
		}
		/// <summary>
		/// Validates whether materials are excluded by master data filter rules (Material Catalog type) and not covered by the project material list.
		/// </summary>
		/// <param name="projectId">The ID of the current project (used to associate with project materials).</param>
		/// <param name="materialEntities">The list of material entities to be validated (checks if they are filtered).</param>
		/// <param name="projectMaterialEntities">The list of material entities in the current project (determines if materials are already included).</param>
		/// <param name="materialGroupEntities">The list of material group entities (associates materials with material catalogs).</param>
		/// <param name="logger">The logger for recording validation errors.</param>
		/// <returns>True if no validation errors; False if validation errors exist.</returns>
		private static bool DoMaterialCatalogFilterValidation(int projectId, List<MaterialEntity> materialEntities, IEnumerable<IProjectMaterialEntity> projectMaterialEntities, IEnumerable<MaterialGroupEntity> materialGroupEntities, IPublicApiExecutionLogger logger)
		{
			bool hasNoValidationError = true;

			if (materialEntities == null || materialGroupEntities == null || !materialEntities.Any() || !materialGroupEntities.Any())
			{
				return hasNoValidationError;
			}

			var availableMaterialCatalogIds = Injector.Get<IRateBookLogic>().GetFilteredIdsByProjectAndType(projectId, (int)MasterDataFilterType.MaterialCatalog);

			if(availableMaterialCatalogIds == null || !availableMaterialCatalogIds.Any())
			{
				return hasNoValidationError;
			}

			var projectMaterialIds = projectMaterialEntities.Select(e => e.MdcMaterialFk).ToHashSet();

			foreach (var item in materialEntities)
			{
				var materialCatalogId = materialGroupEntities.Where(s=> s.Id == item.MaterialGroupFk).Select(e=>e.MaterialCatalogFk).FirstOrDefault();
				if (!availableMaterialCatalogIds.Contains(materialCatalogId) &&
					 !projectMaterialIds.Contains(item.Id))
				{
					logger.WriteError(
						 $"Material Code: {item.Code} has been filtered by 'Master Data Filter': Material Catalog!");

					hasNoValidationError = false;  
				}
			}

			return hasNoValidationError;
		}
		private void MapLineItemDto2Entity(EstLineItemApiDtoV2 dto, ref EstMainBizComp.EstLineItemEntity entity, bool canOverwrite, int projectId, List<EstResourceApiDto> estResourceApiDtos, IPublicApiExecutionLogger logger,List<int> Ids =null)
		{

			//(1) Map Assembly          
			#region Set lineitem mandatory foreign keys
			//EstHeaderFk
			entity.EstHeaderFk = _estHeader.Id;

			//EstLineItemCode
			entity.Code = dto.Code;

			//EstResources
			List<EstResourceApiDto> resourceDtos = null;
			if (dto.EstResources != null && dto.EstResources.Any())
			{
				resourceDtos = estResourceApiDtos.Where(e => e.EstLineItemCode.Equals(dto.Code)).ToList();
			}

			//EstAssemblyFk
			if (!string.IsNullOrEmpty(dto.EstAssemblyCode))
			{
				EstAssemblyBizComp.EstLineItemEntity assembly = null;
				EstAssemblyBizComp.EstAssemblyCatEntity assemblyCatalog = null;

				//EstAssemblyCatalogFk
				if (!string.IsNullOrEmpty(dto.EstAssemblyCatalogCode) && !string.IsNullOrEmpty(dto.AssemblyCatFirstLevelCode))
				{
					var assemblyCatalogs = _assemblyCatalogs.Where(e => e.Code == dto.EstAssemblyCatalogCode).ToList();
					foreach (var item in assemblyCatalogs)
					{
						var firstLevelAssemblyCat = item.EstAssemblyCatFk.HasValue ? _estAssembliesStructureLogic.GetFirstLevelAssemblyCatByKey(item.EstAssemblyCatFk.Value) : item;
						var firstLevelCode = firstLevelAssemblyCat != null ? firstLevelAssemblyCat.Code : item.Code;

						if (firstLevelCode == dto.AssemblyCatFirstLevelCode)
						{
							assemblyCatalog = item;
							break;
						}
					}

					assembly = assemblyCatalog != null ? _estAssemblies.FirstOrDefault(e => e.Code == dto.EstAssemblyCode && e.EstAssemblyCatFk == assemblyCatalog.Id) : null;
				}
				else
				{
					assembly = _estAssemblies.FirstOrDefault(e => e.Code == dto.EstAssemblyCode);
				}

				if (assembly != null && canOverwrite)
				{
					//entity.EstAssemblyFk = assembly.Id;
					//entity.EstAssemblyCatFk = assemblyCatalog != null ? (int?)assemblyCatalog.Id : null;
					if (resourceDtos == null || !resourceDtos.Any())
					{
						var postData = new EstLineItemResolveAssembliesData()
						{
							LineItemCreationData = new EstLineItemCreationData()
							{
								SelectedItems = new List<RIB.Visual.Estimate.Main.BusinessComponents.EstLineItemEntity>() { entity },
								ProjectId = projectId,
								EstHeaderFk = _estHeader.Id
							},
							AssemblyIds = new List<int>() { assembly.Id },
							DragDropAssemlySourceType = 3
						};

						entity = _estLineItemLogic.ResolveAssembliesToLineItem(postData).FirstOrDefault();
						//_estLineItemLogic.ResolveAssembliesToLineItem(postData);
					}
					else
					{
						EstimateContext.ScriptEstLineItemLogic.CopyPropertyFromAssembly(entity, assembly);
					}
				}
			}

			//EstAssembly2HeaderFk, TODO
			dto.CopyTo(entity);

			//UomFk
			if (!string.IsNullOrEmpty(dto.Uom))
			{
				var uom = _uoms.FirstOrDefault(e => e.UnitInfo.Description.ToUpper().Trim() == dto.Uom.ToUpper().Trim());
				if (uom != null) { entity.BasUomFk = uom.Id; }
			}


			//UomTargetFk
			if (!string.IsNullOrEmpty(dto.UomItem))
			{
				var uomTarget = _uoms.FirstOrDefault(e => e.UnitInfo.Description.ToUpper().Trim() == dto.UomItem.ToUpper().Trim());
				if (uomTarget != null) { entity.BasUomTargetFk = uomTarget.Id; }
			}


			#endregion

			#region Set line item leading structure foreign keys

			//ActivityFk
			if (!string.IsNullOrEmpty(dto.ActivityCode))
			{
				var item = _estActivities.FirstOrDefault(e => e.Code == dto.ActivityCode);
				if (item != null)
				{
					entity.ActivityFk = item.Id;
				}
			}

			//LocationFk
			if (!string.IsNullOrEmpty(dto.PrjLocationCode))
			{
				var item = _estLocations.FirstOrDefault(e => e.Code.Equals(dto.PrjLocationCode));
				if (item != null)
				{
					entity.PrjLocationFk = int.Parse(item.Id.ToString());
				}
			}

			//BoqItemFk
			if (!string.IsNullOrEmpty(dto.BoqReferenceNo))
			{
				var itemHeader = _estBoqItemHeaders.FirstOrDefault(e => e.Reference == dto.BoqReferenceHeaderNo);
				if (itemHeader != null)
				{
					var item = _estBoqItems.FirstOrDefault(e => e.Reference == dto.BoqReferenceNo && e.BoqHeaderFk == itemHeader.BoqHeaderFk);
					if (item != null)
					{
						entity.BoqItemFk = item.Id;
						entity.BoqHeaderFk = item.BoqHeaderFk;
					}
				}
			}

			//MdcControllingUnitFk
			if (!string.IsNullOrEmpty(dto.ControllingUnitCode))
			{
				var item = _estControllingUnits.FirstOrDefault(e => e.Code == dto.ControllingUnitCode);
				if (item != null)
				{
					entity.MdcControllingUnitFk = item.Id;
				}
			}

			_entityCostGroupHelper.AttachCostGroup(entity, dto.LicCostGroups, false);

			_entityCostGroupHelper.AttachCostGroup(entity, dto.PrjCostGroups, true);

			//PrcStructureFk
			if (!string.IsNullOrEmpty(dto.PrcStructureCode))
			{
				var item = _prcStructures.FirstOrDefault(e => e.Code == dto.PrcStructureCode);
				if (item != null)
				{
					entity.PrcStructureFk = item.Id;
				}
			}


			#endregion

			#region Set line item optional foreign keys
			//PrjChangeFk
			if (!string.IsNullOrEmpty(dto.PrjChangeCode))
			{
				var item = _prjChanges.FirstOrDefault(e => e.Code == dto.PrjChangeCode && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.PrjChangeFk = item.Id;
				}
			}

			//PrcPackageFk
			if (!string.IsNullOrEmpty(dto.PrcPackageCode))
			{
				var item = _prcPackages.FirstOrDefault(e => e.Code == dto.PrcPackageCode && e.ProjectFk == projectId);
				if (item != null)
				{
					//entity.PrcPackageFk = item.Id;
					IPrcPackage2HeaderLogic prcPackage2HeaderLogic = RVPARB.BusinessEnvironment.GetExportedValue<IPrcPackage2HeaderLogic>();
					var prcPackage2HeaderEntity = prcPackage2HeaderLogic.GetPackage2ByHeaderId(item.Id, item.PrcHeaderFk);
					if (prcPackage2HeaderEntity != null)
					{
						//entity.PrcPackage2HeaderFk = prcPackage2HeaderEntity.Id;
					}
				}
			}

			//EstCostRiskFk
			if (!string.IsNullOrEmpty(dto.EstCostRiskCode))
			{
				var item = _estCostRisks.FirstOrDefault(e => e.DisplayMember == dto.EstCostRiskCode);
				if (item != null)
				{
					entity.EstCostRiskFk = item.Id;
				}
			}

			//MdcAssetMasterFk
			if (!string.IsNullOrEmpty(dto.MdcAssetMasterCode))
			{
				var item = _mdcAssetMasters.FirstOrDefault(e => e.Code == dto.MdcAssetMasterCode);
				if (item != null)
				{
					entity.MdcAssetMasterFk = item.Id;
				}
			}

			//EstQtyRelBoqFk
			if (!string.IsNullOrEmpty(dto.EstQtyRelBoq))
			{
				var item = _estQuantityRel.FirstOrDefault(e => e.DescriptionInfo.Description.Trim().Equals(dto.EstQtyRelBoq.Trim()));
				if (item != null)
				{
					entity.EstQtyRelBoqFk = item.Id;
				}
			}

			//EstQtyRelActFk
			if (!string.IsNullOrEmpty(dto.EstQtyRelAct))
			{
				var item = _estQuantityRel.FirstOrDefault(e => e.DescriptionInfo.Description.Trim().Equals(dto.EstQtyRelAct.Trim()));
				if (item != null)
				{
					entity.EstQtyRelActFk = item.Id;
				}
			}

			//EstQtyRelGtuFk
			if (!string.IsNullOrEmpty(dto.EstQtyRelGtu))
			{
				var item = _estQuantityRel.FirstOrDefault(e => e.DescriptionInfo.Description.Trim().Equals(dto.EstQtyRelGtu.Trim()));
				if (item != null)
				{
					entity.EstQtyRelGtuFk = item.Id;
				}
			}

			//EstQtyTelAotFk
			if (!string.IsNullOrEmpty(dto.EstQtyTelAot))
			{
				var item = _estQuantityRel.FirstOrDefault(e => e.DescriptionInfo.Description.Trim().Equals(dto.EstQtyTelAot.Trim()));
				if (item != null)
				{
					entity.EstQtyTelAotFk = item.Id;
				}
			}

			//LgmJobFk
			if (!string.IsNullOrEmpty(dto.LgmJobCode))
			{
				var item = _lgmJobs.FirstOrDefault(e => e.Code.Equals(dto.LgmJobCode) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.LgmJobFk = item.Id;
				}
			}
			#endregion

			#region Set line item's sort code foreign keys

			//SortCode01Fk
			if (!string.IsNullOrEmpty(dto.SortCode01))
			{
				var item = this._sortCode01s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode01.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode01Fk = item.Id;
				}
			}
			//SortCode02Fk
			if (!string.IsNullOrEmpty(dto.SortCode02))
			{
				var item = this._sortCode02s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode02.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode02Fk = item.Id;
				}
			}
			//SortCode03Fk
			if (!string.IsNullOrEmpty(dto.SortCode03))
			{
				var item = this._sortCode03s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode03.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode03Fk = item.Id;
				}
			}
			//SortCode04Fk
			if (!string.IsNullOrEmpty(dto.SortCode04))
			{
				var item = this._sortCode04s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode04.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode04Fk = item.Id;
				}
			}
			//SortCode05Fk
			if (!string.IsNullOrEmpty(dto.SortCode05))
			{
				var item = this._sortCode05s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode05.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode05Fk = item.Id;
				}
			}
			//SortCode06Fk
			if (!string.IsNullOrEmpty(dto.SortCode06))
			{
				var item = this._sortCode06s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode06.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode06Fk = item.Id;
				}
			}
			//SortCode07Fk
			if (!string.IsNullOrEmpty(dto.SortCode07))
			{
				var item = this._sortCode07s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode07.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode07Fk = item.Id;
				}
			}
			//SortCode08Fk
			if (!string.IsNullOrEmpty(dto.SortCode08))
			{
				var item = this._sortCode08s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode08.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode08Fk = item.Id;
				}
			}
			//SortCode09Fk
			if (!string.IsNullOrEmpty(dto.SortCode09))
			{
				var item = this._sortCode09s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode09.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode09Fk = item.Id;
				}
			}
			//SortCode10Fk
			if (!string.IsNullOrEmpty(dto.SortCode10))
			{
				var item = this._sortCode10s.FirstOrDefault(e => e.Code.Trim().Equals(dto.SortCode10.Trim()) && e.ProjectFk == projectId);
				if (item != null)
				{
					entity.SortCode10Fk = item.Id;
				}
			}
			#endregion

			//#region overWrite (update, not insert) assembly entity if it arealdy exsit in database.
			//if (canOverwrite)
			//{
			//keep the exist entity primary key and version for updating (not insert)
			//Assembly Code is unique under a Assembly Catalog.
			//var entityInDb = _existedLineItems.FirstOrDefault(e => e.EstHeaderFk == entity.EstHeaderFk && e.EstAssemblyCatFk == entity.EstAssemblyCatFk && e.Code == entity.Code);
			//if (entityInDb != null)
			//{
			//    entity.EstHeaderFk = entityInDb.EstHeaderFk;
			//    entity.Id = entityInDb.Id;
			//    entity.InsertedAt = entityInDb.InsertedAt;
			//    entity.InsertedBy = entityInDb.InsertedBy;
			//    entity.UpdatedAt = DateTime.UtcNow;
			//    entity.UpdatedBy = BusinessApplication.BusinessEnvironment.CurrentContext.UserId;
			//    entity.Version = entityInDb.Version;

			//    //keep the exsited translation foreign key.
			//    entity.DescriptionInfo.DescriptionTr = entityInDb.DescriptionInfo.DescriptionTr;
			//}
			//}

			//#endregion

			#region Set line item's rule and param msg
			var entityId = entity.Id;

			if (dto.LineItemRules != null && dto.LineItemRules.Any())
			{
				foreach (var ruleDto in dto.LineItemRules)
				{
					//first thing is to check there are lineItem2Rule or not 

					var prjEstRule = _prjEstRules.FirstOrDefault(e => e.Code.Trim().Equals(ruleDto.Code.Trim()));
					var mdcRule = _mdcRules.FirstOrDefault(e => e.Code.Trim().Equals(ruleDto.Code.Trim()));
					var filterdMdcRule = _filteredMdcRules.FirstOrDefault(e => e.Code.Trim().Equals(ruleDto.Code.Trim()));

					//if rule is project unique rule ,it needn't filtered by master data filter.
					if ((filterdMdcRule == null && prjEstRule == null) || (filterdMdcRule == null && prjEstRule != null && mdcRule != null))
					{
						continue;
					}

					EstLineItem2EstRuleEntity lineItem2Rule = _estRuleLineItemLogic.GetListByLineItemId(_estHeader.Id, entity.Id).Where(e => e.Code == ruleDto.Code).FirstOrDefault();

					#region handle the rule
					//need copy the mdcRule to prjRule, then assign the prjRule to the lineItem here
					//if mdcRule and prjEstRule both is null, then will have no ruleParam need to get and copy
					#region create new prjEstRule
					if (prjEstRule == null && mdcRule != null)
					{
						var copyResult = _estRulePrjEstRuleLogic.CopyMasterRuleToProjectRule(new List<int>() { mdcRule.Id }, projectId);
						prjEstRule = new PrjEstRuleEntity();
						prjEstRule.Code = mdcRule.Code;
						prjEstRule.EstEvaluationSequenceFk = mdcRule.EstEvaluationSequenceFk;
						prjEstRule.Id = copyResult.First().Value;

						_prjEstRules.Add(prjEstRule);

						//copy the mdcRule's parameter to the prjEstRule, 
						//1. collect the mdcRule's parameter for prjEstRule
						//2. assign it to the lineItem, copy it to lineItemParam, collect it
						//3. compare it with the imported lineItemParameter data, collect which can overwrite and which need create
						//4. handle the param's parameterValueFk msg
						if (_estRuleParams.Count == 0)
						{
							//load these data when need, will complete this optimized solution later
							//EstRuleParamEntity
							_estRuleParams = new RIB.Visual.Estimate.Rule.BusinessComponents.EstimateRuleParameterLogic().GetAllEstRuleParameters().ToList();
						}
						var mdcRuleParams = _estRuleParams.Where(e => e.EstRuleFk == mdcRule.Id).ToList();
						if (mdcRuleParams != null && mdcRuleParams.Count > 0)
						{
							foreach (var ruleParam in mdcRuleParams)
							{
								//create prjEstRuleParam here							
								var createdPrjEstRuleParam = new EstimateRulePrjEstRuleParamLogic().Create(ruleParam, projectId, mdcRule.MdcLineItemContextFk, prjEstRule.Id);
								createdPrjEstRuleParams.Add(createdPrjEstRuleParam);

								//create estLineItemParam here
								var createdEstLineItemParam = new EstimateParameterLineItemLogic().Create(createdPrjEstRuleParam, _estHeader.Id);
								createdEstLineItemParams.Add(createdEstLineItemParam);
							}
						}
					}
					#endregion
					#endregion

					#region assign prjRule to lineitem and handle the lineItem2Rule
					if (prjEstRule != null)
					{
						var eva = new EstEvaluationSequenceEntity();
						if (!string.IsNullOrEmpty(ruleDto.EvaluationSequence))
						{
							eva = _estEvaluationSequence.FirstOrDefault(e => e.DescriptionInfo.Description.Equals(ruleDto.EvaluationSequence.Trim()));
						}

						if (lineItem2Rule == null)
						{
							//create lineItem2Rule here and collect it
							lineItem2Rule = _estRuleLineItemLogic.Create(new Platform.Core.IdentificationData());
							lineItem2Rule.Code = prjEstRule.Code;
							lineItem2Rule.PrjEstRuleFk = prjEstRule.Id;
							lineItem2Rule.EstHeaderFk = entity.EstHeaderFk;
							lineItem2Rule.EstLineItemFk = entity.Id;

							lineItem2RuleForCreated.Add(lineItem2Rule);
						}
						else
						{
							lineItem2RuleForUpdated.Add(lineItem2Rule);
						}

						//if existed, collect it and use it to compare with import lineItemParam data, 
						//if imported lineItemParam data has the same code with the existed, it means maybe need overwrite
						lineItem2Rule.IsExecution = ruleDto.IsExecution;
						lineItem2Rule.EstEvaluationSequenceFk = eva != null ? eva.Id : prjEstRule.EstEvaluationSequenceFk;
						lineItem2Rule.Comment = ruleDto.Comment;
					}
					#endregion
				}
			}

			if (dto.LineItemRuleParams != null && dto.LineItemRuleParams.Any())
			{
				foreach (var paramDto in dto.LineItemRuleParams)
				{
					var existLineItemParam = _estLineItemParams.Where(e => e.EstHeaderFk == _estHeader.Id && e.EstLineItemFk == entityId && e.Code.Trim().Equals(paramDto.Code.Trim())).FirstOrDefault();
					if (existLineItemParam == null)
					{
						var createdLineItemParam = new EstimateParameterLineItemLogic().Create(new Platform.Core.IdentificationData());
						createdLineItemParam.EstHeaderFk = _estHeader.Id;
						createdLineItemParam.EstLineItemFk = entity.Id;
						MapEstLineItemParamDto2Entity(paramDto, createdLineItemParam, true);

						createdEstLineItemParams.Add(createdLineItemParam);
					}
					else
					{
						MapEstLineItemParamDto2Entity(paramDto, existLineItemParam, canOverwrite);
						updatedEstLineItemParams.Add(existLineItemParam);
					}
				}
			}

			#endregion

			#region Set line item's resource here

			//Update,
			//If the clearResource is true,delete all of the existed lineitem's resources, then create new resource entities.
			//If the clearResource is false, keep the existed lineitem's resources and create new resource entities.
			if (entity.Version > 0 && canOverwrite && dto.ClearResource)
			{
				List<EstMainBizComp.EstResourceEntity> updatedItems = _existedResources.Where(e => e.EstHeaderFk == _estHeader.Id && e.EstLineItemFk == entityId).ToList();
				if (updatedItems != null && updatedItems.Count > 0)
				{
					_estResourceLogic.Delete(updatedItems, true);
					logger.WriteInfo(string.Format("Lineitem (Code : {0}) {1} existed estResource(s) are deleted.", entity.Code, updatedItems.Count));
				}
			}

			//Create and Update.
			//Create new resource entities.
			if (resourceDtos != null && resourceDtos.Any())
			{
				var _EstUserDefinedValTotalCalculator = new EstUserDefinedValTotalCalculator(entity.EstHeaderFk);
				var userDefinedColumns =_EstUserDefinedValTotalCalculator.GetUserDefinedColumns().ToList();
				var index = 0;

				foreach (var estResourcDto in resourceDtos)
				{
					EstMainBizComp.EstResourceEntity createdItem = _estResourceLogic.Create(entity.EstHeaderFk, entity.Id, estResourcDto.Code);

					MapResourceDto2Entity(estResourcDto, createdItem, true, projectId, _estHeaderJobId, entity.LgmJobFk);
					createdItem.UserDefinedcolValEntity = new UserDefinedcolValEntity();
					createdItem.UserDefinedcolValEntity.TableId = (int)userDefinedColumnTableIds.EstimateResource;
					createdItem.UserDefinedcolValEntity.Pk1 = _estHeader.Id;
					createdItem.UserDefinedcolValEntity.Pk2 = createdItem.EstLineItemFk;
					createdItem.UserDefinedcolValEntity.Pk3 = createdItem.Id;
					if (estResourcDto.UserDefineds != null && estResourcDto.UserDefineds.Any())
					{
						foreach(var kvp in estResourcDto.UserDefineds)
						{
							var userDefinedColumn = userDefinedColumns.Find(s => s.Code == kvp.Code);
							if(userDefinedColumn == null || kvp == null)
							{
								continue;
							}
							switch (userDefinedColumn.Id)
							{
								case 1:
									createdItem.UserDefinedcolValEntity.ColVal1 = kvp.ColVal;
									break;
								case 2:
									createdItem.UserDefinedcolValEntity.ColVal2 = kvp.ColVal;
									break;
								case 3:
									createdItem.UserDefinedcolValEntity.ColVal3 = kvp.ColVal;
									break;
								case 4:
									createdItem.UserDefinedcolValEntity.ColVal4 = kvp.ColVal;
									break;
								case 5:
									createdItem.UserDefinedcolValEntity.ColVal5 = kvp.ColVal;
									break;

							}
						}
						if(Ids !=null && Ids.Any())
						{
							createdItem.UserDefinedcolValEntity.Id = Ids[index];
							index++;
						}
						
					}
					createEstResourceEntities.AddRange(new List<EstResourceEntity>() { createdItem }.Flatten(e => e.ResourceChildren).ToList());
					entity.Resources.Add(createdItem);
				}
			}
			#endregion

			#region Set line item's model here
			if (dto.LineItemModelList != null)
			{
				//get all model objects from linetiem dto
				var allObjects = new List<EstModelObjectApiDto>();
				foreach (var model in dto.LineItemModelList)
				{
					allObjects.AddRange(model.modelObjects);
				}

				//map model objects
				foreach (var modelObject in allObjects)
				{
					var existedObject = _lineItemObjects.FirstOrDefault(e => e.CpiId == modelObject.CpiId);
					var lineitem2mdlobjectEntity = new EstLineItem2MdlObjectEntity();
					#region Map modelObject to estLineItem2MdlObjectEntity
					string[] keys = { "EST_HEADER_FK", entity.EstHeaderFk.ToString(CultureInfo.InvariantCulture) };
					lineitem2mdlobjectEntity.Id = _estLineItem2MdlObejctLogic.SequenceManager.GetNext("EST_LINEITEM2MDL_OBJECT", keys);
					lineitem2mdlobjectEntity.EstHeaderFk = entity.EstHeaderFk;
					lineitem2mdlobjectEntity.EstLineItemFk = entity.Id;
					lineitem2mdlobjectEntity.MdlModelFk = existedObject.ModelFk;
					lineitem2mdlobjectEntity.MdlObjectFk = existedObject.Id;
					lineitem2mdlobjectEntity.Quantity = modelObject.Quantity;
					lineitem2mdlobjectEntity.QuantityDetail = modelObject.QuantityDetail;
					lineitem2mdlobjectEntity.QuantityTarget = modelObject.QuantityItem;
					lineitem2mdlobjectEntity.QuantityTargetDetail = modelObject.QuantityItemDetail;
					lineitem2mdlobjectEntity.QuantityTotal = lineitem2mdlobjectEntity.Quantity * lineitem2mdlobjectEntity.QuantityTarget;
					#endregion
					createEstLineItem2MdlObject.Add(lineitem2mdlobjectEntity);
				}
			}
			#endregion
		}
	}
}
