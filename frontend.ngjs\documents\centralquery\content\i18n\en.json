﻿{
	"documents": {
		"centralquery": {
			"groupingContainer":"Document Structure",
			"documents": "Documents",
			"createdocuments": "Create Documents Central Query",
			"documentscentralquery": "Documents Central Query",
			"rubricCategoryMissingDefautStatus": "Missing default status. Please set a default status for the selected rubric category first.",
			"NoDefaultStatus": "No default status",
			"wizard":{
				"changestatus": "Change Status For Project Document"
			},
			"bim360Documents": {
				"syncDocumentToITwoTitle": "Synchronize BIM 360 documents to iTWO 4.0",
				"syncDocumentToBim360Title": "Synchronize iTWO 4.0 documents to BIM 360",
				"bim360Project": "BIM 360 Project",
				"itwo40Project": "iTWO 4.0 Project",
				"folder": "Folder",
				"destineFolder": "Destine Folder",
				"keyWord": "Keyword",
				"btnSearchText": "Search",
				"status_all": "(All)",
				"btnSynchronizeText": "Synchronize",
				"btnLoadDocumentText": "Load Document",
				"msgDocumentSelected": "Document selected:{0} files, total size {1} MB.",
				"documentsSaved" : "Selected documents have been saved as iTWO 4.0 documents successfully.",
				"documentsSavedToBim360": "Documents have been synchronized to BIM 360 successfully!",
				"documentsNotSaved" : "Failed to save the selected documents. Message:",
				"selectBim360Folder" : "Select BIM 360 folder",
				"folderNameDisplay" : "Folder Name",
				"columns": {
					"isSelectedTitle": "Selected",
					"status" : "Status",
					"name": "Name",
					"code": "Code",
					"description" : "Description",
					"version": "Version",
					"size": "Size",
					"documentId": "Document ID",
					"originalFileName": "Original File Name",
					"projectCode": "Project Code",
					"projectName": "Project Name"
				},
				"compressDocumentsTip" : "Compress selected documents as one zip file before saving to iTWO4.0",
				"zipFileName": "File name (.zip):"
			},
			"workflow": {
				"chooseApprovalLevelTitle": "Choose Approval Level",
				"assignApproversForLeve1": "Assign Approvers for Leve 1",
				"assignApproversForLeve2": "Assign Approvers for Leve 2",
				"assignApproversForLeve3": "Assign Approvers for Leve 3",
				"singleApproval": "Single Approval",
				"groupApproval": "Group Approval",
				"comments": "Comments",
				"levelApproval1": "1 Level Approval",
				"levelApproval2": "2 Level Approval",
				"levelApproval3": "3 Level Approval",
				"reviewAndApprovalDocuments": "Please review and approve below documents",
				"preview": "Preview",
				"download": "Download",
				"approvalComment": "Approval Comment : (up to  255 characters)",
				"declineComment": "Decline Comment : (up to  255 characters)",
				"ok": "OK",
				"cancel": "Cancel",
				"approvedDocumentList": "Approved document list",
				"approvedCommentList": "Approved comment list",
				"declinedDocumentList": "Declined document list",
				"declinedCommentList": "Declined comment list",
				"continueDocument": "Status of below listed document(s) cannot run with this workflow and will be ignored. Continue with the remaining document(s)?? ",
				"approval": "Approval",
				"decline": "Decline",
				"approvalDate": "Approval Date",
				"declinedDate": "Declined Date",
				"commentDate": "Comment Date",
				"uploadDate": "Upload Date",
				"file": "File",
				"project": "Project",
				"approver": "Approver",
				"declinedBy": "Declined By",
				"clerk": "Clerk",
				"comment": "Comment",
				"status": "Status",
				"issuer": "Issuer",
				"issuedDate": "Issued Date",
				"issuerComment": "Issuer's Comment",
				"documentId": "Document ID",
				"documentInValidNote": "Current status of the selected document(s) not yet have the Approval workflow configured! Please go to Customizing module, project document status matrix, select the rubric category, for the starting status, enable and highlight the target approved status in matrix, scroll down to workflow section, add workflow template: ID 83 - Document Approval. Then it should work!"
			},
			"contextConfigTitle": "Context Configuration for New Document",
			"fromDocStructureTitle": "From Document Structure Selection",
			"fromDocProjectTitle": "From Fields of Highlighted Document Project",
			"entitySubsidiary": "Branch",
			"entityPES": "Performance Entry Sheet",
			"entityProject": "Project",
			"entityRfqHeader": "Request for Quotation",
			"entityReqHeader": "Requisition",
			"contextTitle": "Inherit Fields Selection",
			"entityStructure": "Procurement Structure",
			"modelFile": {
				"title": "Clean Up Conversion Copy of Document",
				"cleanRange": "Clean Up Range:",
				"allData": "All Conversion Data",
				"partialData": "Conversion Data older than",
				"oneMonth": "One Month",
				"twoMonth": "Two Months",
				"threeMonth": "Three Months",
				"halfYear": "Half Year",
				"oneYear": "One Years",
				"twoYear": "Two Years",
				"note": "Note:",
				"note1": "1.In order to preview documents in PDF Viewer,a conversion copy of the document will be stored in the server.If you would like to clean up there data to free up disk space,this wizard will help you to do so.",
				"note2": "2.Clean up conversion will not affect the preview feature in PDF Viewer except a one time Ad-Hoc auto conversion will be performed when preview next time.",
				"delSuccess": "Delete Success!"
			},
			"sharepoint": {
				"shareViaSharePointTitle": "Share via SharePoint",
				"shareViaSharePointCompleted": "Share via SharePoint completed.<br>",
				"openFile": "Open Document in SharePoint",
				"openFolder": "Open Folder in SharePoint",
				"title": "SharePoint Configuration",
				"commonTitle": "SharePoint",
				"projectNo": "Project No",
				"btnSynchronizeText": "Synchronize Selected Projects",
				"btnSearchText": "Search",
				"refresh": "Refresh",
				"refreshTitle": "Refresh from previous saved drilldown setting and customizing change",
				"reset": "Reset",
				"resetTitle": "Reset from the folder template and Customizing change.",
				"metaData": "Metadata",
				"metaDataType": "Metadata Type",
				"replaceExistedSpDoc": "Replace Existed SharePoint Document.",
				"replaceExistedSpDocCompleted": "Replace Existed SharePoint Document completed.",
				"saveMetadataCompleted": "Save Meta Completed.",
				"metaDataInfo": "Metadata is structured and descriptive information that provides context about the document, e.g., title or author.",
				"disableSharePoint": "SharePoint Integration is not yet enabled in Customizing!",
				"noDrillDownContinueSyncInfo": "No folder setting found for project {{ProjectNo}}. It is not possible to add/change folder structure after project is synced. Do you still want to sync project without folder setting?'. If click yes, proceed, else abort.",
				"noDrillDownAbortSyncInfo": "Selected project {0} has no folder configuration and cannot be synchronized!",
				"folderMoreThan500Info": "The total number of folders from the selected project has exceeded 500, which could lead to synchronization failure. Please reduce the project selection and try again.",
				"folderMoreThan200Info": "Synchronization will take some time as the total number of folders from the selected project has exceeded 200. Please be patient...",
				"columns": {
					"isSelectedTitle": "Selected",
					"projectSynced" : "Status",
					"projectNo": "Project Number",
					"projectName": "Project Name",
					"folderTemplate": "Folder Template",
					"projectDrillDown": "Project Drill Down Folder",
					"userAssignment": "User Assignment",
					"autoSyncDoc": "Auto Sync of Document",
					"sync2SharePoint": "Sync to SharePoint",
					"synchronizeBtnText": "Synchronize "
				},
				"folderTemplate": {
					"templateName": "Template Name",
					"structure": "Structure",
					"projectMetaData": "Project Metadata",
					"rubricCategory": "Rubric Category",
					"rubricCategroyOfDocument": "Rubric Category of Document",
					"documentCategory": "Document Category",
					"projectDocumentType": "Project Document Type",
					"businessPartner": "Business Partner",
					"procurementStructure": "Procurement Structure",
					"controllingUnit": "Controlling Unit",
					"folderStructure": "Folder Structure",
					"shareOptions": "Share Options",
					"userAssignment": "User Assignment",
					"canView": "Can View",
					"canEdit": "Can Edit",
					"users": "Users",
					"id": "Id",
					"mail": "Mail",
					"displayName": "Display Name",
					"saveAsTemplate": "Save to Template",
					"apply": "Apply",
					"applyToProjectsSelected": "Apply to Selected {{count}} Project(s)",
					"availableTemplateName": "Available Template Name",
					"newTemplateName": "New Template Name",
					"existNullInfolderStructure" : "Exist empty data in folder structure.",
					"applyToAllRsult": "Folder config get updated for {{count}} project(s)!",
					"applyResultDialogTitle": "Result",
					"error": {
						"templateNameRequired": "Template name is required.",
						"metaDataRequired": "Folder Structure in section Structure is required."
					}
				},
				"drillDown": {
					"update": "Update",
					"updateForAllProjects": "Update for all Projects"
				},
				"shareViaSharepoint": {
					"selectedDocument": "Selected Documents",
					"addAssignment": "Add Assignment",
					"message": "Message",
					"send": "Send"
				}
			}
		}
	}
}
