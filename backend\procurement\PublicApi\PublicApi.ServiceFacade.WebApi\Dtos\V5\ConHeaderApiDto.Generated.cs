﻿//------------------------------------------------------------------------------
// This is auto-generated code.
//------------------------------------------------------------------------------
// This code was generated by Devart Entity Developer tool using Data Transfer Object template.
// created for Version 1.0
//
// Changes to this file may cause incorrect behavior and will be lost if
// the code is regenerated.
//------------------------------------------------------------------------------

using System.Collections.Generic;
using RIB.Visual.Platform.Common;
using RIB.Visual.Procurement.PublicApi.BusinessComponents;

namespace RIB.Visual.Procurement.PublicApi.ServiceFacade.WebApi.V5
{


    /// <summary>
    /// There are no comments for ConHeaderApiEntity in the schema.
    /// </summary>
    [RIB.Visual.Platform.Common.MappedTable("CON_HEADERAPI_V")]
    [RIB.Visual.Platform.Common.PublicApiVersion(5)]
    public partial class ConHeaderApiDto : RIB.Visual.Platform.Core.ITypedDto<ConHeaderApiEntity>
    {
        #region Constructors

        /// <summary>
        /// Initializes an instance of class ConHeaderApiDto.
        /// </summary>
        public ConHeaderApiDto()
        {
        }

        /// <summary>
        /// Initializes an instance of class ConHeaderApiDto.
        /// </summary>
        /// <param name="entity">the instance of class ConHeaderApiEntity</param>
        public ConHeaderApiDto(ConHeaderApiEntity entity)
        {
            Id = entity.Id;
            ConStatusId = entity.ConStatusId;
            ConStatusDescription = entity.ConStatusDescription;
            CompanyId = entity.CompanyId;
            CompanyCode = entity.CompanyCode;
            ProjectId = entity.ProjectId;
            ProjectCode = entity.ProjectCode;
            ProjectDescription = entity.ProjectDescription;
            PackageId = entity.PackageId;
            PackageCode = entity.PackageCode;
            PackageDescription = entity.PackageDescription;
            TaxCodeId = entity.TaxCodeId;
            TaxCodeCode = entity.TaxCodeCode;
            TaxCodeDescription = entity.TaxCodeDescription;
            ClerkPrcId = entity.ClerkPrcId;
            ClerkPrcCode = entity.ClerkPrcCode;
            ClerkPrcDescription = entity.ClerkPrcDescription;
            ClerkReqId = entity.ClerkReqId;
            ClerkReqCode = entity.ClerkReqCode;
            ClerkReqDescription = entity.ClerkReqDescription;
            CurrencyId = entity.CurrencyId;
            CurrencyDescription = entity.CurrencyDescription;
            Exchangerate = entity.Exchangerate;
            PrjChangeId = entity.PrjChangeId;
            PrjChangeCode = entity.PrjChangeCode;
            PrjChangeDescription = entity.PrjChangeDescription;
            ConHeaderId = entity.ConHeaderId;
            ConHeaderCode = entity.ConHeaderCode;
            ConHeaderDescription = entity.ConHeaderDescription;
            Haschanges = entity.Haschanges;
            MaterialCatalogId = entity.MaterialCatalogId;
            MaterialCatalogCode = entity.MaterialCatalogCode;
            MaterialCatalogDescription = entity.MaterialCatalogDescription;
            PrcHeaderId = entity.PrcHeaderId;
            PrcConfigurationId = entity.PrcConfigurationId;
            PaymentTermFiId = entity.PaymentTermFiId;
            PaymentTermFiCode = entity.PaymentTermFiCode;
            PaymentTermFiDescription = entity.PaymentTermFiDescription;
            PaymentTermPaId = entity.PaymentTermPaId;
            PaymentTermPaCode = entity.PaymentTermPaCode;
            PaymentTermPaDescription = entity.PaymentTermPaDescription;
            Code = entity.Code;
            Description = entity.Description;
            SearchPattern = entity.SearchPattern;
            DateOrdered = entity.DateOrdered;
            DateReported = entity.DateReported;
            DateCanceled = entity.DateCanceled;
            DateDelivery = entity.DateDelivery;
            DateCallofffrom = entity.DateCallofffrom;
            DateCalloffto = entity.DateCalloffto;
            IsNotAccrualPrr = entity.IsNotAccrualPrr;
            ConTypeId = entity.ConTypeId;
            ConTypeDescription = entity.ConTypeDescription;
            PrcAwardmethodId = entity.PrcAwardmethodId;
            PrcAwardmethodDescription = entity.PrcAwardmethodDescription;
            PrcContracttypeId = entity.PrcContracttypeId;
            PrcContracttypeDescription = entity.PrcContracttypeDescription;
            MdcControllingunitId = entity.MdcControllingunitId;
            MdcControllingunitCode = entity.MdcControllingunitCode;
            MdcControllingunitDescription = entity.MdcControllingunitDescription;
            BusinesspartnerId = entity.BusinesspartnerId;
            BusinesspartnerDescription = entity.BusinesspartnerDescription;
            SubsidiaryId = entity.SubsidiaryId;
            SubsidiaryDescription = entity.SubsidiaryDescription;
            SupplierId = entity.SupplierId;
            SupplierCode = entity.SupplierCode;
            SupplierDescription = entity.SupplierDescription;
            ContactId = entity.ContactId;
            Businesspartner2Id = entity.Businesspartner2Id;
            Businesspartner2Description = entity.Businesspartner2Description;
            Subsidiary2Id = entity.Subsidiary2Id;
            Subsidiary2Description = entity.Subsidiary2Description;
            Supplier2Id = entity.Supplier2Id;
            Supplier2Code = entity.Supplier2Code;
            Supplier2Description = entity.Supplier2Description;
            Contact2Id = entity.Contact2Id;
            PrcIncotermId = entity.PrcIncotermId;
            PrcIncotermDescription = entity.PrcIncotermDescription;
            CompanyInvoiceId = entity.CompanyInvoiceId;
            CompanyInvoiceCode = entity.CompanyInvoiceCode;
            AddressId = entity.AddressId;
            AddressDescription = entity.AddressDescription;
            MdcBillingSchemaId = entity.MdcBillingSchemaId;
            MdcBillingSchemaDescription = entity.MdcBillingSchemaDescription;
            BusinesspartnerAgentId = entity.BusinesspartnerAgentId;
            BusinesspartnerAgentDescription = entity.BusinesspartnerAgentDescription;
            PrcPackage2headerId = entity.PrcPackage2headerId;
            PrcPackage2headerDescription = entity.PrcPackage2headerDescription;
            CodeQuotation = entity.CodeQuotation;
            DateQuotation = entity.DateQuotation;
            Remark = entity.Remark;
            UserDefined1 = entity.UserDefined1;
            UserDefined2 = entity.UserDefined2;
            UserDefined3 = entity.UserDefined3;
            UserDefined4 = entity.UserDefined4;
            UserDefined5 = entity.UserDefined5;
            ConfirmationCode = entity.ConfirmationCode;
            ConfirmationDate = entity.ConfirmationDate;
            ExternalCode = entity.ExternalCode;
            InsertedAt = entity.InsertedAt;
            InsertedBy = entity.InsertedBy;
            UpdatedAt = entity.UpdatedAt;
            UpdatedBy = entity.UpdatedBy;
            Version = entity.Version;
            PaymentTermAdId = entity.PaymentTermAdId;
            PaymentTermAdCode = entity.PaymentTermAdCode;
            PaymentTermAdDescription = entity.PaymentTermAdDescription;
            PrcCopyModeId = entity.PrcCopyModeId;
            PrcCopymodeDescription = entity.PrcCopymodeDescription;
            DateEffective = entity.DateEffective;
            DatePenalty = entity.DatePenalty;
            PenaltyPercentperday = entity.PenaltyPercentperday;
            PenaltyPercentmax = entity.PenaltyPercentmax;
            PenaltyComment = entity.PenaltyComment;
            VatGroupId = entity.VatGroupId;
            VatGroupDescription = entity.VatGroupDescription;
            ProvingPeriod = entity.ProvingPeriod;
            ProvingDealdline = entity.ProvingDealdline;
            ApprovalPeriod = entity.ApprovalPeriod;
            ApprovalDealdline = entity.ApprovalDealdline;
            Isfreeitemsallowed = entity.Isfreeitemsallowed;
            BankId = entity.BankId;
            MdcPriceListId = entity.MdcPriceListId;
            MdcPriceListDescription = entity.MdcPriceListDescription;
            QtnHeaderId = entity.QtnHeaderId;
            QtnHeaderCode = entity.QtnHeaderCode;
            QtnHeaderDescription = entity.QtnHeaderDescription;
            LanguageId = entity.LanguageId;
            BasAccassignBusinessId = entity.BasAccassignBusinessId;
            BasAccassignBusinessDescription = entity.BasAccassignBusinessDescription;
            BasAccassignControlId = entity.BasAccassignControlId;
            BasAccassignControlDescription = entity.BasAccassignControlDescription;
            BasAccassignAccountId = entity.BasAccassignAccountId;
            BasAccassignAccountDescription = entity.BasAccassignAccountDescription;
            BasAccassignConTypeId = entity.BasAccassignConTypeId;
            BasAccassignConTypeDescription = entity.BasAccassignConTypeDescription;
            ExecutionStart = entity.ExecutionStart;
            ExecutionEnd = entity.ExecutionEnd;
            OrdHeaderId = entity.OrdHeaderId;
            OrdHeaderCode = entity.OrdHeaderCode;
            OrdHeaderDescription = entity.OrdHeaderDescription;
            OverallDiscount = entity.OverallDiscount;
            OverallDiscountOc = entity.OverallDiscountOc;
            OverallDiscountPercent = entity.OverallDiscountPercent;
            SalesTaxMethodId = entity.SalesTaxMethodId;
            SalesTaxMethodDesc = entity.SalesTaxMethodDesc;
            BoqWicCatId = entity.BoqWicCatId;
            BoqWicCatCode = entity.BoqWicCatCode;
            BoqWicCatBoqId = entity.BoqWicCatBoqId;
            BoqWicCatBoqReference = entity.BoqWicCatBoqReference;
            ReqHeaderId = entity.ReqHeaderId;
            ValidFrom = entity.ValidFrom;
            ValidTo = entity.ValidTo;
            BaselineUpdate = entity.BaselineUpdate;
            StructureDes = entity.StructureDes;
            StructureCode = entity.StructureCode;
            StructureId = entity.StructureId;
            IsFramework = entity.IsFramework;
            BasLanguageFk = entity.BasLanguageFk;
            BasLanguageDesc = entity.BasLanguageDesc;

            // call partial method if implemented
            OnConstruct(entity);
        }

        #endregion

        #region Properties
    
        /// <summary>
        /// There are no comments for Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ID", TypeName = "int", Order = 0)]
        [System.ComponentModel.DataAnnotations.Key]
        [System.ComponentModel.DataAnnotations.Required()]
        public int Id { get; set; }
    
        /// <summary>
        /// There are no comments for ConStatusId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_STATUS_ID", TypeName = "int", Order = 1)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConStatusFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ConStatusId { get; set; }
    
        /// <summary>
        /// There are no comments for ConStatusDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_STATUS_DESC", TypeName = "nvarchar(2000)", Order = 2)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ConStatusDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_ID", TypeName = "int", Order = 3)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CompanyId { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_CODE", TypeName = "nvarchar(16)", Order = 4)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string CompanyCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_ID", TypeName = "int", Order = 5)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectFk")]
        public int? ProjectId { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_CODE", TypeName = "nvarchar(16)", Order = 6)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ProjectCode { get; set; }
    
        /// <summary>
        /// There are no comments for ProjectDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_PROJECT_DESC", TypeName = "nvarchar(252)", Order = 7)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ProjectDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PackageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_ID", TypeName = "int", Order = 8)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PackageFk")]
        public int? PackageId { get; set; }
    
        /// <summary>
        /// There are no comments for PackageCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_CODE", TypeName = "nvarchar(16)", Order = 9)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PackageCode { get; set; }
    
        /// <summary>
        /// There are no comments for PackageDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE_DESC", TypeName = "nvarchar(252)", Order = 10)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PackageDescription { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_ID", TypeName = "int", Order = 11)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("TaxCodeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int TaxCodeId { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_CODE", TypeName = "nvarchar(16)", Order = 12)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string TaxCodeCode { get; set; }
    
        /// <summary>
        /// There are no comments for TaxCodeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_TAX_CODE_DESC", TypeName = "nvarchar(2000)", Order = 13)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string TaxCodeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_ID", TypeName = "int", Order = 14)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkPrcFk")]
        public int? ClerkPrcId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_CODE", TypeName = "nvarchar(16)", Order = 15)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkPrcCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkPrcDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_PRC_DESC", TypeName = "nvarchar(252)", Order = 16)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkPrcDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_ID", TypeName = "int", Order = 17)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ClerkReqFk")]
        public int? ClerkReqId { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_CODE", TypeName = "nvarchar(16)", Order = 18)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ClerkReqCode { get; set; }
    
        /// <summary>
        /// There are no comments for ClerkReqDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CLERK_REQ_DESC", TypeName = "nvarchar(252)", Order = 19)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ClerkReqDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_ID", TypeName = "int", Order = 20)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasCurrencyFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int CurrencyId { get; set; }
    
        /// <summary>
        /// There are no comments for CurrencyDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_CURRENCY_DESC", TypeName = "nvarchar(2000)", Order = 21)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string CurrencyDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Exchangerate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXCHANGERATE", TypeName = "numeric(10,5)", Order = 22)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExchangeRate")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal Exchangerate { get; set; }
    
        /// <summary>
        /// There are no comments for PrjChangeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_ID", TypeName = "int", Order = 23)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProjectChangeFk")]
        public int? PrjChangeId { get; set; }
    
        /// <summary>
        /// There are no comments for PrjChangeCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_CODE", TypeName = "nvarchar(16)", Order = 24)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PrjChangeCode { get; set; }
    
        /// <summary>
        /// There are no comments for PrjChangeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRJ_CHANGE_DESC", TypeName = "nvarchar(252)", Order = 25)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PrjChangeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_ID", TypeName = "int", Order = 26)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConHeaderFk")]
        public int? ConHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_CODE", TypeName = "nvarchar(16)", Order = 27)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string ConHeaderCode { get; set; }
    
        /// <summary>
        /// There are no comments for ConHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_HEADER_DESC", TypeName = "nvarchar(252)", Order = 28)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ConHeaderDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Haschanges in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("HASCHANGES", TypeName = "bit", Order = 29)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("HasChanges")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Haschanges { get; set; }
    
        /// <summary>
        /// There are no comments for MaterialCatalogId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_ID", TypeName = "int", Order = 30)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("MaterialCatalogFk")]
        public int? MaterialCatalogId { get; set; }
    
        /// <summary>
        /// There are no comments for MaterialCatalogCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_CODE", TypeName = "nvarchar(16)", Order = 31)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string MaterialCatalogCode { get; set; }
    
        /// <summary>
        /// There are no comments for MaterialCatalogDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_MATERIAL_CATALOG_DESC", TypeName = "nvarchar(2000)", Order = 32)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MaterialCatalogDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_HEADER_ID", TypeName = "int", Order = 33)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcHeaderFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcConfigurationId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONFIGURATION_ID", TypeName = "int", Order = 34)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcConfigurationId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_ID", TypeName = "int", Order = 35)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermFiFk")]
        public int? PaymentTermFiId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_CODE", TypeName = "nvarchar(16)", Order = 36)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermFiCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermFiDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_FI_DESC", TypeName = "nvarchar(2000)", Order = 37)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermFiDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_ID", TypeName = "int", Order = 38)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermPaFk")]
        public int? PaymentTermPaId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_CODE", TypeName = "nvarchar(16)", Order = 39)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermPaCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermPaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_PA_DESC", TypeName = "nvarchar(2000)", Order = 40)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermPaDescription { get; set; }
    
        /// <summary>
        /// There are no comments for Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE", TypeName = "nvarchar(16)", Order = 41)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Code")]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string Code { get; set; }
    
        /// <summary>
        /// There are no comments for Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DESCRIPTION", TypeName = "nvarchar(252)", Order = 42)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Description")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Description { get; set; }
    
        /// <summary>
        /// There are no comments for SearchPattern in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("SEARCH_PATTERN", TypeName = "nvarchar(450)", Order = 43)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SearchPattern")]
        [System.ComponentModel.DataAnnotations.StringLength(450)]
        public string SearchPattern { get; set; }
    
        /// <summary>
        /// There are no comments for DateOrdered in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_ORDERED", TypeName = "date", Order = 44)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateOrdered")]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateOrdered { get; set; }
    
        /// <summary>
        /// There are no comments for DateReported in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_REPORTED", TypeName = "date", Order = 45)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateReported")]
        public System.DateTime? DateReported { get; set; }
    
        /// <summary>
        /// There are no comments for DateCanceled in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CANCELED", TypeName = "date", Order = 46)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCanceled")]
        public System.DateTime? DateCanceled { get; set; }
    
        /// <summary>
        /// There are no comments for DateDelivery in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_DELIVERY", TypeName = "date", Order = 47)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateDelivery")]
        public System.DateTime? DateDelivery { get; set; }
    
        /// <summary>
        /// There are no comments for DateCallofffrom in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CALLOFFFROM", TypeName = "date", Order = 48)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCallofffrom")]
        public System.DateTime? DateCallofffrom { get; set; }
    
        /// <summary>
        /// There are no comments for DateCalloffto in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_CALLOFFTO", TypeName = "date", Order = 49)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateCalloffto")]
        public System.DateTime? DateCalloffto { get; set; }
    
        /// <summary>
        /// There are no comments for IsNotAccrualPrr in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISNOTACCRUAL_PRR", TypeName = "bit", Order = 50)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsNotAccrualPrr { get; set; }
    
        /// <summary>
        /// There are no comments for ConTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_TYPE_ID", TypeName = "int", Order = 51)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConTypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int ConTypeId { get; set; }
    
        /// <summary>
        /// There are no comments for ConTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CON_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 52)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string ConTypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcAwardmethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_ID", TypeName = "int", Order = 53)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AwardmethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcAwardmethodId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcAwardmethodDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_AWARDMETHOD_DESC", TypeName = "nvarchar(2000)", Order = 54)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcAwardmethodDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcContracttypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_ID", TypeName = "int", Order = 55)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContracttypeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcContracttypeId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcContracttypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_CONTRACTTYPE_DESC", TypeName = "nvarchar(2000)", Order = 56)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcContracttypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_ID", TypeName = "int", Order = 57)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ControllingUnitFk")]
        public int? MdcControllingunitId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_CODE", TypeName = "nvarchar(16)", Order = 58)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string MdcControllingunitCode { get; set; }
    
        /// <summary>
        /// There are no comments for MdcControllingunitDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_CONTROLLINGUNIT_DESC", TypeName = "nvarchar(2000)", Order = 59)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcControllingunitDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_ID", TypeName = "int", Order = 60)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int BusinesspartnerId { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_DESC", TypeName = "nvarchar(252)", Order = 61)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        [System.ComponentModel.DataAnnotations.Required()]
        public string BusinesspartnerDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_ID", TypeName = "int", Order = 62)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SubsidiaryFk")]
        public int? SubsidiaryId { get; set; }
    
        /// <summary>
        /// There are no comments for SubsidiaryDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY_DESC", TypeName = "nvarchar(252)", Order = 63)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SubsidiaryDescription { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_ID", TypeName = "int", Order = 64)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SupplierFk")]
        public int? SupplierId { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_CODE", TypeName = "nvarchar(252)", Order = 65)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierCode { get; set; }
    
        /// <summary>
        /// There are no comments for SupplierDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER_DESC", TypeName = "nvarchar(252)", Order = 66)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string SupplierDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ContactId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT_ID", TypeName = "int", Order = 67)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ContactFk")]
        public int? ContactId { get; set; }
    
        /// <summary>
        /// There are no comments for Businesspartner2Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER2_ID", TypeName = "int", Order = 68)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartner2Fk")]
        public int? Businesspartner2Id { get; set; }
    
        /// <summary>
        /// There are no comments for Businesspartner2Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER2_DESC", TypeName = "nvarchar(252)", Order = 69)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Businesspartner2Description { get; set; }
    
        /// <summary>
        /// There are no comments for Subsidiary2Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY2_ID", TypeName = "int", Order = 70)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Subsidiary2Fk")]
        public int? Subsidiary2Id { get; set; }
    
        /// <summary>
        /// There are no comments for Subsidiary2Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUBSIDIARY2_DESC", TypeName = "nvarchar(252)", Order = 71)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Subsidiary2Description { get; set; }
    
        /// <summary>
        /// There are no comments for Supplier2Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER2_ID", TypeName = "int", Order = 72)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Supplier2Fk")]
        public int? Supplier2Id { get; set; }
    
        /// <summary>
        /// There are no comments for Supplier2Code in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER2_CODE", TypeName = "nvarchar(252)", Order = 73)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Supplier2Code { get; set; }
    
        /// <summary>
        /// There are no comments for Supplier2Description in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_SUPPLIER2_DESC", TypeName = "nvarchar(252)", Order = 74)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string Supplier2Description { get; set; }
    
        /// <summary>
        /// There are no comments for Contact2Id in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_CONTACT2_ID", TypeName = "int", Order = 75)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Contact2Fk")]
        public int? Contact2Id { get; set; }
    
        /// <summary>
        /// There are no comments for PrcIncotermId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_ID", TypeName = "int", Order = 76)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IncotermFk")]
        public int? PrcIncotermId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcIncotermDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_INCOTERM_DESC", TypeName = "nvarchar(2000)", Order = 77)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcIncotermDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyInvoiceId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_INVOICE_ID", TypeName = "int", Order = 78)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CompanyInvoiceFk")]
        public int? CompanyInvoiceId { get; set; }
    
        /// <summary>
        /// There are no comments for CompanyInvoiceCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_COMPANY_INVOICE_CODE", TypeName = "nvarchar(16)", Order = 79)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string CompanyInvoiceCode { get; set; }
    
        /// <summary>
        /// There are no comments for AddressId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_ID", TypeName = "int", Order = 80)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("AddressFk")]
        public int? AddressId { get; set; }
    
        /// <summary>
        /// There are no comments for AddressDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ADDRESS_DESC", TypeName = "nvarchar(2000)", Order = 81)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string AddressDescription { get; set; }
    
        /// <summary>
        /// There are no comments for MdcBillingSchemaId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_ID", TypeName = "int", Order = 82)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BillingSchemaFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int MdcBillingSchemaId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcBillingSchemaDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_BILLING_SCHEMA_DESC", TypeName = "nvarchar(2000)", Order = 83)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcBillingSchemaDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerAgentId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_AGENT_ID", TypeName = "int", Order = 84)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BusinessPartnerAgentFk")]
        public int? BusinesspartnerAgentId { get; set; }
    
        /// <summary>
        /// There are no comments for BusinesspartnerAgentDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BUSINESSPARTNER_AGENT_DESC", TypeName = "nvarchar(252)", Order = 85)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BusinesspartnerAgentDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackage2headerId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_ID", TypeName = "int", Order = 86)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Package2HeaderFk")]
        public int? PrcPackage2headerId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcPackage2headerDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_PACKAGE2HEADER_DESC", TypeName = "nvarchar(252)", Order = 87)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string PrcPackage2headerDescription { get; set; }
    
        /// <summary>
        /// There are no comments for CodeQuotation in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CODE_QUOTATION", TypeName = "nvarchar(20)", Order = 88)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("CodeQuotation")]
        [System.ComponentModel.DataAnnotations.StringLength(20)]
        public string CodeQuotation { get; set; }
    
        /// <summary>
        /// There are no comments for DateQuotation in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_QUOTATION", TypeName = "date", Order = 89)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateQuotation")]
        public System.DateTime? DateQuotation { get; set; }
    
        /// <summary>
        /// There are no comments for Remark in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REMARK", TypeName = "nvarchar(2000)", Order = 90)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Remark")]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string Remark { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined1 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED1", TypeName = "nvarchar(252)", Order = 91)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined1")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined1 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined2 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED2", TypeName = "nvarchar(252)", Order = 92)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined2")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined2 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined3 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED3", TypeName = "nvarchar(252)", Order = 93)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined3")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined3 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined4 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED4", TypeName = "nvarchar(252)", Order = 94)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined4")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined4 { get; set; }
    
        /// <summary>
        /// There are no comments for UserDefined5 in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("USERDEFINED5", TypeName = "nvarchar(252)", Order = 95)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Userdefined5")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string UserDefined5 { get; set; }
    
        /// <summary>
        /// There are no comments for ConfirmationCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONFIRMATION_CODE", TypeName = "nvarchar(252)", Order = 96)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConfirmationCode")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ConfirmationCode { get; set; }
    
        /// <summary>
        /// There are no comments for ConfirmationDate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("CONFIRMATION_DATE", TypeName = "date", Order = 97)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ConfirmationDate")]
        public System.DateTime? ConfirmationDate { get; set; }
    
        /// <summary>
        /// There are no comments for ExternalCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXTERNAL_CODE", TypeName = "nvarchar(252)", Order = 98)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExternalCode")]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string ExternalCode { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("INSERTED", TypeName = "datetime", Order = 99)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InsertedAt")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime InsertedAt { get; set; }
    
        /// <summary>
        /// There are no comments for InsertedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOISR", TypeName = "int", Order = 100)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("InsertedBy")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int InsertedBy { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedAt in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("UPDATED", TypeName = "datetime", Order = 101)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UpdatedAt")]
        [RIB.Visual.Platform.Common.DomainName(Name = @"date")]
        public System.DateTime? UpdatedAt { get; set; }
    
        /// <summary>
        /// There are no comments for UpdatedBy in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("WHOUPD", TypeName = "int", Order = 102)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("UpdatedBy")]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int? UpdatedBy { get; set; }
    
        /// <summary>
        /// There are no comments for Version in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VERSION", TypeName = "int", Order = 103)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("Version")]
        [System.ComponentModel.DataAnnotations.Required()]
        [RIB.Visual.Platform.Common.DomainName(Name = @"integer")]
        public int Version { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_ID", TypeName = "int", Order = 104)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PaymentTermAdFk")]
        public int? PaymentTermAdId { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_CODE", TypeName = "nvarchar(16)", Order = 105)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string PaymentTermAdCode { get; set; }
    
        /// <summary>
        /// There are no comments for PaymentTermAdDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_PAYMENT_TERM_AD_DESC", TypeName = "nvarchar(2000)", Order = 106)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PaymentTermAdDescription { get; set; }
    
        /// <summary>
        /// There are no comments for PrcCopyModeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_ID", TypeName = "int", Order = 107)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PrcCopyModeFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int PrcCopyModeId { get; set; }
    
        /// <summary>
        /// There are no comments for PrcCopymodeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PRC_COPYMODE_DESC", TypeName = "nvarchar(2000)", Order = 108)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string PrcCopymodeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for DateEffective in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_EFFECTIVE", TypeName = "date", Order = 109)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DateEffective")]
        [System.ComponentModel.DataAnnotations.Required()]
        public System.DateTime DateEffective { get; set; }
    
        /// <summary>
        /// There are no comments for DatePenalty in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("DATE_PENALTY", TypeName = "date", Order = 110)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("DatePenalty")]
        public System.DateTime? DatePenalty { get; set; }
    
        /// <summary>
        /// There are no comments for PenaltyPercentperday in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PENALTY_PERCENTPERDAY", TypeName = "numeric(9,3)", Order = 111)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PenaltyPercentPerDay")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal PenaltyPercentperday { get; set; }
    
        /// <summary>
        /// There are no comments for PenaltyPercentmax in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PENALTY_PERCENTMAX", TypeName = "numeric(9,3)", Order = 112)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PenaltyPercentMax")]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal PenaltyPercentmax { get; set; }
    
        /// <summary>
        /// There are no comments for PenaltyComment in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PENALTY_COMMENT", TypeName = "nvarchar(255)", Order = 113)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("PenaltyComment")]
        [System.ComponentModel.DataAnnotations.StringLength(255)]
        public string PenaltyComment { get; set; }
    
        /// <summary>
        /// There are no comments for VatGroupId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_ID", TypeName = "int", Order = 114)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BpdVatGroupFk")]
        public int? VatGroupId { get; set; }
    
        /// <summary>
        /// There are no comments for VatGroupDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_VATGROUP_DESC", TypeName = "nvarchar(2000)", Order = 115)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string VatGroupDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ProvingPeriod in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROVING_PERIOD", TypeName = "int", Order = 116)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProvingPeriod")]
        public int? ProvingPeriod { get; set; }
    
        /// <summary>
        /// There are no comments for ProvingDealdline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("PROVING_DEALDLINE", TypeName = "int", Order = 117)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ProvingDealdline")]
        public int? ProvingDealdline { get; set; }
    
        /// <summary>
        /// There are no comments for ApprovalPeriod in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("APPROVAL_PERIOD", TypeName = "int", Order = 118)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ApprovalPeriod")]
        public int? ApprovalPeriod { get; set; }
    
        /// <summary>
        /// There are no comments for ApprovalDealdline in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("APPROVAL_DEALDLINE", TypeName = "int", Order = 119)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ApprovalDealdline")]
        public int? ApprovalDealdline { get; set; }
    
        /// <summary>
        /// There are no comments for Isfreeitemsallowed in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFREEITEMSALLOWED", TypeName = "bit", Order = 120)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("IsFreeItemsAllowed")]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool Isfreeitemsallowed { get; set; }
    
        /// <summary>
        /// There are no comments for BankId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BPD_BANK_ID", TypeName = "int", Order = 121)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BankFk")]
        public int? BankId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcPriceListId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_PRICE_LIST_ID", TypeName = "int", Order = 122)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("MdcPriceListFk")]
        public int? MdcPriceListId { get; set; }
    
        /// <summary>
        /// There are no comments for MdcPriceListDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("MDC_PRICE_LIST_DESC", TypeName = "nvarchar(2000)", Order = 123)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string MdcPriceListDescription { get; set; }
    
        /// <summary>
        /// There are no comments for QtnHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_ID", TypeName = "int", Order = 124)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("QtnHeaderFk")]
        public int? QtnHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for QtnHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_CODE", TypeName = "nvarchar(16)", Order = 125)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string QtnHeaderCode { get; set; }
    
        /// <summary>
        /// There are no comments for QtnHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("QTN_HEADER_DESC", TypeName = "nvarchar(252)", Order = 126)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string QtnHeaderDescription { get; set; }
    
        /// <summary>
        /// There are no comments for LanguageId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("LANGUAGE_ID", TypeName = "int", Order = 127)]
        [System.ComponentModel.DataAnnotations.Required()]
        public int LanguageId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignBusinessId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_BUSINESS_ID", TypeName = "int", Order = 128)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignBusinessFk")]
        public int? BasAccassignBusinessId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignBusinessDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_BUSINESS_DESC", TypeName = "nvarchar(2000)", Order = 129)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasAccassignBusinessDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignControlId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CONTROL_ID", TypeName = "int", Order = 130)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignControlFk")]
        public int? BasAccassignControlId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignControlDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CONTROL_DESC", TypeName = "nvarchar(2000)", Order = 131)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasAccassignControlDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignAccountId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_ACCOUNT_ID", TypeName = "int", Order = 132)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignAccountFk")]
        public int? BasAccassignAccountId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignAccountDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_ACCOUNT_DESC", TypeName = "nvarchar(2000)", Order = 133)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasAccassignAccountDescription { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignConTypeId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_ID", TypeName = "int", Order = 134)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BasAccassignConTypeFk")]
        public int? BasAccassignConTypeId { get; set; }
    
        /// <summary>
        /// There are no comments for BasAccassignConTypeDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_ACCASSIGN_CON_TYPE_DESC", TypeName = "nvarchar(2000)", Order = 135)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string BasAccassignConTypeDescription { get; set; }
    
        /// <summary>
        /// There are no comments for ExecutionStart in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXECUTION_START", TypeName = "date", Order = 136)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExecutionStart")]
        public System.DateTime? ExecutionStart { get; set; }
    
        /// <summary>
        /// There are no comments for ExecutionEnd in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("EXECUTION_END", TypeName = "date", Order = 137)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ExecutionEnd")]
        public System.DateTime? ExecutionEnd { get; set; }
    
        /// <summary>
        /// There are no comments for OrdHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ORD_HEADER_ID", TypeName = "int", Order = 138)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("OrdHeaderFk")]
        public int? OrdHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for OrdHeaderCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ORD_HEADER_CODE", TypeName = "nvarchar(16)", Order = 139)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string OrdHeaderCode { get; set; }
    
        /// <summary>
        /// There are no comments for OrdHeaderDescription in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ORD_HEADER_DESC", TypeName = "nvarchar(252)", Order = 140)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string OrdHeaderDescription { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscount in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT", TypeName = "numeric(19,7)", Order = 141)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscount { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscountOc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_OC", TypeName = "numeric(19,7)", Order = 142)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscountOc { get; set; }
    
        /// <summary>
        /// There are no comments for OverallDiscountPercent in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("OVERALL_DISCOUNT_PERCENT", TypeName = "numeric(9,3)", Order = 143)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [System.ComponentModel.DataAnnotations.Required()]
        public decimal OverallDiscountPercent { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_ID", TypeName = "int", Order = 144)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("SalesTaxMethodFk")]
        [System.ComponentModel.DataAnnotations.Required()]
        public int SalesTaxMethodId { get; set; }
    
        /// <summary>
        /// There are no comments for SalesTaxMethodDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_SALES_TAX_METHOD_DESC", TypeName = "nvarchar(2000)", Order = 145)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string SalesTaxMethodDesc { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_ID", TypeName = "int", Order = 146)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BoqWicCatFk")]
        public int? BoqWicCatId { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_CODE", TypeName = "nvarchar(16)", Order = 147)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string BoqWicCatCode { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatBoqId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_BOQ_ID", TypeName = "int", Order = 148)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BoqWicCatBoqFk")]
        public int? BoqWicCatBoqId { get; set; }
    
        /// <summary>
        /// There are no comments for BoqWicCatBoqReference in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BOQ_WIC_CAT_BOQ_REFERENCE", TypeName = "nvarchar(252)", Order = 149)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BoqWicCatBoqReference { get; set; }
    
        /// <summary>
        /// There are no comments for ReqHeaderId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("REQ_HEADER_ID", TypeName = "int", Order = 150)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("ReqHeaderFk")]
        public int? ReqHeaderId { get; set; }
    
        /// <summary>
        /// There are no comments for ValidFrom in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VALIDFROM", TypeName = "datetime", Order = 151)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? ValidFrom { get; set; }
    
        /// <summary>
        /// There are no comments for ValidTo in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("VALIDTO", TypeName = "datetime", Order = 152)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public System.DateTime? ValidTo { get; set; }
    
        /// <summary>
        /// There are no comments for BaselineUpdate in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BASELINE_UPDATE", TypeName = "datetime", Order = 153)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        [RIB.Visual.Platform.Common.InternalApiField("BaselineUpdate")]
        public System.DateTime? BaselineUpdate { get; set; }
    
        /// <summary>
        /// There are no comments for StructureDes in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STRUCTURE_DES", TypeName = "nvarchar(2000)", Order = 154)]
        [System.ComponentModel.DataAnnotations.StringLength(2000)]
        public string StructureDes { get; set; }
    
        /// <summary>
        /// There are no comments for StructureCode in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STRUCTURE_CODE", TypeName = "nvarchar(16)", Order = 155)]
        [System.ComponentModel.DataAnnotations.StringLength(16)]
        public string StructureCode { get; set; }
    
        /// <summary>
        /// There are no comments for StructureId in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("STRUCTURE_ID", TypeName = "int", Order = 156)]
        public int? StructureId { get; set; }
    
        /// <summary>
        /// There are no comments for IsFramework in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("ISFRAMEWORK", TypeName = "bit", Order = 157)]
        [System.ComponentModel.DataAnnotations.Required()]
        public bool IsFramework { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageFk in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_FK", TypeName = "int", Order = 158)]
        [RIB.Visual.Platform.Common.CopyFromPublicApi]
        public int? BasLanguageFk { get; set; }
    
        /// <summary>
        /// There are no comments for BasLanguageDesc in the schema.
        /// </summary>
        [RIB.Visual.Platform.Common.MappedColumn("BAS_LANGUAGE_DESC", TypeName = "nvarchar(252)", Order = 159)]
        [System.ComponentModel.DataAnnotations.StringLength(252)]
        public string BasLanguageDesc { get; set; }

        #endregion

        System.Type RIB.Visual.Platform.Core.IDto.EntityType
        {
            get { return typeof(ConHeaderApiEntity); }
        }

        /// <summary>
        /// Copy the current ConHeaderApiDto instance to a new ConHeaderApiEntity instance.
        /// </summary>
        /// <returns>a new instance of class ConHeaderApiEntity</returns>
        public ConHeaderApiEntity Copy()
        {
          var entity = new ConHeaderApiEntity();

          entity.Id = this.Id;
          entity.ConStatusId = this.ConStatusId;
          entity.ConStatusDescription = this.ConStatusDescription;
          entity.CompanyId = this.CompanyId;
          entity.CompanyCode = this.CompanyCode;
          entity.ProjectId = this.ProjectId;
          entity.ProjectCode = this.ProjectCode;
          entity.ProjectDescription = this.ProjectDescription;
          entity.PackageId = this.PackageId;
          entity.PackageCode = this.PackageCode;
          entity.PackageDescription = this.PackageDescription;
          entity.TaxCodeId = this.TaxCodeId;
          entity.TaxCodeCode = this.TaxCodeCode;
          entity.TaxCodeDescription = this.TaxCodeDescription;
          entity.ClerkPrcId = this.ClerkPrcId;
          entity.ClerkPrcCode = this.ClerkPrcCode;
          entity.ClerkPrcDescription = this.ClerkPrcDescription;
          entity.ClerkReqId = this.ClerkReqId;
          entity.ClerkReqCode = this.ClerkReqCode;
          entity.ClerkReqDescription = this.ClerkReqDescription;
          entity.CurrencyId = this.CurrencyId;
          entity.CurrencyDescription = this.CurrencyDescription;
          entity.Exchangerate = this.Exchangerate;
          entity.PrjChangeId = this.PrjChangeId;
          entity.PrjChangeCode = this.PrjChangeCode;
          entity.PrjChangeDescription = this.PrjChangeDescription;
          entity.ConHeaderId = this.ConHeaderId;
          entity.ConHeaderCode = this.ConHeaderCode;
          entity.ConHeaderDescription = this.ConHeaderDescription;
          entity.Haschanges = this.Haschanges;
          entity.MaterialCatalogId = this.MaterialCatalogId;
          entity.MaterialCatalogCode = this.MaterialCatalogCode;
          entity.MaterialCatalogDescription = this.MaterialCatalogDescription;
          entity.PrcHeaderId = this.PrcHeaderId;
          entity.PrcConfigurationId = this.PrcConfigurationId;
          entity.PaymentTermFiId = this.PaymentTermFiId;
          entity.PaymentTermFiCode = this.PaymentTermFiCode;
          entity.PaymentTermFiDescription = this.PaymentTermFiDescription;
          entity.PaymentTermPaId = this.PaymentTermPaId;
          entity.PaymentTermPaCode = this.PaymentTermPaCode;
          entity.PaymentTermPaDescription = this.PaymentTermPaDescription;
          entity.Code = this.Code;
          entity.Description = this.Description;
          entity.SearchPattern = this.SearchPattern;
          entity.DateOrdered = this.DateOrdered;
          entity.DateReported = this.DateReported;
          entity.DateCanceled = this.DateCanceled;
          entity.DateDelivery = this.DateDelivery;
          entity.DateCallofffrom = this.DateCallofffrom;
          entity.DateCalloffto = this.DateCalloffto;
          entity.IsNotAccrualPrr = this.IsNotAccrualPrr;
          entity.ConTypeId = this.ConTypeId;
          entity.ConTypeDescription = this.ConTypeDescription;
          entity.PrcAwardmethodId = this.PrcAwardmethodId;
          entity.PrcAwardmethodDescription = this.PrcAwardmethodDescription;
          entity.PrcContracttypeId = this.PrcContracttypeId;
          entity.PrcContracttypeDescription = this.PrcContracttypeDescription;
          entity.MdcControllingunitId = this.MdcControllingunitId;
          entity.MdcControllingunitCode = this.MdcControllingunitCode;
          entity.MdcControllingunitDescription = this.MdcControllingunitDescription;
          entity.BusinesspartnerId = this.BusinesspartnerId;
          entity.BusinesspartnerDescription = this.BusinesspartnerDescription;
          entity.SubsidiaryId = this.SubsidiaryId;
          entity.SubsidiaryDescription = this.SubsidiaryDescription;
          entity.SupplierId = this.SupplierId;
          entity.SupplierCode = this.SupplierCode;
          entity.SupplierDescription = this.SupplierDescription;
          entity.ContactId = this.ContactId;
          entity.Businesspartner2Id = this.Businesspartner2Id;
          entity.Businesspartner2Description = this.Businesspartner2Description;
          entity.Subsidiary2Id = this.Subsidiary2Id;
          entity.Subsidiary2Description = this.Subsidiary2Description;
          entity.Supplier2Id = this.Supplier2Id;
          entity.Supplier2Code = this.Supplier2Code;
          entity.Supplier2Description = this.Supplier2Description;
          entity.Contact2Id = this.Contact2Id;
          entity.PrcIncotermId = this.PrcIncotermId;
          entity.PrcIncotermDescription = this.PrcIncotermDescription;
          entity.CompanyInvoiceId = this.CompanyInvoiceId;
          entity.CompanyInvoiceCode = this.CompanyInvoiceCode;
          entity.AddressId = this.AddressId;
          entity.AddressDescription = this.AddressDescription;
          entity.MdcBillingSchemaId = this.MdcBillingSchemaId;
          entity.MdcBillingSchemaDescription = this.MdcBillingSchemaDescription;
          entity.BusinesspartnerAgentId = this.BusinesspartnerAgentId;
          entity.BusinesspartnerAgentDescription = this.BusinesspartnerAgentDescription;
          entity.PrcPackage2headerId = this.PrcPackage2headerId;
          entity.PrcPackage2headerDescription = this.PrcPackage2headerDescription;
          entity.CodeQuotation = this.CodeQuotation;
          entity.DateQuotation = this.DateQuotation;
          entity.Remark = this.Remark;
          entity.UserDefined1 = this.UserDefined1;
          entity.UserDefined2 = this.UserDefined2;
          entity.UserDefined3 = this.UserDefined3;
          entity.UserDefined4 = this.UserDefined4;
          entity.UserDefined5 = this.UserDefined5;
          entity.ConfirmationCode = this.ConfirmationCode;
          entity.ConfirmationDate = this.ConfirmationDate;
          entity.ExternalCode = this.ExternalCode;
          entity.InsertedAt = this.InsertedAt;
          entity.InsertedBy = this.InsertedBy;
          entity.UpdatedAt = this.UpdatedAt;
          entity.UpdatedBy = this.UpdatedBy;
          entity.Version = this.Version;
          entity.PaymentTermAdId = this.PaymentTermAdId;
          entity.PaymentTermAdCode = this.PaymentTermAdCode;
          entity.PaymentTermAdDescription = this.PaymentTermAdDescription;
          entity.PrcCopyModeId = this.PrcCopyModeId;
          entity.PrcCopymodeDescription = this.PrcCopymodeDescription;
          entity.DateEffective = this.DateEffective;
          entity.DatePenalty = this.DatePenalty;
          entity.PenaltyPercentperday = this.PenaltyPercentperday;
          entity.PenaltyPercentmax = this.PenaltyPercentmax;
          entity.PenaltyComment = this.PenaltyComment;
          entity.VatGroupId = this.VatGroupId;
          entity.VatGroupDescription = this.VatGroupDescription;
          entity.ProvingPeriod = this.ProvingPeriod;
          entity.ProvingDealdline = this.ProvingDealdline;
          entity.ApprovalPeriod = this.ApprovalPeriod;
          entity.ApprovalDealdline = this.ApprovalDealdline;
          entity.Isfreeitemsallowed = this.Isfreeitemsallowed;
          entity.BankId = this.BankId;
          entity.MdcPriceListId = this.MdcPriceListId;
          entity.MdcPriceListDescription = this.MdcPriceListDescription;
          entity.QtnHeaderId = this.QtnHeaderId;
          entity.QtnHeaderCode = this.QtnHeaderCode;
          entity.QtnHeaderDescription = this.QtnHeaderDescription;
          entity.LanguageId = this.LanguageId;
          entity.BasAccassignBusinessId = this.BasAccassignBusinessId;
          entity.BasAccassignBusinessDescription = this.BasAccassignBusinessDescription;
          entity.BasAccassignControlId = this.BasAccassignControlId;
          entity.BasAccassignControlDescription = this.BasAccassignControlDescription;
          entity.BasAccassignAccountId = this.BasAccassignAccountId;
          entity.BasAccassignAccountDescription = this.BasAccassignAccountDescription;
          entity.BasAccassignConTypeId = this.BasAccassignConTypeId;
          entity.BasAccassignConTypeDescription = this.BasAccassignConTypeDescription;
          entity.ExecutionStart = this.ExecutionStart;
          entity.ExecutionEnd = this.ExecutionEnd;
          entity.OrdHeaderId = this.OrdHeaderId;
          entity.OrdHeaderCode = this.OrdHeaderCode;
          entity.OrdHeaderDescription = this.OrdHeaderDescription;
          entity.OverallDiscount = this.OverallDiscount;
          entity.OverallDiscountOc = this.OverallDiscountOc;
          entity.OverallDiscountPercent = this.OverallDiscountPercent;
          entity.SalesTaxMethodId = this.SalesTaxMethodId;
          entity.SalesTaxMethodDesc = this.SalesTaxMethodDesc;
          entity.BoqWicCatId = this.BoqWicCatId;
          entity.BoqWicCatCode = this.BoqWicCatCode;
          entity.BoqWicCatBoqId = this.BoqWicCatBoqId;
          entity.BoqWicCatBoqReference = this.BoqWicCatBoqReference;
          entity.ReqHeaderId = this.ReqHeaderId;
          entity.ValidFrom = this.ValidFrom;
          entity.ValidTo = this.ValidTo;
          entity.BaselineUpdate = this.BaselineUpdate;
          entity.StructureDes = this.StructureDes;
          entity.StructureCode = this.StructureCode;
          entity.StructureId = this.StructureId;
          entity.IsFramework = this.IsFramework;
          entity.BasLanguageFk = this.BasLanguageFk;
          entity.BasLanguageDesc = this.BasLanguageDesc;

            // call partial method if implemented
            OnCopy(entity);

          return entity;
        }

		/// <summary> prototypes for partial OnCopy Method </summary>
		/// <param name="entity"></param>
        partial void OnCopy(ConHeaderApiEntity entity);


		/// <summary> prototypes for partial OnConstruct Method </summary>
		/// <param name="entity"></param>
        partial void OnConstruct(ConHeaderApiEntity entity);
    }

}
