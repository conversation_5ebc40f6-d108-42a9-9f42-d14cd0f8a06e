import { _common, _estimatePage, _validate, _mainView, _boqPage, _bidPage, _saleContractPage, _modalView, _salesPage, _wipPage, _package, _procurementConfig, _procurementPage, _controllingUnit, _projectPage, _rfqPage, _materialPage, _ticketSystemPage, _commonAPI } from "cypress/pages";
import { app, tile, cnt, commonLocators, sidebar, btn } from "cypress/locators";
import { CONTROLLING_UNIT_DESCRIPTION } from "cypress/pages/variables";
import { DataCells } from "cypress/pages/interfaces";
import apiConstantData from "cypress/constantData/apiConstantData";
import CommonLocators from "cypress/locators/common-locators";


let CONTAINERS_CONTROLLING_UNIT
let CONTROLLING_UNIT_PARAMETERS: DataCells

let REQUISITION_PARAMETERS: DataCells
let MODAL_PROJECTS

let CLERK_PARAMETER: DataCells
let CONTAINER_COLUMNS_COMPANIES
let CONTAINER_COLUMNS_CLERKS

let CONTAINERS_MATERIAL

let CONTAINER_COLUMNS_REQUISITION


describe("PCM- 2.105 | Assign Controlling Unit", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });

  before(function () {
    cy.fixture("pcm/pcm-2.105-assign-controlling-unit.json")
      .then((data) => {
        this.data = data;
        MODAL_PROJECTS = this.data.MODAL.PROJECTS
        CONTAINERS_CONTROLLING_UNIT = this.data.CONTAINERS.CONTROLLING_UNIT
       
        CONTAINER_COLUMNS_COMPANIES = this.data.CONTAINER_COLUMNS.COMPANIES
        CONTAINER_COLUMNS_CLERKS = this.data.CONTAINER_COLUMNS.CLERKS

        CONTAINERS_MATERIAL = this.data.CONTAINERS.MATERIAL

        CONTAINER_COLUMNS_REQUISITION = this.data.CONTAINER_COLUMNS.REQUISITION
      })
      .then(() => {
        cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        _common.openDesktopTile(tile.DesktopTiles.PROJECT);
        cy.WaitUntilLoaderComplete_Trial();
        _common.openTab(app.TabBar.PROJECT).then(() => {
          _common.setDefaultView(app.TabBar.PROJECT);
          cy.WaitUntilLoaderComplete_Trial();
          _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _commonAPI.getAccessToken().then((result) => {
          cy.log(`Token Retrieved: ${result.token}`);
        });
      });
  })
  after(() => {
    cy.LOGOUT();
  });

  it('TC - API: Create project', function () {
    _commonAPI.createProject().then(() => {
      _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
      _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
    });
  });
  it("TC - Assign logged-in user a clerk", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.COMPANY);

    cy.WaitUntilLoaderComplete_Trial();
    cy.REFRESH_CONTAINER()
    cy.WaitUntilLoaderComplete_Trial();

    _common.openTab(app.TabBar.COMPANY).then(() => {
      _common.setDefaultView(app.TabBar.COMPANY)
      cy.WaitUntilLoaderComplete_Trial();
      _common.select_tabFromFooter(cnt.uuid.COMPANIES, app.FooterTab.COMPANIES, 0);
      _common.setup_gridLayout(cnt.uuid.COMPANIES, CONTAINER_COLUMNS_COMPANIES)
    });
    cy.WaitUntilLoaderComplete_Trial();
    cy.REFRESH_CONTAINER()
    cy.WaitUntilLoaderComplete_Trial();
    _common.maximizeContainer(cnt.uuid.COMPANIES)
    _common.search_inSubContainer(cnt.uuid.COMPANIES, MODAL_PROJECTS.COMPANY_NAME)
    _common.clickOn_cellHasUniqueValue(cnt.uuid.COMPANIES, app.GridCells.COMPANY_NAME_SMALL, MODAL_PROJECTS.COMPANY_NAME)
    _common.clickOn_goToButton_toSelectModule(cnt.uuid.COMPANIES, commonLocators.CommonKeys.CLERK)
    cy.WaitUntilLoaderComplete_Trial();
    cy.REFRESH_CONTAINER()
    cy.WaitUntilLoaderComplete_Trial();
    _common.openTab(app.TabBar.CLERK).then(() => {
      _common.setDefaultView(app.TabBar.CLERK)
      cy.WaitUntilLoaderComplete_Trial();
      _common.select_tabFromFooter(cnt.uuid.CLERKS, app.FooterTab.CLERKS, 0);
      _common.setup_gridLayout(cnt.uuid.CLERKS, CONTAINER_COLUMNS_CLERKS)
    });
    cy.WaitUntilLoaderComplete_Trial();
    cy.REFRESH_CONTAINER()
    cy.WaitUntilLoaderComplete_Trial();
    _common.maximizeContainer(cnt.uuid.CLERKS)
    _common.assign_clerkForLoggedInUser(cnt.uuid.CLERKS, CLERK_PARAMETER)
    cy.WaitUntilLoaderComplete_Trial();
  });

  it('TC - API: Create controlling units', function () {
    CONTROLLING_UNIT_PARAMETERS = {
      [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTROLLING_UNIT.QUANTITY],
      [app.GridCells.UOM_FK]: [apiConstantData.ID.UOM_BAGS]
    }
    _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 3, CONTROLLING_UNIT_PARAMETERS)
  })

  it("TC - Go to Ticket System Clear the Cart and Add item to the Cart", function () {
    _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
    _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.TICKET_SYSTEM)
    cy.WaitUntilLoaderComplete_Trial();

    _common.openTab(app.TabBar.CART).then(() => {
      _common.select_tabFromFooter(cnt.uuid.CART_ITEMS, app.FooterTab.CART_ITEMS)
    })
    _ticketSystemPage.deleteRecord_toClearItemsInCart(cnt.uuid.CART_ITEMS)

    _common.openTab(app.TabBar.SEARCH).then(() => {
      _common.select_tabFromFooter(cnt.uuid.COMMODITY_SEARCH, app.FooterTab.COMMODITY_SEARCH)
    })
    _ticketSystemPage.enterRecord_toAddMaterialInCart(cnt.uuid.COMMODITY_SEARCH, [CONTAINERS_MATERIAL.CODE], [CONTAINERS_MATERIAL.QUANTITY])
  })

  it("TC - Go to Cart and Create Requisition for Added Material and verify controlling unit lookup", function () {
    let REQUISITION_PARAMETERS = {
      [commonLocators.CommonKeys.LABEL]: CommonLocators.CommonLabels.CONTROLLING_UNIT,
      [commonLocators.CommonKeys.INPUT_CLASS]: app.InputFields.INPUT_GROUP_CONTENT,
      [commonLocators.CommonKeys.POPUP_TYPE]: CommonLocators.CommonKeys.GRID,
      [CommonLocators.CommonKeys.VALUE]: Cypress.env('API_CNT_CODE_0')
    }
    _common.openTab(app.TabBar.CART).then(() => {
      _common.select_tabFromFooter(cnt.uuid.CART_ITEMS, app.FooterTab.CART_ITEMS)
    })
    _ticketSystemPage.enterRecord_toPlaceOrderForRequisitionFromCart(cnt.uuid.CART_ITEMS)
    cy.WaitUntilLoaderComplete_Trial();
    _common.edit_inputFieldWithDropdown_fromModal(REQUISITION_PARAMETERS)
    _common.clickOn_modalFooterButton(btn.ButtonText.OK);
    _common.clickOn_modalFooterButton(btn.ButtonText.REQUISITION);

    cy.WaitUntilLoaderComplete_Trial();
    _common.openTab(app.TabBar.MAIN).then(() => {
      _common.select_tabFromFooter(cnt.uuid.REQUISITIONS, app.FooterTab.REQUISITIONS)
      _common.setup_gridLayout(cnt.uuid.REQUISITIONS, CONTAINER_COLUMNS_REQUISITION)
    })
    _common.clear_subContainerFilter(cnt.uuid.REQUISITIONS)
    _common.maximizeContainer(cnt.uuid.REQUISITIONS)
    _common.assert_cellData_insideActiveRow(cnt.uuid.REQUISITIONS, app.GridCells.TRANSLATED, Cypress.env('API_CNT_CODE_0'), CONTROLLING_UNIT_DESCRIPTION)
  })

})