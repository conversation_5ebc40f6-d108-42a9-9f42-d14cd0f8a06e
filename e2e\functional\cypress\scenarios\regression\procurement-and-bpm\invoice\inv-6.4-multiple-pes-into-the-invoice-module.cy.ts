import apiConstantData from "cypress/constantData/apiConstantData";
import { commonLocators, sidebar, app, cnt, tile, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _boqPage, _common, _commonAPI, _controllingUnit, _estimatePage, _package, _procurementContractPage, _procurementPage, _projectPage, _validate } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";

const INVOICE_NO = _common.generateRandomString(5)

let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let API_CONTROLLING_UNIT_PARAMETERS
let CONTAINERS_CONTRACT, CONTAINER_COLUMNS_PES_ITEM;

let CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_CONTRACT_ITEMS, CONTAINERS_ITEM;
let CONTAINERS_PERFORMANCE_ENTRY_SHEET, CONTAINER_COLUMNS_HEADERS
describe("PCM- 6.4 | Multiple pes into the invoice module", () => {

    before(function () {
        cy.fixture("pcm/inv-6.4-multiple-pes-into-the-invoice-module.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINERS_ITEM = this.data.CONTAINERS.ITEM
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            CONTAINER_COLUMNS_PES_ITEM = this.data.CONTAINER_COLUMNS.PES_ITEM
            CONTAINER_COLUMNS_CONTRACT_ITEMS = this.data.CONTAINER_COLUMNS.CONTRACT_ITEMS

            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }
            CONTAINER_COLUMNS_HEADERS = this.data.CONTAINER_COLUMNS.HEADERS
            CONTAINERS_PERFORMANCE_ENTRY_SHEET = this.data.CONTAINERS.PERFORMANCE_ENTRY_SHEET
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
            _common.openDesktopTile(tile.DesktopTiles.PROJECT);
            _common.waitForLoaderToDisappear();
            _common.openTab(app.TabBar.PROJECT).then(() => {
                _common.setDefaultView(app.TabBar.PROJECT);
                _common.waitForLoaderToDisappear();
                _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
            });
            _commonAPI.getAccessToken().then((result) => {
                cy.log(`Token Retrieved: ${result.token}`);
            });
        });
    });

    after(() => {
        cy.LOGOUT();
    });

    it('TC - API: Create project and location', function () {
        _commonAPI.createProject().then(() => {
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
            _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        });

    });

    it("TC - API: Create controlling unit", function () {
        API_CONTROLLING_UNIT_PARAMETERS = {
            [app.GridCells.QUANTITY_SMALL]: [CONTAINERS_CONTRACT.QUANTITY1, CONTAINERS_CONTRACT.QUANTITY1],
            [app.GridCells.BAS_UOM_FK]: [apiConstantData.ID.UOM_BAGS, apiConstantData.ID.UOM_BAGS]
        }
        _commonAPI.createControllingUnit(Cypress.env('API_PROJECT_ID_1'), 2, API_CONTROLLING_UNIT_PARAMETERS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTROLLING_UNITS)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1')).pinnedItem();
        _common.openTab(app.TabBar.CONTROLLINGSTRUCTURE).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTROLLING_UNIT, app.FooterTab.CONTROLLING_UNITS, 1);
        }).then(() => {
            cy.log(Cypress.env(`API_CNT_CODE_0`))
            cy.log(Cypress.env(`API_CNT_ID_0`))
        })
        _common.clickOn_expandCollapseButton(cnt.uuid.CONTROLLING_UNIT, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowInContainer(cnt.uuid.CONTROLLING_UNIT)
        _common.saveCellDataToEnv(cnt.uuid.CONTROLLING_UNIT, app.GridCells.CODE, "CU_CODE")

    })

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT)
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_3)
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0)
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
        })
        cy.log(Cypress.env(`API_CNT_CODE_0`))
        cy.log(Cypress.env(`API_CNT_ID_0`))
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('API_PROJECT_NUMBER_1'))
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal_byClass(PROCUREMENT_CONTRACT_PARAMETER)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CONTROLLING_UNIT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env(`CU_CODE`))
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, "CONTRACT_CODE")
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CONTRACT_STATUS);
        _common.changeStatus_fromModal(CommonLocators.CommonKeys.APPROVED)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMSCONTRACT, app.FooterTab.ITEMS, 2);
            _common.setup_gridLayout(cnt.uuid.ITEMSCONTRACT, CONTAINER_COLUMNS_CONTRACT_ITEMS)
        });
        _common.clear_subContainerFilter(cnt.uuid.ITEMSCONTRACT)
        _common.waitForLoaderToDisappear()
        _common.maximizeContainer(cnt.uuid.ITEMSCONTRACT)
        _common.create_newRecord(cnt.uuid.ITEMSCONTRACT)
        _common.edit_dropdownCellWithInput(cnt.uuid.ITEMSCONTRACT, app.GridCells.MDC_MATERIAL_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEM.MATERIALCODE)
        _common.edit_containerCell(cnt.uuid.ITEMSCONTRACT, app.GridCells.QUANTITY_SMALL, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_ITEM.QUANTITY)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_validationModalFooterButton_ifExists(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMSCONTRACT)

    });

    it("TC- Create PES from contract", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        })
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _procurementPage.getCode_fromPESModal("PES_CODE1")
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.YES)
        _common.waitForLoaderToDisappear()
        _procurementPage.getCode_fromPESModal("PES_CODE2")
        _common.clickOn_modalFooterButton(btn.ButtonText.CLOSE)
        _common.waitForLoaderToDisappear()
        
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_PES)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.YES)
        _common.waitForLoaderToDisappear()
        _procurementPage.getCode_fromPESModal("PES_CODE3")
        _common.clickOn_modalFooterButton(btn.ButtonText.GO_TO_PES)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
      
    })

    it("TC - Update PES items quantity", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        cy.REFRESH_CONTAINER()
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.setDefaultView(app.TabBar.PERFORMANCEENTRYSHEET)
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS)
            _common.setup_gridLayout(cnt.uuid.HEADERS, CONTAINER_COLUMNS_HEADERS)
            _common.set_columnAtTop([CONTAINER_COLUMNS_HEADERS.pesvalue, CONTAINER_COLUMNS_HEADERS.code], cnt.uuid.HEADERS)
        })
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS)
            _common.setup_gridLayout(cnt.uuid.ITEMS, CONTAINER_COLUMNS_PES_ITEM)
            _common.set_columnAtTop([CONTAINER_COLUMNS_PES_ITEM.quantity, CONTAINER_COLUMNS_PES_ITEM.total], cnt.uuid.ITEMS)
        })

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS);
            _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        })
        _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE1"))

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS);
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS);
        })
        _common.maximizeContainer(cnt.uuid.ITEMS)
        _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.enterRecord_inNewRow(cnt.uuid.ITEMS, app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PERFORMANCE_ENTRY_SHEET.QUANTITY[0])
        _common.select_activeRowInContainer(cnt.uuid.ITEMS)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMS)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS);
            _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        })
        _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE1"))
        _common.saveCellDataToEnv(cnt.uuid.HEADERS, app.GridCells.PES_VALUE, "PES_VALUE1")
        _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE2"))

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS);
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS);
        })
        _common.maximizeContainer(cnt.uuid.ITEMS)
        _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.enterRecord_inNewRow(cnt.uuid.ITEMS, app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PERFORMANCE_ENTRY_SHEET.QUANTITY[1])
        _common.select_activeRowInContainer(cnt.uuid.ITEMS)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMS)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS);
            _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        })
        _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE2"))
        _common.saveCellDataToEnv(cnt.uuid.HEADERS, app.GridCells.PES_VALUE, "PES_VALUE2")
        _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE3"))

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS);
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.ITEMS, app.FooterTab.ITEMS);
        })
        _common.maximizeContainer(cnt.uuid.ITEMS)
        _common.clear_subContainerFilter(cnt.uuid.ITEMS)
        _common.select_rowInSubContainer(cnt.uuid.ITEMS)
        _common.enterRecord_inNewRow(cnt.uuid.ITEMS, app.GridCells.QUANTITY_SMALL,app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_PERFORMANCE_ENTRY_SHEET.QUANTITY[2])
        _common.select_activeRowInContainer(cnt.uuid.ITEMS)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.ITEMS)

        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS);
        })
        _common.clear_subContainerFilter(cnt.uuid.HEADERS)
        _common.select_rowHasValue(cnt.uuid.HEADERS, Cypress.env("PES_CODE3"))
        _common.saveCellDataToEnv(cnt.uuid.HEADERS, app.GridCells.PES_VALUE, "PES_VALUE3")
    })

    it('TC - Create invoice and assert record', function () {
        _common.openTab(app.TabBar.PERFORMANCEENTRYSHEET).then(() => {
            _common.select_tabFromFooter(cnt.uuid.HEADERS, app.FooterTab.HEADERS);
        })
        _common.select_allContainerData(cnt.uuid.HEADERS)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CREATE_INVOICE);
        _common.waitForLoaderToDisappear()
        _package.enterRecord_toCreate_Invoice_FromWizard(CONTAINERS_PERFORMANCE_ENTRY_SHEET.LABEL, INVOICE_NO);
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar()
        _common.openTab(app.TabBar.INVOICES).then(() => {
			_common.select_tabFromFooter(cnt.uuid.INVOICEHEADER, app.FooterTab.INVOICEHEADER);
            _common.clear_subContainerFilter(cnt.uuid.INVOICEHEADER)
		});
        _common.select_rowInSubContainer(cnt.uuid.INVOICEHEADER)

        _common.openTab(app.TabBar.INVOICES).then(() => {
			_common.select_tabFromFooter(cnt.uuid.INVOICERECONSILIATION, app.FooterTab.RECONCILLIATION);
		});
		_common.clickOn_cellHasUniqueValue(cnt.uuid.INVOICERECONSILIATION, app.GridCells.RECON_NAME, CONTAINERS_PERFORMANCE_ENTRY_SHEET.VALUE)
        _validate.verify_additionOfThreeValues(Cypress.env("PES_VALUE1"), Cypress.env("PES_VALUE2"), Cypress.env("PES_VALUE3"))
		
    });
});