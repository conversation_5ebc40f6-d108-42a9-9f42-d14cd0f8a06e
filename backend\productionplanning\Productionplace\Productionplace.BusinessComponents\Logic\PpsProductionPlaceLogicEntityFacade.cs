﻿using System;
using System.Collections.Generic;
using System.ComponentModel.Composition;
using System.Linq;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Core.Core;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;

namespace RIB.Visual.ProductionPlanning.ProductionPlace.BusinessComponents.Logic
{
    /// <summary>
    /// Class for PpsProductionPlaceLogic implementing ICreateEntityFacade with dictionary-based entity access and manipulation.
    /// </summary>
    [Export(typeof(IEntityFacade))]
    public class PpsProductionPlaceLogicEntityFacade : ICreateEntityFacade
    {
	     private readonly PpsProductionPlaceLogic logic = new PpsProductionPlaceLogic();
        private static ConvertProperties _entityProperties = new ConvertProperties()
              .Add("Id", true)
              .Add("Code")
              .Add("Description")
              .Add("Sorting")
              .Add("PpsProdPlaceTypeFk")
              .Add("ResResourceFk")
              .Add("BasSiteFk")
              .Add("PositionX")
              .Add("PositionY")
              .Add("PositionZ")
              .Add("Length")
              .Add("BasUomLengthFk")
              .Add("Width")
              .Add("BasUomWidthFk")
              .Add("Height")
              .Add("BasUomHeightFk")
              .Add("IsLive")
              ;


        /// <summary>
        /// Saves the entity represented by the given dictionary and returns the updated entity as a dictionary.
        /// </summary>
        /// <param name="entityDictionary">Dictionary containing entity properties and values.</param>
        /// <returns>Dictionary of the saved entity's properties and values.</returns>
        IDictionary<String, Object> IEntityFacade.Save(IDictionary<String, Object> entityDictionary)
        {
            var id = entityDictionary.GetId<int>();
            
            var entity = logic.GetCoresByIds(new IdentificationData() { Id = id }.Obj2Array()).FirstOrDefault();
            entity.SetObject(entityDictionary, _entityProperties);
            return logic.Save(entity).AsDictionary(_entityProperties);
        }

        /// <summary>
        /// Gets the entity by its ID and returns its properties as a dictionary.
        /// </summary>
        /// <param name="id">The ID of the entity.</param>
        /// <returns>Dictionary of the entity's properties and values, or null if not found.</returns>
        IDictionary<String, Object> IEntityFacade.Get(int id)
        {
            var entity = logic.GetCoresByIds(new Platform.Core.IdentificationData() { Id = id }.Obj2Array()).FirstOrDefault();
            if (entity == null)
            {
                return null;
            }
            var objectDic = entity.AsDictionary(_entityProperties);
            return objectDic;
        }

        /// <summary>
        /// Gets the permission descriptor ID for the production place entity.
        /// </summary>
        string IEntityFacade.Id
        {
            get { return PermissionDescriptors.ProductionPlace; }
        }

        /// <summary>
        /// Gets the name of the entity type.
        /// </summary>
        string IEntityFacade.Name
        {
            get { return "PpsProductionPlaceEntity"; }
        }

        /// <summary>
        /// Gets the module name for the production place entity.
        /// </summary>
        string IEntityFacade.ModuleName
        {
            get { return "productionplanning.productionplace"; }
        }

        /// <summary>
        /// Gets the list of property names for the production place entity.
        /// </summary>
        string[] IEntityFacade.Properties
        {
            get
            {
                return _entityProperties.GetPropertyNames();
            }
        }

        /// <summary>
        /// Creates a new entity and returns its properties as a dictionary.
        /// </summary>
        /// <returns>Dictionary of the new entity's properties and values.</returns>
        IDictionary<String, Object> ICreateEntityFacade.Create()
        {
            var entity = logic.Create(new IdentificationData { });
            var objectDic = entity.AsDictionary(_entityProperties);

            return objectDic;
        }
    }
}
