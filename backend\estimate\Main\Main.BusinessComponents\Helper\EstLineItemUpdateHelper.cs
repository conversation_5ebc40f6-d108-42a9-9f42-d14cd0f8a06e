using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RIB.Visual.Estimate.Rule.BusinessComponents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;
using System.Net.Mime;
using Application = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RIB.Visual.Platform.Core;
using RVPBizComp = RIB.Visual.Platform.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Platform.BusinessComponents;

namespace RIB.Visual.Estimate.Main.BusinessComponents
{
	/// <summary>
	///
	/// </summary>
	public class EstLineItemUpdateHelper: EstimateScopeService
	{
		private readonly object _Lock = new object();

		private EstLineItemUpdateOption _EstLineItemUpdateOption = new EstLineItemUpdateOption();

		private int _lgmJobFk = 0;

		private bool _isBudgetCalByBidImport = false;

		private IEstParameterVisitor _EstParameterVisitor;

		private EstLineItemUpdateFrmPrjLogic _EstLineItemUpdateFrmPrjLogic;

		/// <summary>
		///
		/// </summary>
		/// <param name="estHeaderId"></param>
		/// <param name="projectId"></param>
		/// <param name="lineItemUpdateOption"></param>
		/// <param name="estimateScopeObject"></param>
		public EstLineItemUpdateHelper(int estHeaderId, int? projectId, EstLineItemUpdateOption lineItemUpdateOption, EstimateScopeObject estimateScopeObject = null)
			:base(estHeaderId, projectId, estimateScopeObject)
		{
			if (lineItemUpdateOption != null)
			{
				this._EstLineItemUpdateOption = lineItemUpdateOption;
			}

			this._EstLineItemUpdateFrmPrjLogic = new EstLineItemUpdateFrmPrjLogic(estHeaderId, projectId, new EstResourceUpdateOption()
			{
				IsUpdateCostCode = this._EstLineItemUpdateOption.IsUpdateCostCode,
				IsUpdateMaterial = this._EstLineItemUpdateOption.IsUpdateMaterial,
				IsUpdateAssembly = this._EstLineItemUpdateOption.IsUpdateAssembly,
				IsUpdatePlantAssembly = this._EstLineItemUpdateOption.IsUpdatePlantAssembly,
				IsUpdateCostType = this._EstLineItemUpdateOption.IsUpdCostType,
				ConsiderIsRate = this._EstLineItemUpdateOption.ConsiderIsRate,
				IsCompulsionIsRate = this._EstLineItemUpdateOption.IsCompulsionIsRate
			}, GetEstimateScopeObject());
		}

		private static void ApplyThreadContext(IContext context)
		{
			if (context != Application.BusinessEnvironment.CurrentContext)
			{
				Application.BusinessEnvironment.CurrentContext = context;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lgmJobFk"></param>
		/// <returns></returns>
		public EstLineItemUpdateHelper SetLgmJobk(int lgmJobFk)
		{
			this._lgmJobFk = lgmJobFk;

			return this;
		}


		/// <summary>
		///
		/// </summary>
		/// <param name="projectFk"></param>
		/// <returns></returns>
		public EstLineItemUpdateHelper SetProjectFk(int projectFk)
		{
			this._ProjectId = projectFk;

			return this;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="isBudgetCal"></param>
		/// <returns></returns>
		public EstLineItemUpdateHelper SetIsBudgetCalByBidImport(bool isBudgetCal)
		{
			this._isBudgetCalByBidImport = isBudgetCal;

			return this;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="estParameterVisitor"></param>
		/// <returns></returns>
		public EstLineItemUpdateHelper SetEstParameterVistor(IEstParameterVisitor estParameterVisitor)
		{
			this._EstParameterVisitor = estParameterVisitor;

			return this;
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		private IEstParameterVisitor GetEstParameterVisitor()
		{
			if (this._EstParameterVisitor == null)
			{
				lock (_Lock)
				{
					if (this._EstParameterVisitor == null)
					{
						this._EstParameterVisitor = new EstParameterVisitor(this._EstHeaderId, this._ProjectId, this._EstLineItemUpdateOption.IsAssembly, this._EstLineItemUpdateOption.IsPrjAssembly).RegisterParameterProviderByConfig();
					}
				}
			}

			return this._EstParameterVisitor;
		}

		/// <summary>
		/// calculate single lineItem, and it will affect all reference lineItem
		/// </summary>
		/// <param name="lineItem">lineItem to calculate</param>
		/// <param name="lineItems">lineItems of header</param>
		/// <param name="existParameters">existint Parameters</param>
		public void CalculateLineItemBase(IScriptEstLineItem lineItem, IEnumerable<IScriptEstLineItem> lineItems, Dictionary<string, decimal> existParameters = null)
		{
			/* update resource with project costcode and project material, and get the exchange rate*/
			if (this._EstLineItemUpdateOption.IsUpdateRate)
			{
				/* attach userDefinedVal to LineItem */
				new EstLineItemAttachmentInitializer(this._EstHeaderId, new List<int>() { lineItem.Id }).AttachUDPEntityToLineItems(new List<IScriptEstLineItem>() { lineItem});

				/* attach userDefinedVal to resource */
				if(lineItem.Resources != null && lineItem.Resources.Any())
				{
					var resources = lineItem.Resources.Flatten(e => e.Resources).Distinct().ToList();

					new EstResourceAttachmentInitializer(this._EstHeaderId, new List<int>() { lineItem.Id }).AttachUDPEntityToResources(resources);
				}

				this._EstLineItemUpdateFrmPrjLogic.UpdateRateFromProject(lineItem.Resources, lineItem);
			}

			/* load latest exchange rates */
			this.GetExchangeRateHelper().ExchangeRate(lineItem.Resources);

			/* convert some special char in detail by culture */
			if (this._EstLineItemUpdateOption.NormalizeResourceDetail)
			{
				EstCultureHelper.SetResourceDetailByCulture(lineItem.Resources);
			}

			/* calculate all detail formula for line item and quantity */
			if (this._EstLineItemUpdateOption.IsCalculateDetail)
			{
				new EstDetailCalculateHelper(this._EstHeaderId, this._ProjectId).CalculateLineItemAndResourceDetails(lineItem, lineItem.Resources, this.GetEstParameterVisitor().GetParameterValuesByLineItem);
			}

			/* if IsSplitQuantityUpdate == true, it means split quantity has been changed, it need to update to lineItem. if not, it will update from lineItem */
			/* update split quantity of lineitems */
			if (this._EstLineItemUpdateOption.IsUpdateSplitQuantity)
			{
				UpdateSplitQuantity(new List<EstLineItemEntity>() { lineItem as EstLineItemEntity }, this.GetEstParameterVisitor().GetParameterValuesByLineItem);
			}

			EstimateCalculatorService.Calculate(new EstimateCalculationOption(this._EstHeaderId, this._ProjectId), new List<EstLineItemEntity>() { lineItem  as EstLineItemEntity }, false, lineItems);

			/* save lineItems and resources */
			if (this._EstLineItemUpdateOption.ToSave)
			{
				using (var transactionScope = TransactionScopeFactory.Create())
				{
					new EstimateMainLineItemLogic().Save(lineItem);

					if (!lineItem.EstLineItemFk.HasValue && lineItem.Resources != null && lineItem.Resources.Any())
					{
						var resourcesToSave = lineItem.Resources.Flatten(e => e.Resources).Select(e => (EstResourceEntity)e).Distinct().ToList();

						new EstimateMainResourceLogic().SaveResources(resourcesToSave);

						var udpsToSave = resourcesToSave.Where(e => e.UserDefinedcolValEntity != null).Select(e => e.UserDefinedcolValEntity).OfType<UserDefinedcolValEntity>().ToList();

						if (udpsToSave.Any())
						{
							var updsToCreate = udpsToSave.Where(e => e.Id <= 0).ToList();

							var updsToUpdate = udpsToSave.Where(e => e.Id > 0).ToList();

							if (updsToCreate.Any())
							{
								var newIds = new Queue<int>(new UserDefinedColumnValueLogic().GetNextIds(updsToCreate.Count()));

								foreach (var item in updsToCreate)
								{
									item.Id = newIds.Dequeue();
								}
							}

							new UserDefinedColumnValueLogic().BulkUpdate(new UserDefinedColumnValueComplete()
							{
								UserDefinedColumnValueToCreate = updsToCreate,
								UserDefinedColumnValueToUpdate = updsToUpdate
							});
						}
					}

					transactionScope.Complete();
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		public IEnumerable<EstLineItemEntity> CalculateLineItemsOfEstimate(IEnumerable<EstLineItemEntity> lineItems)
		{
			if (lineItems == null || !lineItems.Any())
			{
				return lineItems;
			}

			// if the Resources is null will take from lineitem.EstResourceEntities,see the definition of the lineitem.Resources
			var _resources = lineItems.Where(e => e.Resources != null && e.Resources.Any()).SelectMany(e => e.Resources).ToList();

			var resources = _resources.Select(e => e as EstResourceEntity).ToList();

			/* update assembly */
			UpdateAssembly(lineItems, resources);

			/* update properties */
			UpdateLineItemAndResourceProperties(lineItems, resources);

			var calculationContext = EstimateCalculatorService.Calculate(new EstimateCalculationOption(this._EstHeaderId, this._ProjectId), lineItems, false, lineItems);

			//calculationContext.SaveModifiedUDP();

			return lineItems;
		}

		/// <summary>
		/// update lineItem and resource propertied from project cost code and project material, load the lasted exchange rate and calculate the detail property
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="isTreeStructure"></param>
		/// <returns></returns>
		public EstCalculationContext CalculateLineItems(IEnumerable<EstLineItemEntity> lineItems, bool isTreeStructure = true)
		{
			if (lineItems == null || !lineItems.Any())
			{
				return new EstCalculationContext(new EstimateCalculationOption(this._EstHeaderId, this._ProjectId));
			}

			var resources = lineItems.Where(e => e.EstResourceEntities != null && e.EstResourceEntities.Any()).SelectMany(e => e.EstResourceEntities).ToList();

			/* update assembly */
			UpdateAssembly(lineItems, resources);

			UpdateLineItemAndResourceProperties(lineItems, resources);

			var calculationContext = EstimateCalculatorService.Calculate(new EstimateCalculationOption(this._EstHeaderId, this._ProjectId)
			{
				IsAssembly = this._EstLineItemUpdateOption.IsAssembly,
				IsTreeStructure = isTreeStructure,
			}, lineItems);

			if (this._EstLineItemUpdateOption.ToSave)
			{
				calculationContext.Save();
			}

			return calculationContext;
		}

		/// <summary>
		/// calculate lineItems of header
		/// </summary>
		/// <param name="lineItems">lineItems of header</param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> CalculateWholeEstimate(IEnumerable<EstLineItemEntity> lineItems)
		{
			if (lineItems == null || !lineItems.Any())
			{
				return lineItems;
			}

			var lineItemsFilterByJob = _lgmJobFk > 0
					 ? lineItems.Where(x => x.EstResourceEntities.Any(y => y.LgmJobFk == _lgmJobFk)).ToList()
					 : lineItems.ToList();

			if (!lineItemsFilterByJob.Any())
			{
				return lineItems;
			}

			var resources = lineItemsFilterByJob.SelectMany(e => e.EstResourceEntities).ToList();

			/* Update Resource CostUnit with button "Cost/Unit Update"
			* Only update the target related lineitems and resources
			* when IsUpdateResourceCostUnit is true, use MdcCostCodeFk, ProjectCostCodeFk or MdcMaterialFk to filter the lineitems and resources
			 */
			if (this._EstLineItemUpdateOption.IsUpdateResourceCostUnit)
			{
				var lineItemIds = FilterLineItemsByCostUnitUpdate(lineItemsFilterByJob, resources);
				lineItemsFilterByJob = lineItemsFilterByJob.Where(e => lineItemIds.Contains(e.Id)).ToList();
				resources = lineItemsFilterByJob.SelectMany(e => e.EstResourceEntities).ToList();
			}

			UpdateAssembly(lineItemsFilterByJob, resources);

			UpdatePlantAssembly(lineItemsFilterByJob, resources);

			if (this._EstLineItemUpdateOption.IsUpdateAssembly && !this._EstLineItemUpdateOption.IsAssembly)
			{
				resources = lineItemsFilterByJob.SelectMany(e => e.EstResourceEntities).ToList();
			}

			var lineItemChangedTracker = new ObjectChangedTracker<EstLineItemEntity>();

			var resourceChangedTracker = new ObjectChangedTracker<EstResourceEntity>(new ChangeTrackerOption<EstResourceEntity>()
			{
				IsTreeStructure = true,
				GetChildrenFunc = e => e.ResourceChildren
			});

			/* use to check whether the lineItems and the resources has modified */
			if (this._EstLineItemUpdateOption.ToSave)
			{
				lineItemChangedTracker.Initialize(lineItemsFilterByJob);

				resourceChangedTracker.Initialize(resources);
			}

			/* calculate quantity and cost */
			using (var estimateScope = new EstimateScope(this._EstHeaderId, this.ProjectId))
			{
				UpdateLineItemAndResourceProperties(lineItemsFilterByJob, resources);

				EstimateCalculatorService.Calculate(new EstimateCalculationOption(this._EstHeaderId, this.ProjectId), lineItemsFilterByJob, true, lineItems);

				/* save lineItems and resources */
				if (this._EstLineItemUpdateOption.ToSave)
				{
					var lineItemsToUpdate = lineItemChangedTracker.GetChangedObjects();

					var resourcesToUpdate = resourceChangedTracker.GetChangedObjects();

					// set original cost/unit or quantity
					if (this._EstLineItemUpdateOption.IsSetCostUnitOriginal)
					{
						SetOriginalCostUnitOrQuantity(resourcesToUpdate);
					}

					EstValueValidateHelper.Validate(lineItemsToUpdate);
					EstValueValidateHelper.Validate(resourcesToUpdate);

					using (var dbContext = new RVPBizComp.DbContext(ModelBuilder.DbModel))
					{
						var bulkSaveHelper = new BulkSaveHelper();
						bulkSaveHelper.BulkUpdate(dbContext, lineItemsToUpdate.ToList());
						bulkSaveHelper.BulkUpdate(dbContext, resourcesToUpdate.ToList());
					}

					estimateScope.SaveModifiedUDP();
				}

				if (!this._EstLineItemUpdateOption.IsUpdateRule)
				{
					CalculateRiskEscalation(lineItemsFilterByJob, resources, this._EstLineItemUpdateOption.IsUpdateEscalation, this._EstLineItemUpdateOption.IsUpdateRisk);
				}

				return lineItems;
			}
		}

		private List<int> FilterLineItemsByCostUnitUpdate(List<EstLineItemEntity> lineItems, List<EstResourceEntity> resources)
		{
			List<EstResourceEntity> filterResouces = resources.Flatten(e => e.ResourceChildren).ToList();

			if (this._EstLineItemUpdateOption.IsUpdateCostCode && this._EstLineItemUpdateOption.MdcCostCodeFk != null && this._EstLineItemUpdateOption.MdcCostCodeFk.HasValue)
			{
				filterResouces = filterResouces.Where(e => e.MdcCostCodeFk == this._EstLineItemUpdateOption.MdcCostCodeFk).ToList();
			}
			else if (this._EstLineItemUpdateOption.IsUpdateCostCode && this._EstLineItemUpdateOption.ProjectCostCodeFk != null && this._EstLineItemUpdateOption.ProjectCostCodeFk.HasValue)
			{
				filterResouces = filterResouces.Where(e => e.ProjectCostCodeFk == this._EstLineItemUpdateOption.ProjectCostCodeFk).ToList();
			}
			else if (this._EstLineItemUpdateOption.IsUpdateMaterial && this._EstLineItemUpdateOption.MdcMaterialFk != null && this._EstLineItemUpdateOption.MdcMaterialFk.HasValue)
			{
				filterResouces = filterResouces.Where(e => e.MdcMaterialFk == this._EstLineItemUpdateOption.MdcMaterialFk).ToList();
			}

			var lineItemIds = filterResouces.Select(e => e.EstLineItemFk).Distinct().ToList();

			var refLineItems = new List<EstLineItemEntity>();
			GetReferenceLineItems(lineItems.Where(e => e.EstLineItemFk.HasValue).ToList(), lineItemIds, refLineItems);
			if (refLineItems.Count > 0)
			{
				lineItemIds.AddRange(refLineItems.Select(e => e.Id));
			}

			return lineItemIds;
		}

		/// <summary>
		/// get reference lineitem by base lineitem ids
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="baseLineItemIds"></param>
		/// <param name="refLineItems"></param>
		private void GetReferenceLineItems(List<EstLineItemEntity> lineItems, List<int> baseLineItemIds, List<EstLineItemEntity> refLineItems)
		{
			var refItems = lineItems.Where(e => baseLineItemIds.Contains(e.EstLineItemFk.Value)).ToList();
			if (refItems.Count > 0)
			{
				refLineItems.AddRange(refItems);

				var refItemIds = refItems.Select(e => e.Id).ToList();
				GetReferenceLineItems(lineItems, refItemIds, refLineItems);
			}
		}

		/// <summary>
		/// calculate lineItems of header
		/// </summary>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> CalculateLineItemsOfHeader()
		{
			return CalculateWholeEstimate(new EstimateMainLineItemLogic().GetLineItemsOfHeader(this._EstHeaderId, _ProjectId, this._EstLineItemUpdateOption.IsAssembly, true));
		}

		/// <summary>
		/// calcualte lineItems of header
		/// </summary>
		/// <param name="lineItems">lineItems of header</param>
		/// <param name="resources">resources of header</param>
		/// <param name="projectId"></param>
		/// <param name="isTreeStructure"></param>
		/// <returns></returns>
		public IEnumerable<EstLineItemEntity> CalculateLineItems(IEnumerable<EstLineItemEntity> lineItems, IEnumerable<EstResourceEntity> resources, int? projectId, bool isTreeStructure = true)
		{
			if (lineItems == null || !lineItems.Any())
			{
				return lineItems;
			}

			if (resources == null || !resources.Any())
			{
				resources = (IEnumerable<EstResourceEntity>)new EstimateMainResourceLogic().GetResourcesOfLineItems(lineItems, projectId, false, true);
			}

			EstLineItemUtilities.AttachResourcesToLineItems(lineItems, resources);

			this.CalculateLineItems(lineItems, isTreeStructure);

			return lineItems;
		}

		/// <summary>
		/// Calculate the lineItems and resources which created by rule
		/// </summary>
		/// <param name="lineItemFks"></param>
		/// <param name="lineitems"></param>
		/// <param name="ruleApplicationContext"></param>
		/// <returns></returns>
		public bool CalculateLineItemsForRule(IEnumerable<int> lineItemFks, IEnumerable<EstLineItemEntity> lineitems, EstRuleApplicationContext ruleApplicationContext)// Dictionary<int, Dictionary<string, IRuleCommonParamEntity>> parameterDic
		{
			bool isSuccess = true;

			var lineitemsModified = lineitems.Where(e => lineItemFks.Contains(e.Id)).ToList();

			if (lineitemsModified == null || !lineitemsModified.Any())
			{
				return isSuccess;
			}

			int estHeaderId = lineitemsModified.First().EstHeaderFk;

			var resources = lineitemsModified.SelectMany(e => e.Resources).ToList();

			this.GetExchangeRateHelper().ExchangeRate(resources);

			/* create func to get parameter of lineItem */
			Func<IScriptEstLineItem, Dictionary<string, decimal>> getParametersFunc = (entity) =>
			{
				if (ruleApplicationContext.GetParameterByLineItem == null || ruleApplicationContext.GetParameterByLineItem(entity) == null)
				{
					return new Dictionary<string, decimal>();
				}

				return ruleApplicationContext.GetParameterByLineItem(entity).ToDictionary(e => e.Key, e => e.Value.ParameterValue);
			};

			/* calculate lineItems and resources */
			var estDetailCalculateHelper = new EstDetailCalculateHelper(this._EstHeaderId, ruleApplicationContext.ProjectId);

			foreach (var lineItem in lineitemsModified)
			{
				estDetailCalculateHelper.CalculateLineItemAndResourceDetails(lineItem, lineItem.Resources, getParametersFunc);
			}

			EstimateCalculatorService.Calculate(new EstimateCalculationOption(this._EstHeaderId, ruleApplicationContext.ProjectId), lineitems);

			return isSuccess;
		}

		/// <summary>
		///
		/// </summary>
		public void CalculateRiskEscalation(List<EstLineItemEntity> lineItemsFilterByJob, List<EstResourceEntity> resources, bool isUpdateEscalation, bool isUpdateRisk)
		{
			this._EstLineItemUpdateOption.IsUpdateEscalation = isUpdateEscalation;

			this._EstLineItemUpdateOption.IsUpdateRisk = isUpdateRisk;

			if (isUpdateEscalation || isUpdateRisk)
			{
				CalculateRiskEscalation(lineItemsFilterByJob, resources);
			}
		}

		/// <summary>
		///
		/// </summary>
		public void CalculateRiskEscalation(List<EstLineItemEntity> lineItemsFilterByJob, List<EstResourceEntity> resources)
		{
			if (this._EstLineItemUpdateOption.IsUpdateRisk)
			{
				new EstRiskRegisterLogic().AutoCalculateRiskAssignments(this._EstHeaderId, false, lineItemsFilterByJob, resources);
			}

			if (this._EstLineItemUpdateOption.IsUpdateEscalation)
			{
				new EstEscalationLogic().CalculateEscalation(this._EstHeaderId, lineItemsFilterByJob, resources);
			}

			if(this._EstLineItemUpdateOption.IsUpdateRisk || this._EstLineItemUpdateOption.IsUpdateEscalation)
			{
				var _estRoundingHelper = lineItemsFilterByJob.Count() > 0 ? new EstimateRoundingHelper(lineItemsFilterByJob.FirstOrDefault().EstHeaderFk) : new EstimateRoundingHelper();

				var estBaseCalculationHelper = new EstBaseCalculationHelper(_estRoundingHelper);

				foreach (var lineitem in lineItemsFilterByJob)
				{
					if (lineitem.IsGc || lineitem.IsIncluded && !lineitem.IsFromBid)
					{
						lineitem.GrandTotal = 0;
					}
					else
					{
						estBaseCalculationHelper.CalculateGrandTotal(lineitem, this.IsCalcTotalWithWq());
					}
				}

				if (!this._EstLineItemUpdateOption.IsUpdateRule)
				{
					new EstimateMainLineItemLogic().BulkSave(ModelBuilder.DbModel, lineItemsFilterByJob);
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		public void UpdateCurrenciesFromEstimate(IEnumerable<EstLineItemEntity> lineItems)
		{
			var estHeaderId = lineItems.FirstOrDefault().EstHeaderFk;

			var estObj = new EstimateMainHeaderLogic().GetItemById(estHeaderId);

			var context = Application.BusinessEnvironment.CurrentContext;

			EstimateRoundingHelper _estRoundingHelper = new EstimateRoundingHelper(estHeaderId);

			ParallelOptions parallelOptions = new ParallelOptions() { MaxDegreeOfParallelism = Environment.ProcessorCount > 1 ? Environment.ProcessorCount / 2 : 1 };

			Parallel.ForEach(lineItems, parallelOptions, lineItem =>
			{
				ApplyThreadContext(context);

				updateLineItemExchangeRatesTotal(estObj, lineItem, _estRoundingHelper);
			});
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="estObj"></param>
		/// <param name="lineItem"></param>
		/// <param name="_estRoundingHelper"></param>
		public void updateLineItemExchangeRatesTotal(EstHeaderEntity estObj, EstLineItemEntity lineItem, EstimateRoundingHelper _estRoundingHelper)
		{
			if (estObj.ExchangeRate1 != null)
			{
				lineItem.CostExchangeRate1 = Decimal.Multiply(lineItem.CostTotal, (decimal)estObj.ExchangeRate1);
				lineItem.CostExchangeRate1 = _estRoundingHelper.DoRoundingValue(estObj.Id, (int)EstRoundingColumnIds.CostExchangeRate1, (decimal)lineItem.CostExchangeRate1);
			}
			else
			{
				lineItem.CostExchangeRate1 = 0;
			}
			if (estObj.ExchangeRate2 != null)
			{
				lineItem.CostExchangeRate2 = Decimal.Multiply(lineItem.CostTotal, (decimal)estObj.ExchangeRate2);
				lineItem.CostExchangeRate2 = _estRoundingHelper.DoRoundingValue(estObj.Id, (int)EstRoundingColumnIds.CostExchangeRate2, (decimal)lineItem.CostExchangeRate2);
			}
			else
			{
				lineItem.CostExchangeRate2 = 0;
			}
		}

		/// <summary>
		/// update lineItem and resource propertied from project cost code and project material, load the lasted exchange rate and calculate the detail property
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="resources"></param>
		private void UpdateLineItemAndResourceProperties(IEnumerable<EstLineItemEntity> lineItems, IEnumerable<EstResourceEntity> resources)
		{
			/* update the mdcCostCodeFk of resources */
			UpdateResourcesCostCodeFk(lineItems, resources);

			/* update infornation from project costcode and material */
			UpdateCostCodeAndMaterial(lineItems, resources);

			if (this._EstLineItemUpdateOption.IsUpdateExchangeRate)
			{
				this.GetExchangeRateHelper().ExchangeRate(resources);
			}

			/* convert some special char in detail by culture */
			NormalizeDetail(lineItems, resources);

			/* calculate the detail of lineitems and resouces */
			CalculateDetails(lineItems);

			/* update split quantity of lineitems */
			UpdateSplitQuantity(lineItems);
		}

		private void UpdateAssembly(IEnumerable<EstLineItemEntity> lineItems, List<EstResourceEntity> resources)
		{
			if (this._EstLineItemUpdateOption.IsUpdateAssembly && !this._EstLineItemUpdateOption.IsAssembly)
			{
				using (var estimateScope = new EstimateScope(this._EstHeaderId, this._ProjectId))
				{
					var resourceDataPrivoder = new EstResourceDataProvider(resources != null ? resources : new List<EstResourceEntity>());

					var assemblyTemplate2LineItemLogic = new AssemblyTemplate2LineItemLogic(this._ProjectId ?? 0);

					var project2EstimateUpdateHandler = new Project2EstimateUpdateHandler(this._ProjectId, this._EstLineItemUpdateOption, new ProjectScopeObject(this._ProjectId ?? 0), resourceDataPrivoder, assemblyTemplate2LineItemLogic);

					var compositeAssemblyUpdateManager = new CompositeAssemblyUpdateManager(this._ProjectId, this._EstHeaderId, project2EstimateUpdateHandler);

					compositeAssemblyUpdateManager.Update(lineItems);

					assemblyTemplate2LineItemLogic.DoCopyRulesAndParams();

					assemblyTemplate2LineItemLogic.Save();

					estimateScope.DeleteUDPEntitiesOfResource();

					resourceDataPrivoder.Save();

					estimateScope.SaveModifiedUDP();
				}
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="resources"></param>
		/// <param name="updateForProjectPlantAssembly"></param>
		public void UpdatePlantAssembly(IEnumerable<EstLineItemEntity> lineItems, List<EstResourceEntity> resources, bool updateForProjectPlantAssembly = false)
		{
			if (this._EstLineItemUpdateOption.IsUpdatePlantAssembly && !this._EstLineItemUpdateOption.IsAssembly)
			{
				using (var estimateScope = new EstimateScope(this._EstHeaderId, this._ProjectId))
				{
					var resourceDataPrivoder = new EstResourceDataProvider(resources != null ? resources : new List<EstResourceEntity>());

					var project2PlantEstimateUpdateHandler = new Project2PlantAssemblyUpdateHandler(this._ProjectId, this._EstLineItemUpdateOption, new ProjectScopeObject(this._ProjectId ?? 0), resourceDataPrivoder);

					var compositeAssemblyUpdateManager = new PlantAssemblyUpdateManager(this._ProjectId, this._EstHeaderId, project2PlantEstimateUpdateHandler);

					compositeAssemblyUpdateManager.Update(lineItems, updateForProjectPlantAssembly);

					estimateScope.DeleteUDPEntitiesOfResource();

					resourceDataPrivoder.Save();

					estimateScope.SaveModifiedUDP();

					DistinctResources(resources);
				}
			}
		}

		private IEnumerable<IScriptEstResource> DistinctResources(IEnumerable<IScriptEstResource> resources)
		{
			var distinctResources = resources.Distinct().ToList();
			foreach (var item in distinctResources)
			{
				if (item.Resources != null && item.Resources.Any())
				{
					item.Resources = DistinctResources(item.Resources).ToList();
				}
			}
			return distinctResources;
		}

		private void UpdateResourcesCostCodeFk(IEnumerable<EstLineItemEntity> lineItems, IEnumerable<EstResourceEntity> resources)
		{
			if (this._EstLineItemUpdateOption.IsSetCostCodeFk)
			{
				this.GetCostCodeSearchService().UpdateResourcesCostCodeFk(resources);
			}
		}

		private void UpdateCostCodeAndMaterial(IEnumerable<EstLineItemEntity> lineItems, IEnumerable<EstResourceEntity> resources)
		{
			if (this._EstLineItemUpdateOption.IsUpdateRate)
			{
				this._EstLineItemUpdateFrmPrjLogic.UpdateRateFromProject(lineItems);

				this._EstLineItemUpdateFrmPrjLogic.Save();

				if (this._EstLineItemUpdateOption.DoSaveModifiedUDP)
				{
					this.SaveModifiedUDP();
				}
			}
		}

		private void NormalizeDetail(IEnumerable<EstLineItemEntity> lineItems, IEnumerable<EstResourceEntity> resources)
		{
			if (this._EstLineItemUpdateOption.NormalizeResourceDetail)
			{
				EstCultureHelper.NormalizeDetailOfLineItems(lineItems);

				EstCultureHelper.NormalizeDetailOfResources(resources);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		public void CalculateDetails(IEnumerable<EstLineItemEntity> lineItems)
		{
			if (this._EstLineItemUpdateOption.IsCalculateDetail)
			{
				new EstDetailCalculateHelper(this._EstHeaderId, this._ProjectId).CalculateDetails(lineItems, this._ProjectId, this.GetEstParameterVisitor().GetParameterValuesByLineItem);
			}
			else
			{
				new EstDetailCalculateHelper(this._EstHeaderId, this._ProjectId).CalculateSystemParamDetail(lineItems, this.ProjectId, this.GetEstParameterVisitor().GetParameterValuesByLineItem);
			}
		}

		private void UpdateSplitQuantity(IEnumerable<EstLineItemEntity> lineItems)
		{
			if (this._EstLineItemUpdateOption.IsUpdateSplitQuantity)
			{
				UpdateSplitQuantity(lineItems, this.GetEstParameterVisitor().GetParameterValuesByLineItem);
			}
		}

		/// <summary>
		///
		/// </summary>
		private void SetOriginalCostUnitOrQuantity(IEnumerable<EstResourceEntity> resourceToUpdate)
		{
			foreach (var resource in resourceToUpdate)
			{
				resource.CostUnitOriginal = resource.CostUnit;

				resource.QuantityOriginal = resource.Quantity;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entity"></param>
		/// <param name="lineItemList"></param>
		/// <returns></returns>
		private IScriptEstLineItem GetRootItem(IScriptEstLineItem entity, IEnumerable<IScriptEstLineItem> lineItemList)
		{
			var currentEntity = entity;

			if (entity == null || lineItemList == null)
			{
				return currentEntity;
			}

			while (currentEntity.EstLineItemFk != null)
			{
				var parent = lineItemList.FirstOrDefault(e => e.Id == currentEntity.EstLineItemFk && e.EstHeaderFk == currentEntity.EstHeaderFk);

				if (parent != null)
				{
					currentEntity = parent;
				}
			}
			return currentEntity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="getParameterFunc"></param>
		private void UpdateSplitQuantity(IEnumerable<EstLineItemEntity> lineItems, Func<IScriptEstLineItem, Dictionary<string, decimal>> getParameterFunc)
		{
			var objectQtyLogic = new EstLineItem2ObjectQtyLogic();

			var lineItemIds = lineItems.Select(e => e.Id).Distinct().ToList();

			var modelObjectEntities = new EstLineItem2MdlObjectLogic().GetList(this._EstHeaderId, lineItemIds).ToList();

			var objectIds = modelObjectEntities.Select(e => e.Id).Distinct().ToList();

			var splitQuantities = objectQtyLogic.GetCoresByFilter(e => objectIds.Contains(e.EstLineItem2MdlObjectFk) && e.EstHeaderFk == this._EstHeaderId).ToList();

			//calculate the split quantity
			EstLineItemUtilities.AttachSplitQuantityToLineItems(lineItems, modelObjectEntities, splitQuantities);

			this.CalculateModelObjectQtyDetails(lineItems, getParameterFunc);

			/* if _IsUpdateLineItemFormSplitQuantity is false, set the quantity of LineItem2ObjectQty from lineItem quantity*/
			if (!this._EstLineItemUpdateOption.IsUpdateLineItemFormSplitQuantity)
			{
				foreach (var lineItem in lineItems)
				{
					if (lineItem.EstLineItem2ObjectQtyEntities != null && lineItem.EstLineItem2ObjectQtyEntities.Any())
					{
						foreach (var estLineItem2ObjectQty in lineItem.EstLineItem2ObjectQtyEntities)
						{
							estLineItem2ObjectQty.Quantity = lineItem.Quantity;

							estLineItem2ObjectQty.QuantityDetail = lineItem.Quantity.ToString();
						}
					}
				}
			}

			/* update the mdlObject from lineItem2ObjectQty */
			var isTotalWq = new EstimateMainHeaderLogic().IsCalcTotalWithWq(this._EstHeaderId);

			foreach (var lineItem in lineItems)
			{
				if (lineItem.EstLineItem2ObjectQtyEntities != null && lineItem.EstLineItem2ObjectQtyEntities.Any())
				{
					var mdlObjectMap = lineItem.EstLineitem2mdlObjectEntities != null ? lineItem.EstLineitem2mdlObjectEntities.GroupBy(e => e.Id).ToDictionary(e => e.Key, e => e.ToList()) : new Dictionary<int, List<EstLineItem2MdlObjectEntity>>();

					var factors = lineItem.QuantityFactor1 * lineItem.QuantityFactor2 * lineItem.QuantityFactor3 * lineItem.QuantityFactor4 * lineItem.ProductivityFactor;

					foreach (var estLineItem2ObjectQty in lineItem.EstLineItem2ObjectQtyEntities)
					{
						if (mdlObjectMap.ContainsKey(estLineItem2ObjectQty.EstLineItem2MdlObjectFk))
						{
							var mdlObjectList = mdlObjectMap[estLineItem2ObjectQty.EstLineItem2MdlObjectFk];

							foreach (var mdlObjectItem in mdlObjectList)
							{
								mdlObjectItem.Quantity = estLineItem2ObjectQty.Quantity;

								mdlObjectItem.QuantityDetail = estLineItem2ObjectQty.QuantityDetail;

								mdlObjectItem.QuantityTarget = estLineItem2ObjectQty.QuantityTarget;

								mdlObjectItem.QuantityTargetDetail = estLineItem2ObjectQty.QuantityTargetDetail;

								mdlObjectItem.WqQuantityTarget = estLineItem2ObjectQty.WqQuantityTarget;

								mdlObjectItem.WqQuantityTargetDetail = estLineItem2ObjectQty.WqQuantityTargetDetail;

								mdlObjectItem.QuantityTotal = estLineItem2ObjectQty.Quantity == 0 ? null
									 : isTotalWq ? estLineItem2ObjectQty.WqQuantityTarget == 0 ? (decimal?)null : estLineItem2ObjectQty.Quantity * estLineItem2ObjectQty.WqQuantityTarget * factors
									 : estLineItem2ObjectQty.QuantityTarget == 0 ? (decimal?)null
									 : estLineItem2ObjectQty.Quantity * estLineItem2ObjectQty.QuantityTarget * factors;
							}
						}
					}
				}
			}

			if (splitQuantities != null)
			{
				objectQtyLogic.SaveInBatch(splitQuantities);
			}

			if (this._EstLineItemUpdateOption.IsUpdateLineItemFormSplitQuantity)
			{
				new EstimateMainLineItemLogic().UpdateSplitQuantities(this._EstHeaderId, lineItems);
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="lineItems"></param>
		/// <param name="getParameterFunc"></param>
		private void CalculateModelObjectQtyDetails(IEnumerable<EstLineItemEntity> lineItems, Func<IScriptEstLineItem, Dictionary<string, decimal>> getParameterFunc)
		{
			if (lineItems == null || !lineItems.Any())
			{
				return;
			}

			var detailCalculator = new DetailCalculator(this._EstHeaderId, this._ProjectId);

			foreach (var lineItem in lineItems)
			{
				if (lineItem.EstLineItem2ObjectQtyEntities == null)
				{
					continue;
				}

				foreach (var obj in lineItem.EstLineItem2ObjectQtyEntities)
				{
					detailCalculator.CalculateModelObjectQty(obj, lineItem, getParameterFunc);
				}
			}
		}
	}
}
