/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { IPinningContext } from '../../interfaces/pinning-context.interface';
import { isAppContextValPinningContext } from '../../model/context/app-context.interface';
import { PlatformConfigurationService } from '../platform-configuration.service';
import { PlatformModuleManagerService } from '../../module-management';
import { filter, includes, isEmpty, find, map, sortBy } from 'lodash';
import { ModuleForPinningContext, MODULE_NAMES_FOR_PINNING_CONTEXT, ModuleNamesForPinningContext, MODULES_FOR_PINNING_CONTEXT, ModuleName } from '../../constant/modules';
import { PlatformTranslateService } from '../platform-translate.service';


/**
 * Service that handles saving of the pinning context.
 * The pinning context will be used for pinning additional details which can be used for searching/filtering from the sidebar.
 */
@Injectable({
	providedIn: 'root'
})
export class PlatformPinningContextService {

	private readonly configurationService = inject(PlatformConfigurationService);
	private readonly moduleManagerService = inject(PlatformModuleManagerService);
	private readonly translate = inject(PlatformTranslateService);
	private readonly pinningContextKey: string = 'pinningContexts';

	/**
	 * Sets pinning context into local storage.
	 * @param pinningContexts An array of pinning contexts.
	 */
	public setPinningContext(pinningContexts: IPinningContext[]): IPinningContext[] {
		pinningContexts = filter(pinningContexts, pc => includes(MODULE_NAMES_FOR_PINNING_CONTEXT, pc.token));
		let localPinningContexts: IPinningContext[] = [];
		if (!isEmpty(pinningContexts)) {
			localPinningContexts = this.getPinningContexts() ?? [];
			pinningContexts.forEach((pinningContext) => {
				const localPinningContext = find(localPinningContexts, lpc => lpc.token === pinningContext.token);
				if (localPinningContext !== undefined) {
					localPinningContext.id = pinningContext.id;
					localPinningContext.info = pinningContext.info;
				} else {
					localPinningContexts.push(pinningContext);
				}
			});
		}

		this.configurationService.setApplicationValue(this.pinningContextKey, localPinningContexts, true);
		return localPinningContexts;
	}

	/**
	 * Gets pinning context for a module.
	 * @param internalModuleName The name of the module.
	 * @returns A pinning context object or null, if the pinning context is not available.
	 */
	public getPinningContextForModule(internalModuleName: ModuleName): IPinningContext | null {
		const localPinningContexts = this.getPinningContexts();
		if (!isEmpty(localPinningContexts)) {
			return find(localPinningContexts, localPinningContext => localPinningContext.token === internalModuleName) || null;
		}
		return null;
	}

	/**
	 * Gets pinning context for a module.
	 * @returns A pinning context object list for the current module.
	 */
	public getPinningContextForCurrentModule(): IPinningContext[] {
		const currentModule = this.moduleManagerService.activeModule; //TODO berweiler: maybe use activeModule$
		if (currentModule) {
			const localPinningContexts = this.getPinningContexts();
			const displayedPinningContextTokens = currentModule.displayedPinningContextTokens;
			if (localPinningContexts !== null && !isEmpty(displayedPinningContextTokens)) {
				return filter(localPinningContexts, lpc => includes(displayedPinningContextTokens, lpc.token));
			}
		}
		return [];
	}

	/**
	 * Gets pinning context for a module.
	 * @returns An extended pinning context object list for the current module for the search sidebar.
	 */
	public getPinningContextInfoForCurrentModule(): ModuleForPinningContext[] {
		const pinningContextsForModule = this.getPinningContextForCurrentModule();
		return this.sortPinningContexts(map(pinningContextsForModule, pcm => {
			const title = this.translate.instant(MODULES_FOR_PINNING_CONTEXT[pcm.token].name.key).text;
			MODULES_FOR_PINNING_CONTEXT[pcm.token].title = `${title}: ${pcm.info}`;
			MODULES_FOR_PINNING_CONTEXT[pcm.token].info = pcm.info;
			return MODULES_FOR_PINNING_CONTEXT[pcm.token];
		}));
	}

	/**
	 * Gets the pinning context from local storage.
	 * @returns A pinning context object or null, if the pinning context is not available.
	 */
	public getPinningContexts(): IPinningContext[] {
		const pinningContexts = this.configurationService.getApplicationValue(this.pinningContextKey);
		if (pinningContexts === null) {
			return [];
		}

		if (!isAppContextValPinningContext(pinningContexts)) {
			throw new Error('Improper settings saved');
		}

		return pinningContexts;
	}

	/**
	 * Removes a pinning context by a given token.
	 * @param token The token of the pinning context.
	 */
	public removePinningContext(token: ModuleNamesForPinningContext): void {
		if (token === 'project.main') {
			this.clearPinningContext();
		}

		const localPinningContexts = this.getPinningContexts();
		const pinningContexts = filter(localPinningContexts, localPinningContext => localPinningContext.token !== token);
		this.configurationService.setApplicationValue(this.pinningContextKey, pinningContexts, true);
	}

	/**
	 * Clears pinning context for passed module.
	 * @param internalModuleName The name of the module.
	 */
	public clearPinningContextForModule(internalModuleName: string): void {
		const localPinningContext = this.getPinningContexts();
		if (!isEmpty(localPinningContext)) {
			const filteredPinningContexts = localPinningContext.filter(item => item.token !== internalModuleName);
			this.setPinningContext(filteredPinningContexts);
		}
	}

	/**
	 * Clears the entire pinning context.
	 */
	public clearPinningContext(): void {
		this.setPinningContext([]);
	}

	private sortPinningContexts(pinningContexts: ModuleForPinningContext[]): ModuleForPinningContext[] {
		return sortBy(pinningContexts, function (pc) {
			return pc.sorting || 9999;
		});
	}
}
