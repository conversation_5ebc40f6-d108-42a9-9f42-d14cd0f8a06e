/**
 * Created by <PERSON><PERSON> on 03.01.2023
 */
(function (angular) {
	/* global globals */
	'use strict';
	let schedulingMainModule = angular.module('scheduling.main');

	/**
	 * @ngdoc service
	 * @name schedulingMainObjectBaseSimulationDataService
	 * @function
	 *
	 * @description
	 * schedulingMainObjectBaseSimulationDataService is the data service for all ObjectBaseSimulation related functionality.
	 */
	schedulingMainModule.factory('schedulingMainObjectBaseSimulationDataService', ['schedulingMainService', 'schedulingMainConstantValues', 'platformDataServiceProcessDatesBySchemeExtension',
		'platformDataServiceFactory', 'platformObservableService', 'schedulingMainCurrentDatesProcessor', 'modelViewerModelSelectionService', 'modelViewerSelectorService',
		'modelViewerModelIdSetService', 'modelViewerCompositeModelObjectSelectionService', 'platformDataServiceDataProcessorExtension', '_', 'platformRuntimeDataService',
		'schedulingMainFilterButtonService', 'PlatformMessenger',
		function (schedulingMainService, schedulingMainConstantValues, platformDataServiceProcessDatesBySchemeExtension,
			platformDataServiceFactory, platformObservableService, schedulingMainCurrentDatesProcessor, modelViewerModelSelectionService, modelViewerSelectorService,
			modelViewerModelIdSetService, modelViewerCompositeModelObjectSelectionService, platformDataServiceDataProcessorExtension, _, platformRuntimeDataService,
			schedulingMainFilterButtonService, PlatformMessenger) {

			let showFilterBtn = false;
			let initFilterMenuFlag = true;
			let estimateIds = [];

			let schedulingMainSimulationServiceOption = {
				flatLeafItem: {
					module: schedulingMainModule,
					serviceName: 'schedulingMainObjectBaseSimulationDataService',
					entityNameTranslationID: 'scheduling.main.objectBaseSimulationListTitle',
					httpCreate: {route: globals.webApiBaseUrl + 'scheduling/main/ojectmodelsimulation/'},
					httpRead: {
						route: globals.webApiBaseUrl + 'scheduling/main/ojectmodelsimulation/',
						endRead: 'listByParents',
						usePostForRead: true,
						initReadData: function initReadData(readData) {
							let selection = schedulingMainService.getSelectedEntities();
							delete readData.filter;
							readData.Ids = _.map(selection, 'Id');
							readData.EstimateIds =  _.uniqBy (estimateIds);
							readData.PerformanceDueDate = schedulingMainService.getDueDate();
						}
					},
					actions: {delete: false, create: false},
					dataProcessor: [platformDataServiceProcessDatesBySchemeExtension.createProcessor(
						schedulingMainConstantValues.schemes.objectSimulation), schedulingMainCurrentDatesProcessor, {processItem: processItem}],
					entityRole: {leaf: {itemName: 'ObjModelSimulation', parentService: schedulingMainService}},
					presenter: {
						list: {
							initCreationData: function initCreationData(creationData) {
								let selected = schedulingMainService.getSelected();
								creationData.PKey1 = selected.Id;

							},
							incorporateDataRead: function incorporateDataRead(response, data) {
								let readItems = response;
								schedulingMainFilterButtonService.initFilterMenu(service);
								return serviceContainer.data.handleReadSucceeded (readItems, data);
							}
						}
					}
				}
			};

			let serviceContainer = platformDataServiceFactory.createNewComplete(schedulingMainSimulationServiceOption);

			let service = serviceContainer.service;
			service.onToolsUpdated = new PlatformMessenger ();
			service.canCreate = function canCreate() {
				return !schedulingMainService.isCurrentTransientRoot();
			};
			service.updateModelSelection = platformObservableService.createObservableBoolean({
				initialValue: true
			});
			service.updateModelSelection.uiHints = {
				id: 'toggleObjectSelection',
				caption$tr$: 'estimate.main.selectLineItemObjects',
				iconClass: 'tlb-icons ico-view-select'
			};

			function updateModelSelectionIfRequired() {
				if (serviceContainer.service.updateModelSelection.getValue()) {
					let selModelId = modelViewerModelSelectionService.getSelectedModelId();
					if (selModelId) {
						let selItems = serviceContainer.service.getSelectedEntities();

						if (selItems.length > 0) {
							selectAssignedObject(selItems);
						}
					}
				}
			}

			function selectAssignedObject(assignedObjects) {
				if (modelViewerModelSelectionService.getSelectedModelId()) {
					if (assignedObjects && assignedObjects.length) {
						let selectedObjectIds = new modelViewerModelIdSetService.ObjectIdSet();

						modelViewerModelSelectionService.forEachSubModel(function (subModelId) {
							selectedObjectIds[subModelId] = [];
						});

						selectedObjectIds = selectedObjectIds.useGlobalModelIds();

						assignedObjects.forEach(function (assignedObject) {
							if (angular.isArray(selectedObjectIds[assignedObject.MdlModelFk])) {
								selectedObjectIds[assignedObject.MdlModelFk].push(assignedObject.ObjectFk);
							}
						});

						if (!selectedObjectIds.isEmpty()) {
							modelViewerCompositeModelObjectSelectionService.setSelectedObjectIds(selectedObjectIds.useSubModelIds());
						}
					} else {
						modelViewerCompositeModelObjectSelectionService.setSelectedObjectIds();
					}
				}
			}

			service.updateModelSelection.registerValueChanged(updateModelSelectionIfRequired);
			service.registerSelectedEntitiesChanged(updateModelSelectionIfRequired);

			service.takeOver = function (entities, hasToModified) {
				_.forEach(entities, function (entity) {
					let loaded = serviceContainer.data.getItemById(entity.Id, serviceContainer.data);
					if (loaded) {
						loaded.PlannedDuration = entity.PlannedDuration;
						loaded.PlannedStart = entity.PlannedStart;
						loaded.PlannedFinish = entity.PlannedFinish;
						platformDataServiceDataProcessorExtension.doProcessItem(loaded, serviceContainer.data);
						if (hasToModified) {
							serviceContainer.service.markItemAsModified(loaded);
						}
					}
				});
			};

			function processItem(item) {
				let activity = schedulingMainService.getSelected(item.ActivityFk);
				let readonly = item && !item.EstHeaderActive || activity && ((!activity.ScheduleTypeIsExecution || !activity.ScheduleTypeIsProcurement) && activity.ScheduleTypeIsDesign || activity.ProgressReportMethodFk !== schedulingMainConstantValues.progressReportMethod.ByModelObjects);

				platformRuntimeDataService.readonly(item, [
					{field: 'ExecutionStarted', readonly: readonly},
					{field: 'ExecutionFinished', readonly: readonly},
					{field: 'ActualStart', readonly: readonly},
					{field: 'ActualFinish', readonly: readonly},
					{field: 'PerformanceDate', readonly: readonly},
					{field: 'PCo', readonly: readonly},
					{field: 'RemainingPCo', readonly: readonly},
					// {field: 'Quantity', readonly: readonly},
					{field: 'PeriodQuantityPerformance', readonly: readonly},
					{field: 'DueDateQuantityPerformance', readonly: readonly},
					{field: 'RemainingQuantity', readonly: readonly}]);
			}

			service.setInitFilterMenuFlag = function setInitFilterMenuFlag(value){
				initFilterMenuFlag = value;
			};

			service.getInitFilterMenuFlag = function getInitFilterMenuFlag(){
				return initFilterMenuFlag;
			};

			service.setShowFilterBtn = function setShowFilterBtn(value) {
				showFilterBtn = value;
			};

			service.getShowFilterBtn = function getShowFilterBtn() {
				return showFilterBtn;
			};

			service.setSelectedEstimateIds = function setSelectedEstimateIds(ids) {
				estimateIds = _.filter (ids, function (d) {
					return d;
				});
			};

			service.clear = function clear() {
				estimateIds = [];
				showFilterBtn = false;
			};

			service.takeOverNewValues = function takeOverNewValues(data) {
				if (data && data.length > 0) {
					_.forEach(data, function (entity) {
						var item = service.getItemById(entity.Id);
						if (item !== null) {
							item.PeriodQuantityPerformance = entity.PeriodQuantityPerformance;
							item.DueDateQuantityPerformance = entity.DueDateQuantityPerformance;
							item.PCo = entity.PCo;
							item.RemainingPCo = 100 - entity.PCo;
							item.RemainingQuantity = entity.RemainingQuantity;
							item.Work = entity.Work;
							item.RemainingWork = entity.RemainingWork;
							item.PerformanceDate = moment.isMoment(entity.PerformanceDate) ? entity.PerformanceDate : moment.utc(entity.PerformanceDate);
							item.ActualFinish = entity.ActualFinish;
							item.ExecutionFinished = entity.ExecutionFinished;
							item.ActualDuration = entity.ActualDuration;
							platformDataServiceDataProcessorExtension.doProcessItem(item, data);
							serviceContainer.data.itemModified.fire(null, item);
						}
					});
				}
			};
			return service;
		}
	]);
})(angular);