/*
 * Copyright(c) RIB Software GmbH
 */

import { inject, Injectable } from '@angular/core';
import { SalesCommonNumberGenerationService } from '@libs/sales/common';
import { Observable } from 'rxjs';
import { ISalesCommonNumberGenerationSetting } from '@libs/sales/interfaces';

/**
 * Sales WIP number generation service
 * 
 * This service provides number generation functionality specific to the WIP module
 * by wrapping the common sales number generation service.
 */
@Injectable({
  providedIn: 'root'
})
export class SalesWipNumberGenerationService {
  private readonly commonNumberGenerationService = inject(SalesCommonNumberGenerationService);
  private readonly moduleName = 'wip';

  /**
   * Loads number generation settings for the WIP module
   * 
   * @returns An observable of number generation settings
   */
  public loadSettings(): Observable<ISalesCommonNumberGenerationSetting[]> {
    return this.commonNumberGenerationService.loadSettings(this.moduleName);
  }

  /**
   * Gets cached number generation settings for the WIP module
   * 
   * @returns The cached settings or undefined if not loaded
   */
  public getSettings(): ISalesCommonNumberGenerationSetting[] | undefined {
    return this.commonNumberGenerationService.getSettings(this.moduleName);
  }

  /**
   * Checks if a number should be generated for a specific rubric category
   * 
   * @param rubricCategoryId The rubric category ID
   * @param rubricIndex The rubric index (optional)
   * @returns True if a number should be generated, false otherwise
   */
  public hasToGenerateForRubricCategory(rubricCategoryId: number, rubricIndex: number = 0): boolean {
    return this.commonNumberGenerationService.hasToGenerateForRubricCategory(this.moduleName, rubricCategoryId, rubricIndex);
  }

  /**
   * Provides default text for number fields
   * 
   * @param rubricCategoryId The rubric category ID
   * @param current The current value
   * @param rubricIndex The rubric index (optional)
   * @returns The default text
   */
  public provideNumberDefaultText(rubricCategoryId: number, current: string = '', rubricIndex: number = 0): string {
    return this.commonNumberGenerationService.provideNumberDefaultText(this.moduleName, rubricCategoryId, current, rubricIndex);
  }

  /**
   * Ensures settings are loaded before use
   * 
   * @returns A promise that resolves when settings are loaded
   */
  public async ensureSettingsLoaded(): Promise<void> {
    return this.commonNumberGenerationService.ensureSettingsLoaded(this.moduleName);
  }
}
