import { commonLocators, app, tile, cnt, sidebar, btn } from 'cypress/locators';
import CommonLocators from 'cypress/locators/common-locators';
import { _common, _projectPage, _businessPartnerPage, _procurementContractPage, _salesPage, _validate } from 'cypress/pages';
import { DataCells } from 'cypress/pages/interfaces';

const PROJECT_NO = 'NAME_A' + _common.generateRandomString(4);
const PROJECT_NO_1 = 'PRJ_NAME' + _common.generateRandomString(4);
const PROJECT_NO_2 = 'A_NAME_A' + _common.generateRandomString(4);
const PROJECT_NO_3 = 'NAME_PRJ' + _common.generateRandomString(4);
const PROJECT_NO_4 = _common.generateRandomString(4);
const PROJECT_NO_5 = '14' + _common.generateRandomString(4);
const PRJ_NAME = _common.generateRandomString(3) + Cypress._.random(0, 999);
const CONTRACT_SALES_DESC = 'Demo-' + _common.generateRandomString(5);
const CONTRACT_SALES_DESC_1 = 'DATA-' + _common.generateRandomString(5);
const CONTRACT_NAME = 'CONTRACT' + _common.generateRandomString(5);
const CONTRACT_NAME_1 = 'CONTRACT' + _common.generateRandomString(5);
const CONTRACT_NAME_2 = 'NEWCONTRACT' + _common.generateRandomString(5);
const BP_CODE = _common.generateRandomString(3) + Cypress._.random(0, 999);
const BP_CODE_1 = _common.generateRandomString(3) + Cypress._.random(0, 999);
const BP_CODE_2 = _common.generateRandomString(3) + Cypress._.random(0, 999);
const BP_CODE_3 = _common.generateRandomString(3) + Cypress._.random(0, 999);
const BP_NAME = 'BP_NAME' + _common.generateRandomString(3);
const BP_NAME_1 = 'TEST_BP' + _common.generateRandomString(3);
const BP_NAME_2 = 'BP-VER-' + _common.generateRandomString(3);
const BP_NAME_3 = _common.generateRandomString(3);

let CONTAINERS_CONTRACT_SALES, CONTAINERS_CONTRACT, CONTAINERS_BUSINESS_PARTNER, CONTAINERS_CERTIFICATE;
let CONTAINER_COLUMNS_CONTRACT_SALES, CONTAINER_COLUMNS_CONTRACT, CONTAINER_COLUMNS_BUSINESS_PARTNER, CONTAINER_COLUMNS_CERTIFICATE;
let PROJECTS_PARAMETERS: DataCells,
	PROJECTS_PARAMETERS_1: DataCells,
	PROJECTS_PARAMETERS_2: DataCells,
	PROJECTS_PARAMETERS_3: DataCells,
	PROJECTS_PARAMETERS_4: DataCells,
	PROJECTS_PARAMETERS_5: DataCells,
	CONTRACT_SALES_PARAMETER: DataCells,
	CONTRACT_SALES_PARAMETER_1: DataCells,
	CONTRACT_SALES_PARAMETER_2: DataCells,
	CONTRACT_SALES_PARAMETER_3: DataCells,
	PROCUREMENT_CONTRACT_PARAMETER: DataCells,
	PROCUREMENT_CONTRACT_PARAMETER_1: DataCells,
	PROCUREMENT_CONTRACT_PARAMETER_2: DataCells,
	PROCUREMENT_CONTRACT_PARAMETER_3: DataCells;
let MODAL_PROJECTS;

describe('PCM- 4.248 | BPD_Certificate_Certificates container_Check searching in Certificates container', () => {
	beforeEach(() => {
		cy.clearCookies();
		cy.clearLocalStorage();
		cy.WaitUntilLoaderComplete_Trial();
		cy.waitUntilDOMLoaded();
	});
	afterEach(() => {
		cy.clearCookies();
		cy.clearLocalStorage();
		cy.WaitUntilLoaderComplete_Trial();
		cy.waitUntilDOMLoaded();
	});
	before(function () {
		cy.fixture('pcm/pcm-4.248-bpd-certificate-certificates-container-check-searching-in-certificates-container.json').then((data) => {
			this.data = data;
			MODAL_PROJECTS = this.data.MODAL.PROJECTS;
			PROJECTS_PARAMETERS = {
				[commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO,
				[commonLocators.CommonLabels.NAME]: MODAL_PROJECTS.PROJECT_DESC[0],
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			PROJECTS_PARAMETERS_1 = {
				[commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO_1,
				[commonLocators.CommonLabels.NAME]: MODAL_PROJECTS.PROJECT_DESC[1],
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			PROJECTS_PARAMETERS_2 = {
				[commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO_2,
				[commonLocators.CommonLabels.NAME]: MODAL_PROJECTS.PROJECT_DESC[2],
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			PROJECTS_PARAMETERS_3 = {
				[commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO_3,
				[commonLocators.CommonLabels.NAME]: MODAL_PROJECTS.PROJECT_DESC[2],
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			PROJECTS_PARAMETERS_4 = {
				[commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO_4,
				[commonLocators.CommonLabels.NAME]: PRJ_NAME,
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			PROJECTS_PARAMETERS_5 = {
				[commonLocators.CommonLabels.PROJECT_NUMBER]: PROJECT_NO_5,
				[commonLocators.CommonLabels.NAME]: PRJ_NAME,
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT;
			CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT;
			PROCUREMENT_CONTRACT_PARAMETER = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: BP_NAME,
			};
			PROCUREMENT_CONTRACT_PARAMETER_1 = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: BP_NAME_1,
			};
			PROCUREMENT_CONTRACT_PARAMETER_2 = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: BP_NAME_1,
			};
			PROCUREMENT_CONTRACT_PARAMETER_3 = {
				[commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
				[commonLocators.CommonLabels.BUSINESS_PARTNER]: BP_NAME_1,
			};
			CONTAINERS_CONTRACT_SALES = this.data.CONTAINERS.CONTRACT_SALES;
			CONTAINER_COLUMNS_CONTRACT_SALES = this.data.CONTAINER_COLUMNS.CONTRACT_SALES;
			CONTRACT_SALES_PARAMETER = {
				[app.InputFields.DOMAIN_TYPE_DESCRIPTION]: CONTRACT_SALES_DESC,
				[app.InputFields.INPUT_GROUP_CONTENT]: BP_NAME,
				[commonLocators.CommonLabels.PROJECT_NAME]: PROJECT_NO_4,
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			CONTRACT_SALES_PARAMETER_1 = {
				[app.InputFields.DOMAIN_TYPE_DESCRIPTION]: CONTRACT_SALES_DESC,
				[app.InputFields.INPUT_GROUP_CONTENT]: BP_NAME_1,
				[commonLocators.CommonLabels.PROJECT_NAME]: PROJECT_NO,
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			CONTRACT_SALES_PARAMETER_2 = {
				[app.InputFields.DOMAIN_TYPE_DESCRIPTION]: CONTRACT_SALES_DESC_1,
				[app.InputFields.INPUT_GROUP_CONTENT]: BP_NAME_1,
				[commonLocators.CommonLabels.PROJECT_NAME]: PROJECT_NO_1,
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			CONTRACT_SALES_PARAMETER_3 = {
				[app.InputFields.DOMAIN_TYPE_DESCRIPTION]: CONTRACT_SALES_DESC_1,
				[app.InputFields.INPUT_GROUP_CONTENT]: BP_NAME_1,
				[commonLocators.CommonLabels.PROJECT_NAME]: PROJECT_NO_2,
				[commonLocators.CommonLabels.CLERK]: MODAL_PROJECTS.CLERK,
			};
			CONTAINERS_BUSINESS_PARTNER = this.data.CONTAINERS.BUSINESS_PARTNER;
			CONTAINER_COLUMNS_BUSINESS_PARTNER = this.data.CONTAINER_COLUMNS.BUSINESS_PARTNER;
			CONTAINERS_CERTIFICATE = this.data.CONTAINERS.CERTIFICATE;
			CONTAINER_COLUMNS_CERTIFICATE = this.data.CONTAINER_COLUMNS.CERTIFICATE;
		});
		cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
	});
	after(() => {
		cy.LOGOUT();
	});

	it('TC - Create new project', function () {
		_common.openDesktopTile(tile.DesktopTiles.PROJECT);
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.PROJECT).then(() => {
			_common.setDefaultView(app.TabBar.PROJECT);
			_common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
		});

		//1_PRJ
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.create_newRecord(cnt.uuid.PROJECTS);
		_projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();

		//2_PRJ
		_common.clear_subContainerFilter(cnt.uuid.PROJECTS);
		_common.create_newRecord(cnt.uuid.PROJECTS);
		_common.select_activeRowInContainer(cnt.uuid.PROJECTS);
		_projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.waitForLoaderToDisappear();

		//3_PRJ
		_common.clear_subContainerFilter(cnt.uuid.PROJECTS);
		_common.create_newRecord(cnt.uuid.PROJECTS);
		_common.select_activeRowInContainer(cnt.uuid.PROJECTS);
		_projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS_2);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.waitForLoaderToDisappear();

		//4_PRJ
		_common.clear_subContainerFilter(cnt.uuid.PROJECTS);
		_common.create_newRecord(cnt.uuid.PROJECTS);
		_common.select_activeRowInContainer(cnt.uuid.PROJECTS);
		_projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS_3);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.waitForLoaderToDisappear();

		//5_PRJ
		_common.clear_subContainerFilter(cnt.uuid.PROJECTS);
		_common.create_newRecord(cnt.uuid.PROJECTS);
		_common.select_activeRowInContainer(cnt.uuid.PROJECTS);
		_projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS_4);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.waitForLoaderToDisappear();

		//6_PRJ
		_common.clear_subContainerFilter(cnt.uuid.PROJECTS);
		_common.create_newRecord(cnt.uuid.PROJECTS);
		_common.select_activeRowInContainer(cnt.uuid.PROJECTS);
		_projectPage.enterRecord_toCreateProject(PROJECTS_PARAMETERS_5);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.waitForLoaderToDisappear();
	});

	it('TC - Create a business partner in business partner module', function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.BUSINESS_PARTNER);
		_common.openTab(app.TabBar.BUSINESS_PARTNERS).then(() => {
			_common.setDefaultView(app.TabBar.BUSINESS_PARTNERS);
			_common.select_tabFromFooter(cnt.uuid.BUSINESS_PARTNERS, app.FooterTab.BUSINESS_PARTNER, 0);
			_common.setup_gridLayout(cnt.uuid.BUSINESS_PARTNERS, CONTAINER_COLUMNS_BUSINESS_PARTNER);
		});
		_common.maximizeContainer(cnt.uuid.BUSINESS_PARTNERS);
		//1_BP
		_common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS);
		_common.clickOn_toolbarButton(cnt.uuid.BUSINESS_PARTNERS, btn.ToolBar.ICO_REC_NEW);
		_businessPartnerPage.enterRecord_toCreateBusinessPartner(BP_CODE, BP_NAME, CONTAINERS_BUSINESS_PARTNER.STREET_NAME, CONTAINERS_BUSINESS_PARTNER.ZIP_CODE, CONTAINERS_BUSINESS_PARTNER.CITY_NAME, CONTAINERS_BUSINESS_PARTNER.COUNTRY_NAME);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.BUSINESS_PARTNERS, app.GridCells.BUSINESS_PARTNER_NAME_1, 'BP_NAME_1');
		_common.waitForLoaderToDisappear();
		//2_BP
		_common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS);
		_common.clickOn_toolbarButton(cnt.uuid.BUSINESS_PARTNERS, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.BUSINESS_PARTNERS);
		_businessPartnerPage.enterRecord_toCreateBusinessPartner(BP_CODE_1, BP_NAME_1, CONTAINERS_BUSINESS_PARTNER.STREET_NAME, CONTAINERS_BUSINESS_PARTNER.ZIP_CODE, CONTAINERS_BUSINESS_PARTNER.CITY_NAME, CONTAINERS_BUSINESS_PARTNER.COUNTRY_NAME);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.BUSINESS_PARTNERS, app.GridCells.BUSINESS_PARTNER_NAME_1, 'BP_NAME_2');
		_common.waitForLoaderToDisappear();
		//3_BP
		_common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS);
		_common.clickOn_toolbarButton(cnt.uuid.BUSINESS_PARTNERS, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.BUSINESS_PARTNERS);
		_businessPartnerPage.enterRecord_toCreateBusinessPartner(BP_CODE_2, BP_NAME_2, CONTAINERS_BUSINESS_PARTNER.STREET_NAME, CONTAINERS_BUSINESS_PARTNER.ZIP_CODE, CONTAINERS_BUSINESS_PARTNER.CITY_NAME, CONTAINERS_BUSINESS_PARTNER.COUNTRY_NAME);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.BUSINESS_PARTNERS, app.GridCells.BUSINESS_PARTNER_NAME_1, 'BP_NAME_3');
		_common.waitForLoaderToDisappear();
		//4_BP
		_common.clear_subContainerFilter(cnt.uuid.BUSINESS_PARTNERS);
		_common.clickOn_toolbarButton(cnt.uuid.BUSINESS_PARTNERS, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.BUSINESS_PARTNERS);
		_businessPartnerPage.enterRecord_toCreateBusinessPartner(BP_CODE_3, BP_NAME_3, CONTAINERS_BUSINESS_PARTNER.STREET_NAME, CONTAINERS_BUSINESS_PARTNER.ZIP_CODE, CONTAINERS_BUSINESS_PARTNER.CITY_NAME, CONTAINERS_BUSINESS_PARTNER.COUNTRY_NAME);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.BUSINESS_PARTNERS, app.GridCells.BUSINESS_PARTNER_NAME_1, 'BP_NAME_4');
		_common.waitForLoaderToDisappear();
		_common.minimizeContainer(cnt.uuid.BUSINESS_PARTNERS);
	});

	it('TC - Create new contract record', function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.CONTRACT).then(() => {
			_common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
			_common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT);
		});
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
		_common.waitForLoaderToDisappear();
		_common.maximizeContainer(cnt.uuid.PROCUREMENTCONTRACT);

		//1_CONTRACT
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT);
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
		cy.SAVE();
		_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT);
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO_4);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT);
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_NAME);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, 'CONTRACT_CODE');
		cy.SAVE();
		_common.waitForLoaderToDisappear();

		//2_CONTRACT
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT);
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT);
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT);
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_NAME_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, 'CONTRACT_CODE_1');
		cy.SAVE();
		_common.waitForLoaderToDisappear();

		//3_CONTRACT
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT);
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER_3);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT);
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO_2);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT);
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_NAME_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, 'CONTRACT_CODE_3');
		cy.SAVE();
		_common.waitForLoaderToDisappear();

		//4_CONTRACT
		_common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT);
		_common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT);
		_procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT);
		_common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT);
		_common.edit_containerCell(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.DESCRIPTION, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTRACT_NAME_2);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.PROCUREMENTCONTRACT, app.GridCells.CODE, 'CONTRACT_CODE_2');
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.minimizeContainer(cnt.uuid.PROCUREMENTCONTRACT);
	});

	it('TC - Create sales contract', function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT_SALES);
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.CONTRACTS).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CONTRACTS, app.FooterTab.CONTRACTS);
			_common.setup_gridLayout(cnt.uuid.CONTRACTS, CONTAINER_COLUMNS_CONTRACT_SALES);
		});
		_common.waitForLoaderToDisappear();
		_common.maximizeContainer(cnt.uuid.CONTRACTS);
		//1_SALES-CONTRACT
		_common.clear_subContainerFilter(cnt.uuid.CONTRACTS);
		_common.create_newRecord(cnt.uuid.CONTRACTS);
		_salesPage.enterRecord_toCreateSalesBID(CONTRACT_SALES_PARAMETER);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CONTRACTS, app.GridCells.CODE, 'CONTRACT_SALES_CODE');
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		//2_SALES-CONTRACT
		_common.clear_subContainerFilter(cnt.uuid.CONTRACTS);
		_common.create_newRecord(cnt.uuid.CONTRACTS);
		_salesPage.enterRecord_toCreateSalesBID(CONTRACT_SALES_PARAMETER_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CONTRACTS, app.GridCells.CODE, 'CONTRACT_SALES_CODE_1');
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		//3_SALES-CONTRACT
		_common.clear_subContainerFilter(cnt.uuid.CONTRACTS);
		_common.create_newRecord(cnt.uuid.CONTRACTS);
		_salesPage.enterRecord_toCreateSalesBID(CONTRACT_SALES_PARAMETER_2);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CONTRACTS, app.GridCells.CODE, 'CONTRACT_SALES_CODE_2');
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		//4_SALES-CONTRACT
		_common.clear_subContainerFilter(cnt.uuid.CONTRACTS);
		_common.create_newRecord(cnt.uuid.CONTRACTS);
		_salesPage.enterRecord_toCreateSalesBID(CONTRACT_SALES_PARAMETER_3);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CONTRACTS, app.GridCells.CODE, 'CONTRACT_SALES_CODE_3');
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.minimizeContainer(cnt.uuid.CONTRACTS);
	});

	it('TC - customizing module', function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART).search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CUSTOMIZING);
		_common.openTab(app.TabBar.MASTER_DATA).then(() => {
			_common.select_tabFromFooter(cnt.uuid.ENTITY_TYPES, app.FooterTab.DATA_TYPES, 0);
		});
		_common.search_inSubContainer(cnt.uuid.ENTITY_TYPES, 'Certificate Status');
		cy.REFRESH_CONTAINER();
		_common.clickOn_cellHasUniqueValue(cnt.uuid.ENTITY_TYPES, app.GridCells.NAME, 'Certificate Status');
		_common.openTab(app.TabBar.MASTER_DATA).then(() => {
			_common.select_tabFromFooter(cnt.uuid.INSTANCES, app.FooterTab.DATA_RECORDS, 2);
		});
		_common.clear_subContainerFilter(cnt.uuid.INSTANCES);
		_common.clickOn_cellHasUniqueValue(cnt.uuid.INSTANCES, app.GridCells.DESCRIPTION_INFO, 'Requested');
		_validate.customizing_DataRecordCheckBox(app.GridCells.IS_DEFAULT, commonLocators.CommonKeys.CHECK);
		cy.SAVE();
		_common.clickOn_cellHasUniqueValue(cnt.uuid.INSTANCES, app.GridCells.DESCRIPTION_INFO, 'Date Received');
		_validate.customizing_DataRecordCheckBox(app.GridCells.IS_DEFAULT, commonLocators.CommonKeys.UNCHECK);
		cy.SAVE();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART).search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CERTIFICATE);
	});

	it('TC - Create a record in certificates container', function () {
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART);
		_common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.CERTIFICATE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0);
			_common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_1);
			_common.setup_gridLayout(cnt.uuid.CERTIFICATE, CONTAINER_COLUMNS_CERTIFICATE);
		});
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.CERTIFICATE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CERTIFICATE_DETAIL, app.FooterTab.CERTIFICATE_DETAIL, 1);
		});
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.CERTIFICATE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0);
		});
		_common.maximizeContainer(cnt.uuid.CERTIFICATE);

		//1_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO_4);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CURRENCY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CERTIFICATE.CURRENCY);
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL));
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL));
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.VALID_TO, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL, 2));
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.REFERENCE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL));
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CON_HEADER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('CONTRACT_CODE'));
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('CONTRACT_SALES_CODE'));
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.REFERENCE, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_CERTIFICATE.REFERENCE[0]);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_0');
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CERTIFICATE_STATUS);
		_common.waitForLoaderToDisappear();
		_common.changeStatus_fromModal(commonLocators.CommonKeys.DATE_RECEIVED);
		cy.SAVE();
		_common.waitForLoaderToDisappear();

		//2__CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CURRENCY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CERTIFICATE.CURRENCY);
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL));
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.VALID_FROM, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL));
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.VALID_TO, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(commonLocators.CommonKeys.INCREMENTED_SMALL, 2));
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.REFERENCE_DATE, app.InputFields.INPUT_GROUP_CONTENT_DATE_PICKER_INPUT, _common.getDate(CommonLocators.CommonKeys.CURRENT_SMALL));
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.REFERENCE, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_CERTIFICATE.REFERENCE[1]);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_1');
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD);
		_common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.CHANGE_CERTIFICATE_STATUS);
		_common.waitForLoaderToDisappear();
		_common.changeStatus_fromModal(commonLocators.CommonKeys.DATE_RECEIVED);
		cy.SAVE();
		_common.waitForLoaderToDisappear();

		//3_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CERTIFICATE.TYPE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CURRENCY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CERTIFICATE.CURRENCY);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CON_HEADER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('CONTRACT_CODE_1'));
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('CONTRACT_SALES_CODE_1'));
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_2');
		_common.waitForLoaderToDisappear();

		//4_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CERTIFICATE.TYPE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CURRENCY_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CERTIFICATE.CURRENCY);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO_1);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CON_HEADER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('CONTRACT_CODE_2'));
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('CONTRACT_SALES_CODE_2'));
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_3');
		_common.waitForLoaderToDisappear();

		//5_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CERTIFICATE.TYPE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO_2);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.CON_HEADER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('CONTRACT_CODE_3'));
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, Cypress.env('CONTRACT_SALES_CODE_3'));
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_4');
		_common.waitForLoaderToDisappear();

		//6_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO_3);
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_5');
		_common.waitForLoaderToDisappear();

		//7_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_2);
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, CONTAINERS_CERTIFICATE.COMMENT[0]);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_6');
		_common.waitForLoaderToDisappear();

		//8_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_2);
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.COMMENT_TEXT, app.InputFields.DOMAIN_TYPE_COMMENT, CONTAINERS_CERTIFICATE.COMMENT[1]);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_7');
		_common.waitForLoaderToDisappear();

		//9_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_3);
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.ISSUER, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_CERTIFICATE.ISSUER[0]);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_8');
		_common.waitForLoaderToDisappear();

		//10_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_3);
		_common.edit_containerCell(cnt.uuid.CERTIFICATE, app.GridCells.ISSUER, app.InputFields.DOMAIN_TYPE_DESCRIPTION, CONTAINERS_CERTIFICATE.ISSUER[1]);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_9');
		_common.waitForLoaderToDisappear();

		//11_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_3);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_ISSUER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_10');
		_common.waitForLoaderToDisappear();

		//12_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_3);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_ISSUER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, BP_NAME_1);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_11');
		_common.waitForLoaderToDisappear();

		//13_CERTIFICATE
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.clickOn_toolbarButton(cnt.uuid.CERTIFICATE, btn.ToolBar.ICO_REC_NEW);
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT_SALES.BUSINESS_PARTNER_NAME);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.edit_dropdownCellWithInput(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, PROJECT_NO_5);
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.saveCellDataToEnv(cnt.uuid.CERTIFICATE, app.GridCells.CODE, 'CERTIFICATE_CODE_100');
		_common.waitForLoaderToDisappear();
		_common.select_activeRowInContainer(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.minimizeContainer(cnt.uuid.CERTIFICATE);
	});

	it('TC - Check searching in certificates container', function () {
		_common.openTab(app.TabBar.CERTIFICATE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0);
			_common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_1);
			_common.setup_gridLayout(cnt.uuid.CERTIFICATE, CONTAINER_COLUMNS_CERTIFICATE);
			_common.set_columnAtTop([CONTAINER_COLUMNS_CERTIFICATE.code, CONTAINER_COLUMNS_CERTIFICATE.projectfk], cnt.uuid.CERTIFICATE);
		});
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar();
		_common.maximizeContainer(cnt.uuid.CERTIFICATE);

		//AC1- code, prjNum
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.search_inSubContainer(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.SEARCH[6]);
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, PROJECT_NO_5, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, PROJECT_NO_5);
		_common.select_rowHasValue(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.SEARCH[6]);
		_common.assert_cellData;
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CODE, CONTAINERS_CERTIFICATE.CODE);

		//AC2- status
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.search_inSubContainer(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED);
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 1);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 2);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 3);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 4);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 5);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 6);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 7);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 8);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, commonLocators.CommonKeys.REQUESTED, 9);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_STATUS_FK, commonLocators.CommonKeys.REQUESTED);
		_common.waitForLoaderToDisappear();

		//AC3- type, bp
		_common.waitForLoaderToDisappear();
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.search_inSubContainer(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.SEARCH[1]);
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CONTRACT_SALES.BUSINESS_PARTNER_NAME, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, CONTAINERS_CONTRACT_SALES.BUSINESS_PARTNER_NAME);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.TYPE, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_TYPE_FK, CONTAINERS_CERTIFICATE.TYPE);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.TYPE, 1);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_TYPE_FK, CONTAINERS_CERTIFICATE.TYPE);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.TYPE, 2);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CERTIFICATE_TYPE_FK, CONTAINERS_CERTIFICATE.TYPE);
		_common.waitForLoaderToDisappear();

		//AC4- prjNum, prjName, bp
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.search_inSubContainer(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.SEARCH[0]);
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, BP_NAME);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME, 1);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, BP_NAME);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, MODAL_PROJECTS.PROJECT_DESC[0], 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_NAME_1, MODAL_PROJECTS.PROJECT_DESC[0]);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, PROJECT_NO, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, PROJECT_NO);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, MODAL_PROJECTS.PROJECT_DESC[1], 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_NAME_1, MODAL_PROJECTS.PROJECT_DESC[1]);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, PROJECT_NO_1, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, PROJECT_NO_1);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, MODAL_PROJECTS.PROJECT_DESC[2], 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_NAME_1, MODAL_PROJECTS.PROJECT_DESC[2]);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, PROJECT_NO_2, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, PROJECT_NO_2);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, MODAL_PROJECTS.PROJECT_DESC[2], 1);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_NAME_1, MODAL_PROJECTS.PROJECT_DESC[2]);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, PROJECT_NO_3, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.PROJECT_FK, PROJECT_NO_3);
		_common.waitForLoaderToDisappear();
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.minimizeContainer(cnt.uuid.CERTIFICATE);

		_common.openTab(app.TabBar.CERTIFICATE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0);
			_common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_1);
			_common.setup_gridLayout(cnt.uuid.CERTIFICATE, CONTAINER_COLUMNS_CERTIFICATE);
			_common.set_columnAtTop(
				[
					CONTAINER_COLUMNS_CERTIFICATE.businesspartnerfk,
					CONTAINER_COLUMNS_CERTIFICATE.currencyfk,
					CONTAINER_COLUMNS_CERTIFICATE.ordheaderfk,
					CONTAINER_COLUMNS_CERTIFICATE.OrdHeaderDescription,
					CONTAINER_COLUMNS_CERTIFICATE.ConHeaderDescription,
				],
				cnt.uuid.CERTIFICATE
			);
		});
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.CERTIFICATE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CERTIFICATE_DETAIL, app.FooterTab.CERTIFICATE_DETAIL, 1);
		});
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar();
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.maximizeContainer(cnt.uuid.CERTIFICATE);

		//AC5- currency
		_common.waitForLoaderToDisappear();
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.search_inSubContainer(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.CURRENCY);
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.CURRENCY, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CURRENCY_FK, CONTAINERS_CERTIFICATE.CURRENCY);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.CURRENCY, 1);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CURRENCY_FK, CONTAINERS_CERTIFICATE.CURRENCY);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.CURRENCY, 2);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CURRENCY_FK, CONTAINERS_CERTIFICATE.CURRENCY);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.CURRENCY, 3);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CURRENCY_FK, CONTAINERS_CERTIFICATE.CURRENCY);
		_common.waitForLoaderToDisappear();

		//AC6- contract-desc
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.search_inSubContainer(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.SEARCH[3]);
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTRACT_NAME, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CON_HEADER_DESCRIPTION_1, CONTRACT_NAME);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTRACT_NAME_1, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CON_HEADER_DESCRIPTION_1, CONTRACT_NAME_1);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTRACT_NAME_1, 1);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CON_HEADER_DESCRIPTION_1, CONTRACT_NAME_1);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTRACT_NAME_2, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.CON_HEADER_DESCRIPTION_1, CONTRACT_NAME_2);

		//AC7- bp, sales-contr
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.search_inSubContainer(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.SEARCH[4]);
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, Cypress.env('CONTRACT_SALES_CODE'), 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_FK, Cypress.env('CONTRACT_SALES_CODE'));
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, Cypress.env('CONTRACT_SALES_CODE_1'), 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_FK, Cypress.env('CONTRACT_SALES_CODE_1'));
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, Cypress.env('CONTRACT_SALES_CODE_2'), 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_FK, Cypress.env('CONTRACT_SALES_CODE_2'));
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, Cypress.env('CONTRACT_SALES_CODE_3'), 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_FK, Cypress.env('CONTRACT_SALES_CODE_3'));
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME_2, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, BP_NAME_2);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME_2, 1);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, BP_NAME_2);
		_common.waitForLoaderToDisappear();
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.minimizeContainer(cnt.uuid.CERTIFICATE);

		//AC8- sales-contr-desc
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.search_inSubContainer(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.SEARCH[7]);
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTRACT_SALES_DESC_1, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_DESCRIPTION, CONTRACT_SALES_DESC_1);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTRACT_SALES_DESC_1, 1);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.ORD_HEADER_DESCRIPTION, CONTRACT_SALES_DESC_1);

		_common.openTab(app.TabBar.CERTIFICATE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CERTIFICATE, app.FooterTab.CERTIFICATES, 0);
			_common.set_containerLayoutUnderEditView(CommonLocators.CommonKeys.LAYOUT_1);
			_common.setup_gridLayout(cnt.uuid.CERTIFICATE, CONTAINER_COLUMNS_CERTIFICATE);
			_common.set_columnAtTop(
				[CONTAINER_COLUMNS_CERTIFICATE.businesspartnerfk, CONTAINER_COLUMNS_CERTIFICATE.reference, CONTAINER_COLUMNS_CERTIFICATE.commenttext, CONTAINER_COLUMNS_CERTIFICATE.issuer, CONTAINER_COLUMNS_CERTIFICATE.businesspartnerissuerfk],
				cnt.uuid.CERTIFICATE
			);
		});
		_common.waitForLoaderToDisappear();
		_common.openTab(app.TabBar.CERTIFICATE).then(() => {
			_common.select_tabFromFooter(cnt.uuid.CERTIFICATE_DETAIL, app.FooterTab.CERTIFICATE_DETAIL, 1);
		});
		_common.waitForLoaderToDisappear();
		_common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).clear_searchInSidebar();
		_common.waitForLoaderToDisappear();
		cy.SAVE();
		_common.waitForLoaderToDisappear();
		_common.maximizeContainer(cnt.uuid.CERTIFICATE);

		//AC9- bp,ref-name, comment, issuer, isser-bp
		_common.clear_subContainerFilter(cnt.uuid.CERTIFICATE);
		_common.search_inSubContainer(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.SEARCH[5]);
		_common.waitForLoaderToDisappear();
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.select_allContainerData(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();

		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.REFERENCE[0], 0);
		_common.assert_cellDataByContent_inContainer(cnt.uuid.CERTIFICATE, app.GridCells.REFERENCE, CONTAINERS_CERTIFICATE.REFERENCE[0]);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.REFERENCE[1], 0);
		_common.assert_activeRow_cellDataByContent_inContainer(cnt.uuid.CERTIFICATE, app.GridCells.REFERENCE, CONTAINERS_CERTIFICATE.REFERENCE[1]);
		_common.waitForLoaderToDisappear();

		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.COMMENT[0], 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.COMMENT_TEXT, CONTAINERS_CERTIFICATE.COMMENT[0]);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.COMMENT[1], 0);
		_common.assert_activeRow_cellDataByContent_inContainer(cnt.uuid.CERTIFICATE, app.GridCells.COMMENT_TEXT, CONTAINERS_CERTIFICATE.COMMENT[1]);
		_common.waitForLoaderToDisappear();

		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.ISSUER[0], 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.ISSUER, CONTAINERS_CERTIFICATE.ISSUER[0]);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, CONTAINERS_CERTIFICATE.ISSUER[1], 0);
		_common.assert_activeRow_cellDataByContent_inContainer(cnt.uuid.CERTIFICATE, app.GridCells.ISSUER, CONTAINERS_CERTIFICATE.ISSUER[1]);
		_common.waitForLoaderToDisappear();

		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME_1, 0);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, BP_NAME_1);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME_1, 1);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, BP_NAME_1);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME_1, 2);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, BP_NAME_1);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME_1, 3);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_FK, BP_NAME_1);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME_1, 4);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_ISSUER_FK, BP_NAME_1);
		_common.select_rowHasValue_onIndexBased(cnt.uuid.CERTIFICATE, BP_NAME_1, 5);
		_common.assert_cellData_insideActiveRow(cnt.uuid.CERTIFICATE, app.GridCells.BUSINESS_PARTNER_ISSUER_FK, BP_NAME_1);
		_common.waitForLoaderToDisappear();
		_common.minimizeContainer(cnt.uuid.CERTIFICATE);
		_common.waitForLoaderToDisappear();
	});
});
