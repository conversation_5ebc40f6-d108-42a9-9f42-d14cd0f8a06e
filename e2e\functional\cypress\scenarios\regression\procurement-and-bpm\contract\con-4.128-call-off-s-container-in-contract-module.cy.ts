import { commonLocators, app, tile, cnt, sidebar, btn } from "cypress/locators";
import CommonLocators from "cypress/locators/common-locators";
import { _common, _package, _procurementContractPage, _procurementPage, _projectPage, _schedulePage, _validate, _wizardCreateRequest } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";
import { PACKAGE_TOTAL_TRANSLATION } from "cypress/pages/variables";

const ALLURE = Cypress.Allure.reporter.getInterface();
const CONTRACT_DESCRIPTION = _common.generateRandomString(4);


let PROCUREMENT_CONTRACT_PARAMETER: DataCells
let CONTAINER_COLUMNS_CONTRACT
let CONTAINERS_CONTRACT
let CONTAINER_COLUMNS_CALL_OFFS


ALLURE.epic("PROCUREMENT AND BPM")
ALLURE.feature("Contract")
ALLURE.story("PCM- 4.128 | Call offs container in Contract module")

describe("PCM- 4.128 | Call offs container in Contract module", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("procurement-and-bpm/con-4.128-call-off-s-container-in-contract-module.json").then((data) => {
            this.data = data;
            CONTAINERS_CONTRACT = this.data.CONTAINERS.CONTRACT
            CONTAINER_COLUMNS_CONTRACT = this.data.CONTAINER_COLUMNS.CONTRACT
            PROCUREMENT_CONTRACT_PARAMETER = {
                [commonLocators.CommonLabels.CONFIGURATION]: CONTAINERS_CONTRACT.CONFIGURATION,
                [commonLocators.CommonLabels.BUSINESS_PARTNER]: CONTAINERS_CONTRACT.BUSINESS_PARTNER_NAME
            }

            CONTAINER_COLUMNS_CALL_OFFS=this.data.CONTAINER_COLUMNS.CALL_OFFS
    
        }).then(() => {
            cy.preLoading(Cypress.env('adminUserName'), Cypress.env('adminPassword'), Cypress.env('parentCompanyName'), Cypress.env('childCompanyName'));
        });
    })
    after(() => {
        cy.LOGOUT();
    });

    it("TC - Create new contract record", function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.CONTRACT);
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.setDefaultView(app.TabBar.CONTRACT)
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
            _common.setup_gridLayout(cnt.uuid.PROCUREMENTCONTRACT, CONTAINER_COLUMNS_CONTRACT)
            _common.set_columnAtTop([CONTAINER_COLUMNS_CONTRACT.projectfk, CONTAINER_COLUMNS_CONTRACT.ProjectFkProjectName, CONTAINER_COLUMNS_CONTRACT.code, CONTAINER_COLUMNS_CONTRACT.businesspartnerfk,CONTAINER_COLUMNS_CONTRACT.structureCode,CONTAINER_COLUMNS_CONTRACT.description], cnt.uuid.PROCUREMENTCONTRACT)
        });
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.clear_searchInSidebar()
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.create_newRecord(cnt.uuid.PROCUREMENTCONTRACT)
        _procurementContractPage.enterRecord_createNewProcurementContract_fromModal(PROCUREMENT_CONTRACT_PARAMETER);
cy.SAVE();
_common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK);
cy.SAVE();
        _common.clickOn_activeRowCell(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.PROJECT_FK)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.PROJECT_FK,commonLocators.CommonKeys.GRID,app.InputFields.INPUT_GROUP_CONTENT,Cypress.env('PROJECT_NUMBER'))
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()       
        _common.enterRecord_inNewRow(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.DESCRIPTION,app.InputFields.DOMAIN_TYPE_DESCRIPTION,CONTRACT_DESCRIPTION)
        _common.select_activeRowInContainer(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,CONTRACT_DESCRIPTION)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.DESCRIPTION,CONTRACT_DESCRIPTION)
        _common.waitForLoaderToDisappear()
    });

    it("TC - Create Generals, Certificates, Header Text", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.GENERALS_CONTRACT, app.FooterTab.GENERALS, 1);
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.GENERALS_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.GENERALS_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.GENERALS_CONTRACT, app.GridCells.PRC_GENERALS_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.NACHLASS)
        _common.select_activeRowInContainer(cnt.uuid.GENERALS_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.enterRecord_inNewRow(cnt.uuid.GENERALS_CONTRACT, app.GridCells.VALUE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.VALUE)
        _common.select_activeRowInContainer(cnt.uuid.GENERALS_CONTRACT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CERTIFICATES, app.FooterTab.CERTIFICATES, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithInput(cnt.uuid.CONTRACT_CERTIFICATES, app.GridCells.BPD_CERTIFICATE_TYPE_FK, commonLocators.CommonKeys.LIST, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_CONTRACT.CIS_CERTIFICATE)
        _common.select_activeRowInContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACTHEADERTEXT, app.FooterTab.HEADER_TEXT, 1);
            _common.waitForLoaderToDisappear()
        });
        _common.maximizeContainer(cnt.uuid.CONTRACTHEADERTEXT)
        _common.waitForLoaderToDisappear()
        _common.delete_recordInContainer_ifRecordExists(cnt.uuid.CONTRACTHEADERTEXT)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CONTRACTHEADERTEXT)
        _common.waitForLoaderToDisappear()
        _common.select_activeRowInContainer(cnt.uuid.CONTRACTHEADERTEXT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACTHEADERTEXT,app.GridCells.PRC_TEXT_TYPE_FK,commonLocators.CommonKeys.GRID,CONTAINERS_CONTRACT.SUPPLIER_TEXT)
        _common.select_activeRowInContainer(cnt.uuid.CONTRACTHEADERTEXT)
        _common.waitForLoaderToDisappear()
        _common.edit_dropdownCellWithCaret(cnt.uuid.CONTRACTHEADERTEXT,app.GridCells.TEXT_MODULE_TYPE_FK,commonLocators.CommonKeys.LIST,CONTAINERS_CONTRACT.INTERNAL_FOOTER)
        _common.select_activeRowInContainer(cnt.uuid.CONTRACTHEADERTEXT)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.minimizeContainer(cnt.uuid.CONTRACTHEADERTEXT)
        _common.waitForLoaderToDisappear()
    })

    it("TC - Verify call offs new button is working", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CALLOFFS, app.FooterTab.CALL_OFFS, 2);
            _common.setup_gridLayout(cnt.uuid.CALLOFFS, CONTAINER_COLUMNS_CALL_OFFS)
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        cy.wait(2000) // Added this as creation of call offs take time.
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,CONTRACT_DESCRIPTION)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.DESCRIPTION,CONTRACT_DESCRIPTION)
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CALLOFFS, app.FooterTab.CALL_OFFS, 2);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.PURCHASE_ORDER)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.PURCHASE_ORDER)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.CALL_OFF)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.CALL_OFF)
    })

    it("TC - Verify call offs delete button is working ", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CALLOFFS, app.FooterTab.CALL_OFFS, 2);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.CALL_OFF)
        _common.waitForLoaderToDisappear()
        _common.delete_recordFromContainer(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
         _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()

        _common.waitForLoaderToDisappear()
        cy.REFRESH_CONTAINER()
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.waitForLoaderToDisappear()
    
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,CONTRACT_DESCRIPTION)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.DESCRIPTION,CONTRACT_DESCRIPTION)
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CALLOFFS, app.FooterTab.CALL_OFFS, 2);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        _validate.verify_isRecordDeleted_basedOnColumn(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS)
    
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.clear_subContainerFilter(cnt.uuid.PROCUREMENTCONTRACT)
        _common.search_inSubContainer(cnt.uuid.PROCUREMENTCONTRACT,CONTRACT_DESCRIPTION)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.DESCRIPTION,CONTRACT_DESCRIPTION)
        _common.waitForLoaderToDisappear()


        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CALLOFFS, app.FooterTab.CALL_OFFS, 2);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        _common.create_newRecord(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        cy.wait(2000) // Added this as creation of call offs take time.
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()

        _common.saveCellDataToEnv(cnt.uuid.CALLOFFS,app.GridCells.CODE,"CALL_OFF")
    });

    it("TC - After created new record, it will show basic contract together", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CALLOFFS, app.FooterTab.CALL_OFFS, 2);
        });
        _common.clear_subContainerFilter(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.PURCHASE_ORDER)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.PURCHASE_ORDER)
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.CALL_OFF)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.CALL_OFF)
    })

    it("TC - Each fields is non editable", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CALLOFFS, app.FooterTab.CALL_OFFS, 2);
        });
        _common.clear_subContainerFilter(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.PURCHASE_ORDER)
        _validate.verify_inputFieldVisibility(cnt.uuid.CALLOFFS,app.GridCells.DESCRIPTION,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.CALLOFFS,app.GridCells.PROJECT_FK,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.CALLOFFS,app.GridCells.STRUCTURE_CODE,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.CALLOFFS,app.GridCells.CONFIGURATION_FK,commonLocators.CommonKeys.NOT_VISIBLE)
    
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.CALL_OFF)
        _validate.verify_inputFieldVisibility(cnt.uuid.CALLOFFS,app.GridCells.DESCRIPTION,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.CALLOFFS,app.GridCells.PROJECT_FK,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.CALLOFFS,app.GridCells.STRUCTURE_CODE,commonLocators.CommonKeys.NOT_VISIBLE)
        _validate.verify_inputFieldVisibility(cnt.uuid.CALLOFFS,app.GridCells.CONFIGURATION_FK,commonLocators.CommonKeys.NOT_VISIBLE)
    })

    it("TC - After created record, the 'purchase orders' should show 'call off'", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CALLOFFS, app.FooterTab.CALL_OFFS, 2);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CALLOFFS,app.GridCells.CODE,Cypress.env("CALL_OFF"))
        _common.assert_cellData_insideActiveRow(cnt.uuid.CALLOFFS,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.CALL_OFF)


        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });

        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.CODE,Cypress.env("CALL_OFF"))
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.PURCHASE_ORDERS,CommonLocators.CommonKeys.CALL_OFF)
    })


    it("TC - Click go to button, it will select new record in contracts container", function () {
        _common.clickOn_toolbarButton(cnt.uuid.CALLOFFS,btn.ToolBar.ICO_GO_TO)
        _common.waitForLoaderToDisappear()

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.PROCUREMENTCONTRACT, app.FooterTab.CONTRACTS, 0);
        });
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROCUREMENTCONTRACT,app.GridCells.CODE,Cypress.env("CALL_OFF"))
    })

    it("TC - After created record, it should create totals/certificate/generals/header text...... together", function () {
        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.waitForLoaderToDisappear()
            _common.select_tabFromFooter(cnt.uuid.CALLOFFS, app.FooterTab.CALL_OFFS, 2);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CALLOFFS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CALLOFFS,app.GridCells.CODE,Cypress.env("CALL_OFF"))

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_CERTIFICATES, app.FooterTab.CERTIFICATES, 1);
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_CERTIFICATES,app.GridCells.BPD_CERTIFICATE_TYPE_FK,CONTAINERS_CONTRACT.CIS_CERTIFICATE)
        _common.select_activeRowInContainer(cnt.uuid.CONTRACT_CERTIFICATES)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_CERTIFICATES,app.GridCells.BPD_CERTIFICATE_TYPE_FK,CONTAINERS_CONTRACT.CIS_CERTIFICATE)
    
    

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.GENERALS_CONTRACT, app.FooterTab.GENERALS, 1);
            _common.waitForLoaderToDisappear()
        });
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.GENERALS_CONTRACT)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.GENERALS_CONTRACT,app.GridCells.PRC_GENERALS_TYPE_FK,CONTAINERS_CONTRACT.NACHLASS)
        _common.select_activeRowInContainer(cnt.uuid.GENERALS_CONTRACT)
        _common.assert_cellData_insideActiveRow(cnt.uuid.GENERALS_CONTRACT,app.GridCells.PRC_GENERALS_TYPE_FK,CONTAINERS_CONTRACT.NACHLASS)


        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACT_TOTALS, app.FooterTab.TOTALS, 1);
            _common.waitForLoaderToDisappear()
        });

        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.CONTRACT_TOTALS)
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACT_TOTALS,app.GridCells.TRANSLATED,CONTAINERS_CONTRACT.TOTAL,PACKAGE_TOTAL_TRANSLATION)
        _common.select_activeRowInContainer(cnt.uuid.CONTRACT_TOTALS)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACT_TOTALS,app.GridCells.TRANSLATED,CONTAINERS_CONTRACT.TOTAL,PACKAGE_TOTAL_TRANSLATION)

        _common.openTab(app.TabBar.CONTRACT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.CONTRACTHEADERTEXT, app.FooterTab.HEADER_TEXT, 1);
            _common.waitForLoaderToDisappear()
        });
        _common.maximizeContainer(cnt.uuid.CONTRACTHEADERTEXT)
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.CONTRACTHEADERTEXT,app.GridCells.PRC_TEXT_TYPE_FK,CONTAINERS_CONTRACT.SUPPLIER_TEXT)
        _common.select_activeRowInContainer(cnt.uuid.CONTRACTHEADERTEXT)
        _common.assert_cellData_insideActiveRow(cnt.uuid.CONTRACTHEADERTEXT,app.GridCells.PRC_TEXT_TYPE_FK,CONTAINERS_CONTRACT.SUPPLIER_TEXT)


        
       
    })
});