{"timekeeping": {"employee": {"createResourcesByEmployeesWizard": {"title": "Create Resources by Employees", "resKind": "Resource Kind", "resGroup": "Resource Group", "uomTime": "Time Unit", "Site": "Site", "resType": "Resource Type", "costCode": "Cost Code", "generatedInfo": "resources are generated:", "genaralNotGeneratedInfo": "These employees could not be generated! Either they have already been generated or the same code already exists in Resources:", "noGeneratedAtAllInfo": "All selected employees could not generated! Either they all have already been generated or the same code already exists in Resources", "noTimesymbolSelected": "No Timesymbol selected. Please select one", "requestError": "An error occurred while generating planned absences"}, "entityTimekeepingGroupDescription": "Timekeeping Group-Description", "entityCurrentCrewLeaderCode": "Current Crew Leader-Code", "entityEmployeeCrewLeaderCode": "Crew Leader-Code", "employeesDocument": "Employee Document", "certificateListTitle": "Employee Certificate", "certificateDetailTitle": "Certificate Detail", "vacationAccount": "Employee Vacation Account", "vacationAccountDetailTitle": "Employee Vacation Account Detail", "employeesDocumentDetailTitle": "Employees Document Detail", "employeeDocumentEntity": "Employee Document", "employeeCertificateEntity": "Employee Certificate", "changeStatus": "Change Certificate Status", "employeesMap": "Employees Map", "entityEmployee": "Employee", "entityEmployeeDocumentTypeFk": "Document Type", "entityFileArchiveDocFk": "File Archive", "entityUrl": "Url", "entityIsHiddenInPublicPpi": "Is Hidden In Public Api", "entityEmpCertificateType": "Certificate Type", "entityEmpCertificateStatusFk": "Certificate Status", "entityComment": "Comment", "entityRemark": "Remark", "entityContactFk": "Contact", "contactInfo": "Contact Info", "entityDescriptionInfo": "Description Info", "entityEmployeeNumber": "Employee Number", "employeeListTitle": "Employees", "employeeDetailTitle": "Employee Details", "employeeStatusFk": "Employee Status", "employeeSkillStatusFk": "Employee Skill Status", "employeeStatusWizard": "Employee Status Wizard", "employeeSkillStatusWizard": "Employee Skill Status Wizard", "entityFirstName": "First Name", "entityFamilyName": "Family Name", "entityInitials": "Initials", "entityOperatingCompany": "Operating company", "entityBirthDate": "Birth date", "entityDate": "Date", "entityTerminalDate": "Terminal Date", "entityIsCrewLeader": "Crew Leader", "entityIsWhiteCollar": "White-collar", "IsHiredLabor": "<PERSON><PERSON>", "IsTimekeeper": "Timekeeper", "entityShift": "Shift", "entityEmployeeGroup": "Employee Group", "entityTimekeepingGroup": "Timekeeping Group", "entityProfessionalCategoryFk": "Professional Category", "entityPaymentGroupFk": "Payment Group", "disableDone": "Employee is disabled", "alreadyDisabled": "Employee is already disabled", "enableDone": "Employee is enabled", "alreadyEnabled": "Employee is already enabled", "crewAssignmentListTitle": "Crew Assignments", "crewAssignmentDetailTitle": "Crew Assignment Details", "entityFromDateTime": "From", "entityToDateTime": "To", "entityCrewLeader": "Crew Leader", "entityCrewAssignment": "Crew Assignment", "pictureListTitle": "Picture List", "entityPictureDate": "Picture Upload Date", "entityUpload": "Upload", "entityPicture": "Picture", "entityUploadAndCreate": "upload and Create", "entityDownload": "Download", "entityDownloadPdf": "Download Pdf", "entityCancelUpload": "Cancel Upload", "entityPreview": "Preview", "entityDefaultPreviewProgram": "Default Preview Program", "entityEditOfficeDocument": "Edit Office Document", "entitySaveDocumentToRib40": "Save Document To RIB 4.0", "pictureViewTitle": "Picture View", "plannedAbsenceListTitle": "Planned Absences", "plannedAbsenceDetailTitle": "Planned Absence Details", "entityTimekeepingPlannedAbsence": "Planned Absence", "entityTimeSymbol": "Time Symbol", "employeeSkillListTitle": "Skills", "employeeSkillDetailTitle": "<PERSON><PERSON> Details", "employeeDefaultListTitle": "Employee Defaults", "employeeDefaultDetailTitle": "Employee Default Details", "employeeWorkingTimeAccountListTitle": "Working Time Account", "employeeWorkingTimeAccountDetailTitle": "Working Time Account Details", "group": "Group", "crewMemberListTitle": "Crew Member", "crewMemberDetailTitle": "Crew Member Details", "employeeWorkingTimeModelListTitle": "Employee Working Time Model", "employeeWorkingTimeModelDetailTitle": "Employee Working Time Model Details", "employeeCrewMemberEntity": "Crew Member", "employeeAssignment": "Assignment", "entityIsHiredLabor": "<PERSON><PERSON>", "entityIsTimekeeper": "Timekeeper", "skillDocumentListTitle": "Skill Documents", "skillDocumentDetailTitle": "Skill Document Details", "timekeepingEmployeeSkillDocumentEntity": "Skill Document", "entityEmployeeSkillDocTypeFk": "Skill Document", "entityBarcode": "Barcode", "entityOriginFileName": "Origin File Name", "entityRefreshDate": "Refresh Date", "entityLeaddays": "Lead Days", "enableEmployeeWizard": "Enable Employee Wizard", "disableEmployeeWizard": "Disable Employee Wizard", "entityCostGroup": "Timekeeping Cost Group", "entityWorkingTimeModel": "Working Time Model", "entityTimeSymbolGroup": "Time Symbol Group", "entityDueDate": "Due Date", "entityProjectAction": "Project Action", "entityControllingunit": "Controlling Unit", "entityReportStatus": "Report Status", "entitySheet": "Sheet", "entityAbsenceday": "Absence Day", "entityDuration": "Duration", "entityWorkingTimeAccountBalance": "Working Time Account Balance", "employeeArea": "Employee Area", "employeeSubArea": "Employee Sub Area", "EntityIsPayroll": "Is Payroll", "EntityHasOptedPayout": "<PERSON> opted pay out", "EntityValidFrom": "<PERSON><PERSON>", "EntityValidTo": "<PERSON><PERSON>", "EntityEmployeeWorkingtimeModelFk": "Working Time Model", "PlannedAbsenceStatusFk": "Planned Absence Status", "translationKeySetPlannedAbsenceStatus": "Set Planned Absence Status", "entityTimesymbolFk": "Time Symbol", "entityVacationBalance": "Vacation Balance", "entityYearlyVacation": "Yearly Vacation", "entityTrafficLight": "Traffic Light", "entityGenerateRecording": "Generate Recording", "entityCurrentCrewLeader": "Current Crew Leader", "ppsAssignmentListTitle": "PPS Assignment", "timekeepingEmployeeCertificationEntity": "Certification", "entityCertificateFk": "Certificate", "entityCertificatDescription": "Certificate-Description", "entityCertificateTypeFk": "Certificate Type", "empValidFrom": "Employee Certificate Valid From", "empValidTo": "Employee Certificate Valid To", "employeesCertification": "Employee Certifications", "employeesCertificationDetailTitle": "Employee Certifications Detail", "employeefallbackwtm": "Employee Fallback WTM", "IsfallbackActive": "Is Employee Fallback Active", "overlaps": "Code alreday in use. The code has to be unique within the timekeeping group.", "BookingDate": "Booking Date", "Comment": "Comment", "entityIsYearlyStartScheduleEntry": "Is YearlyStartScheduleEntry", "entityIsYearlyEndScheduleEntry": "IsYearlyExpireScheduleEntry", "CreateResource": "Create Resource", "employeeComponent": "Employee Map container", "entityFromTime": "From Time", "entityToTime": "To Time", "entityExternalId": "External ID", "entityRemainingVacationPreviousYear": "Remaining Vacation Previous Year", "entityVacationFromMinVacationPayPreYear": "Vacation from minimum vacation pay previous year", "entityGrantedVacationPreviousYear": "Granted Vacation previous year", "entityGrantedVacationCurrentYear": "Granted Vacation current year up to last billing month", "entityLastBillingMonthYear": "last billing month/year", "entityGrantedVacationInCurrentPeriod": "Granted Vacation in current period", "entityAvailableVacation": "Available Vacation", "entityRequestedVacationCurrentYear": "Requested vacation for the current year", "entityVacationTakenCurrentYear": "Vacation Taken Current Year", "entityPlaceOfWorkFk": "Place Of Work", "calculateVacationFields": "Calculate Vacation Balance"}}}