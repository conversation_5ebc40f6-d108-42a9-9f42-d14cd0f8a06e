import { app, sidebar, commonLocators, cnt, btn, tile } from "cypress/locators";
import { _common, _validate, _estimatePage, _projectPage } from "cypress/pages";
import { DataCells } from "cypress/pages/interfaces";


// VARIABLES----------------------------------------------------------------
const COSTCODE_CODE = "CCC-" + _common.generateRandomString(3);
const COSTCODE_DESC = "CCD-" + _common.generateRandomString(3);
const COSTCODE_CODE2 = "CCC-" + _common.generateRandomString(3);
const COSTCODE_DESC2 = "CCD-" + _common.generateRandomString(3);
const ESTIMATE_CODE = "ESTC-" + _common.generateRandomString(3);
const ESTIMATE_DESCRIPTION = "ESTD-" + _common.generateRandomString(3);
const LINE_ITEM_DESCRIPTION = "LID-" + _common.generateRandomString(3);
const PRJ_NO = _common.generateRandomString(5);
const PRJ_NAME = _common.generateRandomString(5);
let CONTAINERS_COST_CODES, CONTAINER_COLUMNS_COST_CODES, CONTAINERS_ESTIMATE, CONTAINERS_LINE_ITEM, CONTAINERS_RESOURCE, MODAL_UPDATE_ESTIMATE_WIZARD
let ESTIMATE_PARAMETERS: DataCells, LINE_ITEM_PARAMETERS: DataCells, RESOURCE_PARAMETERS: DataCells, UPDATE_ESTIMATE_PARAMETER: DataCells, PROJECT_PARAMETERS: DataCells, LINE_ITEM_PARAMETERS1: DataCells;

describe("EST- 9.3 | New wizard to enable / disable Cost Codes", () => {
  beforeEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
  afterEach(() => {
    cy.clearCookies();
    cy.clearLocalStorage();
    cy.WaitUntilLoaderComplete_Trial();
    cy.waitUntilDOMLoaded();
  });
    before(function () {
        cy.fixture("estimate/est-9.3-new-wizard-to-enable-disable-cost-codes.json").then((data) => {
            this.data = data;
            CONTAINERS_COST_CODES = this.data.CONTAINERS.COST_CODES
            CONTAINER_COLUMNS_COST_CODES = this.data.CONTAINER_COLUMNS.COST_CODES
            CONTAINERS_ESTIMATE = this.data.CONTAINERS.ESTIMATE;
            CONTAINERS_LINE_ITEM = this.data.CONTAINERS.LINE_ITEMS
            CONTAINERS_RESOURCE = this.data.CONTAINERS.RESOURCE
            ESTIMATE_PARAMETERS = {
                [app.GridCells.CODE]: ESTIMATE_CODE,
                [app.GridCells.DESCRIPTION_INFO]: ESTIMATE_DESCRIPTION,
                [app.GridCells.RUBRIC_CATEGORY_FK]: CONTAINERS_ESTIMATE.RUBRIC_CATEGORY,
                [app.GridCells.EST_TYPE_FK]: CONTAINERS_ESTIMATE.ESTIMATE_TYPE,
            };
            LINE_ITEM_PARAMETERS = {
                [app.GridCells.DESCRIPTION_INFO]: LINE_ITEM_DESCRIPTION,
                [app.GridCells.QUANTITY_SMALL]: CONTAINERS_LINE_ITEM.QUANTITY,
            };
            RESOURCE_PARAMETERS = {
                [app.GridCells.EST_RESOURCE_TYPE_SHORT_KEY]: CONTAINERS_RESOURCE.SHORT_KEY,
                [app.GridCells.CODE]: COSTCODE_DESC2,
            },
                MODAL_UPDATE_ESTIMATE_WIZARD = this.data.MODAL.UPDATE_ESTIMATE_WIZARD
            UPDATE_ESTIMATE_PARAMETER = {
                [commonLocators.CommonKeys.CHECKBOX]: MODAL_UPDATE_ESTIMATE_WIZARD
            },
                PROJECT_PARAMETERS = {
                    [commonLocators.CommonLabels.PROJECT_NUMBER]: PRJ_NO,
                    [commonLocators.CommonLabels.NAME]: PRJ_NAME,
                    [commonLocators.CommonLabels.CLERK]: CONTAINERS_ESTIMATE.CLERK
                },
                LINE_ITEM_PARAMETERS1 = {
                    [app.GridCells.DESCRIPTION_INFO]: LINE_ITEM_DESCRIPTION,
                    [app.GridCells.BAS_UOM_FK]: CONTAINERS_LINE_ITEM.UOM,
                }
            cy.preLoading(Cypress.env("adminUserName"),
                Cypress.env("adminPassword"),
                Cypress.env("parentCompanyName"),
                Cypress.env("childCompanyName"));
            _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
            _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.COST_CODES)
            _common.waitForLoaderToDisappear()
        });
    });

    it('TC - Create a Cost code in Cost Code Module', function () {
        _common.openTab(app.TabBar.COST_CODES).then(() => {
            _common.setDefaultView(app.TabBar.COST_CODES, commonLocators.CommonKeys.DEFAULT)
            _common.select_tabFromFooter(cnt.uuid.COST_CODES, app.FooterTab.COSTCODES, 0);
            _common.setup_gridLayout(cnt.uuid.COST_CODES, CONTAINER_COLUMNS_COST_CODES)
            _common.set_columnAtTop(CONTAINER_COLUMNS_COST_CODES.isactive, cnt.uuid.COST_CODES)
            _common.waitForLoaderToDisappear()
        });
        _common.clear_subContainerFilter(cnt.uuid.COST_CODES)
        _common.maximizeContainer(cnt.uuid.COST_CODES)
        _common.select_allContainerData(cnt.uuid.COST_CODES)
        _common.clickOn_expandCollapseButton(cnt.uuid.COST_CODES, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.COLLAPSE_ALL)
        cy.wait(1000)//required wait to open modal
        _common.create_newRecord(cnt.uuid.COST_CODES)
        _common.waitForLoaderToDisappear()
        _common.edit_containerCell(cnt.uuid.COST_CODES, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, COSTCODE_CODE)
        _common.edit_containerCell(cnt.uuid.COST_CODES, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, COSTCODE_DESC)
        _common.edit_dropdownCellWithInput(cnt.uuid.COST_CODES, app.GridCells.CURRENCY_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_COST_CODES.CURRENCY)
        _common.edit_containerCell(cnt.uuid.COST_CODES, app.GridCells.RATE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_COST_CODES.MARKET_RATE)
        cy.SAVE()
        _common.clickOn_toolbarButton(cnt.uuid.COST_CODES, btn.IconButtons.ICO_SUB_FLD_NEW)
        _common.edit_containerCell(cnt.uuid.COST_CODES, app.GridCells.CODE, app.InputFields.DOMAIN_TYPE_CODE, COSTCODE_CODE2)
        _common.edit_containerCell(cnt.uuid.COST_CODES, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, COSTCODE_DESC2)
        _common.edit_dropdownCellWithInput(cnt.uuid.COST_CODES, app.GridCells.CURRENCY_FK, commonLocators.CommonKeys.GRID, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_COST_CODES.CURRENCY)
        _common.edit_containerCell(cnt.uuid.COST_CODES, app.GridCells.RATE, app.InputFields.INPUT_GROUP_CONTENT, CONTAINERS_COST_CODES.MARKET_RATE)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
    })

    it('TC - Verify users must not be able to manually adjust the is active flag in cost code master', function () {
        _common.search_inSubContainer(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _validate.verify_checkBoxDisabled_forActiveCell(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE)
    })

    it('TC - Verify when users run the wizard to disable a child cost code, only that child is disabled.', function () {
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.ENABLE_DISABLE_COST_CODE)
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        _common.select_allContainerData(cnt.uuid.COST_CODES)
        _common.clickOn_expandCollapseButton(cnt.uuid.COST_CODES, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.CHECK)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.UNCHECK)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.ENABLE_DISABLE_COST_CODE)
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
    })

    it('TC - Verify when users run the wizard to disable a parent, parent + children are disabled and price version, used in company editable ', function () {
        _common.select_allContainerData(cnt.uuid.COST_CODES)
        _common.clickOn_expandCollapseButton(cnt.uuid.COST_CODES, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.ENABLE_DISABLE_COST_CODE)
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        cy.wait(2000)//required wait to open modal
        _common.select_allContainerData(cnt.uuid.COST_CODES)
        _common.clickOn_expandCollapseButton(cnt.uuid.COST_CODES, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.UNCHECK)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.UNCHECK)
        _common.minimizeContainer(cnt.uuid.COST_CODES)
        _common.openTab(app.TabBar.COST_CODES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PRICE_VERSION_V1, app.FooterTab.PRICE_VERSIONS, 1);
        });
        _common.clear_subContainerFilter(cnt.uuid.PRICE_VERSION_V1)
        _common.create_newRecord(cnt.uuid.PRICE_VERSION_V1)
        _common.edit_containerCell(cnt.uuid.PRICE_VERSION_V1, app.GridCells.DESCRIPTION_INFO, app.InputFields.DOMAIN_TYPE_TRANSLATION, COSTCODE_DESC)
        cy.SAVE()
        _common.openTab(app.TabBar.COST_CODES).then(() => {
            _common.select_tabFromFooter(cnt.uuid.USED_IN_COMPANY_COST_CODES, app.FooterTab.USED_IN_COMPANY);
        })
        _common.waitForLoaderToDisappear()
        _common.clickOn_cellHasUniqueValue(cnt.uuid.USED_IN_COMPANY_COST_CODES, app.GridCells.CODE, CONTAINERS_COST_CODES.COMPANY_CODE)
        _common.set_cellCheckboxValue(cnt.uuid.USED_IN_COMPANY_COST_CODES, app.GridCells.IS_CHECKED_SMALL_CASE, commonLocators.CommonKeys.UNCHECK)
        _common.set_cellCheckboxValue(cnt.uuid.USED_IN_COMPANY_COST_CODES, app.GridCells.IS_CHECKED_SMALL_CASE, commonLocators.CommonKeys.CHECK)
        cy.wait(1000) //required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.YES)
        _common.waitForLoaderToDisappear()
        cy.SAVE()
    })

    it('TC - Verify when estimators enables a child where the parent is disabled, then parent + child is enabled', function () {
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.ENABLE_DISABLE_COST_CODE)
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        _common.select_allContainerData(cnt.uuid.COST_CODES)
        _common.clickOn_expandCollapseButton(cnt.uuid.COST_CODES, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.CHECK)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.CHECK)
        _common.select_allContainerData(cnt.uuid.COST_CODES)
        _common.clickOn_expandCollapseButton(cnt.uuid.COST_CODES, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.ENABLE_DISABLE_COST_CODE)
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        cy.wait(2000)//required wait to open modal
        _common.select_allContainerData(cnt.uuid.COST_CODES)
        _common.clickOn_expandCollapseButton(cnt.uuid.COST_CODES, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.UNCHECK)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.UNCHECK)
    })

    it('TC - Verify any disabled cost code should be visible in Look ups of new project ', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.setDefaultView(app.TabBar.PROJECT)
            _common.select_tabFromFooter(cnt.uuid.PROJECTS, app.FooterTab.PROJECTS, 0);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.PROJECTS)
        _common.create_newRecord(cnt.uuid.PROJECTS);
        _projectPage.enterRecord_toCreateProject(PROJECT_PARAMETERS);
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem();
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PRJ_NAME).pinnedItem();
    })

    it("TC - Create an estimate and line,resource item in it", function () {
        _common.openTab(app.TabBar.ESTIMATE).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATE)
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
            _common.waitForLoaderToDisappear()
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, PRJ_NAME).pinnedItem();
        _common.waitForLoaderToDisappear()
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
        _common.create_newRecord(cnt.uuid.ESTIMATE);
        _estimatePage.enterRecord_toCreateEstimate(cnt.uuid.ESTIMATE, ESTIMATE_PARAMETERS);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 2);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS);
        _common.create_newRecord(cnt.uuid.ESTIMATE_LINEITEMS)
        _estimatePage.enterRecord_toCreateLineItem(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_PARAMETERS1)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 3);
        });
        _common.clear_subContainerFilter(cnt.uuid.RESOURCES)
        _common.create_newRecord(cnt.uuid.RESOURCES)
        cy.wait(2000) //required wait to load page
        _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS)
        cy.SAVE()
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 2);
        });
        _common.select_rowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 3);
        });
        _common.clear_subContainerFilter(cnt.uuid.RESOURCES)
        _common.select_rowInContainer(cnt.uuid.RESOURCES)
        _common.assert_cellData_insideActiveRow(cnt.uuid.RESOURCES, app.GridCells.COST_UNIT, CONTAINERS_COST_CODES.MARKET_RATE)

    })

    it('TC - Verify if user enables a parent where child is disabled, then parent + child must be enabled.', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.COST_CODES)
        _common.waitForLoaderToDisappear()
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.ENABLE_DISABLE_COST_CODE)
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        _common.select_allContainerData(cnt.uuid.COST_CODES)
        _common.clickOn_expandCollapseButton(cnt.uuid.COST_CODES, btn.IconButtons.ICO_TREE_COLLAPSE, btn.ButtonText.EXPAND_ALL)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.CHECK)
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _validate.verify_activeRowsCellCheckboxValue(cnt.uuid.COST_CODES, app.GridCells.IS_ACTIVE, commonLocators.CommonKeys.CHECK)
    })

    it('TC - Verify look ups in existing projects should not be affected & verify update estimate.', function () {
        _common.select_rowHasValue(cnt.uuid.COST_CODES, COSTCODE_DESC2)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.ENABLE_DISABLE_COST_CODE)
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        cy.wait(2000)//required wait to open modal
        _common.clickOn_modalFooterButton(btn.ButtonText.OK);
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('projectName')).pinnedItem();
        _common.openTab(app.TabBar.ESTIMATE).then(() => {
            _common.setDefaultView(app.TabBar.ESTIMATE)
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE, app.FooterTab.ESTIMATE, 2);
        });
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem().search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env("projectName")).pinnedItem();
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE);
        _common.create_newRecord(cnt.uuid.ESTIMATE);
        _estimatePage.enterRecord_toCreateEstimate(cnt.uuid.ESTIMATE, ESTIMATE_PARAMETERS);
        _common.waitForLoaderToDisappear()
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.clickOn_toolbarButton(cnt.uuid.ESTIMATE, btn.IconButtons.ICO_GO_TO)
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 2);
        });
        _common.clear_subContainerFilter(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.create_newRecord(cnt.uuid.ESTIMATE_LINEITEMS)
        _estimatePage.enterRecord_toCreateLineItem(cnt.uuid.ESTIMATE_LINEITEMS, LINE_ITEM_PARAMETERS)
        cy.SAVE();
        _common.waitForLoaderToDisappear()
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 3);
        });
        _common.clear_subContainerFilter(cnt.uuid.RESOURCES)
        _common.create_newRecord(cnt.uuid.RESOURCES)
        cy.wait(2000) //required wait to load page
        _estimatePage.enterRecord_toCreateResource(cnt.uuid.RESOURCES, RESOURCE_PARAMETERS)
        cy.SAVE()
        _common.waitForLoaderToDisappear()
        _common.assert_cellData_insideActiveRow(cnt.uuid.RESOURCES, app.GridCells.COST_UNIT, CONTAINERS_COST_CODES.MARKET_RATE)
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_ESTIMATE)
        _estimatePage.update_estimate_fromWizard((UPDATE_ESTIMATE_PARAMETER))
                      cy.wait(2000)
        _common.clickOn_modalFooterButton_ifExists(btn.ButtonText.OK)
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.ESTIMATE_LINEITEMS, app.FooterTab.LINE_ITEMS, 2);
        });
        _common.select_rowInContainer(cnt.uuid.ESTIMATE_LINEITEMS)
        _common.openTab(app.TabBar.ESTIMATELINEITEM).then(() => {
            _common.select_tabFromFooter(cnt.uuid.RESOURCES, app.FooterTab.RESOURCES, 3);
        });
        _common.clear_subContainerFilter(cnt.uuid.RESOURCES)
        _common.select_rowInContainer(cnt.uuid.RESOURCES)
        _common.assert_cellData_insideActiveRow(cnt.uuid.RESOURCES, app.GridCells.COST_UNIT, CONTAINERS_COST_CODES.MARKET_RATE)

    })

    it('TC - Verify updating cost codes from master to project (using the wizard in project module) must keep same cost/unit as the disabled cost code.', function () {
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_QUICKSTART)
        _common.search_fromSidebar(commonLocators.CommonKeys.QUICKSTART, sidebar.SideBarOptions.PROJECT)
        _common.waitForLoaderToDisappear()
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_SEARCH).delete_pinnedItem()
        _common.search_fromSidebar(commonLocators.CommonKeys.STANDARD, Cypress.env('projectName')).pinnedItem();
        _common.toggleSidebar(sidebar.sideBarList.SIDEBAR_NEW_WIZARD)
        _common.search_fromSidebar(commonLocators.CommonKeys.WIZARD, sidebar.SideBarOptions.UPDATE_COST_CODES_PRICES)
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.waitForLoaderToDisappear()
        _common.clickOn_modalFooterButton(btn.ButtonText.OK)
        _common.openTab(app.TabBar.PROJECT).then(() => {
            _common.select_tabFromFooter(cnt.uuid.PROJECT_COSTCODES, app.FooterTab.COST_CODES, 1);
        })
        _common.clear_subContainerFilter(cnt.uuid.PROJECT_COSTCODES)
        _common.search_inSubContainer(cnt.uuid.PROJECT_COSTCODES, COSTCODE_DESC2)
        _common.select_rowHasValue(cnt.uuid.PROJECT_COSTCODES, COSTCODE_DESC2)
        _common.assert_cellData_insideActiveRow(cnt.uuid.PROJECT_COSTCODES, app.GridCells.RATE, CONTAINERS_COST_CODES.MARKET_RATE)
    })

    after(() => {
        cy.LOGOUT();
    });

});