using System;
using System.Collections.Generic;
using System.Data.Entity.Validation;
using System.Linq;
using System.Linq.Dynamic;
using System.Linq.Expressions;
using System.Transactions;
using System.ComponentModel.Composition;
using System.Data.Entity;
using System.Data.Entity.Infrastructure;
using System.Text.RegularExpressions;

using RIB.Visual.Platform.OperationalManagement;
using RIB.Visual.Platform.Core;
using Permissions = RIB.Visual.Platform.Core.Permissions;
using RVPBC = RIB.Visual.Platform.BusinessComponents;
using Permission = RIB.Visual.Platform.BusinessComponents.Permission;
using RIB.Visual.Platform.AppServer.Runtime;
using OPN = RIB.Visual.Platform.OperationalManagement;
using IdentificationData = RIB.Visual.Platform.Core.IdentificationData;

using RIB.Visual.Basics.Core.Core;
using RIB.Visual.Basics.Core.Common;
using RIB.Visual.Basics.Core.BusinessComponents;
using CoreFinal = RIB.Visual.Basics.Core.Core.Final;

using RIB.Visual.Basics.Common.Common;
using RIB.Visual.Basics.Common.BusinessComponents;
using RIB.Visual.Basics.Common.BusinessComponents.ExtensionClasses;
using RIB.Visual.Basics.Common.BusinessComponents.Final;
using RIB.Visual.Basics.Customize.BusinessComponents;
using RVBC = RIB.Visual.Basics.Company.BusinessComponents;

using RIB.Visual.Cloud.Common.Core;
using RIB.Visual.Cloud.Common.BusinessComponents;
using RIB.Visual.Scheduling.Main.BusinessComponents.Logic;
using RVSLB = RIB.Visual.Scheduling.Lookup.BusinessComponents;
using RIB.Visual.Scheduling.Schedule.BusinessComponents;
using Constants = RIB.Visual.Scheduling.Main.Core.Constants;
using NLS = RIB.Visual.Scheduling.Main.Localization.Properties.Resources;
using RIB.Visual.Scheduling.Calendar.BusinessComponents;
using RVPARB = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication;
using RIB.Visual.Platform.BusinessComponents;
using System.Data.SqlClient;
using RIB.Visual.Basics.Common.Core.Final;
using System.Diagnostics;
using System.IO;
using System.Collections;
using static RIB.Visual.Basics.Core.Common.EntityIdentifier.Scheduling;
using RIB.Visual.Basics.Characteristic.BusinessComponents;
using System.Collections.Concurrent;
using System.Globalization;
using RIB.Visual.Basics.Core.Core.Basics;

namespace RIB.Visual.Scheduling.Main.BusinessComponents
{

	/// <summary>
	///
	/// </summary>
	[Export(typeof(IEntityInfoProvider))]
	public class ActivityEntityInfo : EntityInfoBase
	{
		/// <summary/>
		public ActivityEntityInfo() : base("scheduling.main") { }

		/// <summary>
		///
		/// </summary>
		/// <param name="entityInfos"></param>
		/// <returns></returns>
		public override IEnumerable<IEntityInfoData> GetEntityInfoData(IEnumerable<IEntityInfoData> entityInfos)
		{

			var result = GetEntityInfo<ActivityEntity>(
					ModelBuilder.DbModel,
					entityInfos,
					(query, ids) => query.Where(entity => ids.Contains(entity.Id)),
					entity => entity.Id,
					ActivityLogic.GetFormattedActivity,
					null // ActivityLogic.GetActivityDetails
					);
			return result;
		}

	}


	/// <summary>
	/// The business logic to deal with activities
	/// </summary>
	[Export("Scheduling.Main.Activity", typeof(IDataBaseLogic))]
	[Export(typeof(IActivityLogic))]
	[Export("scheduling", typeof(IChangeStatus))]
	[Export("scheduling.activity", typeof(IOwnerMappingReadLogic))]
	[EntityStatus("PSD_ACTIVITYSTATE", "scheduling.main", "Activity Status")]
	public class ActivityLogic : EntityUpdateLogic<ActivityEntity, IdentificationData>, IActivityLogic, IEntityProvider, IEntityCopier, IEntityAggregator, ICreateEntityFacade, IDataBaseLogic, IChangeStatus, IOwnerMappingReadLogic
	{
		private const string schedulingMainSettingsAppId = "19AB090F4A124F0087554B62E52A76DA";
		private static string _permissionGUID = "13120439D96C47369C5C24A2DF29238D";
		private static Boolean _hasTempCreatePermission = false;
		private static String _tempCreatePermissionGUID = "";

		private int? ScheduleCodeFormatFk = null;

		/// <summary>
		/// The singleton identifier instance for the <see cref="ActivityObservationEntity"/> type
		/// </summary>
		private static readonly Lazy<CoreFinal.IIdentifier<ActivityEntity>> IdentifierInstance = IdentifierFactory.Create<ActivityEntity>("Id");

		/// <summary>
		/// ActivityLogic constructor, doing some initialisation
		/// </summary>
		public ActivityLogic()
		{
			PermissionGUID = "13120439D96C47369C5C24A2DF29238D";
			Identifier = IdentifierInstance.Value;
			SetTempMatchingFunc<DdTempIdsEntity>((e, tmp) => (e.Id == tmp.Id));
		}

		/// <summary>
		/// Gets DbModel.
		/// </summary>
		/// <returns/>
		public override DbCompiledModel GetDbModel()
		{
			return ModelBuilder.DbModel;
		}

		/// <summary>
		/// Method to get one sequence id
		/// </summary>
		/// <returns/>
		protected override string GetEntityTableName()
		{
			return "PSD_ACTIVITY";
		}

		/// <summary>
		/// Provide temporary access rights for create
		/// </summary>
		/// <param name="tempGUID">String filled with temporary access right guid </param>
		public bool ProvideTemporarilyCreatePermission(out String tempGUID)
		{
			Permission.Ensure(_permissionGUID, Permissions.Create);

			if (String.IsNullOrEmpty(_tempCreatePermissionGUID))
			{
				_tempCreatePermissionGUID = Guid.NewGuid().ToString("N");
				_hasTempCreatePermission = true;
			}
			tempGUID = _tempCreatePermissionGUID;

			return _hasTempCreatePermission;
		}


		/// <summary>
		/// Get Activities byIds
		/// </summary>
		/// <param name="actIds">Ids of desired activities</param>
		/// <returns>Activities with given Ids </returns>
		public IEnumerable<IActivityEntity> GetActivitiesByIds(IEnumerable<int> actIds)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);
			var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel);
			var entities = dbcontext.Entities<ActivityEntity>().Where(activity => actIds.Contains(activity.Id)).ToList();
			return entities;
		}


		/// <summary>
		/// Function to be called when a temp access right is not longer used
		/// </summary>
		public void resolveTemporarilyCreatePermission()
		{
			_hasTempCreatePermission = false;
			_tempCreatePermissionGUID = "";
		}

		/// <summary>
		/// Formatter for LastObject Summary data
		/// </summary>
		/// <param name="entity"></param>
		/// <returns></returns>
		public static string GetFormattedActivity(ActivityEntity entity)
		{
			var res = entity.Code;
			res += string.IsNullOrWhiteSpace(res) || string.IsNullOrWhiteSpace(entity.Description) ? "" : ",";
			res += string.IsNullOrWhiteSpace(entity.Description) ? "" : entity.Description;
			return res;
		}

		/// <summary>
		/// Returns project entities by a filter.
		/// </summary>
		/// <param name="filterIn"></param>
		/// <param name="filterOut"></param>
		/// <returns>project entities</returns>
		public IEnumerable<ActivityEntity> GetList(RVPBC.FilterRequest filterIn, ref RVPBC.FilterResponse filterOut)
		{
			//TODO kh - disabled, implemented by permission-join on database
			//Permission.Ensure(_permissionGUID, Permissions.Read);

			var execInfo = new RVPBC.FilterExecutionInfo(filterIn, filterOut);
			execInfo.CreateHint("GetList() ");
			string requestId = null;

			int? scheduleId = null;
			int? projectId = null;

			if (filterIn.HasPinningContext)
			{
				var scheduleIdentity = filterIn.GetPinningItem("scheduling.main");
				scheduleId = scheduleIdentity?.Id;
				var projectIdentity = filterIn.GetPinningItem("project.main");
				projectId = projectIdentity?.Id;
			}

			try
			{
				using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{
					IList<ActivityEntity> entities = null;
					IQueryable<ActivityEntity> query = null;

					query = dbContext.Entities<ActivityEntity>();

					var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
					//query = query.Where(e => e.CompanyFk == context.ClientId);


					// set schedule and project context to query
					if (filterIn.PKeys != null && filterIn.FurtherFilters != null && !filterIn.FurtherFilters.Any(e => e.Token.Equals("PSD_SCHEDULE")))
					{
						var id = filterIn.PKeys.FirstOrDefault().Id;
						if (id > 0)
						{
							scheduleId = GetByFilter(e => e.Id == id).FirstOrDefault().ScheduleFk;
						}
					}
					if (scheduleId.HasValue)
					{
						query = query.Where(e => e.ScheduleFk == scheduleId.Value && !e.BaselineFk.HasValue);
					}

					else if (projectId.HasValue)
					{
						query = query.Where(e => e.ProjectFk == projectId.Value && !e.BaselineFk.HasValue);
					}
					else
					{
						query = query.Where(e => e.CompanyFk == context.ClientId && !e.BaselineFk.HasValue);
					}

					if (!string.IsNullOrWhiteSpace(filterIn.Pattern))
					{
						// expand filterIn.Pattern to Like expression
						query = RVPBC.FilterRequest.ExpandToSearchPattern(filterIn, query,
							filterVal => "SearchPattern.Contains(\"" + filterVal + "\")");
					}

					//DEV-22511. Error while starting workflow in scheduling module
					if (filterIn.PKeys != null && filterIn.FurtherFilters == null)
					{
						var id = filterIn.PKeys.FirstOrDefault().Id;
						query = query.Where(e => e.Id == id && !e.BaselineFk.HasValue);
					}
					else if (filterIn.FurtherFilters == null || !filterIn.FurtherFilters.Any(e => e.Token.Contains("PSD_SCHEDULE")))
					{
						if (filterIn.PKeys != null)
						{
							var ids = filterIn.PKeys.Select(p => p.Id);
							query = query.Where(e => ids.Contains(e.Id));
						}
					}
					else
					{
						if (!scheduleId.HasValue && !projectId.HasValue)
						{
							RVPBC.FilterRequest.TokenValueFilter furtherFilter =
								filterIn.FurtherFilters.FirstOrDefault(e => e.Token.Equals("PSD_SCHEDULE"));
							if (furtherFilter != null) // we're expecting a primary key for psd_schedule
							{
								scheduleId = Convert.ToInt32(furtherFilter.Value);
								if (scheduleId.HasValue)
								{
									query = query.Where(e => e.ScheduleFk == scheduleId.Value && !e.BaselineFk.HasValue);
								}
							}
							else
							{
								var scheduleCode = String.Empty;
								var projectNo = String.Empty;
								furtherFilter = filterIn.FurtherFilters.FirstOrDefault(e => e.Token.Equals("PSD_SCHEDULE.CODE"));
								if (furtherFilter != null)
								{
									scheduleCode = furtherFilter.Value;
								}
								furtherFilter = filterIn.FurtherFilters.FirstOrDefault(e => e.Token.Equals("PSD_SCHEDULE.PROJECTNO"));
								if (furtherFilter != null)
								{
									projectNo = furtherFilter.Value;
								}
								if (!string.IsNullOrEmpty(scheduleCode) && !string.IsNullOrEmpty(projectNo))
								{
									scheduleId = new ScheduleLogic().GetScheduleIDByCodeFromProject(scheduleCode, projectNo);
									if (scheduleId.HasValue)
									{
										query = query.Where(e => e.ScheduleFk == scheduleId.Value && !e.BaselineFk.HasValue);
									}
								}
							}
						}
						var objectSelection = filterIn.FurtherFilters.FirstOrDefault(ff => ff.Token == "MDL_OBJECT_SELECTION");
						if (objectSelection != null)
						{
							var selectedObjectIds = ModelElementIdCompressor.ParseCompressedString(objectSelection.Value).ToArray();
							if (selectedObjectIds.Length > 0)
							{
								var perModelExpressions = selectedObjectIds.Select(modelObjectIds =>
								{
									var modelId = modelObjectIds.ModelId;
									var objectIds = modelObjectIds.Ids.ToArray();
									return (Expression<Func<Activity2ModelObjectVEntity, Boolean>>)(e => (e.ModelFk == modelId) && objectIds.Contains(e.ObjectFk));
								});
								var modelExpr = perModelExpressions.JoinWithOr();

								query = query.ReduceByEntity(() => dbContext.Entities<Activity2ModelObjectVEntity>(),
									ExpressionEnumerableExtension.JoinWithAnd(
										(e, a2O) => e.Id == a2O.ActivityFk,
										modelExpr.ExpandParameters<Activity2ModelObjectVEntity, ActivityEntity, Activity2ModelObjectVEntity>(1)));
							}
						}
					}
					if (!filterIn.IncludeNonActiveItems.HasValue || !filterIn.IncludeNonActiveItems.Value)
					{
						// todo schedule also checked if islive = true
						if (!scheduleId.HasValue)
						{

							var activeScheduleIds = new List<int>();
							if (projectId.HasValue)
							{
								activeScheduleIds = new ScheduleLogic().GetCoresAsListByFilter(e => e.IsLive && e.IsActive && e.ProjectFk == projectId.Value).Select(e => e.Id).ToList();
							}
							else
							{
								activeScheduleIds = new ScheduleLogic().GetCoresAsListByFilter(e => e.IsLive && e.IsActive).Select(e => e.Id).ToList();
							}

							if (activeScheduleIds != null && activeScheduleIds.Any())
							{
								var exprs = new List<Expression<Func<ActivityEntity, Boolean>>>();
								Expression<Func<ActivityEntity, Boolean>> expression = null;
								var chunks = activeScheduleIds.ToSizedChunks(2000);
								foreach (var chunk in chunks)
								{
									exprs.Add(e => chunk.Contains(e.ScheduleFk));
								}
								if (exprs.Count > 0)
								{
									expression = exprs.JoinWithOr();
								}
								query = query.Where(expression);
							}
						}
						query = query.Where(e => e.IsLive);
					}

					query = EvaluateAdditionalColumnFilter(query, filterIn.FurtherFilters, dbContext);

					// process enhanced filter
					if (filterIn.IsEnhancedFilter.HasValue && filterIn.IsEnhancedFilter.Value)
					{
						execInfo.CreateHint("GetList() -> Execute enhanced filter");

						//var resultKeys = FilterType2Logic.GetEnhancedFilterResults(filterIn, null/*modelObjProvider*/, null, out requestId
						//query = query.Where(e => e.DdTempIdsEntities.Any(d => d.RequestId == requestId));
						query = EvaluateEnhancedFilterResults(filterIn, dbContext, query);
					}

					var userId = BusinessApplication.BusinessEnvironment.CurrentContext.UserId;

					if (query.Count() > 0 || !scheduleId.HasValue || !projectId.HasValue)
					{
						var aceQuery = ScheduleUserAccessCacheMapping.ProvideUserAccessCacheAsQueryable(dbContext);

						// join / filter by accessible instances
						query = query.Where(e => aceQuery.Any(p => e.ScheduleFk >= p.FromId && e.ScheduleFk <= p.ToId));
					}

					execInfo.CreateHint("GetList() -> RetrieveEntities");

					// handle paging within this method completely
					entities = filterIn.OrderBy == null
						? RVPBC.FilterRequest.RetrieveEntities(filterIn, filterOut, query, e => e.Code, e => new IdentificationData(e.Id))
						: RVPBC.FilterRequest.RetrieveEntities(filterIn, filterOut, query);

					execInfo.CreateHint("GetList() -> done");
					filterOut.RecordsFound = query.Count();
					filterOut.RecordsRetrieved = entities.Count();
					var result = BuildUpTree(entities);
					entities.Clear();
					ScheduleDataPool dataPool = new ScheduleDataPool();
					var scheduleIds = result.Select(e => e.ScheduleFk).Distinct().ToArray();
					dataPool.LoadRelationsForSchedules(scheduleIds, dbContext);
					dataPool.Activities = DoIncludeTransientProperties(result, dataPool);

					DateTime dueDate = DateTime.Now;
					var includeTransientRootEntity = false;
					if (filterIn.FurtherFilters != null)
					{
						var token = filterIn.FurtherFilters.FirstOrDefault(e => e.Token.Equals("INCLUDE_DUEDATE"));
						if (token == null || !DateTime.TryParse(token.Value, out dueDate))
						{
							dueDate = DateTime.Now;
						}
						var filter = filterIn.FurtherFilters.FirstOrDefault(ff => ff.Token.Equals("INCLUDE_TRANSIENTROOTENTITY"));
						includeTransientRootEntity = filter != null ? bool.Parse(filter.Value) : false;
					}

					dataPool.LoadForCompletionCalcOfLoadedActivities(dbContext);

					IncludeCompletionInformation(result, dueDate, dataPool);

					if (includeTransientRootEntity)
					{
						if (scheduleId.HasValue && scheduleId.Value > 0)
						{
							result = new ActivityEntity[] { GetTransientRootActivity(result, true, scheduleId.Value) }; //the client must deliver a scheduleId if INCLUDE_TRANSIENTROOTENTITY is true
						}
					}

					return result;
				}
			}
			catch (Exception ex)
			{
				throw new SchedulingBusinessLayerException(ex.Message, ex, (int)OPN.ExceptionErrorCodes.BusinessFatalError);
			}
			finally
			{
				// we clean the temporary produced Id list from table BAS_DDTEMPIDS  basics.customize.activitystate
				if (!String.IsNullOrEmpty(requestId))
				{
					FilterType2Logic.CleanRequestIds(requestId);
				}
			}
		}

		private void AddActivityInfoProperties(IEnumerable<ActivityEntity> result)
		{
			var activities = result.ToArray();
			var actIds = activities.CollectIds(e => e.Id).ToArray();
			// In case more properties are needed as transient fields, please enhance the view for performance reasons
			var activityInfos = GetActivityInfo(actIds);
			var calendarIds = activities.Select(e => e.CalendarFk).Distinct().ToArray();
			var calLogic = new SchedulingCalendarLogic();
			var calendars = calLogic.GetByIds(calendarIds.Select(e => new IdentificationData() { Id = e }));
			//Flat properties
			foreach (var act in activities)
			{
				var activityInfo = activityInfos.FirstOrDefault(pi => pi.Id == act.Id);
				act.IsUpdatedToEstimate = true;
				if (activityInfo != null)
				{
					act.Schedule = new ScheduleEntity();
					act.IsReadOnly = activityInfo.IsReadOnly;
					act.Baseline = activityInfo.Baseline;
					act.IsAssignedToEstimate = activityInfo.IsAssignedToEstimate;
					act.EstimateCodes = activityInfo.EstimateCodes;
					act.IsAssignedToRequisition = activityInfo.IsAssignedToRequisition;
					act.RequisitionCodes = activityInfo.RequisitionCodes;
					act.CalendarCode = activityInfo.CalendarCode;
					act.CalendarDescription = activityInfo.CalendarDescription;
					act.HasHammock = activityInfo.HasHammock;
					act.IsAssignedToHammock = activityInfo.IsAssignedToHammock;
					act.HasReports = activityInfo.HasReports;
					act.HasCalculatedEnd = activityInfo.HasCalculatedEnd;
					act.HasCalculatedStart = activityInfo.HasCalculatedStart;
					act.PackageId = activityInfo.PackageId;
					act.PackageCode = activityInfo.PackageCode;
					act.PackageDesc = activityInfo.PackageDesc;
					if (activityInfo.ProcurementStructureFk.HasValue)
					{
						act.PrcStructureFk = activityInfo.ProcurementStructureFk;
					}
					act.ActivityMasterFk = activityInfo.ActivityMasterFk;
					act.ScheduleMasterFk = activityInfo.ScheduleMasterFk;
					act.Schedule.ScheduleMasterFk = activityInfo.ScheduleMasterFk;
					act.IsInterCompany = activityInfo.IsInterCompany;
					act.Schedule.IsReadOnly = activityInfo.ScheduleIsReadOnly;
					act.Schedule.Id = act.ScheduleFk;
					act.Schedule.Code = activityInfo.ScheduleCode;
					act.Schedule.DescriptionInfo.Translated = activityInfo.ScheduleDescription;
					act.Schedule.DescriptionInfo.Description = activityInfo.ScheduleDescription;
					act.Schedule.TargetStart = activityInfo.TargetStart;
					act.Schedule.TargetEnd = activityInfo.TargetEnd;
					act.Schedule.RubricCategoryFk = activityInfo.ScheduleRubricCategoryFk;
					act.ScheduleIsReadOnly = activityInfo.ScheduleIsReadOnly;
					act.ScheduleCode = activityInfo.ScheduleCode;
					act.ScheduleDescription = activityInfo.ScheduleDescription;
					act.Schedule.ProjectFk = act.ProjectFk;
					act.TargetStart = activityInfo.TargetStart;
					act.TargetEnd = activityInfo.TargetEnd;
					act.ProjectNo = activityInfo.ProjectNo;
					act.ProjectName = activityInfo.ProjectName;
					act.ProjectName2 = activityInfo.ProjectName2;
					act.ProjectLongNo = activityInfo.ProjectLongNo;
					act.EstimateHoursTotal = activityInfo.EstimateHoursTotal;
					act.TotalCost = activityInfo.TotalCost;
					act.ScheduleTypeIsExecution = activityInfo.ScheduleTypeIsExecution;
					act.ScheduleTypeIsDesign = activityInfo.ScheduleTypeIsDesign;
					act.ScheduleTypeIsProcurement = activityInfo.SchedTypeIProcurement;

					if (act.IsDurationEstimationDriven && activityInfo.EstimateHoursTotal.HasValue)
					{
						var calendar = calendars.FirstOrDefault(e => e.Id == act.CalendarFk);
						var workHoursPerDay = calendar != null && calendar.WorkHoursPerDay.HasValue
							? calendar.WorkHoursPerDay.Value
							: 8m;
						var lineItemDuration = Math.Ceiling(activityInfo.EstimateHoursTotal.Value / workHoursPerDay);
						if (lineItemDuration != act.PlannedDuration)
						{
							act.IsUpdatedToEstimate = false;
						}
					}// Adjusting "Duration Estimation Driven" logic as per description
					if (!activityInfo.ScheduleTypeIsDesign && (activityInfo.ScheduleTypeIsExecution || activityInfo.SchedTypeIProcurement))
					{
						act.IsDurationEstimationDriven = false;
						act.DurationEstimationIsReadOnly = true;
					}
					else
					{
						act.DurationEstimationIsReadOnly = false; // Allow editing
					}
				}
			}
		}

		internal IEnumerable<ActivityInfoVEntity> GetActivityInfo(int[] actIds)
		{
			IContext currentContext = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;
			var languageId = currentContext.DataLanguageId;

			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				IQueryable<ActivityInfoVEntity> dbSet = dbContext.Set<ActivityInfoVEntity>();
				dbSet = dbSet.Where(e => e.LanguageId == languageId);
				dbSet = dbSet.Where(e => actIds.Contains(e.Id));

				return dbSet.ToArray();
			}
		}

		/// <summary>
		/// GetList
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetList(int projectfk)
		{
			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				IEnumerable<ActivityEntity> entities = null;
				entities = dbContext.Entities<ActivityEntity>().Where(e => e.ProjectFk == projectfk).OrderBy(e => e.Id).ToList();
				return entities;
			}
		}

		/// <summary>
		/// refreshing *Finish, *Start, *Duration Fields of an ActivityEntity of Type Hammock
		/// </summary>
		/// <param name="activity"></param>
		/// <param name="childActivities"></param>
		/// <param name="nonWorkingDays"></param>
		/// <returns></returns>
		public static void RefreshDateFields(ActivityEntity activity, IEnumerable<ActivityEntity> childActivities, NonWorkingDayCheck nonWorkingDays = null)
		{
			IEnumerable<ActivityEntity> locallyCachedAssignedActivities = childActivities as System.Data.Entity.IDbSet<ActivityEntity> != null ? (childActivities as System.Data.Entity.IDbSet<ActivityEntity>).ToList() : childActivities.ToList();

			if (!locallyCachedAssignedActivities.Any())
			{
				return;
			}

			var plannedStart = locallyCachedAssignedActivities.Select(a => a.PlannedStart).Min();
			var plannedFinish = locallyCachedAssignedActivities.Select(a => a.PlannedFinish).Max();

			// Actual start and actual finish
			DateTime? tempActualStart = null;
			if (locallyCachedAssignedActivities.All(a => a.ActualStart.HasValue))
			{
				tempActualStart = locallyCachedAssignedActivities.Select(a => a.ActualStart).Min();
			}
			DateTime? tempActualFinish = null;
			if (locallyCachedAssignedActivities.All(a => a.ActualFinish.HasValue))
			{
				tempActualFinish = locallyCachedAssignedActivities.Select(a => a.ActualFinish).Max();
			}

			// Current start and current finish
			DateTime tempCurrentStart = locallyCachedAssignedActivities.Select(c => c.CurrentStart).Min();
			DateTime? tempCurrentFinish = null;
			if (locallyCachedAssignedActivities.Any(a => a.CurrentFinish.HasValue))
			{
				tempCurrentFinish = locallyCachedAssignedActivities.Select(a => a.CurrentFinish.GetValueOrDefault()).Max();
			}

			if (nonWorkingDays == null)
			{
				var filter = new UtilitiesDataFilter { Calendar = activity.CalendarFk, Start = plannedStart, End = plannedFinish };
				nonWorkingDays = new CalendarUtilitiesLogic().GetNonWorkingDayChecker(filter) as NonWorkingDayCheck;
			}

			if (DateTime.Compare(plannedStart, activity.PlannedStart) == 0 &&
				 DateTimeExtension.Compare(plannedFinish, activity.PlannedFinish) == 0 &&
				 DateTimeExtension.Compare(tempActualStart, activity.ActualStart) == 0 &&
				 DateTimeExtension.Compare(tempActualFinish, activity.ActualFinish) == 0 &&
				 DateTimeExtension.Compare(tempCurrentStart, activity.CurrentStart) == 0 &&
				 DateTimeExtension.Compare(tempActualFinish, activity.CurrentFinish) == 0)
			{
				return;
			}

			var tempCurrentDuration = 1.0m;
			if (tempCurrentFinish.HasValue)
			{
				tempCurrentDuration = new CalendarUtilitiesLogic().GetDuration(tempCurrentStart, tempCurrentFinish.Value, nonWorkingDays);
			}

			var tempPlannedDuration = 1.0m;
			if (plannedFinish.HasValue)
			{
				tempPlannedDuration = new CalendarUtilitiesLogic().GetDuration(plannedStart, plannedFinish.Value, nonWorkingDays);
			}

			decimal? tempActualDuration = null;
			if (tempActualStart.HasValue && tempActualFinish.HasValue)
			{
				tempActualDuration = new CalendarUtilitiesLogic().GetDuration(tempActualStart.Value, tempActualFinish.Value, nonWorkingDays);
			}

			activity.PlannedStart = plannedStart;
			activity.PlannedFinish = plannedFinish;
			activity.PlannedDuration = tempPlannedDuration;
			activity.ActualStart = tempActualStart;
			activity.ActualFinish = tempActualFinish;
			activity.ActualDuration = tempActualDuration;
			activity.CurrentStart = tempCurrentStart;
			activity.CurrentFinish = tempCurrentFinish;
			activity.CurrentDuration = tempCurrentDuration;
			activity.ExecutionStarted = tempActualStart.HasValue;
			activity.ExecutionFinished = tempActualFinish.HasValue;
		}

		/// <summary>
		/// Writes to all date fields of the transient root entity.
		/// This function should only be used if "activity" is the transient root activity.
		/// </summary>
		/// <param name="activity"></param>
		/// <param name="childActivities"></param>
		/// <param name="scheduleFk"></param>
		public static void WriteTransientRootDateFields(ActivityEntity activity, IEnumerable<ActivityEntity> childActivities, int scheduleFk)
		{
			if (childActivities.Count() > 0)
			{
				RefreshDateFields(activity, childActivities); //writes to all date fields which would be persitant if activity would be a persitant activity
			}
			else
			{
				//var calendar = new SchedulingCalendarLogic().GetCalendarById(calendarFk);
				var startFinish = new ScheduleLogic().GetStartFinish(scheduleFk);
				Tuple<DateTime, DateTime> customStartFinish;
				if (startFinish.Item2.HasValue)
				{
					customStartFinish = new Tuple<DateTime, DateTime>(startFinish.Item1, startFinish.Item2.Value);
				}
				else
				{
					customStartFinish = new Tuple<DateTime, DateTime>(startFinish.Item1, startFinish.Item1.AddMonths(1));
				}
				var filter = new UtilitiesDataFilter();
				filter.Calendar = activity.CalendarFk;
				filter.Start = customStartFinish.Item1;
				filter.End = customStartFinish.Item2;
				var nonWorkingDays = new CalendarUtilitiesLogic().GetNonWorkingDayChecker(filter) as NonWorkingDayCheck;
				var duration = new CalendarUtilitiesLogic().GetDuration(customStartFinish.Item1, customStartFinish.Item2, nonWorkingDays);

				activity.PlannedStart = customStartFinish.Item1;
				activity.PlannedFinish = customStartFinish.Item2;
				activity.PlannedDuration = duration;
				activity.CurrentStart = customStartFinish.Item1;
				activity.CurrentFinish = customStartFinish.Item2;
				activity.CurrentDuration = duration;
			}
			WriteToTransientRootsTransientDateFields(activity);
		}

		/// <summary>
		/// Writes to the transient date fields of an activitiy.
		/// The persitant datefields are nessecarry in order to write correctly.
		/// Only use this function on the transient root activity.
		/// </summary>
		/// <param name="activity"></param>
		private static void WriteToTransientRootsTransientDateFields(ActivityEntity activity)
		{
			var logic = new CalendarUtilitiesLogic();
			if (activity.PlannedFinish.HasValue)
			{
				activity.PlannedCalendarDays = logic.GetTotalDuration(activity.PlannedStart, activity.PlannedFinish).Value;
			}
			if (activity.ActualStart.HasValue && activity.ActualFinish.HasValue)
			{
				activity.ActualCalendarDays = logic.GetTotalDuration(activity.ActualStart, activity.ActualFinish).Value;
			}
			if (activity.CurrentFinish.HasValue)
			{
				activity.CurrentCalendarDays = logic.GetTotalDuration(activity.CurrentStart, activity.CurrentFinish).Value;
			}
		}

		/// <summary>
		/// Id used for the transient root activity
		/// </summary>
		public const int TransientRootActivityId = -1;

		/// <summary>
		/// Type used for the transient root activity
		/// </summary>
		public const int TransientRootActivityTypeFk = -1;

		/// <summary>
		/// Gets the Transient Root Activty
		/// </summary>
		/// <returns></returns>
		public static ActivityEntity GetTransientRootActivity(int scheduleFk, int calendarFk = 0)
		{
			var schedule = new ScheduleLogic().GetScheduleById(scheduleFk);
			var transientRootActivity = new ActivityEntity();
			transientRootActivity.Id = TransientRootActivityId;
			transientRootActivity.Version = 1;
			transientRootActivity.Code = "ROOT";
			transientRootActivity.IsOnLongestPath = false;
			transientRootActivity.IsCritical = false;
			transientRootActivity.CalendarFk = calendarFk;
			transientRootActivity.ActivityTypeFk = TransientRootActivityTypeFk;
			var defaultStatus = new BasicsCustomizeActivityStateLogic().GetByFilter(stat => stat.IsDefault).FirstOrDefault();
			transientRootActivity.ActivityStateFk = defaultStatus != null ? defaultStatus.Id : 0;
			transientRootActivity.ScheduleFk = scheduleFk;
			transientRootActivity.Schedule = schedule;
			transientRootActivity.ProjectFk = schedule.ProjectFk;
			transientRootActivity.ProjectName = schedule.Project.ProjectName;
			transientRootActivity.ProjectName2 = schedule.Project.ProjectName2;
			transientRootActivity.ProjectNo = schedule.Project.ProjectNo;
			transientRootActivity.CompanyFk = schedule.CompanyFk;
			transientRootActivity.IsUpdatedToEstimate = true;
			return transientRootActivity;
		}

		/// <summary>
		/// Gets the Transient Root Activty filled up with its childs the persitentRoots
		/// </summary>
		/// <param name="persistentRoots"></param>
		/// <param name="hirachically"></param>
		/// <param name="scheduleFk"></param>
		/// <returns></returns>
		public static ActivityEntity GetTransientRootActivity(IList<ActivityEntity> persistentRoots, bool hirachically, int scheduleFk)
		{
			var calendarFk = new ScheduleLogic().GetCalendarId(scheduleFk);
			var transientRootActivity = GetTransientRootActivity(scheduleFk, calendarFk);
			if (persistentRoots.Any())
			{
				foreach (var actJ in persistentRoots.Where(actI => actI.ParentActivityFk == null))
				{
					actJ.ParentActivityFk = TransientRootActivityId;
					if (hirachically)
					{
						transientRootActivity.ActivityEntities_ParentActivityFk.Add(actJ);
					}
				}
			}
			WriteTransientRootDateFields(transientRootActivity, persistentRoots, scheduleFk);
			return transientRootActivity;
		}

		/// <summary>
		/// Removes the Transient Root
		/// </summary>
		/// <param name="activities"></param>
		/// <returns></returns>
		public static bool RemoveTransientRootActivity(IList<ActivityEntity> activities)
		{
			var transientRoot = activities != null ? activities.FirstOrDefault(act => act.Id == TransientRootActivityId) : null;
			if (transientRoot != null)
			{
				activities.Remove(transientRoot);
				foreach (var act in activities)
				{
					act.ParentActivityFk = null;
				}
				foreach (var act in transientRoot.ActivityEntities_ParentActivityFk)
				{
					act.ParentActivityFk = null;
					activities.Add(act);
				}
				return true;
			}
			else
			{
				return false;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="activities"></param>
		/// <returns></returns>
		public static bool RemoveTransientRootParentFks(IList<ActivityEntity> activities)
		{
			var isTransientRootEntityEnabled = false;
			if (activities != null)
			{
				foreach (var act in activities)
				{
					if (act.ParentActivityFk == TransientRootActivityId)
					{
						isTransientRootEntityEnabled = true;
						act.ParentActivityFk = null;
					}
				}
			}
			return isTransientRootEntityEnabled;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="activities"></param>
		public static void AddTransientRootParentFks(IList<ActivityEntity> activities)
		{
			if (activities != null)
			{
				foreach (var act in activities)
				{
					if (act.ParentActivityFk == null)
					{
						act.ParentActivityFk = TransientRootActivityId;
					}
				}
			}
		}

		/// <summary>
		/// gets the persistent Id from a nonpersistent Id
		/// </summary>
		/// <param name="nonPersistentId"></param>
		/// <returns></returns>
		public static int? GetPersistentId(int? nonPersistentId)
		{
			if (nonPersistentId.HasValue)
			{
				return nonPersistentId == TransientRootActivityId ? null : nonPersistentId;
			}
			else
			{
				return null;
			}
		}

		/// <summary>
		/// gets the nonpersistent Id from a persistent Id
		/// </summary>
		/// <param name="persistentId"></param>
		/// <returns></returns>
		public static int GetNonPersistentId(int? persistentId)
		{
			return persistentId ?? TransientRootActivityId;
		}


		/// <summary>
		/// Removes the Transient Root
		/// </summary>
		/// <param name="complete"></param>
		/// <returns></returns>
		public static void RemoveTransientRootActivity(ActivityComplete complete)
		{
			if (complete.EffectedActivities != null)
			{
				complete.isTransientRootEntityEnabled = RemoveTransientRootParentFks(complete.EffectedActivities);
			}
			if (complete.Activity != null)
			{
				complete.isTransientRootEntityEnabled = RemoveTransientRootParentFks(new ActivityEntity[] { complete.Activity }) || complete.isTransientRootEntityEnabled;
			}
			var removed = RemoveTransientRootActivity(complete.EffectedActivities);
			complete.HasTransientRootEntityInclude = complete.HasTransientRootEntityInclude || removed;
		}

		/// <summary>
		/// Removes the Transient Root
		/// </summary>
		/// <param name="complete"></param>
		/// <returns></returns>
		public void AddTransientRootActivity(ActivityComplete complete)
		{
			var activities = new List<ActivityEntity>();

			if (complete.isTransientRootEntityEnabled || complete.HasTransientRootEntityInclude)
			{
				if (complete.EffectedActivities != null && complete.EffectedActivities.Any())
				{
					activities.AddRange(complete.EffectedActivities);
				}

				if (complete.Activity != null && activities.All(c => c.Id != complete.Activity.Id))
				{
					activities.Add(complete.Activity);
				}
			}

			if (complete.isTransientRootEntityEnabled && activities.Any())
			{
				AddTransientRootParentFks(activities);
			}

			if (complete.HasTransientRootEntityInclude)
			{
				if (complete.EffectedActivities == null)
				{
					complete.EffectedActivities = new List<ActivityEntity>();
				}
				if (activities.Any())
				{
					foreach (var act in GetAllRootsNotInBaseline(activities[0].ScheduleFk))
					{
						if (!activities.Select(actJ => actJ.Id).Contains(act.Id))
						{
							activities.Add(act);
						}
					}
					complete.EffectedActivities.Add(GetTransientRootActivity(activities, false, activities[0].ScheduleFk));
				}
				else
				{
					var act = GetActivityById(complete.MainItemId);
					if (act != null)
					{
						complete.EffectedActivities.Add(GetTransientRootActivity(activities, false, act.ScheduleFk));
					}
				}
			}
		}

		private IEnumerable<ActivityEntity> GetAllRootsNotInBaseline(int scheduleFk)
		{
			return GetActivity(act =>
				act.ScheduleFk == scheduleFk && act.ParentActivityFk == null && act.IsLive &&
				act.BaseActivityFk == null && act.BaselineFk == null).ToList();
		}

		/// <summary>
		/// Create Activity Tree
		/// </summary>
		/// <param name="activities">list of activities to build up tree</param>
		/// <returns>tree of activities</returns>
		public IList<ActivityEntity> BuildUpTree(IList<ActivityEntity> activities)
		{
			var resMap = new Dictionary<long, ActivityEntity>();

			foreach (var item in activities)
			{
				item.ActivityEntities_ParentActivityFk.Clear();
				resMap.Add(item.Id, item);
			}

			var current = new List<ActivityEntity>();
			current.AddRange(activities);

			var newItems = new List<ActivityEntity>();
			var parents = new List<ActivityEntity>();

			while (current.Count > 0)
			{
				foreach (var item in current)
				{
					if (item.ParentActivityFk.HasValue)
					{
						long papsId = item.ParentActivityFk.Value;
						ActivityEntity parent = null;
						if (!resMap.TryGetValue(papsId, out parent))
						{
							parent = GetActivity(e => e.Id == papsId).FirstOrDefault();
							parent.ActivityEntities_ParentActivityFk.Clear();
							resMap.Add(papsId, parent);
							newItems.Add(parent);

							parent.ActivityEntities_ParentActivityFk.Add(item);
						}
						else
						{
							parent.ActivityEntities_ParentActivityFk.Add(item);
						}
					}
					else
					{
						parents.Add(item);
					}
				}

				current.Clear();
				current.AddRange(newItems);
				newItems.Clear();
			}

			return parents;
		}

		/// <summary>
		/// Adds transient properties to activity regarding schedule and project
		/// </summary>
		/// <param name="activities">Activities for which the project and schedule information is needed</param>
		public void IncludeTransientProperties(IEnumerable<ActivityEntity> activities)
		{

			DoIncludeTransientProperties(activities);
		}

		/// <summary>
		/// Adds transient properties to activity regarding schedule and project
		/// </summary>
		/// <param name="activities">Activities for which the project and schedule information is needed</param>
		/// <param name="dataPool">Loaded Data Pool</param>
		internal void IncludeTransientProperties(IEnumerable<ActivityEntity> activities, ScheduleDataPool dataPool)
		{
			DoIncludeTransientProperties(activities, dataPool);
		}
		/// <summary>
		/// Returns a list of activities
		/// </summary>
		/// <param name="filter">filter for selecting the appropriate activities</param>
		/// <returns>all activities including completion information matching the filter </returns>
		public IEnumerable<ActivityEntity> GetActivityWithCompletion(Expression<Func<ActivityEntity, bool>> filter)
		{
			var res = GetActivity(filter);
			IncludeCompletionInformation(res);

			return res;
		}

		/// <summary>
		/// Returns a list of activities
		/// </summary>
		/// <param name="filter">filter for selecting the appropriate activities</param>
		/// <returns>all activities including completion information matching the filter </returns>
		public IEnumerable<ActivityEntity> GetActivity(Expression<Func<ActivityEntity, bool>> filter = null)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				IEnumerable<ActivityEntity> entities = null;
				if (filter != null)
				{
					entities = dbcontext.Entities<ActivityEntity>().Where(filter).ToList();
				}
				else
				{
					entities = dbcontext.Entities<ActivityEntity>().ToList();
				}

				return entities.ToList();
			}
		}

		/// <summary>
		/// Returns a list of activities
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetActivityByParentId(long nId)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.Entities<ActivityEntity>().Where(activity => activity.ParentActivityFk == nId).ToList();

				DoIncludeTransientProperties(entities);
				IncludeCompletionInformation(entities);

				return entities;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <param name="scheduleId"></param>
		/// <param name="filterActivitiesIds">If activites ids are null, then it return all activities under the schedule</param>
		/// <returns></returns>
		public IEnumerable<int> GetActivitiesIds(int projectId, int scheduleId, IEnumerable<int> filterActivitiesIds = null)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var activityIds = new List<int>();

				if (filterActivitiesIds == null)
				{
					return dbcontext.Entities<ActivityEntity>().Where(activity => activity.ProjectFk == projectId && activity.ScheduleFk == scheduleId)
							.Select(e => e.Id).ToList();
				}
				else
				{
					return dbcontext.Entities<ActivityEntity>().Where(activity => activity.ProjectFk == projectId && activity.ScheduleFk == scheduleId &&
					filterActivitiesIds.Contains(activity.Id))
					.Select(e => e.Id).ToList();
				}
			}
		}


		/// <summary>
		/// Get all activities with the given project id
		/// </summary>
		/// <param name="projectId">id of the project</param>
		/// <returns>activities corresponding to the project id</returns>
		public IEnumerable<ActivityEntity> GetProjectActivities(int projectId)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.Entities<ActivityEntity>().Where(activity => activity.ProjectFk == projectId).ToList();

				DoIncludeTransientProperties(entities);
				IncludeCompletionInformation(entities);

				return entities;
			}
		}

		/// <summary>
		/// Get all activities with the given project id
		/// <param name="projectId">id of the project</param>
		/// </summary>
		public IEnumerable<IActivityEntity> GetProjectActivitiesById(int projectId)
		{
			return GetProjectActivities(projectId);
		}

		/// <summary>
		/// Get all activities with the given schedule id
		/// </summary>
		/// <param name="schedules">list of schedules</param>
		/// <returns>activities corresponding to the schedule id</returns>
		public IEnumerable<ActivityEntity> GetScheduleActivities(IEnumerable<IScheduleEntity> schedules)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			var scheduleIds = schedules.Select(s => s.Id).ToArray();

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var activityEntities = dbcontext.Entities<ActivityEntity>().Where(a => scheduleIds.Contains(a.ScheduleFk) && a.IsLive && a.BaselineFk == null && a.BaseActivityFk == null).ToArray();
				var activities = activityEntities.CollectIds(e => e.Id).ToArray();

				var ProgressReportList = dbcontext.Entities<ActivityProgressReportEntity>().Where(pr => pr.ActivityFk.HasValue && activities.Contains(pr.ActivityFk.Value)).ToList();
				foreach (var entity in activityEntities)
				{
					entity.PercentageCompletion = ProgressReportList.Where(x => x.ActivityFk == entity.Id).Select(s => s.PCo).LastOrDefault();
				}
				return activityEntities;
			}
		}

		/// <summary>
		/// Get all activities with the given schedule id
		/// </summary>
		/// <param name="scheduleId">id of the schedule</param>
		/// <returns>activities corresponding to the schedule id</returns>
		public IEnumerable<ActivityEntity> GetScheduleActivities(int scheduleId)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.Entities<ActivityEntity>().Where(activity => activity.ScheduleFk == scheduleId && activity.IsLive && activity.BaselineFk == null && activity.BaseActivityFk == null).ToList();

				return entities;
			}
		}

		/// <summary>
		/// Get all activities + basline activities with the given schedule id
		/// </summary>
		/// <param name="scheduleId">id of the schedule</param>
		/// <returns>activities corresponding to the schedule id</returns>
		public IEnumerable<ActivityEntity> GetScheduleActivitiesIncludingBaselineActivity(int scheduleId)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.Entities<ActivityEntity>().Where(activity => activity.ScheduleFk == scheduleId && activity.IsLive).ToList();

				return entities;
			}
		}

		/// <summary>
		/// returns id of schedules which contains activities
		/// </summary>
		/// <param name="scheduleIds">list of schedule ids</param>
		/// <returns>list of schedules ids which contains activities</returns>
		public IEnumerable<int> ScheduleContainsActivities(IEnumerable<int> scheduleIds)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.Entities<ActivityEntity>().Where(a => scheduleIds.Contains(a.ScheduleFk) && a.IsLive && a.BaselineFk == null && a.BaseActivityFk == null).ToArray();

				return entities.Select(e => e.ScheduleFk);
			}
		}

		/// <summary>
		/// Get all activities with the given schedule id
		/// </summary>
		public IEnumerable<IActivityEntity> GetScheduleActivitiesById(int scheduleId)
		{
			return GetScheduleActivities(scheduleId);
		}

		/// <summary>
		/// Get all activities for the given baseline id
		/// </summary>
		/// <param name="baselineIDs">id of the baseline</param>
		/// <returns>activities corresponding to the baseline id</returns>
		public IEnumerable<ActivityEntity> GetBaselineActivities(IEnumerable<int> baselineIDs)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.Entities<ActivityEntity>().Where(act => act.BaseActivityFk.HasValue && act.BaselineFk.HasValue && baselineIDs.Contains(act.BaselineFk.Value)).
					ToList();

				return entities;
			}
		}

		/// <summary>
		/// Get an activity from a given id
		/// </summary>
		/// <param name="activityId">id of the searched activity</param>
		/// <returns>activities corresponding to the schedule id</returns>
		public ActivityEntity GetActivityById(int activityId)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				return dbcontext.Entities<ActivityEntity>().Where(activity => activity.Id == activityId).FirstOrDefault();
			}
		}

		/// <summary>
		/// Get all activities before a given date
		/// </summary>
		/// <param name="date">The date which is the filter of the returned activities</param>
		/// <returns>activities corresponding to the schedule id</returns>
		public IEnumerable<ActivityEntity> GetActivityBeforeDate(DateTime date)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.Entities<ActivityEntity>().Select(i => i).Where(activity => activity.PlannedStart < date);

				DoIncludeTransientProperties(entities);
				IncludeCompletionInformation(entities);

				return entities;
			}
		}

		/// <summary>
		/// Return activities hierarchically structured Parent->Children
		/// </summary>
		/// <param name="scheduleId">Schedule id</param>
		/// <param name="startId">start id</param>
		/// <param name="depth">depth od descendant</param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetActivitiesStructured(int scheduleId, int startId, int depth)
		{
			// TODO kh review - disabled to get restricted/no access role working
			//Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.ExecuteStoredProcedure<ActivityEntity>("PSD_ACTIVITY_ITEM_ITEMTREE_SP", true, scheduleId, startId, depth).Where(i => startId == 0 ? i.ParentActivityFk == null : i.ParentActivityFk == startId).ToList();

				DoIncludeTransientProperties(entities);

				return entities;
			}
		}

		/// <summary>
		/// Return activities hirearchically structured Parent->Children
		/// </summary>
		/// <param name="scheduleId">Schedule id</param>
		/// <param name="startId">start id</param>
		/// <param name="depth">depth od descendant</param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetActivitiesStructuredWithCompletion(int scheduleId, int startId, int depth)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.ExecuteStoredProcedure<ActivityEntity>("PSD_ACTIVITY_ITEM_ITEMTREE_SP", true, scheduleId, startId, depth).Where(i => startId == 0 ? i.ParentActivityFk == null : i.ParentActivityFk == startId).ToList();

				DoIncludeTransientProperties(entities);
				IncludeCompletionInformation(entities);

				return entities;
			}
		}
		/// <summary>
		/// Return activities hirearchically structured Parent->Children
		/// </summary>
		/// <param name="scheduleId">Schedule id</param>
		/// <param name="startId">start id</param>
		/// <param name="depth">depth od descendant</param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetActivitieTree(int scheduleId, int startId, int depth)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.ExecuteStoredProcedure<ActivityEntity>("PSD_ACTIVITY_ITEM_ITEMTREE_SP", true, scheduleId, startId, depth).Where(i => startId == 0 ? i.ParentActivityFk == null : i.ParentActivityFk == startId).ToList();

				return entities;
			}
		}

		/// <summary>
		/// Return activities hirearchically
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetActivitieTreeByProjectId(int projectId)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var entities = dbcontext.Entities<ActivityEntity>().Include(e => e.ActivityEntities_ParentActivityFk).Where(e => e.ProjectFk == projectId).ToList();

				return entities;
			}
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="projectId"></param>
		/// <returns></returns>
		public IEnumerable<IActivityEntity> GetTreeByProjectId(int projectId)
		{
			return GetActivitieTreeByProjectId(projectId);
		}

		/// <summary>
		/// Get all project activities with the given schedule id and filter value
		/// </summary>
		/// <param name="scheduleId">id of the schedule</param>
		/// <param name="filterValue">filter value</param>
		/// <returns>activities corresponding to the schedule id and filter value</returns>
		public IEnumerable<ActivityEntity> GetProjectActivitiesSearchList(int scheduleId, string filterValue)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				IQueryable<ActivityEntity> query = null;
				var entities = new List<ActivityEntity>();
				query = dbContext.Entities<ActivityEntity>();
				var filter = string.Format("({0})", filterValue);
				query = query.Where(e => e.ScheduleFk == scheduleId && e.ParentActivityFk == null && e.BaselineFk == null && e.IsLive);
				query = query.Where(filter);
				if (query.Any())
				{
					entities = query.OrderBy(e => e.Code).ToList();
				}
				return entities;
			}
		}

		/// <summary>
		/// Get all project activities with the given schedule id and filter value Activity Code
		/// </summary>
		/// <param name="projectId">id of the schedule</param>
		/// <param name="filterValue">filter value</param>
		/// <returns>activities corresponding to the schedule id and filter value</returns>
		public int? GetProjectActivityByCode(int? projectId, string filterValue)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);

			var dbConn = (System.Data.SqlClient.SqlConnection)RVPBC.DbContext.CreateConnection();

			int? activityId = null;

			using (SqlConnection sqlConn = new SqlConnection(dbConn.ConnectionString))
			{
				string sql = "SELECT * FROM PSD_ACTIVITY WHERE PRJ_PROJECT_FK = @projectId AND CODE = @filterValue AND PSD_BASELINE_FK IS NULL AND ISLIVE = 1";

				sqlConn.Open();
				SqlCommand command = new SqlCommand(sql, sqlConn);

				command.Parameters.AddWithValue("@projectId", projectId);
				command.Parameters.AddWithValue("@filterValue", filterValue);

				SqlDataReader reader = command.ExecuteReader();
				while (reader.Read())
				{
					return activityId = Int32.Parse(reader.GetValue(0).ToString());
				}
			}

			return activityId;
		}

		/// <summary>
		/// creates new activity/ies from the given list
		/// </summary>
		/// <returns>the created activities</returns>
		public ActivityEntity CreateActivity(int scheduleId, int projectId, int? parentActivityId)
		{
			Permission.Ensure(_permissionGUID, Permissions.Create);

			var entity = doCreateActivity(scheduleId, projectId, parentActivityId);
			var calendarLogic = new Calendar.BusinessComponents.CalendarUtilitiesLogic();

			//Adjust to next working day according calendar.
			entity.PlannedStart = calendarLogic.GetNextWorkingDay(entity.CalendarFk, entity.PlannedStart);
			entity.CurrentStart = calendarLogic.GetNextWorkingDay(entity.CalendarFk, entity.PlannedStart);
			entity.PlannedFinish = new DateTime(entity.PlannedStart.Year, entity.PlannedStart.Month, entity.PlannedStart.Day, 23, 59, 59);
			entity.CurrentFinish = entity.PlannedFinish;

			return entity;
		}

		/// <summary>
		/// creates new activity/ies from the given list
		/// </summary>
		/// <returns>the created activities</returns>
		public ActivityEntity CreateActivity(int scheduleId, int projectId, int parentActivityId, String tempGUID)
		{
			EnsureTempGUID(tempGUID);

			return doCreateActivity(scheduleId, projectId, parentActivityId);
		}

		/// <summary>
		/// creates new activity and sets some values from the summary activity
		/// </summary>
		/// <returns>the created activity</returns>
		public ActivityEntity CreateActivity(int scheduleId, int projectId, int parentActivityId, string lastCode, bool newHierarchy, ActivityEntity parentActivity)
		{
			var activity = CreateActivity(scheduleId, projectId, parentActivityId, lastCode, newHierarchy);
			if (parentActivityId > 0 && newHierarchy)
			{
				var siblings = GetActivityByParentId((long)parentActivityId);
				if (siblings == null || !siblings.Any())
				{
					activity.PlannedStart = parentActivity.PlannedStart;
					activity.CurrentStart = parentActivity.CurrentStart;
					activity.PlannedDuration = parentActivity.PlannedDuration;
					activity.CurrentDuration = parentActivity.CurrentDuration;
					activity.PlannedFinish = parentActivity.PlannedFinish;
					activity.CurrentFinish = parentActivity.CurrentFinish;
					activity.Quantity = parentActivity.Quantity;
					activity.QuantityUoMFk = parentActivity.QuantityUoMFk;
				}
			}
			return activity;
		}

		/// <summary>
		/// creates new activity/ies from the given list
		/// </summary>
		/// <returns>the created activities</returns>
		public ActivityEntity CreateActivity(int scheduleId, int projectId, int? parentActivityId, string lastCode, bool newHierarchy)
		{
			Permission.Ensure(_permissionGUID, Permissions.Create);

			ActivityEntity entity = CreateActivity(scheduleId, projectId, parentActivityId);
			// code generation
			//ActivityCodeLogic codeLogic = ActivityCodeLogic.Instance;

			int? codeFormatFk = null;
			ScheduleLogic scheduleLogic = new ScheduleLogic();
			var schedule = scheduleLogic.GetCoresAsListByFilter(e => e.Id == scheduleId).FirstOrDefault();

			if (schedule != null)
			{
				if (schedule.CodeFormatFk.HasValue)
				{
					codeFormatFk = schedule.CodeFormatFk.Value;
				}
			}

			if (!codeFormatFk.HasValue)
			{
				var defCodeFormat = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValues<IDefaultEntityProvider>("basics.customize.codeformat").FirstOrDefault();
				var format = defCodeFormat.GetDefaultForCurrentContext();
				if (format != null)
				{
					codeFormatFk = format.Id;
				}
			}

			var generator = EntityCodeGeneratorFactory.GetGenerator(codeFormatFk);
			string mLastCode = lastCode;
			bool mNewHierarchy = newHierarchy;
			IEntityCore last = new ActivityEntity();
			ActivityEntity lastItem = null;
			if (generator.GetType().Name.StartsWith("Running"))
			{
				lastItem = GetActivity(e => e.ScheduleFk == scheduleId).OrderBy(e => e.Code).LastOrDefault();
			}
			else
			{
				lastItem = GetActivity(e => e.ParentActivityFk == parentActivityId && e.ScheduleFk == scheduleId).OrderBy(e => e.Code).LastOrDefault();
			}
			last.Code = mLastCode;
			if (lastItem != null)
			{
				last.Code = lastItem.Code;
				mNewHierarchy = false;
				if (!lastCode.IsNullOrEmpty())
				{
					var code1Parts = lastCode.Split('.');
					var code2Parts = lastItem.Code.Split('.');
					if (code1Parts.Count() >= code2Parts.Count())
					{
						if (code1Parts[code2Parts.Count() - 1].CompareTo(code2Parts[code2Parts.Count() - 1]) < 0)
						{
							last.Code = lastItem.Code;
						}
					}
				}
			}

			bool isValid = false;
			while (!isValid)
			{
				//string code = codeLogic.GenerateCode(mLastCode, mNewHierarchy);
				string code;
				if (mNewHierarchy)
				{
					code = generator.GetSingleChildCode(last);
				}
				else
				{
					code = generator.GetSingleCode(last);
				}
				if (!String.IsNullOrEmpty(code))
				{
					entity.Code = code;
					//mLastCode = code;
					last.Code = code;
					mNewHierarchy = false;
					//if (IsUnique(e => e.Code == entity.Code && e.ScheduleFk == entity.ScheduleFk))
					if (IsUnique(e => e.Code == code && e.ScheduleFk == entity.ScheduleFk && !e.BaselineFk.HasValue && !e.BaseActivityFk.HasValue))
					{
						isValid = true;
					}
				}
			}

			return entity;
		}
		/// <summary>
		/// Saves an activity entity
		/// </summary>
		/// <param name="entity">The entity to be saved</param>
		/// <returns>The actual state of the saved entity</returns>
		public ActivityEntity SaveActivity(ActivityEntity entity)
		{
			Permission.Ensure(_permissionGUID, Permissions.Write);

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var planUtils = new PlanningUtilities();

				//calculate and set duration or planned finish date.
				if (entity != null)
				{
					planUtils.CalculateDuration(entity);

					//set earliest/latest start/end if constraint is set
					planUtils.CalculateConstraintDates(entity);
				}


				return SaveActivityWithoutDurationCalculation(entity, dbcontext);
			}
		}

		/// <summary>
		/// Gives access to sequence manager in order to make it available in schedule generation
		/// </summary>
		public ISequenceManager GetSequenceManager()
		{
			return SequenceManager;
		}

		private ActivityEntity SaveActivityWithoutDurationCalculation(ActivityEntity activityEntity)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				return SaveActivityWithoutDurationCalculation(activityEntity, dbcontext);
			}
		}

		private ActivityEntity SaveActivityWithoutDurationCalculation(ActivityEntity entity, RVPBC.DbContext dbcontext)
		{
			try
			{
				var version = entity.Version;
				dbcontext.Save(entity);
				ExecuteStoredProcSearchPattern(entity.Id, dbcontext);
				if (entity.ParentActivityFk != entity.ParentActivityIdAsRead || version == 0)
				{
					ExecuteStoredProcLevelInformation(entity.ScheduleFk, dbcontext);
				}
			}
			catch (DbEntityValidationException e)
			{
				String errorMessage = String.Empty;
				foreach (var eve in e.EntityValidationErrors)
				{

					errorMessage += NLS.ERR_EntityValidation;
					foreach (var ve in eve.ValidationErrors)
					{
						errorMessage += String.Format(NLS.ERR_EntityValidationErrMessage, ve.PropertyName, ve.ErrorMessage);
					}
				}
				throw new SchedulingBusinessLayerException
				{
					ErrorCode = (int)ExceptionErrorCodes.ResourceFatalError,
					ErrorDetail = errorMessage,
					ErrorMessage = errorMessage
				};
			}
			//long activityId = entity.Id;

			//return dbcontext.Entities<ActivityEntity>().FirstOrDefault(activity => activity.Id == activityId);
			return entity;
		}


		private void UpdateEntitiesInDbContext<T>(IEnumerable<T> entitiesToSave, IEnumerable<T> entitiesToDelete, RVPBC.DbContext dbContext) where T : RVPBC.EntityBase, IIdentifyable
		{
			if (entitiesToSave != null && entitiesToSave.Any())
			{
				foreach (var entity in entitiesToSave)
				{
					var dbEntity = dbContext.Entities<T>().Find(entity.Id);
					if (dbEntity == null)
					{
						dbContext.Entities<T>().Add(entity);
					}
					else
					{
						dbContext.Entry(dbEntity).CurrentValues.SetValues(entity);
					}
				}
			}
			if (entitiesToDelete != null && entitiesToDelete.Any())
			{
				foreach (var entity in entitiesToDelete)
				{
					var dbEntity = dbContext.Entities<T>().Find(entity.Id);
					if (dbEntity != null)
					{
						dbContext.Entry(dbEntity).State = EntityState.Deleted;
					}
				}
			}
		}

		/// <summary>
		/// Update activities
		/// </summary>
		/// <param name="items">all activities that are updated</param>
		public List<ActivityEntity> UpdateActivities(IEnumerable<ActivityEntity> items)
		{
			Permission.Ensure(_permissionGUID, Permissions.Write);
			ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(items));

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				return dbcontext.Save(items).ToList();
			}
		}
		/// <summary>
		        /// insert activities from ext sys
		        /// </summary>
		        /// <param name="items">all activities that are updated</param>
		public List<ActivityEntity> InsertActivities(IEnumerable<ActivityEntity> items)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var chunks = items.ToSizedChunks(5000);
				foreach (var chunk in chunks)
				{
					var bulkhelper = new BulkSaveHelper();
					bulkhelper.BulkInsert(dbcontext, chunk);
				}
			}
			return null;
		}


		/// <summary>
		/// insert activities progress report from ext sys
		/// </summary>
		/// <param name="items"></param>
		///    items:
		///     all progress report activities that are updated
		/// <returns></returns>
		public List<ActivityProgressReportEntity> InsertProgressReportActivities(IEnumerable<ActivityProgressReportEntity> items)
		{
			using (RIB.Visual.Platform.BusinessComponents.DbContext dbContext = new RIB.Visual.Platform.BusinessComponents.DbContext(ModelBuilder.DbModel))
			{
				IEnumerable<IEnumerable<ActivityProgressReportEntity>> enumerable = items.ToSizedChunks(5000);
				foreach (IEnumerable<ActivityProgressReportEntity> item in enumerable)
				{
					BulkSaveHelper bulkSaveHelper = new BulkSaveHelper();
					bulkSaveHelper.BulkInsert(dbContext, item);
				}
			}



			return null;
		}
		/// <summary>
		/// update activities from ext sys
		/// </summary>
		/// <param name="items">all activities that are updated</param>
		public List<ActivityEntity> UpdateActivitiesExt(IEnumerable<ActivityEntity> items)
		{
			ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(items));

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var chunks = items.ToSizedChunks(5000);
				foreach (var chunk in chunks)
				{
					var bulkhelper = new BulkSaveHelper();
					bulkhelper.BulkUpdate(dbcontext, chunk);
				}
			}
			return null;
		}

		private static readonly LazyExportedValue<IActiveCollaboratorsManager> ActiveCollaboratorsMgr = new();

		/// <summary>
		/// Saves a singles activity with all dependend data
		/// </summary>
		/// <param name="activityComplete">The element and its entire to be saved</param>
		/// <returns></returns>
		public void Update(ActivityComplete activityComplete)
		{
			List<ActivityEntity> activities = new List<ActivityEntity>();

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var calcLogic = new Calculation();
				calcLogic.WithTransientProperties = false;
				var startLevelProc = 0;
				if (activityComplete.Activity != null)
				{
					//dbcontext.Entry(activityComplete.Activity).State = activityComplete.Activity.Version == 0 ? EntityState.Added : EntityState.Modified;
					if (activityComplete.Activity.ActivityTypeFk == Constants.ActivityTypeSubSchedule && (activityComplete.Activity.ActivitySubFk.HasValue ||
						activityComplete.Activity.ScheduleSubFk.HasValue))
					{
						activityComplete.Activity.SchedulingMethodFk = Constants.SchedulingMethodManual;
					}
					activities.Add(activityComplete.Activity);
					if (activityComplete.Activity.Version == 0)
					{
						startLevelProc = activityComplete.Activity.ScheduleFk;
					}
				}
				ScheduleDataPool pool = null;

				//var pool = new ScheduleDataPool(activityComplete, dbcontext);
				var schedulingModelObjectSimulationLogic = new SchedulingOjectModelSimulationLogic();

				if (activityComplete.RelationshipsToDelete != null && activityComplete.RelationshipsToDelete.Any())
				{
					if (pool == null)
					{
						pool = new ScheduleDataPool(activityComplete, dbcontext);
					}
					new SchedulingAction().HandleDeleteRelationships(activityComplete, pool, dbcontext);
				}
				if (activityComplete.RelationshipsToSave != null && activityComplete.RelationshipsToSave.Any())
				{
					if (pool == null)
					{
						pool = new ScheduleDataPool(activityComplete, dbcontext);
					}
					var changes = calcLogic.ValidateRelationships(activityComplete.RelationshipsToSave, pool);
					if (changes.EffectedActivities != null && changes.EffectedActivities.Any())
					{
						if (activityComplete.EffectedActivities == null || !activityComplete.EffectedActivities.Any())
						{
							activityComplete.EffectedActivities = changes.EffectedActivities;
						}
						else
						{
							foreach (var effected in changes.EffectedActivities)
							{
								if (!activityComplete.EffectedActivities.Contains(effected))
								{
									activityComplete.EffectedActivities.Add(effected);
								}
							}
						}
					}
				}

				if (activityComplete.ActivityPlanningChange != null &&
					 (activityComplete.ActivityPlanningChange.CalculationNeeded == true || activityComplete.ActivityPlanningChange.ChangedField == "CalculateSummary"))
				{
					if (pool == null)
					{
						pool = new ScheduleDataPool(activityComplete, dbcontext);
					}

					calcLogic.ValidateComplete(activityComplete, pool);
					if (activityComplete.ActivityPlanningChange.CalculationNeeded == true)
					{
						calcLogic.CalculateActivity2ModelObject(activityComplete, 1);
						var splits = new ActivitySplitsLogic().ValidateActivitySplitsEndDate(activities, pool);
						if (splits != null)
						{
							if (activityComplete.SplitsToSave == null)
							{
								activityComplete.SplitsToSave = new List<ActivitySplitEntity>();
							}
							activityComplete.SplitsToSave.AddRange(splits);
						}
					}
				}

				if (activityComplete.EffectedActivities != null && activityComplete.EffectedActivities.Any())
				{
					foreach (var tmpAct in activityComplete.EffectedActivities)
					{
						if (activities.All(a => a.Id != tmpAct.Id))
						{
							activities.Add(tmpAct);
						}
					}
				}

				//var parentActivities = new List<ActivityEntity>();
				//foreach (var actvity in activities)
				//{
				//	if (actvity.ParentActivityFk.HasValue)
				//	{
				//		if (pool == null)
				//		{
				//			pool = new ScheduleDataPool(activityComplete, dbcontext);
				//		}
				//		var tmpAct = pool.GetActivities(e => e.Id == actvity.ParentActivityFk.Value).FirstOrDefault();
				//		if (tmpAct != null && tmpAct.ActivityTypeFk != Constants.ActivityTypeSummaryActivity)
				//		{
				//			tmpAct.ActivityTypeFk = Constants.ActivityTypeSummaryActivity;
				//			if (activities.All(a => a.Id != tmpAct.Id) && parentActivities.All(a => a.Id != tmpAct.Id))
				//			{
				//				parentActivities.Add(tmpAct);
				//			}
				//		}
				//	}
				//}
				//activities.AddRange(parentActivities);

				if (activityComplete.ProgressReportsToDelete != null && activityComplete.ProgressReportsToDelete.Any())
				{
					var mdlObjectReportsToDelete = activityComplete.ProgressReportsToDelete.Where(e => e.Activity2ModelObjectFk.HasValue).ToArray();
					if (mdlObjectReportsToDelete.Length > 0)
					{
						if (activityComplete.ObjModelSimulationToSave == null)
						{
							activityComplete.ObjModelSimulationToSave = schedulingModelObjectSimulationLogic.DeleteReports(mdlObjectReportsToDelete).ToList();

						}
						else
						{
							activityComplete.ObjModelSimulationToSave.AddRange(schedulingModelObjectSimulationLogic.DeleteReports(mdlObjectReportsToDelete));
						}
					}
				}

				using (TransactionScope transaction = TransactionScopeFactory.Create())
				{
					EntityUpdateDispatcher.Handle(activityComplete);

					if (activityComplete.ActivityByBaselineToSave != null)
					{
						AddActivityInfoProperties(activityComplete.ActivityByBaselineToSave);
					}

					if (activityComplete.BelongsToActivityEstLineItemDtoToSave != null || activityComplete.BelongsToActivityEstLineItemDtoToDelete != null)
					{//Individual interfaces are not supported
						var estLineItemLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();

						if (activityComplete.BelongsToActivityEstLineItemDtoToSave != null)
						{
							List<IEstLineItemEntity> entities = new List<IEstLineItemEntity>();
							if (pool == null)
							{
								pool = new ScheduleDataPool(activityComplete, dbcontext);
							}
							var activityEntity = pool.GetActivityById(activityComplete.MainItemId);
							foreach (var item in activityComplete.BelongsToActivityEstLineItemDtoToSave)
							{
								entities.Add((IEstLineItemEntity)estLineItemLogic.Save(item));
								schedulingModelObjectSimulationLogic.SaveActivity2ModelObjectDatafromBidAndJobEstimate(entities.ToList(), activityEntity);
							}
							activityComplete.BelongsToActivityEstLineItemDtoToSave = entities;
						}
					}

					if (activities.Any() || (activityComplete.ProgressReportsToSave != null && activityComplete.ProgressReportsToSave.Any()) ||
						(activityComplete.HammockActivityToDelete != null && activityComplete.HammockActivityToDelete.Any()) ||
						(activityComplete.HammockActivityToSave != null && activityComplete.HammockActivityToSave.Any()))
					{
						ActivityEntity act = activities.FirstOrDefault();
						if (act == null && activityComplete.ProgressReportsToSave != null && activityComplete.ProgressReportsToSave.Any())
						{
							if (pool == null)
							{
								pool = new ScheduleDataPool(activityComplete, dbcontext);
							}
							act = pool.GetActivityById(activityComplete.ProgressReportsToSave.First().ActivityFk.Value);
							if (activities.All(a => a.Id != act.Id))
							{
								activities.Add(act);
							}
						}

						var missingActivityFks = new List<int>();
						if (activityComplete.HammockActivityToDelete != null && activityComplete.HammockActivityToDelete.Any())
						{
							missingActivityFks.AddRange(activityComplete.HammockActivityToDelete.Select(ha => ha.ActivityFk));
						}
						if (activityComplete.HammockActivityToSave != null && activityComplete.HammockActivityToSave.Any())
						{
							missingActivityFks.AddRange(activityComplete.HammockActivityToSave.Select(ha => ha.ActivityFk));
						}
						missingActivityFks = missingActivityFks.Distinct().ToList();
						missingActivityFks = missingActivityFks.Where(ci => activities.All(a => a.Id != ci)).Select(hi => hi).ToList();
						if (missingActivityFks.Any())
						{
							if (pool == null)
							{
								pool = new ScheduleDataPool(activityComplete, dbcontext);
							}
							activities.AddRange(pool.GetActivities(a => missingActivityFks.Contains(a.Id)));
						}
						if (act == null)
						{
							act = activities.FirstOrDefault();
						}

						if (activityComplete.MainItemId > 0)
						{
							if (!activityComplete.HammockActivityToDelete.IsNullOrEmpty() || !activityComplete.HammockActivityToSave.IsNullOrEmpty())
							{
								if (pool == null)
								{
									pool = new ScheduleDataPool(activityComplete, dbcontext);
								}
								var mainActivity = pool.GetActivityById(activityComplete.MainItemId);

								var todelete = activityComplete.HammockActivityToDelete != null ? activityComplete.HammockActivityToDelete : new List<HammockActivityEntity>();
								var tosave = activityComplete.HammockActivityToSave != null ? activityComplete.HammockActivityToSave : new List<HammockActivityEntity>();

								this.refreshDateFields(mainActivity, tosave, todelete);
								this.refreshDateFields(activities.First(), tosave, todelete);
							}
						}

						if (activityComplete.Activity != null && activityComplete.Activity.IsAssignedToHammock && activityComplete.ActivityPlanningChange.CalculationNeeded.HasValue && activityComplete.ActivityPlanningChange.CalculationNeeded.Value)
						{
							if (pool == null)
							{
								pool = new ScheduleDataPool(activityComplete, dbcontext);
							}
							//adding activities when their hammock relation was changed in order to recalculate their HasHamock property
							var toDeleteHammocks = pool.Hammocks.Where(ham => ham.ActivityMemberFk == activityComplete.MainItemId);
							IEnumerable<int> changedHammockActivityIds = new List<int>();
							foreach (var ham in toDeleteHammocks)
							{
								changedHammockActivityIds = changedHammockActivityIds.Concat(pool.Hammocks.Where(h => h.ActivityFk == ham.ActivityFk).Select(secHam => secHam.ActivityFk));
							}
							changedHammockActivityIds = changedHammockActivityIds.Distinct();
							var activitiesIds = activities.Select(seAct => seAct.Id);
							activities.AddRange(pool.Activities.Where(wheAct => changedHammockActivityIds.Contains(wheAct.Id) && !activitiesIds.Contains(wheAct.Id)));
						}
						//adding activities to "actvities" that transient property (predecessor and successor) needed to be updated when a relation to or from it was added or deleted
						var dueToRelationChange2BeUpdatedActivities = Enumerable.Empty<ActivityEntity>();
						var dueToRelationChange2BeUpdatedActivitiesIds = Enumerable.Empty<int>();
						if (activityComplete.RelationshipsToDelete != null && activityComplete.RelationshipsToDelete.Any())
						{
							dueToRelationChange2BeUpdatedActivitiesIds = dueToRelationChange2BeUpdatedActivitiesIds.Concat(activityComplete.RelationshipsToDelete.Select(rel => rel.ParentActivityFk));
							dueToRelationChange2BeUpdatedActivitiesIds = dueToRelationChange2BeUpdatedActivitiesIds.Concat(activityComplete.RelationshipsToDelete.Select(rel => rel.ChildActivityFk));
						}
						if (activityComplete.RelationshipsToSave != null && activityComplete.RelationshipsToSave.Any())
						{
							dueToRelationChange2BeUpdatedActivitiesIds = dueToRelationChange2BeUpdatedActivitiesIds.Concat(activityComplete.RelationshipsToSave.Select(rel => rel.ParentActivityFk));
							dueToRelationChange2BeUpdatedActivitiesIds = dueToRelationChange2BeUpdatedActivitiesIds.Concat(activityComplete.RelationshipsToSave.Select(rel => rel.ChildActivityFk));
						}
						if (dueToRelationChange2BeUpdatedActivitiesIds.Any())
						{
							if (pool == null)
							{
								pool = new ScheduleDataPool(activityComplete, dbcontext);
							}
							dueToRelationChange2BeUpdatedActivities = pool.Activities.Where(actJ => dueToRelationChange2BeUpdatedActivitiesIds.Contains(actJ.Id));
							activities.AddRange(dueToRelationChange2BeUpdatedActivities);
						}
						activities = activities.Distinct().ToList();
						if (activityComplete.EffectedActivities != null && activityComplete.RelationshipsToSave != null)
						{
							AddSomeMorePredecessorSuccessor(activityComplete.EffectedActivities,
								activityComplete.RelationshipsToSave);
						}
					}

					ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(activities));

					UpdateActivityCompleteWithBulkSaveHelper(activityComplete, dbcontext);

					if (activityComplete.Activity != null && activityComplete.Activity.ActivityMasterFk.HasValue)
					{
						var masterActivity = GetActivityById(activityComplete.Activity.ActivityMasterFk.Value);
						if (masterActivity != null)
						{
							masterActivity.ActivitySubFk = activityComplete.Activity.Id;
							masterActivity.ScheduleSubFk = activityComplete.Activity.ScheduleFk;
							dbcontext.Save(masterActivity);
						}
					}

					transaction.Complete();
				}

				ExecuteStoredProcSearchPattern(activityComplete.MainItemId, dbcontext);
				var schedules =
					activities.Where(a => a.ParentActivityFk != a.ParentActivityIdAsRead).Select(c => c.ScheduleFk).Distinct().ToList();
				if (startLevelProc > 0)
				{
					schedules.Add(startLevelProc);
				}
				foreach (var schedule in schedules)
				{
					ExecuteStoredProcLevelInformation(schedule, dbcontext);
				}

				if (activityComplete.MainItemId > 0 && (!activityComplete.ProgressReportsToDelete.IsNullOrEmpty() || !activityComplete.ProgressReportsToSave.IsNullOrEmpty()))
				{
					var dueDate = activityComplete.ActivityPlanningChange != null
					? activityComplete.ActivityPlanningChange.DueDate
					: DateTime.Today;
					if (pool == null)
					{
						pool = new ScheduleDataPool(activityComplete, dbcontext);
					}
					var mainActivity = pool.GetActivityById(activityComplete.MainItemId);
					if (activities.All(a => a.Id != mainActivity.Id))
					{
						activities.Add(mainActivity);
					}
					var period = new RVBC.BasicsCompanyPeriodLogic().GetPeriod(mainActivity.CompanyFk, dueDate);

					activities = DoIncludeCompletionInformationForChanged(mainActivity.ScheduleFk, activities, new CompletionCalculation(), DateTime.Now, period, pool);
					if (activities.Count > 0)
					{
						if (activityComplete.EffectedActivities == null)
						{
							activityComplete.EffectedActivities = new List<ActivityEntity>();
						}

						if (activityComplete.EffectedActivities != null)
						{
							foreach (var tmpAct in activities)
							{
								if (activityComplete.EffectedActivities.All(a => a.Id != tmpAct.Id))
								{
									activityComplete.EffectedActivities.Add(tmpAct);
								}
							}
						}
					}
					if (activityComplete.ProgressReportsToSave != null)
					{
						new ActivityProgressReportLogic().FillModelValues(activityComplete.ProgressReportsToSave);
					}
					if (activityComplete.ObjModelSimulationToSave != null)
					{
						activityComplete.ObjModelSimulationToSave = new SchedulingOjectModelSimulationLogic().GetHeaderAndLineItemInfo(activityComplete.ObjModelSimulationToSave).ToList();
					}

				}
				DoIncludeTransientProperties(activities, pool);
			}
		}

		/// <summary>
		/// Adds some more predecessor / successors
		/// </summary>
		/// <param name="effectedActivities"></param>
		/// <param name="relationshipsToSave"></param>
		public void AddSomeMorePredecessorSuccessor(
			List<ActivityEntity> effectedActivities, IEnumerable<ActivityRelationshipEntity> relationshipsToSave)
		{
			if (effectedActivities == null || relationshipsToSave == null)
			{
				return;
			}

			var successorAndPredecessorData = new PredecessorsAndSuccessorsStringGen();
			successorAndPredecessorData.Generate(
							from ea in effectedActivities select ea.Id,
				e => effectedActivities,
				rs => relationshipsToSave,
				256);


			foreach (var a in effectedActivities)
			{
				if (a.Predecessor == null)
				{
					a.Predecessor = new List<dynamic>();
				}
				if (a.Successor == null)
				{
					a.Successor = new List<dynamic>();
				}

				successorAndPredecessorData.GetPredecessor(a).ForEach(p =>
				{
					// already added?
					if (a.Predecessor.Find(x => x.id == p.id) == null)
					{
						a.Predecessor.Add(p);
					}
				});
				successorAndPredecessorData.GetSuccessor(a).ForEach(p =>
				{
					// already added?
					if (a.Successor.Find(x => x.id == p.id) == null)
					{
						a.Successor.Add(p);
					}
				});
			}

		}

		/// <summary>
		/// Update activity complete with EffectedActivities, Clerks, Events, Relationships, ProgressReports
		/// </summary>
		/// <param name="activityComplete">ActivityComplete Entity</param>
		public void UpdateOnly(ActivityComplete activityComplete)
		{
			ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts([activityComplete]));

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				using (
					TransactionScope transaction = TransactionScopeFactory.Create())
				{
					//if (activityComplete.Activity != null)
					//{
					//	dbcontext.Entry(activityComplete.Activity).State = activityComplete.Activity.Version == 0 ? EntityState.Added : EntityState.Modified;
					//}
					//UpdateEntitiesInDbContext(activityComplete.EffectedActivities, null, dbcontext);
					//UpdateEntitiesInDbContext(activityComplete.ClerksToSave, activityComplete.ClerksToDelete, dbcontext);
					//UpdateEntitiesInDbContext(activityComplete.EventsToSave, activityComplete.EventsToDelete, dbcontext);
					//UpdateEntitiesInDbContext(activityComplete.RelationshipsToSave, activityComplete.RelationshipsToDelete, dbcontext);
					//UpdateEntitiesInDbContext(activityComplete.ProgressReportsToSave, activityComplete.ProgressReportsToDelete,
					//	dbcontext);
					//UpdateEntitiesInDbContext(activityComplete.HammockActivityToSave, activityComplete.HammockActivityToDelete,
					//	dbcontext);
					//EntityUpdateDispatcher.Handle(activityComplete);
					if (activityComplete.BelongsToActivityEstLineItemDtoToSave != null ||
						 activityComplete.BelongsToActivityEstLineItemDtoToDelete != null)
					{
						//Individual interfaces are not supported
						var estLineItemLogic =
							Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();

						if (activityComplete.BelongsToActivityEstLineItemDtoToSave != null)
						{
							List<IIdentifyable> entities = new List<IIdentifyable>();
							foreach (var item in activityComplete.BelongsToActivityEstLineItemDtoToSave)
							{
								entities.Add((IIdentifyable)estLineItemLogic.Save(item));
							}
							activityComplete.BelongsToActivityEstLineItemDtoToSave = entities;
						}
					}
					//UpdateEntitiesInDbContext(activityComplete.SplitsToSave, activityComplete.SplitsToDelete, dbcontext);

					//dbcontext.DetectChangesOnSave = true;
					//dbcontext.SaveChanges();

					UpdateActivityCompleteWithBulkSaveHelper(activityComplete, dbcontext);

					if (activityComplete.LineItemsToUpdate != null && activityComplete.LineItemsToUpdate.Any())
					{
						var estLineItemLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IEstimateMainLineItemLogic>();
						foreach (var li in activityComplete.LineItemsToUpdate)
						{
							estLineItemLogic.Save(li);
						}
					}

					transaction.Complete();
				}
			}
		}

		/// <summary>deletes given activity</summary>
		/// <param name="entity">activity to delete</param>
		/// <returns>true, if successful</returns>
		public void DeleteActivity(ActivityEntity entity)
		{
			DeleteActivities(new List<ActivityEntity>() { entity });
		}

		/// <summary>
		/// Delete list of given activities
		/// </summary>
		/// <param name="entities">list of activities to delete</param>
		public IEnumerable<ActivityEntity> DeleteActivities(IEnumerable<ActivityEntity> entities)
		{
			Permission.Ensure(_permissionGUID, Permissions.Delete);
			List<ActivityEntity> res = new List<ActivityEntity>();
			var childIds = entities.Where(a => a.ActivityTypeFk == Constants.ActivityTypeActivity || a.ActivityTypeFk == Constants.ActivityTypeMilestone).Select(ac => ac.Id).ToList();
			IEnumerable<ActivityRelationshipEntity> deletedRelations = new ActivityRelationshipLogic().GetCoresAsListByFilter(r => childIds.Contains(r.ParentActivityFk));
			List<int> activityIds = new List<int>();
			GetActvityIdsRecrusive(entities, activityIds);
			IEnumerable<HammockActivityEntity> hammocks = new HammockActivityLogic().GetByActivityIds(activityIds).ToList();
			var toDelete = GetEntitiesToDelete(entities);
			if (toDelete != null && toDelete.Any())
			{
				var edg = new EntityDeleteGuard();
				var relInfo = edg.GetEntityRelationInfo("scheduling.main.activity");
				try
				{
					edg.Delete(relInfo, toDelete);
				}
				catch (TransactionAbortedException) { }//Defect: #112022 Database deadlock when deleting the schedule activity

				using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{
					var adjustSummaries = AdjustSummariesAfterDelete(toDelete, dbcontext);
					if (adjustSummaries != null && adjustSummaries.Any())
					{
						res.AddRange(adjustSummaries);
					}
					if (deletedRelations != null && deletedRelations.Any())
					{
						res.AddRange(AdjustSuccessorsAfterDelete(deletedRelations));
					}
					var changedActOfHammock = GetChangedActivitiesOfHammockMembers(hammocks);
					if (changedActOfHammock != null && changedActOfHammock.Any())
					{
						res.AddRange(GetChangedActivitiesOfHammockMembers(hammocks));
					}
				}
			}
			return res;
		}

		private void GetActvityIdsRecrusive(IEnumerable<IScriptActivity> entities, List<int> activityIds)
		{
			foreach (var entity in entities)
			{
				activityIds.Add(entity.Id);
				if (entity.ActivityChildren.Any() && entity.ActivityChildren.Count > 0)
				{
					GetActvityIdsRecrusive(entity.ActivityChildren, activityIds);
				}
			}
		}

		private IEnumerable<ActivityEntity> GetChangedActivitiesOfHammockMembers(IEnumerable<HammockActivityEntity> hammocks)
		{
			List<ActivityEntity> changedActivityList = new List<ActivityEntity>();
			if (hammocks.Any())
			{
				foreach (var hammock in hammocks)
				{
					var isAssignedToOtherHammock = new HammockActivityLogic().IsMemberAssignedToOtherHammock(hammock);
					if (!isAssignedToOtherHammock)
					{
						// IsAssignedToHammock flag has the deafault value of false, so not necessary to change the flag.
						changedActivityList.Add(GetActivityById(hammock.ActivityMemberFk));
					}
				}
			}
			return changedActivityList;
		}

		private IEnumerable<ActivityEntity> GetEntitiesToDelete(IEnumerable<ActivityEntity> candidates)
		{
			RemoveTransientRootActivity(candidates.ToList());
			return candidates.Where(e => e != null && e.Version > 0).ToArray();
		}

		private List<ActivityEntity> AdjustSuccessorsAfterDelete(IEnumerable<ActivityRelationshipEntity> deletedRelations)
		{
			List<int> deletedRelationIds = deletedRelations.Select(e => e.ChildActivityFk).ToList();
			List<ActivityEntity> relationActivity = new ActivityLogic().GetActivity(ra => deletedRelationIds.Contains(ra.Id)).ToList();

			return relationActivity;
		}

		private List<ActivityEntity> AdjustSummariesAfterDelete(IEnumerable<ActivityEntity> entities, RVPBC.DbContext dbcontext)
		{
			List<ActivityEntity> parents = null;
			var parentIds = entities.Where(a => a.ParentActivityFk.HasValue).Select(e => e.ParentActivityFk.Value).Distinct().ToArray();
			if (parentIds.Any())
			{
				parents = dbcontext.Entities<ActivityEntity>().Where(e => parentIds.Contains(e.Id)).ToList();
				var activities = dbcontext.Entities<ActivityEntity>().Where(e => e.ParentActivityFk.HasValue && parentIds.Contains(e.ParentActivityFk.Value)).ToArray();

				if (parents != null && parents.Any())
				{
					foreach (var parent in parents)
					{
						var acts = activities.Where(e => e.ParentActivityFk == parent.Id).ToArray();
						if (acts.Any())
						{
							DateTime entityWithSmallestDate = (from d in acts select d.PlannedStart).Min();
							DateTime? entityWithBiggestDate = (from d in acts select d.PlannedFinish).Max();
							//var totalQuantity = acts.Sum(e => e.Quantity);
							var totalQuantity = (decimal)0.0;
							foreach (var act in acts)
							{
								//sum only quantities with same UoM
								if (act.Quantity.HasValue && act.QuantityUoMFk.HasValue && act.QuantityUoMFk == parent.QuantityUoMFk && act.IsQuantityEvaluated)
								{
									totalQuantity += act.Quantity.Value;
								}
							}

							parent.PlannedStart = entityWithSmallestDate;
							parent.PlannedFinish = entityWithBiggestDate;
							parent.EarliestStart = entityWithSmallestDate;
							parent.EarliestFinish = entityWithBiggestDate;
							parent.LatestStart = entityWithSmallestDate;
							parent.LatestFinish = entityWithBiggestDate;

							parent.PlannedDuration = new Calendar.BusinessComponents.CalendarUtilitiesLogic().GetDuration(parent.CalendarFk,
								parent.PlannedStart, parent.PlannedFinish.Value);

							parent.Quantity = totalQuantity;
							parent.ActivityTypeFk = Constants.ActivityTypeSummaryActivity;
						}
						else
						{
							parent.ActivityTypeFk = Constants.ActivityTypeActivity;
						}
					}

					dbcontext.Save(parents as IEnumerable<ActivityEntity>);

					var grandpas = AdjustSummariesAfterDelete(parents, dbcontext);

					if (grandpas != null && grandpas.Any())
					{
						parents.AddRange(grandpas);
					}
				}
			}

			return parents;
		}

		/// <summary>
		/// Provides completion calc info
		/// </summary>
		/// <param name="activities"></param>
		/// <param name="performanceDueDate"></param>
		/// <param name="dataPool"></param>
		public void IncludeCompletion(IEnumerable<ActivityEntity> activities, DateTime performanceDueDate, ScheduleDataPool dataPool)
		{
			IncludeCompletionInformation(activities, performanceDueDate, dataPool);
		}

		/// <summary>
		/// Provides completion calc info
		/// </summary>
		/// <param name="activities"></param>
		/// <param name="performanceDueDate"></param>
		public void IncludeCompletion(IEnumerable<ActivityEntity> activities, DateTime performanceDueDate)
		{
			IncludeCompletionInformation(activities, performanceDueDate);
		}

		private void IncludeCompletionInformation(IEnumerable<ActivityEntity> activities)
		{
			IncludeCompletionInformation(activities, DateTime.Now);
		}

		private void IncludeCompletionInformation(IEnumerable<ActivityEntity> activities, DateTime performanceDueDate)
		{
			var dataPool = new ScheduleDataPool();
			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				dataPool.AssertActivitiesLoaded(activities.Select(act => act.Id), dbContext);
			}

			IncludeCompletionInformation(activities, performanceDueDate, dataPool);
		}

		private void IncludeCompletionInformation(IEnumerable<ActivityEntity> activities, DateTime performanceDueDate, ScheduleDataPool dataPool)
		{

			if (dataPool.ProgressReports != null && dataPool.ProgressReports.Any())
			{
				var compLogic = new CompletionCalculation();
				var period = new RVBC.BasicsCompanyPeriodLogic().GetPeriod(activities.ElementAt(0).CompanyFk, performanceDueDate);

				DoIncludeCompletionInformation(activities, compLogic, performanceDueDate, period, dataPool);
			}
		}
		/// <summary>
		/// Set the property HasReports
		/// </summary>
		/// <param name="activities"></param>
		public void ActivityReportHisories(IEnumerable<ActivityEntity> activities)
		{
			var scheduleIds = activities.CollectIds(e => e.ScheduleFk).ToArray();
			ActivityProgressReportLogic logic = new ActivityProgressReportLogic();
			IEnumerable<ActivityProgressReportEntity> reportList = logic.GetCoresByFilter(e => scheduleIds.Contains(e.ScheduleFk));

			foreach (var activity in activities)
			{
				if (reportList.Any(e => e.ActivityFk == activity.Id))
				{
					activity.HasReports = true;
				}
			}
		}

		private void DoIncludeCompletionInformation(IEnumerable<ActivityEntity> activities, CompletionCalculation compLogic, DateTime performanceDueDate, RVBC.CompanyPeriodEntity period, ScheduleDataPool dataPool)
		{
			foreach (var activity in activities)
			{
				DoIncludeCompletionInformation(activity.ActivityEntities_ParentActivityFk, compLogic, performanceDueDate, period, dataPool);
				compLogic.ProvideCompletionInformationTo(activity, performanceDueDate, period, dataPool);
			}
		}

		private List<ActivityEntity> DoIncludeCompletionInformationForChanged(int mainActivityScheduleFk, List<ActivityEntity> activities, CompletionCalculation compLogic, DateTime performanceDueDate, RVBC.CompanyPeriodEntity period, ScheduleDataPool dataPool)
		{
			List<ActivityEntity> res = new List<ActivityEntity>();
			foreach (var activity in activities)
			{
				if (activity.ScheduleFk == mainActivityScheduleFk)
				{//only include complete information to activites which beloning to the schedule of the main activity
					res.Add(activity);
					//compLogic.ProvideCompletionInformationTo(activity, performanceDueDate, period, dataPool);
					int? parentId = activity.ParentActivityFk;

					while (parentId.HasValue && res.FirstOrDefault(a => a.Id == parentId.Value) == null)
					{
						var parent = dataPool.GetActivityById(parentId.Value);
						if (parent != null)
						{
							//compLogic.ProvideCompletionInformationTo(parent, performanceDueDate, period, dataPool);
							res.Add(parent);
							parentId = parent.ParentActivityFk;
						}
						else
						{
							parentId = null;
						}
					}
				}
			}

			if (res.Any())
			{
				DoIncludeCompletionInformation(res, compLogic, performanceDueDate, period, dataPool);
			}

			return res;
		}

		private List<ActivityEntity> DoIncludeTransientProperties(IEnumerable<ActivityEntity> activities, ScheduleDataPool dataPool = null)
		{
			var allActivities = new List<ActivityEntity>();

			if (activities != null && activities.Any())
			{

				DoCollectTransientProperties(activities, allActivities);

				allActivities = allActivities.Distinct().ToList();

				var predecessorsSuccessorsStringGenerator = new PredecessorsAndSuccessorsStringGen();
				if (dataPool != null && dataPool.Relationships.Any())
				{
					predecessorsSuccessorsStringGenerator.Generate(
						allActivities.Where(a => a.ActivityTypeFk == Constants.ActivityTypeActivity || a.ActivityTypeFk == Constants.ActivityTypeMilestone || a.ActivityTypeFk == Constants.ActivityTypeSubSchedule).CollectIds(e => e.Id),
						e => allActivities,
						dataPool.GetRelationshipsLoadedRelatedToActivities,
						256);
				}
				else
				{
					predecessorsSuccessorsStringGenerator.Generate(
						allActivities.Where(a => a.ActivityTypeFk == Constants.ActivityTypeActivity || a.ActivityTypeFk == Constants.ActivityTypeMilestone || a.ActivityTypeFk == Constants.ActivityTypeSubSchedule).CollectIds(e => e.Id),
						e => allActivities,
						new ActivityRelationshipLogic().GetRelationsByTempTable,
						256);
					//new ActivityRelationshipLogic().GetCoresByChildOrParentActivityIds,
				}

				var calUtilityLogic = new CalendarUtilitiesLogic();
				DoDispatchTransientProperties(activities, predecessorsSuccessorsStringGenerator, calUtilityLogic);

				AddActivityInfoProperties(allActivities);
			}
			return allActivities;
		}

		private void DoCollectTransientProperties(IEnumerable<ActivityEntity> activities, List<ActivityEntity> allActivities)
		{
			if (activities == null)
			{
				return; // Handle null case, you might log or throw an exception here
			}
			foreach (var a in activities)
			{
				if (a != null)
				{
					allActivities.Add(a);
					DoCollectTransientProperties(a.ActivityEntities_ParentActivityFk, allActivities);
				}
			}

		}

		private void DoDispatchTransientProperties(
			IEnumerable<ActivityEntity> activities,
			PredecessorsAndSuccessorsStringGen successorAndPredecessorData,
			CalendarUtilitiesLogic calUtilityLogic)
		{
			foreach (var a in activities)
			{
				//a.PlannedFinish may not be null
				a.PlannedCalendarDays = calUtilityLogic.GetTotalDuration(a.PlannedStart, a.PlannedFinish.Value).Value;
				a.ActualCalendarDays = calUtilityLogic.GetTotalDuration(a.ActualStart, a.ActualFinish);
				//a.CurrentFinish may not be null
				a.CurrentCalendarDays = calUtilityLogic.GetTotalDuration(a.CurrentStart, a.CurrentFinish.Value).Value;
				if (a.Predecessor == null)
				{
					a.Predecessor = new List<dynamic>();
				}
				if (a.Successor == null)
				{
					a.Successor = new List<dynamic>();
				}

				successorAndPredecessorData.GetPredecessor(a).ForEach(p =>
				{
					// already added?
					if (a.Predecessor.Find(x => x.id == p.id) == null)
					{
						a.Predecessor.Add(p);
					}
				});
				successorAndPredecessorData.GetSuccessor(a).ForEach(p =>
				{
					// already added?
					if (a.Successor.Find(x => x.id == p.id) == null)
					{
						a.Successor.Add(p);
					}
				});

				DoDispatchTransientProperties(a.ActivityEntities_ParentActivityFk, successorAndPredecessorData, calUtilityLogic);
			}
		}

		///// <summary>
		///// execute StoredProcedure to update SearchPattern in PSD_ACTIVITY
		///// </summary>
		///// <param name="id">id of Activity</param>
		//private void ExecuteStoredProcSearchPattern(int id)
		//{
		//	using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
		//	{
		//		dbcontext.ExecuteStoredProcedure("PSD_ACTIVITY_PROC", id);
		//	}
		//}

		/// <summary>
		/// execute StoredProcedure to update SearchPattern in PSD_ACTIVITY
		/// </summary>
		/// <param name="id">id of Activity</param>
		/// <param name="dbcontext">dbcontext</param>
		private void ExecuteStoredProcSearchPattern(int id, RVPBC.DbContext dbcontext)
		{
			dbcontext.ExecuteStoredProcedure("PSD_ACTIVITY_PROC", id);
		}

		/// <summary>
		/// execute StoredProcedure to update ActivityLevel1 - ActivityLevel8 in PSD_ACTIVITY
		/// </summary>
		/// <param name="schedule"></param>
		/// <param name="dbcontext"></param>
		public void ExecuteStoredProcLevelInformation(int schedule, RVPBC.DbContext dbcontext)
		{
			dbcontext.ExecuteStoredProcedure("PSD_ACTIVITY_LEVEL8_PROC", schedule);
		}

		/// <summary>
		/// Check if an entity with same code in same schedule already exists
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		public bool IsUnique(Expression<Func<ActivityEntity, bool>> filter = null)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				IEnumerable<ActivityEntity> entities = null;
				if (filter != null)
				{
					entities = dbcontext.GetFiltered<ActivityEntity>(filter).ToList();
				}

				if (entities.Any())
				{
					return false;
				}
				return true;
			}
		}

		private string AddPrefixAndSuffix(string code, string prefix, string suffix)
		{
			string newCode = code;

			if (!String.IsNullOrEmpty(prefix) && !code.StartsWith(prefix))
			{
				newCode = prefix + code;
			}
			if (!String.IsNullOrEmpty(suffix) && !code.EndsWith(suffix))
			{
				newCode += suffix;
			}
			return newCode;
		}

		/// <summary>
		/// creates new activity/ies from the given list
		/// </summary>
		/// <returns>the created activities</returns>
		public ActivityEntity GetNextActivity(int scheduleId, int projectId, int parentId, ActivityEntity activity, ActivityEntity lastEntity = null)
		{
			Permission.Ensure(_permissionGUID, Permissions.Read);
			int? codeFormatFk = null;
			ScheduleLogic scheduleLogic = new ScheduleLogic();
			var schedule = scheduleLogic.GetCoresAsListByFilter(e => e.Id == scheduleId).FirstOrDefault();

			if (schedule != null)
			{
				if (schedule.CodeFormatFk.HasValue)
				{
					codeFormatFk = schedule.CodeFormatFk.Value;
				}
			}

			var generator = EntityCodeGeneratorFactory.GetGenerator(codeFormatFk);
			var prefix = EntityCodeGeneratorFactory.GetPrefix(codeFormatFk);
			var suffix = EntityCodeGeneratorFactory.GetSuffix(codeFormatFk);

			var code = "";
			var mode = 0;
			ActivityEntity newActivity = CreateActivity(scheduleId, projectId, parentId);
			var entity = lastEntity;
			if (lastEntity == null)
			{
				entity = GetActivity(e => e.Code.CompareTo(activity.Code) > 0 && e.ScheduleFk == activity.ScheduleFk && e.ParentActivityFk == activity.ParentActivityFk).OrderBy(e => e.Code).FirstOrDefault();
			}
			string newCodeWithoutFix = "";

			if (entity == null || activity == null || String.IsNullOrEmpty(activity.Code))
			{
				//code = ActivityCodeLogic.Instance.GenerateCode(activity.Code, false);
				code = generator.GetSingleCode(activity);
				mode = 1;
			}
			else
			{
				var actCode = activity.Code;
				var entityCode = entity.Code;
				if (!String.IsNullOrEmpty(prefix))
				{
					if (actCode.StartsWith(prefix))
					{
						actCode = actCode.Remove(0, prefix.Length);
					}
					if (entityCode.StartsWith(prefix))
					{
						entityCode = entityCode.Remove(0, prefix.Length);
					}
				}
				if (!String.IsNullOrEmpty(suffix))
				{
					if (actCode.EndsWith(suffix))
					{
						//actCode = actCode.Replace(suffix, "");
						var pos = actCode.Length - suffix.Length;
						if (pos > 0 && pos < actCode.Length)
						{
							actCode = actCode.Remove(pos, suffix.Length);
						}
					}
					if (entityCode.EndsWith(suffix))
					{
						//entityCode = entityCode.Replace(suffix, "");
						var pos = entityCode.Length - suffix.Length;
						if (pos > 0 && pos < entityCode.Length)
						{
							entityCode = entityCode.Remove(pos, suffix.Length);
						}
					}
				}
				//determine code
				var code1Parts = actCode.Split('.');
				var code2Parts = entityCode.Split('.');
				var count1 = code1Parts.Count();
				var count2 = code2Parts.Count();
				long value1 = 0, value2 = 0;
				bool success1, success2;

				if (count1 == count2)
				{
					string[] text1 = Regex.Split(code1Parts[count1 - 1], @"\d+$");
					success1 = Int64.TryParse(code1Parts[count1 - 1], out value1);
					success2 = Int64.TryParse(code2Parts[count2 - 1], out value2);
					Match match1 = Regex.Match(code1Parts[count1 - 1], @"\d+$");
					Match match2 = Regex.Match(code2Parts[count2 - 1], @"\d+$");
					if (success1 && !success2)
					{
						//code = ActivityCodeLogic.Instance.GenerateCode(activity.Code, false);
						code = generator.GetSingleCode(activity);
						mode = 1;
					}
					else if ((!match1.Success && !match2.Success) || (!success1 && !success2))
					{
						code = actCode + '1';
						newCodeWithoutFix = code;
						code = AddPrefixAndSuffix(newCodeWithoutFix, prefix, suffix);
					}
					else
					{
						success1 = Int64.TryParse(match1.Value, out value1);
						success2 = Int64.TryParse(match2.Value, out value2);
						long diff = (long)(value2 - value1) / 2;
						value1 += diff;
						if (diff <= 0)
						{
							code = actCode + '1';
						}
						else if (count1 == 1)
						{
							if (text1.Any())
							{
								code = text1[0] + value1.ToString();
							}
							else
							{
								code = value1.ToString();
							}
							code = generator.GetFormattedCode(code);
						}
						else
						{
							code = code1Parts[0];
							for (int i = 1; i < count1 - 1; i++)
							{
								code = code + '.' + code1Parts[i];
							}
							code = code + '.' + generator.GetFormattedCode(value1.ToString());
						}
						newCodeWithoutFix = code;
						code = AddPrefixAndSuffix(newCodeWithoutFix, prefix, suffix);
					}
				}
				else if (count1 < count2)
				{
					string[] text2 = Regex.Split(code2Parts[count1], @"\d+$");
					success2 = Int64.TryParse(code2Parts[count1], out value2);
					if (!success2)
					{
						//code = ActivityCodeLogic.Instance.GenerateCode(activity.Code, true);
						code = generator.GetSingleChildCode(activity);
						mode = 1;
					}
					else
					{
						long diff = (long)(value2) / 2;
						code = actCode + '.' + generator.GetFormattedCode(diff.ToString());
						newCodeWithoutFix = code;
						code = AddPrefixAndSuffix(newCodeWithoutFix, prefix, suffix);
					}
				}
				else
				{
					//code = ActivityCodeLogic.Instance.GenerateCode(activity.Code, false);
					code = generator.GetSingleCode(activity);
					mode = 1;
				}
			}
			bool isValid = IsUnique(e => e.Code == code && e.ScheduleFk == activity.ScheduleFk);
			while (!isValid)
			{
				if (mode == 1)
				{
					//code = ActivityCodeLogic.Instance.GenerateCode(code, false);
					code = generator.GetSingleCode(activity);
				}
				else
				{
					newCodeWithoutFix = newCodeWithoutFix + '1';
					code = AddPrefixAndSuffix(newCodeWithoutFix, prefix, suffix);
				}
				activity.Code = code;
				if (IsUnique(e => e.Code == code && e.ScheduleFk == activity.ScheduleFk && !e.BaselineFk.HasValue && !e.BaseActivityFk.HasValue))
				{
					isValid = true;
				}

			}
			if (code.Length > 16)
			{
				throw new SchedulingBusinessLayerException
				{
					ErrorCode = (int)ExceptionErrorCodes.ResourceFatalError,
					ErrorDetail = NLS.ERR_CodeTooLong,
					ErrorMessage = NLS.ERR_CodeTooLong
				};

			}
			newActivity.Code = code;
			return newActivity;
		}

		/// <summary>
		/// move entities
		/// </summary>
		/// <param name="sourceEntities">entites to move</param>
		/// <param name="scheduleId">target id</param>
		/// <param name="targetId">target id</param>
		public IEnumerable<ActivityEntity> Move(IEnumerable<ActivityEntity> sourceEntities, int? scheduleId, int? targetId)
		{
			return CopyOrMove(sourceEntities, scheduleId, targetId, true);
		}

		/// <summary>
		/// copy entities
		/// </summary>
		/// <param name="sourceEntities">entites to move</param>
		/// <param name="scheduleId">target id</param>
		/// <param name="targetId">target id</param>
		public IEnumerable<ActivityEntity> Copy(IEnumerable<ActivityEntity> sourceEntities, int? scheduleId, int? targetId)
		{
			return CopyOrMove(sourceEntities, scheduleId, targetId, false);
		}

		/// <summary>
		/// move entities
		/// </summary>
		/// <param name="sourceEntities">entites to move</param>
		/// <param name="scheduleId">target id</param>
		/// <param name="targetId">target id</param>
		/// <param name="moveOperation">target id</param>
		public IEnumerable<ActivityEntity> CopyOrMove(IEnumerable<ActivityEntity> sourceEntities, int? scheduleId, int? targetId, bool moveOperation = true)
		{
			Permission.Ensure(_permissionGUID, Permissions.Write);
			List<ActivityEntity> activities = new List<ActivityEntity>();
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				ActivityEntity targetActivity = null;
				int? parentId = null;
				int scheduleFk;
				bool changeTypeToSummary;
				bool isOperationAllowed;
				parentId = targetId;
				if (targetId.HasValue)
				{
					targetActivity = dbcontext.Entities<ActivityEntity>().Include(e => e.ActivityEntities_ParentActivityFk).FirstOrDefault(activity => activity.Id == targetId);
					if (targetActivity != null)
					{
						changeTypeToSummary = targetActivity.ActivityTypeFk == Constants.ActivityTypeActivity || targetActivity.ActivityTypeFk == Constants.ActivityTypeMilestone;
						bool isNotSummary = targetActivity.ActivityTypeFk != Constants.ActivityTypeSummaryActivity;
						bool isTargetTypeChangeable = dbcontext.Entities<HammockActivityEntity>().All(ham => ham.ActivityMemberFk != targetId);
						isOperationAllowed = isNotSummary ? isTargetTypeChangeable && changeTypeToSummary : true;
						scheduleFk = targetActivity.ScheduleFk;
					}
					else
					{
						throw new BusinessLayerException("Cannot move to target as the target is not persitant in database");
					}
				}
				else if (scheduleId.HasValue)
				{
					scheduleFk = scheduleId.Value;
					changeTypeToSummary = false;
					isOperationAllowed = true;
				}
				else
				{
					throw new BusinessLayerException("Schedule is not clear. Operation cannot be excuted. (Pinning?)");
				}
				if (isOperationAllowed)
				{
					//change the type of summary if the targetactivity is not null and the type is Activity or Milestone
					if (changeTypeToSummary)
					{
						targetActivity.ActivityTypeFk = Constants.ActivityTypeSummaryActivity;
						dbcontext.Save(targetActivity);
					}
					if (moveOperation)
					{
						activities = Move(sourceEntities, parentId, dbcontext).ToList();
					}
					else
					{
						var copyTarget = targetActivity == null ? new CopyTarget(scheduleFk, dbcontext) : new CopyTarget(targetActivity);
						activities = Copy(sourceEntities, copyTarget, dbcontext).ToList();
					}
					if (changeTypeToSummary)
					{
						activities.Add(targetActivity);
					}
					//if (targetActivity != null)
					//{
					//	IHierachicalEntityAccess ha = new ActivityRelationInfo();
					//	var children = BuildPartialTree(ha.LoadChildrenTree(targetActivity).Where(e => e.Id != targetActivity.Id).OfType<ActivityEntity>().ToList());
					//	if (children != null)
					//	{
					//		targetActivity.ActivityEntities_ParentActivityFk = children;
					//	}
					//	activities.Add(targetActivity);
					//}

					//var summariesDeletedActivitesFrom = new List<int>();
					////moving sourceactivities to the the target activities
					//foreach (var entity in sourceEntities)
					//{
					//	if (entity.ParentActivityFk.HasValue)
					//		summariesDeletedActivitesFrom.Add(entity.ParentActivityFk.Value); //log the summaries where the sourcesactivites moved away from
					//	entity.ParentActivityFk = parentId;
					//	dbcontext.Save(entity);
					//}
					//summariesDeletedActivitesFrom.Distinct();
					////Changing the type of the logged summary activities to activity, if it doesn't have any more childs
					//foreach (var summaryId in summariesDeletedActivitesFrom)
					//{
					//	if (dbcontext.Entities<ActivityEntity>().Where(activity => activity.ParentActivityFk == summaryId).Count() == 0)
					//	{
					//		var changeSummaryToActivityEntity = dbcontext.Entities<ActivityEntity>().Where(activity => activity.Id == summaryId).FirstOrDefault();
					//		changeSummaryToActivityEntity.ActivityTypeFk = Constants.ActivityTypeActivity;
					//		dbcontext.Save(changeSummaryToActivityEntity);
					//	}
					//}
					ExecuteStoredProcLevelInformation(scheduleFk, dbcontext);
				}
			}
			return activities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="sourceEntities"></param>
		/// <param name="parentId"></param>
		/// <param name="dbcontext"></param>
		public IEnumerable<ActivityEntity> Move(IEnumerable<ActivityEntity> sourceEntities, int? parentId, RVPBC.DbContext dbcontext)
		{
			var summariesDeletedActivitesFrom = new List<int>();
			var activities = new List<ActivityEntity>();
			//moving sourceactivities to the the target activity
			foreach (var entity in sourceEntities)
			{
				if (entity.ParentActivityFk.HasValue)
				{
					summariesDeletedActivitesFrom.Add(entity.ParentActivityFk.Value); //log the summaries where the sourcesactivites moved away from
				}
				entity.ParentActivityFk = parentId;
				dbcontext.Save(entity);
			}
			activities = sourceEntities.ToList();
			var changedActivities = changeEmptySummariesToActivtiy(summariesDeletedActivitesFrom, dbcontext);
			if (changedActivities != null && changedActivities.Any())
			{
				activities = activities.Concat(changedActivities).ToList();
			}
			return activities;
		}

		/// <summary>
		/// Changing the activityTypeFk to activity of each summarie containing summariesToCheck if the summarie is empty
		/// </summary>
		/// <param name="summariesToCheck"></param>
		/// <param name="dbcontext"></param>
		public IEnumerable<ActivityEntity> changeEmptySummariesToActivtiy(IEnumerable<int> summariesToCheck, RVPBC.DbContext dbcontext = null)
		{
			var changedActivities = new List<ActivityEntity>();
			dbcontext = dbcontext == null ? new RVPBC.DbContext(ModelBuilder.DbModel) : dbcontext;
			summariesToCheck.Distinct();
			//Changing the type of the logged summary activities to activity, if it doesn't have any more childs
			foreach (var summaryId in summariesToCheck)
			{
				if (dbcontext.Entities<ActivityEntity>().Where(activity => activity.ParentActivityFk == summaryId).Count() == 0)
				{
					var changeSummaryToActivityEntity = dbcontext.Entities<ActivityEntity>().Where(activity => activity.Id == summaryId).FirstOrDefault();
					changeSummaryToActivityEntity.ActivityTypeFk = Constants.ActivityTypeActivity;
					changedActivities.Add(changeSummaryToActivityEntity);
					dbcontext.Save(changeSummaryToActivityEntity);
				}
			}
			return changedActivities;
		}

		private struct CopyTarget
		{
			public int? parentActivityId { get; set; }
			public int scheduleId { get; set; }
			public int projectId { get; set; }
			public string lastCode { get; set; }
			public bool newHirachy { get; set; }
			/// <summary>
			/// Copy to target as Child of it
			/// </summary>
			/// <param name="targetActivity"></param>
			public CopyTarget(ActivityEntity targetActivity)
				: this()
			{
				this.parentActivityId = targetActivity.Id;
				this.scheduleId = targetActivity.ScheduleFk;
				this.projectId = targetActivity.ProjectFk;
				this.lastCode = !targetActivity.ParentActivityFk.HasValue ? targetActivity.Code : "";
				this.newHirachy = true;
			}
			public CopyTarget(int scheduleId, RVPBC.DbContext dbcontext)
				: this()
			{
				var activity = dbcontext.Entities<ActivityEntity>().Where(act => scheduleId == act.ScheduleFk).OrderByDescending(act => act.Code).FirstOrDefault();
				this.parentActivityId = null;
				this.lastCode = activity.Code;
				this.scheduleId = scheduleId;
				this.projectId = activity.ProjectFk;
				this.newHirachy = true;
			}
		}

		private IEnumerable<ActivityEntity> Copy(IEnumerable<ActivityEntity> sourceEntities, CopyTarget copyTarget, RVPBC.DbContext dbcontext)
		{
			var activities = new List<ActivityEntity>();
			foreach (var activityEntity in sourceEntities)
			{
				var newActivity = CopyActivity(activityEntity, copyTarget);
				activities.Add(newActivity);
				dbcontext.Save(newActivity);
				CopyChildrenIfActivityHasChildren(dbcontext, activityEntity, new CopyTarget(newActivity));
			}
			ExecuteStoredProcLevelInformation(copyTarget.scheduleId, dbcontext);
			return activities;
		}


		private void TakeOverInDictionary(int index, Dictionary<int, int> idMappingDictionary, IEnumerable<IScriptActivity> activities, ActivityComplete actComplete)
		{
			foreach (var child in activities)
			{
				if (!idMappingDictionary.ContainsKey(child.Id))
				{
					idMappingDictionary.Add(child.Id, actComplete.EffectedActivities.ElementAt(index).Id);
				}
				index++;
				if (child.ActivityChildren.Count > 0)
				{
					TakeOverInDictionary(index, idMappingDictionary, child.ActivityChildren, actComplete);
				}
			}
		}

		private void InitRelations(ActivityRelationshipEntity targetEntity, ActivityRelationshipEntity sourceEntity)
		{
			targetEntity.FixLagPercent = sourceEntity.FixLagPercent;
			targetEntity.VarLagPercent = sourceEntity.VarLagPercent;
			targetEntity.UseCalendar = sourceEntity.UseCalendar;
			targetEntity.FixLagTime = sourceEntity.FixLagTime;
			targetEntity.VarLagTime = sourceEntity.VarLagTime;
		}

		private void CheckSummary(ActivityComplete activityComplete, ScheduleDataPool datapool)
		{
			List<ActivityEntity> activities = new List<ActivityEntity>();
			if (activityComplete.EffectedActivities != null && activityComplete.EffectedActivities.Any())
			{
				foreach (var activity in activityComplete.EffectedActivities)
				{
					if (activity.ParentActivityFk.HasValue)
					{
						var tmpAct = datapool.GetActivities(e => e.Id == activity.ParentActivityFk.Value).FirstOrDefault();
						if (tmpAct != null && tmpAct.ActivityTypeFk != Constants.ActivityTypeSummaryActivity)
						{
							tmpAct.ActivityTypeFk = Constants.ActivityTypeSummaryActivity;
						}
					}
				}
			}
		}
		/// <summary>
		/// copy entities
		/// </summary>
		/// <param name="listFrom">entites to copy</param>
		/// <param name="toItem">target id</param>
		/// <param name="scheduleId"></param>
		/// <param name="projectId"></param>
		/// <param name="relType"></param>
		public IEnumerable<ActivityComplete> Copy(List<ActivityEntity> listFrom, int? toItem, int scheduleId, int projectId, int relType)
		{
			Permission.Ensure(_permissionGUID, Permissions.Create);
			//var protocolFile = Path.Combine(Path.GetTempPath(), "CopyProtokoll.txt");

			//Stream myFile = File.Create(protocolFile);

			/* Create a new text writer using the output stream, and add it to */
			/* the trace listeners. */
			//TextWriterTraceListener myTextListener = new
			//	TextWriterTraceListener(myFile);
			//Trace.Listeners.Add(myTextListener);
			//Trace.AutoFlush = true;
			//Trace.WriteLine("Copy starts at " + DateTime.Now);

			//Stopwatch stopWatch = new Stopwatch();
			//stopWatch.Start();

			var completeList = new List<ActivityComplete>();
			var calendarLogic = new Calendar.BusinessComponents.CalendarUtilitiesLogic();
			var scheduleLogic = new ScheduleLogic();
			var relLogic = new ActivityRelationshipLogic();

			var schedule = scheduleLogic.GetScheduleById(scheduleId);
			var sucCalc = new ShiftSuccessorCalculation();
			var result = new List<ActivityComplete>();
			//// Get the elapsed time as a TimeSpan value.
			//TimeSpan ts = stopWatch.Elapsed;
			//// Format and display the TimeSpan value.
			//string elapsedTime;

			var lookupLogic = new RVSLB.SchedulingLookupLogic();
			int defConstraintType = 0;
			// get default constraint type (Alm #138626)
			defConstraintType = lookupLogic.GetDefaultConstraintType();
			if (defConstraintType < 0 || (defConstraintType != Constants.AsSoonAsPossible && defConstraintType != Constants.AsLateAsPossible && defConstraintType != Constants.NoConstraint))
			{
				defConstraintType = Constants.AsSoonAsPossible;
			}
			var flatListForSource = new List<ActivityEntity>();
			foreach (var from in listFrom)
			{
				Flatten(from, flatListForSource);
			}
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				ActivityEntity targetActivity = null;
				var calcLogic = new Calculation(1, scheduleId);
				calcLogic.WithTransientProperties = false;
				var dataPoolCopy = calcLogic.DataPool;

				var id = 0;
				if (toItem.HasValue)
				{
					targetActivity = dbcontext.Entities<ActivityEntity>().FirstOrDefault(activity => activity.Id == toItem);
				}
				if (targetActivity != null || !toItem.HasValue)
				{
					var lastActivities = new List<ActivityEntity>();
					if (targetActivity == null)
					{
						var cal = scheduleLogic.GetCalendarId(scheduleId);
						targetActivity = new ActivityEntity
						{
							ScheduleFk = scheduleId,
							ProjectFk = projectId,
							CalendarFk = cal
						};
						//lastActivities = GetScheduleActivities(scheduleId).ToList();
						lastActivities = dataPoolCopy.Activities.ToList();
					}
					else
					{
						if (targetActivity.ActivityTypeFk != Constants.ActivityTypeSummaryActivity)
						{
							//targetActivity.Id = 0;
							//lastActivities = GetActivity(activity => activity.Id == toItem).ToList();
							lastActivities = dataPoolCopy.GetActivities(activity => activity.Id == toItem).ToList();
						}
						else
						{
							id = targetActivity.Id;
							var acts =
								GetActivitieTree(targetActivity.ScheduleFk, id, 999).OrderBy(e => e.PlannedFinish).LastOrDefault() ??
								targetActivity;
							Flatten(acts, lastActivities);
						}
					}
					var lastActivity = lastActivities.OrderBy(e => e.PlannedFinish).LastOrDefault(e => e.ActivityTypeFk != Constants.ActivityTypeSummaryActivity);
					var relInfo = new ActivityRelationInfo();
					relInfo._identifier = "scheduling.main.activity.drag";
					DateTime? startDate = null;
					DateTime? finishDate = null;

					ScheduleCodeFormatFk = null;
					if (schedule != null)
					{
						if (schedule.TargetStart.HasValue)
						{
							startDate = schedule.TargetStart.Value;
						}
						ScheduleCodeFormatFk = schedule.CodeFormatFk;
					}
					if (lastActivity != null)
					{
						if (lastActivity.PlannedFinish.HasValue &&
							 (relType == 0 ||
							  relType == Constants.FinishStartRelationship))
						{
							startDate = new DateTime(lastActivity.PlannedFinish.Value.Year,
								lastActivity.PlannedFinish.Value.Month, lastActivity.PlannedFinish.Value.Day, 23, 59, 59).AddDays(1);

						}
						else if (relType == Constants.StartStartRelationship)
						{
							startDate = new DateTime(lastActivity.PlannedStart.Year,
								lastActivity.PlannedStart.Month, lastActivity.PlannedStart.Day, 23, 59, 59).AddDays(1);

						} // finish finish, start finish
						else if (relType == Constants.FinishFinishRelationship)
						{
							finishDate = new DateTime(lastActivity.PlannedFinish.Value.Year,
								lastActivity.PlannedFinish.Value.Month, lastActivity.PlannedFinish.Value.Day, 23, 59, 59).AddDays(1);
						}
						else if (relType == Constants.StartFinishRelationship)
						{
							finishDate = new DateTime(lastActivity.PlannedStart.Year,
								lastActivity.PlannedStart.Month, lastActivity.PlannedStart.Day, 23, 59, 59).AddDays(1);

						}
					}
					else
					{
						if (startDate == null)
						{
							startDate = DateTime.Today;
						}
					}
					ActivityEntity previousActivity = null;
					Dictionary<int, ActivityEntity> idMappingDictionary = new Dictionary<int, ActivityEntity>();
					var hasToRescheduled = true;
					var firstMainActivityId = 0;

					var prjCalLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectCalendarLogic>();
					var prjCalendarIds = prjCalLogic.GetProjectCalendarsByProject(projectId).Select(e => e.CalendarFk).ToList();
					var calendarIds = listFrom.Select(e => e.CalendarFk).ToList();
					var calendars = new SchedulingCalendarLogic().GetListByFilter(e => calendarIds.Contains(e.Id)).ToList();
					var actsToSave = new Dictionary<int, ActivityEntity>();
					var relsToSave = new Dictionary<int, ActivityRelationshipEntity>();

					var newActivityIds = new Stack<int>(this.SequenceManager.GetNextList("PSD_ACTIVITY", flatListForSource.Distinct().Count()).Reverse());
					var activityIds = flatListForSource.Distinct().Where(act => act.ActivityTypeFk != Constants.ActivityTypeSummaryActivity).Select(e => e.Id).ToArray();

					var allRelations = relLogic.GetRelationsByTempTable(activityIds).ToList();
					var newRelationIds = new Stack<int>();
					if (allRelations.Distinct().Count() > 0)
					{
						newRelationIds = new Stack<int>(this.SequenceManager.GetNextList("PSD_ACTIVITYRELATIONSHIP", allRelations.Distinct().Count()).Reverse());
					}
					var lastCode = targetActivity.Code;
					var generator = EntityCodeGeneratorFactory.GetGenerator(ScheduleCodeFormatFk);

					foreach (var entity in listFrom)
					{
						if (!idMappingDictionary.ContainsKey(entity.Id))
						{

							var copyResult = CopySourceActivity(entity, targetActivity, idMappingDictionary, newActivityIds, defConstraintType, prjCalendarIds, calendars, allRelations, newRelationIds, dataPoolCopy, lastCode, generator);
							var cpy = copyResult.Item1;
							lastCode = copyResult.Item2;
							//stopWatch.Stop();
							//// Get the elapsed time as a TimeSpan value.
							//ts = stopWatch.Elapsed;
							//// Format and display the TimeSpan value.
							//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
							//	ts.Hours, ts.Minutes, ts.Seconds,
							//	ts.Milliseconds / 10);
							//Trace.WriteLine("After init " + elapsedTime);
							//Trace.Flush();
							//stopWatch.Restart();

							//var cpy = (ActivityComplete)DoCreateDeepCopy(relInfo, entity, targetActivity, ownerMapping);
							//stopWatch.Stop();
							//// Get the elapsed time as a TimeSpan value.
							//ts = stopWatch.Elapsed;
							//// Format and display the TimeSpan value.
							//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
							//  ts.Hours, ts.Minutes, ts.Seconds,
							//  ts.Milliseconds / 10);
							//Trace.WriteLine("After copy " + elapsedTime);
							//Trace.Flush();
							//stopWatch.Restart();

							var firstCopiedActivity = cpy.Activity;

							if (entity.ActivityTypeFk == Constants.ActivityTypeSummaryActivity && cpy.EffectedActivities != null && cpy.EffectedActivities.Any())
							{
								firstCopiedActivity = cpy.EffectedActivities.OrderBy(e => e.PlannedStart).FirstOrDefault(e => e.ActivityTypeFk != Constants.ActivityTypeSummaryActivity);

								if (firstCopiedActivity == null)
								{
									firstCopiedActivity = cpy.Activity;
								}
							}
							if (firstMainActivityId == 0)
							{
								firstMainActivityId = firstCopiedActivity.Id;
							}

							if (startDate.HasValue)
							{
								NonWorkingDayCheck nonWorkingDays = dataPoolCopy.GetNonWorkingDays(entity);
								startDate = nonWorkingDays.GetNextWorkingDay(startDate.Value);
							}

							var planningChange = new CalculationActivityEntity();
							planningChange.Id = cpy.Activity.Id;
							planningChange.WithOutShiftSuccessor = true;
							if (cpy.MainItemId == 0)
							{
								cpy.MainItemId = planningChange.Id;
							}
							if (finishDate != null)
							{
								planningChange.EndDate = finishDate;
								if (cpy.Activity.ActivityTypeFk == Constants.ActivityTypeSummaryActivity)
								{
									planningChange.Duration = firstCopiedActivity.PlannedDuration;
								}
								else
								{
									planningChange.Duration = cpy.Activity.PlannedDuration;
								}
							}
							else
							{
								planningChange.StartDate = startDate;
							}
							cpy.ActivityPlanningChange = planningChange;
							//stopWatch.Stop();
							//// Get the elapsed time as a TimeSpan value.
							//ts = stopWatch.Elapsed;
							//// Format and display the TimeSpan value.
							//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
							//	ts.Hours, ts.Minutes, ts.Seconds,
							//	ts.Milliseconds / 10);
							//Trace.WriteLine("Before update" + elapsedTime);
							//Trace.Flush();
							//stopWatch.Restart();
							dataPoolCopy.Activities = new List<ActivityEntity>() { cpy.Activity };
							if (cpy.EffectedActivities != null)
							{
								dataPoolCopy.Activities = cpy.EffectedActivities;
							}
							calcLogic.ValidateComplete(cpy, dataPoolCopy);
							CheckSummary(cpy, dataPoolCopy);
							UpdateCompleteWithBulk(cpy);

							//stopWatch.Stop();
							//// Get the elapsed time as a TimeSpan value.
							//ts = stopWatch.Elapsed;
							//// Format and display the TimeSpan value.
							//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
							//	ts.Hours, ts.Minutes, ts.Seconds,
							//	ts.Milliseconds / 10);
							//Trace.WriteLine("After update" + elapsedTime);
							//Trace.Flush();
							//stopWatch.Restart();

							if (relType == 0)
							{
								if (cpy.EffectedActivities != null && cpy.EffectedActivities.Any())
								{
									List<ActivityEntity> tmpEffected = new List<ActivityEntity>();
									foreach (var eff in cpy.EffectedActivities)
									{
										var relExist = dataPoolCopy.GetRelationshipsLoadedRelatedToActivity(eff.Id).Where(e => e.ChildActivityFk == eff.Id).ToArray();
										if (eff.ActivityTypeFk != Constants.ActivityTypeSummaryActivity && !tmpEffected.Contains(eff) && (relExist == null || relExist.Length <= 0))
										{
											actsToSave[eff.Id] = eff;
											eff.ConstraintDate = null;
											eff.ConstraintTypeFk = defConstraintType;

											planningChange.Id = eff.Id;
											var effCpy = new ActivityComplete
											{
												Activity = eff,
												MainItemId = eff.Id,
												ActivityPlanningChange = planningChange
											};
											calcLogic.ValidateComplete(effCpy, dataPoolCopy);
											CheckSummary(effCpy, dataPoolCopy);

											dataPoolCopy.Activities = new List<ActivityEntity>() { effCpy.Activity };
											if (effCpy.RelationshipsToSave != null)
											{
												dataPoolCopy.Relationships = effCpy.RelationshipsToSave;
												effCpy.RelationshipsToSave.Select(e => relsToSave[e.Id] = e);
											}

											//stopWatch.Stop();
											//// Get the elapsed time as a TimeSpan value.
											//ts = stopWatch.Elapsed;
											//// Format and display the TimeSpan value.
											//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
											//	ts.Hours, ts.Minutes, ts.Seconds,
											//	ts.Milliseconds / 10);
											//Trace.WriteLine("After 2. update " + elapsedTime);
											//Trace.Flush();
											//stopWatch.Restart();

											if (effCpy.EffectedActivities != null && effCpy.EffectedActivities.Any())
											{
												dataPoolCopy.Activities = effCpy.EffectedActivities;
												tmpEffected.AddRange(effCpy.EffectedActivities);
												effCpy.EffectedActivities.Select(e => actsToSave[e.Id] = e);
											}
										}
									}
								}
								var actIds = entity.ActivityChildren.Select(e => e.Id).ToArray();
								var rel = allRelations.Where(rel => actIds.Contains(rel.ChildActivityFk) || actIds.Contains(rel.ParentActivityFk)).Distinct();

								// find relationships in rel by ownerMapping
								foreach (var oldSummeryActivityId in idMappingDictionary.Keys)
								{
									var oldSummeryActivity = flatListForSource.FirstOrDefault(e => e.Id == oldSummeryActivityId);
									var listOfOldIds = ((ActivityEntity)oldSummeryActivity).ActivityChildren.Select(e => e.Id);
									foreach (var oldAct in ((ActivityEntity)oldSummeryActivity).ActivityChildren)
									{
										var newRel = rel.Where(e => e.ParentActivityFk == oldAct.Id && !listOfOldIds.Contains(e.ChildActivityFk));
										if (newRel != null)
										{
											foreach (var activityRelationshipEntity in newRel)
											{
												if (idMappingDictionary.ContainsKey(activityRelationshipEntity.ChildActivityFk))
												{
													var parentId = idMappingDictionary[oldAct.Id].Id;
													var childId = idMappingDictionary[activityRelationshipEntity.ChildActivityFk].Id;

													// first check if we find a relationship of the same type
													var relation = dataPoolCopy.GetRelationships(r => r.ParentActivityFk == parentId && r.ChildActivityFk == childId && r.RelationKindFk == activityRelationshipEntity.RelationKindFk).FirstOrDefault();
													if (relation == null)
													{
														var toBeSavedRel = relLogic.CreateForDragAndDrop(idMappingDictionary[oldAct.Id].Id, cpy.Activity.ScheduleFk, schedule.UseCalendarForLagtime,
																activityRelationshipEntity.RelationKindFk.Value, newRelationIds, idMappingDictionary[activityRelationshipEntity.ChildActivityFk].Id,
																idMappingDictionary[activityRelationshipEntity.ChildActivityFk].ScheduleFk);

														if (toBeSavedRel != null)
														{
															InitRelations(toBeSavedRel, activityRelationshipEntity);

															if (cpy.RelationshipsToSave == null)
															{
																cpy.RelationshipsToSave = new List<ActivityRelationshipEntity>();
															}
															cpy.RelationshipsToSave.Add(toBeSavedRel);
															relsToSave[toBeSavedRel.Id] = toBeSavedRel;
															dataPoolCopy.Relationships = cpy.RelationshipsToSave;

														}
														hasToRescheduled = true;
													}
												}
											}
										}
									}
								}
								//stopWatch.Stop();
								//// Get the elapsed time as a TimeSpan value.
								//ts = stopWatch.Elapsed;
								//// Format and display the TimeSpan value.
								//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
								//	ts.Hours, ts.Minutes, ts.Seconds,
								//	ts.Milliseconds / 10);
								//Trace.WriteLine("After relations " + elapsedTime);
								//Trace.Flush();
								//stopWatch.Restart();
							}
							else
							{
								if (lastActivity != null && lastActivity.Id > 0)
								{

									if (cpy.EffectedActivities != null)
									{
										var act = cpy.EffectedActivities.FirstOrDefault(e => e.Id == firstCopiedActivity.Id);
										if (act != null)
										{
											planningChange.Id = act.Id;
											var effCpy = new ActivityComplete
											{
												Activity = act,
												MainItemId = act.Id,
												ActivityPlanningChange = planningChange
											};
											calcLogic.ValidateComplete(effCpy, dataPoolCopy);
											CheckSummary(effCpy, dataPoolCopy);
											actsToSave[act.Id] = act;
											dataPoolCopy.Activities = new List<ActivityEntity>() { effCpy.Activity };
											if (effCpy.EffectedActivities != null)
											{
												dataPoolCopy.Activities = effCpy.EffectedActivities;
												effCpy.EffectedActivities.Select(e => actsToSave[e.Id] = e);
											}
											if (effCpy.RelationshipsToSave != null)
											{
												dataPoolCopy.Relationships = effCpy.RelationshipsToSave;
												effCpy.RelationshipsToSave.Select(e => relsToSave[e.Id] = e);
											}

											//stopWatch.Stop();
											//// Get the elapsed time as a TimeSpan value.
											//ts = stopWatch.Elapsed;
											//// Format and display the TimeSpan value.
											//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
											//	ts.Hours, ts.Minutes, ts.Seconds,
											//	ts.Milliseconds / 10);
											//Trace.WriteLine("After 3. update " + elapsedTime);
											//Trace.Flush();
											//stopWatch.Restart();
										}
									}
									cpy.ActivityPlanningChange = null;

									if (previousActivity == null)
									{
										if (lastActivity.Id != targetActivity.Id)
										{
											var relation = relLogic.Create(lastActivity.Id, firstCopiedActivity.Id, relType, scheduleId, dataPoolCopy);

											if (cpy.RelationshipsToSave == null)
											{
												cpy.RelationshipsToSave = new List<ActivityRelationshipEntity>();
											}
											cpy.RelationshipsToSave.AddRange(relation.RelationshipsToSave);
											dataPoolCopy.Relationships = cpy.RelationshipsToSave;

											if (relation.EffectedActivities == null)
											{
												if (cpy.EffectedActivities != null)
												{
													cpy.EffectedActivities = new List<ActivityEntity>();
												}
												cpy.EffectedActivities.AddRange(relation.EffectedActivities);
											}
										}
									}

									dataPoolCopy.Activities = new List<ActivityEntity>() { cpy.Activity };
									if (cpy.EffectedActivities != null)
									{
										dataPoolCopy.Activities = cpy.EffectedActivities;
										cpy.EffectedActivities.Select(e => actsToSave[e.Id] = e);

									}
									if (cpy.RelationshipsToSave != null)
									{
										dataPoolCopy.Relationships = cpy.RelationshipsToSave;
										cpy.RelationshipsToSave.Select(e => relsToSave[e.Id] = e);
									}

									//stopWatch.Stop();
									//// Get the elapsed time as a TimeSpan value.
									//ts = stopWatch.Elapsed;
									//// Format and display the TimeSpan value.
									//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
									//	ts.Hours, ts.Minutes, ts.Seconds,
									//	ts.Milliseconds / 10);
									//Trace.WriteLine("After update with relation " + elapsedTime);
									//Trace.Flush();
									//stopWatch.Restart();

								}
								if (previousActivity != null && previousActivity.Id != cpy.MainItemId)
								{

									var actIds = entity.ActivityChildren.Select(e => e.Id).ToArray();
									var rel = allRelations.Where(rel => actIds.Contains(rel.ChildActivityFk) || actIds.Contains(rel.ParentActivityFk)).Distinct();
									// find relationships in rel by ownerMapping
									foreach (var oldSummeryActivityId in idMappingDictionary.Keys)
									{
										var oldSummeryActivity = flatListForSource.FirstOrDefault(act => act.Id == oldSummeryActivityId);
										var listOfOldIds = ((ActivityEntity)oldSummeryActivity).ActivityChildren.Select(e => e.Id);
										foreach (var oldAct in ((ActivityEntity)oldSummeryActivity).ActivityChildren)
										{

											var newRel = rel.Where(e => e.ParentActivityFk == oldAct.Id && !listOfOldIds.Contains(e.ChildActivityFk));
											if (newRel != null)
											{
												foreach (var activityRelationshipEntity in newRel)
												{
													if (idMappingDictionary.ContainsKey(activityRelationshipEntity.ChildActivityFk))
													{
														var parentId = idMappingDictionary[oldAct.Id].Id;
														var childId = idMappingDictionary[activityRelationshipEntity.ChildActivityFk].Id;

														// first check if we find a relationship of the same type
														var relation = dataPoolCopy.GetRelationships(r => r.ParentActivityFk == parentId && r.ChildActivityFk == childId && r.RelationKindFk == activityRelationshipEntity.RelationKindFk).FirstOrDefault();
														if (relation == null)
														{
															var toBeSavedRel = relLogic.CreateForDragAndDrop(idMappingDictionary[oldAct.Id].Id, cpy.Activity.ScheduleFk, schedule.UseCalendarForLagtime,
																	activityRelationshipEntity.RelationKindFk.Value, newRelationIds, idMappingDictionary[activityRelationshipEntity.ChildActivityFk].Id,
																	idMappingDictionary[activityRelationshipEntity.ChildActivityFk].ScheduleFk);
															if (toBeSavedRel != null)
															{
																InitRelations(toBeSavedRel, activityRelationshipEntity);
																relsToSave[toBeSavedRel.Id] = toBeSavedRel;
																if (cpy.RelationshipsToSave == null)
																{
																	cpy.RelationshipsToSave = new List<ActivityRelationshipEntity>();
																}
																cpy.RelationshipsToSave.Add(toBeSavedRel);
																dataPoolCopy.Relationships = cpy.RelationshipsToSave;
															}

															hasToRescheduled = true;
														}
													}
												}
											}
										}
									}
									//stopWatch.Stop();
									//// Get the elapsed time as a TimeSpan value.
									//ts = stopWatch.Elapsed;
									//// Format and display the TimeSpan value.
									//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
									//	ts.Hours, ts.Minutes, ts.Seconds,
									//	ts.Milliseconds / 10);
									//Trace.WriteLine("After relations 2 " + elapsedTime);
									//Trace.Flush();
									//stopWatch.Restart();

								}
							}
							previousActivity = cpy.Activity;
							result.Add(cpy);
						}
					}
					var listOfOldActIds = idMappingDictionary.Keys.ToArray();
					var relations = allRelations.Where(rel => listOfOldActIds.Contains(rel.ChildActivityFk) || listOfOldActIds.Contains(rel.ParentActivityFk)).Distinct();
					ActivityEntity actNew;
					foreach (var oldActivityId in idMappingDictionary.Keys)
					{
						var oldActivity = flatListForSource.FirstOrDefault(act => act.Id == oldActivityId);
						var newRel = relations.Where(e => e.ParentActivityFk == oldActivity.Id && listOfOldActIds.Contains(e.ChildActivityFk));
						if (newRel != null)
						{
							foreach (var activityRelationshipEntity in newRel)
							{
								if (idMappingDictionary.ContainsKey(activityRelationshipEntity.ChildActivityFk))
								{
									var parentId = idMappingDictionary[oldActivity.Id].Id;
									var childId = idMappingDictionary[activityRelationshipEntity.ChildActivityFk].Id;
									var existRel = dataPoolCopy.GetRelationships(e => e.ParentActivityFk == parentId && e.ChildActivityFk == childId);
									if (existRel == null || !existRel.Any())
									{
										actNew = idMappingDictionary[oldActivityId];
										if (actNew != null)
										{

											var toBeSavedRel = relLogic.CreateForDragAndDrop(idMappingDictionary[oldActivity.Id].Id, actNew.ScheduleFk, schedule.UseCalendarForLagtime,
													activityRelationshipEntity.RelationKindFk.Value, newRelationIds, idMappingDictionary[activityRelationshipEntity.ChildActivityFk].Id,
													idMappingDictionary[activityRelationshipEntity.ChildActivityFk].ScheduleFk);

											if (toBeSavedRel != null)
											{
												InitRelations(toBeSavedRel, activityRelationshipEntity);
												relsToSave[toBeSavedRel.Id] = toBeSavedRel;
												dataPoolCopy.Relationships = new List<ActivityRelationshipEntity>() { toBeSavedRel };
												if (result != null && result.Any())
												{
													if (result[0].RelationshipsToSave == null)
													{
														result[0].RelationshipsToSave = new List<ActivityRelationshipEntity>();
													}
													result[0].RelationshipsToSave.Add(toBeSavedRel);
												}
											}
											hasToRescheduled = true;
										}
									}
								}
							}
						}
					}

					// copy characteristics
					var sectionIds = new List<KeyValuePair<int, int>>();
					sectionIds.Add(new KeyValuePair<int, int>(Constants.CharacteristicSection, Constants.CharacteristicSection));
					var sourceDestObjectIds = idMappingDictionary.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Id);
					new CharacteristicDataLogic().CopyBulkInGroups(Constants.CharacteristicSection, new ConcurrentDictionary<int, int>(sourceDestObjectIds));

					//stopWatch.Stop();
					//// Get the elapsed time as a TimeSpan value.
					//ts = stopWatch.Elapsed;
					//// Format and display the TimeSpan value.
					//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
					//	ts.Hours, ts.Minutes, ts.Seconds,
					//	ts.Milliseconds / 10);
					//Trace.WriteLine("After end relatins " + elapsedTime);
					//Trace.Flush();
					//stopWatch.Restart();

					if (toItem.HasValue && targetActivity.Id == toItem.Value && targetActivity.ActivityTypeFk != Constants.ActivityTypeSummaryActivity && targetActivity.ActivityTypeFk != Constants.ActivityTypeHammock)
					{
						//var rels = relLogic.GetCoresByChildOrParentActivityIds(new List<int>() { toItem.Value });
						var rels = allRelations.Where(rel => toItem.Value == rel.ChildActivityFk || toItem.Value == rel.ParentActivityFk).Distinct();

						if (rels != null && rels.Any())
						{
							var childRels = rels.Where(e => e.ChildActivityFk == toItem.Value).ToArray();
							foreach (var rel in childRels)
							{
								rel.ChildActivityFk = firstMainActivityId;
							}
							var parentRels = rels.Where(e => e.ParentActivityFk == toItem.Value).ToArray();
							foreach (var rel in parentRels)
							{
								rel.ParentActivityFk = firstMainActivityId;
							}
							rels.Select(e => relsToSave[e.Id] = e);
							dataPoolCopy.Relationships = rels;
							if (result != null && result.Any())
							{
								if (result[0].RelationshipsToSave == null)
								{
									result[0].RelationshipsToSave = new List<ActivityRelationshipEntity>();
								}
								result[0].RelationshipsToSave.AddRange(rels);
							}
							hasToRescheduled = true;
						}
					}
					//stopWatch.Stop();
					//// Get the elapsed time as a TimeSpan value.
					//ts = stopWatch.Elapsed;
					//// Format and display the TimeSpan value.
					//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
					//	ts.Hours, ts.Minutes, ts.Seconds,
					//	ts.Milliseconds / 10);
					//Trace.WriteLine("before reschedule " + elapsedTime);
					//Trace.Flush();
					//stopWatch.Restart();
					//Trace.WriteLine("in 3372 hastoschedulde " + hasToRescheduled);
					UpdateWithBulk(actsToSave.Values, relsToSave.Values);
					if (hasToRescheduled)
					{
						UpdateCompleteWithBulk(sucCalc.RescheduleAllActivities(scheduleId, dataPoolCopy));
					}
					relInfo._identifier = "scheduling.main.activity";
					//stopWatch.Stop();
					//// Get the elapsed time as a TimeSpan value.
					//ts = stopWatch.Elapsed;
					//// Format and display the TimeSpan value.
					//elapsedTime = String.Format("{0:00}:{1:00}:{2:00}.{3:00}",
					//	ts.Hours, ts.Minutes, ts.Seconds,
					//	ts.Milliseconds / 10);
					//Trace.WriteLine("Copy End " + elapsedTime);
					//Trace.WriteLine("Copy End at " + DateTime.Now);
					//Trace.Close();
					//myFile.Close();
				}
				var activities = new List<ActivityEntity>();
				foreach (var item in result)
				{
					if (item.Activity != null)
					{
						activities.Add(item.Activity);
					}
					if (item.EffectedActivities != null && item.EffectedActivities.Any())
					{
						activities.AddRange(item.EffectedActivities);
					}
				}
				DoIncludeTransientProperties(activities, dataPoolCopy);
			}
			return result;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="relInfo"></param>
		/// <param name="entity"></param>
		/// <param name="targetActivity"></param>
		/// <param name="ownerMapping"></param>
		/// <returns></returns>
		public IIdentifyable DoCreateDeepCopy(IEntityRelationInfo relInfo, IIdentifyable entity, IIdentifyable targetActivity, IDictionary<ActivityEntity, ActivityEntity> ownerMapping)
		{
			var dc = new DeepCopier();
			var newDeepCopy = dc.CreateDeepCopy(relInfo, entity, targetActivity);
			var copy = newDeepCopy as ActivityComplete;
			if (copy != null)
			{
				if (copy.Activity != null)
				{
					ownerMapping.Add((ActivityEntity)entity, copy.Activity);
					if (copy.EffectedActivities != null && copy.EffectedActivities.Any())
					{
						foreach (var eff in copy.EffectedActivities)
						{
							ownerMapping[(ActivityEntity)eff] = eff;
						}
					}
				}
			}
			return newDeepCopy;
		}

		private void Flatten(ActivityEntity act, List<ActivityEntity> flatList)
		{
			flatList.Add(act);
			if (act.ActivityEntities_ParentActivityFk.Any())
			{
				foreach (var child in act.ActivityEntities_ParentActivityFk)
				{
					Flatten(child, flatList);
				}
			}
		}

		/// <summary>
		/// copy entities
		/// </summary>
		/// <param name="listFrom">entites to copy</param>
		/// <param name="toItem">target id</param>
		/// <param name="scheduleId"></param>
		/// <param name="projectId"></param>
		/// <param name="relType"></param>
		public IEnumerable<ActivityComplete> CopyFromTemplates(IEnumerable<int> listFrom, int? toItem, int scheduleId, int projectId, int relType)
		{
			Permission.Ensure(_permissionGUID, Permissions.Create);

			var completeList = new List<ActivityComplete>();
			var calendarLogic = new Calendar.BusinessComponents.CalendarUtilitiesLogic();
			var relLogic = new ActivityRelationshipLogic();
			var scheduleLogic = new ScheduleLogic();
			//var schedule = scheduleLogic.GetCoresAsListByFilter(e => e.Id == scheduleId).FirstOrDefault();
			var calcLogic = new Calculation(1, scheduleId);
			calcLogic.WithTransientProperties = false;
			var dataPoolCopy = calcLogic.DataPool;
			var schedule = dataPoolCopy.Schedules.FirstOrDefault(schedule => schedule.Id == scheduleId);

			var newHierarchy = false;
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				ActivityEntity targetActivity = null;

				if (toItem.HasValue)
				{
					//targetActivity = dbcontext.Entities<ActivityEntity>().FirstOrDefault(activity => activity.Id == toItem);
					targetActivity = dataPoolCopy.GetActivityById(toItem.Value);
				}
				if (targetActivity != null || !toItem.HasValue)
				{
					var parentId = 0;
					var lastActivities = new List<ActivityEntity>();
					var lastCode = string.Empty;
					if (targetActivity == null)
					{

						targetActivity = new ActivityEntity
						{
							ScheduleFk = scheduleId,
							ProjectFk = projectId
						};
						lastActivities = dataPoolCopy.GetActivities(e => e.ScheduleFk == scheduleId).ToList();
						var last = lastActivities.Where(a => !a.ParentActivityFk.HasValue).OrderBy(e => e.PlannedFinish).LastOrDefault(e => e.ActivityTypeFk == Constants.ActivityTypeSummaryActivity);
						lastCode = last?.Code;
					}
					else
					{
						if (targetActivity.ActivityTypeFk == Constants.ActivityTypeSummaryActivity)
						{
							parentId = targetActivity.Id;
							var act = dataPoolCopy.GetActivities(e => e.ScheduleFk == scheduleId && e.ParentActivityFk == parentId)
								.OrderBy(e => e.PlannedFinish).LastOrDefault() ?? targetActivity;
							lastCode = act?.Code;
							Flatten(act, lastActivities);
						}
						else
						{
							lastActivities = dataPoolCopy.GetActivities(e => e.Id == toItem.Value).ToList();
							lastCode = targetActivity?.Code;
							newHierarchy = true;
						}
					}
					var lastActivity = lastActivities.OrderBy(e => e.PlannedFinish).LastOrDefault(e => e.ActivityTypeFk != Constants.ActivityTypeSummaryActivity);
					DateTime startDate;
					startDate = DateTime.Today;
					ScheduleCodeFormatFk = null;
					if (schedule != null)
					{
						if (schedule.TargetStart.HasValue)
						{
							startDate = schedule.TargetStart.Value;
						}
						ScheduleCodeFormatFk = schedule.CodeFormatFk;
					}

					DateTime? finishDate = null;
					if (lastActivity != null)
					{
						if (lastActivity.PlannedFinish.HasValue &&
							 (relType == 0 || relType == Constants.FinishFinishRelationship || relType == Constants.FinishStartRelationship))
						{
							startDate = new DateTime(lastActivity.PlannedFinish.Value.Year,
								lastActivity.PlannedFinish.Value.Month, lastActivity.PlannedFinish.Value.Day, 23, 59, 59).AddDays(1);
							if (relType == Constants.FinishFinishRelationship)
							{
								finishDate = startDate.AddDays(-1);
							}
						}
						else if (relType == Constants.StartFinishRelationship || relType == Constants.StartStartRelationship)
						{
							startDate = new DateTime(lastActivity.PlannedStart.Year,
								lastActivity.PlannedStart.Month, lastActivity.PlannedStart.Day, 23, 59, 59).AddDays(1);
							if (relType == Constants.StartFinishRelationship)
							{
								finishDate = startDate.AddDays(-1);
							}
						}
					}

					var activityIds = new Stack<int>(this.SequenceManager.GetNextList("PSD_ACTIVITY", listFrom.Count()).Reverse());

					var generator = EntityCodeGeneratorFactory.GetGenerator(ScheduleCodeFormatFk);

					var today = DateTime.Today;
					if (schedule != null && schedule.InitWithTargetStart)
					{
						if (schedule.TargetStart.HasValue)
						{
							today = schedule.TargetStart.Value;
						}
					}

					foreach (var templId in listFrom)
					{
						var activityComplete = new ActivityComplete();
						var activity = new ActivityEntity();
						// init default values here
						activity.CompanyFk = schedule.CompanyFk;
						activity.ScheduleFk = scheduleId;
						activity.ProjectFk = projectId;
						activity.CalendarFk = schedule.CalendarFk ?? 1;
						if (parentId > 0)
						{
							activity.ParentActivityFk = parentId;
						}

						DoInitActivityWithDefaults(activity, schedule);
						activity.Id = activityIds.Pop();
						activity.Code = SetNewCodeForSource(scheduleId, parentId, lastCode, newHierarchy, dataPoolCopy, generator);

						activityComplete.Activity = activity;

						var tmplLogic = new ActitvityTemplateCalculation(templId);
						var planningChange = new CalculationActivityEntity();
						planningChange.CalculationNeeded = true;
						startDate = calendarLogic.GetNextWorkingDay(activityComplete.Activity.CalendarFk,
							new DateTime(startDate.Year, startDate.Month, startDate.Day, 0, 0, 0));
						var tmpActivityComplete = tmplLogic.Validate(activityComplete);
						// assign parent
						SetNewOwner(tmpActivityComplete.Activity, targetActivity);

						if (tmpActivityComplete.MainItemId == 0)
						{
							tmpActivityComplete.MainItemId = activityComplete.Activity.Id;
						}

						planningChange.Id = activityComplete.Activity.Id;
						if (finishDate != null)
						{
							planningChange.EndDate = finishDate;
							planningChange.Duration = activityComplete.Activity.PlannedDuration;
						}
						else
						{
							planningChange.StartDate = startDate;
						}
						tmpActivityComplete.ActivityPlanningChange = planningChange;
						tmpActivityComplete = calcLogic.ValidateComplete(tmpActivityComplete, dataPoolCopy);
						//Update(tmpActivityComplete);
						dataPoolCopy.Activities = new List<ActivityEntity>() { tmpActivityComplete.Activity };
						if (tmpActivityComplete.EffectedActivities != null)
						{
							dataPoolCopy.Activities = tmpActivityComplete.EffectedActivities;

						}
						if (tmpActivityComplete.RelationshipsToSave != null)
						{
							dataPoolCopy.Relationships = tmpActivityComplete.RelationshipsToSave;
						}
						if (tmpActivityComplete.EventsToSave != null)
						{
							dataPoolCopy.Events = tmpActivityComplete.EventsToSave;
						}


						if (relType != 0)
						{
							if (lastActivity != null && lastActivity.Id > 0)
							{
								tmpActivityComplete.ActivityPlanningChange = null;
								var relation = relLogic.Create(lastActivity.Id, tmpActivityComplete.Activity.Id, relType, scheduleId, dataPoolCopy);

								if (tmpActivityComplete.RelationshipsToSave == null)
								{
									tmpActivityComplete.RelationshipsToSave = new List<ActivityRelationshipEntity>();
								}
								tmpActivityComplete.RelationshipsToSave.AddRange(relation.RelationshipsToSave);
								dataPoolCopy.Relationships = tmpActivityComplete.RelationshipsToSave;

								if (relation.EffectedActivities == null)
								{
									if (tmpActivityComplete.EffectedActivities != null)
									{
										tmpActivityComplete.EffectedActivities = new List<ActivityEntity>();
									}
									tmpActivityComplete.EffectedActivities.AddRange(relation.EffectedActivities);
								}

								dataPoolCopy.Activities = new List<ActivityEntity>() { tmpActivityComplete.Activity };
								if (tmpActivityComplete.EffectedActivities != null)
								{
									dataPoolCopy.Activities = tmpActivityComplete.EffectedActivities;

								}
								if (tmpActivityComplete.RelationshipsToSave != null)
								{
									dataPoolCopy.Relationships = tmpActivityComplete.RelationshipsToSave;
								}
								if (tmpActivityComplete.EventsToSave != null)
								{
									dataPoolCopy.Events = tmpActivityComplete.EventsToSave;
								}   
							}
						}

						var complete = new ActivityComplete();
						complete.TakeOverSavedFrom(tmpActivityComplete);
						completeList.Add(complete);
						UpdateActivityCompleteWithBulkSaveHelper(complete, dbcontext);
					}
				}
			}

			return completeList;
		}

		private void CopyChildrenIfActivityHasChildren(RVPBC.DbContext dbcontext, ActivityEntity activityEntity, CopyTarget copyTarget)
		{
			var entities = dbcontext.Entities<ActivityEntity>().Where(activity => activity.ParentActivityFk == activityEntity.Id).ToList();
			if (entities.Any())
			{
				foreach (var entity in entities)
				{
					var newActivity = CopyActivity(entity, copyTarget);
					dbcontext.Save(newActivity);
					var newCopyTarget = new CopyTarget(newActivity);
					CopyChildrenIfActivityHasChildren(dbcontext, entity, newCopyTarget);
				}
			}
		}

		private ActivityEntity CopyActivity(ActivityEntity activityEntity, CopyTarget copyTarget)
		{
			var newActivity = CreateActivity(copyTarget.scheduleId, copyTarget.projectId, copyTarget.parentActivityId, copyTarget.lastCode, copyTarget.newHirachy);

			#region copy properties
			//newActivity.ProjectFk = activityEntity.ProjectFk;
			newActivity.ActivityTypeFk = activityEntity.ActivityTypeFk;
			newActivity.BaselineFk = activityEntity.BaselineFk;
			//newActivity.Code = activityEntity.Code;
			newActivity.Description = activityEntity.Description;
			if (copyTarget.parentActivityId > 0)
			{
				newActivity.ParentActivityFk = copyTarget.parentActivityId;
			}
			//newActivity.ScheduleFk = activityEntity.ScheduleFk;
			newActivity.CompanyFk = activityEntity.CompanyFk;
			newActivity.BaseActivityFk = activityEntity.BaseActivityFk;
			newActivity.ActivityTemplateFk = activityEntity.ActivityTemplateFk;
			newActivity.ControllingUnitFk = activityEntity.ControllingUnitFk;
			newActivity.CalendarFk = activityEntity.CalendarFk;
			newActivity.AllowModify = activityEntity.AllowModify;
			newActivity.Specification = activityEntity.Specification;
			newActivity.ActivityStateFk = activityEntity.ActivityStateFk;
			newActivity.SchedulingMethodFk = activityEntity.SchedulingMethodFk;
			newActivity.SubScheduleFk = activityEntity.SubScheduleFk;
			newActivity.LocationFk = activityEntity.LocationFk;
			newActivity.LocationSpecification = activityEntity.LocationSpecification;
			newActivity.ActivityPresentationFk = activityEntity.ActivityPresentationFk;
			newActivity.ChartPresentationFk = activityEntity.ChartPresentationFk;
			newActivity.PlannedStart = activityEntity.PlannedStart;
			newActivity.PlannedFinish = activityEntity.PlannedFinish;
			newActivity.ConstraintTypeFk = activityEntity.ConstraintTypeFk;
			newActivity.ConstraintDate = activityEntity.ConstraintDate;
			newActivity.ActualStart = activityEntity.ActualStart;
			newActivity.ActualFinish = activityEntity.ActualFinish;
			newActivity.ExecutionStarted = activityEntity.ExecutionStarted;
			newActivity.ExecutionFinished = activityEntity.ExecutionFinished;
			newActivity.PlannedDuration = activityEntity.PlannedDuration;
			newActivity.EarliestStart = activityEntity.EarliestStart;
			newActivity.LatestStart = activityEntity.LatestStart;
			newActivity.EarliestFinish = activityEntity.EarliestFinish;
			newActivity.LatestFinish = activityEntity.LatestFinish;
			newActivity.TotalFloatTime = activityEntity.TotalFloatTime;
			newActivity.FreeFloatTime = activityEntity.FreeFloatTime;
			newActivity.ResourceFactor = activityEntity.ResourceFactor;
			newActivity.PerformanceFactor = activityEntity.PerformanceFactor;
			newActivity.PerformanceRuleFk = activityEntity.PerformanceRuleFk;
			newActivity.Perf1UoMFk = activityEntity.Perf1UoMFk;
			newActivity.Perf2UoMFk = activityEntity.Perf2UoMFk;
			newActivity.TaskTypeFk = activityEntity.TaskTypeFk;
			newActivity.Work = activityEntity.Work;
			newActivity.QuantityUoMFk = activityEntity.QuantityUoMFk;
			newActivity.Quantity = activityEntity.Quantity;
			newActivity.ProgressReportMethodFk = activityEntity.ProgressReportMethodFk;
			newActivity.Bas3dVisualizationTypeFk = activityEntity.Bas3dVisualizationTypeFk;
			newActivity.SCurveFk = activityEntity.SCurveFk;
			newActivity.EventTypeFk = activityEntity.EventTypeFk;
			newActivity.IsCritical = activityEntity.IsCritical;
			newActivity.IsLive = activityEntity.IsLive;
			newActivity.UserDefinedText01 = activityEntity.UserDefinedText01;
			newActivity.UserDefinedText02 = activityEntity.UserDefinedText02;
			newActivity.UserDefinedText03 = activityEntity.UserDefinedText03;
			newActivity.UserDefinedText04 = activityEntity.UserDefinedText04;
			newActivity.UserDefinedText05 = activityEntity.UserDefinedText05;
			newActivity.UserDefinedText06 = activityEntity.UserDefinedText06;
			newActivity.UserDefinedText07 = activityEntity.UserDefinedText07;
			newActivity.UserDefinedText08 = activityEntity.UserDefinedText08;
			newActivity.UserDefinedText09 = activityEntity.UserDefinedText09;
			newActivity.UserDefinedText10 = activityEntity.UserDefinedText10;
			newActivity.UserDefinedNumber01 = activityEntity.UserDefinedNumber01;
			newActivity.UserDefinedNumber02 = activityEntity.UserDefinedNumber02;
			newActivity.UserDefinedNumber03 = activityEntity.UserDefinedNumber03;
			newActivity.UserDefinedNumber04 = activityEntity.UserDefinedNumber04;
			newActivity.UserDefinedNumber05 = activityEntity.UserDefinedNumber05;
			newActivity.UserDefinedNumber06 = activityEntity.UserDefinedNumber06;
			newActivity.UserDefinedNumber07 = activityEntity.UserDefinedNumber07;
			newActivity.UserDefinedNumber08 = activityEntity.UserDefinedNumber08;
			newActivity.UserDefinedNumber09 = activityEntity.UserDefinedNumber09;
			newActivity.UserDefinedNumber10 = activityEntity.UserDefinedNumber10;
			newActivity.UserDefinedDate01 = activityEntity.UserDefinedDate01;
			newActivity.UserDefinedDate02 = activityEntity.UserDefinedDate02;
			newActivity.UserDefinedDate03 = activityEntity.UserDefinedDate03;
			newActivity.UserDefinedDate04 = activityEntity.UserDefinedDate04;
			newActivity.UserDefinedDate05 = activityEntity.UserDefinedDate05;
			newActivity.UserDefinedDate06 = activityEntity.UserDefinedDate06;
			newActivity.UserDefinedDate07 = activityEntity.UserDefinedDate07;
			newActivity.UserDefinedDate08 = activityEntity.UserDefinedDate08;
			newActivity.UserDefinedDate09 = activityEntity.UserDefinedDate09;
			newActivity.SearchPattern = activityEntity.SearchPattern;
			newActivity.UserDefinedDate10 = activityEntity.UserDefinedDate10;
			#endregion

			return newActivity;
		}

		/// <summary>
		/// Change activity status
		/// </summary>
		/// <param name="activityId"></param>
		/// <param name="newStatusId"></param>
		/// <returns></returns>
		public ActivityEntity ChangeStatus(int activityId, int newStatusId)
		{
			var activity = this.GetByFilter(e => e.Id == activityId).FirstOrDefault();
			var state = new BasicsCustomizeActivityStateLogic().GetListByFilter(s => s.Id == newStatusId).FirstOrDefault();
			activity.IsReadOnly = state.IsReadOnly;
			activity.ActivityStateFk = newStatusId;
			return SaveActivity(activity);
		}

		#region IEntityFacade members

		/// <summary>
		/// Name of the entity, for IEntityFacade
		/// </summary>
		string IEntityFacade.Name
		{
			get { return "Activity"; }
		}

		/// <summary>
		/// UUID to clearly determine the entity provider, for IEntityFacade
		/// </summary>
		string IEntityFacade.Id
		{
			get { return EntitiyFacadeIdentifier.SCHEDULING_MAIN_ACTIVITY; }
		}

		/// <summary>
		/// Module name
		/// </summary>
		string IEntityFacade.ModuleName
		{
			get { return "scheduling.main"; }
		}

		/// <summary>
		/// Get Entity Properties, for IEntityFacade
		/// </summary>
		string[] IEntityFacade.Properties
		{
			get
			{
				return GetProperties(0).GetPropertyNames();
			}
		}

		/// <summary>
		/// Get a ActivityEntity by Id, for IEntityFacade
		/// </summary>
		IDictionary<string, object> IEntityFacade.Get(int id)
		{
			var entity = GetActivityById(id);
			var objectDic = ToDictionary(entity);
			return objectDic;
		}

		/// <summary>
		/// Save a ActivityEntity, for IEntityFacade
		/// </summary>
		IDictionary<string, object> IEntityFacade.Save(IDictionary<string, object> entityDictionary)
		{
			int id = entityDictionary.GetId<int>();
			var entity = GetActivityById(id);
			if (entity == null)
			{
				entity = new ActivityEntity();
			}
			entity.SetObject(entityDictionary, GetProperties(entity.Version));
			VerifyActivityFacadeEntity(entity);
			return ToDictionary(this.SaveActivity(entity));
		}

		private void VerifyActivityFacadeEntity(ActivityEntity entity)
		{
			if (entity.ScheduleFk <= 0)
			{
				throw new SchedulingBusinessLayerException(NLS.ERR_ActivityHasNoScheduleAssigned);
			}

			ScheduleEntity schedule = new ScheduleLogic().GetScheduleById(entity.ScheduleFk);
			if (schedule == null)
			{
				throw new SchedulingBusinessLayerException(NLS.ERR_NoScheduleFoundForAssignedId);
			}

			if (entity.ParentActivityFk.HasValue)
			{
				int parentId = entity.ParentActivityFk.Value;
				var parent = GetActivityById(parentId);
				if (parent.ScheduleFk != entity.ScheduleFk)
				{

				}
			}

			if (entity.ProjectFk <= 0)
			{
				entity.ProjectFk = schedule.ProjectFk;
			}

			if (entity.CalendarFk <= 0 && schedule.CalendarFk.HasValue)
			{
				entity.CalendarFk = schedule.CalendarFk.Value;
			}
		}

		private IDictionary<string, object> ToDictionary(ActivityEntity entity)
		{
			var objectDic = entity.AsDictionary(GetProperties(entity.Version));

			return objectDic;
		}

		private ConvertProperties GetProperties(int entityVersion)
		{
			bool isReadonly = entityVersion != 0;
			ConvertProperties entityProperties = new ConvertProperties()
				.Add("Id", isReadonly)
				.Add("ProjectFk")
				.Add("BaselineFk")
				.Add("Code")
				.Add("Description")
				.Add("ParentActivityFk", isReadonly)
				.Add("ScheduleFk")
				.Add("CompanyFk", isReadonly)
				.Add("BaseActivityFk", isReadonly)
				.Add("ActivityTemplateFk")
				.Add("ControllingUnitFk")
				.Add("CalendarFk")
				.Add("AllowModify")
				.Add("Specification")
				.Add("ActivityStateFk")
				.Add("SchedulingMethodFk")
				.Add("SubScheduleFk")
				.Add("LocationSpecification")
				.Add("ActivityPresentationFk")
				.Add("PlannedStart", isReadonly)
				.Add("PlannedFinish", isReadonly)
				.Add("CurrentFinish", isReadonly)
				.Add("CurrentStart", isReadonly)
				.Add("CurrentDuration", isReadonly)
				.Add("ConstraintTypeFk")
				.Add("ConstraintDate")
				.Add("ActualStart")
				.Add("ActualFinish")
				.Add("ExecutionStarted")
				.Add("ExecutionFinished")
				.Add("PlannedDuration", isReadonly)
				.Add("EarliestStart")
				.Add("LatestStart")
				.Add("EarliestFinish")
				.Add("LatestFinish")
				.Add("TotalFloatTime")
				.Add("FreeFloatTime")
				.Add("ResourceFactor")
				.Add("PerformanceFactor")
				.Add("PerformanceRuleFk")
				.Add("Perf1UoMFk", "Performance1UoMFk")
				.Add("Perf2UoMFk", "Performance2UoMFk")
				.Add("TaskTypeFk")
				.Add("Work")
				.Add("QuantityUoMFk")
				.Add("Quantity")
				.Add("ProgressReportMethodFk")
				.Add("IsCritical")
				.Add("IsLive")
				.Add("UserDefinedText01")
				.Add("UserDefinedText02")
				.Add("UserDefinedText03")
				.Add("UserDefinedText04")
				.Add("UserDefinedText05")
				.Add("UserDefinedText06")
				.Add("UserDefinedText07")
				.Add("UserDefinedText08")
				.Add("UserDefinedText09")
				.Add("UserDefinedText10")
				.Add("UserDefinedNumber01")
				.Add("UserDefinedNumber02")
				.Add("UserDefinedNumber03")
				.Add("UserDefinedNumber04")
				.Add("UserDefinedNumber05")
				.Add("UserDefinedNumber06")
				.Add("UserDefinedNumber07")
				.Add("UserDefinedNumber08")
				.Add("UserDefinedNumber09")
				.Add("UserDefinedNumber10")
				.Add("UserDefinedDate01")
				.Add("UserDefinedDate02")
				.Add("UserDefinedDate03")
				.Add("UserDefinedDate04")
				.Add("UserDefinedDate05")
				.Add("UserDefinedDate06")
				.Add("UserDefinedDate07")
				.Add("UserDefinedDate08")
				.Add("UserDefinedDate09")
				.Add("UserDefinedDate10")
				.Add("SearchPattern")
				.Add("PeriodQuantityPerformance", isReadonly)
				.Add("DueDateQuantityPerformance", isReadonly)
				.Add("RemainingActivityQuantity", isReadonly)
				.Add("PeriodWorkPerformance", isReadonly)
				.Add("DueDateWorkPerformance", isReadonly)
				.Add("RemainingActivityWork", isReadonly)
				.Add("PercentageCompletion", isReadonly)
				.Add("PercentageRemaining", isReadonly);
			return entityProperties;
		}

		#endregion

		#region IActivityLogic

		int IActivityLogic.UpdateActivitiesInProcurementSchedule(int prjId, IEnumerable<ISchedulingActivity> activities)
		{
			ScheduleLogic scheduleLogic = new ScheduleLogic();

			var scheduleId = scheduleLogic.AssertProcurementScheduleByProject(prjId);

			return DoUpdateActivitiesInSchedule(prjId, scheduleId, activities);
		}

		int IActivityLogic.UpdateActivitiesInSchedule(int scheduleId, IEnumerable<ISchedulingActivity> activities)
		{
			ScheduleLogic scheduleLogic = new ScheduleLogic();

			var prjId = scheduleLogic.GetScheduleById(scheduleId).ProjectFk;

			return DoUpdateActivitiesInSchedule(prjId, scheduleId, activities);
		}

		ISchedulingActivity IActivityLogic.GetSchedulingActivityById(int activityId)
		{
			var activity = GetActivityById(activityId);

			var events = new EventLogic().GetListByFilter(e => e.ActivityFk == activityId);

			return new SchedulingActivityEntity { Code = activity.Code, EarliestFinish = activity.EarliestFinish, EarliestStart = activity.EarliestStart, EndDate = activity.PlannedFinish, LatestFinish = activity.LatestFinish, LatestStart = activity.LatestStart, PackageDescription = activity.Description, StartDate = activity.PlannedStart, ScheduleFk = activity.ScheduleFk, Events = events };
		}

		/// <summary>
		/// Create or save an activity in a procurement schedule to given schedule
		/// </summary>
		/// <param name="schedule">The schedule the activity is to be created in</param>
		/// <param name="parentActivity">The parent activity of the activity to be created. null is a vailid option to create a new root</param>
		/// <returns>The newly created instance</returns>
		IActivityEntity IActivityLogic.CreateActivity(IScheduleEntity schedule, IActivityEntity parentActivity)
		{
			var sched = schedule as ScheduleEntity;
			int parentId = (parentActivity != null) ? parentActivity.Id : -1;

			return CreateActivity(sched.Id, sched.ProjectFk, parentId);
		}

		/// <summary>
		/// Delete an activity in a schedule
		/// </summary>
		/// <param name="activity">The activity to be deleted</param>
		void IActivityLogic.DeleteActivity(IActivityEntity activity)
		{
			var act = GetActivityById(activity.Id);
			DeleteActivity(act);
		}

		/// <summary>
		/// returns progress values e.g. performace, quantity, remaining,... and IActivity values for given id
		/// </summary>
		/// <param name="activityId">Activity Id</param>
		/// <returns>IActivity and progress values</returns>
		IProgressActivityEntity IActivityLogic.GetProgressActivity(int activityId)
		{
			var progressActivity = new ProgressActivity();
			var activity = GetActivityById(activityId);

			if (activity.ActivityTypeFk == Constants.ActivityTypeSummaryActivity)
			{
				using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{
					activity.ActivityEntities_ParentActivityFk = dbcontext.ExecuteStoredProcedure<ActivityEntity>("PSD_ACTIVITY_ITEM_ITEMTREE_SP", true, activity.ScheduleFk, activityId, 999).ToList();
				}
			}
			IncludeCompletionInformation(new List<ActivityEntity>() { activity });

			progressActivity.DueDateQuantityPerformance = activity.DueDateQuantityPerformance;
			progressActivity.DueDateWorkPerformance = activity.DueDateWorkPerformance;
			progressActivity.LastProgressDate = activity.LastProgressDate;
			progressActivity.PercentageCompletion = activity.PercentageCompletion;
			progressActivity.PercentageRemaining = activity.PercentageRemaining;
			progressActivity.PeriodQuantityPerformance = activity.PeriodQuantityPerformance;
			progressActivity.PeriodWorkPerformance = activity.PeriodWorkPerformance;
			progressActivity.RemainingActivityQuantity = activity.RemainingActivityQuantity;
			progressActivity.RemainingActivityWork = activity.RemainingActivityWork;
			progressActivity.Activity = (IActivityEntity)activity;

			return progressActivity;
		}


		/// <summary>
		/// Update activity
		/// </summary>
		/// <param name="activity"></param>
		/// <returns></returns>
		int IActivityLogic.UpdateActivity(IActivityEntity activity)
		{
			return UpdateActivities(new List<ActivityEntity>() { (ActivityEntity)activity }).First().Id;
		}

		/// <summary>
		/// update a list of activities
		/// </summary>
		/// <param name="activities">List of activities to be updated</param>
		/// <returns>The activity with the given Id in the schedule passed</returns>
		IEnumerable<IActivityEntity> IActivityLogic.UpdateActivities(IEnumerable<IActivityEntity> activities)
		{
			IEnumerable<IActivityEntity> res = null;
			var acts = activities.OfType<ActivityEntity>().ToList();
			if (acts != null && acts.Any())
			{
				using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{
					var pool = new ScheduleDataPool();
					UpdateEntitiesInDbContext(acts, null, dbcontext);
					pool.LoadScheduleData(acts.First().ScheduleFk, dbcontext);
					var tC = new TypeCalculation();
					var complete = tC.Validate(acts, pool);
					if (complete.EffectedActivities != null && complete.EffectedActivities.Any())
					{
						acts.AddRange(complete.EffectedActivities);
					}
					res = UpdateActivities(acts.Distinct()).ToList();
					UpdateEntitiesInDbContext(complete.RelationshipsToSave, complete.RelationshipsToDelete, dbcontext);
					dbcontext.DetectChangesOnSave = true;
					dbcontext.SaveChanges();
					//AddActivityInfoProperties
				}
			}
			else
			{
				res = new List<ActivityEntity>();
			}

			return res;
		}

		/// <summary>
		/// Get an Activity by Id
		/// </summary>
		/// <param name="actId">The Id of the desired activity</param>
		/// <returns>The activity with the given Id</returns>
		IActivityEntity IActivityLogic.GetActivity(int actId)
		{
			return GetActivityById(actId);
		}

		/// <summary>
		/// Synchronize psdActivity
		/// </summary>
		/// <param name="activities"></param>
		/// <returns></returns>
		IEnumerable<IActivityEntity> IActivityLogic.SynchronizeActivities(IEnumerable<IActivityEntity> activities)
		{
			var psdIds = activities.CollectIds(e => e.Id);
			var synList = GetActivity(e => psdIds.Contains(e.Id));
			foreach (var psdActivity in synList)
			{
				var synActivity = activities.FirstOrDefault(e => e.Id == psdActivity.Id);
				psdActivity.Id = synActivity.Id;
				psdActivity.PlannedStart = synActivity.PlannedStart;
				psdActivity.PlannedFinish = synActivity.PlannedFinish;
				psdActivity.EarliestStart = synActivity.EarliestStart;
				psdActivity.EarliestFinish = synActivity.EarliestFinish;
				psdActivity.LatestStart = synActivity.LatestStart;
				psdActivity.LatestFinish = synActivity.LatestFinish;
				psdActivity.CalendarFk = synActivity.CalendarFk;
				psdActivity.LocationFk = synActivity.LocationFk;
				psdActivity.ControllingUnitFk = synActivity.ControllingUnitFk;
				psdActivity.ActualStart = synActivity.ActualStart;
				psdActivity.ActualFinish = synActivity.ActualFinish;
				if (psdActivity.ActualFinish.HasValue)
				{
					psdActivity.ExecutionStarted = true;
				}
				if (psdActivity.ActualFinish.HasValue)
				{
					psdActivity.ExecutionFinished = true;
				}
			}

			return UpdateActivities(synList);
		}

		private int DoUpdateActivitiesInSchedule(int prjId, int scheduleId, IEnumerable<ISchedulingActivity> activities)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				List<ActivityEntity> activitiesToSave = new List<ActivityEntity>();
				var rootActivityEntities = new Dictionary<string, ActivityEntity>();
				var nodeActivityEntities = new Dictionary<string, ActivityEntity>();
				var planUtils = new PlanningUtilities();
				var scheduleActivityEntities = dbcontext.Entities<ActivityEntity>().Where(a => a.ScheduleFk == scheduleId && a.IsLive).ToList();
				//fix 87534: 1) Always delete leaf activities, then create new activities.
				//           2) Leaf activity's Code need to be generated automatically by schedule's Code Format rule.
				var leafActivitiesToDelete = new List<ActivityEntity>();
				var lastCode = string.Empty;
				foreach (var activity in activities)
				{
					var nodeEntity =
						scheduleActivityEntities.FirstOrDefault(e => e.Code == activity.Code);
					var rootEntity = AssertSummaryActivity(prjId, scheduleId, activity, rootActivityEntities, scheduleActivityEntities);
					activitiesToSave.Add(rootEntity);
					if (nodeEntity == null)
					{
						nodeEntity = CreateActivity(scheduleId, prjId, rootEntity.Id);
						scheduleActivityEntities.Add(nodeEntity);
					}
					if (nodeEntity != null)
					{
						var nodeIdent = new Platform.Core.IdentificationData() { PKey1 = nodeEntity.Id, PKey2 = nodeEntity.ScheduleFk };
						var nodeChildActitiviesInDb = scheduleActivityEntities.Where(e => e.ParentActivityFk == nodeEntity.Id).ToList();
						leafActivitiesToDelete.AddRange(nodeChildActitiviesInDb);
						scheduleActivityEntities = scheduleActivityEntities.Except(nodeChildActitiviesInDb).ToList();
						if (nodeEntity.Code != activity.Code || nodeEntity.Description != activity.PackageDescription || nodeEntity.PlannedStart != activity.StartDate || nodeEntity.PlannedFinish != activity.EndDate || nodeEntity.Version == 0)
						{
							nodeEntity.Code = activity.Code;
							nodeEntity.Description = activity.PackageDescription;
							nodeEntity.PlannedStart = activity.StartDate;
							nodeEntity.PlannedFinish = activity.EndDate.HasValue ? activity.EndDate : activity.StartDate;
							nodeEntity.ActivityTypeFk = Constants.ActivityTypeSummaryActivity;
							planUtils.CalculateDuration(nodeEntity);
							activitiesToSave.Add(nodeEntity);
						}
						nodeActivityEntities.Add(nodeEntity.Code, nodeEntity);
						//add clerks from procurement
						if (activity.ClerkResponsible.HasValue && activity.ClerkRoleResponsible.HasValue)
						{
							var clerkLogic = new ActivityClerkLogic();
							var clerkResponsible = clerkLogic.Create(nodeIdent);
							clerkResponsible.ClerkFk = activity.ClerkResponsible.Value;
							clerkResponsible.ClerkRoleFk = activity.ClerkRoleResponsible.Value;
							clerkResponsible.ActivityEntity = nodeEntity;
							clerkLogic.Save(clerkResponsible);
						}
						if (activity.ClerkOwner.HasValue && activity.ClerkRoleOwner.HasValue)
						{
							var clerkLogic = new ActivityClerkLogic();
							var clerkOwner = clerkLogic.Create(nodeIdent);
							clerkOwner.ClerkFk = activity.ClerkOwner.Value;
							clerkOwner.ClerkRoleFk = activity.ClerkRoleOwner.Value;
							clerkOwner.ActivityEntity = nodeEntity;
							clerkLogic.Save(clerkOwner);
						}
						foreach (var schedulingEvent in activity.Events)
						{
							var leafEntity = CreateActivity(scheduleId, prjId, nodeEntity.Id, lastCode, false);
							leafEntity.EventTypeFk = schedulingEvent.EventTypeFk;
							scheduleActivityEntities.Add(leafEntity);
							lastCode = leafEntity.Code;
							if (schedulingEvent.StartDate.HasValue)
							{
								leafEntity.PlannedStart = schedulingEvent.StartDate.Value;
								leafEntity.PlannedFinish = schedulingEvent.EndDate ?? schedulingEvent.StartDate.Value.AddDays(1);
								leafEntity.CurrentStart = leafEntity.PlannedStart;
								leafEntity.CurrentFinish = leafEntity.PlannedFinish;
								leafEntity.ActivityTypeFk = Constants.ActivityTypeActivity;
								planUtils.CalculateDuration(leafEntity);
							}
							else
							{
								leafEntity.PlannedStart = schedulingEvent.EndDate ?? DateTime.Today;
								leafEntity.PlannedFinish = leafEntity.PlannedStart;
								leafEntity.CurrentStart = leafEntity.PlannedStart;
								leafEntity.CurrentFinish = leafEntity.PlannedFinish;
								leafEntity.ActivityTypeFk = Constants.ActivityTypeMilestone;
							}
							leafEntity.Description = schedulingEvent.Description;
							activitiesToSave.Add(leafEntity);
						}
					}
				}
				CalculateSummaryDatesForProcurement(nodeActivityEntities, scheduleActivityEntities, planUtils);
				CalculateSummaryDatesForProcurement(rootActivityEntities, scheduleActivityEntities, planUtils);
				UpdateActivities(activitiesToSave);
				EventLogic eventlogic = new EventLogic();
				var activityIds = leafActivitiesToDelete.Select(e => e.Id).Distinct().ToList();
				var events = eventlogic.GetListByFilter(e => e.ActivityFk.HasValue && activityIds.Contains(e.ActivityFk.Value));
				eventlogic.Delete(events);
				this.Delete<ActivityEntity>(ModelBuilder.DbModel, leafActivitiesToDelete);
			}
			return scheduleId;
		}

		private void CalculateSummaryDatesForProcurement(Dictionary<string, ActivityEntity> summaries, List<ActivityEntity> activities, PlanningUtilities planUtils)
		{
			foreach (var sumAct in summaries.Values)
			{
				//child activities
				var entities = activities.Where(act => act.ParentActivityFk == sumAct.Id).ToList();

				if (entities != null && entities.Any())
				{
					DateTime entityWithSmallestDate = (from d in entities select d.PlannedStart).Min();
					DateTime? entityWithBiggestDate = (from d in entities select d.PlannedFinish).Max();
					sumAct.PlannedStart = entityWithSmallestDate;
					sumAct.PlannedFinish = entityWithBiggestDate;
					sumAct.CurrentStart = entityWithSmallestDate;
					sumAct.CurrentFinish = entityWithBiggestDate;
					planUtils.CalculateDuration(sumAct);
					sumAct.CurrentDuration = sumAct.PlannedDuration;
				}
			}
		}

		/// <summary>
		/// Patch to Save the Activity Entities
		/// </summary>
		/// <param name="properties"></param>
		/// <returns></returns>
		public IEnumerable<IActivityEntity> SaveEntities(Dictionary<int, Dictionary<string, object>> properties)
		{
			using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var ids = properties.Select(e => e.Key);
				IEnumerable<ActivityEntity> entities = dbContext.Entities<ActivityEntity>().Where(e => ids.Contains(e.Id));
				foreach (var item in entities.ToList())
				{
					item.SetObject(properties[item.Id], GetProperties(item.Version));
				}
				return dbContext.Save(entities);
			}
		}

		#endregion

		#region IEntityProvider member

		IIdentifyable IEntityProvider.GetById(int id)
		{
			return GetActivityById(id);
		}

		IEnumerable<IIdentifyable> IEntityProvider.GetByIds(IEnumerable<int> ids)
		{
			return GetActivity(a => ids.Contains(a.Id));
		}

		#endregion

		/// <summary>
		///    Asserts that the activity determined by the id passed are loaded (this includes their companions in plan with all relations, events, ...)
		/// </summary>
		/// <param name="prjId">ID of project</param>
		/// <param name="scheduleId">ID of schedule</param>
		/// <param name="activity">information of summary activity</param>
		/// <param name="rootActivityEntities">Dictionary containing all summary activities already loaded</param>
		/// <param name="scheduleActivityEntities">All activities of schedule, old already stored ones and recently created as well</param>
		private ActivityEntity AssertSummaryActivity(int prjId, int scheduleId, ISchedulingActivity activity, Dictionary<string, ActivityEntity> rootActivityEntities, List<ActivityEntity> scheduleActivityEntities)
		{
			ActivityEntity res;
			var schedule = new ScheduleLogic().GetScheduleById(scheduleId);
			string code = schedule != null ? schedule.Code : activity.Sorting.ToString();
			if (!rootActivityEntities.TryGetValue(code, out res))
			{
				res = scheduleActivityEntities.Where(act => act.Code == code).FirstOrDefault();
				if (res == null)
				{
					res = new ActivityLogic().CreateActivity(scheduleId, prjId, -1);
					res.Description = activity.TypeDescription;
					res.Code = code;
					res.ActivityTypeFk = Constants.ActivityTypeSummaryActivity;

					rootActivityEntities[code] = res;
				}
				else if (!rootActivityEntities.ContainsKey(code))
				{
					res.Description = activity.TypeDescription;
					rootActivityEntities[code] = res;
				}
			}
			return res;
		}

		/// <summary>
		/// creates new activity/ies from the given list
		/// </summary>
		/// <returns>the created activities</returns>
		private ActivityEntity doCreateActivity(int scheduleId, int projectId, int? parentActivityId)
		{
			var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

			var schLogic = new ScheduleLogic();
			var schedule = schLogic.GetScheduleById(scheduleId);
			var calId = schedule.CalendarFk ?? 1;


			// init default values here
			var entity = new ActivityEntity();
			//entity.CompanyFk = context.ClientId;
			entity.CompanyFk = schedule.CompanyFk;
			entity.ScheduleFk = scheduleId;
			entity.ProjectFk = projectId;
			entity.CalendarFk = calId;
			if ((parentActivityId.HasValue && parentActivityId > 0) || !parentActivityId.HasValue)
			{
				entity.ParentActivityFk = parentActivityId;
			}

			DoInitNewActivityWithDefaults(entity, schedule);

			DoIncludeTransientProperties(new List<ActivityEntity>() { entity });
			return entity;
		}

		private void DoInitNewActivityWithDefaults(ActivityEntity entity, ScheduleEntity schedule)
		{
			var id = SequenceManager.GetNext("PSD_ACTIVITY");
			entity.Id = id;
			DoInitActivityWithDefaults(entity, schedule);

			// add schedule info
			entity.Schedule = schedule;
			entity.ScheduleCode = schedule.Code;
			entity.ScheduleDescription = schedule.DescriptionInfo.Translated;

			entity.TargetStart = schedule.TargetStart;
			entity.TargetEnd = schedule.TargetEnd;
			// project info
			var projectLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();
			var project = projectLogic.GetProjectById(entity.ProjectFk);
			entity.ProjectNo = project.ProjectNo;
			entity.ProjectName = project.ProjectName;
			entity.ProjectName2 = project.ProjectName2;
			// schedule type
			var schedType = new Basics.Customize.BusinessComponents.BasicsCustomizeScheduleTypeLogic().GetById(schedule.ScheduleTypeFk) as BasicsCustomizeScheduleTypeEntity;
			entity.ScheduleTypeIsExecution = schedType.Isexecution;
			entity.ScheduleTypeIsDesign = schedType.IsDesign;
			entity.ScheduleTypeIsProcurement = schedType.Isprocurement;
		}

		private void EnsureTempGUID(String tempGUID)
		{
			if (!_hasTempCreatePermission || tempGUID != _tempCreatePermissionGUID)
			{
				throw new SchedulingBusinessLayerException(NLS.ERR_NoRightsForActivityCreationGranted);
			}
		}

		private string GetNextCode(int scheduleId, int? parentActivityId, string lastCode, bool newHierarchy, IDictionary<IIdentifyable, IIdentifyable> ownerMapping)
		{
			int? codeFormatFk = ScheduleCodeFormatFk;
			if (ScheduleCodeFormatFk == null)
			{
				ScheduleLogic scheduleLogic = new ScheduleLogic();
				var schedule = scheduleLogic.GetCoresAsListByFilter(e => e.Id == scheduleId).FirstOrDefault();
				if (schedule != null)
				{
					if (schedule.CodeFormatFk.HasValue)
					{
						codeFormatFk = schedule.CodeFormatFk.Value;
						ScheduleCodeFormatFk = codeFormatFk;
					}
				}
			}

			var generator = EntityCodeGeneratorFactory.GetGenerator(codeFormatFk);

			bool mNewHierarchy = newHierarchy;
			IEntityCore last = new ActivityEntity();
			last.Code = lastCode;
			ActivityEntity lastItem = null;
			if (ownerMapping.Count > 0)
			{
				var acts = ownerMapping.Values.OfType<ActivityEntity>();
				lastItem = acts.Where(e => e.ParentActivityFk == parentActivityId && e.ScheduleFk == scheduleId).OrderBy(e => e.Code).LastOrDefault();
			}
			if (lastItem == null)
			{
				var lastItems = GetActivity(e => e.ScheduleFk == scheduleId);
				lastItem = lastItems.Where(e => e.ParentActivityFk == parentActivityId && e.ScheduleFk == scheduleId).OrderBy(e => e.Code).LastOrDefault();

				if (lastItem == null && (generator.GetType() == typeof(RunningNumberGenerator) || generator.GetType() == typeof(RunningNumberWithZerosGenerator)))
				{
					lastItem = lastItems.OrderBy(e => e.Code).LastOrDefault();
				}
			}

			if (lastItem != null)
			{
				var code1Parts = lastCode.Split('.');
				var code2Parts = lastItem.Code.Split('.');
				if (code1Parts.Count() >= code2Parts.Count() || newHierarchy)
				{
					if (newHierarchy || code1Parts[code2Parts.Count() - 1].CompareTo(code2Parts[code2Parts.Count() - 1]) < 0)
					{
						last.Code = lastItem.Code;
						mNewHierarchy = false;
					}
				}
			}

			bool isValid = false;
			while (!isValid)
			{
				string code;
				if (mNewHierarchy)
				{
					code = generator.GetSingleChildCode(last);
				}
				else
				{
					code = generator.GetSingleCode(last);
				}
				if (!String.IsNullOrEmpty(code))
				{
					last.Code = code;
					mNewHierarchy = false;
					isValid = true;
					if (ownerMapping != null && ownerMapping.Count > 0)
					{
						var copiedActivities = ownerMapping.Values.OfType<ActivityEntity>();
						foreach (var actEntity in copiedActivities)
						{
							if (actEntity != null && actEntity.Code.CompareTo(code) == 0)
							{
								isValid = false;
								break;
							}
						}
					}
					if (isValid)
					{
						isValid = IsUniqueForDeepCopy(e => e.Code == code && e.ScheduleFk == scheduleId && !e.BaselineFk.HasValue && !e.BaseActivityFk.HasValue);
					}
				}
			}
			if (last.Code.Length > 16)
			{
				throw new SchedulingBusinessLayerException
				{
					ErrorCode = (int)ExceptionErrorCodes.ResourceFatalError,
					ErrorDetail = NLS.ERR_CodeTooLong,
					ErrorMessage = NLS.ERR_CodeTooLong
				};

			}
			return last.Code;
		}


		/// <summary>
		/// Create Activity Tree
		/// </summary>
		/// <param name="activities">list of activities to build up tree</param>
		/// <returns>tree of activities</returns>
		private IList<ActivityEntity> BuildPartialTree(IList<ActivityEntity> activities)
		{
			var resMap = activities.ToDictionary(a => a.Id);
			var parents = new List<ActivityEntity>();

			foreach (var item in activities)
			{
				//item.ActivityEntities_ParentActivityFk.Clear();
				ActivityEntity parent = null;
				if (item.ParentActivityFk.HasValue && resMap.TryGetValue(item.ParentActivityFk.Value, out parent))
				{
					var exist = parent.ActivityEntities_ParentActivityFk.Where(e => e.Id == item.Id);
					if (exist == null)
					{
						parent.ActivityEntities_ParentActivityFk.Add(item);
					}
				}
				else
				{
					parents.Add(item);
				}
			}

			return parents;
		}

		#region IEntityCopier implementation

		IEnumerable<IIdentifyable> IEntityCopier.AsFlatList(IEnumerable<IIdentifyable> copies)
		{
			return copies;
		}

		IDictionary<IIdentifyable, IIdentifyable> IEntityCopier.Copy(IEnumerable<IIdentifyable> toBeCopied, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping)
		{
			var res = new Dictionary<IIdentifyable, IIdentifyable>();
			var tbc = toBeCopied.OfType<ActivityEntity>();
			Stack<int> ids = new Stack<int>(this.SequenceManager.GetNextList("PSD_ACTIVITY", tbc.Count()).Reverse());

			foreach (var orig in tbc)
			{
				var copy = DoCopy(orig);

				copy.Id = ids.Pop();
				SetNewOwner(copy, newOwner, ownerMapping);
				SetNewCode(copy, newOwner, true, ownerMapping);
				ownerMapping.Add(orig, copy);

				res.Add(orig, copy);
			}

			return res;
		}

		IIdentifyable IEntityCopier.Copy(IIdentifyable toBeCopied, IIdentifyable newOwner)
		{
			IIdentifyable res = null;
			var orig = (ActivityEntity)toBeCopied;
			if (orig != null)
			{
				var copy = DoCopy(orig);
				copy.Id = this.SequenceManager.GetNext("PSD_ACTIVITY");
				SetNewOwner(copy, newOwner);
				SetNewCode(copy, newOwner, true);
			}

			return res;
		}

		IIdentifyable IEntityCopier.Copy(IIdentifyable toBeCopied)
		{
			IIdentifyable res = null;
			var orig = (ActivityEntity)toBeCopied;
			if (orig != null)
			{
				res = DoCopy(orig);
				res.Id = this.SequenceManager.GetNext("PSD_ACTIVITY");
			}

			return res;
		}

		IDictionary<IIdentifyable, IIdentifyable> IEntityCopier.DeepCopy(IEnumerable<IIdentifyable> toBeCopied, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping)
		{
			var res = new Dictionary<IIdentifyable, IIdentifyable>();

			var activitiesToBeCopied = toBeCopied.OfType<ActivityEntity>().ToArray();
			var all = activitiesToBeCopied.Flatten(a => a.ActivityEntities_ParentActivityFk).ToArray();
			Stack<int> ids = new Stack<int>(this.SequenceManager.GetNextList("PSD_ACTIVITY", all.Count()).Reverse());

			foreach (var actToBeCopied in activitiesToBeCopied)
			{
				var copy = CopyForDeepCopy(actToBeCopied);
				SetNewOwner(copy, newOwner, ownerMapping);
				// SetNewCode(copy, newOwner, false, ownerMapping); ALM 128631: new code NOT wanted
				copy.Id = ids.Pop();
				ownerMapping.Add(actToBeCopied, copy);
				res.Add(actToBeCopied, copy);

				if (actToBeCopied.ActivityEntities_ParentActivityFk != null && actToBeCopied.ActivityEntities_ParentActivityFk.Any())
				{
					var copiedChildren = DoDeepCopy(actToBeCopied.ActivityEntities_ParentActivityFk, copy, ids, ownerMapping, false);
					foreach (var copiedAct in copiedChildren)
					{
						res.Add(copiedAct.Key, copiedAct.Value);
					}
				}
			}

			return res;
		}

		IDictionary<IIdentifyable, IIdentifyable> IEntityCopier.DeepCopy(IIdentifyable toBeCopied, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping)
		{
			var res = new Dictionary<IIdentifyable, IIdentifyable>();

			var orig = toBeCopied as ActivityEntity;
			if (orig != null)
			{
				var copy = DoCopy(orig);
				SetNewOwner(copy, newOwner, ownerMapping);
				SetNewCode(copy, newOwner, false, res);
				copy.Id = this.SequenceManager.GetNext("PSD_ACTIVITY");
				res.Add(orig, copy);

				IHierachicalEntityAccess ha = new ActivityRelationInfo();
				var children = BuildPartialTree(ha.LoadChildrenTree(orig).Where(e => e.Id != orig.Id).OfType<ActivityEntity>().ToList());
				if (children.Any())
				{
					DoDeepCopy(children, copy, res, true);
				}
			}

			return res;
		}

		IDictionary<IIdentifyable, IIdentifyable> IEntityCopier.DeepCopy(IIdentifyable toBeCopied)
		{
			var res = new Dictionary<IIdentifyable, IIdentifyable>();
			var orig = toBeCopied as ActivityEntity;
			if (orig != null)
			{
				var copy = DoCopy(orig);
				var newOwner = (toBeCopied as ActivityEntity).Clone() as ActivityEntity;
				newOwner.Id = 0;
				SetNewCode(copy, newOwner, false, res);
				copy.Id = this.SequenceManager.GetNext("PSD_ACTIVITY");
				res.Add(orig, copy);

				IHierachicalEntityAccess ha = new ActivityRelationInfo();
				var children = BuildPartialTree(ha.LoadChildrenTree(orig).Where(e => e.Id != orig.Id).OfType<ActivityEntity>().ToList());
				if (children.Any())
				{
					DoDeepCopy(children, copy, res, true);
				}
			}

			return res;
		}

		IEnumerable<IIdentifyable> IEntityCopier.GetAggregatedForDeepCopy(IEntityRelationInfo responsible, IIdentifyable entity, IEntityRelationInfo intermediate)
		{
			var schedule = entity as ScheduleEntity;

			if (schedule != null)
			{
				return GetActivitiesStructured(schedule.Id, 0, 15).ToArray();
			}
			else
			{
				return new List<ActivityEntity>();
			}
		}

		IEntityAggregator IEntityCopier.GetAggregator()
		{
			return this;
		}

		void IEntityCopier.ValidateCopies(IEnumerable<IIdentifyable> copies, IEntityRelationInfo responsible)
		{
			var ignoreCriticalPath = responsible.GetIdentifier() == "scheduling.schedule.schedule";
			int defConstraintType = Constants.AsSoonAsPossible;

			if (responsible.GetIdentifier() == "scheduling.main.activity.drag")
			{
				var lookupLogic = new RVSLB.SchedulingLookupLogic();
				// get default constraint type (Alm #138626)
				defConstraintType = lookupLogic.GetDefaultConstraintType();
				if (defConstraintType < 0 || (defConstraintType != Constants.AsSoonAsPossible && defConstraintType != Constants.AsLateAsPossible && defConstraintType != Constants.NoConstraint))
				{
					defConstraintType = Constants.AsSoonAsPossible;
				}
			}

			var copiedActivities = copies.OfType<ActivityEntity>();
			foreach (var act in copiedActivities)
			{
				if (responsible.GetIdentifier() == "scheduling.main.activity.drag")
				{
					act.ConstraintDate = null;
					act.ConstraintTypeFk = defConstraintType;
				}
				DoValidateActivity(act, ignoreCriticalPath);
			}
		}



		/// <summary>
		/// Provide aggregated for deep copy
		/// </summary>
		/// <param name="responsible"></param>
		/// <param name="entity"></param>
		/// <param name="intermediate"></param>
		/// <returns></returns>
		protected override IEnumerable<IIdentifyable> ProvideAggregatedForDeepCopy(IEntityRelationInfo responsible, IIdentifyable entity,
	IEntityRelationInfo intermediate)
		{
			IEnumerable<int> children = null;
			if (responsible.GetIdentifier() == "scheduling.schedule.schedule")
			{
				IHierachicalEntityAccess ha = new ActivityRelationInfo();
				var candidates = ha.LoadChildrenTree(entity).OfType<ActivityEntity>();

				if (candidates != null && candidates.Any())
				{
					children = candidates.Select(e => e.Id);
				}

				if (children != null && children.Any())
				{
					var subscheduleFk = candidates.First().SubScheduleFk;
					return GetListByFilter(e => e.SubScheduleFk == subscheduleFk);
				}
				else
				{
					return candidates;
				}
			}
			return null;
		}


		void IEntityCopier.ValidateCopy(IIdentifyable copy, IEntityRelationInfo responsible)
		{
			DoValidateActivity(copy as ActivityEntity);
		}

		private void DoValidateActivity(ActivityEntity act, bool criticalPathIgnore = false)
		{
			if (act != null)
			{
				act.EarliestStart = null;
				act.EarliestFinish = null;
				act.LatestStart = null;
				act.LatestFinish = null;
				act.ActualStart = null;
				act.ActualFinish = null;
				act.ActualDuration = null;
				act.ExecutionStarted = false;
				act.ExecutionFinished = false;
				act.FreeFloatTime = null;
				act.TotalFloatTime = null;
			}
			if (!criticalPathIgnore)
			{
				act.IsCritical = false;
			}
		}

		private ActivityEntity DoCopy(ActivityEntity tc)
		{
			var copy = (ActivityEntity)tc.Clone();

			copy.Version = 0;
			copy.UpdatedAt = null;
			copy.UpdatedBy = null;
			if (copy.Code.Length <= 13)
			{
				copy.Code = copy.Code + "(1)";
			}
			else
			{
				copy.Code = copy.Code.Substring(0, 13) + "(1)";
			}

			return copy;
		}

		private ActivityEntity CopyForDeepCopy(ActivityEntity tc)
		{
			var copy = (ActivityEntity)tc.Clone();

			copy.Version = 0;
			copy.UpdatedAt = null;
			copy.UpdatedBy = null;
			copy.ActivityStateFk = 1;

			return copy;
		}

		private IDictionary<IIdentifyable, IIdentifyable> DoDeepCopy(IEnumerable<ActivityEntity> tbc, IIdentifyable owner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping, bool newCode)
		{
			var all = tbc.Flatten(a => a.ActivityEntities_ParentActivityFk);
			Stack<int> ids = new Stack<int>(this.SequenceManager.GetNextList("PSD_ACTIVITY", all.Count()).Reverse());
			return DoDeepCopy(tbc, owner, ids, ownerMapping, newCode);
		}

		private IDictionary<IIdentifyable, IIdentifyable> DoDeepCopy(IEnumerable<ActivityEntity> tbc, IIdentifyable owner, Stack<int> ids, IDictionary<IIdentifyable, IIdentifyable> ownerMapping, bool newCode)
		{
			var res = new Dictionary<IIdentifyable, IIdentifyable>();

			foreach (var orig in tbc.OrderBy(a => a.Code))
			{
				var copy = CopyForDeepCopy(orig);

				copy.Id = ids.Pop();
				IIdentifyable newOwner = owner;
				if (copy.ParentActivityFk.HasValue)
				{
					var tmp = ownerMapping.Keys.FirstOrDefault(a => a.Id == copy.ParentActivityFk.Value);
					IIdentifyable newParent = null;
					if (tmp != null && ownerMapping.TryGetValue(tmp, out newParent))
					{
						newOwner = newParent;
					}
				}

				SetNewOwner(copy, newOwner, ownerMapping);
				if (newCode)
				{
					SetNewCode(copy, newOwner, true, ownerMapping); // ALM 128631: new code NOT wanted
				}
				ownerMapping[orig] = copy;
				res.Add(orig, copy);

				if (orig.ActivityEntities_ParentActivityFk != null && orig.ActivityEntities_ParentActivityFk.Any())
				{
					var copiedChildren = DoDeepCopy(orig.ActivityEntities_ParentActivityFk, copy, ids, ownerMapping, newCode);
					foreach (var copiedAct in copiedChildren)
					{
						res.Add(copiedAct.Key, copiedAct.Value);
						ownerMapping[copiedAct.Key] = copiedAct.Value;
					}
				}
			}

			return res;
		}

		/// <summary>
		/// Sets a new owner of the activity
		/// </summary>
		/// <param name="copy"></param>
		/// <param name="newOwner"></param>
		/// <param name="ownerMapping"></param>
		public void SetNewOwner(ActivityEntity copy, IIdentifyable newOwner, IDictionary<IIdentifyable, IIdentifyable> ownerMapping = null)
		{
			ActivityEntity parentAct = newOwner as ActivityEntity;
			if (parentAct != null)
			{
				copy.ScheduleFk = parentAct.ScheduleFk;
				copy.ProjectReleaseFk = null;
				if (copy.ProjectFk != parentAct.ProjectFk)
				{
					if (ownerMapping != null && ownerMapping.Any())
					{
						// handling controlling units
						var controllingUnitIds = ownerMapping.Keys.OfType<IControllingUnitEntity>().Select(c => c.Id);
						if (copy.ControllingUnitFk.HasValue && !controllingUnitIds.Contains(copy.ControllingUnitFk.Value))
						{
							copy.ControllingUnitFk = null;
						}

						var acts = ownerMapping.Keys.OfType<IProjectCalendarEntity>();
						var oldCalendar = acts.FirstOrDefault(a => a.CalendarFk == copy.CalendarFk);
						if (oldCalendar != null)
						{
							var newPrjCalendar = ownerMapping[oldCalendar] as IProjectCalendarEntity;
							if (newPrjCalendar != null)
							{
								copy.CalendarFk = newPrjCalendar.CalendarFk;
							}
						}
						else
						{
							copy.LocationFk = null;
							//var calendar = new SchedulingCalendarLogic().GetCalendarById(copy.CalendarFk);
							var calendar = new SchedulingCalendarLogic().GetById(new IdentificationData() { Id = copy.CalendarFk });
							if (calendar != null && calendar.CalendarTypeFk != 1 || calendar == null)
							{
								copy.CalendarFk = parentAct.CalendarFk;
							}
						}
						var locs = ownerMapping.Keys.OfType<IProjectLocationEntity>();
						var oldLocation = locs.FirstOrDefault(a => a.Id == copy.LocationFk);
						copy.LocationFk = null;
						if (oldLocation != null)
						{
							var newLocation = ownerMapping[oldLocation] as IProjectLocationEntity;
							if (newLocation != null)
							{
								copy.LocationFk = newLocation.Id;
							}
						}
					}
					else
					{
						copy.ControllingUnitFk = null;

						copy.LocationFk = null;
						//var calendar = new SchedulingCalendarLogic().GetCalendarById(copy.CalendarFk);
						var calendar = new SchedulingCalendarLogic().GetById(new IdentificationData() { Id = copy.CalendarFk });
						if (calendar != null && calendar.CalendarTypeFk != 1 || calendar == null)
						{
							copy.CalendarFk = parentAct.CalendarFk;
						}
					}
				}
				copy.ProjectFk = parentAct.ProjectFk;
				if (parentAct.Id > 0)
				{
					copy.ParentActivityFk = parentAct.Id;
				}
				else
				{
					copy.ParentActivityFk = null;
				}
			}
			else
			{
				ScheduleEntity schedule = newOwner as ScheduleEntity;
				ScheduleCodeFormatFk = null;
				if (schedule != null)
				{
					if (copy.ProjectFk != schedule.ProjectFk)
					{
						copy.ProjectReleaseFk = null;
						if (ownerMapping != null)
						{
							// handling controlling units
							var controllingUnitIds = ownerMapping.Keys.OfType<IControllingUnitEntity>().Select(c => c.Id);
							if (copy.ControllingUnitFk.HasValue && !controllingUnitIds.Contains(copy.ControllingUnitFk.Value))
							{
								copy.ControllingUnitFk = null;
							}

							var acts = ownerMapping.Keys.OfType<IProjectCalendarEntity>();
							var oldCalendar = acts.FirstOrDefault(a => a.CalendarFk == copy.CalendarFk);

							if (oldCalendar != null)
							{
								var newPrjCalendar = ownerMapping[oldCalendar] as IProjectCalendarEntity;
								if (newPrjCalendar != null)
								{
									copy.CalendarFk = newPrjCalendar.CalendarFk;
								}
							}
							var locs = ownerMapping.Keys.OfType<IProjectLocationEntity>();
							var oldLocation = locs.FirstOrDefault(a => a.Id == copy.LocationFk);
							copy.LocationFk = null;
							if (oldLocation != null)
							{
								var newLocation = ownerMapping[oldLocation] as IProjectLocationEntity;
								if (newLocation != null)
								{
									copy.LocationFk = newLocation.Id;
								}
							}

						}
						else
						{
							copy.ControllingUnitFk = null;
						}
					}
					copy.ProjectFk = schedule.ProjectFk;
					copy.ScheduleFk = newOwner.Id;
					copy.ParentActivityFk = null;
					ScheduleCodeFormatFk = schedule.CodeFormatFk;
				}
			}
		}

		private void SetNewCode(ActivityEntity copy, IIdentifyable newOwner, bool newHierarchy, IDictionary<IIdentifyable, IIdentifyable> ownerMapping = null)
		{
			ActivityEntity parentAct = newOwner as ActivityEntity;
			if (parentAct != null && parentAct.Id > 0)
			{
				copy.Code = GetNextCode(parentAct.ScheduleFk, parentAct.Id, parentAct.Code, true, ownerMapping);
			}
			else
			{
				copy.Code = GetNextCode(copy.ScheduleFk, null, copy.Code, newHierarchy, ownerMapping);
			}
		}


		#endregion

		#region IEntityAggregator implementation

		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IEnumerable<IIdentifyable> toBeAggregated)
		{
			DoAggregate(to, relInfo, toBeAggregated);
		}

		void IEntityAggregator.Aggregate(IIdentifyable to, IEntityRelationInfo relInfo, IIdentifyable toBeAggregated)
		{
			var tba = new List<IIdentifyable>();
			tba.Add(toBeAggregated);

			DoAggregate(to, relInfo, tba);
		}

		IIdentifyable IEntityAggregator.ProvideCompleteEntity()
		{
			return new ActivityComplete();
		}

		private void DoAggregate(IIdentifyable to, IEntityRelationInfo relInfo, IEnumerable<IIdentifyable> toBeAggregated)
		{
			var updateEntity = (ActivityComplete)to;
			if (updateEntity != null)
			{
				switch (relInfo.GetIdentifier())
				{
					case "scheduling.main.activity":
					case "scheduling.main.activity.drag":
						if (updateEntity.Activity == null)
						{
							updateEntity.Activity = (ActivityEntity)toBeAggregated.FirstOrDefault();
							to.Id = updateEntity.Activity.Id;
						}
						else
						{
							updateEntity.EffectedActivities = toBeAggregated.OfType<ActivityEntity>().ToList();
						}
						break;
					case "scheduling.main.event": updateEntity.EventsToSave = toBeAggregated.OfType<EventEntity>().ToList(); break;
					case "scheduling.main.relationship": updateEntity.RelationshipsToSave = toBeAggregated.OfType<ActivityRelationshipEntity>().ToList(); break;
					case "scheduling.main.activityclerk": updateEntity.ClerksToSave = toBeAggregated.OfType<ActivityClerkEntity>().ToList(); break;
				}
			}
		}

		#endregion

		IDictionary<string, object> ICreateEntityFacade.Create()
		{
			var entity = new ActivityEntity();
			DoInitNewActivityWithDefaults(entity, null);

			var objectDic = ToDictionary(entity);
			return objectDic;
		}

		void IDataBaseLogic.Delete(IEnumerable<IIdentifyable> ToDelete)
		{
			var acts = ToDelete.OfType<ActivityEntity>();

			this.DeleteActivities(acts);
		}

		IEnumerable<IIdentifyable> IDataBaseLogic.Save(IEnumerable<IIdentifyable> ToSave)
		{
			var acts = ToSave.OfType<ActivityEntity>();
			if (acts.Any())
			{
				return this.UpdateActivities(acts);
			}
			var updates = ToSave.OfType<ActivityComplete>();
			if (updates.Any())
			{
				//foreach (var toUpdate in updates)
				//{
				//    this.Update(toUpdate);
				//}
				this.Update(CombineActivities(updates));

				return updates;
			}

			return null;
		}


		private ActivityComplete CombineActivities(IEnumerable<ActivityComplete> toSave)
		{
			var combinedActivity = new ActivityComplete();
			combinedActivity.EffectedActivities = new List<ActivityEntity>();
			if (toSave.Any())
			{
				combinedActivity.Activity = toSave.First().Activity;
				foreach (var activityComplete in toSave)
				{
					combinedActivity.EffectedActivities.Add(activityComplete.Activity);
					if (activityComplete.EffectedActivities != null && activityComplete.EffectedActivities.Any())
					{
						combinedActivity.EffectedActivities.AddRange(activityComplete.EffectedActivities);
					}

					if (activityComplete.RequiredByRequisitionDtoToSave != null &&
						activityComplete.RequiredByRequisitionDtoToSave.Any())
					{
						if (combinedActivity.RequiredByRequisitionDtoToSave != null)
						{
							combinedActivity.RequiredByRequisitionDtoToSave.ToList().AddRange(activityComplete.RequiredByRequisitionDtoToSave);
						}
						else
						{
							var list = new List<IIdentifyable>();
							list.AddRange(activityComplete.RequiredByRequisitionDtoToSave);
							combinedActivity.RequiredByRequisitionDtoToSave = list;
						}
					}

					if (activityComplete.RequiredByRequisitionDtoToDelete != null &&
						activityComplete.RequiredByRequisitionDtoToDelete.Any())
					{
						if (combinedActivity.RequiredByRequisitionDtoToDelete != null)
						{
							combinedActivity.RequiredByRequisitionDtoToDelete.ToList().AddRange(activityComplete.RequiredByRequisitionDtoToDelete);
						}
						else
						{
							var list = new List<IIdentifyable>();
							list.AddRange(activityComplete.RequiredByRequisitionDtoToDelete);
							combinedActivity.RequiredByRequisitionDtoToDelete = list;
						}
					}

					if (activityComplete.RelationshipsToSave != null &&
						activityComplete.RelationshipsToSave.Any())
					{
						if (combinedActivity.RelationshipsToSave != null)
						{
							combinedActivity.RelationshipsToSave.AddRange(activityComplete.RelationshipsToSave);
						}
						else
						{
							var list = new List<ActivityRelationshipEntity>();
							list.AddRange(activityComplete.RelationshipsToSave);
							combinedActivity.RelationshipsToSave = list;
						}
					}

					if (activityComplete.RelationshipsToDelete != null &&
						activityComplete.RelationshipsToDelete.Any())
					{
						if (combinedActivity.RelationshipsToDelete != null)
						{
							combinedActivity.RelationshipsToDelete.AddRange(activityComplete.RelationshipsToDelete);
						}
						else
						{
							var list = new List<ActivityRelationshipEntity>();
							list.AddRange(activityComplete.RelationshipsToDelete);
							combinedActivity.RelationshipsToDelete = list;
						}
					}

					if (activityComplete.EventsToSave != null &&
						activityComplete.EventsToSave.Any())
					{
						if (combinedActivity.EventsToSave != null)
						{
							combinedActivity.EventsToSave.AddRange(activityComplete.EventsToSave);
						}
						else
						{
							var list = new List<EventEntity>();
							list.AddRange(activityComplete.EventsToSave);
							combinedActivity.EventsToSave = list;
						}
					}

					if (activityComplete.EventsToDelete != null &&
						activityComplete.EventsToDelete.Any())
					{
						if (combinedActivity.EventsToDelete != null)
						{
							combinedActivity.EventsToDelete.AddRange(activityComplete.EventsToDelete);
						}
						else
						{
							var list = new List<EventEntity>();
							list.AddRange(activityComplete.EventsToDelete);
							combinedActivity.EventsToDelete = list;
						}
					}

					if (activityComplete.ClerksToSave != null &&
						activityComplete.ClerksToSave.Any())
					{
						if (combinedActivity.ClerksToSave != null)
						{
							combinedActivity.ClerksToSave.AddRange(activityComplete.ClerksToSave);
						}
						else
						{
							var list = new List<ActivityClerkEntity>();
							list.AddRange(activityComplete.ClerksToSave);
							combinedActivity.ClerksToSave = list;
						}
					}

					if (activityComplete.ClerksToDelete != null &&
											activityComplete.ClerksToDelete.Any())
					{
						if (combinedActivity.ClerksToDelete != null)
						{
							combinedActivity.ClerksToDelete.AddRange(activityComplete.ClerksToDelete);
						}
						else
						{
							var list = new List<ActivityClerkEntity>();
							list.AddRange(activityComplete.ClerksToDelete);
							combinedActivity.ClerksToDelete = list;
						}
					}

					if (activityComplete.ProgressReportsToSave != null &&
						activityComplete.ProgressReportsToSave.Any())
					{
						if (combinedActivity.ProgressReportsToSave != null)
						{
							combinedActivity.ProgressReportsToSave.AddRange(activityComplete.ProgressReportsToSave);
						}
						else
						{
							var list = new List<ActivityProgressReportEntity>();
							list.AddRange(activityComplete.ProgressReportsToSave);
							combinedActivity.ProgressReportsToSave = list;
						}
					}

					if (activityComplete.ProgressReportsToDelete != null &&
						activityComplete.ProgressReportsToDelete.Any())
					{
						if (combinedActivity.ProgressReportsToDelete != null)
						{
							combinedActivity.ProgressReportsToDelete.AddRange(activityComplete.ProgressReportsToDelete);
						}
						else
						{
							var list = new List<ActivityProgressReportEntity>();
							list.AddRange(activityComplete.ProgressReportsToDelete);
							combinedActivity.ProgressReportsToDelete = list;
						}
					}

					if (activityComplete.BelongsToActivityEstLineItemDtoToSave != null &&
						activityComplete.BelongsToActivityEstLineItemDtoToSave.Any())
					{
						if (combinedActivity.BelongsToActivityEstLineItemDtoToSave != null)
						{
							combinedActivity.BelongsToActivityEstLineItemDtoToSave.ToList().AddRange(activityComplete.BelongsToActivityEstLineItemDtoToSave);
						}
						else
						{
							var list = new List<IIdentifyable>();
							list.AddRange(activityComplete.BelongsToActivityEstLineItemDtoToSave);
							combinedActivity.BelongsToActivityEstLineItemDtoToSave = list;
						}
					}

					if (activityComplete.BelongsToActivityEstLineItemDtoToDelete != null &&
						activityComplete.BelongsToActivityEstLineItemDtoToDelete.Any())
					{
						if (combinedActivity.BelongsToActivityEstLineItemDtoToDelete != null)
						{
							combinedActivity.BelongsToActivityEstLineItemDtoToDelete.ToList().AddRange(activityComplete.BelongsToActivityEstLineItemDtoToDelete);
						}
						else
						{
							var list = new List<IIdentifyable>();
							list.AddRange(activityComplete.BelongsToActivityEstLineItemDtoToDelete);
							combinedActivity.BelongsToActivityEstLineItemDtoToDelete = list;
						}
					}
				}
			}

			return combinedActivity;
		}

		#region IGetItemLogic implementation

		/// <summary>
		///
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public IIdentifyable GetItemByKey(int id)
		{
			return this.GetActivityById(id);
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="keys"></param>
		/// <returns></returns>
		public IEnumerable<IIdentifyable> GetItemsByKey(IEnumerable<int?> keys)
		{
			Expression<Func<ActivityEntity, bool>> predicate = e => keys.Contains(e.Id);
			return GetActivity(predicate);
		}

		#endregion
		/// <summary>
		/// method to get activities with the help of the public api request
		/// </summary>
		/// <param name="request"></param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetActivityListForPublicApi(ActivityRequest request)
		{
			IEnumerable<ActivityEntity> activities = null;

			if (request.ScheduleId.HasValue)
			{
				activities = GetActivity(
					e => e.ScheduleFk == request.ScheduleId.Value && e.IsLive && !e.BaselineFk.HasValue && !e.BaseActivityFk.HasValue);
			}
			else
			{
				if (!String.IsNullOrEmpty(request.ProjectNo) && request.ProjectId <= 0)
				{
					var projectLogic = BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();
					var cId = BusinessApplication.BusinessEnvironment.CurrentContext.ClientId;
					var project = projectLogic.GetProjectInfo(request.ProjectNo, cId);
					if (project != null)
					{
						request.ProjectId = project.Id;
					}
				}
				if (!string.IsNullOrEmpty(request.ScheduleCode))
				{
					var scheduleLogic = new ScheduleLogic();
					request.ScheduleId = scheduleLogic.GetScheduleIDByCodeFromProject(request.ScheduleCode, request.ProjectId);
				}
				if (request.ScheduleId.HasValue)
				{
					activities = GetActivity(
						e => e.ScheduleFk == request.ScheduleId.Value && e.IsLive && !e.BaselineFk.HasValue && !e.BaseActivityFk.HasValue);
				}
				else
				{
					activities =
						GetActivity(
							e => e.ProjectFk == request.ProjectId && e.IsLive && !e.BaselineFk.HasValue && !e.BaseActivityFk.HasValue);
				}
			}

			if (request.BusinessPartnerId.HasValue)
			{
				var contractLogic =
					Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IContractHeaderInfoProvider>();
				var conheaders =
					contractLogic.GetConHeaderSearchList(string.Format("BusinessPartnerFk={0}", request.BusinessPartnerId.Value)).ToArray();
				if (conheaders.Length > 0)
				{
					var headerIds = conheaders.Select(e => (int?)e.Id).ToArray();
					var accountLogic =
						Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment
							.GetExportedValue<IGetSubItemLogic<IIdentifyable>>("Procurement.Contract.Accountassignment");
					List<IConAccountAssignmentEntity> accounts = new List<IConAccountAssignmentEntity>();
					foreach (var id in headerIds)
					{
						if (id.HasValue)
						{
							var account = accountLogic.GetListByParentId(id.Value).ToArray();
							if (account.Length > 0)
							{
								accounts.AddRange(account.OfType<IConAccountAssignmentEntity>());
							}
						}
					}
					if (accounts.Count > 0)
					{
						var scheduleIds = accounts.Where(e => e.PsdActivityFk == null).Select(e => e.PsdScheduleFk).Distinct().ToArray();
						var assignedActs = accounts.Select(e => e.PsdActivityFk).Distinct().ToArray();
						activities =
							activities.Where(
								e =>
									scheduleIds.Length > 0 && scheduleIds.Contains(e.ScheduleFk) || assignedActs.Length > 0 &&
									assignedActs.Contains(e.Id));
						//if (scheduleIds.Length > 0)
						//{
						//	activities = activities.Where(e => scheduleIds.Contains(e.ScheduleFk));
						//}
						//if (assignedActs.Length > 0)
						//{
						//	activities = activities.Where(e => assignedActs.Contains(e.Id));
						//}
					}
					else
					{
						activities = new List<ActivityEntity>();
					}
				}
				else
				{
					activities = new List<ActivityEntity>();
				}
			}

			if (request.ActivityStateIds != null && request.ActivityStateIds.Length > 0)
			{
				activities =
					activities.Where(e => e.ActivityStateFk == request.ActivityStateIds.FirstOrDefault());
			}

			if (!String.IsNullOrEmpty(request.SearchText))
			{
				var searchtext = request.SearchText.ToLower();
				var locationLogic =
					Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectLocationInfoProvider>();
				var locationList = locationLogic.GetLocationListByPrjId(request.ProjectId);
				var locationIds =
					locationList.Where(
						e =>
							e.Code.ToLower().Contains(searchtext) ||
							e.DescriptionInfo.Description != null && e.DescriptionInfo.Description.ToLower().Contains(searchtext) ||
							e.DescriptionInfo.Translated != null && e.DescriptionInfo.Translated.ToLower().Contains(searchtext))
						.Select(e => e.Id);

				activities = activities.Where(
					e =>
						(e.SearchPattern != null && e.SearchPattern.ToLower().Contains(searchtext)) ||
						e.Code.ToLower().Contains(searchtext) ||
						(e.Description != null && e.Description.ToLower().Contains(searchtext)) ||
						(e.LocationSpecification != null && e.LocationSpecification.ToLower().Contains(searchtext)) ||
						(e.LocationFk.HasValue && locationIds.Contains(e.LocationFk.Value)));
			}

			if (request.FromDate.HasValue)
			{
				activities =
					activities.Where(
						e =>
							e.PlannedStart.CompareTo(request.FromDate.Value) >= 0 ||
							(e.PlannedFinish.HasValue && e.PlannedFinish.Value.CompareTo(request.FromDate.Value) >= 0));
			}

			if (request.ToDate.HasValue)
			{
				activities =
					activities.Where(
						e =>
							e.PlannedStart.CompareTo(request.ToDate.Value) <= 0 ||
							(e.PlannedFinish.HasValue && e.PlannedFinish.Value.CompareTo(request.ToDate.Value) <= 0));
			}

			var activityEntities = activities as ActivityEntity[] ?? activities.ToArray();
			//DoIncludeTransientProperties(activityEntities);

			var startRec = 0;
			var pageSize = request.PageSize > 0 ? request.PageSize : 100;
			var pageNumber = request.PageIndex <= 0 ? 0 : request.PageIndex - 1;
			startRec = pageSize * pageNumber;

			if (String.IsNullOrEmpty(request.FirstOrderBy))
			{
				request.FirstOrderBy = "UpdatedAt";
			}
			if (String.IsNullOrEmpty(request.FirstKindOfOrder))
			{
				request.FirstKindOfOrder = "Descending";
			}

			ParameterExpression paramExpr = Expression.Parameter(typeof(ActivityEntity), "e");
			var convFirst = Expression.Convert(Expression.Property(paramExpr, request.FirstOrderBy), typeof(object));
			var exprFirst = Expression.Lambda<Func<ActivityEntity, object>>(convFirst, paramExpr).Compile();

			IOrderedEnumerable<ActivityEntity> orderedEntities = null;
			if (request.FirstKindOfOrder == "Descending")
			{
				orderedEntities = activityEntities.OrderByDescending(exprFirst);
			}
			else
			{
				orderedEntities = activityEntities.OrderBy(exprFirst);
			}
			if (!String.IsNullOrEmpty(request.SecondOrderBy))
			{
				var propertyExpr = Expression.Property(paramExpr, request.SecondOrderBy);
				var conversion = Expression.Convert(propertyExpr, typeof(object));

				var exprSecond = Expression.Lambda<Func<ActivityEntity, object>>(conversion, paramExpr).Compile();

				if (String.IsNullOrEmpty(request.SecondKindOfOrder) || request.SecondKindOfOrder == "Descending")
				{
					orderedEntities = orderedEntities.ThenByDescending(exprSecond);
				}
				else
				{
					orderedEntities = orderedEntities.ThenBy(exprSecond);
				}
			}
			return orderedEntities
				.Skip(startRec)
				.Take(pageSize).ToList();

		}

		/// <summary>
		/// change status
		/// </summary>
		/// <param name="identification"></param>
		/// <param name="statusId"></param>
		public RVPBC.EntityBase ChangeStatus(IStatusIdentifyable identification, int statusId)
		{
			this.ChangeStatus(identification.Id, statusId);
			return this.ChangeStatus(identification.Id, statusId);
		}

		/// <summary>
		/// get current status by special id
		/// </summary>
		/// <param name="identification"></param>
		/// <returns></returns>
		public int GetCurrentStatus(IStatusIdentifyable identification)
		{
			var item = this.GetActivityById(identification.Id);
			return item.ActivityStateFk;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="identifications"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		public IEnumerable<int> GetCanChangeStatusEntities(IEnumerable<IStatusIdentifyable> identifications)
		{
			throw new NotImplementedException();
		}

		/// <summary>
		/// creates new activity/ies from the given list
		/// </summary>
		/// <returns>the created activities</returns>
		public ActivityEntity CreateActivityForImport(int scheduleId, int projectId, int? parentActivityId, String tempGUID)
		{
			EnsureTempGUID(tempGUID);

			var context = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.CurrentContext;

			// init default values here
			var entity = new ActivityEntity();
			entity.CompanyFk = context.ClientId;
			entity.ScheduleFk = scheduleId;
			entity.ProjectFk = projectId;
			if ((parentActivityId.HasValue && parentActivityId > 0) || !parentActivityId.HasValue)
			{
				entity.ParentActivityFk = parentActivityId;
			}

			//var id = SequenceManager.GetNext("PSD_ACTIVITY");
			//entity.Id = id;

			entity.PlannedDuration = 1;
			entity.CurrentDuration = 1;
			entity.ResourceFactor = 1;
			entity.PerformanceFactor = 1;

			entity.ActivityTypeFk = Constants.ActivityTypeActivity;

			entity.IsCritical = false;
			entity.IsLive = true;
			entity.Quantity = 1;

			return entity;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="enable"></param>
		public void SetTransientRootEnable(bool enable)
		{
			var userId = RVPARB.BusinessEnvironment.CurrentContext.UserId;
			var manager = ProfileFactory.CreateProfileManager();
			manager.Profile.SetUserAppValue<bool>(userId, schedulingMainSettingsAppId, "TransientRootEnable", enable);
			manager.SaveProfile();
		}

		/// <summary>
		///
		/// </summary>
		/// <returns></returns>
		public bool? GetTransientRootEnable()
		{
			var userId = RVPARB.BusinessEnvironment.CurrentContext.UserId;
			var manager = ProfileFactory.CreateProfileManager();
			return manager.Profile.GetUserAppValue<bool>(userId, schedulingMainSettingsAppId, "TransientRootEnable");
		}

		private List<ActivityEntity> GetEffectedActivities(ActivityEntity entity, ScheduleDataPool pool)
		{
			var effected = new List<ActivityEntity>();
			var parent = entity;
			while (parent != null)
			{
				if (parent.Id != entity.Id)
				{
					effected.Add(parent);
				}
				if (parent.ParentActivityFk.HasValue)
				{
					parent = pool.GetActivityById(parent.ParentActivityFk.Value);
				}
				else
				{
					parent = null;
				}
			}
			return effected;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="entitiesToProcess"></param>
		/// <param name="changeRequestList"></param>
		/// <returns></returns>
		public IEnumerable<IEnumerable<IBulkInformationData>> ProcessEntities(IEnumerable<ActivityEntity> entitiesToProcess, IEnumerable<IEnumerable<IBulkInformationData>> changeRequestList)
		{
			RemoveTransientRootActivity(entitiesToProcess.ToList());
			var scheduleIds = entitiesToProcess.Select(e => e.ScheduleFk).Distinct().ToArray();
			var calcLogic = new Calculation();
			calcLogic.WithTransientProperties = false;
			var dicCalendars = new Dictionary<int, int[]>();
			var dicSchedule = new Dictionary<int, bool>();
			var dicControllingUnits = new Dictionary<int, int[]>();
			var projCalLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValues<IProjectCalendarLogic>().FirstOrDefault();
			var cuLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValues<IControllingUnitLogic>().FirstOrDefault();

			var dataPool = new ScheduleDataPool();
			var actsToSave = new Dictionary<int, ActivityEntity>();

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				foreach (var scheduleId in scheduleIds)
				{
					dataPool.LoadScheduleData(scheduleId, dbcontext);
					dicSchedule[scheduleId] = false;
				}
			}
			var changeActivities = new Dictionary<int, ActivityEntity>();
			var hasToReschedule = false;
			foreach (var changeRequest in changeRequestList)
			{
				foreach (var change in changeRequest)
				{
					if (change != null && change.IsChanged && change.Id!= TransientRootActivityId)
					{
						var activity = entitiesToProcess.Where(e => e.Id == change.Id).FirstOrDefault();
						ActivityEntity oldActivity = dataPool.GetActivityById(activity.Id).Clone() as ActivityEntity;
						var planningChange = new CalculationActivityEntity();
						planningChange.WithOutShiftSuccessor = true;
						planningChange.Id = activity.Id;
						var complete = new ActivityComplete();
						complete.Activity = oldActivity;
						complete.ActivityPlanningChange = planningChange;
						complete.MainItemId = activity.Id;
						bool hasToUpdate = true;
						switch (change.AffectedProperty)
						{
							case "PlannedDuration":
								planningChange.Duration = activity.PlannedDuration;
								activity.PlannedDuration = oldActivity.PlannedDuration;
								hasToReschedule = true;
								break;
							case "PlannedStart":
								planningChange.StartDate = activity.PlannedStart;
								activity.PlannedStart = oldActivity.PlannedStart;
								hasToReschedule = true;
								break;
							case "PlannedFinish":
								planningChange.EndDate = activity.PlannedFinish;
								activity.PlannedFinish = oldActivity.PlannedFinish;
								hasToReschedule = true;
								break;
							case "ActualStart":
								planningChange.ActualStart = activity.ActualStart;
								activity.ActualStart = oldActivity.ActualStart;
								planningChange.ChangedField = "ActualStart";
								complete.EffectedActivities = GetEffectedActivities(activity, dataPool);
								hasToReschedule = true;
								break;
							case "ActualFinish":
								planningChange.ActualStart = activity.ActualFinish;
								complete.Activity = oldActivity;
								planningChange.ChangedField = "ActualFinish";
								complete.EffectedActivities = GetEffectedActivities(activity, dataPool);
								hasToReschedule = true;
								break;
							case "ExecutionStarted":
								planningChange.ExecutionStarted = activity.ExecutionStarted;
								activity.ExecutionStarted = oldActivity.ExecutionStarted;
								planningChange.ChangedField = "ExecutionStarted";
								complete.EffectedActivities = GetEffectedActivities(activity, dataPool);
								hasToReschedule = true;
								break;
							case "ExecutionFinished":
								planningChange.ExecutionFinished = activity.ExecutionFinished;
								planningChange.DueDate = DateTime.Now;
								complete.Activity = oldActivity;
								planningChange.ChangedField = "ExecutionFinished";
								complete.EffectedActivities = GetEffectedActivities(activity, dataPool);
								hasToReschedule = true;
								break;
							case "CalendarFk":
								// get project calendars
								int[] cals;
								if (!dicCalendars.TryGetValue(activity.ProjectFk, out cals))
								{
									cals = projCalLogic.GetProjectCalendarsByProject(activity.ProjectFk).Select(e => e.CalendarFk).ToArray();
									dicCalendars[activity.ProjectFk] = cals;
								}
								if (cals.Contains(activity.CalendarFk))
								{
									planningChange.CalendarFk = activity.CalendarFk;
									oldActivity.CalendarFk = activity.CalendarFk;
									hasToReschedule = true;
								}
								else
								{
									change.IsChanged = false;
									var result = new BulkValidationResultData();
									result.Apply = true;
									result.Error = "Calendar not assigned to current project!";
									result.Valid = false;
									change.ValidationResultData = result;
									hasToReschedule = hasToReschedule || false;
									hasToUpdate = false;
								}
								break;
							case "ConstraintTypeFk":
								if ((activity.ConstraintTypeFk != null && activity.ConstraintTypeFk != Constants.AsLateAsPossible &&
									activity.ConstraintTypeFk != Constants.AsSoonAsPossible && activity.ConstraintTypeFk != Constants.NoConstraint) ||
									activity.ExecutionStarted)
								{
									//SaveActivity(activity);
									hasToUpdate = false;
									dataPool.Activities = new List<ActivityEntity>() { activity };
									hasToReschedule = hasToReschedule || false;
								}
								else
								{
									planningChange.ConstraintTypeFk = activity.ConstraintTypeFk;
									activity.ConstraintTypeFk = oldActivity.ConstraintTypeFk;
									hasToReschedule = true;
								}
								break;
							case "Quantity":
								planningChange.Quantity = activity.Quantity;
								activity.Quantity = oldActivity.Quantity;
								hasToReschedule = true;
								break;
							case "PerformanceFactor":
								planningChange.PerformanceFactor = activity.PerformanceFactor;
								activity.PerformanceFactor = oldActivity.PerformanceFactor;
								hasToReschedule = true;
								break;
							case "ResourceFactor":
								planningChange.ResourceFactor = activity.ResourceFactor;
								activity.ResourceFactor = oldActivity.ResourceFactor;
								hasToReschedule = true;
								break;
							case "QuantityUoMFk":
								if (activity.PerformanceFactor.HasValue && activity.Perf1UoMFk.HasValue && activity.Perf1UoMFk.Value > 0 && activity.Perf2UoMFk.HasValue && activity.Perf2UoMFk.Value > 0
									&& activity.Quantity.HasValue && activity.QuantityUoMFk.HasValue)
								{
									planningChange.UoM = activity.QuantityUoMFk;
									oldActivity.QuantityUoMFk = activity.QuantityUoMFk;
									hasToReschedule = true;
								}
								else
								{
									//SaveActivity(activity);
									hasToReschedule = true;
									hasToUpdate = false;
									dataPool.Activities = new List<ActivityEntity>() { activity };
								}
								break;
							case "Perf1UoMFk":
								if (activity.PerformanceFactor.HasValue && activity.QuantityUoMFk.HasValue && activity.Perf2UoMFk.HasValue && activity.Perf2UoMFk.Value > 0
									&& activity.Quantity.HasValue && activity.Perf1UoMFk.HasValue && activity.Perf1UoMFk.Value > 0)
								{
									planningChange.UoM1 = activity.Perf1UoMFk;
									//activity.Perf1UoMFk = oldActivity.Perf1UoMFk;
									hasToReschedule = true;
								}
								else
								{
									//SaveActivity(activity);
									hasToReschedule = hasToReschedule || false;
									hasToUpdate = false;
									dataPool.Activities = new List<ActivityEntity>() { activity };
								}
								break;
							case "Perf2UoMFk":
								if (activity.PerformanceFactor.HasValue && activity.QuantityUoMFk.HasValue && activity.Perf1UoMFk.HasValue && activity.Perf1UoMFk.Value > 0
									&& activity.Quantity.HasValue && activity.Perf2UoMFk.HasValue && activity.Perf2UoMFk.Value > 0)
								{
									planningChange.UoM2 = activity.Perf2UoMFk;
									//activity.Perf2UoMFk = oldActivity.Perf2UoMFk;
									hasToReschedule = true;
								}
								else
								{
									//SaveActivity(activity);
									hasToReschedule = hasToReschedule || false;
									dataPool.Activities = new List<ActivityEntity>() { activity };
									hasToUpdate = false;
								}
								break;
							case "PercentageCompletion":
								planningChange.DueDate = DateTime.Now;
								planningChange.PercentageCompletion = activity.PercentageCompletion;
								complete.Activity = oldActivity;
								hasToReschedule = true;
								break;
							case "DueDateQuantityPerformance":
								planningChange.DueDate = DateTime.Now;
								planningChange.DueDateQuantityPerformance = activity.DueDateQuantityPerformance;
								planningChange.RemainingActivityQuantity = activity.RemainingActivityQuantity;
								complete.Activity = oldActivity;
								hasToReschedule = true;
								break;
							case "PeriodQuantityPerformance":
								planningChange.DueDate = DateTime.Now;
								planningChange.PeriodQuantityPerformance = activity.PeriodQuantityPerformance;
								complete.Activity = oldActivity;
								hasToReschedule = true;
								break;
							case "RemainingActivityQuantity":
								planningChange.DueDate = DateTime.Now;
								planningChange.RemainingActivityQuantity = activity.RemainingActivityQuantity;
								complete.Activity = oldActivity;
								hasToReschedule = true;
								break;
							case "PeriodWorkPerformance":
								planningChange.DueDate = DateTime.Now;
								planningChange.PeriodWorkPerformance = activity.PeriodWorkPerformance;
								complete.Activity = oldActivity;
								hasToReschedule = true;
								break;
							case "DueDateWorkPerformance":
								planningChange.DueDate = DateTime.Now;
								planningChange.DueDateWorkPerformance = activity.DueDateWorkPerformance;
								complete.Activity = oldActivity;
								hasToReschedule = true;
								break;
							case "RemainingActivityWork":
								planningChange.DueDate = DateTime.Now;
								planningChange.RemainingActivityWork = activity.RemainingActivityWork;
								complete.Activity = oldActivity;
								hasToReschedule = true;
								break;
							case "LocationFk":
								if (activity.ActivityTemplateFk.HasValue && !activity.ControllingUnitFk.HasValue)
								{
									planningChange.LocationFk = activity.LocationFk;
									planningChange.ActivityTemplateFk = activity.ActivityTemplateFk;
									activity.LocationFk = oldActivity.LocationFk;
									hasToReschedule = true;
								}
								else
								{
									hasToReschedule = hasToReschedule || false;
									//SaveActivity(activity);
									dataPool.Activities = new List<ActivityEntity>() { activity };
									hasToUpdate = false;
								}
								break;
							case "ControllingUnitFk":
								// get project controllingUnits
								int[] cus;
								if (oldActivity.ControllingUnitFk.HasValue)
								{
									change.IsChanged = false;
									activity.ControllingUnitFk = oldActivity.ControllingUnitFk;
									var result = new BulkValidationResultData();
									result.Apply = true;
									result.Error = "ControllingUnit already exists and can't be changed!";
									result.Valid = false;
									change.ValidationResultData = result;
								}
								else
								{
									if (!dicControllingUnits.TryGetValue(activity.ProjectFk, out cus))
									{
										cus = cuLogic.GetListByProjectId(activity.ProjectFk).Select(e => e.Id).ToArray();
										dicControllingUnits[activity.ProjectFk] = cus;
									}

									if (cus.IsNullOrEmpty() || activity.ControllingUnitFk.HasValue && !cus.Contains(activity.ControllingUnitFk.Value))
									{
										change.IsChanged = false;
										activity.ControllingUnitFk = oldActivity.ControllingUnitFk;
										var result = new BulkValidationResultData();
										result.Apply = true;
										result.Error = "ControllingUnit not assigned to current project!";
										result.Valid = false;
										change.ValidationResultData = result;
									}
								}

								hasToReschedule = hasToReschedule || false;
								hasToUpdate = false;
								break;

							case "ActivityTemplateFk":
								planningChange.LocationFk = activity.LocationFk;
								planningChange.ActivityTemplateFk = activity.ActivityTemplateFk;
								activity.ActivityTemplateFk = oldActivity.ActivityTemplateFk;
								hasToReschedule = true;
								break;
							case "ActivityReportingTime":
								planningChange.DueDate = DateTime.Now;
								activity.ReportingDate = oldActivity.ReportingDate;
								planningChange.ReportingDate = activity.ReportingDate;
								complete.Activity = oldActivity;
								hasToReschedule = true;
								break;
							default:
								//SaveActivity(activity);
								dataPool.Activities = new List<ActivityEntity>() { activity };
								hasToReschedule = hasToReschedule || false;
								hasToUpdate = false;
								break;

						}
						if (hasToUpdate)
						{
							var res = calcLogic.ValidateComplete(complete, dataPool);
							if (res.ProgressReportsToSave != null && res.ProgressReportsToSave.Any())
							{
								dataPool.ProgressReports = res.ProgressReportsToSave;
							}
							if (res.EffectedActivities != null && res.EffectedActivities.Any())
							{
								dataPool.Activities = res.EffectedActivities;
							}

							UpdateOnly(res);
							dataPool.Activities = new List<ActivityEntity>() { oldActivity };
						}
						else
						{
							if (actsToSave.ContainsKey(activity.Id))
							{
								actsToSave[activity.Id] = activity;
							}
							else
							{
								actsToSave.Add(activity.Id, activity);
							}
						}

						dicSchedule[activity.ScheduleFk] = hasToReschedule;
					}
				}
			}

			if (actsToSave.Any())
			{
				var bulkSaver = new BulkSaveHelper();
				using (var dbContext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{

					var chunks = actsToSave.Values.ToSizedChunks(5000);
					foreach (var chunk in chunks)
					{
						bulkSaver.BulkUpdate(dbContext, chunk);
					}
				}
			}
			if (hasToReschedule)
			{
				var sucCalc = new ShiftSuccessorCalculation();
				foreach (var scheduleId in scheduleIds)
				{
					if (dicSchedule[scheduleId])
					{
						var res = sucCalc.RescheduleAllActivities(scheduleId, dataPool);
						UpdateOnly(res);
					}
				}
			}
			//cha
			return changeRequestList;
		}

		/// <summary>
		/// Get acitivies by baseline
		/// </summary>
		/// <param name="baselineId"></param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetTreeByBaseline(int baselineId)
		{
			var entities = GetListByFilter(e => e.BaselineFk == baselineId).ToList();

			var result = BuildUpTree(entities);
			entities.Clear();

			DoIncludeTransientProperties(result);
			DateTime dueDate = DateTime.Now;
			IncludeCompletionInformation(result, dueDate);
			return result;
		}

		/// <summary>
		/// can selected activities be deleted
		/// </summary>
		/// <param name="activities"></param>
		/// <returns></returns>
		public Dictionary<string, object> CanBeDeleted(IEnumerable<ActivityEntity> activities)
		{
			bool canBeDeleted = true;
			string errMessage;
			var guard = new EntityDeleteGuard();
			IEntityRelationInfo relInfo = guard.GetEntityRelationInfo(RelationInfoIdentifier.SchedulingMainActivity);
			var agent = relInfo.GetEntityDeleteAgent();

			var flattList = activities.Flatten(e => e.ActivityEntities_ParentActivityFk).ToArray();

			//var errorMsgList = new List<String>();
			//if (!agent.CheckAggregatedCanBeDeleted(relInfo, flattList, guard))
			//{
			//	errMessage = guard.GetInstanceCantBeDeleteMessage(relInfo, flattList.FirstOrDefault());
			//	errorMsgList.Add(errMessage);
			//	canBeDeleted = false;
			//}
			var errorMsgList = new List<object>();
			foreach (var item in flattList)
			{
				if (!agent.CheckAggregatedCanBeDeleted(relInfo, item, guard))
				{
					errMessage = guard.GetInstanceCantBeDeleteMessage(relInfo, item);
					errorMsgList.Add(new { Code = item.Code, Description = errMessage });
					canBeDeleted = false;
				}
				if (canBeDeleted)
				{
					if (item.ScheduleSubFk.HasValue || item.ActivitySubFk.HasValue)
					{
						errorMsgList.Add(new { Code = item.Code, Description = NLS.ERR_ActivityCannotBeDeletedHasSubSchedule });
						canBeDeleted = false;
					}
				}
			}

			return new Dictionary<string, object>() {
				{ "canBeDeleted", canBeDeleted },
				{ "errorMsg" ,errorMsgList }
			};
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="filterFn"></param>
		/// <returns></returns>
		/// <exception cref="NotImplementedException"></exception>
		//	public IEnumerable<IActivityEntity> GetByFilter(Expression<Func<IActivityEntity, Boolean>> filterFn) => throw new NotImplementedException();

		IEnumerable<IActivityEntity> IActivityLogic.GetByFilter(Expression<Func<IActivityEntity, bool>> filterFn)
		{
			var newFilter = filterFn.ChangeParameterType<IActivityEntity, ActivityEntity, bool>();
			return this.GetByFilter(newFilter);
		}

		IDictionary<Int32, Int32> IOwnerMappingReadLogic.Read(IDictionary<IIdentifyable, IIdentifyable> ownerMapping)
		{
			IDictionary<int, int> valuePairs = new Dictionary<int, int>();

			foreach (var item in ownerMapping)
			{
				if (item.Key is ActivityEntity)
				{
					valuePairs.Add(item.Key.Id, item.Value.Id);
				}
			}
			return valuePairs;
		}

		private int GetProjectCalender(int projectId, int calendarId, List<int> prjCalendarIds, List<CalendarEntity> calendars)
		{
			var prjCalLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectCalendarLogic>();
			int resultCalendarId = calendarId;

			if (!prjCalendarIds.Contains(calendarId))
			{
				var calendar = calendars.Where(e => e.Id == calendarId).FirstOrDefault();
				if (calendar != null)
				{
					int? calSourceId = null;
					var calId = calendarId;
					var calType = calendar.CalendarTypeFk;
					if (calendar.CalendarTypeFk == 2)
					{
						// create project calendar for copied calendar
						var action = new CalendarActionEntity() { Action = 5, Calendar = new CalendarEntity() { Id = calendarId } };
						var calComplete = new SchedulingCalendarActionLogic().Execute(action);
						calId = calComplete.Calendar.Id;
						calType = calComplete.Calendar.CalendarTypeFk;
						calSourceId = calendarId;
						resultCalendarId = calId;
					}
					var prjCal = prjCalLogic.Create(new Platform.Core.IdentificationData() { PKey1 = projectId }) as IProjectCalendarEntity;
					prjCal.CalendarFk = calId;
					prjCal.CalendarTypeFk = calType;
					prjCal.CalendarSourceFk = calSourceId;

					prjCalLogic.Save(new List<IProjectCalendarEntity>() { prjCal });
					// add it to list
					prjCalendarIds.Add(prjCal.CalendarFk);
				}
			}

			return resultCalendarId;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="currentActivityId"></param>
		/// <returns></returns>
		public int? GetParentSCurveFk(int currentActivityId)
		{
			int? GetParentSCurveFkRecursive(int activityId)
			{
				using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
				{
					var activity = dbcontext.Entities<ActivityEntity>().Where(x => x.Id == activityId).Select(s => new { s.SCurveFk, s.ParentActivityFk }).FirstOrDefault();

					if (activity == null)
					{
						return null;
					}

					if (activity.SCurveFk.HasValue)
					{
						return activity.SCurveFk;
					}

					return activity.ParentActivityFk.HasValue ? GetParentSCurveFkRecursive(activity.ParentActivityFk.Value) : null;

				}
			}
			return GetParentSCurveFkRecursive(currentActivityId);
		}

		private void UpdateCompleteWithBulk(ActivityComplete complete)
		{
			var activities = new List<ActivityEntity>();

			if (complete.Activity != null)
			{
				activities.Add(complete.Activity);
			}
			if (complete.EffectedActivities != null && complete.EffectedActivities.Any())
			{
				activities.AddRange(complete.EffectedActivities);
			}
			ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(activities));

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var bulkhelper = new BulkSaveHelper();
				if (activities.Count > 0)
				{
					var newActs = activities.Where(e => e.Version == 0);
					var oldActs = activities.Where(e => e.Version > 0);

					if (newActs != null && newActs.Any())
					{
						var chunks = newActs.ToSizedChunks(5000);
						foreach (var chunk in chunks)
						{
							bulkhelper.BulkInsert(dbcontext, chunk);
						}
					}
					if (oldActs != null && oldActs.Any())
					{

						var chunks = oldActs.ToSizedChunks(5000);
						foreach (var chunk in chunks)
						{
							bulkhelper.BulkUpdate(dbcontext, chunk);
						}
					}
				}
				if (complete.RelationshipsToSave != null && complete.RelationshipsToSave.Any())
				{
					var newRels = complete.RelationshipsToSave.Where(e => e.Version == 0);
					var oldRels = complete.RelationshipsToSave.Where(e => e.Version > 0);

					var relchunks = newRels.ToSizedChunks(5000);
					foreach (var chunk in relchunks)
					{
						bulkhelper.BulkInsert(dbcontext, chunk);
					}
					relchunks = oldRels.ToSizedChunks(5000);
					foreach (var chunk in relchunks)
					{
						bulkhelper.BulkUpdate(dbcontext, chunk);
					}

				}
				if (complete.EventsToSave != null && complete.EventsToSave.Any())
				{
					var newEvs = complete.EventsToSave.Where(e => e.Version == 0);
					var oldEvs = complete.EventsToSave.Where(e => e.Version > 0);

					var evchunks = newEvs.ToSizedChunks(5000);
					foreach (var chunk in evchunks)
					{
						bulkhelper.BulkInsert(dbcontext, chunk);
					}
					evchunks = oldEvs.ToSizedChunks(5000);
					foreach (var chunk in evchunks)
					{
						bulkhelper.BulkUpdate(dbcontext, chunk);
					}
				}
			}
		}

		private void UpdateWithBulk(IEnumerable<ActivityEntity> activities, IEnumerable<ActivityRelationshipEntity> relations)
		{
			ActiveCollaboratorsMgr.Value.LogActivity(Context.UserId, () => GetCollaborationContexts(activities));

			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				var bulkhelper = new BulkSaveHelper();
				if (activities != null && activities.Any())
				{
					var newActs = activities.Where(e => e.Version == 0);
					var oldActs = activities.Where(e => e.Version > 0);

					if (newActs != null && newActs.Any())
					{
						var chunks = newActs.ToSizedChunks(5000);
						foreach (var chunk in chunks)
						{
							bulkhelper.BulkInsert(dbcontext, chunk);
						}
					}
					if (oldActs != null && oldActs.Any())
					{

						var chunks = oldActs.ToSizedChunks(5000);
						foreach (var chunk in chunks)
						{
							bulkhelper.BulkUpdate(dbcontext, chunk);
						}
					}
				}
				if (relations != null && relations.Any())
				{
					var newRels = relations.Where(e => e.Version == 0);
					var oldRels = relations.Where(e => e.Version > 0);

					if (newRels != null && newRels.Any())
					{
						var relchunks = newRels.ToSizedChunks(5000);
						foreach (var chunk in relchunks)
						{
							bulkhelper.BulkInsert(dbcontext, chunk);
						}
					}
					if (oldRels != null && oldRels.Any())
					{

						var relchunks = oldRels.ToSizedChunks(5000);
						foreach (var chunk in relchunks)
						{
							bulkhelper.BulkUpdate(dbcontext, chunk);
						}
					}
				}
			}
		}
		/// <summary>
		/// Check if an entity with same code in same schedule already exists
		/// </summary>
		/// <param name="filter"></param>
		/// <returns></returns>
		private bool IsUniqueForDeepCopy(Expression<Func<ActivityEntity, bool>> filter = null)
		{
			using (var dbcontext = new RVPBC.DbContext(ModelBuilder.DbModel))
			{
				IQueryable<ActivityEntity> query = dbcontext.Entities<ActivityEntity>();
				if (filter != null)
				{
					query = query.Where(filter);
				}

				if (query != null && query.Count() > 0)
				{
					return false;
				}
				return true;
			}
		}


		internal static class DateTimeExtension
		{
			public static int Compare(DateTime? dateTime1, DateTime? dateTime2)
			{
				if (!dateTime1.HasValue && !dateTime2.HasValue)
				{
					return 0;
				}
				else if (!dateTime1.HasValue && dateTime2.HasValue)
				{
					return DateTime.Compare(new DateTime(0), dateTime2.Value);
				}
				else if (dateTime1.HasValue && !dateTime2.HasValue)
				{
					return DateTime.Compare(dateTime1.Value, new DateTime(0));
				}
				else
				{
					return DateTime.Compare(dateTime1.Value, dateTime2.Value);
				}
			}
		}
		/// <summary>
		///
		/// </summary>
		/// <param name="activityIds"></param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetActivitiesByTempTable(int[] activityIds)
		{
			var ids = activityIds.Select(e => new IdentificationData() { Id = e });
			var activities = GetSourceByForeignIds<DdTempIdsEntity>(ids, (e, tmp) => e.Id == tmp.Id).OrderBy(e => e.Code).ToList();
			foreach (var activity in activities)
			{
				if (activity.ActivityEntities_ParentActivityFk != null && activity.ActivityEntities_ParentActivityFk.Any())
				{
					activity.ActivityEntities_ParentActivityFk = activity.ActivityEntities_ParentActivityFk.OrderBy(e => e.Code).ToList();
				}
			}
			return activities;
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="scheduleId"></param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetActivitiesForSource(int scheduleId)
		{
			var acts = GetScheduleActivities(scheduleId).ToList();
			DoIncludeTransientProperties(acts);
			return BuildUpTree(acts);
		}

		private (ActivityComplete, string) CopySourceActivity(ActivityEntity activity, ActivityEntity targetActivity, Dictionary<int, ActivityEntity> idMapping, Stack<int> newActivityIds, int constraintTypeId,
			List<int> prjCalendarIds, List<CalendarEntity> calendars, List<ActivityRelationshipEntity> relations, Stack<int> newRelationIds, ScheduleDataPool dataPool, string lastCode, IEntityCodeGenerator generator)
		{
			var complete = new ActivityComplete();
			int? parentId = targetActivity.Id > 0 ? targetActivity.Id : null;
			complete.Activity = DoCopy(activity);
			complete.Activity.Id = newActivityIds.Pop();
			complete.Activity.ParentActivityFk = targetActivity.Id > 0 ? targetActivity.Id : null;
			complete.Activity.ConstraintDate = null;
			complete.Activity.ConstraintTypeFk = constraintTypeId;
			complete.Activity.ScheduleFk = targetActivity.ScheduleFk;
			complete.Activity.ProjectReleaseFk = null;
			var newHierarchy = !(targetActivity.Id > 0);
			complete.Activity.Code = SetNewCodeForSource(targetActivity.ScheduleFk, parentId, lastCode, newHierarchy, dataPool, generator);
			lastCode = complete.Activity.Code;
			if (activity.ProjectFk != targetActivity.ProjectFk)
			{
				complete.Activity.ControllingUnitFk = null;
				complete.Activity.LocationFk = null;
				complete.Activity.ProjectFk = targetActivity.ProjectFk;
				complete.Activity.CalendarFk = GetProjectCalender(targetActivity.ProjectFk, activity.CalendarFk, prjCalendarIds, calendars);
			}
			idMapping.Add(activity.Id, complete.Activity);
			dataPool.Activities = new List<ActivityEntity>() { complete.Activity };

			List<int> actIds = new List<int>() { activity.Id };
			if (activity.ActivityTypeFk != Constants.ActivityTypeSummaryActivity && relations.Any())
			{
				if (relations != null && relations.Any())
				{
					var rels = relations.Where(e => e.ScheduleFk == activity.ScheduleFk && e.ParentActivityFk == activity.Id && e.ChildScheduleFk == activity.ScheduleFk && e.ChildActivityFk == activity.Id);
					if (rels != null && rels.Any())
					{
						if (complete.RelationshipsToSave == null)
						{
							complete.RelationshipsToSave = new List<ActivityRelationshipEntity>();
						}
						complete.RelationshipsToSave.AddRange(CopyRelations(rels, idMapping, newRelationIds));
						dataPool.Relationships = complete.RelationshipsToSave;
					}
				}
			}
			if (activity.ActivityEntities_ParentActivityFk != null && activity.ActivityEntities_ParentActivityFk.Any())
			{
				var code = lastCode;
				complete.EffectedActivities = new List<ActivityEntity>();
				var result = CopySourcActivityChildren(activity.ActivityEntities_ParentActivityFk, complete.Activity, idMapping, newActivityIds, constraintTypeId, relations, newRelationIds, dataPool, code, generator);
				complete.EffectedActivities = result.Item1;
				if (complete.RelationshipsToSave == null)
				{
					complete.RelationshipsToSave = new List<ActivityRelationshipEntity>();
				}
				complete.RelationshipsToSave = result.Item2;
				dataPool.Activities = complete.EffectedActivities;
				dataPool.Relationships = complete.RelationshipsToSave;
			}
			return (complete, lastCode);
		}

		private (List<ActivityEntity>, List<ActivityRelationshipEntity>) CopySourcActivityChildren(IEnumerable<ActivityEntity> children, ActivityEntity newOwner, Dictionary<int, ActivityEntity> idMapping, Stack<int> newActivityIds, int constraintTypeId,
			List<ActivityRelationshipEntity> relations, Stack<int> newRelationIds, ScheduleDataPool dataPool, string lastCode, IEntityCodeGenerator generator)
		{
			List<ActivityEntity> list = new List<ActivityEntity>();
			List<ActivityRelationshipEntity> listRelation = new List<ActivityRelationshipEntity>();
			var newHierarchy = true;
			var code = lastCode;
			foreach (var child in children)
			{
				if (!idMapping.ContainsKey(child.Id))
				{
					var newEntity = DoCopy(child);
					newEntity.Id = newActivityIds.Pop();
					newEntity.ConstraintDate = null;
					newEntity.ConstraintTypeFk = constraintTypeId;
					newEntity.ScheduleFk = newOwner.ScheduleFk;
					newEntity.ParentActivityFk = newOwner.Id;
					newEntity.ProjectReleaseFk = null;

					if (child.ProjectFk != newOwner.ProjectFk)
					{
						newEntity.ControllingUnitFk = null;
						newEntity.LocationFk = null;
						newEntity.ProjectFk = newOwner.ProjectFk;
						newEntity.CalendarFk = newOwner.CalendarFk;
					}
					newEntity.Code = SetNewCodeForSource(newOwner.ScheduleFk, newOwner.Id, code, newHierarchy, dataPool, generator);
					idMapping.Add(child.Id, newEntity);
					dataPool.Activities = new List<ActivityEntity>() { newEntity };
					newHierarchy = false;
					lastCode = code = newEntity.Code;
					list.Add(newEntity);
					if (relations != null && relations.Any())
					{
						var rels = relations.Where(e => e.ScheduleFk == child.ScheduleFk && e.ParentActivityFk == child.Id && e.ChildScheduleFk == child.ScheduleFk && e.ChildActivityFk == child.Id);
						if (rels != null && rels.Any())
						{
							listRelation.AddRange(CopyRelations(rels, idMapping, newRelationIds));
						}
					}
					if (child.ActivityEntities_ParentActivityFk != null && child.ActivityEntities_ParentActivityFk.Any())
					{
						var result = CopySourcActivityChildren(child.ActivityEntities_ParentActivityFk, newEntity, idMapping, newActivityIds, constraintTypeId, relations, newRelationIds, dataPool, code, generator);
						list.AddRange(result.Item1);
						listRelation.AddRange(result.Item2);
					}
				}
			}
			return (list, listRelation);
		}
		private List<ActivityRelationshipEntity> CopyRelations(IEnumerable<ActivityRelationshipEntity> entities, Dictionary<int, ActivityEntity> idMapping, Stack<int> newRelationIds)
		{
			var listRelations = new List<ActivityRelationshipEntity>();
			foreach (var entity in entities)
			{
				var copy = (ActivityRelationshipEntity)entity.Clone();
				copy.Id = newRelationIds.Pop();

				if (idMapping.ContainsKey(entity.ParentActivityFk))
				{
					copy.ParentActivityFk = idMapping[entity.ParentActivityFk].Id;
					var activityEntity = idMapping[entity.ParentActivityFk];
					if (activityEntity != null)
					{
						copy.ScheduleFk = activityEntity.ScheduleFk;
					}
				}

				if (idMapping.ContainsKey(entity.ChildActivityFk))
				{
					copy.ChildActivityFk = idMapping[entity.ChildActivityFk].Id;
					var activityEntity = idMapping[entity.ChildActivityFk];
					if (activityEntity != null)
					{
						copy.ChildScheduleFk = activityEntity.ScheduleFk;
					}
				}
				copy.Version = 0;
				copy.UpdatedAt = null;
				copy.UpdatedBy = null;
				listRelations.Add(copy);
			}
			return listRelations;
		}
		private string SetNewCodeForSource(int scheduleId, int? parentActivityId, string lastCode, bool newHierarchy, ScheduleDataPool dataPool, IEntityCodeGenerator generator)
		{
			bool mNewHierarchy = newHierarchy;
			IEntityCore last = new ActivityEntity();
			last.Code = lastCode;
			ActivityEntity lastItem = null;
			var lastItems = dataPool.GetActivities(e => e.ScheduleFk == scheduleId);
			if (generator.GetType() == typeof(RunningNumberGenerator) || generator.GetType() == typeof(RunningNumberWithZerosGenerator))
			{
				lastItem = lastItems.OrderBy(e => e.Code).LastOrDefault();
			} else
			{
				lastItem = lastItems.Where(e => e.ParentActivityFk == parentActivityId && e.ScheduleFk == scheduleId).OrderBy(e => e.Code).LastOrDefault();
			}

			if (lastItem != null)
			{
				last.Code = lastItem.Code;
				mNewHierarchy = false;
				if (!lastCode.IsNullOrEmpty())
				{
					var code1Parts = lastCode.Split('.');
					var code2Parts = lastItem.Code.Split('.');
					if (code1Parts.Count() >= code2Parts.Count())
					{
						if (code1Parts[code2Parts.Count() - 1].CompareTo(code2Parts[code2Parts.Count() - 1]) < 0)
						{
							last.Code = lastItem.Code;
							mNewHierarchy = false;
						}
					}
				}
			}
			bool isValid = false;
			while (!isValid)
			{
				string code;
				if (mNewHierarchy)
				{
					code = generator.GetSingleChildCode(last);
				}
				else
				{
					code = generator.GetSingleCode(last);
				}
				if (!String.IsNullOrEmpty(code))
				{
					last.Code = code;
					mNewHierarchy = false;
					isValid = true;
					var exist = lastItems.FirstOrDefault(e => e.Code == code);
					if (exist != null)
					{
						isValid = false;
					}
				}
			}
			if (last.Code.Length > 16)
			{
				throw new SchedulingBusinessLayerException
				{
					ErrorCode = (int)ExceptionErrorCodes.ResourceFatalError,
					ErrorDetail = NLS.ERR_CodeTooLong,
					ErrorMessage = NLS.ERR_CodeTooLong
				};

			}
			return last.Code;
		}
		/// <summary>
		/// Returns the collaboration contexts for a given set of complete update entities.
		/// </summary>
		/// <param name="entities">The complete update entities.</param>
		/// <returns>The collaboration context(s).</returns>
		protected override IEnumerable<(String Area, String Context)> GetCollaborationContexts(IEnumerable<IIdentifyable> entities)
		{
			ArgumentNullException.ThrowIfNull(entities);

			return entities
				.Select(e => e switch
				{
					ActivityComplete ce => [ce.Activity.ScheduleFk],
					ActivityEntity act => [act.ScheduleFk],
					_ => Array.Empty<Int32>()
				})
				.SelectMany(h => h)
				.Distinct()
				.Select(headerId => ("scheduling.main", headerId.ToString(CultureInfo.InvariantCulture)));
		}

		/// <summary>
		///
		/// </summary>
		/// <param name="fromItems"></param>
		/// <returns></returns>
		public IEnumerable<ActivityEntity> GetActivitiesForSourceCopy(IEnumerable<int> fromItems)
		{
			var activities = GetActivitiesByTempTable(fromItems.ToArray()).ToList();
			var toDelete = new List<ActivityEntity>();
			foreach (var activity in activities)
			{
				if (activity.ActivityEntities_ParentActivityFk != null && activity.ActivityEntities_ParentActivityFk.Any())
				{
					toDelete.AddRange(activity.ActivityEntities_ParentActivityFk);
					toDelete.AddRange(CollectChildren(activity.ActivityEntities_ParentActivityFk));
				}
			}
			var deleteIds = toDelete.CollectIds(e=>e.Id).ToList();
			activities.RemoveAll(e => deleteIds.Contains(e.Id));

			return activities.OrderBy(e => e.Code);

		}
		private IEnumerable<ActivityEntity> CollectChildren(IEnumerable<ActivityEntity> activities)
		{
			var toDelete = new List<ActivityEntity>();
			foreach (var activity in activities)
			{
				if (activity.ActivityEntities_ParentActivityFk != null && activity.ActivityEntities_ParentActivityFk.Any())
				{
					toDelete.AddRange(activity.ActivityEntities_ParentActivityFk);
					toDelete.AddRange(CollectChildren(activity.ActivityEntities_ParentActivityFk));
				}
			}
			return toDelete;
		}

		private void UpdateEntitiesWithBulk(IEnumerable<RVPBC.EntityBase> entitiesToSave, IEnumerable<RVPBC.EntityBase> entitiesToDelete, RVPBC.DbContext dbContext)
		{
			var bulkSaveHelper = new BulkSaveHelper();
			if (entitiesToSave != null && entitiesToSave.Any())
			{
				var toInsert = entitiesToSave.Where(e => e.Version == 0).ToList();
				var toUpdate = entitiesToSave.Where(e => e.Version != 0).Distinct().ToList();
				bulkSaveHelper.BulkInsert(dbContext, toInsert);
				bulkSaveHelper.BulkUpdate(dbContext, toUpdate);
			}
			if (entitiesToDelete != null && entitiesToDelete.Any())
			{
				bulkSaveHelper.BulkDelete(dbContext, entitiesToDelete);
			}
		}

		private void UpdateActivityCompleteWithBulkSaveHelper(ActivityComplete activityComplete, RVPBC.DbContext dbContext)
		{
			var bulkSaveHelper = new BulkSaveHelper();
			// save activities
			var activities = new List<ActivityEntity>();
			if (activityComplete.Activity != null)
			{
				activities.Add(activityComplete.Activity);
			}
			if (activityComplete.EffectedActivities != null)
			{
				foreach (var item in activityComplete.EffectedActivities)
				{
					if (activities.All(e => e.Id != item.Id))
					{
						activities.Add(item);
					}
				}
			}
			if (activities.Any())
			{
				UpdateEntitiesWithBulk(activities, null, dbContext);
			}
			// delete/ save ProgressReports
			UpdateEntitiesWithBulk(activityComplete.ProgressReportsToSave, activityComplete.ProgressReportsToDelete, dbContext);

			// delete / save Clerks
			UpdateEntitiesWithBulk(activityComplete.ClerksToSave, activityComplete.ClerksToDelete, dbContext);

			// delete ObjModelSimulation
			UpdateEntitiesWithBulk(activityComplete.ObjModelSimulationToSave, activityComplete.ObjModelSimulationToDelete, dbContext);

			// delete Split
			UpdateEntitiesWithBulk(activityComplete.SplitsToSave, activityComplete.SplitsToDelete, dbContext);

			// save ActivityByBaseline
			UpdateEntitiesWithBulk(activityComplete.ActivityByBaselineToSave, null, dbContext);

			// delete Hammock
			UpdateEntitiesWithBulk(activityComplete.HammockActivityToSave, activityComplete.HammockActivityToDelete, dbContext);

			// delete event
			UpdateEntitiesWithBulk(activityComplete.EventsToSave, activityComplete.EventsToDelete, dbContext);

			// delete relation
			UpdateEntitiesWithBulk(activityComplete.RelationshipsToSave, activityComplete.RelationshipsToDelete, dbContext);
		}
		private void DoInitActivityWithDefaults(ActivityEntity entity, ScheduleEntity schedule)
		{
			var projectLogic = RIB.Visual.Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IProjectInfoProvider>();
			var today = DateTime.Today;
			if (schedule != null && schedule.InitWithTargetStart)
			{
				if (schedule.TargetStart.HasValue)
				{
					today = schedule.TargetStart.Value;
				}
				else
				{
					var date = projectLogic.GetProjectStartDate(schedule.ProjectFk);
					if (date.HasValue)
					{
						today = date.Value;
					}
				}
			}
			entity.PlannedStart = new DateTime(today.Year, today.Month, today.Day, 0, 0, 0);

			entity.CurrentStart = entity.PlannedStart;

			entity.PlannedFinish = new DateTime(entity.PlannedStart.Year, entity.PlannedStart.Month, entity.PlannedStart.Day, 23, 59, 59);
			entity.CurrentFinish = entity.PlannedFinish;

			entity.PlannedDuration = 1;
			entity.CurrentDuration = 1;
			entity.ResourceFactor = 1;
			entity.PerformanceFactor = 1;

			entity.ActivityTypeFk = Constants.ActivityTypeActivity;

			//setdefault BAS_3DVISUALIZATIONTYPE
			var bas3dVisualizationTypeLogic = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValues<IDefaultEntityProvider>("basics.customize.threedvisualizationtype").FirstOrDefault();
			if (bas3dVisualizationTypeLogic != null)
			{
				var dbEntity = bas3dVisualizationTypeLogic.GetDefault(null);
				if (dbEntity != null)
				{
					entity.Bas3dVisualizationTypeFk = dbEntity.Id;
				}
			}

			var provider = Platform.AppServer.Runtime.BusinessApplication.BusinessEnvironment.GetExportedValue<IDefaultEntityProvider>("basics.customize.activitystate"); var status = provider.GetDefault(schedule.RubricCategoryFk);

			var lookupLogic = new RVSLB.SchedulingLookupLogic();
			int def = 0;
			if (status != null)
			{
				entity.ActivityStateFk = status.Id;
			}
			else
			{
				throw new SchedulingBusinessLayerException()
				{
					ErrorCode = (int)ExceptionErrorCodes.BusinessFatalError,
					ErrorMessage = NLS.ERR_ActivityStatusNoDefault
				};
			}

			def = lookupLogic.GetDefaultConstraintType();
			if (def > 0 && (def == Constants.AsSoonAsPossible || def == Constants.AsLateAsPossible || def == Constants.NoConstraint))
			{
				entity.ConstraintTypeFk = def;
			}
			else
			{
				entity.ConstraintTypeFk = Constants.AsSoonAsPossible;
			}

			def = lookupLogic.GetDefaultSchedulingMethod();
			if (def > 0)
			{
				entity.SchedulingMethodFk = def;
			}

			def = lookupLogic.GetDefaultProgressReportMethod();
			if (def > 0)
			{
				entity.ProgressReportMethodFk = def;
			}

			def = lookupLogic.GetDefaultTaskType();
			if (def > 0)
			{
				entity.TaskTypeFk = def;
			}

			var apl = new BasicsCustomizeActivityPresentationLogic();
			var apd = apl.GetDefault();
			if (apd != null)
			{
				entity.ActivityPresentationFk = apd.Id;
			}

			entity.IsCritical = false;
			entity.IsLive = true;
			entity.Quantity = 1;
			entity.IsQuantityEvaluated = true;
		}
	}
}
