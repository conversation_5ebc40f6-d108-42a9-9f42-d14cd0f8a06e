﻿<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{B460A276-9ABF-4A98-8C5B-8384AFED520F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>RIB.Visual.Procurement.Quote.UnitTests</RootNamespace>
    <AssemblyName>RIB.Visual.Procurement.Quote.UnitTests</AssemblyName>
    <WarningLevel>4</WarningLevel>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <RIBvisualBinPool>$(SolutionDir)..\..\..\BinPool\$(Configuration).Server</RIBvisualBinPool>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFramework>net8.0</TargetFramework>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <SignAssembly>true</SignAssembly>
    <AssemblyOriginatorKeyFile>RIBvisual.snk</AssemblyOriginatorKeyFile>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>.\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <RunCodeAnalysis>false</RunCodeAnalysis>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <Optimize>true</Optimize>
    <OutputPath>.\bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Prefer32Bit>false</Prefer32Bit>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>612,618</WarningsNotAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="RIB.Visual.Basics.BillingSchema.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.BillingSchema.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Material.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Material.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Cloud.Common.Core">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Cloud.Common.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Runtime">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessComponents">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.BusinessEnvironment">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.BusinessEnvironment.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Core">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.OperationalManagement">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.OperationalManagement.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Localization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.AppServer.Web">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.AppServer.Web.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Procurement.RfQ.BusinessComponents">
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Procurement.RfQ.BusinessComponents.dll</HintPath>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.Localization">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.Localization.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.ServiceDomain">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.ServiceDomain.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Services.Platform.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Services.Platform.ServiceFacade.WebApi.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Procurement.Common.ServiceFacade.WebApi">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Procurement.Common.ServiceFacade.WebApi.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Procurement.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Procurement.Common.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Core.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Core.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.LookupData.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.LookupData.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Procurement.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Procurement.Common.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Common.Core">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Common.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Basics.Company.BusinessComponents">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Basics.Company.BusinessComponents.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.UnitTests.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.UnitTests.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Procurement.Common.UnitTests">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Procurement.Common.UnitTests.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Caching">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Runtime.Caching.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.Configuration.ConfigurationManager.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="FluentAssertions">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\FluentAssertions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.SqlServer.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.ComponentModel.Composition.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="EntityFramework.Functions">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\EntityFramework.Functions.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.CodeDom">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\System.CodeDom.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="RIB.Visual.Platform.Server.Common">
      <SpecificVersion>false</SpecificVersion>
      <HintPath>$(RIBvisualBinPool)\RIB.Visual.Platform.Server.Common.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{B4F97281-0DBD-4835-9ED8-7DFB966E87FF}" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Platform.AppServer.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </None>
    <None Include="Platform.Database.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </None>
    <None Include="RIB.Visual.Procurement.Quote.UnitTests.dll.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </None>
    <None Include="RIBvisual.snk" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Logic\QuoteHeaderLogicTest.cs" />
    <Compile Include="TestClass\QuoteDeepCopyTest.cs" />
    <Compile Include="TestCommon.cs" />
    <Compile Include="QuoteTestFixture.cs" />
    <Compile Include="TestData\QuoteDeepCopyTestData.cs" />
    <Compile Include="TestHandler\QuoteDeepCopyTestHandler.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Quote.BusinessComponents\RIB.Visual.Procurement.Quote.BusinessComponents.csproj">
      <Project>{BD79FEE7-069F-43BE-AC6D-6024C2AAA755}</Project>
      <Name>RIB.Visual.Procurement.Quote.BusinessComponents</Name>
    </ProjectReference>
    <ProjectReference Include="..\Quote.Common\RIB.Visual.Procurement.Quote.Common.csproj">
      <Project>{8F4D6A30-C9B5-41D0-B95C-A54EAED58526}</Project>
      <Name>RIB.Visual.Procurement.Quote.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Quote.Core\RIB.Visual.Procurement.Quote.Core.csproj">
      <Project>{3207B084-AFE6-4660-A293-D01B5F04D084}</Project>
      <Name>RIB.Visual.Procurement.Quote.Core</Name>
    </ProjectReference>
  </ItemGroup>
  <Target Name="PostBuild" AfterTargets="PostBuildEvent">
    <Exec Command="xcopy &quot;$(TargetDir)$(TargetName).*&quot; &quot;$(RIBvisualBinPool)\*&quot; /D /C /Y /F" />
  </Target>
  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="Moq" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="System.Runtime.Caching" />
    <PackageReference Include="System.Data.SqlClient" />
    <PackageReference Include="xunit" />
    <PackageReference Include="xunit.runner.visualstudio">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="$(RIBvisualBinPool)\xunit.runner.json">
      <Link>xunit.runner.json</Link>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>