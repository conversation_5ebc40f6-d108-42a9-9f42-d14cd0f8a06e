/*
 * Copyright(c) RIB Software GmbH
 */

import { BusinessModuleInfoBase, EntityInfo } from '@libs/ui/business-base';
import { TIMEKEEPING_CERTIFICATE_ENTITY_INFO } from './timekeeping-certificate-entity-info.model';
import { TIMEKEEPING_CERTIFICATE_DOCUMENT_ENTITY_INFO } from './timekeeping-certificate-document-entity-info.model';
import { TIMEKEEPING_CERTIFIED_EMPLOYEE_ENTITY_INFO } from './timekeeping-certified-employee-entity-info.model';

/**
 * The module info object for the `timekeeping.certificate` content module.
 */
export class TimekeepingCertificateModuleInfo extends BusinessModuleInfoBase {
	private static _instance?: TimekeepingCertificateModuleInfo;

	/**
	 * Returns the singleton instance of the class.
	 */
	public static get instance(): TimekeepingCertificateModuleInfo {
		if (!this._instance) {
			this._instance = new TimekeepingCertificateModuleInfo();
		}
		return this._instance;
	}

	/**
	 * Initializes the module information of timekeeping certificate module
	 */
	private constructor() {
		super();
	}

	/**
	 * Returns the internal module name
	 * @returns {string}
	 */
	public override get internalModuleName(): string {
		return 'timekeeping.certificate';
	}

	/**
	 * Lists all `EntityInfo` objects registered in this module.
	 */
	public override get entities(): EntityInfo[] {
		return [
			TIMEKEEPING_CERTIFICATE_ENTITY_INFO,
			TIMEKEEPING_CERTIFIED_EMPLOYEE_ENTITY_INFO,
			TIMEKEEPING_CERTIFICATE_DOCUMENT_ENTITY_INFO
		];
	}

	/**
	 * Translations this module should preload.
	 */
	public override get preloadedTranslations(): string[] {
		return super.preloadedTranslations.concat([
			'timekeeping.common',
			'basics.customize',
			'cloud.common'
		]);
	}

	/**
	 * Translation container UUID – used for centralized entity labels.
	 */
	protected override get translationContainer(): string | undefined {
		return '0cb97ba3f9b54010a10261f8c3f1efde'; //0cb97ba3f9b54010a10261f8c3f1efde 8a1b7e3c-4f0d-4a8b-9d7c-1e2f3a4b5c6d
	}
}
